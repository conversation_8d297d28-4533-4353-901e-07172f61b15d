dependencies:
  # Required IDF version
  idf: ">=4.1"

  # Defining a dependency from the ESP Component Registry:
  # https://components.espressif.com/component/example/cmp
  example/cmp: "^3.3.3"

  # # Other ways to define dependencies
  #
  # # For components maintained by Espressif only name can be used.
  # # Same as `espressif/cmp`
  # component: "~1.0.0"
  #
  # # Or in a longer form with extra parameters
  # component2:
  #   version: ">=2.0.0"
  #
  #   # For transient dependencies `public` flag can be set.
  #   # `public` flag doesn't have an effect for the `main` component.
  #   # All dependencies of `main` are public by default.
  #   public: true
  #
  #   # For components hosted on non-default registry:
  #   service_url: "https://componentregistry.company.com"
  #
  # # For components in git repository:
  # test_component:
  #   path: test_component
  #   git: ssh://**************/user/components.git
  #
  # # For test projects during component development
  # # components can be used from a local directory
  # # with relative or absolute path
  # some_local_component:
  #   path: ../../projects/component
