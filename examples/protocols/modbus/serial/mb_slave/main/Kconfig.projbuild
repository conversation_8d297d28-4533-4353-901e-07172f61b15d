menu "Modbus Example Configuration"

    orsource "$IDF_PATH/examples/common_components/env_caps/$IDF_TARGET/Kconfig.env_caps"

    config MB_UART_PORT_ONE
        bool
        default y
        depends on (ESP_CONSOLE_UART_NUM !=1) && (SOC_UART_HP_NUM > 1)

    config MB_UART_PORT_TWO
        bool
        default y
        depends on (ESP_CONSOLE_UART_NUM !=2) && (SOC_UART_HP_NUM > 2)

    config MB_UART_PORT_NUM
        int "UART port number"
        range 0 2 if MB_UART_PORT_TWO
        default 2 if MB_UART_PORT_TWO
        range 0 1 if MB_UART_PORT_ONE
        default 1 if MB_UART_PORT_ONE
        help
            UART communication port number for Modbus example.

    config MB_UART_BAUD_RATE
        int "UART communication speed"
        range 1200 115200
        default 115200
        help
            UART communication speed for Modbus example.

    config MB_UART_RXD
        int "UART RXD pin number"
        range ENV_GPIO_RANGE_MIN ENV_GPIO_IN_RANGE_MAX
        default 22 if IDF_TARGET_ESP32 || IDF_TARGET_ESP32C6 || IDF_TARGET_ESP32P4
        default 8 if IDF_TARGET_ESP32S2 || IDF_TARGET_ESP32S3 || IDF_TARGET_ESP32C3
        default 8 if IDF_TARGET_ESP32C2 || IDF_TARGET_ESP32H2 || IDF_TARGET_ESP32C5
        default 8 if IDF_TARGET_ESP32C61
        help
            GPIO number for UART RX pin. See UART documentation for more information
            about available pin numbers for UART.

    config MB_UART_TXD
        int "UART TXD pin number"
        range ENV_GPIO_RANGE_MIN ENV_GPIO_OUT_RANGE_MAX
        default 23 if IDF_TARGET_ESP32 || IDF_TARGET_ESP32C6 || IDF_TARGET_ESP32P4
        default 9 if IDF_TARGET_ESP32S2 || IDF_TARGET_ESP32S3 || IDF_TARGET_ESP32C3
        default 9 if IDF_TARGET_ESP32C2 || IDF_TARGET_ESP32H2 || IDF_TARGET_ESP32C5
        default 9 if IDF_TARGET_ESP32C61
        help
            GPIO number for UART TX pin. See UART documentation for more information
            about available pin numbers for UART.

    config MB_UART_RTS
        int "UART RTS pin number"
        range ENV_GPIO_RANGE_MIN ENV_GPIO_OUT_RANGE_MAX
        default 20 if IDF_TARGET_ESP32P4
        default 18 if IDF_TARGET_ESP32 || IDF_TARGET_ESP32C6
        default 10 if IDF_TARGET_ESP32S2 || IDF_TARGET_ESP32S3 || IDF_TARGET_ESP32C3
        default 10 if IDF_TARGET_ESP32C2 || IDF_TARGET_ESP32H2 || IDF_TARGET_ESP32C5
        default 10 if IDF_TARGET_ESP32C61
        help
            GPIO number for UART RTS pin. This pin is connected to
            ~RE/DE pin of RS485 transceiver to switch direction.
            See UART documentation for more information about available pin
            numbers for UART.

    choice MB_COMM_MODE
        prompt "Modbus communication mode"
        default MB_COMM_MODE_RTU if CONFIG_FMB_COMM_MODE_RTU_EN
        help
            Selection of Modbus communication mode option for Modbus.

        config MB_COMM_MODE_RTU
            bool "RTU mode"
            depends on FMB_COMM_MODE_RTU_EN

        config MB_COMM_MODE_ASCII
            bool "ASCII mode"
            depends on FMB_COMM_MODE_ASCII_EN

    endchoice

    config MB_SLAVE_ADDR
        int "Modbus slave address"
        range 1 127
        default 1
        help
            This is the Modbus slave address in the network.
            It is used to organize Modbus network with several slaves connected into the same segment.


endmenu
