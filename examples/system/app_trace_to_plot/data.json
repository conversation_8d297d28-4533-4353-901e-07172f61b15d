{"//data.json": "Apptrace plotting config file for 'read_trace.py'", "//Summary and usage": "This file is used for apptrace plotting. You can use this json file with '--plot-config' parameter of the 'read_trace.py' file is to configure graphs for visualizing sensor data", "//More information": "To get more information about apptrace plotting and configuration file, please check 'app_trace_to_plot' example in '../examples/system' and 'read_trace.py' file", "Temperature sensors": {"data_streams": {"Outside temperature": {"id": 1, "x_axis_data_size": 4, "x_axis_timestamp": true, "y_axis_data_size": 1, "data_type": "int", "opacity": 0.5, "line_color": "crimson", "marker_line_width": 2, "marker_size": 9, "mode": "lines+markers"}, "Engine temperature": {"id": 2, "x_axis_data_size": 4, "x_axis_timestamp": true, "y_axis_data_size": 2, "data_type": "int", "line_color": "#0d0887", "mode": "lines+markers"}}, "xaxis_title": "Timestamp", "yaxis_title": "Temperature in Celcius"}, "Altitude sensors": {"data_streams": {"Altitude": {"id": 3, "x_axis_data_size": 4, "x_axis_timestamp": true, "y_axis_data_size": 4, "data_type": "int", "fill": "tozer<PERSON>", "fillcolor": "gray", "hovertext": "(in meter)", "type": "scatter", "fillpattern": {"shape": "x"}, "error_y": {"type": "percent", "value": 10}, "mode": "markers"}}, "xaxis_title": "Timestamp", "yaxis_title": "Altitude in meters"}, "Pressure sensors": {"data_streams": {"Pressure": {"id": 4, "x_axis_data_size": 4, "x_axis_timestamp": true, "y_axis_data_size": 8, "data_type": "float", "precision": 2, "type": "bar"}}, "xaxis_title": "Timestamp", "yaxis_title": "Pressure in milibar"}}