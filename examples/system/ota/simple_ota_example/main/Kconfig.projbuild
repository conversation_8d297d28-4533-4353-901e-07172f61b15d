menu "Example Configuration"

    config EXAMPLE_FIRMWARE_UPGRADE_URL
        string "firmware upgrade url endpoint"
        default "https://***********:8070/hello_world.bin"
        help
            URL of server which hosts the firmware
            image.

    config EXAMPLE_USE_CERT_BUNDLE
        bool "Enable certificate bundle"
        default y
        depends on MBEDTLS_CERTIFICATE_BUNDLE
        help
            Enable trusted root certificate bundle. This approach allows to have
            OTA updates functional with any public server without requirement
            to explicitly add its server certificate.

    config EXAMPLE_FIRMWARE_UPGRADE_URL_FROM_STDIN
        bool
        default y if EXAMPLE_FIRMWARE_UPGRADE_URL = "FROM_STDIN"

    config EXAMPLE_SKIP_COMMON_NAME_CHECK
        bool "Skip server certificate CN fieldcheck"
        default n
        help
            This allows you to skip the validation of OTA server certificate CN field.

    config EXAMPLE_FIRMWARE_UPGRADE_BIND_IF
        bool "Support firmware upgrade bind specified interface"
        default n
        help
            This allows you to bind specified interface in OTA example.

    choice EXAMPLE_FIRMWARE_UPGRADE_BIND_IF_TYPE
        prompt "Choose OTA data bind interface"
        default EXAMPLE_FIRMWARE_UPGRADE_BIND_IF_STA
        depends on EXAMPLE_FIRMWARE_UPGRADE_BIND_IF
        help
            Select which interface type of OTA data go through.

        config EXAMPLE_FIRMWARE_UPGRADE_BIND_IF_STA
            bool "Bind wifi station interface"
            depends on EXAMPLE_CONNECT_WIFI
            help
                Select wifi station interface to pass the OTA data.

        config EXAMPLE_FIRMWARE_UPGRADE_BIND_IF_ETH
            bool "Bind ethernet interface"
            depends on EXAMPLE_CONNECT_ETHERNET
            help
                Select ethernet interface to pass the OTA data.
    endchoice
endmenu
