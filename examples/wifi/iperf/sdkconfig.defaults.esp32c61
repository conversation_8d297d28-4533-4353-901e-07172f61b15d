#
# ESP32C61-Specific
#
CONFIG_IDF_ENV_FPGA=n

CONFIG_ESP_WIFI_STATIC_RX_BUFFER_NUM=20
CONFIG_ESP_WIFI_DYNAMIC_RX_BUFFER_NUM=40
CONFIG_ESP_WIFI_DYNAMIC_TX_BUFFER_NUM=36
CONFIG_ESP_WIFI_AMPDU_TX_ENABLED=y
CONFIG_ESP_WIFI_TX_BA_WIN=32
CONFIG_ESP_WIFI_AMPDU_RX_ENABLED=y
CONFIG_ESP_WIFI_RX_BA_WIN=20
CONFIG_ESP_WIFI_NVS_ENABLED=n

CONFIG_LWIP_TCP_SND_BUF_DEFAULT=25920
CONFIG_LWIP_TCP_WND_DEFAULT=43200
CONFIG_LWIP_TCP_RECVMBOX_SIZE=48
CONFIG_LWIP_UDP_RECVMBOX_SIZE=64
CONFIG_LWIP_TCPIP_RECVMBOX_SIZE=48
CONFIG_LWIP_IP_REASS_MAX_PBUFS=15

#
# Serial flasher config
#
CONFIG_ESPTOOLPY_FLASHMODE_QIO=y
CONFIG_ESPTOOLPY_FLASHFREQ_80M=y

#
# Wi-Fi
#
CONFIG_ESP_WIFI_ENABLE_WIFI_TX_STATS=y
CONFIG_ESP_WIFI_ENABLE_WIFI_RX_STATS=y
CONFIG_ESP_WIFI_ENABLE_WIFI_RX_MU_STATS=n
CONFIG_ESP_WIFI_ENABLE_DUMP_HESIGB=n
CONFIG_ESP_WIFI_ENABLE_DUMP_MU_CFO=n
CONFIG_ESP_WIFI_ENABLE_DUMP_CTRL_NDPA=n
CONFIG_ESP_WIFI_ENABLE_DUMP_CTRL_BFRP=n
CONFIG_ESP_WIFI_SLP_IRAM_OPT=n
CONFIG_LWIP_EXTRA_IRAM_OPTIMIZATION=n

CONFIG_LWIP_TCPIP_CORE_LOCKING=y
CONFIG_LWIP_TCPIP_CORE_LOCKING_INPUT=y

CONFIG_IPERF_DEF_TCP_TX_BUFFER_LEN=2880
