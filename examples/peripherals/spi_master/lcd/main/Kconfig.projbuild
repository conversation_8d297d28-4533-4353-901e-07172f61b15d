menu "Example Configuration"

    choice LCD_TYPE
        prompt "LCD module type"
        default LCD_TYPE_AUTO
        help
            The type of LCD on the evaluation board.

        config LCD_TYPE_AUTO
            bool "Auto detect"
        config LCD_TYPE_ST7789V
            bool "ST7789V (WROVER Kit v2 or v3)"
        config LCD_TYPE_ILI9341
            bool "ILI9341 (WROVER Kit v1 or DevKitJ v1)"
    endchoice

    config LCD_OVERCLOCK
        bool
        prompt "Run LCD at higher clock speed than allowed"
        default "n"
        help
            The ILI9341 and ST7789 specify that the maximum clock speed for the SPI interface is 10MHz. However,
            in practice the driver chips work fine with a higher clock rate, and using that gives a better framerate.
            Select this to try using the out-of-spec clock rate.

endmenu
