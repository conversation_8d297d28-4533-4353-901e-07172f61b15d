menu "Example Ethernet Configuration"

    orsource "$IDF_PATH/examples/common_components/env_caps/$IDF_TARGET/Kconfig.env_caps"

    config EXAMPLE_USE_INTERNAL_ETHERNET
        depends on SOC_EMAC_SUPPORTED
        select ETH_USE_ESP32_EMAC
        default y
        bool "Internal EMAC"
        help
            Use internal Ethernet MAC controller.

    if EXAMPLE_USE_INTERNAL_ETHERNET
        choice EXAMPLE_ETH_PHY_MODEL
            prompt "Ethernet PHY Device"
            default EXAMPLE_ETH_PHY_IP101
            help
                Select the Ethernet PHY device to use in the example.

            config EXAMPLE_ETH_PHY_GENERIC
                bool "Generic 802.3 PHY"
                help
                    Any Ethernet PHY chip compliant with IEEE 802.3 can be used. However, while
                    basic functionality should always work, some specific features might be limited,
                    even if the PHY meets IEEE 802.3 standard. A typical example is loopback
                    functionality, where certain PHYs may require setting a specific speed mode to
                    operate correctly.

            config EXAMPLE_ETH_PHY_IP101
                bool "IP101"
                help
                    IP101 is a single port 10/100 MII/RMII/TP/Fiber Fast Ethernet Transceiver.
                    Goto http://www.icplus.com.tw/pp-IP101G.html for more information about it.

            config EXAMPLE_ETH_PHY_RTL8201
                bool "RTL8201/SR8201"
                help
                    RTL8201F/SR8201F is a single port 10/100Mb Ethernet Transceiver with auto MDIX.
                    Goto http://www.corechip-sz.com/productsview.asp?id=22 for more information about it.

            config EXAMPLE_ETH_PHY_LAN87XX
                bool "LAN87xx"
                help
                    Below chips are supported:
                    LAN8710A is a small footprint MII/RMII 10/100 Ethernet Transceiver with HP Auto-MDIX and
                        flexPWR® Technology.
                    LAN8720A is a small footprint RMII 10/100 Ethernet Transceiver with HP Auto-MDIX Support.
                    LAN8740A/LAN8741A is a small footprint MII/RMII 10/100 Energy Efficient Ethernet Transceiver
                        with HP Auto-MDIX and flexPWR® Technology.
                    LAN8742A is a small footprint RMII 10/100 Ethernet Transceiver with HP Auto-MDIX and
                        flexPWR® Technology.
                    Goto https://www.microchip.com for more information about them.

            config EXAMPLE_ETH_PHY_DP83848
                bool "DP83848"
                help
                    DP83848 is a single port 10/100Mb/s Ethernet Physical Layer Transceiver.
                    Goto http://www.ti.com/product/DP83848J for more information about it.

            config EXAMPLE_ETH_PHY_KSZ80XX
                bool "KSZ80xx"
                help
                    With the KSZ80xx series, Microchip offers single-chip 10BASE-T/100BASE-TX
                    Ethernet Physical Layer Transceivers (PHY).
                    The following chips are supported: KSZ8001, KSZ8021, KSZ8031, KSZ8041,
                        KSZ8051, KSZ8061, KSZ8081, KSZ8091
                    Goto https://www.microchip.com for more information about them.
        endchoice # EXAMPLE_ETH_PHY_MODEL

        config EXAMPLE_ETH_MDC_GPIO
            int "SMI MDC GPIO number"
            range ENV_GPIO_RANGE_MIN ENV_GPIO_OUT_RANGE_MAX
            default 23 if IDF_TARGET_ESP32
            default 31 if IDF_TARGET_ESP32P4
            help
                Set the GPIO number used by SMI MDC.

        config EXAMPLE_ETH_MDIO_GPIO
            int "SMI MDIO GPIO number"
            range ENV_GPIO_RANGE_MIN ENV_GPIO_OUT_RANGE_MAX
            default 18 if IDF_TARGET_ESP32
            default 52 if IDF_TARGET_ESP32P4
            help
                Set the GPIO number used by SMI MDIO.

        config EXAMPLE_ETH_PHY_RST_GPIO
            int "PHY Reset GPIO number"
            range -1 ENV_GPIO_OUT_RANGE_MAX
            default 5 if IDF_TARGET_ESP32
            default 51 if IDF_TARGET_ESP32P4
            help
                Set the GPIO number used to reset PHY chip.
                Set to -1 to disable PHY chip hardware reset.

        config EXAMPLE_ETH_PHY_ADDR
            int "PHY Address"
            range -1 31
            default 1
            help
                Set PHY address according your board schematic.
                Set to -1 to driver find the PHY address automatically.
    endif # EXAMPLE_USE_INTERNAL_ETHERNET

    config EXAMPLE_USE_SPI_ETHERNET
        bool "SPI Ethernet"
        default n
        select ETH_USE_SPI_ETHERNET
        help
            Use external SPI-Ethernet module(s).

    if EXAMPLE_USE_SPI_ETHERNET
        config EXAMPLE_SPI_ETHERNETS_NUM
            int "Number of SPI Ethernet modules to use at a time"
            range 1 2
            default 1
            help
                Set the number of SPI Ethernet modules you want to use at a time. Multiple SPI modules can be connected
                to one SPI interface and can be separately accessed based on state of associated Chip Select (CS).

        choice EXAMPLE_ETHERNET_TYPE_SPI
            prompt "Ethernet SPI"
            default EXAMPLE_USE_W5500
            help
                Select which kind of Ethernet will be used in the example.

            config EXAMPLE_USE_DM9051
                bool "DM9051 Module"
                select ETH_SPI_ETHERNET_DM9051
                help
                    Select external SPI-Ethernet module (DM9051).

            config EXAMPLE_USE_KSZ8851SNL
                bool "KSZ8851SNL Module"
                select ETH_SPI_ETHERNET_KSZ8851SNL
                help
                    Select external SPI-Ethernet module (KSZ8851SNL).

            config EXAMPLE_USE_W5500
                bool "W5500 Module"
                select ETH_SPI_ETHERNET_W5500
                help
                    Select external SPI-Ethernet module (W5500).
        endchoice

        config EXAMPLE_ETH_SPI_HOST
            int "SPI Host Number"
            range 0 2
            default 1
            help
                Set the SPI host used to communicate with the SPI Ethernet Controller.

        config EXAMPLE_ETH_SPI_SCLK_GPIO
            int "SPI SCLK GPIO number"
            range ENV_GPIO_RANGE_MIN ENV_GPIO_OUT_RANGE_MAX
            default 14 if IDF_TARGET_ESP32
            default 12 if IDF_TARGET_ESP32S2 || IDF_TARGET_ESP32S3
            default 6 if IDF_TARGET_ESP32C3 || IDF_TARGET_ESP32C2 || IDF_TARGET_ESP32C6 || IDF_TARGET_ESP32C61
            default 4 if IDF_TARGET_ESP32H2
            default 33 if IDF_TARGET_ESP32P4
            default 8 if IDF_TARGET_ESP32C5
            help
                Set the GPIO number used by SPI SCLK.

        config EXAMPLE_ETH_SPI_MOSI_GPIO
            int "SPI MOSI GPIO number"
            range ENV_GPIO_RANGE_MIN ENV_GPIO_OUT_RANGE_MAX
            default 13 if IDF_TARGET_ESP32
            default 11 if IDF_TARGET_ESP32S2 || IDF_TARGET_ESP32S3
            default 7 if IDF_TARGET_ESP32C3 || IDF_TARGET_ESP32C2 || IDF_TARGET_ESP32C6 || IDF_TARGET_ESP32C61
            default 5 if IDF_TARGET_ESP32H2
            default 32 if IDF_TARGET_ESP32P4
            default 10 if IDF_TARGET_ESP32C5
            help
                Set the GPIO number used by SPI MOSI.

        config EXAMPLE_ETH_SPI_MISO_GPIO
            int "SPI MISO GPIO number"
            range ENV_GPIO_RANGE_MIN ENV_GPIO_IN_RANGE_MAX
            default 12 if IDF_TARGET_ESP32
            default 13 if IDF_TARGET_ESP32S2 || IDF_TARGET_ESP32S3
            default 2 if IDF_TARGET_ESP32C3 || IDF_TARGET_ESP32C2 || IDF_TARGET_ESP32C6 || IDF_TARGET_ESP32C61
            default 0 if IDF_TARGET_ESP32H2
            default 24 if IDF_TARGET_ESP32P4
            default 9 if IDF_TARGET_ESP32C5
            help
                Set the GPIO number used by SPI MISO.

        config EXAMPLE_ETH_SPI_CLOCK_MHZ
            int "SPI clock speed (MHz)"
            range 5 80
            default 16
            help
                Set the clock speed (MHz) of SPI interface.

        config EXAMPLE_ETH_SPI_CS0_GPIO
            int "SPI CS0 GPIO number for SPI Ethernet module #1"
            range ENV_GPIO_RANGE_MIN ENV_GPIO_OUT_RANGE_MAX
            default 15 if IDF_TARGET_ESP32
            default 10 if IDF_TARGET_ESP32S2 || IDF_TARGET_ESP32C3 || IDF_TARGET_ESP32S3 || IDF_TARGET_ESP32C2
            default 3 if IDF_TARGET_ESP32C6 || IDF_TARGET_ESP32C5 || IDF_TARGET_ESP32C61
            default 1 if IDF_TARGET_ESP32H2
            default 21 if IDF_TARGET_ESP32P4
            help
                Set the GPIO number used by SPI CS0, i.e. Chip Select associated with the first SPI Eth module).

        config EXAMPLE_ETH_SPI_CS1_GPIO
            depends on EXAMPLE_SPI_ETHERNETS_NUM > 1
            int "SPI CS1 GPIO number for SPI Ethernet module #2"
            range ENV_GPIO_RANGE_MIN ENV_GPIO_OUT_RANGE_MAX
            default 32 if IDF_TARGET_ESP32
            default 7 if IDF_TARGET_ESP32S2 || IDF_TARGET_ESP32S3
            default 8 if IDF_TARGET_ESP32C3
            default 21 if IDF_TARGET_ESP32C6
            default 3 if IDF_TARGET_ESP32C2
            default 11 if IDF_TARGET_ESP32H2
            default 23 if IDF_TARGET_ESP32P4 || IDF_TARGET_ESP32C61
            default 1 if IDF_TARGET_ESP32C5
            help
                Set the GPIO number used by SPI CS1, i.e. Chip Select associated with the second SPI Eth module.

        config EXAMPLE_ETH_SPI_INT0_GPIO
            int "Interrupt GPIO number SPI Ethernet module #1"
            range -1 ENV_GPIO_IN_RANGE_MAX
            default 4 if IDF_TARGET_ESP32 || IDF_TARGET_ESP32S2 || IDF_TARGET_ESP32C3 || IDF_TARGET_ESP32S3
            default 4 if IDF_TARGET_ESP32C2 || IDF_TARGET_ESP32C6 || IDF_TARGET_ESP32C5
            default 10 if IDF_TARGET_ESP32H2
            default 48 if IDF_TARGET_ESP32P4
            default 0 if IDF_TARGET_ESP32C61
            help
                Set the GPIO number used by the first SPI Ethernet module interrupt line.
                Set -1 to use SPI Ethernet module in polling mode.

        config EXAMPLE_ETH_SPI_INT1_GPIO
            depends on EXAMPLE_SPI_ETHERNETS_NUM > 1
            int "Interrupt GPIO number SPI Ethernet module #2"
            range -1 ENV_GPIO_IN_RANGE_MAX
            default 33 if IDF_TARGET_ESP32
            default 5 if IDF_TARGET_ESP32S2 || IDF_TARGET_ESP32C3 || IDF_TARGET_ESP32S3 || IDF_TARGET_ESP32C2
            default 5 if IDF_TARGET_ESP32C6 || IDF_TARGET_ESP32C5
            default 9 if IDF_TARGET_ESP32H2
            default 47 if IDF_TARGET_ESP32P4
            default 1 if IDF_TARGET_ESP32C61
            help
                Set the GPIO number used by the second SPI Ethernet module interrupt line.
                Set -1 to use SPI Ethernet module in polling mode.

        config EXAMPLE_ETH_SPI_POLLING0_MS_VAL
            depends on EXAMPLE_ETH_SPI_INT0_GPIO < 0
            int "Polling period in msec of SPI Ethernet Module #1"
            default 10
            help
                Set SPI Ethernet module polling period.

        config EXAMPLE_ETH_SPI_POLLING1_MS_VAL
            depends on EXAMPLE_SPI_ETHERNETS_NUM > 1 && EXAMPLE_ETH_SPI_INT1_GPIO < 0
            int "Polling period in msec of SPI Ethernet Module #2"
            default 10
            help
                Set SPI Ethernet module polling period.

        # Hidden variable to ensure that polling period option is visible only when interrupt is set disabled and
        # it is set to known value (0) when interrupt is enabled at the same time.
        config EXAMPLE_ETH_SPI_POLLING0_MS
            int
            default EXAMPLE_ETH_SPI_POLLING0_MS_VAL if EXAMPLE_ETH_SPI_POLLING0_MS_VAL > 0
            default 0

        # Hidden variable to ensure that polling period option is visible only when interrupt is set disabled and
        # it is set to known value (0) when interrupt is enabled at the same time.
        config EXAMPLE_ETH_SPI_POLLING1_MS
            depends on EXAMPLE_SPI_ETHERNETS_NUM > 1
            int
            default EXAMPLE_ETH_SPI_POLLING1_MS_VAL if EXAMPLE_ETH_SPI_POLLING1_MS_VAL > 0
            default 0

        config EXAMPLE_ETH_SPI_PHY_RST0_GPIO
            int "PHY Reset GPIO number of SPI Ethernet Module #1"
            range -1 ENV_GPIO_OUT_RANGE_MAX
            default -1
            help
                Set the GPIO number used to reset PHY chip on the first SPI Ethernet module.
                Set to -1 to disable PHY chip hardware reset.

        config EXAMPLE_ETH_SPI_PHY_RST1_GPIO
            depends on EXAMPLE_SPI_ETHERNETS_NUM > 1
            int "PHY Reset GPIO number of SPI Ethernet Module #2"
            range -1 ENV_GPIO_OUT_RANGE_MAX
            default -1
            help
                Set the GPIO number used to reset PHY chip on the second SPI Ethernet module.
                Set to -1 to disable PHY chip hardware reset.

        config EXAMPLE_ETH_SPI_PHY_ADDR0
            int "PHY Address of SPI Ethernet Module #1"
            range 0 31
            default 1
            help
                Set the first SPI Ethernet module PHY address according your board schematic.

        config EXAMPLE_ETH_SPI_PHY_ADDR1
            depends on EXAMPLE_SPI_ETHERNETS_NUM > 1
            int "PHY Address of SPI Ethernet Module #2"
            range 0 31
            default 1
            help
                Set the second SPI Ethernet module PHY address according your board schematic.
    endif # EXAMPLE_USE_SPI_ETHERNET
endmenu
