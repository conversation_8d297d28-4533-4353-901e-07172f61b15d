# TCPlp

This module will contain a port of <PERSON><PERSON>lp for OpenThread. Currently, it only
contains part of the implementation; the rest will be added in future pull
requests.

## URL

Based on https://github.com/ucbrise/tcplp.

## License

BSD 3-Clause

TCPlp is derived from the TCP stack in the FreeBSD Operating System. Files
taken from FreeBSD retain their original copyright notices and licenses (which
are BSD-like licenses). New code contributed as part of TCPlp is provided
under the BSD 3-Clause License.

## Description

TCPlp is a full-scale TCP stack for low-power wireless networks and
resource-constrained embedded systems. It is designed to support performant and
efficient TCP operation over IEEE 802.15.4 networks. It is based on the TCP
stack in the FreeBSD operating system.

TCPlp is a software artifact that accompanies an NSDI 2020 paper:

<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>. Performant TCP for Low-Power Wireless Networks. NSDI 2020.

The paper is available at the following locations:
* https://www.usenix.org/conference/nsdi20/presentation/kumar
* https://arxiv.org/abs/1811.02721
