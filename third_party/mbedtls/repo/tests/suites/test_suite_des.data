DES check weak key #1
des_check_weak:"0101010101010101":1

DES check weak key #2
des_check_weak:"FEE0FEE0FEF1FEF1":1

DES check weak key #3
des_check_weak:"0101010101010100":0

DES check weak key #4
des_check_weak:"EEE0FEE0FEF1FEF1":0

DES Encrypt OpenSSL Test Vector #1
des_encrypt_ecb:"0000000000000000":"0000000000000000":"8CA64DE9C1B123A7"

DES Encrypt OpenSSL Test Vector #2
des_encrypt_ecb:"FFFFFFFFFFFFFFFF":"FFFFFFFFFFFFFFFF":"7359B2163E4EDC58"

DES Encrypt OpenSSL Test Vector #3
des_encrypt_ecb:"3000000000000000":"1000000000000001":"958E6E627A05557B"

DES Encrypt OpenSSL Test Vector #4
des_encrypt_ecb:"1111111111111111":"1111111111111111":"F40379AB9E0EC533"

DES Encrypt OpenSSL Test Vector #5
des_encrypt_ecb:"0123456789ABCDEF":"1111111111111111":"17668DFC7292532D"

DES Encrypt OpenSSL Test Vector #6
des_encrypt_ecb:"1111111111111111":"0123456789ABCDEF":"8A5AE1F81AB8F2DD"

DES Encrypt OpenSSL Test Vector #7
des_encrypt_ecb:"0000000000000000":"0000000000000000":"8CA64DE9C1B123A7"

DES Encrypt OpenSSL Test Vector #8
des_encrypt_ecb:"FEDCBA9876543210":"0123456789ABCDEF":"ED39D950FA74BCC4"

DES Encrypt OpenSSL Test Vector #9
des_encrypt_ecb:"7CA110454A1A6E57":"01A1D6D039776742":"690F5B0D9A26939B"

DES Encrypt OpenSSL Test Vector #10
des_encrypt_ecb:"0131D9619DC1376E":"5CD54CA83DEF57DA":"7A389D10354BD271"

DES Encrypt OpenSSL Test Vector #11
des_encrypt_ecb:"07A1133E4A0B2686":"0248D43806F67172":"868EBB51CAB4599A"

DES Encrypt OpenSSL Test Vector #12
des_encrypt_ecb:"3849674C2602319E":"51454B582DDF440A":"7178876E01F19B2A"

DES Encrypt OpenSSL Test Vector #13
des_encrypt_ecb:"04B915BA43FEB5B6":"42FD443059577FA2":"AF37FB421F8C4095"

DES Encrypt OpenSSL Test Vector #14
des_encrypt_ecb:"0113B970FD34F2CE":"059B5E0851CF143A":"86A560F10EC6D85B"

DES Encrypt OpenSSL Test Vector #15
des_encrypt_ecb:"0170F175468FB5E6":"0756D8E0774761D2":"0CD3DA020021DC09"

DES Encrypt OpenSSL Test Vector #16
des_encrypt_ecb:"43297FAD38E373FE":"762514B829BF486A":"EA676B2CB7DB2B7A"

DES Encrypt OpenSSL Test Vector #17
des_encrypt_ecb:"07A7137045DA2A16":"3BDD119049372802":"DFD64A815CAF1A0F"

DES Encrypt OpenSSL Test Vector #18
des_encrypt_ecb:"04689104C2FD3B2F":"26955F6835AF609A":"5C513C9C4886C088"

DES Encrypt OpenSSL Test Vector #19
des_encrypt_ecb:"37D06BB516CB7546":"164D5E404F275232":"0A2AEEAE3FF4AB77"

DES Encrypt OpenSSL Test Vector #20
des_encrypt_ecb:"1F08260D1AC2465E":"6B056E18759F5CCA":"EF1BF03E5DFA575A"

DES Encrypt OpenSSL Test Vector #21
des_encrypt_ecb:"584023641ABA6176":"004BD6EF09176062":"88BF0DB6D70DEE56"

DES Encrypt OpenSSL Test Vector #22
des_encrypt_ecb:"025816164629B007":"480D39006EE762F2":"A1F9915541020B56"

DES Encrypt OpenSSL Test Vector #23
des_encrypt_ecb:"49793EBC79B3258F":"437540C8698F3CFA":"6FBF1CAFCFFD0556"

DES Encrypt OpenSSL Test Vector #24
des_encrypt_ecb:"4FB05E1515AB73A7":"072D43A077075292":"2F22E49BAB7CA1AC"

DES Encrypt OpenSSL Test Vector #25
des_encrypt_ecb:"49E95D6D4CA229BF":"02FE55778117F12A":"5A6B612CC26CCE4A"

DES Encrypt OpenSSL Test Vector #26
des_encrypt_ecb:"018310DC409B26D6":"1D9D5C5018F728C2":"5F4C038ED12B2E41"

DES Encrypt OpenSSL Test Vector #27
des_encrypt_ecb:"1C587F1C13924FEF":"305532286D6F295A":"63FAC0D034D9F793"

DES Encrypt OpenSSL Test Vector #28
des_encrypt_ecb:"0101010101010101":"0123456789ABCDEF":"617B3A0CE8F07100"

DES Encrypt OpenSSL Test Vector #29
des_encrypt_ecb:"1F1F1F1F0E0E0E0E":"0123456789ABCDEF":"DB958605F8C8C606"

DES Encrypt OpenSSL Test Vector #30
des_encrypt_ecb:"E0FEE0FEF1FEF1FE":"0123456789ABCDEF":"EDBFD1C66C29CCC7"

DES Encrypt OpenSSL Test Vector #31
des_encrypt_ecb:"0000000000000000":"FFFFFFFFFFFFFFFF":"355550B2150E2451"

DES Encrypt OpenSSL Test Vector #32
des_encrypt_ecb:"FFFFFFFFFFFFFFFF":"0000000000000000":"CAAAAF4DEAF1DBAE"

DES Encrypt OpenSSL Test Vector #33
des_encrypt_ecb:"0123456789ABCDEF":"0000000000000000":"D5D44FF720683D0D"

DES Encrypt OpenSSL Test Vector #34
des_encrypt_ecb:"FEDCBA9876543210":"FFFFFFFFFFFFFFFF":"2A2BB008DF97C2F2"

DES Decrypt OpenSSL Test Vector #1
des_decrypt_ecb:"0000000000000000":"8CA64DE9C1B123A7":"0000000000000000"

DES Decrypt OpenSSL Test Vector #2
des_decrypt_ecb:"FFFFFFFFFFFFFFFF":"7359B2163E4EDC58":"FFFFFFFFFFFFFFFF"

DES Decrypt OpenSSL Test Vector #3
des_decrypt_ecb:"3000000000000000":"958E6E627A05557B":"1000000000000001"

DES Decrypt OpenSSL Test Vector #4
des_decrypt_ecb:"1111111111111111":"F40379AB9E0EC533":"1111111111111111"

DES Decrypt OpenSSL Test Vector #5
des_decrypt_ecb:"0123456789ABCDEF":"17668DFC7292532D":"1111111111111111"

DES Decrypt OpenSSL Test Vector #6
des_decrypt_ecb:"1111111111111111":"8A5AE1F81AB8F2DD":"0123456789ABCDEF"

DES Decrypt OpenSSL Test Vector #7
des_decrypt_ecb:"0000000000000000":"8CA64DE9C1B123A7":"0000000000000000"

DES Decrypt OpenSSL Test Vector #8
des_decrypt_ecb:"FEDCBA9876543210":"ED39D950FA74BCC4":"0123456789ABCDEF"

DES Decrypt OpenSSL Test Vector #9
des_decrypt_ecb:"7CA110454A1A6E57":"690F5B0D9A26939B":"01A1D6D039776742"

DES Decrypt OpenSSL Test Vector #10
des_decrypt_ecb:"0131D9619DC1376E":"7A389D10354BD271":"5CD54CA83DEF57DA"

DES Decrypt OpenSSL Test Vector #11
des_decrypt_ecb:"07A1133E4A0B2686":"868EBB51CAB4599A":"0248D43806F67172"

DES Decrypt OpenSSL Test Vector #12
des_decrypt_ecb:"3849674C2602319E":"7178876E01F19B2A":"51454B582DDF440A"

DES Decrypt OpenSSL Test Vector #13
des_decrypt_ecb:"04B915BA43FEB5B6":"AF37FB421F8C4095":"42FD443059577FA2"

DES Decrypt OpenSSL Test Vector #14
des_decrypt_ecb:"0113B970FD34F2CE":"86A560F10EC6D85B":"059B5E0851CF143A"

DES Decrypt OpenSSL Test Vector #15
des_decrypt_ecb:"0170F175468FB5E6":"0CD3DA020021DC09":"0756D8E0774761D2"

DES Decrypt OpenSSL Test Vector #16
des_decrypt_ecb:"43297FAD38E373FE":"EA676B2CB7DB2B7A":"762514B829BF486A"

DES Decrypt OpenSSL Test Vector #17
des_decrypt_ecb:"07A7137045DA2A16":"DFD64A815CAF1A0F":"3BDD119049372802"

DES Decrypt OpenSSL Test Vector #18
des_decrypt_ecb:"04689104C2FD3B2F":"5C513C9C4886C088":"26955F6835AF609A"

DES Decrypt OpenSSL Test Vector #19
des_decrypt_ecb:"37D06BB516CB7546":"0A2AEEAE3FF4AB77":"164D5E404F275232"

DES Decrypt OpenSSL Test Vector #20
des_decrypt_ecb:"1F08260D1AC2465E":"EF1BF03E5DFA575A":"6B056E18759F5CCA"

DES Decrypt OpenSSL Test Vector #21
des_decrypt_ecb:"584023641ABA6176":"88BF0DB6D70DEE56":"004BD6EF09176062"

DES Decrypt OpenSSL Test Vector #22
des_decrypt_ecb:"025816164629B007":"A1F9915541020B56":"480D39006EE762F2"

DES Decrypt OpenSSL Test Vector #23
des_decrypt_ecb:"49793EBC79B3258F":"6FBF1CAFCFFD0556":"437540C8698F3CFA"

DES Decrypt OpenSSL Test Vector #24
des_decrypt_ecb:"4FB05E1515AB73A7":"2F22E49BAB7CA1AC":"072D43A077075292"

DES Decrypt OpenSSL Test Vector #25
des_decrypt_ecb:"49E95D6D4CA229BF":"5A6B612CC26CCE4A":"02FE55778117F12A"

DES Decrypt OpenSSL Test Vector #26
des_decrypt_ecb:"018310DC409B26D6":"5F4C038ED12B2E41":"1D9D5C5018F728C2"

DES Decrypt OpenSSL Test Vector #27
des_decrypt_ecb:"1C587F1C13924FEF":"63FAC0D034D9F793":"305532286D6F295A"

DES Decrypt OpenSSL Test Vector #28
des_decrypt_ecb:"0101010101010101":"617B3A0CE8F07100":"0123456789ABCDEF"

DES Decrypt OpenSSL Test Vector #29
des_decrypt_ecb:"1F1F1F1F0E0E0E0E":"DB958605F8C8C606":"0123456789ABCDEF"

DES Decrypt OpenSSL Test Vector #30
des_decrypt_ecb:"E0FEE0FEF1FEF1FE":"EDBFD1C66C29CCC7":"0123456789ABCDEF"

DES Decrypt OpenSSL Test Vector #31
des_decrypt_ecb:"0000000000000000":"355550B2150E2451":"FFFFFFFFFFFFFFFF"

DES Decrypt OpenSSL Test Vector #32
des_decrypt_ecb:"FFFFFFFFFFFFFFFF":"CAAAAF4DEAF1DBAE":"0000000000000000"

DES Decrypt OpenSSL Test Vector #33
des_decrypt_ecb:"0123456789ABCDEF":"D5D44FF720683D0D":"0000000000000000"

DES Decrypt OpenSSL Test Vector #34
des_decrypt_ecb:"FEDCBA9876543210":"2A2BB008DF97C2F2":"FFFFFFFFFFFFFFFF"

DES-CBC Encrypt OpenSSL Test Vector #1
des_encrypt_cbc:"0123456789abcdef":"fedcba9876543210":"37363534333231204E6F77206973207468652074696D6520":"ccd173ffab2039f4acd8aefddfd8a1eb468e91157888ba68":0

DES-CBC Decrypt OpenSSL Test Vector #1
des_decrypt_cbc:"0123456789abcdef":"fedcba9876543210":"ccd173ffab2039f4acd8aefddfd8a1eb468e91157888ba68":"37363534333231204E6F77206973207468652074696D6520":0

3DES-ECB 2Key Encrypt OpenSSL Test Vector #1
des3_encrypt_ecb:2:"0000000000000000FFFFFFFFFFFFFFFF":"0000000000000000":"9295B59BB384736E"

3DES-ECB 2Key Encrypt OpenSSL Test Vector #2
des3_encrypt_ecb:2:"FFFFFFFFFFFFFFFF3000000000000000":"FFFFFFFFFFFFFFFF":"199E9D6DF39AA816"

3DES-ECB 2Key Decrypt OpenSSL Test Vector #1
des3_decrypt_ecb:2:"0000000000000000FFFFFFFFFFFFFFFF":"9295B59BB384736E":"0000000000000000"

3DES-ECB 2Key Decrypt OpenSSL Test Vector #2
des3_decrypt_ecb:2:"FFFFFFFFFFFFFFFF3000000000000000":"199E9D6DF39AA816":"FFFFFFFFFFFFFFFF"

3DES-CBC 3Key Encrypt OpenSSL Test Vector #1
des3_encrypt_cbc:3:"0123456789abcdeff1e0d3c2b5a49786fedcba9876543210":"fedcba9876543210":"37363534333231204E6F77206973207468652074696D6520":"3FE301C962AC01D02213763C1CBD4CDC799657C064ECF5D4":0

3DES-CBC 3Key Decrypt OpenSSL Test Vector #1
des3_decrypt_cbc:3:"0123456789abcdeff1e0d3c2b5a49786fedcba9876543210":"fedcba9876543210":"3FE301C962AC01D02213763C1CBD4CDC799657C064ECF5D4":"37363534333231204E6F77206973207468652074696D6520":0

DES-CBC Encrypt (Invalid input length)
des_encrypt_cbc:"0123456789abcdef":"fedcba9876543210":"37363534333231204E6F77206973207468652074696D65":"":MBEDTLS_ERR_DES_INVALID_INPUT_LENGTH

3DES-CBC 3Key Encrypt (Invalid input length)
des3_encrypt_cbc:3:"0123456789abcdeff1e0d3c2b5a49786fedcba9876543210":"fedcba9876543210":"37363534333231204E6F77206973207468652074696D65":"":MBEDTLS_ERR_DES_INVALID_INPUT_LENGTH

Run through parity bit tests
des_key_parity_run:

DES Selftest
des_selftest:
