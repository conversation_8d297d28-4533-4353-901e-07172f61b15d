Cipher list
mbedtls_cipher_list:

Set padding with AES-CBC
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
set_padding:MBEDTLS_CIPHER_AES_128_CBC:MBEDTLS_PADDING_PKCS7:0

Set padding with AES-CFB
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CFB
set_padding:MBEDTLS_CIPHER_AES_128_CFB128:MBEDTLS_PADDING_PKCS7:MBEDTLS_ERR_CIPHER_BAD_INPUT_DATA

Set padding with AES-CTR
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CTR
set_padding:MBEDTLS_CIPHER_AES_128_CTR:MBEDTLS_PADDING_PKCS7:MBEDTLS_ERR_CIPHER_BAD_INPUT_DATA

Set padding with CAMELLIA-CBC
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
set_padding:MBEDTLS_CIPHER_CAMELLIA_128_CBC:MBEDTLS_PADDING_PKCS7:0

Set padding with CAMELLIA-CFB
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CFB
set_padding:MBEDTLS_CIPHER_CAMELLIA_128_CFB128:MBEDTLS_PADDING_PKCS7:MBEDTLS_ERR_CIPHER_BAD_INPUT_DATA

Set padding with CAMELLIA-CTR
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CTR
set_padding:MBEDTLS_CIPHER_CAMELLIA_128_CTR:MBEDTLS_PADDING_PKCS7:MBEDTLS_ERR_CIPHER_BAD_INPUT_DATA

Set padding with DES-CBC
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
set_padding:MBEDTLS_CIPHER_DES_CBC:MBEDTLS_PADDING_PKCS7:0

Set padding with NULL
depends_on:MBEDTLS_CIPHER_NULL_CIPHER
set_padding:MBEDTLS_CIPHER_NULL:MBEDTLS_PADDING_PKCS7:MBEDTLS_ERR_CIPHER_BAD_INPUT_DATA

Set non-existent padding with AES-CBC
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC
set_padding:MBEDTLS_CIPHER_AES_128_CBC:-1:MBEDTLS_ERR_CIPHER_FEATURE_UNAVAILABLE

Set non-existent padding with CAMELLIA-CBC
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC
set_padding:MBEDTLS_CIPHER_CAMELLIA_128_CBC:-1:MBEDTLS_ERR_CIPHER_FEATURE_UNAVAILABLE

Set non-existent padding with DES-CBC
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC
set_padding:MBEDTLS_CIPHER_DES_CBC:-1:MBEDTLS_ERR_CIPHER_FEATURE_UNAVAILABLE

Check PKCS padding #1 (correct)
depends_on:MBEDTLS_CIPHER_PADDING_PKCS7
check_padding:MBEDTLS_PADDING_PKCS7:"DABBAD0004040404":0:4

Check PKCS padding #2 (correct)
depends_on:MBEDTLS_CIPHER_PADDING_PKCS7
check_padding:MBEDTLS_PADDING_PKCS7:"DABBAD0001":0:4

Check PKCS padding #3 (correct)
depends_on:MBEDTLS_CIPHER_PADDING_PKCS7
check_padding:MBEDTLS_PADDING_PKCS7:"DABBAD000101":0:5

Check PKCS padding #4 (correct)
depends_on:MBEDTLS_CIPHER_PADDING_PKCS7
check_padding:MBEDTLS_PADDING_PKCS7:"030303":0:0

Check PKCS padding #5 (null padding)
depends_on:MBEDTLS_CIPHER_PADDING_PKCS7
check_padding:MBEDTLS_PADDING_PKCS7:"DABBAD0000":MBEDTLS_ERR_CIPHER_INVALID_PADDING:0

Check PKCS padding #6 (too few padding bytes)
depends_on:MBEDTLS_CIPHER_PADDING_PKCS7
check_padding:MBEDTLS_PADDING_PKCS7:"DABBAD0002":MBEDTLS_ERR_CIPHER_INVALID_PADDING:0

Check PKCS padding #7 (non-uniform padding bytes #1)
depends_on:MBEDTLS_CIPHER_PADDING_PKCS7
check_padding:MBEDTLS_PADDING_PKCS7:"DABBAD00030203":MBEDTLS_ERR_CIPHER_INVALID_PADDING:0

Check PKCS padding #7 (non-uniform padding bytes #2)
depends_on:MBEDTLS_CIPHER_PADDING_PKCS7
check_padding:MBEDTLS_PADDING_PKCS7:"DABBAD00030103":MBEDTLS_ERR_CIPHER_INVALID_PADDING:0

Check PKCS padding #7 (non-uniform padding bytes #3)
depends_on:MBEDTLS_CIPHER_PADDING_PKCS7
check_padding:MBEDTLS_PADDING_PKCS7:"DABBAD00030703":MBEDTLS_ERR_CIPHER_INVALID_PADDING:0

Check PKCS padding #7 (non-uniform padding bytes #4)
depends_on:MBEDTLS_CIPHER_PADDING_PKCS7
check_padding:MBEDTLS_PADDING_PKCS7:"DABBAD00030b03":MBEDTLS_ERR_CIPHER_INVALID_PADDING:0

Check PKCS padding #7 (non-uniform padding bytes #5)
depends_on:MBEDTLS_CIPHER_PADDING_PKCS7
check_padding:MBEDTLS_PADDING_PKCS7:"DABBAD00031303":MBEDTLS_ERR_CIPHER_INVALID_PADDING:0

Check PKCS padding #7 (non-uniform padding bytes #6)
depends_on:MBEDTLS_CIPHER_PADDING_PKCS7
check_padding:MBEDTLS_PADDING_PKCS7:"DABBAD00032303":MBEDTLS_ERR_CIPHER_INVALID_PADDING:0

Check PKCS padding #7 (non-uniform padding bytes #7)
depends_on:MBEDTLS_CIPHER_PADDING_PKCS7
check_padding:MBEDTLS_PADDING_PKCS7:"DABBAD00034203":MBEDTLS_ERR_CIPHER_INVALID_PADDING:0

Check PKCS padding #7 (non-uniform padding bytes #8)
depends_on:MBEDTLS_CIPHER_PADDING_PKCS7
check_padding:MBEDTLS_PADDING_PKCS7:"DABBAD00038303":MBEDTLS_ERR_CIPHER_INVALID_PADDING:0

Check PKCS padding #7 (non-uniform padding bytes #9)
depends_on:MBEDTLS_CIPHER_PADDING_PKCS7
check_padding:MBEDTLS_PADDING_PKCS7:"DABBAD00020303":MBEDTLS_ERR_CIPHER_INVALID_PADDING:0

Check PKCS padding #7 (non-uniform padding bytes #10)
depends_on:MBEDTLS_CIPHER_PADDING_PKCS7
check_padding:MBEDTLS_PADDING_PKCS7:"DABBAD00010303":MBEDTLS_ERR_CIPHER_INVALID_PADDING:0

Check PKCS padding #7 (non-uniform padding bytes #11)
depends_on:MBEDTLS_CIPHER_PADDING_PKCS7
check_padding:MBEDTLS_PADDING_PKCS7:"DABBAD00070303":MBEDTLS_ERR_CIPHER_INVALID_PADDING:0

Check PKCS padding #7 (non-uniform padding bytes #12)
depends_on:MBEDTLS_CIPHER_PADDING_PKCS7
check_padding:MBEDTLS_PADDING_PKCS7:"DABBAD000b0303":MBEDTLS_ERR_CIPHER_INVALID_PADDING:0

Check PKCS padding #7 (non-uniform padding bytes #13)
depends_on:MBEDTLS_CIPHER_PADDING_PKCS7
check_padding:MBEDTLS_PADDING_PKCS7:"DABBAD00130303":MBEDTLS_ERR_CIPHER_INVALID_PADDING:0

Check PKCS padding #7 (non-uniform padding bytes #14)
depends_on:MBEDTLS_CIPHER_PADDING_PKCS7
check_padding:MBEDTLS_PADDING_PKCS7:"DABBAD00230303":MBEDTLS_ERR_CIPHER_INVALID_PADDING:0

Check PKCS padding #7 (non-uniform padding bytes #15)
depends_on:MBEDTLS_CIPHER_PADDING_PKCS7
check_padding:MBEDTLS_PADDING_PKCS7:"DABBAD00420303":MBEDTLS_ERR_CIPHER_INVALID_PADDING:0

Check PKCS padding #7 (non-uniform padding bytes #16)
depends_on:MBEDTLS_CIPHER_PADDING_PKCS7
check_padding:MBEDTLS_PADDING_PKCS7:"DABBAD00830303":MBEDTLS_ERR_CIPHER_INVALID_PADDING:0

Check PKCS padding #8 (overlong)
depends_on:MBEDTLS_CIPHER_PADDING_PKCS7
check_padding:MBEDTLS_PADDING_PKCS7:"040404":MBEDTLS_ERR_CIPHER_INVALID_PADDING:0

Check one and zeros padding #1 (correct)
depends_on:MBEDTLS_CIPHER_PADDING_ONE_AND_ZEROS
check_padding:MBEDTLS_PADDING_ONE_AND_ZEROS:"DABBAD0080":0:4

Check one and zeros padding #2 (correct)
depends_on:MBEDTLS_CIPHER_PADDING_ONE_AND_ZEROS
check_padding:MBEDTLS_PADDING_ONE_AND_ZEROS:"DABBAD008000":0:4

Check one and zeros padding #3 (correct)
depends_on:MBEDTLS_CIPHER_PADDING_ONE_AND_ZEROS
check_padding:MBEDTLS_PADDING_ONE_AND_ZEROS:"DABBAD00800000":0:4

Check one and zeros padding #4 (correct)
depends_on:MBEDTLS_CIPHER_PADDING_ONE_AND_ZEROS
check_padding:MBEDTLS_PADDING_ONE_AND_ZEROS:"DABBAD00808000":0:5

Check one and zeros padding #5 (correct)
depends_on:MBEDTLS_CIPHER_PADDING_ONE_AND_ZEROS
check_padding:MBEDTLS_PADDING_ONE_AND_ZEROS:"800000":0:0

Check one and zeros padding #6 (missing one)
depends_on:MBEDTLS_CIPHER_PADDING_ONE_AND_ZEROS
check_padding:MBEDTLS_PADDING_ONE_AND_ZEROS:"DABBAD0000":MBEDTLS_ERR_CIPHER_INVALID_PADDING:4

Check one and zeros padding #7 (overlong)
depends_on:MBEDTLS_CIPHER_PADDING_ONE_AND_ZEROS
check_padding:MBEDTLS_PADDING_ONE_AND_ZEROS:"0000000000":MBEDTLS_ERR_CIPHER_INVALID_PADDING:4

Check one and zeros padding #8 (last byte 0x80 | x)
depends_on:MBEDTLS_CIPHER_PADDING_ONE_AND_ZEROS
check_padding:MBEDTLS_PADDING_ONE_AND_ZEROS:"0000000082":MBEDTLS_ERR_CIPHER_INVALID_PADDING:4

Check zeros and len padding #1 (correct)
depends_on:MBEDTLS_CIPHER_PADDING_ZEROS_AND_LEN
check_padding:MBEDTLS_PADDING_ZEROS_AND_LEN:"DABBAD0001":0:4

Check zeros and len padding #2 (correct)
depends_on:MBEDTLS_CIPHER_PADDING_ZEROS_AND_LEN
check_padding:MBEDTLS_PADDING_ZEROS_AND_LEN:"DABBAD000002":0:4

Check zeros and len padding #3 (correct)
depends_on:MBEDTLS_CIPHER_PADDING_ZEROS_AND_LEN
check_padding:MBEDTLS_PADDING_ZEROS_AND_LEN:"DABBAD000003":0:3

Check zeros and len padding #4 (correct)
depends_on:MBEDTLS_CIPHER_PADDING_ZEROS_AND_LEN
check_padding:MBEDTLS_PADDING_ZEROS_AND_LEN:"000003":0:0

Check zeros and len padding #5 (overlong)
depends_on:MBEDTLS_CIPHER_PADDING_ZEROS_AND_LEN
check_padding:MBEDTLS_PADDING_ZEROS_AND_LEN:"000004":MBEDTLS_ERR_CIPHER_INVALID_PADDING:0

Check zeros and len padding #6 (not enough zeros)
depends_on:MBEDTLS_CIPHER_PADDING_ZEROS_AND_LEN
check_padding:MBEDTLS_PADDING_ZEROS_AND_LEN:"DABBAD000004":MBEDTLS_ERR_CIPHER_INVALID_PADDING:0

Check zeros padding #1 (correct)
depends_on:MBEDTLS_CIPHER_PADDING_ZEROS
check_padding:MBEDTLS_PADDING_ZEROS:"DABBAD00":0:3

Check zeros padding #2 (correct)
depends_on:MBEDTLS_CIPHER_PADDING_ZEROS
check_padding:MBEDTLS_PADDING_ZEROS:"DABBAD0000":0:3

Check zeros padding #3 (correct)
depends_on:MBEDTLS_CIPHER_PADDING_ZEROS
check_padding:MBEDTLS_PADDING_ZEROS:"DABBAD":0:3

Check zeros padding #4 (correct)
depends_on:MBEDTLS_CIPHER_PADDING_ZEROS
check_padding:MBEDTLS_PADDING_ZEROS:"000000":0:0

Check no padding #1 (correct by definition)
check_padding:MBEDTLS_PADDING_NONE:"DABBAD00":0:4

Check no padding #2 (correct by definition)
check_padding:MBEDTLS_PADDING_NONE:"DABBAD0001":0:5

Check no padding #3 (correct by definition)
check_padding:MBEDTLS_PADDING_NONE:"":0:0
