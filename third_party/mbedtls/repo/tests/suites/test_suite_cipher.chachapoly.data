Decrypt empty buffer
depends_on:MBEDTLS_CHACHAPOLY_C
dec_empty_buf:MBEDTLS_CIPHER_CHACHA20_POLY1305:0:0

ChaCha20+Poly1305 Encrypt and decrypt 0 bytes
depends_on:MBEDTLS_CHACHAPOLY_C
enc_dec_buf:MBEDTLS_CIPHER_CHACHA20_POLY1305:"CHACHA20-POLY1305":256:0:-1

ChaCha20+Poly1305 Encrypt and decrypt 1 bytes
depends_on:MBEDTLS_CHACHAPOLY_C
enc_dec_buf:MBEDTLS_CIPHER_CHACHA20_POLY1305:"CHACHA20-POLY1305":256:1:-1

ChaCha20+Poly1305 Encrypt and decrypt 2 bytes
depends_on:MBEDTLS_CHACHAPOLY_C
enc_dec_buf:MBEDTLS_CIPHER_CHACHA20_POLY1305:"CHACHA20-POLY1305":256:2:-1

Cha<PERSON>ha20+Poly1305 Encrypt and decrypt 7 bytes
depends_on:MBEDTLS_CHACHAPOLY_C
enc_dec_buf:MBEDTLS_CIPHER_CHACHA20_POLY1305:"CHACHA20-POLY1305":256:7:-1

ChaCha20+Poly1305 Encrypt and decrypt 8 bytes
depends_on:MBEDTLS_CHACHAPOLY_C
enc_dec_buf:MBEDTLS_CIPHER_CHACHA20_POLY1305:"CHACHA20-POLY1305":256:8:-1

ChaCha20+Poly1305 Encrypt and decrypt 9 bytes
depends_on:MBEDTLS_CHACHAPOLY_C
enc_dec_buf:MBEDTLS_CIPHER_CHACHA20_POLY1305:"CHACHA20-POLY1305":256:9:-1

ChaCha20+Poly1305 Encrypt and decrypt 15 bytes
depends_on:MBEDTLS_CHACHAPOLY_C
enc_dec_buf:MBEDTLS_CIPHER_CHACHA20_POLY1305:"CHACHA20-POLY1305":256:15:-1

ChaCha20+Poly1305 Encrypt and decrypt 16 bytes
depends_on:MBEDTLS_CHACHAPOLY_C
enc_dec_buf:MBEDTLS_CIPHER_CHACHA20_POLY1305:"CHACHA20-POLY1305":256:16:-1

ChaCha20+Poly1305 Encrypt and decrypt 17 bytes
depends_on:MBEDTLS_CHACHAPOLY_C
enc_dec_buf:MBEDTLS_CIPHER_CHACHA20_POLY1305:"CHACHA20-POLY1305":256:17:-1

ChaCha20+Poly1305 Encrypt and decrypt 31 bytes
depends_on:MBEDTLS_CHACHAPOLY_C
enc_dec_buf:MBEDTLS_CIPHER_CHACHA20_POLY1305:"CHACHA20-POLY1305":256:31:-1

ChaCha20+Poly1305 Encrypt and decrypt 32 bytes
depends_on:MBEDTLS_CHACHAPOLY_C
enc_dec_buf:MBEDTLS_CIPHER_CHACHA20_POLY1305:"CHACHA20-POLY1305":256:32:-1

ChaCha20+Poly1305 Encrypt and decrypt 33 bytes
depends_on:MBEDTLS_CHACHAPOLY_C
enc_dec_buf:MBEDTLS_CIPHER_CHACHA20_POLY1305:"CHACHA20-POLY1305":256:33:-1

ChaCha20+Poly1305 Encrypt and decrypt 47 bytes
depends_on:MBEDTLS_CHACHAPOLY_C
enc_dec_buf:MBEDTLS_CIPHER_CHACHA20_POLY1305:"CHACHA20-POLY1305":256:47:-1

ChaCha20+Poly1305 Encrypt and decrypt 48 bytes
depends_on:MBEDTLS_CHACHAPOLY_C
enc_dec_buf:MBEDTLS_CIPHER_CHACHA20_POLY1305:"CHACHA20-POLY1305":256:48:-1

ChaCha20+Poly1305 Encrypt and decrypt 49 bytes
depends_on:MBEDTLS_CHACHAPOLY_C
enc_dec_buf:MBEDTLS_CIPHER_CHACHA20_POLY1305:"CHACHA20-POLY1305":256:49:-1

ChaCha20+Poly1305 Encrypt and decrypt 0 bytes in multiple parts 1
depends_on:MBEDTLS_CHACHAPOLY_C
enc_dec_buf_multipart:MBEDTLS_CIPHER_CHACHA20_POLY1305:256:0:0:-1:0:0:0:0

ChaCha20+Poly1305 Encrypt and decrypt 1 bytes in multiple parts 1
depends_on:MBEDTLS_CHACHAPOLY_C
enc_dec_buf_multipart:MBEDTLS_CIPHER_CHACHA20_POLY1305:256:1:0:-1:1:0:1:0

ChaCha20+Poly1305 Encrypt and decrypt 1 bytes in multiple parts 2
depends_on:MBEDTLS_CHACHAPOLY_C
enc_dec_buf_multipart:MBEDTLS_CIPHER_CHACHA20_POLY1305:256:0:1:-1:0:1:0:1

ChaCha20+Poly1305 Encrypt and decrypt 16 bytes in multiple parts 1
depends_on:MBEDTLS_CHACHAPOLY_C
enc_dec_buf_multipart:MBEDTLS_CIPHER_CHACHA20_POLY1305:256:16:0:-1:16:0:16:0

ChaCha20+Poly1305 Encrypt and decrypt 16 bytes in multiple parts 2
depends_on:MBEDTLS_CHACHAPOLY_C
enc_dec_buf_multipart:MBEDTLS_CIPHER_CHACHA20_POLY1305:256:0:16:-1:0:16:0:16

ChaCha20+Poly1305 Encrypt and decrypt 16 bytes in multiple parts 3
depends_on:MBEDTLS_CHACHAPOLY_C
enc_dec_buf_multipart:MBEDTLS_CIPHER_CHACHA20_POLY1305:256:1:15:-1:1:15:1:15

ChaCha20+Poly1305 Encrypt and decrypt 16 bytes in multiple parts 4
depends_on:MBEDTLS_CHACHAPOLY_C
enc_dec_buf_multipart:MBEDTLS_CIPHER_CHACHA20_POLY1305:256:15:1:-1:15:1:15:1

ChaCha20+Poly1305 Encrypt and decrypt 22 bytes in multiple parts 1
depends_on:MBEDTLS_CHACHAPOLY_C
enc_dec_buf_multipart:MBEDTLS_CIPHER_CHACHA20_POLY1305:256:15:7:-1:15:7:15:7

ChaCha20+Poly1305 Encrypt and decrypt 22 bytes in multiple parts 2
depends_on:MBEDTLS_CHACHAPOLY_C
enc_dec_buf_multipart:MBEDTLS_CIPHER_CHACHA20_POLY1305:256:7:15:-1:7:15:7:15

ChaCha20+Poly1305 Encrypt and decrypt 22 bytes in multiple parts 3
depends_on:MBEDTLS_CHACHAPOLY_C
enc_dec_buf_multipart:MBEDTLS_CIPHER_CHACHA20_POLY1305:256:16:6:-1:16:6:16:6

ChaCha20+Poly1305 Encrypt and decrypt 22 bytes in multiple parts 4
depends_on:MBEDTLS_CHACHAPOLY_C
enc_dec_buf_multipart:MBEDTLS_CIPHER_CHACHA20_POLY1305:256:6:16:-1:6:16:6:16

ChaCha20+Poly1305 Encrypt and decrypt 32 bytes in multiple parts
depends_on:MBEDTLS_CHACHAPOLY_C
enc_dec_buf_multipart:MBEDTLS_CIPHER_CHACHA20_POLY1305:256:16:16:-1:16:16:16:16

ChaCha20+Poly1305 RFC 7539 Test Vector #1
depends_on:MBEDTLS_CHACHAPOLY_C
auth_crypt_tv:MBEDTLS_CIPHER_CHACHA20_POLY1305:"1c9240a5eb55d38af333888604f6b5f0473917c1402b80099dca5cbc207075c0":"000000000102030405060708":"f33388860000000000004e91":"64a0861575861af460f062c79be643bd5e805cfd345cf389f108670ac76c8cb24c6cfc18755d43eea09ee94e382d26b0bdb7b73c321b0100d4f03b7f355894cf332f830e710b97ce98c8a84abd0b948114ad176e008d33bd60f982b1ff37c8559797a06ef4f0ef61c186324e2b3506383606907b6a7c02b0f9f6157b53c867e4b9166c767b804d46a59b5216cde7a4e99040c5a40433225ee282a1b0a06c523eaf4534d7f83fa1155b0047718cbc546a0d072b04b3564eea1b422273f548271a0bb2316053fa76991955ebd63159434ecebb4e466dae5a1073a6727627097a1049e617d91d361094fa68f0ff77987130305beaba2eda04df997b714d6c6f2c29a6ad5cb4022b02709b":"eead9d67890cbb22392336fea1851f38":"":"496e7465726e65742d4472616674732061726520647261667420646f63756d656e74732076616c696420666f722061206d6178696d756d206f6620736978206d6f6e74687320616e64206d617920626520757064617465642c207265706c616365642c206f72206f62736f6c65746564206279206f7468657220646f63756d656e747320617420616e792074696d652e20497420697320696e617070726f70726961746520746f2075736520496e7465726e65742d447261667473206173207265666572656e6365206d6174657269616c206f7220746f2063697465207468656d206f74686572207468616e206173202fe2809c776f726b20696e2070726f67726573732e2fe2809d":0

ChaCha20+Poly1305 RFC 7539 Test Vector #1 Unauthentic (1st bit flipped)
depends_on:MBEDTLS_CHACHAPOLY_C
auth_crypt_tv:MBEDTLS_CIPHER_CHACHA20_POLY1305:"1c9240a5eb55d38af333888604f6b5f0473917c1402b80099dca5cbc207075c0":"000000000102030405060708":"f33388860000000000004e91":"64a0861575861af460f062c79be643bd5e805cfd345cf389f108670ac76c8cb24c6cfc18755d43eea09ee94e382d26b0bdb7b73c321b0100d4f03b7f355894cf332f830e710b97ce98c8a84abd0b948114ad176e008d33bd60f982b1ff37c8559797a06ef4f0ef61c186324e2b3506383606907b6a7c02b0f9f6157b53c867e4b9166c767b804d46a59b5216cde7a4e99040c5a40433225ee282a1b0a06c523eaf4534d7f83fa1155b0047718cbc546a0d072b04b3564eea1b422273f548271a0bb2316053fa76991955ebd63159434ecebb4e466dae5a1073a6727627097a1049e617d91d361094fa68f0ff77987130305beaba2eda04df997b714d6c6f2c29a6ad5cb4022b02709b":"6ead9d67890cbb22392336fea1851f38":"FAIL":"":0

Chacha20+Poly1305 RFC 7539 Test Vector #1 (streaming)
depends_on:MBEDTLS_CHACHAPOLY_C
decrypt_test_vec:MBEDTLS_CIPHER_CHACHA20_POLY1305:-1:"1c9240a5eb55d38af333888604f6b5f0473917c1402b80099dca5cbc207075c0":"000000000102030405060708":"64a0861575861af460f062c79be643bd5e805cfd345cf389f108670ac76c8cb24c6cfc18755d43eea09ee94e382d26b0bdb7b73c321b0100d4f03b7f355894cf332f830e710b97ce98c8a84abd0b948114ad176e008d33bd60f982b1ff37c8559797a06ef4f0ef61c186324e2b3506383606907b6a7c02b0f9f6157b53c867e4b9166c767b804d46a59b5216cde7a4e99040c5a40433225ee282a1b0a06c523eaf4534d7f83fa1155b0047718cbc546a0d072b04b3564eea1b422273f548271a0bb2316053fa76991955ebd63159434ecebb4e466dae5a1073a6727627097a1049e617d91d361094fa68f0ff77987130305beaba2eda04df997b714d6c6f2c29a6ad5cb4022b02709b":"496e7465726e65742d4472616674732061726520647261667420646f63756d656e74732076616c696420666f722061206d6178696d756d206f6620736978206d6f6e74687320616e64206d617920626520757064617465642c207265706c616365642c206f72206f62736f6c65746564206279206f7468657220646f63756d656e747320617420616e792074696d652e20497420697320696e617070726f70726961746520746f2075736520496e7465726e65742d447261667473206173207265666572656e6365206d6174657269616c206f7220746f2063697465207468656d206f74686572207468616e206173202fe2809c776f726b20696e2070726f67726573732e2fe2809d":"f33388860000000000004e91":"eead9d67890cbb22392336fea1851f38":0:0

ChaCha20+Poly1305 IV Length 0
depends_on:MBEDTLS_CHACHAPOLY_C
iv_len_validity:MBEDTLS_CIPHER_CHACHA20_POLY1305:"CHACHA20-POLY1305":0:MBEDTLS_ERR_CIPHER_BAD_INPUT_DATA

ChaCha20+Poly1305 IV Length 11
depends_on:MBEDTLS_CHACHAPOLY_C
iv_len_validity:MBEDTLS_CIPHER_CHACHA20_POLY1305:"CHACHA20-POLY1305":11:MBEDTLS_ERR_CIPHER_BAD_INPUT_DATA

ChaCha20+Poly1305 IV Length 12
depends_on:MBEDTLS_CHACHAPOLY_C
iv_len_validity:MBEDTLS_CIPHER_CHACHA20_POLY1305:"CHACHA20-POLY1305":12:0

ChaCha20+Poly1305 IV Length 13
depends_on:MBEDTLS_CHACHAPOLY_C
iv_len_validity:MBEDTLS_CIPHER_CHACHA20_POLY1305:"CHACHA20-POLY1305":13:MBEDTLS_ERR_CIPHER_BAD_INPUT_DATA

ChaCha20+Poly1305 IV Length 16
depends_on:MBEDTLS_CHACHAPOLY_C
iv_len_validity:MBEDTLS_CIPHER_CHACHA20_POLY1305:"CHACHA20-POLY1305":16:MBEDTLS_ERR_CIPHER_BAD_INPUT_DATA
