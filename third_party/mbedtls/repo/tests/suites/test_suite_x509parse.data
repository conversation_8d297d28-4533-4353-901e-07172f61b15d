X509 CRT information #1
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA1
x509_cert_info:"data_files/parse_input/server1.crt":"cert. version     \: 3\nserial number     \: 01\nissuer name       \: C=NL, O=PolarSSL, CN=PolarSSL Test CA\nsubject name      \: C=NL, O=PolarSSL, CN=PolarSSL Server 1\nissued  on        \: 2019-02-10 14\:44\:06\nexpires on        \: 2029-02-10 14\:44\:06\nsigned using      \: RSA with SHA1\nRSA key size      \: 2048 bits\nbasic constraints \: CA=false\n"

X509 CRT information #1 (DER)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA1
x509_cert_info:"data_files/parse_input/server1.crt.der":"cert. version     \: 3\nserial number     \: 01\nissuer name       \: C=NL, O=PolarSSL, CN=PolarSSL Test CA\nsubject name      \: C=NL, O=PolarSSL, CN=PolarSSL Server 1\nissued  on        \: 2019-02-10 14\:44\:06\nexpires on        \: 2029-02-10 14\:44\:06\nsigned using      \: RSA with SHA1\nRSA key size      \: 2048 bits\nbasic constraints \: CA=false\n"

X509 CRT information #2
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA1
x509_cert_info:"data_files/parse_input/server2.crt":"cert. version     \: 3\nserial number     \: 02\nissuer name       \: C=NL, O=PolarSSL, CN=PolarSSL Test CA\nsubject name      \: C=NL, O=PolarSSL, CN=localhost\nissued  on        \: 2019-02-10 14\:44\:06\nexpires on        \: 2029-02-10 14\:44\:06\nsigned using      \: RSA with SHA1\nRSA key size      \: 2048 bits\nbasic constraints \: CA=false\n"

X509 CRT information #2 (DER)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA1
x509_cert_info:"data_files/parse_input/server2.crt.der":"cert. version     \: 3\nserial number     \: 02\nissuer name       \: C=NL, O=PolarSSL, CN=PolarSSL Test CA\nsubject name      \: C=NL, O=PolarSSL, CN=localhost\nissued  on        \: 2019-02-10 14\:44\:06\nexpires on        \: 2029-02-10 14\:44\:06\nsigned using      \: RSA with SHA1\nRSA key size      \: 2048 bits\nbasic constraints \: CA=false\n"

X509 CRT information #3
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA1
x509_cert_info:"data_files/parse_input/test-ca.crt":"cert. version     \: 3\nserial number     \: 03\nissuer name       \: C=NL, O=PolarSSL, CN=PolarSSL Test CA\nsubject name      \: C=NL, O=PolarSSL, CN=PolarSSL Test CA\nissued  on        \: 2019-02-10 14\:44\:00\nexpires on        \: 2029-02-10 14\:44\:00\nsigned using      \: RSA with SHA1\nRSA key size      \: 2048 bits\nbasic constraints \: CA=true\n"

X509 CRT information #3 (DER)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA1
x509_cert_info:"data_files/parse_input/test-ca.crt.der":"cert. version     \: 3\nserial number     \: 03\nissuer name       \: C=NL, O=PolarSSL, CN=PolarSSL Test CA\nsubject name      \: C=NL, O=PolarSSL, CN=PolarSSL Test CA\nissued  on        \: 2019-02-10 14\:44\:00\nexpires on        \: 2029-02-10 14\:44\:00\nsigned using      \: RSA with SHA1\nRSA key size      \: 2048 bits\nbasic constraints \: CA=true\n"

X509 CRT information MD5 Digest
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_MD5
x509_cert_info:"data_files/parse_input/cert_md5.crt":"cert. version     \: 3\nserial number     \: 06\nissuer name       \: C=NL, O=PolarSSL, CN=PolarSSL Test CA\nsubject name      \: C=NL, O=PolarSSL, CN=PolarSSL Cert MD5\nissued  on        \: 2000-01-01 12\:12\:12\nexpires on        \: 2030-01-01 12\:12\:12\nsigned using      \: RSA with MD5\nRSA key size      \: 2048 bits\nbasic constraints \: CA=false\n"

X509 CRT information SHA1 Digest
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA1
x509_cert_info:"data_files/parse_input/cert_sha1.crt":"cert. version     \: 3\nserial number     \: 07\nissuer name       \: C=NL, O=PolarSSL, CN=PolarSSL Test CA\nsubject name      \: C=NL, O=PolarSSL, CN=PolarSSL Cert SHA1\nissued  on        \: 2019-02-10 14\:44\:06\nexpires on        \: 2029-02-10 14\:44\:06\nsigned using      \: RSA with SHA1\nRSA key size      \: 2048 bits\nbasic constraints \: CA=false\n"

X509 CRT information SHA224 Digest
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA224
x509_cert_info:"data_files/parse_input/cert_sha224.crt":"cert. version     \: 3\nserial number     \: 08\nissuer name       \: C=NL, O=PolarSSL, CN=PolarSSL Test CA\nsubject name      \: C=NL, O=PolarSSL, CN=PolarSSL Cert SHA224\nissued  on        \: 2019-02-10 14\:44\:06\nexpires on        \: 2029-02-10 14\:44\:06\nsigned using      \: RSA with SHA-224\nRSA key size      \: 2048 bits\nbasic constraints \: CA=false\n"

X509 CRT information SHA256 Digest
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509_cert_info:"data_files/parse_input/cert_sha256.crt":"cert. version     \: 3\nserial number     \: 09\nissuer name       \: C=NL, O=PolarSSL, CN=PolarSSL Test CA\nsubject name      \: C=NL, O=PolarSSL, CN=PolarSSL Cert SHA256\nissued  on        \: 2019-02-10 14\:44\:06\nexpires on        \: 2029-02-10 14\:44\:06\nsigned using      \: RSA with SHA-256\nRSA key size      \: 2048 bits\nbasic constraints \: CA=false\n"

X509 CRT information SHA384 Digest
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA384
x509_cert_info:"data_files/parse_input/cert_sha384.crt":"cert. version     \: 3\nserial number     \: 0A\nissuer name       \: C=NL, O=PolarSSL, CN=PolarSSL Test CA\nsubject name      \: C=NL, O=PolarSSL, CN=PolarSSL Cert SHA384\nissued  on        \: 2019-02-10 14\:44\:06\nexpires on        \: 2029-02-10 14\:44\:06\nsigned using      \: RSA with SHA-384\nRSA key size      \: 2048 bits\nbasic constraints \: CA=false\n"

X509 CRT information SHA512 Digest
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA512
x509_cert_info:"data_files/parse_input/cert_sha512.crt":"cert. version     \: 3\nserial number     \: 0B\nissuer name       \: C=NL, O=PolarSSL, CN=PolarSSL Test CA\nsubject name      \: C=NL, O=PolarSSL, CN=PolarSSL Cert SHA512\nissued  on        \: 2019-02-10 14\:44\:06\nexpires on        \: 2029-02-10 14\:44\:06\nsigned using      \: RSA with SHA-512\nRSA key size      \: 2048 bits\nbasic constraints \: CA=false\n"

X509 CRT information RSA-PSS, SHA1 Digest
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_X509_RSASSA_PSS_SUPPORT:MBEDTLS_MD_CAN_SHA1
x509_cert_info:"data_files/parse_input/server9.crt":"cert. version     \: 3\nserial number     \: 16\nissuer name       \: C=NL, O=PolarSSL, CN=PolarSSL Test CA\nsubject name      \: C=NL, O=PolarSSL, CN=localhost\nissued  on        \: 2014-01-20 13\:38\:16\nexpires on        \: 2024-01-18 13\:38\:16\nsigned using      \: RSASSA-PSS (SHA1, MGF1-SHA1, 0xEA)\nRSA key size      \: 1024 bits\nbasic constraints \: CA=false\n"

X509 CRT information RSA-PSS, SHA224 Digest
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_X509_RSASSA_PSS_SUPPORT:MBEDTLS_MD_CAN_SHA224
x509_cert_info:"data_files/parse_input/server9-sha224.crt":"cert. version     \: 3\nserial number     \: 17\nissuer name       \: C=NL, O=PolarSSL, CN=PolarSSL Test CA\nsubject name      \: C=NL, O=PolarSSL, CN=localhost\nissued  on        \: 2014-01-20 13\:57\:36\nexpires on        \: 2024-01-18 13\:57\:36\nsigned using      \: RSASSA-PSS (SHA224, MGF1-SHA224, 0xE2)\nRSA key size      \: 1024 bits\nbasic constraints \: CA=false\n"

X509 CRT information RSA-PSS, SHA256 Digest
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_X509_RSASSA_PSS_SUPPORT:MBEDTLS_MD_CAN_SHA256
x509_cert_info:"data_files/parse_input/server9-sha256.crt":"cert. version     \: 3\nserial number     \: 18\nissuer name       \: C=NL, O=PolarSSL, CN=PolarSSL Test CA\nsubject name      \: C=NL, O=PolarSSL, CN=localhost\nissued  on        \: 2014-01-20 13\:57\:45\nexpires on        \: 2024-01-18 13\:57\:45\nsigned using      \: RSASSA-PSS (SHA256, MGF1-SHA256, 0xDE)\nRSA key size      \: 1024 bits\nbasic constraints \: CA=false\n"

X509 CRT information RSA-PSS, SHA384 Digest
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_X509_RSASSA_PSS_SUPPORT:MBEDTLS_MD_CAN_SHA384
x509_cert_info:"data_files/parse_input/server9-sha384.crt":"cert. version     \: 3\nserial number     \: 19\nissuer name       \: C=NL, O=PolarSSL, CN=PolarSSL Test CA\nsubject name      \: C=NL, O=PolarSSL, CN=localhost\nissued  on        \: 2014-01-20 13\:57\:58\nexpires on        \: 2024-01-18 13\:57\:58\nsigned using      \: RSASSA-PSS (SHA384, MGF1-SHA384, 0xCE)\nRSA key size      \: 1024 bits\nbasic constraints \: CA=false\n"

X509 CRT information RSA-PSS, SHA512 Digest
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_X509_RSASSA_PSS_SUPPORT:MBEDTLS_MD_CAN_SHA512
x509_cert_info:"data_files/parse_input/server9-sha512.crt":"cert. version     \: 3\nserial number     \: 1A\nissuer name       \: C=NL, O=PolarSSL, CN=PolarSSL Test CA\nsubject name      \: C=NL, O=PolarSSL, CN=localhost\nissued  on        \: 2014-01-20 13\:58\:12\nexpires on        \: 2024-01-18 13\:58\:12\nsigned using      \: RSASSA-PSS (SHA512, MGF1-SHA512, 0xBE)\nRSA key size      \: 1024 bits\nbasic constraints \: CA=false\n"

X509 CRT information EC, SHA1 Digest
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_PK_CAN_ECDSA_SOME:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_MD_CAN_SHA1
x509_cert_info:"data_files/parse_input/server5-sha1.crt":"cert. version     \: 3\nserial number     \: 12\nissuer name       \: C=NL, O=PolarSSL, CN=Polarssl Test EC CA\nsubject name      \: C=NL, O=PolarSSL, CN=localhost\nissued  on        \: 2013-09-24 16\:21\:27\nexpires on        \: 2023-09-22 16\:21\:27\nsigned using      \: ECDSA with SHA1\nEC key size       \: 256 bits\nbasic constraints \: CA=false\n"

X509 CRT information EC, SHA224 Digest
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_PK_CAN_ECDSA_SOME:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_MD_CAN_SHA224
x509_cert_info:"data_files/parse_input/server5-sha224.crt":"cert. version     \: 3\nserial number     \: 13\nissuer name       \: C=NL, O=PolarSSL, CN=Polarssl Test EC CA\nsubject name      \: C=NL, O=PolarSSL, CN=localhost\nissued  on        \: 2013-09-24 16\:21\:27\nexpires on        \: 2023-09-22 16\:21\:27\nsigned using      \: ECDSA with SHA224\nEC key size       \: 256 bits\nbasic constraints \: CA=false\n"

X509 CRT information EC, SHA256 Digest
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_PK_CAN_ECDSA_SOME:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_MD_CAN_SHA256
x509_cert_info:"data_files/parse_input/server5.crt":"cert. version     \: 3\nserial number     \: 09\nissuer name       \: C=NL, O=PolarSSL, CN=Polarssl Test EC CA\nsubject name      \: C=NL, O=PolarSSL, CN=localhost\nissued  on        \: 2013-09-24 15\:52\:04\nexpires on        \: 2023-09-22 15\:52\:04\nsigned using      \: ECDSA with SHA256\nEC key size       \: 256 bits\nbasic constraints \: CA=false\n"

X509 CRT information EC, SHA384 Digest
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_PK_CAN_ECDSA_SOME:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_MD_CAN_SHA384
x509_cert_info:"data_files/parse_input/server5-sha384.crt":"cert. version     \: 3\nserial number     \: 14\nissuer name       \: C=NL, O=PolarSSL, CN=Polarssl Test EC CA\nsubject name      \: C=NL, O=PolarSSL, CN=localhost\nissued  on        \: 2013-09-24 16\:21\:27\nexpires on        \: 2023-09-22 16\:21\:27\nsigned using      \: ECDSA with SHA384\nEC key size       \: 256 bits\nbasic constraints \: CA=false\n"

X509 CRT information EC, SHA512 Digest
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_PK_CAN_ECDSA_SOME:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_MD_CAN_SHA512
x509_cert_info:"data_files/parse_input/server5-sha512.crt":"cert. version     \: 3\nserial number     \: 15\nissuer name       \: C=NL, O=PolarSSL, CN=Polarssl Test EC CA\nsubject name      \: C=NL, O=PolarSSL, CN=localhost\nissued  on        \: 2013-09-24 16\:21\:27\nexpires on        \: 2023-09-22 16\:21\:27\nsigned using      \: ECDSA with SHA512\nEC key size       \: 256 bits\nbasic constraints \: CA=false\n"

X509 CRT information EC, SHA256 Digest, hardware module name SAN
depends_on:MBEDTLS_PK_CAN_ECDSA_SOME:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_MD_CAN_SHA256
x509_cert_info:"data_files/parse_input/server5-othername.crt.der":"cert. version     \: 3\nserial number     \: 4D\nissuer name       \: C=UK, O=Mbed TLS, CN=Mbed TLS othername SAN\nsubject name      \: C=UK, O=Mbed TLS, CN=Mbed TLS othername SAN\nissued  on        \: 2023-06-20 09\:04\:43\nexpires on        \: 2033-06-17 09\:04\:43\nsigned using      \: ECDSA with SHA256\nEC key size       \: 256 bits\nsubject alt name  \:\n    otherName \:\n        hardware module name \:\n            hardware type          \: *******.********\n            hardware serial number \: 313233343536\n"

X509 CRT information EC, SHA256 Digest, binary hardware module name SAN
depends_on:MBEDTLS_PK_CAN_ECDSA_SOME:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_MD_CAN_SHA256
x509_cert_info:"data_files/parse_input/server5-nonprintable_othername.crt.der":"cert. version     \: 3\nserial number     \: 4D\nissuer name       \: C=UK, O=Mbed TLS, CN=Mbed TLS non-printable othername SAN\nsubject name      \: C=UK, O=Mbed TLS, CN=Mbed TLS non-printable othername SAN\nissued  on        \: 2023-06-20 09\:49\:20\nexpires on        \: 2033-06-17 09\:49\:20\nsigned using      \: ECDSA with SHA256\nEC key size       \: 256 bits\nsubject alt name  \:\n    otherName \:\n        hardware module name \:\n            hardware type          \: *******.********\n            hardware serial number \: 3132338081008180333231\n"

X509 CRT information EC, SHA256 Digest, directoryName SAN
depends_on:MBEDTLS_PK_CAN_ECDSA_SOME:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_MD_CAN_SHA256
x509_cert_info:"data_files/parse_input/server5-directoryname.crt.der":"cert. version     \: 3\nserial number     \: 4D\nissuer name       \: C=UK, O=Mbed TLS, CN=Mbed TLS directoryName SAN\nsubject name      \: C=UK, O=Mbed TLS, CN=Mbed TLS directoryName SAN\nissued  on        \: 2023-01-10 16\:59\:29\nexpires on        \: 2033-01-07 16\:59\:29\nsigned using      \: ECDSA with SHA256\nEC key size       \: 256 bits\nsubject alt name  \:\n    directoryName \: C=UK, O=Mbed TLS, CN=Mbed TLS directoryName SAN\n"

X509 CRT information EC, SHA256 Digest, two directoryName SANs
depends_on:MBEDTLS_PK_CAN_ECDSA_SOME:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_MD_CAN_SHA256
x509_cert_info:"data_files/parse_input/server5-two-directorynames.crt.der":"cert. version     \: 3\nserial number     \: 4D\nissuer name       \: C=UK, O=Mbed TLS, CN=Mbed TLS directoryName SAN\nsubject name      \: C=UK, O=Mbed TLS, CN=Mbed TLS directoryName SAN\nissued  on        \: 2023-01-12 10\:34\:11\nexpires on        \: 2033-01-09 10\:34\:11\nsigned using      \: ECDSA with SHA256\nEC key size       \: 256 bits\nsubject alt name  \:\n    directoryName \: C=UK, O=Mbed TLS, CN=Mbed TLS directoryName SAN\n    directoryName \: O=MALFORM_ME\n"

X509 CRT information EC, SHA256 Digest, Wisun Fan device
depends_on:MBEDTLS_PK_CAN_ECDSA_SOME:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_MD_CAN_SHA256
x509_cert_info:"data_files/parse_input/server5-fan.crt.der":"cert. version     \: 3\nserial number     \: 4D\nissuer name       \: C=UK, O=Mbed TLS, CN=Mbed TLS FAN\nsubject name      \: C=UK, O=Mbed TLS, CN=Mbed TLS FAN\nissued  on        \: 2023-06-20 09\:49\:35\nexpires on        \: 2033-06-17 09\:49\:35\nsigned using      \: ECDSA with SHA256\nEC key size       \: 256 bits\next key usage     \: Wi-SUN Alliance Field Area Network (FAN)\n"

X509 CRT information, NS Cert Type
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA1
x509_cert_info:"data_files/parse_input/server1.cert_type.crt":"cert. version     \: 3\nserial number     \: 01\nissuer name       \: C=NL, O=PolarSSL, CN=PolarSSL Test CA\nsubject name      \: C=NL, O=PolarSSL, CN=PolarSSL Server 1\nissued  on        \: 2019-02-10 14\:44\:06\nexpires on        \: 2029-02-10 14\:44\:06\nsigned using      \: RSA with SHA1\nRSA key size      \: 2048 bits\nbasic constraints \: CA=false\ncert. type        \: SSL Server\n"

X509 CRT information, Key Usage
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA1
x509_cert_info:"data_files/parse_input/server1.key_usage.crt":"cert. version     \: 3\nserial number     \: 01\nissuer name       \: C=NL, O=PolarSSL, CN=PolarSSL Test CA\nsubject name      \: C=NL, O=PolarSSL, CN=PolarSSL Server 1\nissued  on        \: 2019-02-10 14\:44\:06\nexpires on        \: 2029-02-10 14\:44\:06\nsigned using      \: RSA with SHA1\nRSA key size      \: 2048 bits\nbasic constraints \: CA=false\nkey usage         \: Digital Signature, Non Repudiation, Key Encipherment\n"

X509 CRT information, Key Usage with decipherOnly
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA1
x509_cert_info:"data_files/parse_input/keyUsage.decipherOnly.crt":"cert. version     \: 3\nserial number     \: 9B\:13\:CE\:4C\:A5\:6F\:DE\:52\nissuer name       \: C=GB, L=Cambridge, O=Default Company Ltd\nsubject name      \: C=GB, L=Cambridge, O=Default Company Ltd\nissued  on        \: 2015-05-12 10\:36\:55\nexpires on        \: 2018-05-11 10\:36\:55\nsigned using      \: RSA with SHA1\nRSA key size      \: 1024 bits\nbasic constraints \: CA=false\nkey usage         \: Digital Signature, Non Repudiation, Key Encipherment, Decipher Only\n"

X509 CRT information, Subject Alt Name
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509_cert_info:"data_files/parse_input/cert_example_multi.crt":"cert. version     \: 3\nserial number     \: 11\nissuer name       \: C=NL, O=PolarSSL, CN=PolarSSL Test CA\nsubject name      \: C=NL, O=PolarSSL, CN=www.example.com\nissued  on        \: 2019-07-10 11\:27\:52\nexpires on        \: 2029-07-10 11\:27\:52\nsigned using      \: RSA with SHA-256\nRSA key size      \: 1024 bits\nsubject alt name  \:\n    dNSName \: example.com\n    dNSName \: example.net\n    dNSName \: *.example.org\n"

X509 CRT information, Multiple different Subject Alt Name
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_PK_CAN_ECDSA_SOME:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_MD_CAN_SHA256
x509_cert_info:"data_files/parse_input/multiple_san.crt":"cert. version     \: 3\nserial number     \: 04\nissuer name       \: C=UK, O=Mbed TLS, CN=Mbed TLS multiple othername SAN\nsubject name      \: C=UK, O=Mbed TLS, CN=Mbed TLS multiple othername SAN\nissued  on        \: 2019-04-22 16\:10\:48\nexpires on        \: 2029-04-19 16\:10\:48\nsigned using      \: ECDSA with SHA256\nEC key size       \: 256 bits\nsubject alt name  \:\n    dNSName \: example.com\n    otherName \:\n        hardware module name \:\n            hardware type          \: *******.********\n            hardware serial number \: 313233343536\n    dNSName \: example.net\n    dNSName \: *.example.org\n"

X509 CRT information, Subject Alt Name + Key Usage
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA1
x509_cert_info:"data_files/parse_input/cert_example_multi_nocn.crt":"cert. version     \: 3\nserial number     \: F7\:C6\:7F\:F8\:E9\:A9\:63\:F9\nissuer name       \: C=NL\nsubject name      \: C=NL\nissued  on        \: 2014-01-22 10\:04\:33\nexpires on        \: 2024-01-22 10\:04\:33\nsigned using      \: RSA with SHA1\nRSA key size      \: 1024 bits\nbasic constraints \: CA=false\nsubject alt name  \:\n    dNSName \: www.shotokan-braunschweig.de\n    dNSName \: www.massimo-abate.eu\n    iPAddress \: ***********\n    iPAddress \: **************\nkey usage         \: Digital Signature, Non Repudiation, Key Encipherment\n"

X509 CRT information, Subject Alt Name with uniformResourceIdentifier
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509_cert_info:"data_files/parse_input/rsa_single_san_uri.crt.der":"cert. version     \: 3\nserial number     \: 6F\:75\:EB\:E9\:6D\:25\:BC\:88\:82\:62\:A3\:E0\:68\:A7\:37\:3B\:EC\:75\:8F\:9C\nissuer name       \: C=UK, O=Mbed TLS, CN=Mbed TLS URI SAN\nsubject name      \: C=UK, O=Mbed TLS, CN=Mbed TLS URI SAN\nissued  on        \: 2023-02-14 10\:38\:05\nexpires on        \: 2043-02-09 10\:38\:05\nsigned using      \: RSA with SHA-256\nRSA key size      \: 2048 bits\nbasic constraints \: CA=false\nsubject alt name  \:\n    uniformResourceIdentifier \: urn\:example.com\:5ff40f78-9210-494f-8206-c2c082f0609c\nkey usage         \: Digital Signature, Non Repudiation, Key Encipherment\n"

X509 CRT information, Subject Alt Name with two uniformResourceIdentifiers
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509_cert_info:"data_files/parse_input/rsa_multiple_san_uri.crt.der":"cert. version     \: 3\nserial number     \: 08\:E2\:93\:18\:91\:26\:D8\:46\:88\:90\:10\:4F\:B5\:86\:CB\:C4\:78\:E6\:EA\:0D\nissuer name       \: C=UK, O=Mbed TLS, CN=Mbed TLS URI SAN\nsubject name      \: C=UK, O=Mbed TLS, CN=Mbed TLS URI SAN\nissued  on        \: 2023-02-14 10\:37\:50\nexpires on        \: 2043-02-09 10\:37\:50\nsigned using      \: RSA with SHA-256\nRSA key size      \: 2048 bits\nbasic constraints \: CA=false\nsubject alt name  \:\n    uniformResourceIdentifier \: urn\:example.com\:5ff40f78-9210-494f-8206-c2c082f0609c\n    uniformResourceIdentifier \: urn\:example.com\:5ff40f78-9210-494f-8206-abcde1234567\nkey usage         \: Digital Signature, Non Repudiation, Key Encipherment\n"

X509 CRT information, RSA Certificate Policy any
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509_cert_info:"data_files/parse_input/test-ca-any_policy.crt":"cert. version     \: 3\nserial number     \: 00\nissuer name       \: C=NL, O=PolarSSL, CN=PolarSSL Test CA\nsubject name      \: C=NL, O=PolarSSL, CN=PolarSSL Test CA\nissued  on        \: 2019-03-21 16\:40\:59\nexpires on        \: 2029-03-21 16\:40\:59\nsigned using      \: RSA with SHA-256\nRSA key size      \: 2048 bits\nbasic constraints \: CA=true\ncertificate policies \: Any Policy\n"

X509 CRT information, ECDSA Certificate Policy any
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_PK_CAN_ECDSA_SOME:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_MD_CAN_SHA256
x509_cert_info:"data_files/parse_input/test-ca-any_policy_ec.crt":"cert. version     \: 3\nserial number     \: 00\nissuer name       \: C=NL, O=PolarSSL, CN=Polarssl Test EC CA\nsubject name      \: C=NL, O=PolarSSL, CN=Polarssl Test EC CA\nissued  on        \: 2019-03-25 09\:02\:45\nexpires on        \: 2029-03-25 09\:02\:45\nsigned using      \: ECDSA with SHA256\nEC key size       \: 384 bits\nbasic constraints \: CA=true\ncertificate policies \: Any Policy\n"

X509 CRT information, RSA Certificate Policy any with qualifier
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509_cert_info:"data_files/parse_input/test-ca-any_policy_with_qualifier.crt":"cert. version     \: 3\nserial number     \: 00\nissuer name       \: C=NL, O=PolarSSL, CN=PolarSSL Test CA\nsubject name      \: C=NL, O=PolarSSL, CN=PolarSSL Test CA\nissued  on        \: 2019-04-28 13\:14\:31\nexpires on        \: 2029-04-28 13\:14\:31\nsigned using      \: RSA with SHA-256\nRSA key size      \: 2048 bits\nbasic constraints \: CA=true\ncertificate policies \: Any Policy\n"

X509 CRT information, ECDSA Certificate Policy any with qualifier
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_PK_CAN_ECDSA_SOME:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_MD_CAN_SHA256
x509_cert_info:"data_files/parse_input/test-ca-any_policy_with_qualifier_ec.crt":"cert. version     \: 3\nserial number     \: 00\nissuer name       \: C=NL, O=PolarSSL, CN=Polarssl Test EC CA\nsubject name      \: C=NL, O=PolarSSL, CN=Polarssl Test EC CA\nissued  on        \: 2019-04-28 10\:16\:05\nexpires on        \: 2029-04-28 10\:16\:05\nsigned using      \: ECDSA with SHA256\nEC key size       \: 384 bits\nbasic constraints \: CA=true\ncertificate policies \: Any Policy\n"

X509 CRT information, RSA Certificate multiple Policies
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509_cert_info:"data_files/parse_input/test-ca-multi_policy.crt":"cert. version     \: 3\nserial number     \: 00\nissuer name       \: C=NL, O=PolarSSL, CN=PolarSSL Test CA\nsubject name      \: C=NL, O=PolarSSL, CN=PolarSSL Test CA\nissued  on        \: 2019-04-28 12\:59\:19\nexpires on        \: 2029-04-28 12\:59\:19\nsigned using      \: RSA with SHA-256\nRSA key size      \: 2048 bits\nbasic constraints \: CA=true\ncertificate policies \: ???, Any Policy\n"

X509 CRT information, ECDSA Certificate multiple Policies
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_PK_CAN_ECDSA_SOME:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_MD_CAN_SHA256
x509_cert_info:"data_files/parse_input/test-ca-multi_policy_ec.crt":"cert. version     \: 3\nserial number     \: 00\nissuer name       \: C=NL, O=PolarSSL, CN=Polarssl Test EC CA\nsubject name      \: C=NL, O=PolarSSL, CN=Polarssl Test EC CA\nissued  on        \: 2019-04-28 12\:59\:51\nexpires on        \: 2029-04-28 12\:59\:51\nsigned using      \: ECDSA with SHA256\nEC key size       \: 384 bits\nbasic constraints \: CA=true\ncertificate policies \: ???, Any Policy\n"

X509 CRT information, RSA Certificate unsupported policy
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509_cert_info:"data_files/parse_input/test-ca-unsupported_policy.crt":"cert. version     \: 3\nserial number     \: 00\nissuer name       \: C=NL, O=PolarSSL, CN=PolarSSL Test CA\nsubject name      \: C=NL, O=PolarSSL, CN=PolarSSL Test CA\nissued  on        \: 2019-04-28 13\:00\:13\nexpires on        \: 2029-04-28 13\:00\:13\nsigned using      \: RSA with SHA-256\nRSA key size      \: 2048 bits\nbasic constraints \: CA=true\ncertificate policies \: ???\n"

X509 CRT information, ECDSA Certificate unsupported policy
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_PK_CAN_ECDSA_SOME:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_MD_CAN_SHA256
x509_cert_info:"data_files/parse_input/test-ca-unsupported_policy_ec.crt":"cert. version     \: 3\nserial number     \: 00\nissuer name       \: C=NL, O=PolarSSL, CN=Polarssl Test EC CA\nsubject name      \: C=NL, O=PolarSSL, CN=Polarssl Test EC CA\nissued  on        \: 2019-04-28 13\:00\:19\nexpires on        \: 2029-04-28 13\:00\:19\nsigned using      \: ECDSA with SHA256\nEC key size       \: 384 bits\nbasic constraints \: CA=true\ncertificate policies \: ???\n"

X509 CRT information, Key Usage + Extended Key Usage
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509_cert_info:"data_files/parse_input/server1.ext_ku.crt":"cert. version     \: 3\nserial number     \: 21\nissuer name       \: C=NL, O=PolarSSL, CN=PolarSSL Test CA\nsubject name      \: C=NL, O=PolarSSL, CN=PolarSSL Server 1\nissued  on        \: 2014-04-01 14\:44\:43\nexpires on        \: 2024-03-29 14\:44\:43\nsigned using      \: RSA with SHA-256\nRSA key size      \: 2048 bits\nbasic constraints \: CA=false\nkey usage         \: Digital Signature, Non Repudiation, Key Encipherment\next key usage     \: TLS Web Server Authentication\n"

X509 CRT information RSA signed by EC
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256:MBEDTLS_PK_CAN_ECDSA_SOME
x509_cert_info:"data_files/parse_input/server4.crt":"cert. version     \: 3\nserial number     \: 08\nissuer name       \: C=NL, O=PolarSSL, CN=Polarssl Test EC CA\nsubject name      \: C=NL, O=PolarSSL, CN=localhost\nissued  on        \: 2013-09-24 15\:52\:04\nexpires on        \: 2023-09-22 15\:52\:04\nsigned using      \: ECDSA with SHA256\nRSA key size      \: 2048 bits\nbasic constraints \: CA=false\n"

X509 CRT information EC signed by RSA
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_PK_CAN_ECDSA_SOME:MBEDTLS_ECP_HAVE_SECP192R1:MBEDTLS_MD_CAN_SHA1:MBEDTLS_RSA_C
x509_cert_info:"data_files/parse_input/server3.crt":"cert. version     \: 3\nserial number     \: 0D\nissuer name       \: C=NL, O=PolarSSL, CN=PolarSSL Test CA\nsubject name      \: C=NL, O=PolarSSL, CN=localhost\nissued  on        \: 2013-08-09 09\:17\:03\nexpires on        \: 2023-08-07 09\:17\:03\nsigned using      \: RSA with SHA1\nEC key size       \: 192 bits\nbasic constraints \: CA=false\n"

X509 CRT information Bitstring in subject name
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA1
x509_cert_info:"data_files/parse_input/bitstring-in-dn.pem":"cert. version     \: 3\nserial number     \: 02\nissuer name       \: CN=Test CA 01, ST=Ecnivorp, C=XX, emailAddress=<EMAIL>, O=Test CA Authority\nsubject name      \: C=XX, O=tca, ST=Ecnivorp, OU=TCA, CN=Client, emailAddress=<EMAIL>, serialNumber=7101012255, uniqueIdentifier=#030B0037313031303132323535\nissued  on        \: 2015-03-11 12\:06\:51\nexpires on        \: 2025-03-08 12\:06\:51\nsigned using      \: RSA with SHA1\nRSA key size      \: 2048 bits\nbasic constraints \: CA=false\nsubject alt name  \:\n    rfc822Name \: <EMAIL>\next key usage     \: TLS Web Client Authentication\n"

X509 CRT information Non-ASCII string in issuer name and subject name
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509_cert_info:"data_files/parse_input/non-ascii-string-in-issuer.crt":"cert. version     \: 3\nserial number     \: 05\:E6\:53\:E7\:1B\:74\:F0\:B5\:D3\:84\:6D\:0C\:6D\:DC\:FA\:3F\:A4\:5A\:2B\:E0\nissuer name       \: C=JP, ST=Tokyo, O=\\C3\\A3\\C2\\83\\C2\\86\\C3\\A3\\C2\\82\\C2\\B9\\C3\\A3\\C2\\83\\C2\\88 Ltd, CN=\\C3\\A3\\C2\\83\\C2\\86\\C3\\A3\\C2\\82\\C2\\B9\\C3\\A3\\C2\\83\\C2\\88 CA\nsubject name      \: C=JP, ST=Tokyo, O=\\C3\\A3\\C2\\83\\C2\\86\\C3\\A3\\C2\\82\\C2\\B9\\C3\\A3\\C2\\83\\C2\\88 Ltd, CN=\\C3\\A3\\C2\\83\\C2\\86\\C3\\A3\\C2\\82\\C2\\B9\\C3\\A3\\C2\\83\\C2\\88 CA\nissued  on        \: 2020-05-20 16\:17\:23\nexpires on        \: 2020-06-19 16\:17\:23\nsigned using      \: RSA with SHA-256\nRSA key size      \: 2048 bits\nbasic constraints \: CA=true\n"

X509 CRT information Parsing IPv4 and IPv6 IP names
depends_on:MBEDTLS_PK_CAN_ECDSA_VERIFY:MBEDTLS_MD_CAN_SHA256:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_RSA_C
x509_cert_info:"data_files/server5-tricky-ip-san.crt.der":"cert. version     \: 3\nserial number     \: 4D\nissuer name       \: C=UK, O=Mbed TLS, CN=Mbed TLS Tricky IP SAN\nsubject name      \: C=UK, O=Mbed TLS, CN=Mbed TLS Tricky IP SAN\nissued  on        \: 2023-06-05 11\:30\:36\nexpires on        \: 2033-06-02 11\:30\:36\nsigned using      \: ECDSA with SHA256\nEC key size       \: 256 bits\nsubject alt name  \:\n    iPAddress \: ************\n    iPAddress \: 6162\:6364\:2E65\:7861\:6D70\:6C65\:2E63\:6F6D\n"

X509 SAN parsing otherName
depends_on:MBEDTLS_PK_CAN_ECDSA_SOME:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_MD_CAN_SHA256
x509_parse_san:"data_files/parse_input/server5-othername.crt.der":"type \: 0\notherName \: hardware module name \: hardware type \: *******.********, hardware serial number \: 313233343536\n":0

X509 SAN parsing binary otherName
depends_on:MBEDTLS_PK_CAN_ECDSA_SOME:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_MD_CAN_SHA256
x509_parse_san:"data_files/parse_input/server5-nonprintable_othername.crt.der":"type \: 0\notherName \: hardware module name \: hardware type \: *******.********, hardware serial number \: 3132338081008180333231\n":0

X509 SAN parsing directoryName
depends_on:MBEDTLS_PK_CAN_ECDSA_SOME:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_MD_CAN_SHA256
x509_parse_san:"data_files/parse_input/server5-directoryname.crt.der":"type \: 4\ndirectoryName \: C=UK, O=Mbed TLS, CN=Mbed TLS directoryName SAN\n":0

X509 SAN parsing directoryName, seq malformed
depends_on:MBEDTLS_PK_CAN_ECDSA_SOME:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_MD_CAN_SHA256
x509_parse_san:"data_files/parse_input/server5-directoryname-seq-malformed.crt.der":"":MBEDTLS_ERR_ASN1_UNEXPECTED_TAG

X509 SAN parsing two directoryNames, second DN OID malformed
depends_on:MBEDTLS_PK_CAN_ECDSA_SOME:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_MD_CAN_SHA256
x509_parse_san:"data_files/parse_input/server5-second-directoryname-oid-malformed.crt.der":"":MBEDTLS_ERR_X509_INVALID_NAME + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 SAN parsing dNSName
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509_parse_san:"data_files/parse_input/cert_example_multi.crt":"type \: 2\ndNSName \: example.com\ntype \: 2\ndNSName \: example.net\ntype \: 2\ndNSName \: *.example.org\n":0

X509 SAN parsing  Multiple different types
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_PK_CAN_ECDSA_SOME:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_MD_CAN_SHA256
x509_parse_san:"data_files/parse_input/multiple_san.crt":"type \: 2\ndNSName \: example.com\ntype \: 0\notherName \: hardware module name \: hardware type \: *******.********, hardware serial number \: 313233343536\ntype \: 2\ndNSName \: example.net\ntype \: 2\ndNSName \: *.example.org\n":0

X509 SAN parsing, no subject alt name
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256:MBEDTLS_PK_CAN_ECDSA_SOME
x509_parse_san:"data_files/parse_input/server4.crt":"":0

X509 SAN parsing, unsupported otherName name
depends_on:MBEDTLS_PK_CAN_ECDSA_SOME:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_MD_CAN_SHA256
x509_parse_san:"data_files/parse_input/server5-unsupported_othername.crt.der":"":0

X509 SAN parsing rfc822Name
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509_parse_san:"data_files/parse_input/test_cert_rfc822name.crt.der":"type \: 1\nrfc822Name \: <EMAIL>\ntype \: 1\nrfc822Name \: <EMAIL>\n":0

X509 CRT information Parsing IP (invalid data)
depends_on:MBEDTLS_PK_CAN_ECDSA_VERIFY:MBEDTLS_MD_CAN_SHA256:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_RSA_C
x509_parse_san:"data_files/server5-tricky-ip-san-malformed-len.crt.der":"":MBEDTLS_ERR_X509_BAD_INPUT_DATA

X509 CRL information #1
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_MD_CAN_SHA1:MBEDTLS_RSA_C:!MBEDTLS_X509_REMOVE_INFO
mbedtls_x509_crl_info:"data_files/parse_input/crl_expired.pem":"CRL version   \: 1\nissuer name   \: C=NL, O=PolarSSL, CN=PolarSSL Test CA\nthis update   \: 2011-02-20 10\:24\:19\nnext update   \: 2011-02-20 11\:24\:19\nRevoked certificates\:\nserial number\: 01 revocation date\: 2011-02-12 14\:44\:07\nserial number\: 03 revocation date\: 2011-02-12 14\:44\:07\nsigned using  \: RSA with SHA1\n"

X509 CRL Information MD5 Digest
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_MD_CAN_MD5:MBEDTLS_RSA_C:!MBEDTLS_X509_REMOVE_INFO
mbedtls_x509_crl_info:"data_files/parse_input/crl_md5.pem":"CRL version   \: 1\nissuer name   \: C=NL, O=PolarSSL, CN=PolarSSL Test CA\nthis update   \: 2011-02-12 14\:44\:07\nnext update   \: 2011-04-13 14\:44\:07\nRevoked certificates\:\nserial number\: 01 revocation date\: 2011-02-12 14\:44\:07\nserial number\: 03 revocation date\: 2011-02-12 14\:44\:07\nsigned using  \: RSA with MD5\n"

X509 CRL Information SHA1 Digest
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_MD_CAN_SHA1:MBEDTLS_RSA_C:!MBEDTLS_X509_REMOVE_INFO
mbedtls_x509_crl_info:"data_files/parse_input/crl_sha1.pem":"CRL version   \: 1\nissuer name   \: C=NL, O=PolarSSL, CN=PolarSSL Test CA\nthis update   \: 2011-02-12 14\:44\:07\nnext update   \: 2011-04-13 14\:44\:07\nRevoked certificates\:\nserial number\: 01 revocation date\: 2011-02-12 14\:44\:07\nserial number\: 03 revocation date\: 2011-02-12 14\:44\:07\nsigned using  \: RSA with SHA1\n"

X509 CRL Information SHA224 Digest
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_MD_CAN_SHA224:MBEDTLS_RSA_C:!MBEDTLS_X509_REMOVE_INFO
mbedtls_x509_crl_info:"data_files/parse_input/crl_sha224.pem":"CRL version   \: 1\nissuer name   \: C=NL, O=PolarSSL, CN=PolarSSL Test CA\nthis update   \: 2011-02-12 14\:44\:07\nnext update   \: 2011-04-13 14\:44\:07\nRevoked certificates\:\nserial number\: 01 revocation date\: 2011-02-12 14\:44\:07\nserial number\: 03 revocation date\: 2011-02-12 14\:44\:07\nsigned using  \: RSA with SHA-224\n"

X509 CRL Information SHA256 Digest
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_MD_CAN_SHA256:MBEDTLS_RSA_C:!MBEDTLS_X509_REMOVE_INFO
mbedtls_x509_crl_info:"data_files/parse_input/crl_sha256.pem":"CRL version   \: 1\nissuer name   \: C=NL, O=PolarSSL, CN=PolarSSL Test CA\nthis update   \: 2011-02-12 14\:44\:07\nnext update   \: 2011-04-13 14\:44\:07\nRevoked certificates\:\nserial number\: 01 revocation date\: 2011-02-12 14\:44\:07\nserial number\: 03 revocation date\: 2011-02-12 14\:44\:07\nsigned using  \: RSA with SHA-256\n"

X509 CRL Information SHA384 Digest
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_MD_CAN_SHA384:MBEDTLS_RSA_C:!MBEDTLS_X509_REMOVE_INFO
mbedtls_x509_crl_info:"data_files/parse_input/crl_sha384.pem":"CRL version   \: 1\nissuer name   \: C=NL, O=PolarSSL, CN=PolarSSL Test CA\nthis update   \: 2011-02-12 14\:44\:07\nnext update   \: 2011-04-13 14\:44\:07\nRevoked certificates\:\nserial number\: 01 revocation date\: 2011-02-12 14\:44\:07\nserial number\: 03 revocation date\: 2011-02-12 14\:44\:07\nsigned using  \: RSA with SHA-384\n"

X509 CRL Information SHA512 Digest
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_MD_CAN_SHA512:MBEDTLS_RSA_C:!MBEDTLS_X509_REMOVE_INFO
mbedtls_x509_crl_info:"data_files/parse_input/crl_sha512.pem":"CRL version   \: 1\nissuer name   \: C=NL, O=PolarSSL, CN=PolarSSL Test CA\nthis update   \: 2011-02-12 14\:44\:07\nnext update   \: 2011-04-13 14\:44\:07\nRevoked certificates\:\nserial number\: 01 revocation date\: 2011-02-12 14\:44\:07\nserial number\: 03 revocation date\: 2011-02-12 14\:44\:07\nsigned using  \: RSA with SHA-512\n"

X509 CRL information RSA-PSS, SHA1 Digest
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_X509_RSASSA_PSS_SUPPORT:MBEDTLS_MD_CAN_SHA1:!MBEDTLS_X509_REMOVE_INFO
mbedtls_x509_crl_info:"data_files/parse_input/crl-rsa-pss-sha1.pem":"CRL version   \: 2\nissuer name   \: C=NL, O=PolarSSL, CN=PolarSSL Test CA\nthis update   \: 2014-01-20 13\:46\:35\nnext update   \: 2024-01-18 13\:46\:35\nRevoked certificates\:\nserial number\: 0A revocation date\: 2013-09-24 16\:28\:38\nserial number\: 16 revocation date\: 2014-01-20 13\:43\:05\nsigned using  \: RSASSA-PSS (SHA1, MGF1-SHA1, 0xEA)\n"

X509 CRL information RSA-PSS, SHA224 Digest
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_X509_RSASSA_PSS_SUPPORT:MBEDTLS_MD_CAN_SHA224:!MBEDTLS_X509_REMOVE_INFO
mbedtls_x509_crl_info:"data_files/parse_input/crl-rsa-pss-sha224.pem":"CRL version   \: 2\nissuer name   \: C=NL, O=PolarSSL, CN=PolarSSL Test CA\nthis update   \: 2014-01-20 13\:56\:06\nnext update   \: 2024-01-18 13\:56\:06\nRevoked certificates\:\nserial number\: 0A revocation date\: 2013-09-24 16\:28\:38\nserial number\: 16 revocation date\: 2014-01-20 13\:43\:05\nsigned using  \: RSASSA-PSS (SHA224, MGF1-SHA224, 0xE2)\n"

X509 CRL information RSA-PSS, SHA256 Digest
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_X509_RSASSA_PSS_SUPPORT:MBEDTLS_MD_CAN_SHA256:!MBEDTLS_X509_REMOVE_INFO
mbedtls_x509_crl_info:"data_files/parse_input/crl-rsa-pss-sha256.pem":"CRL version   \: 2\nissuer name   \: C=NL, O=PolarSSL, CN=PolarSSL Test CA\nthis update   \: 2014-01-20 13\:56\:16\nnext update   \: 2024-01-18 13\:56\:16\nRevoked certificates\:\nserial number\: 0A revocation date\: 2013-09-24 16\:28\:38\nserial number\: 16 revocation date\: 2014-01-20 13\:43\:05\nsigned using  \: RSASSA-PSS (SHA256, MGF1-SHA256, 0xDE)\n"

X509 CRL information RSA-PSS, SHA384 Digest
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_X509_RSASSA_PSS_SUPPORT:MBEDTLS_MD_CAN_SHA384:!MBEDTLS_X509_REMOVE_INFO
mbedtls_x509_crl_info:"data_files/parse_input/crl-rsa-pss-sha384.pem":"CRL version   \: 2\nissuer name   \: C=NL, O=PolarSSL, CN=PolarSSL Test CA\nthis update   \: 2014-01-20 13\:56\:28\nnext update   \: 2024-01-18 13\:56\:28\nRevoked certificates\:\nserial number\: 0A revocation date\: 2013-09-24 16\:28\:38\nserial number\: 16 revocation date\: 2014-01-20 13\:43\:05\nsigned using  \: RSASSA-PSS (SHA384, MGF1-SHA384, 0xCE)\n"

X509 CRL information RSA-PSS, SHA512 Digest
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_X509_RSASSA_PSS_SUPPORT:MBEDTLS_MD_CAN_SHA512:!MBEDTLS_X509_REMOVE_INFO
mbedtls_x509_crl_info:"data_files/parse_input/crl-rsa-pss-sha512.pem":"CRL version   \: 2\nissuer name   \: C=NL, O=PolarSSL, CN=PolarSSL Test CA\nthis update   \: 2014-01-20 13\:56\:38\nnext update   \: 2024-01-18 13\:56\:38\nRevoked certificates\:\nserial number\: 0A revocation date\: 2013-09-24 16\:28\:38\nserial number\: 16 revocation date\: 2014-01-20 13\:43\:05\nsigned using  \: RSASSA-PSS (SHA512, MGF1-SHA512, 0xBE)\n"

X509 CRL Information EC, SHA1 Digest
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_MD_CAN_SHA1:MBEDTLS_PK_CAN_ECDSA_SOME:!MBEDTLS_X509_REMOVE_INFO
mbedtls_x509_crl_info:"data_files/parse_input/crl-ec-sha1.pem":"CRL version   \: 2\nissuer name   \: C=NL, O=PolarSSL, CN=Polarssl Test EC CA\nthis update   \: 2013-09-24 16\:31\:08\nnext update   \: 2023-09-22 16\:31\:08\nRevoked certificates\:\nserial number\: 0A revocation date\: 2013-09-24 16\:28\:38\nsigned using  \: ECDSA with SHA1\n"

X509 CRL Information EC, SHA224 Digest
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_MD_CAN_SHA224:MBEDTLS_PK_CAN_ECDSA_SOME:!MBEDTLS_X509_REMOVE_INFO
mbedtls_x509_crl_info:"data_files/parse_input/crl-ec-sha224.pem":"CRL version   \: 2\nissuer name   \: C=NL, O=PolarSSL, CN=Polarssl Test EC CA\nthis update   \: 2013-09-24 16\:31\:08\nnext update   \: 2023-09-22 16\:31\:08\nRevoked certificates\:\nserial number\: 0A revocation date\: 2013-09-24 16\:28\:38\nsigned using  \: ECDSA with SHA224\n"

X509 CRL Information EC, SHA256 Digest
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_MD_CAN_SHA256:MBEDTLS_PK_CAN_ECDSA_SOME:!MBEDTLS_X509_REMOVE_INFO
mbedtls_x509_crl_info:"data_files/parse_input/crl-ec-sha256.pem":"CRL version   \: 2\nissuer name   \: C=NL, O=PolarSSL, CN=Polarssl Test EC CA\nthis update   \: 2013-09-24 16\:31\:08\nnext update   \: 2023-09-22 16\:31\:08\nRevoked certificates\:\nserial number\: 0A revocation date\: 2013-09-24 16\:28\:38\nsigned using  \: ECDSA with SHA256\n"

X509 CRL Information EC, SHA384 Digest
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_MD_CAN_SHA384:MBEDTLS_PK_CAN_ECDSA_SOME:!MBEDTLS_X509_REMOVE_INFO
mbedtls_x509_crl_info:"data_files/parse_input/crl-ec-sha384.pem":"CRL version   \: 2\nissuer name   \: C=NL, O=PolarSSL, CN=Polarssl Test EC CA\nthis update   \: 2013-09-24 16\:31\:08\nnext update   \: 2023-09-22 16\:31\:08\nRevoked certificates\:\nserial number\: 0A revocation date\: 2013-09-24 16\:28\:38\nsigned using  \: ECDSA with SHA384\n"

X509 CRL Information EC, SHA512 Digest
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_MD_CAN_SHA512:MBEDTLS_PK_CAN_ECDSA_SOME:!MBEDTLS_X509_REMOVE_INFO
mbedtls_x509_crl_info:"data_files/parse_input/crl-ec-sha512.pem":"CRL version   \: 2\nissuer name   \: C=NL, O=PolarSSL, CN=Polarssl Test EC CA\nthis update   \: 2013-09-24 16\:31\:08\nnext update   \: 2023-09-22 16\:31\:08\nRevoked certificates\:\nserial number\: 0A revocation date\: 2013-09-24 16\:28\:38\nsigned using  \: ECDSA with SHA512\n"

X509 CRL Malformed Input (trailing spaces at end of file)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_MD_CAN_SHA1:MBEDTLS_MD_CAN_SHA512:MBEDTLS_PK_CAN_ECDSA_VERIFY
mbedtls_x509_crl_parse:"data_files/parse_input/crl-malformed-trailing-spaces.pem":MBEDTLS_ERR_PEM_NO_HEADER_FOOTER_PRESENT

X509 CRL Unsupported critical extension (issuingDistributionPoint)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
mbedtls_x509_crl_parse:"data_files/parse_input/crl-idp.pem":MBEDTLS_ERR_X509_INVALID_EXTENSIONS + MBEDTLS_ERR_ASN1_UNEXPECTED_TAG

X509 CRL Unsupported non-critical extension (issuingDistributionPoint)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
mbedtls_x509_crl_parse:"data_files/parse_input/crl-idpnc.pem":0

X509 CSR Information RSA with MD5
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_MD_CAN_MD5:MBEDTLS_RSA_C:!MBEDTLS_X509_REMOVE_INFO
mbedtls_x509_csr_info:"data_files/parse_input/server1.req.md5":"CSR version   \: 1\nsubject name  \: C=NL, O=PolarSSL, CN=PolarSSL Server 1\nsigned using  \: RSA with MD5\nRSA key size  \: 2048 bits\n"

X509 CSR Information RSA with SHA1
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_MD_CAN_SHA1:MBEDTLS_RSA_C:!MBEDTLS_X509_REMOVE_INFO
mbedtls_x509_csr_info:"data_files/parse_input/server1.req.sha1":"CSR version   \: 1\nsubject name  \: C=NL, O=PolarSSL, CN=PolarSSL Server 1\nsigned using  \: RSA with SHA1\nRSA key size  \: 2048 bits\n"

X509 CSR Information RSA with SHA224
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_MD_CAN_SHA224:MBEDTLS_RSA_C:!MBEDTLS_X509_REMOVE_INFO
mbedtls_x509_csr_info:"data_files/parse_input/server1.req.sha224":"CSR version   \: 1\nsubject name  \: C=NL, O=PolarSSL, CN=PolarSSL Server 1\nsigned using  \: RSA with SHA-224\nRSA key size  \: 2048 bits\n"

X509 CSR Information RSA with SHA-256
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_MD_CAN_SHA256:MBEDTLS_RSA_C:MBEDTS_X509_INFO
mbedtls_x509_csr_info:"data_files/parse_input/server1.req.sha256":"CSR version   \: 1\nsubject name  \: C=NL, O=PolarSSL, CN=PolarSSL Server 1\nsigned using  \: RSA with SHA-256\nRSA key size  \: 2048 bits\n"

X509 CSR Information RSA with SHA384
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_MD_CAN_SHA384:MBEDTLS_RSA_C:!MBEDTLS_X509_REMOVE_INFO
mbedtls_x509_csr_info:"data_files/parse_input/server1.req.sha384":"CSR version   \: 1\nsubject name  \: C=NL, O=PolarSSL, CN=PolarSSL Server 1\nsigned using  \: RSA with SHA-384\nRSA key size  \: 2048 bits\n"

X509 CSR Information RSA with SHA512
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_MD_CAN_SHA512:MBEDTLS_RSA_C:!MBEDTLS_X509_REMOVE_INFO
mbedtls_x509_csr_info:"data_files/parse_input/server1.req.sha512":"CSR version   \: 1\nsubject name  \: C=NL, O=PolarSSL, CN=PolarSSL Server 1\nsigned using  \: RSA with SHA-512\nRSA key size  \: 2048 bits\n"

X509 CSR Information RSA with SHA-256, containing commas
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_MD_CAN_SHA256:MBEDTLS_RSA_C:MBEDTS_X509_INFO
mbedtls_x509_csr_info:"data_files/parse_input/server1.req.commas.sha256":"CSR version   \: 1\nsubject name  \: C=NL, O=PolarSSL\\, Commas, CN=PolarSSL Server 1\nsigned using  \: RSA with SHA-256\nRSA key size  \: 2048 bits\n"

X509 CSR Information EC with SHA1
depends_on:MBEDTLS_PK_CAN_ECDSA_SOME:MBEDTLS_PEM_PARSE_C:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_MD_CAN_SHA1:!MBEDTLS_X509_REMOVE_INFO
mbedtls_x509_csr_info:"data_files/parse_input/server5.req.sha1":"CSR version   \: 1\nsubject name  \: C=NL, O=PolarSSL, CN=localhost\nsigned using  \: ECDSA with SHA1\nEC key size   \: 256 bits\n\nkey usage         \: Digital Signature, Non Repudiation, Key Encipherment\n"

X509 CSR Information EC with SHA224
depends_on:MBEDTLS_PK_CAN_ECDSA_SOME:MBEDTLS_PEM_PARSE_C:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_MD_CAN_SHA224:!MBEDTLS_X509_REMOVE_INFO
mbedtls_x509_csr_info:"data_files/parse_input/server5.req.sha224":"CSR version   \: 1\nsubject name  \: C=NL, O=PolarSSL, CN=localhost\nsigned using  \: ECDSA with SHA224\nEC key size   \: 256 bits\n\nkey usage         \: Digital Signature, Non Repudiation, Key Encipherment\n"

X509 CSR Information EC with SHA256
depends_on:MBEDTLS_PK_CAN_ECDSA_SOME:MBEDTLS_PEM_PARSE_C:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_MD_CAN_SHA256:!MBEDTLS_X509_REMOVE_INFO
mbedtls_x509_csr_info:"data_files/parse_input/server5.req.sha256":"CSR version   \: 1\nsubject name  \: C=NL, O=PolarSSL, CN=localhost\nsigned using  \: ECDSA with SHA256\nEC key size   \: 256 bits\n\nkey usage         \: Digital Signature, Non Repudiation, Key Encipherment\n"

X509 CSR Information EC with SHA384
depends_on:MBEDTLS_PK_CAN_ECDSA_SOME:MBEDTLS_PEM_PARSE_C:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_MD_CAN_SHA384:!MBEDTLS_X509_REMOVE_INFO
mbedtls_x509_csr_info:"data_files/parse_input/server5.req.sha384":"CSR version   \: 1\nsubject name  \: C=NL, O=PolarSSL, CN=localhost\nsigned using  \: ECDSA with SHA384\nEC key size   \: 256 bits\n\nkey usage         \: Digital Signature, Non Repudiation, Key Encipherment\n"

X509 CSR Information EC with SHA512
depends_on:MBEDTLS_PK_CAN_ECDSA_SOME:MBEDTLS_PEM_PARSE_C:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_MD_CAN_SHA512:!MBEDTLS_X509_REMOVE_INFO
mbedtls_x509_csr_info:"data_files/parse_input/server5.req.sha512":"CSR version   \: 1\nsubject name  \: C=NL, O=PolarSSL, CN=localhost\nsigned using  \: ECDSA with SHA512\nEC key size   \: 256 bits\n\nkey usage         \: Digital Signature, Non Repudiation, Key Encipherment\n"

X509 CSR Information RSA-PSS with SHA1
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_X509_RSASSA_PSS_SUPPORT:MBEDTLS_MD_CAN_SHA1:!MBEDTLS_X509_REMOVE_INFO
mbedtls_x509_csr_info:"data_files/parse_input/server9.req.sha1":"CSR version   \: 1\nsubject name  \: C=NL, O=PolarSSL, CN=localhost\nsigned using  \: RSASSA-PSS (SHA1, MGF1-SHA1, 0x6A)\nRSA key size  \: 1024 bits\n\nkey usage         \: Digital Signature, Non Repudiation, Key Encipherment\n"

X509 CSR Information RSA-PSS with SHA224
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_X509_RSASSA_PSS_SUPPORT:MBEDTLS_MD_CAN_SHA224:!MBEDTLS_X509_REMOVE_INFO
mbedtls_x509_csr_info:"data_files/parse_input/server9.req.sha224":"CSR version   \: 1\nsubject name  \: C=NL, O=PolarSSL, CN=localhost\nsigned using  \: RSASSA-PSS (SHA224, MGF1-SHA224, 0x62)\nRSA key size  \: 1024 bits\n\nkey usage         \: Digital Signature, Non Repudiation, Key Encipherment\n"

X509 CSR Information RSA-PSS with SHA256
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_X509_RSASSA_PSS_SUPPORT:MBEDTLS_MD_CAN_SHA256:!MBEDTLS_X509_REMOVE_INFO
mbedtls_x509_csr_info:"data_files/parse_input/server9.req.sha256":"CSR version   \: 1\nsubject name  \: C=NL, O=PolarSSL, CN=localhost\nsigned using  \: RSASSA-PSS (SHA256, MGF1-SHA256, 0x5E)\nRSA key size  \: 1024 bits\n\nkey usage         \: Digital Signature, Non Repudiation, Key Encipherment\n"

X509 CSR Information RSA-PSS with SHA384
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_X509_RSASSA_PSS_SUPPORT:MBEDTLS_MD_CAN_SHA384:!MBEDTLS_X509_REMOVE_INFO
mbedtls_x509_csr_info:"data_files/parse_input/server9.req.sha384":"CSR version   \: 1\nsubject name  \: C=NL, O=PolarSSL, CN=localhost\nsigned using  \: RSASSA-PSS (SHA384, MGF1-SHA384, 0x4E)\nRSA key size  \: 1024 bits\n\nkey usage         \: Digital Signature, Non Repudiation, Key Encipherment\n"

X509 CSR Information RSA-PSS with SHA512
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_X509_RSASSA_PSS_SUPPORT:MBEDTLS_MD_CAN_SHA512:!MBEDTLS_X509_REMOVE_INFO
mbedtls_x509_csr_info:"data_files/parse_input/server9.req.sha512":"CSR version   \: 1\nsubject name  \: C=NL, O=PolarSSL, CN=localhost\nsigned using  \: RSASSA-PSS (SHA512, MGF1-SHA512, 0x3E)\nRSA key size  \: 1024 bits\n\nkey usage         \: Digital Signature, Non Repudiation, Key Encipherment\n"

X509 CSR Information RSA with SHA-256 - Microsoft header
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_MD_CAN_SHA256:MBEDTLS_RSA_C
mbedtls_x509_csr_info:"data_files/parse_input/server1-ms.req.sha256":"CSR version   \: 1\nsubject name  \: C=NL, O=PolarSSL, CN=PolarSSL Server 1\nsigned using  \: RSA with SHA-256\nRSA key size  \: 2048 bits\n"

X509 CSR Information v3 extensions #1 (all)
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_RSA_C:!MBEDTLS_X509_REMOVE_INFO
mbedtls_x509_csr_info:"data_files/parse_input/test_csr_v3_all.csr.der":"CSR version   \: 1\nsubject name  \: CN=etcd\nsigned using  \: RSA with SHA-256\nRSA key size  \: 1024 bits\n\nsubject alt name  \:\n    otherName \:\n        hardware module name \:\n            hardware type          \: *******.********\n            hardware serial number \: 3132338081008180333231\ncert. type        \: SSL Client\nkey usage         \: CRL Sign\n"

X509 CSR Information v3 extensions #2 (nsCertType only)
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_RSA_C:!MBEDTLS_X509_REMOVE_INFO
mbedtls_x509_csr_info:"data_files/parse_input/test_csr_v3_nsCertType.csr.der":"CSR version   \: 1\nsubject name  \: CN=etcd\nsigned using  \: RSA with SHA-256\nRSA key size  \: 1024 bits\n\ncert. type        \: SSL Server\n"

X509 CSR Information v3 extensions #3 (subjectAltName only)
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_RSA_C:!MBEDTLS_X509_REMOVE_INFO
mbedtls_x509_csr_info:"data_files/parse_input/test_csr_v3_subjectAltName.csr.der":"CSR version   \: 1\nsubject name  \: CN=etcd\nsigned using  \: RSA with SHA-256\nRSA key size  \: 1024 bits\n\nsubject alt name  \:\n    dNSName \: example.com\n    dNSName \: example.net\n    dNSName \: *.example.org\n"

X509 CSR Information v3 extensions #4 (keyUsage only)
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_RSA_C:!MBEDTLS_X509_REMOVE_INFO
mbedtls_x509_csr_info:"data_files/parse_input/test_csr_v3_keyUsage.csr.der":"CSR version   \: 1\nsubject name  \: CN=etcd\nsigned using  \: RSA with SHA-256\nRSA key size  \: 1024 bits\n\nkey usage         \: Digital Signature, Key Encipherment\n"

X509 Verify Information: empty
x509_verify_info:0:"":""

X509 Verify Information: one issue
x509_verify_info:MBEDTLS_X509_BADCERT_MISSING:"":"Certificate was missing\n"

X509 Verify Information: two issues
x509_verify_info:MBEDTLS_X509_BADCERT_EXPIRED | MBEDTLS_X509_BADCRL_EXPIRED:"":"The certificate validity has expired\nThe CRL is expired\n"

X509 Verify Information: two issues, one unknown
x509_verify_info:MBEDTLS_X509_BADCERT_OTHER | 0x80000000:"":"Other reason (can be used by verify callback)\nUnknown reason (this should not happen)\n"

X509 Verify Information: empty, with prefix
x509_verify_info:0:"  ! ":""

X509 Verify Information: one issue, with prefix
x509_verify_info:MBEDTLS_X509_BADCERT_MISSING:"  ! ":"  ! Certificate was missing\n"

X509 Verify Information: two issues, with prefix
x509_verify_info:MBEDTLS_X509_BADCERT_EXPIRED | MBEDTLS_X509_BADCRL_EXPIRED:"  ! ":"  ! The certificate validity has expired\n  ! The CRL is expired\n"

X509 Get Distinguished Name #1
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA1
mbedtls_x509_dn_gets:"data_files/server1.crt":"subject":"C=NL, O=PolarSSL, CN=PolarSSL Server 1"

X509 Get Distinguished Name #2
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA1
mbedtls_x509_dn_gets:"data_files/server1.crt":"issuer":"C=NL, O=PolarSSL, CN=PolarSSL Test CA"

X509 Get Distinguished Name #3
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA1
mbedtls_x509_dn_gets:"data_files/server2.crt":"subject":"C=NL, O=PolarSSL, CN=localhost"

X509 Get Distinguished Name #4
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA1
mbedtls_x509_dn_gets:"data_files/server2.crt":"issuer":"C=NL, O=PolarSSL, CN=PolarSSL Test CA"

X509 Get Distinguished Name #5
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA1
mbedtls_x509_dn_gets:"data_files/server1.commas.crt":"subject":"C=NL, O=PolarSSL\\, Commas, CN=PolarSSL Server 1"

X509 Get Distinguished Name #6
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA1
mbedtls_x509_dn_gets:"data_files/server1.hashsymbol.crt":"subject":"C=NL, O=\\#PolarSSL, CN=PolarSSL Server 1"

X509 Get Distinguished Name #7
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA1
mbedtls_x509_dn_gets:"data_files/server1.spaces.crt":"subject":"C=NL, O=\\ PolarSSL\\ , CN=PolarSSL Server 1"

X509 Get Distinguished Name #8
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA1
mbedtls_x509_dn_gets:"data_files/server1.asciichars.crt":"subject":"C=NL, O=\\E6\\9E\\81\\E5\\9C\\B0SSL, CN=PolarSSL Server 1"

X509 Get Modified DN #1
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA1
mbedtls_x509_dn_gets_subject_replace:"data_files/server1.crt":"Modified":"C=NL, O=Modified, CN=PolarSSL Server 1":0

X509 Get Modified DN #2 Name exactly 255 bytes
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA1
mbedtls_x509_dn_gets_subject_replace:"data_files/server1.crt":"123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345":"C=NL, O=123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345, CN=PolarSSL Server 1":0

X509 Get Modified DN #3 Name exceeds 255 bytes
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA1
mbedtls_x509_dn_gets_subject_replace:"data_files/server1.crt":"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456":"":MBEDTLS_ERR_X509_BUFFER_TOO_SMALL

X509 Get Modified DN #4 Name exactly 255 bytes, with comma requiring escaping
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA1
mbedtls_x509_dn_gets_subject_replace:"data_files/server1.crt":"1234567890,1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234":"":MBEDTLS_ERR_X509_BUFFER_TOO_SMALL

X509 Get Modified DN #5 Name exactly 255 bytes, ending with comma requiring escaping
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA1
mbedtls_x509_dn_gets_subject_replace:"data_files/server1.crt":"12345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234,":"":MBEDTLS_ERR_X509_BUFFER_TOO_SMALL

X509 Get Next DN #1 No Multivalue RDNs
mbedtls_x509_dn_get_next:"C=NL, O=PolarSSL, CN=PolarSSL Server 1":0:"C O CN":3:"C=NL, O=PolarSSL, CN=PolarSSL Server 1"

X509 Get Next DN #2 Initial Multivalue RDN
mbedtls_x509_dn_get_next:"C=NL, O=PolarSSL, CN=PolarSSL Server 1":0x01:"C CN":2:"C=NL + O=PolarSSL, CN=PolarSSL Server 1"

X509 Get Next DN #3 Single Multivalue RDN
mbedtls_x509_dn_get_next:"C=NL, O=PolarSSL, CN=PolarSSL Server 1":0x03:"C":1:"C=NL + O=PolarSSL + CN=PolarSSL Server 1"

X509 Get Next DN #4 Consecutive Multivalue RDNs
mbedtls_x509_dn_get_next:"C=NL, O=PolarSSL, title=Example, CN=PolarSSL Server 1":0x05:"C title":2:"C=NL + O=PolarSSL, title=Example + CN=PolarSSL Server 1"

# Parse the following valid DN:
#
# 31 0B <- Set of
#     30 09 <- Sequence of
#         06 03 55 04 06 <- OID 2.5.4.6 countryName (C)
#         13 02 4E 4C <- PrintableString "NL"
# 31 11 <- Set of
#     30 0F <- Sequence of
#         06 03 55 04 0A <- OID 2.5.4.10 organizationName (O)
#         0C 08 50 6F 6C 61 72 53 53 4C <- UTF8String "PolarSSL"
# 31 19 <- Set of
#     30 17 <- Sequence of
#         06 03 55 04 03 <- OID 2.5.4.3 commonName (CN)
#         0C 10 50 6F 6C 61 72 53 53 4C 20 54 65 73 74 20 43 41 <- UTF8String "PolarSSL Test CA"
#
X509 Get Name Valid DN
mbedtls_x509_get_name:"310B3009060355040613024E4C3111300F060355040A0C08506F6C617253534C3119301706035504030C10506F6C617253534C2054657374204341":0

# Parse the following corrupted DN:
#
# 31 0B <- Set of
#     30 09 <- Sequence of
#         06 03 55 04 06 <- OID 2.5.4.6 countryName (C)
#         13 02 4E 4C <- PrintableString "NL"
# 31 11 <- Set of
#     30 0F <- Sequence of
#         06 03 55 04 0A <- OID 2.5.4.10 organizationName (O)
#         0C 08 50 6F 6C 61 72 53 53 4C <- UTF8String "PolarSSL"
# 30 19 <- Sequence of (corrupted)
#     30 17 <- Sequence of
#         06 03 55 04 03 <- OID 2.5.4.3 commonName (CN)
#         0C 10 50 6F 6C 61 72 53 53 4C 20 54 65 73 74 20 43 41 <- UTF8String "PolarSSL Test CA"
#
# The third 'Set of' is corrupted to instead be a 'Sequence of', causing an
# error and forcing mbedtls_x509_get_name() to clean up the names it has
# already allocated.
#
X509 Get Name Corrupted DN Mem Leak
mbedtls_x509_get_name:"310B3009060355040613024E4C3111300F060355040A0C08506F6C617253534C3019301706035504030C10506F6C617253534C2054657374204341":MBEDTLS_ERR_X509_INVALID_NAME + MBEDTLS_ERR_ASN1_UNEXPECTED_TAG

X509 Time Expired #1
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_RSA_C:MBEDTLS_HAVE_TIME_DATE:MBEDTLS_MD_CAN_SHA1
mbedtls_x509_time_is_past:"data_files/server1.crt":"valid_from":1

X509 Time Expired #2
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_RSA_C:MBEDTLS_HAVE_TIME_DATE:MBEDTLS_MD_CAN_SHA1
mbedtls_x509_time_is_past:"data_files/server1.crt":"valid_to":0

X509 Time Expired #3
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_RSA_C:MBEDTLS_HAVE_TIME_DATE:MBEDTLS_MD_CAN_SHA1
mbedtls_x509_time_is_past:"data_files/server2.crt":"valid_from":1

X509 Time Expired #4
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_RSA_C:MBEDTLS_HAVE_TIME_DATE:MBEDTLS_MD_CAN_SHA1
mbedtls_x509_time_is_past:"data_files/server2.crt":"valid_to":0

X509 Time Expired #5
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_RSA_C:MBEDTLS_HAVE_TIME_DATE:MBEDTLS_MD_CAN_SHA1
mbedtls_x509_time_is_past:"data_files/test-ca.crt":"valid_from":1

X509 Time Expired #6
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_RSA_C:MBEDTLS_HAVE_TIME_DATE:MBEDTLS_MD_CAN_SHA1
mbedtls_x509_time_is_past:"data_files/test-ca.crt":"valid_to":0

X509 Time Future #1
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_PK_CAN_ECDSA_SOME:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_HAVE_TIME_DATE:MBEDTLS_MD_CAN_SHA256
mbedtls_x509_time_is_future:"data_files/server5.crt":"valid_from":0

X509 Time Future #2
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_PK_CAN_ECDSA_SOME:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_HAVE_TIME_DATE:MBEDTLS_MD_CAN_SHA256
mbedtls_x509_time_is_future:"data_files/server5.crt":"valid_to":1

X509 Time Future #3
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_PK_CAN_ECDSA_SOME:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_HAVE_TIME_DATE:MBEDTLS_MD_CAN_SHA256
mbedtls_x509_time_is_future:"data_files/server5-future.crt":"valid_from":1

X509 Time Future #4
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_PK_CAN_ECDSA_SOME:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_HAVE_TIME_DATE:MBEDTLS_MD_CAN_SHA256
mbedtls_x509_time_is_future:"data_files/server5-future.crt":"valid_to":1

X509 Time Future #5
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_PK_CAN_ECDSA_SOME:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_HAVE_TIME_DATE:MBEDTLS_MD_CAN_SHA256
mbedtls_x509_time_is_future:"data_files/test-ca2.crt":"valid_from":0

X509 Time Future #6
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_PK_CAN_ECDSA_SOME:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_HAVE_TIME_DATE:MBEDTLS_MD_CAN_SHA256
mbedtls_x509_time_is_future:"data_files/test-ca2.crt":"valid_to":1

X509 CRT verification #1 (Revoked Cert, Expired CRL, no CN)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_MD_CAN_SHA1:MBEDTLS_RSA_C:MBEDTLS_PKCS1_V15:MBEDTLS_HAVE_TIME_DATE
x509_verify:"data_files/server1.crt":"data_files/test-ca.crt":"data_files/crl_expired.pem":"NULL":MBEDTLS_ERR_X509_CERT_VERIFY_FAILED:MBEDTLS_X509_BADCERT_REVOKED | MBEDTLS_X509_BADCRL_EXPIRED:"compat":"NULL"

X509 CRT verification #1a (Revoked Cert, Future CRL, no CN)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_MD_CAN_SHA256:MBEDTLS_PK_CAN_ECDSA_VERIFY:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_MD_CAN_SHA1:MBEDTLS_HAVE_TIME_DATE
x509_verify:"data_files/server6.crt":"data_files/test-ca2.crt":"data_files/crl-future.pem":"NULL":MBEDTLS_ERR_X509_CERT_VERIFY_FAILED:MBEDTLS_X509_BADCERT_REVOKED | MBEDTLS_X509_BADCRL_FUTURE:"compat":"NULL"

X509 CRT verification #2 (Revoked Cert, Expired CRL)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_MD_CAN_SHA1:MBEDTLS_RSA_C:MBEDTLS_PKCS1_V15:MBEDTLS_HAVE_TIME_DATE
x509_verify:"data_files/server1.crt":"data_files/test-ca.crt":"data_files/crl_expired.pem":"PolarSSL Server 1":MBEDTLS_ERR_X509_CERT_VERIFY_FAILED:MBEDTLS_X509_BADCERT_REVOKED | MBEDTLS_X509_BADCRL_EXPIRED:"compat":"NULL"

X509 CRT verification #2a (Revoked Cert, Future CRL)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_MD_CAN_SHA256:MBEDTLS_PK_CAN_ECDSA_VERIFY:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_MD_CAN_SHA1:MBEDTLS_HAVE_TIME_DATE
x509_verify:"data_files/server6.crt":"data_files/test-ca2.crt":"data_files/crl-future.pem":"localhost":MBEDTLS_ERR_X509_CERT_VERIFY_FAILED:MBEDTLS_X509_BADCERT_REVOKED | MBEDTLS_X509_BADCRL_FUTURE:"compat":"NULL"

X509 CRT verification #3 (Revoked Cert, Future CRL, CN Mismatch)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_MD_CAN_SHA1:MBEDTLS_RSA_C:MBEDTLS_PKCS1_V15:MBEDTLS_HAVE_TIME_DATE
x509_verify:"data_files/server1.crt":"data_files/test-ca.crt":"data_files/crl_expired.pem":"PolarSSL Wrong CN":MBEDTLS_ERR_X509_CERT_VERIFY_FAILED:MBEDTLS_X509_BADCERT_REVOKED | MBEDTLS_X509_BADCRL_EXPIRED | MBEDTLS_X509_BADCERT_CN_MISMATCH:"compat":"NULL"

X509 CRT verification #3a (Revoked Cert, Expired CRL, CN Mismatch)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_MD_CAN_SHA256:MBEDTLS_PK_CAN_ECDSA_VERIFY:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_MD_CAN_SHA1:MBEDTLS_HAVE_TIME_DATE
x509_verify:"data_files/server6.crt":"data_files/test-ca2.crt":"data_files/crl-future.pem":"Wrong CN":MBEDTLS_ERR_X509_CERT_VERIFY_FAILED:MBEDTLS_X509_BADCERT_REVOKED | MBEDTLS_X509_BADCRL_FUTURE | MBEDTLS_X509_BADCERT_CN_MISMATCH:"compat":"NULL"

X509 CRT verification #4 (Valid Cert, Expired CRL)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_MD_CAN_SHA1:MBEDTLS_RSA_C:MBEDTLS_PKCS1_V15:MBEDTLS_HAVE_TIME_DATE
x509_verify:"data_files/server2.crt":"data_files/test-ca.crt":"data_files/crl_expired.pem":"NULL":MBEDTLS_ERR_X509_CERT_VERIFY_FAILED:MBEDTLS_X509_BADCRL_EXPIRED:"compat":"NULL"

X509 CRT verification #4a (Revoked Cert, Future CRL)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_MD_CAN_SHA256:MBEDTLS_PK_CAN_ECDSA_VERIFY:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_MD_CAN_SHA1:MBEDTLS_HAVE_TIME_DATE
x509_verify:"data_files/server5.crt":"data_files/test-ca2.crt":"data_files/crl-future.pem":"NULL":MBEDTLS_ERR_X509_CERT_VERIFY_FAILED:MBEDTLS_X509_BADCRL_FUTURE:"compat":"NULL"

X509 CRT verification #5 (Revoked Cert)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_MD_CAN_SHA1:MBEDTLS_RSA_C:MBEDTLS_PKCS1_V15:MBEDTLS_HAVE_TIME_DATE
x509_verify:"data_files/server1.crt":"data_files/test-ca.crt":"data_files/crl.pem":"NULL":MBEDTLS_ERR_X509_CERT_VERIFY_FAILED:MBEDTLS_X509_BADCERT_REVOKED:"compat":"NULL"

X509 CRT verification #5' (Revoked Cert, differing DN string formats #1)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_MD_CAN_SHA1:MBEDTLS_RSA_C:MBEDTLS_PKCS1_V15:MBEDTLS_HAVE_TIME_DATE
x509_verify:"data_files/server1.crt":"data_files/test-ca_utf8.crt":"data_files/crl.pem":"NULL":MBEDTLS_ERR_X509_CERT_VERIFY_FAILED:MBEDTLS_X509_BADCERT_REVOKED:"compat":"NULL"

X509 CRT verification #5'' (Revoked Cert, differing DN string formats #2)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_MD_CAN_SHA1:MBEDTLS_RSA_C:MBEDTLS_PKCS1_V15:MBEDTLS_HAVE_TIME_DATE
x509_verify:"data_files/server1.crt":"data_files/test-ca_printable.crt":"data_files/crl.pem":"NULL":MBEDTLS_ERR_X509_CERT_VERIFY_FAILED:MBEDTLS_X509_BADCERT_REVOKED:"compat":"NULL"

X509 CRT verification #5''' (Revoked Cert, differing upper and lower case)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_MD_CAN_SHA1:MBEDTLS_RSA_C:MBEDTLS_PKCS1_V15:MBEDTLS_HAVE_TIME_DATE
x509_verify:"data_files/server1.crt":"data_files/test-ca_uppercase.crt":"data_files/crl.pem":"NULL":MBEDTLS_ERR_X509_CERT_VERIFY_FAILED:MBEDTLS_X509_BADCERT_REVOKED:"compat":"NULL"

X509 CRT verification #6 (Revoked Cert)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_MD_CAN_SHA1:MBEDTLS_RSA_C:MBEDTLS_PKCS1_V15:MBEDTLS_HAVE_TIME_DATE
x509_verify:"data_files/server1.crt":"data_files/test-ca.crt":"data_files/crl.pem":"PolarSSL Server 1":MBEDTLS_ERR_X509_CERT_VERIFY_FAILED:MBEDTLS_X509_BADCERT_REVOKED:"compat":"NULL"

X509 CRT verification #7 (Revoked Cert, CN Mismatch)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_MD_CAN_SHA1:MBEDTLS_RSA_C:MBEDTLS_PKCS1_V15:MBEDTLS_HAVE_TIME_DATE
x509_verify:"data_files/server1.crt":"data_files/test-ca.crt":"data_files/crl.pem":"PolarSSL Wrong CN":MBEDTLS_ERR_X509_CERT_VERIFY_FAILED:MBEDTLS_X509_BADCERT_REVOKED | MBEDTLS_X509_BADCERT_CN_MISMATCH:"compat":"NULL"

X509 CRT verification #8 (Valid Cert)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_MD_CAN_SHA256:MBEDTLS_PK_CAN_ECDSA_VERIFY:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_MD_CAN_SHA1
x509_verify:"data_files/server5.crt":"data_files/test-ca2.crt":"data_files/crl-ec-sha1.pem":"NULL":0:0:"compat":"NULL"

X509 CRT verification #8a (Expired Cert)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_MD_CAN_SHA256:MBEDTLS_PK_CAN_ECDSA_VERIFY:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_MD_CAN_SHA1:MBEDTLS_HAVE_TIME_DATE
x509_verify:"data_files/server5-expired.crt":"data_files/test-ca2.crt":"data_files/crl-ec-sha1.pem":"NULL":MBEDTLS_ERR_X509_CERT_VERIFY_FAILED:MBEDTLS_X509_BADCERT_EXPIRED:"compat":"NULL"

X509 CRT verification #8b (Future Cert)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_MD_CAN_SHA256:MBEDTLS_PK_CAN_ECDSA_VERIFY:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_MD_CAN_SHA1:MBEDTLS_HAVE_TIME_DATE
x509_verify:"data_files/server5-future.crt":"data_files/test-ca2.crt":"data_files/crl-ec-sha1.pem":"NULL":MBEDTLS_ERR_X509_CERT_VERIFY_FAILED:MBEDTLS_X509_BADCERT_FUTURE:"compat":"NULL"

X509 CRT verification #8c (Expired Cert, longer chain)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_MD_CAN_SHA256:MBEDTLS_PK_CAN_ECDSA_VERIFY:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_MD_CAN_SHA1:MBEDTLS_RSA_C:MBEDTLS_PKCS1_V15:MBEDTLS_HAVE_TIME_DATE
x509_verify:"data_files/server7-expired.crt":"data_files/test-ca2.crt":"data_files/crl-ec-sha1.pem":"NULL":MBEDTLS_ERR_X509_CERT_VERIFY_FAILED:MBEDTLS_X509_BADCERT_EXPIRED:"compat":"NULL"

X509 CRT verification #8d (Future Cert, longer chain)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_MD_CAN_SHA256:MBEDTLS_PK_CAN_ECDSA_VERIFY:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_MD_CAN_SHA1:MBEDTLS_RSA_C:MBEDTLS_PKCS1_V15:MBEDTLS_HAVE_TIME_DATE
x509_verify:"data_files/server7-future.crt":"data_files/test-ca2.crt":"data_files/crl-ec-sha1.pem":"NULL":MBEDTLS_ERR_X509_CERT_VERIFY_FAILED:MBEDTLS_X509_BADCERT_FUTURE:"compat":"NULL"

X509 CRT verification #9 (Not trusted Cert)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_MD_CAN_SHA1:MBEDTLS_RSA_C:MBEDTLS_PKCS1_V15
x509_verify:"data_files/server2.crt":"data_files/server1.crt":"data_files/crl.pem":"NULL":MBEDTLS_ERR_X509_CERT_VERIFY_FAILED:MBEDTLS_X509_BADCERT_NOT_TRUSTED:"compat":"NULL"

X509 CRT verification #10 (Not trusted Cert, Expired CRL)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_RSA_C:MBEDTLS_PKCS1_V15:MBEDTLS_MD_CAN_SHA1
x509_verify:"data_files/server2.crt":"data_files/server1.crt":"data_files/crl_expired.pem":"NULL":MBEDTLS_ERR_X509_CERT_VERIFY_FAILED:MBEDTLS_X509_BADCERT_NOT_TRUSTED:"compat":"NULL"

X509 CRT verification #13 (Valid Cert MD5 Digest, MD5 forbidden)
depends_on:MBEDTLS_MD_CAN_MD5:MBEDTLS_PEM_PARSE_C:MBEDTLS_MD_CAN_SHA1:MBEDTLS_RSA_C:MBEDTLS_PKCS1_V15
x509_verify:"data_files/cert_md5.crt":"data_files/test-ca.crt":"data_files/crl.pem":"NULL":MBEDTLS_ERR_X509_CERT_VERIFY_FAILED:MBEDTLS_X509_BADCERT_BAD_MD:"compat":"NULL"

X509 CRT verification #13 (Valid Cert MD5 Digest, MD5 allowed)
depends_on:MBEDTLS_MD_CAN_MD5:MBEDTLS_PEM_PARSE_C:MBEDTLS_MD_CAN_SHA1:MBEDTLS_RSA_C:MBEDTLS_PKCS1_V15
x509_verify:"data_files/cert_md5.crt":"data_files/test-ca.crt":"data_files/crl.pem":"NULL":0:0:"all":"NULL"

X509 CRT verification #14 (Valid Cert SHA1 Digest explicitly allowed in profile)
depends_on:MBEDTLS_MD_CAN_SHA1:MBEDTLS_PEM_PARSE_C:MBEDTLS_MD_CAN_SHA1:MBEDTLS_RSA_C:MBEDTLS_PKCS1_V15
x509_verify:"data_files/cert_sha1.crt":"data_files/test-ca.crt":"data_files/crl.pem":"NULL":0:0:"compat":"NULL"

X509 CRT verification #14 (Valid Cert SHA1 Digest forbidden in default profile)
depends_on:MBEDTLS_MD_CAN_SHA1:MBEDTLS_PEM_PARSE_C:MBEDTLS_MD_CAN_SHA1:MBEDTLS_RSA_C:MBEDTLS_PKCS1_V15
x509_verify:"data_files/cert_sha1.crt":"data_files/test-ca.crt":"data_files/crl.pem":"NULL":MBEDTLS_ERR_X509_CERT_VERIFY_FAILED:MBEDTLS_X509_BADCRL_BAD_MD | MBEDTLS_X509_BADCERT_BAD_MD:"":"NULL"

X509 CRT verification #15 (Valid Cert SHA224 Digest)
depends_on:MBEDTLS_MD_CAN_SHA224:MBEDTLS_PEM_PARSE_C:MBEDTLS_MD_CAN_SHA1:MBEDTLS_RSA_C:MBEDTLS_PKCS1_V15
x509_verify:"data_files/cert_sha224.crt":"data_files/test-ca.crt":"data_files/crl.pem":"NULL":0:0:"compat":"NULL"

X509 CRT verification #16 (Valid Cert SHA256 Digest)
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_PEM_PARSE_C:MBEDTLS_MD_CAN_SHA1:MBEDTLS_RSA_C:MBEDTLS_PKCS1_V15
x509_verify:"data_files/cert_sha256.crt":"data_files/test-ca.crt":"data_files/crl.pem":"NULL":0:0:"compat":"NULL"

X509 CRT verification #17 (Valid Cert SHA384 Digest)
depends_on:MBEDTLS_MD_CAN_SHA384:MBEDTLS_PEM_PARSE_C:MBEDTLS_MD_CAN_SHA1:MBEDTLS_RSA_C:MBEDTLS_PKCS1_V15
x509_verify:"data_files/cert_sha384.crt":"data_files/test-ca.crt":"data_files/crl.pem":"NULL":0:0:"compat":"NULL"

X509 CRT verification #18 (Valid Cert SHA512 Digest)
depends_on:MBEDTLS_MD_CAN_SHA512:MBEDTLS_PEM_PARSE_C:MBEDTLS_MD_CAN_SHA1:MBEDTLS_RSA_C:MBEDTLS_PKCS1_V15
x509_verify:"data_files/cert_sha512.crt":"data_files/test-ca.crt":"data_files/crl.pem":"NULL":0:0:"compat":"NULL"

X509 CRT verification #19 (Valid Cert, denying callback)
depends_on:MBEDTLS_MD_CAN_SHA512:MBEDTLS_PEM_PARSE_C:MBEDTLS_MD_CAN_SHA1:MBEDTLS_RSA_C:MBEDTLS_PKCS1_V15
x509_verify:"data_files/cert_sha512.crt":"data_files/test-ca.crt":"data_files/crl.pem":"NULL":MBEDTLS_ERR_X509_CERT_VERIFY_FAILED:MBEDTLS_X509_BADCERT_OTHER:"compat":"verify_none"

X509 CRT verification #19 (Not trusted Cert, allowing callback)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_RSA_C:MBEDTLS_PKCS1_V15:MBEDTLS_MD_CAN_SHA1
x509_verify:"data_files/server2.crt":"data_files/server1.crt":"data_files/crl_expired.pem":"NULL":0:0:"compat":"verify_all"

X509 CRT verification #21 (domain matching wildcard certificate, case insensitive)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_MD_CAN_SHA1:MBEDTLS_RSA_C:MBEDTLS_PKCS1_V15
x509_verify:"data_files/cert_example_wildcard.crt":"data_files/test-ca.crt":"data_files/crl.pem":"mail.ExAmPlE.com":0:0:"compat":"NULL"

X509 CRT verification #22 (domain not matching wildcard certificate)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_MD_CAN_SHA1:MBEDTLS_RSA_C:MBEDTLS_PKCS1_V15
x509_verify:"data_files/cert_example_wildcard.crt":"data_files/test-ca.crt":"data_files/crl.pem":"mail.example.net":MBEDTLS_ERR_X509_CERT_VERIFY_FAILED:MBEDTLS_X509_BADCERT_CN_MISMATCH:"compat":"NULL"

X509 CRT verification #23 (domain not matching wildcard certificate)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_MD_CAN_SHA1:MBEDTLS_RSA_C:MBEDTLS_PKCS1_V15
x509_verify:"data_files/cert_example_wildcard.crt":"data_files/test-ca.crt":"data_files/crl.pem":"example.com":MBEDTLS_ERR_X509_CERT_VERIFY_FAILED:MBEDTLS_X509_BADCERT_CN_MISMATCH:"compat":"NULL"

X509 CRT verification #24 (domain matching CN of multi certificate)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_MD_CAN_SHA1:MBEDTLS_MD_CAN_SHA256:MBEDTLS_RSA_C:MBEDTLS_PKCS1_V15
x509_verify:"data_files/cert_example_multi.crt":"data_files/test-ca.crt":"data_files/crl.pem":"www.example.com":MBEDTLS_ERR_X509_CERT_VERIFY_FAILED:MBEDTLS_X509_BADCERT_CN_MISMATCH:"compat":"NULL"

X509 CRT verification #25 (domain matching multi certificate)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_MD_CAN_SHA1:MBEDTLS_MD_CAN_SHA256:MBEDTLS_RSA_C:MBEDTLS_PKCS1_V15
x509_verify:"data_files/cert_example_multi.crt":"data_files/test-ca.crt":"data_files/crl.pem":"example.net":0:0:"compat":"NULL"

X509 CRT verification #26 (domain not matching multi certificate)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_MD_CAN_SHA1:MBEDTLS_MD_CAN_SHA256:MBEDTLS_RSA_C:MBEDTLS_PKCS1_V15
x509_verify:"data_files/cert_example_multi.crt":"data_files/test-ca.crt":"data_files/crl.pem":"www.example.net":MBEDTLS_ERR_X509_CERT_VERIFY_FAILED:MBEDTLS_X509_BADCERT_CN_MISMATCH:"compat":"NULL"

X509 CRT verification #27.1 (domain not matching multi certificate: suffix)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_MD_CAN_SHA1:MBEDTLS_MD_CAN_SHA256:MBEDTLS_RSA_C:MBEDTLS_PKCS1_V15
x509_verify:"data_files/cert_example_multi.crt":"data_files/test-ca.crt":"data_files/crl.pem":"xample.net":MBEDTLS_ERR_X509_CERT_VERIFY_FAILED:MBEDTLS_X509_BADCERT_CN_MISMATCH:"compat":"NULL"

X509 CRT verification #27.2 (domain not matching multi certificate: head junk)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_MD_CAN_SHA1:MBEDTLS_MD_CAN_SHA256:MBEDTLS_RSA_C:MBEDTLS_PKCS1_V15
x509_verify:"data_files/cert_example_multi.crt":"data_files/test-ca.crt":"data_files/crl.pem":"bexample.net":MBEDTLS_ERR_X509_CERT_VERIFY_FAILED:MBEDTLS_X509_BADCERT_CN_MISMATCH:"compat":"NULL"

X509 CRT verification #28 (domain not matching wildcard in multi certificate)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_MD_CAN_SHA1:MBEDTLS_MD_CAN_SHA256:MBEDTLS_RSA_C:MBEDTLS_PKCS1_V15
x509_verify:"data_files/cert_example_multi.crt":"data_files/test-ca.crt":"data_files/crl.pem":"example.org":MBEDTLS_ERR_X509_CERT_VERIFY_FAILED:MBEDTLS_X509_BADCERT_CN_MISMATCH:"compat":"NULL"

X509 CRT verification #29 (domain matching wildcard in multi certificate)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_MD_CAN_SHA1:MBEDTLS_MD_CAN_SHA256:MBEDTLS_RSA_C:MBEDTLS_PKCS1_V15
x509_verify:"data_files/cert_example_multi.crt":"data_files/test-ca.crt":"data_files/crl.pem":"mail.example.org":0:0:"compat":"NULL"

X509 CRT verification #30 (domain matching multi certificate without CN)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_MD_CAN_SHA1:MBEDTLS_RSA_C:MBEDTLS_PKCS1_V15
x509_verify:"data_files/cert_example_multi_nocn.crt":"data_files/test-ca.crt":"data_files/crl.pem":"www.shotokan-braunschweig.de":MBEDTLS_ERR_X509_CERT_VERIFY_FAILED:MBEDTLS_X509_BADCERT_NOT_TRUSTED:"compat":"NULL"

X509 CRT verification #31 (domain not matching multi certificate without CN)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_MD_CAN_SHA1:MBEDTLS_RSA_C:MBEDTLS_PKCS1_V15
x509_verify:"data_files/cert_example_multi_nocn.crt":"data_files/test-ca.crt":"data_files/crl.pem":"www.example.net":MBEDTLS_ERR_X509_CERT_VERIFY_FAILED:MBEDTLS_X509_BADCERT_CN_MISMATCH + MBEDTLS_X509_BADCERT_NOT_TRUSTED:"compat":"NULL"

X509 CRT verification #32 (Valid, EC cert, RSA CA)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_RSA_C:MBEDTLS_PK_CAN_ECDSA_VERIFY:MBEDTLS_ECP_HAVE_SECP192R1:MBEDTLS_RSA_C:MBEDTLS_PKCS1_V15:MBEDTLS_MD_CAN_SHA1
x509_verify:"data_files/server3.crt":"data_files/test-ca.crt":"data_files/crl.pem":"NULL":0:0:"compat":"NULL"

X509 CRT verification #33 (Valid, RSA cert, EC CA)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_RSA_C:MBEDTLS_PK_CAN_ECDSA_VERIFY:MBEDTLS_MD_CAN_SHA256:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_RSA_C:MBEDTLS_PKCS1_V15:MBEDTLS_ECP_HAVE_SECP384R1
x509_verify:"data_files/server4.crt":"data_files/test-ca2.crt":"data_files/crl-ec-sha256.pem":"NULL":0:0:"compat":"NULL"

X509 CRT verification #34 (Valid, EC cert, EC CA)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_PK_CAN_ECDSA_VERIFY:MBEDTLS_MD_CAN_SHA256:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_ECP_HAVE_SECP384R1
x509_verify:"data_files/server5.crt":"data_files/test-ca2.crt":"data_files/crl-ec-sha256.pem":"NULL":0:0:"compat":"NULL"

X509 CRT verification #35 (Revoked, EC CA)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_PK_CAN_ECDSA_VERIFY:MBEDTLS_MD_CAN_SHA256:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_HAVE_TIME_DATE
x509_verify:"data_files/server6.crt":"data_files/test-ca2.crt":"data_files/crl-ec-sha256.pem":"NULL":MBEDTLS_ERR_X509_CERT_VERIFY_FAILED:MBEDTLS_X509_BADCERT_REVOKED:"compat":"NULL"

X509 CRT verification #36 (Valid, EC CA, SHA1 Digest)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_PK_CAN_ECDSA_VERIFY:MBEDTLS_MD_CAN_SHA256:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_MD_CAN_SHA1
x509_verify:"data_files/server5-sha1.crt":"data_files/test-ca2.crt":"data_files/crl-ec-sha256.pem":"NULL":0:0:"compat":"NULL"

X509 CRT verification #37 (Valid, EC CA, SHA224 Digest)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_PK_CAN_ECDSA_VERIFY:MBEDTLS_MD_CAN_SHA256:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_MD_CAN_SHA224
x509_verify:"data_files/server5-sha224.crt":"data_files/test-ca2.crt":"data_files/crl-ec-sha256.pem":"NULL":0:0:"compat":"NULL"

X509 CRT verification #38 (Valid, EC CA, SHA384 Digest)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_PK_CAN_ECDSA_VERIFY:MBEDTLS_MD_CAN_SHA256:MBEDTLS_MD_CAN_SHA384:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_ECP_HAVE_SECP384R1
x509_verify:"data_files/server5-sha384.crt":"data_files/test-ca2.crt":"data_files/crl-ec-sha256.pem":"NULL":0:0:"compat":"NULL"

X509 CRT verification #39 (Valid, EC CA, SHA512 Digest)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_PK_CAN_ECDSA_VERIFY:MBEDTLS_MD_CAN_SHA256:MBEDTLS_MD_CAN_SHA512:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_ECP_HAVE_SECP384R1
x509_verify:"data_files/server5-sha512.crt":"data_files/test-ca2.crt":"data_files/crl-ec-sha256.pem":"NULL":0:0:"compat":"NULL"

X509 CRT verification #40 (Valid, depth 0, RSA, CA)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_RSA_C:MBEDTLS_PKCS1_V15:MBEDTLS_MD_CAN_SHA256:MBEDTLS_MD_CAN_SHA1
x509_verify:"data_files/test-ca.crt":"data_files/test-ca.crt":"data_files/crl.pem":"NULL":0:0:"compat":"NULL"

X509 CRT verification #41 (Valid, depth 0, EC, CA)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_PK_CAN_ECDSA_VERIFY:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_MD_CAN_SHA256
x509_verify:"data_files/test-ca2.crt":"data_files/test-ca2.crt":"data_files/crl-ec-sha256.pem":"NULL":0:0:"compat":"NULL"

X509 CRT verification #42 (Depth 0, not CA, RSA)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_RSA_C:MBEDTLS_PKCS1_V15:MBEDTLS_MD_CAN_SHA1
x509_verify:"data_files/server2.crt":"data_files/server2.crt":"data_files/crl.pem":"NULL":MBEDTLS_ERR_X509_CERT_VERIFY_FAILED:MBEDTLS_X509_BADCERT_NOT_TRUSTED:"compat":"NULL"

X509 CRT verification #43 (Depth 0, not CA, EC)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_PK_CAN_ECDSA_VERIFY:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_MD_CAN_SHA256
x509_verify:"data_files/server5.crt":"data_files/server5.crt":"data_files/crl-ec-sha256.pem":"NULL":MBEDTLS_ERR_X509_CERT_VERIFY_FAILED:MBEDTLS_X509_BADCERT_NOT_TRUSTED:"compat":"NULL"

X509 CRT verification #44 (Corrupted signature, EC)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_PK_CAN_ECDSA_VERIFY:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_MD_CAN_SHA256
x509_verify:"data_files/server5-badsign.crt":"data_files/test-ca2.crt":"data_files/crl-ec-sha256.pem":"NULL":MBEDTLS_ERR_X509_CERT_VERIFY_FAILED:MBEDTLS_X509_BADCERT_NOT_TRUSTED:"compat":"NULL"

X509 CRT verification #45 (Corrupted signature, RSA)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_RSA_C:MBEDTLS_PKCS1_V15:MBEDTLS_MD_CAN_SHA1
x509_verify:"data_files/server2-badsign.crt":"data_files/test-ca.crt":"data_files/crl.pem":"NULL":MBEDTLS_ERR_X509_CERT_VERIFY_FAILED:MBEDTLS_X509_BADCERT_NOT_TRUSTED:"compat":"NULL"

X509 CRT verification #45b (Corrupted signature, intermediate CA)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_RSA_C:MBEDTLS_PKCS1_V15:MBEDTLS_MD_CAN_SHA1:MBEDTLS_PK_CAN_ECDSA_SOME:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_MD_CAN_SHA256
x509_verify:"data_files/server7-badsign.crt":"data_files/test-ca2.crt":"data_files/crl.pem":"NULL":MBEDTLS_ERR_X509_CERT_VERIFY_FAILED:MBEDTLS_X509_BADCERT_NOT_TRUSTED:"compat":"NULL"

X509 CRT verification #46 (Valid, depth 2, EC-RSA-EC)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_PK_CAN_ECDSA_VERIFY:MBEDTLS_RSA_C:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_RSA_C:MBEDTLS_PKCS1_V15:MBEDTLS_MD_CAN_SHA256
x509_verify:"data_files/server7_int-ca.crt":"data_files/test-ca2.crt":"data_files/crl-ec-sha256.pem":"NULL":0:0:"compat":"NULL"

X509 CRT verification #47 (Untrusted, depth 2, EC-RSA-EC)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_PK_CAN_ECDSA_VERIFY:MBEDTLS_RSA_C:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_RSA_C:MBEDTLS_PKCS1_V15:MBEDTLS_MD_CAN_SHA1:MBEDTLS_MD_CAN_SHA256
x509_verify:"data_files/server7_int-ca.crt":"data_files/test-ca.crt":"data_files/crl-ec-sha256.pem":"NULL":MBEDTLS_ERR_X509_CERT_VERIFY_FAILED:MBEDTLS_X509_BADCERT_NOT_TRUSTED:"compat":"NULL"

X509 CRT verification #48 (Missing intermediate CA, EC-RSA-EC)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_PK_CAN_ECDSA_VERIFY:MBEDTLS_RSA_C:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_RSA_C:MBEDTLS_PKCS1_V15:MBEDTLS_MD_CAN_SHA1:MBEDTLS_MD_CAN_SHA256
x509_verify:"data_files/server7.crt":"data_files/test-ca.crt":"data_files/crl-ec-sha256.pem":"NULL":MBEDTLS_ERR_X509_CERT_VERIFY_FAILED:MBEDTLS_X509_BADCERT_NOT_TRUSTED:"compat":"NULL"

X509 CRT verification #49 (Valid, depth 2, RSA-EC-RSA)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_PK_CAN_ECDSA_VERIFY:MBEDTLS_RSA_C:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_RSA_C:MBEDTLS_PKCS1_V15:MBEDTLS_MD_CAN_SHA256:MBEDTLS_MD_CAN_SHA1
x509_verify:"data_files/server8_int-ca2.crt":"data_files/test-ca.crt":"data_files/crl-ec-sha256.pem":"NULL":0:0:"compat":"NULL"

X509 CRT verification #50 (Valid, multiple CAs)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_RSA_C:MBEDTLS_PKCS1_V15:MBEDTLS_PK_CAN_ECDSA_VERIFY:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_MD_CAN_SHA1:MBEDTLS_MD_CAN_SHA256
x509_verify:"data_files/server2.crt":"data_files/test-ca_cat12.crt":"data_files/crl.pem":"NULL":0:0:"compat":"NULL"

X509 CRT verification #51 (Valid, multiple CAs, reverse order)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_RSA_C:MBEDTLS_PKCS1_V15:MBEDTLS_PK_CAN_ECDSA_VERIFY:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_MD_CAN_SHA1:MBEDTLS_MD_CAN_SHA256
x509_verify:"data_files/server2.crt":"data_files/test-ca_cat21.crt":"data_files/crl.pem":"NULL":0:0:"compat":"NULL"

X509 CRT verification #52 (CA keyUsage valid)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_PK_CAN_ECDSA_VERIFY:MBEDTLS_MD_CAN_SHA256:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_ECP_HAVE_SECP384R1
x509_verify:"data_files/server5.crt":"data_files/test-ca2.ku-crt_crl.crt":"data_files/crl-ec-sha256.pem":"NULL":0:0:"compat":"NULL"

X509 CRT verification #53 (CA keyUsage missing cRLSign)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_PK_CAN_ECDSA_VERIFY:MBEDTLS_MD_CAN_SHA256:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_ECP_HAVE_SECP384R1
x509_verify:"data_files/server5.crt":"data_files/test-ca2.ku-crt.crt":"data_files/crl-ec-sha256.pem":"NULL":MBEDTLS_ERR_X509_CERT_VERIFY_FAILED:MBEDTLS_X509_BADCRL_NOT_TRUSTED:"compat":"NULL"

X509 CRT verification #54 (CA keyUsage missing cRLSign, no CRL)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_PK_CAN_ECDSA_VERIFY:MBEDTLS_MD_CAN_SHA256:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_MD_CAN_SHA1:MBEDTLS_RSA_C
x509_verify:"data_files/server5.crt":"data_files/test-ca2.ku-crt.crt":"data_files/crl.pem":"NULL":0:0:"compat":"NULL"

X509 CRT verification #55 (CA keyUsage missing keyCertSign)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_PK_CAN_ECDSA_VERIFY:MBEDTLS_MD_CAN_SHA256:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_ECP_HAVE_SECP384R1
x509_verify:"data_files/server5.crt":"data_files/test-ca2.ku-crl.crt":"data_files/crl-ec-sha256.pem":"NULL":MBEDTLS_ERR_X509_CERT_VERIFY_FAILED:MBEDTLS_X509_BADCERT_NOT_TRUSTED:"compat":"NULL"

X509 CRT verification #56 (CA keyUsage plain wrong)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_PK_CAN_ECDSA_VERIFY:MBEDTLS_MD_CAN_SHA256:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_ECP_HAVE_SECP384R1
x509_verify:"data_files/server5.crt":"data_files/test-ca2.ku-ds.crt":"data_files/crl-ec-sha256.pem":"NULL":MBEDTLS_ERR_X509_CERT_VERIFY_FAILED:MBEDTLS_X509_BADCERT_NOT_TRUSTED:"compat":"NULL"

X509 CRT verification #57 (Valid, RSASSA-PSS, SHA-1)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_X509_RSASSA_PSS_SUPPORT:MBEDTLS_MD_CAN_SHA1:MBEDTLS_PKCS1_V15
x509_verify:"data_files/server9.crt":"data_files/test-ca.crt":"data_files/crl.pem":"NULL":0:0:"compat":"NULL"

X509 CRT verification #58 (Valid, RSASSA-PSS, SHA-224)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_X509_RSASSA_PSS_SUPPORT:MBEDTLS_MD_CAN_SHA224:MBEDTLS_MD_CAN_SHA1
x509_verify:"data_files/server9-sha224.crt":"data_files/test-ca.crt":"data_files/crl-rsa-pss-sha224.pem":"NULL":0:0:"compat":"NULL"

X509 CRT verification #59 (Valid, RSASSA-PSS, SHA-256)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_X509_RSASSA_PSS_SUPPORT:MBEDTLS_MD_CAN_SHA256:MBEDTLS_MD_CAN_SHA1
x509_verify:"data_files/server9-sha256.crt":"data_files/test-ca.crt":"data_files/crl-rsa-pss-sha256.pem":"NULL":0:0:"compat":"NULL"

X509 CRT verification #60 (Valid, RSASSA-PSS, SHA-384)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_X509_RSASSA_PSS_SUPPORT:MBEDTLS_MD_CAN_SHA384:MBEDTLS_MD_CAN_SHA1
x509_verify:"data_files/server9-sha384.crt":"data_files/test-ca.crt":"data_files/crl-rsa-pss-sha384.pem":"NULL":0:0:"compat":"NULL"

X509 CRT verification #61 (Valid, RSASSA-PSS, SHA-512)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_X509_RSASSA_PSS_SUPPORT:MBEDTLS_MD_CAN_SHA512:MBEDTLS_MD_CAN_SHA1
x509_verify:"data_files/server9-sha512.crt":"data_files/test-ca.crt":"data_files/crl-rsa-pss-sha512.pem":"NULL":0:0:"compat":"NULL"

X509 CRT verification #62 (Revoked, RSASSA-PSS, SHA-1)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_X509_RSASSA_PSS_SUPPORT:MBEDTLS_MD_CAN_SHA1:MBEDTLS_HAVE_TIME_DATE
x509_verify:"data_files/server9.crt":"data_files/test-ca.crt":"data_files/crl-rsa-pss-sha1.pem":"NULL":MBEDTLS_ERR_X509_CERT_VERIFY_FAILED:MBEDTLS_X509_BADCERT_REVOKED:"compat":"NULL"

X509 CRT verification #63 (Revoked, RSASSA-PSS, SHA-1, CRL badsign)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_X509_RSASSA_PSS_SUPPORT:MBEDTLS_MD_CAN_SHA1
x509_verify:"data_files/server9.crt":"data_files/test-ca.crt":"data_files/crl-rsa-pss-sha1-badsign.pem":"NULL":MBEDTLS_ERR_X509_CERT_VERIFY_FAILED:MBEDTLS_X509_BADCRL_NOT_TRUSTED:"compat":"NULL"

X509 CRT verification #64 (Valid, RSASSA-PSS, SHA-1, not top)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_X509_RSASSA_PSS_SUPPORT:MBEDTLS_MD_CAN_SHA1:MBEDTLS_RSA_C:MBEDTLS_PKCS1_V15
x509_verify:"data_files/server9-with-ca.crt":"data_files/test-ca.crt":"data_files/crl.pem":"NULL":0:0:"compat":"NULL"

X509 CRT verification #65 (RSASSA-PSS, SHA1, bad cert signature)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_X509_RSASSA_PSS_SUPPORT:MBEDTLS_MD_CAN_SHA1
x509_verify:"data_files/server9-badsign.crt":"data_files/test-ca.crt":"data_files/crl.pem":"NULL":MBEDTLS_ERR_X509_CERT_VERIFY_FAILED:MBEDTLS_X509_BADCERT_NOT_TRUSTED:"compat":"NULL"

X509 CRT verification #66 (RSASSA-PSS, SHA1, no RSA CA)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_X509_RSASSA_PSS_SUPPORT:MBEDTLS_MD_CAN_SHA1:MBEDTLS_PK_CAN_ECDSA_VERIFY:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_MD_CAN_SHA256
x509_verify:"data_files/server9.crt":"data_files/test-ca2.crt":"data_files/crl.pem":"NULL":MBEDTLS_ERR_X509_CERT_VERIFY_FAILED:MBEDTLS_X509_BADCERT_NOT_TRUSTED:"compat":"NULL"

X509 CRT verification #67 (Valid, RSASSA-PSS, all defaults)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_X509_RSASSA_PSS_SUPPORT:MBEDTLS_MD_CAN_SHA1
x509_verify:"data_files/server9-defaults.crt":"data_files/test-ca.crt":"data_files/crl-rsa-pss-sha1.pem":"NULL":0:0:"compat":"NULL"

X509 CRT verification #68 (RSASSA-PSS, wrong salt_len, !USE_PSA)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_X509_RSASSA_PSS_SUPPORT:MBEDTLS_MD_CAN_SHA256:MBEDTLS_MD_CAN_SHA1:!MBEDTLS_USE_PSA_CRYPTO
x509_verify:"data_files/server9-bad-saltlen.crt":"data_files/test-ca.crt":"data_files/crl-rsa-pss-sha1.pem":"NULL":MBEDTLS_ERR_X509_CERT_VERIFY_FAILED:MBEDTLS_X509_BADCERT_NOT_TRUSTED:"compat":"NULL"

X509 CRT verification #68 (RSASSA-PSS, wrong salt_len, USE_PSA)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_X509_RSASSA_PSS_SUPPORT:MBEDTLS_MD_CAN_SHA256:MBEDTLS_MD_CAN_SHA1:MBEDTLS_USE_PSA_CRYPTO
x509_verify:"data_files/server9-bad-saltlen.crt":"data_files/test-ca.crt":"data_files/crl-rsa-pss-sha1.pem":"NULL":0:0:"compat":"NULL"

X509 CRT verification #69 (RSASSA-PSS, wrong mgf_hash)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_X509_RSASSA_PSS_SUPPORT:MBEDTLS_MD_CAN_SHA256:MBEDTLS_MD_CAN_SHA224:MBEDTLS_MD_CAN_SHA1
x509_verify:"data_files/server9-bad-mgfhash.crt":"data_files/test-ca.crt":"data_files/crl.pem":"NULL":MBEDTLS_ERR_X509_CERT_VERIFY_FAILED:MBEDTLS_X509_BADCERT_NOT_TRUSTED:"compat":"NULL"

X509 CRT verification #70 (v1 trusted CA)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_RSA_C:MBEDTLS_PKCS1_V15:MBEDTLS_MD_CAN_SHA256:MBEDTLS_MD_CAN_SHA1
x509_verify:"data_files/server1-v1.crt":"data_files/test-ca-v1.crt":"data_files/crl.pem":"NULL":0:0:"compat":"NULL"

X509 CRT verification #71 (v1 trusted CA, other)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_RSA_C:MBEDTLS_PKCS1_V15:MBEDTLS_MD_CAN_SHA256:MBEDTLS_MD_CAN_SHA1
x509_verify:"data_files/server2-v1.crt":"data_files/server1-v1.crt":"data_files/crl.pem":"NULL":0:0:"compat":"NULL"

X509 CRT verification #72 (v1 chain)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_RSA_C:MBEDTLS_PKCS1_V15:MBEDTLS_MD_CAN_SHA256:MBEDTLS_MD_CAN_SHA1
x509_verify:"data_files/server2-v1-chain.crt":"data_files/test-ca-v1.crt":"data_files/crl.pem":"NULL":MBEDTLS_ERR_X509_CERT_VERIFY_FAILED:MBEDTLS_X509_BADCERT_NOT_TRUSTED:"compat":"NULL"

X509 CRT verification #73 (selfsigned trusted without CA bit)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_PK_CAN_ECDSA_VERIFY:MBEDTLS_MD_CAN_SHA256:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_MD_CAN_SHA1:MBEDTLS_RSA_C
x509_verify:"data_files/server5-selfsigned.crt":"data_files/server5-selfsigned.crt":"data_files/crl.pem":"NULL":0:0:"compat":"NULL"

X509 CRT verification #74 (signed by selfsigned trusted without CA bit)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_PK_CAN_ECDSA_VERIFY:MBEDTLS_MD_CAN_SHA256:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_MD_CAN_SHA1:MBEDTLS_RSA_C
x509_verify:"data_files/server6-ss-child.crt":"data_files/server5-selfsigned.crt":"data_files/crl.pem":"NULL":MBEDTLS_ERR_X509_CERT_VERIFY_FAILED:MBEDTLS_X509_BADCERT_NOT_TRUSTED:"compat":"NULL"

X509 CRT verification #75 (encoding mismatch)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_MD_CAN_SHA1:MBEDTLS_RSA_C:MBEDTLS_PKCS1_V15
x509_verify:"data_files/enco-cert-utf8str.pem":"data_files/enco-ca-prstr.pem":"data_files/crl.pem":"NULL":0:0:"compat":"NULL"

X509 CRT verification #76 (multiple CRLs, not revoked)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_PK_CAN_ECDSA_VERIFY:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_MD_CAN_SHA256:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA1
x509_verify:"data_files/server5.crt":"data_files/test-ca_cat12.crt":"data_files/crl_cat_ec-rsa.pem":"NULL":0:0:"compat":"NULL"

X509 CRT verification #77 (multiple CRLs, revoked)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_PK_CAN_ECDSA_VERIFY:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_MD_CAN_SHA256:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA1:MBEDTLS_HAVE_TIME_DATE
x509_verify:"data_files/server6.crt":"data_files/test-ca_cat12.crt":"data_files/crl_cat_ec-rsa.pem":"NULL":MBEDTLS_ERR_X509_CERT_VERIFY_FAILED:MBEDTLS_X509_BADCERT_REVOKED:"compat":"NULL"

X509 CRT verification #78 (multiple CRLs, revoked by second)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_PK_CAN_ECDSA_VERIFY:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_MD_CAN_SHA256:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA1:MBEDTLS_HAVE_TIME_DATE
x509_verify:"data_files/server6.crt":"data_files/test-ca_cat12.crt":"data_files/crl_cat_rsa-ec.pem":"NULL":MBEDTLS_ERR_X509_CERT_VERIFY_FAILED:MBEDTLS_X509_BADCERT_REVOKED:"compat":"NULL"

X509 CRT verification #79 (multiple CRLs, revoked by future)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_PK_CAN_ECDSA_VERIFY:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_MD_CAN_SHA256:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA1:MBEDTLS_HAVE_TIME_DATE
x509_verify:"data_files/server6.crt":"data_files/test-ca_cat12.crt":"data_files/crl_cat_ecfut-rsa.pem":"NULL":MBEDTLS_ERR_X509_CERT_VERIFY_FAILED:MBEDTLS_X509_BADCERT_REVOKED|MBEDTLS_X509_BADCRL_FUTURE:"compat":"NULL"

X509 CRT verification #80 (multiple CRLs, first future, revoked by second)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_PK_CAN_ECDSA_VERIFY:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_MD_CAN_SHA256:MBEDTLS_RSA_C:MBEDTLS_PKCS1_V15:MBEDTLS_MD_CAN_SHA1:MBEDTLS_HAVE_TIME_DATE
x509_verify:"data_files/server1.crt":"data_files/test-ca_cat12.crt":"data_files/crl_cat_ecfut-rsa.pem":"NULL":MBEDTLS_ERR_X509_CERT_VERIFY_FAILED:MBEDTLS_X509_BADCERT_REVOKED:"compat":"NULL"

X509 CRT verification #81 (multiple CRLs, none relevant)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_PK_CAN_ECDSA_VERIFY:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_MD_CAN_SHA256:MBEDTLS_RSA_C:MBEDTLS_PKCS1_V15:MBEDTLS_MD_CAN_SHA1
x509_verify:"data_files/enco-cert-utf8str.pem":"data_files/enco-ca-prstr.pem":"data_files/crl_cat_rsa-ec.pem":"NULL":0:0:"compat":"NULL"

X509 CRT verification #82 (Not yet valid CA and valid CA)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_PK_CAN_ECDSA_VERIFY:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_MD_CAN_SHA1:MBEDTLS_MD_CAN_SHA256
x509_verify:"data_files/server5.crt":"data_files/test-ca2_cat-future-present.crt":"data_files/crl-ec-sha1.pem":"NULL":0:0:"compat":"NULL"

X509 CRT verification #83 (valid CA and Not yet valid CA)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_PK_CAN_ECDSA_VERIFY:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_MD_CAN_SHA1:MBEDTLS_MD_CAN_SHA256
x509_verify:"data_files/server5.crt":"data_files/test-ca2_cat-present-future.crt":"data_files/crl-ec-sha1.pem":"NULL":0:0:"compat":"NULL"

X509 CRT verification #84 (valid CA and Not yet valid CA)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_PK_CAN_ECDSA_VERIFY:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_MD_CAN_SHA1:MBEDTLS_MD_CAN_SHA256
x509_verify:"data_files/server5.crt":"data_files/test-ca2_cat-present-past.crt":"data_files/crl-ec-sha1.pem":"NULL":0:0:"compat":"NULL"

X509 CRT verification #85 (Not yet valid CA and valid CA)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_PK_CAN_ECDSA_VERIFY:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_MD_CAN_SHA1:MBEDTLS_MD_CAN_SHA256
x509_verify:"data_files/server5.crt":"data_files/test-ca2_cat-past-present.crt":"data_files/crl-ec-sha1.pem":"NULL":0:0:"compat":"NULL"

X509 CRT verification #86 (Not yet valid CA and invalid CA)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_PK_CAN_ECDSA_VERIFY:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_MD_CAN_SHA1:MBEDTLS_MD_CAN_SHA256:MBEDTLS_HAVE_TIME_DATE
x509_verify:"data_files/server5.crt":"data_files/test-ca2_cat-future-invalid.crt":"data_files/crl-ec-sha1.pem":"NULL":MBEDTLS_ERR_X509_CERT_VERIFY_FAILED:MBEDTLS_X509_BADCERT_FUTURE:"compat":"NULL"

X509 CRT verification #87 (Expired CA and invalid CA)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_PK_CAN_ECDSA_VERIFY:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_MD_CAN_SHA1:MBEDTLS_MD_CAN_SHA256:MBEDTLS_HAVE_TIME_DATE
x509_verify:"data_files/server5.crt":"data_files/test-ca2_cat-past-invalid.crt":"data_files/crl-ec-sha1.pem":"NULL":MBEDTLS_ERR_X509_CERT_VERIFY_FAILED:MBEDTLS_X509_BADCERT_EXPIRED:"compat":"NULL"

X509 CRT verification #88 (Spurious cert in the chain)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_PK_CAN_ECDSA_VERIFY:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_MD_CAN_SHA1:MBEDTLS_MD_CAN_SHA256:MBEDTLS_RSA_C:MBEDTLS_PKCS1_V15
x509_verify:"data_files/server7_spurious_int-ca.crt":"data_files/test-ca2.crt":"data_files/crl-ec-sha1.pem":"NULL":0:0:"compat":"NULL"

X509 CRT verification #89 (Spurious cert later in the chain)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_PK_CAN_ECDSA_VERIFY:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_MD_CAN_SHA1:MBEDTLS_MD_CAN_SHA256:MBEDTLS_RSA_C:MBEDTLS_PKCS1_V15
x509_verify:"data_files/server10_int3_spurious_int-ca2.crt":"data_files/test-ca.crt":"data_files/crl-ec-sha1.pem":"NULL":0:0:"compat":"NULL"

X509 CRT verification #90 (EE with same name as trusted root)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_PK_CAN_ECDSA_VERIFY:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_MD_CAN_SHA256:MBEDTLS_MD_CAN_SHA1
x509_verify:"data_files/server5-ss-forgeca.crt":"data_files/test-int-ca3.crt":"data_files/crl-ec-sha1.pem":"NULL":MBEDTLS_ERR_X509_CERT_VERIFY_FAILED:MBEDTLS_X509_BADCERT_NOT_TRUSTED:"":"NULL"

X509 CRT verification #91 (same CA with good then bad key)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_MD_CAN_SHA1:MBEDTLS_RSA_C:MBEDTLS_PKCS1_V15:MBEDTLS_MD_CAN_SHA256:MBEDTLS_PK_CAN_ECDSA_VERIFY
x509_verify:"data_files/server1.crt":"data_files/test-ca-good-alt.crt":"data_files/crl-ec-sha256.pem":"NULL":0:0:"compat":"NULL"

X509 CRT verification #91 (same CA with bad then good key)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_MD_CAN_SHA1:MBEDTLS_RSA_C:MBEDTLS_PKCS1_V15:MBEDTLS_MD_CAN_SHA256:MBEDTLS_PK_CAN_ECDSA_VERIFY
x509_verify:"data_files/server1.crt":"data_files/test-ca-alt-good.crt":"data_files/crl-ec-sha256.pem":"NULL":0:0:"compat":"NULL"

X509 CRT verification #92 (bad name, allowing callback)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_PK_CAN_ECDSA_VERIFY:MBEDTLS_MD_CAN_SHA256:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_ECP_HAVE_SECP384R1
x509_verify:"data_files/server5.crt":"data_files/test-ca2.crt":"data_files/crl-ec-sha256.pem":"globalhost":0:0:"":"verify_all"

X509 CRT verification #93 (Suite B invalid, EC cert, RSA CA)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_RSA_C:MBEDTLS_PK_CAN_ECDSA_VERIFY:MBEDTLS_ECP_HAVE_SECP192R1:MBEDTLS_PKCS1_V15:MBEDTLS_MD_CAN_SHA1
x509_verify:"data_files/server3.crt":"data_files/test-ca.crt":"data_files/crl.pem":"NULL":MBEDTLS_ERR_X509_CERT_VERIFY_FAILED:MBEDTLS_X509_BADCERT_BAD_MD|MBEDTLS_X509_BADCERT_BAD_PK|MBEDTLS_X509_BADCERT_BAD_KEY|MBEDTLS_X509_BADCRL_BAD_MD|MBEDTLS_X509_BADCRL_BAD_PK:"suite_b":"NULL"

X509 CRT verification #94 (Suite B invalid, RSA cert, EC CA)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_RSA_C:MBEDTLS_PK_CAN_ECDSA_VERIFY:MBEDTLS_MD_CAN_SHA256:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_PKCS1_V15:MBEDTLS_ECP_HAVE_SECP384R1
x509_verify:"data_files/server4.crt":"data_files/test-ca2.crt":"data_files/crl-ec-sha256.pem":"NULL":MBEDTLS_ERR_X509_CERT_VERIFY_FAILED:MBEDTLS_X509_BADCERT_BAD_PK:"suite_b":"NULL"

X509 CRT verification #95 (Suite B Valid, EC cert, EC CA)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_PK_CAN_ECDSA_VERIFY:MBEDTLS_MD_CAN_SHA256:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_ECP_HAVE_SECP384R1
x509_verify:"data_files/server5.crt":"data_files/test-ca2.crt":"data_files/crl-ec-sha256.pem":"NULL":0:0:"suite_b":"NULL"

X509 CRT verification #96 (next profile Invalid Cert SHA224 Digest)
depends_on:MBEDTLS_MD_CAN_SHA224:MBEDTLS_PEM_PARSE_C:MBEDTLS_RSA_C:MBEDTLS_PKCS1_V15:MBEDTLS_MD_CAN_SHA1
x509_verify:"data_files/cert_sha224.crt":"data_files/test-ca.crt":"data_files/crl.pem":"NULL":MBEDTLS_ERR_X509_CERT_VERIFY_FAILED:MBEDTLS_X509_BADCERT_BAD_MD|MBEDTLS_X509_BADCRL_BAD_MD:"next":"NULL"

X509 CRT verification #97 (next profile Valid Cert SHA256 Digest)
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_PEM_PARSE_C:MBEDTLS_RSA_C:MBEDTLS_PKCS1_V15:MBEDTLS_PK_CAN_ECDSA_VERIFY:MBEDTLS_MD_CAN_SHA1
x509_verify:"data_files/cert_sha256.crt":"data_files/test-ca.crt":"data_files/crl-ec-sha256.pem":"NULL":0:0:"next":"NULL"

X509 CRT verification #98 (Revoked Cert, revocation date in the future, _with_ MBEDTLS_HAVE_TIME_DATE)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_MD_CAN_SHA1:MBEDTLS_RSA_C:MBEDTLS_PKCS1_V15:MBEDTLS_HAVE_TIME_DATE
x509_verify:"data_files/server1.crt":"data_files/test-ca.crt":"data_files/crl-futureRevocationDate.pem":"NULL":MBEDTLS_ERR_X509_CERT_VERIFY_FAILED:MBEDTLS_X509_BADCERT_REVOKED|MBEDTLS_X509_BADCRL_FUTURE:"compat":"NULL"

X509 CRT verification #99 (Revoked Cert, revocation date in the future, _without_ MBEDTLS_HAVE_TIME_DATE)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_MD_CAN_SHA1:MBEDTLS_RSA_C:MBEDTLS_PKCS1_V15:!MBEDTLS_HAVE_TIME_DATE
x509_verify:"data_files/server1.crt":"data_files/test-ca.crt":"data_files/crl-futureRevocationDate.pem":"NULL":MBEDTLS_ERR_X509_CERT_VERIFY_FAILED:MBEDTLS_X509_BADCERT_REVOKED:"compat":"NULL"

X509 CRT verification: domain identical to IPv4 in SubjectAltName
depends_on:MBEDTLS_PK_CAN_ECDSA_VERIFY:MBEDTLS_MD_CAN_SHA256:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_RSA_C
x509_verify:"data_files/server5-tricky-ip-san.crt.der":"data_files/server5-tricky-ip-san.crt.der":"data_files/crl_sha256.pem":"abcd":MBEDTLS_ERR_X509_CERT_VERIFY_FAILED:MBEDTLS_X509_BADCERT_CN_MISMATCH:"":"NULL"

X509 CRT verification: domain identical to IPv6 in SubjectAltName
depends_on:MBEDTLS_PK_CAN_ECDSA_VERIFY:MBEDTLS_MD_CAN_SHA256:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_RSA_C
x509_verify:"data_files/server5-tricky-ip-san.crt.der":"data_files/server5-tricky-ip-san.crt.der":"data_files/crl_sha256.pem":"abcd.example.com":MBEDTLS_ERR_X509_CERT_VERIFY_FAILED:MBEDTLS_X509_BADCERT_CN_MISMATCH:"":"NULL"

X509 CRT verification: matching IPv4 in SubjectAltName
depends_on:MBEDTLS_PK_CAN_ECDSA_SOME:MBEDTLS_MD_CAN_SHA256:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_RSA_C
x509_verify:"data_files/server5-tricky-ip-san.crt.der":"data_files/server5-tricky-ip-san.crt.der":"data_files/crl_sha256.pem":"************":0:0:"":"NULL"

X509 CRT verification: mismatching IPv4 in SubjectAltName
depends_on:MBEDTLS_PK_CAN_ECDSA_SOME:MBEDTLS_MD_CAN_SHA256:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_RSA_C
x509_verify:"data_files/server5-tricky-ip-san.crt.der":"data_files/server5-tricky-ip-san.crt.der":"data_files/crl_sha256.pem":"********":MBEDTLS_ERR_X509_CERT_VERIFY_FAILED:MBEDTLS_X509_BADCERT_CN_MISMATCH:"":"NULL"

X509 CRT verification: IPv4 with trailing data in SubjectAltName
depends_on:MBEDTLS_PK_CAN_ECDSA_SOME:MBEDTLS_MD_CAN_SHA256:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_RSA_C
x509_verify:"data_files/server5-tricky-ip-san.crt.der":"data_files/server5-tricky-ip-san.crt.der":"data_files/crl_sha256.pem":"************?":MBEDTLS_ERR_X509_CERT_VERIFY_FAILED:MBEDTLS_X509_BADCERT_CN_MISMATCH:"":"NULL"

X509 CRT verification: matching IPv6 in SubjectAltName
depends_on:MBEDTLS_PK_CAN_ECDSA_SOME:MBEDTLS_MD_CAN_SHA256:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_RSA_C
x509_verify:"data_files/server5-tricky-ip-san.crt.der":"data_files/server5-tricky-ip-san.crt.der":"data_files/crl_sha256.pem":"6162\:6364\:2E65\:7861\:6D70\:6C65\:2E63\:6F6D":0:0:"":"NULL"

X509 CRT verification: mismatching IPv6 in SubjectAltName
depends_on:MBEDTLS_PK_CAN_ECDSA_SOME:MBEDTLS_MD_CAN_SHA256:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_RSA_C
x509_verify:"data_files/server5-tricky-ip-san.crt.der":"data_files/server5-tricky-ip-san.crt.der":"data_files/crl_sha256.pem":"6162\:6364\:\:6F6D":MBEDTLS_ERR_X509_CERT_VERIFY_FAILED:MBEDTLS_X509_BADCERT_CN_MISMATCH:"":"NULL"

X509 CRT verification: matching URI in SubjectAltName
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_PK_CAN_ECDSA_SOME:MBEDTLS_MD_CAN_SHA256:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_RSA_C
x509_verify:"data_files/rsa_single_san_uri.crt.der":"data_files/rsa_single_san_uri.crt.der":"data_files/crl_sha256.pem":"urn\:example.com\:5ff40f78-9210-494f-8206-c2c082f0609c":0:0:"":"NULL"

X509 CRT verification: URI with trailing data in SubjectAltName
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_PK_CAN_ECDSA_SOME:MBEDTLS_MD_CAN_SHA256:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_RSA_C
x509_verify:"data_files/rsa_single_san_uri.crt.der":"data_files/rsa_single_san_uri.crt.der":"data_files/crl_sha256.pem":"urn\:example.com\:5ff40f78-9210-494f-8206-c2c082f0609cz":MBEDTLS_ERR_X509_CERT_VERIFY_FAILED:MBEDTLS_X509_BADCERT_CN_MISMATCH:"":"NULL"

X509 CRT verification: URI with preceding data in SubjectAltName
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_PK_CAN_ECDSA_SOME:MBEDTLS_MD_CAN_SHA256:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_RSA_C
x509_verify:"data_files/rsa_single_san_uri.crt.der":"data_files/rsa_single_san_uri.crt.der":"data_files/crl_sha256.pem":"zurn\:example.com\:5ff40f78-9210-494f-8206-c2c082f0609c":MBEDTLS_ERR_X509_CERT_VERIFY_FAILED:MBEDTLS_X509_BADCERT_CN_MISMATCH:"":"NULL"

X509 CRT verification: URI with bad data in SubjectAltName
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_PK_CAN_ECDSA_SOME:MBEDTLS_MD_CAN_SHA256:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_RSA_C
x509_verify:"data_files/rsa_single_san_uri.crt.der":"data_files/rsa_single_san_uri.crt.der":"data_files/crl_sha256.pem":"bad\:example.com\:5ff40f78-9210-494f-8206-c2c082f0609c":MBEDTLS_ERR_X509_CERT_VERIFY_FAILED:MBEDTLS_X509_BADCERT_CN_MISMATCH:"":"NULL"

X509 CRT parse CN: IPv4 valid address
x509_crt_parse_cn_inet_pton:"***********":"0A0A0A0A":4

X509 CRT parse CN: IPv4 leading zeroes #1
x509_crt_parse_cn_inet_pton:"0***********":"":0

X509 CRT parse CN: IPv4 leading zeroes #2
x509_crt_parse_cn_inet_pton:"************":"":0

X509 CRT parse CN: IPv4 excess 0s
x509_crt_parse_cn_inet_pton:"10.0000.10.10":"":0

X509 CRT parse CN: IPv4 short address
x509_crt_parse_cn_inet_pton:"10.10.10":"":0

X509 CRT parse CN: IPv4 invalid ? char
x509_crt_parse_cn_inet_pton:"10.10?10.10":"":0

X509 CRT parse CN: IPv4 invalid - char
x509_crt_parse_cn_inet_pton:"10.-10.10.10":"":0

X509 CRT parse CN: IPv4 invalid + char
x509_crt_parse_cn_inet_pton:"10.+10.10.10":"":0

X509 CRT parse CN: IPv4 begin dot
x509_crt_parse_cn_inet_pton:".***********":"":0

X509 CRT parse CN: IPv4 end dot
x509_crt_parse_cn_inet_pton:"***********.":"":0

X509 CRT parse CN: IPv4 consecutive dots
x509_crt_parse_cn_inet_pton:"10.10..10.10.":"":0

X509 CRT parse CN: IPv4 overlarge octet 256
x509_crt_parse_cn_inet_pton:"10.256.10.10":"":0

X509 CRT parse CN: IPv4 overlarge octet 999
x509_crt_parse_cn_inet_pton:"10.10.10.999":"":0

X509 CRT parse CN: IPv4 overlarge octet 1000
x509_crt_parse_cn_inet_pton:"10.1000.10.10":"":0

X509 CRT parse CN: IPv4 additional octet
x509_crt_parse_cn_inet_pton:"***********.10":"":0

X509 CRT parse CN: IPv6 valid address
x509_crt_parse_cn_inet_pton:"1\:2\:3\:4\:5\:6\:7\:8":"00010002000300040005000600070008":16

X509 CRT parse CN: IPv6 valid address shorthand
x509_crt_parse_cn_inet_pton:"6263\:\:1":"62630000000000000000000000000001":16

X509 CRT parse CN: IPv6 valid address shorthand start
x509_crt_parse_cn_inet_pton:"\:\:1":"00000000000000000000000000000001":16

X509 CRT parse CN: IPv6 valid address extra 0s
x509_crt_parse_cn_inet_pton:"0001\:\:0001\:0001":"00010000000000000000000000010001":16

X509 CRT parse CN: IPv6 invalid address excess 0s
x509_crt_parse_cn_inet_pton:"1\:00000\:1\:0":"":0

X509 CRT parse CN: IPv6 invalid address - start single colon
x509_crt_parse_cn_inet_pton:"\:6263\:\:1":"":0

X509 CRT parse CN: IPv6 invalid address - end single colon
x509_crt_parse_cn_inet_pton:"6263\:\:1\:":"":0

X509 CRT parse CN: IPv6 short address
x509_crt_parse_cn_inet_pton:"1\:1\:1":"":0

X509 CRT parse CN: IPv6 wildcard address
x509_crt_parse_cn_inet_pton:"\:\:":"00000000000000000000000000000000":16

X509 CRT parse CN: IPv6 address too long
x509_crt_parse_cn_inet_pton:"1\:2\:3\:4\:5\:6\:7\:8\:9":"":0

X509 CRT parse CN: IPv6 long hextet
x509_crt_parse_cn_inet_pton:"12345\:\:1":"":0

X509 CRT parse CN: IPv6 invalid char
x509_crt_parse_cn_inet_pton:"\:\:\:1":"":0

X509 CRT parse CN: IPv6 invalid - char
x509_crt_parse_cn_inet_pton:"\:\:-1\:1":"":0

X509 CRT parse CN: IPv6 invalid + char
x509_crt_parse_cn_inet_pton:"\:\:+1\:1":"":0

X509 CRT parse CN: IPv6 valid address IPv4-mapped
x509_crt_parse_cn_inet_pton:"\:\:ffff\:*******":"00000000000000000000ffff01020304":16

X509 CRT parse CN: IPv6 invalid address IPv4-mapped #1
x509_crt_parse_cn_inet_pton:"\:\:ffff\:999.2.3.4":"":0

X509 CRT parse CN: IPv6 invalid address IPv4-mapped #2
x509_crt_parse_cn_inet_pton:"\:\:ffff\:111*******":"":0

X509 CRT parse CN: IPv6 invalid address IPv4-mapped #3
x509_crt_parse_cn_inet_pton:"\:\:*******\:ffff":"":0

X509 CRT verification with ca callback: failure
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_MD_CAN_SHA1:MBEDTLS_RSA_C:MBEDTLS_PKCS1_V15:MBEDTLS_X509_TRUSTED_CERTIFICATE_CALLBACK
x509_verify_ca_cb_failure:"data_files/server1.crt":"data_files/test-ca.crt":"NULL":MBEDTLS_ERR_X509_FATAL_ERROR

X509 CRT verification callback: bad name
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_PK_CAN_ECDSA_VERIFY:MBEDTLS_MD_CAN_SHA256:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_ECP_HAVE_SECP384R1
x509_verify_callback:"data_files/server5.crt":"data_files/test-ca2.crt":"globalhost":MBEDTLS_ERR_X509_CERT_VERIFY_FAILED:"depth 1 - serial C1\:43\:E2\:7E\:62\:43\:CC\:E8 - subject C=NL, O=PolarSSL, CN=Polarssl Test EC CA - flags 0x00000000\ndepth 0 - serial 09 - subject C=NL, O=PolarSSL, CN=localhost - flags 0x00000004\n"

X509 CRT verification callback: trusted EE cert
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_PK_CAN_ECDSA_VERIFY:MBEDTLS_MD_CAN_SHA256:MBEDTLS_ECP_HAVE_SECP256R1
x509_verify_callback:"data_files/server5-selfsigned.crt":"data_files/server5-selfsigned.crt":"NULL":0:"depth 0 - serial 53\:A2\:CB\:4B\:12\:4E\:AD\:83\:7D\:A8\:94\:B2 - subject CN=selfsigned, OU=testing, O=PolarSSL, C=NL - flags 0x00000000\n"

X509 CRT verification callback: trusted EE cert, expired
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_PK_CAN_ECDSA_VERIFY:MBEDTLS_MD_CAN_SHA256:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_HAVE_TIME_DATE
x509_verify_callback:"data_files/server5-ss-expired.crt":"data_files/server5-ss-expired.crt":"NULL":MBEDTLS_ERR_X509_CERT_VERIFY_FAILED:"depth 0 - serial D8\:64\:61\:05\:E3\:A3\:CD\:78 - subject C=UK, O=mbed TLS, OU=testsuite, CN=localhost - flags 0x00000001\n"

X509 CRT verification callback: simple
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_MD_CAN_SHA1:MBEDTLS_RSA_C:MBEDTLS_PKCS1_V15
x509_verify_callback:"data_files/server1.crt":"data_files/test-ca.crt":"NULL":0:"depth 1 - serial 03 - subject C=NL, O=PolarSSL, CN=PolarSSL Test CA - flags 0x00000000\ndepth 0 - serial 01 - subject C=NL, O=PolarSSL, CN=PolarSSL Server 1 - flags 0x00000000\n"

X509 CRT verification callback: simple, EE expired
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_MD_CAN_SHA256:MBEDTLS_PK_CAN_ECDSA_VERIFY:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_MD_CAN_SHA1:MBEDTLS_HAVE_TIME_DATE
x509_verify_callback:"data_files/server5-expired.crt":"data_files/test-ca2.crt":"NULL":MBEDTLS_ERR_X509_CERT_VERIFY_FAILED:"depth 1 - serial C1\:43\:E2\:7E\:62\:43\:CC\:E8 - subject C=NL, O=PolarSSL, CN=Polarssl Test EC CA - flags 0x00000000\ndepth 0 - serial 1E - subject C=NL, O=PolarSSL, CN=localhost - flags 0x00000001\n"

X509 CRT verification callback: simple, root expired
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_MD_CAN_SHA256:MBEDTLS_PK_CAN_ECDSA_VERIFY:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_MD_CAN_SHA1:MBEDTLS_HAVE_TIME_DATE
x509_verify_callback:"data_files/server5.crt":"data_files/test-ca2-expired.crt":"NULL":MBEDTLS_ERR_X509_CERT_VERIFY_FAILED:"depth 1 - serial 01 - subject C=NL, O=PolarSSL, CN=Polarssl Test EC CA - flags 0x00000001\ndepth 0 - serial 09 - subject C=NL, O=PolarSSL, CN=localhost - flags 0x00000000\n"

X509 CRT verification callback: two trusted roots
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_MD_CAN_SHA1:MBEDTLS_RSA_C:MBEDTLS_PKCS1_V15:MBEDTLS_PK_CAN_ECDSA_VERIFY:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_MD_CAN_SHA256
x509_verify_callback:"data_files/server1.crt":"data_files/test-ca_cat12.crt":"NULL":0:"depth 1 - serial 03 - subject C=NL, O=PolarSSL, CN=PolarSSL Test CA - flags 0x00000000\ndepth 0 - serial 01 - subject C=NL, O=PolarSSL, CN=PolarSSL Server 1 - flags 0x00000000\n"

X509 CRT verification callback: two trusted roots, reversed order
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_MD_CAN_SHA1:MBEDTLS_RSA_C:MBEDTLS_PKCS1_V15:MBEDTLS_PK_CAN_ECDSA_VERIFY:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_MD_CAN_SHA256
x509_verify_callback:"data_files/server1.crt":"data_files/test-ca_cat21.crt":"NULL":0:"depth 1 - serial 03 - subject C=NL, O=PolarSSL, CN=PolarSSL Test CA - flags 0x00000000\ndepth 0 - serial 01 - subject C=NL, O=PolarSSL, CN=PolarSSL Server 1 - flags 0x00000000\n"

X509 CRT verification callback: root included
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_MD_CAN_SHA1:MBEDTLS_RSA_C:MBEDTLS_PKCS1_V15:MBEDTLS_PK_CAN_ECDSA_VERIFY:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_MD_CAN_SHA256
x509_verify_callback:"data_files/server1_ca.crt":"data_files/test-ca_cat21.crt":"NULL":0:"depth 1 - serial 03 - subject C=NL, O=PolarSSL, CN=PolarSSL Test CA - flags 0x00000000\ndepth 0 - serial 01 - subject C=NL, O=PolarSSL, CN=PolarSSL Server 1 - flags 0x00000000\n"

X509 CRT verification callback: intermediate ca
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_PK_CAN_ECDSA_VERIFY:MBEDTLS_RSA_C:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_RSA_C:MBEDTLS_PKCS1_V15:MBEDTLS_MD_CAN_SHA256:MBEDTLS_MD_CAN_SHA1
x509_verify_callback:"data_files/server7_int-ca.crt":"data_files/test-ca_cat12.crt":"NULL":0:"depth 2 - serial C1\:43\:E2\:7E\:62\:43\:CC\:E8 - subject C=NL, O=PolarSSL, CN=Polarssl Test EC CA - flags 0x00000000\ndepth 1 - serial 0E - subject C=NL, O=PolarSSL, CN=PolarSSL Test Intermediate CA - flags 0x00000000\ndepth 0 - serial 10 - subject C=NL, O=PolarSSL, CN=localhost - flags 0x00000000\n"

X509 CRT verification callback: intermediate ca, root included
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_PK_CAN_ECDSA_VERIFY:MBEDTLS_RSA_C:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_RSA_C:MBEDTLS_PKCS1_V15:MBEDTLS_MD_CAN_SHA256:MBEDTLS_MD_CAN_SHA1
x509_verify_callback:"data_files/server7_int-ca_ca2.crt":"data_files/test-ca_cat12.crt":"NULL":0:"depth 2 - serial C1\:43\:E2\:7E\:62\:43\:CC\:E8 - subject C=NL, O=PolarSSL, CN=Polarssl Test EC CA - flags 0x00000000\ndepth 1 - serial 0E - subject C=NL, O=PolarSSL, CN=PolarSSL Test Intermediate CA - flags 0x00000000\ndepth 0 - serial 10 - subject C=NL, O=PolarSSL, CN=localhost - flags 0x00000000\n"

X509 CRT verification callback: intermediate ca trusted
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_PK_CAN_ECDSA_VERIFY:MBEDTLS_RSA_C:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_RSA_C:MBEDTLS_PKCS1_V15:MBEDTLS_MD_CAN_SHA256
x509_verify_callback:"data_files/server7_int-ca_ca2.crt":"data_files/test-int-ca.crt":"NULL":0:"depth 1 - serial 0E - subject C=NL, O=PolarSSL, CN=PolarSSL Test Intermediate CA - flags 0x00000000\ndepth 0 - serial 10 - subject C=NL, O=PolarSSL, CN=localhost - flags 0x00000000\n"

X509 CRT verification callback: intermediate ca, EE expired
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_PK_CAN_ECDSA_VERIFY:MBEDTLS_RSA_C:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_RSA_C:MBEDTLS_PKCS1_V15:MBEDTLS_MD_CAN_SHA256:MBEDTLS_MD_CAN_SHA1:MBEDTLS_HAVE_TIME_DATE
x509_verify_callback:"data_files/server7-expired.crt":"data_files/test-ca2.crt":"NULL":MBEDTLS_ERR_X509_CERT_VERIFY_FAILED:"depth 2 - serial C1\:43\:E2\:7E\:62\:43\:CC\:E8 - subject C=NL, O=PolarSSL, CN=Polarssl Test EC CA - flags 0x00000000\ndepth 1 - serial 0E - subject C=NL, O=PolarSSL, CN=PolarSSL Test Intermediate CA - flags 0x00000000\ndepth 0 - serial 10 - subject C=NL, O=PolarSSL, CN=localhost - flags 0x00000001\n"

X509 CRT verification callback: intermediate ca, int expired
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_PK_CAN_ECDSA_VERIFY:MBEDTLS_RSA_C:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_RSA_C:MBEDTLS_PKCS1_V15:MBEDTLS_MD_CAN_SHA256:MBEDTLS_MD_CAN_SHA1:MBEDTLS_HAVE_TIME_DATE
x509_verify_callback:"data_files/server7_int-ca-exp.crt":"data_files/test-ca2.crt":"NULL":MBEDTLS_ERR_X509_CERT_VERIFY_FAILED:"depth 2 - serial C1\:43\:E2\:7E\:62\:43\:CC\:E8 - subject C=NL, O=PolarSSL, CN=Polarssl Test EC CA - flags 0x00000000\ndepth 1 - serial 0E - subject C=NL, O=PolarSSL, CN=PolarSSL Test Intermediate CA - flags 0x00000001\ndepth 0 - serial 10 - subject C=NL, O=PolarSSL, CN=localhost - flags 0x00000000\n"

X509 CRT verification callback: intermediate ca, root expired
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_PK_CAN_ECDSA_VERIFY:MBEDTLS_RSA_C:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_RSA_C:MBEDTLS_PKCS1_V15:MBEDTLS_MD_CAN_SHA256:MBEDTLS_MD_CAN_SHA1:MBEDTLS_HAVE_TIME_DATE
x509_verify_callback:"data_files/server7_int-ca.crt":"data_files/test-ca2-expired.crt":"NULL":MBEDTLS_ERR_X509_CERT_VERIFY_FAILED:"depth 2 - serial 01 - subject C=NL, O=PolarSSL, CN=Polarssl Test EC CA - flags 0x00000001\ndepth 1 - serial 0E - subject C=NL, O=PolarSSL, CN=PolarSSL Test Intermediate CA - flags 0x00000000\ndepth 0 - serial 10 - subject C=NL, O=PolarSSL, CN=localhost - flags 0x00000000\n"

X509 CRT verification callback: two intermediates
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_PK_CAN_ECDSA_VERIFY:MBEDTLS_RSA_C:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_RSA_C:MBEDTLS_PKCS1_V15:MBEDTLS_MD_CAN_SHA256:MBEDTLS_MD_CAN_SHA1
x509_verify_callback:"data_files/server10_int3_int-ca2.crt":"data_files/test-ca_cat21.crt":"NULL":0:"depth 3 - serial 03 - subject C=NL, O=PolarSSL, CN=PolarSSL Test CA - flags 0x00000000\ndepth 2 - serial 0F - subject C=NL, O=PolarSSL, CN=PolarSSL Test Intermediate EC CA - flags 0x00000000\ndepth 1 - serial 4D - subject C=UK, O=mbed TLS, CN=mbed TLS Test intermediate CA 3 - flags 0x00000000\ndepth 0 - serial 4B - subject CN=localhost - flags 0x00000000\n"

X509 CRT verification callback: two intermediates, root included
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_PK_CAN_ECDSA_VERIFY:MBEDTLS_RSA_C:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_RSA_C:MBEDTLS_PKCS1_V15:MBEDTLS_MD_CAN_SHA256:MBEDTLS_MD_CAN_SHA1
x509_verify_callback:"data_files/server10_int3_int-ca2_ca.crt":"data_files/test-ca_cat21.crt":"NULL":0:"depth 3 - serial 03 - subject C=NL, O=PolarSSL, CN=PolarSSL Test CA - flags 0x00000000\ndepth 2 - serial 0F - subject C=NL, O=PolarSSL, CN=PolarSSL Test Intermediate EC CA - flags 0x00000000\ndepth 1 - serial 4D - subject C=UK, O=mbed TLS, CN=mbed TLS Test intermediate CA 3 - flags 0x00000000\ndepth 0 - serial 4B - subject CN=localhost - flags 0x00000000\n"

X509 CRT verification callback: two intermediates, top int trusted
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_PK_CAN_ECDSA_VERIFY:MBEDTLS_RSA_C:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_RSA_C:MBEDTLS_PKCS1_V15:MBEDTLS_MD_CAN_SHA256
x509_verify_callback:"data_files/server10_int3_int-ca2.crt":"data_files/test-int-ca2.crt":"NULL":0:"depth 2 - serial 0F - subject C=NL, O=PolarSSL, CN=PolarSSL Test Intermediate EC CA - flags 0x00000000\ndepth 1 - serial 4D - subject C=UK, O=mbed TLS, CN=mbed TLS Test intermediate CA 3 - flags 0x00000000\ndepth 0 - serial 4B - subject CN=localhost - flags 0x00000000\n"

X509 CRT verification callback: two intermediates, low int trusted
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_PK_CAN_ECDSA_VERIFY:MBEDTLS_RSA_C:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_RSA_C:MBEDTLS_PKCS1_V15:MBEDTLS_MD_CAN_SHA256:MBEDTLS_MD_CAN_SHA1
x509_verify_callback:"data_files/server10_int3_int-ca2_ca.crt":"data_files/test-int-ca3.crt":"NULL":0:"depth 1 - serial 4D - subject C=UK, O=mbed TLS, CN=mbed TLS Test intermediate CA 3 - flags 0x00000000\ndepth 0 - serial 4B - subject CN=localhost - flags 0x00000000\n"

X509 CRT verification callback: no intermediate, bad signature
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_PK_CAN_ECDSA_VERIFY:MBEDTLS_MD_CAN_SHA256:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_ECP_HAVE_SECP384R1
x509_verify_callback:"data_files/server5-badsign.crt":"data_files/test-ca2.crt":"NULL":MBEDTLS_ERR_X509_CERT_VERIFY_FAILED:"depth 0 - serial 09 - subject C=NL, O=PolarSSL, CN=localhost - flags 0x00000008\n"

X509 CRT verification callback: one intermediate, bad signature
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_RSA_C:MBEDTLS_PKCS1_V15:MBEDTLS_MD_CAN_SHA1:MBEDTLS_PK_CAN_ECDSA_VERIFY:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_MD_CAN_SHA256
x509_verify_callback:"data_files/server7-badsign.crt":"data_files/test-ca2.crt":"NULL":MBEDTLS_ERR_X509_CERT_VERIFY_FAILED:"depth 2 - serial C1\:43\:E2\:7E\:62\:43\:CC\:E8 - subject C=NL, O=PolarSSL, CN=Polarssl Test EC CA - flags 0x00000000\ndepth 1 - serial 0E - subject C=NL, O=PolarSSL, CN=PolarSSL Test Intermediate CA - flags 0x00000000\ndepth 0 - serial 10 - subject C=NL, O=PolarSSL, CN=localhost - flags 0x00000008\n"

X509 CRT ASN1 (Empty Certificate)
x509parse_crt:"":"":MBEDTLS_ERR_X509_INVALID_FORMAT

X509 CRT ASN1 (inv Certificate, bad tag)
x509parse_crt:"0500":"":MBEDTLS_ERR_X509_INVALID_FORMAT

X509 CRT ASN1 (inv Certificate, no length)
x509parse_crt:"30":"":MBEDTLS_ERR_X509_INVALID_FORMAT

X509 CRT ASN1 (inv Certificate, bad length encoding)
x509parse_crt:"3085":"":MBEDTLS_ERR_X509_INVALID_FORMAT

X509 CRT ASN1 (inv Certificate, length data incomplete)
x509parse_crt:"308200":"":MBEDTLS_ERR_X509_INVALID_FORMAT

X509 CRT ASN1 (inv Certificate, length out of bounds)
x509parse_crt:"3001":"":MBEDTLS_ERR_X509_INVALID_FORMAT

X509 CRT ASN1 (inv TBS, invalid tag)
x509parse_crt:"30020500":"":MBEDTLS_ERR_X509_INVALID_FORMAT + MBEDTLS_ERR_ASN1_UNEXPECTED_TAG

X509 CRT ASN1 (inv TBS, length missing)
x509parse_crt:"300130":"":MBEDTLS_ERR_X509_INVALID_FORMAT + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (inv TBS, invalid length encoding)
x509parse_crt:"30023085":"":MBEDTLS_ERR_X509_INVALID_FORMAT + MBEDTLS_ERR_ASN1_INVALID_LENGTH

X509 CRT ASN1 (inv TBS, length data incomplete)
x509parse_crt:"300430839999":"":MBEDTLS_ERR_X509_INVALID_FORMAT + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (inv TBS, length out of bounds)
x509parse_crt:"30023003":"":MBEDTLS_ERR_X509_INVALID_FORMAT + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (TBS empty)
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_RSA_C
x509parse_crt:"30153000300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_FORMAT + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (TBS, invalid version tag, serial missing)
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_RSA_C
x509parse_crt:"301730020500300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_SERIAL + MBEDTLS_ERR_ASN1_UNEXPECTED_TAG

X509 CRT ASN1 (TBS, valid outer version tag, no outer length)
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_RSA_C
x509parse_crt:"30163001a0300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_FORMAT + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (TBS, inv inner version tag)
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_RSA_C
x509parse_crt:"30193004a0020500300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_VERSION + MBEDTLS_ERR_ASN1_UNEXPECTED_TAG

X509 CRT ASN1 (TBS, valid inner version tag, no inner length)
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_RSA_C
x509parse_crt:"30183003a00102300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_VERSION + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (TBS, valid inner version tag, inv inner length encoding)
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_RSA_C
x509parse_crt:"30193004a0020285300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_VERSION + MBEDTLS_ERR_ASN1_INVALID_LENGTH

X509 CRT ASN1 (TBS, valid inner version tag, inner length too large for int)
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_RSA_C
# tbsCertificate.version = 0x01000000000000000000000000000000 rejected by mbedtls_asn1_get_int
x509parse_crt:"30293014a012021001000000000000000000000000000000300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_VERSION + MBEDTLS_ERR_ASN1_INVALID_LENGTH

X509 CRT ASN1 (TBS, valid inner version tag, inner vs. outer length mismatch)
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_RSA_C
x509parse_crt:"301b3006a00402010200300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_VERSION + MBEDTLS_ERR_ASN1_LENGTH_MISMATCH

X509 CRT ASN1 (TBS, valid version tag, length exceeds TBS)
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_RSA_C
x509parse_crt:"30293014a012021100000000000000000000000000000000300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_VERSION + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (TBS, valid version tag + length, unknown version number 3)
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_RSA_C
x509parse_crt:"308196308180a0030201038204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffff300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_UNKNOWN_VERSION

X509 CRT ASN1 (TBS, valid version tag + length, unknown version number 4)
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_RSA_C
x509parse_crt:"308196308180a0030201048204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffff300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_UNKNOWN_VERSION

X509 CRT ASN1 (TBS, valid version tag + length, version number overflow)
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_RSA_C
x509parse_crt:"308199308183a00602047FFFFFFF8204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffff300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_UNKNOWN_VERSION

X509 CRT ASN1 (TBS, serial missing)
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_RSA_C
x509parse_crt:"301a3005a003020102300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_SERIAL + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (TBS, inv serial, tag wrong)
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_RSA_C
x509parse_crt:"301c3007a0030201020500300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_SERIAL + MBEDTLS_ERR_ASN1_UNEXPECTED_TAG

X509 CRT ASN1 (TBS, inv serial, length missing)
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_RSA_C
x509parse_crt:"301b3006a00302010282300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_SERIAL + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (TBS, inv serial, inv length encoding)
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_RSA_C
x509parse_crt:"301c3007a0030201028285300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_SERIAL + MBEDTLS_ERR_ASN1_INVALID_LENGTH

X509 CRT ASN1 (TBS, inv serial, length out of bounds)
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_RSA_C
x509parse_crt:"301c3007a0030201028201300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_SERIAL + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (TBS, AlgID missing)
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_RSA_C
x509parse_crt:"3020300ba0030201028204deadbeef300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_ALG + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (TBS, inv AlgID, tag wrong)
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_RSA_C
x509parse_crt:"3022300da0030201028204deadbeef0500300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_ALG + MBEDTLS_ERR_ASN1_UNEXPECTED_TAG

X509 CRT ASN1 (TBS, inv AlgID, OID missing)
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_RSA_C
x509parse_crt:"307b3073a0030201008204deadbeef3000300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffff3000030200ff":"":MBEDTLS_ERR_X509_INVALID_ALG + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (TBS, inv AlgID, OID tag wrong)
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_RSA_C
x509parse_crt:"307f3075a0030201008204deadbeef30020500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffff30020500030200ff":"":MBEDTLS_ERR_X509_INVALID_ALG + MBEDTLS_ERR_ASN1_UNEXPECTED_TAG

X509 CRT ASN1 (TBS, inv AlgID, OID inv length encoding)
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_RSA_C
x509parse_crt:"307f3075a0030201008204deadbeef30020685300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffff30020685030200ff":"":MBEDTLS_ERR_X509_INVALID_ALG + MBEDTLS_ERR_ASN1_INVALID_LENGTH

X509 CRT ASN1 (TBS, inv AlgID, OID length out of bounds)
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_RSA_C
x509parse_crt:"307f3075a0030201008204deadbeef30020601300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffff30020601030200ff":"":MBEDTLS_ERR_X509_INVALID_ALG + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (TBS, inv AlgID, OID empty)
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_RSA_C
x509parse_crt:"307f3075a0030201008204deadbeef30020600300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffff30020600030200ff":"":MBEDTLS_ERR_X509_UNKNOWN_SIG_ALG + MBEDTLS_ERR_OID_NOT_FOUND

X509 CRT ASN1 (TBS, inv AlgID, OID unknown)
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_RSA_C
x509parse_crt:"3081873079a0030201008204deadbeef30060604deadbeef300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffff30060604deadbeef030200ff":"":MBEDTLS_ERR_X509_UNKNOWN_SIG_ALG + MBEDTLS_ERR_OID_NOT_FOUND

X509 CRT ASN1 (TBS, inv AlgID, param inv length encoding)
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_RSA_C
x509parse_crt:"308196308180a0030201008204deadbeef300d06092a864886f70d01010b0685300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffff300d06092a864886f70d01010b0685030200ff":"":MBEDTLS_ERR_X509_INVALID_ALG + MBEDTLS_ERR_ASN1_INVALID_LENGTH

X509 CRT ASN1 (TBS, inv AlgID, param length out of bounds)
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_RSA_C
x509parse_crt:"308196308180a0030201008204deadbeef300d06092a864886f70d01010b0601300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffff300d06092a864886f70d01010b0601030200ff":"":MBEDTLS_ERR_X509_INVALID_ALG + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (TBS, inv AlgID, param length mismatch)
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_RSA_C
x509parse_crt:"30819a308182a0030201008204deadbeef300f06092a864886f70d01010b06010000300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffff300f06092a864886f70d01010b06010000030200ff":"":MBEDTLS_ERR_X509_INVALID_ALG + MBEDTLS_ERR_ASN1_LENGTH_MISMATCH

X509 CRT ASN1 (TBS, inv AlgID, params present but empty)
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_RSA_C
x509parse_crt:"308196308180a0030201008204deadbeef300d06092a864886f70d01010b0600300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffff300d06092a864886f70d01010b0600030200ff":"":MBEDTLS_ERR_X509_INVALID_ALG

X509 CRT ASN1 (TBS, inv AlgID, bad RSASSA-PSS params)
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_X509_RSASSA_PSS_SUPPORT
x509parse_crt:"308196308180a0030201008204deadbeef300d06092a864886f70d01010a3100300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffff300d06092a864886f70d01010a3100030200ff":"":MBEDTLS_ERR_X509_INVALID_ALG + MBEDTLS_ERR_ASN1_UNEXPECTED_TAG

X509 CRT ASN1 (TBS, Issuer missing)
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_RSA_C
x509parse_crt:"302f301aa0030201008204deadbeef300d06092a864886f70d01010b0500300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_FORMAT + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (TBS, inv Issuer, RDNSequence inv tag)
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_RSA_C
x509parse_crt:"3031301ca0030201008204deadbeef300d06092a864886f70d01010b05000500300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_FORMAT + MBEDTLS_ERR_ASN1_UNEXPECTED_TAG

X509 CRT ASN1 (TBS, inv Issuer, RDNSequence length missing)
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_RSA_C
x509parse_crt:"3030301ba0030201008204deadbeef300d06092a864886f70d01010b050030300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_FORMAT + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (TBS, inv Issuer, RDNSequence inv length encoding)
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_RSA_C
x509parse_crt:"3031301ca0030201008204deadbeef300d06092a864886f70d01010b05003085300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_FORMAT + MBEDTLS_ERR_ASN1_INVALID_LENGTH

X509 CRT ASN1 (TBS, inv Issuer, RDNSequence length out of bounds)
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_RSA_C
x509parse_crt:"3031301ca0030201008204deadbeef300d06092a864886f70d01010b05003001300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_FORMAT + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (TBS, inv Issuer, RDNSequence empty)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"3081893074a0030201028204deadbeef300d06092a864886f70d01010b05003000301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffff300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_NAME + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (TBS, inv Issuer, RDN inv tag)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"30818b3076a0030201028204deadbeef300d06092a864886f70d01010b050030020500301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffff300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_NAME + MBEDTLS_ERR_ASN1_UNEXPECTED_TAG

X509 CRT ASN1 (TBS, inv Issuer, RDN inv length encoding)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"30818b3076a0030201028204deadbeef300d06092a864886f70d01010b050030023185301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffff300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_NAME + MBEDTLS_ERR_ASN1_INVALID_LENGTH

X509 CRT ASN1 (TBS, inv Issuer, RDN length out of bounds)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"30818b3076a0030201028204deadbeef300d06092a864886f70d01010b050030023101301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffff300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_NAME + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (TBS, inv Issuer, RDN empty)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"30818b3076a0030201028204deadbeef300d06092a864886f70d01010b050030023100301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffff300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_NAME + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (TBS, inv Issuer, AttrTypeAndValue inv tag)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"30818d3078a0030201028204deadbeef300d06092a864886f70d01010b0500300431020500301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffff300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_NAME + MBEDTLS_ERR_ASN1_UNEXPECTED_TAG

X509 CRT ASN1 (TBS, inv Issuer, AttrTypeAndValue inv length encoding)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"30818d3078a0030201028204deadbeef300d06092a864886f70d01010b0500300431023085301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffff300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_NAME + MBEDTLS_ERR_ASN1_INVALID_LENGTH

X509 CRT ASN1 (TBS, inv Issuer, AttrTypeAndValue length out of bounds)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"30818d3078a0030201028204deadbeef300d06092a864886f70d01010b0500300431023001301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffff300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_NAME + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (TBS, inv Issuer, AttrTypeAndValue empty)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"30818d3078a0030201028204deadbeef300d06092a864886f70d01010b0500300431023000301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffff300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_NAME + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (TBS, inv Issuer, AttrTypeAndValue type inv tag)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"30818f307aa0030201028204deadbeef300d06092a864886f70d01010b05003006310430020500301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffff300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_NAME + MBEDTLS_ERR_ASN1_UNEXPECTED_TAG

X509 CRT ASN1 (TBS, inv Issuer, AttrTypeAndValue type inv no length data)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"30818e3079a0030201028204deadbeef300d06092a864886f70d01010b050030053103300106301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffff300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_NAME + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (TBS, inv Issuer, AttrTypeAndValue type inv length encoding)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"30818f307aa0030201028204deadbeef300d06092a864886f70d01010b05003006310430020685301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffff300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_NAME + MBEDTLS_ERR_ASN1_INVALID_LENGTH

X509 CRT ASN1 (TBS, inv Issuer, AttrTypeAndValue type length out of bounds)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"30818f307aa0030201028204deadbeef300d06092a864886f70d01010b05003006310430020601301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffff300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_NAME + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (TBS, inv Issuer, AttrTypeAndValue value missing)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"30818f307aa0030201028204deadbeef300d06092a864886f70d01010b05003006310430020600301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffff300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_NAME + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (TBS, inv Issuer, AttrTypeAndValue value inv tag)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"308191307ca0030201028204deadbeef300d06092a864886f70d01010b050030083106300406000500301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffff300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_NAME + MBEDTLS_ERR_ASN1_UNEXPECTED_TAG;

X509 CRT ASN1 (TBS, inv Issuer, AttrTypeAndValue value length missing)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"308190307ba0030201028204deadbeef300d06092a864886f70d01010b050030073105300306000c301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffff300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_NAME + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (TBS, inv Issuer, AttrTypeAndValue value inv length encoding)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"308191307ca0030201028204deadbeef300d06092a864886f70d01010b050030083106300406000C85301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffff300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_NAME + MBEDTLS_ERR_ASN1_INVALID_LENGTH

X509 CRT ASN1 (TBS, inv Issuer, AttrTypeAndValue value length out of bounds)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"308191307ca0030201028204deadbeef300d06092a864886f70d01010b050030083106300406000c01301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffff300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_NAME + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (TBS, inv Issuer, AttrTypeAndValue value length mismatch)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"308193307ea0030201028204deadbeef300d06092a864886f70d01010b0500300a3108300606000c010000301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffff300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_NAME + MBEDTLS_ERR_ASN1_LENGTH_MISMATCH

X509 CRT ASN1 (TBS, inv Issuer, 2nd AttributeTypeValue empty)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"308198308182a0030201028204deadbeef300d06092a864886f70d01010b0500300e310c300806000c04546573743000301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffff300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_NAME + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (TBS, Validity missing)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"303d3028a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a300806000c0454657374300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_DATE + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (TBS, inv Validity, inv tag)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"303f302aa0030201028204deadbeef300d06092a864886f70d01010b0500300c310a300806000c04546573740500300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_DATE + MBEDTLS_ERR_ASN1_UNEXPECTED_TAG

X509 CRT ASN1 (TBS, inv Validity, length field missing)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"303e3029a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a300806000c045465737430300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_DATE + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (TBS, inv Validity, inv length encoding)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"303f302aa0030201028204deadbeef300d06092a864886f70d01010b0500300c310a300806000c04546573743085300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_DATE + MBEDTLS_ERR_ASN1_INVALID_LENGTH

X509 CRT ASN1 (TBS, inv Validity, length out of bounds)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"303f302aa0030201028204deadbeef300d06092a864886f70d01010b0500300c310a300806000c04546573743001300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_DATE + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (TBS, inv Validity, notBefore missing)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"30793064a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a300806000c04546573743000300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffff300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_DATE + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (TBS, inv Validity, notBefore inv tag)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"307b3066a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a300806000c045465737430020500300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffff300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_DATE + MBEDTLS_ERR_ASN1_UNEXPECTED_TAG

X509 CRT ASN1 (TBS, inv Validity, notBefore no length)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"307a3065a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a300806000c0454657374300117300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffff300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_DATE + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (TBS, inv Validity, notBefore inv length encoding)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"307b3066a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a300806000c04546573743002178f300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffff300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_DATE + MBEDTLS_ERR_ASN1_INVALID_LENGTH

X509 CRT ASN1 (TBS, inv Validity, notBefore length out of bounds)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"307b3066a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a300806000c045465737430021701300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffff300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_DATE + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (TBS, inv Validity, notBefore empty)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"3081893074a0030201008204deadbeef300d06092a864886f70d01010b0500300c310a3008060013045465737430101700170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffff300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_DATE

X509 CRT ASN1 (TBS, inv Validity, notBefore invalid)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"308196308180a0030201008204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303000000000170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffff300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_DATE

X509 CRT ASN1 (TBS, inv Validity, notAfter missing)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"3081873072a0030201008204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374300e170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffff300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_DATE + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (TBS, inv Validity, notAfter inv tag)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"3081893074a0030201008204deadbeef300d06092a864886f70d01010b0500300c310a300806001304546573743010170c3039313233313233353935390500300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffff300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_DATE + MBEDTLS_ERR_ASN1_UNEXPECTED_TAG

X509 CRT ASN1 (TBS, inv Validity, notAfter length missing)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"3081883073a0030201008204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374300f170c30393132333132333539353917300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffff300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_DATE + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (TBS, inv Validity, notAfter inv length encoding)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"3081893074a0030201008204deadbeef300d06092a864886f70d01010b0500300c310a300806001304546573743010170c3039313233313233353935391785300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffff300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_DATE + MBEDTLS_ERR_ASN1_INVALID_LENGTH

X509 CRT ASN1 (TBS, inv Validity, notAfter length out of bounds)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"3081893074a0030201008204deadbeef300d06092a864886f70d01010b0500300c310a300806001304546573743010170c3039313233313233353935391701300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffff300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_DATE + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (TBS, inv Validity, notAfter empty)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"3081893074a0030201008204deadbeef300d06092a864886f70d01010b0500300c310a300806001304546573743010170c3039313233313233353935391700300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffff300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_DATE

X509 CRT ASN1 (TBS, inv Validity, notAfter invalid)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"308196308180a0030201008204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303931323331323335393539170c303930313031303000000000300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffff300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_DATE

X509 CRT ASN1 (TBS, inv Validity, data remaining after 'notAfter')
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"308198308182a0030201008204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301e170c303930313031303030303030170c3039313233313233353935391700300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffff300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_DATE + MBEDTLS_ERR_ASN1_LENGTH_MISMATCH

X509 CRT ASN1 (TBS, Subject missing)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"305b3046a0030201008204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_FORMAT + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (TBS, inv Subject, RDNSequence inv tag)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"305c3047a0030201008204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c30393132333132333539353900300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_FORMAT + MBEDTLS_ERR_ASN1_UNEXPECTED_TAG

X509 CRT ASN1 (TBS, inv Subject, RDNSequence length missing)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"305c3047a0030201008204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c30393132333132333539353930300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_FORMAT + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (TBS, inv Subject, RDNSequence inv length encoding)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"305d3048a0030201008204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c3039313233313233353935393085300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_FORMAT + MBEDTLS_ERR_ASN1_INVALID_LENGTH

X509 CRT ASN1 (TBS, inv Subject, RDNSequence length out of bounds)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"305d3048a0030201008204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c3039313233313233353935393001300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_FORMAT + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (TBS, inv Subject, RDN inv tag)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"30818b3076a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c30393132333132333539353930020500302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffff300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_NAME + MBEDTLS_ERR_ASN1_UNEXPECTED_TAG

X509 CRT ASN1 (TBS, inv Subject, RDN inv length encoding)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"30818b3076a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c30393132333132333539353930023185302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffff300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_NAME + MBEDTLS_ERR_ASN1_INVALID_LENGTH

X509 CRT ASN1 (TBS, inv Subject, RDN length out of bounds)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"30818b3076a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c30393132333132333539353930023101302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffff300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_NAME + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (TBS, inv Subject, RDN empty)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"30818b3076a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c30393132333132333539353930023100302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffff300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_NAME + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (TBS, inv Subject, AttrTypeAndValue inv tag)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"30818d3078a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300431020500302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffff300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_NAME + MBEDTLS_ERR_ASN1_UNEXPECTED_TAG

X509 CRT ASN1 (TBS, inv Subject, AttrTypeAndValue inv length encoding)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"30818d3078a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300431023085302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffff300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_NAME + MBEDTLS_ERR_ASN1_INVALID_LENGTH

X509 CRT ASN1 (TBS, inv Subject, AttrTypeAndValue length out of bounds)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"30818d3078a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300431023001302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffff300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_NAME + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (TBS, inv Subject, AttrTypeAndValue empty)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"30818d3078a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300431023000302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffff300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_NAME + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (TBS, inv Subject, AttrTypeAndValue type inv tag)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"30818f307aa0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c3039313233313233353935393006310430020500302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffff300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_NAME + MBEDTLS_ERR_ASN1_UNEXPECTED_TAG

X509 CRT ASN1 (TBS, inv Subject, AttrTypeAndValue type inv no length data)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"30818e3079a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c30393132333132333539353930053103300106302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffff300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_NAME + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (TBS, inv Subject, AttrTypeAndValue type inv length encoding)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"30818f307aa0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c3039313233313233353935393006310430020685302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffff300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_NAME + MBEDTLS_ERR_ASN1_INVALID_LENGTH

X509 CRT ASN1 (TBS, inv Subject, AttrTypeAndValue type length out of bounds )
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"30818f307aa0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c3039313233313233353935393006310430020601302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffff300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_NAME + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (TBS, inv Subject, AttrTypeAndValue value missing)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"30818f307aa0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c3039313233313233353935393006310430020600302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffff300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_NAME + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (TBS, inv Subject, AttrTypeAndValue value inv tag)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"308191307ca0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c30393132333132333539353930083106300406000500302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffff300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_NAME + MBEDTLS_ERR_ASN1_UNEXPECTED_TAG;

X509 CRT ASN1 (TBS, inv Subject, AttrTypeAndValue value length missing)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"308190307ba0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c30393132333132333539353930073105300306000c302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffff300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_NAME + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (TBS, inv Subject, AttrTypeAndValue value inv length encoding)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"308191307ca0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c30393132333132333539353930083106300406000C85302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffff300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_NAME + MBEDTLS_ERR_ASN1_INVALID_LENGTH

X509 CRT ASN1 (TBS, inv Subject, AttrTypeAndValue value length out of bounds)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"308191307ca0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c30393132333132333539353930083106300406000c01302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffff300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_NAME + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (TBS, inv Subject, AttrTypeAndValue value length mismatch)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"308193307ea0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300a3108300606000c010000302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffff300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_NAME + MBEDTLS_ERR_ASN1_LENGTH_MISMATCH

X509 CRT ASN1 (TBS, inv Subject, 2nd AttributeTypeValue empty)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"308198308182a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300e310c300806000c04546573743000302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffff300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_NAME + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (TBS, SubPubKeyInfo missing)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"30693054a0030201008204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_PK_KEY_INVALID_FORMAT + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (TBS, inv SubPubKeyInfo, inv tag)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"306b3056a0030201008204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a300806001304546573740500300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_PK_KEY_INVALID_FORMAT + MBEDTLS_ERR_ASN1_UNEXPECTED_TAG

X509 CRT ASN1 (TBS, inv SubPubKeyInfo, length missing)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"306a3055a0030201008204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a3008060013045465737430300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_PK_KEY_INVALID_FORMAT + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (TBS, inv SubPubKeyInfo, inv length encoding)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"306b3056a0030201008204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a300806001304546573743085300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_PK_KEY_INVALID_FORMAT + MBEDTLS_ERR_ASN1_INVALID_LENGTH

X509 CRT ASN1 (TBS, inv SubPubKeyInfo, length out of bounds)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"306b3056a0030201008204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a300806001304546573743001300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_PK_KEY_INVALID_FORMAT + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (TBS, inv SubPubKeyInfo, empty)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"306b3056a0030201008204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a300806001304546573743000300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_PK_INVALID_ALG + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (TBS, inv SubPubKeyInfo, inv algorithm tag)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"306d3058a0030201008204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a3008060013045465737430020500300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_PK_INVALID_ALG + MBEDTLS_ERR_ASN1_UNEXPECTED_TAG

X509 CRT ASN1 (TBS, inv SubPubKeyInfo, algorithm length missing)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"306c3057a0030201008204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374300130300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_PK_INVALID_ALG + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (TBS, inv SubPubKeyInfo, algorithm inv length encoding)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"306d3058a0030201008204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a3008060013045465737430023085300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_PK_INVALID_ALG + MBEDTLS_ERR_ASN1_INVALID_LENGTH

X509 CRT ASN1 (TBS, inv SubPubKeyInfo, algorithm length out of bounds)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"306d3058a0030201008204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a3008060013045465737430023001300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_PK_INVALID_ALG + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (TBS, inv SubPubKeyInfo, algorithm empty)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"3081883073a0030201008204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374301d300003190030160210ffffffffffffffffffffffffffffffff0202ffff300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_PK_INVALID_ALG + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (TBS, inv SubPubKeyInfo, algorithm unknown)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"308196308180a0030201008204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010100050003190030160210ffffffffffffffffffffffffffffffff0202ffff300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_PK_UNKNOWN_PK_ALG

X509 CRT ASN1 (TBS, inv SubPubKeyInfo, bitstring missing)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"307a3065a0030201008204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374300f300d06092A864886F70D0101010500300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_PK_INVALID_PUBKEY + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (TBS, inv SubPubKeyInfo, bitstring inv tag)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"307c3067a0030201008204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a300806001304546573743011300d06092A864886F70D01010105000500300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_PK_INVALID_PUBKEY + MBEDTLS_ERR_ASN1_UNEXPECTED_TAG

X509 CRT ASN1 (TBS, inv SubPubKeyInfo, bitstring length missing)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"307b3066a0030201008204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a300806001304546573743010300d06092A864886F70D010101050003300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_PK_INVALID_PUBKEY + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (TBS, inv SubPubKeyInfo, bitstring inv length encoding)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"307c3067a0030201008204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a300806001304546573743011300d06092A864886F70D01010105000385300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_PK_INVALID_PUBKEY + MBEDTLS_ERR_ASN1_INVALID_LENGTH

X509 CRT ASN1 (TBS, inv SubPubKeyInfo, bitstring length out of bounds)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"307c3067a0030201008204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a300806001304546573743011300d06092A864886F70D01010105000301300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_PK_INVALID_PUBKEY + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (TBS, inv SubPubKeyInfo, no bitstring data)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"307c3067a0030201008204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a300806001304546573743011300d06092A864886F70D01010105000300300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_PK_INVALID_PUBKEY + MBEDTLS_ERR_ASN1_INVALID_DATA

X509 CRT ASN1 (TBS, inv SubPubKeyInfo, inv bitstring start)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"307d3068a0030201008204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a300806001304546573743012300d06092A864886F70D0101010500030101300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_PK_INVALID_PUBKEY + MBEDTLS_ERR_ASN1_INVALID_DATA

X509 CRT ASN1 (TBS, inv SubPubKeyInfo, inv internal bitstring length)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"308180306ba0030201008204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a300806001304546573743015300d06092A864886F70D0101010500030400300000300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_PK_INVALID_PUBKEY

X509 CRT ASN1 (TBS, inv SubPubKeyInfo, inv internal bitstring tag)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"308180306ba0030201008204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a300806001304546573743015300d06092A864886F70D0101010500030400310000300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_PK_INVALID_PUBKEY + MBEDTLS_ERR_ASN1_UNEXPECTED_TAG

X509 CRT ASN1 (TBS, inv SubPubKeyInfo, inv RSA modulus)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"3081873072a0030201008204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374301c300d06092A864886F70D0101010500030b0030080202ffff0302ffff300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_PK_INVALID_PUBKEY + MBEDTLS_ERR_ASN1_UNEXPECTED_TAG

X509 CRT ASN1 (TBS, inv SubPubKeyInfo, total length mismatch)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"3081893074a0030201008204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374301e300d06092A864886F70D0101010500030b0030080202ffff0202ffff0500300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_PK_INVALID_PUBKEY + MBEDTLS_ERR_ASN1_LENGTH_MISMATCH

X509 CRT ASN1 (TBS, inv SubPubKeyInfo, check failed)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"3081873072a0030201008204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374301c300d06092A864886F70D0101010500030b0030080202ffff0202ffff300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_PK_INVALID_PUBKEY

X509 CRT ASN1 (TBS, inv SubPubKeyInfo, check failed, expanded length notation)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"308196308180a0030201008204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210fffffffffffffffffffffffffffffffe0202ffff300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_PK_INVALID_PUBKEY

# We expect an extension parsing error here because the IssuerID is optional.
# Hence, if we find an ASN.1 tag doesn't match the IssuerID, we assume the
# IssuerID is skipped and that the tag should hence belong to the next field,
# namely the v3 extensions. However, the tag the test exercises is a NULL tag,
# and hence we obtain an INVALID_TAG error during extension parsing.
X509 CRT ASN1 (TBS, inv IssuerID, inv tag)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"308198308182a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffff0500300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS + MBEDTLS_ERR_ASN1_UNEXPECTED_TAG

X509 CRT ASN1 (TBS, inv IssuerID, length missing)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"308197308181a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa1300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_FORMAT + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (TBS, inv IssuerID, inv length encoding)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"308198308182a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa185300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_FORMAT + MBEDTLS_ERR_ASN1_INVALID_LENGTH

X509 CRT ASN1 (TBS, inv IssuerID, length out of bounds)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"308198308182a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa101300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_FORMAT + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (TBS, no IssuerID, inv SubjectID, length missing)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"308197308181a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa2300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_FORMAT + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (TBS, no IssuerID, inv SubjectID, inv length encoding)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"308198308182a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa285300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_FORMAT + MBEDTLS_ERR_ASN1_INVALID_LENGTH

X509 CRT ASN1 (TBS, no IssuerID, inv SubjectID, length out of bounds)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"308198308182a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa201300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_FORMAT + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (TBS, valid IssuerID, inv SubjectID, inv tag)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"30819a308184a0030201008204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa1000500300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_FORMAT + MBEDTLS_ERR_ASN1_LENGTH_MISMATCH

X509 CRT ASN1 (TBS, valid IssuerID, inv SubjectID, length missing)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"308199308183a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a2300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_FORMAT + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (TBS, valid IssuerID, inv SubjectID, inv length encoding)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"30819a308184a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a285300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_FORMAT + MBEDTLS_ERR_ASN1_INVALID_LENGTH

X509 CRT ASN1 (TBS, valid IssuerID, inv SubjectID, length out of bounds)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"30819a308184a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a201300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_FORMAT + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (TBS, IssuerID unsupported in v1 CRT)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"30819a308184a0030201008204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a201300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_FORMAT + MBEDTLS_ERR_ASN1_LENGTH_MISMATCH

X509 CRT ASN1 (TBS, SubjectID unsupported in v1 CRT)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"30819a308184a0030201008204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa200a201300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_FORMAT + MBEDTLS_ERR_ASN1_LENGTH_MISMATCH

X509 CRT ASN1 (TBS, inv v3Ext, inv tag)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"30819c308186a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a2000500300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS + MBEDTLS_ERR_ASN1_UNEXPECTED_TAG

X509 CRT ASN1 (TBS, inv v3Ext, outer length missing)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"30819b308185a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a3300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (TBS, inv v3Ext, outer length inv encoding)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"30819c308186a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a385300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS + MBEDTLS_ERR_ASN1_INVALID_LENGTH

X509 CRT ASN1 (TBS, inv v3Ext, outer length out of bounds)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"30819c308186a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a301300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (TBS, inv v3Ext, outer length 0)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"30819c308186a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a300300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (TBS, inv v3Ext, inner tag invalid)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"30819e308188a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a3020500300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS + MBEDTLS_ERR_ASN1_UNEXPECTED_TAG

X509 CRT ASN1 (TBS, inv v3Ext, inner length missing)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"30819d308187a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a30130300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (TBS, inv v3Ext, inner length inv encoding)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"30819e308188a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a3023085300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS + MBEDTLS_ERR_ASN1_INVALID_LENGTH

X509 CRT ASN1 (TBS, inv v3Ext, inner length out of bounds)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"30819e308188a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a3023001300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (TBS, inv v3Ext, inner/outer length mismatch)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"30819f308189a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a303300000300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS + MBEDTLS_ERR_ASN1_LENGTH_MISMATCH

X509 CRT ASN1 (TBS, inv v3Ext, first ext inv tag)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"3081a030818aa0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a30430020500300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS + MBEDTLS_ERR_ASN1_UNEXPECTED_TAG

X509 CRT ASN1 (TBS, inv v3Ext, first ext length missing)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"30819f308189a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a303300130300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (TBS, inv v3Ext, inv first ext length encoding)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"3081a030818aa0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a30430023085300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS + MBEDTLS_ERR_ASN1_INVALID_LENGTH

X509 CRT ASN1 (TBS, inv v3Ext, first ext length out of bounds)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"3081a030818aa0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a30430023001300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (TBS, inv v3Ext, first ext empty)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"3081a030818aa0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a30430023000300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (TBS, inv v3Ext, first ext extnID inv tag)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"3081a230818ca0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a306300430020500300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS + MBEDTLS_ERR_ASN1_UNEXPECTED_TAG

X509 CRT ASN1 (TBS, inv v3Ext, first ext extnID length missing)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"3081a130818ba0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a3053003300106300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (TBS, inv v3Ext, first ext extnID inv length encoding)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"3081a230818ca0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a306300430020685300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS + MBEDTLS_ERR_ASN1_INVALID_LENGTH

X509 CRT ASN1 (TBS, inv v3Ext, first ext extnID length out of bounds)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"3081a230818ca0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a306300430020601300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (TBS, inv v3Ext, no extnValue)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"3081a230818ca0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a306300430020600300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (TBS, inv v3Ext, inv critical tag)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"3081a430818ea0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a3083006300406000500300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS + MBEDTLS_ERR_ASN1_UNEXPECTED_TAG

X509 CRT ASN1 (TBS, inv v3Ext, critical length missing)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"3081a330818da0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a30730053003060001300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (TBS, inv v3Ext, critical inv length encoding)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"3081a430818ea0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a3083006300406000185300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS + MBEDTLS_ERR_ASN1_INVALID_LENGTH

X509 CRT ASN1 (TBS, inv v3Ext, critical length out of bounds)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"3081a430818ea0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a3083006300406000101300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (TBS, inv v3Ext, critical length 0)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"3081a430818ea0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a3083006300406000100300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS + MBEDTLS_ERR_ASN1_INVALID_LENGTH

X509 CRT ASN1 (TBS, inv v3Ext, critical length 2)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"3081a6308190a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a30a30083006060001020000300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS + MBEDTLS_ERR_ASN1_INVALID_LENGTH

X509 CRT ASN1 (TBS, inv v3Ext, extnValue inv tag)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"3081a7308191a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a30b3009300706000101000500300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS + MBEDTLS_ERR_ASN1_UNEXPECTED_TAG

X509 CRT ASN1 (TBS, inv v3Ext, extnValue length missing)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"3081a6308190a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a30a30083006060001010004300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (TBS, inv v3Ext, extnValue length inv encoding)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"3081a7308191a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a30b3009300706000101000485300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS + MBEDTLS_ERR_ASN1_INVALID_LENGTH

X509 CRT ASN1 (TBS, inv v3Ext, extnValue length out of bounds)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"3081a7308191a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a30b3009300706000101000401300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (TBS, inv v3Ext, data remaining after extnValue)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"3081a9308193a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a30d300b3009060001010004000500300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS + MBEDTLS_ERR_ASN1_LENGTH_MISMATCH

X509 CRT ASN1 (TBSCertificate v3, inv CertificatePolicies, data missing)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"3081a7308191a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092a864886f70d010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a30b300930070603551d200400300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (TBSCertificate v3, inv CertificatePolicies, invalid outer tag)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"3081a9308193a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092a864886f70d010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a30d300b30090603551d2004020500300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS + MBEDTLS_ERR_ASN1_UNEXPECTED_TAG

X509 CRT ASN1 (TBSCertificate v3, inv CertificatePolicies, outer length missing)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"3081a8308192a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092a864886f70d010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a30c300a30080603551d20040130300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (TBSCertificate v3, inv CertificatePolicies, outer length inv encoding)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"3081a9308193a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092a864886f70d010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a30d300b30090603551d2004023085300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS + MBEDTLS_ERR_ASN1_INVALID_LENGTH

X509 CRT ASN1 (TBSCertificate v3, inv CertificatePolicies, outer length out of bounds)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"3081a9308193a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092a864886f70d010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a30d300b30090603551d2004023001300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (TBSCertificate v3, inv CertificatePolicies, no policies)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"3081a9308193a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092a864886f70d010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a30d300b30090603551d2004023000300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS + MBEDTLS_ERR_ASN1_LENGTH_MISMATCH

X509 CRT ASN1 (TBSCertificate v3, inv CertificatePolicies, policy invalid tag)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"3081ab308195a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092a864886f70d010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a30f300d300b0603551d20040430020500300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS + MBEDTLS_ERR_ASN1_UNEXPECTED_TAG

X509 CRT ASN1 (TBSCertificate v3, inv CertificatePolicies, policy length missing)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"3081aa308194a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092a864886f70d010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a30e300c300a0603551d200403300130300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (TBSCertificate v3, inv CertificatePolicies, policy length inv encoding)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"3081ab308195a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092a864886f70d010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a30f300d300b0603551d20040430023085300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS + MBEDTLS_ERR_ASN1_INVALID_LENGTH

X509 CRT ASN1 (TBSCertificate v3, inv CertificatePolicies, policy length out of bounds)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"3081ab308195a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092a864886f70d010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a30f300d300b0603551d20040430023001300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (TBSCertificate v3, inv CertificatePolicies, empty policy)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"3081ab308195a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092a864886f70d010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a30f300d300b0603551d20040430023000300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (TBSCertificate v3, inv CertificatePolicies, policy invalid OID tag)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"3081ad308197a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092a864886f70d010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a311300f300d0603551d200406300430020500300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS + MBEDTLS_ERR_ASN1_UNEXPECTED_TAG

X509 CRT ASN1 (TBSCertificate v3, inv CertificatePolicies, policy no OID length)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"3081ac308196a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092a864886f70d010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a310300e300c0603551d2004053003300106300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (TBSCertificate v3, inv CertificatePolicies, policy OID length inv encoding)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"3081ad308197a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092a864886f70d010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a311300f300d0603551d200406300430020685300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS + MBEDTLS_ERR_ASN1_INVALID_LENGTH

X509 CRT ASN1 (TBSCertificate v3, inv CertificatePolicies, policy OID length out of bounds)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"3081ad308197a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092a864886f70d010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a311300f300d0603551d200406300430020601300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (TBSCertificate v3, inv CertificatePolicies, unknown critical policy)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"3081b130819ba0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092a864886f70d010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a315301330110603551d20010101040730053003060100300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_FEATURE_UNAVAILABLE

X509 CRT ASN1 (TBSCertificate v3, inv CertificatePolicies, policy qualifier invalid tag)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"3081b030819aa0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092a864886f70d010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a314301230100603551d200409300730050601000500300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS + MBEDTLS_ERR_ASN1_UNEXPECTED_TAG

X509 CRT ASN1 (TBSCertificate v3, inv CertificatePolicies, policy qualifier no length)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"3081af308199a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092a864886f70d010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a3133011300f0603551d2004083006300406010030300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (TBSCertificate v3, inv CertificatePolicies, policy qualifier inv length encoding)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"3081b030819aa0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092a864886f70d010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a314301230100603551d200409300730050601003085300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS + MBEDTLS_ERR_ASN1_INVALID_LENGTH

X509 CRT ASN1 (TBSCertificate v3, inv CertificatePolicies, policy qualifier length out of bounds)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"3081b030819aa0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092a864886f70d010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a314301230100603551d200409300730050601003001300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (TBS, inv extBasicConstraint, no pathlen length)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"3081b030819aa0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a314301230100603551d130101010406300402010102300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (inv extBasicConstraint, pathlen is INT_MAX)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256:MBEDTLS_MD_CAN_SHA1
mbedtls_x509_crt_parse_file:"data_files/parse_input/server1_pathlen_int_max.crt":MBEDTLS_ERR_X509_INVALID_EXTENSIONS + MBEDTLS_ERR_ASN1_INVALID_LENGTH:0

X509 CRT ASN1 (pathlen is INT_MAX-1)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256:MBEDTLS_MD_CAN_SHA1
mbedtls_x509_crt_parse_file:"data_files/parse_input/server1_pathlen_int_max-1.crt":0:1

X509 CRT ASN1 (TBS, inv extBasicConstraint, pathlen inv length encoding)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"3081b130819ba0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a315301330110603551d13010101040730050201010285300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS + MBEDTLS_ERR_ASN1_INVALID_LENGTH

X509 CRT ASN1 (TBS, inv extBasicConstraint, pathlen length out of bounds)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"3081b130819ba0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a315301330110603551d13010101040730050201010201300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (TBS, inv extBasicConstraint, pathlen empty)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"3081b130819ba0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a315301330110603551d13010101040730050201010200300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS + MBEDTLS_ERR_ASN1_INVALID_LENGTH

X509 CRT ASN1 (TBS, inv extBasicConstraint, pathlen length mismatch)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"3081b430819ea0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a318301630140603551d13010101040a30080201010201010500300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS + MBEDTLS_ERR_ASN1_LENGTH_MISMATCH

X509 CRT ASN1 (TBS, inv v3Ext, ExtKeyUsage bad second tag)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"3081bd3081a7a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a321301f301d0603551d250416301406082b0601050507030107082b06010505070302300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS + MBEDTLS_ERR_ASN1_UNEXPECTED_TAG

X509 CRT ASN1 (TBS, inv SubjectAltName, empty)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"3081a7308191a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a30b300930070603551d110400300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (TBS, inv SubjectAltName, inv tag)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"3081a9308193a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a30d300b30090603551d1104020500300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS + MBEDTLS_ERR_ASN1_UNEXPECTED_TAG

X509 CRT ASN1 (TBS, inv SubjectAltName, length missing)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"3081a8308192a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a30c300a30080603551d11040130300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (TBS, inv SubjectAltName, inv length encoding)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"3081a9308193a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a30d300b30090603551d1104023085300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS + MBEDTLS_ERR_ASN1_INVALID_LENGTH

X509 CRT ASN1 (TBS, inv SubjectAltName, length out of bounds)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"3081a9308193a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a30d300b30090603551d1104023001300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (TBS, inv SubjectAltName, data remaining after name SEQUENCE)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"3081aa308194a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a30e300c300a0603551d110403300000300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS + MBEDTLS_ERR_ASN1_LENGTH_MISMATCH

X509 CRT ASN1 (TBS, inv SubjectAltName, name component length missing)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"3081aa308194a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a30e300c300a0603551d110403300180300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (TBS, inv SubjectAltName, name component inv length encoding)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"3081ab308195a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a30f300d300b0603551d11040430028085300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS + MBEDTLS_ERR_ASN1_INVALID_LENGTH

X509 CRT ASN1 (TBS, inv SubjectAltName, name component length out of bounds)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"3081ab308195a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a30f300d300b0603551d11040430028001300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (TBS, inv SubjectAltName, name component unexpected tag)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"3081ab308195a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a30f300d300b0603551d11040430024000300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS + MBEDTLS_ERR_ASN1_UNEXPECTED_TAG

X509 CRT ASN1 (TBS, inv SubjectAltName, otherName component empty)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"3081ab308195a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a30f300d300b0603551d1104043002a000300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (TBS, inv SubjectAltName, otherName invalid OID tag)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"3081ad308197a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a311300f300d0603551d1104063004a0020500300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS + MBEDTLS_ERR_ASN1_UNEXPECTED_TAG

X509 CRT ASN1 (TBS, inv SubjectAltName, otherName OID length missing)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"3081ac308196a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a310300e300c0603551d1104053003a00106300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (TBS, inv SubjectAltName, otherName OID inv length encoding)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"3081ad308197a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a311300f300d0603551d1104063004a0020685300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS + MBEDTLS_ERR_ASN1_INVALID_LENGTH

X509 CRT ASN1 (TBS, inv SubjectAltName, otherName OID length out of bounds)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"3081ad308197a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a311300f300d0603551d1104063004a0020601300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (TBS, inv SubjectAltName, HWModuleName EXPLICIT tag missing
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"3081b530819fa0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a319301730150603551d11040e300ca00a06082b06010505070804300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (TBS, inv SubjectAltName, HWModuleName unexpected EXPLICIT tag)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"3081b73081a1a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a31b301930170603551d110410300ea00c06082b060105050708040500300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS + MBEDTLS_ERR_ASN1_UNEXPECTED_TAG

X509 CRT ASN1 (TBS, inv SubjectAltName, HWModuleName outer length missing)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"3081b63081a0a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a31a301830160603551d11040f300da00b06082b06010505070804a0300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (TBS, inv SubjectAltName, HWModuleName inv outer length)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"3081b73081a1a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a31b301930170603551d110410300ea00c06082b06010505070804a085300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS + MBEDTLS_ERR_ASN1_INVALID_LENGTH

X509 CRT ASN1 (TBS, inv SubjectAltName, HWModuleName outer length out of bounds)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"3081b73081a1a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a31b301930170603551d110410300ea00c06082b06010505070804a001300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (TBS, inv SubjectAltName, HWModuleName outer length 0)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"3081b73081a1a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a31b301930170603551d110410300ea00c06082b06010505070804a000300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (TBS, inv SubjectAltName, HWModuleName inner tag invalid)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"3081b93081a3a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a31d301b30190603551d1104123010a00e06082b06010505070804a0020500300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS + MBEDTLS_ERR_ASN1_UNEXPECTED_TAG

X509 CRT ASN1 (TBS, inv SubjectAltName, HWModuleName inner length missing)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"3081b83081a2a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a31c301a30180603551d110411300fa00d06082b06010505070804a00130300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (TBS, inv SubjectAltName, HWModuleName inner length inv encoding)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"3081b93081a3a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a31d301b30190603551d1104123010a00e06082b06010505070804a0023085300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS + MBEDTLS_ERR_ASN1_INVALID_LENGTH

X509 CRT ASN1 (TBS, inv SubjectAltName, HWModuleName inner length out of bounds)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"3081b93081a3a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a31d301b30190603551d1104123010a00e06082b06010505070804a0023001300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (TBS, inv SubjectAltName, HWModuleName empty)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"3081b93081a3a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a31d301b30190603551d1104123010a00e06082b06010505070804a0023000300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (TBS, inv SubjectAltName, HWModuleName unexpected OID tag)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"3081bb3081a5a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a31f301d301b0603551d1104143012a01006082b06010505070804a00430020500300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS + MBEDTLS_ERR_ASN1_UNEXPECTED_TAG

X509 CRT ASN1 (TBS, inv SubjectAltName, HWModuleName OID no length)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"3081ba3081a4a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a31e301c301a0603551d1104133011a00f06082b06010505070804a003300106300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (TBS, inv SubjectAltName, HWModuleName OID inv length encoding)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"3081bb3081a5a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a31f301d301b0603551d1104143012a01006082b06010505070804a00430020685300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS + MBEDTLS_ERR_ASN1_INVALID_LENGTH

X509 CRT ASN1 (TBS, inv SubjectAltName, HWModuleName OID length out of bounds)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"3081bb3081a5a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a31f301d301b0603551d1104143012a01006082b06010505070804a00430020601300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (TBS, inv SubjectAltName, HWModuleName data missing)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"3081bb3081a5a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a31f301d301b0603551d1104143012a01006082b06010505070804a00430020600300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (TBS, inv SubjectAltName, HWModuleName data invalid tag)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"3081bd3081a7a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a321301f301d0603551d1104163014a01206082b06010505070804a006300406000500300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS + MBEDTLS_ERR_ASN1_UNEXPECTED_TAG

X509 CRT ASN1 (TBS, inv SubjectAltName, HWModuleName data length missing)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"3081bc3081a6a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a320301e301c0603551d1104153013a01106082b06010505070804a0053003060004300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (TBS, inv SubjectAltName, HWModuleName data inv length encoding)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"3081bd3081a7a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a321301f301d0603551d1104163014a01206082b06010505070804a006300406000485300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS + MBEDTLS_ERR_ASN1_INVALID_LENGTH

X509 CRT ASN1 (TBS, inv SubjectAltName, HWModuleName data length out of bounds)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"3081bd3081a7a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a321301f301d0603551d1104163014a01206082b06010505070804a006300406000401300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (TBS, inv SubjectAltName, HWModuleName data remaining #1)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"3081bf3081a9a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a3233021301f0603551d1104183016a01406082b06010505070804a0083006060004000500300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS + MBEDTLS_ERR_ASN1_LENGTH_MISMATCH

X509 CRT ASN1 (TBS, inv SubjectAltName, HWModuleName data remaining #2)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"3081bf3081a9a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a3233021301f0603551d1104183016a01406082b06010505070804a0083004060004000500300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS + MBEDTLS_ERR_ASN1_LENGTH_MISMATCH

X509 CRT ASN1 (TBS, inv SubjectAltName, HWModuleName data remaining #3)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"3081bf3081a9a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a3233021301f0603551d1104183016a01406082b06010505070804a0063004060004000500300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS + MBEDTLS_ERR_ASN1_LENGTH_MISMATCH

X509 CRT ASN1 (TBS, inv v3Ext, SubjectAltName repeated)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"3081dc3081c6a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a340303e301d0603551d11041630148208666f6f2e7465737482086261722e74657374301d0603551d11041630148208666f6f2e7465737482086261722e74657374300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS

X509 CRT ASN1 (TBS, inv v3Ext, ExtKeyUsage repeated)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"3081dc3081c6a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a340303e301d0603551d250416301406082b0601050507030106082b06010505070302301d0603551d250416301406082b0601050507030106082b06010505070302300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS

X509 CRT ASN1 (TBS, inv v3Ext, SubjectAltName repeated outside Extensions)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"3081dc3081c6a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a321301f301d0603551d11041630148208666f6f2e7465737482086261722e74657374301d0603551d11041630148208666f6f2e7465737482086261722e74657374300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_FORMAT + MBEDTLS_ERR_ASN1_LENGTH_MISMATCH

X509 CRT (TBS, valid v3Ext in v3 CRT)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"3081b93081a3a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa321301f301d0603551d11041630148208666f6f2e7465737482086261722e74657374300d06092a864886f70d01010b0500030200ff":"cert. version     \: 3\nserial number     \: DE\:AD\:BE\:EF\nissuer name       \: ??=Test\nsubject name      \: ??=Test\nissued  on        \: 2009-01-01 00\:00\:00\nexpires on        \: 2009-12-31 23\:59\:59\nsigned using      \: RSA with SHA-256\nRSA key size      \: 128 bits\nsubject alt name  \:\n    dNSName \: foo.test\n    dNSName \: bar.test\n":0

X509 CRT ASN1 (TBS, valid v3Ext in v1 CRT)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"3081b93081a3a0030201008204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa321301f301d0603551d11041630148208666f6f2e7465737482086261722e74657374300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_FORMAT + MBEDTLS_ERR_ASN1_LENGTH_MISMATCH

X509 CRT ASN1 (TBS, valid v3Ext in v2 CRT)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"3081b93081a3a0030201018204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa321301f301d0603551d11041630148208666f6f2e7465737482086261722e74657374300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_INVALID_FORMAT + MBEDTLS_ERR_ASN1_LENGTH_MISMATCH

X509 CRT ASN1 (TBS, valid SubjectID, valid IssuerID, inv v3Ext, SubjectAltName repeated outside Extensions, inv SubjectAltNames tag)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA1
x509parse_crt:"308203723082025aa003020102020111300d06092a864886f70d0101050500303b310b3009060355040613024e4c3111300f060355040a1308506f6c617253534c3119301706035504031310506f6c617253534c2054657374204341301e170d3132303531303133323334315a170d3232303531313133323334315a303a310b3009060355040613024e4c3111300f060355040a1308506f6c617253534c311830160603550403130f7777772e6578616d706c652e636f6d30820122300d06092a864886f70d01010105000382010f003082010a0282010100b93c4ac5c8a38e9017a49e52aa7175266180e7c7b56d8cffaab64126b7be11ad5c73160c64114804ffd6e13b05db89bbb39709d51c14dd688739b03d71cbe276d01ad8182d801b54f6e5449af1cbaf612edf490d9d09b7edb1fd3cfd3cfa24cf5dbf7ce453e725b5ea4422e926d3ea20949ee66167ba2e07670b032fa209edf0338f0bce10ef67a4c608dac1edc23fd74add153df95e1c8160463eb5b33d2fa6de471cbc92aeebdf276b1656b7dcecd15557a56eec7525f5b77bdfabd23a5a91987d97170b130aa76b4a8bc14730fb3af84104d5c1dfb81dbf7b01a565a2e01e36b7a65ccc305af8cd6fcdf1196225ca01e3357ffa20f5dcfd69b26a007d17f70203010001a38181307f30090603551d1304023000301d0603551d0e041604147de49c6be6f9717d46d2123dad6b1dfdc2aa784c301f0603551d23041830168014b45ae4a5b3ded252f6b9d5a6950feb3ebcc7fdff30320603551d11042b3029c20b6578616d706c652e636f6d820b6578616d706c652e6e6574820d2a2e6578616d706c652e6f7267300d06092a864886f70d010105050003820101004f09cb7ad5eef5ef620ddc7ba285d68cca95b46bda115b92007513b9ca0bceeafbc31fe23f7f217479e2e6bcda06e52f6ff655c67339cf48bc0d2f0cd27a06c34a4cd9485da0d07389e4d4851d969a0e5799c66f1d21271f8d0529e840ae823968c39707cf3c934c1adf2fa6a455487f7c8c1ac922da24cd9239c68aecb08df5698267cb04eede534196c127dc2ffe33fad30eb8d432a9842853a5f0d189d5a298e71691bb9cc0418e8c58acffe3dd2e7aabb0b97176ad0f2733f7a929d3c076c0bf06407c0ed5a47c8ae2326e16aeda641fb0557cdbddf1a4ba447cb39958d2346e00ea976c143af2101e0aa249107601f4f2c818fdcc6346128b091bf194e6":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS + MBEDTLS_ERR_ASN1_UNEXPECTED_TAG

X509 CRT ASN1 (SignatureAlgorithm missing)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"3081aa3081a7a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a321301f301d0603551d11041630148208666f6f2e7465737482086261722e74657374":"":MBEDTLS_ERR_X509_INVALID_ALG + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (inv SignatureAlgorithm, bad tag)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"3081ac3081a7a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a321301f301d0603551d11041630148208666f6f2e7465737482086261722e746573740500":"":MBEDTLS_ERR_X509_INVALID_ALG + MBEDTLS_ERR_ASN1_UNEXPECTED_TAG

X509 CRT ASN1 (inv SignatureAlgorithm, length missing)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"3081ab3081a7a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a321301f301d0603551d11041630148208666f6f2e7465737482086261722e7465737430":"":MBEDTLS_ERR_X509_INVALID_ALG + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (inv SignatureAlgorithm, inv length encoding)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"3081ac3081a7a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a321301f301d0603551d11041630148208666f6f2e7465737482086261722e746573743085":"":MBEDTLS_ERR_X509_INVALID_ALG + MBEDTLS_ERR_ASN1_INVALID_LENGTH

X509 CRT ASN1 (inv SignatureAlgorithm, length out of bounds)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"3081ac3081a7a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a321301f301d0603551d11041630148208666f6f2e7465737482086261722e746573743001":"":MBEDTLS_ERR_X509_INVALID_ALG + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (inv SignatureAlgorithm, not the same as SignatureAlgorithm in TBS)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"3081bd3081a7a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a321301f301d0603551d11041630148208666f6f2e7465737482086261722e74657374300d06092a864886f70d01010a0500030200ff":"":MBEDTLS_ERR_X509_SIG_MISMATCH

X509 CRT ASN1 (Signature missing)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"3081b93081a7a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a321301f301d0603551d11041630148208666f6f2e7465737482086261722e74657374300d06092a864886f70d01010b0500":"":MBEDTLS_ERR_X509_INVALID_SIGNATURE + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (inv Signature, bad tag)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"3081bb3081a7a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a321301f301d0603551d11041630148208666f6f2e7465737482086261722e74657374300d06092a864886f70d01010b05000500":"":MBEDTLS_ERR_X509_INVALID_SIGNATURE + MBEDTLS_ERR_ASN1_UNEXPECTED_TAG

X509 CRT ASN1 (inv Signature, length missing)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"3081ba3081a7a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a321301f301d0603551d11041630148208666f6f2e7465737482086261722e74657374300d06092a864886f70d01010b050003":"":MBEDTLS_ERR_X509_INVALID_SIGNATURE + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (inv Signature, inv length encoding)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"3081bb3081a7a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a321301f301d0603551d11041630148208666f6f2e7465737482086261722e74657374300d06092a864886f70d01010b05000385":"":MBEDTLS_ERR_X509_INVALID_SIGNATURE + MBEDTLS_ERR_ASN1_INVALID_LENGTH

X509 CRT ASN1 (inv Signature, length out of bounds)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"3081bb3081a7a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a321301f301d0603551d11041630148208666f6f2e7465737482086261722e74657374300d06092a864886f70d01010b05000301":"":MBEDTLS_ERR_X509_INVALID_SIGNATURE + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRT ASN1 (inv Signature, inv data #1)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
# signature = bit string with invalid encoding (missing number of unused bits)
x509parse_crt:"3081bb3081a7a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a321301f301d0603551d11041630148208666f6f2e7465737482086261722e74657374300d06092a864886f70d01010b05000300":"":MBEDTLS_ERR_X509_INVALID_SIGNATURE + MBEDTLS_ERR_ASN1_INVALID_DATA

X509 CRT ASN1 (inv Signature, inv data #2)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
# signature = bit string with invalid encoding (number of unused bits too large)
x509parse_crt:"3081bc3081a7a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a321301f301d0603551d11041630148208666f6f2e7465737482086261722e74657374300d06092a864886f70d01010b0500030108":"":MBEDTLS_ERR_X509_INVALID_SIGNATURE + MBEDTLS_ERR_ASN1_INVALID_DATA

X509 CRT ASN1 (empty Signature)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
# signature = empty bit string in DER encoding
x509parse_crt:"3081bc3081a7a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a321301f301d0603551d11041630148208666f6f2e7465737482086261722e74657374300d06092a864886f70d01010b0500030100":"cert. version     \: 3\nserial number     \: DE\:AD\:BE\:EF\nissuer name       \: ??=Test\nsubject name      \: ??=Test\nissued  on        \: 2009-01-01 00\:00\:00\nexpires on        \: 2009-12-31 23\:59\:59\nsigned using      \: RSA with SHA-256\nRSA key size      \: 128 bits\nsubject alt name  \:\n    dNSName \: foo.test\n    dNSName \: bar.test\n":0

X509 CRT ASN1 (dummy 24-bit Signature)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
# signature = bit string "011001100110111101101111"
x509parse_crt:"3081bf3081a7a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a321301f301d0603551d11041630148208666f6f2e7465737482086261722e74657374300d06092a864886f70d01010b0500030400666f6f":"cert. version     \: 3\nserial number     \: DE\:AD\:BE\:EF\nissuer name       \: ??=Test\nsubject name      \: ??=Test\nissued  on        \: 2009-01-01 00\:00\:00\nexpires on        \: 2009-12-31 23\:59\:59\nsigned using      \: RSA with SHA-256\nRSA key size      \: 128 bits\nsubject alt name  \:\n    dNSName \: foo.test\n    dNSName \: bar.test\n":0

# The ASN.1 module rejects non-octet-aligned bit strings.
X509 CRT ASN1 (inv Signature: not octet-aligned)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
# signature = bit string "01100110011011110110111"
x509parse_crt:"3081bf3081a7a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a321301f301d0603551d11041630148208666f6f2e7465737482086261722e74657374300d06092a864886f70d01010b0500030401666f6e":"":MBEDTLS_ERR_X509_INVALID_SIGNATURE + MBEDTLS_ERR_ASN1_INVALID_DATA

X509 CRT ASN1 (inv Signature, length mismatch)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"3081be3081a7a0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a321301f301d0603551d11041630148208666f6f2e7465737482086261722e74657374300d06092a864886f70d01010b0500030200ff00":"":MBEDTLS_ERR_X509_INVALID_FORMAT + MBEDTLS_ERR_ASN1_LENGTH_MISMATCH

X509 CRT ASN1 (well-formed)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"308196308180a0030201008204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092a864886f70d010101050003190030160210ffffffffffffffffffffffffffffffff0202ffff300d06092a864886f70d01010b0500030200ff":"cert. version     \: 1\nserial number     \: DE\:AD\:BE\:EF\nissuer name       \: ?\?=Test\nsubject name      \: ?\?=Test\nissued  on        \: 2009-01-01 00\:00\:00\nexpires on        \: 2009-12-31 23\:59\:59\nsigned using      \: RSA with SHA-256\nRSA key size      \: 128 bits\n":0

X509 CRT ASN1 (GeneralizedTime in notBefore, UTCTime in notAfter)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"308198308182a0030201008204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301e180e3230313030313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092a864886f70d010101050003190030160210ffffffffffffffffffffffffffffffff0202ffff300d06092a864886f70d01010b0500030200ff":"cert. version     \: 1\nserial number     \: DE\:AD\:BE\:EF\nissuer name       \: ?\?=Test\nsubject name      \: ?\?=Test\nissued  on        \: 2010-01-01 00\:00\:00\nexpires on        \: 2009-12-31 23\:59\:59\nsigned using      \: RSA with SHA-256\nRSA key size      \: 128 bits\n":0

X509 CRT ASN1 (UTCTime in notBefore, GeneralizedTime in notAfter)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"308198308182a0030201008204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301e170c303931323331323335393539180e3230313030313031303030303030300c310a30080600130454657374302a300d06092A864886F70D010101050003190030160210ffffffffffffffffffffffffffffffff0202ffff300d06092a864886f70d01010b0500030200ff":"cert. version     \: 1\nserial number     \: DE\:AD\:BE\:EF\nissuer name       \: ?\?=Test\nsubject name      \: ?\?=Test\nissued  on        \: 2009-12-31 23\:59\:59\nexpires on        \: 2010-01-01 00\:00\:00\nsigned using      \: RSA with SHA-256\nRSA key size      \: 128 bits\n":0

X509 CRT ASN1 (Name with X520 CN)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"308199308183a0030201008204deadbeef300d06092a864886f70d01010b0500300f310d300b0603550403130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092a864886f70d010101050003190030160210ffffffffffffffffffffffffffffffff0202ffff300d06092a864886f70d01010b0500030200ff":"cert. version     \: 1\nserial number     \: DE\:AD\:BE\:EF\nissuer name       \: CN=Test\nsubject name      \: ?\?=Test\nissued  on        \: 2009-01-01 00\:00\:00\nexpires on        \: 2009-12-31 23\:59\:59\nsigned using      \: RSA with SHA-256\nRSA key size      \: 128 bits\n":0

X509 CRT ASN1 (Name with X520 C)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"308199308183a0030201008204deadbeef300d06092a864886f70d01010b0500300f310d300b0603550406130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092a864886f70d010101050003190030160210ffffffffffffffffffffffffffffffff0202ffff300d06092a864886f70d01010b0500030200ff":"cert. version     \: 1\nserial number     \: DE\:AD\:BE\:EF\nissuer name       \: C=Test\nsubject name      \: ?\?=Test\nissued  on        \: 2009-01-01 00\:00\:00\nexpires on        \: 2009-12-31 23\:59\:59\nsigned using      \: RSA with SHA-256\nRSA key size      \: 128 bits\n":0

X509 CRT ASN1 (Name with X520 L)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"308199308183a0030201008204deadbeef300d06092a864886f70d01010b0500300f310d300b0603550407130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092a864886f70d010101050003190030160210ffffffffffffffffffffffffffffffff0202ffff300d06092a864886f70d01010b0500030200ff":"cert. version     \: 1\nserial number     \: DE\:AD\:BE\:EF\nissuer name       \: L=Test\nsubject name      \: ?\?=Test\nissued  on        \: 2009-01-01 00\:00\:00\nexpires on        \: 2009-12-31 23\:59\:59\nsigned using      \: RSA with SHA-256\nRSA key size      \: 128 bits\n":0

X509 CRT ASN1 (Name with X520 ST)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"308199308183a0030201008204deadbeef300d06092a864886f70d01010b0500300f310d300b0603550408130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092a864886f70d010101050003190030160210ffffffffffffffffffffffffffffffff0202ffff300d06092a864886f70d01010b0500030200ff":"cert. version     \: 1\nserial number     \: DE\:AD\:BE\:EF\nissuer name       \: ST=Test\nsubject name      \: ?\?=Test\nissued  on        \: 2009-01-01 00\:00\:00\nexpires on        \: 2009-12-31 23\:59\:59\nsigned using      \: RSA with SHA-256\nRSA key size      \: 128 bits\n":0

X509 CRT ASN1 (Name with X520 O)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"308199308183a0030201008204deadbeef300d06092a864886f70d01010b0500300f310d300b060355040a130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092a864886f70d010101050003190030160210ffffffffffffffffffffffffffffffff0202ffff300d06092a864886f70d01010b0500030200ff":"cert. version     \: 1\nserial number     \: DE\:AD\:BE\:EF\nissuer name       \: O=Test\nsubject name      \: ?\?=Test\nissued  on        \: 2009-01-01 00\:00\:00\nexpires on        \: 2009-12-31 23\:59\:59\nsigned using      \: RSA with SHA-256\nRSA key size      \: 128 bits\n":0

X509 CRT ASN1 (Name with X520 OU)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"308199308183a0030201008204deadbeef300d06092a864886f70d01010b0500300f310d300b060355040b130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092a864886f70d010101050003190030160210ffffffffffffffffffffffffffffffff0202ffff300d06092a864886f70d01010b0500030200ff":"cert. version     \: 1\nserial number     \: DE\:AD\:BE\:EF\nissuer name       \: OU=Test\nsubject name      \: ?\?=Test\nissued  on        \: 2009-01-01 00\:00\:00\nexpires on        \: 2009-12-31 23\:59\:59\nsigned using      \: RSA with SHA-256\nRSA key size      \: 128 bits\n":0

X509 CRT ASN1 (Name with unknown X520 part)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"308199308183a0030201008204deadbeef300d06092a864886f70d01010b0500300f310d300b06035504de130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092a864886f70d010101050003190030160210ffffffffffffffffffffffffffffffff0202ffff300d06092a864886f70d01010b0500030200ff":"cert. version     \: 1\nserial number     \: DE\:AD\:BE\:EF\nissuer name       \: ?\?=Test\nsubject name      \: ?\?=Test\nissued  on        \: 2009-01-01 00\:00\:00\nexpires on        \: 2009-12-31 23\:59\:59\nsigned using      \: RSA with SHA-256\nRSA key size      \: 128 bits\n":0

X509 CRT ASN1 (Name with composite RDN)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA1
x509parse_crt:"3082029f30820208a00302010202044c20e3bd300d06092a864886f70d01010505003056310b3009060355040613025553310b300906035504080c0243413121301f060355040a0c18496e7465726e6574205769646769747320507479204c74643117301506035504030c0e4672616e6b656e63657274204341301e170d3133303830323135313433375a170d3135303831373035353433315a3081d1310b3009060355040613025553311330110603550408130a57617368696e67746f6e31133011060b2b0601040182373c0201031302555331193017060b2b0601040182373c020102130844656c6177617265311a3018060355040a1311417574686f72697a652e4e6574204c4c43311d301b060355040f131450726976617465204f7267616e697a6174696f6e312a300e06035504051307343336393139313018060355040313117777772e617574686f72697a652e6e6574311630140603550407130d53616e204672616e636973636f30819f300d06092a864886f70d010101050003818d0030818902818100d885c62e209b6ac005c64f0bcfdaac1f2b67a18802f75b08851ff933deed888b7b68a62fcabdb21d4a8914becfeaaa1b7e08a09ffaf9916563586dc95e2877262b0b5f5ec27eb4d754aa6facd1d39d25b38a2372891bacdd3e919f791ed25704e8920e380e5623a38e6a23935978a3aec7a8e761e211d42effa2713e44e7de0b0203010001300d06092a864886f70d010105050003818100092f7424d3f6da4b8553829d958ed1980b9270b42c0d3d5833509a28c66bb207df9f3c51d122065e00b87c08c2730d2745fe1c279d16fae4d53b4bf5bdfa3631fceeb2e772b6b08a3eca5a2e2c687aefd23b4b73bf77ac6099711342cf070b35c6f61333a7cbf613d8dd4bd73e9df34bcd4284b0b4df57c36c450613f11e5dac":"cert. version     \: 3\nserial number     \: 4C\:20\:E3\:BD\nissuer name       \: C=US, ST=CA, O=Internet Widgits Pty Ltd, CN=Frankencert CA\nsubject name      \: C=US, ST=Washington, *******.4.1.311.60.2.1.3=#13025553, *******.4.1.311.60.2.1.2=#130844656C6177617265, O=Authorize.Net LLC, 2.5.4.15=#131450726976617465204F7267616E697A6174696F6E, serialNumber=4369191 + CN=www.authorize.net, L=San Francisco\nissued  on        \: 2013-08-02 15\:14\:37\nexpires on        \: 2015-08-17 05\:54\:31\nsigned using      \: RSA with SHA1\nRSA key size      \: 1024 bits\n":0

X509 CRT ASN1 (Name with PKCS9 email)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"30819f308189a0030201008204deadbeef300d06092a864886f70d01010b050030153113301106092a864886f70d010901130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092a864886f70d010101050003190030160210ffffffffffffffffffffffffffffffff0202ffff300d06092a864886f70d01010b0500030200ff":"cert. version     \: 1\nserial number     \: DE\:AD\:BE\:EF\nissuer name       \: emailAddress=Test\nsubject name      \: ?\?=Test\nissued  on        \: 2009-01-01 00\:00\:00\nexpires on        \: 2009-12-31 23\:59\:59\nsigned using      \: RSA with SHA-256\nRSA key size      \: 128 bits\n":0

X509 CRT ASN1 (Name with unknown PKCS9 part)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"30819f308189a0030201008204deadbeef300d06092a864886f70d01010b050030153113301106092a864886f70d0109ab130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092a864886f70d010101050003190030160210ffffffffffffffffffffffffffffffff0202ffff300d06092a864886f70d01010b0500030200ff":"cert. version     \: 1\nserial number     \: DE\:AD\:BE\:EF\nissuer name       \: ?\?=Test\nsubject name      \: ?\?=Test\nissued  on        \: 2009-01-01 00\:00\:00\nexpires on        \: 2009-12-31 23\:59\:59\nsigned using      \: RSA with SHA-256\nRSA key size      \: 128 bits\n":0

X509 CRT ASN1 (ECDSA signature, RSA key)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA1:MBEDTLS_PK_CAN_ECDSA_SOME
x509parse_crt:"3081e630819e020103300906072a8648ce3d0401300f310d300b0603550403130454657374301e170d3133303731303039343631385a170d3233303730383039343631385a300f310d300b0603550403130454657374304c300d06092a864886f70d0101010500033b003038023100e8f546061d3b49bc2f6b7524b7ea4d73a8d5293ee8c64d9407b70b5d16baebc32b8205591eab4e1eb57e9241883701250203010001300906072a8648ce3d0401033800303502186e18209afbed14a0d9a796efcad68891e3ccd5f75815c833021900e92b4fd460b1994693243b9ffad54729de865381bda41d25":"cert. version     \: 1\nserial number     \: 03\nissuer name       \: CN=Test\nsubject name      \: CN=Test\nissued  on        \: 2013-07-10 09\:46\:18\nexpires on        \: 2023-07-08 09\:46\:18\nsigned using      \: ECDSA with SHA1\nRSA key size      \: 384 bits\n":0

X509 CRT ASN1 (ECDSA signature, EC key)
depends_on:MBEDTLS_PK_CAN_ECDSA_SOME:MBEDTLS_ECP_HAVE_SECP192R1:MBEDTLS_MD_CAN_SHA1
x509parse_crt:"3081eb3081a3020900f41534662ec7e912300906072a8648ce3d0401300f310d300b0603550403130454657374301e170d3133303731303039343031395a170d3233303730383039343031395a300f310d300b06035504031304546573743049301306072a8648ce3d020106082a8648ce3d030101033200042137969fabd4e370624a0e1a33e379cab950cce00ef8c3c3e2adaeb7271c8f07659d65d3d777dcf21614363ae4b6e617300906072a8648ce3d04010338003035021858cc0f957946fe6a303d92885a456aa74c743c7b708cbd37021900fe293cac21af352d16b82eb8ea54e9410b3abaadd9f05dd6":"cert. version     \: 1\nserial number     \: F4\:15\:34\:66\:2E\:C7\:E9\:12\nissuer name       \: CN=Test\nsubject name      \: CN=Test\nissued  on        \: 2013-07-10 09\:40\:19\nexpires on        \: 2023-07-08 09\:40\:19\nsigned using      \: ECDSA with SHA1\nEC key size       \: 192 bits\n":0

X509 CRT ASN1 (RSA signature, EC key)
depends_on:MBEDTLS_PK_CAN_ECDSA_SOME:MBEDTLS_ECP_HAVE_SECP192R1:MBEDTLS_MD_CAN_SHA1:MBEDTLS_RSA_C
x509parse_crt:"3081e430819f020104300d06092a864886f70d0101050500300f310d300b0603550403130454657374301e170d3133303731303135303233375a170d3233303730383135303233375a300f310d300b06035504031304546573743049301306072a8648ce3d020106082a8648ce3d03010103320004e962551a325b21b50cf6b990e33d4318fd16677130726357a196e3efe7107bcb6bdc6d9db2a4df7c964acfe81798433d300d06092a864886f70d01010505000331001a6c18cd1e457474b2d3912743f44b571341a7859a0122774a8e19a671680878936949f904c9255bdd6fffdb33a7e6d8":"cert. version     \: 1\nserial number     \: 04\nissuer name       \: CN=Test\nsubject name      \: CN=Test\nissued  on        \: 2013-07-10 15\:02\:37\nexpires on        \: 2023-07-08 15\:02\:37\nsigned using      \: RSA with SHA1\nEC key size       \: 192 bits\n":0

X509 CRT ASN1 (Unsupported critical extension)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt:"308203353082021da00302010202104d3ebbb8a870f9c78c55a8a7e12fd516300d06092a864886f70d01010b05003010310e300c06035504030c0564756d6d79301e170d3230303432383137343234335a170d3230303632373137343234335a3010310e300c06035504030c0564756d6d7930820122300d06092a864886f70d01010105000382010f003082010a0282010100a51b75b3f7da2d60ea1b0fc077f0dbb2bbb6fe1b474028368af8dc2664672896efff171033b0aede0b323a89d5c6db4d517404bc97b65264e41b9e9e86a6f40ace652498d4b3b859544d1bacfd7f86325503eed046f517406545c0ffb5560f83446dedce0fcafcc41ac8495488a6aa912ae45192ef7e3efa20d0f7403b0baa62c7e2e5404c620c5793623132aa20f624f08d88fbf0985af39433f5a24d0b908e5219d8ba6a404d3ee8418203b62a40c8eb18837354d50281a6a2bf5012e505c419482787b7a81e5935613ceea0c6d93e86f76282b6aa406fb3a1796c56b32e8a22afc3f7a3c9daa8f0e2846ff0d50abfc862a52f6cf0aaece6066c860376f3ed0203010001a3818a308187300c0603551d13040530030101ff30130603551d110101ff04093007820564756d6d79301206082b0601050507011f0101ff0403040100300e0603551d0f0101ff040403020184301d0603551d0e04160414e6e451ec8d19d9677b2d272a9d73b939fa2d915a301f0603551d23041830168014e6e451ec8d19d9677b2d272a9d73b939fa2d915a300d06092a864886f70d01010b0500038201010056d06047b7f48683e2347ca726997d9700b4f2cf1d8bc0ef17addac8445d38ffd7f8079055ead878b6a74c8384d0e30150c8990aa74f59cda6ebcb49465d8991ffa16a4c927a26e4639d1875a3ac396c7455c7eda40dbe66054a03d27f961c15e86bd5b06db6b26572977bcda93453b6b6a88ef96b31996a7bd17323525b33050d28deec9c33a3f9765a11fb99d0e222bd39a6db3a788474c9ca347377688f837d42f5841667bffcbe6b473e6f229f286a0829963e591a99aa7f67e9d20c36ccd2ac84cb85b7a8b3396a6cbe59a573ffff726f373197c230de5c92a52c5bc87e29c20bdf6e89609764a60c649022aabd768f3557661b083ae00e6afc8a5bf2ed":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS + MBEDTLS_ERR_ASN1_UNEXPECTED_TAG

X509 CRT ASN1 (Unsupported critical extension recognized by callback)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt_cb:"308203353082021da00302010202104d3ebbb8a870f9c78c55a8a7e12fd516300d06092a864886f70d01010b05003010310e300c06035504030c0564756d6d79301e170d3230303432383137343234335a170d3230303632373137343234335a3010310e300c06035504030c0564756d6d7930820122300d06092a864886f70d01010105000382010f003082010a0282010100a51b75b3f7da2d60ea1b0fc077f0dbb2bbb6fe1b474028368af8dc2664672896efff171033b0aede0b323a89d5c6db4d517404bc97b65264e41b9e9e86a6f40ace652498d4b3b859544d1bacfd7f86325503eed046f517406545c0ffb5560f83446dedce0fcafcc41ac8495488a6aa912ae45192ef7e3efa20d0f7403b0baa62c7e2e5404c620c5793623132aa20f624f08d88fbf0985af39433f5a24d0b908e5219d8ba6a404d3ee8418203b62a40c8eb18837354d50281a6a2bf5012e505c419482787b7a81e5935613ceea0c6d93e86f76282b6aa406fb3a1796c56b32e8a22afc3f7a3c9daa8f0e2846ff0d50abfc862a52f6cf0aaece6066c860376f3ed0203010001a3818a308187300c0603551d13040530030101ff30130603551d110101ff04093007820564756d6d79301206082b0601050507011f0101ff0403040100300e0603551d0f0101ff040403020184301d0603551d0e04160414e6e451ec8d19d9677b2d272a9d73b939fa2d915a301f0603551d23041830168014e6e451ec8d19d9677b2d272a9d73b939fa2d915a300d06092a864886f70d01010b0500038201010056d06047b7f48683e2347ca726997d9700b4f2cf1d8bc0ef17addac8445d38ffd7f8079055ead878b6a74c8384d0e30150c8990aa74f59cda6ebcb49465d8991ffa16a4c927a26e4639d1875a3ac396c7455c7eda40dbe66054a03d27f961c15e86bd5b06db6b26572977bcda93453b6b6a88ef96b31996a7bd17323525b33050d28deec9c33a3f9765a11fb99d0e222bd39a6db3a788474c9ca347377688f837d42f5841667bffcbe6b473e6f229f286a0829963e591a99aa7f67e9d20c36ccd2ac84cb85b7a8b3396a6cbe59a573ffff726f373197c230de5c92a52c5bc87e29c20bdf6e89609764a60c649022aabd768f3557661b083ae00e6afc8a5bf2ed":"cert. version     \: 3\nserial number     \: 4D\:3E\:BB\:B8\:A8\:70\:F9\:C7\:8C\:55\:A8\:A7\:E1\:2F\:D5\:16\nissuer name       \: CN=dummy\nsubject name      \: CN=dummy\nissued  on        \: 2020-04-28 17\:42\:43\nexpires on        \: 2020-06-27 17\:42\:43\nsigned using      \: RSA with SHA-256\nRSA key size      \: 2048 bits\nbasic constraints \: CA=true\nsubject alt name  \:\n    dNSName \: dummy\nkey usage         \: Digital Signature, Key Cert Sign\n":0

X509 CRT ASN1 (Unsupported critical extension not recognized by callback)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt_cb:"308203353082021da00302010202104d3ebbb8a870f9c78c55a8a7e12fd516300d06092a864886f70d01010b05003010310e300c06035504030c0564756d6d79301e170d3230303432383137343234335a170d3230303632373137343234335a3010310e300c06035504030c0564756d6d7930820122300d06092a864886f70d01010105000382010f003082010a0282010100a51b75b3f7da2d60ea1b0fc077f0dbb2bbb6fe1b474028368af8dc2664672896efff171033b0aede0b323a89d5c6db4d517404bc97b65264e41b9e9e86a6f40ace652498d4b3b859544d1bacfd7f86325503eed046f517406545c0ffb5560f83446dedce0fcafcc41ac8495488a6aa912ae45192ef7e3efa20d0f7403b0baa62c7e2e5404c620c5793623132aa20f624f08d88fbf0985af39433f5a24d0b908e5219d8ba6a404d3ee8418203b62a40c8eb18837354d50281a6a2bf5012e505c419482787b7a81e5935613ceea0c6d93e86f76282b6aa406fb3a1796c56b32e8a22afc3f7a3c9daa8f0e2846ff0d50abfc862a52f6cf0aaece6066c860376f3ed0203010001a3818a308187300c0603551d13040530030101ff30130603551d110101ff04093007820564756d6d79301206082b0601050507011e0101ff0403040100300e0603551d0f0101ff040403020184301d0603551d0e04160414e6e451ec8d19d9677b2d272a9d73b939fa2d915a301f0603551d23041830168014e6e451ec8d19d9677b2d272a9d73b939fa2d915a300d06092a864886f70d01010b0500038201010056d06047b7f48683e2347ca726997d9700b4f2cf1d8bc0ef17addac8445d38ffd7f8079055ead878b6a74c8384d0e30150c8990aa74f59cda6ebcb49465d8991ffa16a4c927a26e4639d1875a3ac396c7455c7eda40dbe66054a03d27f961c15e86bd5b06db6b26572977bcda93453b6b6a88ef96b31996a7bd17323525b33050d28deec9c33a3f9765a11fb99d0e222bd39a6db3a788474c9ca347377688f837d42f5841667bffcbe6b473e6f229f286a0829963e591a99aa7f67e9d20c36ccd2ac84cb85b7a8b3396a6cbe59a573ffff726f373197c230de5c92a52c5bc87e29c20bdf6e89609764a60c649022aabd768f3557661b083ae00e6afc8a5bf2ed":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS + MBEDTLS_ERR_ASN1_UNEXPECTED_TAG

X509 CRT ASN1 (Unsupported non critical extension recognized by callback)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt_cb:"308203353082021da00302010202104d3ebbb8a870f9c78c55a8a7e12fd516300d06092a864886f70d01010b05003010310e300c06035504030c0564756d6d79301e170d3230303432383137343234335a170d3230303632373137343234335a3010310e300c06035504030c0564756d6d7930820122300d06092a864886f70d01010105000382010f003082010a0282010100a51b75b3f7da2d60ea1b0fc077f0dbb2bbb6fe1b474028368af8dc2664672896efff171033b0aede0b323a89d5c6db4d517404bc97b65264e41b9e9e86a6f40ace652498d4b3b859544d1bacfd7f86325503eed046f517406545c0ffb5560f83446dedce0fcafcc41ac8495488a6aa912ae45192ef7e3efa20d0f7403b0baa62c7e2e5404c620c5793623132aa20f624f08d88fbf0985af39433f5a24d0b908e5219d8ba6a404d3ee8418203b62a40c8eb18837354d50281a6a2bf5012e505c419482787b7a81e5935613ceea0c6d93e86f76282b6aa406fb3a1796c56b32e8a22afc3f7a3c9daa8f0e2846ff0d50abfc862a52f6cf0aaece6066c860376f3ed0203010001a3818a308187300c0603551d13040530030101ff30130603551d110101ff04093007820564756d6d79301206082b0601050507011f0101000403040100300e0603551d0f0101ff040403020184301d0603551d0e04160414e6e451ec8d19d9677b2d272a9d73b939fa2d915a301f0603551d23041830168014e6e451ec8d19d9677b2d272a9d73b939fa2d915a300d06092a864886f70d01010b0500038201010056d06047b7f48683e2347ca726997d9700b4f2cf1d8bc0ef17addac8445d38ffd7f8079055ead878b6a74c8384d0e30150c8990aa74f59cda6ebcb49465d8991ffa16a4c927a26e4639d1875a3ac396c7455c7eda40dbe66054a03d27f961c15e86bd5b06db6b26572977bcda93453b6b6a88ef96b31996a7bd17323525b33050d28deec9c33a3f9765a11fb99d0e222bd39a6db3a788474c9ca347377688f837d42f5841667bffcbe6b473e6f229f286a0829963e591a99aa7f67e9d20c36ccd2ac84cb85b7a8b3396a6cbe59a573ffff726f373197c230de5c92a52c5bc87e29c20bdf6e89609764a60c649022aabd768f3557661b083ae00e6afc8a5bf2ed":"cert. version     \: 3\nserial number     \: 4D\:3E\:BB\:B8\:A8\:70\:F9\:C7\:8C\:55\:A8\:A7\:E1\:2F\:D5\:16\nissuer name       \: CN=dummy\nsubject name      \: CN=dummy\nissued  on        \: 2020-04-28 17\:42\:43\nexpires on        \: 2020-06-27 17\:42\:43\nsigned using      \: RSA with SHA-256\nRSA key size      \: 2048 bits\nbasic constraints \: CA=true\nsubject alt name  \:\n    dNSName \: dummy\nkey usage         \: Digital Signature, Key Cert Sign\n":0

X509 CRT ASN1 (Unsupported non critical extension not recognized by callback)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt_cb:"308203353082021da00302010202104d3ebbb8a870f9c78c55a8a7e12fd516300d06092a864886f70d01010b05003010310e300c06035504030c0564756d6d79301e170d3230303432383137343234335a170d3230303632373137343234335a3010310e300c06035504030c0564756d6d7930820122300d06092a864886f70d01010105000382010f003082010a0282010100a51b75b3f7da2d60ea1b0fc077f0dbb2bbb6fe1b474028368af8dc2664672896efff171033b0aede0b323a89d5c6db4d517404bc97b65264e41b9e9e86a6f40ace652498d4b3b859544d1bacfd7f86325503eed046f517406545c0ffb5560f83446dedce0fcafcc41ac8495488a6aa912ae45192ef7e3efa20d0f7403b0baa62c7e2e5404c620c5793623132aa20f624f08d88fbf0985af39433f5a24d0b908e5219d8ba6a404d3ee8418203b62a40c8eb18837354d50281a6a2bf5012e505c419482787b7a81e5935613ceea0c6d93e86f76282b6aa406fb3a1796c56b32e8a22afc3f7a3c9daa8f0e2846ff0d50abfc862a52f6cf0aaece6066c860376f3ed0203010001a3818a308187300c0603551d13040530030101ff30130603551d110101ff04093007820564756d6d79301206082b0601050507011e0101000403040100300e0603551d0f0101ff040403020184301d0603551d0e04160414e6e451ec8d19d9677b2d272a9d73b939fa2d915a301f0603551d23041830168014e6e451ec8d19d9677b2d272a9d73b939fa2d915a300d06092a864886f70d01010b0500038201010056d06047b7f48683e2347ca726997d9700b4f2cf1d8bc0ef17addac8445d38ffd7f8079055ead878b6a74c8384d0e30150c8990aa74f59cda6ebcb49465d8991ffa16a4c927a26e4639d1875a3ac396c7455c7eda40dbe66054a03d27f961c15e86bd5b06db6b26572977bcda93453b6b6a88ef96b31996a7bd17323525b33050d28deec9c33a3f9765a11fb99d0e222bd39a6db3a788474c9ca347377688f837d42f5841667bffcbe6b473e6f229f286a0829963e591a99aa7f67e9d20c36ccd2ac84cb85b7a8b3396a6cbe59a573ffff726f373197c230de5c92a52c5bc87e29c20bdf6e89609764a60c649022aabd768f3557661b083ae00e6afc8a5bf2ed":"cert. version     \: 3\nserial number     \: 4D\:3E\:BB\:B8\:A8\:70\:F9\:C7\:8C\:55\:A8\:A7\:E1\:2F\:D5\:16\nissuer name       \: CN=dummy\nsubject name      \: CN=dummy\nissued  on        \: 2020-04-28 17\:42\:43\nexpires on        \: 2020-06-27 17\:42\:43\nsigned using      \: RSA with SHA-256\nRSA key size      \: 2048 bits\nbasic constraints \: CA=true\nsubject alt name  \:\n    dNSName \: dummy\nkey usage         \: Digital Signature, Key Cert Sign\n":0

X509 CRT ASN1 (Unsupported critical policy recognized by callback)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt_cb:"3081b130819ba0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092a864886f70d010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a315301330110603551d20010101040730053003060101300d06092a864886f70d01010b0500030200ff":"cert. version     \: 3\nserial number     \: DE\:AD\:BE\:EF\nissuer name       \: ??=Test\nsubject name      \: ??=Test\nissued  on        \: 2009-01-01 00\:00\:00\nexpires on        \: 2009-12-31 23\:59\:59\nsigned using      \: RSA with SHA-256\nRSA key size      \: 128 bits\ncertificate policies \: ???\n":0

X509 CRT ASN1 (Unsupported critical policy not recognized by callback)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt_cb:"3081b130819ba0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092a864886f70d010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a315301330110603551d20010101040730053003060100300d06092a864886f70d01010b0500030200ff":"":MBEDTLS_ERR_X509_FEATURE_UNAVAILABLE

X509 CRT ASN1 (Unsupported non critical policy recognized by callback)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt_cb:"3081b130819ba0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092a864886f70d010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a315301330110603551d20010100040730053003060101300d06092a864886f70d01010b0500030200ff":"cert. version     \: 3\nserial number     \: DE\:AD\:BE\:EF\nissuer name       \: ??=Test\nsubject name      \: ??=Test\nissued  on        \: 2009-01-01 00\:00\:00\nexpires on        \: 2009-12-31 23\:59\:59\nsigned using      \: RSA with SHA-256\nRSA key size      \: 128 bits\ncertificate policies \: ???\n":0

X509 CRT ASN1 (Unsupported non critical policy not recognized by callback)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crt_cb:"3081b130819ba0030201028204deadbeef300d06092a864886f70d01010b0500300c310a30080600130454657374301c170c303930313031303030303030170c303931323331323335393539300c310a30080600130454657374302a300d06092a864886f70d010101050003190030160210ffffffffffffffffffffffffffffffff0202ffffa100a200a315301330110603551d20010100040730053003060100300d06092a864886f70d01010b0500030200ff":"cert. version     \: 3\nserial number     \: DE\:AD\:BE\:EF\nissuer name       \: ??=Test\nsubject name      \: ??=Test\nissued  on        \: 2009-01-01 00\:00\:00\nexpires on        \: 2009-12-31 23\:59\:59\nsigned using      \: RSA with SHA-256\nRSA key size      \: 128 bits\ncertificate policies \: ???\n":0

X509 CRL ASN1 (Incorrect first tag)
x509parse_crl:"":"":MBEDTLS_ERR_X509_INVALID_FORMAT

X509 CRL ASN1 (Correct first tag, data length does not match)
x509parse_crl:"300000":"":MBEDTLS_ERR_X509_INVALID_FORMAT + MBEDTLS_ERR_ASN1_LENGTH_MISMATCH

X509 CRL ASN1 (TBSCertList, tag missing)
x509parse_crl:"3000":"":MBEDTLS_ERR_X509_INVALID_FORMAT + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRL ASN1 (TBSCertList, version tag len missing)
x509parse_crl:"3003300102":"":MBEDTLS_ERR_X509_INVALID_VERSION + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRL ASN1 (TBSCertList, version correct, alg missing)
x509parse_crl:"30053003020100":"":MBEDTLS_ERR_X509_INVALID_ALG + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRL ASN1 (TBSCertList, alg correct, incorrect version)
x509parse_crl:"300b3009020102300406000500":"":MBEDTLS_ERR_X509_UNKNOWN_VERSION

X509 CRL ASN1 (TBSCertList, correct version, sig_oid1 unknown)
x509parse_crl:"300b3009020100300406000500":"":MBEDTLS_ERR_X509_UNKNOWN_SIG_ALG

X509 CRL ASN1 (TBSCertList, sig_oid1 id unknown)
x509parse_crl:"30143012020100300d06092a864886f70d01010f0500":"":MBEDTLS_ERR_X509_UNKNOWN_SIG_ALG

X509 CRL ASN1 (TBSCertList, sig_oid1 correct, issuer missing)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA224
x509parse_crl:"30143012020100300d06092a864886f70d01010e0500":"":MBEDTLS_ERR_X509_INVALID_FORMAT + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRL ASN1 (TBSCertList, issuer set missing)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA224
x509parse_crl:"30163014020100300d06092a864886f70d01010e05003000":"":MBEDTLS_ERR_X509_INVALID_NAME + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRL ASN1 (TBSCertList, correct issuer, thisUpdate missing)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA224
x509parse_crl:"30253023020100300d06092a864886f70d01010e0500300f310d300b0603550403130441424344":"":MBEDTLS_ERR_X509_INVALID_DATE + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRL ASN1 (TBSCertList, correct thisUpdate, nextUpdate missing, entries length missing)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA224
x509parse_crl:"30343032020100300d06092a864886f70d01010e0500300f310d300b0603550403130441424344170c30393031303130303030303030":"":MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRL ASN1 (TBSCertList, entries present, invalid sig_alg)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA224
x509parse_crl:"304a3047020100300d06092a864886f70d01010e0500300f310d300b0603550403130441424344170c303930313031303030303030301430128202abcd170c30383132333132333539353900":"":MBEDTLS_ERR_X509_INVALID_ALG + MBEDTLS_ERR_ASN1_UNEXPECTED_TAG

X509 CRL ASN1 (TBSCertList, entries present, date in entry invalid)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA224
x509parse_crl:"304a3047020100300d06092a864886f70d01010e0500300f310d300b0603550403130441424344170c303930313031303030303030301430128202abcd190c30383132333132333539353900":"":MBEDTLS_ERR_X509_INVALID_DATE + MBEDTLS_ERR_ASN1_UNEXPECTED_TAG

X509 CRL ASN1 (TBSCertList, sig_alg present, sig_alg does not match)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA224
x509parse_crl:"30583047020100300d06092a864886f70d01010e0500300f310d300b0603550403130441424344170c303930313031303030303030301430128202abcd170c303831323331323335393539300d06092a864886f70d01010d0500":"":MBEDTLS_ERR_X509_SIG_MISMATCH

X509 CRL ASN1 (TBSCertList, sig present, len mismatch)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA224
x509parse_crl:"305d3047020100300d06092a864886f70d01010e0500300f310d300b0603550403130441424344170c303930313031303030303030301430128202abcd170c303831323331323335393539300d06092a864886f70d01010e05000302000100":"":MBEDTLS_ERR_X509_INVALID_FORMAT + MBEDTLS_ERR_ASN1_LENGTH_MISMATCH

# 305c
#  3047                                   tbsCertList TBSCertList
#   020100                                version INTEGER OPTIONAL
#   300d                                  signatureAlgorithm AlgorithmIdentifi
#    06092a864886f70d01010e
#    0500
#   300f                                  issuer Name
#    310d300b0603550403130441424344
#   170c303930313031303030303030          thisUpdate Time
#   3014                                  revokedCertificates
#    3012                                 entry 1
#     8202abcd                            userCertificate CertificateSerialNum
#     170c303831323331323335393539        revocationDate Time
#  300d                                   signatureAlgorithm AlgorithmIdentifi
#   06092a864886f70d01010e
#   0500
#  03020001                               signatureValue BIT STRING
# The subsequent TBSCertList negative tests remove or modify some elements.
X509 CRL ASN1 (TBSCertList, sig present)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA224:!MBEDTLS_X509_REMOVE_INFO
x509parse_crl:"305c3047020100300d06092a864886f70d01010e0500300f310d300b0603550403130441424344170c303930313031303030303030301430128202abcd170c303831323331323335393539300d06092a864886f70d01010e050003020001":"CRL version   \: 1\nissuer name   \: CN=ABCD\nthis update   \: 2009-01-01 00\:00\:00\nnext update   \: 0000-00-00 00\:00\:00\nRevoked certificates\:\nserial number\: AB\:CD revocation date\: 2008-12-31 23\:59\:59\nsigned using  \: RSA with SHA-224\n":0

X509 CRL ASN1 (TBSCertList, signatureValue missing)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA224
x509parse_crl:"30583047020100300d06092a864886f70d01010e0500300f310d300b0603550403130441424344170c303930313031303030303030301430128202abcd170c303831323331323335393539300d06092a864886f70d01010e0500":"":MBEDTLS_ERR_X509_INVALID_SIGNATURE + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRL ASN1 (TBSCertList, signatureAlgorithm missing)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA224
x509parse_crl:"30493047020100300d06092a864886f70d01010e0500300f310d300b0603550403130441424344170c303930313031303030303030301430128202abcd170c303831323331323335393539":"":MBEDTLS_ERR_X509_INVALID_ALG + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRL ASN1 (TBSCertList, single empty entry at end)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA224
x509parse_crl:"30373035020100300d06092a864886f70d01010e0500300f310d300b0603550403130441424344170c30393031303130303030303030023000":"":MBEDTLS_ERR_X509_INVALID_SERIAL + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRL ASN1 (TBSCertList, good entry then empty entry at end)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA224
x509parse_crl:"304b3049020100300d06092a864886f70d01010e0500300f310d300b0603550403130441424344170c303930313031303030303030301630128202abcd170c3038313233313233353935393000":"":MBEDTLS_ERR_X509_INVALID_SERIAL + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRL ASN1 (TBSCertList, missing time in entry)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA224
x509parse_crl:"304e3039020100300d06092a864886f70d01010e0500300f310d300b0603550403130441424344170c303930313031303030303030300630048202abcd300d06092a864886f70d01010e050003020001":"":MBEDTLS_ERR_X509_INVALID_DATE + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRL ASN1 (TBSCertList, missing time in entry at end)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA224
x509parse_crl:"303b3039020100300d06092a864886f70d01010e0500300f310d300b0603550403130441424344170c303930313031303030303030300630048202abcd":"":MBEDTLS_ERR_X509_INVALID_DATE + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRL ASN1 (TBSCertList, invalid tag for time in entry)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA224
x509parse_crl:"305c3047020100300d06092a864886f70d01010e0500300f310d300b0603550403130441424344170c303930313031303030303030301430128202abcd190c303831323331323335393539300d06092a864886f70d01010e050003020001":"":MBEDTLS_ERR_X509_INVALID_DATE + MBEDTLS_ERR_ASN1_UNEXPECTED_TAG

X509 CRL ASN1 (TBSCertList, invalid tag for serial)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA224
x509parse_crl:"305c3047020100300d06092a864886f70d01010e0500300f310d300b0603550403130441424344170c303930313031303030303030301430128402abcd170c303831323331323335393539300d06092a864886f70d01010e050003020001":"":MBEDTLS_ERR_X509_INVALID_SERIAL + MBEDTLS_ERR_ASN1_UNEXPECTED_TAG

X509 CRL ASN1 (TBSCertList, no entries)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA224:!MBEDTLS_X509_REMOVE_INFO
x509parse_crl:"30463031020100300d06092a864886f70d01010e0500300f310d300b0603550403130441424344170c303930313031303030303030300d06092a864886f70d01010e050003020001":"CRL version   \: 1\nissuer name   \: CN=ABCD\nthis update   \: 2009-01-01 00\:00\:00\nnext update   \: 0000-00-00 00\:00\:00\nRevoked certificates\:\nsigned using  \: RSA with SHA-224\n":0

X509 CRL ASN1 (invalid version 2)
x509parse_crl:"30463031020102300d06092a864886f70d01010e0500300f310d300b0603550403130441424344170c303930313031303030303030300d06092a864886f70d01010e050003020001":"":MBEDTLS_ERR_X509_UNKNOWN_VERSION

X509 CRL ASN1 (invalid version overflow)
x509parse_crl:"3049303102047fffffff300d06092a864886f70d01010e0500300f310d300b0603550403130441424344170c303930313031303030303030300d06092a864886f70d01010e050003020001":"":MBEDTLS_ERR_X509_UNKNOWN_VERSION

X509 CRL ASN1 (extension seq too long, crl-idp.pem byte 121)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crl:"308201b330819c020101300d06092a864886f70d01010b0500303b310b3009060355040613024e4c3111300f060355040a1308506f6c617253534c3119301706035504031310506f6c617253534c2054657374204341170d3138303331343037333134385a170d3238303331343037333134385aa02d302b30300603551d1c0101ff041f301da01ba0198617687474703a2f2f706b692e6578616d706c652e636f6d2f300d06092a864886f70d01010b05000382010100b3fbe9d586eaf4b8ff60cf8edae06a85135db78f78198498719725b5b403c0b803c2c150f52faae7306d6a7871885dc2e9dc83a164bac7263776474ef642b660040b35a1410ac291ac8f6f18ab85e7fd6e22bd1af1c41ca95cf2448f6e2b42a018493dfc03c6b6aa1b9e3fe7b76af2182fb2121db4166bf0167d6f379c5a58adee5082423434d97be2909f5e7488053f996646db10dd49782626da53ad8eada01813c031b2bacdb0203bc017aac1735951a11d013ee4d1d5f7143ccbebf2371e66a1bec6e1febe69148f50784eef8adbb66664c96196d7e0c0bcdc807f447b54e058f37642a3337995bfbcd332208bd6016936705c82263eabd7affdba92fae3":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRL ASN1 (extension oid too long, crl-idp.pem byte 123)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crl:"308201b330819c020101300d06092a864886f70d01010b0500303b310b3009060355040613024e4c3111300f060355040a1308506f6c617253534c3119301706035504031310506f6c617253534c2054657374204341170d3138303331343037333134385a170d3238303331343037333134385aa02d302b30290628551d1c0101ff041f301da01ba0198617687474703a2f2f706b692e6578616d706c652e636f6d2f300d06092a864886f70d01010b05000382010100b3fbe9d586eaf4b8ff60cf8edae06a85135db78f78198498719725b5b403c0b803c2c150f52faae7306d6a7871885dc2e9dc83a164bac7263776474ef642b660040b35a1410ac291ac8f6f18ab85e7fd6e22bd1af1c41ca95cf2448f6e2b42a018493dfc03c6b6aa1b9e3fe7b76af2182fb2121db4166bf0167d6f379c5a58adee5082423434d97be2909f5e7488053f996646db10dd49782626da53ad8eada01813c031b2bacdb0203bc017aac1735951a11d013ee4d1d5f7143ccbebf2371e66a1bec6e1febe69148f50784eef8adbb66664c96196d7e0c0bcdc807f447b54e058f37642a3337995bfbcd332208bd6016936705c82263eabd7affdba92fae3":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRL ASN1 (extension critical invalid length, crl-idp.pem byte 128)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crl:"308201b330819c020101300d06092a864886f70d01010b0500303b310b3009060355040613024e4c3111300f060355040a1308506f6c617253534c3119301706035504031310506f6c617253534c2054657374204341170d3138303331343037333134385a170d3238303331343037333134385aa02d302b30290603551d1c0102ff041f301da01ba0198617687474703a2f2f706b692e6578616d706c652e636f6d2f300d06092a864886f70d01010b05000382010100b3fbe9d586eaf4b8ff60cf8edae06a85135db78f78198498719725b5b403c0b803c2c150f52faae7306d6a7871885dc2e9dc83a164bac7263776474ef642b660040b35a1410ac291ac8f6f18ab85e7fd6e22bd1af1c41ca95cf2448f6e2b42a018493dfc03c6b6aa1b9e3fe7b76af2182fb2121db4166bf0167d6f379c5a58adee5082423434d97be2909f5e7488053f996646db10dd49782626da53ad8eada01813c031b2bacdb0203bc017aac1735951a11d013ee4d1d5f7143ccbebf2371e66a1bec6e1febe69148f50784eef8adbb66664c96196d7e0c0bcdc807f447b54e058f37642a3337995bfbcd332208bd6016936705c82263eabd7affdba92fae3":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS + MBEDTLS_ERR_ASN1_INVALID_LENGTH

X509 CRL ASN1 (extension data too long, crl-idp.pem byte 131)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crl:"308201b330819c020101300d06092a864886f70d01010b0500303b310b3009060355040613024e4c3111300f060355040a1308506f6c617253534c3119301706035504031310506f6c617253534c2054657374204341170d3138303331343037333134385a170d3238303331343037333134385aa02d302b30290603551d1c0101ff0420301da01ba0198617687474703a2f2f706b692e6578616d706c652e636f6d2f300d06092a864886f70d01010b05000382010100b3fbe9d586eaf4b8ff60cf8edae06a85135db78f78198498719725b5b403c0b803c2c150f52faae7306d6a7871885dc2e9dc83a164bac7263776474ef642b660040b35a1410ac291ac8f6f18ab85e7fd6e22bd1af1c41ca95cf2448f6e2b42a018493dfc03c6b6aa1b9e3fe7b76af2182fb2121db4166bf0167d6f379c5a58adee5082423434d97be2909f5e7488053f996646db10dd49782626da53ad8eada01813c031b2bacdb0203bc017aac1735951a11d013ee4d1d5f7143ccbebf2371e66a1bec6e1febe69148f50784eef8adbb66664c96196d7e0c0bcdc807f447b54e058f37642a3337995bfbcd332208bd6016936705c82263eabd7affdba92fae3":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CRL ASN1 (extension data too short, crl-idp.pem byte 131)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509parse_crl:"308201b330819c020101300d06092a864886f70d01010b0500303b310b3009060355040613024e4c3111300f060355040a1308506f6c617253534c3119301706035504031310506f6c617253534c2054657374204341170d3138303331343037333134385a170d3238303331343037333134385aa02d302b30290603551d1c0101ff041e301da01ba0198617687474703a2f2f706b692e6578616d706c652e636f6d2f300d06092a864886f70d01010b05000382010100b3fbe9d586eaf4b8ff60cf8edae06a85135db78f78198498719725b5b403c0b803c2c150f52faae7306d6a7871885dc2e9dc83a164bac7263776474ef642b660040b35a1410ac291ac8f6f18ab85e7fd6e22bd1af1c41ca95cf2448f6e2b42a018493dfc03c6b6aa1b9e3fe7b76af2182fb2121db4166bf0167d6f379c5a58adee5082423434d97be2909f5e7488053f996646db10dd49782626da53ad8eada01813c031b2bacdb0203bc017aac1735951a11d013ee4d1d5f7143ccbebf2371e66a1bec6e1febe69148f50784eef8adbb66664c96196d7e0c0bcdc807f447b54e058f37642a3337995bfbcd332208bd6016936705c82263eabd7affdba92fae3":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS + MBEDTLS_ERR_ASN1_LENGTH_MISMATCH

X509 CRL ASN1 (extension not critical explicit, crl-idp.pem byte 129)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256:!MBEDTLS_X509_REMOVE_INFO
x509parse_crl:"308201b330819c020101300d06092a864886f70d01010b0500303b310b3009060355040613024e4c3111300f060355040a1308506f6c617253534c3119301706035504031310506f6c617253534c2054657374204341170d3138303331343037333134385a170d3238303331343037333134385aa02d302b30290603551d1c010100041f301da01ba0198617687474703a2f2f706b692e6578616d706c652e636f6d2f300d06092a864886f70d01010b05000382010100b3fbe9d586eaf4b8ff60cf8edae06a85135db78f78198498719725b5b403c0b803c2c150f52faae7306d6a7871885dc2e9dc83a164bac7263776474ef642b660040b35a1410ac291ac8f6f18ab85e7fd6e22bd1af1c41ca95cf2448f6e2b42a018493dfc03c6b6aa1b9e3fe7b76af2182fb2121db4166bf0167d6f379c5a58adee5082423434d97be2909f5e7488053f996646db10dd49782626da53ad8eada01813c031b2bacdb0203bc017aac1735951a11d013ee4d1d5f7143ccbebf2371e66a1bec6e1febe69148f50784eef8adbb66664c96196d7e0c0bcdc807f447b54e058f37642a3337995bfbcd332208bd6016936705c82263eabd7affdba92fae3":"CRL version   \: 2\nissuer name   \: C=NL, O=PolarSSL, CN=PolarSSL Test CA\nthis update   \: 2018-03-14 07\:31\:48\nnext update   \: 2028-03-14 07\:31\:48\nRevoked certificates\:\nsigned using  \: RSA with SHA-256\n":0

X509 CRT parse file dir3/Readme
mbedtls_x509_crt_parse_file:"data_files/dir3/Readme":MBEDTLS_ERR_X509_INVALID_FORMAT:0

X509 CRT parse file dir3/test-ca.crt
depends_on:MBEDTLS_MD_CAN_SHA1:MBEDTLS_RSA_C
mbedtls_x509_crt_parse_file:"data_files/dir3/test-ca.crt":0:1

X509 CRT parse file dir3/test-ca2.crt
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_PK_CAN_ECDSA_SOME:MBEDTLS_ECP_HAVE_SECP384R1
mbedtls_x509_crt_parse_file:"data_files/dir3/test-ca2.crt":0:1

# The parse_path tests are known to fail when compiled for a 32-bit architecture
# and run via qemu-user on Linux on a 64-bit host. This is due to a known
# bug in Qemu: https://gitlab.com/qemu-project/qemu/-/issues/263
X509 CRT parse path #1 (one cert)
depends_on:MBEDTLS_MD_CAN_SHA1:MBEDTLS_RSA_C
mbedtls_x509_crt_parse_path:"data_files/dir1":0:1

X509 CRT parse path #2 (two certs)
depends_on:MBEDTLS_MD_CAN_SHA1:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256:MBEDTLS_PK_CAN_ECDSA_SOME:MBEDTLS_ECP_HAVE_SECP384R1
mbedtls_x509_crt_parse_path:"data_files/dir2":0:2

X509 CRT parse path #3 (two certs, one non-cert)
depends_on:MBEDTLS_MD_CAN_SHA1:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256:MBEDTLS_PK_CAN_ECDSA_SOME:MBEDTLS_ECP_HAVE_SECP384R1
mbedtls_x509_crt_parse_path:"data_files/dir3":1:2

X509 CRT verify long chain (max intermediate CA, trusted)
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_PK_CAN_ECDSA_SOME:MBEDTLS_ECP_HAVE_SECP256R1
mbedtls_x509_crt_verify_max:"data_files/dir-maxpath/00.crt":"data_files/dir-maxpath":MBEDTLS_X509_MAX_INTERMEDIATE_CA:0:0

X509 CRT verify long chain (max intermediate CA, untrusted)
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_PK_CAN_ECDSA_SOME:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_ECP_HAVE_SECP384R1
mbedtls_x509_crt_verify_max:"data_files/test-ca2.crt":"data_files/dir-maxpath":MBEDTLS_X509_MAX_INTERMEDIATE_CA-1:MBEDTLS_ERR_X509_CERT_VERIFY_FAILED:MBEDTLS_X509_BADCERT_NOT_TRUSTED

X509 CRT verify long chain (max intermediate CA + 1)
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_PK_CAN_ECDSA_SOME:MBEDTLS_ECP_HAVE_SECP256R1
mbedtls_x509_crt_verify_max:"data_files/dir-maxpath/00.crt":"data_files/dir-maxpath":MBEDTLS_X509_MAX_INTERMEDIATE_CA+1:MBEDTLS_ERR_X509_FATAL_ERROR:-1

X509 CRT verify chain #1 (zero pathlen intermediate)
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_RSA_C
mbedtls_x509_crt_verify_chain:"data_files/dir4/cert14.crt data_files/dir4/cert13.crt data_files/dir4/cert12.crt":"data_files/dir4/cert11.crt":MBEDTLS_X509_BADCERT_NOT_TRUSTED:MBEDTLS_ERR_X509_CERT_VERIFY_FAILED:"":0

X509 CRT verify chain #2 (zero pathlen root)
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_RSA_C
mbedtls_x509_crt_verify_chain:"data_files/dir4/cert23.crt data_files/dir4/cert22.crt":"data_files/dir4/cert21.crt":MBEDTLS_X509_BADCERT_NOT_TRUSTED:MBEDTLS_ERR_X509_CERT_VERIFY_FAILED:"":0

X509 CRT verify chain #3 (nonzero pathlen root)
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_RSA_C
mbedtls_x509_crt_verify_chain:"data_files/dir4/cert34.crt data_files/dir4/cert33.crt data_files/dir4/cert32.crt":"data_files/dir4/cert31.crt":MBEDTLS_X509_BADCERT_NOT_TRUSTED:MBEDTLS_ERR_X509_CERT_VERIFY_FAILED:"":0

X509 CRT verify chain #4 (nonzero pathlen intermediate)
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_RSA_C
mbedtls_x509_crt_verify_chain:"data_files/dir4/cert45.crt data_files/dir4/cert44.crt data_files/dir4/cert43.crt data_files/dir4/cert42.crt":"data_files/dir4/cert41.crt":MBEDTLS_X509_BADCERT_NOT_TRUSTED:MBEDTLS_ERR_X509_CERT_VERIFY_FAILED:"":0

X509 CRT verify chain #5 (nonzero maxpathlen intermediate)
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_RSA_C:MBEDTLS_PKCS1_V15
mbedtls_x509_crt_verify_chain:"data_files/dir4/cert54.crt data_files/dir4/cert53.crt data_files/dir4/cert52.crt":"data_files/dir4/cert51.crt":0:0:"":0

X509 CRT verify chain #6 (nonzero maxpathlen root)
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_RSA_C:MBEDTLS_PKCS1_V15
mbedtls_x509_crt_verify_chain:"data_files/dir4/cert63.crt data_files/dir4/cert62.crt":"data_files/dir4/cert61.crt":0:0:"":0

X509 CRT verify chain #7 (maxpathlen root, self signed in path)
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_RSA_C:MBEDTLS_PKCS1_V15
mbedtls_x509_crt_verify_chain:"data_files/dir4/cert74.crt data_files/dir4/cert73.crt data_files/dir4/cert72.crt":"data_files/dir4/cert71.crt":0:0:"":0

X509 CRT verify chain #8 (self signed maxpathlen root)
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_RSA_C:MBEDTLS_PKCS1_V15
mbedtls_x509_crt_verify_chain:"data_files/dir4/cert61.crt data_files/dir4/cert63.crt data_files/dir4/cert62.crt":"data_files/dir4/cert61.crt":0:0:"":0

X509 CRT verify chain #9 (zero pathlen first intermediate, valid)
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_PK_CAN_ECDSA_VERIFY:MBEDTLS_ECP_HAVE_SECP256R1
mbedtls_x509_crt_verify_chain:"data_files/dir4/cert83.crt data_files/dir4/cert82.crt":"data_files/dir4/cert81.crt":0:0:"":0

X509 CRT verify chain #10 (zero pathlen root, valid)
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_PK_CAN_ECDSA_VERIFY:MBEDTLS_ECP_HAVE_SECP256R1
mbedtls_x509_crt_verify_chain:"data_files/dir4/cert92.crt":"data_files/dir4/cert91.crt":0:0:"":0

X509 CRT verify chain #11 (valid chain, missing profile)
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_PK_CAN_ECDSA_VERIFY:MBEDTLS_ECP_HAVE_SECP256R1
mbedtls_x509_crt_verify_chain:"data_files/dir4/cert92.crt":"data_files/dir4/cert91.crt":-1:MBEDTLS_ERR_X509_BAD_INPUT_DATA:"nonesuch":0

X509 CRT verify chain #12 (suiteb profile, RSA root)
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_RSA_C:MBEDTLS_PKCS1_V15:MBEDTLS_PK_CAN_ECDSA_VERIFY:MBEDTLS_ECP_HAVE_SECP192R1:MBEDTLS_MD_CAN_SHA1
mbedtls_x509_crt_verify_chain:"data_files/server3.crt":"data_files/test-ca.crt":MBEDTLS_X509_BADCERT_BAD_MD|MBEDTLS_X509_BADCERT_BAD_PK|MBEDTLS_X509_BADCERT_BAD_KEY:MBEDTLS_ERR_X509_CERT_VERIFY_FAILED:"suiteb":0

X509 CRT verify chain #13 (RSA only profile, EC root)
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_RSA_C:MBEDTLS_PK_CAN_ECDSA_VERIFY:MBEDTLS_ECP_HAVE_SECP384R1
mbedtls_x509_crt_verify_chain:"data_files/server4.crt":"data_files/test-ca2.crt":MBEDTLS_X509_BADCERT_BAD_PK|MBEDTLS_X509_BADCERT_BAD_KEY:MBEDTLS_ERR_X509_CERT_VERIFY_FAILED:"rsa3072":0

X509 CRT verify chain #13 (RSA only profile, EC trusted EE)
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_RSA_C:MBEDTLS_PK_CAN_ECDSA_VERIFY:MBEDTLS_ECP_HAVE_SECP256R1
mbedtls_x509_crt_verify_chain:"data_files/server5-selfsigned.crt":"data_files/server5-selfsigned.crt":MBEDTLS_X509_BADCERT_BAD_PK|MBEDTLS_X509_BADCERT_BAD_KEY:MBEDTLS_ERR_X509_CERT_VERIFY_FAILED:"rsa3072":0

X509 CRT verify chain #14 (RSA-3072 profile, root key too small)
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_RSA_C:MBEDTLS_PKCS1_V15:MBEDTLS_MD_CAN_SHA1
mbedtls_x509_crt_verify_chain:"data_files/server1.crt":"data_files/test-ca.crt":MBEDTLS_X509_BADCERT_BAD_MD|MBEDTLS_X509_BADCERT_BAD_KEY:MBEDTLS_ERR_X509_CERT_VERIFY_FAILED:"rsa3072":0

X509 CRT verify chain #15 (suiteb profile, rsa intermediate)
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_RSA_C:MBEDTLS_PKCS1_V15:MBEDTLS_PK_CAN_ECDSA_VERIFY:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_ECP_HAVE_SECP256R1
mbedtls_x509_crt_verify_chain:"data_files/server7.crt data_files/test-int-ca.crt":"data_files/test-ca2.crt":MBEDTLS_X509_BADCERT_BAD_PK:MBEDTLS_ERR_X509_CERT_VERIFY_FAILED:"suiteb":0

X509 CRT verify chain #16 (RSA-only profile, EC intermediate)
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_RSA_C:MBEDTLS_PKCS1_V15:MBEDTLS_PK_CAN_ECDSA_VERIFY:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_MD_CAN_SHA1
mbedtls_x509_crt_verify_chain:"data_files/server8.crt data_files/test-int-ca2.crt":"data_files/test-ca.crt":MBEDTLS_X509_BADCERT_BAD_PK|MBEDTLS_X509_BADCERT_BAD_KEY:MBEDTLS_ERR_X509_CERT_VERIFY_FAILED:"rsa3072":0

X509 CRT verify chain #17 (SHA-512 profile)
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_RSA_C:MBEDTLS_PKCS1_V15:MBEDTLS_PK_CAN_ECDSA_VERIFY:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_ECP_HAVE_SECP384R1
mbedtls_x509_crt_verify_chain:"data_files/server7.crt data_files/test-int-ca.crt":"data_files/test-ca2.crt":MBEDTLS_X509_BADCERT_BAD_MD:MBEDTLS_ERR_X509_CERT_VERIFY_FAILED:"sha512":0

X509 CRT verify chain #18 (len=1, vrfy fatal on depth 1)
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_PK_CAN_ECDSA_VERIFY:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_MD_CAN_SHA512
mbedtls_x509_crt_verify_chain:"data_files/server5.crt":"data_files/test-ca2.crt":-1:-2:"":2

X509 CRT verify chain #19 (len=0, vrfy fatal on depth 0)
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_PK_CAN_ECDSA_VERIFY:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_MD_CAN_SHA512
mbedtls_x509_crt_verify_chain:"data_files/server5.crt":"data_files/test-ca2.crt":-1:-1:"":1

X509 CRT verify chain #20 (len=1, vrfy fatal on depth 0)
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_PK_CAN_ECDSA_VERIFY:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_MD_CAN_SHA512:MBEDTLS_MD_CAN_SHA1:MBEDTLS_RSA_C
mbedtls_x509_crt_verify_chain:"data_files/server5.crt":"data_files/test-ca.crt":-1:-1:"":1

X509 CRT verify chain #21 (len=3, vrfy fatal on depth 3)
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_PK_CAN_ECDSA_VERIFY:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_RSA_C:MBEDTLS_PKCS1_V15:MBEDTLS_MD_CAN_SHA1:MBEDTLS_ECP_HAVE_SECP384R1
mbedtls_x509_crt_verify_chain:"data_files/server10_int3_int-ca2_ca.crt":"data_files/test-ca.crt":-1:-4:"":8

X509 CRT verify chain #22 (len=3, vrfy fatal on depth 2)
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_PK_CAN_ECDSA_VERIFY:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA1:MBEDTLS_ECP_HAVE_SECP384R1
mbedtls_x509_crt_verify_chain:"data_files/server10_int3_int-ca2_ca.crt":"data_files/test-ca.crt":-1:-3:"":4

X509 CRT verify chain #23 (len=3, vrfy fatal on depth 1)
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_PK_CAN_ECDSA_VERIFY:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA1:MBEDTLS_ECP_HAVE_SECP384R1
mbedtls_x509_crt_verify_chain:"data_files/server10_int3_int-ca2_ca.crt":"data_files/test-ca.crt":-1:-2:"":2

X509 CRT verify chain #24 (len=3, vrfy fatal on depth 0)
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_PK_CAN_ECDSA_VERIFY:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA1:MBEDTLS_ECP_HAVE_SECP384R1
mbedtls_x509_crt_verify_chain:"data_files/server10_int3_int-ca2_ca.crt":"data_files/test-ca.crt":-1:-1:"":1

X509 CRT verify chain #25 (len=3, vrfy fatal on depth 3, untrusted)
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_PK_CAN_ECDSA_VERIFY:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA1:MBEDTLS_ECP_HAVE_SECP384R1
mbedtls_x509_crt_verify_chain:"data_files/server10_int3_int-ca2_ca.crt":"data_files/test-ca2.crt":-1:-4:"":8

X509 OID description #1
x509_oid_desc:"2b06010505070301":"TLS Web Server Authentication"

X509 OID description #2
x509_oid_desc:"2b0601050507030f":"notfound"

X509 OID description #3
x509_oid_desc:"2b0601050507030100":"notfound"

X509 OID numstring #1 (wide buffer)
x509_oid_numstr:"2b06010505070301":"*******.*******.1":20:17

X509 OID numstring #2 (buffer just fits)
x509_oid_numstr:"2b06010505070301":"*******.*******.1":18:17

X509 OID numstring #3 (buffer too small)
x509_oid_numstr:"2b06010505070301":"*******.*******.1":17:MBEDTLS_ERR_OID_BUF_TOO_SMALL

X509 OID numstring #4 (larger number)
x509_oid_numstr:"2a864886f70d":"1.2.840.113549":15:14

X509 OID numstring #5 (arithmetic overflow)
x509_oid_numstr:"2a8648f9f8f7f6f5f4f3f2f1f001":"":100:MBEDTLS_ERR_ASN1_INVALID_DATA

X509 CRT keyUsage #1 (no extension, expected KU)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA1
x509_check_key_usage:"data_files/server1.crt":MBEDTLS_X509_KU_DIGITAL_SIGNATURE|MBEDTLS_X509_KU_KEY_ENCIPHERMENT:0

X509 CRT keyUsage #2 (no extension, surprising KU)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA1
x509_check_key_usage:"data_files/server1.crt":MBEDTLS_X509_KU_KEY_CERT_SIGN:0

X509 CRT keyUsage #3 (extension present, no KU)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA1
x509_check_key_usage:"data_files/server1.key_usage.crt":0:0

X509 CRT keyUsage #4 (extension present, single KU present)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA1
x509_check_key_usage:"data_files/server1.key_usage.crt":MBEDTLS_X509_KU_DIGITAL_SIGNATURE:0

X509 CRT keyUsage #5 (extension present, single KU absent)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA1
x509_check_key_usage:"data_files/server1.key_usage.crt":MBEDTLS_X509_KU_KEY_CERT_SIGN:MBEDTLS_ERR_X509_BAD_INPUT_DATA

X509 CRT keyUsage #6 (extension present, combined KU present)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA1
x509_check_key_usage:"data_files/server1.key_usage.crt":MBEDTLS_X509_KU_DIGITAL_SIGNATURE|MBEDTLS_X509_KU_KEY_ENCIPHERMENT:0

X509 CRT keyUsage #7 (extension present, combined KU both absent)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA1
x509_check_key_usage:"data_files/server1.key_usage.crt":MBEDTLS_X509_KU_KEY_CERT_SIGN|MBEDTLS_X509_KU_CRL_SIGN:MBEDTLS_ERR_X509_BAD_INPUT_DATA

X509 CRT keyUsage #8 (extension present, combined KU one absent)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA1
x509_check_key_usage:"data_files/server1.key_usage.crt":MBEDTLS_X509_KU_KEY_ENCIPHERMENT|MBEDTLS_X509_KU_KEY_AGREEMENT:MBEDTLS_ERR_X509_BAD_INPUT_DATA

X509 CRT keyUsage #9 (extension present, decOnly allowed absent)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA1
x509_check_key_usage:"data_files/server1.key_usage.crt":MBEDTLS_X509_KU_DIGITAL_SIGNATURE|MBEDTLS_X509_KU_KEY_ENCIPHERMENT|MBEDTLS_X509_KU_DECIPHER_ONLY:0

X509 CRT keyUsage #10 (extension present, decOnly non-allowed present)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA1
x509_check_key_usage:"data_files/keyUsage.decipherOnly.crt":MBEDTLS_X509_KU_DIGITAL_SIGNATURE|MBEDTLS_X509_KU_KEY_ENCIPHERMENT:MBEDTLS_ERR_X509_BAD_INPUT_DATA

X509 CRT keyUsage #11 (extension present, decOnly allowed present)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA1
x509_check_key_usage:"data_files/keyUsage.decipherOnly.crt":MBEDTLS_X509_KU_DIGITAL_SIGNATURE|MBEDTLS_X509_KU_KEY_ENCIPHERMENT|MBEDTLS_X509_KU_DECIPHER_ONLY:0

X509 CRT extendedKeyUsage #1 (no extension, serverAuth)
depends_on:MBEDTLS_PK_CAN_ECDSA_VERIFY:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_MD_CAN_SHA256
x509_check_extended_key_usage:"data_files/server5.crt":"2b06010505070301":0

X509 CRT extendedKeyUsage #2 (single value, present)
depends_on:MBEDTLS_PK_CAN_ECDSA_VERIFY:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_MD_CAN_SHA256
x509_check_extended_key_usage:"data_files/server5.eku-srv.crt":"2b06010505070301":0

X509 CRT extendedKeyUsage #3 (single value, absent)
depends_on:MBEDTLS_PK_CAN_ECDSA_VERIFY:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_MD_CAN_SHA256
x509_check_extended_key_usage:"data_files/server5.eku-cli.crt":"2b06010505070301":MBEDTLS_ERR_X509_BAD_INPUT_DATA

X509 CRT extendedKeyUsage #4 (two values, first)
depends_on:MBEDTLS_PK_CAN_ECDSA_VERIFY:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_MD_CAN_SHA256
x509_check_extended_key_usage:"data_files/server5.eku-srv_cli.crt":"2b06010505070301":0

X509 CRT extendedKeyUsage #5 (two values, second)
depends_on:MBEDTLS_PK_CAN_ECDSA_VERIFY:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_MD_CAN_SHA256
x509_check_extended_key_usage:"data_files/server5.eku-srv_cli.crt":"2b06010505070302":0

X509 CRT extendedKeyUsage #6 (two values, other)
depends_on:MBEDTLS_PK_CAN_ECDSA_VERIFY:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_MD_CAN_SHA256
x509_check_extended_key_usage:"data_files/server5.eku-srv_cli.crt":"2b06010505070303":MBEDTLS_ERR_X509_BAD_INPUT_DATA

X509 CRT extendedKeyUsage #7 (any, random)
depends_on:MBEDTLS_PK_CAN_ECDSA_VERIFY:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_MD_CAN_SHA256
x509_check_extended_key_usage:"data_files/server5.eku-cs_any.crt":"2b060105050703ff":0

X509 RSASSA-PSS parameters ASN1 (good, all defaults)
x509_parse_rsassa_pss_params:"":MBEDTLS_ASN1_CONSTRUCTED | MBEDTLS_ASN1_SEQUENCE:MBEDTLS_MD_SHA1:MBEDTLS_MD_SHA1:20:0

X509 RSASSA-PSS parameters ASN1 (wrong initial tag)
x509_parse_rsassa_pss_params:"":MBEDTLS_ASN1_SEQUENCE:MBEDTLS_MD_SHA1:MBEDTLS_MD_SHA1:20:MBEDTLS_ERR_X509_INVALID_ALG + MBEDTLS_ERR_ASN1_UNEXPECTED_TAG

X509 RSASSA-PSS parameters ASN1 (unknown tag in top-level sequence)
x509_parse_rsassa_pss_params:"a400":MBEDTLS_ASN1_CONSTRUCTED | MBEDTLS_ASN1_SEQUENCE:MBEDTLS_MD_SHA1:MBEDTLS_MD_SHA1:20:MBEDTLS_ERR_X509_INVALID_ALG + MBEDTLS_ERR_ASN1_LENGTH_MISMATCH

X509 RSASSA-PSS parameters ASN1 (good, HashAlg SHA256)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509_parse_rsassa_pss_params:"a00d300b0609608648016503040201":MBEDTLS_ASN1_CONSTRUCTED | MBEDTLS_ASN1_SEQUENCE:MBEDTLS_MD_SHA256:MBEDTLS_MD_SHA1:20:0

X509 RSASSA-PSS parameters ASN1 (good, explicit HashAlg = default)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA1
x509_parse_rsassa_pss_params:"a009300706052b0e03021a":MBEDTLS_ASN1_CONSTRUCTED | MBEDTLS_ASN1_SEQUENCE:MBEDTLS_MD_SHA1:MBEDTLS_MD_SHA1:20:0

X509 RSASSA-PSS parameters ASN1 (HashAlg wrong len #1)
x509_parse_rsassa_pss_params:"a00a300706052b0e03021a":MBEDTLS_ASN1_CONSTRUCTED | MBEDTLS_ASN1_SEQUENCE:MBEDTLS_MD_SHA1:MBEDTLS_MD_SHA1:20:MBEDTLS_ERR_X509_INVALID_ALG + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 RSASSA-PSS parameters ASN1 (HashAlg wrong len #2)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA1
x509_parse_rsassa_pss_params:"a00a300706052b0e03021a00":MBEDTLS_ASN1_CONSTRUCTED | MBEDTLS_ASN1_SEQUENCE:MBEDTLS_MD_SHA1:MBEDTLS_MD_SHA1:20:MBEDTLS_ERR_X509_INVALID_ALG + MBEDTLS_ERR_ASN1_LENGTH_MISMATCH

X509 RSASSA-PSS parameters ASN1 (HashAlg with parameters)
x509_parse_rsassa_pss_params:"a00f300d06096086480165030402013000":MBEDTLS_ASN1_CONSTRUCTED | MBEDTLS_ASN1_SEQUENCE:MBEDTLS_MD_SHA256:MBEDTLS_MD_SHA1:20:MBEDTLS_ERR_X509_INVALID_ALG + MBEDTLS_ERR_ASN1_INVALID_DATA

X509 RSASSA-PSS parameters ASN1 (HashAlg unknown OID)
x509_parse_rsassa_pss_params:"a00d300b06096086480165030402ff":MBEDTLS_ASN1_CONSTRUCTED | MBEDTLS_ASN1_SEQUENCE:MBEDTLS_MD_SHA256:MBEDTLS_MD_SHA1:20:MBEDTLS_ERR_X509_INVALID_ALG + MBEDTLS_ERR_OID_NOT_FOUND

X509 RSASSA-PSS parameters ASN1 (good, MGAlg = MGF1-SHA256)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509_parse_rsassa_pss_params:"a11a301806092a864886f70d010108300b0609608648016503040201":MBEDTLS_ASN1_CONSTRUCTED | MBEDTLS_ASN1_SEQUENCE:MBEDTLS_MD_SHA1:MBEDTLS_MD_SHA256:20:0

X509 RSASSA-PSS parameters ASN1 (good, explicit MGAlg = default)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA1
x509_parse_rsassa_pss_params:"a116301406092a864886f70d010108300706052b0e03021a":MBEDTLS_ASN1_CONSTRUCTED | MBEDTLS_ASN1_SEQUENCE:MBEDTLS_MD_SHA1:MBEDTLS_MD_SHA1:20:0

X509 RSASSA-PSS parameters ASN1 (MGAlg wrong len #1)
x509_parse_rsassa_pss_params:"a11b301806092a864886f70d010108300b0609608648016503040201":MBEDTLS_ASN1_CONSTRUCTED | MBEDTLS_ASN1_SEQUENCE:MBEDTLS_MD_SHA1:MBEDTLS_MD_SHA256:20:MBEDTLS_ERR_X509_INVALID_ALG + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 RSASSA-PSS parameters ASN1 (MGAlg wrong len #2)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509_parse_rsassa_pss_params:"a11b301806092a864886f70d010108300b060960864801650304020100":MBEDTLS_ASN1_CONSTRUCTED | MBEDTLS_ASN1_SEQUENCE:MBEDTLS_MD_SHA1:MBEDTLS_MD_SHA256:20:MBEDTLS_ERR_X509_INVALID_ALG + MBEDTLS_ERR_ASN1_LENGTH_MISMATCH

X509 RSASSA-PSS parameters ASN1 (MGAlg AlgId wrong len #1)
x509_parse_rsassa_pss_params:"a11a301906092a864886f70d010108300b0609608648016503040201":MBEDTLS_ASN1_CONSTRUCTED | MBEDTLS_ASN1_SEQUENCE:MBEDTLS_MD_SHA1:MBEDTLS_MD_SHA256:20:MBEDTLS_ERR_X509_INVALID_ALG + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 RSASSA-PSS parameters ASN1 (MGAlg OID != MGF1)
x509_parse_rsassa_pss_params:"a11a301806092a864886f70d010109300b0609608648016503040201":MBEDTLS_ASN1_CONSTRUCTED | MBEDTLS_ASN1_SEQUENCE:MBEDTLS_MD_SHA1:MBEDTLS_MD_SHA256:20:MBEDTLS_ERR_X509_FEATURE_UNAVAILABLE + MBEDTLS_ERR_OID_NOT_FOUND

X509 RSASSA-PSS parameters ASN1 (MGAlg.params wrong tag)
x509_parse_rsassa_pss_params:"a11a301806092a864886f70d010108310b0609608648016503040201":MBEDTLS_ASN1_CONSTRUCTED | MBEDTLS_ASN1_SEQUENCE:MBEDTLS_MD_SHA1:MBEDTLS_MD_SHA256:20:MBEDTLS_ERR_X509_INVALID_ALG + MBEDTLS_ERR_ASN1_UNEXPECTED_TAG

X509 RSASSA-PSS parameters ASN1 (MGAlg.params wrong len #1a)
x509_parse_rsassa_pss_params:"a10f300d06092a864886f70d0101083000":MBEDTLS_ASN1_CONSTRUCTED | MBEDTLS_ASN1_SEQUENCE:MBEDTLS_MD_SHA1:MBEDTLS_MD_SHA256:20:MBEDTLS_ERR_X509_INVALID_ALG + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 RSASSA-PSS parameters ASN1 (MGAlg.params wrong len #1b)
x509_parse_rsassa_pss_params:"a11b301906092a864886f70d010108300c0609608648016503040201":MBEDTLS_ASN1_CONSTRUCTED | MBEDTLS_ASN1_SEQUENCE:MBEDTLS_MD_SHA1:MBEDTLS_MD_SHA256:20:MBEDTLS_ERR_X509_INVALID_ALG + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 RSASSA-PSS parameters ASN1 (MGAlg.params.alg not an OID)
x509_parse_rsassa_pss_params:"a11a301806092a864886f70d010108300b0709608648016503040201":MBEDTLS_ASN1_CONSTRUCTED | MBEDTLS_ASN1_SEQUENCE:MBEDTLS_MD_SHA1:MBEDTLS_MD_SHA256:20:MBEDTLS_ERR_X509_INVALID_ALG + MBEDTLS_ERR_ASN1_UNEXPECTED_TAG

X509 RSASSA-PSS parameters ASN1 (MGAlg.params.alg unknown OID)
x509_parse_rsassa_pss_params:"a11a301806092a864886f70d010108300b06096086480165030402ff":MBEDTLS_ASN1_CONSTRUCTED | MBEDTLS_ASN1_SEQUENCE:MBEDTLS_MD_SHA1:MBEDTLS_MD_SHA256:20:MBEDTLS_ERR_X509_INVALID_ALG + MBEDTLS_ERR_OID_NOT_FOUND

X509 RSASSA-PSS parameters ASN1 (MGAlg.params.params NULL)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509_parse_rsassa_pss_params:"a11c301a06092a864886f70d010108300d06096086480165030402010500":MBEDTLS_ASN1_CONSTRUCTED | MBEDTLS_ASN1_SEQUENCE:MBEDTLS_MD_SHA1:MBEDTLS_MD_SHA256:20:0

X509 RSASSA-PSS parameters ASN1 (MGAlg.params.params wrong tag)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509_parse_rsassa_pss_params:"a11c301a06092a864886f70d010108300d06096086480165030402013000":MBEDTLS_ASN1_CONSTRUCTED | MBEDTLS_ASN1_SEQUENCE:MBEDTLS_MD_SHA1:MBEDTLS_MD_SHA256:20:MBEDTLS_ERR_X509_INVALID_ALG + MBEDTLS_ERR_ASN1_UNEXPECTED_TAG

X509 RSASSA-PSS parameters ASN1 (MGAlg.params wrong len #1c)
x509_parse_rsassa_pss_params:"a11d301b06092a864886f70d010108300e06096086480165030402010500":MBEDTLS_ASN1_CONSTRUCTED | MBEDTLS_ASN1_SEQUENCE:MBEDTLS_MD_SHA1:MBEDTLS_MD_SHA256:20:MBEDTLS_ERR_X509_INVALID_ALG + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 RSASSA-PSS parameters ASN1 (MGAlg.params wrong len #2)
depends_on:MBEDTLS_RSA_C:MBEDTLS_MD_CAN_SHA256
x509_parse_rsassa_pss_params:"a11d301b06092a864886f70d010108300e0609608648016503040201050000":MBEDTLS_ASN1_CONSTRUCTED | MBEDTLS_ASN1_SEQUENCE:MBEDTLS_MD_SHA1:MBEDTLS_MD_SHA256:20:MBEDTLS_ERR_X509_INVALID_ALG + MBEDTLS_ERR_ASN1_LENGTH_MISMATCH

X509 RSASSA-PSS parameters ASN1 (good, saltLen = 94)
x509_parse_rsassa_pss_params:"a20302015e":MBEDTLS_ASN1_CONSTRUCTED | MBEDTLS_ASN1_SEQUENCE:MBEDTLS_MD_SHA1:MBEDTLS_MD_SHA1:94:0

X509 RSASSA-PSS parameters ASN1 (good, explicit saltLen = default)
x509_parse_rsassa_pss_params:"a203020114":MBEDTLS_ASN1_CONSTRUCTED | MBEDTLS_ASN1_SEQUENCE:MBEDTLS_MD_SHA1:MBEDTLS_MD_SHA1:20:0

X509 RSASSA-PSS parameters ASN1 (saltLen wrong len #1)
x509_parse_rsassa_pss_params:"a20402015e":MBEDTLS_ASN1_CONSTRUCTED | MBEDTLS_ASN1_SEQUENCE:MBEDTLS_MD_SHA1:MBEDTLS_MD_SHA1:94:MBEDTLS_ERR_X509_INVALID_ALG + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 RSASSA-PSS parameters ASN1 (saltLen wrong len #2)
x509_parse_rsassa_pss_params:"a20402015e00":MBEDTLS_ASN1_CONSTRUCTED | MBEDTLS_ASN1_SEQUENCE:MBEDTLS_MD_SHA1:MBEDTLS_MD_SHA1:94:MBEDTLS_ERR_X509_INVALID_ALG + MBEDTLS_ERR_ASN1_LENGTH_MISMATCH

X509 RSASSA-PSS parameters ASN1 (saltLen not an int)
x509_parse_rsassa_pss_params:"a2023000":MBEDTLS_ASN1_CONSTRUCTED | MBEDTLS_ASN1_SEQUENCE:MBEDTLS_MD_SHA1:MBEDTLS_MD_SHA1:94:MBEDTLS_ERR_X509_INVALID_ALG + MBEDTLS_ERR_ASN1_UNEXPECTED_TAG

X509 RSASSA-PSS parameters ASN1 (good, explicit trailerField = default)
x509_parse_rsassa_pss_params:"a303020101":MBEDTLS_ASN1_CONSTRUCTED | MBEDTLS_ASN1_SEQUENCE:MBEDTLS_MD_SHA1:MBEDTLS_MD_SHA1:20:0

X509 RSASSA-PSS parameters ASN1 (trailerField wrong len #1)
x509_parse_rsassa_pss_params:"a304020101":MBEDTLS_ASN1_CONSTRUCTED | MBEDTLS_ASN1_SEQUENCE:MBEDTLS_MD_SHA1:MBEDTLS_MD_SHA1:20:MBEDTLS_ERR_X509_INVALID_ALG + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 RSASSA-PSS parameters ASN1 (trailerField wrong len #2)
x509_parse_rsassa_pss_params:"a30402010100":MBEDTLS_ASN1_CONSTRUCTED | MBEDTLS_ASN1_SEQUENCE:MBEDTLS_MD_SHA1:MBEDTLS_MD_SHA1:20:MBEDTLS_ERR_X509_INVALID_ALG + MBEDTLS_ERR_ASN1_LENGTH_MISMATCH

X509 RSASSA-PSS parameters ASN1 (trailerField not an int)
x509_parse_rsassa_pss_params:"a3023000":MBEDTLS_ASN1_CONSTRUCTED | MBEDTLS_ASN1_SEQUENCE:MBEDTLS_MD_SHA1:MBEDTLS_MD_SHA1:20:MBEDTLS_ERR_X509_INVALID_ALG + MBEDTLS_ERR_ASN1_UNEXPECTED_TAG

X509 RSASSA-PSS parameters ASN1 (trailerField not 1)
x509_parse_rsassa_pss_params:"a303020102":MBEDTLS_ASN1_CONSTRUCTED | MBEDTLS_ASN1_SEQUENCE:MBEDTLS_MD_SHA1:MBEDTLS_MD_SHA1:20:MBEDTLS_ERR_X509_INVALID_ALG

X509 CSR ASN.1 (OK)
depends_on:MBEDTLS_PK_CAN_ECDSA_SOME:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_MD_CAN_SHA1:!MBEDTLS_X509_REMOVE_INFO
mbedtls_x509_csr_parse:"308201183081bf0201003034310b3009060355040613024e4c3111300f060355040a1308506f6c617253534c31123010060355040313096c6f63616c686f73743059301306072a8648ce3d020106082a8648ce3d0301070342000437cc56d976091e5a723ec7592dff206eee7cf9069174d0ad14b5f768225962924ee500d82311ffea2fd2345d5d16bd8a88c26b770d55cd8a2a0efa01c8b4edffa029302706092a864886f70d01090e311a301830090603551d1304023000300b0603551d0f0404030205e0300906072a8648ce3d04010349003046022100b49fd8c8f77abfa871908dfbe684a08a793d0f490a43d86fcf2086e4f24bb0c2022100f829d5ccd3742369299e6294394717c4b723a0f68b44e831b6e6c3bcabf97243":"CSR version   \: 1\nsubject name  \: C=NL, O=PolarSSL, CN=localhost\nsigned using  \: ECDSA with SHA1\nEC key size   \: 256 bits\n\nkey usage         \: Digital Signature, Non Repudiation, Key Encipherment\n":0

X509 CSR ASN.1 (Unsupported critical extension, critical=true)
depends_on:MBEDTLS_PK_CAN_ECDSA_SOME:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_MD_CAN_SHA256:!MBEDTLS_X509_REMOVE_INFO
mbedtls_x509_csr_parse:"308201233081cb02010030413119301706035504030c1053656c66207369676e65642074657374310b300906035504061302444531173015060355040a0c0e41757468437274444220546573743059301306072a8648ce3d020106082a8648ce3d03010703420004c11ebb9951848a436ca2c8a73382f24bbb6c28a92e401d4889b0c361f377b92a8b0497ff2f5a5f6057ae85f704ab1850bef075914f68ed3aeb15a1ff1ebc0dc6a028302606092a864886f70d01090e311930173015060b2b0601040183890c8622020101ff0403010101300a06082a8648ce3d040302034700304402200c4108fd098525993d3fd5b113f0a1ead8750852baf55a2f8e670a22cabc0ba1022034db93a0fcb993912adcf2ea8cb4b66389af30e264d43c0daea03255e45d2ccc":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS+MBEDTLS_ERR_ASN1_UNEXPECTED_TAG

X509 CSR ASN.1 (Unsupported non-critical extension, critical=false)
depends_on:MBEDTLS_PK_CAN_ECDSA_SOME:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_MD_CAN_SHA256:!MBEDTLS_X509_REMOVE_INFO
mbedtls_x509_csr_parse:"308201243081cb02010030413119301706035504030c1053656c66207369676e65642074657374310b300906035504061302444531173015060355040a0c0e41757468437274444220546573743059301306072a8648ce3d020106082a8648ce3d03010703420004b5e718a7df6b21912733e77c42f266b8283e6cae7adf8afd56b990c1c6232ea0a2a46097c218353bc948444aea3d00423e84802e28c48099641fe9977cdfb505a028302606092a864886f70d01090e311930173015060b2b0601040183890c8622020101000403010101300a06082a8648ce3d0403020348003045022100f0ba9a0846ad0a7cefd0a61d5fc92194dc06037a44158de2d0c569912c058d430220421f27a9f249c1687e2fa34db3f543c8512fd925dfe5ae00867f13963ffd4f8d":"CSR version   \: 1\nsubject name  \: CN=Self signed test, C=DE, O=AuthCrtDB Test\nsigned using  \: ECDSA with SHA256\nEC key size   \: 256 bits\n":0

X509 CSR ASN.1 (Unsupported non-critical extension, critical undefined)
depends_on:MBEDTLS_PK_CAN_ECDSA_SOME:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_MD_CAN_SHA256:!MBEDTLS_X509_REMOVE_INFO
mbedtls_x509_csr_parse:"308201223081c802010030413119301706035504030c1053656c66207369676e65642074657374310b300906035504061302444531173015060355040a0c0e41757468437274444220546573743059301306072a8648ce3d020106082a8648ce3d030107034200045f94b28d133418833bf10c442d91306459d3925e7cea06ebb9220932e7de116fb671c5d2d6c0a3784a12897217aef8432e7228fcea0ab016bdb67b67ced4c612a025302306092a864886f70d01090e311630143012060b2b0601040183890c8622020403010101300a06082a8648ce3d04030203490030460221009b1e8b25775c18525e96753e1ed55875f8d62f026c5b7f70eb5037ad27dc92de022100ba1dfe14de6af6a603f763563fd046b1cd3714b54d6daf5d8a72076497f11014":"CSR version   \: 1\nsubject name  \: CN=Self signed test, C=DE, O=AuthCrtDB Test\nsigned using  \: ECDSA with SHA256\nEC key size   \: 256 bits\n":0

X509 CSR ASN.1 (Unsupported critical extension accepted by callback, critical=true)
depends_on:MBEDTLS_PK_CAN_ECDSA_SOME:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_MD_CAN_SHA256:!MBEDTLS_X509_REMOVE_INFO
mbedtls_x509_csr_parse_with_ext_cb:"308201233081cb02010030413119301706035504030c1053656c66207369676e65642074657374310b300906035504061302444531173015060355040a0c0e41757468437274444220546573743059301306072a8648ce3d020106082a8648ce3d03010703420004c11ebb9951848a436ca2c8a73382f24bbb6c28a92e401d4889b0c361f377b92a8b0497ff2f5a5f6057ae85f704ab1850bef075914f68ed3aeb15a1ff1ebc0dc6a028302606092a864886f70d01090e311930173015060b2b0601040183890c8622020101ff0403010101300a06082a8648ce3d040302034700304402200c4108fd098525993d3fd5b113f0a1ead8750852baf55a2f8e670a22cabc0ba1022034db93a0fcb993912adcf2ea8cb4b66389af30e264d43c0daea03255e45d2ccc":"CSR version   \: 1\nsubject name  \: CN=Self signed test, C=DE, O=AuthCrtDB Test\nsigned using  \: ECDSA with SHA256\nEC key size   \: 256 bits\n":0:1

X509 CSR ASN.1 (Unsupported critical extension rejected by callback, critical=true)
depends_on:MBEDTLS_PK_CAN_ECDSA_SOME:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_MD_CAN_SHA256:!MBEDTLS_X509_REMOVE_INFO
mbedtls_x509_csr_parse_with_ext_cb:"308201233081cb02010030413119301706035504030c1053656c66207369676e65642074657374310b300906035504061302444531173015060355040a0c0e41757468437274444220546573743059301306072a8648ce3d020106082a8648ce3d03010703420004c11ebb9951848a436ca2c8a73382f24bbb6c28a92e401d4889b0c361f377b92a8b0497ff2f5a5f6057ae85f704ab1850bef075914f68ed3aeb15a1ff1ebc0dc6a028302606092a864886f70d01090e311930173015060b2b0601040183890c8622020101ff0403010101300a06082a8648ce3d040302034700304402200c4108fd098525993d3fd5b113f0a1ead8750852baf55a2f8e670a22cabc0ba1022034db93a0fcb993912adcf2ea8cb4b66389af30e264d43c0daea03255e45d2ccc":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS+MBEDTLS_ERR_ASN1_UNEXPECTED_TAG:0

X509 CSR ASN.1 (bad first tag)
mbedtls_x509_csr_parse:"3100":"":MBEDTLS_ERR_X509_INVALID_FORMAT

X509 CSR ASN.1 (bad sequence: overlong)
mbedtls_x509_csr_parse:"3001":"":MBEDTLS_ERR_X509_INVALID_FORMAT

X509 CSR ASN.1 (total length mistmatch)
mbedtls_x509_csr_parse:"30010000":"":MBEDTLS_ERR_X509_INVALID_FORMAT + MBEDTLS_ERR_ASN1_LENGTH_MISMATCH

X509 CSR ASN.1 (bad CRI: not a sequence)
mbedtls_x509_csr_parse:"30023100":"":MBEDTLS_ERR_X509_INVALID_FORMAT + MBEDTLS_ERR_ASN1_UNEXPECTED_TAG

X509 CSR ASN.1 (bad CRI: overlong)
mbedtls_x509_csr_parse:"30023001":"":MBEDTLS_ERR_X509_INVALID_FORMAT + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CSR ASN.1 (bad CRI.Version: overlong)
mbedtls_x509_csr_parse:"30053002020100":"":MBEDTLS_ERR_X509_INVALID_VERSION + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CSR ASN.1 (bad CRI.Version: not v1)
mbedtls_x509_csr_parse:"30053003020101":"":MBEDTLS_ERR_X509_UNKNOWN_VERSION

X509 CSR ASN.1 (bad CRI.Name: not a sequence)
mbedtls_x509_csr_parse:"300730050201003100":"":MBEDTLS_ERR_X509_INVALID_FORMAT + MBEDTLS_ERR_ASN1_UNEXPECTED_TAG

X509 CSR ASN.1 (bad CRI.Name: overlong)
mbedtls_x509_csr_parse:"30083005020100300100":"":MBEDTLS_ERR_X509_INVALID_FORMAT + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CSR ASN.1 (bad CRI.Name payload: not a set)
mbedtls_x509_csr_parse:"3009300702010030023000":"":MBEDTLS_ERR_X509_INVALID_NAME + MBEDTLS_ERR_ASN1_UNEXPECTED_TAG

X509 CSR ASN.1 (bad CRI.Name payload: overlong)
mbedtls_x509_csr_parse:"300a30080201003002310100":"":MBEDTLS_ERR_X509_INVALID_NAME + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CSR ASN.1 (bad SubjectPublicKeyInfo: missing)
mbedtls_x509_csr_parse:"30143012020100300d310b3009060355040613024e4c":"":MBEDTLS_ERR_PK_KEY_INVALID_FORMAT + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CSR ASN.1 (bad SubjectPublicKeyInfo: not a sequence)
mbedtls_x509_csr_parse:"30163014020100300d310b3009060355040613024e4c3100":"":MBEDTLS_ERR_PK_KEY_INVALID_FORMAT + MBEDTLS_ERR_ASN1_UNEXPECTED_TAG

X509 CSR ASN.1 (bad SubjectPublicKeyInfo: overlong)
mbedtls_x509_csr_parse:"30173014020100300d310b3009060355040613024e4c300100":"":MBEDTLS_ERR_PK_KEY_INVALID_FORMAT + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CSR ASN.1 (bad attributes: missing)
depends_on:MBEDTLS_PK_CAN_ECDSA_SOME:MBEDTLS_ECP_HAVE_SECP256R1
mbedtls_x509_csr_parse:"3081973081940201003034310b3009060355040613024e4c3111300f060355040a1308506f6c617253534c31123010060355040313096c6f63616c686f73743059301306072a8648ce3d020106082a8648ce3d0301070342000437cc56d976091e5a723ec7592dff206eee7cf9069174d0ad14b5f768225962924ee500d82311ffea2fd2345d5d16bd8a88c26b770d55cd8a2a0efa01c8b4edff":"":MBEDTLS_ERR_X509_INVALID_FORMAT + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CSR ASN.1 (bad attributes: bad tag)
depends_on:MBEDTLS_PK_CAN_ECDSA_SOME:MBEDTLS_ECP_HAVE_SECP256R1
mbedtls_x509_csr_parse:"3081993081960201003034310b3009060355040613024e4c3111300f060355040a1308506f6c617253534c31123010060355040313096c6f63616c686f73743059301306072a8648ce3d020106082a8648ce3d0301070342000437cc56d976091e5a723ec7592dff206eee7cf9069174d0ad14b5f768225962924ee500d82311ffea2fd2345d5d16bd8a88c26b770d55cd8a2a0efa01c8b4edff0500":"":MBEDTLS_ERR_X509_INVALID_FORMAT + MBEDTLS_ERR_ASN1_UNEXPECTED_TAG

X509 CSR ASN.1 (bad attributes: overlong)
depends_on:MBEDTLS_PK_CAN_ECDSA_SOME:MBEDTLS_ECP_HAVE_SECP256R1
mbedtls_x509_csr_parse:"30819a3081960201003034310b3009060355040613024e4c3111300f060355040a1308506f6c617253534c31123010060355040313096c6f63616c686f73743059301306072a8648ce3d020106082a8648ce3d0301070342000437cc56d976091e5a723ec7592dff206eee7cf9069174d0ad14b5f768225962924ee500d82311ffea2fd2345d5d16bd8a88c26b770d55cd8a2a0efa01c8b4edffa00100":"":MBEDTLS_ERR_X509_INVALID_FORMAT + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CSR ASN.1 (bad sigAlg: missing)
depends_on:MBEDTLS_PK_CAN_ECDSA_SOME:MBEDTLS_ECP_HAVE_SECP256R1
mbedtls_x509_csr_parse:"3081c23081bf0201003034310b3009060355040613024e4c3111300f060355040a1308506f6c617253534c31123010060355040313096c6f63616c686f73743059301306072a8648ce3d020106082a8648ce3d0301070342000437cc56d976091e5a723ec7592dff206eee7cf9069174d0ad14b5f768225962924ee500d82311ffea2fd2345d5d16bd8a88c26b770d55cd8a2a0efa01c8b4edffa029302706092a864886f70d01090e311a301830090603551d1304023000300b0603551d0f0404030205e0":"":MBEDTLS_ERR_X509_INVALID_ALG + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CSR ASN.1 (bad sigAlg: not a sequence)
depends_on:MBEDTLS_PK_CAN_ECDSA_SOME:MBEDTLS_ECP_HAVE_SECP256R1
mbedtls_x509_csr_parse:"3081c43081bf0201003034310b3009060355040613024e4c3111300f060355040a1308506f6c617253534c31123010060355040313096c6f63616c686f73743059301306072a8648ce3d020106082a8648ce3d0301070342000437cc56d976091e5a723ec7592dff206eee7cf9069174d0ad14b5f768225962924ee500d82311ffea2fd2345d5d16bd8a88c26b770d55cd8a2a0efa01c8b4edffa029302706092a864886f70d01090e311a301830090603551d1304023000300b0603551d0f0404030205e03100":"":MBEDTLS_ERR_X509_INVALID_ALG + MBEDTLS_ERR_ASN1_UNEXPECTED_TAG

X509 CSR ASN.1 (bad sigAlg: overlong)
depends_on:MBEDTLS_PK_CAN_ECDSA_SOME:MBEDTLS_ECP_HAVE_SECP256R1
mbedtls_x509_csr_parse:"3081c43081bf0201003034310b3009060355040613024e4c3111300f060355040a1308506f6c617253534c31123010060355040313096c6f63616c686f73743059301306072a8648ce3d020106082a8648ce3d0301070342000437cc56d976091e5a723ec7592dff206eee7cf9069174d0ad14b5f768225962924ee500d82311ffea2fd2345d5d16bd8a88c26b770d55cd8a2a0efa01c8b4edffa029302706092a864886f70d01090e311a301830090603551d1304023000300b0603551d0f0404030205e03001":"":MBEDTLS_ERR_X509_INVALID_ALG + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CSR ASN.1 (bad sigAlg: unknown)
depends_on:MBEDTLS_PK_CAN_ECDSA_SOME:MBEDTLS_ECP_HAVE_SECP256R1
mbedtls_x509_csr_parse:"3081cd3081bf0201003034310b3009060355040613024e4c3111300f060355040a1308506f6c617253534c31123010060355040313096c6f63616c686f73743059301306072a8648ce3d020106082a8648ce3d0301070342000437cc56d976091e5a723ec7592dff206eee7cf9069174d0ad14b5f768225962924ee500d82311ffea2fd2345d5d16bd8a88c26b770d55cd8a2a0efa01c8b4edffa029302706092a864886f70d01090e311a301830090603551d1304023000300b0603551d0f0404030205e0300906072a8648ce3d04ff":"":MBEDTLS_ERR_X509_UNKNOWN_SIG_ALG

X509 CSR ASN.1 (bad sig: missing)
depends_on:MBEDTLS_PK_CAN_ECDSA_SOME:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_MD_CAN_SHA1
mbedtls_x509_csr_parse:"3081cd3081bf0201003034310b3009060355040613024e4c3111300f060355040a1308506f6c617253534c31123010060355040313096c6f63616c686f73743059301306072a8648ce3d020106082a8648ce3d0301070342000437cc56d976091e5a723ec7592dff206eee7cf9069174d0ad14b5f768225962924ee500d82311ffea2fd2345d5d16bd8a88c26b770d55cd8a2a0efa01c8b4edffa029302706092a864886f70d01090e311a301830090603551d1304023000300b0603551d0f0404030205e0300906072a8648ce3d0401":"":MBEDTLS_ERR_X509_INVALID_SIGNATURE + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CSR ASN.1 (bad sig: not a bit string)
depends_on:MBEDTLS_PK_CAN_ECDSA_SOME:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_MD_CAN_SHA1
mbedtls_x509_csr_parse:"3081cf3081bf0201003034310b3009060355040613024e4c3111300f060355040a1308506f6c617253534c31123010060355040313096c6f63616c686f73743059301306072a8648ce3d020106082a8648ce3d0301070342000437cc56d976091e5a723ec7592dff206eee7cf9069174d0ad14b5f768225962924ee500d82311ffea2fd2345d5d16bd8a88c26b770d55cd8a2a0efa01c8b4edffa029302706092a864886f70d01090e311a301830090603551d1304023000300b0603551d0f0404030205e0300906072a8648ce3d04010400":"":MBEDTLS_ERR_X509_INVALID_SIGNATURE + MBEDTLS_ERR_ASN1_UNEXPECTED_TAG

X509 CSR ASN.1 (bad sig: overlong)
depends_on:MBEDTLS_PK_CAN_ECDSA_SOME:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_MD_CAN_SHA1
mbedtls_x509_csr_parse:"3081cf3081bf0201003034310b3009060355040613024e4c3111300f060355040a1308506f6c617253534c31123010060355040313096c6f63616c686f73743059301306072a8648ce3d020106082a8648ce3d0301070342000437cc56d976091e5a723ec7592dff206eee7cf9069174d0ad14b5f768225962924ee500d82311ffea2fd2345d5d16bd8a88c26b770d55cd8a2a0efa01c8b4edffa029302706092a864886f70d01090e311a301830090603551d1304023000300b0603551d0f0404030205e0300906072a8648ce3d04010301":"":MBEDTLS_ERR_X509_INVALID_SIGNATURE + MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CSR ASN.1 (extra data after signature)
depends_on:MBEDTLS_PK_CAN_ECDSA_SOME:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_MD_CAN_SHA1
mbedtls_x509_csr_parse:"308201193081bf0201003034310b3009060355040613024e4c3111300f060355040a1308506f6c617253534c31123010060355040313096c6f63616c686f73743059301306072a8648ce3d020106082a8648ce3d0301070342000437cc56d976091e5a723ec7592dff206eee7cf9069174d0ad14b5f768225962924ee500d82311ffea2fd2345d5d16bd8a88c26b770d55cd8a2a0efa01c8b4edffa029302706092a864886f70d01090e311a301830090603551d1304023000300b0603551d0f0404030205e0300906072a8648ce3d04010349003046022100b49fd8c8f77abfa871908dfbe684a08a793d0f490a43d86fcf2086e4f24bb0c2022100f829d5ccd3742369299e6294394717c4b723a0f68b44e831b6e6c3bcabf9724300":"":MBEDTLS_ERR_X509_INVALID_FORMAT + MBEDTLS_ERR_ASN1_LENGTH_MISMATCH

X509 CSR ASN.1 (invalid version overflow)
mbedtls_x509_csr_parse:"3008300602047fffffff":"":MBEDTLS_ERR_X509_UNKNOWN_VERSION

# Used test_csr_v3_all.csr.der as a base for malforming CSR extenstions/attributes
# Please see makefile for data_files to check malformation details (test_csr_v3_all_malformed_xxx.csr files)
X509 CSR ASN.1 (attributes: invalid sequence tag)
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_RSA_C
mbedtls_x509_csr_parse_file:"data_files/parse_input/test_csr_v3_all_malformed_attributes_sequence_tag.csr.der":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS+MBEDTLS_ERR_ASN1_UNEXPECTED_TAG

X509 CSR ASN.1 (attributes: invalid attribute id)
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_RSA_C
mbedtls_x509_csr_parse_file:"data_files/parse_input/test_csr_v3_all_malformed_attributes_id_tag.csr.der":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS+MBEDTLS_ERR_ASN1_UNEXPECTED_TAG

X509 CSR ASN.1 (attributes: not extension request)
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_RSA_C
mbedtls_x509_csr_parse_file:"data_files/parse_input/test_csr_v3_all_malformed_attributes_extension_request.csr.der":"CSR version   \: 1\nsubject name  \: CN=etcd\nsigned using  \: RSA with SHA-256\nRSA key size  \: 1024 bits\n":0

X509 CSR ASN.1 (attributes: invalid extenstion request set tag)
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_RSA_C
mbedtls_x509_csr_parse_file:"data_files/parse_input/test_csr_v3_all_malformed_attributes_extension_request_set_tag.csr.der":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS+MBEDTLS_ERR_ASN1_UNEXPECTED_TAG

X509 CSR ASN.1 (attributes: invalid extenstion request sequence tag)
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_RSA_C
mbedtls_x509_csr_parse_file:"data_files/parse_input/test_csr_v3_all_malformed_attributes_extension_request_sequence_tag.csr.der":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS+MBEDTLS_ERR_ASN1_UNEXPECTED_TAG

X509 CSR ASN.1 (attributes: invalid len (len > data))
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_RSA_C
mbedtls_x509_csr_parse_file:"data_files/parse_input/test_csr_v3_all_malformed_attributes_len1.csr.der":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS+MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CSR ASN.1 (attributes: invalid len (len < data))
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_RSA_C
mbedtls_x509_csr_parse_file:"data_files/parse_input/test_csr_v3_all_malformed_attributes_len2.csr.der":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS+MBEDTLS_ERR_ASN1_LENGTH_MISMATCH

X509 CSR ASN.1 (attributes: extension request invalid len (len > data))
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_RSA_C
mbedtls_x509_csr_parse_file:"data_files/parse_input/test_csr_v3_all_malformed_attributes_extension_request_sequence_len1.csr.der":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS+MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CSR ASN.1 (attributes: extension request invalid len (len < data))
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_RSA_C
mbedtls_x509_csr_parse_file:"data_files/parse_input/test_csr_v3_all_malformed_attributes_extension_request_sequence_len2.csr.der":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS+MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CSR ASN.1 (extensions: invalid sequence tag)
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_RSA_C
mbedtls_x509_csr_parse_file:"data_files/parse_input/test_csr_v3_all_malformed_extensions_sequence_tag.csr.der":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS+MBEDTLS_ERR_ASN1_UNEXPECTED_TAG

X509 CSR ASN.1 (extensions: invalid extension id tag)
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_RSA_C
mbedtls_x509_csr_parse_file:"data_files/parse_input/test_csr_v3_all_malformed_extension_id_tag.csr.der":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS+MBEDTLS_ERR_ASN1_UNEXPECTED_TAG

X509 CSR ASN.1 (extensions: invalid extension data tag)
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_RSA_C
mbedtls_x509_csr_parse_file:"data_files/parse_input/test_csr_v3_all_malformed_extension_data_tag.csr.der":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS+MBEDTLS_ERR_ASN1_UNEXPECTED_TAG

X509 CSR ASN.1 (extensions: invalid extension data len (len > data))
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_RSA_C
mbedtls_x509_csr_parse_file:"data_files/parse_input/test_csr_v3_all_malformed_extension_data_len1.csr.der":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS+MBEDTLS_ERR_ASN1_OUT_OF_DATA

X509 CSR ASN.1 (extensions: invalid extension data len (len < data))
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_RSA_C
mbedtls_x509_csr_parse_file:"data_files/parse_input/test_csr_v3_all_malformed_extension_data_len2.csr.der":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS+MBEDTLS_ERR_ASN1_LENGTH_MISMATCH

X509 CSR ASN.1 (extensions: invalid extension key usage bitstream tag)
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_RSA_C
mbedtls_x509_csr_parse_file:"data_files/parse_input/test_csr_v3_all_malformed_extension_key_usage_bitstream_tag.csr.der":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS+MBEDTLS_ERR_ASN1_UNEXPECTED_TAG

X509 CSR ASN.1 (extensions: invalid extension subject alt name sequence tag)
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_RSA_C
mbedtls_x509_csr_parse_file:"data_files/parse_input/test_csr_v3_all_malformed_extension_subject_alt_name_sequence_tag.csr.der":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS+MBEDTLS_ERR_ASN1_UNEXPECTED_TAG

X509 CSR ASN.1 (extensions: invalid extension ns cert bitstream tag)
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_RSA_C
mbedtls_x509_csr_parse_file:"data_files/parse_input/test_csr_v3_all_malformed_extension_ns_cert_bitstream_tag.csr.der":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS+MBEDTLS_ERR_ASN1_UNEXPECTED_TAG

X509 CSR ASN.1 (extensions: duplicated extension)
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_RSA_C
mbedtls_x509_csr_parse_file:"data_files/parse_input/test_csr_v3_all_malformed_duplicated_extension.csr.der":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS+MBEDTLS_ERR_ASN1_INVALID_DATA

X509 CSR ASN.1 (extensions: invalid extension type data)
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_RSA_C
mbedtls_x509_csr_parse_file:"data_files/parse_input/test_csr_v3_all_malformed_extension_type_oid.csr.der":"CSR version   \: 1\nsubject name  \: CN=etcd\nsigned using  \: RSA with SHA-256\nRSA key size  \: 1024 bits\n\ncert. type        \: SSL Client\nkey usage         \: CRL Sign\n":0

X509 File parse (no issues)
depends_on:MBEDTLS_PK_CAN_ECDSA_SOME:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_MD_CAN_SHA256:MBEDTLS_RSA_C
mbedtls_x509_crt_parse_file:"data_files/parse_input/server7_int-ca.crt":0:2

X509 File parse (extra space in one certificate)
depends_on:MBEDTLS_PK_CAN_ECDSA_SOME:MBEDTLS_MD_CAN_SHA256:MBEDTLS_RSA_C
mbedtls_x509_crt_parse_file:"data_files/parse_input/server7_pem_space.crt":1:1

X509 File parse (all certificates fail)
depends_on:MBEDTLS_PK_CAN_ECDSA_SOME:MBEDTLS_RSA_C
mbedtls_x509_crt_parse_file:"data_files/parse_input/server7_all_space.crt":MBEDTLS_ERR_PEM_INVALID_DATA + MBEDTLS_ERR_BASE64_INVALID_CHARACTER:0

X509 File parse (trailing spaces, OK)
depends_on:MBEDTLS_PK_CAN_ECDSA_SOME:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_MD_CAN_SHA256:MBEDTLS_RSA_C
mbedtls_x509_crt_parse_file:"data_files/parse_input/server7_trailing_space.crt":0:2

X509 File parse (Algorithm Params Tag mismatch)
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_RSA_C
mbedtls_x509_crt_parse_file:"data_files/parse_input/cli-rsa-sha256-badalg.crt.der":MBEDTLS_ERR_X509_SIG_MISMATCH:0

X509 File parse (does not conform to RFC 5480 / RFC 5758 - AlgorithmIdentifier's parameters field is present, mbedTLS generated before bugfix, OK)
depends_on:MBEDTLS_PK_CAN_ECDSA_SOME:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_MD_CAN_SHA256
x509parse_crt_file:"data_files/parse_input/server5-non-compliant.crt":0

X509 File parse (conforms to RFC 5480 / RFC 5758 - AlgorithmIdentifier's parameters field must be absent for ECDSA)
depends_on:MBEDTLS_PK_CAN_ECDSA_SOME:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_MD_CAN_SHA256
x509parse_crt_file:"data_files/parse_input/server5.crt":0

X509 File parse & read the ca_istrue field (Not Set)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_RSA_C:MBEDTLS_HAVE_TIME_DATE:MBEDTLS_MD_CAN_SHA1
mbedtls_x509_get_ca_istrue:"data_files/parse_input/server1.crt":0

X509 File parse & read the ca_istrue field (Set)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_RSA_C:MBEDTLS_HAVE_TIME_DATE:MBEDTLS_MD_CAN_SHA1
mbedtls_x509_get_ca_istrue:"data_files/test-ca.crt":1

X509 File parse & read the ca_istrue field (Legacy Certificate)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_RSA_C:MBEDTLS_HAVE_TIME_DATE:MBEDTLS_MD_CAN_SHA1:MBEDTLS_MD_CAN_SHA256
mbedtls_x509_get_ca_istrue:"data_files/server1-v1.crt":MBEDTLS_ERR_X509_INVALID_EXTENSIONS

X509 Get time (UTC no issues)
depends_on:MBEDTLS_X509_USE_C
x509_get_time:MBEDTLS_ASN1_UTC_TIME:"500101000000Z":0:1950:1:1:0:0:0

X509 Get time (Generalized Time no issues)
depends_on:MBEDTLS_X509_USE_C
x509_get_time:MBEDTLS_ASN1_GENERALIZED_TIME:"99991231235959Z":0:9999:12:31:23:59:59

X509 Get time (UTC year without leap day)
depends_on:MBEDTLS_X509_USE_C
x509_get_time:MBEDTLS_ASN1_UTC_TIME:"490229121212Z":MBEDTLS_ERR_X509_INVALID_DATE:0:0:0:0:0:0

X509 Get time (UTC year with leap day)
depends_on:MBEDTLS_X509_USE_C
x509_get_time:MBEDTLS_ASN1_UTC_TIME:"000229121212Z":0:2000:2:29:12:12:12

X509 Get time (UTC invalid day of month #1)
depends_on:MBEDTLS_X509_USE_C
x509_get_time:MBEDTLS_ASN1_UTC_TIME:"000132121212Z":MBEDTLS_ERR_X509_INVALID_DATE:0:0:0:0:0:0

X509 Get time (UTC invalid day of month #2)
depends_on:MBEDTLS_X509_USE_C
x509_get_time:MBEDTLS_ASN1_UTC_TIME:"001131121212Z":MBEDTLS_ERR_X509_INVALID_DATE:0:0:0:0:0:0

X509 Get time (UTC invalid hour)
depends_on:MBEDTLS_X509_USE_C
x509_get_time:MBEDTLS_ASN1_UTC_TIME:"001130241212Z":MBEDTLS_ERR_X509_INVALID_DATE:0:0:0:0:0:0

X509 Get time (UTC invalid min)
depends_on:MBEDTLS_X509_USE_C
x509_get_time:MBEDTLS_ASN1_UTC_TIME:"001130236012Z":MBEDTLS_ERR_X509_INVALID_DATE:0:0:0:0:0:0

X509 Get time (UTC invalid sec)
depends_on:MBEDTLS_X509_USE_C
x509_get_time:MBEDTLS_ASN1_UTC_TIME:"001130235960Z":MBEDTLS_ERR_X509_INVALID_DATE:0:0:0:0:0:0

X509 Get time (UTC without time zone)
depends_on:MBEDTLS_X509_USE_C
x509_get_time:MBEDTLS_ASN1_UTC_TIME:"000229121212":0:2000:2:29:12:12:12

X509 Get time (UTC with invalid time zone #1)
depends_on:MBEDTLS_X509_USE_C
x509_get_time:MBEDTLS_ASN1_UTC_TIME:"000229121212J":MBEDTLS_ERR_X509_INVALID_DATE:0:0:0:0:0:0

X509 Get time (UTC with invalid time zone #2)
depends_on:MBEDTLS_X509_USE_C
x509_get_time:MBEDTLS_ASN1_UTC_TIME:"000229121212+0300":MBEDTLS_ERR_X509_INVALID_DATE:0:0:0:0:0:0

X509 Get time (Date with invalid tag)
depends_on:MBEDTLS_X509_USE_C
x509_get_time:MBEDTLS_ASN1_CONTEXT_SPECIFIC:"000229121212":MBEDTLS_ERR_X509_INVALID_DATE+MBEDTLS_ERR_ASN1_UNEXPECTED_TAG:0:0:0:0:0:0

X509 Get time (UTC, truncated)
depends_on:MBEDTLS_X509_USE_C
x509_get_time:MBEDTLS_ASN1_UTC_TIME:"000229121":MBEDTLS_ERR_X509_INVALID_DATE:0:0:0:0:0:0

X509 Get time (Generalized Time, truncated)
depends_on:MBEDTLS_X509_USE_C
x509_get_time:MBEDTLS_ASN1_GENERALIZED_TIME:"20000229121":MBEDTLS_ERR_X509_INVALID_DATE:0:0:0:0:0:0

X509 Get time (UTC without seconds)
depends_on:MBEDTLS_X509_USE_C
x509_get_time:MBEDTLS_ASN1_UTC_TIME:"0002291212":MBEDTLS_ERR_X509_INVALID_DATE:2000:2:29:12:12:0

X509 Get time (UTC without seconds and with invalid time zone #1)
depends_on:MBEDTLS_X509_USE_C
x509_get_time:MBEDTLS_ASN1_UTC_TIME:"0002291212J":MBEDTLS_ERR_X509_INVALID_DATE:0:0:0:0:0:0

X509 Get time (UTC without second and with invalid time zone #2)
depends_on:MBEDTLS_X509_USE_C
x509_get_time:MBEDTLS_ASN1_UTC_TIME:"0002291212+0300":MBEDTLS_ERR_X509_INVALID_DATE:0:0:0:0:0:0

X509 Get time (UTC invalid character in year)
depends_on:MBEDTLS_X509_USE_C
x509_get_time:MBEDTLS_ASN1_UTC_TIME:"0\\1130231212Z":MBEDTLS_ERR_X509_INVALID_DATE:0:0:0:0:0:0

X509 Get time (UTC invalid character in month)
depends_on:MBEDTLS_X509_USE_C
x509_get_time:MBEDTLS_ASN1_UTC_TIME:"001%30231212Z":MBEDTLS_ERR_X509_INVALID_DATE:0:0:0:0:0:0

X509 Get time (UTC invalid character in day)
depends_on:MBEDTLS_X509_USE_C
x509_get_time:MBEDTLS_ASN1_UTC_TIME:"0011`0231212Z":MBEDTLS_ERR_X509_INVALID_DATE:0:0:0:0:0:0

X509 Get time (UTC invalid character in hour)
depends_on:MBEDTLS_X509_USE_C
x509_get_time:MBEDTLS_ASN1_UTC_TIME:"0011302h1212Z":MBEDTLS_ERR_X509_INVALID_DATE:0:0:0:0:0:0

X509 Get time (UTC invalid character in min)
depends_on:MBEDTLS_X509_USE_C
x509_get_time:MBEDTLS_ASN1_UTC_TIME:"00113023u012Z":MBEDTLS_ERR_X509_INVALID_DATE:0:0:0:0:0:0

X509 Get time (UTC invalid character in sec)
depends_on:MBEDTLS_X509_USE_C
x509_get_time:MBEDTLS_ASN1_UTC_TIME:"0011302359n0Z":MBEDTLS_ERR_X509_INVALID_DATE:0:0:0:0:0:0

X509 Get time (Generalized Time, year multiple of 100 but not 400 is not a leap year)
depends_on:MBEDTLS_X509_USE_C
x509_get_time:MBEDTLS_ASN1_GENERALIZED_TIME:"19000229000000Z":MBEDTLS_ERR_X509_INVALID_DATE:0:0:0:0:0:0

X509 Get time (Generalized Time, year multiple of 4 but not 100 is a leap year)
depends_on:MBEDTLS_X509_USE_C
x509_get_time:MBEDTLS_ASN1_GENERALIZED_TIME:"19920229000000Z":0:1992:2:29:0:0:0

X509 Get time (Generalized Time, year multiple of 400 is a leap year)
depends_on:MBEDTLS_X509_USE_C
x509_get_time:MBEDTLS_ASN1_GENERALIZED_TIME:"20000229000000Z":0:2000:2:29:0:0:0

X509 Get time (Generalized Time invalid leap year not multiple of 4, 100 or 400)
depends_on:MBEDTLS_X509_USE_C
x509_get_time:MBEDTLS_ASN1_GENERALIZED_TIME:"19910229000000Z":MBEDTLS_ERR_X509_INVALID_DATE:0:0:0:0:0:0

X509 CRT verify restart: trusted EE, max_ops=0 (disabled)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_MD_CAN_SHA256:MBEDTLS_ECP_HAVE_SECP256R1
x509_verify_restart:"data_files/server5-selfsigned.crt":"data_files/server5-selfsigned.crt":0:0:0:0:0

X509 CRT verify restart: trusted EE, max_ops=1
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_MD_CAN_SHA256:MBEDTLS_ECP_HAVE_SECP256R1
x509_verify_restart:"data_files/server5-selfsigned.crt":"data_files/server5-selfsigned.crt":0:0:1:0:0

X509 CRT verify restart: no intermediate, max_ops=0 (disabled)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_MD_CAN_SHA256:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_ECP_HAVE_SECP384R1
x509_verify_restart:"data_files/server5.crt":"data_files/test-ca2.crt":0:0:0:0:0

X509 CRT verify restart: no intermediate, max_ops=1
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_MD_CAN_SHA256:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_ECP_HAVE_SECP384R1
x509_verify_restart:"data_files/server5.crt":"data_files/test-ca2.crt":0:0:1:100:10000

X509 CRT verify restart: no intermediate, max_ops=40000
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_MD_CAN_SHA256:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_ECP_HAVE_SECP384R1
x509_verify_restart:"data_files/server5.crt":"data_files/test-ca2.crt":0:0:40000:0:0

X509 CRT verify restart: no intermediate, max_ops=500
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_MD_CAN_SHA256:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_ECP_HAVE_SECP384R1
x509_verify_restart:"data_files/server5.crt":"data_files/test-ca2.crt":0:0:500:20:80

X509 CRT verify restart: no intermediate, badsign, max_ops=0 (disabled)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_MD_CAN_SHA256:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_ECP_HAVE_SECP384R1
x509_verify_restart:"data_files/server5-badsign.crt":"data_files/test-ca2.crt":MBEDTLS_ERR_X509_CERT_VERIFY_FAILED:MBEDTLS_X509_BADCERT_NOT_TRUSTED:0:0:0

X509 CRT verify restart: no intermediate, badsign, max_ops=1
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_MD_CAN_SHA256:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_ECP_HAVE_SECP384R1
x509_verify_restart:"data_files/server5-badsign.crt":"data_files/test-ca2.crt":MBEDTLS_ERR_X509_CERT_VERIFY_FAILED:MBEDTLS_X509_BADCERT_NOT_TRUSTED:1:100:10000

X509 CRT verify restart: no intermediate, badsign, max_ops=40000
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_MD_CAN_SHA256:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_ECP_HAVE_SECP384R1
x509_verify_restart:"data_files/server5-badsign.crt":"data_files/test-ca2.crt":MBEDTLS_ERR_X509_CERT_VERIFY_FAILED:MBEDTLS_X509_BADCERT_NOT_TRUSTED:40000:0:0

X509 CRT verify restart: no intermediate, badsign, max_ops=500
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_MD_CAN_SHA256:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_ECP_HAVE_SECP384R1
x509_verify_restart:"data_files/server5-badsign.crt":"data_files/test-ca2.crt":MBEDTLS_ERR_X509_CERT_VERIFY_FAILED:MBEDTLS_X509_BADCERT_NOT_TRUSTED:500:20:80

X509 CRT verify restart: one int, max_ops=0 (disabled)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_MD_CAN_SHA256:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_RSA_C
x509_verify_restart:"data_files/server10_int3_int-ca2.crt":"data_files/test-int-ca2.crt":0:0:0:0:0

X509 CRT verify restart: one int, max_ops=1
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_MD_CAN_SHA256:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_RSA_C
x509_verify_restart:"data_files/server10_int3_int-ca2.crt":"data_files/test-int-ca2.crt":0:0:1:100:10000

X509 CRT verify restart: one int, max_ops=30000
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_MD_CAN_SHA256:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_RSA_C
x509_verify_restart:"data_files/server10_int3_int-ca2.crt":"data_files/test-int-ca2.crt":0:0:30000:0:0

X509 CRT verify restart: one int, max_ops=500
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_MD_CAN_SHA256:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_RSA_C
x509_verify_restart:"data_files/server10_int3_int-ca2.crt":"data_files/test-int-ca2.crt":0:0:500:25:100

X509 CRT verify restart: one int, EE badsign, max_ops=0 (disabled)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_MD_CAN_SHA256:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_RSA_C
x509_verify_restart:"data_files/server10-bs_int3.pem":"data_files/test-int-ca2.crt":MBEDTLS_ERR_X509_CERT_VERIFY_FAILED:MBEDTLS_X509_BADCERT_NOT_TRUSTED:0:0:0

X509 CRT verify restart: one int, EE badsign, max_ops=1
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_MD_CAN_SHA256:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_RSA_C
x509_verify_restart:"data_files/server10-bs_int3.pem":"data_files/test-int-ca2.crt":MBEDTLS_ERR_X509_CERT_VERIFY_FAILED:MBEDTLS_X509_BADCERT_NOT_TRUSTED:1:100:10000

X509 CRT verify restart: one int, EE badsign, max_ops=30000
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_MD_CAN_SHA256:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_RSA_C
x509_verify_restart:"data_files/server10-bs_int3.pem":"data_files/test-int-ca2.crt":MBEDTLS_ERR_X509_CERT_VERIFY_FAILED:MBEDTLS_X509_BADCERT_NOT_TRUSTED:30000:0:0

X509 CRT verify restart: one int, EE badsign, max_ops=500
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_MD_CAN_SHA256:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_RSA_C
x509_verify_restart:"data_files/server10-bs_int3.pem":"data_files/test-int-ca2.crt":MBEDTLS_ERR_X509_CERT_VERIFY_FAILED:MBEDTLS_X509_BADCERT_NOT_TRUSTED:500:25:100

X509 CRT verify restart: one int, int badsign, max_ops=0 (disabled)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_MD_CAN_SHA256:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_RSA_C
x509_verify_restart:"data_files/server10_int3-bs.pem":"data_files/test-int-ca2.crt":MBEDTLS_ERR_X509_CERT_VERIFY_FAILED:MBEDTLS_X509_BADCERT_NOT_TRUSTED:0:0:0

X509 CRT verify restart: one int, int badsign, max_ops=1
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_MD_CAN_SHA256:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_RSA_C
x509_verify_restart:"data_files/server10_int3-bs.pem":"data_files/test-int-ca2.crt":MBEDTLS_ERR_X509_CERT_VERIFY_FAILED:MBEDTLS_X509_BADCERT_NOT_TRUSTED:1:100:10000

X509 CRT verify restart: one int, int badsign, max_ops=30000
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_MD_CAN_SHA256:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_RSA_C
x509_verify_restart:"data_files/server10_int3-bs.pem":"data_files/test-int-ca2.crt":MBEDTLS_ERR_X509_CERT_VERIFY_FAILED:MBEDTLS_X509_BADCERT_NOT_TRUSTED:30000:0:0

X509 CRT verify restart: one int, int badsign, max_ops=500
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_MD_CAN_SHA256:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_RSA_C
x509_verify_restart:"data_files/server10_int3-bs.pem":"data_files/test-int-ca2.crt":MBEDTLS_ERR_X509_CERT_VERIFY_FAILED:MBEDTLS_X509_BADCERT_NOT_TRUSTED:500:25:100

X509 ext types accessor: ext type present
depends_on:MBEDTLS_X509_CRT_PARSE_C
x509_accessor_ext_types:MBEDTLS_X509_EXT_KEY_USAGE:MBEDTLS_X509_EXT_KEY_USAGE

X509 ext types accessor: ext type not present
depends_on:MBEDTLS_X509_CRT_PARSE_C
x509_accessor_ext_types:MBEDTLS_X509_EXT_KEY_USAGE:MBEDTLS_X509_EXT_SUBJECT_ALT_NAME

X509 CRT parse Subject Key Id - Correct Subject Key ID
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_RSA_C
x509_crt_parse_subjectkeyid:"data_files/authorityKeyId_subjectKeyId.crt.der":"A505E864B8DCDF600F50124D60A864AF4D8B4393":0

X509 CRT parse Subject Key Id - Wrong OCTET_STRING tag
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_RSA_C
x509_crt_parse_subjectkeyid:"data_files/authorityKeyId_subjectKeyId_tag_malformed.crt.der":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS+MBEDTLS_ERR_ASN1_UNEXPECTED_TAG

X509 CRT parse Subject Key Id - Wrong OCTET_STRING length
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_RSA_C
x509_crt_parse_subjectkeyid:"data_files/authorityKeyId_subjectKeyId_tag_len_malformed.crt.der":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS+MBEDTLS_ERR_ASN1_LENGTH_MISMATCH

X509 CRT parse Authority Key Id - Correct Authority Key ID
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_RSA_C
x509_crt_parse_authoritykeyid:"data_files/authorityKeyId_subjectKeyId.crt.der":"A505E864B8DCDF600F50124D60A864AF4D8B4393":"C=NL, OU=PolarSSL, CN=PolarSSL Test CA":"680430CD074DE63FCDC051260FD042C2B512B6BA":0

X509 CRT parse Authority Key Id - Correct Authority Key ID (no keyid)
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_RSA_C
x509_crt_parse_authoritykeyid:"data_files/authorityKeyId_no_keyid.crt.der":"":"C=NL, OU=PolarSSL, CN=PolarSSL Test CA":"680430CD074DE63FCDC051260FD042C2B512B6BA":0

X509 CRT parse Authority Key Id - Correct Authority Key ID (no issuer)
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_RSA_C
x509_crt_parse_authoritykeyid:"data_files/authorityKeyId_no_issuer.crt.der":"A505E864B8DCDF600F50124D60A864AF4D8B4393":"":"":0

X509 CRT parse Authority Key Id - no Authority Key ID
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_RSA_C
x509_crt_parse_authoritykeyid:"data_files/authorityKeyId_no_authorityKeyId.crt.der":"":"":"":0

X509 CRT parse Authority Key Id - Wrong Length
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_RSA_C
x509_crt_parse_authoritykeyid:"data_files/authorityKeyId_subjectKeyId_length_malformed.crt.der":"":"":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS+MBEDTLS_ERR_ASN1_LENGTH_MISMATCH

X509 CRT parse Authority Key Id - Wrong Sequence tag
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_RSA_C
x509_crt_parse_authoritykeyid:"data_files/authorityKeyId_subjectKeyId_sequence_tag_malformed.crt.der":"":"":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS+MBEDTLS_ERR_ASN1_UNEXPECTED_TAG

X509 CRT parse Authority Key Id - Wrong KeyId Tag
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_RSA_C
x509_crt_parse_authoritykeyid:"data_files/authorityKeyId_subjectKeyId_keyid_tag_malformed.crt.der":"":"":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS+MBEDTLS_ERR_ASN1_UNEXPECTED_TAG

X509 CRT parse Authority Key Id - Wrong KeyId Tag Length
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_RSA_C
x509_crt_parse_authoritykeyid:"data_files/authorityKeyId_subjectKeyId_keyid_tag_len_malformed.crt.der":"":"":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS+MBEDTLS_ERR_ASN1_INVALID_LENGTH

X509 CRT parse Authority Key Id - Wrong Issuer Tag
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_RSA_C
x509_crt_parse_authoritykeyid:"data_files/authorityKeyId_subjectKeyId_issuer_tag1_malformed.crt.der":"":"":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS+MBEDTLS_ERR_ASN1_UNEXPECTED_TAG

X509 CRT parse Authority Key Id - Wrong DirectoryName tag in issuer field
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_RSA_C
x509_crt_parse_authoritykeyid:"data_files/authorityKeyId_subjectKeyId_issuer_tag2_malformed.crt.der":"":"":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS+MBEDTLS_ERR_ASN1_UNEXPECTED_TAG

X509 CRT parse Authority Key Id - Wrong Serial Number Tag
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_RSA_C
x509_crt_parse_authoritykeyid:"data_files/authorityKeyId_subjectKeyId_sn_tag_malformed.crt.der":"":"":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS+MBEDTLS_ERR_ASN1_UNEXPECTED_TAG

X509 CRT parse Authority Key Id - Wrong Serial Number Tag length
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_RSA_C
x509_crt_parse_authoritykeyid:"data_files/authorityKeyId_subjectKeyId_sn_len_malformed.crt.der":"":"":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS+MBEDTLS_ERR_ASN1_LENGTH_MISMATCH

# clusterfuzz-testcase-minimized-fuzz_x509crt-6666050834661376: test for bad sequence of names in authorityCertIssuer (see issue #7576)
X509 CRT parse Authority Key Id - Wrong Issuer sequence
depends_on:MBEDTLS_MD_CAN_MD5:MBEDTLS_RSA_C
x509_crt_parse_authoritykeyid:"data_files/clusterfuzz-testcase-minimized-fuzz_x509crt-6666050834661376.crt.der":"":"":"":MBEDTLS_ERR_X509_INVALID_EXTENSIONS+MBEDTLS_ERR_ASN1_OUT_OF_DATA
