PSA hash finish: SHA-1 Test Vector NIST CAVS #1
depends_on:PSA_WANT_ALG_SHA_1
hash_finish:PSA_ALG_SHA_1:"":"da39a3ee5e6b4b0d3255bfef95601890afd80709"

PSA hash finish: SHA-1 Test Vector NIST CAVS #2
depends_on:PSA_WANT_ALG_SHA_1
hash_finish:PSA_ALG_SHA_1:"a8":"99f2aa95e36f95c2acb0eaf23998f030638f3f15"

PSA hash finish: SHA-1 Test Vector NIST CAVS #3
depends_on:PSA_WANT_ALG_SHA_1
hash_finish:PSA_ALG_SHA_1:"3000":"f944dcd635f9801f7ac90a407fbc479964dec024"

PSA hash finish: SHA-1 Test Vector NIST CAVS #4
depends_on:PSA_WANT_ALG_SHA_1
hash_finish:PSA_ALG_SHA_1:"42749e":"a444319e9b6cc1e8464c511ec0969c37d6bb2619"

PSA hash finish: SHA-1 Test Vector NIST CAVS #5
depends_on:PSA_WANT_ALG_SHA_1
hash_finish:PSA_ALG_SHA_1:"9fc3fe08":"16a0ff84fcc156fd5d3ca3a744f20a232d172253"

PSA hash finish: SHA-1 Test Vector NIST CAVS #6
depends_on:PSA_WANT_ALG_SHA_1
hash_finish:PSA_ALG_SHA_1:"b5c1c6f1af":"fec9deebfcdedaf66dda525e1be43597a73a1f93"

PSA hash finish: SHA-1 Test Vector NIST CAVS #7
depends_on:PSA_WANT_ALG_SHA_1
hash_finish:PSA_ALG_SHA_1:"ec29561244ede706b6eb30a1c371d74450a105c3f9735f7fa9fe38cf67f304a5736a106e92e17139a6813b1c81a4f3d3fb9546ab4296fa9f722826c066869edacd73b2548035185813e22634a9da44000d95a281ff9f264ecce0a931222162d021cca28db5f3c2aa24945ab1e31cb413ae29810fd794cad5dfaf29ec43cb38d198fe4ae1da2359780221405bd6712a5305da4b1b737fce7cd21c0eb7728d08235a9011":"970111c4e77bcc88cc20459c02b69b4aa8f58217"

PSA hash finish: SHA-1 Test Vector NIST CAVS #8
depends_on:PSA_WANT_ALG_SHA_1
hash_finish:PSA_ALG_SHA_1:"5fc2c3f6a7e79dc94be526e5166a238899d54927ce470018fbfd668fd9dd97cbf64e2c91584d01da63be3cc9fdff8adfefc3ac728e1e335b9cdc87f069172e323d094b47fa1e652afe4d6aa147a9f46fda33cacb65f3aa12234746b9007a8c85fe982afed7815221e43dba553d8fe8a022cdac1b99eeeea359e5a9d2e72e382dffa6d19f359f4f27dc3434cd27daeeda8e38594873398678065fbb23665aba9309d946135da0e4a4afdadff14db18e85e71dd93c3bf9faf7f25c8194c4269b1ee3d9934097ab990025d9c3aaf63d5109f52335dd3959d38ae485050e4bbb6235574fc0102be8f7a306d6e8de6ba6becf80f37415b57f9898a5824e77414197422be3d36a6080":"0423dc76a8791107d14e13f5265b343f24cc0f19"

PSA hash finish: SHA-1 Test Vector NIST CAVS #9
depends_on:PSA_WANT_ALG_SHA_1
hash_finish:PSA_ALG_SHA_1:"0f865f46a8f3aed2da18482aa09a8f390dc9da07d51d1bd10fe0bf5f3928d5927d08733d32075535a6d1c8ac1b2dc6ba0f2f633dc1af68e3f0fa3d85e6c60cb7b56c239dc1519a007ea536a07b518ecca02a6c31b46b76f021620ef3fc6976804018380e5ab9c558ebfc5cb1c9ed2d974722bf8ab6398f1f2b82fa5083f85c16a5767a3a07271d67743f00850ce8ec428c7f22f1cf01f99895c0c844845b06a06cecb0c6cf83eb55a1d4ebc44c2c13f6f7aa5e0e08abfd84e7864279057abc471ee4a45dbbb5774afa24e51791a0eada11093b88681fe30baa3b2e94113dc63342c51ca5d1a6096d0897b626e42cb91761058008f746f35465465540ad8c6b8b60f7e1461b3ce9e6529625984cb8c7d46f07f735be067588a0117f23e34ff57800e2bbe9a1605fde6087fb15d22c5d3ac47566b8c448b0cee40373e5ba6eaa21abee71366afbb27dbbd300477d70c371e7b8963812f5ed4fb784fb2f3bd1d3afe883cdd47ef32beaea":"6692a71d73e00f27df976bc56df4970650d90e45"

PSA hash finish: SHA-1 Test Vector NIST CAVS #10
depends_on:PSA_WANT_ALG_SHA_1
hash_finish:PSA_ALG_SHA_1:"8236153781bd2f1b81ffe0def1beb46f5a70191142926651503f1b3bb1016acdb9e7f7acced8dd168226f118ff664a01a8800116fd023587bfba52a2558393476f5fc69ce9c65001f23e70476d2cc81c97ea19caeb194e224339bcb23f77a83feac5096f9b3090c51a6ee6d204b735aa71d7e996d380b80822e4dfd43683af9c7442498cacbea64842dfda238cb099927c6efae07fdf7b23a4e4456e0152b24853fe0d5de4179974b2b9d4a1cdbefcbc01d8d311b5dda059136176ea698ab82acf20dd490be47130b1235cb48f8a6710473cfc923e222d94b582f9ae36d4ca2a32d141b8e8cc36638845fbc499bce17698c3fecae2572dbbd470552430d7ef30c238c2124478f1f780483839b4fb73d63a9460206824a5b6b65315b21e3c2f24c97ee7c0e78faad3df549c7ca8ef241876d9aafe9a309f6da352bec2caaa92ee8dca392899ba67dfed90aef33d41fc2494b765cb3e2422c8e595dabbfaca217757453fb322a13203f425f6073a9903e2dc5818ee1da737afc345f0057744e3a56e1681c949eb12273a3bfc20699e423b96e44bd1ff62e50a848a890809bfe1611c6787d3d741103308f849a790f9c015098286dbacfc34c1718b2c2b77e32194a75dda37954a320fa68764027852855a7e5b5274eb1e2cbcd27161d98b59ad245822015f48af82a45c0ed59be94f9af03d9736048570d6e3ef63b1770bc98dfb77de84b1bb1708d872b625d9ab9b06c18e5dbbf34399391f0f8aa26ec0dac7ff4cb8ec97b52bcb942fa6db2385dcd1b3b9d567aaeb425d567b0ebe267235651a1ed9bf78fd93d3c1dd077fe340bb04b00529c58f45124b717c168d07e9826e33376988bc5cf62845c2009980a4dfa69fbc7e5a0b1bb20a5958ca967aec68eb31dd8fccca9afcd30a26bab26279f1bf6724ff":"11863b483809ef88413ca9b0084ac4a5390640af"

PSA hash finish: SHA-224 Test Vector NIST CAVS #1
depends_on:PSA_WANT_ALG_SHA_224
hash_finish:PSA_ALG_SHA_224:"":"d14a028c2a3a2bc9476102bb288234c415a2b01f828ea62ac5b3e42f"

PSA hash finish: SHA-224 Test Vector NIST CAVS #2
depends_on:PSA_WANT_ALG_SHA_224
hash_finish:PSA_ALG_SHA_224:"ff":"e33f9d75e6ae1369dbabf81b96b4591ae46bba30b591a6b6c62542b5"

PSA hash finish: SHA-224 Test Vector NIST CAVS #3
depends_on:PSA_WANT_ALG_SHA_224
hash_finish:PSA_ALG_SHA_224:"984c":"2fa9df9157d9e027cfbc4c6a9df32e1adc0cbe2328ec2a63c5ae934e"

PSA hash finish: SHA-224 Test Vector NIST CAVS #4
depends_on:PSA_WANT_ALG_SHA_224
hash_finish:PSA_ALG_SHA_224:"50efd0":"b5a9820413c2bf8211fbbf5df1337043b32fa4eafaf61a0c8e9ccede"

PSA hash finish: SHA-224 Test Vector NIST CAVS #5
depends_on:PSA_WANT_ALG_SHA_224
hash_finish:PSA_ALG_SHA_224:"e5e09924":"fd19e74690d291467ce59f077df311638f1c3a46e510d0e49a67062d"

PSA hash finish: SHA-224 Test Vector NIST CAVS #6
depends_on:PSA_WANT_ALG_SHA_224
hash_finish:PSA_ALG_SHA_224:"21ebecb914":"78f4a71c21c694499ce1c7866611b14ace70d905012c356323c7c713"

PSA hash finish: SHA-224 Test Vector NIST CAVS #7
depends_on:PSA_WANT_ALG_SHA_224
hash_finish:PSA_ALG_SHA_224:"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":"1302149d1e197c41813b054c942329d420e366530f5517b470e964fe"

PSA hash finish: SHA-256 Test Vector NIST CAVS #1
depends_on:PSA_WANT_ALG_SHA_256
hash_finish:PSA_ALG_SHA_256:"":"e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855"

PSA hash finish: SHA-256 Test Vector NIST CAVS #2
depends_on:PSA_WANT_ALG_SHA_256
hash_finish:PSA_ALG_SHA_256:"bd":"68325720aabd7c82f30f554b313d0570c95accbb7dc4b5aae11204c08ffe732b"

PSA hash finish: SHA-256 Test Vector NIST CAVS #3
depends_on:PSA_WANT_ALG_SHA_256
hash_finish:PSA_ALG_SHA_256:"5fd4":"7c4fbf484498d21b487b9d61de8914b2eadaf2698712936d47c3ada2558f6788"

PSA hash finish: SHA-256 Test Vector NIST CAVS #4
depends_on:PSA_WANT_ALG_SHA_256
hash_finish:PSA_ALG_SHA_256:"b0bd69":"4096804221093ddccfbf46831490ea63e9e99414858f8d75ff7f642c7ca61803"

PSA hash finish: SHA-256 Test Vector NIST CAVS #5
depends_on:PSA_WANT_ALG_SHA_256
hash_finish:PSA_ALG_SHA_256:"c98c8e55":"7abc22c0ae5af26ce93dbb94433a0e0b2e119d014f8e7f65bd56c61ccccd9504"

PSA hash finish: SHA-256 Test Vector NIST CAVS #6
depends_on:PSA_WANT_ALG_SHA_256
hash_finish:PSA_ALG_SHA_256:"81a723d966":"7516fb8bb11350df2bf386bc3c33bd0f52cb4c67c6e4745e0488e62c2aea2605"

PSA hash finish: SHA-256 Test Vector NIST CAVS #7
depends_on:PSA_WANT_ALG_SHA_256
hash_finish:PSA_ALG_SHA_256:"8390cf0be07661cc7669aac54ce09a37733a629d45f5d983ef201f9b2d13800e555d9b1097fec3b783d7a50dcb5e2b644b96a1e9463f177cf34906bf388f366db5c2deee04a30e283f764a97c3b377a034fefc22c259214faa99babaff160ab0aaa7e2ccb0ce09c6b32fe08cbc474694375aba703fadbfa31cf685b30a11c57f3cf4edd321e57d3ae6ebb1133c8260e75b9224fa47a2bb205249add2e2e62f817491482ae152322be0900355cdcc8d42a98f82e961a0dc6f537b7b410eff105f59673bfb787bf042aa071f7af68d944d27371c64160fe9382772372516c230c1f45c0d6b6cca7f274b394da9402d3eafdf733994ec58ab22d71829a98399574d4b5908a447a5a681cb0dd50a31145311d92c22a16de1ead66a5499f2dceb4cae694772ce90762ef8336afec653aa9b1a1c4820b221136dfce80dce2ba920d88a530c9410d0a4e0358a3a11052e58dd73b0b179ef8f56fe3b5a2d117a73a0c38a1392b6938e9782e0d86456ee4884e3c39d4d75813f13633bc79baa07c0d2d555afbf207f52b7dca126d015aa2b9873b3eb065e90b9b065a5373fe1fb1b20d594327d19fba56cb81e7b6696605ffa56eba3c27a438697cc21b201fd7e09f18deea1b3ea2f0d1edc02df0e20396a145412cd6b13c32d2e605641c948b714aec30c0649dc44143511f35ab0fd5dd64c34d06fe86f3836dfe9edeb7f08cfc3bd40956826356242191f99f53473f32b0cc0cf9321d6c92a112e8db90b86ee9e87cc32d0343db01e32ce9eb782cb24efbbbeb440fe929e8f2bf8dfb1550a3a2e742e8b455a3e5730e9e6a7a9824d17acc0f72a7f67eae0f0970f8bde46dcdefaed3047cf807e7f00a42e5fd11d40f5e98533d7574425b7d2bc3b3845c443008b58980e768e464e17cc6f6b3939eee52f713963d07d8c4abf02448ef0b889c9671e2f8a436ddeeffcca7176e9bf9d1005ecd377f2fa67c23ed1f137e60bf46018a8bd613d038e883704fc26e798969df35ec7bbc6a4fe46d8910bd82fa3cded265d0a3b6d399e4251e4d8233daa21b5812fded6536198ff13aa5a1cd46a5b9a17a4ddc1d9f85544d1d1cc16f3df858038c8e071a11a7e157a85a6a8dc47e88d75e7009a8b26fdb73f33a2a70f1e0c259f8f9533b9b8f9af9288b7274f21baeec78d396f8bacdcc22471207d9b4efccd3fedc5c5a2214ff5e51c553f35e21ae696fe51e8df733a8e06f50f419e599e9f9e4b37ce643fc810faaa47989771509d69a110ac916261427026369a21263ac4460fb4f708f8ae28599856db7cb6a43ac8e03d64a9609807e76c5f312b9d1863bfa304e8953647648b4f4ab0ed995e":"4109cdbec3240ad74cc6c37f39300f70fede16e21efc77f7865998714aad0b5e"

PSA hash finish: SHA-384 Test Vector NIST CAVS #1
depends_on:PSA_WANT_ALG_SHA_384
hash_finish:PSA_ALG_SHA_384:"":"38b060a751ac96384cd9327eb1b1e36a21fdb71114be07434c0cc7bf63f6e1da274edebfe76f65fbd51ad2f14898b95b"

PSA hash finish: SHA-384 Test Vector NIST CAVS #2
depends_on:PSA_WANT_ALG_SHA_384
hash_finish:PSA_ALG_SHA_384:"ab":"fb94d5be118865f6fcbc978b825da82cff188faec2f66cb84b2537d74b4938469854b0ca89e66fa2e182834736629f3d"

PSA hash finish: SHA-384 Test Vector NIST CAVS #3
depends_on:PSA_WANT_ALG_SHA_384
hash_finish:PSA_ALG_SHA_384:"7c27":"3d80be467df86d63abb9ea1d3f9cb39cd19890e7f2c53a6200bedc5006842b35e820dc4e0ca90ca9b97ab23ef07080fc"

PSA hash finish: SHA-384 Test Vector NIST CAVS #4
depends_on:PSA_WANT_ALG_SHA_384
hash_finish:PSA_ALG_SHA_384:"31f5ca":"78d54b943421fdf7ba90a7fb9637c2073aa480454bd841d39ff72f4511fc21fb67797b652c0c823229342873d3bef955"

PSA hash finish: SHA-384 Test Vector NIST CAVS #5
depends_on:PSA_WANT_ALG_SHA_384
hash_finish:PSA_ALG_SHA_384:"7bdee3f8":"8bdafba0777ee446c3431c2d7b1fbb631089f71d2ca417abc1d230e1aba64ec2f1c187474a6f4077d372c14ad407f99a"

PSA hash finish: SHA-384 Test Vector NIST CAVS #6
depends_on:PSA_WANT_ALG_SHA_384
hash_finish:PSA_ALG_SHA_384:"8f05604915":"504e414bf1db1060f14c8c799e25b1e0c4dcf1504ebbd129998f0ae283e6de86e0d3c7e879c73ec3b1836c3ee89c2649"

PSA hash finish: SHA-384 Test Vector NIST CAVS #7
depends_on:PSA_WANT_ALG_SHA_384
hash_finish:PSA_ALG_SHA_384:"665da6eda214":"4c022f112010908848312f8b8f1072625fd5c105399d562ea1d56130619a7eac8dfc3748fd05ee37e4b690be9daa9980"

PSA hash finish: SHA-384 Test Vector NIST CAVS #8
depends_on:PSA_WANT_ALG_SHA_384
hash_finish:PSA_ALG_SHA_384:"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":"cba9e3eb12a6f83db11e8a6ff40d1049854ee094416bc527fea931d8585428a8ed6242ce81f6769b36e2123a5c23483e"

PSA hash finish: SHA-512 Test Vector NIST CAVS #1
depends_on:PSA_WANT_ALG_SHA_512
hash_finish:PSA_ALG_SHA_512:"":"cf83e1357eefb8bdf1542850d66d8007d620e4050b5715dc83f4a921d36ce9ce47d0d13c5d85f2b0ff8318d2877eec2f63b931bd47417a81a538327af927da3e"

PSA hash finish: SHA-512 Test Vector NIST CAVS #2
depends_on:PSA_WANT_ALG_SHA_512
hash_finish:PSA_ALG_SHA_512:"8f":"e4cd2d19931b5aad9c920f45f56f6ce34e3d38c6d319a6e11d0588ab8b838576d6ce6d68eea7c830de66e2bd96458bfa7aafbcbec981d4ed040498c3dd95f22a"

PSA hash finish: SHA-512 Test Vector NIST CAVS #3
depends_on:PSA_WANT_ALG_SHA_512
hash_finish:PSA_ALG_SHA_512:"e724":"7dbb520221a70287b23dbcf62bfc1b73136d858e86266732a7fffa875ecaa2c1b8f673b5c065d360c563a7b9539349f5f59bef8c0c593f9587e3cd50bb26a231"

PSA hash finish: SHA-512 Test Vector NIST CAVS #4
depends_on:PSA_WANT_ALG_SHA_512
hash_finish:PSA_ALG_SHA_512:"de4c90":"33ce98281045a5c4c9df0363d8196f1d7dfcd5ee46ac89776fd8a4344c12f123a66788af5bd41ceff1941aa5637654b4064c88c14e00465ab79a2fc6c97e1014"

PSA hash finish: SHA-512 Test Vector NIST CAVS #5
depends_on:PSA_WANT_ALG_SHA_512
hash_finish:PSA_ALG_SHA_512:"a801e94b":"dadb1b5a27f9fece8d86adb2a51879beb1787ff28f4e8ce162cad7fee0f942efcabbf738bc6f797fc7cc79a3a75048cd4c82ca0757a324695bfb19a557e56e2f"

PSA hash finish: SHA-512 Test Vector NIST CAVS #6
depends_on:PSA_WANT_ALG_SHA_512
hash_finish:PSA_ALG_SHA_512:"94390d3502":"b6175c4c4cccf69e0ce5f0312010886ea6b34d43673f942ae42483f9cbb7da817de4e11b5d58e25a3d9bd721a22cdffe1c40411cc45df1911fa5506129b69297"

PSA hash finish: SHA-512 Test Vector NIST CAVS #7
depends_on:PSA_WANT_ALG_SHA_512
hash_finish:PSA_ALG_SHA_512:"49297dd63e5f":"1fcc1e6f6870859d11649f5e5336a9cd16329c029baf04d5a6edf257889a2e9522b497dd656bb402da461307c4ee382e2e89380c8e6e6e7697f1e439f650fa94"

PSA hash finish: SHA-512 Test Vector NIST CAVS #8
depends_on:PSA_WANT_ALG_SHA_512
hash_finish:PSA_ALG_SHA_512:"990d1ae71a62d7bda9bfdaa1762a68d296eee72a4cd946f287a898fbabc002ea941fd8d4d991030b4d27a637cce501a834bb95eab1b7889a3e784c7968e67cbf552006b206b68f76d9191327524fcc251aeb56af483d10b4e0c6c5e599ee8c0fe4faeca8293844a8547c6a9a90d093f2526873a19ad4a5e776794c68c742fb834793d2dfcb7fea46c63af4b70fd11cb6e41834e72ee40edb067b292a794990c288d5007e73f349fb383af6a756b8301ad6e5e0aa8cd614399bb3a452376b1575afa6bdaeaafc286cb064bb91edef97c632b6c1113d107fa93a0905098a105043c2f05397f702514439a08a9e5ddc196100721d45c8fc17d2ed659376f8a00bd5cb9a0860e26d8a29d8d6aaf52de97e9346033d6db501a35dbbaf97c20b830cd2d18c2532f3a59cc497ee64c0e57d8d060e5069b28d86edf1adcf59144b221ce3ddaef134b3124fbc7dd000240eff0f5f5f41e83cd7f5bb37c9ae21953fe302b0f6e8b68fa91c6ab99265c64b2fd9cd4942be04321bb5d6d71932376c6f2f88e02422ba6a5e2cb765df93fd5dd0728c6abdaf03bce22e0678a544e2c3636f741b6f4447ee58a8fc656b43ef817932176adbfc2e04b2c812c273cd6cbfa4098f0be036a34221fa02643f5ee2e0b38135f2a18ecd2f16ebc45f8eb31b8ab967a1567ee016904188910861ca1fa205c7adaa194b286893ffe2f4fbe0384c2aef72a4522aeafd3ebc71f9db71eeeef86c48394a1c86d5b36c352cc33a0a2c800bc99e62fd65b3a2fd69e0b53996ec13d8ce483ce9319efd9a85acefabdb5342226febb83fd1daf4b24265f50c61c6de74077ef89b6fecf9f29a1f871af1e9f89b2d345cda7499bd45c42fa5d195a1e1a6ba84851889e730da3b2b916e96152ae0c92154b49719841db7e7cc707ba8a5d7b101eb4ac7b629bb327817910fff61580b59aab78182d1a2e33473d05b00b170b29e331870826cfe45af206aa7d0246bbd8566ca7cfb2d3c10bfa1db7dd48dd786036469ce7282093d78b5e1a5b0fc81a54c8ed4ceac1e5305305e78284ac276f5d7862727aff246e17addde50c670028d572cbfc0be2e4f8b2eb28fa68ad7b4c6c2a239c460441bfb5ea049f23b08563b4e47729a59e5986a61a6093dbd54f8c36ebe87edae01f251cb060ad1364ce677d7e8d5a4a4ca966a7241cc360bc2acb280e5f9e9c1b032ad6a180a35e0c5180b9d16d026c865b252098cc1d99ba7375ca31c7702c0d943d5e3dd2f6861fa55bd46d94b67ed3e52eccd8dd06d968e01897d6de97ed3058d91dd":"8e4bc6f8b8c60fe4d68c61d9b159c8693c3151c46749af58da228442d927f23359bd6ccd6c2ec8fa3f00a86cecbfa728e1ad60b821ed22fcd309ba91a4138bc9"

PSA hash finish: SHA3-224 Test Vector NIST ""
depends_on:PSA_WANT_ALG_SHA3_224
hash_finish:PSA_ALG_SHA3_224:"":"6b4e03423667dbb73b6e15454f0eb1abd4597f9a1b078e3f5b5a6bc7"

PSA hash finish: SHA3-256 Test Vector NIST ""
depends_on:PSA_WANT_ALG_SHA3_256
hash_finish:PSA_ALG_SHA3_256:"":"a7ffc6f8bf1ed76651c14756a061d662f580ff4de43b49fa82d80a4b80f8434a"

PSA hash finish: SHA3-384 Test Vector NIST ""
depends_on:PSA_WANT_ALG_SHA3_384
hash_finish:PSA_ALG_SHA3_384:"":"0c63a75b845e4f7d01107d852e4c2485c51a50aaaa94fc61995e71bbee983a2ac3713831264adb47fb6bd1e058d5f004"

PSA hash finish: SHA3-512 Test Vector NIST ""
depends_on:PSA_WANT_ALG_SHA3_512
hash_finish:PSA_ALG_SHA3_512:"":"a69f73cca23a9ac5c8b567dc185a756e97c982164fe25859e0d1dcc1475c80a615b2123af1f5f94c11e3e9402c3ac558f500199d95b6d3e301758586281dcd26"

PSA hash finish: SHA3-224 Test Vector NIST "abc"
depends_on:PSA_WANT_ALG_SHA3_224
hash_finish:PSA_ALG_SHA3_224:"616263":"e642824c3f8cf24ad09234ee7d3c766fc9a3a5168d0c94ad73b46fdf"

PSA hash finish: SHA3-256 Test Vector NIST "abc"
depends_on:PSA_WANT_ALG_SHA3_256
hash_finish:PSA_ALG_SHA3_256:"616263":"3a985da74fe225b2045c172d6bd390bd855f086e3e9d525b46bfe24511431532"

PSA hash finish: SHA3-384 Test Vector NIST "abc"
depends_on:PSA_WANT_ALG_SHA3_384
hash_finish:PSA_ALG_SHA3_384:"616263":"ec01498288516fc926459f58e2c6ad8df9b473cb0fc08c2596da7cf0e49be4b298d88cea927ac7f539f1edf228376d25"

PSA hash finish: SHA3-512 Test Vector NIST "abc"
depends_on:PSA_WANT_ALG_SHA3_512
hash_finish:PSA_ALG_SHA3_512:"616263":"b751850b1a57168a5693cd924b6b096e08f621827444f70d884f5d0240d2712e10e116e9192af3c91a7ec57647e3934057340b4cf408d5a56592f8274eec53f0"

PSA hash finish: SHA3-224 Test Vector NIST 448 bits: "abcdbcdecdefdefgefghfghighijhijkijkljklmklmnlmnomnopnopq"
depends_on:PSA_WANT_ALG_SHA3_224
hash_finish:PSA_ALG_SHA3_224:"6162636462636465636465666465666765666768666768696768696a68696a6b696a6b6c6a6b6c6d6b6c6d6e6c6d6e6f6d6e6f706e6f7071":"8a24108b154ada21c9fd5574494479ba5c7e7ab76ef264ead0fcce33"

PSA hash finish: SHA3-256 Test Vector NIST 448 bits: "abcdbcdecdefdefgefghfghighijhijkijkljklmklmnlmnomnopnopq"
depends_on:PSA_WANT_ALG_SHA3_256
hash_finish:PSA_ALG_SHA3_256:"6162636462636465636465666465666765666768666768696768696a68696a6b696a6b6c6a6b6c6d6b6c6d6e6c6d6e6f6d6e6f706e6f7071":"41c0dba2a9d6240849100376a8235e2c82e1b9998a999e21db32dd97496d3376"

PSA hash finish: SHA3-384 Test Vector NIST 448 bits: "abcdbcdecdefdefgefghfghighijhijkijkljklmklmnlmnomnopnopq"
depends_on:PSA_WANT_ALG_SHA3_384
hash_finish:PSA_ALG_SHA3_384:"6162636462636465636465666465666765666768666768696768696a68696a6b696a6b6c6a6b6c6d6b6c6d6e6c6d6e6f6d6e6f706e6f7071":"991c665755eb3a4b6bbdfb75c78a492e8c56a22c5c4d7e429bfdbc32b9d4ad5aa04a1f076e62fea19eef51acd0657c22"

PSA hash finish: SHA3-512 Test Vector NIST 448 bits: "abcdbcdecdefdefgefghfghighijhijkijkljklmklmnlmnomnopnopq"
depends_on:PSA_WANT_ALG_SHA3_512
hash_finish:PSA_ALG_SHA3_512:"6162636462636465636465666465666765666768666768696768696a68696a6b696a6b6c6a6b6c6d6b6c6d6e6c6d6e6f6d6e6f706e6f7071":"04a371e84ecfb5b8b77cb48610fca8182dd457ce6f326a0fd3d7ec2f1e91636dee691fbe0c985302ba1b0d8dc78c086346b533b49c030d99a27daf1139d6e75e"

PSA hash finish: SHA3-224 Test Vector NIST 896 bits: "abcdefghbcdefghicdefghijdefghijkefghijklfghijklmghijklmnhijklmnoijklmnopjklmnopqklmnopqrlmnopqrsmnopqrstnopqrstu"
depends_on:PSA_WANT_ALG_SHA3_224
hash_finish:PSA_ALG_SHA3_224:"61626364656667686263646566676869636465666768696a6465666768696a6b65666768696a6b6c666768696a6b6c6d6768696a6b6c6d6e68696a6b6c6d6e6f696a6b6c6d6e6f706a6b6c6d6e6f70716b6c6d6e6f7071726c6d6e6f707172736d6e6f70717273746e6f707172737475":"543e6868e1666c1a643630df77367ae5a62a85070a51c14cbf665cbc"

PSA hash finish: SHA3-256 Test Vector NIST 896 bits: "abcdefghbcdefghicdefghijdefghijkefghijklfghijklmghijklmnhijklmnoijklmnopjklmnopqklmnopqrlmnopqrsmnopqrstnopqrstu"
depends_on:PSA_WANT_ALG_SHA3_256
hash_finish:PSA_ALG_SHA3_256:"61626364656667686263646566676869636465666768696a6465666768696a6b65666768696a6b6c666768696a6b6c6d6768696a6b6c6d6e68696a6b6c6d6e6f696a6b6c6d6e6f706a6b6c6d6e6f70716b6c6d6e6f7071726c6d6e6f707172736d6e6f70717273746e6f707172737475":"916f6061fe879741ca6469b43971dfdb28b1a32dc36cb3254e812be27aad1d18"

PSA hash finish: SHA3-384 Test Vector NIST 896 bits: "abcdefghbcdefghicdefghijdefghijkefghijklfghijklmghijklmnhijklmnoijklmnopjklmnopqklmnopqrlmnopqrsmnopqrstnopqrstu"
depends_on:PSA_WANT_ALG_SHA3_384
hash_finish:PSA_ALG_SHA3_384:"61626364656667686263646566676869636465666768696a6465666768696a6b65666768696a6b6c666768696a6b6c6d6768696a6b6c6d6e68696a6b6c6d6e6f696a6b6c6d6e6f706a6b6c6d6e6f70716b6c6d6e6f7071726c6d6e6f707172736d6e6f70717273746e6f707172737475":"79407d3b5916b59c3e30b09822974791c313fb9ecc849e406f23592d04f625dc8c709b98b43b3852b337216179aa7fc7"

PSA hash finish: SHA3-512 Test Vector NIST 896 bits: "abcdefghbcdefghicdefghijdefghijkefghijklfghijklmghijklmnhijklmnoijklmnopjklmnopqklmnopqrlmnopqrsmnopqrstnopqrstu"
depends_on:PSA_WANT_ALG_SHA3_512
hash_finish:PSA_ALG_SHA3_512:"61626364656667686263646566676869636465666768696a6465666768696a6b65666768696a6b6c666768696a6b6c6d6768696a6b6c6d6e68696a6b6c6d6e6f696a6b6c6d6e6f706a6b6c6d6e6f70716b6c6d6e6f7071726c6d6e6f707172736d6e6f70717273746e6f707172737475":"afebb2ef542e6579c50cad06d2e578f9f8dd6881d7dc824d26360feebf18a4fa73e3261122948efcfd492e74e82e2189ed0fb440d187f382270cb455f21dd185"

PSA hash finish: MD5 Test vector RFC1321 #1
depends_on:PSA_WANT_ALG_MD5
hash_finish:PSA_ALG_MD5:"":"d41d8cd98f00b204e9800998ecf8427e"

PSA hash finish: MD5 Test vector RFC1321 #2
depends_on:PSA_WANT_ALG_MD5
hash_finish:PSA_ALG_MD5:"61":"0cc175b9c0f1b6a831c399e269772661"

PSA hash finish: MD5 Test vector RFC1321 #3
depends_on:PSA_WANT_ALG_MD5
hash_finish:PSA_ALG_MD5:"616263":"900150983cd24fb0d6963f7d28e17f72"

PSA hash finish: MD5 Test vector RFC1321 #4
depends_on:PSA_WANT_ALG_MD5
hash_finish:PSA_ALG_MD5:"6d65737361676520646967657374":"f96b697d7cb7938d525a2f31aaf161d0"

PSA hash finish: MD5 Test vector RFC1321 #5
depends_on:PSA_WANT_ALG_MD5
hash_finish:PSA_ALG_MD5:"6162636465666768696a6b6c6d6e6f707172737475767778797a":"c3fcd3d76192e4007dfb496cca67e13b"

PSA hash finish: MD5 Test vector RFC1321 #6
depends_on:PSA_WANT_ALG_MD5
hash_finish:PSA_ALG_MD5:"4142434445464748494a4b4c4d4e4f505152535455565758595a6162636465666768696a6b6c6d6e6f707172737475767778797a30313233343536373839":"d174ab98d277d9f5a5611c2c9f419d9f"

PSA hash finish: MD5 Test vector RFC1321 #7
depends_on:PSA_WANT_ALG_MD5
hash_finish:PSA_ALG_MD5:"3132333435363738393031323334353637383930313233343536373839303132333435363738393031323334353637383930313233343536373839303132333435363738393031323334353637383930":"57edf4a22be3c955ac49da2e2107b67a"

PSA hash finish: RIPEMD160 Test vector from paper #1
depends_on:PSA_WANT_ALG_RIPEMD160
hash_finish:PSA_ALG_RIPEMD160:"":"9c1185a5c5e9fc54612808977ee8f548b2258d31"

PSA hash finish: RIPEMD160 Test vector from paper #2
depends_on:PSA_WANT_ALG_RIPEMD160
hash_finish:PSA_ALG_RIPEMD160:"61":"0bdc9d2d256b3ee9daae347be6f4dc835a467ffe"

PSA hash finish: RIPEMD160 Test vector from paper #3
depends_on:PSA_WANT_ALG_RIPEMD160
hash_finish:PSA_ALG_RIPEMD160:"616263":"8eb208f7e05d987a9b044a8e98c6b087f15a0bfc"

PSA hash finish: RIPEMD160 Test vector from paper #4
depends_on:PSA_WANT_ALG_RIPEMD160
hash_finish:PSA_ALG_RIPEMD160:"6d65737361676520646967657374":"5d0689ef49d2fae572b881b123a85ffa21595f36"

PSA hash finish: RIPEMD160 Test vector from paper #5
depends_on:PSA_WANT_ALG_RIPEMD160
hash_finish:PSA_ALG_RIPEMD160:"6162636465666768696a6b6c6d6e6f707172737475767778797a":"f71c27109c692c1b56bbdceb5b9d2865b3708dbc"

PSA hash finish: RIPEMD160 Test vector from paper #6
depends_on:PSA_WANT_ALG_RIPEMD160
hash_finish:PSA_ALG_RIPEMD160:"6162636462636465636465666465666765666768666768696768696a68696a6b696a6b6c6a6b6c6d6b6c6d6e6c6d6e6f6d6e6f706e6f7071":"12a053384a9c0c88e405a06c27dcf49ada62eb2b"

PSA hash finish: RIPEMD160 Test vector from paper #7
depends_on:PSA_WANT_ALG_RIPEMD160
hash_finish:PSA_ALG_RIPEMD160:"4142434445464748494a4b4c4d4e4f505152535455565758595a6162636465666768696a6b6c6d6e6f707172737475767778797a30313233343536373839":"b0e20b6e3116640286ed3a87a5713079b21f5189"

PSA hash finish: RIPEMD160 Test vector from paper #8
depends_on:PSA_WANT_ALG_RIPEMD160
hash_finish:PSA_ALG_RIPEMD160:"3132333435363738393031323334353637383930313233343536373839303132333435363738393031323334353637383930313233343536373839303132333435363738393031323334353637383930":"9b752e45573d4b39f4dbd3323cab82bf63326bfb"

PSA hash verify: SHA-1
depends_on:PSA_WANT_ALG_SHA_1
hash_verify:PSA_ALG_SHA_1:"bd":"9034aaf45143996a2b14465c352ab0c6fa26b221"

PSA hash verify: SHA-224
depends_on:PSA_WANT_ALG_SHA_224
hash_verify:PSA_ALG_SHA_224:"bd":"b1e46bb9efe45af554363449c6945a0d6169fc3a5a396a56cb97cb57"

PSA hash verify: SHA-256
depends_on:PSA_WANT_ALG_SHA_256
hash_verify:PSA_ALG_SHA_256:"bd":"68325720aabd7c82f30f554b313d0570c95accbb7dc4b5aae11204c08ffe732b"

PSA hash verify: SHA-384
depends_on:PSA_WANT_ALG_SHA_384
hash_verify:PSA_ALG_SHA_384:"bd":"4372e38a92a28b5d2c391e62452a86d50e0267228be176c77d2402effe9fa50de407bbb851b37d5904aba2dede74da2a"

PSA hash verify: SHA-512
depends_on:PSA_WANT_ALG_SHA_512
hash_verify:PSA_ALG_SHA_512:"bd":"296e2267d74c278daaaa940d17b0cfb74a5083f8e069726d8c841cbe596e0431cb7741a5b50f71666cfd54bacb7b00aea891499cf4ef6a03c8a83fe37c3f7baf"

PSA hash verify: MD5
depends_on:PSA_WANT_ALG_MD5
hash_verify:PSA_ALG_MD5:"bd":"abae57cb562ecf295b4a37a76efe61fb"

PSA hash verify: RIPEMD160
depends_on:PSA_WANT_ALG_RIPEMD160
hash_verify:PSA_ALG_RIPEMD160:"bd":"5089265ee5d9af75d12dbf7ea2f27dbdee435b37"

PSA hash verify: SHA3-224
depends_on:PSA_WANT_ALG_SHA3_224
hash_verify:PSA_ALG_SHA3_224:"bd":"79bd1a58a357d1a0ac15b43400bc396d17bdc6d3d90369f16f650b25"

PSA hash verify: SHA3-256
depends_on:PSA_WANT_ALG_SHA3_256
hash_verify:PSA_ALG_SHA3_256:"bd":"b389fa0f45f21196cc2736e8de396497a2414be31e7a500a499918b8cf3257b2"

PSA hash verify: SHA3-384
depends_on:PSA_WANT_ALG_SHA3_384
hash_verify:PSA_ALG_SHA3_384:"bd":"5a337b67965736040c5b1f2d4df7f9ca76cf01866c7d64ed8dd812b97995da9b14ef07f9c4d9190888e4b15c4df2203d"

PSA hash verify: SHA3-512
depends_on:PSA_WANT_ALG_SHA3_512
hash_verify:PSA_ALG_SHA3_512:"bd":"72bacd82495cb72a44523cda462f0f02c9f33b6312e24e44f5c40deed2bbc37854b606cb2f62cce6a394b4157d8e6e89b22682380dc129dddd402693ffa98a6c"

PSA hash multi part: SHA-1 Test Vector NIST CAVS #1
depends_on:PSA_WANT_ALG_SHA_1
hash_multi_part:PSA_ALG_SHA_1:"":"da39a3ee5e6b4b0d3255bfef95601890afd80709"

PSA hash multi part: SHA-1 Test Vector NIST CAVS #2
depends_on:PSA_WANT_ALG_SHA_1
hash_multi_part:PSA_ALG_SHA_1:"a8":"99f2aa95e36f95c2acb0eaf23998f030638f3f15"

PSA hash multi part: SHA-1 Test Vector NIST CAVS #3
depends_on:PSA_WANT_ALG_SHA_1
hash_multi_part:PSA_ALG_SHA_1:"3000":"f944dcd635f9801f7ac90a407fbc479964dec024"

PSA hash multi part: SHA-1 Test Vector NIST CAVS #4
depends_on:PSA_WANT_ALG_SHA_1
hash_multi_part:PSA_ALG_SHA_1:"42749e":"a444319e9b6cc1e8464c511ec0969c37d6bb2619"

PSA hash multi part: SHA-1 Test Vector NIST CAVS #5
depends_on:PSA_WANT_ALG_SHA_1
hash_multi_part:PSA_ALG_SHA_1:"9fc3fe08":"16a0ff84fcc156fd5d3ca3a744f20a232d172253"

PSA hash multi part: SHA-1 Test Vector NIST CAVS #6
depends_on:PSA_WANT_ALG_SHA_1
hash_multi_part:PSA_ALG_SHA_1:"b5c1c6f1af":"fec9deebfcdedaf66dda525e1be43597a73a1f93"

PSA hash multi part: SHA-1 Test Vector NIST CAVS #7
depends_on:PSA_WANT_ALG_SHA_1
hash_multi_part:PSA_ALG_SHA_1:"ec29561244ede706b6eb30a1c371d74450a105c3f9735f7fa9fe38cf67f304a5736a106e92e17139a6813b1c81a4f3d3fb9546ab4296fa9f722826c066869edacd73b2548035185813e22634a9da44000d95a281ff9f264ecce0a931222162d021cca28db5f3c2aa24945ab1e31cb413ae29810fd794cad5dfaf29ec43cb38d198fe4ae1da2359780221405bd6712a5305da4b1b737fce7cd21c0eb7728d08235a9011":"970111c4e77bcc88cc20459c02b69b4aa8f58217"

PSA hash multi part: SHA-1 Test Vector NIST CAVS #8
depends_on:PSA_WANT_ALG_SHA_1
hash_multi_part:PSA_ALG_SHA_1:"5fc2c3f6a7e79dc94be526e5166a238899d54927ce470018fbfd668fd9dd97cbf64e2c91584d01da63be3cc9fdff8adfefc3ac728e1e335b9cdc87f069172e323d094b47fa1e652afe4d6aa147a9f46fda33cacb65f3aa12234746b9007a8c85fe982afed7815221e43dba553d8fe8a022cdac1b99eeeea359e5a9d2e72e382dffa6d19f359f4f27dc3434cd27daeeda8e38594873398678065fbb23665aba9309d946135da0e4a4afdadff14db18e85e71dd93c3bf9faf7f25c8194c4269b1ee3d9934097ab990025d9c3aaf63d5109f52335dd3959d38ae485050e4bbb6235574fc0102be8f7a306d6e8de6ba6becf80f37415b57f9898a5824e77414197422be3d36a6080":"0423dc76a8791107d14e13f5265b343f24cc0f19"

PSA hash multi part: SHA-1 Test Vector NIST CAVS #9
depends_on:PSA_WANT_ALG_SHA_1
hash_multi_part:PSA_ALG_SHA_1:"0f865f46a8f3aed2da18482aa09a8f390dc9da07d51d1bd10fe0bf5f3928d5927d08733d32075535a6d1c8ac1b2dc6ba0f2f633dc1af68e3f0fa3d85e6c60cb7b56c239dc1519a007ea536a07b518ecca02a6c31b46b76f021620ef3fc6976804018380e5ab9c558ebfc5cb1c9ed2d974722bf8ab6398f1f2b82fa5083f85c16a5767a3a07271d67743f00850ce8ec428c7f22f1cf01f99895c0c844845b06a06cecb0c6cf83eb55a1d4ebc44c2c13f6f7aa5e0e08abfd84e7864279057abc471ee4a45dbbb5774afa24e51791a0eada11093b88681fe30baa3b2e94113dc63342c51ca5d1a6096d0897b626e42cb91761058008f746f35465465540ad8c6b8b60f7e1461b3ce9e6529625984cb8c7d46f07f735be067588a0117f23e34ff57800e2bbe9a1605fde6087fb15d22c5d3ac47566b8c448b0cee40373e5ba6eaa21abee71366afbb27dbbd300477d70c371e7b8963812f5ed4fb784fb2f3bd1d3afe883cdd47ef32beaea":"6692a71d73e00f27df976bc56df4970650d90e45"

PSA hash multi part: SHA-1 Test Vector NIST CAVS #10
depends_on:PSA_WANT_ALG_SHA_1
hash_multi_part:PSA_ALG_SHA_1:"8236153781bd2f1b81ffe0def1beb46f5a70191142926651503f1b3bb1016acdb9e7f7acced8dd168226f118ff664a01a8800116fd023587bfba52a2558393476f5fc69ce9c65001f23e70476d2cc81c97ea19caeb194e224339bcb23f77a83feac5096f9b3090c51a6ee6d204b735aa71d7e996d380b80822e4dfd43683af9c7442498cacbea64842dfda238cb099927c6efae07fdf7b23a4e4456e0152b24853fe0d5de4179974b2b9d4a1cdbefcbc01d8d311b5dda059136176ea698ab82acf20dd490be47130b1235cb48f8a6710473cfc923e222d94b582f9ae36d4ca2a32d141b8e8cc36638845fbc499bce17698c3fecae2572dbbd470552430d7ef30c238c2124478f1f780483839b4fb73d63a9460206824a5b6b65315b21e3c2f24c97ee7c0e78faad3df549c7ca8ef241876d9aafe9a309f6da352bec2caaa92ee8dca392899ba67dfed90aef33d41fc2494b765cb3e2422c8e595dabbfaca217757453fb322a13203f425f6073a9903e2dc5818ee1da737afc345f0057744e3a56e1681c949eb12273a3bfc20699e423b96e44bd1ff62e50a848a890809bfe1611c6787d3d741103308f849a790f9c015098286dbacfc34c1718b2c2b77e32194a75dda37954a320fa68764027852855a7e5b5274eb1e2cbcd27161d98b59ad245822015f48af82a45c0ed59be94f9af03d9736048570d6e3ef63b1770bc98dfb77de84b1bb1708d872b625d9ab9b06c18e5dbbf34399391f0f8aa26ec0dac7ff4cb8ec97b52bcb942fa6db2385dcd1b3b9d567aaeb425d567b0ebe267235651a1ed9bf78fd93d3c1dd077fe340bb04b00529c58f45124b717c168d07e9826e33376988bc5cf62845c2009980a4dfa69fbc7e5a0b1bb20a5958ca967aec68eb31dd8fccca9afcd30a26bab26279f1bf6724ff":"11863b483809ef88413ca9b0084ac4a5390640af"

PSA hash multi part: SHA-224 Test Vector NIST CAVS #1
depends_on:PSA_WANT_ALG_SHA_224
hash_multi_part:PSA_ALG_SHA_224:"":"d14a028c2a3a2bc9476102bb288234c415a2b01f828ea62ac5b3e42f"

PSA hash multi part: SHA-224 Test Vector NIST CAVS #2
depends_on:PSA_WANT_ALG_SHA_224
hash_multi_part:PSA_ALG_SHA_224:"ff":"e33f9d75e6ae1369dbabf81b96b4591ae46bba30b591a6b6c62542b5"

PSA hash multi part: SHA-224 Test Vector NIST CAVS #3
depends_on:PSA_WANT_ALG_SHA_224
hash_multi_part:PSA_ALG_SHA_224:"984c":"2fa9df9157d9e027cfbc4c6a9df32e1adc0cbe2328ec2a63c5ae934e"

PSA hash multi part: SHA-224 Test Vector NIST CAVS #4
depends_on:PSA_WANT_ALG_SHA_224
hash_multi_part:PSA_ALG_SHA_224:"50efd0":"b5a9820413c2bf8211fbbf5df1337043b32fa4eafaf61a0c8e9ccede"

PSA hash multi part: SHA-224 Test Vector NIST CAVS #5
depends_on:PSA_WANT_ALG_SHA_224
hash_multi_part:PSA_ALG_SHA_224:"e5e09924":"fd19e74690d291467ce59f077df311638f1c3a46e510d0e49a67062d"

PSA hash multi part: SHA-224 Test Vector NIST CAVS #6
depends_on:PSA_WANT_ALG_SHA_224
hash_multi_part:PSA_ALG_SHA_224:"21ebecb914":"78f4a71c21c694499ce1c7866611b14ace70d905012c356323c7c713"

PSA hash multi part: SHA-224 Test Vector NIST CAVS #7
depends_on:PSA_WANT_ALG_SHA_224
hash_multi_part:PSA_ALG_SHA_224:"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":"1302149d1e197c41813b054c942329d420e366530f5517b470e964fe"

PSA hash multi part: SHA-256 Test Vector NIST CAVS #1
depends_on:PSA_WANT_ALG_SHA_256
hash_multi_part:PSA_ALG_SHA_256:"":"e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855"

PSA hash multi part: SHA-256 Test Vector NIST CAVS #2
depends_on:PSA_WANT_ALG_SHA_256
hash_multi_part:PSA_ALG_SHA_256:"bd":"68325720aabd7c82f30f554b313d0570c95accbb7dc4b5aae11204c08ffe732b"

PSA hash multi part: SHA-256 Test Vector NIST CAVS #3
depends_on:PSA_WANT_ALG_SHA_256
hash_multi_part:PSA_ALG_SHA_256:"5fd4":"7c4fbf484498d21b487b9d61de8914b2eadaf2698712936d47c3ada2558f6788"

PSA hash multi part: SHA-256 Test Vector NIST CAVS #4
depends_on:PSA_WANT_ALG_SHA_256
hash_multi_part:PSA_ALG_SHA_256:"b0bd69":"4096804221093ddccfbf46831490ea63e9e99414858f8d75ff7f642c7ca61803"

PSA hash multi part: SHA-256 Test Vector NIST CAVS #5
depends_on:PSA_WANT_ALG_SHA_256
hash_multi_part:PSA_ALG_SHA_256:"c98c8e55":"7abc22c0ae5af26ce93dbb94433a0e0b2e119d014f8e7f65bd56c61ccccd9504"

PSA hash multi part: SHA-256 Test Vector NIST CAVS #6
depends_on:PSA_WANT_ALG_SHA_256
hash_multi_part:PSA_ALG_SHA_256:"81a723d966":"7516fb8bb11350df2bf386bc3c33bd0f52cb4c67c6e4745e0488e62c2aea2605"

PSA hash multi part: SHA-256 Test Vector NIST CAVS #7
depends_on:PSA_WANT_ALG_SHA_256
hash_multi_part:PSA_ALG_SHA_256:"8390cf0be07661cc7669aac54ce09a37733a629d45f5d983ef201f9b2d13800e555d9b1097fec3b783d7a50dcb5e2b644b96a1e9463f177cf34906bf388f366db5c2deee04a30e283f764a97c3b377a034fefc22c259214faa99babaff160ab0aaa7e2ccb0ce09c6b32fe08cbc474694375aba703fadbfa31cf685b30a11c57f3cf4edd321e57d3ae6ebb1133c8260e75b9224fa47a2bb205249add2e2e62f817491482ae152322be0900355cdcc8d42a98f82e961a0dc6f537b7b410eff105f59673bfb787bf042aa071f7af68d944d27371c64160fe9382772372516c230c1f45c0d6b6cca7f274b394da9402d3eafdf733994ec58ab22d71829a98399574d4b5908a447a5a681cb0dd50a31145311d92c22a16de1ead66a5499f2dceb4cae694772ce90762ef8336afec653aa9b1a1c4820b221136dfce80dce2ba920d88a530c9410d0a4e0358a3a11052e58dd73b0b179ef8f56fe3b5a2d117a73a0c38a1392b6938e9782e0d86456ee4884e3c39d4d75813f13633bc79baa07c0d2d555afbf207f52b7dca126d015aa2b9873b3eb065e90b9b065a5373fe1fb1b20d594327d19fba56cb81e7b6696605ffa56eba3c27a438697cc21b201fd7e09f18deea1b3ea2f0d1edc02df0e20396a145412cd6b13c32d2e605641c948b714aec30c0649dc44143511f35ab0fd5dd64c34d06fe86f3836dfe9edeb7f08cfc3bd40956826356242191f99f53473f32b0cc0cf9321d6c92a112e8db90b86ee9e87cc32d0343db01e32ce9eb782cb24efbbbeb440fe929e8f2bf8dfb1550a3a2e742e8b455a3e5730e9e6a7a9824d17acc0f72a7f67eae0f0970f8bde46dcdefaed3047cf807e7f00a42e5fd11d40f5e98533d7574425b7d2bc3b3845c443008b58980e768e464e17cc6f6b3939eee52f713963d07d8c4abf02448ef0b889c9671e2f8a436ddeeffcca7176e9bf9d1005ecd377f2fa67c23ed1f137e60bf46018a8bd613d038e883704fc26e798969df35ec7bbc6a4fe46d8910bd82fa3cded265d0a3b6d399e4251e4d8233daa21b5812fded6536198ff13aa5a1cd46a5b9a17a4ddc1d9f85544d1d1cc16f3df858038c8e071a11a7e157a85a6a8dc47e88d75e7009a8b26fdb73f33a2a70f1e0c259f8f9533b9b8f9af9288b7274f21baeec78d396f8bacdcc22471207d9b4efccd3fedc5c5a2214ff5e51c553f35e21ae696fe51e8df733a8e06f50f419e599e9f9e4b37ce643fc810faaa47989771509d69a110ac916261427026369a21263ac4460fb4f708f8ae28599856db7cb6a43ac8e03d64a9609807e76c5f312b9d1863bfa304e8953647648b4f4ab0ed995e":"4109cdbec3240ad74cc6c37f39300f70fede16e21efc77f7865998714aad0b5e"

PSA hash multi part: SHA-384 Test Vector NIST CAVS #1
depends_on:PSA_WANT_ALG_SHA_384
hash_multi_part:PSA_ALG_SHA_384:"":"38b060a751ac96384cd9327eb1b1e36a21fdb71114be07434c0cc7bf63f6e1da274edebfe76f65fbd51ad2f14898b95b"

PSA hash multi part: SHA-384 Test Vector NIST CAVS #2
depends_on:PSA_WANT_ALG_SHA_384
hash_multi_part:PSA_ALG_SHA_384:"ab":"fb94d5be118865f6fcbc978b825da82cff188faec2f66cb84b2537d74b4938469854b0ca89e66fa2e182834736629f3d"

PSA hash multi part: SHA-384 Test Vector NIST CAVS #3
depends_on:PSA_WANT_ALG_SHA_384
hash_multi_part:PSA_ALG_SHA_384:"7c27":"3d80be467df86d63abb9ea1d3f9cb39cd19890e7f2c53a6200bedc5006842b35e820dc4e0ca90ca9b97ab23ef07080fc"

PSA hash multi part: SHA-384 Test Vector NIST CAVS #4
depends_on:PSA_WANT_ALG_SHA_384
hash_multi_part:PSA_ALG_SHA_384:"31f5ca":"78d54b943421fdf7ba90a7fb9637c2073aa480454bd841d39ff72f4511fc21fb67797b652c0c823229342873d3bef955"

PSA hash multi part: SHA-384 Test Vector NIST CAVS #5
depends_on:PSA_WANT_ALG_SHA_384
hash_multi_part:PSA_ALG_SHA_384:"7bdee3f8":"8bdafba0777ee446c3431c2d7b1fbb631089f71d2ca417abc1d230e1aba64ec2f1c187474a6f4077d372c14ad407f99a"

PSA hash multi part: SHA-384 Test Vector NIST CAVS #6
depends_on:PSA_WANT_ALG_SHA_384
hash_multi_part:PSA_ALG_SHA_384:"8f05604915":"504e414bf1db1060f14c8c799e25b1e0c4dcf1504ebbd129998f0ae283e6de86e0d3c7e879c73ec3b1836c3ee89c2649"

PSA hash multi part: SHA-384 Test Vector NIST CAVS #7
depends_on:PSA_WANT_ALG_SHA_384
hash_multi_part:PSA_ALG_SHA_384:"665da6eda214":"4c022f112010908848312f8b8f1072625fd5c105399d562ea1d56130619a7eac8dfc3748fd05ee37e4b690be9daa9980"

PSA hash multi part: SHA-384 Test Vector NIST CAVS #8
depends_on:PSA_WANT_ALG_SHA_384
hash_multi_part:PSA_ALG_SHA_384:"7f46ce506d593c4ed53c82edeb602037e0485befbee03f7f930fe532d18ff2a3f5fd6076672c8145a1bf40dd94f7abab47c9ae71c234213d2ad1069c2dac0b0ba15257ae672b8245960ae55bd50315c0097daa3a318745788d70d14706910809ca6e396237fe4934fa46f9ce782d66606d8bd6b2d283b1160513ce9c24e9f084b97891f99d4cdefc169a029e431ca772ba1bba426fce6f01d8e286014e5acc66b799e4db62bd4783322f8a32ff78e0de3957df50ce10871f4e0680df4e8ca3960af9bc6f4efa8eb3962d18f474eb178c3265cc46b8f2ff5ab1a7449fea297dfcfabfa01f28abbb7289bb354b691b5664ec6d098af51be19947ec5ba7ebd66380d1141953ba78d4aa5401679fa7b0a44db1981f864d3535c45afe4c61183d5b0ad51fae71ca07e34240283959f7530a32c70d95a088e501c230059f333b0670825009e7e22103ef22935830df1fac8ef877f5f3426dd54f7d1128dd871ad9a7d088f94c0e8712013295b8d69ae7623b880978c2d3c6ad26dc478f8dc47f5c0adcc618665dc3dc205a9071b2f2191e16cac5bd89bb59148fc719633752303aa08e518dbc389f0a5482caaa4c507b8729a6f3edd061efb39026cecc6399f51971cf7381d605e144a5928c8c2d1ad7467b05da2f202f4f3234e1aff19a0198a28685721c3d2d52311c721e3fdcbaf30214cdc3acff8c433880e104fb63f2df7ce69a97857819ba7ac00ac8eae1969764fde8f68cf8e0916d7e0c151147d4944f99f42ae50f30e1c79a42d2b6c5188d133d3cbbf69094027b354b295ccd0f7dc5a87d73638bd98ebfb00383ca0fa69cb8dcb35a12510e5e07ad8789047d0b63841a1bb928737e8b0a0c33254f47aa8bfbe3341a09c2b76dbcefa67e30df300d34f7b8465c4f869e51b6bcfe6cf68b238359a645036bf7f63f02924e087ce7457e483b6025a859903cb484574aa3b12cf946f32127d537c33bee3141b5db96d10a148c50ae045f287210757710d6846e04b202f79e87dd9a56bc6da15f84a77a7f63935e1dee00309cd276a8e7176cb04da6bb0e9009534438732cb42d008008853d38d19beba46e61006e30f7efd1bc7c2906b024e4ff898a1b58c448d68b43c6ab63f34f85b3ac6aa4475867e51b583844cb23829f4b30f4bdd817d88e2ef3e7b4fc0a624395b05ec5e8686082b24d29fef2b0d3c29e031d5f94f504b1d3df9361eb5ffbadb242e66c39a8094cfe62f85f639f3fd65fc8ae0c74a8f4c6e1d070b9183a434c722caaa0225f8bcd68614d6f0738ed62f8484ec96077d155c08e26c46be262a73e3551698bd70d8d5610cf37c4c306eed04ba6a040a9c3e6d7e15e8acda17f477c2484cf5c56b813313927be8387b1024f995e98fc87f1029091c01424bdc2b296c2eadb7d25b3e762a2fd0c2dcd1727ddf91db97c5984305265f3695a7f5472f2d72c94d68c27914f14f82aa8dd5fe4e2348b0ca967a3f98626a091552f5d0ffa2bf10350d23c996256c01fdeffb2c2c612519869f877e4929c6e95ff15040f1485e22ed14119880232fef3b57b3848f15b1766a5552879df8f06":"cba9e3eb12a6f83db11e8a6ff40d1049854ee094416bc527fea931d8585428a8ed6242ce81f6769b36e2123a5c23483e"

PSA hash multi part: SHA-512 Test Vector NIST CAVS #1
depends_on:PSA_WANT_ALG_SHA_512
hash_multi_part:PSA_ALG_SHA_512:"":"cf83e1357eefb8bdf1542850d66d8007d620e4050b5715dc83f4a921d36ce9ce47d0d13c5d85f2b0ff8318d2877eec2f63b931bd47417a81a538327af927da3e"

PSA hash multi part: SHA-512 Test Vector NIST CAVS #2
depends_on:PSA_WANT_ALG_SHA_512
hash_multi_part:PSA_ALG_SHA_512:"8f":"e4cd2d19931b5aad9c920f45f56f6ce34e3d38c6d319a6e11d0588ab8b838576d6ce6d68eea7c830de66e2bd96458bfa7aafbcbec981d4ed040498c3dd95f22a"

PSA hash multi part: SHA-512 Test Vector NIST CAVS #3
depends_on:PSA_WANT_ALG_SHA_512
hash_multi_part:PSA_ALG_SHA_512:"e724":"7dbb520221a70287b23dbcf62bfc1b73136d858e86266732a7fffa875ecaa2c1b8f673b5c065d360c563a7b9539349f5f59bef8c0c593f9587e3cd50bb26a231"

PSA hash multi part: SHA-512 Test Vector NIST CAVS #4
depends_on:PSA_WANT_ALG_SHA_512
hash_multi_part:PSA_ALG_SHA_512:"de4c90":"33ce98281045a5c4c9df0363d8196f1d7dfcd5ee46ac89776fd8a4344c12f123a66788af5bd41ceff1941aa5637654b4064c88c14e00465ab79a2fc6c97e1014"

PSA hash multi part: SHA-512 Test Vector NIST CAVS #5
depends_on:PSA_WANT_ALG_SHA_512
hash_multi_part:PSA_ALG_SHA_512:"a801e94b":"dadb1b5a27f9fece8d86adb2a51879beb1787ff28f4e8ce162cad7fee0f942efcabbf738bc6f797fc7cc79a3a75048cd4c82ca0757a324695bfb19a557e56e2f"

PSA hash multi part: SHA-512 Test Vector NIST CAVS #6
depends_on:PSA_WANT_ALG_SHA_512
hash_multi_part:PSA_ALG_SHA_512:"94390d3502":"b6175c4c4cccf69e0ce5f0312010886ea6b34d43673f942ae42483f9cbb7da817de4e11b5d58e25a3d9bd721a22cdffe1c40411cc45df1911fa5506129b69297"

PSA hash multi part: SHA-512 Test Vector NIST CAVS #7
depends_on:PSA_WANT_ALG_SHA_512
hash_multi_part:PSA_ALG_SHA_512:"49297dd63e5f":"1fcc1e6f6870859d11649f5e5336a9cd16329c029baf04d5a6edf257889a2e9522b497dd656bb402da461307c4ee382e2e89380c8e6e6e7697f1e439f650fa94"

PSA hash multi part: SHA-512 Test Vector NIST CAVS #8
depends_on:PSA_WANT_ALG_SHA_512
hash_multi_part:PSA_ALG_SHA_512:"990d1ae71a62d7bda9bfdaa1762a68d296eee72a4cd946f287a898fbabc002ea941fd8d4d991030b4d27a637cce501a834bb95eab1b7889a3e784c7968e67cbf552006b206b68f76d9191327524fcc251aeb56af483d10b4e0c6c5e599ee8c0fe4faeca8293844a8547c6a9a90d093f2526873a19ad4a5e776794c68c742fb834793d2dfcb7fea46c63af4b70fd11cb6e41834e72ee40edb067b292a794990c288d5007e73f349fb383af6a756b8301ad6e5e0aa8cd614399bb3a452376b1575afa6bdaeaafc286cb064bb91edef97c632b6c1113d107fa93a0905098a105043c2f05397f702514439a08a9e5ddc196100721d45c8fc17d2ed659376f8a00bd5cb9a0860e26d8a29d8d6aaf52de97e9346033d6db501a35dbbaf97c20b830cd2d18c2532f3a59cc497ee64c0e57d8d060e5069b28d86edf1adcf59144b221ce3ddaef134b3124fbc7dd000240eff0f5f5f41e83cd7f5bb37c9ae21953fe302b0f6e8b68fa91c6ab99265c64b2fd9cd4942be04321bb5d6d71932376c6f2f88e02422ba6a5e2cb765df93fd5dd0728c6abdaf03bce22e0678a544e2c3636f741b6f4447ee58a8fc656b43ef817932176adbfc2e04b2c812c273cd6cbfa4098f0be036a34221fa02643f5ee2e0b38135f2a18ecd2f16ebc45f8eb31b8ab967a1567ee016904188910861ca1fa205c7adaa194b286893ffe2f4fbe0384c2aef72a4522aeafd3ebc71f9db71eeeef86c48394a1c86d5b36c352cc33a0a2c800bc99e62fd65b3a2fd69e0b53996ec13d8ce483ce9319efd9a85acefabdb5342226febb83fd1daf4b24265f50c61c6de74077ef89b6fecf9f29a1f871af1e9f89b2d345cda7499bd45c42fa5d195a1e1a6ba84851889e730da3b2b916e96152ae0c92154b49719841db7e7cc707ba8a5d7b101eb4ac7b629bb327817910fff61580b59aab78182d1a2e33473d05b00b170b29e331870826cfe45af206aa7d0246bbd8566ca7cfb2d3c10bfa1db7dd48dd786036469ce7282093d78b5e1a5b0fc81a54c8ed4ceac1e5305305e78284ac276f5d7862727aff246e17addde50c670028d572cbfc0be2e4f8b2eb28fa68ad7b4c6c2a239c460441bfb5ea049f23b08563b4e47729a59e5986a61a6093dbd54f8c36ebe87edae01f251cb060ad1364ce677d7e8d5a4a4ca966a7241cc360bc2acb280e5f9e9c1b032ad6a180a35e0c5180b9d16d026c865b252098cc1d99ba7375ca31c7702c0d943d5e3dd2f6861fa55bd46d94b67ed3e52eccd8dd06d968e01897d6de97ed3058d91dd":"8e4bc6f8b8c60fe4d68c61d9b159c8693c3151c46749af58da228442d927f23359bd6ccd6c2ec8fa3f00a86cecbfa728e1ad60b821ed22fcd309ba91a4138bc9"

PSA hash multi part: MD5 Test vector RFC1321 #1
depends_on:PSA_WANT_ALG_MD5
hash_multi_part:PSA_ALG_MD5:"":"d41d8cd98f00b204e9800998ecf8427e"

PSA hash multi part: MD5 Test vector RFC1321 #2
depends_on:PSA_WANT_ALG_MD5
hash_multi_part:PSA_ALG_MD5:"61":"0cc175b9c0f1b6a831c399e269772661"

PSA hash multi part: MD5 Test vector RFC1321 #3
depends_on:PSA_WANT_ALG_MD5
hash_multi_part:PSA_ALG_MD5:"616263":"900150983cd24fb0d6963f7d28e17f72"

PSA hash multi part: MD5 Test vector RFC1321 #4
depends_on:PSA_WANT_ALG_MD5
hash_multi_part:PSA_ALG_MD5:"6d65737361676520646967657374":"f96b697d7cb7938d525a2f31aaf161d0"

PSA hash multi part: MD5 Test vector RFC1321 #5
depends_on:PSA_WANT_ALG_MD5
hash_multi_part:PSA_ALG_MD5:"6162636465666768696a6b6c6d6e6f707172737475767778797a":"c3fcd3d76192e4007dfb496cca67e13b"

PSA hash multi part: MD5 Test vector RFC1321 #6
depends_on:PSA_WANT_ALG_MD5
hash_multi_part:PSA_ALG_MD5:"4142434445464748494a4b4c4d4e4f505152535455565758595a6162636465666768696a6b6c6d6e6f707172737475767778797a30313233343536373839":"d174ab98d277d9f5a5611c2c9f419d9f"

PSA hash multi part: MD5 Test vector RFC1321 #7
depends_on:PSA_WANT_ALG_MD5
hash_multi_part:PSA_ALG_MD5:"3132333435363738393031323334353637383930313233343536373839303132333435363738393031323334353637383930313233343536373839303132333435363738393031323334353637383930":"57edf4a22be3c955ac49da2e2107b67a"

PSA hash multi part: RIPEMD160 Test vector from paper #1
depends_on:PSA_WANT_ALG_RIPEMD160
hash_multi_part:PSA_ALG_RIPEMD160:"":"9c1185a5c5e9fc54612808977ee8f548b2258d31"

PSA hash multi part: RIPEMD160 Test vector from paper #2
depends_on:PSA_WANT_ALG_RIPEMD160
hash_multi_part:PSA_ALG_RIPEMD160:"61":"0bdc9d2d256b3ee9daae347be6f4dc835a467ffe"

PSA hash multi part: RIPEMD160 Test vector from paper #3
depends_on:PSA_WANT_ALG_RIPEMD160
hash_multi_part:PSA_ALG_RIPEMD160:"616263":"8eb208f7e05d987a9b044a8e98c6b087f15a0bfc"

PSA hash multi part: RIPEMD160 Test vector from paper #4
depends_on:PSA_WANT_ALG_RIPEMD160
hash_multi_part:PSA_ALG_RIPEMD160:"6d65737361676520646967657374":"5d0689ef49d2fae572b881b123a85ffa21595f36"

PSA hash multi part: RIPEMD160 Test vector from paper #5
depends_on:PSA_WANT_ALG_RIPEMD160
hash_multi_part:PSA_ALG_RIPEMD160:"6162636465666768696a6b6c6d6e6f707172737475767778797a":"f71c27109c692c1b56bbdceb5b9d2865b3708dbc"

PSA hash multi part: RIPEMD160 Test vector from paper #6
depends_on:PSA_WANT_ALG_RIPEMD160
hash_multi_part:PSA_ALG_RIPEMD160:"6162636462636465636465666465666765666768666768696768696a68696a6b696a6b6c6a6b6c6d6b6c6d6e6c6d6e6f6d6e6f706e6f7071":"12a053384a9c0c88e405a06c27dcf49ada62eb2b"

PSA hash multi part: RIPEMD160 Test vector from paper #7
depends_on:PSA_WANT_ALG_RIPEMD160
hash_multi_part:PSA_ALG_RIPEMD160:"4142434445464748494a4b4c4d4e4f505152535455565758595a6162636465666768696a6b6c6d6e6f707172737475767778797a30313233343536373839":"b0e20b6e3116640286ed3a87a5713079b21f5189"

PSA hash multi part: RIPEMD160 Test vector from paper #8
depends_on:PSA_WANT_ALG_RIPEMD160
hash_multi_part:PSA_ALG_RIPEMD160:"3132333435363738393031323334353637383930313233343536373839303132333435363738393031323334353637383930313233343536373839303132333435363738393031323334353637383930":"9b752e45573d4b39f4dbd3323cab82bf63326bfb"

PSA hash multi part: SHA3-224 Test Vector NIST ""
depends_on:PSA_WANT_ALG_SHA3_224
hash_multi_part:PSA_ALG_SHA3_224:"":"6b4e03423667dbb73b6e15454f0eb1abd4597f9a1b078e3f5b5a6bc7"

PSA hash multi part: SHA3-256 Test Vector NIST ""
depends_on:PSA_WANT_ALG_SHA3_256
hash_multi_part:PSA_ALG_SHA3_256:"":"a7ffc6f8bf1ed76651c14756a061d662f580ff4de43b49fa82d80a4b80f8434a"

PSA hash multi part: SHA3-384 Test Vector NIST ""
depends_on:PSA_WANT_ALG_SHA3_384
hash_multi_part:PSA_ALG_SHA3_384:"":"0c63a75b845e4f7d01107d852e4c2485c51a50aaaa94fc61995e71bbee983a2ac3713831264adb47fb6bd1e058d5f004"

PSA hash multi part: SHA3-512 Test Vector NIST ""
depends_on:PSA_WANT_ALG_SHA3_512
hash_multi_part:PSA_ALG_SHA3_512:"":"a69f73cca23a9ac5c8b567dc185a756e97c982164fe25859e0d1dcc1475c80a615b2123af1f5f94c11e3e9402c3ac558f500199d95b6d3e301758586281dcd26"

PSA hash multi part: SHA3-224 Test Vector NIST "abc"
depends_on:PSA_WANT_ALG_SHA3_224
hash_multi_part:PSA_ALG_SHA3_224:"616263":"e642824c3f8cf24ad09234ee7d3c766fc9a3a5168d0c94ad73b46fdf"

PSA hash multi part: SHA3-256 Test Vector NIST "abc"
depends_on:PSA_WANT_ALG_SHA3_256
hash_multi_part:PSA_ALG_SHA3_256:"616263":"3a985da74fe225b2045c172d6bd390bd855f086e3e9d525b46bfe24511431532"

PSA hash multi part: SHA3-384 Test Vector NIST "abc"
depends_on:PSA_WANT_ALG_SHA3_384
hash_multi_part:PSA_ALG_SHA3_384:"616263":"ec01498288516fc926459f58e2c6ad8df9b473cb0fc08c2596da7cf0e49be4b298d88cea927ac7f539f1edf228376d25"

PSA hash multi part: SHA3-512 Test Vector NIST "abc"
depends_on:PSA_WANT_ALG_SHA3_512
hash_multi_part:PSA_ALG_SHA3_512:"616263":"b751850b1a57168a5693cd924b6b096e08f621827444f70d884f5d0240d2712e10e116e9192af3c91a7ec57647e3934057340b4cf408d5a56592f8274eec53f0"

PSA hash multi part: SHA3-224 Test Vector NIST 448 bits: "abcdbcdecdefdefgefghfghighijhijkijkljklmklmnlmnomnopnopq"
depends_on:PSA_WANT_ALG_SHA3_224
hash_multi_part:PSA_ALG_SHA3_224:"6162636462636465636465666465666765666768666768696768696a68696a6b696a6b6c6a6b6c6d6b6c6d6e6c6d6e6f6d6e6f706e6f7071":"8a24108b154ada21c9fd5574494479ba5c7e7ab76ef264ead0fcce33"

PSA hash multi part: SHA3-256 Test Vector NIST 448 bits: "abcdbcdecdefdefgefghfghighijhijkijkljklmklmnlmnomnopnopq"
depends_on:PSA_WANT_ALG_SHA3_256
hash_multi_part:PSA_ALG_SHA3_256:"6162636462636465636465666465666765666768666768696768696a68696a6b696a6b6c6a6b6c6d6b6c6d6e6c6d6e6f6d6e6f706e6f7071":"41c0dba2a9d6240849100376a8235e2c82e1b9998a999e21db32dd97496d3376"

PSA hash multi part: SHA3-384 Test Vector NIST 448 bits: "abcdbcdecdefdefgefghfghighijhijkijkljklmklmnlmnomnopnopq"
depends_on:PSA_WANT_ALG_SHA3_384
hash_multi_part:PSA_ALG_SHA3_384:"6162636462636465636465666465666765666768666768696768696a68696a6b696a6b6c6a6b6c6d6b6c6d6e6c6d6e6f6d6e6f706e6f7071":"991c665755eb3a4b6bbdfb75c78a492e8c56a22c5c4d7e429bfdbc32b9d4ad5aa04a1f076e62fea19eef51acd0657c22"

PSA hash multi part: SHA3-512 Test Vector NIST 448 bits: "abcdbcdecdefdefgefghfghighijhijkijkljklmklmnlmnomnopnopq"
depends_on:PSA_WANT_ALG_SHA3_512
hash_multi_part:PSA_ALG_SHA3_512:"6162636462636465636465666465666765666768666768696768696a68696a6b696a6b6c6a6b6c6d6b6c6d6e6c6d6e6f6d6e6f706e6f7071":"04a371e84ecfb5b8b77cb48610fca8182dd457ce6f326a0fd3d7ec2f1e91636dee691fbe0c985302ba1b0d8dc78c086346b533b49c030d99a27daf1139d6e75e"

PSA hash multi part: SHA3-224 Test Vector NIST 896 bits: "abcdefghbcdefghicdefghijdefghijkefghijklfghijklmghijklmnhijklmnoijklmnopjklmnopqklmnopqrlmnopqrsmnopqrstnopqrstu"
depends_on:PSA_WANT_ALG_SHA3_224
hash_multi_part:PSA_ALG_SHA3_224:"61626364656667686263646566676869636465666768696a6465666768696a6b65666768696a6b6c666768696a6b6c6d6768696a6b6c6d6e68696a6b6c6d6e6f696a6b6c6d6e6f706a6b6c6d6e6f70716b6c6d6e6f7071726c6d6e6f707172736d6e6f70717273746e6f707172737475":"543e6868e1666c1a643630df77367ae5a62a85070a51c14cbf665cbc"

PSA hash multi part: SHA3-256 Test Vector NIST 896 bits: "abcdefghbcdefghicdefghijdefghijkefghijklfghijklmghijklmnhijklmnoijklmnopjklmnopqklmnopqrlmnopqrsmnopqrstnopqrstu"
depends_on:PSA_WANT_ALG_SHA3_256
hash_multi_part:PSA_ALG_SHA3_256:"61626364656667686263646566676869636465666768696a6465666768696a6b65666768696a6b6c666768696a6b6c6d6768696a6b6c6d6e68696a6b6c6d6e6f696a6b6c6d6e6f706a6b6c6d6e6f70716b6c6d6e6f7071726c6d6e6f707172736d6e6f70717273746e6f707172737475":"916f6061fe879741ca6469b43971dfdb28b1a32dc36cb3254e812be27aad1d18"

PSA hash multi part: SHA3-384 Test Vector NIST 896 bits: "abcdefghbcdefghicdefghijdefghijkefghijklfghijklmghijklmnhijklmnoijklmnopjklmnopqklmnopqrlmnopqrsmnopqrstnopqrstu"
depends_on:PSA_WANT_ALG_SHA3_384
hash_multi_part:PSA_ALG_SHA3_384:"61626364656667686263646566676869636465666768696a6465666768696a6b65666768696a6b6c666768696a6b6c6d6768696a6b6c6d6e68696a6b6c6d6e6f696a6b6c6d6e6f706a6b6c6d6e6f70716b6c6d6e6f7071726c6d6e6f707172736d6e6f70717273746e6f707172737475":"79407d3b5916b59c3e30b09822974791c313fb9ecc849e406f23592d04f625dc8c709b98b43b3852b337216179aa7fc7"

PSA hash multi part: SHA3-512 Test Vector NIST 896 bits: "abcdefghbcdefghicdefghijdefghijkefghijklfghijklmghijklmnhijklmnoijklmnopjklmnopqklmnopqrlmnopqrsmnopqrstnopqrstu"
depends_on:PSA_WANT_ALG_SHA3_512
hash_multi_part:PSA_ALG_SHA3_512:"61626364656667686263646566676869636465666768696a6465666768696a6b65666768696a6b6c666768696a6b6c6d6768696a6b6c6d6e68696a6b6c6d6e6f696a6b6c6d6e6f706a6b6c6d6e6f70716b6c6d6e6f7071726c6d6e6f707172736d6e6f70717273746e6f707172737475":"afebb2ef542e6579c50cad06d2e578f9f8dd6881d7dc824d26360feebf18a4fa73e3261122948efcfd492e74e82e2189ed0fb440d187f382270cb455f21dd185"

# HMAC test vectors generated in python with e.g.
# import hmac; hmac.new(bytes([0xaa]*32), b'abc', 'SHA3-224').hexdigest()

PSA HMAC SHA3-224
depends_on:PSA_WANT_ALG_SHA3_224
hmac:PSA_ALG_SHA3_224:"abc":"bf0905154ad610b6a3d6d0b9a1c692494e987337d956624a066d7a1f"

PSA HMAC SHA3-256
depends_on:PSA_WANT_ALG_SHA3_256
hmac:PSA_ALG_SHA3_256:"abc":"a986419a162b6d4731a8e96e44a2c6e784d50137907b457c9fb77c62705dc4d9"

PSA HMAC SHA3-384
depends_on:PSA_WANT_ALG_SHA3_384
hmac:PSA_ALG_SHA3_384:"abc":"87b864ee25f8bfebd516eddd7cdd400d3c368a09e4b1fabaee5636da8a9c876c3f802c366537663910f2e6c5a8426381"

PSA HMAC SHA3-512
depends_on:PSA_WANT_ALG_SHA3_512
hmac:PSA_ALG_SHA3_512:"abc":"2cef45b6950e41a70bc85cb431b2161d47c9e2932187fa15d80e3b7af1da38aa8fe823a72efd7e536e5236a5b0798418d8c8f08b0de5fc262867bb3752b6482d"

PSA HMAC SHA-1
depends_on:PSA_WANT_ALG_SHA_1
hmac:PSA_ALG_SHA_1:"abc":"0b3a7f96afea3e14a0835f7c9468a24649f85596"

PSA HMAC SHA-224
depends_on:PSA_WANT_ALG_SHA_224
hmac:PSA_ALG_SHA_224:"abc":"249c405cef8bcd3ceeafdb9a933179739fb9b1d7f174df4667ec82f3"

PSA HMAC SHA-256
depends_on:PSA_WANT_ALG_SHA_256
hmac:PSA_ALG_SHA_256:"abc":"b89a1b878289c739595104da55b6f7a8afec3e0757fc166080dc267c09c46841"

PSA HMAC SHA-384
depends_on:PSA_WANT_ALG_SHA_384
hmac:PSA_ALG_SHA_384:"abc":"25a8b55c884bc38286305f76332631726498f5586280b88bc6179cd00c6878fb7d1bb3ee1643fcd2fb02b95823ff1af2"

PSA HMAC SHA-512
depends_on:PSA_WANT_ALG_SHA_512
hmac:PSA_ALG_SHA_512:"abc":"d6e5eebb5cf27f5b686fefc416ee8c431bb10770216aa3c6ba13897ef3fc040b98abc53b95039f2c50622473f958af64a3cae1afbea8ffffa8d35ca24f31e222"

PSA HMAC RIPEMD160
depends_on:PSA_WANT_ALG_RIPEMD160
hmac:PSA_ALG_RIPEMD160:"abc":"114a5cf4637f57c044472e1a0b87cce9137190b2"

PSA HMAC MD5
depends_on:PSA_WANT_ALG_MD5
hmac:PSA_ALG_MD5:"abc":"39677b12b80118927387aa4a65d16a5e"

PSA HMAC input length 0
depends_on:PSA_WANT_ALG_SHA_256
hmac:PSA_ALG_SHA_256:"":"63210aee265762634fa3db8c1aa920dcd07d31ec297309580394a21412f83372"

PSA HMAC input length 1
depends_on:PSA_WANT_ALG_SHA_256
hmac:PSA_ALG_SHA_256:"x":"f61c11d66441e3c0b9902a8491caa2da5a0d0d95ef0fc61d8a3b5ea5e0416f5c"

PSA HMAC input length 2890
depends_on:PSA_WANT_ALG_SHA_256
hmac:PSA_ALG_SHA_256:"0123456789101112131415161718192021222324252627282930313233343536373839404142434445464748495051525354555657585960616263646566676869707172737475767778798081828384858687888990919293949596979899100101102103104105106107108109110111112113114115116117118119120121122123124125126127128129130131132133134135136137138139140141142143144145146147148149150151152153154155156157158159160161162163164165166167168169170171172173174175176177178179180181182183184185186187188189190191192193194195196197198199200201202203204205206207208209210211212213214215216217218219220221222223224225226227228229230231232233234235236237238239240241242243244245246247248249250251252253254255256257258259260261262263264265266267268269270271272273274275276277278279280281282283284285286287288289290291292293294295296297298299300301302303304305306307308309310311312313314315316317318319320321322323324325326327328329330331332333334335336337338339340341342343344345346347348349350351352353354355356357358359360361362363364365366367368369370371372373374375376377378379380381382383384385386387388389390391392393394395396397398399400401402403404405406407408409410411412413414415416417418419420421422423424425426427428429430431432433434435436437438439440441442443444445446447448449450451452453454455456457458459460461462463464465466467468469470471472473474475476477478479480481482483484485486487488489490491492493494495496497498499500501502503504505506507508509510511512513514515516517518519520521522523524525526527528529530531532533534535536537538539540541542543544545546547548549550551552553554555556557558559560561562563564565566567568569570571572573574575576577578579580581582583584585586587588589590591592593594595596597598599600601602603604605606607608609610611612613614615616617618619620621622623624625626627628629630631632633634635636637638639640641642643644645646647648649650651652653654655656657658659660661662663664665666667668669670671672673674675676677678679680681682683684685686687688689690691692693694695696697698699700701702703704705706707708709710711712713714715716717718719720721722723724725726727728729730731732733734735736737738739740741742743744745746747748749750751752753754755756757758759760761762763764765766767768769770771772773774775776777778779780781782783784785786787788789790791792793794795796797798799800801802803804805806807808809810811812813814815816817818819820821822823824825826827828829830831832833834835836837838839840841842843844845846847848849850851852853854855856857858859860861862863864865866867868869870871872873874875876877878879880881882883884885886887888889890891892893894895896897898899900901902903904905906907908909910911912913914915916917918919920921922923924925926927928929930931932933934935936937938939940941942943944945946947948949950951952953954955956957958959960961962963964965966967968969970971972973974975976977978979980981982983984985986987988989990991992993994995996997998999":"dd2e08786029097be5f634fda74a7e20e3e1638e71282892c4fd21d9a71ae418"
