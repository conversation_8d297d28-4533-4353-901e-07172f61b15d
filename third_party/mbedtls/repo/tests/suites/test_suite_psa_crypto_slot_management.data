Transient slot, check after closing
transient_slot_lifecycle:0x1:0:0:PSA_KEY_TYPE_RAW_DATA:"0123456789abcdef0123456789abcdef":INVALIDATE_BY_CLOSING

Transient slot, check after closing and restarting
transient_slot_lifecycle:0x13:0:0:PSA_KEY_TYPE_RAW_DATA:"0123456789abcdef0123456789abcdef":INVALIDATE_BY_CLOSING_WITH_SHUTDOWN

Transient slot, check after destroying
transient_slot_lifecycle:0x135:0:0:PSA_KEY_TYPE_RAW_DATA:"0123456789abcdef0123456789abcdef":INVALIDATE_BY_DESTROYING

Transient slot, check after destroying and restarting
transient_slot_lifecycle:0x1357:0:0:PSA_KEY_TYPE_RAW_DATA:"0123456789abcdef0123456789abcdef":INVALIDATE_BY_DESTROYING_WITH_SHUTDOWN

Transient slot, check after restart with live handles
transient_slot_lifecycle:0x13579:0:0:PSA_KEY_TYPE_RAW_DATA:"0123456789abcdef0123456789abcdef":INVALIDATE_BY_SHUTDOWN

Persistent slot, check after closing, id=min
persistent_slot_lifecycle:PSA_KEY_LIFETIME_PERSISTENT:124:PSA_KEY_ID_USER_MIN:0:0:0:PSA_KEY_TYPE_RAW_DATA:"0123456789abcdef0123456789abcdef":INVALIDATE_BY_CLOSING

Persistent slot, check after closing and restarting, id=min
persistent_slot_lifecycle:PSA_KEY_LIFETIME_PERSISTENT:125:PSA_KEY_ID_USER_MIN:0:0:0:PSA_KEY_TYPE_RAW_DATA:"0123456789abcdef0123456789abcdef":INVALIDATE_BY_CLOSING_WITH_SHUTDOWN

Persistent slot, check after destroying, id=min
persistent_slot_lifecycle:PSA_KEY_LIFETIME_PERSISTENT:126:PSA_KEY_ID_USER_MIN:0:0:0:PSA_KEY_TYPE_RAW_DATA:"0123456789abcdef0123456789abcdef":INVALIDATE_BY_DESTROYING

Persistent slot, check after destroying and restarting, id=min
persistent_slot_lifecycle:PSA_KEY_LIFETIME_PERSISTENT:127:PSA_KEY_ID_USER_MIN:0:0:0:PSA_KEY_TYPE_RAW_DATA:"0123456789abcdef0123456789abcdef":INVALIDATE_BY_DESTROYING_WITH_SHUTDOWN

Persistent slot, check after purging, id=min
persistent_slot_lifecycle:PSA_KEY_LIFETIME_PERSISTENT:200:PSA_KEY_ID_USER_MIN:0:0:0:PSA_KEY_TYPE_RAW_DATA:"0123456789abcdef0123456789abcdef":INVALIDATE_BY_PURGING

Persistent slot, check after purging and restarting, id=min
persistent_slot_lifecycle:PSA_KEY_LIFETIME_PERSISTENT:201:PSA_KEY_ID_USER_MIN:0:0:0:PSA_KEY_TYPE_RAW_DATA:"0123456789abcdef0123456789abcdef":INVALIDATE_BY_PURGING_WITH_SHUTDOWN

Persistent slot, check after restart with live handle, id=min
persistent_slot_lifecycle:PSA_KEY_LIFETIME_PERSISTENT:128:PSA_KEY_ID_USER_MIN:0:0:0:PSA_KEY_TYPE_RAW_DATA:"0123456789abcdef0123456789abcdef":INVALIDATE_BY_SHUTDOWN

Persistent slot, check after closing, id=max
persistent_slot_lifecycle:PSA_KEY_LIFETIME_PERSISTENT:129:PSA_KEY_ID_USER_MAX:0:0:0:PSA_KEY_TYPE_RAW_DATA:"0123456789abcdef0123456789abcdef":INVALIDATE_BY_CLOSING

Persistent slot, check after destroying, id=max
persistent_slot_lifecycle:PSA_KEY_LIFETIME_PERSISTENT:130:PSA_KEY_ID_USER_MAX:0:0:0:PSA_KEY_TYPE_RAW_DATA:"0123456789abcdef0123456789abcdef":INVALIDATE_BY_DESTROYING

Persistent slot, check after purging, id=max
persistent_slot_lifecycle:PSA_KEY_LIFETIME_PERSISTENT:202:PSA_KEY_ID_USER_MAX:0:0:0:PSA_KEY_TYPE_RAW_DATA:"0123456789abcdef0123456789abcdef":INVALIDATE_BY_PURGING

Persistent slot, check after restart, id=max
persistent_slot_lifecycle:PSA_KEY_LIFETIME_PERSISTENT:131:PSA_KEY_ID_USER_MAX:0:0:0:PSA_KEY_TYPE_RAW_DATA:"0123456789abcdef0123456789abcdef":INVALIDATE_BY_SHUTDOWN

Persistent slot: ECP keypair (ECDSA, exportable), close
depends_on:PSA_WANT_ALG_ECDSA:PSA_WANT_KEY_TYPE_ECC_KEY_PAIR_BASIC:PSA_WANT_KEY_TYPE_ECC_KEY_PAIR_IMPORT:PSA_WANT_KEY_TYPE_ECC_KEY_PAIR_EXPORT:PSA_WANT_ECC_SECP_R1_256
persistent_slot_lifecycle:PSA_KEY_LIFETIME_PERSISTENT:132:1:PSA_KEY_USAGE_EXPORT | PSA_KEY_USAGE_SIGN_HASH | PSA_KEY_USAGE_VERIFY_HASH:PSA_ALG_ECDSA_ANY:0:PSA_KEY_TYPE_ECC_KEY_PAIR(PSA_ECC_FAMILY_SECP_R1):"49c9a8c18c4b885638c431cf1df1c994131609b580d4fd43a0cab17db2f13eee":INVALIDATE_BY_CLOSING

Persistent slot: ECP keypair (ECDSA, exportable), close+restart
depends_on:PSA_WANT_ALG_ECDSA:PSA_WANT_KEY_TYPE_ECC_KEY_PAIR_BASIC:PSA_WANT_KEY_TYPE_ECC_KEY_PAIR_IMPORT:PSA_WANT_KEY_TYPE_ECC_KEY_PAIR_EXPORT:PSA_WANT_ECC_SECP_R1_256
persistent_slot_lifecycle:PSA_KEY_LIFETIME_PERSISTENT:133:1:PSA_KEY_USAGE_EXPORT | PSA_KEY_USAGE_SIGN_HASH | PSA_KEY_USAGE_VERIFY_HASH:PSA_ALG_ECDSA_ANY:0:PSA_KEY_TYPE_ECC_KEY_PAIR(PSA_ECC_FAMILY_SECP_R1):"49c9a8c18c4b885638c431cf1df1c994131609b580d4fd43a0cab17db2f13eee":INVALIDATE_BY_CLOSING_WITH_SHUTDOWN

Persistent slot: ECP keypair (ECDSA, exportable), purge
depends_on:PSA_WANT_ALG_ECDSA:PSA_WANT_KEY_TYPE_ECC_KEY_PAIR_BASIC:PSA_WANT_KEY_TYPE_ECC_KEY_PAIR_IMPORT:PSA_WANT_KEY_TYPE_ECC_KEY_PAIR_EXPORT:PSA_WANT_ECC_SECP_R1_256
persistent_slot_lifecycle:PSA_KEY_LIFETIME_PERSISTENT:132:1:PSA_KEY_USAGE_EXPORT | PSA_KEY_USAGE_SIGN_HASH | PSA_KEY_USAGE_VERIFY_HASH:PSA_ALG_ECDSA_ANY:0:PSA_KEY_TYPE_ECC_KEY_PAIR(PSA_ECC_FAMILY_SECP_R1):"49c9a8c18c4b885638c431cf1df1c994131609b580d4fd43a0cab17db2f13eee":INVALIDATE_BY_PURGING

Persistent slot: ECP keypair (ECDSA, exportable), restart
depends_on:PSA_WANT_ALG_ECDSA:PSA_WANT_KEY_TYPE_ECC_KEY_PAIR_BASIC:PSA_WANT_KEY_TYPE_ECC_KEY_PAIR_IMPORT:PSA_WANT_KEY_TYPE_ECC_KEY_PAIR_EXPORT:PSA_WANT_ECC_SECP_R1_256
persistent_slot_lifecycle:PSA_KEY_LIFETIME_PERSISTENT:134:1:PSA_KEY_USAGE_EXPORT | PSA_KEY_USAGE_SIGN_HASH | PSA_KEY_USAGE_VERIFY_HASH:PSA_ALG_ECDSA_ANY:0:PSA_KEY_TYPE_ECC_KEY_PAIR(PSA_ECC_FAMILY_SECP_R1):"49c9a8c18c4b885638c431cf1df1c994131609b580d4fd43a0cab17db2f13eee":INVALIDATE_BY_SHUTDOWN

Persistent slot: ECP keypair (ECDH+ECDSA, exportable), close
depends_on:PSA_WANT_ALG_ECDH:PSA_WANT_ALG_ECDSA:PSA_WANT_ALG_HKDF:PSA_WANT_ALG_SHA_256:PSA_WANT_KEY_TYPE_ECC_KEY_PAIR_BASIC:PSA_WANT_KEY_TYPE_ECC_KEY_PAIR_IMPORT:PSA_WANT_KEY_TYPE_ECC_KEY_PAIR_EXPORT:PSA_WANT_ECC_SECP_R1_256
persistent_slot_lifecycle:PSA_KEY_LIFETIME_PERSISTENT:135:1:PSA_KEY_USAGE_EXPORT | PSA_KEY_USAGE_SIGN_HASH | PSA_KEY_USAGE_VERIFY_HASH:PSA_ALG_KEY_AGREEMENT(PSA_ALG_ECDH, PSA_ALG_HKDF(PSA_ALG_SHA_256)):PSA_ALG_ECDSA_ANY:PSA_KEY_TYPE_ECC_KEY_PAIR(PSA_ECC_FAMILY_SECP_R1):"49c9a8c18c4b885638c431cf1df1c994131609b580d4fd43a0cab17db2f13eee":INVALIDATE_BY_CLOSING

Persistent slot: ECP keypair (ECDH+ECDSA, exportable), close+restart
depends_on:PSA_WANT_ALG_ECDH:PSA_WANT_ALG_ECDSA:PSA_WANT_ALG_HKDF:PSA_WANT_ALG_SHA_256:PSA_WANT_KEY_TYPE_ECC_KEY_PAIR_BASIC:PSA_WANT_KEY_TYPE_ECC_KEY_PAIR_IMPORT:PSA_WANT_KEY_TYPE_ECC_KEY_PAIR_EXPORT:PSA_WANT_ECC_SECP_R1_256
persistent_slot_lifecycle:PSA_KEY_LIFETIME_PERSISTENT:136:1:PSA_KEY_USAGE_EXPORT | PSA_KEY_USAGE_SIGN_HASH | PSA_KEY_USAGE_VERIFY_HASH:PSA_ALG_KEY_AGREEMENT(PSA_ALG_ECDH, PSA_ALG_HKDF(PSA_ALG_SHA_256)):PSA_ALG_ECDSA_ANY:PSA_KEY_TYPE_ECC_KEY_PAIR(PSA_ECC_FAMILY_SECP_R1):"49c9a8c18c4b885638c431cf1df1c994131609b580d4fd43a0cab17db2f13eee":INVALIDATE_BY_CLOSING_WITH_SHUTDOWN

Persistent slot: ECP keypair (ECDH+ECDSA, exportable), purge
depends_on:PSA_WANT_ALG_ECDH:PSA_WANT_ALG_ECDSA:PSA_WANT_ALG_HKDF:PSA_WANT_ALG_SHA_256:PSA_WANT_KEY_TYPE_ECC_KEY_PAIR_BASIC:PSA_WANT_KEY_TYPE_ECC_KEY_PAIR_IMPORT:PSA_WANT_KEY_TYPE_ECC_KEY_PAIR_EXPORT:PSA_WANT_ECC_SECP_R1_256
persistent_slot_lifecycle:PSA_KEY_LIFETIME_PERSISTENT:135:1:PSA_KEY_USAGE_EXPORT | PSA_KEY_USAGE_SIGN_HASH | PSA_KEY_USAGE_VERIFY_HASH:PSA_ALG_KEY_AGREEMENT(PSA_ALG_ECDH, PSA_ALG_HKDF(PSA_ALG_SHA_256)):PSA_ALG_ECDSA_ANY:PSA_KEY_TYPE_ECC_KEY_PAIR(PSA_ECC_FAMILY_SECP_R1):"49c9a8c18c4b885638c431cf1df1c994131609b580d4fd43a0cab17db2f13eee":INVALIDATE_BY_PURGING

Persistent slot: ECP keypair (ECDH+ECDSA, exportable), restart
depends_on:PSA_WANT_ALG_ECDH:PSA_WANT_ALG_ECDSA:PSA_WANT_ALG_HKDF:PSA_WANT_ALG_SHA_256:PSA_WANT_KEY_TYPE_ECC_KEY_PAIR_BASIC:PSA_WANT_KEY_TYPE_ECC_KEY_PAIR_IMPORT:PSA_WANT_KEY_TYPE_ECC_KEY_PAIR_EXPORT:PSA_WANT_ECC_SECP_R1_256
persistent_slot_lifecycle:PSA_KEY_LIFETIME_PERSISTENT:137:1:PSA_KEY_USAGE_EXPORT | PSA_KEY_USAGE_SIGN_HASH | PSA_KEY_USAGE_VERIFY_HASH:PSA_ALG_KEY_AGREEMENT(PSA_ALG_ECDH, PSA_ALG_HKDF(PSA_ALG_SHA_256)):PSA_ALG_ECDSA_ANY:PSA_KEY_TYPE_ECC_KEY_PAIR(PSA_ECC_FAMILY_SECP_R1):"49c9a8c18c4b885638c431cf1df1c994131609b580d4fd43a0cab17db2f13eee":INVALIDATE_BY_SHUTDOWN

Persistent slot, check after closing, persistence=2
persistent_slot_lifecycle:PSA_KEY_LIFETIME_FROM_PERSISTENCE_AND_LOCATION(2, PSA_KEY_LOCATION_LOCAL_STORAGE):124:PSA_KEY_ID_USER_MIN:0:0:0:PSA_KEY_TYPE_RAW_DATA:"0123456789abcdef0123456789abcdef":INVALIDATE_BY_CLOSING

Persistent slot, check after closing and restarting, persistence=2
persistent_slot_lifecycle:PSA_KEY_LIFETIME_FROM_PERSISTENCE_AND_LOCATION(2, PSA_KEY_LOCATION_LOCAL_STORAGE):125:PSA_KEY_ID_USER_MIN:0:0:0:PSA_KEY_TYPE_RAW_DATA:"0123456789abcdef0123456789abcdef":INVALIDATE_BY_CLOSING_WITH_SHUTDOWN

Persistent slot, check after destroying, persistence=2
persistent_slot_lifecycle:PSA_KEY_LIFETIME_FROM_PERSISTENCE_AND_LOCATION(2, PSA_KEY_LOCATION_LOCAL_STORAGE):126:PSA_KEY_ID_USER_MIN:0:0:0:PSA_KEY_TYPE_RAW_DATA:"0123456789abcdef0123456789abcdef":INVALIDATE_BY_DESTROYING

Persistent slot, check after destroying and restarting, persistence=2
persistent_slot_lifecycle:PSA_KEY_LIFETIME_FROM_PERSISTENCE_AND_LOCATION(2, PSA_KEY_LOCATION_LOCAL_STORAGE):127:PSA_KEY_ID_USER_MIN:0:0:0:PSA_KEY_TYPE_RAW_DATA:"0123456789abcdef0123456789abcdef":INVALIDATE_BY_DESTROYING_WITH_SHUTDOWN

Persistent slot, check after purging, persistence=2
persistent_slot_lifecycle:PSA_KEY_LIFETIME_FROM_PERSISTENCE_AND_LOCATION(2, PSA_KEY_LOCATION_LOCAL_STORAGE):200:PSA_KEY_ID_USER_MIN:0:0:0:PSA_KEY_TYPE_RAW_DATA:"0123456789abcdef0123456789abcdef":INVALIDATE_BY_PURGING

Persistent slot, check after purging and restarting, persistence=2
persistent_slot_lifecycle:PSA_KEY_LIFETIME_FROM_PERSISTENCE_AND_LOCATION(2, PSA_KEY_LOCATION_LOCAL_STORAGE):201:PSA_KEY_ID_USER_MIN:0:0:0:PSA_KEY_TYPE_RAW_DATA:"0123456789abcdef0123456789abcdef":INVALIDATE_BY_PURGING_WITH_SHUTDOWN

Persistent slot, check after restart with live handle, persistence=2
persistent_slot_lifecycle:PSA_KEY_LIFETIME_FROM_PERSISTENCE_AND_LOCATION(2, PSA_KEY_LOCATION_LOCAL_STORAGE):128:PSA_KEY_ID_USER_MIN:0:0:0:PSA_KEY_TYPE_RAW_DATA:"0123456789abcdef0123456789abcdef":INVALIDATE_BY_SHUTDOWN

Attempt to overwrite: close before
create_existent:PSA_KEY_LIFETIME_PERSISTENT:0x1736:1:CLOSE_BEFORE

Attempt to overwrite: close after
create_existent:PSA_KEY_LIFETIME_PERSISTENT:0x7361:1:CLOSE_AFTER

Attempt to overwrite: keep open
create_existent:PSA_KEY_LIFETIME_PERSISTENT:0x3617:1:KEEP_OPEN

Open failure: invalid identifier (0)
depends_on:MBEDTLS_PSA_CRYPTO_STORAGE_C
open_fail:0:PSA_ERROR_DOES_NOT_EXIST

Open failure: invalid identifier (random seed UID)
depends_on:MBEDTLS_PSA_CRYPTO_STORAGE_C
open_fail:PSA_CRYPTO_ITS_RANDOM_SEED_UID:PSA_ERROR_DOES_NOT_EXIST

Open failure: invalid identifier (reserved range)
depends_on:MBEDTLS_PSA_CRYPTO_STORAGE_C
open_fail:PSA_KEY_ID_VENDOR_MAX + 1:PSA_ERROR_DOES_NOT_EXIST

Open failure: invalid identifier (implementation range)
depends_on:MBEDTLS_PSA_CRYPTO_STORAGE_C
open_fail:PSA_KEY_ID_USER_MAX + 1:PSA_ERROR_DOES_NOT_EXIST

Open failure: non-existent identifier
depends_on:MBEDTLS_PSA_CRYPTO_STORAGE_C
open_fail:1:PSA_ERROR_DOES_NOT_EXIST

Create failure: read-only key
depends_on:MBEDTLS_PSA_CRYPTO_STORAGE_C
create_fail:PSA_KEY_LIFETIME_FROM_PERSISTENCE_AND_LOCATION(PSA_KEY_PERSISTENCE_READ_ONLY, PSA_KEY_LOCATION_LOCAL_STORAGE):1:PSA_ERROR_INVALID_ARGUMENT

Create failure: invalid location for a persistent key
depends_on:MBEDTLS_PSA_CRYPTO_STORAGE_C
create_fail:PSA_KEY_LIFETIME_FROM_PERSISTENCE_AND_LOCATION(PSA_KEY_PERSISTENCE_DEFAULT, 0xbad10cU):1:PSA_ERROR_INVALID_ARGUMENT

Create failure: invalid location for a volatile key
depends_on:MBEDTLS_PSA_CRYPTO_STORAGE_C
create_fail:PSA_KEY_LIFETIME_FROM_PERSISTENCE_AND_LOCATION(PSA_KEY_PERSISTENCE_VOLATILE, 0xbad10cU):0:PSA_ERROR_INVALID_ARGUMENT

Create failure: invalid key id (0) for a persistent key
depends_on:MBEDTLS_PSA_CRYPTO_STORAGE_C
create_fail:PSA_KEY_LIFETIME_PERSISTENT:0:PSA_ERROR_INVALID_ARGUMENT

Create failure: invalid key id (1) for a volatile key
create_fail:PSA_KEY_LIFETIME_VOLATILE:1:PSA_ERROR_INVALID_ARGUMENT

Create failure: invalid key id (random seed UID)
depends_on:MBEDTLS_PSA_CRYPTO_STORAGE_C
create_fail:PSA_KEY_LIFETIME_PERSISTENT:PSA_CRYPTO_ITS_RANDOM_SEED_UID:PSA_ERROR_INVALID_ARGUMENT

Create failure: invalid key id (reserved range)
depends_on:MBEDTLS_PSA_CRYPTO_STORAGE_C
create_fail:PSA_KEY_LIFETIME_PERSISTENT:PSA_KEY_ID_VENDOR_MAX + 1:PSA_ERROR_INVALID_ARGUMENT

Create failure: invalid key id (implementation range)
depends_on:MBEDTLS_PSA_CRYPTO_STORAGE_C
create_fail:PSA_KEY_LIFETIME_PERSISTENT:PSA_KEY_ID_USER_MAX + 1:PSA_ERROR_INVALID_ARGUMENT

Open not supported
depends_on:!MBEDTLS_PSA_CRYPTO_STORAGE_C:!MBEDTLS_PSA_CRYPTO_BUILTIN_KEYS
open_fail:1:PSA_ERROR_NOT_SUPPORTED

Create not supported
depends_on:!MBEDTLS_PSA_CRYPTO_STORAGE_C
create_fail:PSA_KEY_LIFETIME_PERSISTENT:1:PSA_ERROR_NOT_SUPPORTED

Copy volatile to volatile
copy_across_lifetimes:PSA_KEY_LIFETIME_VOLATILE:0x10:0:PSA_KEY_USAGE_EXPORT | PSA_KEY_USAGE_COPY:0:0:PSA_KEY_TYPE_RAW_DATA:"4142434445":PSA_KEY_LIFETIME_VOLATILE:0x10:0:PSA_KEY_USAGE_EXPORT:0:0:PSA_KEY_USAGE_EXPORT:0:0

Copy volatile to persistent
depends_on:MBEDTLS_PSA_CRYPTO_STORAGE_C
copy_across_lifetimes:PSA_KEY_LIFETIME_VOLATILE:0x100:0:PSA_KEY_USAGE_EXPORT | PSA_KEY_USAGE_COPY:0:0:PSA_KEY_TYPE_RAW_DATA:"4142434445":PSA_KEY_LIFETIME_PERSISTENT:0x100:1:PSA_KEY_USAGE_EXPORT:0:0:PSA_KEY_USAGE_EXPORT:0:0

Copy persistent to volatile
depends_on:MBEDTLS_PSA_CRYPTO_STORAGE_C
copy_across_lifetimes:PSA_KEY_LIFETIME_PERSISTENT:0x1000:1:PSA_KEY_USAGE_EXPORT | PSA_KEY_USAGE_COPY:0:0:PSA_KEY_TYPE_RAW_DATA:"4142434445":PSA_KEY_LIFETIME_VOLATILE:0x1000:0:PSA_KEY_USAGE_EXPORT:0:0:PSA_KEY_USAGE_EXPORT:0:0

Copy persistent to persistent
depends_on:MBEDTLS_PSA_CRYPTO_STORAGE_C
copy_across_lifetimes:PSA_KEY_LIFETIME_PERSISTENT:0x10000:1:PSA_KEY_USAGE_EXPORT | PSA_KEY_USAGE_COPY:0:0:PSA_KEY_TYPE_RAW_DATA:"4142434445":PSA_KEY_LIFETIME_PERSISTENT:0x10000:2:PSA_KEY_USAGE_EXPORT:0:0:PSA_KEY_USAGE_EXPORT:0:0

Copy persistent to persistent, same id but different owner
depends_on:MBEDTLS_PSA_CRYPTO_STORAGE_C:MBEDTLS_PSA_CRYPTO_KEY_ID_ENCODES_OWNER
copy_across_lifetimes:PSA_KEY_LIFETIME_PERSISTENT:0x10000:1:PSA_KEY_USAGE_EXPORT | PSA_KEY_USAGE_COPY:0:0:PSA_KEY_TYPE_RAW_DATA:"4142434445":PSA_KEY_LIFETIME_PERSISTENT:0x10001:1:PSA_KEY_USAGE_EXPORT:0:0:PSA_KEY_USAGE_EXPORT:0:0

Copy persistent to persistent with enrollment algorithm
depends_on:PSA_WANT_ALG_CBC_NO_PADDING:PSA_WANT_ALG_CTR:PSA_WANT_KEY_TYPE_AES:MBEDTLS_PSA_CRYPTO_STORAGE_C
copy_across_lifetimes:PSA_KEY_LIFETIME_PERSISTENT:0x100000:1:PSA_KEY_USAGE_EXPORT | PSA_KEY_USAGE_COPY:PSA_ALG_CTR:PSA_ALG_CBC_NO_PADDING:PSA_KEY_TYPE_AES:"404142434445464748494a4b4c4d4e4f":PSA_KEY_LIFETIME_PERSISTENT:0x100000:2:PSA_KEY_USAGE_EXPORT:PSA_ALG_CTR:PSA_ALG_CBC_NO_PADDING:PSA_KEY_USAGE_EXPORT:PSA_ALG_CTR:PSA_ALG_CBC_NO_PADDING

Copy volatile to occupied
depends_on:PSA_WANT_ALG_CBC_NO_PADDING:PSA_WANT_ALG_CTR:PSA_WANT_KEY_TYPE_AES:MBEDTLS_PSA_CRYPTO_STORAGE_C
copy_to_occupied:PSA_KEY_LIFETIME_VOLATILE:0:PSA_KEY_USAGE_EXPORT | PSA_KEY_USAGE_COPY:PSA_ALG_CTR:PSA_KEY_TYPE_AES:"404142434445464748494a4b4c4d4e4f":PSA_KEY_LIFETIME_PERSISTENT:2:PSA_KEY_USAGE_EXPORT:PSA_ALG_CBC_NO_PADDING:PSA_KEY_TYPE_AES:"606162636465666768696a6b6c6d6e6f"

Copy persistent to occupied
depends_on:PSA_WANT_ALG_CBC_NO_PADDING:PSA_WANT_ALG_CTR:PSA_WANT_KEY_TYPE_AES:MBEDTLS_PSA_CRYPTO_STORAGE_C
copy_to_occupied:PSA_KEY_LIFETIME_PERSISTENT:1:PSA_KEY_USAGE_EXPORT | PSA_KEY_USAGE_COPY:PSA_ALG_CTR:PSA_KEY_TYPE_AES:"404142434445464748494a4b4c4d4e4f":PSA_KEY_LIFETIME_PERSISTENT:2:PSA_KEY_USAGE_EXPORT:PSA_ALG_CBC_NO_PADDING:PSA_KEY_TYPE_AES:"606162636465666768696a6b6c6d6e6f"

Copy persistent to same
depends_on:PSA_WANT_ALG_CTR:PSA_WANT_KEY_TYPE_AES:MBEDTLS_PSA_CRYPTO_STORAGE_C
copy_to_occupied:PSA_KEY_LIFETIME_PERSISTENT:1:PSA_KEY_USAGE_EXPORT | PSA_KEY_USAGE_COPY:PSA_ALG_CTR:PSA_KEY_TYPE_AES:"404142434445464748494a4b4c4d4e4f":PSA_KEY_LIFETIME_PERSISTENT:1:PSA_KEY_USAGE_EXPORT:PSA_ALG_CTR:PSA_KEY_TYPE_AES:"404142434445464748494a4b4c4d4e4f"

invalid handle: 0
invalid_handle:INVALID_HANDLE_0:PSA_SUCCESS

invalid handle: never opened
invalid_handle:INVALID_HANDLE_UNOPENED:PSA_ERROR_INVALID_HANDLE

invalid handle: already closed
invalid_handle:INVALID_HANDLE_CLOSED:PSA_ERROR_INVALID_HANDLE

invalid handle: huge
invalid_handle:INVALID_HANDLE_HUGE:PSA_ERROR_INVALID_HANDLE

Open many transient keys
many_transient_keys:42

# Eviction from a key slot to be able to import a new persistent key.
Key slot eviction to import a new persistent key
key_slot_eviction_to_import_new_key:PSA_KEY_LIFETIME_PERSISTENT

# Eviction from a key slot to be able to import a new volatile key.
Key slot eviction to import a new volatile key
key_slot_eviction_to_import_new_key:PSA_KEY_LIFETIME_VOLATILE

# Check that non reusable key slots are not deleted/overwritten in case of key
# slot starvation:
# . An attempt to access a persistent key while all RAM key slots are occupied
#   by volatile keys fails and does not lead to volatile key data to be
#   spoiled.
# . With all key slot in use with one containing a persistent key, an attempt
#   to copy the persistent key fails (the persistent key slot cannot be
#   reclaimed as it is accessed by the copy process) without the persistent key
#   data and volatile key data being spoiled.
Non reusable key slots integrity in case of key slot starvation
non_reusable_key_slots_integrity_in_case_of_key_slot_starvation
