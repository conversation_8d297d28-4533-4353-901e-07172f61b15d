Decrypt empty buffer
depends_on:MBEDTLS_CHACHA20_C
dec_empty_buf:MBEDTLS_CIPHER_CHACHA20:0:0

Chacha20 RFC 7539 Test Vector #1
depends_on:MBEDTLS_CHACHA20_C
decrypt_test_vec:MBEDTLS_CIPHER_CHACHA20:-1:"0000000000000000000000000000000000000000000000000000000000000000":"000000000000000000000000":"76b8e0ada0f13d90405d6ae55386bd28bdd219b8a08ded1aa836efcc8b770dc7da41597c5157488d7724e03fb8d84a376a43b8f41518a11cc387b669b2ee6586":"00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000":"":"":0:0

ChaCha20 Encrypt and decrypt 0 bytes
depends_on:MBEDTLS_CHACHA20_C
enc_dec_buf:MBEDTLS_CIPHER_CHACHA20:"CHACHA20":256:0:-1

Cha<PERSON>ha20 Encrypt and decrypt 1 bytes
depends_on:MBEDTLS_CHACHA20_C
enc_dec_buf:MBEDTLS_CIPHER_CHACHA20:"CHACHA20":256:1:-1

Cha<PERSON>ha<PERSON> Encrypt and decrypt 2 bytes
depends_on:MBEDTLS_CHACHA20_C
enc_dec_buf:MBEDTLS_CIPHER_CHACHA20:"CHACHA20":256:2:-1

Cha<PERSON>ha20 Encrypt and decrypt 7 bytes
depends_on:MBEDTLS_CHACHA20_C
enc_dec_buf:MBEDTLS_CIPHER_CHACHA20:"CHACHA20":256:7:-1

ChaCha20 Encrypt and decrypt 8 bytes
depends_on:MBEDTLS_CHACHA20_C
enc_dec_buf:MBEDTLS_CIPHER_CHACHA20:"CHACHA20":256:8:-1

ChaCha20 Encrypt and decrypt 9 bytes
depends_on:MBEDTLS_CHACHA20_C
enc_dec_buf:MBEDTLS_CIPHER_CHACHA20:"CHACHA20":256:9:-1

ChaCha20 Encrypt and decrypt 15 bytes
depends_on:MBEDTLS_CHACHA20_C
enc_dec_buf:MBEDTLS_CIPHER_CHACHA20:"CHACHA20":256:15:-1

ChaCha20 Encrypt and decrypt 16 bytes
depends_on:MBEDTLS_CHACHA20_C
enc_dec_buf:MBEDTLS_CIPHER_CHACHA20:"CHACHA20":256:16:-1

ChaCha20 Encrypt and decrypt 17 bytes
depends_on:MBEDTLS_CHACHA20_C
enc_dec_buf:MBEDTLS_CIPHER_CHACHA20:"CHACHA20":256:17:-1

ChaCha20 Encrypt and decrypt 31 bytes
depends_on:MBEDTLS_CHACHA20_C
enc_dec_buf:MBEDTLS_CIPHER_CHACHA20:"CHACHA20":256:31:-1

ChaCha20 Encrypt and decrypt 32 bytes
depends_on:MBEDTLS_CHACHA20_C
enc_dec_buf:MBEDTLS_CIPHER_CHACHA20:"CHACHA20":256:32:-1

ChaCha20 Encrypt and decrypt 33 bytes
depends_on:MBEDTLS_CHACHA20_C
enc_dec_buf:MBEDTLS_CIPHER_CHACHA20:"CHACHA20":256:33:-1

ChaCha20 Encrypt and decrypt 47 bytes
depends_on:MBEDTLS_CHACHA20_C
enc_dec_buf:MBEDTLS_CIPHER_CHACHA20:"CHACHA20":256:47:-1

ChaCha20 Encrypt and decrypt 48 bytes
depends_on:MBEDTLS_CHACHA20_C
enc_dec_buf:MBEDTLS_CIPHER_CHACHA20:"CHACHA20":256:48:-1

ChaCha20 Encrypt and decrypt 49 bytes
depends_on:MBEDTLS_CHACHA20_C
enc_dec_buf:MBEDTLS_CIPHER_CHACHA20:"CHACHA20":256:49:-1

ChaCha20 Encrypt and decrypt 0 bytes in multiple parts 1
depends_on:MBEDTLS_CHACHA20_C
enc_dec_buf_multipart:MBEDTLS_CIPHER_CHACHA20:256:0:0:-1:0:0:0:0

ChaCha20 Encrypt and decrypt 1 bytes in multiple parts 1
depends_on:MBEDTLS_CHACHA20_C
enc_dec_buf_multipart:MBEDTLS_CIPHER_CHACHA20:256:1:0:-1:1:0:1:0

ChaCha20 Encrypt and decrypt 1 bytes in multiple parts 2
depends_on:MBEDTLS_CHACHA20_C
enc_dec_buf_multipart:MBEDTLS_CIPHER_CHACHA20:256:0:1:-1:0:1:0:1

ChaCha20 Encrypt and decrypt 16 bytes in multiple parts 1
depends_on:MBEDTLS_CHACHA20_C
enc_dec_buf_multipart:MBEDTLS_CIPHER_CHACHA20:256:16:0:-1:16:0:16:0

ChaCha20 Encrypt and decrypt 16 bytes in multiple parts 2
depends_on:MBEDTLS_CHACHA20_C
enc_dec_buf_multipart:MBEDTLS_CIPHER_CHACHA20:256:0:16:-1:0:16:0:16

ChaCha20 Encrypt and decrypt 16 bytes in multiple parts 3
depends_on:MBEDTLS_CHACHA20_C
enc_dec_buf_multipart:MBEDTLS_CIPHER_CHACHA20:256:1:15:-1:1:15:1:15

ChaCha20 Encrypt and decrypt 16 bytes in multiple parts 4
depends_on:MBEDTLS_CHACHA20_C
enc_dec_buf_multipart:MBEDTLS_CIPHER_CHACHA20:256:15:1:-1:15:1:15:1

ChaCha20 Encrypt and decrypt 22 bytes in multiple parts 1
depends_on:MBEDTLS_CHACHA20_C
enc_dec_buf_multipart:MBEDTLS_CIPHER_CHACHA20:256:15:7:-1:15:7:15:7

ChaCha20 Encrypt and decrypt 22 bytes in multiple parts 2
depends_on:MBEDTLS_CHACHA20_C
enc_dec_buf_multipart:MBEDTLS_CIPHER_CHACHA20:256:7:15:-1:7:15:7:15

ChaCha20 Encrypt and decrypt 22 bytes in multiple parts 3
depends_on:MBEDTLS_CHACHA20_C
enc_dec_buf_multipart:MBEDTLS_CIPHER_CHACHA20:256:16:6:-1:16:6:16:6

ChaCha20 Encrypt and decrypt 22 bytes in multiple parts 4
depends_on:MBEDTLS_CHACHA20_C
enc_dec_buf_multipart:MBEDTLS_CIPHER_CHACHA20:256:6:16:-1:6:16:6:16

ChaCha20 Encrypt and decrypt 32 bytes in multiple parts
depends_on:MBEDTLS_CHACHA20_C
enc_dec_buf_multipart:MBEDTLS_CIPHER_CHACHA20:256:16:16:-1:16:16:16:16

ChaCha20 IV Length 0
depends_on:MBEDTLS_CHACHA20_C
iv_len_validity:MBEDTLS_CIPHER_CHACHA20:"CHACHA20":0:MBEDTLS_ERR_CIPHER_BAD_INPUT_DATA

ChaCha20 IV Length 11
depends_on:MBEDTLS_CHACHA20_C
iv_len_validity:MBEDTLS_CIPHER_CHACHA20:"CHACHA20":11:MBEDTLS_ERR_CIPHER_BAD_INPUT_DATA

ChaCha20 IV Length 12
depends_on:MBEDTLS_CHACHA20_C
iv_len_validity:MBEDTLS_CIPHER_CHACHA20:"CHACHA20":12:0

ChaCha20 IV Length 13
depends_on:MBEDTLS_CHACHA20_C
iv_len_validity:MBEDTLS_CIPHER_CHACHA20:"CHACHA20":13:MBEDTLS_ERR_CIPHER_BAD_INPUT_DATA

ChaCha20 IV Length 16
depends_on:MBEDTLS_CHACHA20_C
iv_len_validity:MBEDTLS_CIPHER_CHACHA20:"CHACHA20":16:MBEDTLS_ERR_CIPHER_BAD_INPUT_DATA
