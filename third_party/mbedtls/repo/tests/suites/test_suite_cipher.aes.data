AES-128 CBC - Decrypt empty buffer
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
dec_empty_buf:MBEDTLS_CIPHER_AES_128_CBC:0:0

AES-128 XTS - Decrypt empty buffer
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_XTS
dec_empty_buf:MBEDTLS_CIPHER_AES_128_XTS:MBEDTLS_ERR_AES_INVALID_INPUT_LENGTH:0

AES-128 CBC - Encrypt and decrypt 0 bytes with PKCS7 padding
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf:MBEDTLS_CIPHER_AES_128_CBC:"AES-128-CBC":128:0:MBEDTLS_PADDING_PKCS7

AES-128 CBC - Encrypt and decrypt 1 byte with PKCS7 padding
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf:MBEDTLS_CIPHER_AES_128_CBC:"AES-128-CBC":128:1:MBEDTLS_PADDING_PKCS7

AES-128 CBC - Encrypt and decrypt 2 bytes with PKCS7 padding
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf:MBEDTLS_CIPHER_AES_128_CBC:"AES-128-CBC":128:2:MBEDTLS_PADDING_PKCS7

AES-128 CBC - Encrypt and decrypt 7 bytes with PKCS7 padding
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf:MBEDTLS_CIPHER_AES_128_CBC:"AES-128-CBC":128:7:MBEDTLS_PADDING_PKCS7

AES-128 CBC - Encrypt and decrypt 8 bytes with PKCS7 padding
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf:MBEDTLS_CIPHER_AES_128_CBC:"AES-128-CBC":128:8:MBEDTLS_PADDING_PKCS7

AES-128 CBC - Encrypt and decrypt 9 bytes with PKCS7 padding
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf:MBEDTLS_CIPHER_AES_128_CBC:"AES-128-CBC":128:9:MBEDTLS_PADDING_PKCS7

AES-128 CBC - Encrypt and decrypt 15 bytes with PKCS7 padding
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf:MBEDTLS_CIPHER_AES_128_CBC:"AES-128-CBC":128:15:MBEDTLS_PADDING_PKCS7

AES-128 CBC - Encrypt and decrypt 16 bytes with PKCS7 padding
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf:MBEDTLS_CIPHER_AES_128_CBC:"AES-128-CBC":128:16:MBEDTLS_PADDING_PKCS7

AES-128 CBC - Encrypt and decrypt 17 bytes with PKCS7 padding
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf:MBEDTLS_CIPHER_AES_128_CBC:"AES-128-CBC":128:17:MBEDTLS_PADDING_PKCS7

AES-128 CBC - Encrypt and decrypt 31 bytes with PKCS7 padding
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf:MBEDTLS_CIPHER_AES_128_CBC:"AES-128-CBC":128:31:MBEDTLS_PADDING_PKCS7

AES-128 CBC - Encrypt and decrypt 32 bytes with PKCS7 padding
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf:MBEDTLS_CIPHER_AES_128_CBC:"AES-128-CBC":128:32:MBEDTLS_PADDING_PKCS7

AES-128 CBC - Encrypt and decrypt 33 bytes with PKCS7 padding
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf:MBEDTLS_CIPHER_AES_128_CBC:"AES-128-CBC":128:33:MBEDTLS_PADDING_PKCS7

AES-128 CBC - Encrypt and decrypt 47 bytes with PKCS7 padding
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf:MBEDTLS_CIPHER_AES_128_CBC:"AES-128-CBC":128:47:MBEDTLS_PADDING_PKCS7

AES-128 CBC - Encrypt and decrypt 48 bytes with PKCS7 padding
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf:MBEDTLS_CIPHER_AES_128_CBC:"AES-128-CBC":128:48:MBEDTLS_PADDING_PKCS7

AES-128 CBC - Encrypt and decrypt 49 bytes with PKCS7 padding
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf:MBEDTLS_CIPHER_AES_128_CBC:"AES-128-CBC":128:49:MBEDTLS_PADDING_PKCS7

AES-128 CBC - Encrypt and decrypt 0 bytes with one and zeros padding
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ONE_AND_ZEROS
enc_dec_buf:MBEDTLS_CIPHER_AES_128_CBC:"AES-128-CBC":128:0:MBEDTLS_PADDING_ONE_AND_ZEROS

AES-128 CBC - Encrypt and decrypt 1 byte with one and zeros padding
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ONE_AND_ZEROS
enc_dec_buf:MBEDTLS_CIPHER_AES_128_CBC:"AES-128-CBC":128:1:MBEDTLS_PADDING_ONE_AND_ZEROS

AES-128 CBC - Encrypt and decrypt 2 bytes with one and zeros padding
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ONE_AND_ZEROS
enc_dec_buf:MBEDTLS_CIPHER_AES_128_CBC:"AES-128-CBC":128:2:MBEDTLS_PADDING_ONE_AND_ZEROS

AES-128 CBC - Encrypt and decrypt 7 bytes with one and zeros padding
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ONE_AND_ZEROS
enc_dec_buf:MBEDTLS_CIPHER_AES_128_CBC:"AES-128-CBC":128:7:MBEDTLS_PADDING_ONE_AND_ZEROS

AES-128 CBC - Encrypt and decrypt 8 bytes with one and zeros padding
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ONE_AND_ZEROS
enc_dec_buf:MBEDTLS_CIPHER_AES_128_CBC:"AES-128-CBC":128:8:MBEDTLS_PADDING_ONE_AND_ZEROS

AES-128 CBC - Encrypt and decrypt 9 bytes with one and zeros padding
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ONE_AND_ZEROS
enc_dec_buf:MBEDTLS_CIPHER_AES_128_CBC:"AES-128-CBC":128:9:MBEDTLS_PADDING_ONE_AND_ZEROS

AES-128 CBC - Encrypt and decrypt 15 bytes with one and zeros padding
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ONE_AND_ZEROS
enc_dec_buf:MBEDTLS_CIPHER_AES_128_CBC:"AES-128-CBC":128:15:MBEDTLS_PADDING_ONE_AND_ZEROS

AES-128 CBC - Encrypt and decrypt 16 bytes with one and zeros padding
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ONE_AND_ZEROS
enc_dec_buf:MBEDTLS_CIPHER_AES_128_CBC:"AES-128-CBC":128:16:MBEDTLS_PADDING_ONE_AND_ZEROS

AES-128 CBC - Encrypt and decrypt 17 bytes with one and zeros padding
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ONE_AND_ZEROS
enc_dec_buf:MBEDTLS_CIPHER_AES_128_CBC:"AES-128-CBC":128:17:MBEDTLS_PADDING_ONE_AND_ZEROS

AES-128 CBC - Encrypt and decrypt 31 bytes with one and zeros padding
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ONE_AND_ZEROS
enc_dec_buf:MBEDTLS_CIPHER_AES_128_CBC:"AES-128-CBC":128:31:MBEDTLS_PADDING_ONE_AND_ZEROS

AES-128 CBC - Encrypt and decrypt 32 bytes with one and zeros padding
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ONE_AND_ZEROS
enc_dec_buf:MBEDTLS_CIPHER_AES_128_CBC:"AES-128-CBC":128:32:MBEDTLS_PADDING_ONE_AND_ZEROS

AES-128 CBC - Encrypt and decrypt 33 bytes with one and zeros padding
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ONE_AND_ZEROS
enc_dec_buf:MBEDTLS_CIPHER_AES_128_CBC:"AES-128-CBC":128:33:MBEDTLS_PADDING_ONE_AND_ZEROS

AES-128 CBC - Encrypt and decrypt 47 bytes with one and zeros padding
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ONE_AND_ZEROS
enc_dec_buf:MBEDTLS_CIPHER_AES_128_CBC:"AES-128-CBC":128:47:MBEDTLS_PADDING_ONE_AND_ZEROS

AES-128 CBC - Encrypt and decrypt 48 bytes with one and zeros padding
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ONE_AND_ZEROS
enc_dec_buf:MBEDTLS_CIPHER_AES_128_CBC:"AES-128-CBC":128:48:MBEDTLS_PADDING_ONE_AND_ZEROS

AES-128 CBC - Encrypt and decrypt 49 bytes with one and zeros padding
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ONE_AND_ZEROS
enc_dec_buf:MBEDTLS_CIPHER_AES_128_CBC:"AES-128-CBC":128:49:MBEDTLS_PADDING_ONE_AND_ZEROS

AES-128 CBC - Encrypt and decrypt 0 bytes with zeros and len padding
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ZEROS_AND_LEN
enc_dec_buf:MBEDTLS_CIPHER_AES_128_CBC:"AES-128-CBC":128:0:MBEDTLS_PADDING_ZEROS_AND_LEN

AES-128 CBC - Encrypt and decrypt 1 byte with zeros and len padding
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ZEROS_AND_LEN
enc_dec_buf:MBEDTLS_CIPHER_AES_128_CBC:"AES-128-CBC":128:1:MBEDTLS_PADDING_ZEROS_AND_LEN

AES-128 CBC - Encrypt and decrypt 2 bytes with zeros and len padding
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ZEROS_AND_LEN
enc_dec_buf:MBEDTLS_CIPHER_AES_128_CBC:"AES-128-CBC":128:2:MBEDTLS_PADDING_ZEROS_AND_LEN

AES-128 CBC - Encrypt and decrypt 7 bytes with zeros and len padding
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ZEROS_AND_LEN
enc_dec_buf:MBEDTLS_CIPHER_AES_128_CBC:"AES-128-CBC":128:7:MBEDTLS_PADDING_ZEROS_AND_LEN

AES-128 CBC - Encrypt and decrypt 8 bytes with zeros and len padding
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ZEROS_AND_LEN
enc_dec_buf:MBEDTLS_CIPHER_AES_128_CBC:"AES-128-CBC":128:8:MBEDTLS_PADDING_ZEROS_AND_LEN

AES-128 CBC - Encrypt and decrypt 9 bytes with zeros and len padding
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ZEROS_AND_LEN
enc_dec_buf:MBEDTLS_CIPHER_AES_128_CBC:"AES-128-CBC":128:9:MBEDTLS_PADDING_ZEROS_AND_LEN

AES-128 CBC - Encrypt and decrypt 15 bytes with zeros and len padding
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ZEROS_AND_LEN
enc_dec_buf:MBEDTLS_CIPHER_AES_128_CBC:"AES-128-CBC":128:15:MBEDTLS_PADDING_ZEROS_AND_LEN

AES-128 CBC - Encrypt and decrypt 16 bytes with zeros and len padding
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ZEROS_AND_LEN
enc_dec_buf:MBEDTLS_CIPHER_AES_128_CBC:"AES-128-CBC":128:16:MBEDTLS_PADDING_ZEROS_AND_LEN

AES-128 CBC - Encrypt and decrypt 17 bytes with zeros and len padding
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ZEROS_AND_LEN
enc_dec_buf:MBEDTLS_CIPHER_AES_128_CBC:"AES-128-CBC":128:17:MBEDTLS_PADDING_ZEROS_AND_LEN

AES-128 CBC - Encrypt and decrypt 31 bytes with zeros and len padding
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ZEROS_AND_LEN
enc_dec_buf:MBEDTLS_CIPHER_AES_128_CBC:"AES-128-CBC":128:31:MBEDTLS_PADDING_ZEROS_AND_LEN

AES-128 CBC - Encrypt and decrypt 32 bytes with zeros and len padding
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ZEROS_AND_LEN
enc_dec_buf:MBEDTLS_CIPHER_AES_128_CBC:"AES-128-CBC":128:32:MBEDTLS_PADDING_ZEROS_AND_LEN

AES-128 CBC - Encrypt and decrypt 33 bytes with zeros and len padding
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ZEROS_AND_LEN
enc_dec_buf:MBEDTLS_CIPHER_AES_128_CBC:"AES-128-CBC":128:33:MBEDTLS_PADDING_ZEROS_AND_LEN

AES-128 CBC - Encrypt and decrypt 47 bytes with zeros and len padding
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ZEROS_AND_LEN
enc_dec_buf:MBEDTLS_CIPHER_AES_128_CBC:"AES-128-CBC":128:47:MBEDTLS_PADDING_ZEROS_AND_LEN

AES-128 CBC - Encrypt and decrypt 48 bytes with zeros and len padding
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ZEROS_AND_LEN
enc_dec_buf:MBEDTLS_CIPHER_AES_128_CBC:"AES-128-CBC":128:48:MBEDTLS_PADDING_ZEROS_AND_LEN

AES-128 CBC - Encrypt and decrypt 49 bytes with zeros and len padding
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ZEROS_AND_LEN
enc_dec_buf:MBEDTLS_CIPHER_AES_128_CBC:"AES-128-CBC":128:49:MBEDTLS_PADDING_ZEROS_AND_LEN

AES-128 CBC - Encrypt and decrypt 0 bytes with zeros padding
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ZEROS
enc_dec_buf:MBEDTLS_CIPHER_AES_128_CBC:"AES-128-CBC":128:0:MBEDTLS_PADDING_ZEROS

AES-128 CBC - Encrypt and decrypt 1 byte with zeros padding
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ZEROS
enc_dec_buf:MBEDTLS_CIPHER_AES_128_CBC:"AES-128-CBC":128:1:MBEDTLS_PADDING_ZEROS

AES-128 CBC - Encrypt and decrypt 2 bytes with zeros padding
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ZEROS
enc_dec_buf:MBEDTLS_CIPHER_AES_128_CBC:"AES-128-CBC":128:2:MBEDTLS_PADDING_ZEROS

AES-128 CBC - Encrypt and decrypt 7 bytes with zeros padding
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ZEROS
enc_dec_buf:MBEDTLS_CIPHER_AES_128_CBC:"AES-128-CBC":128:7:MBEDTLS_PADDING_ZEROS

AES-128 CBC - Encrypt and decrypt 8 bytes with zeros padding
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ZEROS
enc_dec_buf:MBEDTLS_CIPHER_AES_128_CBC:"AES-128-CBC":128:8:MBEDTLS_PADDING_ZEROS

AES-128 CBC - Encrypt and decrypt 9 bytes with zeros padding
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ZEROS
enc_dec_buf:MBEDTLS_CIPHER_AES_128_CBC:"AES-128-CBC":128:9:MBEDTLS_PADDING_ZEROS

AES-128 CBC - Encrypt and decrypt 15 bytes with zeros padding
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ZEROS
enc_dec_buf:MBEDTLS_CIPHER_AES_128_CBC:"AES-128-CBC":128:15:MBEDTLS_PADDING_ZEROS

AES-128 CBC - Encrypt and decrypt 16 bytes with zeros padding
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ZEROS
enc_dec_buf:MBEDTLS_CIPHER_AES_128_CBC:"AES-128-CBC":128:16:MBEDTLS_PADDING_ZEROS

AES-128 CBC - Encrypt and decrypt 17 bytes with zeros padding
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ZEROS
enc_dec_buf:MBEDTLS_CIPHER_AES_128_CBC:"AES-128-CBC":128:17:MBEDTLS_PADDING_ZEROS

AES-128 CBC - Encrypt and decrypt 31 bytes with zeros padding
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ZEROS
enc_dec_buf:MBEDTLS_CIPHER_AES_128_CBC:"AES-128-CBC":128:31:MBEDTLS_PADDING_ZEROS

AES-128 CBC - Encrypt and decrypt 32 bytes with zeros padding
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ZEROS
enc_dec_buf:MBEDTLS_CIPHER_AES_128_CBC:"AES-128-CBC":128:32:MBEDTLS_PADDING_ZEROS

AES-128 CBC - Encrypt and decrypt 33 bytes with zeros padding
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ZEROS
enc_dec_buf:MBEDTLS_CIPHER_AES_128_CBC:"AES-128-CBC":128:33:MBEDTLS_PADDING_ZEROS

AES-128 CBC - Encrypt and decrypt 47 bytes with zeros padding
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ZEROS
enc_dec_buf:MBEDTLS_CIPHER_AES_128_CBC:"AES-128-CBC":128:47:MBEDTLS_PADDING_ZEROS

AES-128 CBC - Encrypt and decrypt 48 bytes with zeros padding
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ZEROS
enc_dec_buf:MBEDTLS_CIPHER_AES_128_CBC:"AES-128-CBC":128:48:MBEDTLS_PADDING_ZEROS

AES-128 CBC - Encrypt and decrypt 49 bytes with zeros padding
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ZEROS
enc_dec_buf:MBEDTLS_CIPHER_AES_128_CBC:"AES-128-CBC":128:49:MBEDTLS_PADDING_ZEROS

AES-128 CBC - Encrypt and decrypt 0 bytes with no padding
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf:MBEDTLS_CIPHER_AES_128_CBC:"AES-128-CBC":128:0:MBEDTLS_PADDING_NONE

AES-128 CBC - Encrypt and decrypt 16 bytes with no padding
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC
enc_dec_buf:MBEDTLS_CIPHER_AES_128_CBC:"AES-128-CBC":128:16:MBEDTLS_PADDING_NONE

AES-128 CBC - Encrypt and decrypt 32 bytes with no padding
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC
enc_dec_buf:MBEDTLS_CIPHER_AES_128_CBC:"AES-128-CBC":128:32:MBEDTLS_PADDING_NONE

AES-128 CBC - Encrypt and decrypt 48 bytes with no padding
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC
enc_dec_buf:MBEDTLS_CIPHER_AES_128_CBC:"AES-128-CBC":128:48:MBEDTLS_PADDING_NONE

AES-128 CBC - Try encrypting 1 bytes with no padding
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_fail:MBEDTLS_CIPHER_AES_128_CBC:MBEDTLS_PADDING_NONE:128:1:MBEDTLS_ERR_CIPHER_FULL_BLOCK_EXPECTED

AES-128 CBC - Try encrypting 2 bytes with no padding
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_fail:MBEDTLS_CIPHER_AES_128_CBC:MBEDTLS_PADDING_NONE:128:2:MBEDTLS_ERR_CIPHER_FULL_BLOCK_EXPECTED

AES-128 CBC - Try encrypting 7 bytes with no padding
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_fail:MBEDTLS_CIPHER_AES_128_CBC:MBEDTLS_PADDING_NONE:128:7:MBEDTLS_ERR_CIPHER_FULL_BLOCK_EXPECTED

AES-128 CBC - Try encrypting 8 bytes with no padding
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_fail:MBEDTLS_CIPHER_AES_128_CBC:MBEDTLS_PADDING_NONE:128:8:MBEDTLS_ERR_CIPHER_FULL_BLOCK_EXPECTED

AES-128 CBC - Try encrypting 9 bytes with no padding
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_fail:MBEDTLS_CIPHER_AES_128_CBC:MBEDTLS_PADDING_NONE:128:9:MBEDTLS_ERR_CIPHER_FULL_BLOCK_EXPECTED

AES-128 CBC - Try encrypting 15 bytes with no padding
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_fail:MBEDTLS_CIPHER_AES_128_CBC:MBEDTLS_PADDING_NONE:128:15:MBEDTLS_ERR_CIPHER_FULL_BLOCK_EXPECTED

AES-128 CBC - Try encrypting 17 bytes with no padding
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_fail:MBEDTLS_CIPHER_AES_128_CBC:MBEDTLS_PADDING_NONE:128:17:MBEDTLS_ERR_CIPHER_FULL_BLOCK_EXPECTED

AES-128 CBC - Try encrypting 31 bytes with no padding
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_fail:MBEDTLS_CIPHER_AES_128_CBC:MBEDTLS_PADDING_NONE:128:31:MBEDTLS_ERR_CIPHER_FULL_BLOCK_EXPECTED

AES-128 CBC - Try encrypting 33 bytes with no padding
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_fail:MBEDTLS_CIPHER_AES_128_CBC:MBEDTLS_PADDING_NONE:128:33:MBEDTLS_ERR_CIPHER_FULL_BLOCK_EXPECTED

AES-128 CBC - Try encrypting 47 bytes with no padding
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_fail:MBEDTLS_CIPHER_AES_128_CBC:MBEDTLS_PADDING_NONE:128:47:MBEDTLS_ERR_CIPHER_FULL_BLOCK_EXPECTED

AES-128 CBC - Try encrypting 49 bytes with no padding
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_fail:MBEDTLS_CIPHER_AES_128_CBC:MBEDTLS_PADDING_NONE:128:49:MBEDTLS_ERR_CIPHER_FULL_BLOCK_EXPECTED

AES-128 CBC - Encrypt and decrypt 0 bytes in multiple parts with PKCS7 padding
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_128_CBC:128:0:0:MBEDTLS_PADDING_PKCS7:0:0:0:0

AES-128 CBC - Encrypt and decrypt 1 bytes in multiple parts with PKCS7 padding 1
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_128_CBC:128:1:0:MBEDTLS_PADDING_PKCS7:0:0:0:0

AES-128 CBC - Encrypt and decrypt 1 bytes in multiple parts with PKCS7 padding 2
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_128_CBC:128:0:1:MBEDTLS_PADDING_PKCS7:0:0:0:0

AES-128 CBC - Encrypt and decrypt 16 bytes in multiple parts with PKCS7 padding 1
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_128_CBC:128:16:0:MBEDTLS_PADDING_PKCS7:16:0:0:16

AES-128 CBC - Encrypt and decrypt 16 bytes in multiple parts with PKCS7 padding 2
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_128_CBC:128:0:16:MBEDTLS_PADDING_PKCS7:0:16:0:16

AES-128 CBC - Encrypt and decrypt 16 bytes in multiple parts with PKCS7 padding 3
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_128_CBC:128:1:15:MBEDTLS_PADDING_PKCS7:0:16:0:16

AES-128 CBC - Encrypt and decrypt 16 bytes in multiple parts with PKCS7 padding 4
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_128_CBC:128:15:1:MBEDTLS_PADDING_PKCS7:0:16:0:16

AES-128 CBC - Encrypt and decrypt 22 bytes in multiple parts with PKCS7 padding 1
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_128_CBC:128:15:7:MBEDTLS_PADDING_PKCS7:0:16:0:16

AES-128 CBC - Encrypt and decrypt 22 bytes in multiple parts with PKCS7 padding 2
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_128_CBC:128:16:6:MBEDTLS_PADDING_PKCS7:16:0:0:16

AES-128 CBC - Encrypt and decrypt 23 bytes in multiple parts with PKCS7 padding 1
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_128_CBC:128:17:6:MBEDTLS_PADDING_PKCS7:16:0:16:0

AES-128 CBC - Encrypt and decrypt 32 bytes in multiple parts with PKCS7 padding 1
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_128_CBC:128:16:16:MBEDTLS_PADDING_PKCS7:16:16:0:32

AES-128 CBC - Encrypt and decrypt 0 bytes in multiple parts with no padding
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_128_CBC:128:0:0:MBEDTLS_PADDING_NONE:0:0:0:0

AES-128 CBC - Encrypt and decrypt 16 bytes in multiple parts with no padding 1
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_128_CBC:128:16:0:MBEDTLS_PADDING_NONE:16:0:16:0

AES-128 CBC - Encrypt and decrypt 16 bytes in multiple parts with no padding 2
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_128_CBC:128:0:16:MBEDTLS_PADDING_NONE:0:16:0:16

AES-128 CBC - Encrypt and decrypt 16 bytes in multiple parts with no padding 3
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_128_CBC:128:1:15:MBEDTLS_PADDING_NONE:0:16:0:16

AES-128 CBC - Encrypt and decrypt 16 bytes in multiple parts with no padding 4
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_128_CBC:128:15:1:MBEDTLS_PADDING_NONE:0:16:0:16

AES-128 CBC - Encrypt and decrypt 32 bytes in multiple parts with no padding 1
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_128_CBC:128:16:16:MBEDTLS_PADDING_NONE:16:16:16:16

AES-128 CFB - Encrypt and decrypt 0 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CFB
enc_dec_buf:MBEDTLS_CIPHER_AES_128_CFB128:"AES-128-CFB128":128:0:-1

AES-128 CFB - Encrypt and decrypt 1 byte
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CFB
enc_dec_buf:MBEDTLS_CIPHER_AES_128_CFB128:"AES-128-CFB128":128:1:-1

AES-128 CFB - Encrypt and decrypt 2 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CFB
enc_dec_buf:MBEDTLS_CIPHER_AES_128_CFB128:"AES-128-CFB128":128:2:-1

AES-128 CFB - Encrypt and decrypt 7 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CFB
enc_dec_buf:MBEDTLS_CIPHER_AES_128_CFB128:"AES-128-CFB128":128:7:-1

AES-128 CFB - Encrypt and decrypt 8 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CFB
enc_dec_buf:MBEDTLS_CIPHER_AES_128_CFB128:"AES-128-CFB128":128:8:-1

AES-128 CFB - Encrypt and decrypt 9 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CFB
enc_dec_buf:MBEDTLS_CIPHER_AES_128_CFB128:"AES-128-CFB128":128:9:-1

AES-128 CFB - Encrypt and decrypt 15 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CFB
enc_dec_buf:MBEDTLS_CIPHER_AES_128_CFB128:"AES-128-CFB128":128:15:-1

AES-128 CFB - Encrypt and decrypt 16 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CFB
enc_dec_buf:MBEDTLS_CIPHER_AES_128_CFB128:"AES-128-CFB128":128:16:-1

AES-128 CFB - Encrypt and decrypt 17 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CFB
enc_dec_buf:MBEDTLS_CIPHER_AES_128_CFB128:"AES-128-CFB128":128:17:-1

AES-128 CFB - Encrypt and decrypt 31 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CFB
enc_dec_buf:MBEDTLS_CIPHER_AES_128_CFB128:"AES-128-CFB128":128:31:-1

AES-128 CFB - Encrypt and decrypt 32 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CFB
enc_dec_buf:MBEDTLS_CIPHER_AES_128_CFB128:"AES-128-CFB128":128:32:-1

AES-128 CFB - Encrypt and decrypt 33 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CFB
enc_dec_buf:MBEDTLS_CIPHER_AES_128_CFB128:"AES-128-CFB128":128:33:-1

AES-128 CFB - Encrypt and decrypt 47 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CFB
enc_dec_buf:MBEDTLS_CIPHER_AES_128_CFB128:"AES-128-CFB128":128:47:-1

AES-128 CFB - Encrypt and decrypt 48 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CFB
enc_dec_buf:MBEDTLS_CIPHER_AES_128_CFB128:"AES-128-CFB128":128:48:-1

AES-128 CFB - Encrypt and decrypt 49 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CFB
enc_dec_buf:MBEDTLS_CIPHER_AES_128_CFB128:"AES-128-CFB128":128:49:-1

AES-128 CFB - Encrypt and decrypt 0 bytes in multiple parts
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CFB
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_128_CFB128:128:0:0:-1:0:0:0:0

AES-128 CFB - Encrypt and decrypt 1 bytes in multiple parts 1
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CFB
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_128_CFB128:128:1:0:-1:1:0:1:0

AES-128 CFB - Encrypt and decrypt 1 bytes in multiple parts 2
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CFB
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_128_CFB128:128:0:1:-1:0:1:0:1

AES-128 CFB - Encrypt and decrypt 16 bytes in multiple parts 1
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CFB
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_128_CFB128:128:16:0:-1:16:0:16:0

AES-128 CFB - Encrypt and decrypt 16 bytes in multiple parts 2
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CFB
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_128_CFB128:128:0:16:-1:0:16:0:16

AES-128 CFB - Encrypt and decrypt 16 bytes in multiple parts 3
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CFB
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_128_CFB128:128:1:15:-1:1:15:1:15

AES-128 CFB - Encrypt and decrypt 16 bytes in multiple parts 4
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CFB
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_128_CFB128:128:15:1:-1:15:1:15:1

AES-128 CFB - Encrypt and decrypt 22 bytes in multiple parts 1
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CFB
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_128_CFB128:128:15:7:-1:15:7:15:7

AES-128 CFB - Encrypt and decrypt 22 bytes in multiple parts 2
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CFB
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_128_CFB128:128:16:6:-1:16:6:16:6

AES-128 CFB - Encrypt and decrypt 23 bytes in multiple parts 1
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CFB
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_128_CFB128:128:17:6:-1:17:6:17:6

AES-128 CFB - Encrypt and decrypt 32 bytes in multiple parts 1
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CFB
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_128_CFB128:128:16:16:-1:16:16:16:16

AES-128 OFB - Encrypt and decrypt 0 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_OFB
enc_dec_buf:MBEDTLS_CIPHER_AES_128_OFB:"AES-128-OFB":128:0:-1

AES-128 OFB - Encrypt and decrypt 1 byte
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_OFB
enc_dec_buf:MBEDTLS_CIPHER_AES_128_OFB:"AES-128-OFB":128:1:-1

AES-128 OFB - Encrypt and decrypt 2 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_OFB
enc_dec_buf:MBEDTLS_CIPHER_AES_128_OFB:"AES-128-OFB":128:2:-1

AES-128 OFB - Encrypt and decrypt 7 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_OFB
enc_dec_buf:MBEDTLS_CIPHER_AES_128_OFB:"AES-128-OFB":128:7:-1

AES-128 OFB - Encrypt and decrypt 8 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_OFB
enc_dec_buf:MBEDTLS_CIPHER_AES_128_OFB:"AES-128-OFB":128:8:-1

AES-128 OFB - Encrypt and decrypt 9 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_OFB
enc_dec_buf:MBEDTLS_CIPHER_AES_128_OFB:"AES-128-OFB":128:9:-1

AES-128 OFB - Encrypt and decrypt 15 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_OFB
enc_dec_buf:MBEDTLS_CIPHER_AES_128_OFB:"AES-128-OFB":128:15:-1

AES-128 OFB - Encrypt and decrypt 16 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_OFB
enc_dec_buf:MBEDTLS_CIPHER_AES_128_OFB:"AES-128-OFB":128:16:-1

AES-128 OFB - Encrypt and decrypt 17 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_OFB
enc_dec_buf:MBEDTLS_CIPHER_AES_128_OFB:"AES-128-OFB":128:17:-1

AES-128 OFB - Encrypt and decrypt 31 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_OFB
enc_dec_buf:MBEDTLS_CIPHER_AES_128_OFB:"AES-128-OFB":128:31:-1

AES-128 OFB - Encrypt and decrypt 32 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_OFB
enc_dec_buf:MBEDTLS_CIPHER_AES_128_OFB:"AES-128-OFB":128:32:-1

AES-128 OFB - Encrypt and decrypt 33 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_OFB
enc_dec_buf:MBEDTLS_CIPHER_AES_128_OFB:"AES-128-OFB":128:33:-1

AES-128 OFB - Encrypt and decrypt 47 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_OFB
enc_dec_buf:MBEDTLS_CIPHER_AES_128_OFB:"AES-128-OFB":128:47:-1

AES-128 OFB - Encrypt and decrypt 48 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_OFB
enc_dec_buf:MBEDTLS_CIPHER_AES_128_OFB:"AES-128-OFB":128:48:-1

AES-128 OFB - Encrypt and decrypt 49 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_OFB
enc_dec_buf:MBEDTLS_CIPHER_AES_128_OFB:"AES-128-OFB":128:49:-1

AES-128 OFB - Encrypt and decrypt 0 bytes in multiple parts
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_OFB
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_128_OFB:128:0:0:-1:0:0:0:0

AES-128 OFB - Encrypt and decrypt 1 bytes in multiple parts 1
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_OFB
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_128_OFB:128:1:0:-1:1:0:1:0

AES-128 OFB - Encrypt and decrypt 1 bytes in multiple parts 2
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_OFB
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_128_OFB:128:0:1:-1:0:1:0:1

AES-128 OFB - Encrypt and decrypt 16 bytes in multiple parts 1
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_OFB
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_128_OFB:128:16:0:-1:16:0:16:0

AES-128 OFB - Encrypt and decrypt 16 bytes in multiple parts 2
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_OFB
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_128_OFB:128:0:16:-1:0:16:0:16

AES-128 OFB - Encrypt and decrypt 16 bytes in multiple parts 3
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_OFB
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_128_OFB:128:1:15:-1:1:15:1:15

AES-128 OFB - Encrypt and decrypt 16 bytes in multiple parts 4
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_OFB
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_128_OFB:128:15:1:-1:15:1:15:1

AES-128 OFB - Encrypt and decrypt 22 bytes in multiple parts 1
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_OFB
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_128_OFB:128:15:7:-1:15:7:15:7

AES-128 OFB - Encrypt and decrypt 22 bytes in multiple parts 2
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_OFB
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_128_OFB:128:16:6:-1:16:6:16:6

AES-128 OFB - Encrypt and decrypt 23 bytes in multiple parts 1
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_OFB
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_128_OFB:128:17:6:-1:17:6:17:6

AES-128 OFB - Encrypt and decrypt 32 bytes in multiple parts 1
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_OFB
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_128_OFB:128:16:16:-1:16:16:16:16

AES-192 OFB - Encrypt and decrypt 0 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_OFB:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf:MBEDTLS_CIPHER_AES_192_OFB:"AES-192-OFB":192:0:-1

AES-192 OFB - Encrypt and decrypt 1 byte
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_OFB:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf:MBEDTLS_CIPHER_AES_192_OFB:"AES-192-OFB":192:1:-1

AES-192 OFB - Encrypt and decrypt 2 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_OFB:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf:MBEDTLS_CIPHER_AES_192_OFB:"AES-192-OFB":192:2:-1

AES-192 OFB - Encrypt and decrypt 7 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_OFB:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf:MBEDTLS_CIPHER_AES_192_OFB:"AES-192-OFB":192:7:-1

AES-192 OFB - Encrypt and decrypt 8 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_OFB:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf:MBEDTLS_CIPHER_AES_192_OFB:"AES-192-OFB":192:8:-1

AES-192 OFB - Encrypt and decrypt 9 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_OFB:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf:MBEDTLS_CIPHER_AES_192_OFB:"AES-192-OFB":192:9:-1

AES-192 OFB - Encrypt and decrypt 15 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_OFB:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf:MBEDTLS_CIPHER_AES_192_OFB:"AES-192-OFB":192:15:-1

AES-192 OFB - Encrypt and decrypt 16 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_OFB:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf:MBEDTLS_CIPHER_AES_192_OFB:"AES-192-OFB":192:16:-1

AES-192 OFB - Encrypt and decrypt 17 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_OFB:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf:MBEDTLS_CIPHER_AES_192_OFB:"AES-192-OFB":192:17:-1

AES-192 OFB - Encrypt and decrypt 31 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_OFB:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf:MBEDTLS_CIPHER_AES_192_OFB:"AES-192-OFB":192:31:-1

AES-192 OFB - Encrypt and decrypt 32 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_OFB:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf:MBEDTLS_CIPHER_AES_192_OFB:"AES-192-OFB":192:32:-1

AES-192 OFB - Encrypt and decrypt 33 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_OFB:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf:MBEDTLS_CIPHER_AES_192_OFB:"AES-192-OFB":192:33:-1

AES-192 OFB - Encrypt and decrypt 47 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_OFB:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf:MBEDTLS_CIPHER_AES_192_OFB:"AES-192-OFB":192:47:-1

AES-192 OFB - Encrypt and decrypt 48 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_OFB:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf:MBEDTLS_CIPHER_AES_192_OFB:"AES-192-OFB":192:48:-1

AES-192 OFB - Encrypt and decrypt 49 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_OFB:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf:MBEDTLS_CIPHER_AES_192_OFB:"AES-192-OFB":192:49:-1

AES-192 OFB - Encrypt and decrypt 0 bytes in multiple parts
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_OFB:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_192_OFB:192:0:0:-1:0:0:0:0

AES-192 OFB - Encrypt and decrypt 1 bytes in multiple parts 1
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_OFB:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_192_OFB:192:1:0:-1:1:0:1:0

AES-192 OFB - Encrypt and decrypt 1 bytes in multiple parts 2
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_OFB:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_192_OFB:192:0:1:-1:0:1:0:1

AES-192 OFB - Encrypt and decrypt 16 bytes in multiple parts 1
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_OFB:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_192_OFB:192:16:0:-1:16:0:16:0

AES-192 OFB - Encrypt and decrypt 16 bytes in multiple parts 2
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_OFB:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_192_OFB:192:0:16:-1:0:16:0:16

AES-192 OFB - Encrypt and decrypt 16 bytes in multiple parts 3
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_OFB:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_192_OFB:192:1:15:-1:1:15:1:15

AES-192 OFB - Encrypt and decrypt 16 bytes in multiple parts 4
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_OFB:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_192_OFB:192:15:1:-1:15:1:15:1

AES-192 OFB - Encrypt and decrypt 22 bytes in multiple parts 1
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_OFB:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_192_OFB:192:15:7:-1:15:7:15:7

AES-192 OFB - Encrypt and decrypt 22 bytes in multiple parts 2
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_OFB:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_192_OFB:192:16:6:-1:16:6:16:6

AES-192 OFB - Encrypt and decrypt 23 bytes in multiple parts 1
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_OFB:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_192_OFB:192:17:6:-1:17:6:17:6

AES-192 OFB - Encrypt and decrypt 32 bytes in multiple parts 1
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_OFB:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_192_OFB:192:16:16:-1:16:16:16:16

AES-256 OFB - Encrypt and decrypt 0 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_OFB:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf:MBEDTLS_CIPHER_AES_256_OFB:"AES-256-OFB":256:0:-1

AES-256 OFB - Encrypt and decrypt 1 byte
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_OFB:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf:MBEDTLS_CIPHER_AES_256_OFB:"AES-256-OFB":256:1:-1

AES-256 OFB - Encrypt and decrypt 2 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_OFB:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf:MBEDTLS_CIPHER_AES_256_OFB:"AES-256-OFB":256:2:-1

AES-256 OFB - Encrypt and decrypt 7 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_OFB:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf:MBEDTLS_CIPHER_AES_256_OFB:"AES-256-OFB":256:7:-1

AES-256 OFB - Encrypt and decrypt 8 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_OFB:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf:MBEDTLS_CIPHER_AES_256_OFB:"AES-256-OFB":256:8:-1

AES-256 OFB - Encrypt and decrypt 9 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_OFB:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf:MBEDTLS_CIPHER_AES_256_OFB:"AES-256-OFB":256:9:-1

AES-256 OFB - Encrypt and decrypt 15 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_OFB:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf:MBEDTLS_CIPHER_AES_256_OFB:"AES-256-OFB":256:15:-1

AES-256 OFB - Encrypt and decrypt 16 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_OFB:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf:MBEDTLS_CIPHER_AES_256_OFB:"AES-256-OFB":256:16:-1

AES-256 OFB - Encrypt and decrypt 17 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_OFB:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf:MBEDTLS_CIPHER_AES_256_OFB:"AES-256-OFB":256:17:-1

AES-256 OFB - Encrypt and decrypt 31 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_OFB:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf:MBEDTLS_CIPHER_AES_256_OFB:"AES-256-OFB":256:31:-1

AES-256 OFB - Encrypt and decrypt 32 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_OFB:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf:MBEDTLS_CIPHER_AES_256_OFB:"AES-256-OFB":256:32:-1

AES-256 OFB - Encrypt and decrypt 33 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_OFB:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf:MBEDTLS_CIPHER_AES_256_OFB:"AES-256-OFB":256:33:-1

AES-256 OFB - Encrypt and decrypt 47 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_OFB:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf:MBEDTLS_CIPHER_AES_256_OFB:"AES-256-OFB":256:47:-1

AES-256 OFB - Encrypt and decrypt 48 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_OFB:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf:MBEDTLS_CIPHER_AES_256_OFB:"AES-256-OFB":256:48:-1

AES-256 OFB - Encrypt and decrypt 49 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_OFB:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf:MBEDTLS_CIPHER_AES_256_OFB:"AES-256-OFB":256:49:-1

AES-256 OFB - Encrypt and decrypt 0 bytes in multiple parts
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_OFB:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_256_OFB:256:0:0:-1:0:0:0:0

AES-256 OFB - Encrypt and decrypt 1 bytes in multiple parts 1
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_OFB:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_256_OFB:256:1:0:-1:1:0:1:0

AES-256 OFB - Encrypt and decrypt 1 bytes in multiple parts 2
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_OFB:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_256_OFB:256:0:1:-1:0:1:0:1

AES-256 OFB - Encrypt and decrypt 16 bytes in multiple parts 1
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_OFB:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_256_OFB:256:16:0:-1:16:0:16:0

AES-256 OFB - Encrypt and decrypt 16 bytes in multiple parts 2
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_OFB:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_256_OFB:256:0:16:-1:0:16:0:16

AES-256 OFB - Encrypt and decrypt 16 bytes in multiple parts 3
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_OFB:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_256_OFB:256:1:15:-1:1:15:1:15

AES-256 OFB - Encrypt and decrypt 16 bytes in multiple parts 4
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_OFB:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_256_OFB:256:15:1:-1:15:1:15:1

AES-256 OFB - Encrypt and decrypt 22 bytes in multiple parts 1
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_OFB:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_256_OFB:256:15:7:-1:15:7:15:7

AES-256 OFB - Encrypt and decrypt 22 bytes in multiple parts 2
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_OFB:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_256_OFB:256:16:6:-1:16:6:16:6

AES-256 OFB - Encrypt and decrypt 23 bytes in multiple parts 1
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_OFB:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_256_OFB:256:17:6:-1:17:6:17:6

AES-256 OFB - Encrypt and decrypt 32 bytes in multiple parts 1
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_OFB:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_256_OFB:256:16:16:-1:16:16:16:16

AES-128 CCM*-NO-TAG - Encrypt and decrypt 0 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C
enc_dec_buf:MBEDTLS_CIPHER_AES_128_CCM_STAR_NO_TAG:"AES-128-CCM*-NO-TAG":128:0:-1

AES-128 CCM*-NO-TAG - Encrypt and decrypt 1 byte
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C
enc_dec_buf:MBEDTLS_CIPHER_AES_128_CCM_STAR_NO_TAG:"AES-128-CCM*-NO-TAG":128:1:-1

AES-128 CCM*-NO-TAG - Encrypt and decrypt 2 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C
enc_dec_buf:MBEDTLS_CIPHER_AES_128_CCM_STAR_NO_TAG:"AES-128-CCM*-NO-TAG":128:2:-1

AES-128 CCM*-NO-TAG - Encrypt and decrypt 7 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C
enc_dec_buf:MBEDTLS_CIPHER_AES_128_CCM_STAR_NO_TAG:"AES-128-CCM*-NO-TAG":128:7:-1

AES-128 CCM*-NO-TAG - Encrypt and decrypt 8 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C
enc_dec_buf:MBEDTLS_CIPHER_AES_128_CCM_STAR_NO_TAG:"AES-128-CCM*-NO-TAG":128:8:-1

AES-128 CCM*-NO-TAG - Encrypt and decrypt 9 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C
enc_dec_buf:MBEDTLS_CIPHER_AES_128_CCM_STAR_NO_TAG:"AES-128-CCM*-NO-TAG":128:9:-1

AES-128 CCM*-NO-TAG - Encrypt and decrypt 15 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C
enc_dec_buf:MBEDTLS_CIPHER_AES_128_CCM_STAR_NO_TAG:"AES-128-CCM*-NO-TAG":128:15:-1

AES-128 CCM*-NO-TAG - Encrypt and decrypt 16 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C
enc_dec_buf:MBEDTLS_CIPHER_AES_128_CCM_STAR_NO_TAG:"AES-128-CCM*-NO-TAG":128:16:-1

AES-128 CCM*-NO-TAG - Encrypt and decrypt 17 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C
enc_dec_buf:MBEDTLS_CIPHER_AES_128_CCM_STAR_NO_TAG:"AES-128-CCM*-NO-TAG":128:17:-1

AES-128 CCM*-NO-TAG - Encrypt and decrypt 31 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C
enc_dec_buf:MBEDTLS_CIPHER_AES_128_CCM_STAR_NO_TAG:"AES-128-CCM*-NO-TAG":128:31:-1

AES-128 CCM*-NO-TAG - Encrypt and decrypt 32 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C
enc_dec_buf:MBEDTLS_CIPHER_AES_128_CCM_STAR_NO_TAG:"AES-128-CCM*-NO-TAG":128:32:-1

AES-128 CCM*-NO-TAG - Encrypt and decrypt 33 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C
enc_dec_buf:MBEDTLS_CIPHER_AES_128_CCM_STAR_NO_TAG:"AES-128-CCM*-NO-TAG":128:33:-1

AES-128 CCM*-NO-TAG - Encrypt and decrypt 47 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C
enc_dec_buf:MBEDTLS_CIPHER_AES_128_CCM_STAR_NO_TAG:"AES-128-CCM*-NO-TAG":128:47:-1

AES-128 CCM*-NO-TAG - Encrypt and decrypt 48 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C
enc_dec_buf:MBEDTLS_CIPHER_AES_128_CCM_STAR_NO_TAG:"AES-128-CCM*-NO-TAG":128:48:-1

AES-128 CCM*-NO-TAG - Encrypt and decrypt 49 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C
enc_dec_buf:MBEDTLS_CIPHER_AES_128_CCM_STAR_NO_TAG:"AES-128-CCM*-NO-TAG":128:49:-1

AES-128 CCM*-NO-TAG - Encrypt and decrypt 0 bytes in multiple parts
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_128_CCM_STAR_NO_TAG:128:0:0:-1:0:0:0:0

AES-128 CCM*-NO-TAG - Encrypt and decrypt 1 bytes in multiple parts 1
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_128_CCM_STAR_NO_TAG:128:1:0:-1:1:0:1:0

AES-128 CCM*-NO-TAG - Encrypt and decrypt 1 bytes in multiple parts 2
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_128_CCM_STAR_NO_TAG:128:0:1:-1:0:1:0:1

AES-128 CCM*-NO-TAG - Encrypt and decrypt 16 bytes in multiple parts 1
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_128_CCM_STAR_NO_TAG:128:16:0:-1:16:0:16:0

AES-128 CCM*-NO-TAG - Encrypt and decrypt 16 bytes in multiple parts 2
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_128_CCM_STAR_NO_TAG:128:0:16:-1:0:16:0:16

AES-128 CCM*-NO-TAG - Encrypt and decrypt 16 bytes in multiple parts 3
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_128_CCM_STAR_NO_TAG:128:1:15:-1:1:15:1:15

AES-128 CCM*-NO-TAG - Encrypt and decrypt 16 bytes in multiple parts 4
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_128_CCM_STAR_NO_TAG:128:15:1:-1:15:1:15:1

AES-128 CCM*-NO-TAG - Encrypt and decrypt 22 bytes in multiple parts 1
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_128_CCM_STAR_NO_TAG:128:15:7:-1:15:7:15:7

AES-128-CCM*-NO-TAG - Encrypt and decrypt 22 bytes in multiple parts 2
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_128_CCM_STAR_NO_TAG:128:16:6:-1:16:6:16:6

AES-128-CCM*-NO-TAG - Encrypt and decrypt 23 bytes in multiple parts 1
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_128_CCM_STAR_NO_TAG:128:17:6:-1:17:6:17:6

AES-128-CCM*-NO-TAG - Encrypt and decrypt 32 bytes in multiple parts 1
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_128_CCM_STAR_NO_TAG:128:16:16:-1:16:16:16:16

AES-192-CCM*-NO-TAG - Encrypt and decrypt 0 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf:MBEDTLS_CIPHER_AES_192_CCM_STAR_NO_TAG:"AES-192-CCM*-NO-TAG":192:0:-1

AES-192-CCM*-NO-TAG - Encrypt and decrypt 1 byte
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf:MBEDTLS_CIPHER_AES_192_CCM_STAR_NO_TAG:"AES-192-CCM*-NO-TAG":192:1:-1

AES-192-CCM*-NO-TAG - Encrypt and decrypt 2 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf:MBEDTLS_CIPHER_AES_192_CCM_STAR_NO_TAG:"AES-192-CCM*-NO-TAG":192:2:-1

AES-192-CCM*-NO-TAG - Encrypt and decrypt 7 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf:MBEDTLS_CIPHER_AES_192_CCM_STAR_NO_TAG:"AES-192-CCM*-NO-TAG":192:7:-1

AES-192-CCM*-NO-TAG - Encrypt and decrypt 8 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf:MBEDTLS_CIPHER_AES_192_CCM_STAR_NO_TAG:"AES-192-CCM*-NO-TAG":192:8:-1

AES-192-CCM*-NO-TAG - Encrypt and decrypt 9 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf:MBEDTLS_CIPHER_AES_192_CCM_STAR_NO_TAG:"AES-192-CCM*-NO-TAG":192:9:-1

AES-192-CCM*-NO-TAG - Encrypt and decrypt 15 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf:MBEDTLS_CIPHER_AES_192_CCM_STAR_NO_TAG:"AES-192-CCM*-NO-TAG":192:15:-1

AES-192-CCM*-NO-TAG - Encrypt and decrypt 16 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf:MBEDTLS_CIPHER_AES_192_CCM_STAR_NO_TAG:"AES-192-CCM*-NO-TAG":192:16:-1

AES-192-CCM*-NO-TAG - Encrypt and decrypt 17 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf:MBEDTLS_CIPHER_AES_192_CCM_STAR_NO_TAG:"AES-192-CCM*-NO-TAG":192:17:-1

AES-192-CCM*-NO-TAG - Encrypt and decrypt 31 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf:MBEDTLS_CIPHER_AES_192_CCM_STAR_NO_TAG:"AES-192-CCM*-NO-TAG":192:31:-1

AES-192-CCM*-NO-TAG - Encrypt and decrypt 32 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf:MBEDTLS_CIPHER_AES_192_CCM_STAR_NO_TAG:"AES-192-CCM*-NO-TAG":192:32:-1

AES-192-CCM*-NO-TAG - Encrypt and decrypt 33 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf:MBEDTLS_CIPHER_AES_192_CCM_STAR_NO_TAG:"AES-192-CCM*-NO-TAG":192:33:-1

AES-192-CCM*-NO-TAG - Encrypt and decrypt 47 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf:MBEDTLS_CIPHER_AES_192_CCM_STAR_NO_TAG:"AES-192-CCM*-NO-TAG":192:47:-1

AES-192-CCM*-NO-TAG - Encrypt and decrypt 48 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf:MBEDTLS_CIPHER_AES_192_CCM_STAR_NO_TAG:"AES-192-CCM*-NO-TAG":192:48:-1

AES-192-CCM*-NO-TAG - Encrypt and decrypt 49 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf:MBEDTLS_CIPHER_AES_192_CCM_STAR_NO_TAG:"AES-192-CCM*-NO-TAG":192:49:-1

AES-192-CCM*-NO-TAG - Encrypt and decrypt 0 bytes in multiple parts
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_192_CCM_STAR_NO_TAG:192:0:0:-1:0:0:0:0

AES-192-CCM*-NO-TAG - Encrypt and decrypt 1 bytes in multiple parts 1
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_192_CCM_STAR_NO_TAG:192:1:0:-1:1:0:1:0

AES-192-CCM*-NO-TAG - Encrypt and decrypt 1 bytes in multiple parts 2
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_192_CCM_STAR_NO_TAG:192:0:1:-1:0:1:0:1

AES-192-CCM*-NO-TAG - Encrypt and decrypt 16 bytes in multiple parts 1
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_192_CCM_STAR_NO_TAG:192:16:0:-1:16:0:16:0

AES-192-CCM*-NO-TAG - Encrypt and decrypt 16 bytes in multiple parts 2
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_192_CCM_STAR_NO_TAG:192:0:16:-1:0:16:0:16

AES-192-CCM*-NO-TAG - Encrypt and decrypt 16 bytes in multiple parts 3
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_192_CCM_STAR_NO_TAG:192:1:15:-1:1:15:1:15

AES-192-CCM*-NO-TAG - Encrypt and decrypt 16 bytes in multiple parts 4
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_192_CCM_STAR_NO_TAG:192:15:1:-1:15:1:15:1

AES-192-CCM*-NO-TAG - Encrypt and decrypt 22 bytes in multiple parts 1
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_192_CCM_STAR_NO_TAG:192:15:7:-1:15:7:15:7

AES-192-CCM*-NO-TAG - Encrypt and decrypt 22 bytes in multiple parts 2
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_192_CCM_STAR_NO_TAG:192:16:6:-1:16:6:16:6

AES-192-CCM*-NO-TAG - Encrypt and decrypt 23 bytes in multiple parts 1
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_192_CCM_STAR_NO_TAG:192:17:6:-1:17:6:17:6

AES-192-CCM*-NO-TAG - Encrypt and decrypt 32 bytes in multiple parts 1
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_192_CCM_STAR_NO_TAG:192:16:16:-1:16:16:16:16

AES-256-CCM*-NO-TAG - Encrypt and decrypt 0 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf:MBEDTLS_CIPHER_AES_256_CCM_STAR_NO_TAG:"AES-256-CCM*-NO-TAG":256:0:-1

AES-256-CCM*-NO-TAG - Encrypt and decrypt 1 byte
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf:MBEDTLS_CIPHER_AES_256_CCM_STAR_NO_TAG:"AES-256-CCM*-NO-TAG":256:1:-1

AES-256-CCM*-NO-TAG - Encrypt and decrypt 2 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf:MBEDTLS_CIPHER_AES_256_CCM_STAR_NO_TAG:"AES-256-CCM*-NO-TAG":256:2:-1

AES-256-CCM*-NO-TAG - Encrypt and decrypt 7 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf:MBEDTLS_CIPHER_AES_256_CCM_STAR_NO_TAG:"AES-256-CCM*-NO-TAG":256:7:-1

AES-256-CCM*-NO-TAG - Encrypt and decrypt 8 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf:MBEDTLS_CIPHER_AES_256_CCM_STAR_NO_TAG:"AES-256-CCM*-NO-TAG":256:8:-1

AES-256-CCM*-NO-TAG - Encrypt and decrypt 9 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf:MBEDTLS_CIPHER_AES_256_CCM_STAR_NO_TAG:"AES-256-CCM*-NO-TAG":256:9:-1

AES-256-CCM*-NO-TAG - Encrypt and decrypt 15 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf:MBEDTLS_CIPHER_AES_256_CCM_STAR_NO_TAG:"AES-256-CCM*-NO-TAG":256:15:-1

AES-256-CCM*-NO-TAG - Encrypt and decrypt 16 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf:MBEDTLS_CIPHER_AES_256_CCM_STAR_NO_TAG:"AES-256-CCM*-NO-TAG":256:16:-1

AES-256-CCM*-NO-TAG - Encrypt and decrypt 17 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf:MBEDTLS_CIPHER_AES_256_CCM_STAR_NO_TAG:"AES-256-CCM*-NO-TAG":256:17:-1

AES-256-CCM*-NO-TAG - Encrypt and decrypt 31 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf:MBEDTLS_CIPHER_AES_256_CCM_STAR_NO_TAG:"AES-256-CCM*-NO-TAG":256:31:-1

AES-256-CCM*-NO-TAG - Encrypt and decrypt 32 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf:MBEDTLS_CIPHER_AES_256_CCM_STAR_NO_TAG:"AES-256-CCM*-NO-TAG":256:32:-1

AES-256-CCM*-NO-TAG - Encrypt and decrypt 33 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf:MBEDTLS_CIPHER_AES_256_CCM_STAR_NO_TAG:"AES-256-CCM*-NO-TAG":256:33:-1

AES-256-CCM*-NO-TAG - Encrypt and decrypt 47 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf:MBEDTLS_CIPHER_AES_256_CCM_STAR_NO_TAG:"AES-256-CCM*-NO-TAG":256:47:-1

AES-256-CCM*-NO-TAG - Encrypt and decrypt 48 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf:MBEDTLS_CIPHER_AES_256_CCM_STAR_NO_TAG:"AES-256-CCM*-NO-TAG":256:48:-1

AES-256-CCM*-NO-TAG - Encrypt and decrypt 49 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf:MBEDTLS_CIPHER_AES_256_CCM_STAR_NO_TAG:"AES-256-CCM*-NO-TAG":256:49:-1

AES-256-CCM*-NO-TAG - Encrypt and decrypt 0 bytes in multiple parts
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_256_CCM_STAR_NO_TAG:256:0:0:-1:0:0:0:0

AES-256-CCM*-NO-TAG - Encrypt and decrypt 1 bytes in multiple parts 1
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_256_CCM_STAR_NO_TAG:256:1:0:-1:1:0:1:0

AES-256-CCM*-NO-TAG - Encrypt and decrypt 1 bytes in multiple parts 2
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_256_CCM_STAR_NO_TAG:256:0:1:-1:0:1:0:1

AES-256-CCM*-NO-TAG - Encrypt and decrypt 16 bytes in multiple parts 1
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_256_CCM_STAR_NO_TAG:256:16:0:-1:16:0:16:0

AES-256-CCM*-NO-TAG - Encrypt and decrypt 16 bytes in multiple parts 2
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_256_CCM_STAR_NO_TAG:256:0:16:-1:0:16:0:16

AES-256-CCM*-NO-TAG - Encrypt and decrypt 16 bytes in multiple parts 3
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_256_CCM_STAR_NO_TAG:256:1:15:-1:1:15:1:15

AES-256-CCM*-NO-TAG - Encrypt and decrypt 16 bytes in multiple parts 4
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_256_CCM_STAR_NO_TAG:256:15:1:-1:15:1:15:1

AES-256-CCM*-NO-TAG - Encrypt and decrypt 22 bytes in multiple parts 1
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_256_CCM_STAR_NO_TAG:256:15:7:-1:15:7:15:7

AES-256-CCM*-NO-TAG - Encrypt and decrypt 22 bytes in multiple parts 2
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_256_CCM_STAR_NO_TAG:256:16:6:-1:16:6:16:6

AES-256-CCM*-NO-TAG - Encrypt and decrypt 23 bytes in multiple parts 1
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_256_CCM_STAR_NO_TAG:256:17:6:-1:17:6:17:6

AES-256-CCM*-NO-TAG - Encrypt and decrypt 32 bytes in multiple parts 1
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_256_CCM_STAR_NO_TAG:256:16:16:-1:16:16:16:16

AES-128 XTS - Encrypt and decrypt 16 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_XTS
enc_dec_buf:MBEDTLS_CIPHER_AES_128_XTS:"AES-128-XTS":256:16:-1

AES-128 XTS - Encrypt and decrypt 17 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_XTS
enc_dec_buf:MBEDTLS_CIPHER_AES_128_XTS:"AES-128-XTS":256:17:-1

AES-128 XTS - Encrypt and decrypt 31 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_XTS
enc_dec_buf:MBEDTLS_CIPHER_AES_128_XTS:"AES-128-XTS":256:31:-1

AES-128 XTS - Encrypt and decrypt 32 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_XTS
enc_dec_buf:MBEDTLS_CIPHER_AES_128_XTS:"AES-128-XTS":256:32:-1

AES-128 XTS - Encrypt and decrypt 33 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_XTS
enc_dec_buf:MBEDTLS_CIPHER_AES_128_XTS:"AES-128-XTS":256:33:-1

AES-128 XTS - Encrypt and decrypt 47 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_XTS
enc_dec_buf:MBEDTLS_CIPHER_AES_128_XTS:"AES-128-XTS":256:47:-1

AES-128 XTS - Encrypt and decrypt 48 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_XTS
enc_dec_buf:MBEDTLS_CIPHER_AES_128_XTS:"AES-128-XTS":256:48:-1

AES-128 XTS - Encrypt and decrypt 49 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_XTS
enc_dec_buf:MBEDTLS_CIPHER_AES_128_XTS:"AES-128-XTS":256:49:-1

AES-256 XTS - Encrypt and decrypt 16 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_XTS:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf:MBEDTLS_CIPHER_AES_256_XTS:"AES-256-XTS":512:16:-1

AES-256 XTS - Encrypt and decrypt 17 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_XTS:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf:MBEDTLS_CIPHER_AES_256_XTS:"AES-256-XTS":512:17:-1

AES-256 XTS - Encrypt and decrypt 31 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_XTS:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf:MBEDTLS_CIPHER_AES_256_XTS:"AES-256-XTS":512:31:-1

AES-256 XTS - Encrypt and decrypt 32 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_XTS:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf:MBEDTLS_CIPHER_AES_256_XTS:"AES-256-XTS":512:32:-1

AES-256 XTS - Encrypt and decrypt 33 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_XTS:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf:MBEDTLS_CIPHER_AES_256_XTS:"AES-256-XTS":512:33:-1

AES-256 XTS - Encrypt and decrypt 47 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_XTS:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf:MBEDTLS_CIPHER_AES_256_XTS:"AES-256-XTS":512:47:-1

AES-256 XTS - Encrypt and decrypt 48 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_XTS:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf:MBEDTLS_CIPHER_AES_256_XTS:"AES-256-XTS":512:48:-1

AES-256 XTS - Encrypt and decrypt 49 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_XTS:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf:MBEDTLS_CIPHER_AES_256_XTS:"AES-256-XTS":512:49:-1

AES-128 CTR - Encrypt and decrypt 0 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CTR
enc_dec_buf:MBEDTLS_CIPHER_AES_128_CTR:"AES-128-CTR":128:0:-1

AES-128 CTR - Encrypt and decrypt 1 byte
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CTR
enc_dec_buf:MBEDTLS_CIPHER_AES_128_CTR:"AES-128-CTR":128:1:-1

AES-128 CTR - Encrypt and decrypt 2 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CTR
enc_dec_buf:MBEDTLS_CIPHER_AES_128_CTR:"AES-128-CTR":128:2:-1

AES-128 CTR - Encrypt and decrypt 7 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CTR
enc_dec_buf:MBEDTLS_CIPHER_AES_128_CTR:"AES-128-CTR":128:7:-1

AES-128 CTR - Encrypt and decrypt 8 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CTR
enc_dec_buf:MBEDTLS_CIPHER_AES_128_CTR:"AES-128-CTR":128:8:-1

AES-128 CTR - Encrypt and decrypt 9 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CTR
enc_dec_buf:MBEDTLS_CIPHER_AES_128_CTR:"AES-128-CTR":128:9:-1

AES-128 CTR - Encrypt and decrypt 15 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CTR
enc_dec_buf:MBEDTLS_CIPHER_AES_128_CTR:"AES-128-CTR":128:15:-1

AES-128 CTR - Encrypt and decrypt 16 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CTR
enc_dec_buf:MBEDTLS_CIPHER_AES_128_CTR:"AES-128-CTR":128:16:-1

AES-128 CTR - Encrypt and decrypt 17 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CTR
enc_dec_buf:MBEDTLS_CIPHER_AES_128_CTR:"AES-128-CTR":128:17:-1

AES-128 CTR - Encrypt and decrypt 31 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CTR
enc_dec_buf:MBEDTLS_CIPHER_AES_128_CTR:"AES-128-CTR":128:31:-1

AES-128 CTR - Encrypt and decrypt 32 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CTR
enc_dec_buf:MBEDTLS_CIPHER_AES_128_CTR:"AES-128-CTR":128:32:-1

AES-128 CTR - Encrypt and decrypt 33 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CTR
enc_dec_buf:MBEDTLS_CIPHER_AES_128_CTR:"AES-128-CTR":128:33:-1

AES-128 CTR - Encrypt and decrypt 47 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CTR
enc_dec_buf:MBEDTLS_CIPHER_AES_128_CTR:"AES-128-CTR":128:47:-1

AES-128 CTR - Encrypt and decrypt 48 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CTR
enc_dec_buf:MBEDTLS_CIPHER_AES_128_CTR:"AES-128-CTR":128:48:-1

AES-128 CTR - Encrypt and decrypt 49 bytes
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CTR
enc_dec_buf:MBEDTLS_CIPHER_AES_128_CTR:"AES-128-CTR":128:49:-1

AES-128 CTR - Encrypt and decrypt 0 bytes in multiple parts
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CTR
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_128_CTR:128:0:0:-1:0:0:0:0

AES-128 CTR - Encrypt and decrypt 1 bytes in multiple parts 1
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CTR
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_128_CTR:128:1:0:-1:1:0:1:0

AES-128 CTR - Encrypt and decrypt 1 bytes in multiple parts 2
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CTR
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_128_CTR:128:0:1:-1:0:1:0:1

AES-128 CTR - Encrypt and decrypt 16 bytes in multiple parts 1
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CTR
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_128_CTR:128:16:0:-1:16:0:16:0

AES-128 CTR - Encrypt and decrypt 16 bytes in multiple parts 2
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CTR
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_128_CTR:128:0:16:-1:0:16:0:16

AES-128 CTR - Encrypt and decrypt 16 bytes in multiple parts 3
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CTR
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_128_CTR:128:1:15:-1:1:15:1:15

AES-128 CTR - Encrypt and decrypt 16 bytes in multiple parts 4
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CTR
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_128_CTR:128:15:1:-1:15:1:15:1

AES-128 CTR - Encrypt and decrypt 22 bytes in multiple parts 1
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CTR
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_128_CTR:128:15:7:-1:15:7:15:7

AES-128 CTR - Encrypt and decrypt 22 bytes in multiple parts 2
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CTR
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_128_CTR:128:16:6:-1:16:6:16:6

AES-128 CTR - Encrypt and decrypt 23 bytes in multiple parts 1
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CTR
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_128_CTR:128:17:6:-1:17:6:17:6

AES-128 CTR - Encrypt and decrypt 32 bytes in multiple parts 1
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CTR
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_128_CTR:128:16:16:-1:16:16:16:16

AES-192 CBC - Encrypt and decrypt 0 bytes with PKCS7 padding
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf:MBEDTLS_CIPHER_AES_192_CBC:"AES-192-CBC":192:0:MBEDTLS_PADDING_PKCS7

AES-192 CBC - Encrypt and decrypt 1 byte with PKCS7 padding
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf:MBEDTLS_CIPHER_AES_192_CBC:"AES-192-CBC":192:1:MBEDTLS_PADDING_PKCS7

AES-192 CBC - Encrypt and decrypt 2 bytes with PKCS7 padding
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf:MBEDTLS_CIPHER_AES_192_CBC:"AES-192-CBC":192:2:MBEDTLS_PADDING_PKCS7

AES-192 CBC - Encrypt and decrypt 7 bytes with PKCS7 padding
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf:MBEDTLS_CIPHER_AES_192_CBC:"AES-192-CBC":192:7:MBEDTLS_PADDING_PKCS7

AES-192 CBC - Encrypt and decrypt 8 bytes with PKCS7 padding
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf:MBEDTLS_CIPHER_AES_192_CBC:"AES-192-CBC":192:8:MBEDTLS_PADDING_PKCS7

AES-192 CBC - Encrypt and decrypt 9 bytes with PKCS7 padding
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf:MBEDTLS_CIPHER_AES_192_CBC:"AES-192-CBC":192:9:MBEDTLS_PADDING_PKCS7

AES-192 CBC - Encrypt and decrypt 15 bytes with PKCS7 padding
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf:MBEDTLS_CIPHER_AES_192_CBC:"AES-192-CBC":192:15:MBEDTLS_PADDING_PKCS7

AES-192 CBC - Encrypt and decrypt 16 bytes with PKCS7 padding
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf:MBEDTLS_CIPHER_AES_192_CBC:"AES-192-CBC":192:16:MBEDTLS_PADDING_PKCS7

AES-192 CBC - Encrypt and decrypt 17 bytes with PKCS7 padding
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf:MBEDTLS_CIPHER_AES_192_CBC:"AES-192-CBC":192:17:MBEDTLS_PADDING_PKCS7

AES-192 CBC - Encrypt and decrypt 31 bytes with PKCS7 padding
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf:MBEDTLS_CIPHER_AES_192_CBC:"AES-192-CBC":192:31:MBEDTLS_PADDING_PKCS7

AES-192 CBC - Encrypt and decrypt 32 bytes with PKCS7 padding
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf:MBEDTLS_CIPHER_AES_192_CBC:"AES-192-CBC":192:32:MBEDTLS_PADDING_PKCS7

AES-192 CBC - Encrypt and decrypt 33 bytes with PKCS7 padding
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf:MBEDTLS_CIPHER_AES_192_CBC:"AES-192-CBC":192:33:MBEDTLS_PADDING_PKCS7

AES-192 CBC - Encrypt and decrypt 47 bytes with PKCS7 padding
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf:MBEDTLS_CIPHER_AES_192_CBC:"AES-192-CBC":192:47:MBEDTLS_PADDING_PKCS7

AES-192 CBC - Encrypt and decrypt 48 bytes with PKCS7 padding
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf:MBEDTLS_CIPHER_AES_192_CBC:"AES-192-CBC":192:48:MBEDTLS_PADDING_PKCS7

AES-192 CBC - Encrypt and decrypt 49 bytes with PKCS7 padding
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf:MBEDTLS_CIPHER_AES_192_CBC:"AES-192-CBC":192:49:MBEDTLS_PADDING_PKCS7

AES-192 CBC - Encrypt and decrypt 0 bytes in multiple parts with PKCS7 padding
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_192_CBC:192:0:0:MBEDTLS_PADDING_PKCS7:0:0:0:0

AES-192 CBC - Encrypt and decrypt 1 bytes in multiple parts with PKCS7 padding 1
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_192_CBC:192:1:0:MBEDTLS_PADDING_PKCS7:0:0:0:0

AES-192 CBC - Encrypt and decrypt 1 bytes in multiple parts with PKCS7 padding 2
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_192_CBC:192:0:1:MBEDTLS_PADDING_PKCS7:0:0:0:0

AES-192 CBC - Encrypt and decrypt 16 bytes in multiple parts with PKCS7 padding 1
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_192_CBC:192:16:0:MBEDTLS_PADDING_PKCS7:16:0:0:16

AES-192 CBC - Encrypt and decrypt 16 bytes in multiple parts with PKCS7 padding 2
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_192_CBC:192:0:16:MBEDTLS_PADDING_PKCS7:0:16:0:16

AES-192 CBC - Encrypt and decrypt 16 bytes in multiple parts with PKCS7 padding 3
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_192_CBC:192:1:15:MBEDTLS_PADDING_PKCS7:0:16:0:16

AES-192 CBC - Encrypt and decrypt 16 bytes in multiple parts with PKCS7 padding 4
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_192_CBC:192:15:1:MBEDTLS_PADDING_PKCS7:0:16:0:16

AES-192 CBC - Encrypt and decrypt 22 bytes in multiple parts with PKCS7 padding 1
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_192_CBC:192:15:7:MBEDTLS_PADDING_PKCS7:0:16:0:16

AES-192 CBC - Encrypt and decrypt 22 bytes in multiple parts with PKCS7 padding 2
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_192_CBC:192:16:6:MBEDTLS_PADDING_PKCS7:16:0:0:16

AES-192 CBC - Encrypt and decrypt 23 bytes in multiple parts with PKCS7 padding 1
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_192_CBC:192:17:6:MBEDTLS_PADDING_PKCS7:16:0:16:0

AES-192 CBC - Encrypt and decrypt 32 bytes in multiple parts with PKCS7 padding 1
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_192_CBC:192:16:16:MBEDTLS_PADDING_PKCS7:16:16:0:32

AES-256 CBC - Decrypt empty buffer
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
dec_empty_buf:MBEDTLS_CIPHER_AES_256_CBC:0:0

AES-256 CBC - Encrypt and decrypt 0 bytes with PKCS7 padding
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf:MBEDTLS_CIPHER_AES_256_CBC:"AES-256-CBC":256:0:MBEDTLS_PADDING_PKCS7

AES-256 CBC - Encrypt and decrypt 1 byte with PKCS7 padding
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf:MBEDTLS_CIPHER_AES_256_CBC:"AES-256-CBC":256:1:MBEDTLS_PADDING_PKCS7

AES-256 CBC - Encrypt and decrypt 2 bytes with PKCS7 padding
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf:MBEDTLS_CIPHER_AES_256_CBC:"AES-256-CBC":256:2:MBEDTLS_PADDING_PKCS7

AES-256 CBC - Encrypt and decrypt 7 bytes with PKCS7 padding
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf:MBEDTLS_CIPHER_AES_256_CBC:"AES-256-CBC":256:7:MBEDTLS_PADDING_PKCS7

AES-256 CBC - Encrypt and decrypt 8 bytes with PKCS7 padding
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf:MBEDTLS_CIPHER_AES_256_CBC:"AES-256-CBC":256:8:MBEDTLS_PADDING_PKCS7

AES-256 CBC - Encrypt and decrypt 9 bytes with PKCS7 padding
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf:MBEDTLS_CIPHER_AES_256_CBC:"AES-256-CBC":256:9:MBEDTLS_PADDING_PKCS7

AES-256 CBC - Encrypt and decrypt 15 bytes with PKCS7 padding
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf:MBEDTLS_CIPHER_AES_256_CBC:"AES-256-CBC":256:15:MBEDTLS_PADDING_PKCS7

AES-256 CBC - Encrypt and decrypt 16 bytes with PKCS7 padding
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf:MBEDTLS_CIPHER_AES_256_CBC:"AES-256-CBC":256:16:MBEDTLS_PADDING_PKCS7

AES-256 CBC - Encrypt and decrypt 17 bytes with PKCS7 padding
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf:MBEDTLS_CIPHER_AES_256_CBC:"AES-256-CBC":256:17:MBEDTLS_PADDING_PKCS7

AES-256 CBC - Encrypt and decrypt 31 bytes with PKCS7 padding
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf:MBEDTLS_CIPHER_AES_256_CBC:"AES-256-CBC":256:31:MBEDTLS_PADDING_PKCS7

AES-256 CBC - Encrypt and decrypt 32 bytes with PKCS7 padding
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf:MBEDTLS_CIPHER_AES_256_CBC:"AES-256-CBC":256:32:MBEDTLS_PADDING_PKCS7

AES-256 CBC - Encrypt and decrypt 33 bytes with PKCS7 padding
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf:MBEDTLS_CIPHER_AES_256_CBC:"AES-256-CBC":256:33:MBEDTLS_PADDING_PKCS7

AES-256 CBC - Encrypt and decrypt 47 bytes with PKCS7 padding
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf:MBEDTLS_CIPHER_AES_256_CBC:"AES-256-CBC":256:47:MBEDTLS_PADDING_PKCS7

AES-256 CBC - Encrypt and decrypt 48 bytes with PKCS7 padding
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf:MBEDTLS_CIPHER_AES_256_CBC:"AES-256-CBC":256:48:MBEDTLS_PADDING_PKCS7

AES-256 CBC - Encrypt and decrypt 49 bytes with PKCS7 padding
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf:MBEDTLS_CIPHER_AES_256_CBC:"AES-256-CBC":256:49:MBEDTLS_PADDING_PKCS7

AES-256 CBC - Encrypt and decrypt 0 bytes in multiple parts with PKCS7 padding
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_256_CBC:256:0:0:MBEDTLS_PADDING_PKCS7:0:0:0:0

AES-256 CBC - Encrypt and decrypt 1 bytes in multiple parts with PKCS7 padding 1
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_256_CBC:256:1:0:MBEDTLS_PADDING_PKCS7:0:0:0:0

AES-256 CBC - Encrypt and decrypt 1 bytes in multiple parts with PKCS7 padding 2
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_256_CBC:256:0:1:MBEDTLS_PADDING_PKCS7:0:0:0:0

AES-256 CBC - Encrypt and decrypt 16 bytes in multiple parts with PKCS7 padding 1
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_256_CBC:256:16:0:MBEDTLS_PADDING_PKCS7:16:0:0:16

AES-256 CBC - Encrypt and decrypt 16 bytes in multiple parts with PKCS7 padding 2
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_256_CBC:256:0:16:MBEDTLS_PADDING_PKCS7:0:16:0:16

AES-256 CBC - Encrypt and decrypt 16 bytes in multiple parts with PKCS7 padding 3
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_256_CBC:256:1:15:MBEDTLS_PADDING_PKCS7:0:16:0:16

AES-256 CBC - Encrypt and decrypt 16 bytes in multiple parts with PKCS7 padding 4
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_256_CBC:256:15:1:MBEDTLS_PADDING_PKCS7:0:16:0:16

AES-256 CBC - Encrypt and decrypt 22 bytes in multiple parts with PKCS7 padding 1
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_256_CBC:256:15:7:MBEDTLS_PADDING_PKCS7:0:16:0:16

AES-256 CBC - Encrypt and decrypt 22 bytes in multiple parts with PKCS7 padding 2
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_256_CBC:256:16:6:MBEDTLS_PADDING_PKCS7:16:0:0:16

AES-256 CBC - Encrypt and decrypt 23 bytes in multiple parts with PKCS7 padding 1
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_256_CBC:256:17:6:MBEDTLS_PADDING_PKCS7:16:0:16:0

AES-256 CBC - Encrypt and decrypt 32 bytes in multiple parts with PKCS7 padding 1
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
enc_dec_buf_multipart:MBEDTLS_CIPHER_AES_256_CBC:256:16:16:MBEDTLS_PADDING_PKCS7:16:16:0:32

AES Decrypt test vector #0
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_PADDING_PKCS7:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
decrypt_test_vec:MBEDTLS_CIPHER_AES_128_CBC:MBEDTLS_PADDING_PKCS7:"ffffffffe00000000000000000000000":"00000000000000000000000000000000":"23f710842b9bb9c32f26648c786807ca":"00000000000000000000000000000000":"":"":MBEDTLS_ERR_CIPHER_INVALID_PADDING:0

AES Decrypt test vector #1
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
decrypt_test_vec:MBEDTLS_CIPHER_AES_128_CBC:MBEDTLS_PADDING_NONE:"ffffffffe00000000000000000000000":"00000000000000000000000000000000":"23f710842b9bb9c32f26648c786807ca":"00000000000000000000000000000000":"":"":0:0

AES Decrypt test vector #2
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
decrypt_test_vec:MBEDTLS_CIPHER_AES_192_CBC:MBEDTLS_PADDING_NONE:"000000000000000000000000000000000000000000000000":"00000000000000000000000000000000":"707b1dbb0ffa40ef7d95def421233fae":"fffffffff80000000000000000000000":"":"":0:0

AES Decrypt test vector #3
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
decrypt_test_vec:MBEDTLS_CIPHER_AES_256_CBC:MBEDTLS_PADDING_NONE:"0000000000000000000000000000000000000000000000000000000000000000":"00000000000000000000000000000000":"49af6b372135acef10132e548f217b17":"ff000000000000000000000000000000":"":"":0:0

AES Decrypt test vector #4
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CFB
decrypt_test_vec:MBEDTLS_CIPHER_AES_128_CFB128:-1:"fffffffe000000000000000000000000":"00000000000000000000000000000000":"1114bc2028009b923f0b01915ce5e7c4":"00000000000000000000000000000000":"":"":0:0:

AES Decrypt test vector #5
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CFB:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
decrypt_test_vec:MBEDTLS_CIPHER_AES_192_CFB128:-1:"ffffffffffffffffffffffffffffffffffffffffffe00000":"00000000000000000000000000000000":"60136703374f64e860b48ce31f930716":"00000000000000000000000000000000":"":"":0:0

AES Decrypt test vector #6
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CFB:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
decrypt_test_vec:MBEDTLS_CIPHER_AES_256_CFB128:-1:"ffffffffff800000000000000000000000000000000000000000000000000000":"00000000000000000000000000000000":"be66cfea2fecd6bf0ec7b4352c99bcaa":"00000000000000000000000000000000":"":"":0:0

AES Decrypt test vector #7
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_OFB
decrypt_test_vec:MBEDTLS_CIPHER_AES_128_OFB:-1:"2B7E151628AED2A6ABF7158809CF4F3C":"000102030405060708090A0B0C0D0E0F":"3B3FD92EB72DAD20333449F8E83CFB4A7789508d16918f03f53c52dac54ed8259740051e9c5fecf64344f7a82260edcc304c6528f659c77866a510d9c1d6ae5e":"6BC1BEE22E409F96E93D7E117393172AAE2D8A571E03AC9C9EB76FAC45AF8E5130C81C46A35CE411E5FBC1191A0A52EFF69F2445DF4F9B17AD2B417BE66C3710":"":"":0:0:

AES Decrypt test vector #8
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_OFB:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
decrypt_test_vec:MBEDTLS_CIPHER_AES_192_OFB:-1:"8E73B0F7DA0E6452C810F32B809079E562F8EAD2522C6B7B":"000102030405060708090A0B0C0D0E0F":"CDC80D6FDDF18CAB34C25909C99A4174fcc28b8d4c63837c09e81700c11004018d9a9aeac0f6596f559c6d4daf59a5f26d9f200857ca6c3e9cac524bd9acc92a":"6BC1BEE22E409F96E93D7E117393172AAE2D8A571E03AC9C9EB76FAC45AF8E5130C81C46A35CE411E5FBC1191A0A52EFF69F2445DF4F9B17AD2B417BE66C3710":"":"":0:0:

AES Decrypt test vector #9
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_OFB:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
decrypt_test_vec:MBEDTLS_CIPHER_AES_256_OFB:-1:"603DEB1015CA71BE2B73AEF0857D77811F352C073B6108D72D9810A30914DFF4":"000102030405060708090A0B0C0D0E0F":"DC7E84BFDA79164B7ECD8486985D38604febdc6740d20b3ac88f6ad82a4fb08d71ab47a086e86eedf39d1c5bba97c4080126141d67f37be8538f5a8be740e484":"6BC1BEE22E409F96E93D7E117393172AAE2D8A571E03AC9C9EB76FAC45AF8E5130C81C46A35CE411E5FBC1191A0A52EFF69F2445DF4F9B17AD2B417BE66C3710":"":"":0:0:

AES-128-ECB Encrypt NIST KAT #1
depends_on:MBEDTLS_AES_C
test_vec_ecb:MBEDTLS_CIPHER_AES_128_ECB:MBEDTLS_ENCRYPT:"00000000000000000000000000000000":"f34481ec3cc627bacd5dc3fb08f273e6":"0336763e966d92595a567cc9ce537f5e":0

AES-128-ECB Encrypt NIST KAT #2
depends_on:MBEDTLS_AES_C
test_vec_ecb:MBEDTLS_CIPHER_AES_128_ECB:MBEDTLS_ENCRYPT:"00000000000000000000000000000000":"9798c4640bad75c7c3227db910174e72":"a9a1631bf4996954ebc093957b234589":0

AES-128-ECB Encrypt NIST KAT #3
depends_on:MBEDTLS_AES_C
test_vec_ecb:MBEDTLS_CIPHER_AES_128_ECB:MBEDTLS_ENCRYPT:"00000000000000000000000000000000":"96ab5c2ff612d9dfaae8c31f30c42168":"ff4f8391a6a40ca5b25d23bedd44a597":0

AES-128-ECB Encrypt NIST KAT #4
depends_on:MBEDTLS_AES_C
test_vec_ecb:MBEDTLS_CIPHER_AES_128_ECB:MBEDTLS_ENCRYPT:"e0000000000000000000000000000000":"00000000000000000000000000000000":"72a1da770f5d7ac4c9ef94d822affd97":0

AES-128-ECB Encrypt NIST KAT #5
depends_on:MBEDTLS_AES_C
test_vec_ecb:MBEDTLS_CIPHER_AES_128_ECB:MBEDTLS_ENCRYPT:"f0000000000000000000000000000000":"00000000000000000000000000000000":"970014d634e2b7650777e8e84d03ccd8":0

AES-128-ECB Encrypt NIST KAT #6
depends_on:MBEDTLS_AES_C
test_vec_ecb:MBEDTLS_CIPHER_AES_128_ECB:MBEDTLS_ENCRYPT:"f8000000000000000000000000000000":"00000000000000000000000000000000":"f17e79aed0db7e279e955b5f493875a7":0

AES-128-ECB Encrypt NIST KAT #7
depends_on:MBEDTLS_AES_C
test_vec_ecb:MBEDTLS_CIPHER_AES_128_ECB:MBEDTLS_ENCRYPT:"fffffffffffff0000000000000000000":"00000000000000000000000000000000":"7b90785125505fad59b13c186dd66ce3":0

AES-128-ECB Encrypt NIST KAT #8
depends_on:MBEDTLS_AES_C
test_vec_ecb:MBEDTLS_CIPHER_AES_128_ECB:MBEDTLS_ENCRYPT:"fffffffffffff8000000000000000000":"00000000000000000000000000000000":"8b527a6aebdaec9eaef8eda2cb7783e5":0

AES-128-ECB Encrypt NIST KAT #9
depends_on:MBEDTLS_AES_C
test_vec_ecb:MBEDTLS_CIPHER_AES_128_ECB:MBEDTLS_ENCRYPT:"fffffffffffffc000000000000000000":"00000000000000000000000000000000":"43fdaf53ebbc9880c228617d6a9b548b":0

AES-128-ECB Encrypt NIST KAT #10
depends_on:MBEDTLS_AES_C
test_vec_ecb:MBEDTLS_CIPHER_AES_128_ECB:MBEDTLS_ENCRYPT:"ffffffffffffffffffffffffffffc000":"00000000000000000000000000000000":"70c46bb30692be657f7eaa93ebad9897":0

AES-128-ECB Encrypt NIST KAT #11
depends_on:MBEDTLS_AES_C
test_vec_ecb:MBEDTLS_CIPHER_AES_128_ECB:MBEDTLS_ENCRYPT:"ffffffffffffffffffffffffffffe000":"00000000000000000000000000000000":"********************************":0

AES-128-ECB Encrypt NIST KAT #12
depends_on:MBEDTLS_AES_C
test_vec_ecb:MBEDTLS_CIPHER_AES_128_ECB:MBEDTLS_ENCRYPT:"fffffffffffffffffffffffffffff000":"00000000000000000000000000000000":"1dbf57877b7b17385c85d0b54851e371":0

AES-128-ECB Encrypt NIST KAT #13
depends_on:MBEDTLS_AES_C
test_vec_ecb:MBEDTLS_CIPHER_AES_128_ECB:MBEDTLS_ENCRYPT:"00000000000000000000000000000000":"ffffffffffffffc00000000000000000":"3a4d354f02bb5a5e47d39666867f246a":0

AES-128-ECB Encrypt NIST KAT #14
depends_on:MBEDTLS_AES_C
test_vec_ecb:MBEDTLS_CIPHER_AES_128_ECB:MBEDTLS_ENCRYPT:"00000000000000000000000000000000":"ffffffffffffffe00000000000000000":"d451b8d6e1e1a0ebb155fbbf6e7b7dc3":0

AES-128-ECB Encrypt NIST KAT #15
depends_on:MBEDTLS_AES_C
test_vec_ecb:MBEDTLS_CIPHER_AES_128_ECB:MBEDTLS_ENCRYPT:"00000000000000000000000000000000":"fffffffffffffff00000000000000000":"6898d4f42fa7ba6a10ac05e87b9f2080":0

AES-128-ECB Encrypt NIST KAT #16
depends_on:MBEDTLS_AES_C
test_vec_ecb:MBEDTLS_CIPHER_AES_128_ECB:MBEDTLS_ENCRYPT:"00000000000000000000000000000000":"ffffffffffffffffffffffffe0000000":"082eb8be35f442fb52668e16a591d1d6":0

AES-128-ECB Encrypt NIST KAT #17
depends_on:MBEDTLS_AES_C
test_vec_ecb:MBEDTLS_CIPHER_AES_128_ECB:MBEDTLS_ENCRYPT:"00000000000000000000000000000000":"fffffffffffffffffffffffff0000000":"e656f9ecf5fe27ec3e4a73d00c282fb3":0

AES-128-ECB Encrypt NIST KAT #18
depends_on:MBEDTLS_AES_C
test_vec_ecb:MBEDTLS_CIPHER_AES_128_ECB:MBEDTLS_ENCRYPT:"00000000000000000000000000000000":"fffffffffffffffffffffffff8000000":"2ca8209d63274cd9a29bb74bcd77683a":0

AES-128-ECB Decrypt NIST KAT #1
depends_on:MBEDTLS_AES_C:!MBEDTLS_BLOCK_CIPHER_NO_DECRYPT
test_vec_ecb:MBEDTLS_CIPHER_AES_128_ECB:MBEDTLS_DECRYPT:"00000000000000000000000000000000":"db4f1aa530967d6732ce4715eb0ee24b":"ff000000000000000000000000000000":0

AES-128-ECB Decrypt NIST KAT #2
depends_on:MBEDTLS_AES_C:!MBEDTLS_BLOCK_CIPHER_NO_DECRYPT
test_vec_ecb:MBEDTLS_CIPHER_AES_128_ECB:MBEDTLS_DECRYPT:"00000000000000000000000000000000":"a81738252621dd180a34f3455b4baa2f":"ff800000000000000000000000000000":0

AES-128-ECB Decrypt NIST KAT #3
depends_on:MBEDTLS_AES_C:!MBEDTLS_BLOCK_CIPHER_NO_DECRYPT
test_vec_ecb:MBEDTLS_CIPHER_AES_128_ECB:MBEDTLS_DECRYPT:"00000000000000000000000000000000":"77e2b508db7fd89234caf7939ee5621a":"ffc00000000000000000000000000000":0

AES-128-ECB Decrypt NIST KAT #4
depends_on:MBEDTLS_AES_C:!MBEDTLS_BLOCK_CIPHER_NO_DECRYPT
test_vec_ecb:MBEDTLS_CIPHER_AES_128_ECB:MBEDTLS_DECRYPT:"00000000000000000000000000000000":"dc43be40be0e53712f7e2bf5ca707209":"6a118a874519e64e9963798a503f1d35":0

AES-128-ECB Decrypt NIST KAT #5
depends_on:MBEDTLS_AES_C:!MBEDTLS_BLOCK_CIPHER_NO_DECRYPT
test_vec_ecb:MBEDTLS_CIPHER_AES_128_ECB:MBEDTLS_DECRYPT:"00000000000000000000000000000000":"92beedab1895a94faa69b632e5cc47ce":"cb9fceec81286ca3e989bd979b0cb284":0

AES-128-ECB Decrypt NIST KAT #6
depends_on:MBEDTLS_AES_C:!MBEDTLS_BLOCK_CIPHER_NO_DECRYPT
test_vec_ecb:MBEDTLS_CIPHER_AES_128_ECB:MBEDTLS_DECRYPT:"00000000000000000000000000000000":"459264f4798f6a78bacb89c15ed3d601":"b26aeb1874e47ca8358ff22378f09144":0

AES-128-ECB Decrypt NIST KAT #7
depends_on:MBEDTLS_AES_C:!MBEDTLS_BLOCK_CIPHER_NO_DECRYPT
test_vec_ecb:MBEDTLS_CIPHER_AES_128_ECB:MBEDTLS_DECRYPT:"b69418a85332240dc82492353956ae0c":"a303d940ded8f0baff6f75414cac5243":"00000000000000000000000000000000":0

AES-128-ECB Decrypt NIST KAT #8
depends_on:MBEDTLS_AES_C:!MBEDTLS_BLOCK_CIPHER_NO_DECRYPT
test_vec_ecb:MBEDTLS_CIPHER_AES_128_ECB:MBEDTLS_DECRYPT:"71b5c08a1993e1362e4d0ce9b22b78d5":"c2dabd117f8a3ecabfbb11d12194d9d0":"00000000000000000000000000000000":0

AES-128-ECB Decrypt NIST KAT #9
depends_on:MBEDTLS_AES_C:!MBEDTLS_BLOCK_CIPHER_NO_DECRYPT
test_vec_ecb:MBEDTLS_CIPHER_AES_128_ECB:MBEDTLS_DECRYPT:"e234cdca2606b81f29408d5f6da21206":"fff60a4740086b3b9c56195b98d91a7b":"00000000000000000000000000000000":0

AES-128-ECB Decrypt NIST KAT #10
depends_on:MBEDTLS_AES_C:!MBEDTLS_BLOCK_CIPHER_NO_DECRYPT
test_vec_ecb:MBEDTLS_CIPHER_AES_128_ECB:MBEDTLS_DECRYPT:"ffffffffffffffff0000000000000000":"84be19e053635f09f2665e7bae85b42d":"00000000000000000000000000000000":0

AES-128-ECB Decrypt NIST KAT #11
depends_on:MBEDTLS_AES_C:!MBEDTLS_BLOCK_CIPHER_NO_DECRYPT
test_vec_ecb:MBEDTLS_CIPHER_AES_128_ECB:MBEDTLS_DECRYPT:"ffffffffffffffff8000000000000000":"32cd652842926aea4aa6137bb2be2b5e":"00000000000000000000000000000000":0

AES-192-ECB Encrypt NIST KAT #1
depends_on:MBEDTLS_AES_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
test_vec_ecb:MBEDTLS_CIPHER_AES_192_ECB:MBEDTLS_ENCRYPT:"000000000000000000000000000000000000000000000000":"fffffffffffffffffffff80000000000":"156f07767a85a4312321f63968338a01":0

AES-192-ECB Encrypt NIST KAT #2
depends_on:MBEDTLS_AES_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
test_vec_ecb:MBEDTLS_CIPHER_AES_192_ECB:MBEDTLS_ENCRYPT:"000000000000000000000000000000000000000000000000":"fffffffffffffffffffffc0000000000":"15eec9ebf42b9ca76897d2cd6c5a12e2":0

AES-192-ECB Encrypt NIST KAT #3
depends_on:MBEDTLS_AES_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
test_vec_ecb:MBEDTLS_CIPHER_AES_192_ECB:MBEDTLS_ENCRYPT:"000000000000000000000000000000000000000000000000":"fffffffffffffffffffffe0000000000":"db0d3a6fdcc13f915e2b302ceeb70fd8":0

AES-192-ECB Encrypt NIST KAT #4
depends_on:MBEDTLS_AES_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
test_vec_ecb:MBEDTLS_CIPHER_AES_192_ECB:MBEDTLS_ENCRYPT:"000000000000000000000000000000000000000000000000":"51719783d3185a535bd75adc65071ce1":"4f354592ff7c8847d2d0870ca9481b7c":0

AES-192-ECB Encrypt NIST KAT #5
depends_on:MBEDTLS_AES_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
test_vec_ecb:MBEDTLS_CIPHER_AES_192_ECB:MBEDTLS_ENCRYPT:"000000000000000000000000000000000000000000000000":"26aa49dcfe7629a8901a69a9914e6dfd":"d5e08bf9a182e857cf40b3a36ee248cc":0

AES-192-ECB Encrypt NIST KAT #6
depends_on:MBEDTLS_AES_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
test_vec_ecb:MBEDTLS_CIPHER_AES_192_ECB:MBEDTLS_ENCRYPT:"000000000000000000000000000000000000000000000000":"941a4773058224e1ef66d10e0a6ee782":"067cd9d3749207791841562507fa9626":0

AES-192-ECB Encrypt NIST KAT #7
depends_on:MBEDTLS_AES_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
test_vec_ecb:MBEDTLS_CIPHER_AES_192_ECB:MBEDTLS_ENCRYPT:"d2926527e0aa9f37b45e2ec2ade5853ef807576104c7ace3":"00000000000000000000000000000000":"dd619e1cf204446112e0af2b9afa8f8c":0

AES-192-ECB Encrypt NIST KAT #8
depends_on:MBEDTLS_AES_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
test_vec_ecb:MBEDTLS_CIPHER_AES_192_ECB:MBEDTLS_ENCRYPT:"982215f4e173dfa0fcffe5d3da41c4812c7bcc8ed3540f93":"00000000000000000000000000000000":"d4f0aae13c8fe9339fbf9e69ed0ad74d":0

AES-192-ECB Encrypt NIST KAT #9
depends_on:MBEDTLS_AES_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
test_vec_ecb:MBEDTLS_CIPHER_AES_192_ECB:MBEDTLS_ENCRYPT:"98c6b8e01e379fbd14e61af6af891596583565f2a27d59e9":"00000000000000000000000000000000":"19c80ec4a6deb7e5ed1033dda933498f":0

AES-192-ECB Encrypt NIST KAT #10
depends_on:MBEDTLS_AES_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
test_vec_ecb:MBEDTLS_CIPHER_AES_192_ECB:MBEDTLS_ENCRYPT:"fffffffffffffffffffffffffff800000000000000000000":"00000000000000000000000000000000":"8dd274bd0f1b58ae345d9e7233f9b8f3":0

AES-192-ECB Encrypt NIST KAT #11
depends_on:MBEDTLS_AES_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
test_vec_ecb:MBEDTLS_CIPHER_AES_192_ECB:MBEDTLS_ENCRYPT:"fffffffffffffffffffffffffffc00000000000000000000":"00000000000000000000000000000000":"9d6bdc8f4ce5feb0f3bed2e4b9a9bb0b":0

AES-192-ECB Encrypt NIST KAT #12
depends_on:MBEDTLS_AES_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
test_vec_ecb:MBEDTLS_CIPHER_AES_192_ECB:MBEDTLS_ENCRYPT:"fffffffffffffffffffffffffffe00000000000000000000":"00000000000000000000000000000000":"fd5548bcf3f42565f7efa94562528d46":0

AES-192-ECB Decrypt NIST KAT #1
depends_on:MBEDTLS_AES_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH:!MBEDTLS_BLOCK_CIPHER_NO_DECRYPT
test_vec_ecb:MBEDTLS_CIPHER_AES_192_ECB:MBEDTLS_DECRYPT:"fffffffffffffffffffffffffffffffff000000000000000":"bb2852c891c5947d2ed44032c421b85f":"00000000000000000000000000000000":0

AES-192-ECB Decrypt NIST KAT #2
depends_on:MBEDTLS_AES_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH:!MBEDTLS_BLOCK_CIPHER_NO_DECRYPT
test_vec_ecb:MBEDTLS_CIPHER_AES_192_ECB:MBEDTLS_DECRYPT:"fffffffffffffffffffffffffffffffff800000000000000":"1b9f5fbd5e8a4264c0a85b80409afa5e":"00000000000000000000000000000000":0

AES-192-ECB Decrypt NIST KAT #3
depends_on:MBEDTLS_AES_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH:!MBEDTLS_BLOCK_CIPHER_NO_DECRYPT
test_vec_ecb:MBEDTLS_CIPHER_AES_192_ECB:MBEDTLS_DECRYPT:"fffffffffffffffffffffffffffffffffc00000000000000":"30dab809f85a917fe924733f424ac589":"00000000000000000000000000000000":0

AES-192-ECB Decrypt NIST KAT #4
depends_on:MBEDTLS_AES_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH:!MBEDTLS_BLOCK_CIPHER_NO_DECRYPT
test_vec_ecb:MBEDTLS_CIPHER_AES_192_ECB:MBEDTLS_DECRYPT:"61257134a518a0d57d9d244d45f6498cbc32f2bafc522d79":"cfe4d74002696ccf7d87b14a2f9cafc9":"00000000000000000000000000000000":0

AES-192-ECB Decrypt NIST KAT #5
depends_on:MBEDTLS_AES_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH:!MBEDTLS_BLOCK_CIPHER_NO_DECRYPT
test_vec_ecb:MBEDTLS_CIPHER_AES_192_ECB:MBEDTLS_DECRYPT:"b0ab0a6a818baef2d11fa33eac947284fb7d748cfb75e570":"d2eafd86f63b109b91f5dbb3a3fb7e13":"00000000000000000000000000000000":0

AES-192-ECB Decrypt NIST KAT #6
depends_on:MBEDTLS_AES_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH:!MBEDTLS_BLOCK_CIPHER_NO_DECRYPT
test_vec_ecb:MBEDTLS_CIPHER_AES_192_ECB:MBEDTLS_DECRYPT:"ee053aa011c8b428cdcc3636313c54d6a03cac01c71579d6":"9b9fdd1c5975655f539998b306a324af":"00000000000000000000000000000000":0

AES-192-ECB Decrypt NIST KAT #7
depends_on:MBEDTLS_AES_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH:!MBEDTLS_BLOCK_CIPHER_NO_DECRYPT
test_vec_ecb:MBEDTLS_CIPHER_AES_192_ECB:MBEDTLS_DECRYPT:"000000000000000000000000000000000000000000000000":"275cfc0413d8ccb70513c3859b1d0f72":"1b077a6af4b7f98229de786d7516b639":0

AES-192-ECB Decrypt NIST KAT #8
depends_on:MBEDTLS_AES_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH:!MBEDTLS_BLOCK_CIPHER_NO_DECRYPT
test_vec_ecb:MBEDTLS_CIPHER_AES_192_ECB:MBEDTLS_DECRYPT:"000000000000000000000000000000000000000000000000":"c9b8135ff1b5adc413dfd053b21bd96d":"9c2d8842e5f48f57648205d39a239af1":0

AES-192-ECB Decrypt NIST KAT #9
depends_on:MBEDTLS_AES_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH:!MBEDTLS_BLOCK_CIPHER_NO_DECRYPT
test_vec_ecb:MBEDTLS_CIPHER_AES_192_ECB:MBEDTLS_DECRYPT:"000000000000000000000000000000000000000000000000":"4a3650c3371ce2eb35e389a171427440":"bff52510095f518ecca60af4205444bb":0

AES-192-ECB Decrypt NIST KAT #10
depends_on:MBEDTLS_AES_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH:!MBEDTLS_BLOCK_CIPHER_NO_DECRYPT
test_vec_ecb:MBEDTLS_CIPHER_AES_192_ECB:MBEDTLS_DECRYPT:"000000000000000000000000000000000000000000000000":"b2099795e88cc158fd75ea133d7e7fbe":"ffffffffffffffffffffc00000000000":0

AES-192-ECB Decrypt NIST KAT #11
depends_on:MBEDTLS_AES_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH:!MBEDTLS_BLOCK_CIPHER_NO_DECRYPT
test_vec_ecb:MBEDTLS_CIPHER_AES_192_ECB:MBEDTLS_DECRYPT:"000000000000000000000000000000000000000000000000":"a6cae46fb6fadfe7a2c302a34242817b":"ffffffffffffffffffffe00000000000":0

AES-192-ECB Decrypt NIST KAT #12
depends_on:MBEDTLS_AES_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH:!MBEDTLS_BLOCK_CIPHER_NO_DECRYPT
test_vec_ecb:MBEDTLS_CIPHER_AES_192_ECB:MBEDTLS_DECRYPT:"000000000000000000000000000000000000000000000000":"026a7024d6a902e0b3ffccbaa910cc3f":"fffffffffffffffffffff00000000000":0

AES-256-ECB Encrypt NIST KAT #1
depends_on:MBEDTLS_AES_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
test_vec_ecb:MBEDTLS_CIPHER_AES_256_ECB:MBEDTLS_ENCRYPT:"c1cc358b449909a19436cfbb3f852ef8bcb5ed12ac7058325f56e6099aab1a1c":"00000000000000000000000000000000":"352065272169abf9856843927d0674fd":0

AES-256-ECB Encrypt NIST KAT #2
depends_on:MBEDTLS_AES_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
test_vec_ecb:MBEDTLS_CIPHER_AES_256_ECB:MBEDTLS_ENCRYPT:"984ca75f4ee8d706f46c2d98c0bf4a45f5b00d791c2dfeb191b5ed8e420fd627":"00000000000000000000000000000000":"4307456a9e67813b452e15fa8fffe398":0

AES-256-ECB Encrypt NIST KAT #3
depends_on:MBEDTLS_AES_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
test_vec_ecb:MBEDTLS_CIPHER_AES_256_ECB:MBEDTLS_ENCRYPT:"b43d08a447ac8609baadae4ff12918b9f68fc1653f1269222f123981ded7a92f":"00000000000000000000000000000000":"4663446607354989477a5c6f0f007ef4":0

AES-256-ECB Encrypt NIST KAT #4
depends_on:MBEDTLS_AES_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
test_vec_ecb:MBEDTLS_CIPHER_AES_256_ECB:MBEDTLS_ENCRYPT:"0000000000000000000000000000000000000000000000000000000000000000":"0b24af36193ce4665f2825d7b4749c98":"a9ff75bd7cf6613d3731c77c3b6d0c04":0

AES-256-ECB Encrypt NIST KAT #5
depends_on:MBEDTLS_AES_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
test_vec_ecb:MBEDTLS_CIPHER_AES_256_ECB:MBEDTLS_ENCRYPT:"0000000000000000000000000000000000000000000000000000000000000000":"761c1fe41a18acf20d241650611d90f1":"623a52fcea5d443e48d9181ab32c7421":0

AES-256-ECB Encrypt NIST KAT #6
depends_on:MBEDTLS_AES_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
test_vec_ecb:MBEDTLS_CIPHER_AES_256_ECB:MBEDTLS_ENCRYPT:"0000000000000000000000000000000000000000000000000000000000000000":"8a560769d605868ad80d819bdba03771":"38f2c7ae10612415d27ca190d27da8b4":0

AES-256-ECB Encrypt NIST KAT #7
depends_on:MBEDTLS_AES_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
test_vec_ecb:MBEDTLS_CIPHER_AES_256_ECB:MBEDTLS_ENCRYPT:"0000000000000000000000000000000000000000000000000000000000000000":"ffffff80000000000000000000000000":"36aff0ef7bf3280772cf4cac80a0d2b2":0

AES-256-ECB Encrypt NIST KAT #8
depends_on:MBEDTLS_AES_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
test_vec_ecb:MBEDTLS_CIPHER_AES_256_ECB:MBEDTLS_ENCRYPT:"0000000000000000000000000000000000000000000000000000000000000000":"ffffffc0000000000000000000000000":"1f8eedea0f62a1406d58cfc3ecea72cf":0

AES-256-ECB Encrypt NIST KAT #9
depends_on:MBEDTLS_AES_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
test_vec_ecb:MBEDTLS_CIPHER_AES_256_ECB:MBEDTLS_ENCRYPT:"0000000000000000000000000000000000000000000000000000000000000000":"ffffffe0000000000000000000000000":"abf4154a3375a1d3e6b1d454438f95a6":0

AES-256-ECB Encrypt NIST KAT #10
depends_on:MBEDTLS_AES_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
test_vec_ecb:MBEDTLS_CIPHER_AES_256_ECB:MBEDTLS_ENCRYPT:"ffffffffffffffffffffffffffffffffffff8000000000000000000000000000":"00000000000000000000000000000000":"45d089c36d5c5a4efc689e3b0de10dd5":0

AES-256-ECB Encrypt NIST KAT #11
depends_on:MBEDTLS_AES_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
test_vec_ecb:MBEDTLS_CIPHER_AES_256_ECB:MBEDTLS_ENCRYPT:"ffffffffffffffffffffffffffffffffffffc000000000000000000000000000":"00000000000000000000000000000000":"b4da5df4becb5462e03a0ed00d295629":0

AES-256-ECB Encrypt NIST KAT #12
depends_on:MBEDTLS_AES_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
test_vec_ecb:MBEDTLS_CIPHER_AES_256_ECB:MBEDTLS_ENCRYPT:"ffffffffffffffffffffffffffffffffffffe000000000000000000000000000":"00000000000000000000000000000000":"dcf4e129136c1a4b7a0f38935cc34b2b":0

AES-256-ECB Decrypt NIST KAT #1
depends_on:MBEDTLS_AES_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH:!MBEDTLS_BLOCK_CIPHER_NO_DECRYPT
test_vec_ecb:MBEDTLS_CIPHER_AES_256_ECB:MBEDTLS_DECRYPT:"fffffffffffffffffffffffffffffffffffffffffffffff00000000000000000":"edf61ae362e882ddc0167474a7a77f3a":"00000000000000000000000000000000":0

AES-256-ECB Decrypt NIST KAT #2
depends_on:MBEDTLS_AES_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH:!MBEDTLS_BLOCK_CIPHER_NO_DECRYPT
test_vec_ecb:MBEDTLS_CIPHER_AES_256_ECB:MBEDTLS_DECRYPT:"fffffffffffffffffffffffffffffffffffffffffffffff80000000000000000":"6168b00ba7859e0970ecfd757efecf7c":"00000000000000000000000000000000":0

AES-256-ECB Decrypt NIST KAT #3
depends_on:MBEDTLS_AES_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH:!MBEDTLS_BLOCK_CIPHER_NO_DECRYPT
test_vec_ecb:MBEDTLS_CIPHER_AES_256_ECB:MBEDTLS_DECRYPT:"fffffffffffffffffffffffffffffffffffffffffffffffc0000000000000000":"d1415447866230d28bb1ea18a4cdfd02":"00000000000000000000000000000000":0

AES-256-ECB Decrypt NIST KAT #4
depends_on:MBEDTLS_AES_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH:!MBEDTLS_BLOCK_CIPHER_NO_DECRYPT
test_vec_ecb:MBEDTLS_CIPHER_AES_256_ECB:MBEDTLS_DECRYPT:"f8be9ba615c5a952cabbca24f68f8593039624d524c816acda2c9183bd917cb9":"a3944b95ca0b52043584ef02151926a8":"00000000000000000000000000000000":0

AES-256-ECB Decrypt NIST KAT #5
depends_on:MBEDTLS_AES_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH:!MBEDTLS_BLOCK_CIPHER_NO_DECRYPT
test_vec_ecb:MBEDTLS_CIPHER_AES_256_ECB:MBEDTLS_DECRYPT:"797f8b3d176dac5b7e34a2d539c4ef367a16f8635f6264737591c5c07bf57a3e":"a74289fe73a4c123ca189ea1e1b49ad5":"00000000000000000000000000000000":0

AES-256-ECB Decrypt NIST KAT #6
depends_on:MBEDTLS_AES_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH:!MBEDTLS_BLOCK_CIPHER_NO_DECRYPT
test_vec_ecb:MBEDTLS_CIPHER_AES_256_ECB:MBEDTLS_DECRYPT:"6838d40caf927749c13f0329d331f448e202c73ef52c5f73a37ca635d4c47707":"b91d4ea4488644b56cf0812fa7fcf5fc":"00000000000000000000000000000000":0

AES-256-ECB Decrypt NIST KAT #7
depends_on:MBEDTLS_AES_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH:!MBEDTLS_BLOCK_CIPHER_NO_DECRYPT
test_vec_ecb:MBEDTLS_CIPHER_AES_256_ECB:MBEDTLS_DECRYPT:"0000000000000000000000000000000000000000000000000000000000000000":"623a52fcea5d443e48d9181ab32c7421":"761c1fe41a18acf20d241650611d90f1":0

AES-256-ECB Decrypt NIST KAT #8
depends_on:MBEDTLS_AES_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH:!MBEDTLS_BLOCK_CIPHER_NO_DECRYPT
test_vec_ecb:MBEDTLS_CIPHER_AES_256_ECB:MBEDTLS_DECRYPT:"0000000000000000000000000000000000000000000000000000000000000000":"38f2c7ae10612415d27ca190d27da8b4":"8a560769d605868ad80d819bdba03771":0

AES-256-ECB Decrypt NIST KAT #9
depends_on:MBEDTLS_AES_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH:!MBEDTLS_BLOCK_CIPHER_NO_DECRYPT
test_vec_ecb:MBEDTLS_CIPHER_AES_256_ECB:MBEDTLS_DECRYPT:"0000000000000000000000000000000000000000000000000000000000000000":"1bc704f1bce135ceb810341b216d7abe":"91fbef2d15a97816060bee1feaa49afe":0

AES-256-ECB Decrypt NIST KAT #10
depends_on:MBEDTLS_AES_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH:!MBEDTLS_BLOCK_CIPHER_NO_DECRYPT
test_vec_ecb:MBEDTLS_CIPHER_AES_256_ECB:MBEDTLS_DECRYPT:"0000000000000000000000000000000000000000000000000000000000000000":"ddc6bf790c15760d8d9aeb6f9a75fd4e":"80000000000000000000000000000000":0

AES-256-ECB Decrypt NIST KAT #11
depends_on:MBEDTLS_AES_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH:!MBEDTLS_BLOCK_CIPHER_NO_DECRYPT
test_vec_ecb:MBEDTLS_CIPHER_AES_256_ECB:MBEDTLS_DECRYPT:"0000000000000000000000000000000000000000000000000000000000000000":"0a6bdc6d4c1e6280301fd8e97ddbe601":"c0000000000000000000000000000000":0

AES-256-ECB Decrypt NIST KAT #12
depends_on:MBEDTLS_AES_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH:!MBEDTLS_BLOCK_CIPHER_NO_DECRYPT
test_vec_ecb:MBEDTLS_CIPHER_AES_256_ECB:MBEDTLS_DECRYPT:"0000000000000000000000000000000000000000000000000000000000000000":"9b80eefb7ebe2d2b16247aa0efc72f5d":"e0000000000000000000000000000000":0

AES-128-ECB crypt Encrypt NIST KAT #1
depends_on:MBEDTLS_AES_C
test_vec_crypt:MBEDTLS_CIPHER_AES_128_ECB:MBEDTLS_ENCRYPT:"00000000000000000000000000000000":"":"f34481ec3cc627bacd5dc3fb08f273e6":"0336763e966d92595a567cc9ce537f5e":0:0

AES-128-ECB crypt Encrypt NIST KAT #2
depends_on:MBEDTLS_AES_C
test_vec_crypt:MBEDTLS_CIPHER_AES_128_ECB:MBEDTLS_ENCRYPT:"f0000000000000000000000000000000":"":"00000000000000000000000000000000":"970014d634e2b7650777e8e84d03ccd8":0:0

AES-128-ECB crypt Encrypt NIST KAT #3
depends_on:MBEDTLS_AES_C
test_vec_crypt:MBEDTLS_CIPHER_AES_128_ECB:MBEDTLS_ENCRYPT:"00000000000000000000000000000000":"":"ffffffffffffffc00000000000000000":"3a4d354f02bb5a5e47d39666867f246a":0:0

AES-128-ECB crypt Decrypt NIST KAT #1
depends_on:MBEDTLS_AES_C:!MBEDTLS_BLOCK_CIPHER_NO_DECRYPT
test_vec_crypt:MBEDTLS_CIPHER_AES_128_ECB:MBEDTLS_DECRYPT:"00000000000000000000000000000000":"":"db4f1aa530967d6732ce4715eb0ee24b":"ff000000000000000000000000000000":0:0

AES-128-ECB crypt Decrypt NIST KAT #2
depends_on:MBEDTLS_AES_C:!MBEDTLS_BLOCK_CIPHER_NO_DECRYPT
test_vec_crypt:MBEDTLS_CIPHER_AES_128_ECB:MBEDTLS_DECRYPT:"b69418a85332240dc82492353956ae0c":"":"a303d940ded8f0baff6f75414cac5243":"00000000000000000000000000000000":0:0

AES-128-ECB crypt Decrypt NIST KAT #3
depends_on:MBEDTLS_AES_C:!MBEDTLS_BLOCK_CIPHER_NO_DECRYPT
test_vec_crypt:MBEDTLS_CIPHER_AES_128_ECB:MBEDTLS_DECRYPT:"ffffffffffffffff8000000000000000":"":"32cd652842926aea4aa6137bb2be2b5e":"00000000000000000000000000000000":0:0

AES-192-ECB crypt Encrypt NIST KAT #1
depends_on:MBEDTLS_AES_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
test_vec_crypt:MBEDTLS_CIPHER_AES_192_ECB:MBEDTLS_ENCRYPT:"000000000000000000000000000000000000000000000000":"":"fffffffffffffffffffff80000000000":"156f07767a85a4312321f63968338a01":0:0

AES-192-ECB crypt Encrypt NIST KAT #2
depends_on:MBEDTLS_AES_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
test_vec_crypt:MBEDTLS_CIPHER_AES_192_ECB:MBEDTLS_ENCRYPT:"000000000000000000000000000000000000000000000000":"":"51719783d3185a535bd75adc65071ce1":"4f354592ff7c8847d2d0870ca9481b7c":0:0

AES-192-ECB crypt Encrypt NIST KAT #3
depends_on:MBEDTLS_AES_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
test_vec_crypt:MBEDTLS_CIPHER_AES_192_ECB:MBEDTLS_ENCRYPT:"d2926527e0aa9f37b45e2ec2ade5853ef807576104c7ace3":"":"00000000000000000000000000000000":"dd619e1cf204446112e0af2b9afa8f8c":0:0

AES-192-ECB crypt Encrypt NIST KAT #4
depends_on:MBEDTLS_AES_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
test_vec_crypt:MBEDTLS_CIPHER_AES_192_ECB:MBEDTLS_ENCRYPT:"fffffffffffffffffffffffffff800000000000000000000":"":"00000000000000000000000000000000":"8dd274bd0f1b58ae345d9e7233f9b8f3":0:0

AES-192-ECB crypt Decrypt NIST KAT #1
depends_on:MBEDTLS_AES_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH:!MBEDTLS_BLOCK_CIPHER_NO_DECRYPT
test_vec_crypt:MBEDTLS_CIPHER_AES_192_ECB:MBEDTLS_DECRYPT:"fffffffffffffffffffffffffffffffff000000000000000":"":"bb2852c891c5947d2ed44032c421b85f":"00000000000000000000000000000000":0:0

AES-192-ECB crypt Decrypt NIST KAT #2
depends_on:MBEDTLS_AES_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH:!MBEDTLS_BLOCK_CIPHER_NO_DECRYPT
test_vec_crypt:MBEDTLS_CIPHER_AES_192_ECB:MBEDTLS_DECRYPT:"61257134a518a0d57d9d244d45f6498cbc32f2bafc522d79":"":"cfe4d74002696ccf7d87b14a2f9cafc9":"00000000000000000000000000000000":0:0

AES-192-ECB crypt Decrypt NIST KAT #3
depends_on:MBEDTLS_AES_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH:!MBEDTLS_BLOCK_CIPHER_NO_DECRYPT
test_vec_crypt:MBEDTLS_CIPHER_AES_192_ECB:MBEDTLS_DECRYPT:"000000000000000000000000000000000000000000000000":"":"275cfc0413d8ccb70513c3859b1d0f72":"1b077a6af4b7f98229de786d7516b639":0:0

AES-192-ECB crypt Decrypt NIST KAT #4
depends_on:MBEDTLS_AES_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH:!MBEDTLS_BLOCK_CIPHER_NO_DECRYPT
test_vec_crypt:MBEDTLS_CIPHER_AES_192_ECB:MBEDTLS_DECRYPT:"000000000000000000000000000000000000000000000000":"":"b2099795e88cc158fd75ea133d7e7fbe":"ffffffffffffffffffffc00000000000":0:0

AES-256-ECB crypt Encrypt NIST KAT #1
depends_on:MBEDTLS_AES_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
test_vec_crypt:MBEDTLS_CIPHER_AES_256_ECB:MBEDTLS_ENCRYPT:"c1cc358b449909a19436cfbb3f852ef8bcb5ed12ac7058325f56e6099aab1a1c":"":"00000000000000000000000000000000":"352065272169abf9856843927d0674fd":0:0

AES-256-ECB crypt Encrypt NIST KAT #2
depends_on:MBEDTLS_AES_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
test_vec_crypt:MBEDTLS_CIPHER_AES_256_ECB:MBEDTLS_ENCRYPT:"0000000000000000000000000000000000000000000000000000000000000000":"":"0b24af36193ce4665f2825d7b4749c98":"a9ff75bd7cf6613d3731c77c3b6d0c04":0:0

AES-256-ECB crypt Encrypt NIST KAT #3
depends_on:MBEDTLS_AES_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
test_vec_crypt:MBEDTLS_CIPHER_AES_256_ECB:MBEDTLS_ENCRYPT:"0000000000000000000000000000000000000000000000000000000000000000":"":"ffffff80000000000000000000000000":"36aff0ef7bf3280772cf4cac80a0d2b2":0:0

AES-256-ECB crypt Encrypt NIST KAT #4
depends_on:MBEDTLS_AES_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
test_vec_crypt:MBEDTLS_CIPHER_AES_256_ECB:MBEDTLS_ENCRYPT:"ffffffffffffffffffffffffffffffffffff8000000000000000000000000000":"":"00000000000000000000000000000000":"45d089c36d5c5a4efc689e3b0de10dd5":0:0

AES-256-ECB crypt Decrypt NIST KAT #1
depends_on:MBEDTLS_AES_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH:!MBEDTLS_BLOCK_CIPHER_NO_DECRYPT
test_vec_crypt:MBEDTLS_CIPHER_AES_256_ECB:MBEDTLS_DECRYPT:"fffffffffffffffffffffffffffffffffffffffffffffff00000000000000000":"":"edf61ae362e882ddc0167474a7a77f3a":"00000000000000000000000000000000":0:0

AES-256-ECB crypt Decrypt NIST KAT #2
depends_on:MBEDTLS_AES_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH:!MBEDTLS_BLOCK_CIPHER_NO_DECRYPT
test_vec_crypt:MBEDTLS_CIPHER_AES_256_ECB:MBEDTLS_DECRYPT:"f8be9ba615c5a952cabbca24f68f8593039624d524c816acda2c9183bd917cb9":"":"a3944b95ca0b52043584ef02151926a8":"00000000000000000000000000000000":0:0

AES-256-ECB crypt Decrypt NIST KAT #3
depends_on:MBEDTLS_AES_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH:!MBEDTLS_BLOCK_CIPHER_NO_DECRYPT
test_vec_crypt:MBEDTLS_CIPHER_AES_256_ECB:MBEDTLS_DECRYPT:"0000000000000000000000000000000000000000000000000000000000000000":"":"623a52fcea5d443e48d9181ab32c7421":"761c1fe41a18acf20d241650611d90f1":0:0

AES-256-ECB crypt Decrypt NIST KAT #4
depends_on:MBEDTLS_AES_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH:!MBEDTLS_BLOCK_CIPHER_NO_DECRYPT
test_vec_crypt:MBEDTLS_CIPHER_AES_256_ECB:MBEDTLS_DECRYPT:"0000000000000000000000000000000000000000000000000000000000000000":"":"ddc6bf790c15760d8d9aeb6f9a75fd4e":"80000000000000000000000000000000":0:0

AES-128-CBC crypt Encrypt NIST KAT #1
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC
test_vec_crypt:MBEDTLS_CIPHER_AES_128_CBC:MBEDTLS_ENCRYPT:"00000000000000000000000000000000":"00000000000000000000000000000000":"80000000000000000000000000000000":"3ad78e726c1ec02b7ebfe92b23d9ec34":0:0

AES-128-CBC crypt Encrypt NIST KAT #2
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC
test_vec_crypt:MBEDTLS_CIPHER_AES_128_CBC:MBEDTLS_ENCRYPT:"ffffffffffffffffffffffffffffe000":"00000000000000000000000000000000":"00000000000000000000000000000000":"********************************":0:0

AES-128-CBC crypt Encrypt NIST KAT #3
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC
test_vec_crypt:MBEDTLS_CIPHER_AES_128_CBC:MBEDTLS_ENCRYPT:"10a58869d74be5a374cf867cfb473859":"00000000000000000000000000000000":"00000000000000000000000000000000":"6d251e6944b051e04eaa6fb4dbf78465":0:0

AES-128-CBC crypt Encrypt NIST KAT #4
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC
test_vec_crypt:MBEDTLS_CIPHER_AES_128_CBC:MBEDTLS_ENCRYPT:"00000000000000000000000000000000":"00000000000000000000000000000000":"f34481ec3cc627bacd5dc3fb08f273e6":"0336763e966d92595a567cc9ce537f5e":0:0

AES-128-CBC crypt Decrypt NIST KAT #1
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC
test_vec_crypt:MBEDTLS_CIPHER_AES_128_CBC:MBEDTLS_DECRYPT:"00000000000000000000000000000000":"00000000000000000000000000000000":"3ad78e726c1ec02b7ebfe92b23d9ec34":"80000000000000000000000000000000":0:0

AES-128-CBC crypt Decrypt NIST KAT #2
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC
test_vec_crypt:MBEDTLS_CIPHER_AES_128_CBC:MBEDTLS_DECRYPT:"ffffc000000000000000000000000000":"00000000000000000000000000000000":"df556a33438db87bc41b1752c55e5e49":"00000000000000000000000000000000":0:0

AES-128-CBC crypt Decrypt NIST KAT #3
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC
test_vec_crypt:MBEDTLS_CIPHER_AES_128_CBC:MBEDTLS_DECRYPT:"10a58869d74be5a374cf867cfb473859":"00000000000000000000000000000000":"6d251e6944b051e04eaa6fb4dbf78465":"00000000000000000000000000000000":0:0

AES-128-CBC crypt Decrypt NIST KAT #4
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC
test_vec_crypt:MBEDTLS_CIPHER_AES_128_CBC:MBEDTLS_DECRYPT:"00000000000000000000000000000000":"00000000000000000000000000000000":"0336763e966d92595a567cc9ce537f5e":"f34481ec3cc627bacd5dc3fb08f273e6":0:0

AES-192-CBC crypt Encrypt NIST KAT #1
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
test_vec_crypt:MBEDTLS_CIPHER_AES_192_CBC:MBEDTLS_ENCRYPT:"000000000000000000000000000000000000000000000000":"00000000000000000000000000000000":"80000000000000000000000000000000":"6cd02513e8d4dc986b4afe087a60bd0c":0:0

AES-192-CBC crypt Encrypt NIST KAT #2
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
test_vec_crypt:MBEDTLS_CIPHER_AES_192_CBC:MBEDTLS_ENCRYPT:"ff0000000000000000000000000000000000000000000000":"00000000000000000000000000000000":"00000000000000000000000000000000":"833f71258d53036b02952c76c744f5a1":0:0

AES-192-CBC crypt Encrypt NIST KAT #3
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
test_vec_crypt:MBEDTLS_CIPHER_AES_192_CBC:MBEDTLS_ENCRYPT:"e9f065d7c13573587f7875357dfbb16c53489f6a4bd0f7cd":"00000000000000000000000000000000":"00000000000000000000000000000000":"0956259c9cd5cfd0181cca53380cde06":0:0

AES-192-CBC crypt Encrypt NIST KAT #4
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
test_vec_crypt:MBEDTLS_CIPHER_AES_192_CBC:MBEDTLS_ENCRYPT:"000000000000000000000000000000000000000000000000":"00000000000000000000000000000000":"1b077a6af4b7f98229de786d7516b639":"275cfc0413d8ccb70513c3859b1d0f72":0:0

AES-192-CBC crypt Decrypt NIST KAT #1
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
test_vec_crypt:MBEDTLS_CIPHER_AES_192_CBC:MBEDTLS_DECRYPT:"000000000000000000000000000000000000000000000000":"00000000000000000000000000000000":"6cd02513e8d4dc986b4afe087a60bd0c":"80000000000000000000000000000000":0:0

AES-192-CBC crypt Decrypt NIST KAT #2
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
test_vec_crypt:MBEDTLS_CIPHER_AES_192_CBC:MBEDTLS_DECRYPT:"ffe000000000000000000000000000000000000000000000":"00000000000000000000000000000000":"7ababc4b3f516c9aafb35f4140b548f9":"00000000000000000000000000000000":0:0

AES-192-CBC crypt Decrypt NIST KAT #3
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
test_vec_crypt:MBEDTLS_CIPHER_AES_192_CBC:MBEDTLS_DECRYPT:"e9f065d7c13573587f7875357dfbb16c53489f6a4bd0f7cd":"00000000000000000000000000000000":"0956259c9cd5cfd0181cca53380cde06":"00000000000000000000000000000000":0:0

AES-192-CBC crypt Decrypt NIST KAT #4
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
test_vec_crypt:MBEDTLS_CIPHER_AES_192_CBC:MBEDTLS_DECRYPT:"000000000000000000000000000000000000000000000000":"00000000000000000000000000000000":"275cfc0413d8ccb70513c3859b1d0f72":"1b077a6af4b7f98229de786d7516b639":0:0

AES-256-CBC crypt Encrypt NIST KAT #1
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
test_vec_crypt:MBEDTLS_CIPHER_AES_256_CBC:MBEDTLS_ENCRYPT:"0000000000000000000000000000000000000000000000000000000000000000":"00000000000000000000000000000000":"80000000000000000000000000000000":"ddc6bf790c15760d8d9aeb6f9a75fd4e":0:0

AES-256-CBC crypt Encrypt NIST KAT #2
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
test_vec_crypt:MBEDTLS_CIPHER_AES_256_CBC:MBEDTLS_ENCRYPT:"ff00000000000000000000000000000000000000000000000000000000000000":"00000000000000000000000000000000":"00000000000000000000000000000000":"ec52a212f80a09df6317021bc2a9819e":0:0

AES-256-CBC crypt Encrypt NIST KAT #3
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
test_vec_crypt:MBEDTLS_CIPHER_AES_256_CBC:MBEDTLS_ENCRYPT:"c47b0294dbbbee0fec4757f22ffeee3587ca4730c3d33b691df38bab076bc558":"00000000000000000000000000000000":"00000000000000000000000000000000":"46f2fb342d6f0ab477476fc501242c5f":0:0

AES-256-CBC crypt Encrypt NIST KAT #4
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
test_vec_crypt:MBEDTLS_CIPHER_AES_256_CBC:MBEDTLS_ENCRYPT:"0000000000000000000000000000000000000000000000000000000000000000":"00000000000000000000000000000000":"014730f80ac625fe84f026c60bfd547d":"5c9d844ed46f9885085e5d6a4f94c7d7":0:0

AES-256-CBC crypt Decrypt NIST KAT #1
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
test_vec_crypt:MBEDTLS_CIPHER_AES_256_CBC:MBEDTLS_DECRYPT:"0000000000000000000000000000000000000000000000000000000000000000":"00000000000000000000000000000000":"ddc6bf790c15760d8d9aeb6f9a75fd4e":"80000000000000000000000000000000":0:0

AES-256-CBC crypt Decrypt NIST KAT #2
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
test_vec_crypt:MBEDTLS_CIPHER_AES_256_CBC:MBEDTLS_DECRYPT:"ffe0000000000000000000000000000000000000000000000000000000000000":"00000000000000000000000000000000":"d1ccb9b1337002cbac42c520b5d67722":"00000000000000000000000000000000":0:0

AES-256-CBC crypt Decrypt NIST KAT #3
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
test_vec_crypt:MBEDTLS_CIPHER_AES_256_CBC:MBEDTLS_DECRYPT:"c47b0294dbbbee0fec4757f22ffeee3587ca4730c3d33b691df38bab076bc558":"00000000000000000000000000000000":"46f2fb342d6f0ab477476fc501242c5f":"00000000000000000000000000000000":0:0

AES-256-CBC crypt Decrypt NIST KAT #4
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
test_vec_crypt:MBEDTLS_CIPHER_AES_256_CBC:MBEDTLS_DECRYPT:"0000000000000000000000000000000000000000000000000000000000000000":"00000000000000000000000000000000":"5c9d844ed46f9885085e5d6a4f94c7d7":"014730f80ac625fe84f026c60bfd547d":0:0

AES-128-CBC crypt Encrypt NIST KAT #1 PSA
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CBC_NO_PADDING
test_vec_crypt:MBEDTLS_CIPHER_AES_128_CBC:MBEDTLS_ENCRYPT:"00000000000000000000000000000000":"00000000000000000000000000000000":"80000000000000000000000000000000":"3ad78e726c1ec02b7ebfe92b23d9ec34":0:1

AES-128-CBC crypt Encrypt NIST KAT #2 PSA
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CBC_NO_PADDING
test_vec_crypt:MBEDTLS_CIPHER_AES_128_CBC:MBEDTLS_ENCRYPT:"ffffffffffffffffffffffffffffe000":"00000000000000000000000000000000":"00000000000000000000000000000000":"********************************":0:1

AES-128-CBC crypt Encrypt NIST KAT #3 PSA
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CBC_NO_PADDING
test_vec_crypt:MBEDTLS_CIPHER_AES_128_CBC:MBEDTLS_ENCRYPT:"10a58869d74be5a374cf867cfb473859":"00000000000000000000000000000000":"00000000000000000000000000000000":"6d251e6944b051e04eaa6fb4dbf78465":0:1

AES-128-CBC crypt Encrypt NIST KAT #4 PSA
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CBC_NO_PADDING
test_vec_crypt:MBEDTLS_CIPHER_AES_128_CBC:MBEDTLS_ENCRYPT:"00000000000000000000000000000000":"00000000000000000000000000000000":"f34481ec3cc627bacd5dc3fb08f273e6":"0336763e966d92595a567cc9ce537f5e":0:1

AES-128-CBC crypt Decrypt NIST KAT #1 PSA
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CBC_NO_PADDING
test_vec_crypt:MBEDTLS_CIPHER_AES_128_CBC:MBEDTLS_DECRYPT:"00000000000000000000000000000000":"00000000000000000000000000000000":"3ad78e726c1ec02b7ebfe92b23d9ec34":"80000000000000000000000000000000":0:1

AES-128-CBC crypt Decrypt NIST KAT #2 PSA
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CBC_NO_PADDING
test_vec_crypt:MBEDTLS_CIPHER_AES_128_CBC:MBEDTLS_DECRYPT:"ffffc000000000000000000000000000":"00000000000000000000000000000000":"df556a33438db87bc41b1752c55e5e49":"00000000000000000000000000000000":0:1

AES-128-CBC crypt Decrypt NIST KAT #3 PSA
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CBC_NO_PADDING
test_vec_crypt:MBEDTLS_CIPHER_AES_128_CBC:MBEDTLS_DECRYPT:"10a58869d74be5a374cf867cfb473859":"00000000000000000000000000000000":"6d251e6944b051e04eaa6fb4dbf78465":"00000000000000000000000000000000":0:1

AES-128-CBC crypt Decrypt NIST KAT #4 PSA
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CBC_NO_PADDING
test_vec_crypt:MBEDTLS_CIPHER_AES_128_CBC:MBEDTLS_DECRYPT:"00000000000000000000000000000000":"00000000000000000000000000000000":"0336763e966d92595a567cc9ce537f5e":"f34481ec3cc627bacd5dc3fb08f273e6":0:1

AES-192-CBC crypt Encrypt NIST KAT #1 PSA
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CBC_NO_PADDING:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
test_vec_crypt:MBEDTLS_CIPHER_AES_192_CBC:MBEDTLS_ENCRYPT:"000000000000000000000000000000000000000000000000":"00000000000000000000000000000000":"80000000000000000000000000000000":"6cd02513e8d4dc986b4afe087a60bd0c":0:1

AES-192-CBC crypt Encrypt NIST KAT #2 PSA
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CBC_NO_PADDING:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
test_vec_crypt:MBEDTLS_CIPHER_AES_192_CBC:MBEDTLS_ENCRYPT:"ff0000000000000000000000000000000000000000000000":"00000000000000000000000000000000":"00000000000000000000000000000000":"833f71258d53036b02952c76c744f5a1":0:1

AES-192-CBC crypt Encrypt NIST KAT #3 PSA
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CBC_NO_PADDING:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
test_vec_crypt:MBEDTLS_CIPHER_AES_192_CBC:MBEDTLS_ENCRYPT:"e9f065d7c13573587f7875357dfbb16c53489f6a4bd0f7cd":"00000000000000000000000000000000":"00000000000000000000000000000000":"0956259c9cd5cfd0181cca53380cde06":0:1

AES-192-CBC crypt Encrypt NIST KAT #4 PSA
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CBC_NO_PADDING:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
test_vec_crypt:MBEDTLS_CIPHER_AES_192_CBC:MBEDTLS_ENCRYPT:"000000000000000000000000000000000000000000000000":"00000000000000000000000000000000":"1b077a6af4b7f98229de786d7516b639":"275cfc0413d8ccb70513c3859b1d0f72":0:1

AES-192-CBC crypt Decrypt NIST KAT #1 PSA
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CBC_NO_PADDING:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
test_vec_crypt:MBEDTLS_CIPHER_AES_192_CBC:MBEDTLS_DECRYPT:"000000000000000000000000000000000000000000000000":"00000000000000000000000000000000":"6cd02513e8d4dc986b4afe087a60bd0c":"80000000000000000000000000000000":0:1

AES-192-CBC crypt Decrypt NIST KAT #2 PSA
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CBC_NO_PADDING:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
test_vec_crypt:MBEDTLS_CIPHER_AES_192_CBC:MBEDTLS_DECRYPT:"ffe000000000000000000000000000000000000000000000":"00000000000000000000000000000000":"7ababc4b3f516c9aafb35f4140b548f9":"00000000000000000000000000000000":0:1

AES-192-CBC crypt Decrypt NIST KAT #3 PSA
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CBC_NO_PADDING:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
test_vec_crypt:MBEDTLS_CIPHER_AES_192_CBC:MBEDTLS_DECRYPT:"e9f065d7c13573587f7875357dfbb16c53489f6a4bd0f7cd":"00000000000000000000000000000000":"0956259c9cd5cfd0181cca53380cde06":"00000000000000000000000000000000":0:1

AES-192-CBC crypt Decrypt NIST KAT #4 PSA
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CBC_NO_PADDING:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
test_vec_crypt:MBEDTLS_CIPHER_AES_192_CBC:MBEDTLS_DECRYPT:"000000000000000000000000000000000000000000000000":"00000000000000000000000000000000":"275cfc0413d8ccb70513c3859b1d0f72":"1b077a6af4b7f98229de786d7516b639":0:1

AES-256-CBC crypt Encrypt NIST KAT #1 PSA
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CBC_NO_PADDING:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
test_vec_crypt:MBEDTLS_CIPHER_AES_256_CBC:MBEDTLS_ENCRYPT:"0000000000000000000000000000000000000000000000000000000000000000":"00000000000000000000000000000000":"80000000000000000000000000000000":"ddc6bf790c15760d8d9aeb6f9a75fd4e":0:1

AES-256-CBC crypt Encrypt NIST KAT #2 PSA
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CBC_NO_PADDING:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
test_vec_crypt:MBEDTLS_CIPHER_AES_256_CBC:MBEDTLS_ENCRYPT:"ff00000000000000000000000000000000000000000000000000000000000000":"00000000000000000000000000000000":"00000000000000000000000000000000":"ec52a212f80a09df6317021bc2a9819e":0:1

AES-256-CBC crypt Encrypt NIST KAT #3 PSA
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CBC_NO_PADDING:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
test_vec_crypt:MBEDTLS_CIPHER_AES_256_CBC:MBEDTLS_ENCRYPT:"c47b0294dbbbee0fec4757f22ffeee3587ca4730c3d33b691df38bab076bc558":"00000000000000000000000000000000":"00000000000000000000000000000000":"46f2fb342d6f0ab477476fc501242c5f":0:1

AES-256-CBC crypt Encrypt NIST KAT #4 PSA
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CBC_NO_PADDING:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
test_vec_crypt:MBEDTLS_CIPHER_AES_256_CBC:MBEDTLS_ENCRYPT:"0000000000000000000000000000000000000000000000000000000000000000":"00000000000000000000000000000000":"014730f80ac625fe84f026c60bfd547d":"5c9d844ed46f9885085e5d6a4f94c7d7":0:1

AES-256-CBC crypt Decrypt NIST KAT #1 PSA
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CBC_NO_PADDING:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
test_vec_crypt:MBEDTLS_CIPHER_AES_256_CBC:MBEDTLS_DECRYPT:"0000000000000000000000000000000000000000000000000000000000000000":"00000000000000000000000000000000":"ddc6bf790c15760d8d9aeb6f9a75fd4e":"80000000000000000000000000000000":0:1

AES-256-CBC crypt Decrypt NIST KAT #2 PSA
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CBC_NO_PADDING:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
test_vec_crypt:MBEDTLS_CIPHER_AES_256_CBC:MBEDTLS_DECRYPT:"ffe0000000000000000000000000000000000000000000000000000000000000":"00000000000000000000000000000000":"d1ccb9b1337002cbac42c520b5d67722":"00000000000000000000000000000000":0:1

AES-256-CBC crypt Decrypt NIST KAT #3 PSA
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CBC_NO_PADDING:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
test_vec_crypt:MBEDTLS_CIPHER_AES_256_CBC:MBEDTLS_DECRYPT:"c47b0294dbbbee0fec4757f22ffeee3587ca4730c3d33b691df38bab076bc558":"00000000000000000000000000000000":"46f2fb342d6f0ab477476fc501242c5f":"00000000000000000000000000000000":0:1

AES-256-CBC crypt Decrypt NIST KAT #4 PSA
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CBC_NO_PADDING:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
test_vec_crypt:MBEDTLS_CIPHER_AES_256_CBC:MBEDTLS_DECRYPT:"0000000000000000000000000000000000000000000000000000000000000000":"00000000000000000000000000000000":"5c9d844ed46f9885085e5d6a4f94c7d7":"014730f80ac625fe84f026c60bfd547d":0:1

AES-128-ECB crypt Encrypt NIST KAT #1 PSA
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_ECB_NO_PADDING
test_vec_crypt:MBEDTLS_CIPHER_AES_128_ECB:MBEDTLS_ENCRYPT:"00000000000000000000000000000000":"":"80000000000000000000000000000000":"3ad78e726c1ec02b7ebfe92b23d9ec34":0:1

AES-128-ECB crypt Encrypt NIST KAT #2 PSA
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_ECB_NO_PADDING
test_vec_crypt:MBEDTLS_CIPHER_AES_128_ECB:MBEDTLS_ENCRYPT:"ffffffffffffffffffffffffffffe000":"":"00000000000000000000000000000000":"********************************":0:1

AES-128-ECB crypt Encrypt NIST KAT #3 PSA
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_ECB_NO_PADDING
test_vec_crypt:MBEDTLS_CIPHER_AES_128_ECB:MBEDTLS_ENCRYPT:"10a58869d74be5a374cf867cfb473859":"":"00000000000000000000000000000000":"6d251e6944b051e04eaa6fb4dbf78465":0:1

AES-128-ECB crypt Encrypt NIST KAT #4 PSA
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_ECB_NO_PADDING
test_vec_crypt:MBEDTLS_CIPHER_AES_128_ECB:MBEDTLS_ENCRYPT:"00000000000000000000000000000000":"":"f34481ec3cc627bacd5dc3fb08f273e6":"0336763e966d92595a567cc9ce537f5e":0:1

AES-128-ECB crypt Decrypt NIST KAT #1 PSA
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_ECB_NO_PADDING:!MBEDTLS_BLOCK_CIPHER_NO_DECRYPT
test_vec_crypt:MBEDTLS_CIPHER_AES_128_ECB:MBEDTLS_DECRYPT:"00000000000000000000000000000000":"":"3ad78e726c1ec02b7ebfe92b23d9ec34":"80000000000000000000000000000000":0:1

AES-128-ECB crypt Decrypt NIST KAT #2 PSA
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_ECB_NO_PADDING:!MBEDTLS_BLOCK_CIPHER_NO_DECRYPT
test_vec_crypt:MBEDTLS_CIPHER_AES_128_ECB:MBEDTLS_DECRYPT:"ffffc000000000000000000000000000":"":"df556a33438db87bc41b1752c55e5e49":"00000000000000000000000000000000":0:1

AES-128-ECB crypt Decrypt NIST KAT #3 PSA
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_ECB_NO_PADDING:!MBEDTLS_BLOCK_CIPHER_NO_DECRYPT
test_vec_crypt:MBEDTLS_CIPHER_AES_128_ECB:MBEDTLS_DECRYPT:"10a58869d74be5a374cf867cfb473859":"":"6d251e6944b051e04eaa6fb4dbf78465":"00000000000000000000000000000000":0:1

AES-128-ECB crypt Decrypt NIST KAT #4 PSA
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_ECB_NO_PADDING:!MBEDTLS_BLOCK_CIPHER_NO_DECRYPT
test_vec_crypt:MBEDTLS_CIPHER_AES_128_ECB:MBEDTLS_DECRYPT:"00000000000000000000000000000000":"":"0336763e966d92595a567cc9ce537f5e":"f34481ec3cc627bacd5dc3fb08f273e6":0:1

AES-192-ECB crypt Encrypt NIST KAT #1 PSA
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_ECB_NO_PADDING:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
test_vec_crypt:MBEDTLS_CIPHER_AES_192_ECB:MBEDTLS_ENCRYPT:"000000000000000000000000000000000000000000000000":"":"80000000000000000000000000000000":"6cd02513e8d4dc986b4afe087a60bd0c":0:1

AES-192-ECB crypt Encrypt NIST KAT #2 PSA
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_ECB_NO_PADDING:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
test_vec_crypt:MBEDTLS_CIPHER_AES_192_ECB:MBEDTLS_ENCRYPT:"ff0000000000000000000000000000000000000000000000":"":"00000000000000000000000000000000":"833f71258d53036b02952c76c744f5a1":0:1

AES-192-ECB crypt Encrypt NIST KAT #3 PSA
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_ECB_NO_PADDING:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
test_vec_crypt:MBEDTLS_CIPHER_AES_192_ECB:MBEDTLS_ENCRYPT:"e9f065d7c13573587f7875357dfbb16c53489f6a4bd0f7cd":"":"00000000000000000000000000000000":"0956259c9cd5cfd0181cca53380cde06":0:1

AES-192-ECB crypt Encrypt NIST KAT #4 PSA
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_ECB_NO_PADDING:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
test_vec_crypt:MBEDTLS_CIPHER_AES_192_ECB:MBEDTLS_ENCRYPT:"000000000000000000000000000000000000000000000000":"":"1b077a6af4b7f98229de786d7516b639":"275cfc0413d8ccb70513c3859b1d0f72":0:1

AES-192-ECB crypt Decrypt NIST KAT #1 PSA
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_ECB_NO_PADDING:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH:!MBEDTLS_BLOCK_CIPHER_NO_DECRYPT
test_vec_crypt:MBEDTLS_CIPHER_AES_192_ECB:MBEDTLS_DECRYPT:"000000000000000000000000000000000000000000000000":"":"6cd02513e8d4dc986b4afe087a60bd0c":"80000000000000000000000000000000":0:1

AES-192-ECB crypt Decrypt NIST KAT #2 PSA
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_ECB_NO_PADDING:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH:!MBEDTLS_BLOCK_CIPHER_NO_DECRYPT
test_vec_crypt:MBEDTLS_CIPHER_AES_192_ECB:MBEDTLS_DECRYPT:"ffe000000000000000000000000000000000000000000000":"":"7ababc4b3f516c9aafb35f4140b548f9":"00000000000000000000000000000000":0:1

AES-192-ECB crypt Decrypt NIST KAT #3 PSA
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_ECB_NO_PADDING:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH:!MBEDTLS_BLOCK_CIPHER_NO_DECRYPT
test_vec_crypt:MBEDTLS_CIPHER_AES_192_ECB:MBEDTLS_DECRYPT:"e9f065d7c13573587f7875357dfbb16c53489f6a4bd0f7cd":"":"0956259c9cd5cfd0181cca53380cde06":"00000000000000000000000000000000":0:1

AES-192-ECB crypt Decrypt NIST KAT #4 PSA
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_ECB_NO_PADDING:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH:!MBEDTLS_BLOCK_CIPHER_NO_DECRYPT
test_vec_crypt:MBEDTLS_CIPHER_AES_192_ECB:MBEDTLS_DECRYPT:"000000000000000000000000000000000000000000000000":"":"275cfc0413d8ccb70513c3859b1d0f72":"1b077a6af4b7f98229de786d7516b639":0:1

AES-256-ECB crypt Encrypt NIST KAT #1 PSA
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_ECB_NO_PADDING:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
test_vec_crypt:MBEDTLS_CIPHER_AES_256_ECB:MBEDTLS_ENCRYPT:"0000000000000000000000000000000000000000000000000000000000000000":"":"80000000000000000000000000000000":"ddc6bf790c15760d8d9aeb6f9a75fd4e":0:1

AES-256-ECB crypt Encrypt NIST KAT #2 PSA
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_ECB_NO_PADDING:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
test_vec_crypt:MBEDTLS_CIPHER_AES_256_ECB:MBEDTLS_ENCRYPT:"ff00000000000000000000000000000000000000000000000000000000000000":"":"00000000000000000000000000000000":"ec52a212f80a09df6317021bc2a9819e":0:1

AES-256-ECB crypt Encrypt NIST KAT #3 PSA
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_ECB_NO_PADDING:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
test_vec_crypt:MBEDTLS_CIPHER_AES_256_ECB:MBEDTLS_ENCRYPT:"c47b0294dbbbee0fec4757f22ffeee3587ca4730c3d33b691df38bab076bc558":"":"00000000000000000000000000000000":"46f2fb342d6f0ab477476fc501242c5f":0:1

AES-256-ECB crypt Encrypt NIST KAT #4 PSA
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_ECB_NO_PADDING:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
test_vec_crypt:MBEDTLS_CIPHER_AES_256_ECB:MBEDTLS_ENCRYPT:"0000000000000000000000000000000000000000000000000000000000000000":"":"014730f80ac625fe84f026c60bfd547d":"5c9d844ed46f9885085e5d6a4f94c7d7":0:1

AES-256-ECB crypt Decrypt NIST KAT #1 PSA
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_ECB_NO_PADDING:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH:!MBEDTLS_BLOCK_CIPHER_NO_DECRYPT
test_vec_crypt:MBEDTLS_CIPHER_AES_256_ECB:MBEDTLS_DECRYPT:"0000000000000000000000000000000000000000000000000000000000000000":"":"ddc6bf790c15760d8d9aeb6f9a75fd4e":"80000000000000000000000000000000":0:1

AES-256-ECB crypt Decrypt NIST KAT #2 PSA
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_ECB_NO_PADDING:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH:!MBEDTLS_BLOCK_CIPHER_NO_DECRYPT
test_vec_crypt:MBEDTLS_CIPHER_AES_256_ECB:MBEDTLS_DECRYPT:"ffe0000000000000000000000000000000000000000000000000000000000000":"":"d1ccb9b1337002cbac42c520b5d67722":"00000000000000000000000000000000":0:1

AES-256-ECB crypt Decrypt NIST KAT #3 PSA
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_ECB_NO_PADDING:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH:!MBEDTLS_BLOCK_CIPHER_NO_DECRYPT
test_vec_crypt:MBEDTLS_CIPHER_AES_256_ECB:MBEDTLS_DECRYPT:"c47b0294dbbbee0fec4757f22ffeee3587ca4730c3d33b691df38bab076bc558":"":"46f2fb342d6f0ab477476fc501242c5f":"00000000000000000000000000000000":0:1

AES-256-ECB crypt Decrypt NIST KAT #4 PSA
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_ECB_NO_PADDING:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH:!MBEDTLS_BLOCK_CIPHER_NO_DECRYPT
test_vec_crypt:MBEDTLS_CIPHER_AES_256_ECB:MBEDTLS_DECRYPT:"0000000000000000000000000000000000000000000000000000000000000000":"":"5c9d844ed46f9885085e5d6a4f94c7d7":"014730f80ac625fe84f026c60bfd547d":0:1

AES-128-CCM*-NO-TAG crypt Encrypt NIST VPT AES-128 #15
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:MBEDTLS_AES_C:MBEDTLS_CCM_C
test_vec_crypt:MBEDTLS_CIPHER_AES_128_CCM_STAR_NO_TAG:MBEDTLS_ENCRYPT:"7301c907b9d2aaac355c5416ff25c59b":"7304b65b6dab466273862c88b9":"484300aa3a506afcd313b49ead8d":"928ca58b0d373dc50c52afac787c":0:0

AES-128-CCM*-NO-TAG crypt Decrypt NIST DVPT AES-128 #15
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:MBEDTLS_AES_C:MBEDTLS_CCM_C
test_vec_crypt:MBEDTLS_CIPHER_AES_128_CCM_STAR_NO_TAG:MBEDTLS_DECRYPT:"90929a4b0ac65b350ad1591611fe4829":"5a8aa485c316e9403aff859fbb":"4bfe4e35784f0a65b545477e5e2f4bae0e1e6fa717eaf2cb":"a16a2e741f1cd9717285b6d882c1fc53655e9773761ad697":0:0

AES-192-CCM*-NO-TAG crypt Encrypt NIST VTT AES-192 #1
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
test_vec_crypt:MBEDTLS_CIPHER_AES_192_CCM_STAR_NO_TAG:MBEDTLS_ENCRYPT:"11fd45743d946e6d37341fec49947e8c70482494a8f07fcc":"c6aeebcb146cfafaae66f78aab":"ee7e6075ba52846de5d6254959a18affc4faf59c8ef63489":"137d9da59baf5cbfd46620c5f298fc766de10ac68e774edf":0:0

AES-192-CCM*-NO-TAG crypt Decrypt NIST DVPT AES-192 #15
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
test_vec_crypt:MBEDTLS_CIPHER_AES_192_CCM_STAR_NO_TAG:MBEDTLS_DECRYPT:"90929a4b0ac65b350ad1591611fe48297e03956f6083e451":"5a8aa485c316e9403aff859fbb":"a5b7d8cca2069908d1ed88e6a9fe2c9bede3131dad54671e":"a16a2e741f1cd9717285b6d882c1fc53655e9773761ad697":0:0

AES-256-CCM*-NO-TAG crypt Encrypt NIST VADT AES-256 #1
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
test_vec_crypt:MBEDTLS_CIPHER_AES_256_CCM_STAR_NO_TAG:MBEDTLS_ENCRYPT:"26511fb51fcfa75cb4b44da75a6e5a0eb8d9c8f3b906f886df3ba3e6da3a1389":"72a60f345a1978fb40f28a2fa4":"30d56ff2a25b83fee791110fcaea48e41db7c7f098a81000":"55f068c0bbba8b598013dd1841fd740fda2902322148ab5e":0:0

AES-256-CCM*-NO-TAG crypt Decrypt NIST DVPT AES-256 #13
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
test_vec_crypt:MBEDTLS_CIPHER_AES_256_CCM_STAR_NO_TAG:MBEDTLS_DECRYPT:"f7079dfa3b5c7b056347d7e437bcded683abd6e2c9e069d333284082cbb5d453":"a544218dadd3c10583db49cf39":"63e00d30e4b08fd2a1cc8d70fab327b2368e77a93be4f412":"3c0e2815d37d844f7ac240ba9d6e3a0b2a86f706e885959e":0:0

AES-128-CCM*-NO-TAG crypt Encrypt NIST VPT AES-128 #15 PSA
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CCM_STAR_NO_TAG
test_vec_crypt:MBEDTLS_CIPHER_AES_128_CCM_STAR_NO_TAG:MBEDTLS_ENCRYPT:"7301c907b9d2aaac355c5416ff25c59b":"7304b65b6dab466273862c88b9":"484300aa3a506afcd313b49ead8d":"928ca58b0d373dc50c52afac787c":0:1

AES-128-CCM*-NO-TAG crypt Decrypt NIST DVPT AES-128 #15 PSA
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CCM_STAR_NO_TAG
test_vec_crypt:MBEDTLS_CIPHER_AES_128_CCM_STAR_NO_TAG:MBEDTLS_DECRYPT:"90929a4b0ac65b350ad1591611fe4829":"5a8aa485c316e9403aff859fbb":"4bfe4e35784f0a65b545477e5e2f4bae0e1e6fa717eaf2cb":"a16a2e741f1cd9717285b6d882c1fc53655e9773761ad697":0:1

AES-192-CCM*-NO-TAG crypt Encrypt NIST VTT AES-192 #1 PSA
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CCM_STAR_NO_TAG:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
test_vec_crypt:MBEDTLS_CIPHER_AES_192_CCM_STAR_NO_TAG:MBEDTLS_ENCRYPT:"11fd45743d946e6d37341fec49947e8c70482494a8f07fcc":"c6aeebcb146cfafaae66f78aab":"ee7e6075ba52846de5d6254959a18affc4faf59c8ef63489":"137d9da59baf5cbfd46620c5f298fc766de10ac68e774edf":0:1

AES-192-CCM*-NO-TAG crypt Decrypt NIST DVPT AES-192 #15 PSA
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CCM_STAR_NO_TAG:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
test_vec_crypt:MBEDTLS_CIPHER_AES_192_CCM_STAR_NO_TAG:MBEDTLS_DECRYPT:"90929a4b0ac65b350ad1591611fe48297e03956f6083e451":"5a8aa485c316e9403aff859fbb":"a5b7d8cca2069908d1ed88e6a9fe2c9bede3131dad54671e":"a16a2e741f1cd9717285b6d882c1fc53655e9773761ad697":0:1

AES-256-CCM*-NO-TAG crypt Encrypt NIST VADT AES-256 #1 PSA
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CCM_STAR_NO_TAG:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
test_vec_crypt:MBEDTLS_CIPHER_AES_256_CCM_STAR_NO_TAG:MBEDTLS_ENCRYPT:"26511fb51fcfa75cb4b44da75a6e5a0eb8d9c8f3b906f886df3ba3e6da3a1389":"72a60f345a1978fb40f28a2fa4":"30d56ff2a25b83fee791110fcaea48e41db7c7f098a81000":"55f068c0bbba8b598013dd1841fd740fda2902322148ab5e":0:1

AES-256-CCM*-NO-TAG crypt Decrypt NIST DVPT AES-256 #13 PSA
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CCM_STAR_NO_TAG:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
test_vec_crypt:MBEDTLS_CIPHER_AES_256_CCM_STAR_NO_TAG:MBEDTLS_DECRYPT:"f7079dfa3b5c7b056347d7e437bcded683abd6e2c9e069d333284082cbb5d453":"a544218dadd3c10583db49cf39":"63e00d30e4b08fd2a1cc8d70fab327b2368e77a93be4f412":"3c0e2815d37d844f7ac240ba9d6e3a0b2a86f706e885959e":0:1

Cipher Corner Case behaviours
depends_on:MBEDTLS_AES_C
cipher_special_behaviours:

Check set padding - 128 bit key
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC
check_set_padding:MBEDTLS_CIPHER_AES_128_CBC

Check set padding - 192 bit key
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
check_set_padding:MBEDTLS_CIPHER_AES_192_CBC

Check set padding - 256 bit key
depends_on:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
check_set_padding:MBEDTLS_CIPHER_AES_256_CBC
