CCM self test
mbedtls_ccm_self_test:

CCM init #1 AES-128: OK
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_setkey:MBEDTLS_CIPHER_ID_AES:128:0

CCM init #2 CAMELLIA-256: OK
depends_on:MBEDTLS_CCM_GCM_CAN_CAMELLIA
mbedtls_ccm_setkey:MBEDTLS_CIPHER_ID_CAMELLIA:256:0

CCM init #3 AES-224: bad key size
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_setkey:MBEDTLS_CIPHER_ID_AES:224:MBEDTLS_ERR_CCM_BAD_INPUT

CCM init #4 DES: bad block size
depends_on:MBEDTLS_DES_C
mbedtls_ccm_setkey:MBEDTLS_CIPHER_ID_DES:128:MBEDTLS_ERR_CCM_BAD_INPUT

CCM lengths #1 all OK
ccm_lengths:5:10:5:8:0

CCM lengths #2 nonce too short
ccm_lengths:5:6:5:8:MBEDTLS_ERR_CCM_BAD_INPUT

CCM lengths #3 nonce too long
ccm_lengths:5:14:5:8:MBEDTLS_ERR_CCM_BAD_INPUT

CCM lengths #4 tag too short
ccm_lengths:5:10:5:2:MBEDTLS_ERR_CCM_BAD_INPUT

CCM lengths #5 tag too long
ccm_lengths:5:10:5:18:MBEDTLS_ERR_CCM_BAD_INPUT

CCM lengths #6 tag length not even
ccm_lengths:5:10:5:7:MBEDTLS_ERR_CCM_BAD_INPUT

CCM lengths #7 AD too long (2^16 - 2^8)
depends_on:!MBEDTLS_CCM_ALT
ccm_lengths:5:10:65280:8:MBEDTLS_ERR_CCM_BAD_INPUT

CCM lengths #8 msg too long for this IV length (2^16, q = 2)
ccm_lengths:65536:13:5:8:MBEDTLS_ERR_CCM_BAD_INPUT

CCM lengths #9 tag length 0
ccm_lengths:5:10:5:0:MBEDTLS_ERR_CCM_BAD_INPUT

CCM lengths #10 Large AD
ccm_lengths:5:10:32768:8:0

CCM* fixed tag lengths #1 all OK
ccm_star_lengths:5:10:5:8:0

CCM* fixed tag lengths #2 all OK - tag length 0
ccm_star_lengths:5:10:5:0:0

CCM* encrypt and tag #1
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_star_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"C0C1C2C3C4C5C6C7C8C9CACBCCCDCECF":"":"ACDE480000000001":"00000005":2:"08D0842143010000000048DEAC020500000055CF000051525354":"223BC1EC841AB553":0

CCM* encrypt and tag #2
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_star_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"C0C1C2C3C4C5C6C7C8C9CACBCCCDCECF":"61626364":"ACDE480000000001":"00000005":4:"69DC842143020000000048DEAC010000000048DEAC0405000000":"D43E022B":0

CCM* encrypt and tag #3
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_star_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"C0C1C2C3C4C5C6C7C8C9CACBCCCDCECF":"CE":"ACDE480000000001":"00000005":6:"2BDC842143020000000048DEACFFFF010000000048DEAC060500000001":"D84FDE529061F9C6F1":0

CCM* auth decrypt tag #1
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_star_auth_decrypt:MBEDTLS_CIPHER_ID_AES:"C0C1C2C3C4C5C6C7C8C9CACBCCCDCECF":"223BC1EC841AB553":"ACDE480000000001":"00000005":2:"08D0842143010000000048DEAC020500000055CF000051525354":"":0

CCM* auth decrypt tag #2
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_star_auth_decrypt:MBEDTLS_CIPHER_ID_AES:"C0C1C2C3C4C5C6C7C8C9CACBCCCDCECF":"D43E022B":"ACDE480000000001":"00000005":4:"69DC842143020000000048DEAC010000000048DEAC0405000000":"61626364":0

CCM* auth decrypt tag #3
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_star_auth_decrypt:MBEDTLS_CIPHER_ID_AES:"C0C1C2C3C4C5C6C7C8C9CACBCCCDCECF":"D84FDE529061F9C6F1":"ACDE480000000001":"00000005":6:"2BDC842143020000000048DEACFFFF010000000048DEAC060500000001":"CE":0

CCM encrypt and tag RFC 3610 #1
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"C0C1C2C3C4C5C6C7C8C9CACBCCCDCECF":"08090A0B0C0D0E0F101112131415161718191A1B1C1D1E":"00000003020100A0A1A2A3A4A5":"0001020304050607":"588C979A61C663D2F066D0C2C0F989806D5F6B61DAC38417E8D12CFDF926E0"

CCM encrypt and tag RFC 3610 #2
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"C0C1C2C3C4C5C6C7C8C9CACBCCCDCECF":"08090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F":"00000004030201A0A1A2A3A4A5":"0001020304050607":"72C91A36E135F8CF291CA894085C87E3CC15C439C9E43A3BA091D56E10400916"

CCM encrypt and tag RFC 3610 #3
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"C0C1C2C3C4C5C6C7C8C9CACBCCCDCECF":"08090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F20":"00000005040302A0A1A2A3A4A5":"0001020304050607":"51B1E5F44A197D1DA46B0F8E2D282AE871E838BB64DA8596574ADAA76FBD9FB0C5"

CCM encrypt and tag RFC 3610 #4
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"C0C1C2C3C4C5C6C7C8C9CACBCCCDCECF":"0C0D0E0F101112131415161718191A1B1C1D1E":"00000006050403A0A1A2A3A4A5":"000102030405060708090A0B":"A28C6865939A9A79FAAA5C4C2A9D4A91CDAC8C96C861B9C9E61EF1"

CCM encrypt and tag RFC 3610 #5
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"C0C1C2C3C4C5C6C7C8C9CACBCCCDCECF":"0C0D0E0F101112131415161718191A1B1C1D1E1F":"00000007060504A0A1A2A3A4A5":"000102030405060708090A0B":"DCF1FB7B5D9E23FB9D4E131253658AD86EBDCA3E51E83F077D9C2D93"

CCM encrypt and tag RFC 3610 #6
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"C0C1C2C3C4C5C6C7C8C9CACBCCCDCECF":"0C0D0E0F101112131415161718191A1B1C1D1E1F20":"00000008070605A0A1A2A3A4A5":"000102030405060708090A0B":"6FC1B011F006568B5171A42D953D469B2570A4BD87405A0443AC91CB94"

CCM encrypt and tag RFC 3610 #7
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"C0C1C2C3C4C5C6C7C8C9CACBCCCDCECF":"08090A0B0C0D0E0F101112131415161718191A1B1C1D1E":"00000009080706A0A1A2A3A4A5":"0001020304050607":"0135D1B2C95F41D5D1D4FEC185D166B8094E999DFED96C048C56602C97ACBB7490"

CCM encrypt and tag RFC 3610 #8
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"C0C1C2C3C4C5C6C7C8C9CACBCCCDCECF":"08090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F":"0000000A090807A0A1A2A3A4A5":"0001020304050607":"7B75399AC0831DD2F0BBD75879A2FD8F6CAE6B6CD9B7DB24C17B4433F434963F34B4"

CCM encrypt and tag RFC 3610 #9
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"C0C1C2C3C4C5C6C7C8C9CACBCCCDCECF":"08090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F20":"0000000B0A0908A0A1A2A3A4A5":"0001020304050607":"82531A60CC24945A4B8279181AB5C84DF21CE7F9B73F42E197EA9C07E56B5EB17E5F4E"

CCM encrypt and tag RFC 3610 #10
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"C0C1C2C3C4C5C6C7C8C9CACBCCCDCECF":"0C0D0E0F101112131415161718191A1B1C1D1E":"0000000C0B0A09A0A1A2A3A4A5":"000102030405060708090A0B":"07342594157785152B074098330ABB141B947B566AA9406B4D999988DD"

CCM encrypt and tag RFC 3610 #11
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"C0C1C2C3C4C5C6C7C8C9CACBCCCDCECF":"0C0D0E0F101112131415161718191A1B1C1D1E1F":"0000000D0C0B0AA0A1A2A3A4A5":"000102030405060708090A0B":"676BB20380B0E301E8AB79590A396DA78B834934F53AA2E9107A8B6C022C"

CCM encrypt and tag RFC 3610 #12
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"C0C1C2C3C4C5C6C7C8C9CACBCCCDCECF":"0C0D0E0F101112131415161718191A1B1C1D1E1F20":"0000000E0D0C0BA0A1A2A3A4A5":"000102030405060708090A0B":"C0FFA0D6F05BDB67F24D43A4338D2AA4BED7B20E43CD1AA31662E7AD65D6DB"

CCM encrypt and tag RFC 3610 #13
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"D7828D13B2B0BDC325A76236DF93CC6B":"08E8CF97D820EA258460E96AD9CF5289054D895CEAC47C":"00412B4EA9CDBE3C9696766CFA":"0BE1A88BACE018B1":"4CB97F86A2A4689A877947AB8091EF5386A6FFBDD080F8E78CF7CB0CDDD7B3"

CCM encrypt and tag RFC 3610 #14
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"D7828D13B2B0BDC325A76236DF93CC6B":"9020EA6F91BDD85AFA0039BA4BAFF9BFB79C7028949CD0EC":"0033568EF7B2633C9696766CFA":"63018F76DC8A1BCB":"4CCB1E7CA981BEFAA0726C55D378061298C85C92814ABC33C52EE81D7D77C08A"

CCM encrypt and tag RFC 3610 #15
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"D7828D13B2B0BDC325A76236DF93CC6B":"B916E0EACC1C00D7DCEC68EC0B3BBB1A02DE8A2D1AA346132E":"00103FE41336713C9696766CFA":"AA6CFA36CAE86B40":"B1D23A2220DDC0AC900D9AA03C61FCF4A559A4417767089708A776796EDB723506"

CCM encrypt and tag RFC 3610 #16
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"D7828D13B2B0BDC325A76236DF93CC6B":"12DAAC5630EFA5396F770CE1A66B21F7B2101C":"00764C63B8058E3C9696766CFA":"D0D0735C531E1BECF049C244":"14D253C3967B70609B7CBB7C499160283245269A6F49975BCADEAF"

CCM encrypt and tag RFC 3610 #17
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"D7828D13B2B0BDC325A76236DF93CC6B":"E88B6A46C78D63E52EB8C546EFB5DE6F75E9CC0D":"00F8B678094E3B3C9696766CFA":"77B60F011C03E1525899BCAE":"5545FF1A085EE2EFBF52B2E04BEE1E2336C73E3F762C0C7744FE7E3C"

CCM encrypt and tag RFC 3610 #18
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"D7828D13B2B0BDC325A76236DF93CC6B":"6435ACBAFB11A82E2F071D7CA4A5EBD93A803BA87F":"00D560912D3F703C9696766CFA":"CD9044D2B71FDB8120EA60C0":"009769ECABDF48625594C59251E6035722675E04C847099E5AE0704551"

CCM encrypt and tag RFC 3610 #19
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"D7828D13B2B0BDC325A76236DF93CC6B":"8A19B950BCF71A018E5E6701C91787659809D67DBEDD18":"0042FFF8F1951C3C9696766CFA":"D85BC7E69F944FB8":"BC218DAA947427B6DB386A99AC1AEF23ADE0B52939CB6A637CF9BEC2408897C6BA"

CCM encrypt and tag RFC 3610 #20
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"D7828D13B2B0BDC325A76236DF93CC6B":"1761433C37C5A35FC1F39F406302EB907C6163BE38C98437":"00920F40E56CDC3C9696766CFA":"74A0EBC9069F5B37":"5810E6FD25874022E80361A478E3E9CF484AB04F447EFFF6F0A477CC2FC9BF548944"

CCM encrypt and tag RFC 3610 #21
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"D7828D13B2B0BDC325A76236DF93CC6B":"A434A8E58500C6E41530538862D686EA9E81301B5AE4226BFA":"0027CA0C7120BC3C9696766CFA":"44A3AA3AAE6475CA":"F2BEED7BC5098E83FEB5B31608F8E29C38819A89C8E776F1544D4151A4ED3A8B87B9CE"

CCM encrypt and tag RFC 3610 #22
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"D7828D13B2B0BDC325A76236DF93CC6B":"B96B49E21D621741632875DB7F6C9243D2D7C2":"005B8CCBCD9AF83C9696766CFA":"EC46BB63B02520C33C49FD70":"31D750A09DA3ED7FDDD49A2032AABF17EC8EBF7D22C8088C666BE5C197"

CCM encrypt and tag RFC 3610 #23
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"D7828D13B2B0BDC325A76236DF93CC6B":"E2FCFBB880442C731BF95167C8FFD7895E337076":"003EBE94044B9A3C9696766CFA":"47A65AC78B3D594227E85E71":"E882F1DBD38CE3EDA7C23F04DD65071EB41342ACDF7E00DCCEC7AE52987D"

CCM encrypt and tag RFC 3610 #24
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"D7828D13B2B0BDC325A76236DF93CC6B":"ABF21C0B02FEB88F856DF4A37381BCE3CC128517D4":"008D493B30AE8B3C9696766CFA":"6E37A6EF546D955D34AB6059":"F32905B88A641B04B9C9FFB58CC390900F3DA12AB16DCE9E82EFA16DA62059"

CCM encrypt and tag AES-128 (P=0, N=13, A=0, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"54caf96ef6d448734700aadab50faf7a":"":"a3803e752ae849c910d8da36af":"":"eba8347baa6d61f87b67c2dd7c6d2053"

CCM encrypt and tag NIST VTT AES-128 #1 (P=24, N=13, A=32, T=4)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"43b1a6bc8d0d22d6d1ca95c18593cca5":"a2b381c7d1545c408fe29817a21dc435a154c87256346b05":"9882578e750b9682c6ca7f8f86":"2084f3861c9ad0ccee7c63a7e05aece5db8b34bd8724cc06b4ca99a7f9c4914f":"cc69ed76985e0ed4c8365a72775e5a19bfccc71aeb116c85a8c74677"

CCM encrypt and tag NIST VTT AES-128 #2 (P=24, N=13, A=32, T=6)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"44e89189b815b4649c4e9b38c4275a5a":"8db6ae1eb959963931d1c5224f29ef50019d2b0db7f5f76f":"374c83e94384061ac01963f88d":"cd149d17dba7ec50000b8c5390d114697fafb61025301f4e3eaa9f4535718a08":"df952dce0f843374d33da94c969eff07b7bc2418ca9ee01e32bc2ffa8600"

CCM encrypt and tag NIST VTT AES-128 #3 (P=24, N=13, A=32, T=8)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"368f35a1f80eaaacd6bb136609389727":"1cccd55825316a94c5979e049310d1d717cdfb7624289dac":"842a8445847502ea77363a16b6":"34396dfcfa6f742aea7040976bd596497a7a6fa4fb85ee8e4ca394d02095b7bf":"1a58094f0e8c6035a5584bfa8d1009c5f78fd2ca487ff222f6d1d897d6051618"

CCM encrypt and tag NIST VTT AES-128 #4 (P=24, N=13, A=32, T=10)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"996a09a652fa6c82eae8be7886d7e75e":"84cdd7380f47524b86168ed95386faa402831f22045183d0":"a8b3eb68f205a46d8f632c3367":"c71620d0477c8137b77ec5c72ced4df3a1e987fd9af6b5b10853f0526d876cd5":"a7fbf9dd1b099ed3acf6bcbd0b6f7cae57bee99f9d084f826d86e69c07f053d1a607"

CCM encrypt and tag NIST VTT AES-128 #5 (P=24, N=13, A=32, T=12)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"3ee186594f110fb788a8bf8aa8be5d4a":"d71864877f2578db092daba2d6a1f9f4698a9c356c7830a1":"44f705d52acf27b7f17196aa9b":"2c16724296ff85e079627be3053ea95adf35722c21886baba343bd6c79b5cb57":"b4dd74e7a0cc51aea45dfb401a41d5822c96901a83247ea0d6965f5aa6e31302a9cc2b36"

CCM encrypt and tag NIST VTT AES-128 #6 (P=24, N=13, A=32, T=14)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"7b2d52a5186d912cf6b83ace7740ceda":"ea384b081f60bb450808e0c20dc2914ae14a320612c3e1e8":"f47be3a2b019d1beededf5b80c":"76cf3522aff97a44b4edd0eef3b81e3ab3cd1ccc93a767a133afd508315f05ed":"79070f33114a980dfd48215051e224dfd01471ac293242afddb36e37da1ee8a88a77d7f12cc6"

CCM encrypt and tag NIST VTT AES-128 #7 (P=24, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"4189351b5caea375a0299e81c621bf43":"4535d12b4377928a7c0a61c9f825a48671ea05910748c8ef":"48c0906930561e0ab0ef4cd972":"40a27c1d1e23ea3dbe8056b2774861a4a201cce49f19997d19206d8c8a343951":"26c56961c035a7e452cce61bc6ee220d77b3f94d18fd10b6d80e8bf80f4a46cab06d4313f0db9be9"

CCM encrypt and tag NIST VTT AES-192 #1 (P=24, N=13, A=32, T=4)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"11fd45743d946e6d37341fec49947e8c70482494a8f07fcc":"ee7e6075ba52846de5d6254959a18affc4faf59c8ef63489":"c6aeebcb146cfafaae66f78aab":"7dc8c52144a7cb65b3e5a846e8fd7eae37bf6996c299b56e49144ebf43a1770f":"137d9da59baf5cbfd46620c5f298fc766de10ac68e774edf1f2c5bad"

CCM encrypt and tag NIST VTT AES-192 #2 (P=24, N=13, A=32, T=6)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"146a163bbf10746e7c1201546ba46de769be23f9d7cc2c80":"473b6600559aefb67f7976f0a5cc744fb456efd86f615648":"f5827e51707d8d64bb522985bb":"599b12ebd3347a5ad098772c44c49eed954ec27c3ba6206d899ddaabca23a762":"26d2be30e171439d54a0fec291c6024d1de09d61b44f53258ba1360406f9"

CCM encrypt and tag NIST VTT AES-192 #3 (P=24, N=13, A=32, T=8)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"bdf277af2226f03ec1a0ba7a8532ade6aea9b3d519fe2d38":"0ff89eff92a530b66684cd75a39481e7e069a7d05e89b692":"cc3c596be884e7caed503315c0":"4d6546167b3ed55f01c62bd384e02e1039c0d67ef7abe33291fecb136272f73b":"6ef66a52c866bd5df20ec5096de92167ad83cab0e095ad0c778a299f1224f10c"

CCM encrypt and tag NIST VTT AES-192 #4 (P=24, N=13, A=32, T=10)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"62f8eba1c2c5f66215493a6fa6ae007aae5be92f7880336a":"f5522e3405d9b77cbf3257db2b9675e618e8744a0ee03f0f":"15769753f503aa324f4b0e8ee0":"1bc05440ee3e34d0f25e90ca1ecbb555d0fb92b311621d171be6f2b719923d23":"b9103942dbbb93e15086751c9bb0a3d33112b55f95b7d4f32ff0bb90a8879812683f"

CCM encrypt and tag NIST VTT AES-192 #5 (P=24, N=13, A=32, T=12)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"5a5667197f46b8027980d0a3166c0a419713d4df0629a860":"d0e4024d6e33daafc011fe463545ed20f172872f6f33cefa":"6236b01079d180fce156fbaab4":"29bdf65b29394d363d5243d4249bad087520f8d733a763daa1356be458d487e5":"479f3d408bfa00d1cd1c8bf11a167ce7ae4bcdb011f04e38733013b8ebe5e92b1917640c"

CCM encrypt and tag NIST VTT AES-192 #6 (P=24, N=13, A=32, T=14)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"d2d4482ea8e98c1cf309671895a16610152ce283434bca38":"78168e5cc3cddf4b90d5bc11613465030903e0196f1fe443":"6ee177d48f59bd37045ec03731":"9ef2d0d556d05cf9d1ee9dab9b322a389c75cd4e9dee2c0d08eea961efce8690":"e2324a6d5643dfc8aea8c08cbbc245494a3dcbcb800c797c3abcdb0563978785bf7fd71c6c1f"

CCM encrypt and tag NIST VTT AES-192 #7 (P=24, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"a7177fd129674c6c91c1c89f4408139afe187026b8114893":"2cea0f7304860a4f40a28c8b890db60f3891b9982478495e":"31bb28f0e1e63c36ca3959dd18":"2529a834668187213f5342a1f3deea0dc2765478c7d71c9c21b9eb1351a5f6cb":"5bb7aa6ab9c02a5712d62343fbe61f774e598d6b87545612380ea23dcffc9574f672bca92e306411"

CCM encrypt and tag NIST VTT AES-256 #1 (P=24, N=13, A=32, T=4)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"9074b1ae4ca3342fe5bf6f14bcf2f27904f0b15179d95a654f61e699692e6f71":"239029f150bccbd67edbb67f8ae456b4ea066a4beee065f9":"2e1e0132468500d4bd47862563":"3c5f5404370abdcb1edde99de60d0682c600b034e063b7d3237723da70ab7552":"9c8d5dd227fd9f81237601830afee4f0115636c8e5d5fd743cb9afed"

CCM encrypt and tag NIST VTT AES-256 #2 (P=24, N=13, A=32, T=6)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"8596a69890b0e47d43aeeca54b52029331da06fae63aa3249faaca94e2605feb":"f0b065da6ecb9ddcab855152d3b4155037adfa758ba96070":"20442e1c3f3c88919c39978b78":"4e0d3aa502bd03fe1761b167c4e0df1d228301d3ebaa4a0281becd813266e255":"d6a0f377f7c1b14dcdba729cae5271b027e71cc7850173ec265867a29eb3"

CCM encrypt and tag NIST VTT AES-256 #3 (P=24, N=13, A=32, T=8)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"bae73483de27b581a7c13f178a6d7bda168c1b4a1cb9180512a13e3ab914eb61":"28ef408d57930086011b167ac04b866e5b58fe6690a0b9c3":"daf54faef6e4fc7867624b76f2":"7022eaa52c9da821da72d2edd98f6b91dfe474999b75b34699aeb38465f70c1c":"356367c6cee4453658418d9517f7c6faddcd7c65aef460138cf050f48c505151"

CCM encrypt and tag NIST VTT AES-256 #4 (P=24, N=13, A=32, T=10)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"d5b321b0ac2fedce0933d57d12195c7b9941f4caa95529125ed21c41fac43374":"6aa6ea668df60b0db85592d0a819c9df9e1099916272aafb":"b35fb2262edfa14938a0fba03e":"ba762bbda601d711e2dfc9dbe3003d39df1043ca845612b8e9dc9ff5c5d06ec4":"97027de5effd82c58f8dbfb909d7696fbe2d54916262912001a4d765bc1c95c90a95"

CCM encrypt and tag NIST VTT AES-256 #5 (P=24, N=13, A=32, T=12)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"7f4af6765cad1d511db07e33aaafd57646ec279db629048aa6770af24849aa0d":"7ebef26bf4ecf6f0ebb2eb860edbf900f27b75b4a6340fdb":"dde2a362ce81b2b6913abc3095":"404f5df97ece7431987bc098cce994fc3c063b519ffa47b0365226a0015ef695":"353022db9c568bd7183a13c40b1ba30fcc768c54264aa2cd2927a053c9244d3217a7ad05"

CCM encrypt and tag NIST VTT AES-256 #6 (P=24, N=13, A=32, T=14)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"5c8b59d3e7986c277d5ad51e4a2233251076809ebf59463f47cd10b4aa951f8c":"138ee53b1914d3322c2dd0a4e02faab2236555131d5eea08":"21ff892b743d661189e205c7f3":"f1e0af185180d2eb63e50e37ba692647cac2c6a149d70c81dbd34685ed78feaa":"5b2f3026f30fdd50accc40ddd093b7997f23d7c6d3c8bc425f82c828413643b8794494cb5236"

CCM encrypt and tag NIST VTT AES-256 #7 (P=24, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"60823b64e0b2da3a7eb772bd5941c534e6ff94ea96b564e2b38f82c78bb54522":"a8be794613835c4366e75817d228438f011a2ec8a86f9797":"48526f1bffc97dd65e42906983":"fab62b3e5deda7a9c1128663cc81c44b74ab1bfe70bc1c9dec7c7fd08173b80a":"cc3efe04d84a4ec5cb6a6c28dc2c2d386a359d9550dbdec963ddd56464aed6d0613159d1aa181dcb"

CCM encrypt and tag NIST VPT AES-128 #1 (P=0, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"2ebf60f0969013a54a3dedb19d20f6c8":"":"1de8c5e21f9db33123ff870add":"e1de6c6119d7db471136285d10b47a450221b16978569190ef6a22b055295603":"0ead29ef205fbb86d11abe5ed704b880"

CCM encrypt and tag NIST VPT AES-128 #2 (P=1, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"6ae7a8e907b8720f4b0d5507c1d0dc41":"0e":"7f18ad442e536a0159e7aa8c0f":"9c9b0f11e020c6512a63dfa1a5ec8df8bd8e2ad83cf87b80b38635621c5dc0d7":"4c201784bdab19e255787fecd02000c49d"

CCM encrypt and tag NIST VPT AES-128 #3 (P=2, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"3d746ae6cac5cefd01f021c0bbf4bc3c":"4360":"597b3614ff9cd567afd1aad4e5":"90446190e1ff5e48e8a09d692b217de3ad0ab4a670e7f1b437f9c07a902cad60":"e38fdb77c1f8bbac2903a2ec7bc0f9c5654d"

CCM encrypt and tag NIST VPT AES-128 #4 (P=3, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"3e4fa1c6f8b00f1296956735ee86e310":"3a6734":"c6a170936568651020edfe15df":"00d57896da2435a4271afb9c98f61a650e63a4955357c47d073c5165dd4ea318":"384be657bfc5f385b179be7333eb3f57df546b"

CCM encrypt and tag NIST VPT AES-128 #5 (P=4, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"7ccbb8557f6e08f436d0957d4bbe7fdf":"4cabeb02":"bb8e2ef2ed9484f9021cda7073":"fba1d18a74a3bb38671ab2842ffaa434cd572a0b45320e4145930b3008d8d350":"32501f4235c4dd96e83d5ab4c3c31c523453c317"

CCM encrypt and tag NIST VPT AES-128 #6 (P=5, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"3725c7905bfaca415908c617b78f8dee":"f5499a7082":"c98ec4473e051a4d4ac56fd082":"11bc87f1c2d2076ba47c5cb530dd6c2a224f7a0f7f554e23d7d29077c7787680":"e378b776242066751af249d521c6eaebdff40b2642"

CCM encrypt and tag NIST VPT AES-128 #7 (P=6, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"80bead98a05d1bb173cd4fca463b8fa3":"e479990bf082":"8a14a6d255aa4032ebff37a3d7":"bb4e706e73d21df66f64173859d47e247527cd9832e20dccff8548ed5f554108":"89c9246238878427f36b1f6c633e4542f32b50ca8edb"

CCM encrypt and tag NIST VPT AES-128 #8 (P=7, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"dc8ec91184ba18eae31ac2d3b252673f":"2a5775986551c8":"0da4c988f521f5648259f2bec2":"6d5573c9279897d7d1602d8a95c04bb5ca3fad2dbe89a024b3651eb227e73bb5":"4f259f2a718faea852a7c4358dfa9f5467357638acac90"

CCM encrypt and tag NIST VPT AES-128 #9 (P=8, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"19f97ef5318b8005fc7133fa31dd1236":"6d972a673fbe1ca1":"01ce9814c6329dbee1d02b1321":"85853f120981f33cf1d50fde6b8bc865fe988a9f12579acdb336f9f992b08b89":"2f12a7e7acecae5d2563309efc19368cdee8266538ca89d3"

CCM encrypt and tag NIST VPT AES-128 #10 (P=9, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"c17944bfaeeb808eed66ae7242ab545f":"7caae2640e734539d3":"910b3db64df3728ca98219e01b":"edf64f98b3ab593cbcf68ab37a8c9472e49cb849d4a744deae925a5a43faf262":"0dae8b3ccf0b439f6ff8ee4a233dfb7753f6bfe321b3e26959"

CCM encrypt and tag NIST VPT AES-128 #11 (P=10, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"0fb9df6f638847f5de371f003dd938f4":"e10cc36bc1c5d3c646ab":"c9ddf61c052f3502ad6b229819":"4f9938d5bc3dcbe47f6b256d5e99723d0891e50c6175aba41b011e4686113c49":"7f797367de50be6dc04e4cf0d8c24189affd35060cb7ca3dd136"

CCM encrypt and tag NIST VPT AES-128 #12 (P=11, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"006ff7d3153caf906ec7929f5aef9276":"31be1b241cae79c54c2446":"57db1541a185bd9cdc34d62025":"7d9681cac38e778fba11f4464f69ed9ebfea31b7ffcaf2925b3381c65d975974":"9dd8a4244fbdb30b624578a625c43233476bbb959acd9edebe2883"

CCM encrypt and tag NIST VPT AES-128 #13 (P=12, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"026331e98aba9e8c23a9e8a91d0b0c97":"a82200ef3a08c390dec5cbf9":"bccfe69bba168b81cbdf7d018a":"26e011143a686a7224ddb8c5b1e5d31713fa22c386785e2c34f498ae56d07ed5":"adf4fc6f9be113066c09248fcb56a9c1a1c3bb16fbb9fbaedacdb12b"

CCM encrypt and tag NIST VPT AES-128 #14 (P=13, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"d32088d50df9aba14d9022c870a0cb85":"4b10788c1a03bca656f04f1f98":"e16c69861efc206e85aab1255e":"0eff7d7bcceb873c3203a8df74f4e91b04bd607ec11202f96cfeb99f5bcdb7aa":"89f15b1cb665a8851da03b874ca6f73242f2f227350c0277e4e72cdaa6"

CCM encrypt and tag NIST VPT AES-128 #15 (P=14, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"7301c907b9d2aaac355c5416ff25c59b":"484300aa3a506afcd313b49ead8d":"7304b65b6dab466273862c88b9":"2c5d114eff62c527cc2e03c33c595a80fe609bfc0fe13ce3380efe05d85cceac":"928ca58b0d373dc50c52afac787ce8eeb5d5b493661259a9d91ea31a5f7e"

CCM encrypt and tag NIST VPT AES-128 #16 (P=15, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"38be46d271bf868c198052391f8a2147":"61bd1385be92097e866550a55278f0":"6758f67db9bfea5f0e0972e08b":"c6de3be97f11d0e2ab85c9353b783f25b37366a78a2012cecf5b7a87138b3c86":"7c9fa8d99b38f825315ece6a2613f55e902f296dcce870263ae50cda4fadae"

CCM encrypt and tag NIST VPT AES-128 #17 (P=16, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"70010ed90e6186ecad41f0d3c7c42ff8":"be322f58efa7f8c68a635e0b9cce77f2":"a5f4f4986e98472965f5abcc4b":"3fec0e5cc24d67139437cbc8112414fc8daccd1a94b49a4c76e2d39303547317":"8e4425ae573974f0f0693a188b525812eef08e3fb15f4227e0d989a4d587a8cf"

CCM encrypt and tag NIST VPT AES-128 #18 (P=17, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"79eae5baddc5887bdf3031fd1d65085b":"001343e6191f5f1738e7d19d4eec2b9592":"9da59614535d1fad35f2ece00f":"46603500af9e4e7a2f9545411a58b21a6efd21f2b5f315d02d964c09270145b3":"2162e27bfbf1d00f2404754a254665fd9270f0edb415993588b2535e2e0e4fd086"

CCM encrypt and tag NIST VPT AES-128 #19 (P=18, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"c14eda0f958465246fe6ab541e5dfd75":"617868ae91f705c6b583b5fd7e1e4086a1bb":"32b63ca7e269223f80a56baaaa":"733f8e7670de3446016916510dfe722ce671570121d91331a64feb3d03f210e6":"b2dc1e548b3d3f225a34082f4391980a0788b4cc36852fd64a423fb8e872252b248e"

CCM encrypt and tag NIST VPT AES-128 #20 (P=19, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"c5e7147f56ba4530b8799ababeb82772":"2f3bf0b566440912a1e47a0c07f1cfd39cb440":"bdd38e173fb20b981659c597d6":"3a069a2bfda44abbb0a82a97e5e9047258c803da2c66190d77149e0f010b3af9":"bd6265dcba9e14c59e515e395dc60bd053345fa6d7568c738e3a7fdf142d8f2d1562c0"

CCM encrypt and tag NIST VPT AES-128 #21 (P=20, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"78c46e3249ca28e1ef0531d80fd37c12":"4802422c9b3b4459ba26e7863ad87b0c172cfe4b":"5de41a86ce3f3fb1b685b3ca4d":"e98a77f2a941b36232589486b05f4278275588665a06d98aec98915cc5607e06":"daea2234ea433533bf0716abe1aa3844b6d3c51e9d5ca3d8ec5065630d2de0717cdeb7d5"

CCM encrypt and tag NIST VPT AES-128 #22 (P=21, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"8883002bf13b3a94b2467225970df938":"d516bbff452e7706c91c7ace3e9baa76d65ff7050f":"818a702d5c8ee973b34e9acda1":"545aeac737c0ca2a3d5e1fd966840c3a0d71e0301abbe99c7af18d24cc7e9633":"b85242fdc06344f2bd9a97b408902ebcd22aece3d42f2da4dd4d817c9fa2d44bc02163a0a9"

CCM encrypt and tag NIST VPT AES-128 #23 (P=22, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"5cea00ee44cfb9cfbb598d3812e380ef":"33bfd0713f30fcac8f7f95920ac6d9b803ddd5480dd8":"948788a9c8188cb988430a7ebd":"50422c5e6a0fb8231b3bb6e2f89607019be6ad92a4dae8e0fe3f9e486476004b":"b168747dea3ae0fbede4402af9a3dc3185d6d162f859d828101682de32923788c70262b84814"

CCM encrypt and tag NIST VPT AES-128 #24 (P=23, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"cb83f77751e72711401cbbf4f61aa0ed":"eede01b08f9a303cdf14c99d7a45732972c6eff2a1db06":"c0b461b2e15b8b116ef9281704":"2bd112231f903fa0dff085db48a2e2a96ec0199249b005d5ab4c2eab753f9ad0":"feb114b7bd3b43497b62454a675a632c3546d2802462c6af57647efda119c59862cd5dd3904efc"

CCM encrypt and tag NIST VPT AES-128 #25 (P=24, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"43c1142877d9f450e12d7b6db47a85ba":"b506a6ba900c1147c806775324b36eb376aa01d4c3eef6f5":"76becd9d27ca8a026215f32712":"6a59aacadd416e465264c15e1a1e9bfa084687492710f9bda832e2571e468224":"14b14fe5b317411392861638ec383ae40ba95fefe34255dc2ec067887114bc370281de6f00836ce4"

CCM encrypt and tag NIST VPT AES-192 #1 (P=0, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"086e2967cde99e90faaea8a94e168bf0e066c503a849a9f3":"":"929542cd690f1babcf1696cb03":"58f70bab24e0a6137e5cd3eb18656f2b5ccddc3f538a0000c65190e4a3668e71":"3bf9d93af6ffac9ac84cd3202d4e0cc8"

CCM encrypt and tag NIST VPT AES-192 #2 (P=1, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"992d38768b11a236945bd4b327c3728fac24c091238b6553":"1c":"b248a90b84b0122a5ad8e12760":"27cabc40da0e1eda0ea5f8abbb7c179e30776250a7b30d711b0e106c5ee9d84a":"1a96f58c3f38c44d1a345f3e2da6679f20"

CCM encrypt and tag NIST VPT AES-192 #3 (P=2, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"5012db40ff6ae23c1e1ce43768c5936c4400b0e79ae77f30":"0c6c":"b67e500b35d60ad7264240027c":"40affd355416200191ba64edec8d7d27ead235a7b2e01a12662273deb36379b8":"c996ef3d6ef9f981557506ecc8797bbaaaa7"

CCM encrypt and tag NIST VPT AES-192 #4 (P=3, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"fa15cc7f0de294d7341b1fd79326c8be78e67822343c1992":"bcb898":"e5257aed2bda0495aa44591db4":"31a0338c3839931fa1dd5131cb796c4c6cfde9fb336d8a80ac35dec463be7a94":"68f08298d9a2147776dca9c1a42382bce323b2"

CCM encrypt and tag NIST VPT AES-192 #5 (P=4, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"b5330a8447d74a7987fb718cfae246b5c7e057991064eeaf":"b46b343e":"2ef29d62b40d8643848797cde8":"1225b036e6044df52314016760e92750de0936120395de750a2c54a7fa0cea82":"c2c39d6f9344e2de064f269d065a2a6108605916"

CCM encrypt and tag NIST VPT AES-192 #6 (P=5, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"30419145ae966591b408c29e5fd14d9112542909be5363f7":"8ceaeb89fd":"27e6b2a482bbc6f13702005708":"e04e81e860daf9696098c723085d8023c240ebe7a643131e35359ab04bd650fe":"ec9d5ed36243ddf77b33d8cf2963ba76fd4e19f3c5"

CCM encrypt and tag NIST VPT AES-192 #7 (P=6, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"748ad503388a34041a7bdae6361d57894357c333bacf02ca":"24d6880aed7e":"518b79d194579b19f2d8845b70":"691dd98f61fd213b0840ec5a6f06ef9a1420be0d59bde5e43546347a2a865a94":"270120f9634ec15536e21d961c675070ec4cff9037bc"

CCM encrypt and tag NIST VPT AES-192 #8 (P=7, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"b930cca30a3fd230c237c8f3cc6792d0c4084dff5c18d775":"2a755e362373ef":"7574802fd82fe96c05431acd40":"1cf83928b6a9e525fe578c5c0f40c322be71b3092239bff954dd6883738d6d71":"f06238b0450fd1f4b6cab1383adb420c4724aa7bdfefb7"

CCM encrypt and tag NIST VPT AES-192 #9 (P=8, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"314c136999e41d137bd7ba17201a9fa406025868334e39b3":"4d54d8b06b204445":"65f7a0f4c0f5bba9d26f7e0ddb":"5c7ce4819b30b975ae6ce58dcc1bfa29a8b6dda8f4b76c7e23516487745e829c":"2baf90c490b11f9607482362ab3f157c42d0e9c6c5cffcf0"

CCM encrypt and tag NIST VPT AES-192 #10 (P=9, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"a19f6be062ec0aaf33046bd52734f3336c85d8368bef86ab":"13511ae5ff6c6860a1":"7f2d07f8169c5672b4df7f6cac":"d68d5f763db6111c5d6324d694cb0236beab877daae8115ecb75d60530777b58":"b3859b757802ebd048467fd8e139eb9ee8fcdca45ed87dc1c8"

CCM encrypt and tag NIST VPT AES-192 #11 (P=10, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"de1c8263345081d2dfa9afdf37675971135e178df554a4d8":"f777aba1fa70f94e6de9":"a301bb82f91a582db01355c388":"9ad52c041390d0d4aaf65a4667c3239c95e7eae6178acc23fb4e70a852d483c6":"9d8bff6d2dcde77104ac6aba025abc01416a7ca9f096ab2529cb"

CCM encrypt and tag NIST VPT AES-192 #12 (P=11, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"248d36bd15f58e47fcf1c948272355821f8492e6e69f3661":"33709d9c7906e2f82dd9e2":"9e8d492c304cf6ad59102bca0e":"9ec08c7ed6b70823d819e9ab019e9929249f966fdb2069311a0ddc680ac468f5":"9114d36b79b1918b2720f40cddce66df9b4802f737bea4bd8f5378"

CCM encrypt and tag NIST VPT AES-192 #13 (P=12, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"77a67fb504b961028633321111aac2c30eb6d71a8cf72056":"10554c062d269ff6dcd98493":"acadc0330194906f8c75ac287f":"8c18486d52571f70f2ba6a747aaa3d4b3ebc2e481ee1b70907dddb94bdfa0ca6":"7f8b0cad79b545e5addf0b04ff4b0f2b2a5067283210aba8630d0306"

CCM encrypt and tag NIST VPT AES-192 #14 (P=13, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"0d423519e4110c06063061323f8c7c95387776b6ee4e4b6e":"4021ff104ff1dbd91e46db249f":"39abe53826d9b8e300fe747533":"cdd9bf1b4f865e922c678ec4947ea0cb02e78bd5c1538f33aeb818ad3f47e519":"7953d3cd66d093785d123f65ba37f16761dd6aedbfc789ad96edf1490d"

CCM encrypt and tag NIST VPT AES-192 #15 (P=14, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"a60cf7ceb62bf3118532bc61daa25ce946991047f951b536":"d64f9426febce6a84c954dd5ded5":"7499494faa44a7576f9ed5580d":"baa482c64eefd09118549a8968f44cfea7a436913a428e30aa4ab44802a4ba35":"f7580f17266d68237747bf57c7ed8242ac1a1979c5a9e7bc67d7698c7efa"

CCM encrypt and tag NIST VPT AES-192 #16 (P=15, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"82d4bc9aac298b09112073277205e1bf42176d1e6339b76c":"25a53fd3e476dc0860eeeea25fcb0c":"70325ef19e581b743095cd5eb1":"6d14bb2635c5d0ae83687f1824279cf141173527e1b32d1baf8a27f7fe34a542":"4a1cfd0023557a184b929965b0a445cb3993ca35acf354cb2b4254ff672e7f"

CCM encrypt and tag NIST VPT AES-192 #17 (P=16, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"6873f1c6c30975aff6f08470264321130a6e5984ade324e9":"5051a0b0b6766cd6ea29a672769d40fe":"7c4d2f7cec04361f187f0726d5":"77743b5d83a00d2c8d5f7e10781531b496e09f3bc9295d7ae9799e64668ef8c5":"0ce5ac8d6b256fb7580bf6acc76426af40bce58fd4cd6548df90a0337c842004"

CCM encrypt and tag NIST VPT AES-192 #18 (P=17, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"3cf8da27d5be1af024158985f725fd7a6242cbe0041f2c17":"f6dd2c64bf597e63263ccae1c54e0805fe":"07f77f114d7264a122a7e9db4f":"30457e99616f0247f1339b101974ea231904d0ef7bd0d5ee9b57c6c16761a282":"ce3031c3a70600e9340b2ddfe56aa72cffdc5e53e68c51ee55b276eb3f85d2cf63"

CCM encrypt and tag NIST VPT AES-192 #19 (P=18, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"b46a3a24c66eb846ca6413c001153dc6998970c12e7acd5a":"56d18d3e2e496440d0a5c9e1bcb464faf5bc":"b79c33c96a0a90030694163e2a":"ea9405d6a46cac9783a7b48ac2e25cc9a3a519c4658b2a8770a37240d41587fb":"01baba2e0d5b49d600d03a7ed84ee878926c0ca478f40a6fbde01f584d938a1c91bf"

CCM encrypt and tag NIST VPT AES-192 #20 (P=19, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"7b71045ccef735bd0c5bea3cf3b7e16e58d9c62061a204e0":"890d05420d57e3b3d8dbef117fe60c3fa6a095":"2b9ecfd179242c295fe6c6fa55":"b89166f97deb9cc7fdeb63639eeafb145895b307749ec1a293b27115f3aa8232":"f842ff6662684de8785af275fa2d82d587de0687ebe35e883cbd53b82f2a4624c03894"

CCM encrypt and tag NIST VPT AES-192 #21 (P=20, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"dc7c67715f2709e150cceff020aaacf88a1e7568191acbcf":"f383bd3e6270876b74abbb5d35e7d4f11d83412c":"da56ea046990c70fa216e5e6c4":"f799818d91be7bab555a2e39f1f45810a94d07179f94fe1151d95ab963c47611":"377b5df263c5c74f63603692cbb61ea37b6d686c743f71e15490ca41d245768988719ede"

CCM encrypt and tag NIST VPT AES-192 #22 (P=21, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"f41e369a1599627e76983e9a4fc2e963dab4960b09ebe390":"81ad3f386bedcbf656ff535c63580d1f87e3c72326":"68ef8285b90f28bcd3cb1bacea":"dbe3e82e49624d968f5463ceb8af189fb3ad8b3b4122142b110d848a286dae71":"9f6028153e06d14d30b862a99a35413413c04a49dc6f68a03a11cf00d58f062a7b36465d13"

CCM encrypt and tag NIST VPT AES-192 #23 (P=22, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"3289e59e3a7b29bf4a309afc253030bba4b9bdd64f0722f9":"53911a67b65738f87fc7c20d6db8044bde1af95838d1":"30259ce106e9bd7a8bacbaf212":"2870bd9a26c510e9a256920899bbc77a4eb9b53f927045a943d5ed6b13638cf3":"70cf37d4b6f7e707376b1574ce17c040b5143da47abb2fe9afafc2fccd98ccf63b0fdec30eac"

CCM encrypt and tag NIST VPT AES-192 #24 (P=23, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"40f1aff2e44d05f12126097a0f07ac0359ba1a609356a4e6":"8d98c580fb366f330dbfda20f91d99a0878b47efd14c6d":"0df3fc6396f851785fca9aa5ff":"e9699b20b0574fce8b5cbc4ef792eb96e2c1cce36b1b1f06ea2a95fe300633cc":"579cdf9da62a2df471e03450516adb4ce99ae0f70b1776a39c3b429a1f922fac0b59e29a122e43"

CCM encrypt and tag NIST VPT AES-192 #25 (P=24, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"91f9d636a071c3aad1743137e0644a73de9e47bd76acd919":"4eaf9384cad976f65f98042d561d760b5a787330dc658f6c":"1bf491ac320d660eb2dd45c6c3":"3bdfd7f18d2b6d0804d779f0679aaa2d7d32978c2df8015ae4b758d337be81dd":"635530cab14e3d0a135bb6eebb5829412676e6dd4995f99cb7e17f235bd660e7e17b2c65320e9fd4"

CCM encrypt and tag NIST VPT AES-256 #1 (P=0, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"c6c14c655e52c8a4c7e8d54e974d698e1f21ee3ba717a0adfa6136d02668c476":"":"291e91b19de518cd7806de44f6":"b4f8326944a45d95f91887c2a6ac36b60eea5edef84c1c358146a666b6878335":"ca482c674b599046cc7d7ee0d00eec1e"

CCM encrypt and tag NIST VPT AES-256 #2 (P=1, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"cc49d4a397887cb57bc92c8a8c26a7aac205c653ef4011c1f48390ad35f5df14":"1a":"6df8c5c28d1728975a0b766cd7":"080f82469505118842e5fa70df5323de175a37609904ee5e76288f94ca84b3c5":"a5f24e87a11a95374d4c190945bf08ef2f"

CCM encrypt and tag NIST VPT AES-256 #3 (P=2, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"36b0175379e7ae19c277fe656a2252a82796309be0f0d4e1c07fdde88aca4510":"be80":"021bd8b551947be4c18cf1a455":"b5c6e8313b9c68e6bb84bffd65fa4108d243f580eab99bb80563ed1050c8266b":"ecacc3152e43d9efea26e16c1d1793e2a8c4"

CCM encrypt and tag NIST VPT AES-256 #4 (P=3, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"ddb739acda6c56ec9aefc4f4cbc258587f443da4e76ddfa85dbe0813a8784944":"db457c":"0bddf342121b82f906368b0d7b":"887486fff7922768186363ef17eb78e5cf2fab8f47a4eb327de8b16d63b02acb":"54473c3f65d6be431e79700378049ac06f2599"

CCM encrypt and tag NIST VPT AES-256 #5 (P=4, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"62b82637e567ad27c3066d533ed76e314522ac5c53851a8c958ce6c64b82ffd0":"87294078":"5bc2896d8b81999546f88232ab":"fffb40b0d18cb23018aac109bf62d849adca42629d8a9ad1299b83fe274f9a63":"2bc22735ab21dfdcfe95bd83592fb6b4168d9a23"

CCM encrypt and tag NIST VPT AES-256 #6 (P=5, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"bc29a16e19cfbe32bf4948e8e4484159bc819b7eec504e4441a1a98ca210e576":"3e8c6d1b12":"4f18bcc8ee0bbb80de30a9e086":"574931ae4b24bdf7e9217eca6ce2a07287999e529f6e106e3721c42dacf00f5d":"45f3795fcf9c66e1a43103d9a18f5fba5fab83f994"

CCM encrypt and tag NIST VPT AES-256 #7 (P=6, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"5f4b4f97b6aa48adb3336c451aac377fde4adf47897fd9ccdf139f33be76b18c":"1b62ad19dcac":"7a76eac44486afdb112fc4aab9":"a66c980f6621e03ff93b55d5a148615c4ad36d6cbdd0b22b173b4b1479fb8ff7":"4ad1fcf57c12b14e0e659a6305b4aeffae82f8a66c94"

CCM encrypt and tag NIST VPT AES-256 #8 (P=7, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"f7aaeff3a1dc0cc5ecf220c67ad9f6dda060b4f1be3cc609cb4f18b2342a88a2":"d48daa2919348d":"d0d6871b9adc8623ac63faf00f":"e97175c23c5b47da8ce67811c6d60a7499b3b7e1347ad860519285b67201fe38":"eb32ab153a8e092fa325bafc176a07c31e6cc0a852d288"

CCM encrypt and tag NIST VPT AES-256 #9 (P=8, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"493e14623cd250058a7fc66a3fee0c24b6e363b966c2314aff53b276b6c2ea7b":"e5653e512d8b0b70":"fe2d8ae8da94a6df563f89ce00":"579a637e37a0974cd2fc3b735d9ed088e8e488ffe210f043e0f9d2079a015ad6":"75d31f8d47bee5c4e2ba537355ae8ab25cc9ed3511ff5053"

CCM encrypt and tag NIST VPT AES-256 #10 (P=9, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"b23255372455c69244a0210e6a9e13b155a5ec9d6d0900e54a8f4d9f7a255e3a":"615d724ae94a5daf8d":"274846196d78f0af2df5860231":"69adcae8a1e9a3f2fe9e62591f7b4c5b19d3b50e769521f67e7ea8d7b58d9fc8":"f019ae51063239287d896e7127f17d13f98013b420219eb877"

CCM encrypt and tag NIST VPT AES-256 #11 (P=10, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"dbf06366f766e2811ecd5d4384d6d08336adc37e0824d620cf0d9e7fd1e7afa9":"2e3cf0af8c96c7b22719":"b3503ed4e277ed9769b20c10c0":"9ae5a04baa9d02c8854e609899c6240851cbc83f81f752bc04c71affa4eed385":"e317df43ab46eb31be7e76f2730d771d56099a0c8d2703d7a24e"

CCM encrypt and tag NIST VPT AES-256 #12 (P=11, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"4dd555bd3a5253a90b68b5d4d46bd050340ee07ddad3a72048c657b5d76bb207":"8015c0f07a7acd4b1cbdd2":"bdb1b82ba864893c2ee8f7426c":"9bcc5848e928ba0068f7a867e79e83a6f93593354a8bfcfc306aeeb9821c1da1":"8e9f80c726980b3d42e43a6512a0481255b729a10f9edb5f07c60c"

CCM encrypt and tag NIST VPT AES-256 #13 (P=12, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"d3ad8cda9a0d91a205c4c05665728bb255d50a83403c9ab9243fcbbe95ae7906":"a203aeb635e195bc33fd42fa":"0b5f69697eb1af24e8e6fcb605":"ea26ea68facdac3c75ba0cdf7b1ad703c9474af83b3fbfc58e548d776b2529b9":"62666297a809c982b50722bd56bc555899345e0404b2938edf33168e"

CCM encrypt and tag NIST VPT AES-256 #14 (P=13, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"e300fc7a5b96806382c35af5b2c2e8e26382751b59010d4b1cfc90a4a9cb06df":"8714eb9ecf8bdb13e919de40f9":"55b59eb434dd1ba3723ee0dc72":"9b1d85384cb6f47c0b13514a303d4e1d95af4c6442691f314a401135f07829ec":"ba6063824d314aa3cbab14b8c54c6520dac0f073856d9b9010b7857736"

CCM encrypt and tag NIST VPT AES-256 #15 (P=14, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"3ae5be5904bae62609ac525e2d1cad90133447573d7b608975a6a2b16cb2efc0":"959403e0771c21a416bd03f38983":"61bf06b9fa5a450d094f3ddcb5":"0245484bcd987787fe97fda6c8ffb6e7058d7b8f7064f27514afaac4048767fd":"37a346bc4909965c5497838251826385a52c68914e9d1f63fd297ee6e7ed"

CCM encrypt and tag NIST VPT AES-256 #16 (P=15, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"fab62b3e5deda7a9c1128663cc81c44b74ab1bfe70bc1c9dec7c7fd08173b80a":"54be71705e453177b53c92bbf2ab13":"a5c1b146c82c34b2e6ebeceb58":"5e60b02b26e2d5f752eb55ea5f50bb354a6f01b800cea5c815ff0030b8c7d475":"788db949697b8cd9abbc74ed9aa40cd6852dc829469368491149d6bb140071"

CCM encrypt and tag NIST VPT AES-256 #17 (P=16, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"ee8ce187169779d13e443d6428e38b38b55dfb90f0228a8a4e62f8f535806e62":"d15f98f2c6d670f55c78a06648332bc9":"121642c4218b391c98e6269c8a":"718d13e47522ac4cdf3f828063980b6d452fcdcd6e1a1904bf87f548a5fd5a05":"cc17bf8794c843457d899391898ed22a6f9d28fcb64234e1cd793c4144f1da50"

CCM encrypt and tag NIST VPT AES-256 #18 (P=17, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"7da6ef35ad594a09cb74daf27e50a6b30d6b4160cf0de41ee32bbf2a208b911d":"b0053d1f490809794250d856062d0aaa92":"98a32d7fe606583e2906420297":"217d130408a738e6a833931e69f8696960c817407301560bbe5fbd92361488b4":"a6341ee3d60eb34a8a8bc2806d50dd57a3f628ee49a8c2005c7d07d354bf80994d"

CCM encrypt and tag NIST VPT AES-256 #19 (P=18, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"0786706f680c27b792d054faa63f499a8e6b5ddb90502946235bf74c022d772c":"6a26677836d65bd0d35a027d278b2534e7df":"f61ef1c8c10a863efeb4a1de86":"67874c808600a27fcab34d6f69cc5c730831ad4589075dd82479823cb9b41dc3":"d1c1f3c60603359c7d6a707f05ecb2296f8e52f2210b7a798ad5c778ee7cfd7fe6e0"

CCM encrypt and tag NIST VPT AES-256 #20 (P=19, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"bac55f9847d93325bf5071c220c0a3dfeb38f214292d47b4acb7b0a597fe056f":"c1a994dc198f5676ea85801cd27cc8f47267ec":"05b50c458adbba16c55fcc454d":"89ad6ae1e550975eaa916a62615e6b6a66366a17a7e06380a95ea5cdcc1d3302":"7c9b138177590edaafec4728c4663e77458ffbe3243faec177de4a2e4a293952073e43"

CCM encrypt and tag NIST VPT AES-256 #21 (P=20, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"8beedeb85d42c2a7fa6f7237b05acb197dd8e1672471ac878064fe5319eab876":"7b125c3b9612a8b554913d0384f4795c90cd387c":"8479bdfad28ebe781e9c01a3f6":"7aebdfd955d6e8a19a701d387447a4bdd59a9382156ab0c0dcd37b89419d6eff":"6cc611d816b18c6847b348e46a4119465104254a04e2dfeeeac9c3255f6227704848d5b2"

CCM encrypt and tag NIST VPT AES-256 #22 (P=21, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"c3a0c126cad581012151c25cf85a44472c23f83b6095b6004f4f32cd60ec2db2":"73b09d18554471309141aa33b687f9248b50fe3154":"94ab51ce75db8b046d6ab92830":"2a243246bfe5b5ab05f51bf5f401af52d5bbaa2549cf57a18e197597fe15dd8c":"b7e8264ca70fd2a4fb76f20a8ad5da3c37f5893fb12abeeaef1187f815ca481ed8ddd3dd37"

CCM encrypt and tag NIST VPT AES-256 #23 (P=22, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"9cdebaeee8690b68751070691f49593668a6de12d3a948b38ddbd3f75218b2d4":"3cbb08f133270e4454bcaaa0f20f6d63c38b6572e766":"af1a97d43151f5ea9c48ad36a3":"f5353fb6bfc8f09d556158132d6cbb97d9045eacdc71f782bcef62d258b1950a":"3966930a2ae8fdd8f40e7007f3fde0bd6eb48a46e6d26eef83da9f6384b1a2bda10790dadb3f"

CCM encrypt and tag NIST VPT AES-256 #24 (P=23, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"d34264a12c35cdd67ac105e2826b071e46f8131d1e325f8e0ae80a6447375135":"79ac1a6a9eca5e07ce635bfd666ef72b16f3f2e140d56c":"3891e308b9f44c5b5a8b59004a":"0cda000ed754456a844c9ed61843deea9dadf5e723ea1448057712996d660f8c":"1abcc9b1649deaa0bfa7dcd23508282d9c50ca7fee72486950608d7bcb39dcf03a2cab01587f61"

CCM encrypt and tag NIST VPT AES-256 #25 (P=24, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"4ad98dbef0fb2a188b6c49a859c920967214b998435a00b93d931b5acecaf976":"9cea3b061e5c402d48497ea4948d75b8af7746d4e570c848":"00d772b07788536b688ff2b84a":"5f8b1400920891e8057639618183c9c847821c1aae79f2a90d75f114db21e975":"f28ec535c2d834963c85814ec4173c0b8983dff8dc4a2d4e0f73bfb28ad42aa8f75f549a93594dd4"

CCM encrypt and tag NIST VNT AES-128 #1 (P=24, N=7, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"c0425ed20cd28fda67a2bcc0ab342a49":"4f065a23eeca6b18d118e1de4d7e5ca1a7c0e556d786d407":"37667f334dce90":"0b3e8d9785c74c8f41ea257d4d87495ffbbb335542b12e0d62bb177ec7a164d9":"768fccdf4898bca099e33c3d40565497dec22dd6e33dcf4384d71be8565c21a455db45816da8158c"

CCM encrypt and tag NIST VNT AES-128 #2 (P=24, N=8, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"0b6256bd328a4cda2510d527c0f73ed4":"78a292662b8e05abc2d44fbefd0840795e7493028015d9f2":"21fd9011d6d9484a":"66ff35c4f86ad7755b149e14e299034763023e7384f4af8c35277d2c7e1a7de2":"5a0be834c57b59d47a4590d8d19a1206d3c06e937a9b57f74034d9fdb43c3f48932aa72177b23bf6"

CCM encrypt and tag NIST VNT AES-128 #3 (P=24, N=9, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"afdccc84f257cb768b7ad735edbd1990":"56d0942490e546798f30d3c60ad4e3e110fc04f5b1c1fa83":"b7776aa998f4d1189b":"9f9ac464de508b98e789243fdb32db458538f8a291ed93ddf8aeaacfbfc371aa":"96f124c74fd737819008ddef440320f4a3733d0062c83c893e259aecf12ba08f2a2e966a3341d6d4"

CCM encrypt and tag NIST VNT AES-128 #4 (P=24, N=10, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"6ccb68d3838d4ddf660b9cd904cad40f":"5ea35c082e2b190e9d98e6b2daad8672f587b4f2968072fc":"c4fb7519a19f13d9d1fc":"092e64fef08b5655a86cdb8de63ffaa7772e8730844e9016141af8bad2216246":"cda5fe3d15d00150b99120c7f206b88a4c2c4a39ca9143425603ab284a73a38cc916f8b653c92ab4"

CCM encrypt and tag NIST VNT AES-128 #5 (P=24, N=11, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"e6ab9e70a4fb51b01c2e262233e64c0d":"ba15916733550d7aa82b2f6b117cd3f54c83ddc16cd0288a":"74e689eb5af9441dd690a6":"42f6518ee0fbe42f28e13b4bb2eb60517b37c9744394d9143393a879c3e107c7":"dcc151443288f35d39ed8fae6f0ce1d1eb656f4f7fd65c0b16f322ce85d7c54e71ac560fd4da9651"

CCM encrypt and tag NIST VNT AES-128 #6 (P=24, N=12, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"005e8f4d8e0cbf4e1ceeb5d87a275848":"b6f345204526439daf84998f380dcfb4b4167c959c04ff65":"0ec3ac452b547b9062aac8fa":"2f1821aa57e5278ffd33c17d46615b77363149dbc98470413f6543a6b749f2ca":"9575e16f35da3c88a19c26a7b762044f4d7bbbafeff05d754829e2a7752fa3a14890972884b511d8"

CCM encrypt and tag NIST VNT AES-128 #7 (P=24, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"ac87fef3b76e725d66d905625a387e82":"959403e0771c21a416bd03f3898390e90d0a0899f69f9552":"61bf06b9fa5a450d094f3ddcb5":"0245484bcd987787fe97fda6c8ffb6e7058d7b8f7064f27514afaac4048767fd":"cabf8aa613d5357aa3e70173d43f1f202b628a61d18e8b572eb66bb8213a515aa61e5f0945cd57f4"

CCM encrypt and tag NIST VNT AES-192 #1 (P=24, N=7, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"ceb009aea4454451feadf0e6b36f45555dd04723baa448e8":"c8d275f919e17d7fe69c2a1f58939dfe4d403791b5df1310":"764043c49460b7":"6e80dd7f1badf3a1c9ab25c75f10bde78c23fa0eb8f9aaa53adefbf4cbf78fe4":"8a0f3d8229e48e7487fd95a28ad392c80b3681d4fbc7bbfd2dd6ef1c45d4ccb723dc074414db506d"

CCM encrypt and tag NIST VNT AES-192 #2 (P=24, N=8, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"1dd56442fa09a42890b1b4274b950770ea8beea2e048193d":"bd92d6744cde446fc8621625658fc4bc00dcb97f06195ad7":"ad749d596d88a4b4":"c67219909828adef64422286008e1e306867a1c0b3da95444507a68b45c953e4":"076cffd0ca978fe2bad411ced45a090abafb22a99896f6a75a1969276aa2b0cdb37ccaf2845dbf6e"

CCM encrypt and tag NIST VNT AES-192 #3 (P=24, N=9, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"8cc622645065c72d0d2aca75802cf1bbbd81096721627c08":"597b3614ff9cd567afd1aad4e5f52cc3fa4ca32b9b213c55":"cd84acbe9abb6a990a":"447b6f36acdad2d1cfd6e9a92f4055ad90142e61f4a19927caea9dbe634d3208":"2d7fb83e6621eed9073e0386d032c6941bef37b2cf36a4c6c5e36222d17c6fb0631c3f560a3ce4a4"

CCM encrypt and tag NIST VNT AES-192 #4 (P=24, N=10, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"ab72eef2aba30205c986e2052d6e2c67881d24ae5fceaa8f":"2a794b84fc9e4a7e6d70a82b5141fd132177a86b4e8fc13a":"d7a46e726ed43f1580eb":"baa86f14271b2be7dbb37ddc7c95ce4857e57aa94624d594d7bd6ceeaada8d5f":"2d7f76464417613bb61d3657481346b74fc9d6abc6a3babd39365dce86859cd82395d11bfc8cf188"

CCM encrypt and tag NIST VNT AES-192 #5 (P=24, N=11, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"af84c6f302c59aeee6d5728ed5da2e3c64a5a781c52c4d1b":"6db41aeb5f7c24df8929dbc30483b3c7934b3bd1cdce5bb9":"df990c42a268950677c433":"a6ab5d78427f297a4b7e21f1091ff3a5b20caa3fe1cbcb09459d9df596a6c8e1":"8c9328258bf71970d33e23a3ff81cc1c9cbe196a1294264bfd6a7255e4801963bb30a63de3fc5b82"

CCM encrypt and tag NIST VNT AES-192 #6 (P=24, N=12, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"d49b255aed8be1c02eb6d8ae2bac6dcd7901f1f61df3bbf5":"062eafb0cd09d26e65108c0f56fcc7a305f31c34e0f3a24c":"1af29e721c98e81fb6286370":"64f8a0eee5487a4958a489ed35f1327e2096542c1bdb2134fb942ca91804c274":"721344e2fd05d2ee50713531052d75e4071103ab0436f65f0af2a663da51bac626c9f4128ba5ec0b"

CCM encrypt and tag NIST VNT AES-192 #7 (P=24, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"36ad1e3fb630d1b1fbccfd685f44edd8984427b78deae7a9":"8b9db1c8f9b4892a5654c85467bcffa2e15e28392c938952":"3af625df8be9d7685a842f260e":"308443033ecd4a814475672b814b7c6d813d0ec2a0caeecbcaba18a2840cdb6c":"6bc6890fee299c712fb8d9df9c141f24ee1572b8f15112c2f8c99ccf2d82788cf613a61d60dae458"

CCM encrypt and tag NIST VNT AES-256 #1 (P=24, N=7, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"553521a765ab0c3fd203654e9916330e189bdf951feee9b44b10da208fee7acf":"644eb34b9a126e437b5e015eea141ca1a88020f2d5d6cc2c":"aaa23f101647d8":"a355d4c611812e5f9258d7188b3df8851477094ffc2af2cf0c8670db903fbbe0":"27ed90668174ebf8241a3c74b35e1246b6617e4123578f153bdb67062a13ef4e986f5bb3d0bb4307"

CCM encrypt and tag NIST VNT AES-256 #2 (P=24, N=8, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"472bf7946bce1d3c6f168f4475e5bb3a67d5df2fa01e64bce8bb6e43a6c8b177":"59eb45bbbeb054b0b97334d53580ce03f699ac2a7e490143":"790134a8db83f2da":"a7a86a4407b7ecebc89434baa65ef173e88bd2dad9899b717ca578867c2d916f":"db4961070f528ccd1a5a0681ee4d0ce3515fb890bccedc2dbc00b1d8b2bc393a8d09e87af7811f55"

CCM encrypt and tag NIST VNT AES-256 #3 (P=24, N=9, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"58ae7965a508e8dd2eda69b5d888a28a1cb3783bad55d59d5b0da87137b72e93":"e61bad17640ecff926d0b0238271ee4c9f8e801dd7243e9e":"caa3d928d2bf2b7f2c":"304678b3ffd3200e33a8912bcb556b3cfec53ca17f70ecba00d359f9f51d3e3b":"7bb1137c14cb4d324a4a8f1115c619ebf74927f0bed60a8d5a9140ff50dc4da375c7d2de80de097f"

CCM encrypt and tag NIST VNT AES-256 #4 (P=24, N=10, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"aecc5e18088bf9fd7b17f089bdd5607b69903b04b726361f8a81e221b1c91891":"d4291c99901345afe29f58912a414a7498f37b44362bdf3c":"c527d309ab29ee91c5fc":"8f9a73e7bc1c11e2919020ba3a404cbddf861e9e78477218e3be2cd4337b278d":"392784a9e0b14bcd37639ec5409d6ead3e75f855e5a92c33ffc040ef3977e0035ce6ea6d157c18d3"

CCM encrypt and tag NIST VNT AES-256 #5 (P=24, N=11, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"97bc7482a87ba005475dfa3448f59d4b3f9c4c969d08b39b1b21ef965c0f5125":"b99bf4dc781795fc4d3a8467b06e1665d4e543657f23129f":"0bcf78103ec52d6df28887":"049c10f0cb37ae08eae2d0766563b7c5a8454f841c2061a4f71a0a2158ae6ce5":"0d3891fa0caac1f7ebe41b480920ffd34d4155064c24f3b17a483163dd8f228d1f20cd4f86cf38fd"

CCM encrypt and tag NIST VNT AES-256 #6 (P=24, N=12, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"d6ff67379a2ead2ca87aa4f29536258f9fb9fc2e91b0ed18e7b9f5df332dd1dc":"98626ffc6c44f13c964e7fcb7d16e988990d6d063d012d33":"2f1d0717a822e20c7cd28f0a":"d50741d34c8564d92f396b97be782923ff3c855ea9757bde419f632c83997630":"50e22db70ac2bab6d6af7059c90d00fbf0fb52eee5eb650e08aca7dec636170f481dcb9fefb85c05"

CCM encrypt and tag NIST VNT AES-256 #7 (P=24, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"4a75ff2f66dae2935403cce27e829ad8be98185c73f8bc61d3ce950a83007e11":"205f2a664a8512e18321a91c13ec13b9e6b633228c57cc1e":"46eb390b175e75da6193d7edb6":"282f05f734f249c0535ee396282218b7c4913c39b59ad2a03ffaf5b0e9b0f780":"58f1584f761983bef4d0060746b5d5ee610ecfda31101a7f5460e9b7856d60a5ad9803c0762f8176"

CCM encrypt and tag NIST VADT AES-128 #1 (P=24, N=13, A=0, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"d24a3d3dde8c84830280cb87abad0bb3":"7c86135ed9c2a515aaae0e9a208133897269220f30870006":"f1100035bb24a8d26004e0e24b":"":"1faeb0ee2ca2cd52f0aa3966578344f24e69b742c4ab37ab1123301219c70599b7c373ad4b3ad67b"

CCM encrypt and tag NIST VADT AES-128 #2 (P=24, N=13, A=1, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"08b0da255d2083808a1b4d367090bacc":"1b156d7e2bf7c9a25ad91cff7b0b02161cb78ff9162286b0":"777828b13679a9e2ca89568233":"dd":"e8b80af4960d5417c15726406e345c5c46831192b03432eed16b6282283e16602331bcca9d51ce76"

CCM encrypt and tag NIST VADT AES-128 #3 (P=24, N=13, A=2, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"1538cc03b60880bf3e7d388e29f27739":"e7b819a853ffe79baaa72097ff0d04f02640ae62bcfd3da5":"9e734de325026b5d7128193973":"c93c":"1d8f42f9730424fa27240bd6277f4882604f440324b11b003ca01d874439b4e1f79a26d8c6dc433a"

CCM encrypt and tag NIST VADT AES-128 #4 (P=24, N=13, A=3, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"f149e41d848f59276cfddd743bafa9a9":"9759e6f21f5a588010f57e6d6eae178d8b20ab59cda66f42":"14b756d66fc51134e203d1c6f9":"f5827e":"f634bf00f1f9f1f93f41049d7f3797b05e805f0b14850f4e78e2a23411147a6187da6818506232ee"

CCM encrypt and tag NIST VADT AES-128 #5 (P=24, N=13, A=4, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"9a57a22c7f26feff8ca6cceff214e4c2":"035c516776c706a7dd5f181fa6aa891b04dd423042ea0667":"88f30fd2b04fb8ddbce8fc26e6":"a95bdff6":"b92f7ec2ebecebdbd2977b3874e61bf496a382153b2529fc9b6443a35f329b2068916fb6ab8227eb"

CCM encrypt and tag NIST VADT AES-128 #6 (P=24, N=13, A=5, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"54caf96ef6d448734700aadab50faf7a":"c69f7c5a50f3e72123371bbfd6bdf532b99ef78500508dfe":"a3803e752ae849c910d8da36af":"5f476348dd":"20c43ad83610880249f1632dd418ec9a5ed333b50e996d1a4e5a32fbe7961b832b722bc07a18595b"

CCM encrypt and tag NIST VADT AES-128 #7 (P=24, N=13, A=6, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"cc0c084d7de011e2f031616a302e7a31":"15b369889699b6de1fa3ee73e5fe19814e46f129074c965b":"f0b4522847f6f8336fe534a4e7":"da853a27aee2":"f39755d160a64611368a8eccf6fcbc45ef7f1f56240eb19a2e3ca4ec3c776ab58843f617d605fd72"

CCM encrypt and tag NIST VADT AES-128 #8 (P=24, N=13, A=7, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"d7572ed0e37261efa02f8c83e695efdc":"1edef80c57d17f969f8bde10ab38a1a8811a124de72c526e":"f4f96d7b4384a3930b3d830f82":"922340ec94861f":"de14558cc686e1836f1f121ea1b941a9ebd4f0fb916dc870fd541b988a801cb5751c7faaf5b0c164"

CCM encrypt and tag NIST VADT AES-128 #9 (P=24, N=13, A=8, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"98a42d7a0c5917deaf3b4de3f0cbe0a1":"9aa9c8358117564371366beeec923051ef433252197aaad5":"03d33ab0c2df7bfce88b5ee4c4":"2d5438b728b950d9":"9ff942baa60f440c17a78e9581216b9a947a67f04d54911feecfff971fdfaa856310b014aa59c978"

CCM encrypt and tag NIST VADT AES-128 #10 (P=24, N=13, A=9, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"2a68e3fe746f593c1b97cb637079c3e5":"13b4a874888db0e5d8fd814b5e7e04f7fdfbc1601ccc02bc":"cd62d0f27b7f4864dc7c343acd":"abe4f1d3812bfe3ccf":"032835a3dbf688d09cf2a32a92b101959d33ff47500f92f4fd49840440f866d1a22b0854996111d8"

CCM encrypt and tag NIST VADT AES-128 #11 (P=24, N=13, A=10, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"46b067cf9b1a28cf187002e90b14e130":"cc0915194218d4536e467433cd6d79ff1d9eb9ff160ab684":"bad8c03292bf01cfd8d34f860c":"8d65880eddb9fd96d276":"bd56edc015692c6ab9bec493a9893863598414a3d11a6a0f27ecdcb257d0d30491e5bf1aa8f90958"

CCM encrypt and tag NIST VADT AES-128 #12 (P=24, N=13, A=11, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"e94dac9c90984790a7c0c867536615ff":"4d64461c55eb16bf7b9120f22be349598f2f394da8460dc6":"c19f06f91e645d4199365f18c0":"537038b5357e358a930bd6":"e9fc5004c2359724e1e4411ae6f834ef6bea046d549753c88790c1648f461a31c84e62ea8592a074"

CCM encrypt and tag NIST VADT AES-128 #13 (P=24, N=13, A=12, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"f6bb5d59b0fa9de0828b115303bf94aa":"011fc50329bfd63a85ebd4f7693363602f1a4147371270b7":"05358f33e1fc6a53ab5a5c98ce":"040b25771239cc2a39446e3c":"4432d7eb42980734d34f19c50cf8abf71ac1b19ed75a727854e5d050a405f755047d09cb0f49546a"

CCM encrypt and tag NIST VADT AES-128 #14 (P=24, N=13, A=13, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"d1da2e961e78063af8de41865b226873":"8e5fa1a6662a8378cda15697e926841594f2f394fa5a34ab":"03739f5474857006340cce554d":"e3afd091d2b588465872a6300f":"ca0d95e3ff186ad6b88d45fc4079e6b7b4a615e7e8dd5f4742d522cc9dc19c47a4fa0b1528069cf8"

CCM encrypt and tag NIST VADT AES-128 #15 (P=24, N=13, A=14, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"1eee667267ef10b03624cf9c341e3f75":"798e31cce0a83702a95171fb1162a17b9ce00ec3592ce262":"0630a3eae27e505c61c56e6560":"d24651ef0561282d3e20e834960c":"f3c3e52f1a1ff528a8d3783ee4e75f114e3e6416334815d2d9236d5c5c9319092078411b72c51ba8"

CCM encrypt and tag NIST VADT AES-128 #16 (P=24, N=13, A=15, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"dbbd26f5d9e970e4e384b2273961be5a":"553714e17a208a2eceb847a4a2d95088388b1ac8d8ca43e0":"0b1eabe504ef4822542e397fec":"477937301c83ba02d50760b603e0ea":"1c80213268bad5402c4dc9b5d836ab7499810d0d8a974716df9a0e986ab2890736423bb3772cec3e"

CCM encrypt and tag NIST VADT AES-128 #17 (P=24, N=13, A=16, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"10a7720f2e18f739c26924925af6b670":"e59782a9aea45f467b90e51a0fdf166baba05663def2d8b6":"8c4e7813ab9bce9dafee01c628":"a209941fab710fda38d11c68b13d930f":"e357b1ccdaca6f3506dc45279c2e4c59f5307a5fd6a99cd72341ea8c0785569973f90ee9ee645acc"

CCM encrypt and tag NIST VADT AES-128 #18 (P=24, N=13, A=17, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"6bffab1f4f4c1ff66b4a669b515b2f8d":"d91b12e8655dd92b1332fc1d71c391c96a17111562d90ba3":"ddb34d5e0140fb96d690e1a2b7":"5cbba9ea778e01af00afb2a934f28c7211":"d302e5b2d5d90433186b804cd7717e2db2f22cdc34fb2942ab30780a2c4f12af8f35350d65284c59"

CCM encrypt and tag NIST VADT AES-128 #19 (P=24, N=13, A=18, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"ae6136df9ab43631ef143515dacedbe7":"6a493c5ef3769ccc4101dbb2eb36e1e5bbc577a057ce0731":"c5c445792208a50c8e93d64aa3":"e04006b68c83a5dd4ceac3cde238e48895ae":"c7584c0203c2535c5702c6ae93b7cbfb066f4a055c627a180d6d676d11fce907b5c93fa1ed7bff2b"

CCM encrypt and tag NIST VADT AES-128 #20 (P=24, N=13, A=19, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"f1908328edf2996ebfc9655472ca5ad0":"eede01b08f9a303cdf14c99d7a45732972c6eff2a1db06eb":"4c693364546930b6c5250e2699":"4a3634e5028df97fbe00eb016e8ea4f1918faa":"90c850790b0b380f5aeb2488fdf43c9d5ef1759861e86f6e52570e769629dcc2e568737ba53a1195"

CCM encrypt and tag NIST VADT AES-128 #21 (P=24, N=13, A=20, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"61cb8eb792e95d099a1455fb789d8d16":"6ad541695a37c32d73ff6d5f870abd5b0f362a8968c4fce0":"1f37b3e59137f2a60dc09d16ac":"09db3efac9473f713da630ae92c2c8604c61c51e":"e65fcc975865c1499b088b58ba163283085d8ca68dc3b235d89756e5d78753ef22c012ae34b39a20"

CCM encrypt and tag NIST VADT AES-128 #22 (P=24, N=13, A=21, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"be1ed49e2cb0caf6b6a0940c58453b93":"a9eec383f63892521e4616fcbadc5485942ffaf4669c43a7":"b78ad129457681fa7346435b97":"161d92c7df1ebb0924719e066e08b95eb4914a5eda":"949be340720c4fdc4adc05cb777dd81a2549628d33fba07e62d2b338a7b34ebd9d85c244c952d681"

CCM encrypt and tag NIST VADT AES-128 #23 (P=24, N=13, A=22, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"34ab6fd7f54a2e0276fcb7cf1e203aba":"8d164f598ea141082b1069776fccd87baf6a2563cbdbc9d1":"6091afb62c1a8eed4da5624dd7":"1ab5cc3d7b01dc74e6cf838bb565fea3187d33d552a2":"0d30ab07153b5153637969e6bd3539448c541e42b3d432fd7ef14622a9b621d1721b944c60f7fd67"

CCM encrypt and tag NIST VADT AES-128 #24 (P=24, N=13, A=23, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"ea96f90fbae12a857f5c97e0cba57943":"49db80f22bc267a70e5636dfbc8a21c83d9691fe4b9c3051":"21cc46d9ced1539b0ad946e600":"105258d2f25f62675aee975cfdb668aff833f05b61eb2a":"d2fcc8b7809b5fc07e44083e437d8180157f1782a9ce9f65c7fa9ee2e7cdc1b755258f2212a8a8f4"

CCM encrypt and tag NIST VADT AES-128 #25 (P=24, N=13, A=24, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"35b403a15212097085d6e2b77ec3d4f2":"7dd7396db6613eb80909a3b8c0029b624912aabedda0659b":"daa423bf9256c3fcc347a293aa":"d3c0ed74e5f25e4c1e479e1a51182bb018698ec267269149":"5b00cf8a66baa7fe22502ed6f4861af71fa64b550d643f95eee82c19ecba34280604b58d92dacd3f"

CCM encrypt and tag NIST VADT AES-128 #26 (P=24, N=13, A=25, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"7a459aadb48f1a528edae71fcf698b84":"0b3d947de8632dc8ff752f619ba7c84716fac7a23e101641":"fa4616b715ea898772b0e89dd4":"0c0b4a45df5c3919c1e1669c5af5d398d9545e44307d95c481":"7db9f3f7dc26fc2adf58d4525d26d5601e977de5a7c33911a1138cff7b624f9908b5b4d7e90a824a"

CCM encrypt and tag NIST VADT AES-128 #27 (P=24, N=13, A=26, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"ca748225057f735f712ecc64791367f0":"e92cd0cb97afe4fb00c4f12e9b9abe1d08db98f49a27f461":"1341a6998eb1f50d4b710a13ac":"5fb96b045f494808c02014f06074bd45b8a8ad12b4cb448ec162":"82b666694232e86e82295beae66ae67d56aceb5d6b1484ceb4a6843ec16078038c10afedc41f5362"

CCM encrypt and tag NIST VADT AES-128 #28 (P=24, N=13, A=27, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"fdf2b2c7fcb3789b4e90abe607dca2af":"d7aa4efa5d75195a400018bd38f7d8cd53fdffe88df1837f":"a69ddc66e63a3415f21009d53a":"c76846da496ed87b9c0f65c6266c9a822224acde9775efb186a4a5":"150d9a8b78d9c04239d66207a1f95021bbb1b7c70d7c354825d05e5a2e76a90f6fe489fd74cab2a3"

CCM encrypt and tag NIST VADT AES-128 #29 (P=24, N=13, A=28, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"7d870d7e52d3053c65eefad47764cfeb":"109317556c21c969eda65a94176d7a11462c9ae18a865b6d":"37d888f4aa452d7bf217f5a529":"9610949f6d23d5b1f3989b2f4e524fab4f297a5bec8ddad4f16cb616":"4e6b967b1571c6d7b9e118b112b7ac949a4a175650316a242dd579cb0d201d22c86bbc7fbe47bd0d"

CCM encrypt and tag NIST VADT AES-128 #30 (P=24, N=13, A=29, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"8fcac40527c0e7ca8eaff265ca12c053":"78d1e96af8cebdcc7e7e2a4ddcfa34f6cf9a24fb85672ad7":"ae9f012fd9af60a400e20b1690":"9ce65598cd1f86afc9aaaf172809570cc306333c25523f863c6d0e0154":"9adb9a95a9379ad795d8d3ffd4e37a045160d6d727f974a6cb3b5151f327e65447e52c7525562c91"

CCM encrypt and tag NIST VADT AES-128 #31 (P=24, N=13, A=30, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"ddf9f150cc3f1c15e8e773663c5b061c":"79d8841ab83279724ce35e1a8abd4e158168dcf388ab4c3d":"98c5036b7d54da9a1177105600":"20c5ab290e6d97f53c74121951f39ba865b3acc465fa3f0fb8a591622277":"d00d29396ffa9e691290d746527777bf96a851f306d4da0b1816df1e0e82bb7bc8105930ad6a2232"

CCM encrypt and tag NIST VADT AES-128 #32 (P=24, N=13, A=31, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"b1dc81d116d94f5eced526b37c004b95":"54390715b6e7c7bd51a234db059a51ba030cf22ee00b7277":"97c8f69fb91b17299461fd8d63":"f8b08aa83bed09ca342249b2cf9e2b45a89dcfb8711a120395e455921af481":"cb629994c3418a662a8cde1b5f4d99aa7df66e24c53dc6df11297930fd44c63675b7cca70671ef4d"

CCM encrypt and tag NIST VADT AES-128 #33 (P=24, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"5a33980e71e7d67fd6cf171454dc96e5":"a34dfa24847c365291ce1b54bcf8d9a75d861e5133cc3a74":"33ae68ebb8010c6b3da6b9cb29":"eca622a37570df619e10ebb18bebadb2f2b49c4d2b2ff715873bb672e30fc0ff":"7a60fa7ee8859e283cce378fb6b95522ab8b70efcdb0265f7c4b4fa597666b86dd1353e400f28864"

CCM encrypt and tag NIST VADT AES-192 #1 (P=24, N=13, A=0, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"26511fb51fcfa75cb4b44da75a6e5a0eb8d9c8f3b906f886":"39f08a2af1d8da6212550639b91fb2573e39a8eb5d801de8":"15b369889699b6de1fa3ee73e5":"":"6342b8700edec97a960eb16e7cb1eb4412fb4e263ddd2206b090155d34a76c8324e5550c3ef426ed"

CCM encrypt and tag NIST VADT AES-192 #2 (P=24, N=13, A=1, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"9748798c0f3cc766795c8ce0e4c979c1930dfe7faefea84a":"100fa71462277d76ca81f2cfdb3d39d3894b0ca28074a0f0":"cdf4ba655acfe8e2134fa0542f":"67":"36e2415b4f888a6072f260d7e786d803be16f8b9cbee112d7ff74e3b05b7d7c13284573bd3e7e481"

CCM encrypt and tag NIST VADT AES-192 #3 (P=24, N=13, A=2, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"393dcac5a28d77297946d7ab471ae03bd303ba3499e2ce26":"262f4ac988812500cb437f52f0c182148e85a0bec67a2736":"fe7329f343f6e726a90b11ae37":"1c8b":"e6d43f822ad168aa9c2e29c07f4592d7bbeb0203f418f3020ecdbc200be353112faf20e2be711908"

CCM encrypt and tag NIST VADT AES-192 #4 (P=24, N=13, A=3, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"a74abc4347e4be0acb0a73bb8f7d25c35bae13b77f80233a":"6372824bf416cd072a7ad0ae5f9f596c6127520c1b688ab4":"6a850e94940da8781159ba97ef":"a4490e":"b14a07bdc119d87611342c4c6935c5786ff1f9ae2eb49e6191c88a3cb4fbafcb8a4a157d587d7e39"

CCM encrypt and tag NIST VADT AES-192 #5 (P=24, N=13, A=4, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"df052e95aea3769a433ce4e4e800b8418649bbe8c6297eb0":"e8c1a89228d8212f75c136bab7923a89f9fea18e781cb836":"ba356d392c3f700f4f2706a4ca":"8ffc0e3d":"66b5d782323925e1bd0a8413a9a5a881356453d5df2cbeb199b2e1e803550dcdde55fd66ecb45edd"

CCM encrypt and tag NIST VADT AES-192 #6 (P=24, N=13, A=5, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"16d345606a315ad2406abbcb43cd8cabe948107ba6d17a72":"d3bef460223c81e4579c9d1d463ac5e0881685de1420a411":"d4ef3e9e04f1b7f20ffc5a022e":"a468f08d07":"abb85db49a9b1c8724ecbc734cc8373bd20083cfa4007b1cfe4d3a3bb25f89f692884be230c6035c"

CCM encrypt and tag NIST VADT AES-192 #7 (P=24, N=13, A=6, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"1c476cfd7dd300d961fd3f24a6fe0e80742b00851676ca63":"6f3938932b5c1280311e892280d8a822a828a0be7fdb1bcd":"e300fc7a5b96806382c35af5b2":"28130f938c45":"df48662fe134e75a85abc2cece2c3b6236c88a70fa792e9beadc9601adf9fbdf4e3e94b395b0a332"

CCM encrypt and tag NIST VADT AES-192 #8 (P=24, N=13, A=7, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"79d1e38a70df1cf239be168833dcd0570bc8f37b3aa26c37":"83c24f3a77b83b4ef45277ba90225f3ba1722312f52b1a07":"8229d6d7e9e21fdc789bff5dcf":"076887d2abe900":"19d880f1d959a68f162de243d4a45747ace704613359b27218d1531a066de60a95d2924a6910e990"

CCM encrypt and tag NIST VADT AES-192 #9 (P=24, N=13, A=8, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"72e6cebdaf88205c4e74428664bc0d7eb4687a272217b7ca":"54bc7e3c227df4e83252a5848fea12dfdb2d14b9e67c1629":"3820db475c7cb04a0f74d8e449":"f427c47e10c45bb3":"91e7baff2b42af63e26c87ce6991af22422c1f82906858b1721961de5c768f4d19bd3034f44f08d2"

CCM encrypt and tag NIST VADT AES-192 #10 (P=24, N=13, A=9, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"39c03a0c8634047b1635348f284d3dc1e752ab40548eb337":"0662e63c88e963d3e0cf2c4653515ae4474a2c78ab0394c0":"9e2ea8eb7f56087ee506925648":"28d157f09a71da80dd":"01dcd4dd3b8c1369518136ce45e8bb9df565b0ad231a887b02ada34addf0aa2f4744ed2e07995491"

CCM encrypt and tag NIST VADT AES-192 #11 (P=24, N=13, A=10, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"e2a92ffbb0b5eb68cb82687f12449fae5167d375131b0b10":"048c9ba4597c3bb595bfd5048e5e9a1296f30e5c0118b177":"441ad5e1382e083a95224f395d":"2352648299b0413cb2ce":"25247a258e4ac0a988d8def60cc174a9d4578cd5346fb5150c96e8ab8774baa421f39c64a386c418"

CCM encrypt and tag NIST VADT AES-192 #12 (P=24, N=13, A=11, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"ef1ad3eb0bde7d4728389da2255d1f8a66ecb72e6f2f1ac4":"9f580cc6c62a05ce125c6bec109a48ca527ee26a64b14b68":"8e7d8a44244daa7df2b340993e":"521583c25eb4a3b2e46120":"ff0ff95bcb0bccd5e4aadd77ac6770f5013654eb3c6386fded2c87135861b43a99f258b6938f66e3"

CCM encrypt and tag NIST VADT AES-192 #13 (P=24, N=13, A=12, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"44cba20b7204ed85327c9c71c6fea00b47ce7bdde9dea490":"6333bde218b784ccd8370492f7c8c722f8ef143af66d71d7":"f3329154d8908f4e4a5b079992":"f1e0af185180d2eb63e50e37":"b9401a4927b34dc15e9193db00212f85f0c319781ec90e3b4484d93cb422cb564acc63d3d18e169c"

CCM encrypt and tag NIST VADT AES-192 #14 (P=24, N=13, A=13, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"b5f43f3ae38a6165f0f990abe9ee50cd9ad7e847a0a51731":"3726c1aaf85ee8099a7ebd3268700e07d4b3f292c65bba34":"13501aebda19a9bf1b5ffaa42a":"ead4c45ff9db54f9902a6de181":"fd80e88f07dad09eed5569a4f9bb65c42ef426dda40450119503d811701642143013f28ce384d912"

CCM encrypt and tag NIST VADT AES-192 #15 (P=24, N=13, A=14, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"13f179aa2a23bc90a85660306394940e9bb226ce3885ec01":"d3b36c6289ad6ae7c5d885fe83d62a76270689ce05fa3b48":"aaa52c63ca1f74a203d08c2078":"5cc924222692979a8e28ab1e0018":"bc4fcef401c2e1d1c335734ff23ea52c3474d2e6f31648a7f58649400ac9e825b038d67f0c2a6f1c"

CCM encrypt and tag NIST VADT AES-192 #16 (P=24, N=13, A=15, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"c1dfc48273d406a3a7b9176f80b2dc4e9a7f68134bab66d2":"67d9728a88f1fac3af43ed6d634ba902896bd226858697d9":"1ac53ba965cdaeeef7326a37e4":"39ba54a410a58a5d11615a2163cc3b":"360f0fc714994e3b59448b50cdd61d511b4f09e0e5fb5ac826a51fe5b9b598a17eb3da10f936813b"

CCM encrypt and tag NIST VADT AES-192 #17 (P=24, N=13, A=16, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"d8a662ab8449bd037da0346a24565683a3bbbbd1800e3c1c":"61fdd10938557080191d13dd6c3002dd445d9af988029199":"166fb8d0e110124c09013e0568":"1c1c082eeb5b8548283d50cc2ace1c35":"23c05927502a4ee6e61e4e10552d49b020643eab476eeacc867601fe79a122a7817819655183283e"

CCM encrypt and tag NIST VADT AES-192 #18 (P=24, N=13, A=17, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"116f4855121d6aa53e8b8b43a2e23d468c8568c744f49de5":"1bd3b5db392402790be16e8d0a715453928f17f3384c13a7":"924322a3ef0c64412f460a91b2":"03c2d22a3bb08bbb96b2811ce4b1110a83":"ad736402626df0f9393fe4491eb812725ad39d6facf20b5b2f9340b0d48a17ae1cc71d7515e61ee9"

CCM encrypt and tag NIST VADT AES-192 #19 (P=24, N=13, A=18, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"e67f3ba11282d61fe36e38cab7b559c2fd9cbe8bf7eb5863":"d7a954dae563b93385c02c82e0143b6c17ce3067d8b54120":"a727ed373886dd872859b92ccd":"68d199e8fced02b7aeba31aa94068a25d27a":"c6cfaa1f54d041089bd81f89197e57a53b2880cefc3f9d877e30b2bcc3f1ea9ec2b8f28bf0af4ecf"

CCM encrypt and tag NIST VADT AES-192 #20 (P=24, N=13, A=19, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"e0a29a2c7840cf9b41de49780b9ee92d646a4bfc5b9da74a":"344dc8b6bd66a1fbbe330a95af5dd2a8783dc264d6a9267d":"fc9fd876b1edded09f70b18824":"36e15baafa0002efbb4bb26503b7e3b79f6c68":"43b3b96aa5a54378f3bb573ffda3e154aa7f425fc3008175b60a77b9d38740356b544b1c0f259086"

CCM encrypt and tag NIST VADT AES-192 #21 (P=24, N=13, A=20, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"26d0a3a8509d97f81379d21981fe1a02c579121ab7356ca0":"37ab2a0b7b69942278e21032fc83eba6cdc34f5285a8b711":"8015c0f07a7acd4b1cbdd21b54":"093ed26ada5628cfb8cfc1391526b3bcc4af97d9":"a3a60b422eb070b499cf6da0a404b13a05cedda549c6b93e6ca0e07e04674f21a46df2659a5905fb"

CCM encrypt and tag NIST VADT AES-192 #22 (P=24, N=13, A=21, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"aac60835c309d837aacc635931af95702a4784c214283ebb":"e8610756528f75607b83926597ef515f4b32a8386437e6d4":"0e20602d4dc38baa1ebf94ded5":"796e55fbe7bed46d025599c258964a99574c523f6a":"e0a3d5f43e688ce104f4ae1a4fcd85500aa6b8fdbcd1b8d3003c0c3b7369e79339433e1754c0937f"

CCM encrypt and tag NIST VADT AES-192 #23 (P=24, N=13, A=22, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"671544bf2988056f7f9ccd526861391a27233793a23f811f":"576b069ae2713f53d2924c1fd68f786cb2eec68892f9e1be":"0a259148a1d081e0df381ecd0c":"61dafc237cb52f83ab773ba8a885462b6f77d4924611":"ce06b3d09b02921f290544032a081a7766612940048867281bb089af0245792c16e6320cf5ffa19e"

CCM encrypt and tag NIST VADT AES-192 #24 (P=24, N=13, A=23, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"90e2c63b6e5394b1aeec03f95a9d13a01a7d4e9d58610786":"44dd098b1f869d670a8a841900c4bef023a1946a0c278354":"dada5465eb9b7229807a39e557":"f5629ca0eea589f6cf963d875a7d2efb656983f2dd2231":"6b38ca85450e05e7b9362ed7e6e291a130ff233b5a561cdef7ec84dd992fdf98514f845dac8f656e"

CCM encrypt and tag NIST VADT AES-192 #25 (P=24, N=13, A=24, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"13cdaaa4f5721c6d7e709cc048063cfb8b9d92e6425903e6":"d7c837971b973f5f651102bf8d032e7dcd10e306739a0d6c":"f97b532259babac5322e9d9a79":"ad6622279832502839a82348486d42e9b38626e8f06317c4":"4709600418f2839841e6d126359f6982bdb53acc7ff209635623d15b24184481eadc63bb8c878fc4"

CCM encrypt and tag NIST VADT AES-192 #26 (P=24, N=13, A=25, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"90851933d4d3257137984cdb9cba2ca737322dac4dbd64bc":"ba1785a149cb8b69a4e011c11a3ff06f6d7218f525ac81b5":"be02df3a840322df8d448c600c":"69a9dd9ac8be489c3a3f7f070bdaca10699171f66ab3da9351":"89ab2efefa8406336d9e2245199fbc9454f0ef650b9ed0f446c7246bd3130803bf8d703ef5bdf15c"

CCM encrypt and tag NIST VADT AES-192 #27 (P=24, N=13, A=26, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"5c5d02c93faa74a848e5046fc52f236049e28cd8096dcac6":"b4da43ebfe9396b68f4689fba8837c68d0064841c6ddd4a7":"54cbf2889437673b8875a0f567":"09fc21ac4a1f43de29621cacf3ad84e055c6b220721af7ce33bb":"d40725397229021a18f3481e3a85f70445557bb2a85e4ae8101a34c777e918e16186fda05a386572"

CCM encrypt and tag NIST VADT AES-192 #28 (P=24, N=13, A=27, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"0234dae5bd7ae66c67ff0c1a3f1a191a0d7bceb451bc2b7d":"0f960a89a7e806f8709047cb7a2e7c4211ad724692c88a05":"16d345606a315ad2406abbcb43":"c37fdf7449fd7e943595d75e977089c623be0a3926e63fdbbfdf4a":"3907880d25f910eab12dd14e704d1b33ea7c453634d54da2a461f44dac1112ae3f9c65671a931d3e"

CCM encrypt and tag NIST VADT AES-192 #29 (P=24, N=13, A=28, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"6351a67fd6daabd2fd49ee944dd41dd37301f958dd17fcc3":"0c0663dd69ccbffbbd0c8c2e9473d0354451ae7a20fa3695":"b8d517b033754058128d13d11a":"511c6924fa96db716f6b053b7a48aebdc1504145a56cd02d6be2590d":"19f2745df5007619c79c84d174e4521b942776478a0601d982c560fede4741e2fd3b54b3a48f3e38"

CCM encrypt and tag NIST VADT AES-192 #30 (P=24, N=13, A=29, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"9a5a9560baed3b8e0e90b92655d4e5f33889e5d7253d9f6c":"5bbe9c1fb2563e3e82999fe097b28da4dc6ff2e020f3b4f3":"c0049382cdd8646756d4e6bff5":"c95a86d52088a8b0107cc5b437a8938b2c9e74e46e2e03bb9bceecdbe3":"6d5401db42b5c48b79203b6ad82806d7460ac4c82ad0809b811020480e834f6fe55900a162a4e61a"

CCM encrypt and tag NIST VADT AES-192 #31 (P=24, N=13, A=30, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"3e61094c80df0053e86d43fccf4e1d3ee2cdb862d3237b0a":"1fada8f4c7daea0d1c370184c169485b80a278708ed41451":"63f00b2488809fdc49ca5f05d5":"a08763ca936abdeece06467bef8c3c47c3a473636a039d4db540c867d3e3":"680dd22f16a1290bde42c9792dfa997aed24d5bd2265b6e095aa6b99d3f894d3790c2aa2dae1ba2c"

CCM encrypt and tag NIST VADT AES-192 #32 (P=24, N=13, A=31, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"b5664dd6ed435df006052f6ded74bb7ce9482ca9229886f7":"0b6de49b530703affc94010c2b793ddc6de0c44d48037ff2":"7a1649896f3e030c18f0205599":"c5f1a26351e53e6509c8bbbed03c42c23ad81c65fccec7ffa1cb494c7f1fc4":"56b02fea595cc24e798691ae905be3d466ca68ca744005dba260b5ea3b047020b73b5bafa17e5084"

CCM encrypt and tag NIST VADT AES-192 #33 (P=24, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"50925853a84a33ff392154e4e737efc18dcfc98f4d5235a9":"718f061e8b972a3adcf465d66c5b28e8661f080127f6722f":"809343e986f6ff47f54d4cac22":"d70aef3532bdc5293a3ebb11589ac1f801c9f93ea0d656e1d04068facf9f768b":"bad3b0e6772e9c4c9c631c095e259d99692292932efb72b8966e91a19617bb748f3495aa433585bb"

CCM encrypt and tag NIST VADT AES-256 #1 (P=24, N=13, A=0, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"26511fb51fcfa75cb4b44da75a6e5a0eb8d9c8f3b906f886df3ba3e6da3a1389":"30d56ff2a25b83fee791110fcaea48e41db7c7f098a81000":"72a60f345a1978fb40f28a2fa4":"":"55f068c0bbba8b598013dd1841fd740fda2902322148ab5e935753e601b79db4ae730b6ae3500731"

CCM encrypt and tag NIST VADT AES-256 #2 (P=24, N=13, A=1, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"a4490ed6ab51dbfccd6f3702a857575dad44da3a27eaf31178abc97da60d1e4b":"1b5cc6b1651dec4bbbf5130343852e971c7ff1774100d9be":"26ceaf6e3b28190a17c4f0c378":"9e":"789bce069a725a96c484e64a9e54dcb7a7c268c85df47815a462ff2dd8ba44a381e1f6edab12b5a9"

CCM encrypt and tag NIST VADT AES-256 #3 (P=24, N=13, A=2, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"df594db94ef8eca56a417afe946085eaed444c7cc648d07d58132e6cb5bc2bc3":"f4d7978fad36223623ccb5bb18a7373cba8a6e3b1c921259":"c1ad812bf2bbb2cdaee4636ee7":"c0c3":"bea778540a90033b2c0d087e3cc447711ea25f7eea96855506ec97f23bd6ea97834f92f7263c3195"

CCM encrypt and tag NIST VADT AES-256 #4 (P=24, N=13, A=3, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"d98193ab2a465e3fcd85651aaeca18b8e91489b73b7c7e93b518c4b5b81fc6ac":"edba7d6312144e90ec9eaace7576045a46e553dcb8ee5a98":"2247dc7e2674e9e0a63fe70613":"4dc2f4":"44b9ea727c847336fd739ad11f4b906b292edb810462f06ef59626ad5cdac2e4d4cb07b538a1fd8f"

CCM encrypt and tag NIST VADT AES-256 #5 (P=24, N=13, A=4, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"45c8afd7373cb0f6b092af3a633d9fd97c4ca378e19d75f9b74d089429726c29":"0b92adbb251dc29a67f0bb97f8e7160862b6c4e843d07fd9":"fdb1fa230ae0b172ff98fc7496":"270981af":"274e2faea3271ea6fa0494c1951f115b5491a893056c3ee4c76fc350e585277e373e9119bf9595cb"

CCM encrypt and tag NIST VADT AES-256 #6 (P=24, N=13, A=5, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"a2e6bf39efd1ceddc92b4333ed92d65efeea6c031ca345adb93a7770a8039bcd":"d822f84b023f12ea9e3ce16b904278e4aaab5e11c2c23f3f":"693cbb46bc8366086ec7cd7776":"3ba11282d6":"9f91fd2f6472e33b02b1eabb9d6655729d44c44dad6b3883fe0667bcc5806b225224b04ade8b21c1"

CCM encrypt and tag NIST VADT AES-256 #7 (P=24, N=13, A=6, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"c5a850167a5bfdf56636ce9e56e2952855504e35cc4f5d24ee5e168853be82d8":"e758796d7db73bccb1697c42df691ac57974b40ca9186a43":"c45b165477e8bfa9ca3a1cd3ca":"4759557e9bab":"93ad58bd5f4f77ac4f92b0ae16c62489e4074c7f152e2ed8a88179e0d32f4928eff13b4ce2873338"

CCM encrypt and tag NIST VADT AES-256 #8 (P=24, N=13, A=7, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"ae8f93c3efe38e2af07e256961dd33028faa0716e5320a7ab319a10d2f4c5548":"bc9ca92a9c9919e39095d3e53fb148694620ae61227e0069":"6333bde218b784ccd8370492f7":"0b1fabdf2a4107":"45811b0c8f754bf03950e520cd4afc81c2e3eb8a11f4fd386d5a6e4b1fbee15d35939c721004502e"

CCM encrypt and tag NIST VADT AES-256 #9 (P=24, N=13, A=8, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"548c2d1eb7d91e003633d4d9ff199e4a8447180edd89ac7867d25a1db288b5ce":"49fd5cbe4aff89dc3b8718f9ce545d612cbbebb289ecbf42":"23b205bd6ff8ed0bab0c98999c":"a6601111cd92c943":"3cfc6211e359ae322802fc9566f377b0dfe17d1dfe0878ebf2a9047e37cc0be1fab0006af8db8dc4"

CCM encrypt and tag NIST VADT AES-256 #10 (P=24, N=13, A=9, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"aab793e377a12484dbdd74c9b3a85c74c286e1cc498663fbd7c718b5633bb91a":"7c0889854658d3408c5d8043aad2f4ae4a89449a36f8a3b8":"10022cddb323e88b3c08f95a0f":"82b8c736037ce2f2e8":"1044250f58857c69f72b5d3454d43949e5c02b3822970b280de1a3f7fc5d06cc30f06075f5504ed7"

CCM encrypt and tag NIST VADT AES-256 #11 (P=24, N=13, A=10, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"06ac39896073a44283611a66ccab067e2dd2faa8da82ff9a45bb29e54d2e6e77":"3216dce3b8b1ce0e79e40fffcac728ab191aaaf319d971d3":"6c7942c9819cf69b817bfcdb0a":"215e2a6c24325340fdec":"c5b3b50ed8a7b7b96b02ba9464b6a2ff80e90548605699a63d70e6dffb31a376a1eb7f94526dca48"

CCM encrypt and tag NIST VADT AES-256 #12 (P=24, N=13, A=11, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"50412c6444bcf9829506ab019e98234af1541061557412740bc120b456052763":"6cdbd63f6d591f59776f828533b28e2453a214d1d0dd8a39":"85684f94c3702c5d870310166d":"f706a3e09df95d3e21d2e0":"8c8b4ae854a5d5c265b25e3b54bded9444cc454b3e0e6a24d6c05eaf406a5ebd578e19edd5227380"

CCM encrypt and tag NIST VADT AES-256 #13 (P=24, N=13, A=12, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"8a56588fe5e125237b6cdc30f940b8d88b2863ec501a0cb00b1abade1b5ce0ed":"c825952293e434ea866db558aaf486ef09a92bf366988f71":"d80210b9f9776ea36dc0e0a787":"e4296d1c8cf4ffc4b2635135":"b8b3b15fdf6a4a0b5abc313afc769e4e8413bd887552583ede3ed995d1b70561c8e28a7b1a7e3dc8"

CCM encrypt and tag NIST VADT AES-256 #14 (P=24, N=13, A=13, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"a4cc7e1c90f8684e6a5f95e6898ab4e3c194cb46e196d8228062b9f3fa744930":"10d4cff95ef490923c9e0906880729d4d05412e7675cce76":"cdc2712e51c7f333d6bad78eee":"569c56b27268d3db54e728aac0":"be3ce3e9dc72499839a98ae52abb17415e8547687e8a3c7b8aaaac20d4c9276f2851cbba2b04d185"

CCM encrypt and tag NIST VADT AES-256 #15 (P=24, N=13, A=14, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"347e12eec56e95aafcc7d25bf10fc756b4e42bc2e43da7f97df24331f27f1f5c":"ca88dddfc876a12f45f19562bc9ca250f43267ab251a7f34":"b8d517b033754058128d13d11a":"511c6924fa96db716f6b053b7a48":"eeedcfa8f5b5b48c1d7e277526eecb7294213b9f5785167ae949b93003dfe63c95c1d49edfb4de3f"

CCM encrypt and tag NIST VADT AES-256 #16 (P=24, N=13, A=15, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"520902aa27c16dee112812b2e685aa203aeb8b8633bd1bfc99728a482d96c1fe":"533fee7d2c7740db55770e48cb1b541d990ea3f8f08ed1a6":"ddf50502f414c1bf24888f1328":"22b4f8f1aac02a9b2ef785d0ff6f93":"fc867b319e0e4ab45ec518a1b5dcec4f29982173f3abfd4d8a8f8d14d2bdac84c3737cfbd75b7c0b"

CCM encrypt and tag NIST VADT AES-256 #17 (P=24, N=13, A=16, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"57da1c2704219ed59abfdf04743a9a93c87a63d471818de0f1564b2db6421562":"ddc3c1aa73fb6de92bb4db138e26f3c2e0543ab4f5924871":"4b60a47b7e90f622fa0bf803e1":"0ae8c012ff39753510df3ee80707e4e2":"daa8256d4753fdf9cfef876295badaba89b45cc497f54d220ec2c6fb687753bca4580adc6aa2f296"

CCM encrypt and tag NIST VADT AES-256 #18 (P=24, N=13, A=17, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"9267ebc99ccf648b146cba3c251187e24a9947d806ceb0ced6894211641a1e0d":"967daf12f16f166b7b5038f83a1cf0b980f5abf4c7746f2a":"9b7298950280e8762ecdc9bbe4":"5824689453bc406bf891b85e4576e38fe8":"7cfe2a7a54306eb8d8a63d3d1ae86794f9a2c22198b2cb4f10ca926f1a430c08c12e23db3d913e93"

CCM encrypt and tag NIST VADT AES-256 #19 (P=24, N=13, A=18, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"7a855e1690ee638de01db43b37401dcd569c1ae03dc73dd0a917d0cadb5abc29":"33ae68ebb8010c6b3da6b9cb29fe9f8bd09b59ec39f4ce4b":"8f160a873a1166c8b32bccbba7":"72674aca7eba2fc0eeafbd143c2c4d8aa6c8":"b22afdf4f12c43ec23e01ac1215a3f5286059211207e957057e9a9203da74387a9468f8af5e27547"

CCM encrypt and tag NIST VADT AES-256 #20 (P=24, N=13, A=19, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"0ebdc6ddb4c502725dd6ee8da95d56a0d1044b4694d6ba8475a4434f23a8474f":"c7360282c85484a5a33ab1c68dd70873ab4e74ffd4a62cd5":"fb717a8c82114477253acc14f6":"41e9d65632f74f449a6842d5e6c4a86ef83791":"2e961b3a2fa1609a4e6fd04bff6ac5e306ae2638706f997b42be2e2ba05c54b619850db5c9d684fe"

CCM encrypt and tag NIST VADT AES-256 #21 (P=24, N=13, A=20, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"2ff64bbec197a63315c2f328dcb4837d0cdc21a5d6f89ff1d97cb51195330cd8":"4a17522da707b4b2587a0ae367a2cd2831bb593a18ef442a":"a235f8ee3de9896b71910ac02c":"2b411bea57b51d10a4d2fb17ef0f204aa53cf112":"1bf122798bd8ee8e73391d589bd046a294d1615794e69cb9e6f3ba30143acbc3a1c1c6ec74333107"

CCM encrypt and tag NIST VADT AES-256 #22 (P=24, N=13, A=21, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"24e9f08a9a007f9976919e10dc432002e2e078a339677f00105c72ed35633a3f":"d3416a81b4246eb0bf8119a72a886bbc0ac9449c69f71d2f":"15977424eeec0ec7f647e6c798":"2d838eb51a4bc69a001a18adf2084a680f02a3c5fc":"e001a8fae390dc5d672cdd18f86a1f728158ec83a002050def9af5679edbcbb7db20ab6af30698db"

CCM encrypt and tag NIST VADT AES-256 #23 (P=24, N=13, A=22, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"0ec1b22b8df05dc92135d2dfbefed8ea81458f5ea1b801e8a218faf6cbdf1a79":"2f59d94d4ab8eeb84c2a6fefb7fb0a3ac059c1e1a65ae34a":"97ebcb8575bb58260208d5c227":"a2f6337f86dd00d1a58448851e95d8c9bace4a5c8710":"7ca0b1dbe34b0391e524b868b0af08b3e096917664d6aa2cabc1f9d0132394149c9062b74b82f04b"

CCM encrypt and tag NIST VADT AES-256 #24 (P=24, N=13, A=23, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"0875020959ed969cfb38636d1d5aabce9658b00171a7614ea9e5395331c7659c":"065ef9eeafbe077c1c7049f43eb0d8999708e8609f214d5c":"451101250ec6f26652249d59dc":"7cc9c51b69f98a06391ab32742fb6365e15106c811fe8a":"990065322a438e136860f7b019807e9feff52a642bf3d44a9163fa7a867f04cab6f52dc250070f31"

CCM encrypt and tag NIST VADT AES-256 #25 (P=24, N=13, A=24, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"ef4c1d2314e671f666cc6667660f1438a293208c7cc29b412d81277f0a635c91":"c99c3e79125b6fd95e737326a842424eb6c6ecea4c0475c4":"50b23b052922366c25dd40e348":"cd0522ebe1fed82465277d1c10ae9316a98b4469be63b180":"76df4be4ec8373864399acda11294b220b9f7c3a7d2b3660b25764e40ac6a171e7e6bab4fdee4288"

CCM encrypt and tag NIST VADT AES-256 #26 (P=24, N=13, A=25, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"8544808e8fbf8c3a5e1d4ca751d4b603af9fe119eabc6923205815e0e748b7e7":"617d54fc6a23601c79e3984f93bfc2d151fde420863206b3":"b44a58724596b4d8dea827c1a0":"f5b2c88f5232c37273b1e66aa31cfa7201e33c21d60054d025":"57b3414db48982c6567265e1e0173bf38fdfaffe4461fbebc1411af83237c0f9eb0bfe8ed914da66"

CCM encrypt and tag NIST VADT AES-256 #27 (P=24, N=13, A=26, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"e19eaddd9f1574447e7e6525f7fd67e3b42807e44fbb60e75d8c3e98abc18361":"b3b0de10b7c0996662f1b064e04e528b7d85ca1166985d33":"a8c459ce0223358826fb1ec0f0":"ef88f4393d6c1e7b7be55a12144209ee051bb779e440432721ef":"d63e6082c95c6c5ff2bc0771321a4f883ef61cff7b99e0ea8a20a1abe7c842ebc08c8c81a2743c81"

CCM encrypt and tag NIST VADT AES-256 #28 (P=24, N=13, A=27, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"9498f02e50487cfbda1ce6459e241233bd4c4cb10281dcb51915dbc7fb6545c0":"0d16cc69caa9f19b88b05e151b3d26accd018ca4a5786a80":"e3bd4bc3a60cddd26c20aa8636":"70cfcb828d483216b46c3cd22e2f9ee879e9e3059b566179b6e16c":"f1c4bedb8d6f91676881daa37656a7e6402f472735b04a0f1f8332f4236437737438e7aa1b5100c7"

CCM encrypt and tag NIST VADT AES-256 #29 (P=24, N=13, A=28, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"3ac7d5bc4698c021e49a685cd71057e09821633957d1d59c3c30cbc3f2d1dbf8":"89198d3acc39b950f0d411119c478c60b2422ffe7e26e00b":"54c8ff5459702aac058bb3be04":"ecbd7091732e49c0f4bda2e63235ea43bbf8c8730f955f9c049dd1ec":"7717b8e4447afcea1eeebf3e39ffdab2f52828e7931ef27e475acd27900478f09fec1f479ab3a7c8"

CCM encrypt and tag NIST VADT AES-256 #30 (P=24, N=13, A=29, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"948882c3667caa81c9b900996e3d591e6fcb3d08333eeb29911e9c6338710c17":"8b9130b0c3c15366831bbb19f377e3209a8dbf7619cd09bd":"43b0aca2f0a9030f90559fa6d3":"a516ca8405e5c8854e667921b5c5e1968bdd052915b55ac9984b7eefb3":"4646b2acdeb11174171da23999cd54e297daa32bbc13d30512e57c576b315f48c11877178389aaa0"

CCM encrypt and tag NIST VADT AES-256 #31 (P=24, N=13, A=30, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"3bf52cc5ee86b9a0190f390a5c0366a560b557000dbe5115fd9ee11630a62769":"094b538110495e938b08cf748a6bcf3e0c80ff9c66570237":"f9fbd02f28ecc929d369182752":"ebf0b3e3199a5c3773c761c725c7600add5f9d8321c9f8e5e5fd1c7a5d2f":"4d8b53016fc8bc9677184c0fa15bbd3d671b9366d82ecb67f8562eadcdcbcdbad1299bea1523f5d2"

CCM encrypt and tag NIST VADT AES-256 #32 (P=24, N=13, A=31, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"e45bb1730d0d539aab3805350ac986540de9f0f6c239ee70395c291397b70309":"bc8b3bc48c7a88c9fafde258b6ccaa9d4f0d018703d63871":"d5c7824af715bb7822b6b340fe":"860f4a09ad8b3d345c2aa18ffb803f0bc3b734a4d047a1437701a5e3d95288":"95f083ad6bbaee6ab540fe023858f8baf25e333fd3e89c00e678a392d228b210dc5c991905dacf3f"

CCM encrypt and tag NIST VADT AES-256 #33 (P=24, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_AES:"2e6e34070caf1b8820ed39edfa83459abe1c15a1827f1c39f7ac316c4c27910f":"771a7baa9cf83aa253349f6475d5e74dba4525307b022ba7":"c49ccef869bb86d21932cb443b":"d37e35d7cdccd9824a1ae4c787819735e4af798a3beb49d4705336d6496853ad":"eebac2475004970071dfa2cfb855c4e78b1add8dcbccfc0bd6b14027324b657a56263df148665393"

CCM auth decrypt tag NIST DVPT AES-128 #1 (P=0, N=7, A=0, T=4)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_auth_decrypt:MBEDTLS_CIPHER_ID_AES:"4ae701103c63deca5b5a3939d7d05992":"02209f55":"5a8aa485c316e9":"":4:0:""

CCM auth decrypt tag NIST DVPT AES-128 #2 (P=0, N=7, A=0, T=4)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_auth_decrypt:MBEDTLS_CIPHER_ID_AES:"4ae701103c63deca5b5a3939d7d05992":"9a04c241":"3796cf51b87266":"":4:MBEDTLS_ERR_CCM_AUTH_FAILED:""

CCM auth decrypt tag NIST DVPT AES-128 #3 (P=0, N=7, A=0, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_auth_decrypt:MBEDTLS_CIPHER_ID_AES:"4bb3c4a4f893ad8c9bdc833c325d62b3":"75d582db43ce9b13ab4b6f7f14341330":"5a8aa485c316e9":"":16:0:""

CCM auth decrypt tag NIST DVPT AES-128 #4 (P=0, N=7, A=0, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_auth_decrypt:MBEDTLS_CIPHER_ID_AES:"4bb3c4a4f893ad8c9bdc833c325d62b3":"3a65e03af37b81d05acc7ec1bc39deb0":"3796cf51b87266":"":16:MBEDTLS_ERR_CCM_AUTH_FAILED:""

CCM auth decrypt tag NIST DVPT AES-128 #5 (P=0, N=13, A=0, T=4)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_auth_decrypt:MBEDTLS_CIPHER_ID_AES:"4bb3c4a4f893ad8c9bdc833c325d62b3":"90156f3f":"5a8aa485c316e9403aff859fbb":"":4:0:""

CCM auth decrypt tag NIST DVPT AES-128 #6 (P=0, N=13, A=0, T=4)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_auth_decrypt:MBEDTLS_CIPHER_ID_AES:"4bb3c4a4f893ad8c9bdc833c325d62b3":"88909016":"a16a2e741f1cd9717285b6d882":"":4:MBEDTLS_ERR_CCM_AUTH_FAILED:""

CCM auth decrypt tag NIST DVPT AES-128 #7 (P=0, N=13, A=0, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_auth_decrypt:MBEDTLS_CIPHER_ID_AES:"19ebfde2d5468ba0a3031bde629b11fd":"fb04dc5a44c6bb000f2440f5154364b4":"5a8aa485c316e9403aff859fbb":"":16:0:""

CCM auth decrypt tag NIST DVPT AES-128 #8 (P=0, N=13, A=0, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_auth_decrypt:MBEDTLS_CIPHER_ID_AES:"19ebfde2d5468ba0a3031bde629b11fd":"5447075bf42a59b91f08064738b015ab":"a16a2e741f1cd9717285b6d882":"":16:MBEDTLS_ERR_CCM_AUTH_FAILED:""

CCM auth decrypt tag NIST DVPT AES-128 #9 (P=24, N=7, A=0, T=4)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_auth_decrypt:MBEDTLS_CIPHER_ID_AES:"19ebfde2d5468ba0a3031bde629b11fd":"a90e8ea44085ced791b2fdb7fd44b5cf0bd7d27718029bb703e1fa6b":"5a8aa485c316e9":"":4:0:"3796cf51b8726652a4204733b8fbb047cf00fb91a9837e22"

CCM auth decrypt tag NIST DVPT AES-128 #10 (P=24, N=7, A=0, T=4)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_auth_decrypt:MBEDTLS_CIPHER_ID_AES:"19ebfde2d5468ba0a3031bde629b11fd":"50aafe0578c115c4a8e126ff7b3ccb64dce8ccaa8ceda69f23e5d81c":"31f8fa25827d48":"":4:MBEDTLS_ERR_CCM_AUTH_FAILED:""

CCM auth decrypt tag NIST DVPT AES-128 #11 (P=24, N=7, A=0, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_auth_decrypt:MBEDTLS_CIPHER_ID_AES:"197afb02ffbd8f699dacae87094d5243":"24ab9eeb0e5508cae80074f1070ee188a637171860881f1f2d9a3fbc210595b7b8b1b41523111a8e":"5a8aa485c316e9":"":16:0:"3796cf51b8726652a4204733b8fbb047cf00fb91a9837e22"

CCM auth decrypt tag NIST DVPT AES-128 #12 (P=24, N=7, A=0, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_auth_decrypt:MBEDTLS_CIPHER_ID_AES:"197afb02ffbd8f699dacae87094d5243":"7ebfda6fa5da1dbffd82dc29b875798fbcef8ba0084fbd2463af747cc88a001fa94e060290f209c4":"31f8fa25827d48":"":16:MBEDTLS_ERR_CCM_AUTH_FAILED:""

CCM auth decrypt tag NIST DVPT AES-128 #13 (P=24, N=13, A=0, T=4)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_auth_decrypt:MBEDTLS_CIPHER_ID_AES:"197afb02ffbd8f699dacae87094d5243":"4a550134f94455979ec4bf89ad2bd80d25a77ae94e456134a3e138b9":"5a8aa485c316e9403aff859fbb":"":4:0:"a16a2e741f1cd9717285b6d882c1fc53655e9773761ad697"

CCM auth decrypt tag NIST DVPT AES-128 #14 (P=24, N=13, A=0, T=4)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_auth_decrypt:MBEDTLS_CIPHER_ID_AES:"197afb02ffbd8f699dacae87094d5243":"118ec53dd1bfbe52d5b9fe5dfebecf2ee674ec983eada654091a5ae9":"49004912fdd7269279b1f06a89":"":4:MBEDTLS_ERR_CCM_AUTH_FAILED:""

CCM auth decrypt tag NIST DVPT AES-128 #15 (P=24, N=13, A=0, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_auth_decrypt:MBEDTLS_CIPHER_ID_AES:"90929a4b0ac65b350ad1591611fe4829":"4bfe4e35784f0a65b545477e5e2f4bae0e1e6fa717eaf2cb6a9a970b9beb2ac1bd4fd62168f8378a":"5a8aa485c316e9403aff859fbb":"":16:0:"a16a2e741f1cd9717285b6d882c1fc53655e9773761ad697"

CCM auth decrypt tag NIST DVPT AES-128 #16 (P=24, N=13, A=0, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_auth_decrypt:MBEDTLS_CIPHER_ID_AES:"90929a4b0ac65b350ad1591611fe4829":"0c56a503aa2c12e87450d45a7b714db980fd348f327c0065a65666144994bad0c8195bcb4ade1337":"49004912fdd7269279b1f06a89":"":16:MBEDTLS_ERR_CCM_AUTH_FAILED:""

CCM auth decrypt tag NIST DVPT AES-128 #17 (P=0, N=7, A=32, T=4)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_auth_decrypt:MBEDTLS_CIPHER_ID_AES:"90929a4b0ac65b350ad1591611fe4829":"782e4318":"5a8aa485c316e9":"3796cf51b8726652a4204733b8fbb047cf00fb91a9837e22ec22b1a268f88e2c":4:0:""

CCM auth decrypt tag NIST DVPT AES-128 #18 (P=0, N=7, A=32, T=4)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_auth_decrypt:MBEDTLS_CIPHER_ID_AES:"90929a4b0ac65b350ad1591611fe4829":"a04f270a":"a265480ca88d5f":"a2248a882ecbf850daf91933a389e78e81623d233dfd47bf8321361a38f138fe":4:MBEDTLS_ERR_CCM_AUTH_FAILED:""

CCM auth decrypt tag NIST DVPT AES-128 #19 (P=0, N=7, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_auth_decrypt:MBEDTLS_CIPHER_ID_AES:"6a798d7c5e1a72b43e20ad5c7b08567b":"41b476013f45e4a781f253a6f3b1e530":"5a8aa485c316e9":"3796cf51b8726652a4204733b8fbb047cf00fb91a9837e22ec22b1a268f88e2c":16:0:""

CCM auth decrypt tag NIST DVPT AES-128 #20 (P=0, N=7, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_auth_decrypt:MBEDTLS_CIPHER_ID_AES:"6a798d7c5e1a72b43e20ad5c7b08567b":"f9f018fcd125822616083fffebc4c8e6":"a265480ca88d5f":"a2248a882ecbf850daf91933a389e78e81623d233dfd47bf8321361a38f138fe":16:MBEDTLS_ERR_CCM_AUTH_FAILED:""

CCM auth decrypt tag NIST DVPT AES-128 #21 (P=0, N=13, A=32, T=4)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_auth_decrypt:MBEDTLS_CIPHER_ID_AES:"6a798d7c5e1a72b43e20ad5c7b08567b":"9f69f24f":"5a8aa485c316e9403aff859fbb":"a16a2e741f1cd9717285b6d882c1fc53655e9773761ad697a7ee6410184c7982":4:0:""

CCM auth decrypt tag NIST DVPT AES-128 #22 (P=0, N=13, A=32, T=4)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_auth_decrypt:MBEDTLS_CIPHER_ID_AES:"6a798d7c5e1a72b43e20ad5c7b08567b":"e17afaa4":"8739b4bea1a099fe547499cbc6":"f6107696edb332b2ea059d8860fee26be42e5e12e1a4f79a8d0eafce1b2278a7":4:MBEDTLS_ERR_CCM_AUTH_FAILED:""

CCM auth decrypt tag NIST DVPT AES-128 #23 (P=0, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_auth_decrypt:MBEDTLS_CIPHER_ID_AES:"f9fdca4ac64fe7f014de0f43039c7571":"1859ac36a40a6b28b34266253627797a":"5a8aa485c316e9403aff859fbb":"a16a2e741f1cd9717285b6d882c1fc53655e9773761ad697a7ee6410184c7982":16:0:""

CCM auth decrypt tag NIST DVPT AES-128 #24 (P=0, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_auth_decrypt:MBEDTLS_CIPHER_ID_AES:"f9fdca4ac64fe7f014de0f43039c7571":"edf8b46eb69ac0044116019dec183072":"8739b4bea1a099fe547499cbc6":"f6107696edb332b2ea059d8860fee26be42e5e12e1a4f79a8d0eafce1b2278a7":16:MBEDTLS_ERR_CCM_AUTH_FAILED:""

CCM auth decrypt tag NIST DVPT AES-128 #25 (P=24, N=7, A=32, T=4)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_auth_decrypt:MBEDTLS_CIPHER_ID_AES:"f9fdca4ac64fe7f014de0f43039c7571":"6be31860ca271ef448de8f8d8b39346daf4b81d7e92d65b338f125fa":"5a8aa485c316e9":"3796cf51b8726652a4204733b8fbb047cf00fb91a9837e22ec22b1a268f88e2c":4:0:"a265480ca88d5f536db0dc6abc40faf0d05be7a966977768"

CCM auth decrypt tag NIST DVPT AES-128 #26 (P=24, N=7, A=32, T=4)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_auth_decrypt:MBEDTLS_CIPHER_ID_AES:"f9fdca4ac64fe7f014de0f43039c7571":"4cc57a9927a6bc401441870d3193bf89ebd163f5c01501c728a66b69":"fdd2d6f503c915":"5b92394f21ddc3ad49d9b0881b829a5935cb3a4d23e292a62fb66b5e7ab7020e":4:MBEDTLS_ERR_CCM_AUTH_FAILED:""

CCM auth decrypt tag NIST DVPT AES-128 #27 (P=24, N=7, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_auth_decrypt:MBEDTLS_CIPHER_ID_AES:"a7aa635ea51b0bb20a092bd5573e728c":"b351ab96b2e45515254558d5212673ee6c776d42dbca3b512cf3a20b7fd7c49e6e79bef475c2906f":"5a8aa485c316e9":"3796cf51b8726652a4204733b8fbb047cf00fb91a9837e22ec22b1a268f88e2c":16:0:"a265480ca88d5f536db0dc6abc40faf0d05be7a966977768"

CCM auth decrypt tag NIST DVPT AES-128 #28 (P=24, N=7, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_auth_decrypt:MBEDTLS_CIPHER_ID_AES:"a7aa635ea51b0bb20a092bd5573e728c":"df1a5285caa41b4bb47f6e5ceceba4e82721828d68427a3081d18ca149d6766bfaccec88f194eb5b":"fdd2d6f503c915":"5b92394f21ddc3ad49d9b0881b829a5935cb3a4d23e292a62fb66b5e7ab7020e":16:MBEDTLS_ERR_CCM_AUTH_FAILED:""

CCM auth decrypt tag NIST DVPT AES-128 #29 (P=24, N=13, A=32, T=4)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_auth_decrypt:MBEDTLS_CIPHER_ID_AES:"a7aa635ea51b0bb20a092bd5573e728c":"934f893824e880f743d196b22d1f340a52608155087bd28ac25e5329":"5a8aa485c316e9403aff859fbb":"a16a2e741f1cd9717285b6d882c1fc53655e9773761ad697a7ee6410184c7982":4:0:"8739b4bea1a099fe547499cbc6d1b13d849b8084c9b6acc5"

CCM auth decrypt tag NIST DVPT AES-128 #30 (P=24, N=13, A=32, T=4)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_auth_decrypt:MBEDTLS_CIPHER_ID_AES:"a7aa635ea51b0bb20a092bd5573e728c":"f43ba9d834ad85dfab3f1c0c27c3441fe4e411a38a261a6559b3b3ee":"0812757ad0cc4d17c4cfe7a642":"ec6c44a7e94e51a3ca6dee229098391575ec7213c85267fbf7492fdbeee61b10":4:MBEDTLS_ERR_CCM_AUTH_FAILED:""

CCM auth decrypt tag NIST DVPT AES-128 #31 (P=24, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_auth_decrypt:MBEDTLS_CIPHER_ID_AES:"26511fb51fcfa75cb4b44da75a6e5a0e":"50038b5fdd364ee747b70d00bd36840ece4ea19998123375c0a458bfcafa3b2609afe0f825cbf503":"5a8aa485c316e9403aff859fbb":"a16a2e741f1cd9717285b6d882c1fc53655e9773761ad697a7ee6410184c7982":16:0:"8739b4bea1a099fe547499cbc6d1b13d849b8084c9b6acc5"

CCM auth decrypt tag NIST DVPT AES-128 #32 (P=24, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_auth_decrypt:MBEDTLS_CIPHER_ID_AES:"26511fb51fcfa75cb4b44da75a6e5a0e":"78ed8ff6b5a1255d0fbd0a719a9c27b059ff5f83d0c4962c390042ba8bb5f6798dab01c5afad7306":"0812757ad0cc4d17c4cfe7a642":"ec6c44a7e94e51a3ca6dee229098391575ec7213c85267fbf7492fdbeee61b10":16:MBEDTLS_ERR_CCM_AUTH_FAILED:""

CCM auth decrypt tag NIST DVPT AES-192 #1 (P=0, N=7, A=0, T=4)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_auth_decrypt:MBEDTLS_CIPHER_ID_AES:"c98ad7f38b2c7e970c9b965ec87a08208384718f78206c6c":"9d4b7f3b":"5a8aa485c316e9":"":4:0:""

CCM auth decrypt tag NIST DVPT AES-192 #2 (P=0, N=7, A=0, T=4)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_auth_decrypt:MBEDTLS_CIPHER_ID_AES:"c98ad7f38b2c7e970c9b965ec87a08208384718f78206c6c":"80745de9":"3796cf51b87266":"":4:MBEDTLS_ERR_CCM_AUTH_FAILED:""

CCM auth decrypt tag NIST DVPT AES-192 #3 (P=0, N=7, A=0, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_auth_decrypt:MBEDTLS_CIPHER_ID_AES:"4bb3c4a4f893ad8c9bdc833c325d62b3d3ad1bccf9282a65":"17223038fa99d53681ca1beabe78d1b4":"5a8aa485c316e9":"":16:0:""

CCM auth decrypt tag NIST DVPT AES-192 #4 (P=0, N=7, A=0, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_auth_decrypt:MBEDTLS_CIPHER_ID_AES:"4bb3c4a4f893ad8c9bdc833c325d62b3d3ad1bccf9282a65":"d0e1eeef4d2a264536bb1c2c1bde7c35":"3796cf51b87266":"":16:MBEDTLS_ERR_CCM_AUTH_FAILED:""

CCM auth decrypt tag NIST DVPT AES-192 #5 (P=0, N=13, A=0, T=4)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_auth_decrypt:MBEDTLS_CIPHER_ID_AES:"4bb3c4a4f893ad8c9bdc833c325d62b3d3ad1bccf9282a65":"fe69ed84":"5a8aa485c316e9403aff859fbb":"":4:0:""

CCM auth decrypt tag NIST DVPT AES-192 #6 (P=0, N=13, A=0, T=4)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_auth_decrypt:MBEDTLS_CIPHER_ID_AES:"4bb3c4a4f893ad8c9bdc833c325d62b3d3ad1bccf9282a65":"db7ffc82":"a16a2e741f1cd9717285b6d882":"":4:MBEDTLS_ERR_CCM_AUTH_FAILED:""

CCM auth decrypt tag NIST DVPT AES-192 #7 (P=0, N=13, A=0, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_auth_decrypt:MBEDTLS_CIPHER_ID_AES:"19ebfde2d5468ba0a3031bde629b11fd4094afcb205393fa":"0c66a8e547ed4f8c2c9a9a1eb5d455b9":"5a8aa485c316e9403aff859fbb":"":16:0:""

CCM auth decrypt tag NIST DVPT AES-192 #8 (P=0, N=13, A=0, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_auth_decrypt:MBEDTLS_CIPHER_ID_AES:"19ebfde2d5468ba0a3031bde629b11fd4094afcb205393fa":"38757b3a61a4dc97ca3ab88bf1240695":"a16a2e741f1cd9717285b6d882":"":16:MBEDTLS_ERR_CCM_AUTH_FAILED:""

CCM auth decrypt tag NIST DVPT AES-192 #9 (P=24, N=7, A=0, T=4)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_auth_decrypt:MBEDTLS_CIPHER_ID_AES:"19ebfde2d5468ba0a3031bde629b11fd4094afcb205393fa":"411986d04d6463100bff03f7d0bde7ea2c3488784378138cddc93a54":"5a8aa485c316e9":"":4:0:"3796cf51b8726652a4204733b8fbb047cf00fb91a9837e22"

CCM auth decrypt tag NIST DVPT AES-192 #10 (P=24, N=7, A=0, T=4)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_auth_decrypt:MBEDTLS_CIPHER_ID_AES:"19ebfde2d5468ba0a3031bde629b11fd4094afcb205393fa":"32b649ab56162e55d4148a1292d6a225a988eb1308298273b6889036":"31f8fa25827d48":"":4:MBEDTLS_ERR_CCM_AUTH_FAILED:""

CCM auth decrypt tag NIST DVPT AES-192 #11 (P=24, N=7, A=0, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_auth_decrypt:MBEDTLS_CIPHER_ID_AES:"197afb02ffbd8f699dacae87094d524324576b99844f75e1":"cba4b4aeb85f0492fd8d905c4a6d8233139833373ef188a8c5a5ebecf7ac8607fe412189e83d9d20":"5a8aa485c316e9":"":16:0:"3796cf51b8726652a4204733b8fbb047cf00fb91a9837e22"

CCM auth decrypt tag NIST DVPT AES-192 #12 (P=24, N=7, A=0, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_auth_decrypt:MBEDTLS_CIPHER_ID_AES:"197afb02ffbd8f699dacae87094d524324576b99844f75e1":"ca62713728b5c9d652504b0ae8fd4fee5d297ee6a8d19cb6e699f15f14d34dcaf9ba8ed4b877c97d":"31f8fa25827d48":"":16:MBEDTLS_ERR_CCM_AUTH_FAILED:""

CCM auth decrypt tag NIST DVPT AES-192 #13 (P=24, N=13, A=0, T=4)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_auth_decrypt:MBEDTLS_CIPHER_ID_AES:"197afb02ffbd8f699dacae87094d524324576b99844f75e1":"042653c674ef2a90f7fb11d30848e530ae59478f1051633a34fad277":"5a8aa485c316e9403aff859fbb":"":4:0:"a16a2e741f1cd9717285b6d882c1fc53655e9773761ad697"

CCM auth decrypt tag NIST DVPT AES-192 #14 (P=24, N=13, A=0, T=4)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_auth_decrypt:MBEDTLS_CIPHER_ID_AES:"197afb02ffbd8f699dacae87094d524324576b99844f75e1":"1902d9769a7ba3d3268e1257395c8c2e5f98eef295dcbfa5a35df775":"49004912fdd7269279b1f06a89":"":4:MBEDTLS_ERR_CCM_AUTH_FAILED:""

CCM auth decrypt tag NIST DVPT AES-192 #15 (P=24, N=13, A=0, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_auth_decrypt:MBEDTLS_CIPHER_ID_AES:"90929a4b0ac65b350ad1591611fe48297e03956f6083e451":"a5b7d8cca2069908d1ed88e6a9fe2c9bede3131dad54671ea7ade30a07d185692ab0ebdf4c78cf7a":"5a8aa485c316e9403aff859fbb":"":16:0:"a16a2e741f1cd9717285b6d882c1fc53655e9773761ad697"

CCM auth decrypt tag NIST DVPT AES-192 #16 (P=24, N=13, A=0, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_auth_decrypt:MBEDTLS_CIPHER_ID_AES:"90929a4b0ac65b350ad1591611fe48297e03956f6083e451":"9a98617fb97a0dfe466be692272dcdaec1c5443a3b51312ef042c86363cc05afb98c66e16be8a445":"49004912fdd7269279b1f06a89":"":16:MBEDTLS_ERR_CCM_AUTH_FAILED:""

CCM auth decrypt tag NIST DVPT AES-192 #17 (P=0, N=7, A=32, T=4)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_auth_decrypt:MBEDTLS_CIPHER_ID_AES:"90929a4b0ac65b350ad1591611fe48297e03956f6083e451":"1d089a5f":"5a8aa485c316e9":"3796cf51b8726652a4204733b8fbb047cf00fb91a9837e22ec22b1a268f88e2c":4:0:""

CCM auth decrypt tag NIST DVPT AES-192 #18 (P=0, N=7, A=32, T=4)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_auth_decrypt:MBEDTLS_CIPHER_ID_AES:"90929a4b0ac65b350ad1591611fe48297e03956f6083e451":"2f46022a":"a265480ca88d5f":"a2248a882ecbf850daf91933a389e78e81623d233dfd47bf8321361a38f138fe":4:MBEDTLS_ERR_CCM_AUTH_FAILED:""

CCM auth decrypt tag NIST DVPT AES-192 #19 (P=0, N=7, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_auth_decrypt:MBEDTLS_CIPHER_ID_AES:"6a798d7c5e1a72b43e20ad5c7b08567b12ab744b61c070e2":"5280a2137fee3deefcfe9b63a1199fb3":"5a8aa485c316e9":"3796cf51b8726652a4204733b8fbb047cf00fb91a9837e22ec22b1a268f88e2c":16:0:""

CCM auth decrypt tag NIST DVPT AES-192 #20 (P=0, N=7, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_auth_decrypt:MBEDTLS_CIPHER_ID_AES:"6a798d7c5e1a72b43e20ad5c7b08567b12ab744b61c070e2":"d40a7318c5f2d82f838c0beeefe0d598":"a265480ca88d5f":"a2248a882ecbf850daf91933a389e78e81623d233dfd47bf8321361a38f138fe":16:MBEDTLS_ERR_CCM_AUTH_FAILED:""

CCM auth decrypt tag NIST DVPT AES-192 #21 (P=0, N=13, A=32, T=4)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_auth_decrypt:MBEDTLS_CIPHER_ID_AES:"6a798d7c5e1a72b43e20ad5c7b08567b12ab744b61c070e2":"5e0eaebd":"5a8aa485c316e9403aff859fbb":"a16a2e741f1cd9717285b6d882c1fc53655e9773761ad697a7ee6410184c7982":4:0:""

CCM auth decrypt tag NIST DVPT AES-192 #22 (P=0, N=13, A=32, T=4)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_auth_decrypt:MBEDTLS_CIPHER_ID_AES:"6a798d7c5e1a72b43e20ad5c7b08567b12ab744b61c070e2":"71b7fc33":"8739b4bea1a099fe547499cbc6":"f6107696edb332b2ea059d8860fee26be42e5e12e1a4f79a8d0eafce1b2278a7":4:MBEDTLS_ERR_CCM_AUTH_FAILED:""

CCM auth decrypt tag NIST DVPT AES-192 #23 (P=0, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_auth_decrypt:MBEDTLS_CIPHER_ID_AES:"f9fdca4ac64fe7f014de0f43039c757194d544ce5d15eed4":"d07ccf9fdc3d33aa94cda3d230da707c":"5a8aa485c316e9403aff859fbb":"a16a2e741f1cd9717285b6d882c1fc53655e9773761ad697a7ee6410184c7982":16:0:""

CCM auth decrypt tag NIST DVPT AES-192 #24 (P=0, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_auth_decrypt:MBEDTLS_CIPHER_ID_AES:"f9fdca4ac64fe7f014de0f43039c757194d544ce5d15eed4":"65fe32b649dc328c9f531584897e85b3":"8739b4bea1a099fe547499cbc6":"f6107696edb332b2ea059d8860fee26be42e5e12e1a4f79a8d0eafce1b2278a7":16:MBEDTLS_ERR_CCM_AUTH_FAILED:""

CCM auth decrypt tag NIST DVPT AES-192 #25 (P=24, N=7, A=32, T=4)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_auth_decrypt:MBEDTLS_CIPHER_ID_AES:"f9fdca4ac64fe7f014de0f43039c757194d544ce5d15eed4":"9f6ca4af9b159148c889a6584d1183ea26e2614874b0504575dea8d1":"5a8aa485c316e9":"3796cf51b8726652a4204733b8fbb047cf00fb91a9837e22ec22b1a268f88e2c":4:0:"a265480ca88d5f536db0dc6abc40faf0d05be7a966977768"

CCM auth decrypt tag NIST DVPT AES-192 #26 (P=24, N=7, A=32, T=4)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_auth_decrypt:MBEDTLS_CIPHER_ID_AES:"f9fdca4ac64fe7f014de0f43039c757194d544ce5d15eed4":"84d8212e9cfc2121252baa3b065b1edcf50497b9594db1ebd7965825":"fdd2d6f503c915":"5b92394f21ddc3ad49d9b0881b829a5935cb3a4d23e292a62fb66b5e7ab7020e":4:MBEDTLS_ERR_CCM_AUTH_FAILED:""

CCM auth decrypt tag NIST DVPT AES-192 #27 (P=24, N=7, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_auth_decrypt:MBEDTLS_CIPHER_ID_AES:"a7aa635ea51b0bb20a092bd5573e728ccd4b3e8cdd2ab33d":"6aab64c4787599d8f213446beadb16e08dba60e97f56dbd14d1d980d6fe0fb44b421992662b97975":"5a8aa485c316e9":"3796cf51b8726652a4204733b8fbb047cf00fb91a9837e22ec22b1a268f88e2c":16:0:"a265480ca88d5f536db0dc6abc40faf0d05be7a966977768"

CCM auth decrypt tag NIST DVPT AES-192 #28 (P=24, N=7, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_auth_decrypt:MBEDTLS_CIPHER_ID_AES:"a7aa635ea51b0bb20a092bd5573e728ccd4b3e8cdd2ab33d":"4980b2ee49b1aaf393175f5ab9bae95ec7904557dfa206603c51d36c826f01384100886198a7f6a3":"fdd2d6f503c915":"5b92394f21ddc3ad49d9b0881b829a5935cb3a4d23e292a62fb66b5e7ab7020e":16:MBEDTLS_ERR_CCM_AUTH_FAILED:""

CCM auth decrypt tag NIST DVPT AES-192 #29 (P=24, N=13, A=32, T=4)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_auth_decrypt:MBEDTLS_CIPHER_ID_AES:"a7aa635ea51b0bb20a092bd5573e728ccd4b3e8cdd2ab33d":"16e543d0e20615ff0df15acd9927ddfe40668a54bb854cccc25e9fce":"5a8aa485c316e9403aff859fbb":"a16a2e741f1cd9717285b6d882c1fc53655e9773761ad697a7ee6410184c7982":4:0:"8739b4bea1a099fe547499cbc6d1b13d849b8084c9b6acc5"

CCM auth decrypt tag NIST DVPT AES-192 #30 (P=24, N=13, A=32, T=4)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_auth_decrypt:MBEDTLS_CIPHER_ID_AES:"a7aa635ea51b0bb20a092bd5573e728ccd4b3e8cdd2ab33d":"df35b109caf690656ae278bbd8f8bba687a2ce11b105dae98ecedb3e":"0812757ad0cc4d17c4cfe7a642":"ec6c44a7e94e51a3ca6dee229098391575ec7213c85267fbf7492fdbeee61b10":4:MBEDTLS_ERR_CCM_AUTH_FAILED:""

CCM auth decrypt tag NIST DVPT AES-192 #31 (P=24, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_auth_decrypt:MBEDTLS_CIPHER_ID_AES:"26511fb51fcfa75cb4b44da75a6e5a0eb8d9c8f3b906f886":"c5b0b2ef17498c5570eb335df4588032958ba3d69bf6f3178464a6f7fa2b76744e8e8d95691cecb8":"5a8aa485c316e9403aff859fbb":"a16a2e741f1cd9717285b6d882c1fc53655e9773761ad697a7ee6410184c7982":16:0:"8739b4bea1a099fe547499cbc6d1b13d849b8084c9b6acc5"

CCM auth decrypt tag NIST DVPT AES-192 #32 (P=24, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_auth_decrypt:MBEDTLS_CIPHER_ID_AES:"26511fb51fcfa75cb4b44da75a6e5a0eb8d9c8f3b906f886":"d1f0518929f4ae2f0543de2a7dfe4bb0110bb3057e524a1c06bd6dc2e6bcc3436cffb969ae900388":"0812757ad0cc4d17c4cfe7a642":"ec6c44a7e94e51a3ca6dee229098391575ec7213c85267fbf7492fdbeee61b10":16:MBEDTLS_ERR_CCM_AUTH_FAILED:""

CCM auth decrypt tag NIST DVPT AES-256 #1 (P=0, N=7, A=0, T=4)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_auth_decrypt:MBEDTLS_CIPHER_ID_AES:"eda32f751456e33195f1f499cf2dc7c97ea127b6d488f211ccc5126fbb24afa6":"469c90bb":"a544218dadd3c1":"":4:0:""

CCM auth decrypt tag NIST DVPT AES-256 #2 (P=0, N=7, A=0, T=4)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_auth_decrypt:MBEDTLS_CIPHER_ID_AES:"eda32f751456e33195f1f499cf2dc7c97ea127b6d488f211ccc5126fbb24afa6":"46a908ed":"d3d5424e20fbec":"":4:MBEDTLS_ERR_CCM_AUTH_FAILED:""

CCM auth decrypt tag NIST DVPT AES-256 #3 (P=0, N=7, A=0, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_auth_decrypt:MBEDTLS_CIPHER_ID_AES:"e1b8a927a95efe94656677b692662000278b441c79e879dd5c0ddc758bdc9ee8":"8207eb14d33855a52acceed17dbcbf6e":"a544218dadd3c1":"":16:0:""

CCM auth decrypt tag NIST DVPT AES-256 #4 (P=0, N=7, A=0, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_auth_decrypt:MBEDTLS_CIPHER_ID_AES:"e1b8a927a95efe94656677b692662000278b441c79e879dd5c0ddc758bdc9ee8":"60f8e127cb4d30db6df0622158cd931d":"d3d5424e20fbec":"":16:MBEDTLS_ERR_CCM_AUTH_FAILED:""

CCM auth decrypt tag NIST DVPT AES-256 #5 (P=0, N=13, A=0, T=4)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_auth_decrypt:MBEDTLS_CIPHER_ID_AES:"e1b8a927a95efe94656677b692662000278b441c79e879dd5c0ddc758bdc9ee8":"8a19a133":"a544218dadd3c10583db49cf39":"":4:0:""

CCM auth decrypt tag NIST DVPT AES-256 #6 (P=0, N=13, A=0, T=4)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_auth_decrypt:MBEDTLS_CIPHER_ID_AES:"e1b8a927a95efe94656677b692662000278b441c79e879dd5c0ddc758bdc9ee8":"2e317f1b":"3c0e2815d37d844f7ac240ba9d":"":4:MBEDTLS_ERR_CCM_AUTH_FAILED:""

CCM auth decrypt tag NIST DVPT AES-256 #7 (P=0, N=13, A=0, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_auth_decrypt:MBEDTLS_CIPHER_ID_AES:"af063639e66c284083c5cf72b70d8bc277f5978e80d9322d99f2fdc718cda569":"97e1a8dd4259ccd2e431e057b0397fcf":"a544218dadd3c10583db49cf39":"":16:0:""

CCM auth decrypt tag NIST DVPT AES-256 #8 (P=0, N=13, A=0, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_auth_decrypt:MBEDTLS_CIPHER_ID_AES:"af063639e66c284083c5cf72b70d8bc277f5978e80d9322d99f2fdc718cda569":"5a9596c511ea6a8671adefc4f2157d8b":"3c0e2815d37d844f7ac240ba9d":"":16:MBEDTLS_ERR_CCM_AUTH_FAILED:""

CCM auth decrypt tag NIST DVPT AES-256 #9 (P=24, N=7, A=0, T=4)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_auth_decrypt:MBEDTLS_CIPHER_ID_AES:"af063639e66c284083c5cf72b70d8bc277f5978e80d9322d99f2fdc718cda569":"64a1341679972dc5869fcf69b19d5c5ea50aa0b5e985f5b722aa8d59":"a544218dadd3c1":"":4:0:"d3d5424e20fbec43ae495353ed830271515ab104f8860c98"

CCM auth decrypt tag NIST DVPT AES-256 #10 (P=24, N=7, A=0, T=4)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_auth_decrypt:MBEDTLS_CIPHER_ID_AES:"af063639e66c284083c5cf72b70d8bc277f5978e80d9322d99f2fdc718cda569":"c5b7f802bffc498c1626e3774f1d9f94045dfd8e1a10a20277d00a75":"bfcda8b5a2d0d2":"":4:MBEDTLS_ERR_CCM_AUTH_FAILED:""

CCM auth decrypt tag NIST DVPT AES-256 #11 (P=24, N=7, A=0, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_auth_decrypt:MBEDTLS_CIPHER_ID_AES:"f7079dfa3b5c7b056347d7e437bcded683abd6e2c9e069d333284082cbb5d453":"bc51c3925a960e7732533e4ef3a4f69ee6826de952bcb0fd374f3bb6db8377ebfc79674858c4f305":"a544218dadd3c1":"":16:0:"d3d5424e20fbec43ae495353ed830271515ab104f8860c98"

CCM auth decrypt tag NIST DVPT AES-256 #12 (P=24, N=7, A=0, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_auth_decrypt:MBEDTLS_CIPHER_ID_AES:"f7079dfa3b5c7b056347d7e437bcded683abd6e2c9e069d333284082cbb5d453":"afa1fa8e8a70e26b02161150556d604101fdf423f332c3363275f2a4907d51b734fe7238cebbd48f":"bfcda8b5a2d0d2":"":16:MBEDTLS_ERR_CCM_AUTH_FAILED:""

CCM auth decrypt tag NIST DVPT AES-256 #13 (P=24, N=13, A=0, T=4)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_auth_decrypt:MBEDTLS_CIPHER_ID_AES:"f7079dfa3b5c7b056347d7e437bcded683abd6e2c9e069d333284082cbb5d453":"63e00d30e4b08fd2a1cc8d70fab327b2368e77a93be4f4123d14fb3f":"a544218dadd3c10583db49cf39":"":4:0:"3c0e2815d37d844f7ac240ba9d6e3a0b2a86f706e885959e"

CCM auth decrypt tag NIST DVPT AES-256 #14 (P=24, N=13, A=0, T=4)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_auth_decrypt:MBEDTLS_CIPHER_ID_AES:"f7079dfa3b5c7b056347d7e437bcded683abd6e2c9e069d333284082cbb5d453":"bb5425b3869b76856ec58e39886fb6f6f2ac13fe44cb132d8d0c0099":"894dcaa61008eb8fb052c60d41":"":4:MBEDTLS_ERR_CCM_AUTH_FAILED:""

CCM auth decrypt tag NIST DVPT AES-256 #15 (P=24, N=13, A=0, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_auth_decrypt:MBEDTLS_CIPHER_ID_AES:"1b0e8df63c57f05d9ac457575ea764524b8610ae5164e6215f426f5a7ae6ede4":"f0050ad16392021a3f40207bed3521fb1e9f808f49830c423a578d179902f912f9ea1afbce1120b3":"a544218dadd3c10583db49cf39":"":16:0:"3c0e2815d37d844f7ac240ba9d6e3a0b2a86f706e885959e"

CCM auth decrypt tag NIST DVPT AES-256 #16 (P=24, N=13, A=0, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_auth_decrypt:MBEDTLS_CIPHER_ID_AES:"1b0e8df63c57f05d9ac457575ea764524b8610ae5164e6215f426f5a7ae6ede4":"c408190d0fbf5034f83b24a8ed9657331a7ce141de4fae769084607b83bd06e6442eac8dacf583cc":"894dcaa61008eb8fb052c60d41":"":16:MBEDTLS_ERR_CCM_AUTH_FAILED:""

CCM auth decrypt tag NIST DVPT AES-256 #17 (P=0, N=7, A=32, T=4)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_auth_decrypt:MBEDTLS_CIPHER_ID_AES:"1b0e8df63c57f05d9ac457575ea764524b8610ae5164e6215f426f5a7ae6ede4":"92d00fbe":"a544218dadd3c1":"d3d5424e20fbec43ae495353ed830271515ab104f8860c988d15b6d36c038eab":4:0:""

CCM auth decrypt tag NIST DVPT AES-256 #18 (P=0, N=7, A=32, T=4)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_auth_decrypt:MBEDTLS_CIPHER_ID_AES:"1b0e8df63c57f05d9ac457575ea764524b8610ae5164e6215f426f5a7ae6ede4":"9143e5c4":"78c46e3249ca28":"232e957c65ffa11988e830d4617d500f1c4a35c1221f396c41ab214f074ca2dc":4:MBEDTLS_ERR_CCM_AUTH_FAILED:""

CCM auth decrypt tag NIST DVPT AES-256 #19 (P=0, N=7, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_auth_decrypt:MBEDTLS_CIPHER_ID_AES:"a4bc10b1a62c96d459fbaf3a5aa3face7313bb9e1253e696f96a7a8e36801088":"93af11a08379eb37a16aa2837f09d69d":"a544218dadd3c1":"d3d5424e20fbec43ae495353ed830271515ab104f8860c988d15b6d36c038eab":16:0:""

CCM auth decrypt tag NIST DVPT AES-256 #20 (P=0, N=7, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_auth_decrypt:MBEDTLS_CIPHER_ID_AES:"a4bc10b1a62c96d459fbaf3a5aa3face7313bb9e1253e696f96a7a8e36801088":"d19b0c14ec686a7961ca7c386d125a65":"78c46e3249ca28":"232e957c65ffa11988e830d4617d500f1c4a35c1221f396c41ab214f074ca2dc":16:MBEDTLS_ERR_CCM_AUTH_FAILED:""

CCM auth decrypt tag NIST DVPT AES-256 #21 (P=0, N=13, A=32, T=4)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_auth_decrypt:MBEDTLS_CIPHER_ID_AES:"a4bc10b1a62c96d459fbaf3a5aa3face7313bb9e1253e696f96a7a8e36801088":"866d4227":"a544218dadd3c10583db49cf39":"3c0e2815d37d844f7ac240ba9d6e3a0b2a86f706e885959e09a1005e024f6907":4:0:""

CCM auth decrypt tag NIST DVPT AES-256 #22 (P=0, N=13, A=32, T=4)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_auth_decrypt:MBEDTLS_CIPHER_ID_AES:"a4bc10b1a62c96d459fbaf3a5aa3face7313bb9e1253e696f96a7a8e36801088":"94cb1127":"e8de970f6ee8e80ede933581b5":"89f8b068d34f56bc49d839d8e47b347e6dae737b903b278632447e6c0485d26a":4:MBEDTLS_ERR_CCM_AUTH_FAILED:""

CCM auth decrypt tag NIST DVPT AES-256 #23 (P=0, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_auth_decrypt:MBEDTLS_CIPHER_ID_AES:"8c5cf3457ff22228c39c051c4e05ed4093657eb303f859a9d4b0f8be0127d88a":"867b0d87cf6e0f718200a97b4f6d5ad5":"a544218dadd3c10583db49cf39":"3c0e2815d37d844f7ac240ba9d6e3a0b2a86f706e885959e09a1005e024f6907":16:0:""

CCM auth decrypt tag NIST DVPT AES-256 #24 (P=0, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_auth_decrypt:MBEDTLS_CIPHER_ID_AES:"8c5cf3457ff22228c39c051c4e05ed4093657eb303f859a9d4b0f8be0127d88a":"677a040d46ee3f2b7838273bdad14f16":"e8de970f6ee8e80ede933581b5":"89f8b068d34f56bc49d839d8e47b347e6dae737b903b278632447e6c0485d26a":16:MBEDTLS_ERR_CCM_AUTH_FAILED:""

CCM auth decrypt tag NIST DVPT AES-256 #25 (P=24, N=7, A=32, T=4)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_auth_decrypt:MBEDTLS_CIPHER_ID_AES:"8c5cf3457ff22228c39c051c4e05ed4093657eb303f859a9d4b0f8be0127d88a":"c2fe12658139f5d0dd22cadf2e901695b579302a72fc56083ebc7720":"a544218dadd3c1":"d3d5424e20fbec43ae495353ed830271515ab104f8860c988d15b6d36c038eab":4:0:"78c46e3249ca28e1ef0531d80fd37c124d9aecb7be6668e3"

CCM auth decrypt tag NIST DVPT AES-256 #26 (P=24, N=7, A=32, T=4)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_auth_decrypt:MBEDTLS_CIPHER_ID_AES:"8c5cf3457ff22228c39c051c4e05ed4093657eb303f859a9d4b0f8be0127d88a":"94748ba81229e53c38583a8564b23ebbafc6f6efdf4c2a81c44db2c9":"6ba004fd176791":"5a053b2a1bb87e85d56527bfcdcd3ecafb991bb10e4c862bb0751c700a29f54b":4:MBEDTLS_ERR_CCM_AUTH_FAILED:""

CCM auth decrypt tag NIST DVPT AES-256 #27 (P=24, N=7, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_auth_decrypt:MBEDTLS_CIPHER_ID_AES:"705334e30f53dd2f92d190d2c1437c8772f940c55aa35e562214ed45bd458ffe":"3341168eb8c48468c414347fb08f71d2086f7c2d1bd581ce1ac68bd42f5ec7fa7e068cc0ecd79c2a":"a544218dadd3c1":"d3d5424e20fbec43ae495353ed830271515ab104f8860c988d15b6d36c038eab":16:0:"78c46e3249ca28e1ef0531d80fd37c124d9aecb7be6668e3"

CCM auth decrypt tag NIST DVPT AES-256 #28 (P=24, N=7, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_auth_decrypt:MBEDTLS_CIPHER_ID_AES:"705334e30f53dd2f92d190d2c1437c8772f940c55aa35e562214ed45bd458ffe":"d543acda712b898cbb27b8f598b2e4438ce587a836e2785147c3338a2400809e739b63ba8227d2f9":"6ba004fd176791":"5a053b2a1bb87e85d56527bfcdcd3ecafb991bb10e4c862bb0751c700a29f54b":16:MBEDTLS_ERR_CCM_AUTH_FAILED:""

CCM auth decrypt tag NIST DVPT AES-256 #29 (P=24, N=13, A=32, T=4)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_auth_decrypt:MBEDTLS_CIPHER_ID_AES:"705334e30f53dd2f92d190d2c1437c8772f940c55aa35e562214ed45bd458ffe":"c0ea400b599561e7905b99262b4565d5c3dc49fad84d7c69ef891339":"a544218dadd3c10583db49cf39":"3c0e2815d37d844f7ac240ba9d6e3a0b2a86f706e885959e09a1005e024f6907":4:0:"e8de970f6ee8e80ede933581b5bcf4d837e2b72baa8b00c3"

CCM auth decrypt tag NIST DVPT AES-256 #30 (P=24, N=13, A=32, T=4)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_auth_decrypt:MBEDTLS_CIPHER_ID_AES:"705334e30f53dd2f92d190d2c1437c8772f940c55aa35e562214ed45bd458ffe":"60871e03ea0eb968536c99f926ea24ef43d41272ad9fb7f63d488623":"8fa501c5dd9ac9b868144c9fa5":"5bb40e3bb72b4509324a7edc852f72535f1f6283156e63f6959ffaf39dcde800":4:MBEDTLS_ERR_CCM_AUTH_FAILED:""

CCM auth decrypt tag NIST DVPT AES-256 #31 (P=24, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_auth_decrypt:MBEDTLS_CIPHER_ID_AES:"314a202f836f9f257e22d8c11757832ae5131d357a72df88f3eff0ffcee0da4e":"8d34cdca37ce77be68f65baf3382e31efa693e63f914a781367f30f2eaad8c063ca50795acd90203":"a544218dadd3c10583db49cf39":"3c0e2815d37d844f7ac240ba9d6e3a0b2a86f706e885959e09a1005e024f6907":16:0:"e8de970f6ee8e80ede933581b5bcf4d837e2b72baa8b00c3"

CCM auth decrypt tag NIST DVPT AES-256 #32 (P=24, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_auth_decrypt:MBEDTLS_CIPHER_ID_AES:"314a202f836f9f257e22d8c11757832ae5131d357a72df88f3eff0ffcee0da4e":"516c0095cc3d85fd55e48da17c592e0c7014b9daafb82bdc4b41096dfdbe9cc1ab610f8f3e038d16":"8fa501c5dd9ac9b868144c9fa5":"5bb40e3bb72b4509324a7edc852f72535f1f6283156e63f6959ffaf39dcde800":16:MBEDTLS_ERR_CCM_AUTH_FAILED:""

CCM-Camellia encrypt and tag RFC 5528 #1
depends_on:MBEDTLS_CCM_GCM_CAN_CAMELLIA
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_CAMELLIA:"C0C1C2C3C4C5C6C7C8C9CACBCCCDCECF":"08090A0B0C0D0E0F101112131415161718191A1B1C1D1E":"00000003020100A0A1A2A3A4A5":"0001020304050607":"BA737185E719310492F38A5F1251DA55FAFBC949848A0DFCAECE746B3DB9AD"

CCM-Camellia encrypt and tag RFC 5528 #2
depends_on:MBEDTLS_CCM_GCM_CAN_CAMELLIA
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_CAMELLIA:"C0C1C2C3C4C5C6C7C8C9CACBCCCDCECF":"08090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F":"00000004030201A0A1A2A3A4A5":"0001020304050607":"5D2564BF8EAFE1D99526EC016D1BF0424CFBD2CD62848F3360B2295DF24283E8"

CCM-Camellia encrypt and tag RFC 5528 #3
depends_on:MBEDTLS_CCM_GCM_CAN_CAMELLIA
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_CAMELLIA:"C0C1C2C3C4C5C6C7C8C9CACBCCCDCECF":"08090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F20":"00000005040302A0A1A2A3A4A5":"0001020304050607":"81F663D6C7787817F9203608B982AD15DC2BBD87D756F79204F551D6682F23AA46"

CCM-Camellia encrypt and tag RFC 5528 #4
depends_on:MBEDTLS_CCM_GCM_CAN_CAMELLIA
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_CAMELLIA:"C0C1C2C3C4C5C6C7C8C9CACBCCCDCECF":"0C0D0E0F101112131415161718191A1B1C1D1E":"00000006050403A0A1A2A3A4A5":"000102030405060708090A0B":"CAEF1E827211B08F7BD90F08C77288C070A4A08B3A933A63E497A0"

CCM-Camellia encrypt and tag RFC 5528 #5
depends_on:MBEDTLS_CCM_GCM_CAN_CAMELLIA
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_CAMELLIA:"C0C1C2C3C4C5C6C7C8C9CACBCCCDCECF":"0C0D0E0F101112131415161718191A1B1C1D1E1F":"00000007060504A0A1A2A3A4A5":"000102030405060708090A0B":"2AD3BAD94FC52E92BE438E827C1023B96A8A77258FA17BA7F331DB09"

CCM-Camellia encrypt and tag RFC 5528 #6
depends_on:MBEDTLS_CCM_GCM_CAN_CAMELLIA
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_CAMELLIA:"C0C1C2C3C4C5C6C7C8C9CACBCCCDCECF":"0C0D0E0F101112131415161718191A1B1C1D1E1F20":"00000008070605A0A1A2A3A4A5":"000102030405060708090A0B":"FEA5480BA53FA8D3C34422AACE4DE67FFA3BB73BABAB36A1EE4FE0FE28"

CCM-Camellia encrypt and tag RFC 5528 #7
depends_on:MBEDTLS_CCM_GCM_CAN_CAMELLIA
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_CAMELLIA:"C0C1C2C3C4C5C6C7C8C9CACBCCCDCECF":"08090A0B0C0D0E0F101112131415161718191A1B1C1D1E":"00000009080706A0A1A2A3A4A5":"0001020304050607":"54532026E54C119A8D36D9EC6E1ED97416C8708C4B5C2CACAFA3BCCF7A4EBF9573"

CCM-Camellia encrypt and tag RFC 5528 #8
depends_on:MBEDTLS_CCM_GCM_CAN_CAMELLIA
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_CAMELLIA:"C0C1C2C3C4C5C6C7C8C9CACBCCCDCECF":"08090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F":"0000000A090807A0A1A2A3A4A5":"0001020304050607":"8AD19B001A87D148F4D92BEF34525CCCE3A63C6512A6F5757388E4913EF14701F441"

CCM-Camellia encrypt and tag RFC 5528 #9
depends_on:MBEDTLS_CCM_GCM_CAN_CAMELLIA
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_CAMELLIA:"C0C1C2C3C4C5C6C7C8C9CACBCCCDCECF":"08090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F20":"0000000B0A0908A0A1A2A3A4A5":"0001020304050607":"5DB08D62407E6E31D60F9CA2C60474219AC0BE50C0D4A5778794D6E230CD25C9FEBF87"

CCM-Camellia encrypt and tag RFC 5528 #10
depends_on:MBEDTLS_CCM_GCM_CAN_CAMELLIA
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_CAMELLIA:"C0C1C2C3C4C5C6C7C8C9CACBCCCDCECF":"0C0D0E0F101112131415161718191A1B1C1D1E":"0000000C0B0A09A0A1A2A3A4A5":"000102030405060708090A0B":"DB118CCEC1B8761C877CD8963A67D6F3BBBC5CD09299EB11F312F23237"

CCM-Camellia encrypt and tag RFC 5528 #11
depends_on:MBEDTLS_CCM_GCM_CAN_CAMELLIA
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_CAMELLIA:"C0C1C2C3C4C5C6C7C8C9CACBCCCDCECF":"0C0D0E0F101112131415161718191A1B1C1D1E1F":"0000000D0C0B0AA0A1A2A3A4A5":"000102030405060708090A0B":"7CC83D8DC49103525B483DC5CA7EA9AB812B7056079DAFFADA16CCCF2C4E"

CCM-Camellia encrypt and tag RFC 5528 #12
depends_on:MBEDTLS_CCM_GCM_CAN_CAMELLIA
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_CAMELLIA:"C0C1C2C3C4C5C6C7C8C9CACBCCCDCECF":"0C0D0E0F101112131415161718191A1B1C1D1E1F20":"0000000E0D0C0BA0A1A2A3A4A5":"000102030405060708090A0B":"2CD35B8820D23E7AA351B0E92FC79367238B2CC748CBB94C2947793D64AF75"

CCM-Camellia encrypt and tag RFC 5528 #13
depends_on:MBEDTLS_CCM_GCM_CAN_CAMELLIA
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_CAMELLIA:"D75C2778078CA93D971F96FDE720F4CD":"C6B5F3E6CA2311AEF7472B203E735EA561ADB17D56C5A3":"00A970110E1927B160B6A31C1C":"6B7F464507FAE496":"A435D727348DDD22907F7EB8F5FDBB4D939DA6524DB4F64558C02D25B127EE"

CCM-Camellia encrypt and tag RFC 5528 #14
depends_on:MBEDTLS_CCM_GCM_CAN_CAMELLIA
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_CAMELLIA:"D75C2778078CA93D971F96FDE720F4CD":"01F6CE6764C574483BB02E6BBF1E0ABD26A22572B4D80EE7":"0083CD8CE0CB42B160B6A31C1C":"986605B43DF15DE7":"8AE052508FBECA932E346F05E0DC0DFBCF939EAFFA3E587C867D6E1C48703806"

CCM-Camellia encrypt and tag RFC 5528 #15
depends_on:MBEDTLS_CCM_GCM_CAN_CAMELLIA
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_CAMELLIA:"D75C2778078CA93D971F96FDE720F4CD":"CDF1D8406FC2E9014953897005FBFB8BA57276F92404608E08":"005F54950B18F2B160B6A31C1C":"48F2E7E1A7671A51":"08B67EE21C8BF26E473E408599E9C0836D6AF0BB18DF55466CA80878A790476DE5"

CCM-Camellia encrypt and tag RFC 5528 #16
depends_on:MBEDTLS_CCM_GCM_CAN_CAMELLIA
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_CAMELLIA:"D75C2778078CA93D971F96FDE720F4CD":"B005DCFA0B59181426A961685A993D8C43185B":"00EC600863319AB160B6A31C1C":"DE97DF3B8CBD6D8E5030DA4C":"63B78B4967B19EDBB733CD1114F64EB226089368C354828D950CC5"

CCM-Camellia encrypt and tag RFC 5528 #17
depends_on:MBEDTLS_CCM_GCM_CAN_CAMELLIA
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_CAMELLIA:"D75C2778078CA93D971F96FDE720F4CD":"2E20211298105F129D5ED95B93F72D30B2FACCD7":"0060CFF1A31EA1B160B6A31C1C":"A5EE93E457DF05466E782DCF":"0BC6BBE2A8B909F4629EE6DC148DA44410E18AF43147383276F66A9F"

CCM-Camellia encrypt and tag RFC 5528 #18
depends_on:MBEDTLS_CCM_GCM_CAN_CAMELLIA
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_CAMELLIA:"D75C2778078CA93D971F96FDE720F4CD":"2645941E75632D3491AF0FC0C9876C3BE4AA7468C9":"000F85CD995C97B160B6A31C1C":"24AA1BF9A5CD876182A25074":"222AD632FA31D6AF970C345F7E77CA3BD0DC25B340A1A3D31F8D4B44B7"

CCM-Camellia encrypt and tag RFC 5528 #19
depends_on:MBEDTLS_CCM_GCM_CAN_CAMELLIA
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_CAMELLIA:"D75C2778078CA93D971F96FDE720F4CD":"070135A6437C9DB120CD61D8F6C39C3EA125FD95A0D23D":"00C29B2CAAC4CDB160B6A31C1C":"691946B9CA07BE87":"05B8E1B9C49CFD56CF130AA6251DC2ECC06CCC508FE697A0066D57C84BEC182768"

CCM-Camellia encrypt and tag RFC 5528 #20
depends_on:MBEDTLS_CCM_GCM_CAN_CAMELLIA
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_CAMELLIA:"D75C2778078CA93D971F96FDE720F4CD":"C8C0880E6C636E20093DD6594217D2E18877DB264E71A5CC":"002C6B7595EE62B160B6A31C1C":"D0C54ECB84627DC4":"54CEB968DEE23611575EC003DFAA1CD48849BDF5AE2EDB6B7FA775B150ED4383C5A9"

CCM-Camellia encrypt and tag RFC 5528 #21
depends_on:MBEDTLS_CCM_GCM_CAN_CAMELLIA
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_CAMELLIA:"D75C2778078CA93D971F96FDE720F4CD":"F75DAA0710C4E64297794DC2B7D2A20757B1AA4E448002FFAB":"00C53CD4C2AA24B160B6A31C1C":"E285E0E4808CDA3D":"B1404546BF667210CA28E309B39BD6CA7E9FC8285FE698D43CD20A02E0BDCAED2010D3"

CCM-Camellia encrypt and tag RFC 5528 #22
depends_on:MBEDTLS_CCM_GCM_CAN_CAMELLIA
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_CAMELLIA:"D75C2778078CA93D971F96FDE720F4CD":"C238822FAC5F98FF929405B0AD127A4E41854E":"00BEE9267FBADCB160B6A31C1C":"6CAEF9941141570D7C813405":"94C8959C11569A297831A721005857AB61B87A2DEA0936B6EB5F625F5D"

CCM-Camellia encrypt and tag RFC 5528 #23
depends_on:MBEDTLS_CCM_GCM_CAN_CAMELLIA
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_CAMELLIA:"D75C2778078CA93D971F96FDE720F4CD":"4DBF3E774AD245E5D5891F9D1C32A0AE022C85D7":"00DFA8B1245007B160B6A31C1C":"36A52CF16B19A2037AB7011E":"5869E3AAD2447C74E0FC05F9A4EA74577F4DE8CA8924764296AD04119CE7"

CCM-Camellia encrypt and tag RFC 5528 #24
depends_on:MBEDTLS_CCM_GCM_CAN_CAMELLIA
mbedtls_ccm_encrypt_and_tag:MBEDTLS_CIPHER_ID_CAMELLIA:"D75C2778078CA93D971F96FDE720F4CD":"9DC9EDAE2FF5DF8636E8C6DE0EED55F7867E33337D":"003B8FD8D3A937B160B6A31C1C":"A4D499F78419728C19178B0C":"4B198156393B0F7796086AAFB454F8C3F034CCA966945F1FCEA7E11BEE6A2F"

CCM encrypt, skip ad AES-128 (P=0, N=13, A=0, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_skip_ad:MBEDTLS_CIPHER_ID_AES:MBEDTLS_CCM_ENCRYPT:"54caf96ef6d448734700aadab50faf7a":"":"a3803e752ae849c910d8da36af":"":"eba8347baa6d61f87b67c2dd7c6d2053"

CCM* encrypt, skip ad AES-128 (P=0, N=13, A=0, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_skip_ad:MBEDTLS_CIPHER_ID_AES:MBEDTLS_CCM_STAR_ENCRYPT:"54caf96ef6d448734700aadab50faf7a":"":"a3803e752ae849c910d8da36af":"":"eba8347baa6d61f87b67c2dd7c6d2053"

CCM decrypt, skip ad AES-128 (P=0, N=13, A=0, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_skip_ad:MBEDTLS_CIPHER_ID_AES:MBEDTLS_CCM_DECRYPT:"54caf96ef6d448734700aadab50faf7a":"":"a3803e752ae849c910d8da36af":"":"eba8347baa6d61f87b67c2dd7c6d2053"

CCM* decrypt, skip ad AES-128 (P=0, N=13, A=0, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_skip_ad:MBEDTLS_CIPHER_ID_AES:MBEDTLS_CCM_STAR_DECRYPT:"54caf96ef6d448734700aadab50faf7a":"":"a3803e752ae849c910d8da36af":"":"eba8347baa6d61f87b67c2dd7c6d2053"

CCM encrypt, skip ad NIST VADT AES-128 (P=24, N=13, A=0, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_skip_ad:MBEDTLS_CIPHER_ID_AES:MBEDTLS_CCM_ENCRYPT:"d24a3d3dde8c84830280cb87abad0bb3":"7c86135ed9c2a515aaae0e9a208133897269220f30870006":"f1100035bb24a8d26004e0e24b":"1faeb0ee2ca2cd52f0aa3966578344f24e69b742c4ab37ab":"1123301219c70599b7c373ad4b3ad67b"

CCM* encrypt, skip ad NIST VADT AES-128 (P=24, N=13, A=0, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_skip_ad:MBEDTLS_CIPHER_ID_AES:MBEDTLS_CCM_STAR_ENCRYPT:"d24a3d3dde8c84830280cb87abad0bb3":"7c86135ed9c2a515aaae0e9a208133897269220f30870006":"f1100035bb24a8d26004e0e24b":"1faeb0ee2ca2cd52f0aa3966578344f24e69b742c4ab37ab":"1123301219c70599b7c373ad4b3ad67b"

CCM decrypt, skip ad NIST DVPT AES-192 (P=24, N=7, A=0, T=4)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_skip_ad:MBEDTLS_CIPHER_ID_AES:MBEDTLS_CCM_DECRYPT:"19ebfde2d5468ba0a3031bde629b11fd4094afcb205393fa":"411986d04d6463100bff03f7d0bde7ea2c3488784378138c":"5a8aa485c316e9":"3796cf51b8726652a4204733b8fbb047cf00fb91a9837e22":"ddc93a54"

CCM* decrypt, skip ad NIST DVPT AES-192 (P=24, N=7, A=0, T=4)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_skip_ad:MBEDTLS_CIPHER_ID_AES:MBEDTLS_CCM_STAR_DECRYPT:"19ebfde2d5468ba0a3031bde629b11fd4094afcb205393fa":"411986d04d6463100bff03f7d0bde7ea2c3488784378138c":"5a8aa485c316e9":"3796cf51b8726652a4204733b8fbb047cf00fb91a9837e22":"ddc93a54"

CCM encrypt, skip update AES-128 (P=0, N=13, A=0, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_skip_update:MBEDTLS_CIPHER_ID_AES:MBEDTLS_CCM_ENCRYPT:"54caf96ef6d448734700aadab50faf7a":"a3803e752ae849c910d8da36af":"":"eba8347baa6d61f87b67c2dd7c6d2053"

CCM decrypt, skip update AES-128 (P=0, N=13, A=0, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_skip_update:MBEDTLS_CIPHER_ID_AES:MBEDTLS_CCM_DECRYPT:"54caf96ef6d448734700aadab50faf7a":"a3803e752ae849c910d8da36af":"":"eba8347baa6d61f87b67c2dd7c6d2053"

CCM* encrypt, skip update AES-128 (P=0, N=13, A=0, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_skip_update:MBEDTLS_CIPHER_ID_AES:MBEDTLS_CCM_STAR_ENCRYPT:"54caf96ef6d448734700aadab50faf7a":"a3803e752ae849c910d8da36af":"":"eba8347baa6d61f87b67c2dd7c6d2053"

CCM* decrypt, skip update AES-128 (P=0, N=13, A=0, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_skip_update:MBEDTLS_CIPHER_ID_AES:MBEDTLS_CCM_STAR_DECRYPT:"54caf96ef6d448734700aadab50faf7a":"a3803e752ae849c910d8da36af":"":"eba8347baa6d61f87b67c2dd7c6d2053"

CCM encrypt, skip update NIST VPT AES-128 #1 (P=0, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_skip_update:MBEDTLS_CIPHER_ID_AES:MBEDTLS_CCM_ENCRYPT:"2ebf60f0969013a54a3dedb19d20f6c8":"1de8c5e21f9db33123ff870add":"e1de6c6119d7db471136285d10b47a450221b16978569190ef6a22b055295603":"0ead29ef205fbb86d11abe5ed704b880"

CCM* encrypt, skip update NIST VPT AES-128 #1 (P=0, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_skip_update:MBEDTLS_CIPHER_ID_AES:MBEDTLS_CCM_STAR_ENCRYPT:"2ebf60f0969013a54a3dedb19d20f6c8":"1de8c5e21f9db33123ff870add":"e1de6c6119d7db471136285d10b47a450221b16978569190ef6a22b055295603":"0ead29ef205fbb86d11abe5ed704b880"

CCM decrypt, skip update NIST DVPT AES-256 #23 (P=0, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_skip_update:MBEDTLS_CIPHER_ID_AES:MBEDTLS_CCM_DECRYPT:"8c5cf3457ff22228c39c051c4e05ed4093657eb303f859a9d4b0f8be0127d88a":"a544218dadd3c10583db49cf39":"3c0e2815d37d844f7ac240ba9d6e3a0b2a86f706e885959e09a1005e024f6907":"867b0d87cf6e0f718200a97b4f6d5ad5"

CCM* decrypt, skip update NIST DVPT AES-256 #23 (P=0, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_skip_update:MBEDTLS_CIPHER_ID_AES:MBEDTLS_CCM_STAR_DECRYPT:"8c5cf3457ff22228c39c051c4e05ed4093657eb303f859a9d4b0f8be0127d88a":"a544218dadd3c10583db49cf39":"3c0e2815d37d844f7ac240ba9d6e3a0b2a86f706e885959e09a1005e024f6907":"867b0d87cf6e0f718200a97b4f6d5ad5"

CCM encrypt, overflow ad NIST VPT AES-128 #14 (P=13, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_overflow_ad:MBEDTLS_CIPHER_ID_AES:MBEDTLS_CCM_ENCRYPT:"d32088d50df9aba14d9022c870a0cb85":"e16c69861efc206e85aab1255e":"a6f73242f2f227350c0277e4e72cdaa6"

CCM encrypt, incomplete ad NIST VPT AES-128 #14 (P=13, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_incomplete_ad:MBEDTLS_CIPHER_ID_AES:MBEDTLS_CCM_ENCRYPT:"d32088d50df9aba14d9022c870a0cb85":"e16c69861efc206e85aab1255e":"a6f73242f2f227350c0277e4e72cdaa6"

CCM encrypt, full ad and overflow NIST VPT AES-128 #14 (P=13, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_full_ad_and_overflow:MBEDTLS_CIPHER_ID_AES:MBEDTLS_CCM_ENCRYPT:"d32088d50df9aba14d9022c870a0cb85":"e16c69861efc206e85aab1255e":"a6f73242f2f227350c0277e4e72cdaa6"

CCM encrypt, incomplete ad and overflow NIST VPT AES-128 #14 (P=13, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_incomplete_ad_and_overflow:MBEDTLS_CIPHER_ID_AES:MBEDTLS_CCM_ENCRYPT:"d32088d50df9aba14d9022c870a0cb85":"e16c69861efc206e85aab1255e":"a6f73242f2f227350c0277e4e72cdaa6"

CCM encrypt, overflow update NIST VPT AES-128 #14 (P=13, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_overflow_update:MBEDTLS_CIPHER_ID_AES:MBEDTLS_CCM_ENCRYPT:"d32088d50df9aba14d9022c870a0cb85":"4b10788c1a03bca656f04f1f98":"e16c69861efc206e85aab1255e":"0eff7d7bcceb873c3203a8df74f4e91b04bd607ec11202f96cfeb99f5bcdb7aa"

CCM encrypt, incomplete update NIST VPT AES-128 #14 (P=13, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_incomplete_update:MBEDTLS_CIPHER_ID_AES:MBEDTLS_CCM_ENCRYPT:"d32088d50df9aba14d9022c870a0cb85":"4b10788c1a03bca656f04f1f98":"e16c69861efc206e85aab1255e":"0eff7d7bcceb873c3203a8df74f4e91b04bd607ec11202f96cfeb99f5bcdb7aa"

CCM encrypt, full update and overflow NIST VPT AES-128 #14 (P=13, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_full_update_and_overflow:MBEDTLS_CIPHER_ID_AES:MBEDTLS_CCM_ENCRYPT:"d32088d50df9aba14d9022c870a0cb85":"4b10788c1a03bca656f04f1f98":"e16c69861efc206e85aab1255e":"0eff7d7bcceb873c3203a8df74f4e91b04bd607ec11202f96cfeb99f5bcdb7aa"

CCM encrypt, incomplete update and overflow NIST VPT AES-128 #14 (P=13, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_incomplete_update_overflow:MBEDTLS_CIPHER_ID_AES:MBEDTLS_CCM_ENCRYPT:"d32088d50df9aba14d9022c870a0cb85":"4b10788c1a03bca656f04f1f98":"e16c69861efc206e85aab1255e":"0eff7d7bcceb873c3203a8df74f4e91b04bd607ec11202f96cfeb99f5bcdb7aa"

CCM decrypt, overflow ad NIST VPT AES-128 #14 (P=13, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_overflow_ad:MBEDTLS_CIPHER_ID_AES:MBEDTLS_CCM_DECRYPT:"d32088d50df9aba14d9022c870a0cb85":"e16c69861efc206e85aab1255e":"a6f73242f2f227350c0277e4e72cdaa6"

CCM decrypt, incomplete ad NIST VPT AES-128 #14 (P=13, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_incomplete_ad:MBEDTLS_CIPHER_ID_AES:MBEDTLS_CCM_DECRYPT:"d32088d50df9aba14d9022c870a0cb85":"e16c69861efc206e85aab1255e":"a6f73242f2f227350c0277e4e72cdaa6"

CCM decrypt, full ad and overflow NIST VPT AES-128 #14 (P=13, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_full_ad_and_overflow:MBEDTLS_CIPHER_ID_AES:MBEDTLS_CCM_DECRYPT:"d32088d50df9aba14d9022c870a0cb85":"e16c69861efc206e85aab1255e":"a6f73242f2f227350c0277e4e72cdaa6"

CCM decrypt, incomplete ad and overflow NIST VPT AES-128 #14 (P=13, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_incomplete_ad_and_overflow:MBEDTLS_CIPHER_ID_AES:MBEDTLS_CCM_DECRYPT:"d32088d50df9aba14d9022c870a0cb85":"e16c69861efc206e85aab1255e":"a6f73242f2f227350c0277e4e72cdaa6"

CCM decrypt, overflow update NIST VPT AES-128 #14 (P=13, N=13, A=32, T=16))
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_overflow_update:MBEDTLS_CIPHER_ID_AES:MBEDTLS_CCM_DECRYPT:"d32088d50df9aba14d9022c870a0cb85":"4b10788c1a03bca656f04f1f98":"e16c69861efc206e85aab1255e":"0eff7d7bcceb873c3203a8df74f4e91b04bd607ec11202f96cfeb99f5bcdb7aa"

CCM decrypt, incomplete update NIST VPT AES-128 #14 (P=13, N=13, A=32, T=16))
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_incomplete_update:MBEDTLS_CIPHER_ID_AES:MBEDTLS_CCM_DECRYPT:"d32088d50df9aba14d9022c870a0cb85":"4b10788c1a03bca656f04f1f98":"e16c69861efc206e85aab1255e":"0eff7d7bcceb873c3203a8df74f4e91b04bd607ec11202f96cfeb99f5bcdb7aa"

CCM decrypt, full update and overflow NIST VPT AES-128 #14 (P=13, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_full_update_and_overflow:MBEDTLS_CIPHER_ID_AES:MBEDTLS_CCM_DECRYPT:"d32088d50df9aba14d9022c870a0cb85":"4b10788c1a03bca656f04f1f98":"e16c69861efc206e85aab1255e":"0eff7d7bcceb873c3203a8df74f4e91b04bd607ec11202f96cfeb99f5bcdb7aa"

CCM decrypt, incomplete update and overflow NIST VPT AES-128 #14 (P=13, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_incomplete_update_overflow:MBEDTLS_CIPHER_ID_AES:MBEDTLS_CCM_DECRYPT:"d32088d50df9aba14d9022c870a0cb85":"4b10788c1a03bca656f04f1f98":"e16c69861efc206e85aab1255e":"0eff7d7bcceb873c3203a8df74f4e91b04bd607ec11202f96cfeb99f5bcdb7aa"

CCM* encrypt, overflow ad NIST VPT AES-128 #14 (P=13, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_overflow_ad:MBEDTLS_CIPHER_ID_AES:MBEDTLS_CCM_STAR_ENCRYPT:"d32088d50df9aba14d9022c870a0cb85":"e16c69861efc206e85aab1255e":"a6f73242f2f227350c0277e4e72cdaa6"

CCM* encrypt, incomplete ad NIST VPT AES-128 #14 (P=13, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_incomplete_ad:MBEDTLS_CIPHER_ID_AES:MBEDTLS_CCM_STAR_ENCRYPT:"d32088d50df9aba14d9022c870a0cb85":"e16c69861efc206e85aab1255e":"a6f73242f2f227350c0277e4e72cdaa6"

CCM* encrypt, full ad and overflow NIST VPT AES-128 #14 (P=13, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_full_ad_and_overflow:MBEDTLS_CIPHER_ID_AES:MBEDTLS_CCM_STAR_ENCRYPT:"d32088d50df9aba14d9022c870a0cb85":"e16c69861efc206e85aab1255e":"a6f73242f2f227350c0277e4e72cdaa6"

CCM* encrypt, overflow update NIST VPT AES-128 #14 (P=13, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_overflow_update:MBEDTLS_CIPHER_ID_AES:MBEDTLS_CCM_STAR_ENCRYPT:"d32088d50df9aba14d9022c870a0cb85":"4b10788c1a03bca656f04f1f98":"e16c69861efc206e85aab1255e":"0eff7d7bcceb873c3203a8df74f4e91b04bd607ec11202f96cfeb99f5bcdb7aa"

CCM* encrypt, incomplete ad and overflow NIST VPT AES-128 #14 (P=13, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_incomplete_ad_and_overflow:MBEDTLS_CIPHER_ID_AES:MBEDTLS_CCM_STAR_ENCRYPT:"d32088d50df9aba14d9022c870a0cb85":"e16c69861efc206e85aab1255e":"a6f73242f2f227350c0277e4e72cdaa6"

CCM* encrypt, incomplete update NIST VPT AES-128 #14 (P=13, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_incomplete_update:MBEDTLS_CIPHER_ID_AES:MBEDTLS_CCM_STAR_ENCRYPT:"d32088d50df9aba14d9022c870a0cb85":"4b10788c1a03bca656f04f1f98":"e16c69861efc206e85aab1255e":"0eff7d7bcceb873c3203a8df74f4e91b04bd607ec11202f96cfeb99f5bcdb7aa"

CCM* encrypt, full update and overflow NIST VPT AES-128 #14 (P=13, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_full_update_and_overflow:MBEDTLS_CIPHER_ID_AES:MBEDTLS_CCM_STAR_ENCRYPT:"d32088d50df9aba14d9022c870a0cb85":"4b10788c1a03bca656f04f1f98":"e16c69861efc206e85aab1255e":"0eff7d7bcceb873c3203a8df74f4e91b04bd607ec11202f96cfeb99f5bcdb7aa"

CCM* encrypt, incomplete update and overflow NIST VPT AES-128 #14 (P=13, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_incomplete_update_overflow:MBEDTLS_CIPHER_ID_AES:MBEDTLS_CCM_STAR_ENCRYPT:"d32088d50df9aba14d9022c870a0cb85":"4b10788c1a03bca656f04f1f98":"e16c69861efc206e85aab1255e":"0eff7d7bcceb873c3203a8df74f4e91b04bd607ec11202f96cfeb99f5bcdb7aa"

CCM* decrypt, overflow ad NIST VPT AES-128 #14 (P=13, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_overflow_ad:MBEDTLS_CIPHER_ID_AES:MBEDTLS_CCM_STAR_DECRYPT:"d32088d50df9aba14d9022c870a0cb85":"e16c69861efc206e85aab1255e":"a6f73242f2f227350c0277e4e72cdaa6"

CCM* decrypt, incomplete ad NIST VPT AES-128 #14 (P=13, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_incomplete_ad:MBEDTLS_CIPHER_ID_AES:MBEDTLS_CCM_STAR_DECRYPT:"d32088d50df9aba14d9022c870a0cb85":"e16c69861efc206e85aab1255e":"a6f73242f2f227350c0277e4e72cdaa6"

CCM* decrypt, full ad and overflow NIST VPT AES-128 #14 (P=13, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_full_ad_and_overflow:MBEDTLS_CIPHER_ID_AES:MBEDTLS_CCM_STAR_DECRYPT:"d32088d50df9aba14d9022c870a0cb85":"e16c69861efc206e85aab1255e":"a6f73242f2f227350c0277e4e72cdaa6"

CCM* decrypt, incomplete ad and overflow NIST VPT AES-128 #14 (P=13, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_incomplete_ad_and_overflow:MBEDTLS_CIPHER_ID_AES:MBEDTLS_CCM_STAR_DECRYPT:"d32088d50df9aba14d9022c870a0cb85":"e16c69861efc206e85aab1255e":"a6f73242f2f227350c0277e4e72cdaa6"

CCM* decrypt, overflow update NIST VPT AES-128 #14 (P=13, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_overflow_update:MBEDTLS_CIPHER_ID_AES:MBEDTLS_CCM_STAR_DECRYPT:"d32088d50df9aba14d9022c870a0cb85":"4b10788c1a03bca656f04f1f98":"e16c69861efc206e85aab1255e":"0eff7d7bcceb873c3203a8df74f4e91b04bd607ec11202f96cfeb99f5bcdb7aa"

CCM* decrypt, incomplete update NIST VPT AES-128 #14 (P=13, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_incomplete_update:MBEDTLS_CIPHER_ID_AES:MBEDTLS_CCM_STAR_DECRYPT:"d32088d50df9aba14d9022c870a0cb85":"4b10788c1a03bca656f04f1f98":"e16c69861efc206e85aab1255e":"0eff7d7bcceb873c3203a8df74f4e91b04bd607ec11202f96cfeb99f5bcdb7aa"

CCM* decrypt, full update and overflow NIST VPT AES-128 #14 (P=13, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_full_update_and_overflow:MBEDTLS_CIPHER_ID_AES:MBEDTLS_CCM_STAR_DECRYPT:"d32088d50df9aba14d9022c870a0cb85":"4b10788c1a03bca656f04f1f98":"e16c69861efc206e85aab1255e":"0eff7d7bcceb873c3203a8df74f4e91b04bd607ec11202f96cfeb99f5bcdb7aa"

CCM* decrypt, incomplete update and overflow NIST VPT AES-128 #14 (P=13, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_incomplete_update_overflow:MBEDTLS_CIPHER_ID_AES:MBEDTLS_CCM_STAR_DECRYPT:"d32088d50df9aba14d9022c870a0cb85":"4b10788c1a03bca656f04f1f98":"e16c69861efc206e85aab1255e":"0eff7d7bcceb873c3203a8df74f4e91b04bd607ec11202f96cfeb99f5bcdb7aa"

CCM encrypt, instant finish NIST VPT AES-128 #14 (P=13, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_instant_finish:MBEDTLS_CIPHER_ID_AES:MBEDTLS_CCM_ENCRYPT:"d32088d50df9aba14d9022c870a0cb85":"4b10788c1a03bca656f04f1f98"

CCM decrypt, instant finish NIST VPT AES-128 #14 (P=13, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_instant_finish:MBEDTLS_CIPHER_ID_AES:MBEDTLS_CCM_DECRYPT:"d32088d50df9aba14d9022c870a0cb85":"4b10788c1a03bca656f04f1f98"

CCM* encrypt, instant finish NIST VPT AES-128 #14 (P=13, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_instant_finish:MBEDTLS_CIPHER_ID_AES:MBEDTLS_CCM_STAR_ENCRYPT:"d32088d50df9aba14d9022c870a0cb85":"4b10788c1a03bca656f04f1f98"

CCM* decrypt, instant finish NIST VPT AES-128 #14 (P=13, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_instant_finish:MBEDTLS_CIPHER_ID_AES:MBEDTLS_CCM_STAR_DECRYPT:"d32088d50df9aba14d9022c870a0cb85":"4b10788c1a03bca656f04f1f98"

CCM encrypt, instant finish AES-128 (P=0, N=13, A=0, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_instant_finish:MBEDTLS_CIPHER_ID_AES:MBEDTLS_CCM_ENCRYPT:"54caf96ef6d448734700aadab50faf7a":"a3803e752ae849c910d8da36af"

CCM decrypt, instant finish AES-128 (P=0, N=13, A=0, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_instant_finish:MBEDTLS_CIPHER_ID_AES:MBEDTLS_CCM_DECRYPT:"54caf96ef6d448734700aadab50faf7a":"a3803e752ae849c910d8da36af"

CCM* encrypt, instant finish AES-128 (P=0, N=13, A=0, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_instant_finish:MBEDTLS_CIPHER_ID_AES:MBEDTLS_CCM_STAR_ENCRYPT:"54caf96ef6d448734700aadab50faf7a":"a3803e752ae849c910d8da36af"

CCM* decrypt, instant finish AES-128 (P=0, N=13, A=0, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_instant_finish:MBEDTLS_CIPHER_ID_AES:MBEDTLS_CCM_STAR_DECRYPT:"54caf96ef6d448734700aadab50faf7a":"a3803e752ae849c910d8da36af"

CCM pass unexpected auth data, NIST VPT AES-128 #14 (P=13, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_unexpected_ad::MBEDTLS_CIPHER_ID_AES:MBEDTLS_CCM_ENCRYPT:"d32088d50df9aba14d9022c870a0cb85":"e16c69861efc206e85aab1255e":"0eff7d7bcceb873c3203a8df74f4e91b04bd607ec11202f96cfeb99f5bcdb7aa"

CCM encrypt, unexpected ciphertext/plaintext data, NIST VPT AES-128 #14 (P=13, N=13, A=32, T=16)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_unexpected_text:MBEDTLS_CIPHER_ID_AES:MBEDTLS_CCM_ENCRYPT:"d32088d50df9aba14d9022c870a0cb85":"4b10788c1a03bca656f04f1f98":"e16c69861efc206e85aab1255e":"0eff7d7bcceb873c3203a8df74f4e91b04bd607ec11202f96cfeb99f5bcdb7aa"

CCM* encrypt, no auth NIST VADT AES-256 #1 (P=24, N=13)
depends_on:MBEDTLS_CCM_GCM_CAN_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mbedtls_ccm_star_no_tag:MBEDTLS_CIPHER_ID_AES:MBEDTLS_CCM_STAR_ENCRYPT:"26511fb51fcfa75cb4b44da75a6e5a0eb8d9c8f3b906f886df3ba3e6da3a1389":"30d56ff2a25b83fee791110fcaea48e41db7c7f098a81000":"72a60f345a1978fb40f28a2fa4":"55f068c0bbba8b598013dd1841fd740fda2902322148ab5e"

CCM* decrypt, no auth NIST DVPT AES-128 #15 (P=24, N=13)
depends_on:MBEDTLS_CCM_GCM_CAN_AES
mbedtls_ccm_star_no_tag:MBEDTLS_CIPHER_ID_AES:MBEDTLS_CCM_STAR_DECRYPT:"90929a4b0ac65b350ad1591611fe4829":"4bfe4e35784f0a65b545477e5e2f4bae0e1e6fa717eaf2cb":"5a8aa485c316e9403aff859fbb":"a16a2e741f1cd9717285b6d882c1fc53655e9773761ad697"
