Hash: MD5
depends_on:PSA_WANT_ALG_MD5
hash_algorithm:PSA_ALG_MD5:16

Hash: RIPEMD160
depends_on:PSA_WANT_ALG_RIPEMD160
hash_algorithm:PSA_ALG_RIPEMD160:20

Hash: SHA-1
depends_on:PSA_WANT_ALG_SHA_1
hash_algorithm:PSA_ALG_SHA_1:20

Hash: SHA-2 SHA-224
depends_on:PSA_WANT_ALG_SHA_224
hash_algorithm:PSA_ALG_SHA_224:28

Hash: SHA-2 SHA-256
depends_on:PSA_WANT_ALG_SHA_256
hash_algorithm:PSA_ALG_SHA_256:32

Hash: SHA-2 SHA-384
depends_on:PSA_WANT_ALG_SHA_384
hash_algorithm:PSA_ALG_SHA_384:48

Hash: SHA-2 SHA-512
depends_on:PSA_WANT_ALG_SHA_512
hash_algorithm:PSA_ALG_SHA_512:64

Hash: SHA-3 SHA3-224
depends_on:PSA_WANT_ALG_SHA3_224
hash_algorithm:PSA_ALG_SHA3_224:28

Hash: SHA-3 SHA3-256
depends_on:PSA_WANT_ALG_SHA3_256
hash_algorithm:PSA_ALG_SHA3_256:32

Hash: SHA-3 SHA3-384
depends_on:PSA_WANT_ALG_SHA3_384
hash_algorithm:PSA_ALG_SHA3_384:48

Hash: SHA-3 SHA3-512
depends_on:PSA_WANT_ALG_SHA3_512
hash_algorithm:PSA_ALG_SHA3_512:64

MAC: HMAC-MD5
depends_on:PSA_WANT_ALG_HMAC:PSA_WANT_ALG_MD5
hmac_algorithm:PSA_ALG_HMAC( PSA_ALG_MD5 ):16:64

MAC: HMAC-RIPEMD160
depends_on:PSA_WANT_ALG_HMAC:PSA_WANT_ALG_RIPEMD160
hmac_algorithm:PSA_ALG_HMAC( PSA_ALG_RIPEMD160 ):20:64

MAC: HMAC-SHA-1
depends_on:PSA_WANT_ALG_HMAC:PSA_WANT_ALG_SHA_1
hmac_algorithm:PSA_ALG_HMAC( PSA_ALG_SHA_1 ):20:64

MAC: HMAC-SHA-224
depends_on:PSA_WANT_ALG_HMAC:PSA_WANT_ALG_SHA_224
hmac_algorithm:PSA_ALG_HMAC( PSA_ALG_SHA_224 ):28:64

MAC: HMAC-SHA-256
depends_on:PSA_WANT_ALG_HMAC:PSA_WANT_ALG_SHA_256
hmac_algorithm:PSA_ALG_HMAC( PSA_ALG_SHA_256 ):32:64

MAC: HMAC-SHA-384
depends_on:PSA_WANT_ALG_HMAC:PSA_WANT_ALG_SHA_384
hmac_algorithm:PSA_ALG_HMAC( PSA_ALG_SHA_384 ):48:128

MAC: HMAC-SHA-512
depends_on:PSA_WANT_ALG_HMAC:PSA_WANT_ALG_SHA_512
hmac_algorithm:PSA_ALG_HMAC( PSA_ALG_SHA_512 ):64:128

MAC: CBC_MAC-AES-128
depends_on:PSA_WANT_ALG_CBC_MAC:PSA_WANT_KEY_TYPE_AES
mac_algorithm:PSA_ALG_CBC_MAC:ALG_IS_BLOCK_CIPHER_MAC:16:PSA_KEY_TYPE_AES:128

MAC: CBC_MAC-AES-192
depends_on:PSA_WANT_ALG_CBC_MAC:PSA_WANT_KEY_TYPE_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mac_algorithm:PSA_ALG_CBC_MAC:ALG_IS_BLOCK_CIPHER_MAC:16:PSA_KEY_TYPE_AES:192

MAC: CBC_MAC-AES-256
depends_on:PSA_WANT_ALG_CBC_MAC:PSA_WANT_KEY_TYPE_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mac_algorithm:PSA_ALG_CBC_MAC:ALG_IS_BLOCK_CIPHER_MAC:16:PSA_KEY_TYPE_AES:256

MAC: CBC_MAC-3DES
depends_on:PSA_WANT_ALG_CBC_MAC:PSA_WANT_KEY_TYPE_DES
mac_algorithm:PSA_ALG_CBC_MAC:ALG_IS_BLOCK_CIPHER_MAC:8:PSA_KEY_TYPE_DES:192

MAC: CMAC-AES-128
depends_on:PSA_WANT_ALG_CMAC:PSA_WANT_KEY_TYPE_AES
mac_algorithm:PSA_ALG_CMAC:ALG_IS_BLOCK_CIPHER_MAC:16:PSA_KEY_TYPE_AES:128

MAC: CMAC-AES-192
depends_on:PSA_WANT_ALG_CMAC:PSA_WANT_KEY_TYPE_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mac_algorithm:PSA_ALG_CMAC:ALG_IS_BLOCK_CIPHER_MAC:16:PSA_KEY_TYPE_AES:192

MAC: CMAC-AES-256
depends_on:PSA_WANT_ALG_CMAC:PSA_WANT_KEY_TYPE_AES:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
mac_algorithm:PSA_ALG_CMAC:ALG_IS_BLOCK_CIPHER_MAC:16:PSA_KEY_TYPE_AES:256

MAC: CMAC-3DES
depends_on:PSA_WANT_ALG_CMAC:PSA_WANT_KEY_TYPE_DES
mac_algorithm:PSA_ALG_CMAC:ALG_IS_BLOCK_CIPHER_MAC:8:PSA_KEY_TYPE_DES:192

Cipher: STREAM_CIPHER
depends_on:PSA_WANT_ALG_STREAM_CIPHER
cipher_algorithm:PSA_ALG_STREAM_CIPHER:ALG_IS_STREAM_CIPHER

Cipher: CTR
depends_on:PSA_WANT_ALG_CTR
cipher_algorithm:PSA_ALG_CTR:ALG_IS_STREAM_CIPHER

Cipher: CFB
depends_on:PSA_WANT_ALG_CFB
cipher_algorithm:PSA_ALG_CFB:ALG_IS_STREAM_CIPHER

Cipher: OFB
depends_on:PSA_WANT_ALG_OFB
cipher_algorithm:PSA_ALG_OFB:ALG_IS_STREAM_CIPHER

Cipher: ECB-nopad
depends_on:PSA_WANT_ALG_ECB_NO_PADDING
cipher_algorithm:PSA_ALG_ECB_NO_PADDING:0

Cipher: CBC-nopad
depends_on:PSA_WANT_ALG_CBC_NO_PADDING
cipher_algorithm:PSA_ALG_CBC_NO_PADDING:0

Cipher: CBC-PKCS#7
depends_on:PSA_WANT_ALG_CBC_PKCS7
cipher_algorithm:PSA_ALG_CBC_PKCS7:0

Cipher: XTS
depends_on:PSA_WANT_ALG_XTS
cipher_algorithm:PSA_ALG_XTS:0

Cipher: CCM*
depends_on:PSA_WANT_ALG_CCM_STAR_NO_TAG
cipher_algorithm:PSA_ALG_CCM_STAR_NO_TAG:ALG_IS_STREAM_CIPHER

AEAD: CCM-AES-128
depends_on:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CCM
aead_algorithm:PSA_ALG_CCM:ALG_IS_AEAD_ON_BLOCK_CIPHER:16:PSA_KEY_TYPE_AES:128

AEAD: CCM-AES-192
depends_on:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CCM:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
aead_algorithm:PSA_ALG_CCM:ALG_IS_AEAD_ON_BLOCK_CIPHER:16:PSA_KEY_TYPE_AES:192

AEAD: CCM-AES-256
depends_on:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CCM:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
aead_algorithm:PSA_ALG_CCM:ALG_IS_AEAD_ON_BLOCK_CIPHER:16:PSA_KEY_TYPE_AES:256

AEAD: CCM-ARIA-128
depends_on:PSA_WANT_KEY_TYPE_ARIA:PSA_WANT_ALG_CCM
aead_algorithm:PSA_ALG_CCM:ALG_IS_AEAD_ON_BLOCK_CIPHER:16:PSA_KEY_TYPE_ARIA:128

AEAD: CCM-ARIA-192
depends_on:PSA_WANT_KEY_TYPE_ARIA:PSA_WANT_ALG_CCM
aead_algorithm:PSA_ALG_CCM:ALG_IS_AEAD_ON_BLOCK_CIPHER:16:PSA_KEY_TYPE_ARIA:192

AEAD: CCM-ARIA-256
depends_on:PSA_WANT_KEY_TYPE_ARIA:PSA_WANT_ALG_CCM
aead_algorithm:PSA_ALG_CCM:ALG_IS_AEAD_ON_BLOCK_CIPHER:16:PSA_KEY_TYPE_ARIA:256

AEAD: CCM-CAMELLIA-128
depends_on:PSA_WANT_KEY_TYPE_CAMELLIA:PSA_WANT_ALG_CCM
aead_algorithm:PSA_ALG_CCM:ALG_IS_AEAD_ON_BLOCK_CIPHER:16:PSA_KEY_TYPE_CAMELLIA:128

AEAD: CCM-CAMELLIA-192
depends_on:PSA_WANT_KEY_TYPE_CAMELLIA:PSA_WANT_ALG_CCM
aead_algorithm:PSA_ALG_CCM:ALG_IS_AEAD_ON_BLOCK_CIPHER:16:PSA_KEY_TYPE_CAMELLIA:192

AEAD: CCM-CAMELLIA-256
depends_on:PSA_WANT_KEY_TYPE_CAMELLIA:PSA_WANT_ALG_CCM
aead_algorithm:PSA_ALG_CCM:ALG_IS_AEAD_ON_BLOCK_CIPHER:16:PSA_KEY_TYPE_CAMELLIA:256

AEAD: GCM-AES-128
depends_on:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_GCM
aead_algorithm:PSA_ALG_GCM:ALG_IS_AEAD_ON_BLOCK_CIPHER:16:PSA_KEY_TYPE_AES:128

AEAD: GCM-AES-192
depends_on:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_GCM:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
aead_algorithm:PSA_ALG_GCM:ALG_IS_AEAD_ON_BLOCK_CIPHER:16:PSA_KEY_TYPE_AES:192

AEAD: GCM-AES-256
depends_on:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_GCM:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
aead_algorithm:PSA_ALG_GCM:ALG_IS_AEAD_ON_BLOCK_CIPHER:16:PSA_KEY_TYPE_AES:256

AEAD: GCM-ARIA-128
depends_on:PSA_WANT_KEY_TYPE_ARIA:PSA_WANT_ALG_GCM
aead_algorithm:PSA_ALG_GCM:ALG_IS_AEAD_ON_BLOCK_CIPHER:16:PSA_KEY_TYPE_ARIA:128

AEAD: GCM-ARIA-192
depends_on:PSA_WANT_KEY_TYPE_ARIA:PSA_WANT_ALG_GCM
aead_algorithm:PSA_ALG_GCM:ALG_IS_AEAD_ON_BLOCK_CIPHER:16:PSA_KEY_TYPE_ARIA:192

AEAD: GCM-ARIA-256
depends_on:PSA_WANT_KEY_TYPE_ARIA:PSA_WANT_ALG_GCM
aead_algorithm:PSA_ALG_GCM:ALG_IS_AEAD_ON_BLOCK_CIPHER:16:PSA_KEY_TYPE_ARIA:256

AEAD: GCM-CAMELLIA-128
depends_on:PSA_WANT_KEY_TYPE_CAMELLIA:PSA_WANT_ALG_GCM
aead_algorithm:PSA_ALG_GCM:ALG_IS_AEAD_ON_BLOCK_CIPHER:16:PSA_KEY_TYPE_CAMELLIA:128

AEAD: GCM-CAMELLIA-192
depends_on:PSA_WANT_KEY_TYPE_CAMELLIA:PSA_WANT_ALG_GCM
aead_algorithm:PSA_ALG_GCM:ALG_IS_AEAD_ON_BLOCK_CIPHER:16:PSA_KEY_TYPE_CAMELLIA:192

AEAD: GCM-CAMELLIA-256
depends_on:PSA_WANT_KEY_TYPE_CAMELLIA:PSA_WANT_ALG_GCM
aead_algorithm:PSA_ALG_GCM:ALG_IS_AEAD_ON_BLOCK_CIPHER:16:PSA_KEY_TYPE_CAMELLIA:256

AEAD: ChaCha20_Poly1305
depends_on:PSA_WANT_ALG_CHACHA20_POLY1305
aead_algorithm:PSA_ALG_CHACHA20_POLY1305:0:16:PSA_KEY_TYPE_CHACHA20:256

Asymmetric signature: RSA PKCS#1 v1.5 raw
depends_on:PSA_WANT_ALG_RSA_PKCS1V15_SIGN
asymmetric_signature_algorithm:PSA_ALG_RSA_PKCS1V15_SIGN_RAW:ALG_IS_RSA_PKCS1V15_SIGN | ALG_IS_SIGN_HASH

Asymmetric signature: RSA PKCS#1 v1.5 SHA-256
depends_on:PSA_WANT_ALG_RSA_PKCS1V15_SIGN:PSA_WANT_ALG_SHA_256
asymmetric_signature_algorithm:PSA_ALG_RSA_PKCS1V15_SIGN( PSA_ALG_SHA_256 ):ALG_IS_RSA_PKCS1V15_SIGN | ALG_IS_SIGN_HASH | ALG_IS_HASH_AND_SIGN

Asymmetric signature: RSA PSS SHA-256
depends_on:PSA_WANT_ALG_RSA_PSS:PSA_WANT_ALG_SHA_256
asymmetric_signature_algorithm:PSA_ALG_RSA_PSS( PSA_ALG_SHA_256 ):ALG_IS_RSA_PSS | ALG_IS_RSA_PSS_STANDARD_SALT | ALG_IS_SIGN_HASH | ALG_IS_HASH_AND_SIGN

Asymmetric signature: RSA PSS-any-salt SHA-256
depends_on:PSA_WANT_ALG_RSA_PSS:PSA_WANT_ALG_SHA_256
asymmetric_signature_algorithm:PSA_ALG_RSA_PSS_ANY_SALT( PSA_ALG_SHA_256 ):ALG_IS_RSA_PSS | ALG_IS_RSA_PSS_ANY_SALT | ALG_IS_SIGN_HASH | ALG_IS_HASH_AND_SIGN

Asymmetric signature: randomized ECDSA (no hashing)
depends_on:PSA_WANT_ALG_ECDSA
asymmetric_signature_algorithm:PSA_ALG_ECDSA_ANY:ALG_IS_ECDSA | ALG_IS_RANDOMIZED_ECDSA | ALG_IS_SIGN_HASH

Asymmetric signature: SHA-256 + randomized ECDSA
depends_on:PSA_WANT_ALG_ECDSA:PSA_WANT_ALG_SHA_256
asymmetric_signature_algorithm:PSA_ALG_ECDSA( PSA_ALG_SHA_256 ):ALG_IS_ECDSA | ALG_IS_RANDOMIZED_ECDSA | ALG_IS_SIGN_HASH | ALG_IS_HASH_AND_SIGN

Asymmetric signature: SHA-256 + deterministic ECDSA using SHA-256
depends_on:PSA_WANT_ALG_DETERMINISTIC_ECDSA:PSA_WANT_ALG_SHA_256
asymmetric_signature_algorithm:PSA_ALG_DETERMINISTIC_ECDSA( PSA_ALG_SHA_256 ):ALG_IS_ECDSA | ALG_IS_DETERMINISTIC_ECDSA | ALG_ECDSA_IS_DETERMINISTIC | ALG_IS_SIGN_HASH | ALG_IS_HASH_AND_SIGN

Asymmetric signature: pure EdDSA
depends_on:PSA_WANT_ALG_EDDSA
asymmetric_signature_algorithm:PSA_ALG_PURE_EDDSA:0

Asymmetric signature: Ed25519ph
depends_on:PSA_WANT_ALG_EDDSA
asymmetric_signature_algorithm:PSA_ALG_ED25519PH:ALG_IS_HASH_EDDSA | ALG_IS_SIGN_HASH | ALG_IS_HASH_AND_SIGN

Asymmetric signature: Ed448ph
depends_on:PSA_WANT_ALG_EDDSA
asymmetric_signature_algorithm:PSA_ALG_ED448PH:ALG_IS_HASH_EDDSA | ALG_IS_SIGN_HASH | ALG_IS_HASH_AND_SIGN

Asymmetric signature: RSA PKCS#1 v1.5 with wildcard hash
depends_on:PSA_WANT_ALG_RSA_PKCS1V15_SIGN
asymmetric_signature_wildcard:PSA_ALG_RSA_PKCS1V15_SIGN( PSA_ALG_ANY_HASH ):ALG_IS_RSA_PKCS1V15_SIGN

Asymmetric signature: RSA PSS with wildcard hash
depends_on:PSA_WANT_ALG_RSA_PSS
asymmetric_signature_wildcard:PSA_ALG_RSA_PSS( PSA_ALG_ANY_HASH ):ALG_IS_RSA_PSS | ALG_IS_RSA_PSS_STANDARD_SALT

Asymmetric signature: RSA PSS-any-salt with wildcard hash
depends_on:PSA_WANT_ALG_RSA_PSS
asymmetric_signature_wildcard:PSA_ALG_RSA_PSS_ANY_SALT( PSA_ALG_ANY_HASH ):ALG_IS_RSA_PSS | ALG_IS_RSA_PSS_ANY_SALT

Asymmetric signature: randomized ECDSA with wildcard hash
depends_on:PSA_WANT_ALG_ECDSA
asymmetric_signature_wildcard:PSA_ALG_ECDSA( PSA_ALG_ANY_HASH ):ALG_IS_ECDSA | ALG_IS_RANDOMIZED_ECDSA

Asymmetric signature: deterministic ECDSA with wildcard hash
depends_on:PSA_WANT_ALG_DETERMINISTIC_ECDSA
asymmetric_signature_wildcard:PSA_ALG_DETERMINISTIC_ECDSA( PSA_ALG_ANY_HASH ):ALG_IS_ECDSA | ALG_IS_DETERMINISTIC_ECDSA | ALG_ECDSA_IS_DETERMINISTIC

Asymmetric encryption: RSA PKCS#1 v1.5
depends_on:PSA_WANT_ALG_RSA_PKCS1V15_CRYPT
asymmetric_encryption_algorithm:PSA_ALG_RSA_PKCS1V15_CRYPT:0

Asymmetric encryption: RSA OAEP using SHA-256
depends_on:PSA_WANT_ALG_RSA_OAEP:PSA_WANT_ALG_SHA_256
asymmetric_encryption_algorithm:PSA_ALG_RSA_OAEP( PSA_ALG_SHA_256 ):ALG_IS_RSA_OAEP

Key derivation: HKDF using SHA-256
depends_on:PSA_WANT_ALG_HKDF:PSA_WANT_ALG_SHA_256
key_derivation_algorithm:PSA_ALG_HKDF( PSA_ALG_SHA_256 ):ALG_IS_HKDF

Key derivation: HKDF using SHA-384
depends_on:PSA_WANT_ALG_HKDF:PSA_WANT_ALG_SHA_384
key_derivation_algorithm:PSA_ALG_HKDF( PSA_ALG_SHA_384 ):ALG_IS_HKDF

Key derivation: HKDF-Extract using SHA-256
depends_on:PSA_WANT_ALG_HKDF_EXTRACT:PSA_WANT_ALG_SHA_256
key_derivation_algorithm:PSA_ALG_HKDF_EXTRACT( PSA_ALG_SHA_256 ):ALG_IS_HKDF_EXTRACT

Key derivation: HKDF-Extract using SHA-384
depends_on:PSA_WANT_ALG_HKDF_EXTRACT:PSA_WANT_ALG_SHA_384
key_derivation_algorithm:PSA_ALG_HKDF_EXTRACT( PSA_ALG_SHA_384 ):ALG_IS_HKDF_EXTRACT

Key derivation: HKDF-Expand using SHA-256
depends_on:PSA_WANT_ALG_HKDF_EXPAND:PSA_WANT_ALG_SHA_256
key_derivation_algorithm:PSA_ALG_HKDF_EXPAND( PSA_ALG_SHA_256 ):ALG_IS_HKDF_EXPAND

Key derivation: HKDF-Expand using SHA-384
depends_on:PSA_WANT_ALG_HKDF_EXPAND:PSA_WANT_ALG_SHA_384
key_derivation_algorithm:PSA_ALG_HKDF_EXPAND( PSA_ALG_SHA_384 ):ALG_IS_HKDF_EXPAND

Key derivation: TLS1.2 ECJPAKE-to-PMS
depends_on:PSA_WANT_ALG_TLS12_ECJPAKE_TO_PMS
key_derivation_algorithm:PSA_ALG_TLS12_ECJPAKE_TO_PMS:0

Key derivation: TLS 1.2 PRF using SHA-256
depends_on:PSA_WANT_ALG_SHA_256:PSA_WANT_ALG_TLS12_PRF
key_derivation_algorithm:PSA_ALG_TLS12_PRF( PSA_ALG_SHA_256 ):ALG_IS_TLS12_PRF

Key derivation: TLS 1.2 PRF using SHA-384
depends_on:PSA_WANT_ALG_SHA_384:PSA_WANT_ALG_TLS12_PRF
key_derivation_algorithm:PSA_ALG_TLS12_PRF( PSA_ALG_SHA_384 ):ALG_IS_TLS12_PRF

Key derivation: TLS 1.2 PSK-to-MS using SHA-256
depends_on:PSA_WANT_ALG_SHA_256:PSA_WANT_ALG_TLS12_PSK_TO_MS
key_derivation_algorithm:PSA_ALG_TLS12_PSK_TO_MS( PSA_ALG_SHA_256 ):ALG_IS_TLS12_PSK_TO_MS

Key derivation: TLS 1.2 PSK-to-MS using SHA-384
depends_on:PSA_WANT_ALG_SHA_384:PSA_WANT_ALG_TLS12_PSK_TO_MS
key_derivation_algorithm:PSA_ALG_TLS12_PSK_TO_MS( PSA_ALG_SHA_384 ):ALG_IS_TLS12_PSK_TO_MS

Key agreement: FFDH, raw output
depends_on:PSA_WANT_ALG_FFDH
key_agreement_algorithm:PSA_ALG_FFDH:ALG_IS_FFDH | ALG_IS_RAW_KEY_AGREEMENT:PSA_ALG_FFDH:PSA_ALG_CATEGORY_KEY_DERIVATION

Key agreement: FFDH, HKDF using SHA-256
depends_on:PSA_WANT_ALG_FFDH:PSA_WANT_ALG_HKDF:PSA_WANT_ALG_SHA_256
key_agreement_algorithm:PSA_ALG_KEY_AGREEMENT( PSA_ALG_FFDH, PSA_ALG_HKDF( PSA_ALG_SHA_256 ) ):ALG_IS_FFDH:PSA_ALG_FFDH:PSA_ALG_HKDF( PSA_ALG_SHA_256 )

Key agreement: FFDH, HKDF using SHA-384
depends_on:PSA_WANT_ALG_FFDH:PSA_WANT_ALG_HKDF:PSA_WANT_ALG_SHA_384
key_agreement_algorithm:PSA_ALG_KEY_AGREEMENT( PSA_ALG_FFDH, PSA_ALG_HKDF( PSA_ALG_SHA_384 ) ):ALG_IS_FFDH:PSA_ALG_FFDH:PSA_ALG_HKDF( PSA_ALG_SHA_384 )

Key agreement: ECDH, raw output
depends_on:PSA_WANT_ALG_ECDH
key_agreement_algorithm:PSA_ALG_ECDH:ALG_IS_ECDH | ALG_IS_RAW_KEY_AGREEMENT:PSA_ALG_ECDH:PSA_ALG_CATEGORY_KEY_DERIVATION

Key agreement: ECDH, HKDF using SHA-256
depends_on:PSA_WANT_ALG_ECDH:PSA_WANT_ALG_HKDF:PSA_WANT_ALG_SHA_256
key_agreement_algorithm:PSA_ALG_KEY_AGREEMENT( PSA_ALG_ECDH, PSA_ALG_HKDF( PSA_ALG_SHA_256 ) ):ALG_IS_ECDH:PSA_ALG_ECDH:PSA_ALG_HKDF( PSA_ALG_SHA_256 )

Key agreement: ECDH, HKDF using SHA-384
depends_on:PSA_WANT_ALG_ECDH:PSA_WANT_ALG_HKDF:PSA_WANT_ALG_SHA_384
key_agreement_algorithm:PSA_ALG_KEY_AGREEMENT( PSA_ALG_ECDH, PSA_ALG_HKDF( PSA_ALG_SHA_384 ) ):ALG_IS_ECDH:PSA_ALG_ECDH:PSA_ALG_HKDF( PSA_ALG_SHA_384 )

PAKE: J-PAKE
pake_algorithm:PSA_ALG_JPAKE

Key type: raw data
key_type:PSA_KEY_TYPE_RAW_DATA:KEY_TYPE_IS_UNSTRUCTURED

Key type: HMAC
depends_on:PSA_WANT_KEY_TYPE_HMAC
key_type:PSA_KEY_TYPE_HMAC:KEY_TYPE_IS_UNSTRUCTURED

Key type: secret for key derivation
key_type:PSA_KEY_TYPE_DERIVE:KEY_TYPE_IS_UNSTRUCTURED

Key type: password
key_type:PSA_KEY_TYPE_PASSWORD:KEY_TYPE_IS_UNSTRUCTURED

Key type: password hash
key_type:PSA_KEY_TYPE_PASSWORD_HASH:KEY_TYPE_IS_UNSTRUCTURED

Block cipher key type: AES
depends_on:PSA_WANT_KEY_TYPE_AES
block_cipher_key_type:PSA_KEY_TYPE_AES:16

Block cipher key type: ARIA
depends_on:PSA_WANT_KEY_TYPE_ARIA
block_cipher_key_type:PSA_KEY_TYPE_ARIA:16

Block cipher key type: DES
depends_on:PSA_WANT_KEY_TYPE_DES
block_cipher_key_type:PSA_KEY_TYPE_DES:8

Block cipher key type: Camellia
depends_on:PSA_WANT_KEY_TYPE_CAMELLIA
block_cipher_key_type:PSA_KEY_TYPE_CAMELLIA:16

Stream cipher key type: ChaCha20
depends_on:PSA_WANT_KEY_TYPE_CHACHA20
stream_cipher_key_type:PSA_KEY_TYPE_CHACHA20

Key type: RSA public key
depends_on:PSA_WANT_KEY_TYPE_RSA_PUBLIC_KEY
key_type:PSA_KEY_TYPE_RSA_PUBLIC_KEY:KEY_TYPE_IS_PUBLIC_KEY | KEY_TYPE_IS_RSA

Key type: RSA key pair
depends_on:PSA_WANT_KEY_TYPE_RSA_KEY_PAIR_BASIC:PSA_WANT_KEY_TYPE_RSA_KEY_PAIR_IMPORT:PSA_WANT_KEY_TYPE_RSA_KEY_PAIR_EXPORT
key_type:PSA_KEY_TYPE_RSA_KEY_PAIR:KEY_TYPE_IS_KEY_PAIR | KEY_TYPE_IS_RSA

ECC key family: SECP K1
ecc_key_family:PSA_ECC_FAMILY_SECP_K1

ECC key family: SECP R1
ecc_key_family:PSA_ECC_FAMILY_SECP_R1

ECC key family: SECP R2
ecc_key_family:PSA_ECC_FAMILY_SECP_R2

ECC key family: SECT K1
ecc_key_family:PSA_ECC_FAMILY_SECT_K1

ECC key family: SECT R1
ecc_key_family:PSA_ECC_FAMILY_SECT_R1

ECC key family: SECT R2
ecc_key_family:PSA_ECC_FAMILY_SECT_R2

ECC key family: Brainpool P R1
ecc_key_family:PSA_ECC_FAMILY_BRAINPOOL_P_R1

ECC key family: Montgomery (Curve25519, Curve448)
ecc_key_family:PSA_ECC_FAMILY_MONTGOMERY

ECC key family: Twisted Edwards (Ed25519, Ed448)
ecc_key_family:PSA_ECC_FAMILY_TWISTED_EDWARDS

DH group family: RFC 7919
dh_key_family:PSA_DH_FAMILY_RFC7919

Lifetime: VOLATILE
lifetime:PSA_KEY_LIFETIME_VOLATILE:KEY_LIFETIME_IS_VOLATILE:PSA_KEY_PERSISTENCE_VOLATILE:PSA_KEY_LOCATION_LOCAL_STORAGE

Lifetime: PERSISTENT
lifetime:PSA_KEY_LIFETIME_PERSISTENT:0:PSA_KEY_PERSISTENCE_DEFAULT:PSA_KEY_LOCATION_LOCAL_STORAGE

Lifetime: volatile, local storage
lifetime:PSA_KEY_LIFETIME_FROM_PERSISTENCE_AND_LOCATION(PSA_KEY_PERSISTENCE_VOLATILE, PSA_KEY_LOCATION_LOCAL_STORAGE):KEY_LIFETIME_IS_VOLATILE:PSA_KEY_PERSISTENCE_VOLATILE:PSA_KEY_LOCATION_LOCAL_STORAGE

Lifetime: default, local storage
lifetime:PSA_KEY_LIFETIME_FROM_PERSISTENCE_AND_LOCATION(PSA_KEY_PERSISTENCE_DEFAULT, PSA_KEY_LOCATION_LOCAL_STORAGE):0:PSA_KEY_PERSISTENCE_DEFAULT:PSA_KEY_LOCATION_LOCAL_STORAGE

Lifetime: 2, local storage
lifetime:PSA_KEY_LIFETIME_FROM_PERSISTENCE_AND_LOCATION(2, PSA_KEY_LOCATION_LOCAL_STORAGE):0:2:PSA_KEY_LOCATION_LOCAL_STORAGE

Lifetime: 254, local storage
lifetime:PSA_KEY_LIFETIME_FROM_PERSISTENCE_AND_LOCATION(254, PSA_KEY_LOCATION_LOCAL_STORAGE):0:254:PSA_KEY_LOCATION_LOCAL_STORAGE

Lifetime: read-only, local storage
lifetime:PSA_KEY_LIFETIME_FROM_PERSISTENCE_AND_LOCATION(PSA_KEY_PERSISTENCE_READ_ONLY, PSA_KEY_LOCATION_LOCAL_STORAGE):KEY_LIFETIME_IS_READ_ONLY:PSA_KEY_PERSISTENCE_READ_ONLY:PSA_KEY_LOCATION_LOCAL_STORAGE

Lifetime: volatile, 0x123456
lifetime:PSA_KEY_LIFETIME_FROM_PERSISTENCE_AND_LOCATION(PSA_KEY_PERSISTENCE_VOLATILE, 0x123456):KEY_LIFETIME_IS_VOLATILE:PSA_KEY_PERSISTENCE_VOLATILE:0x123456

Lifetime: default, 0x123456
lifetime:PSA_KEY_LIFETIME_FROM_PERSISTENCE_AND_LOCATION(PSA_KEY_PERSISTENCE_DEFAULT, 0x123456):0:PSA_KEY_PERSISTENCE_DEFAULT:0x123456

Lifetime: 2, 0x123456
lifetime:PSA_KEY_LIFETIME_FROM_PERSISTENCE_AND_LOCATION(2, 0x123456):0:2:0x123456

Lifetime: 254, 0x123456
lifetime:PSA_KEY_LIFETIME_FROM_PERSISTENCE_AND_LOCATION(254, 0x123456):0:254:0x123456

Lifetime: read-only, 0x123456
lifetime:PSA_KEY_LIFETIME_FROM_PERSISTENCE_AND_LOCATION(PSA_KEY_PERSISTENCE_READ_ONLY, 0x123456):KEY_LIFETIME_IS_READ_ONLY:PSA_KEY_PERSISTENCE_READ_ONLY:0x123456
