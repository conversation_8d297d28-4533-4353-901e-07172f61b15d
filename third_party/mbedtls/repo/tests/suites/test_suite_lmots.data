LMOTS sign-verify test #1
# This test uses a fixed message, and then generates a private key, signs the
# message, and verifies the signature.
lmots_sign_verify_test:"c41ba177a0ca1ec31dfb2e145237e65b":"00000000000000000000000000000000":12:"403cbcc9808bb4b5ad72476ea297b2854c928ff5336f0b98ac2237ec83225ae7"

LMOTS sign-verify test #2
# This test uses a fixed message, and then generates a private key, signs the
# message, and verifies the signature.
lmots_sign_verify_test:"55a6647a581004306792b653a561d9f3":"00000000000000000000000000000000":12:"c3dbc3fea047dca8fb7a3cdf609a5b7f48599c193c90e958ce9388c84df0a906"

LMOTS NULL-message sign-verify test
# This test uses a NULL zero-length message, and then generates a private key,
# signs the message, and verifies the signature.
lmots_sign_verify_null_msg_test::"00000000000000000000000000000000":12:"be5fa89144f2d665c66ead8216bc02006e0eccd8b3697a0aea44f6c93afe7955"

LMOTS hsslms interop test #1
# This test uses data from https://github.com/pmvr/python-hsslms due to the
# limited amount of available test vectors for LMOTS, and few implementations
# providing direct access to the underlying OTS signature scheme. The private
# key is stored in data_files/lms_hsslms_sha256_m32_h5_lmots_sha256_n32_w8_prv.
# This test uses the same OTS key as the LMS hsslms interop test 1 (leaf 0 of
# the LMS key), and the same message.
#
# To produce another signature with this message and key (note that the actual
# signature bytes will differ due to randomization):
# pip3 install --user hsslms==0.1.2
#
# from hsslms import LMS_Priv, LM_OTS_Priv, LMS_ALGORITHM_TYPE, LMOTS_ALGORITHM_TYPE
# import pickle
#
# with open('tests/data_files/lms_hsslms_sha256_m32_h5_lmots_sha256_n32_w8_prv', 'rb') as private_key_file:
#     private_key = pickle.load(private_key_file)
#
# ots_private_key = LM_OTS_Priv(private_key.otstypecode, private_key.I, 0, private_key.SEED)
# ots_public_key = ots_private_key.gen_pub()
# message = bytes.fromhex('60da1a17c88c59da8a730e6ca8effd37')
# sig = ots_private_key.sign(message)
# print('lmots_verify_test:"{}":"{}":"{}":0'.format(message.hex(), sig.hex(), ots_public_key.pubkey.hex()))
lmots_verify_test:"60da1a17c88c59da8a730e6ca8effd37":"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":"0000000447cc5b29dd0cecd01c382434a6d1686400000000761e8e577fb4d12058806fc7bdaaef0ba64e454dc59b0230a77b43bbd83dc8c6":0

LMOTS hsslms interop test #2
# This test uses data from https://github.com/pmvr/python-hsslms due to the
# limited amount of available test vectors for LMOTS, and few implementations
# providing direct access to the underlying OTS signature scheme. The private
# key is stored in data_files/lms_hsslms_sha256_m32_h5_lmots_sha256_n32_w8_prv.
# This test uses the same OTS key as the LMS hsslms interop test 2 (leaf 1 of
# the LMS key), and the same message.
#
# To produce another signature with this message and key (note that the actual
# signature bytes will differ due to randomization):
# pip3 install --user hsslms==0.1.2
#
# from hsslms import LMS_Priv, LM_OTS_Priv, LMS_ALGORITHM_TYPE, LMOTS_ALGORITHM_TYPE
# import pickle
#
# with open('tests/data_files/lms_hsslms_sha256_m32_h5_lmots_sha256_n32_w8_prv', 'rb') as private_key_file:
#     private_key = pickle.load(private_key_file)
#
#ots_private_key = LM_OTS_Priv(private_key.otstypecode, private_key.I, 1, private_key.SEED)
#ots_public_key = ots_private_key.gen_pub()
#message = bytes.fromhex('92d036bde8c45b8bb5dea2a072560b1e29fc4bb7dc4549ce90bccee8a6e962a1')
#sig = ots_private_key.sign(message)
#print('lmots_verify_test:"{}":"{}":"{}":0'.format(message.hex(), sig.hex(), ots_public_key.pubkey.hex()))
lmots_verify_test:"92d036bde8c45b8bb5dea2a072560b1e29fc4bb7dc4549ce90bccee8a6e962a1":"00000004e29f47d2314ebaf22ebb821dec653f5bd105aced5d24829787a93da910baa495cd5a8576dad606cc7407c4d8a38a715ded879274c5347a200cc1c08a6fcc7288e280bb2e66b682c4b20c514f7a990ce01594001917be8d1cb5a23c22dc00c81b18b8047177cc109a1ff862f535319b703be8e4439062348b7bc73e85e69f7d3f033767146130991f78b497e2a0eee1059d2cd87e0a99c1aae47a6496664735cdb383a8f7a1d686199cf2e07a67e9ef409048efb76cf0c689c1c6c67a5b6966e4b4773710bdff3f72a4f85428187f912c9f13a8bd06533450ce04dbbd2c022eab44a5f6a822d78918f692fa5c6c90aab8941072d679b89388160556597acf17b95b3ffdf8c4c21df5327bd756772a45fdde182004d91cff5aba111fbb70b5970a7d7416220de31e6e76646372e4a1606fbbd5be215a32bbb84da99c63af271edbd42ee87de174cabec7734b6d924d329640bdb84059cbcea89caa703667f5e1b3c1c71b53213f1cd7d1da3e42da70edeb7c0b596bcb981c08eb0f02408ee028a57165cbdc36c9edafa559826c2e690e73da7c7fa1b0fa0e6041a692a2e8f27af80513c07ecd89caaf78ddf8e2edaa17bece335068153b253ceef38b491801c1ef7c648045ce7c517afab888603648b17d3a98a3b5622b469a829b023c5cb2ce42462c28d22bc3de91dd8b38bab539971b0c7596dcd0d8c0d84bfd7925d6e2f2d114ca4f91fca12178a451ac0dabc8c21396ad5be57ea0648bd1054de00aa7fd3d46453ebacf6b611e05842f5f019aeca3c798ae063631fd5e56ea1f7a21bbea5c30e6d60a724ce187e7c497d918d2a4d5094224dde94a02e851eae1626533992a599a641466e4d683e40b5a28695aababd2d7f7d2ccee72c289876c8d581babaeb3d738f1d1fc765e9fee3f70670913e07cd38fc7b37e2caba0a735352aa3f4b2467010bb1b725d4bbd86d8c98eece10e925d8bb5c0e993dfa45621f91596f5d1e1446b118c48bc1e403627fdd299ad4d3d5f3a2dfb239bf22e7ff25d83287ba3a96b24cda0252df1907af1cb74d31d720c5baca0f316769f7f98b409c17bb543c39628446183e326d0745b4424520a9d582fc817eac55b0efc2ca4659a60a95e1d3b77bf1454e5cd4d1d54d51159d3df70a78345d1d6a7e0746b3deb080883f6506e9e7d0fb4bddaa66aa7cf555df1bb9d3f848b7e604b690a403f4e40188110e0ef9af15dc4952a8ed100987e39e8184be8dc62441ac2a561c7cbe431c45b0ec03c41c4867e38925977fc240ed2a04d73d4319435de354dfe0184220c71bd59be4e7f6dc9a1a27f4eefc990d615b2c12e13f1821727a607afdab359d2bad5b1be689a36662e052cfade2c0f5cc842c090082068d324f0e338830030d255ee6e6d9303c0037c24985338dfa16b5980a99782af1b3aca9123b5063e0b9f1a31105e2c9eaae2353b2ed53dab5b4fb43b4697d05fcf4941be071edf3456ac8e35eba39800ad968155574c14b6ce109982177b00ea5fbb739dc7553e40c98824d4932185e61ccc380b07476ae210ce3657b24f4639261a5e7e0c52d6afdea97bb2fc":"0000000447cc5b29dd0cecd01c382434a6d1686400000001f337dde97685d008a4440b59550277390018d3f1d485fa4b8c91796032de494b":0

LMOTS hsslms interop NULL-message test
# This test uses data from https://github.com/pmvr/python-hsslms due to the
# limited amount of available test vectors for LMOTS, and few implementations
# providing direct access to the underlying OTS signature scheme. The private
# key is stored in data_files/lms_hsslms_sha256_m32_h5_lmots_sha256_n32_w8_prv.
#
# To produce another signature with this message and key (note that the actual
# signature bytes will differ due to randomization):
# pip3 install --user hsslms==0.1.2
#
# from hsslms import LMS_Priv, LM_OTS_Priv, LMS_ALGORITHM_TYPE, LMOTS_ALGORITHM_TYPE
# import pickle
#
# with open('tests/data_files/lms_hsslms_sha256_m32_h5_lmots_sha256_n32_w8_prv', 'rb') as private_key_file:
#     private_key = pickle.load(private_key_file)
#
#ots_private_key = LM_OTS_Priv(private_key.otstypecode, private_key.I, 3, private_key.SEED)
#ots_public_key = ots_private_key.gen_pub()
#message = bytes()
#sig = ots_private_key.sign(message)
#print('lmots_verify_test:"{}":"{}":"{}":0'.format(message.hex(), sig.hex(), ots_public_key.pubkey.hex()))
lmots_verify_test:"":"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":"0000000447cc5b29dd0cecd01c382434a6d16864000000033fa1330497e44e2773f08e4727eb4d745db9051d6a60779e58a922dc8a7d4ede":0

LMOTS hash-sigs interop negative test (altered random value)
# This test uses the valid signature from hsslms interop test 1, and then
# alters the random value (C) of the signature, and is expected to fail to
# verify.
lmots_verify_test:"60da1a17c88c59da8a730e6ca8effd37":"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":"0000000447cc5b29dd0cecd01c382434a6d1686400000000761e8e577fb4d12058806fc7bdaaef0ba64e454dc59b0230a77b43bbd83dc8c6":MBEDTLS_ERR_LMS_VERIFY_FAILED

LMOTS negative test (invalid type) #1
# This test uses the valid signature from hsslms interop test 1, and then
# sets an invalid LMOTS type (0x5), and is expected to fail to
# verify.
lmots_verify_test:"60da1a17c88c59da8a730e6ca8effd37":"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":"0000000447cc5b29dd0cecd01c382434a6d1686400000000761e8e577fb4d12058806fc7bdaaef0ba64e454dc59b0230a77b43bbd83dc8c6":MBEDTLS_ERR_LMS_VERIFY_FAILED

LMOTS negative test (invalid type) #2
# This test uses the valid signature from hsslms interop test 1, and then
# sets an invalid LMOTS type (0x3), and is expected to fail to
# verify.
lmots_verify_test:"60da1a17c88c59da8a730e6ca8effd37":"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":"0000000447cc5b29dd0cecd01c382434a6d1686400000000761e8e577fb4d12058806fc7bdaaef0ba64e454dc59b0230a77b43bbd83dc8c6":MBEDTLS_ERR_LMS_VERIFY_FAILED

LMOTS key import / export test
# This test uses the valid public key for hsslms interop test 1, imports it, and
# then exports it. It also checks if the export correctly fails when the export
# buffer is too small.
lmots_import_export_test:"0000000447cc5b29dd0cecd01c382434a6d1686400000001f337dde97685d008a4440b59550277390018d3f1d485fa4b8c91796032de494b":0

LMOTS key import too large key test
# This test uses the valid public key for hsslms interop test 1, add an extra
# byte, and then imports it. This should fail.
lmots_import_export_test:"0000000447cc5b29dd0cecd01c382434a6d1686400000001f337dde97685d008a4440b59550277390018d3f1d485fa4b8c91796032de494b00":MBEDTLS_ERR_LMS_BAD_INPUT_DATA

LMOTS key import too small key test
# This test uses the valid public key for hsslms interop test 1, removes a byte,
# and then imports it. This should fail.
lmots_import_export_test:"0000000447cc5b29dd0cecd01c382434a6d1686400000001f337dde97685d008a4440b59550277390018d3f1d485fa4b8c91796032de49":MBEDTLS_ERR_LMS_BAD_INPUT_DATA

LMOTS key import no type test
# This test uses the valid public key for hsslms interop test 1, cuts it down so
# it's smaller than the LMOTS type offset, and imports it. This should fail, and
# not attempt to read invalidly outside the buffer.
lmots_import_export_test:"000000":MBEDTLS_ERR_LMS_BAD_INPUT_DATA

LMOTS key import invalid type test #1
# This test uses the valid public key for hsslms interop test 1, alters the
# LMOTS type to 0x3, and imports it. This should fail.
lmots_import_export_test:"0000000347cc5b29dd0cecd01c382434a6d1686400000001f337dde97685d008a4440b59550277390018d3f1d485fa4b8c91796032de494b":MBEDTLS_ERR_LMS_BAD_INPUT_DATA

LMOTS key import invalid type test #2
# This test uses the valid public key for hsslms interop test 1, alters the
# LMOTS type to 0x5, and imports it. This should fail, and not attempt to read
# invalidly outside the buffer.
lmots_import_export_test:"0000000547cc5b29dd0cecd01c382434a6d1686400000001f337dde97685d008a4440b59550277390018d3f1d485fa4b8c91796032de494b":MBEDTLS_ERR_LMS_BAD_INPUT_DATA

LMOTS key reuse test
# This test uses a fixed message, and then generates a private key, signs the
# message, and then attempts to sign the message again. The second signature
# must fail as private key material must be deleted after a key is used to sign.
lmots_reuse_test:"cfcd1e81193e310c9d931d1b00818d14":"00000000000000000000000000000000":12:"a7f53cc5a228ce63811ba4d7c1f74f7fce62afbf6813f3ca3ae43c11b138086f"

LMOTS signature leak test
# This test uses a fixed message, and then generates a private key, signs the
# message, and then uses a test hook to check that the signature has not been
# modifier before the private key has been deleted (which could cause signature
# leakage during errors).
lmots_signature_leak_test:"cfcd1e81193e310c9d931d1b00818d14":"00000000000000000000000000000000":12:"a7f53cc5a228ce63811ba4d7c1f74f7fce62afbf6813f3ca3ae43c11b138086f"
