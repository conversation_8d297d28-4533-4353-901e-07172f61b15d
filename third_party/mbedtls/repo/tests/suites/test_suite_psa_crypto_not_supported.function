/* BEGIN_HEADER */

#include "psa/crypto.h"
#include "test/psa_crypto_helpers.h"

#define INVALID_KEY_ID mbedtls_svc_key_id_make(0, 0xfedcba98)

/* END_HEADER */

/* BEGIN_DEPENDENCIES
 * depends_on:MBEDTLS_PSA_CRYPTO_C
 * END_DEPENDENCIES
 */

/* BEGIN_CASE */
void import_not_supported(int key_type, data_t *key_material)
{
    psa_key_attributes_t attributes = PSA_KEY_ATTRIBUTES_INIT;
    mbedtls_svc_key_id_t key_id = INVALID_KEY_ID;

    PSA_ASSERT(psa_crypto_init());
    psa_set_key_type(&attributes, key_type);
    TEST_EQUAL(psa_import_key(&attributes,
                              key_material->x, key_material->len,
                              &key_id),
               PSA_ERROR_NOT_SUPPORTED);
    TEST_ASSERT(mbedtls_svc_key_id_equal(key_id, MBEDTLS_SVC_KEY_ID_INIT));

exit:
    psa_destroy_key(key_id);
    PSA_DONE();
}
/* END_CASE */

/* BEGIN_CASE */
void generate_not_supported(int key_type, int bits)
{
    psa_key_attributes_t attributes = PSA_KEY_ATTRIBUTES_INIT;
    mbedtls_svc_key_id_t key_id = INVALID_KEY_ID;

    PSA_ASSERT(psa_crypto_init());
    psa_set_key_type(&attributes, key_type);
    psa_set_key_bits(&attributes, bits);
    TEST_EQUAL(psa_generate_key(&attributes, &key_id),
               PSA_ERROR_NOT_SUPPORTED);
    TEST_ASSERT(mbedtls_svc_key_id_equal(key_id, MBEDTLS_SVC_KEY_ID_INIT));

exit:
    psa_destroy_key(key_id);
    PSA_DONE();
}
/* END_CASE */
