# The following two manually crafted test cases are redundant with
# systematically generated test cases, but useful to have as an anchor when
# debugging changes to the test code or to the test case generation.

PSA storage read: AES-GCM+CTR
depends_on:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_GCM:PSA_WANT_ALG_CTR
key_storage_read:PSA_KEY_LIFETIME_PERSISTENT:PSA_KEY_TYPE_AES:128:PSA_KEY_USAGE_EXPORT | PSA_KEY_USAGE_ENCRYPT:PSA_ALG_GCM:PSA_ALG_CTR:"404142434445464748494a4b4c4d4e4f":"505341004b45590000000000010000000024800001010000000250050010c00410000000404142434445464748494a4b4c4d4e4f":1

PSA storage save: AES-GCM+CTR
depends_on:PSA_WANT_KEY_TYPE_AES
key_storage_save:PSA_KEY_LIFETIME_PERSISTENT:PSA_KEY_TYPE_AES:128:PSA_KEY_USAGE_EXPORT | PSA_KEY_USAGE_ENCRYPT:PSA_ALG_GCM:PSA_ALG_CTR:"404142434445464748494a4b4c4d4e4f":"505341004b45590000000000010000000024800001010000000250050010c00410000000404142434445464748494a4b4c4d4e4f"
