/* BEGIN_HEADER */
#include "mbedtls/ecjpake.h"

#if defined(MBEDTLS_ECP_DP_SECP256R1_ENABLED) && defined(MBEDTLS_MD_CAN_SHA256)
static const unsigned char ecjpake_test_x1[] = {
    0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x0a, 0x0b, 0x0c,
    0x0d, 0x0e, 0x0f, 0x10, 0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17, 0x18,
    0x19, 0x1a, 0x1b, 0x1c, 0x1d, 0x1e, 0x1f, 0x21
};

static const unsigned char ecjpake_test_x2[] = {
    0x61, 0x62, 0x63, 0x64, 0x65, 0x66, 0x67, 0x68, 0x69, 0x6a, 0x6b, 0x6c,
    0x6d, 0x6e, 0x6f, 0x70, 0x71, 0x72, 0x73, 0x74, 0x75, 0x76, 0x77, 0x78,
    0x79, 0x7a, 0x7b, 0x7c, 0x7d, 0x7e, 0x7f, 0x81
};

static const unsigned char ecjpake_test_x3[] = {
    0x61, 0x62, 0x63, 0x64, 0x65, 0x66, 0x67, 0x68, 0x69, 0x6a, 0x6b, 0x6c,
    0x6d, 0x6e, 0x6f, 0x70, 0x71, 0x72, 0x73, 0x74, 0x75, 0x76, 0x77, 0x78,
    0x79, 0x7a, 0x7b, 0x7c, 0x7d, 0x7e, 0x7f, 0x81
};

static const unsigned char ecjpake_test_x4[] = {
    0xc1, 0xc2, 0xc3, 0xc4, 0xc5, 0xc6, 0xc7, 0xc8, 0xc9, 0xca, 0xcb, 0xcc,
    0xcd, 0xce, 0xcf, 0xd0, 0xd1, 0xd2, 0xd3, 0xd4, 0xd5, 0xd6, 0xd7, 0xd8,
    0xd9, 0xda, 0xdb, 0xdc, 0xdd, 0xde, 0xdf, 0xe1
};

static const unsigned char ecjpake_test_X1[] = {
    0x04, 0xac, 0xcf, 0x01, 0x06, 0xef, 0x85, 0x8f, 0xa2, 0xd9, 0x19, 0x33,
    0x13, 0x46, 0x80, 0x5a, 0x78, 0xb5, 0x8b, 0xba, 0xd0, 0xb8, 0x44, 0xe5,
    0xc7, 0x89, 0x28, 0x79, 0x14, 0x61, 0x87, 0xdd, 0x26, 0x66, 0xad, 0xa7,
    0x81, 0xbb, 0x7f, 0x11, 0x13, 0x72, 0x25, 0x1a, 0x89, 0x10, 0x62, 0x1f,
    0x63, 0x4d, 0xf1, 0x28, 0xac, 0x48, 0xe3, 0x81, 0xfd, 0x6e, 0xf9, 0x06,
    0x07, 0x31, 0xf6, 0x94, 0xa4
};

static const unsigned char ecjpake_test_X2[] = {
    0x04, 0x7e, 0xa6, 0xe3, 0xa4, 0x48, 0x70, 0x37, 0xa9, 0xe0, 0xdb, 0xd7,
    0x92, 0x62, 0xb2, 0xcc, 0x27, 0x3e, 0x77, 0x99, 0x30, 0xfc, 0x18, 0x40,
    0x9a, 0xc5, 0x36, 0x1c, 0x5f, 0xe6, 0x69, 0xd7, 0x02, 0xe1, 0x47, 0x79,
    0x0a, 0xeb, 0x4c, 0xe7, 0xfd, 0x65, 0x75, 0xab, 0x0f, 0x6c, 0x7f, 0xd1,
    0xc3, 0x35, 0x93, 0x9a, 0xa8, 0x63, 0xba, 0x37, 0xec, 0x91, 0xb7, 0xe3,
    0x2b, 0xb0, 0x13, 0xbb, 0x2b
};

static const unsigned char ecjpake_test_X3[] = {
    0x04, 0x7e, 0xa6, 0xe3, 0xa4, 0x48, 0x70, 0x37, 0xa9, 0xe0, 0xdb, 0xd7,
    0x92, 0x62, 0xb2, 0xcc, 0x27, 0x3e, 0x77, 0x99, 0x30, 0xfc, 0x18, 0x40,
    0x9a, 0xc5, 0x36, 0x1c, 0x5f, 0xe6, 0x69, 0xd7, 0x02, 0xe1, 0x47, 0x79,
    0x0a, 0xeb, 0x4c, 0xe7, 0xfd, 0x65, 0x75, 0xab, 0x0f, 0x6c, 0x7f, 0xd1,
    0xc3, 0x35, 0x93, 0x9a, 0xa8, 0x63, 0xba, 0x37, 0xec, 0x91, 0xb7, 0xe3,
    0x2b, 0xb0, 0x13, 0xbb, 0x2b
};

static const unsigned char ecjpake_test_X4[] = {
    0x04, 0x19, 0x0a, 0x07, 0x70, 0x0f, 0xfa, 0x4b, 0xe6, 0xae, 0x1d, 0x79,
    0xee, 0x0f, 0x06, 0xae, 0xb5, 0x44, 0xcd, 0x5a, 0xdd, 0xaa, 0xbe, 0xdf,
    0x70, 0xf8, 0x62, 0x33, 0x21, 0x33, 0x2c, 0x54, 0xf3, 0x55, 0xf0, 0xfb,
    0xfe, 0xc7, 0x83, 0xed, 0x35, 0x9e, 0x5d, 0x0b, 0xf7, 0x37, 0x7a, 0x0f,
    0xc4, 0xea, 0x7a, 0xce, 0x47, 0x3c, 0x9c, 0x11, 0x2b, 0x41, 0xcc, 0xd4,
    0x1a, 0xc5, 0x6a, 0x56, 0x12
};

/* Load my private and public keys, and peer's public keys */
static int ecjpake_test_load(mbedtls_ecjpake_context *ctx,
                             const unsigned char *xm1, size_t len_xm1,
                             const unsigned char *xm2, size_t len_xm2,
                             const unsigned char *Xm1, size_t len_Xm1,
                             const unsigned char *Xm2, size_t len_Xm2,
                             const unsigned char *Xp1, size_t len_Xp1,
                             const unsigned char *Xp2, size_t len_Xp2)
{
    int ret;

    MBEDTLS_MPI_CHK(mbedtls_mpi_read_binary(&ctx->xm1, xm1, len_xm1));
    MBEDTLS_MPI_CHK(mbedtls_mpi_read_binary(&ctx->xm2, xm2, len_xm2));

    MBEDTLS_MPI_CHK(mbedtls_ecp_point_read_binary(&ctx->grp,
                                                  &ctx->Xm1, Xm1, len_Xm1));
    MBEDTLS_MPI_CHK(mbedtls_ecp_point_read_binary(&ctx->grp,
                                                  &ctx->Xm2, Xm2, len_Xm2));
    MBEDTLS_MPI_CHK(mbedtls_ecp_point_read_binary(&ctx->grp,
                                                  &ctx->Xp1, Xp1, len_Xp1));
    MBEDTLS_MPI_CHK(mbedtls_ecp_point_read_binary(&ctx->grp,
                                                  &ctx->Xp2, Xp2, len_Xp2));

cleanup:
    return ret;
}

#define ADD_SIZE(x)   x, sizeof(x)
#endif /* MBEDTLS_ECP_DP_SECP256R1_ENABLED && MBEDTLS_MD_CAN_SHA256 */
/* END_HEADER */

/* BEGIN_DEPENDENCIES
 * depends_on:MBEDTLS_ECJPAKE_C
 * END_DEPENDENCIES
 */

/* BEGIN_CASE */
void ecjpake_invalid_param()
{
    mbedtls_ecjpake_context ctx;
    unsigned char buf[42] = { 0 };
    size_t const len = sizeof(buf);
    mbedtls_ecjpake_role invalid_role = (mbedtls_ecjpake_role) 42;
    mbedtls_md_type_t valid_md = MBEDTLS_MD_SHA256;
    mbedtls_ecp_group_id valid_group = MBEDTLS_ECP_DP_SECP256R1;

    MD_PSA_INIT();

    mbedtls_ecjpake_init(&ctx);

    TEST_EQUAL(MBEDTLS_ERR_ECP_BAD_INPUT_DATA,
               mbedtls_ecjpake_setup(&ctx,
                                     invalid_role,
                                     valid_md,
                                     valid_group,
                                     buf, len));
exit:
    MD_PSA_DONE();
}
/* END_CASE */

/* BEGIN_CASE depends_on:MBEDTLS_SELF_TEST */
void ecjpake_selftest()
{
    MD_PSA_INIT();

    TEST_ASSERT(mbedtls_ecjpake_self_test(1) == 0);

exit:
    MD_PSA_DONE();
}
/* END_CASE */

/* BEGIN_CASE depends_on:MBEDTLS_ECP_DP_SECP256R1_ENABLED:MBEDTLS_MD_CAN_SHA256 */
void read_bad_md(data_t *msg)
{
    mbedtls_ecjpake_context corrupt_ctx;
    const unsigned char *pw = NULL;
    const size_t pw_len = 0;
    int any_role = MBEDTLS_ECJPAKE_CLIENT;

    MD_PSA_INIT();

    mbedtls_ecjpake_init(&corrupt_ctx);
    TEST_ASSERT(mbedtls_ecjpake_setup(&corrupt_ctx, any_role,
                                      MBEDTLS_MD_SHA256, MBEDTLS_ECP_DP_SECP256R1, pw,
                                      pw_len) == 0);
    corrupt_ctx.md_type = MBEDTLS_MD_NONE;

    TEST_EQUAL(mbedtls_ecjpake_read_round_one(&corrupt_ctx, msg->x,
                                              msg->len), MBEDTLS_ERR_MD_BAD_INPUT_DATA);

exit:
    mbedtls_ecjpake_free(&corrupt_ctx);
    MD_PSA_DONE();
}
/* END_CASE */

/* BEGIN_CASE depends_on:MBEDTLS_ECP_DP_SECP256R1_ENABLED:MBEDTLS_MD_CAN_SHA256 */
void read_round_one(int role, data_t *msg, int ref_ret)
{
    mbedtls_ecjpake_context ctx;
    const unsigned char *pw = NULL;
    const size_t pw_len = 0;

    MD_PSA_INIT();

    mbedtls_ecjpake_init(&ctx);

    TEST_ASSERT(mbedtls_ecjpake_setup(&ctx, role,
                                      MBEDTLS_MD_SHA256, MBEDTLS_ECP_DP_SECP256R1, pw,
                                      pw_len) == 0);

    TEST_ASSERT(mbedtls_ecjpake_read_round_one(&ctx, msg->x, msg->len) == ref_ret);

exit:
    mbedtls_ecjpake_free(&ctx);
    MD_PSA_DONE();
}
/* END_CASE */

/* BEGIN_CASE depends_on:MBEDTLS_ECP_DP_SECP256R1_ENABLED:MBEDTLS_MD_CAN_SHA256 */
void read_round_two_cli(data_t *msg, int ref_ret)
{
    mbedtls_ecjpake_context ctx;
    const unsigned char *pw = NULL;
    const size_t pw_len = 0;

    MD_PSA_INIT();

    mbedtls_ecjpake_init(&ctx);

    TEST_ASSERT(mbedtls_ecjpake_setup(&ctx, MBEDTLS_ECJPAKE_CLIENT,
                                      MBEDTLS_MD_SHA256, MBEDTLS_ECP_DP_SECP256R1, pw,
                                      pw_len) == 0);

    TEST_ASSERT(ecjpake_test_load(&ctx,
                                  ADD_SIZE(ecjpake_test_x1), ADD_SIZE(ecjpake_test_x2),
                                  ADD_SIZE(ecjpake_test_X1), ADD_SIZE(ecjpake_test_X2),
                                  ADD_SIZE(ecjpake_test_X3), ADD_SIZE(ecjpake_test_X4))
                == 0);

    TEST_ASSERT(mbedtls_ecjpake_read_round_two(&ctx, msg->x, msg->len) == ref_ret);

exit:
    mbedtls_ecjpake_free(&ctx);
    MD_PSA_DONE();
}
/* END_CASE */

/* BEGIN_CASE depends_on:MBEDTLS_ECP_DP_SECP256R1_ENABLED:MBEDTLS_MD_CAN_SHA256 */
void read_round_two_srv(data_t *msg, int ref_ret)
{
    mbedtls_ecjpake_context ctx;
    const unsigned char *pw = NULL;
    const size_t pw_len = 0;

    MD_PSA_INIT();

    mbedtls_ecjpake_init(&ctx);

    TEST_ASSERT(mbedtls_ecjpake_setup(&ctx, MBEDTLS_ECJPAKE_SERVER,
                                      MBEDTLS_MD_SHA256, MBEDTLS_ECP_DP_SECP256R1, pw,
                                      pw_len) == 0);

    TEST_ASSERT(ecjpake_test_load(&ctx,
                                  ADD_SIZE(ecjpake_test_x3), ADD_SIZE(ecjpake_test_x4),
                                  ADD_SIZE(ecjpake_test_X3), ADD_SIZE(ecjpake_test_X4),
                                  ADD_SIZE(ecjpake_test_X1), ADD_SIZE(ecjpake_test_X2))
                == 0);

    TEST_ASSERT(mbedtls_ecjpake_read_round_two(&ctx, msg->x, msg->len) == ref_ret);

exit:
    mbedtls_ecjpake_free(&ctx);
    MD_PSA_DONE();
}
/* END_CASE */
