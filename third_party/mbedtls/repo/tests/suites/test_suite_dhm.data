Diffie-<PERSON><PERSON> full exchange: tiny x_size
dhm_do_dhm:"12df4d7689dff4c99d9ae57d7":1:"1e32158a35e34d7b619657d6":0

Diffie-<PERSON><PERSON> full exchange: 5-bit, x_size=3
dhm_do_dhm:"17":3:"5":0

Di<PERSON><PERSON>-<PERSON><PERSON> full exchange: 5-bit, x_size=2
dhm_do_dhm:"17":2:"5":0

## Repeat this test case and a few similar ones several times. The RNG state
## changes, so we get to exercise the code with a few different values.
Diffie-<PERSON>man full exchange: 5-bit #1
dhm_do_dhm:"17":1:"5":0

Di<PERSON><PERSON>-<PERSON><PERSON> full exchange: 5-bit #2
dhm_do_dhm:"17":1:"5":0

Diffie-<PERSON><PERSON> full exchange: 5-bit #3
dhm_do_dhm:"17":1:"5":0

Diffie-<PERSON><PERSON> full exchange: 5-bit #4
dhm_do_dhm:"17":1:"5":0

Di<PERSON><PERSON>-<PERSON><PERSON> full exchange: 5-bit #5
dhm_do_dhm:"17":1:"5":0

## This is x_size = P_size + 1. Arguably x_size > P_size makes no sense,
## but it's the current undocumented behavior to treat it the same as when
## x_size = P_size. If this behavior changes in the future, change the expected
## return status from 0 to MBEDTLS_ERR_DHM_BAD_INPUT_DATA.
Diffie-Hellman full exchange: 97-bit, x_size=14
dhm_do_dhm:"12df4d7689dff4c99d9ae57d7":14:"1e32158a35e34d7b619657d6":0

Diffie-Hellman full exchange: 97-bit #1
dhm_do_dhm:"12df4d7689dff4c99d9ae57d7":13:"1e32158a35e34d7b619657d6":0

Diffie-Hellman full exchange: 97-bit #2
dhm_do_dhm:"12df4d7689dff4c99d9ae57d7":13:"1e32158a35e34d7b619657d6":0

Diffie-Hellman full exchange: 97-bit #3
dhm_do_dhm:"12df4d7689dff4c99d9ae57d7":13:"1e32158a35e34d7b619657d6":0

Diffie-Hellman full exchange: 97-bit #4
dhm_do_dhm:"12df4d7689dff4c99d9ae57d7":13:"1e32158a35e34d7b619657d6":0

Diffie-Hellman full exchange: 97-bit #5
dhm_do_dhm:"12df4d7689dff4c99d9ae57d7":13:"1e32158a35e34d7b619657d6":0

Diffie-Hellman full exchange: 97-bit, x_size=12
dhm_do_dhm:"12df4d7689dff4c99d9ae57d7":12:"1e32158a35e34d7b619657d6":0

Diffie-Hellman full exchange: 97-bit, x_size=11
dhm_do_dhm:"12df4d7689dff4c99d9ae57d7":11:"1e32158a35e34d7b619657d6":0

Diffie-Hellman full exchange: 97-bit, x_size=1 #1
dhm_do_dhm:"12df4d7689dff4c99d9ae57d7":1:"1e32158a35e34d7b619657d6":0

Diffie-Hellman full exchange: 97-bit, x_size=1 #2
dhm_do_dhm:"12df4d7689dff4c99d9ae57d7":1:"1e32158a35e34d7b619657d6":0

Diffie-Hellman full exchange: 97-bit, x_size=1 #3
dhm_do_dhm:"12df4d7689dff4c99d9ae57d7":1:"1e32158a35e34d7b619657d6":0

Diffie-Hellman full exchange: 97-bit, x_size=1 #4
dhm_do_dhm:"12df4d7689dff4c99d9ae57d7":1:"1e32158a35e34d7b619657d6":0

Diffie-Hellman full exchange: 97-bit, x_size=1 #5
dhm_do_dhm:"12df4d7689dff4c99d9ae57d7":1:"1e32158a35e34d7b619657d6":0

Diffie-Hellman full exchange: 286-bit
dhm_do_dhm:"301abc09a57b66a953bfcc206a32e9ab56724084e4b47635779ca35fee79ce1060cb4117":36:"15aa1039b4dd361ed1b5b88e52f2919d0cbcb15adbe5fc290dab13b34e7":0

Diffie-Hellman small modulus
dhm_do_dhm:"3":1:"5":MBEDTLS_ERR_DHM_MAKE_PARAMS_FAILED+MBEDTLS_ERR_MPI_BAD_INPUT_DATA

Diffie-Hellman zero modulus
dhm_do_dhm:"0":1:"5":MBEDTLS_ERR_DHM_BAD_INPUT_DATA

Diffie-Hellman with G=0
dhm_do_dhm:"12df4d7689dff4c99d9ae57d7":13:"0":MBEDTLS_ERR_DHM_BAD_INPUT_DATA

Diffie-Hellman with G=1
dhm_do_dhm:"12df4d7689dff4c99d9ae57d7":13:"1":MBEDTLS_ERR_DHM_BAD_INPUT_DATA

Diffie-Hellman with G=-1
dhm_do_dhm:"12df4d7689dff4c99d9ae57d7":13:"-1":MBEDTLS_ERR_DHM_BAD_INPUT_DATA

Diffie-Hellman with G=P-1
dhm_do_dhm:"12df4d7689dff4c99d9ae57d7":13:"12df4d7689dff4c99d9ae57d6":MBEDTLS_ERR_DHM_BAD_INPUT_DATA

Diffie-Hellman with G=P-2
dhm_do_dhm:"12df4d7689dff4c99d9ae57d7":13:"12df4d7689dff4c99d9ae57d5":0

Diffie-Hellman with G=P
dhm_do_dhm:"12df4d7689dff4c99d9ae57d7":13:"12df4d7689dff4c99d9ae57d7":MBEDTLS_ERR_DHM_BAD_INPUT_DATA

Diffie-Hellman with G=P+1
dhm_do_dhm:"12df4d7689dff4c99d9ae57d7":13:"12df4d7689dff4c99d9ae57d8":MBEDTLS_ERR_DHM_BAD_INPUT_DATA

Diffie-Hellman with G=P+2
dhm_do_dhm:"12df4d7689dff4c99d9ae57d7":13:"12df4d7689dff4c99d9ae57d9":0

Diffie-Hellman: x_size < 0
dhm_do_dhm:"12df4d7689dff4c99d9ae57d7":-1:"1e32158a35e34d7b619657d6":MBEDTLS_ERR_DHM_BAD_INPUT_DATA

Diffie-Hellman MPI_MAX_SIZE modulus
dhm_make_public:MBEDTLS_MPI_MAX_SIZE:"5":0

Diffie-Hellman MPI_MAX_SIZE + 1 modulus
dhm_make_public:MBEDTLS_MPI_MAX_SIZE + 1:"5":MBEDTLS_ERR_DHM_MAKE_PUBLIC_FAILED+MBEDTLS_ERR_MPI_BAD_INPUT_DATA

DH load parameters from PEM file (1024-bit, g=2)
depends_on:MBEDTLS_PEM_PARSE_C
dhm_file:"data_files/dhparams.pem":"9e35f430443a09904f3a39a979797d070df53378e79c2438bef4e761f3c714553328589b041c809be1d6c6b5f1fc9f47d3a25443188253a992a56818b37ba9de5a40d362e56eff0be5417474c125c199272c8fe41dea733df6f662c92ae76556e755d10c64e6a50968f67fc6ea73d0dca8569be2ba204e23580d8bca2f4975b3":"02":128

DH load parameters from PEM file (2048-bit, large g, privateValueLength)
depends_on:MBEDTLS_PEM_PARSE_C
dhm_file:"data_files/dh.optlen.pem":"b3126aeaf47153c7d67f403030b292b5bd5a6c9eae1c137af34087fce2a36a578d70c5c560ad2bdb924c4a4dbee20a1671be7103ce87defa76908936803dbeca60c33e1289c1a03ac2c6c4e49405e5902fa0596a1cbaa895cc402d5213ed4a5f1f5ba8b5e1ed3da951a4c475afeb0ca660b7368c38c8e809f382d96ae19e60dc984e61cb42b5dfd723322acf327f9e413cda6400c15c5b2ea1fa34405d83982fba40e6d852da3d91019bf23511314254dc211a90833e5b1798ee52a78198c555644729ad92f060367c74ded37704adfc273a4a33fec821bd2ebd3bc051730e97a4dd14d2b766062592f5eec09d16bb50efebf2cc00dd3e0e3418e60ec84870f7":"800abfe7dc667aa17bcd7c04614bc221a65482ccc04b604602b0e131908a938ea11b48dc515dab7abcbb1e0c7fd66511edc0d86551b7632496e03df94357e1c4ea07a7ce1e381a2fcafdff5f5bf00df828806020e875c00926e4d011f88477a1b01927d73813cad4847c6396b9244621be2b00b63c659253318413443cd244215cd7fd4cbe796e82c6cf70f89cc0c528fb8e344809b31876e7ef739d5160d095c9684188b0c8755c7a468d47f56d6db9ea012924ecb0556fb71312a8d7c93bb2898ea08ee54eeb594548285f06a973cbbe2a0cb02e90f323fe045521f34c68354a6d3e95dbfff1eb64692edc0a44f3d3e408d0e479a541e779a6054259e2d854":256

DH load parameters from DER file (2048-bit, large g, privateValueLength)
dhm_file:"data_files/dh.optlen.der":"b3126aeaf47153c7d67f403030b292b5bd5a6c9eae1c137af34087fce2a36a578d70c5c560ad2bdb924c4a4dbee20a1671be7103ce87defa76908936803dbeca60c33e1289c1a03ac2c6c4e49405e5902fa0596a1cbaa895cc402d5213ed4a5f1f5ba8b5e1ed3da951a4c475afeb0ca660b7368c38c8e809f382d96ae19e60dc984e61cb42b5dfd723322acf327f9e413cda6400c15c5b2ea1fa34405d83982fba40e6d852da3d91019bf23511314254dc211a90833e5b1798ee52a78198c555644729ad92f060367c74ded37704adfc273a4a33fec821bd2ebd3bc051730e97a4dd14d2b766062592f5eec09d16bb50efebf2cc00dd3e0e3418e60ec84870f7":"800abfe7dc667aa17bcd7c04614bc221a65482ccc04b604602b0e131908a938ea11b48dc515dab7abcbb1e0c7fd66511edc0d86551b7632496e03df94357e1c4ea07a7ce1e381a2fcafdff5f5bf00df828806020e875c00926e4d011f88477a1b01927d73813cad4847c6396b9244621be2b00b63c659253318413443cd244215cd7fd4cbe796e82c6cf70f89cc0c528fb8e344809b31876e7ef739d5160d095c9684188b0c8755c7a468d47f56d6db9ea012924ecb0556fb71312a8d7c93bb2898ea08ee54eeb594548285f06a973cbbe2a0cb02e90f323fe045521f34c68354a6d3e95dbfff1eb64692edc0a44f3d3e408d0e479a541e779a6054259e2d854":256

Diffie-Hellman selftest
dhm_selftest:
