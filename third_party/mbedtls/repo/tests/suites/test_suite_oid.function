/* BEGIN_HEADER */
#include "mbedtls/oid.h"
#include "mbedtls/asn1.h"
#include "mbedtls/asn1write.h"
#include "string.h"
/* END_HEADER */

/* BEGIN_DEPENDENCIES
 * depends_on:MBEDTLS_OID_C:!MBEDTLS_X509_REMOVE_INFO
 * END_DEPENDENCIES
 */

/* BEGIN_CASE */
void oid_get_certificate_policies(data_t *oid, char *result_str)
{
    mbedtls_asn1_buf asn1_buf = { 0, 0, NULL };
    int ret;
    const char *desc;

    asn1_buf.tag = MBEDTLS_ASN1_OID;
    asn1_buf.p = oid->x;
    asn1_buf.len = oid->len;

    ret = mbedtls_oid_get_certificate_policies(&asn1_buf, &desc);
    if (strlen(result_str) == 0) {
        TEST_ASSERT(ret == MBEDTLS_ERR_OID_NOT_FOUND);
    } else {
        TEST_ASSERT(ret == 0);
        TEST_ASSERT(strcmp((char *) desc, result_str) == 0);
    }
}
/* END_CASE */

/* BEGIN_CASE */
void oid_get_extended_key_usage(data_t *oid, char *result_str)
{
    mbedtls_asn1_buf asn1_buf = { 0, 0, NULL };
    int ret;
    const char *desc;

    asn1_buf.tag = MBEDTLS_ASN1_OID;
    asn1_buf.p = oid->x;
    asn1_buf.len = oid->len;

    ret = mbedtls_oid_get_extended_key_usage(&asn1_buf, &desc);
    if (strlen(result_str) == 0) {
        TEST_ASSERT(ret == MBEDTLS_ERR_OID_NOT_FOUND);
    } else {
        TEST_ASSERT(ret == 0);
        TEST_ASSERT(strcmp((char *) desc, result_str) == 0);
    }
}
/* END_CASE */

/* BEGIN_CASE */
void oid_get_x509_extension(data_t *oid, int exp_type)
{
    mbedtls_asn1_buf ext_oid = { 0, 0, NULL };
    int ret;
    int ext_type;

    ext_oid.tag = MBEDTLS_ASN1_OID;
    ext_oid.p = oid->x;
    ext_oid.len = oid->len;

    ret = mbedtls_oid_get_x509_ext_type(&ext_oid, &ext_type);
    if (exp_type == 0) {
        TEST_ASSERT(ret == MBEDTLS_ERR_OID_NOT_FOUND);
    } else {
        TEST_ASSERT(ret == 0);
        TEST_ASSERT(ext_type == exp_type);
    }
}
/* END_CASE */

/* BEGIN_CASE */
void oid_get_md_alg_id(data_t *oid, int exp_md_id)
{
    mbedtls_asn1_buf md_oid = { 0, 0, NULL };
    int ret;
    mbedtls_md_type_t md_id = 0;

    md_oid.tag = MBEDTLS_ASN1_OID;
    md_oid.p = oid->x;
    md_oid.len = oid->len;

    ret = mbedtls_oid_get_md_alg(&md_oid, &md_id);

    if (exp_md_id < 0) {
        TEST_ASSERT(ret == MBEDTLS_ERR_OID_NOT_FOUND);
        TEST_ASSERT(md_id == 0);
    } else {
        TEST_ASSERT(ret == 0);
        TEST_ASSERT((mbedtls_md_type_t) exp_md_id == md_id);
    }
}
/* END_CASE */

/* BEGIN_CASE */
void mbedtls_oid_get_md_hmac(data_t *oid, int exp_md_id)
{
    mbedtls_asn1_buf md_oid = { 0, 0, NULL };
    int ret;
    mbedtls_md_type_t md_id = 0;

    md_oid.tag = MBEDTLS_ASN1_OID;
    md_oid.p = oid->x;
    md_oid.len = oid->len;

    ret = mbedtls_oid_get_md_hmac(&md_oid, &md_id);

    if (exp_md_id < 0) {
        TEST_ASSERT(ret == MBEDTLS_ERR_OID_NOT_FOUND);
        TEST_ASSERT(md_id == 0);
    } else {
        TEST_ASSERT(ret == 0);
        TEST_ASSERT((mbedtls_md_type_t) exp_md_id == md_id);
    }
}
/* END_CASE */

/* BEGIN_CASE */
void oid_get_numeric_string(data_t *oid, int error_ret, char *result_str)
{
    char buf[256];
    mbedtls_asn1_buf input_oid = { 0, 0, NULL };
    int ret;

    input_oid.tag = MBEDTLS_ASN1_OID;
    /* Test that an empty OID is not dereferenced */
    input_oid.p = oid->len ? oid->x : (void *) 1;
    input_oid.len = oid->len;

    ret = mbedtls_oid_get_numeric_string(buf, sizeof(buf), &input_oid);

    if (error_ret == 0) {
        TEST_EQUAL(ret, strlen(result_str));
        TEST_ASSERT(ret >= 3);
        TEST_EQUAL(strcmp(buf, result_str), 0);
    } else {
        TEST_EQUAL(ret, error_ret);
    }
}
/* END_CASE */

/* BEGIN_CASE */
void oid_from_numeric_string(char *oid_str, int error_ret,
                             data_t *exp_oid_buf)
{
    mbedtls_asn1_buf oid = { 0, 0, NULL };
    mbedtls_asn1_buf exp_oid = { 0, 0, NULL };
    int ret;

    exp_oid.tag = MBEDTLS_ASN1_OID;
    exp_oid.p = exp_oid_buf->x;
    exp_oid.len = exp_oid_buf->len;

    ret = mbedtls_oid_from_numeric_string(&oid, oid_str, strlen(oid_str));

    if (error_ret == 0) {
        TEST_EQUAL(oid.len, exp_oid.len);
        TEST_ASSERT(memcmp(oid.p, exp_oid.p, oid.len) == 0);
        mbedtls_free(oid.p);
        oid.p = NULL;
        oid.len = 0;
    } else {
        TEST_EQUAL(ret, error_ret);
    }
}
/* END_CASE */
