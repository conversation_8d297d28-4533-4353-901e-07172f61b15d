# Automatically generated by generate_psa_tests.py. Do not edit!

PSA AES 128-bit
depends_on:PSA_WANT_KEY_TYPE_AES
generate_key:PSA_KEY_TYPE_AES:128:PSA_SUCCESS:

PSA AES 192-bit
depends_on:PSA_WANT_KEY_TYPE_AES
generate_key:PSA_KEY_TYPE_AES:192:PSA_SUCCESS:

PSA AES 256-bit
depends_on:PSA_WANT_KEY_TYPE_AES
generate_key:PSA_KEY_TYPE_AES:256:PSA_SUCCESS:

PSA ARIA 128-bit
depends_on:PSA_WANT_KEY_TYPE_ARIA
generate_key:PSA_KEY_TYPE_ARIA:128:PSA_SUCCESS:

PSA ARIA 192-bit
depends_on:PSA_WANT_KEY_TYPE_ARIA
generate_key:PSA_KEY_TYPE_ARIA:192:PSA_SUCCESS:

PSA ARIA 256-bit
depends_on:PSA_WANT_KEY_TYPE_ARIA
generate_key:PSA_KEY_TYPE_ARIA:256:PSA_SUCCESS:

PSA CAMELLIA 128-bit
depends_on:PSA_WANT_KEY_TYPE_CAMELLIA
generate_key:PSA_KEY_TYPE_CAMELLIA:128:PSA_SUCCESS:

PSA CAMELLIA 192-bit
depends_on:PSA_WANT_KEY_TYPE_CAMELLIA
generate_key:PSA_KEY_TYPE_CAMELLIA:192:PSA_SUCCESS:

PSA CAMELLIA 256-bit
depends_on:PSA_WANT_KEY_TYPE_CAMELLIA
generate_key:PSA_KEY_TYPE_CAMELLIA:256:PSA_SUCCESS:

PSA CHACHA20 256-bit
depends_on:PSA_WANT_KEY_TYPE_CHACHA20
generate_key:PSA_KEY_TYPE_CHACHA20:256:PSA_SUCCESS:

PSA DERIVE 120-bit
depends_on:PSA_WANT_KEY_TYPE_DERIVE
generate_key:PSA_KEY_TYPE_DERIVE:120:PSA_SUCCESS:

PSA DERIVE 128-bit
depends_on:PSA_WANT_KEY_TYPE_DERIVE
generate_key:PSA_KEY_TYPE_DERIVE:128:PSA_SUCCESS:

PSA DES 64-bit
depends_on:PSA_WANT_KEY_TYPE_DES
generate_key:PSA_KEY_TYPE_DES:64:PSA_SUCCESS:

PSA DES 128-bit
depends_on:PSA_WANT_KEY_TYPE_DES
generate_key:PSA_KEY_TYPE_DES:128:PSA_SUCCESS:

PSA DES 192-bit
depends_on:PSA_WANT_KEY_TYPE_DES
generate_key:PSA_KEY_TYPE_DES:192:PSA_SUCCESS:

PSA HMAC 128-bit
depends_on:PSA_WANT_KEY_TYPE_HMAC
generate_key:PSA_KEY_TYPE_HMAC:128:PSA_SUCCESS:

PSA HMAC 160-bit
depends_on:PSA_WANT_KEY_TYPE_HMAC
generate_key:PSA_KEY_TYPE_HMAC:160:PSA_SUCCESS:

PSA HMAC 224-bit
depends_on:PSA_WANT_KEY_TYPE_HMAC
generate_key:PSA_KEY_TYPE_HMAC:224:PSA_SUCCESS:

PSA HMAC 256-bit
depends_on:PSA_WANT_KEY_TYPE_HMAC
generate_key:PSA_KEY_TYPE_HMAC:256:PSA_SUCCESS:

PSA HMAC 384-bit
depends_on:PSA_WANT_KEY_TYPE_HMAC
generate_key:PSA_KEY_TYPE_HMAC:384:PSA_SUCCESS:

PSA HMAC 512-bit
depends_on:PSA_WANT_KEY_TYPE_HMAC
generate_key:PSA_KEY_TYPE_HMAC:512:PSA_SUCCESS:

PSA PASSWORD 48-bit
depends_on:PSA_WANT_KEY_TYPE_PASSWORD
generate_key:PSA_KEY_TYPE_PASSWORD:48:PSA_SUCCESS:

PSA PASSWORD 168-bit
depends_on:PSA_WANT_KEY_TYPE_PASSWORD
generate_key:PSA_KEY_TYPE_PASSWORD:168:PSA_SUCCESS:

PSA PASSWORD 336-bit
depends_on:PSA_WANT_KEY_TYPE_PASSWORD
generate_key:PSA_KEY_TYPE_PASSWORD:336:PSA_SUCCESS:

PSA PASSWORD_HASH 128-bit
depends_on:PSA_WANT_KEY_TYPE_PASSWORD_HASH
generate_key:PSA_KEY_TYPE_PASSWORD_HASH:128:PSA_SUCCESS:

PSA PASSWORD_HASH 256-bit
depends_on:PSA_WANT_KEY_TYPE_PASSWORD_HASH
generate_key:PSA_KEY_TYPE_PASSWORD_HASH:256:PSA_SUCCESS:

PSA PEPPER 128-bit
depends_on:PSA_WANT_KEY_TYPE_PEPPER:DEPENDENCY_NOT_IMPLEMENTED_YET
generate_key:PSA_KEY_TYPE_PEPPER:128:PSA_SUCCESS:

PSA PEPPER 256-bit
depends_on:PSA_WANT_KEY_TYPE_PEPPER:DEPENDENCY_NOT_IMPLEMENTED_YET
generate_key:PSA_KEY_TYPE_PEPPER:256:PSA_SUCCESS:

PSA RAW_DATA 8-bit
depends_on:PSA_WANT_KEY_TYPE_RAW_DATA
generate_key:PSA_KEY_TYPE_RAW_DATA:8:PSA_SUCCESS:

PSA RAW_DATA 40-bit
depends_on:PSA_WANT_KEY_TYPE_RAW_DATA
generate_key:PSA_KEY_TYPE_RAW_DATA:40:PSA_SUCCESS:

PSA RAW_DATA 128-bit
depends_on:PSA_WANT_KEY_TYPE_RAW_DATA
generate_key:PSA_KEY_TYPE_RAW_DATA:128:PSA_SUCCESS:

PSA RSA_KEY_PAIR 1024-bit
depends_on:PSA_WANT_KEY_TYPE_RSA_KEY_PAIR_GENERATE:PSA_VENDOR_RSA_GENERATE_MIN_KEY_BITS <= 1024
generate_key:PSA_KEY_TYPE_RSA_KEY_PAIR:1024:PSA_SUCCESS:

PSA RSA_KEY_PAIR 1536-bit
depends_on:PSA_WANT_KEY_TYPE_RSA_KEY_PAIR_GENERATE:PSA_VENDOR_RSA_GENERATE_MIN_KEY_BITS <= 1536
generate_key:PSA_KEY_TYPE_RSA_KEY_PAIR:1536:PSA_SUCCESS:

PSA RSA_PUBLIC_KEY 1024-bit
generate_key:PSA_KEY_TYPE_RSA_PUBLIC_KEY:1024:PSA_ERROR_INVALID_ARGUMENT:

PSA RSA_PUBLIC_KEY 1536-bit
generate_key:PSA_KEY_TYPE_RSA_PUBLIC_KEY:1536:PSA_ERROR_INVALID_ARGUMENT:

PSA ECC_KEY_PAIR(BRAINPOOL_P_R1) 160-bit
depends_on:PSA_WANT_KEY_TYPE_ECC_KEY_PAIR_GENERATE:PSA_WANT_ECC_BRAINPOOL_P_R1_160:DEPENDENCY_NOT_IMPLEMENTED_YET
generate_key:PSA_KEY_TYPE_ECC_KEY_PAIR(PSA_ECC_FAMILY_BRAINPOOL_P_R1):160:PSA_SUCCESS:

PSA ECC_KEY_PAIR(BRAINPOOL_P_R1) 192-bit
depends_on:PSA_WANT_KEY_TYPE_ECC_KEY_PAIR_GENERATE:PSA_WANT_ECC_BRAINPOOL_P_R1_192:DEPENDENCY_NOT_IMPLEMENTED_YET
generate_key:PSA_KEY_TYPE_ECC_KEY_PAIR(PSA_ECC_FAMILY_BRAINPOOL_P_R1):192:PSA_SUCCESS:

PSA ECC_KEY_PAIR(BRAINPOOL_P_R1) 224-bit
depends_on:PSA_WANT_KEY_TYPE_ECC_KEY_PAIR_GENERATE:PSA_WANT_ECC_BRAINPOOL_P_R1_224:DEPENDENCY_NOT_IMPLEMENTED_YET
generate_key:PSA_KEY_TYPE_ECC_KEY_PAIR(PSA_ECC_FAMILY_BRAINPOOL_P_R1):224:PSA_SUCCESS:

PSA ECC_KEY_PAIR(BRAINPOOL_P_R1) 256-bit
depends_on:PSA_WANT_KEY_TYPE_ECC_KEY_PAIR_GENERATE:PSA_WANT_ECC_BRAINPOOL_P_R1_256
generate_key:PSA_KEY_TYPE_ECC_KEY_PAIR(PSA_ECC_FAMILY_BRAINPOOL_P_R1):256:PSA_SUCCESS:

PSA ECC_KEY_PAIR(BRAINPOOL_P_R1) 320-bit
depends_on:PSA_WANT_KEY_TYPE_ECC_KEY_PAIR_GENERATE:PSA_WANT_ECC_BRAINPOOL_P_R1_320:DEPENDENCY_NOT_IMPLEMENTED_YET
generate_key:PSA_KEY_TYPE_ECC_KEY_PAIR(PSA_ECC_FAMILY_BRAINPOOL_P_R1):320:PSA_SUCCESS:

PSA ECC_KEY_PAIR(BRAINPOOL_P_R1) 384-bit
depends_on:PSA_WANT_KEY_TYPE_ECC_KEY_PAIR_GENERATE:PSA_WANT_ECC_BRAINPOOL_P_R1_384
generate_key:PSA_KEY_TYPE_ECC_KEY_PAIR(PSA_ECC_FAMILY_BRAINPOOL_P_R1):384:PSA_SUCCESS:

PSA ECC_KEY_PAIR(BRAINPOOL_P_R1) 512-bit
depends_on:PSA_WANT_KEY_TYPE_ECC_KEY_PAIR_GENERATE:PSA_WANT_ECC_BRAINPOOL_P_R1_512
generate_key:PSA_KEY_TYPE_ECC_KEY_PAIR(PSA_ECC_FAMILY_BRAINPOOL_P_R1):512:PSA_SUCCESS:

PSA ECC_PUBLIC_KEY(BRAINPOOL_P_R1) 160-bit
generate_key:PSA_KEY_TYPE_ECC_PUBLIC_KEY(PSA_ECC_FAMILY_BRAINPOOL_P_R1):160:PSA_ERROR_INVALID_ARGUMENT:

PSA ECC_PUBLIC_KEY(BRAINPOOL_P_R1) 192-bit
generate_key:PSA_KEY_TYPE_ECC_PUBLIC_KEY(PSA_ECC_FAMILY_BRAINPOOL_P_R1):192:PSA_ERROR_INVALID_ARGUMENT:

PSA ECC_PUBLIC_KEY(BRAINPOOL_P_R1) 224-bit
generate_key:PSA_KEY_TYPE_ECC_PUBLIC_KEY(PSA_ECC_FAMILY_BRAINPOOL_P_R1):224:PSA_ERROR_INVALID_ARGUMENT:

PSA ECC_PUBLIC_KEY(BRAINPOOL_P_R1) 256-bit
generate_key:PSA_KEY_TYPE_ECC_PUBLIC_KEY(PSA_ECC_FAMILY_BRAINPOOL_P_R1):256:PSA_ERROR_INVALID_ARGUMENT:

PSA ECC_PUBLIC_KEY(BRAINPOOL_P_R1) 320-bit
generate_key:PSA_KEY_TYPE_ECC_PUBLIC_KEY(PSA_ECC_FAMILY_BRAINPOOL_P_R1):320:PSA_ERROR_INVALID_ARGUMENT:

PSA ECC_PUBLIC_KEY(BRAINPOOL_P_R1) 384-bit
generate_key:PSA_KEY_TYPE_ECC_PUBLIC_KEY(PSA_ECC_FAMILY_BRAINPOOL_P_R1):384:PSA_ERROR_INVALID_ARGUMENT:

PSA ECC_PUBLIC_KEY(BRAINPOOL_P_R1) 512-bit
generate_key:PSA_KEY_TYPE_ECC_PUBLIC_KEY(PSA_ECC_FAMILY_BRAINPOOL_P_R1):512:PSA_ERROR_INVALID_ARGUMENT:

PSA ECC_KEY_PAIR(MONTGOMERY) 255-bit
depends_on:PSA_WANT_KEY_TYPE_ECC_KEY_PAIR_GENERATE:PSA_WANT_ECC_MONTGOMERY_255
generate_key:PSA_KEY_TYPE_ECC_KEY_PAIR(PSA_ECC_FAMILY_MONTGOMERY):255:PSA_SUCCESS:

PSA ECC_KEY_PAIR(MONTGOMERY) 448-bit
depends_on:PSA_WANT_KEY_TYPE_ECC_KEY_PAIR_GENERATE:PSA_WANT_ECC_MONTGOMERY_448
generate_key:PSA_KEY_TYPE_ECC_KEY_PAIR(PSA_ECC_FAMILY_MONTGOMERY):448:PSA_SUCCESS:

PSA ECC_PUBLIC_KEY(MONTGOMERY) 255-bit
generate_key:PSA_KEY_TYPE_ECC_PUBLIC_KEY(PSA_ECC_FAMILY_MONTGOMERY):255:PSA_ERROR_INVALID_ARGUMENT:

PSA ECC_PUBLIC_KEY(MONTGOMERY) 448-bit
generate_key:PSA_KEY_TYPE_ECC_PUBLIC_KEY(PSA_ECC_FAMILY_MONTGOMERY):448:PSA_ERROR_INVALID_ARGUMENT:

PSA ECC_KEY_PAIR(SECP_K1) 192-bit
depends_on:PSA_WANT_KEY_TYPE_ECC_KEY_PAIR_GENERATE:PSA_WANT_ECC_SECP_K1_192
generate_key:PSA_KEY_TYPE_ECC_KEY_PAIR(PSA_ECC_FAMILY_SECP_K1):192:PSA_SUCCESS:

PSA ECC_KEY_PAIR(SECP_K1) 225-bit
depends_on:PSA_WANT_KEY_TYPE_ECC_KEY_PAIR_GENERATE:PSA_WANT_ECC_SECP_K1_225:DEPENDENCY_NOT_IMPLEMENTED_YET
generate_key:PSA_KEY_TYPE_ECC_KEY_PAIR(PSA_ECC_FAMILY_SECP_K1):225:PSA_SUCCESS:

PSA ECC_KEY_PAIR(SECP_K1) 256-bit
depends_on:PSA_WANT_KEY_TYPE_ECC_KEY_PAIR_GENERATE:PSA_WANT_ECC_SECP_K1_256
generate_key:PSA_KEY_TYPE_ECC_KEY_PAIR(PSA_ECC_FAMILY_SECP_K1):256:PSA_SUCCESS:

PSA ECC_PUBLIC_KEY(SECP_K1) 192-bit
generate_key:PSA_KEY_TYPE_ECC_PUBLIC_KEY(PSA_ECC_FAMILY_SECP_K1):192:PSA_ERROR_INVALID_ARGUMENT:

PSA ECC_PUBLIC_KEY(SECP_K1) 225-bit
generate_key:PSA_KEY_TYPE_ECC_PUBLIC_KEY(PSA_ECC_FAMILY_SECP_K1):225:PSA_ERROR_INVALID_ARGUMENT:

PSA ECC_PUBLIC_KEY(SECP_K1) 256-bit
generate_key:PSA_KEY_TYPE_ECC_PUBLIC_KEY(PSA_ECC_FAMILY_SECP_K1):256:PSA_ERROR_INVALID_ARGUMENT:

PSA ECC_KEY_PAIR(SECP_R1) 224-bit
depends_on:PSA_WANT_KEY_TYPE_ECC_KEY_PAIR_GENERATE:PSA_WANT_ECC_SECP_R1_224
generate_key:PSA_KEY_TYPE_ECC_KEY_PAIR(PSA_ECC_FAMILY_SECP_R1):224:PSA_SUCCESS:

PSA ECC_KEY_PAIR(SECP_R1) 256-bit
depends_on:PSA_WANT_KEY_TYPE_ECC_KEY_PAIR_GENERATE:PSA_WANT_ECC_SECP_R1_256
generate_key:PSA_KEY_TYPE_ECC_KEY_PAIR(PSA_ECC_FAMILY_SECP_R1):256:PSA_SUCCESS:

PSA ECC_KEY_PAIR(SECP_R1) 384-bit
depends_on:PSA_WANT_KEY_TYPE_ECC_KEY_PAIR_GENERATE:PSA_WANT_ECC_SECP_R1_384
generate_key:PSA_KEY_TYPE_ECC_KEY_PAIR(PSA_ECC_FAMILY_SECP_R1):384:PSA_SUCCESS:

PSA ECC_KEY_PAIR(SECP_R1) 521-bit
depends_on:PSA_WANT_KEY_TYPE_ECC_KEY_PAIR_GENERATE:PSA_WANT_ECC_SECP_R1_521
generate_key:PSA_KEY_TYPE_ECC_KEY_PAIR(PSA_ECC_FAMILY_SECP_R1):521:PSA_SUCCESS:

PSA ECC_PUBLIC_KEY(SECP_R1) 224-bit
generate_key:PSA_KEY_TYPE_ECC_PUBLIC_KEY(PSA_ECC_FAMILY_SECP_R1):224:PSA_ERROR_INVALID_ARGUMENT:

PSA ECC_PUBLIC_KEY(SECP_R1) 256-bit
generate_key:PSA_KEY_TYPE_ECC_PUBLIC_KEY(PSA_ECC_FAMILY_SECP_R1):256:PSA_ERROR_INVALID_ARGUMENT:

PSA ECC_PUBLIC_KEY(SECP_R1) 384-bit
generate_key:PSA_KEY_TYPE_ECC_PUBLIC_KEY(PSA_ECC_FAMILY_SECP_R1):384:PSA_ERROR_INVALID_ARGUMENT:

PSA ECC_PUBLIC_KEY(SECP_R1) 521-bit
generate_key:PSA_KEY_TYPE_ECC_PUBLIC_KEY(PSA_ECC_FAMILY_SECP_R1):521:PSA_ERROR_INVALID_ARGUMENT:

PSA ECC_KEY_PAIR(SECP_R2) 160-bit
depends_on:PSA_WANT_KEY_TYPE_ECC_KEY_PAIR_GENERATE:PSA_WANT_ECC_SECP_R2_160:DEPENDENCY_NOT_IMPLEMENTED_YET
generate_key:PSA_KEY_TYPE_ECC_KEY_PAIR(PSA_ECC_FAMILY_SECP_R2):160:PSA_SUCCESS:

PSA ECC_PUBLIC_KEY(SECP_R2) 160-bit
generate_key:PSA_KEY_TYPE_ECC_PUBLIC_KEY(PSA_ECC_FAMILY_SECP_R2):160:PSA_ERROR_INVALID_ARGUMENT:

PSA ECC_KEY_PAIR(SECT_K1) 163-bit
depends_on:PSA_WANT_KEY_TYPE_ECC_KEY_PAIR_GENERATE:PSA_WANT_ECC_SECT_K1_163:DEPENDENCY_NOT_IMPLEMENTED_YET
generate_key:PSA_KEY_TYPE_ECC_KEY_PAIR(PSA_ECC_FAMILY_SECT_K1):163:PSA_SUCCESS:

PSA ECC_KEY_PAIR(SECT_K1) 233-bit
depends_on:PSA_WANT_KEY_TYPE_ECC_KEY_PAIR_GENERATE:PSA_WANT_ECC_SECT_K1_233:DEPENDENCY_NOT_IMPLEMENTED_YET
generate_key:PSA_KEY_TYPE_ECC_KEY_PAIR(PSA_ECC_FAMILY_SECT_K1):233:PSA_SUCCESS:

PSA ECC_KEY_PAIR(SECT_K1) 239-bit
depends_on:PSA_WANT_KEY_TYPE_ECC_KEY_PAIR_GENERATE:PSA_WANT_ECC_SECT_K1_239:DEPENDENCY_NOT_IMPLEMENTED_YET
generate_key:PSA_KEY_TYPE_ECC_KEY_PAIR(PSA_ECC_FAMILY_SECT_K1):239:PSA_SUCCESS:

PSA ECC_KEY_PAIR(SECT_K1) 283-bit
depends_on:PSA_WANT_KEY_TYPE_ECC_KEY_PAIR_GENERATE:PSA_WANT_ECC_SECT_K1_283:DEPENDENCY_NOT_IMPLEMENTED_YET
generate_key:PSA_KEY_TYPE_ECC_KEY_PAIR(PSA_ECC_FAMILY_SECT_K1):283:PSA_SUCCESS:

PSA ECC_KEY_PAIR(SECT_K1) 409-bit
depends_on:PSA_WANT_KEY_TYPE_ECC_KEY_PAIR_GENERATE:PSA_WANT_ECC_SECT_K1_409:DEPENDENCY_NOT_IMPLEMENTED_YET
generate_key:PSA_KEY_TYPE_ECC_KEY_PAIR(PSA_ECC_FAMILY_SECT_K1):409:PSA_SUCCESS:

PSA ECC_KEY_PAIR(SECT_K1) 571-bit
depends_on:PSA_WANT_KEY_TYPE_ECC_KEY_PAIR_GENERATE:PSA_WANT_ECC_SECT_K1_571:DEPENDENCY_NOT_IMPLEMENTED_YET
generate_key:PSA_KEY_TYPE_ECC_KEY_PAIR(PSA_ECC_FAMILY_SECT_K1):571:PSA_SUCCESS:

PSA ECC_PUBLIC_KEY(SECT_K1) 163-bit
generate_key:PSA_KEY_TYPE_ECC_PUBLIC_KEY(PSA_ECC_FAMILY_SECT_K1):163:PSA_ERROR_INVALID_ARGUMENT:

PSA ECC_PUBLIC_KEY(SECT_K1) 233-bit
generate_key:PSA_KEY_TYPE_ECC_PUBLIC_KEY(PSA_ECC_FAMILY_SECT_K1):233:PSA_ERROR_INVALID_ARGUMENT:

PSA ECC_PUBLIC_KEY(SECT_K1) 239-bit
generate_key:PSA_KEY_TYPE_ECC_PUBLIC_KEY(PSA_ECC_FAMILY_SECT_K1):239:PSA_ERROR_INVALID_ARGUMENT:

PSA ECC_PUBLIC_KEY(SECT_K1) 283-bit
generate_key:PSA_KEY_TYPE_ECC_PUBLIC_KEY(PSA_ECC_FAMILY_SECT_K1):283:PSA_ERROR_INVALID_ARGUMENT:

PSA ECC_PUBLIC_KEY(SECT_K1) 409-bit
generate_key:PSA_KEY_TYPE_ECC_PUBLIC_KEY(PSA_ECC_FAMILY_SECT_K1):409:PSA_ERROR_INVALID_ARGUMENT:

PSA ECC_PUBLIC_KEY(SECT_K1) 571-bit
generate_key:PSA_KEY_TYPE_ECC_PUBLIC_KEY(PSA_ECC_FAMILY_SECT_K1):571:PSA_ERROR_INVALID_ARGUMENT:

PSA ECC_KEY_PAIR(SECT_R1) 163-bit
depends_on:PSA_WANT_KEY_TYPE_ECC_KEY_PAIR_GENERATE:PSA_WANT_ECC_SECT_R1_163:DEPENDENCY_NOT_IMPLEMENTED_YET
generate_key:PSA_KEY_TYPE_ECC_KEY_PAIR(PSA_ECC_FAMILY_SECT_R1):163:PSA_SUCCESS:

PSA ECC_KEY_PAIR(SECT_R1) 233-bit
depends_on:PSA_WANT_KEY_TYPE_ECC_KEY_PAIR_GENERATE:PSA_WANT_ECC_SECT_R1_233:DEPENDENCY_NOT_IMPLEMENTED_YET
generate_key:PSA_KEY_TYPE_ECC_KEY_PAIR(PSA_ECC_FAMILY_SECT_R1):233:PSA_SUCCESS:

PSA ECC_KEY_PAIR(SECT_R1) 283-bit
depends_on:PSA_WANT_KEY_TYPE_ECC_KEY_PAIR_GENERATE:PSA_WANT_ECC_SECT_R1_283:DEPENDENCY_NOT_IMPLEMENTED_YET
generate_key:PSA_KEY_TYPE_ECC_KEY_PAIR(PSA_ECC_FAMILY_SECT_R1):283:PSA_SUCCESS:

PSA ECC_KEY_PAIR(SECT_R1) 409-bit
depends_on:PSA_WANT_KEY_TYPE_ECC_KEY_PAIR_GENERATE:PSA_WANT_ECC_SECT_R1_409:DEPENDENCY_NOT_IMPLEMENTED_YET
generate_key:PSA_KEY_TYPE_ECC_KEY_PAIR(PSA_ECC_FAMILY_SECT_R1):409:PSA_SUCCESS:

PSA ECC_KEY_PAIR(SECT_R1) 571-bit
depends_on:PSA_WANT_KEY_TYPE_ECC_KEY_PAIR_GENERATE:PSA_WANT_ECC_SECT_R1_571:DEPENDENCY_NOT_IMPLEMENTED_YET
generate_key:PSA_KEY_TYPE_ECC_KEY_PAIR(PSA_ECC_FAMILY_SECT_R1):571:PSA_SUCCESS:

PSA ECC_PUBLIC_KEY(SECT_R1) 163-bit
generate_key:PSA_KEY_TYPE_ECC_PUBLIC_KEY(PSA_ECC_FAMILY_SECT_R1):163:PSA_ERROR_INVALID_ARGUMENT:

PSA ECC_PUBLIC_KEY(SECT_R1) 233-bit
generate_key:PSA_KEY_TYPE_ECC_PUBLIC_KEY(PSA_ECC_FAMILY_SECT_R1):233:PSA_ERROR_INVALID_ARGUMENT:

PSA ECC_PUBLIC_KEY(SECT_R1) 283-bit
generate_key:PSA_KEY_TYPE_ECC_PUBLIC_KEY(PSA_ECC_FAMILY_SECT_R1):283:PSA_ERROR_INVALID_ARGUMENT:

PSA ECC_PUBLIC_KEY(SECT_R1) 409-bit
generate_key:PSA_KEY_TYPE_ECC_PUBLIC_KEY(PSA_ECC_FAMILY_SECT_R1):409:PSA_ERROR_INVALID_ARGUMENT:

PSA ECC_PUBLIC_KEY(SECT_R1) 571-bit
generate_key:PSA_KEY_TYPE_ECC_PUBLIC_KEY(PSA_ECC_FAMILY_SECT_R1):571:PSA_ERROR_INVALID_ARGUMENT:

PSA ECC_KEY_PAIR(SECT_R2) 163-bit
depends_on:PSA_WANT_KEY_TYPE_ECC_KEY_PAIR_GENERATE:PSA_WANT_ECC_SECT_R2_163:DEPENDENCY_NOT_IMPLEMENTED_YET
generate_key:PSA_KEY_TYPE_ECC_KEY_PAIR(PSA_ECC_FAMILY_SECT_R2):163:PSA_SUCCESS:

PSA ECC_PUBLIC_KEY(SECT_R2) 163-bit
generate_key:PSA_KEY_TYPE_ECC_PUBLIC_KEY(PSA_ECC_FAMILY_SECT_R2):163:PSA_ERROR_INVALID_ARGUMENT:

PSA ECC_KEY_PAIR(TWISTED_EDWARDS) 255-bit
depends_on:PSA_WANT_KEY_TYPE_ECC_KEY_PAIR_GENERATE:PSA_WANT_ECC_TWISTED_EDWARDS_255:DEPENDENCY_NOT_IMPLEMENTED_YET
generate_key:PSA_KEY_TYPE_ECC_KEY_PAIR(PSA_ECC_FAMILY_TWISTED_EDWARDS):255:PSA_SUCCESS:

PSA ECC_KEY_PAIR(TWISTED_EDWARDS) 448-bit
depends_on:PSA_WANT_KEY_TYPE_ECC_KEY_PAIR_GENERATE:PSA_WANT_ECC_TWISTED_EDWARDS_448:DEPENDENCY_NOT_IMPLEMENTED_YET
generate_key:PSA_KEY_TYPE_ECC_KEY_PAIR(PSA_ECC_FAMILY_TWISTED_EDWARDS):448:PSA_SUCCESS:

PSA ECC_PUBLIC_KEY(TWISTED_EDWARDS) 255-bit
generate_key:PSA_KEY_TYPE_ECC_PUBLIC_KEY(PSA_ECC_FAMILY_TWISTED_EDWARDS):255:PSA_ERROR_INVALID_ARGUMENT:

PSA ECC_PUBLIC_KEY(TWISTED_EDWARDS) 448-bit
generate_key:PSA_KEY_TYPE_ECC_PUBLIC_KEY(PSA_ECC_FAMILY_TWISTED_EDWARDS):448:PSA_ERROR_INVALID_ARGUMENT:

PSA DH_KEY_PAIR(RFC7919) 2048-bit
depends_on:PSA_WANT_KEY_TYPE_DH_KEY_PAIR_GENERATE:PSA_WANT_DH_RFC7919_2048
generate_key:PSA_KEY_TYPE_DH_KEY_PAIR(PSA_DH_FAMILY_RFC7919):2048:PSA_SUCCESS:

PSA DH_KEY_PAIR(RFC7919) 3072-bit
depends_on:PSA_WANT_KEY_TYPE_DH_KEY_PAIR_GENERATE:PSA_WANT_DH_RFC7919_3072
generate_key:PSA_KEY_TYPE_DH_KEY_PAIR(PSA_DH_FAMILY_RFC7919):3072:PSA_SUCCESS:

PSA DH_KEY_PAIR(RFC7919) 4096-bit
depends_on:PSA_WANT_KEY_TYPE_DH_KEY_PAIR_GENERATE:PSA_WANT_DH_RFC7919_4096
generate_key:PSA_KEY_TYPE_DH_KEY_PAIR(PSA_DH_FAMILY_RFC7919):4096:PSA_SUCCESS:

PSA DH_KEY_PAIR(RFC7919) 6144-bit
depends_on:PSA_WANT_KEY_TYPE_DH_KEY_PAIR_GENERATE:PSA_WANT_DH_RFC7919_6144
generate_key:PSA_KEY_TYPE_DH_KEY_PAIR(PSA_DH_FAMILY_RFC7919):6144:PSA_SUCCESS:

PSA DH_KEY_PAIR(RFC7919) 8192-bit
depends_on:PSA_WANT_KEY_TYPE_DH_KEY_PAIR_GENERATE:PSA_WANT_DH_RFC7919_8192
generate_key:PSA_KEY_TYPE_DH_KEY_PAIR(PSA_DH_FAMILY_RFC7919):8192:PSA_SUCCESS:

PSA DH_PUBLIC_KEY(RFC7919) 2048-bit
generate_key:PSA_KEY_TYPE_DH_PUBLIC_KEY(PSA_DH_FAMILY_RFC7919):2048:PSA_ERROR_INVALID_ARGUMENT:

PSA DH_PUBLIC_KEY(RFC7919) 3072-bit
generate_key:PSA_KEY_TYPE_DH_PUBLIC_KEY(PSA_DH_FAMILY_RFC7919):3072:PSA_ERROR_INVALID_ARGUMENT:

PSA DH_PUBLIC_KEY(RFC7919) 4096-bit
generate_key:PSA_KEY_TYPE_DH_PUBLIC_KEY(PSA_DH_FAMILY_RFC7919):4096:PSA_ERROR_INVALID_ARGUMENT:

PSA DH_PUBLIC_KEY(RFC7919) 6144-bit
generate_key:PSA_KEY_TYPE_DH_PUBLIC_KEY(PSA_DH_FAMILY_RFC7919):6144:PSA_ERROR_INVALID_ARGUMENT:

PSA DH_PUBLIC_KEY(RFC7919) 8192-bit
generate_key:PSA_KEY_TYPE_DH_PUBLIC_KEY(PSA_DH_FAMILY_RFC7919):8192:PSA_ERROR_INVALID_ARGUMENT:

# End of automatically generated file.
