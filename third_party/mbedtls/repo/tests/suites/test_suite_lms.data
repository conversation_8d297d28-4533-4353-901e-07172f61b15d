LMS sign-verify test
# This test uses a fixed message, and then generates a private key, signs the
# message, and verifies the signature.
lms_sign_verify_test:"c41ba177a0ca1ec31dfb2e145237e65b":"626201f41afd7c9af793cf158da58e33"

LMS NULL-message sign-verify test
# This test uses a NULL zero-length message, and then generates a private key,
# signs the message, and verifies the signature.
lms_sign_verify_null_msg_test:"923a3c8e38c9b72e067996bfdaa36856"

LMS pyhsslms interop test #1
# This test uses data from https://github.com/russhousley/pyhsslms due to the
# limited amount of available test vectors for LMS. The private key is stored in
# data_files/lms_pyhsslms_sha256_m32_h5_lmots_sha256_n32_w8_prv. Note that this signature
# uses leaf key 0, so must be the first signature generated by the key if the
# signature is to be reproduced. Message data is random. Note that pyhsslms
# stores public keys and signatures in HSS form, which appends a 4-byte "levels"
# word at the start of the key/sig. We strip these 4 bytes from the signature
# and the public key before including them in a the test data.
#
# To produce another signature with this message and key (note that the actual
# signature bytes will differ due to randomization):
# * pip3 install --user pyhsslms
# * cp data_files/lms_pyhsslms_sha256_m32_h5_lmots_sha256_n32_w8_prv tmp/lms.prv
# * cp data_files/lms_pyhsslms_sha256_m32_h5_lmots_sha256_n32_w8_pub tmp/lms.pub
#
# import pyhsslms
#
# private_key = pyhsslms.HssLmsPrivateKey('tmp/lms')
# public_key = private_key.hss_pub
#
# message1 = bytes.fromhex('60da1a17c88c59da8a730e6ca8effd37')
# sig1 = private_key.sign(message1)[4:]
# print('lms_verify_test:"{}":"{}":"{}":0'.format(message1.hex(), sig1.hex(), public_key.serialize()[4:].hex()))
lms_verify_test:"60da1a17c88c59da8a730e6ca8effd37":"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":"0000000600000004d96bb26744d99ef624e32161c36d3d6efcdd0484e2b17a6dd183125be4b1af1cda931a91a3acb1151877c174f7943fd9":0

LMS pyhsslms interop test #2
# This test case continues from "LMS pyhsslms interop test #1".
# The signature uses leaf key 1, so must be the second signature generated by
# the key if the signature is to be reproduced.
#
# To produce another signature with this message and key (note that the actual
# signature bytes will differ due to randomization), after generating the
# first signature:
#
# message2 = bytes.fromhex('92d036bde8c45b8bb5dea2a072560b1e29fc4bb7dc4549ce90bccee8a6e962a1')
# sig2 = private_key.sign(message2)[4:]
# print('lms_verify_test:"{}":"{}":"{}":0'.format(message2.hex(), sig2.hex(), public_key.serialize()[4:].hex()))
lms_verify_test:"92d036bde8c45b8bb5dea2a072560b1e29fc4bb7dc4549ce90bccee8a6e962a1":"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":"0000000600000004d96bb26744d99ef624e32161c36d3d6efcdd0484e2b17a6dd183125be4b1af1cda931a91a3acb1151877c174f7943fd9":0

LMS pyhsslms interop NULL-message test
# This test uses data from https://github.com/russhousley/pyhsslms due to the limited
# amount of available test vectors for LMS. The private key is stored in
# data_files/lms_pyhsslms_sha256_m32_h5_lmots_sha256_n32_w8_prv. Note that this signature
# uses leaf key 2, so must be the third signature generated by the key if the
# signature is to be reproduced. Message data is random. Note that hash-sigs
# stores public keys and signatures in HSS form, which appends a 4-byte
# "levels" word at the start of the key/sig. We strip these 4 bytes from the
# signature and the public key before including them in a the test data.
#
# To produce another signature with this message and key (note that the actual
# signature bytes will differ due to randomization):
# * pip3 install --user pyhsslms
# * cp data_files/lms_pyhsslms_sha256_m32_h5_lmots_sha256_n32_w8_prv tmp/lms.prv
# * touch message.bin (create empty message file)
# * hsslms sign tmp/lms.prv message.bin (incorrect signature using leaf node 0)
# * rm message.bin.sig
# * hsslms sign tmp/lms.prv message.bin (incorrect signature using leaf node 1)
# * rm message.bin.sig
# * hsslms sign tmp/lms.prv message.bin (correct signature using leaf node 2)
# * cat message.bin.sig | xxd
#
# To validate the signature:
# * <Save signature in binary format>
# * touch message.bin (create empty message file)
# * echo -n -e "\0\0\0\0" > message.bin.sig; cat sig.bin >> message.bin.sig (restore the
#   HSS levels)
# * cp data_files/lms_pyhsslms_sha256_m32_h5_lmots_sha256_n32_w8 tmp/lms.pub
# * hsslms verify tmp/lms message.bin
lms_verify_test:"":"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":"0000000600000004d96bb26744d99ef624e32161c36d3d6efcdd0484e2b17a6dd183125be4b1af1cda931a91a3acb1151877c174f7943fd9":0

LMS hash-sigs interop test #1
# This test uses data from https://github.com/cisco/hash-sigs due to the
# limited amount of available test vectors for LMS. The private key is stored in
# data_files/lms_hash-sigs_sha256_m32_h5_lmots_sha256_n32_w8_prv and
# data_files/lms_hash-sigs_sha256_m32_h5_lmots_sha256_n32_w8_aux. Note that this
# signature uses leaf key 0, so must be the first signature generated by the key
# if the signature is to be reproduced. Message data is random. Note that
# hash-sigs stores public keys and signatures in HSS form, which appends a
# 4-byte "levels" word at the start of the key/sig. We strip these 4 bytes from
# the signature and the public key before including them in a the test data.
#
# To produce another signature with this message and key (note that the actual
# signature bytes will differ due to randomization):
# * <download and build hash-sigs>
# * cp data_files/lms_hash-sigs_sha256_m32_h5_lmots_sha256_n32_w8_prv tmp/lms.prv
# * cp data_files/lms_hash-sigs_sha256_m32_h5_lmots_sha256_n32_w8_aux tmp/lms.aux
# * <Save message in binary format>
# * <hash-sigs>/demo sign tmp/lms message.bin
# * cat message.bin.sig | xxd
#
# To validate the signature:
# * Save message and signature in binary format
# * echo -n -e "\0\0\0\0" > message.bin.sig; cat sig.bin >> message.bin.sig (restore the
#   HSS levels)
# * cp data_files/lms_hash-sigs_sha256_m32_h5_lmots_sha256_n32_w8_pub tmp/lms.pub
# * <hash-sigs/demo> verify tmp/lms message.bin
lms_verify_test:"6b7439e31ef128c54f1536f745ff1246":"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":"0000000600000004e18760ef2c86192aee88579e376f35cd153419d622803a483e79f6d368629308a8ab6ff663c4f108b2033af290dcedfa":0

LMS hash-sigs interop test #2
# This test uses data from https://github.com/cisco/hash-sigs due to the
# limited amount of available test vectors for LMS. The private key is stored in
# data_files/lms_hash-sigs_sha256_m32_h5_lmots_sha256_n32_w8_prv and
# data_files/lms_hash-sigs_sha256_m32_h5_lmots_sha256_n32_w8_aux. Note that this
# signature uses leaf key 1, so must be the second signature generated by the key
# if the signature is to be reproduced. Message data is random. Note that
# hash-sigs stores public keys and signatures in HSS form, which appends a
# 4-byte "levels" word at the start of the key/sig. We strip these 4 bytes from
# the signature and the public key before including them in a the test data.
#
# To produce another signature with this message and key (note that the actual
# signature bytes will differ due to randomization):
# * <download and build hash-sigs>
# * cp data_files/lms_hash-sigs_sha256_m32_h5_lmots_sha256_n32_w8_prv tmp/lms.prv
# * cp data_files/lms_hash-sigs_sha256_m32_h5_lmots_sha256_n32_w8_aux tmp/lms.aux
# * <Save message in binary format>
# * <hash-sigs>/demo sign tmp/lms message.bin (incorrect signature using leaf node 0)
# * rm message.bin.sig
# * <hash-sigs>/demo sign tmp/lms message.bin (correct signature using leaf node 1)
# * cat message.bin.sig | xxd
#
# To validate the signature:
# * Save message and signature in binary format
# * echo -n -e "\0\0\0\0" > message.bin.sig; cat sig.bin >> message.bin.sig (restore the
#   HSS levels)
# * cp data_files/lms_hash-sigs_sha256_m32_h5_lmots_sha256_n32_w8_pub tmp/lms.pub
# * <hash-sigs/demo> verify tmp/lms message.bin
lms_verify_test:"0705ba8297c7b9fa5f08e37825ad24a0":"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":"0000000600000004e18760ef2c86192aee88579e376f35cd153419d622803a483e79f6d368629308a8ab6ff663c4f108b2033af290dcedfa":0

LMS hsslms interop test #1
# This test uses data from https://github.com/pmvr/python-hsslms due to the
# limited amount of available test vectors for LMS. The private key is stored in
# data_files/lms_hsslms_sha256_m32_h5_lmots_sha256_n32_w8_prv
#
# To produce another signature with this message and key (note that the actual
# signature bytes will differ due to randomization):
# pip3 install --user hsslms==0.1.2
#
# from hsslms import LMS_Priv, LMS_ALGORITHM_TYPE, LMOTS_ALGORITHM_TYPE
# import pickle
#
# with open('tests/data_files/lms_hsslms_sha256_m32_h5_lmots_sha256_n32_w8_prv', 'rb') as private_key_file:
#     private_key = pickle.load(private_key_file)
#
# public_key = private_key.gen_pub()
#
# message = bytes.fromhex('60da1a17c88c59da8a730e6ca8effd37')
# sig = private_key.sign(message)
#
# print('lms_verify_test:"{}":"{}":"{}":0'.format(message.hex(), sig.hex(), public_key.get_pubkey().hex()))
lms_verify_test:"60da1a17c88c59da8a730e6ca8effd37":"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":"000000060000000447cc5b29dd0cecd01c382434a6d16864d51b60cdb2a9eed2419015d8524c717ce38a865d7a37da6c84f94621ad595f5d":0

LMS hsslms interop test #2
# This test uses data from https://github.com/pmvr/python-hsslms due to the
# limited amount of available test vectors for LMS. The private key is stored in
# data_files/lms_hsslms_sha256_m32_h5_lmots_sha256_n32_w8_prv
#
# To produce another signature with this message and key (note that the actual
# signature bytes will differ due to randomization):
# pip3 install --user hsslms==0.1.2
#
# from hsslms import LMS_Priv, LMS_ALGORITHM_TYPE, LMOTS_ALGORITHM_TYPE
# import pickle
#
# with open('tests/data_files/lms_hsslms_sha256_m32_h5_lmots_sha256_n32_w8_prv', 'rb') as private_key_file:
#     private_key = pickle.load(private_key_file)
#
# public_key = private_key.gen_pub()
#
# message = bytes.fromhex('60da1a17c88c59da8a730e6ca8effd37')
# sig = private_key.sign(message)
#
# message = bytes.fromhex('92d036bde8c45b8bb5dea2a072560b1e29fc4bb7dc4549ce90bccee8a6e962a1')
# sig = private_key.sign(message)
#
# print('lms_verify_test:"{}":"{}":"{}":0'.format(message.hex(), sig.hex(), public_key.get_pubkey().hex()))
lms_verify_test:"92d036bde8c45b8bb5dea2a072560b1e29fc4bb7dc4549ce90bccee8a6e962a1":"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":"000000060000000447cc5b29dd0cecd01c382434a6d16864d51b60cdb2a9eed2419015d8524c717ce38a865d7a37da6c84f94621ad595f5d":0

LMS negative test (invalid lms type) #1
# This test uses the data from hash-sigs interop test #1. This test has a valid
# LMOTS type (0x4) but an invalid LMS type (0x5), and should fail.
lms_verify_test:"bfff9cd687351db88a98c71fd2f9b927a0ee600130a112533b791041d30cb91665fc369a5ac7cc9a04547414ac45288081d19d4a600579c73ac4bc953de03ad6":"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":"0000000600000004d96bb26744d99ef624e32161c36d3d6efcdd0484e2b17a6dd183125be4b1af1cda931a91a3acb1151877c174f7943fd9":MBEDTLS_ERR_LMS_VERIFY_FAILED

LMS negative test (invalid lms type) #2
# This test uses the data from hash-sigs interop test #1. This test has a valid
# LMOTS type (0x4) but an invalid LMS type (0x7), and should fail.
lms_verify_test:"bfff9cd687351db88a98c71fd2f9b927a0ee600130a112533b791041d30cb91665fc369a5ac7cc9a04547414ac45288081d19d4a600579c73ac4bc953de03ad6":"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":"0000000600000004d96bb26744d99ef624e32161c36d3d6efcdd0484e2b17a6dd183125be4b1af1cda931a91a3acb1151877c174f7943fd9":MBEDTLS_ERR_LMS_VERIFY_FAILED

LMS negative test (invalid lm_ots type) #1
# This test uses the data from hash-sigs interop test #1. This test has an
# invalid LMOTS type (0x3) but a valid LMS type (0x6), and should fail.
lms_verify_test:"bfff9cd687351db88a98c71fd2f9b927a0ee600130a112533b791041d30cb91665fc369a5ac7cc9a04547414ac45288081d19d4a600579c73ac4bc953de03ad6":"0000000000000003e474c96c496b231ecedcd92566ab3e1cdfac83f7e56abaae6571401e212e59bdbcc18105e0709249510d13d7ae1091558c217033316ac70a36aae764bd0f4032e369453c634b81061079d216d03d3c55976a1aabc00287b307297ae03587ba20839460daae85d26dfcef7638c10a1d8559612e5e9ced1a4205a52ca0ce88e58602e59cf9ab34adf2e958e56570573297b99f733fbbf58d2440526fd4dc15c5509e8a11405f6c08772abcf58731fbf9a73642670e3247c5f70905f0fa81f6174bf32977209923507525a170fcd260e81f04193583fbcd305ac245c80eb337ca326fd1105e73748fab8a1f0c8d8a99f011718e7aae027a34d2a85ff18769fa277810126a86b51b096a04d8e28a4fb8c5e14e50a67cb1ee88e43e5cc077902442f5d9c55ac2b8acdd76c67bc740c6083aba4e3cb404c23f1f3118337563fef6a4b01fb476810c5b5682d0aecdccd55c85a4af50e9150f7d83dcccd8e822a302e6e5a52e00505e6e65338dcfb9cfbe22594e9e18ebde36af29450c5ea31523019cf64fd6eca8c77d98c2a146dcedd51bf6c61c1f7cacbce3ab20c8606930cc42737e17f2703cf0980aef560768d1ac5585c05a60a5f94db15f5ac4d4df5cbd81430878d4e9b77346e3a6538b80b80873e3e6c37860470091979296631440adb8cc71991aba2a4dd2884764878306fe774a25512cfbf080f2829ea2903ffa748dd187c21aef918ee3436a1bd336c3d09cc1f748d7528db90a98f69078b82c4d23de7bcec092a292d2b8cac71a5c87d008f128b89a5e608a4501aef41e9f17e4056ed4767957188f780159daebf327751386980b0fca5a2d36b141acff957f46ce2381897099619475db9d3a613e7ef98b056f42b4d6eafb1d62eebbe46a7502f893fbd36ccfb12a280f45ffb93f050eb280bf0a6cd640abdea8590bffb98bdb29ee3a31daa0fa3ab35fee11dc1b7d1fcea82b0e284b2e35b34e77c3f401ed887e7fc6c97b327f76f99caca2f355afa2753a8923bfb06fb2a9df08d31c93882e34ef5a3cccc9d078855334bdf909ae418b177724c42fb1d586d212c4474932acce295236030f4379158957300fe9fdc5cc9145e3ded50cf9f5a8e19321961536c4a47fffc3eb4383fb78a5d2aeed5b45b92132b5e2a53e3b67841fa2e1bd217ee2c30812c4eb1bd4f8c85b328e23d27f12a2fad5c6b236c87f8fd1aad441416e53ebd4d45d4bf601b94eb37dc9a065218ae58e69dba1250bb20626baeda961b3ef11d217697e73f41fa3870d726a032bc4a388fb12c023822945df058e22f54e5f6377eab34297c57883515204fc189d0d4b0ad9bacb24acf7f9d55e7c6368bb8ababd7622f586ec22683306c5d88d5244219a3952adbd85c89893a441a58b532e15600cd5afdbb5b441e1670b72656c7995189bdf993154e09912db8c4ddaff0e75591720230cf99c8b71cd841dffc4372c5e0f9ff906a379d28d6884351609bf7c849ebfabfb049ae986bbb8467251dbf5ccdd05a86ff6ce1392f7ca1bd49595ad9ad805d71b389afab1865f7f69dc24662af19934f025ced212536509500c132aec000000068b991bed50319a6cb9ff040b92f1563889b3787c37145fc9737d4643f66ade33ebd85a2c29b8c64a581cff01b89d59807d6fade2d2c88872f77d0ed83d97c4b5438681d0b95feb973125e4ee70ebe11699290b831e86571e36513a71f159d48ce563f6814cc2a89851d2520c5275b34cc83614cab14c0d197166580d800ee6b9004b8fd72daac8d73c36c1623c37be93ba49a06c4efde238a3a10a05daba5d4942f7de52648af2be31f33e723b3605346282f5d5e356c5d0004eea40fe0b80abf658f6c96c56319ab53d7fefb5879e0136d1cf320973a2f47c1ee3d21554910f09d17afac4607657c4309890957a4954bf86e2a8491ba37dd88b2c3fe3c7edebd767c400bc23e40d165b27133c726b90bc26cbb2a86a6aa400c47aa7ffc538388de8490b9349afa53b814ffe2a56ff16a496e9648284193754f989f6f12aeb6e":"0000000600000004d96bb26744d99ef624e32161c36d3d6efcdd0484e2b17a6dd183125be4b1af1cda931a91a3acb1151877c174f7943fd9":MBEDTLS_ERR_LMS_VERIFY_FAILED

LMS negative test (invalid lm_ots type) #2
# This test uses the data from hash-sigs interop test #1. This test has an
# invalid LMOTS type (0x5) but a valid LMS type (0x6), and should fail.
lms_verify_test:"bfff9cd687351db88a98c71fd2f9b927a0ee600130a112533b791041d30cb91665fc369a5ac7cc9a04547414ac45288081d19d4a600579c73ac4bc953de03ad6":"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":"0000000600000004d96bb26744d99ef624e32161c36d3d6efcdd0484e2b17a6dd183125be4b1af1cda931a91a3acb1151877c174f7943fd9":MBEDTLS_ERR_LMS_VERIFY_FAILED

LMS negative test (invalid leaf ID)
# This test uses the data from hash-sigs interop test #1. In this case,
# the leaf ID is 1024, which is invalid for MBEDTLS_LMS_SHA256_M32_H10. This
# test should fail to verify the signature.
lms_verify_test:"bfff9cd687351db88a98c71fd2f9b927a0ee600130a112533b791041d30cb91665fc369a5ac7cc9a04547414ac45288081d19d4a600579c73ac4bc953de03ad6":"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":"0000000600000004d96bb26744d99ef624e32161c36d3d6efcdd0484e2b17a6dd183125be4b1af1cda931a91a3acb1151877c174f7943fd9":MBEDTLS_ERR_LMS_VERIFY_FAILED

LMS import/export test
# This test uses the key from hsslms interop test 1, imports it, exports it and
# tests that it is the same. It also checks if the export correctly fail when
# the buffer is too small.
lms_import_export_test:"000000060000000447cc5b29dd0cecd01c382434a6d16864d51b60cdb2a9eed2419015d8524c717ce38a865d7a37da6c84f94621ad595f5d":0

LMS key import too large key test
# This test uses the valid public key for hsslms interop test 1, add an extra
# byte, and then imports it. This should fail.
lms_import_export_test:"000000060000000447cc5b29dd0cecd01c382434a6d16864d51b60cdb2a9eed2419015d8524c717ce38a865d7a37da6c84f94621ad595f5d00":MBEDTLS_ERR_LMS_BAD_INPUT_DATA

LMS key import too small key test
# This test uses the valid public key for hsslms interop test 1, removes a byte,
# and then imports it. This should fail.
lms_import_export_test:"000000060000000447cc5b29dd0cecd01c382434a6d16864d51b60cdb2a9eed2419015d8524c717ce38a865d7a37da6c84f94621ad595f":MBEDTLS_ERR_LMS_BAD_INPUT_DATA

LMS key import no LMS type test
# This test uses the valid public key for hsslms interop test 1, cuts it down so
# it's smaller than the LMS type offset, and imports it. This should fail, and
# not attempt to read invalidly outside the buffer.
lms_import_export_test:"000000":MBEDTLS_ERR_LMS_BAD_INPUT_DATA

LMS key import no LMOTS type test
# This test uses the valid public key for hsslms interop test 1, cuts it down so
# it's smaller than the LMOTS type offset, and imports it. This should fail, and
# not attempt to read invalidly outside the buffer.
lms_import_export_test:"00000006000000":MBEDTLS_ERR_LMS_BAD_INPUT_DATA

LMS key import invalid LMS type test #1
# This test uses the valid public key for hsslms interop test 1, alters the
# LMS type to 0x5, and imports it. This should fail.
lms_import_export_test:"000000050000000447cc5b29dd0cecd01c382434a6d16864d51b60cdb2a9eed2419015d8524c717ce38a865d7a37da6c84f94621ad595f5d":MBEDTLS_ERR_LMS_BAD_INPUT_DATA

LMS key import invalid LMS type test #2
# This test uses the valid public key for hsslms interop test 1, alters the
# LMS type to 0x7, and imports it. This should fail, and not attempt to read
# invalidly outside the buffer.
lms_import_export_test:"000000070000000447cc5b29dd0cecd01c382434a6d16864d51b60cdb2a9eed2419015d8524c717ce38a865d7a37da6c84f94621ad595f5d":MBEDTLS_ERR_LMS_BAD_INPUT_DATA

LMS key import invalid LMOTS type test #1
# This test uses the valid public key for hsslms interop test 1, alters the
# LMOTS type to 0x3, and imports it. This should fail.
lms_import_export_test:"000000060000000347cc5b29dd0cecd01c382434a6d16864d51b60cdb2a9eed2419015d8524c717ce38a865d7a37da6c84f94621ad595f5d":MBEDTLS_ERR_LMS_BAD_INPUT_DATA

LMS key import invalid LMOTS type test #2
# This test uses the valid public key for hsslms interop test 1, alters the
# LMOTS type to 0x5, and imports it. This should fail, and not attempt to read
# invalidly outside the buffer.
lms_import_export_test:"000000060000000547cc5b29dd0cecd01c382434a6d16864d51b60cdb2a9eed2419015d8524c717ce38a865d7a37da6c84f94621ad595f5d":MBEDTLS_ERR_LMS_BAD_INPUT_DATA
