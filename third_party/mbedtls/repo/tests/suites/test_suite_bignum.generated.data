# Automatically generated by generate_bignum_tests.py. Do not edit!

MPI add #1 0 (null) + 0 (null)
mpi_add_mpi:"":"":"0"

MPI add #2 0 (null) + 0 (1 limb)
mpi_add_mpi:"":"0":"0"

MPI add #3 0 (null) + negative 0 (null)
mpi_add_mpi:"":"-":"0"

MPI add #4 0 (null) + negative with leading zero limb
mpi_add_mpi:"":"-0":"0"

MPI add #5 0 (null) + positive
mpi_add_mpi:"":"7b":"7b"

MPI add #6 0 (null) + negative
mpi_add_mpi:"":"-7b":"-7b"

MPI add #7 0 (null) + positive with leading zero limb
mpi_add_mpi:"":"0000000000000000123":"123"

MPI add #8 0 (null) + negative with leading zero limb
mpi_add_mpi:"":"-0000000000000000123":"-123"

MPI add #9 0 (null) + large positive
mpi_add_mpi:"":"1230000000000000000":"1230000000000000000"

MPI add #10 0 (null) + large negative
mpi_add_mpi:"":"-1230000000000000000":"-1230000000000000000"

MPI add #11 0 (1 limb) + 0 (null)
mpi_add_mpi:"0":"":"0"

MPI add #12 0 (1 limb) + 0 (1 limb)
mpi_add_mpi:"0":"0":"0"

MPI add #13 0 (1 limb) + negative 0 (null)
mpi_add_mpi:"0":"-":"0"

MPI add #14 0 (1 limb) + negative with leading zero limb
mpi_add_mpi:"0":"-0":"0"

MPI add #15 0 (1 limb) + positive
mpi_add_mpi:"0":"7b":"7b"

MPI add #16 0 (1 limb) + negative
mpi_add_mpi:"0":"-7b":"-7b"

MPI add #17 0 (1 limb) + positive with leading zero limb
mpi_add_mpi:"0":"0000000000000000123":"123"

MPI add #18 0 (1 limb) + negative with leading zero limb
mpi_add_mpi:"0":"-0000000000000000123":"-123"

MPI add #19 0 (1 limb) + large positive
mpi_add_mpi:"0":"1230000000000000000":"1230000000000000000"

MPI add #20 0 (1 limb) + large negative
mpi_add_mpi:"0":"-1230000000000000000":"-1230000000000000000"

MPI add #21 negative 0 (null) + 0 (null)
mpi_add_mpi:"-":"":"0"

MPI add #22 negative 0 (null) + 0 (1 limb)
mpi_add_mpi:"-":"0":"0"

MPI add #23 negative 0 (null) + negative 0 (null)
mpi_add_mpi:"-":"-":"0"

MPI add #24 negative 0 (null) + negative with leading zero limb
mpi_add_mpi:"-":"-0":"0"

MPI add #25 negative 0 (null) + positive
mpi_add_mpi:"-":"7b":"7b"

MPI add #26 negative 0 (null) + negative
mpi_add_mpi:"-":"-7b":"-7b"

MPI add #27 negative 0 (null) + positive with leading zero limb
mpi_add_mpi:"-":"0000000000000000123":"123"

MPI add #28 negative 0 (null) + negative with leading zero limb
mpi_add_mpi:"-":"-0000000000000000123":"-123"

MPI add #29 negative 0 (null) + large positive
mpi_add_mpi:"-":"1230000000000000000":"1230000000000000000"

MPI add #30 negative 0 (null) + large negative
mpi_add_mpi:"-":"-1230000000000000000":"-1230000000000000000"

MPI add #31 negative with leading zero limb + 0 (null)
mpi_add_mpi:"-0":"":"0"

MPI add #32 negative with leading zero limb + 0 (1 limb)
mpi_add_mpi:"-0":"0":"0"

MPI add #33 negative with leading zero limb + negative 0 (null)
mpi_add_mpi:"-0":"-":"0"

MPI add #34 negative with leading zero limb + negative with leading zero limb
mpi_add_mpi:"-0":"-0":"0"

MPI add #35 negative with leading zero limb + positive
mpi_add_mpi:"-0":"7b":"7b"

MPI add #36 negative with leading zero limb + negative
mpi_add_mpi:"-0":"-7b":"-7b"

MPI add #37 negative with leading zero limb + positive with leading zero limb
mpi_add_mpi:"-0":"0000000000000000123":"123"

MPI add #38 negative with leading zero limb + negative with leading zero limb
mpi_add_mpi:"-0":"-0000000000000000123":"-123"

MPI add #39 negative with leading zero limb + large positive
mpi_add_mpi:"-0":"1230000000000000000":"1230000000000000000"

MPI add #40 negative with leading zero limb + large negative
mpi_add_mpi:"-0":"-1230000000000000000":"-1230000000000000000"

MPI add #41 positive + 0 (null)
mpi_add_mpi:"7b":"":"7b"

MPI add #42 positive + 0 (1 limb)
mpi_add_mpi:"7b":"0":"7b"

MPI add #43 positive + negative 0 (null)
mpi_add_mpi:"7b":"-":"7b"

MPI add #44 positive + negative with leading zero limb
mpi_add_mpi:"7b":"-0":"7b"

MPI add #45 positive + positive
mpi_add_mpi:"7b":"7b":"f6"

MPI add #46 positive + negative , result=0
mpi_add_mpi:"7b":"-7b":"0"

MPI add #47 positive + positive with leading zero limb
mpi_add_mpi:"7b":"0000000000000000123":"19e"

MPI add #48 positive + negative with leading zero limb , result<0
mpi_add_mpi:"7b":"-0000000000000000123":"-a8"

MPI add #49 positive + large positive
mpi_add_mpi:"7b":"1230000000000000000":"123000000000000007b"

MPI add #50 positive + large negative , result<0
mpi_add_mpi:"7b":"-1230000000000000000":"-122ffffffffffffff85"

MPI add #51 negative + 0 (null)
mpi_add_mpi:"-7b":"":"-7b"

MPI add #52 negative + 0 (1 limb)
mpi_add_mpi:"-7b":"0":"-7b"

MPI add #53 negative + negative 0 (null)
mpi_add_mpi:"-7b":"-":"-7b"

MPI add #54 negative + negative with leading zero limb
mpi_add_mpi:"-7b":"-0":"-7b"

MPI add #55 negative + positive , result=0
mpi_add_mpi:"-7b":"7b":"0"

MPI add #56 negative + negative
mpi_add_mpi:"-7b":"-7b":"-f6"

MPI add #57 negative + positive with leading zero limb , result>0
mpi_add_mpi:"-7b":"0000000000000000123":"a8"

MPI add #58 negative + negative with leading zero limb
mpi_add_mpi:"-7b":"-0000000000000000123":"-19e"

MPI add #59 negative + large positive , result>0
mpi_add_mpi:"-7b":"1230000000000000000":"122ffffffffffffff85"

MPI add #60 negative + large negative
mpi_add_mpi:"-7b":"-1230000000000000000":"-123000000000000007b"

MPI add #61 positive with leading zero limb + 0 (null)
mpi_add_mpi:"0000000000000000123":"":"123"

MPI add #62 positive with leading zero limb + 0 (1 limb)
mpi_add_mpi:"0000000000000000123":"0":"123"

MPI add #63 positive with leading zero limb + negative 0 (null)
mpi_add_mpi:"0000000000000000123":"-":"123"

MPI add #64 positive with leading zero limb + negative with leading zero limb
mpi_add_mpi:"0000000000000000123":"-0":"123"

MPI add #65 positive with leading zero limb + positive
mpi_add_mpi:"0000000000000000123":"7b":"19e"

MPI add #66 positive with leading zero limb + negative , result>0
mpi_add_mpi:"0000000000000000123":"-7b":"a8"

MPI add #67 positive with leading zero limb + positive with leading zero limb
mpi_add_mpi:"0000000000000000123":"0000000000000000123":"246"

MPI add #68 positive with leading zero limb + negative with leading zero limb , result=0
mpi_add_mpi:"0000000000000000123":"-0000000000000000123":"0"

MPI add #69 positive with leading zero limb + large positive
mpi_add_mpi:"0000000000000000123":"1230000000000000000":"1230000000000000123"

MPI add #70 positive with leading zero limb + large negative , result<0
mpi_add_mpi:"0000000000000000123":"-1230000000000000000":"-122fffffffffffffedd"

MPI add #71 negative with leading zero limb + 0 (null)
mpi_add_mpi:"-0000000000000000123":"":"-123"

MPI add #72 negative with leading zero limb + 0 (1 limb)
mpi_add_mpi:"-0000000000000000123":"0":"-123"

MPI add #73 negative with leading zero limb + negative 0 (null)
mpi_add_mpi:"-0000000000000000123":"-":"-123"

MPI add #74 negative with leading zero limb + negative with leading zero limb
mpi_add_mpi:"-0000000000000000123":"-0":"-123"

MPI add #75 negative with leading zero limb + positive , result<0
mpi_add_mpi:"-0000000000000000123":"7b":"-a8"

MPI add #76 negative with leading zero limb + negative
mpi_add_mpi:"-0000000000000000123":"-7b":"-19e"

MPI add #77 negative with leading zero limb + positive with leading zero limb , result=0
mpi_add_mpi:"-0000000000000000123":"0000000000000000123":"0"

MPI add #78 negative with leading zero limb + negative with leading zero limb
mpi_add_mpi:"-0000000000000000123":"-0000000000000000123":"-246"

MPI add #79 negative with leading zero limb + large positive , result>0
mpi_add_mpi:"-0000000000000000123":"1230000000000000000":"122fffffffffffffedd"

MPI add #80 negative with leading zero limb + large negative
mpi_add_mpi:"-0000000000000000123":"-1230000000000000000":"-1230000000000000123"

MPI add #81 large positive + 0 (null)
mpi_add_mpi:"1230000000000000000":"":"1230000000000000000"

MPI add #82 large positive + 0 (1 limb)
mpi_add_mpi:"1230000000000000000":"0":"1230000000000000000"

MPI add #83 large positive + negative 0 (null)
mpi_add_mpi:"1230000000000000000":"-":"1230000000000000000"

MPI add #84 large positive + negative with leading zero limb
mpi_add_mpi:"1230000000000000000":"-0":"1230000000000000000"

MPI add #85 large positive + positive
mpi_add_mpi:"1230000000000000000":"7b":"123000000000000007b"

MPI add #86 large positive + negative , result>0
mpi_add_mpi:"1230000000000000000":"-7b":"122ffffffffffffff85"

MPI add #87 large positive + positive with leading zero limb
mpi_add_mpi:"1230000000000000000":"0000000000000000123":"1230000000000000123"

MPI add #88 large positive + negative with leading zero limb , result>0
mpi_add_mpi:"1230000000000000000":"-0000000000000000123":"122fffffffffffffedd"

MPI add #89 large positive + large positive
mpi_add_mpi:"1230000000000000000":"1230000000000000000":"2460000000000000000"

MPI add #90 large positive + large negative , result=0
mpi_add_mpi:"1230000000000000000":"-1230000000000000000":"0"

MPI add #91 large negative + 0 (null)
mpi_add_mpi:"-1230000000000000000":"":"-1230000000000000000"

MPI add #92 large negative + 0 (1 limb)
mpi_add_mpi:"-1230000000000000000":"0":"-1230000000000000000"

MPI add #93 large negative + negative 0 (null)
mpi_add_mpi:"-1230000000000000000":"-":"-1230000000000000000"

MPI add #94 large negative + negative with leading zero limb
mpi_add_mpi:"-1230000000000000000":"-0":"-1230000000000000000"

MPI add #95 large negative + positive , result<0
mpi_add_mpi:"-1230000000000000000":"7b":"-122ffffffffffffff85"

MPI add #96 large negative + negative
mpi_add_mpi:"-1230000000000000000":"-7b":"-123000000000000007b"

MPI add #97 large negative + positive with leading zero limb , result<0
mpi_add_mpi:"-1230000000000000000":"0000000000000000123":"-122fffffffffffffedd"

MPI add #98 large negative + negative with leading zero limb
mpi_add_mpi:"-1230000000000000000":"-0000000000000000123":"-1230000000000000123"

MPI add #99 large negative + large positive , result=0
mpi_add_mpi:"-1230000000000000000":"1230000000000000000":"0"

MPI add #100 large negative + large negative
mpi_add_mpi:"-1230000000000000000":"-1230000000000000000":"-2460000000000000000"

MPI add #101 large positive + large positive
mpi_add_mpi:"1c67967269c6":"1c67967269c6":"38cf2ce4d38c"

MPI add #102 large positive + positive
mpi_add_mpi:"1c67967269c6":"9cde3":"1c67967c37a9"

MPI add #103 large positive + large negative , result=0
mpi_add_mpi:"1c67967269c6":"-1c67967269c6":"0"

MPI add #104 large positive + negative , result>0
mpi_add_mpi:"1c67967269c6":"-9cde3":"1c6796689be3"

MPI add #105 positive + large positive
mpi_add_mpi:"9cde3":"1c67967269c6":"1c67967c37a9"

MPI add #106 positive + positive
mpi_add_mpi:"9cde3":"9cde3":"139bc6"

MPI add #107 positive + large negative , result<0
mpi_add_mpi:"9cde3":"-1c67967269c6":"-1c6796689be3"

MPI add #108 positive + negative , result=0
mpi_add_mpi:"9cde3":"-9cde3":"0"

MPI add #109 large negative + large positive , result=0
mpi_add_mpi:"-1c67967269c6":"1c67967269c6":"0"

MPI add #110 large negative + positive , result<0
mpi_add_mpi:"-1c67967269c6":"9cde3":"-1c6796689be3"

MPI add #111 large negative + large negative
mpi_add_mpi:"-1c67967269c6":"-1c67967269c6":"-38cf2ce4d38c"

MPI add #112 large negative + negative
mpi_add_mpi:"-1c67967269c6":"-9cde3":"-1c67967c37a9"

MPI add #113 negative + large positive , result>0
mpi_add_mpi:"-9cde3":"1c67967269c6":"1c6796689be3"

MPI add #114 negative + positive , result=0
mpi_add_mpi:"-9cde3":"9cde3":"0"

MPI add #115 negative + large negative
mpi_add_mpi:"-9cde3":"-1c67967269c6":"-1c67967c37a9"

MPI add #116 negative + negative
mpi_add_mpi:"-9cde3":"-9cde3":"-139bc6"

MPI compare #1 0 (null) == 0 (null)
mpi_cmp_mpi:"":"":0

MPI compare #2 0 (null) == 0 (1 limb)
mpi_cmp_mpi:"":"0":0

MPI compare #3 0 (null) == negative 0 (null)
mpi_cmp_mpi:"":"-":0

MPI compare #4 0 (null) == negative with leading zero limb
mpi_cmp_mpi:"":"-0":0

MPI compare #5 0 (null) < positive
mpi_cmp_mpi:"":"7b":-1

MPI compare #6 0 (null) > negative
mpi_cmp_mpi:"":"-7b":1

MPI compare #7 0 (null) < positive with leading zero limb
mpi_cmp_mpi:"":"0000000000000000123":-1

MPI compare #8 0 (null) > negative with leading zero limb
mpi_cmp_mpi:"":"-0000000000000000123":1

MPI compare #9 0 (null) < large positive
mpi_cmp_mpi:"":"1230000000000000000":-1

MPI compare #10 0 (null) > large negative
mpi_cmp_mpi:"":"-1230000000000000000":1

MPI compare #11 0 (1 limb) == 0 (null)
mpi_cmp_mpi:"0":"":0

MPI compare #12 0 (1 limb) == 0 (1 limb)
mpi_cmp_mpi:"0":"0":0

MPI compare #13 0 (1 limb) == negative 0 (null)
mpi_cmp_mpi:"0":"-":0

MPI compare #14 0 (1 limb) == negative with leading zero limb
mpi_cmp_mpi:"0":"-0":0

MPI compare #15 0 (1 limb) < positive
mpi_cmp_mpi:"0":"7b":-1

MPI compare #16 0 (1 limb) > negative
mpi_cmp_mpi:"0":"-7b":1

MPI compare #17 0 (1 limb) < positive with leading zero limb
mpi_cmp_mpi:"0":"0000000000000000123":-1

MPI compare #18 0 (1 limb) > negative with leading zero limb
mpi_cmp_mpi:"0":"-0000000000000000123":1

MPI compare #19 0 (1 limb) < large positive
mpi_cmp_mpi:"0":"1230000000000000000":-1

MPI compare #20 0 (1 limb) > large negative
mpi_cmp_mpi:"0":"-1230000000000000000":1

MPI compare #21 negative 0 (null) == 0 (null)
mpi_cmp_mpi:"-":"":0

MPI compare #22 negative 0 (null) == 0 (1 limb)
mpi_cmp_mpi:"-":"0":0

MPI compare #23 negative 0 (null) == negative 0 (null)
mpi_cmp_mpi:"-":"-":0

MPI compare #24 negative 0 (null) == negative with leading zero limb
mpi_cmp_mpi:"-":"-0":0

MPI compare #25 negative 0 (null) < positive
mpi_cmp_mpi:"-":"7b":-1

MPI compare #26 negative 0 (null) > negative
mpi_cmp_mpi:"-":"-7b":1

MPI compare #27 negative 0 (null) < positive with leading zero limb
mpi_cmp_mpi:"-":"0000000000000000123":-1

MPI compare #28 negative 0 (null) > negative with leading zero limb
mpi_cmp_mpi:"-":"-0000000000000000123":1

MPI compare #29 negative 0 (null) < large positive
mpi_cmp_mpi:"-":"1230000000000000000":-1

MPI compare #30 negative 0 (null) > large negative
mpi_cmp_mpi:"-":"-1230000000000000000":1

MPI compare #31 negative with leading zero limb == 0 (null)
mpi_cmp_mpi:"-0":"":0

MPI compare #32 negative with leading zero limb == 0 (1 limb)
mpi_cmp_mpi:"-0":"0":0

MPI compare #33 negative with leading zero limb == negative 0 (null)
mpi_cmp_mpi:"-0":"-":0

MPI compare #34 negative with leading zero limb == negative with leading zero limb
mpi_cmp_mpi:"-0":"-0":0

MPI compare #35 negative with leading zero limb < positive
mpi_cmp_mpi:"-0":"7b":-1

MPI compare #36 negative with leading zero limb > negative
mpi_cmp_mpi:"-0":"-7b":1

MPI compare #37 negative with leading zero limb < positive with leading zero limb
mpi_cmp_mpi:"-0":"0000000000000000123":-1

MPI compare #38 negative with leading zero limb > negative with leading zero limb
mpi_cmp_mpi:"-0":"-0000000000000000123":1

MPI compare #39 negative with leading zero limb < large positive
mpi_cmp_mpi:"-0":"1230000000000000000":-1

MPI compare #40 negative with leading zero limb > large negative
mpi_cmp_mpi:"-0":"-1230000000000000000":1

MPI compare #41 positive > 0 (null)
mpi_cmp_mpi:"7b":"":1

MPI compare #42 positive > 0 (1 limb)
mpi_cmp_mpi:"7b":"0":1

MPI compare #43 positive > negative 0 (null)
mpi_cmp_mpi:"7b":"-":1

MPI compare #44 positive > negative with leading zero limb
mpi_cmp_mpi:"7b":"-0":1

MPI compare #45 positive == positive
mpi_cmp_mpi:"7b":"7b":0

MPI compare #46 positive > negative
mpi_cmp_mpi:"7b":"-7b":1

MPI compare #47 positive < positive with leading zero limb
mpi_cmp_mpi:"7b":"0000000000000000123":-1

MPI compare #48 positive > negative with leading zero limb
mpi_cmp_mpi:"7b":"-0000000000000000123":1

MPI compare #49 positive < large positive
mpi_cmp_mpi:"7b":"1230000000000000000":-1

MPI compare #50 positive > large negative
mpi_cmp_mpi:"7b":"-1230000000000000000":1

MPI compare #51 negative < 0 (null)
mpi_cmp_mpi:"-7b":"":-1

MPI compare #52 negative < 0 (1 limb)
mpi_cmp_mpi:"-7b":"0":-1

MPI compare #53 negative < negative 0 (null)
mpi_cmp_mpi:"-7b":"-":-1

MPI compare #54 negative < negative with leading zero limb
mpi_cmp_mpi:"-7b":"-0":-1

MPI compare #55 negative < positive
mpi_cmp_mpi:"-7b":"7b":-1

MPI compare #56 negative == negative
mpi_cmp_mpi:"-7b":"-7b":0

MPI compare #57 negative < positive with leading zero limb
mpi_cmp_mpi:"-7b":"0000000000000000123":-1

MPI compare #58 negative > negative with leading zero limb
mpi_cmp_mpi:"-7b":"-0000000000000000123":1

MPI compare #59 negative < large positive
mpi_cmp_mpi:"-7b":"1230000000000000000":-1

MPI compare #60 negative > large negative
mpi_cmp_mpi:"-7b":"-1230000000000000000":1

MPI compare #61 positive with leading zero limb > 0 (null)
mpi_cmp_mpi:"0000000000000000123":"":1

MPI compare #62 positive with leading zero limb > 0 (1 limb)
mpi_cmp_mpi:"0000000000000000123":"0":1

MPI compare #63 positive with leading zero limb > negative 0 (null)
mpi_cmp_mpi:"0000000000000000123":"-":1

MPI compare #64 positive with leading zero limb > negative with leading zero limb
mpi_cmp_mpi:"0000000000000000123":"-0":1

MPI compare #65 positive with leading zero limb > positive
mpi_cmp_mpi:"0000000000000000123":"7b":1

MPI compare #66 positive with leading zero limb > negative
mpi_cmp_mpi:"0000000000000000123":"-7b":1

MPI compare #67 positive with leading zero limb == positive with leading zero limb
mpi_cmp_mpi:"0000000000000000123":"0000000000000000123":0

MPI compare #68 positive with leading zero limb > negative with leading zero limb
mpi_cmp_mpi:"0000000000000000123":"-0000000000000000123":1

MPI compare #69 positive with leading zero limb < large positive
mpi_cmp_mpi:"0000000000000000123":"1230000000000000000":-1

MPI compare #70 positive with leading zero limb > large negative
mpi_cmp_mpi:"0000000000000000123":"-1230000000000000000":1

MPI compare #71 negative with leading zero limb < 0 (null)
mpi_cmp_mpi:"-0000000000000000123":"":-1

MPI compare #72 negative with leading zero limb < 0 (1 limb)
mpi_cmp_mpi:"-0000000000000000123":"0":-1

MPI compare #73 negative with leading zero limb < negative 0 (null)
mpi_cmp_mpi:"-0000000000000000123":"-":-1

MPI compare #74 negative with leading zero limb < negative with leading zero limb
mpi_cmp_mpi:"-0000000000000000123":"-0":-1

MPI compare #75 negative with leading zero limb < positive
mpi_cmp_mpi:"-0000000000000000123":"7b":-1

MPI compare #76 negative with leading zero limb < negative
mpi_cmp_mpi:"-0000000000000000123":"-7b":-1

MPI compare #77 negative with leading zero limb < positive with leading zero limb
mpi_cmp_mpi:"-0000000000000000123":"0000000000000000123":-1

MPI compare #78 negative with leading zero limb == negative with leading zero limb
mpi_cmp_mpi:"-0000000000000000123":"-0000000000000000123":0

MPI compare #79 negative with leading zero limb < large positive
mpi_cmp_mpi:"-0000000000000000123":"1230000000000000000":-1

MPI compare #80 negative with leading zero limb > large negative
mpi_cmp_mpi:"-0000000000000000123":"-1230000000000000000":1

MPI compare #81 large positive > 0 (null)
mpi_cmp_mpi:"1230000000000000000":"":1

MPI compare #82 large positive > 0 (1 limb)
mpi_cmp_mpi:"1230000000000000000":"0":1

MPI compare #83 large positive > negative 0 (null)
mpi_cmp_mpi:"1230000000000000000":"-":1

MPI compare #84 large positive > negative with leading zero limb
mpi_cmp_mpi:"1230000000000000000":"-0":1

MPI compare #85 large positive > positive
mpi_cmp_mpi:"1230000000000000000":"7b":1

MPI compare #86 large positive > negative
mpi_cmp_mpi:"1230000000000000000":"-7b":1

MPI compare #87 large positive > positive with leading zero limb
mpi_cmp_mpi:"1230000000000000000":"0000000000000000123":1

MPI compare #88 large positive > negative with leading zero limb
mpi_cmp_mpi:"1230000000000000000":"-0000000000000000123":1

MPI compare #89 large positive == large positive
mpi_cmp_mpi:"1230000000000000000":"1230000000000000000":0

MPI compare #90 large positive > large negative
mpi_cmp_mpi:"1230000000000000000":"-1230000000000000000":1

MPI compare #91 large negative < 0 (null)
mpi_cmp_mpi:"-1230000000000000000":"":-1

MPI compare #92 large negative < 0 (1 limb)
mpi_cmp_mpi:"-1230000000000000000":"0":-1

MPI compare #93 large negative < negative 0 (null)
mpi_cmp_mpi:"-1230000000000000000":"-":-1

MPI compare #94 large negative < negative with leading zero limb
mpi_cmp_mpi:"-1230000000000000000":"-0":-1

MPI compare #95 large negative < positive
mpi_cmp_mpi:"-1230000000000000000":"7b":-1

MPI compare #96 large negative < negative
mpi_cmp_mpi:"-1230000000000000000":"-7b":-1

MPI compare #97 large negative < positive with leading zero limb
mpi_cmp_mpi:"-1230000000000000000":"0000000000000000123":-1

MPI compare #98 large negative < negative with leading zero limb
mpi_cmp_mpi:"-1230000000000000000":"-0000000000000000123":-1

MPI compare #99 large negative < large positive
mpi_cmp_mpi:"-1230000000000000000":"1230000000000000000":-1

MPI compare #100 large negative == large negative
mpi_cmp_mpi:"-1230000000000000000":"-1230000000000000000":0

MPI compare #101 negative > negative
mpi_cmp_mpi:"-2":"-3":1

MPI compare #102 negative == negative
mpi_cmp_mpi:"-2":"-2":0

MPI compare #103 positive < positive
mpi_cmp_mpi:"2b4":"2b5":-1

MPI compare #104 positive < positive
mpi_cmp_mpi:"2b5":"2b6":-1

MPI compare (abs) #1 0 (null) == 0 (null)
mpi_cmp_abs:"":"":0

MPI compare (abs) #2 0 (null) == 0 (1 limb)
mpi_cmp_abs:"":"0":0

MPI compare (abs) #3 0 (null) == 0 (null)
mpi_cmp_abs:"":"":0

MPI compare (abs) #4 0 (null) == 0 (1 limb)
mpi_cmp_abs:"":"0":0

MPI compare (abs) #5 0 (null) < positive
mpi_cmp_abs:"":"7b":-1

MPI compare (abs) #6 0 (null) < positive
mpi_cmp_abs:"":"7b":-1

MPI compare (abs) #7 0 (null) < positive with leading zero limb
mpi_cmp_abs:"":"0000000000000000123":-1

MPI compare (abs) #8 0 (null) < positive with leading zero limb
mpi_cmp_abs:"":"0000000000000000123":-1

MPI compare (abs) #9 0 (null) < large positive
mpi_cmp_abs:"":"1230000000000000000":-1

MPI compare (abs) #10 0 (null) < large positive
mpi_cmp_abs:"":"1230000000000000000":-1

MPI compare (abs) #11 0 (1 limb) == 0 (null)
mpi_cmp_abs:"0":"":0

MPI compare (abs) #12 0 (1 limb) == 0 (1 limb)
mpi_cmp_abs:"0":"0":0

MPI compare (abs) #13 0 (1 limb) == 0 (null)
mpi_cmp_abs:"0":"":0

MPI compare (abs) #14 0 (1 limb) == 0 (1 limb)
mpi_cmp_abs:"0":"0":0

MPI compare (abs) #15 0 (1 limb) < positive
mpi_cmp_abs:"0":"7b":-1

MPI compare (abs) #16 0 (1 limb) < positive
mpi_cmp_abs:"0":"7b":-1

MPI compare (abs) #17 0 (1 limb) < positive with leading zero limb
mpi_cmp_abs:"0":"0000000000000000123":-1

MPI compare (abs) #18 0 (1 limb) < positive with leading zero limb
mpi_cmp_abs:"0":"0000000000000000123":-1

MPI compare (abs) #19 0 (1 limb) < large positive
mpi_cmp_abs:"0":"1230000000000000000":-1

MPI compare (abs) #20 0 (1 limb) < large positive
mpi_cmp_abs:"0":"1230000000000000000":-1

MPI compare (abs) #21 0 (null) == 0 (null)
mpi_cmp_abs:"":"":0

MPI compare (abs) #22 0 (null) == 0 (1 limb)
mpi_cmp_abs:"":"0":0

MPI compare (abs) #23 0 (null) == 0 (null)
mpi_cmp_abs:"":"":0

MPI compare (abs) #24 0 (null) == 0 (1 limb)
mpi_cmp_abs:"":"0":0

MPI compare (abs) #25 0 (null) < positive
mpi_cmp_abs:"":"7b":-1

MPI compare (abs) #26 0 (null) < positive
mpi_cmp_abs:"":"7b":-1

MPI compare (abs) #27 0 (null) < positive with leading zero limb
mpi_cmp_abs:"":"0000000000000000123":-1

MPI compare (abs) #28 0 (null) < positive with leading zero limb
mpi_cmp_abs:"":"0000000000000000123":-1

MPI compare (abs) #29 0 (null) < large positive
mpi_cmp_abs:"":"1230000000000000000":-1

MPI compare (abs) #30 0 (null) < large positive
mpi_cmp_abs:"":"1230000000000000000":-1

MPI compare (abs) #31 0 (1 limb) == 0 (null)
mpi_cmp_abs:"0":"":0

MPI compare (abs) #32 0 (1 limb) == 0 (1 limb)
mpi_cmp_abs:"0":"0":0

MPI compare (abs) #33 0 (1 limb) == 0 (null)
mpi_cmp_abs:"0":"":0

MPI compare (abs) #34 0 (1 limb) == 0 (1 limb)
mpi_cmp_abs:"0":"0":0

MPI compare (abs) #35 0 (1 limb) < positive
mpi_cmp_abs:"0":"7b":-1

MPI compare (abs) #36 0 (1 limb) < positive
mpi_cmp_abs:"0":"7b":-1

MPI compare (abs) #37 0 (1 limb) < positive with leading zero limb
mpi_cmp_abs:"0":"0000000000000000123":-1

MPI compare (abs) #38 0 (1 limb) < positive with leading zero limb
mpi_cmp_abs:"0":"0000000000000000123":-1

MPI compare (abs) #39 0 (1 limb) < large positive
mpi_cmp_abs:"0":"1230000000000000000":-1

MPI compare (abs) #40 0 (1 limb) < large positive
mpi_cmp_abs:"0":"1230000000000000000":-1

MPI compare (abs) #41 positive > 0 (null)
mpi_cmp_abs:"7b":"":1

MPI compare (abs) #42 positive > 0 (1 limb)
mpi_cmp_abs:"7b":"0":1

MPI compare (abs) #43 positive > 0 (null)
mpi_cmp_abs:"7b":"":1

MPI compare (abs) #44 positive > 0 (1 limb)
mpi_cmp_abs:"7b":"0":1

MPI compare (abs) #45 positive == positive
mpi_cmp_abs:"7b":"7b":0

MPI compare (abs) #46 positive == positive
mpi_cmp_abs:"7b":"7b":0

MPI compare (abs) #47 positive < positive with leading zero limb
mpi_cmp_abs:"7b":"0000000000000000123":-1

MPI compare (abs) #48 positive < positive with leading zero limb
mpi_cmp_abs:"7b":"0000000000000000123":-1

MPI compare (abs) #49 positive < large positive
mpi_cmp_abs:"7b":"1230000000000000000":-1

MPI compare (abs) #50 positive < large positive
mpi_cmp_abs:"7b":"1230000000000000000":-1

MPI compare (abs) #51 positive > 0 (null)
mpi_cmp_abs:"7b":"":1

MPI compare (abs) #52 positive > 0 (1 limb)
mpi_cmp_abs:"7b":"0":1

MPI compare (abs) #53 positive > 0 (null)
mpi_cmp_abs:"7b":"":1

MPI compare (abs) #54 positive > 0 (1 limb)
mpi_cmp_abs:"7b":"0":1

MPI compare (abs) #55 positive == positive
mpi_cmp_abs:"7b":"7b":0

MPI compare (abs) #56 positive == positive
mpi_cmp_abs:"7b":"7b":0

MPI compare (abs) #57 positive < positive with leading zero limb
mpi_cmp_abs:"7b":"0000000000000000123":-1

MPI compare (abs) #58 positive < positive with leading zero limb
mpi_cmp_abs:"7b":"0000000000000000123":-1

MPI compare (abs) #59 positive < large positive
mpi_cmp_abs:"7b":"1230000000000000000":-1

MPI compare (abs) #60 positive < large positive
mpi_cmp_abs:"7b":"1230000000000000000":-1

MPI compare (abs) #61 positive with leading zero limb > 0 (null)
mpi_cmp_abs:"0000000000000000123":"":1

MPI compare (abs) #62 positive with leading zero limb > 0 (1 limb)
mpi_cmp_abs:"0000000000000000123":"0":1

MPI compare (abs) #63 positive with leading zero limb > 0 (null)
mpi_cmp_abs:"0000000000000000123":"":1

MPI compare (abs) #64 positive with leading zero limb > 0 (1 limb)
mpi_cmp_abs:"0000000000000000123":"0":1

MPI compare (abs) #65 positive with leading zero limb > positive
mpi_cmp_abs:"0000000000000000123":"7b":1

MPI compare (abs) #66 positive with leading zero limb > positive
mpi_cmp_abs:"0000000000000000123":"7b":1

MPI compare (abs) #67 positive with leading zero limb == positive with leading zero limb
mpi_cmp_abs:"0000000000000000123":"0000000000000000123":0

MPI compare (abs) #68 positive with leading zero limb == positive with leading zero limb
mpi_cmp_abs:"0000000000000000123":"0000000000000000123":0

MPI compare (abs) #69 positive with leading zero limb < large positive
mpi_cmp_abs:"0000000000000000123":"1230000000000000000":-1

MPI compare (abs) #70 positive with leading zero limb < large positive
mpi_cmp_abs:"0000000000000000123":"1230000000000000000":-1

MPI compare (abs) #71 positive with leading zero limb > 0 (null)
mpi_cmp_abs:"0000000000000000123":"":1

MPI compare (abs) #72 positive with leading zero limb > 0 (1 limb)
mpi_cmp_abs:"0000000000000000123":"0":1

MPI compare (abs) #73 positive with leading zero limb > 0 (null)
mpi_cmp_abs:"0000000000000000123":"":1

MPI compare (abs) #74 positive with leading zero limb > 0 (1 limb)
mpi_cmp_abs:"0000000000000000123":"0":1

MPI compare (abs) #75 positive with leading zero limb > positive
mpi_cmp_abs:"0000000000000000123":"7b":1

MPI compare (abs) #76 positive with leading zero limb > positive
mpi_cmp_abs:"0000000000000000123":"7b":1

MPI compare (abs) #77 positive with leading zero limb == positive with leading zero limb
mpi_cmp_abs:"0000000000000000123":"0000000000000000123":0

MPI compare (abs) #78 positive with leading zero limb == positive with leading zero limb
mpi_cmp_abs:"0000000000000000123":"0000000000000000123":0

MPI compare (abs) #79 positive with leading zero limb < large positive
mpi_cmp_abs:"0000000000000000123":"1230000000000000000":-1

MPI compare (abs) #80 positive with leading zero limb < large positive
mpi_cmp_abs:"0000000000000000123":"1230000000000000000":-1

MPI compare (abs) #81 large positive > 0 (null)
mpi_cmp_abs:"1230000000000000000":"":1

MPI compare (abs) #82 large positive > 0 (1 limb)
mpi_cmp_abs:"1230000000000000000":"0":1

MPI compare (abs) #83 large positive > 0 (null)
mpi_cmp_abs:"1230000000000000000":"":1

MPI compare (abs) #84 large positive > 0 (1 limb)
mpi_cmp_abs:"1230000000000000000":"0":1

MPI compare (abs) #85 large positive > positive
mpi_cmp_abs:"1230000000000000000":"7b":1

MPI compare (abs) #86 large positive > positive
mpi_cmp_abs:"1230000000000000000":"7b":1

MPI compare (abs) #87 large positive > positive with leading zero limb
mpi_cmp_abs:"1230000000000000000":"0000000000000000123":1

MPI compare (abs) #88 large positive > positive with leading zero limb
mpi_cmp_abs:"1230000000000000000":"0000000000000000123":1

MPI compare (abs) #89 large positive == large positive
mpi_cmp_abs:"1230000000000000000":"1230000000000000000":0

MPI compare (abs) #90 large positive == large positive
mpi_cmp_abs:"1230000000000000000":"1230000000000000000":0

MPI compare (abs) #91 large positive > 0 (null)
mpi_cmp_abs:"1230000000000000000":"":1

MPI compare (abs) #92 large positive > 0 (1 limb)
mpi_cmp_abs:"1230000000000000000":"0":1

MPI compare (abs) #93 large positive > 0 (null)
mpi_cmp_abs:"1230000000000000000":"":1

MPI compare (abs) #94 large positive > 0 (1 limb)
mpi_cmp_abs:"1230000000000000000":"0":1

MPI compare (abs) #95 large positive > positive
mpi_cmp_abs:"1230000000000000000":"7b":1

MPI compare (abs) #96 large positive > positive
mpi_cmp_abs:"1230000000000000000":"7b":1

MPI compare (abs) #97 large positive > positive with leading zero limb
mpi_cmp_abs:"1230000000000000000":"0000000000000000123":1

MPI compare (abs) #98 large positive > positive with leading zero limb
mpi_cmp_abs:"1230000000000000000":"0000000000000000123":1

MPI compare (abs) #99 large positive == large positive
mpi_cmp_abs:"1230000000000000000":"1230000000000000000":0

MPI compare (abs) #100 large positive == large positive
mpi_cmp_abs:"1230000000000000000":"1230000000000000000":0

MPI compare (abs) #101 positive < positive
mpi_cmp_abs:"2":"3":-1

MPI compare (abs) #102 positive == positive
mpi_cmp_abs:"2":"2":0

MPI compare (abs) #103 positive < positive
mpi_cmp_abs:"2b4":"2b5":-1

MPI compare (abs) #104 positive < positive
mpi_cmp_abs:"2b5":"2b6":-1

# End of automatically generated file.
