/* BEGIN_HEADER */
#include "mbedtls/md5.h"
#include "mbedtls/ripemd160.h"
/* END_HEADER */

/* BEGIN_CASE depends_on:MBEDTLS_MD5_C */
void md5_text(char *text_src_string, data_t *hash)
{
    int ret;
    unsigned char src_str[100];
    unsigned char output[16];

    memset(src_str, 0x00, sizeof(src_str));
    memset(output, 0x00, sizeof(output));

    strncpy((char *) src_str, text_src_string, sizeof(src_str) - 1);

    ret = mbedtls_md5(src_str, strlen((char *) src_str), output);
    TEST_ASSERT(ret == 0);

    TEST_ASSERT(mbedtls_test_hexcmp(output, hash->x,
                                    sizeof(output), hash->len) == 0);
}
/* END_CASE */

/* BEGIN_CASE depends_on:MBEDTLS_RIPEMD160_C */
void ripemd160_text(char *text_src_string, data_t *hash)
{
    int ret;
    unsigned char src_str[100];
    unsigned char output[20];

    memset(src_str, 0x00, sizeof(src_str));
    memset(output, 0x00, sizeof(output));

    strncpy((char *) src_str, text_src_string, sizeof(src_str) - 1);

    ret = mbedtls_ripemd160(src_str, strlen((char *) src_str), output);
    TEST_ASSERT(ret == 0);

    TEST_ASSERT(mbedtls_test_hexcmp(output, hash->x,
                                    sizeof(output), hash->len) == 0);
}
/* END_CASE */

/* BEGIN_CASE depends_on:MBEDTLS_MD5_C:MBEDTLS_SELF_TEST */
void md5_selftest()
{
    TEST_ASSERT(mbedtls_md5_self_test(1) == 0);
}
/* END_CASE */

/* BEGIN_CASE depends_on:MBEDTLS_RIPEMD160_C:MBEDTLS_SELF_TEST */
void ripemd160_selftest()
{
    TEST_ASSERT(mbedtls_ripemd160_self_test(1) == 0);
}
/* END_CASE */
