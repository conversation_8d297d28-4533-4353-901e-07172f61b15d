Invalid input
invalid:

AES-128-ECB Encrypt NIST KAT #1
depends_on:MBEDTLS_AES_C
test_vec:MBEDTLS_CIPHER_ID_AES:"00000000000000000000000000000000":"f34481ec3cc627bacd5dc3fb08f273e6":"0336763e966d92595a567cc9ce537f5e"

AES-128-ECB Encrypt NIST KAT #2
depends_on:MBEDTLS_AES_C
test_vec:MBEDTLS_CIPHER_ID_AES:"00000000000000000000000000000000":"9798c4640bad75c7c3227db910174e72":"a9a1631bf4996954ebc093957b234589"

AES-128-ECB Encrypt NIST KAT #3
depends_on:MBEDTLS_AES_C
test_vec:MBEDTLS_CIPHER_ID_AES:"00000000000000000000000000000000":"96ab5c2ff612d9dfaae8c31f30c42168":"ff4f8391a6a40ca5b25d23bedd44a597"

AES-128-ECB Encrypt NIST KAT #4
depends_on:MBEDTLS_AES_C
test_vec:MBEDTLS_CIPHER_ID_AES:"e0000000000000000000000000000000":"00000000000000000000000000000000":"72a1da770f5d7ac4c9ef94d822affd97"

AES-128-ECB Encrypt NIST KAT #5
depends_on:MBEDTLS_AES_C
test_vec:MBEDTLS_CIPHER_ID_AES:"f0000000000000000000000000000000":"00000000000000000000000000000000":"970014d634e2b7650777e8e84d03ccd8"

AES-128-ECB Encrypt NIST KAT #6
depends_on:MBEDTLS_AES_C
test_vec:MBEDTLS_CIPHER_ID_AES:"f8000000000000000000000000000000":"00000000000000000000000000000000":"f17e79aed0db7e279e955b5f493875a7"

AES-128-ECB Encrypt NIST KAT #7
depends_on:MBEDTLS_AES_C
test_vec:MBEDTLS_CIPHER_ID_AES:"fffffffffffff0000000000000000000":"00000000000000000000000000000000":"7b90785125505fad59b13c186dd66ce3"

AES-128-ECB Encrypt NIST KAT #8
depends_on:MBEDTLS_AES_C
test_vec:MBEDTLS_CIPHER_ID_AES:"fffffffffffff8000000000000000000":"00000000000000000000000000000000":"8b527a6aebdaec9eaef8eda2cb7783e5"

AES-128-ECB Encrypt NIST KAT #9
depends_on:MBEDTLS_AES_C
test_vec:MBEDTLS_CIPHER_ID_AES:"fffffffffffffc000000000000000000":"00000000000000000000000000000000":"43fdaf53ebbc9880c228617d6a9b548b"

AES-128-ECB Encrypt NIST KAT #10
depends_on:MBEDTLS_AES_C
test_vec:MBEDTLS_CIPHER_ID_AES:"ffffffffffffffffffffffffffffc000":"00000000000000000000000000000000":"70c46bb30692be657f7eaa93ebad9897"

AES-128-ECB Encrypt NIST KAT #11
depends_on:MBEDTLS_AES_C
test_vec:MBEDTLS_CIPHER_ID_AES:"ffffffffffffffffffffffffffffe000":"00000000000000000000000000000000":"323994cfb9da285a5d9642e1759b224a"

AES-128-ECB Encrypt NIST KAT #12
depends_on:MBEDTLS_AES_C
test_vec:MBEDTLS_CIPHER_ID_AES:"fffffffffffffffffffffffffffff000":"00000000000000000000000000000000":"1dbf57877b7b17385c85d0b54851e371"

AES-128-ECB Encrypt NIST KAT #13
depends_on:MBEDTLS_AES_C
test_vec:MBEDTLS_CIPHER_ID_AES:"00000000000000000000000000000000":"ffffffffffffffc00000000000000000":"3a4d354f02bb5a5e47d39666867f246a"

AES-128-ECB Encrypt NIST KAT #14
depends_on:MBEDTLS_AES_C
test_vec:MBEDTLS_CIPHER_ID_AES:"00000000000000000000000000000000":"ffffffffffffffe00000000000000000":"d451b8d6e1e1a0ebb155fbbf6e7b7dc3"

AES-128-ECB Encrypt NIST KAT #15
depends_on:MBEDTLS_AES_C
test_vec:MBEDTLS_CIPHER_ID_AES:"00000000000000000000000000000000":"fffffffffffffff00000000000000000":"6898d4f42fa7ba6a10ac05e87b9f2080"

AES-128-ECB Encrypt NIST KAT #16
depends_on:MBEDTLS_AES_C
test_vec:MBEDTLS_CIPHER_ID_AES:"00000000000000000000000000000000":"ffffffffffffffffffffffffe0000000":"082eb8be35f442fb52668e16a591d1d6"

AES-128-ECB Encrypt NIST KAT #17
depends_on:MBEDTLS_AES_C
test_vec:MBEDTLS_CIPHER_ID_AES:"00000000000000000000000000000000":"fffffffffffffffffffffffff0000000":"e656f9ecf5fe27ec3e4a73d00c282fb3"

AES-128-ECB Encrypt NIST KAT #18
depends_on:MBEDTLS_AES_C
test_vec:MBEDTLS_CIPHER_ID_AES:"00000000000000000000000000000000":"fffffffffffffffffffffffff8000000":"2ca8209d63274cd9a29bb74bcd77683a"

AES-192-ECB Encrypt NIST KAT #1
depends_on:MBEDTLS_AES_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
test_vec:MBEDTLS_CIPHER_ID_AES:"000000000000000000000000000000000000000000000000":"fffffffffffffffffffff80000000000":"156f07767a85a4312321f63968338a01"

AES-192-ECB Encrypt NIST KAT #2
depends_on:MBEDTLS_AES_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
test_vec:MBEDTLS_CIPHER_ID_AES:"000000000000000000000000000000000000000000000000":"fffffffffffffffffffffc0000000000":"15eec9ebf42b9ca76897d2cd6c5a12e2"

AES-192-ECB Encrypt NIST KAT #3
depends_on:MBEDTLS_AES_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
test_vec:MBEDTLS_CIPHER_ID_AES:"000000000000000000000000000000000000000000000000":"fffffffffffffffffffffe0000000000":"db0d3a6fdcc13f915e2b302ceeb70fd8"

AES-192-ECB Encrypt NIST KAT #4
depends_on:MBEDTLS_AES_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
test_vec:MBEDTLS_CIPHER_ID_AES:"000000000000000000000000000000000000000000000000":"51719783d3185a535bd75adc65071ce1":"4f354592ff7c8847d2d0870ca9481b7c"

AES-192-ECB Encrypt NIST KAT #5
depends_on:MBEDTLS_AES_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
test_vec:MBEDTLS_CIPHER_ID_AES:"000000000000000000000000000000000000000000000000":"26aa49dcfe7629a8901a69a9914e6dfd":"d5e08bf9a182e857cf40b3a36ee248cc"

AES-192-ECB Encrypt NIST KAT #6
depends_on:MBEDTLS_AES_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
test_vec:MBEDTLS_CIPHER_ID_AES:"000000000000000000000000000000000000000000000000":"941a4773058224e1ef66d10e0a6ee782":"067cd9d3749207791841562507fa9626"

AES-192-ECB Encrypt NIST KAT #7
depends_on:MBEDTLS_AES_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
test_vec:MBEDTLS_CIPHER_ID_AES:"d2926527e0aa9f37b45e2ec2ade5853ef807576104c7ace3":"00000000000000000000000000000000":"dd619e1cf204446112e0af2b9afa8f8c"

AES-192-ECB Encrypt NIST KAT #8
depends_on:MBEDTLS_AES_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
test_vec:MBEDTLS_CIPHER_ID_AES:"982215f4e173dfa0fcffe5d3da41c4812c7bcc8ed3540f93":"00000000000000000000000000000000":"d4f0aae13c8fe9339fbf9e69ed0ad74d"

AES-192-ECB Encrypt NIST KAT #9
depends_on:MBEDTLS_AES_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
test_vec:MBEDTLS_CIPHER_ID_AES:"98c6b8e01e379fbd14e61af6af891596583565f2a27d59e9":"00000000000000000000000000000000":"19c80ec4a6deb7e5ed1033dda933498f"

AES-192-ECB Encrypt NIST KAT #10
depends_on:MBEDTLS_AES_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
test_vec:MBEDTLS_CIPHER_ID_AES:"fffffffffffffffffffffffffff800000000000000000000":"00000000000000000000000000000000":"8dd274bd0f1b58ae345d9e7233f9b8f3"

AES-192-ECB Encrypt NIST KAT #11
depends_on:MBEDTLS_AES_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
test_vec:MBEDTLS_CIPHER_ID_AES:"fffffffffffffffffffffffffffc00000000000000000000":"00000000000000000000000000000000":"9d6bdc8f4ce5feb0f3bed2e4b9a9bb0b"

AES-192-ECB Encrypt NIST KAT #12
depends_on:MBEDTLS_AES_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
test_vec:MBEDTLS_CIPHER_ID_AES:"fffffffffffffffffffffffffffe00000000000000000000":"00000000000000000000000000000000":"fd5548bcf3f42565f7efa94562528d46"

AES-256-ECB Encrypt NIST KAT #1
depends_on:MBEDTLS_AES_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
test_vec:MBEDTLS_CIPHER_ID_AES:"c1cc358b449909a19436cfbb3f852ef8bcb5ed12ac7058325f56e6099aab1a1c":"00000000000000000000000000000000":"352065272169abf9856843927d0674fd"

AES-256-ECB Encrypt NIST KAT #2
depends_on:MBEDTLS_AES_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
test_vec:MBEDTLS_CIPHER_ID_AES:"984ca75f4ee8d706f46c2d98c0bf4a45f5b00d791c2dfeb191b5ed8e420fd627":"00000000000000000000000000000000":"4307456a9e67813b452e15fa8fffe398"

AES-256-ECB Encrypt NIST KAT #3
depends_on:MBEDTLS_AES_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
test_vec:MBEDTLS_CIPHER_ID_AES:"b43d08a447ac8609baadae4ff12918b9f68fc1653f1269222f123981ded7a92f":"00000000000000000000000000000000":"4663446607354989477a5c6f0f007ef4"

AES-256-ECB Encrypt NIST KAT #4
depends_on:MBEDTLS_AES_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
test_vec:MBEDTLS_CIPHER_ID_AES:"0000000000000000000000000000000000000000000000000000000000000000":"0b24af36193ce4665f2825d7b4749c98":"a9ff75bd7cf6613d3731c77c3b6d0c04"

AES-256-ECB Encrypt NIST KAT #5
depends_on:MBEDTLS_AES_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
test_vec:MBEDTLS_CIPHER_ID_AES:"0000000000000000000000000000000000000000000000000000000000000000":"761c1fe41a18acf20d241650611d90f1":"623a52fcea5d443e48d9181ab32c7421"

AES-256-ECB Encrypt NIST KAT #6
depends_on:MBEDTLS_AES_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
test_vec:MBEDTLS_CIPHER_ID_AES:"0000000000000000000000000000000000000000000000000000000000000000":"8a560769d605868ad80d819bdba03771":"38f2c7ae10612415d27ca190d27da8b4"

AES-256-ECB Encrypt NIST KAT #7
depends_on:MBEDTLS_AES_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
test_vec:MBEDTLS_CIPHER_ID_AES:"0000000000000000000000000000000000000000000000000000000000000000":"ffffff80000000000000000000000000":"36aff0ef7bf3280772cf4cac80a0d2b2"

AES-256-ECB Encrypt NIST KAT #8
depends_on:MBEDTLS_AES_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
test_vec:MBEDTLS_CIPHER_ID_AES:"0000000000000000000000000000000000000000000000000000000000000000":"ffffffc0000000000000000000000000":"1f8eedea0f62a1406d58cfc3ecea72cf"

AES-256-ECB Encrypt NIST KAT #9
depends_on:MBEDTLS_AES_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
test_vec:MBEDTLS_CIPHER_ID_AES:"0000000000000000000000000000000000000000000000000000000000000000":"ffffffe0000000000000000000000000":"abf4154a3375a1d3e6b1d454438f95a6"

AES-256-ECB Encrypt NIST KAT #10
depends_on:MBEDTLS_AES_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
test_vec:MBEDTLS_CIPHER_ID_AES:"ffffffffffffffffffffffffffffffffffff8000000000000000000000000000":"00000000000000000000000000000000":"45d089c36d5c5a4efc689e3b0de10dd5"

AES-256-ECB Encrypt NIST KAT #11
depends_on:MBEDTLS_AES_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
test_vec:MBEDTLS_CIPHER_ID_AES:"ffffffffffffffffffffffffffffffffffffc000000000000000000000000000":"00000000000000000000000000000000":"b4da5df4becb5462e03a0ed00d295629"

AES-256-ECB Encrypt NIST KAT #12
depends_on:MBEDTLS_AES_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
test_vec:MBEDTLS_CIPHER_ID_AES:"ffffffffffffffffffffffffffffffffffffe000000000000000000000000000":"00000000000000000000000000000000":"dcf4e129136c1a4b7a0f38935cc34b2b"

ARIA-128-ECB Encrypt - RFC 5794
depends_on:MBEDTLS_ARIA_C
test_vec:MBEDTLS_CIPHER_ID_ARIA:"000102030405060708090a0b0c0d0e0f":"00112233445566778899aabbccddeeff":"d718fbd6ab644c739da95f3be6451778"

ARIA-192-ECB Encrypt - RFC 5794
depends_on:MBEDTLS_ARIA_C
test_vec:MBEDTLS_CIPHER_ID_ARIA:"000102030405060708090a0b0c0d0e0f1011121314151617":"00112233445566778899aabbccddeeff":"26449c1805dbe7aa25a468ce263a9e79"

ARIA-256-ECB Encrypt - RFC 5794
depends_on:MBEDTLS_ARIA_C
test_vec:MBEDTLS_CIPHER_ID_ARIA:"000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f":"00112233445566778899aabbccddeeff":"f92bd7c79fb72e2f2b8f80c1972d24fc"

Camellia-128-ECB Encrypt RFC3713 #1
depends_on:MBEDTLS_CAMELLIA_C
test_vec:MBEDTLS_CIPHER_ID_CAMELLIA:"0123456789abcdeffedcba9876543210":"0123456789abcdeffedcba9876543210":"67673138549669730857065648eabe43"

Camellia-192-ECB Encrypt RFC3713 #1
depends_on:MBEDTLS_CAMELLIA_C
test_vec:MBEDTLS_CIPHER_ID_CAMELLIA:"0123456789abcdeffedcba98765432100011223344556677":"0123456789abcdeffedcba9876543210":"b4993401b3e996f84ee5cee7d79b09b9"

Camellia-256-ECB Encrypt RFC3713 #1
depends_on:MBEDTLS_CAMELLIA_C
test_vec:MBEDTLS_CIPHER_ID_CAMELLIA:"0123456789abcdeffedcba987654321000112233445566778899aabbccddeeff":"0123456789abcdeffedcba9876543210":"9acc237dff16d76c20ef7c919e3a7509"

Camellia-128-ECB Encrypt Perl EVP #1 [#1]
depends_on:MBEDTLS_CAMELLIA_C
test_vec:MBEDTLS_CIPHER_ID_CAMELLIA:"000102030405060708090A0B0C0D0E0F":"00112233445566778899AABBCCDDEEFF":"77CF412067AF8270613529149919546F"

Camellia-192-ECB Encrypt Perl EVP #1 [#1]
depends_on:MBEDTLS_CAMELLIA_C
test_vec:MBEDTLS_CIPHER_ID_CAMELLIA:"000102030405060708090A0B0C0D0E0F1011121314151617":"00112233445566778899AABBCCDDEEFF":"B22F3C36B72D31329EEE8ADDC2906C68"

Camellia-256-ECB Encrypt Perl EVP #1 [#1]
depends_on:MBEDTLS_CAMELLIA_C
test_vec:MBEDTLS_CIPHER_ID_CAMELLIA:"000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F":"00112233445566778899AABBCCDDEEFF":"2EDF1F3418D53B88841FC8985FB1ECF2"

Camellia-128-ECB Encrypt Perl EVP #1 [#2]
depends_on:MBEDTLS_CAMELLIA_C
test_vec:MBEDTLS_CIPHER_ID_CAMELLIA:"2B7E151628AED2A6ABF7158809CF4F3C":"6BC1BEE22E409F96E93D7E117393172A":"432FC5DCD628115B7C388D770B270C96"

Camellia-128-ECB Encrypt Perl EVP #2
depends_on:MBEDTLS_CAMELLIA_C
test_vec:MBEDTLS_CIPHER_ID_CAMELLIA:"2B7E151628AED2A6ABF7158809CF4F3C":"AE2D8A571E03AC9C9EB76FAC45AF8E51":"0BE1F14023782A22E8384C5ABB7FAB2B"

Camellia-128-ECB Encrypt Perl EVP #3
depends_on:MBEDTLS_CAMELLIA_C
test_vec:MBEDTLS_CIPHER_ID_CAMELLIA:"2B7E151628AED2A6ABF7158809CF4F3C":"30C81C46A35CE411E5FBC1191A0A52EF":"A0A1ABCD1893AB6FE0FE5B65DF5F8636"

Camellia-128-ECB Encrypt Perl EVP #4
depends_on:MBEDTLS_CAMELLIA_C
test_vec:MBEDTLS_CIPHER_ID_CAMELLIA:"2B7E151628AED2A6ABF7158809CF4F3C":"F69F2445DF4F9B17AD2B417BE66C3710":"E61925E0D5DFAA9BB29F815B3076E51A"

Camellia-192-ECB Encrypt Perl EVP #1 [#2]
depends_on:MBEDTLS_CAMELLIA_C
test_vec:MBEDTLS_CIPHER_ID_CAMELLIA:"8E73B0F7DA0E6452C810F32B809079E562F8EAD2522C6B7B":"6BC1BEE22E409F96E93D7E117393172A":"CCCC6C4E138B45848514D48D0D3439D3"

Camellia-192-ECB Encrypt Perl EVP #2
depends_on:MBEDTLS_CAMELLIA_C
test_vec:MBEDTLS_CIPHER_ID_CAMELLIA:"8E73B0F7DA0E6452C810F32B809079E562F8EAD2522C6B7B":"AE2D8A571E03AC9C9EB76FAC45AF8E51":"5713C62C14B2EC0F8393B6AFD6F5785A"

Camellia-192-ECB Encrypt Perl EVP #3
depends_on:MBEDTLS_CAMELLIA_C
test_vec:MBEDTLS_CIPHER_ID_CAMELLIA:"8E73B0F7DA0E6452C810F32B809079E562F8EAD2522C6B7B":"30C81C46A35CE411E5FBC1191A0A52EF":"B40ED2B60EB54D09D030CF511FEEF366"

Camellia-192-ECB Encrypt Perl EVP #4
depends_on:MBEDTLS_CAMELLIA_C
test_vec:MBEDTLS_CIPHER_ID_CAMELLIA:"8E73B0F7DA0E6452C810F32B809079E562F8EAD2522C6B7B":"F69F2445DF4F9B17AD2B417BE66C3710":"909DBD95799096748CB27357E73E1D26"

Camellia-256-ECB Encrypt Perl EVP #1 [#2]
depends_on:MBEDTLS_CAMELLIA_C
test_vec:MBEDTLS_CIPHER_ID_CAMELLIA:"603DEB1015CA71BE2B73AEF0857D77811F352C073B6108D72D9810A30914DFF4":"6BC1BEE22E409F96E93D7E117393172A":"BEFD219B112FA00098919CD101C9CCFA"

Camellia-256-ECB Encrypt Perl EVP #2
depends_on:MBEDTLS_CAMELLIA_C
test_vec:MBEDTLS_CIPHER_ID_CAMELLIA:"603DEB1015CA71BE2B73AEF0857D77811F352C073B6108D72D9810A30914DFF4":"AE2D8A571E03AC9C9EB76FAC45AF8E51":"C91D3A8F1AEA08A9386CF4B66C0169EA"

Camellia-256-ECB Encrypt Perl EVP #3
depends_on:MBEDTLS_CAMELLIA_C
test_vec:MBEDTLS_CIPHER_ID_CAMELLIA:"603DEB1015CA71BE2B73AEF0857D77811F352C073B6108D72D9810A30914DFF4":"30C81C46A35CE411E5FBC1191A0A52EF":"A623D711DC5F25A51BB8A80D56397D28"

Camellia-256-ECB Encrypt Perl EVP #4
depends_on:MBEDTLS_CAMELLIA_C
test_vec:MBEDTLS_CIPHER_ID_CAMELLIA:"603DEB1015CA71BE2B73AEF0857D77811F352C073B6108D72D9810A30914DFF4":"F69F2445DF4F9B17AD2B417BE66C3710":"7960109FB6DC42947FCFE59EA3C5EB6B"

