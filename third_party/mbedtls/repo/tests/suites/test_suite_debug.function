/* BEGIN_HEADER */
#include "debug_internal.h"
#include "string.h"
#include "mbedtls/pk.h"
#include <test/ssl_helpers.h>

struct buffer_data {
    char buf[2000];
    char *ptr;
};

void string_debug(void *data, int level, const char *file, int line, const char *str)
{
    struct buffer_data *buffer = (struct buffer_data *) data;
    char *p = buffer->ptr;
    ((void) level);

    memcpy(p, file, strlen(file));
    p += strlen(file);

    *p++ = '(';
    *p++ = '0' + (line / 1000) % 10;
    *p++ = '0' + (line / 100) % 10;
    *p++ = '0' + (line / 10) % 10;
    *p++ = '0' + (line / 1) % 10;
    *p++ = ')';
    *p++ = ':';
    *p++ = ' ';

#if defined(MBEDTLS_THREADING_C)
    /* Skip "thread ID" (up to the first space) as it is not predictable */
    while (*str++ != ' ') {
        ;
    }
#endif

    memcpy(p, str, strlen(str));
    p += strlen(str);

    /* Detect if debug messages output partial lines and mark them */
    if (p[-1] != '\n') {
        *p++ = '*';
    }

    buffer->ptr = p;
}
/* END_HEADER */

/* BEGIN_DEPENDENCIES
 * depends_on:MBEDTLS_DEBUG_C:MBEDTLS_SSL_TLS_C
 * END_DEPENDENCIES
 */

/* BEGIN_CASE */
void debug_print_msg_threshold(int threshold, int level, char *file,
                               int line, char *result_str)
{
    mbedtls_ssl_context ssl;
    mbedtls_ssl_config conf;
    struct buffer_data buffer;

    MD_PSA_INIT();

    mbedtls_ssl_init(&ssl);
    mbedtls_ssl_config_init(&conf);
    memset(buffer.buf, 0, 2000);
    buffer.ptr = buffer.buf;

    TEST_EQUAL(mbedtls_ssl_config_defaults(&conf,
                                           MBEDTLS_SSL_IS_CLIENT,
                                           MBEDTLS_SSL_TRANSPORT_STREAM,
                                           MBEDTLS_SSL_PRESET_DEFAULT),
               0);
    mbedtls_ssl_conf_rng(&conf, mbedtls_test_random, NULL);
    mbedtls_ssl_conf_dbg(&conf, string_debug, &buffer);

    TEST_ASSERT(mbedtls_ssl_setup(&ssl, &conf) == 0);

    mbedtls_debug_set_threshold(threshold);

    mbedtls_debug_print_msg(&ssl, level, file, line,
                            "Text message, 2 == %d", 2);

    TEST_ASSERT(strcmp(buffer.buf, result_str) == 0);

exit:
    mbedtls_ssl_free(&ssl);
    mbedtls_ssl_config_free(&conf);
    MD_PSA_DONE();
}
/* END_CASE */

/* BEGIN_CASE */
void mbedtls_debug_print_ret(char *file, int line, char *text, int value,
                             char *result_str)
{
    mbedtls_ssl_context ssl;
    mbedtls_ssl_config conf;
    struct buffer_data buffer;

    MD_PSA_INIT();

    mbedtls_ssl_init(&ssl);
    mbedtls_ssl_config_init(&conf);
    memset(buffer.buf, 0, 2000);
    buffer.ptr = buffer.buf;

    TEST_EQUAL(mbedtls_ssl_config_defaults(&conf,
                                           MBEDTLS_SSL_IS_CLIENT,
                                           MBEDTLS_SSL_TRANSPORT_STREAM,
                                           MBEDTLS_SSL_PRESET_DEFAULT),
               0);
    mbedtls_ssl_conf_rng(&conf, mbedtls_test_random, NULL);
    mbedtls_ssl_conf_dbg(&conf, string_debug, &buffer);

    TEST_ASSERT(mbedtls_ssl_setup(&ssl, &conf) == 0);

    mbedtls_debug_print_ret(&ssl, 0, file, line, text, value);

    TEST_ASSERT(strcmp(buffer.buf, result_str) == 0);

exit:
    mbedtls_ssl_free(&ssl);
    mbedtls_ssl_config_free(&conf);
    MD_PSA_DONE();
}
/* END_CASE */

/* BEGIN_CASE */
void mbedtls_debug_print_buf(char *file, int line, char *text,
                             data_t *data, char *result_str)
{
    mbedtls_ssl_context ssl;
    mbedtls_ssl_config conf;
    struct buffer_data buffer;

    MD_PSA_INIT();

    mbedtls_ssl_init(&ssl);
    mbedtls_ssl_config_init(&conf);
    memset(buffer.buf, 0, 2000);
    buffer.ptr = buffer.buf;

    TEST_EQUAL(mbedtls_ssl_config_defaults(&conf,
                                           MBEDTLS_SSL_IS_CLIENT,
                                           MBEDTLS_SSL_TRANSPORT_STREAM,
                                           MBEDTLS_SSL_PRESET_DEFAULT),
               0);
    mbedtls_ssl_conf_rng(&conf, mbedtls_test_random, NULL);
    mbedtls_ssl_conf_dbg(&conf, string_debug, &buffer);

    TEST_ASSERT(mbedtls_ssl_setup(&ssl, &conf) == 0);

    mbedtls_debug_print_buf(&ssl, 0, file, line, text, data->x, data->len);

    TEST_ASSERT(strcmp(buffer.buf, result_str) == 0);

exit:
    mbedtls_ssl_free(&ssl);
    mbedtls_ssl_config_free(&conf);
    MD_PSA_DONE();
}
/* END_CASE */

/* BEGIN_CASE depends_on:MBEDTLS_FS_IO:MBEDTLS_X509_CRT_PARSE_C:!MBEDTLS_X509_REMOVE_INFO */
void mbedtls_debug_print_crt(char *crt_file, char *file, int line,
                             char *prefix, char *result_str)
{
    mbedtls_x509_crt   crt;
    mbedtls_ssl_context ssl;
    mbedtls_ssl_config conf;
    struct buffer_data buffer;

    mbedtls_ssl_init(&ssl);
    mbedtls_ssl_config_init(&conf);
    mbedtls_x509_crt_init(&crt);
    MD_OR_USE_PSA_INIT();

    memset(buffer.buf, 0, 2000);
    buffer.ptr = buffer.buf;

    TEST_EQUAL(mbedtls_ssl_config_defaults(&conf,
                                           MBEDTLS_SSL_IS_CLIENT,
                                           MBEDTLS_SSL_TRANSPORT_STREAM,
                                           MBEDTLS_SSL_PRESET_DEFAULT),
               0);
    mbedtls_ssl_conf_rng(&conf, mbedtls_test_random, NULL);
    mbedtls_ssl_conf_dbg(&conf, string_debug, &buffer);

    TEST_ASSERT(mbedtls_ssl_setup(&ssl, &conf) == 0);

    TEST_ASSERT(mbedtls_x509_crt_parse_file(&crt, crt_file) == 0);
    mbedtls_debug_print_crt(&ssl, 0, file, line, prefix, &crt);

    TEST_ASSERT(strcmp(buffer.buf, result_str) == 0);

exit:
    mbedtls_x509_crt_free(&crt);
    mbedtls_ssl_free(&ssl);
    mbedtls_ssl_config_free(&conf);
    MD_OR_USE_PSA_DONE();
}
/* END_CASE */

/* BEGIN_CASE depends_on:MBEDTLS_BIGNUM_C */
void mbedtls_debug_print_mpi(char *value, char *file, int line,
                             char *prefix, char *result_str)
{
    mbedtls_ssl_context ssl;
    mbedtls_ssl_config conf;
    struct buffer_data buffer;
    mbedtls_mpi val;

    MD_PSA_INIT();

    mbedtls_ssl_init(&ssl);
    mbedtls_ssl_config_init(&conf);
    mbedtls_mpi_init(&val);
    memset(buffer.buf, 0, 2000);
    buffer.ptr = buffer.buf;

    TEST_EQUAL(mbedtls_ssl_config_defaults(&conf,
                                           MBEDTLS_SSL_IS_CLIENT,
                                           MBEDTLS_SSL_TRANSPORT_STREAM,
                                           MBEDTLS_SSL_PRESET_DEFAULT),
               0);
    mbedtls_ssl_conf_rng(&conf, mbedtls_test_random, NULL);
    mbedtls_ssl_conf_dbg(&conf, string_debug, &buffer);

    TEST_ASSERT(mbedtls_ssl_setup(&ssl, &conf) == 0);

    TEST_ASSERT(mbedtls_test_read_mpi(&val, value) == 0);

    mbedtls_debug_print_mpi(&ssl, 0, file, line, prefix, &val);

    TEST_ASSERT(strcmp(buffer.buf, result_str) == 0);

exit:
    mbedtls_mpi_free(&val);
    mbedtls_ssl_free(&ssl);
    mbedtls_ssl_config_free(&conf);
    MD_PSA_DONE();
}
/* END_CASE */
