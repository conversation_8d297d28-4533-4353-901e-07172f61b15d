Test mbedtls_mpi_mod_raw_io #1 BE (Buffer and limbs just fit, input limb-aligned)
mpi_mod_raw_io:"0941379d00fed1491fe15df284dfde4a142f68aa8d412023195cee66883e6290ffe703f4ea5963bf212713cee46b107c09182b5edcd955adac418bf4918e2889af48e1099d513830cec85c26ac1e158b52620e33ba8692f893efbb2f958b4424":96:24:MBEDTLS_MPI_MOD_EXT_REP_BE:0:0

Test mbedtls_mpi_mod_raw_io #1 LE (Buffer and limbs just fit, input limb-aligned)
mpi_mod_raw_io:"0941379d00fed1491fe15df284dfde4a142f68aa8d412023195cee66883e6290ffe703f4ea5963bf212713cee46b107c09182b5edcd955adac418bf4918e2889af48e1099d513830cec85c26ac1e158b52620e33ba8692f893efbb2f958b4424":96:24:MBEDTLS_MPI_MOD_EXT_REP_LE:0:0

Test mbedtls_mpi_mod_raw_io #2 BE (Buffer and limbs just fit, input unaligned)
mpi_mod_raw_io:"0941379d00fed1491fe15df284dfde4a142f68aa8d412023195cee66883e6290ffe703f4ea5963bf212713cee46b107c09182b5edcd955adac418bf4918e2889af48e1099d513830cec85c26ac1e158b52620e33ba8692f893efbb2f958b":94:24:MBEDTLS_MPI_MOD_EXT_REP_BE:0:0

Test mbedtls_mpi_mod_raw_io #2 LE  (Buffer and limbs just fit, input unaligned)
mpi_mod_raw_io:"0941379d00fed1491fe15df284dfde4a142f68aa8d412023195cee66883e6290ffe703f4ea5963bf212713cee46b107c09182b5edcd955adac418bf4918e2889af48e1099d513830cec85c26ac1e158b52620e33ba8692f893efbb2f958b":94:24:MBEDTLS_MPI_MOD_EXT_REP_LE:0:0

Test mbedtls_mpi_mod_raw_io #3 BE (Buffer just fits, extra limbs, input limb-aligned)
mpi_mod_raw_io:"0941379d00fed1491fe15df284dfde4a142f68aa8d412023195cee66883e6290ffe703f4ea5963bf212713cee46b107c09182b5edcd955adac418bf4918e2889af48e1099d513830cec85c26ac1e158b52620e33ba8692f893efbb2f958b4424":96:28:MBEDTLS_MPI_MOD_EXT_REP_BE:0:0

Test mbedtls_mpi_mod_raw_io #3 LE (Buffer just fits, extra limbs, input limb-aligned)
mpi_mod_raw_io:"0941379d00fed1491fe15df284dfde4a142f68aa8d412023195cee66883e6290ffe703f4ea5963bf212713cee46b107c09182b5edcd955adac418bf4918e2889af48e1099d513830cec85c26ac1e158b52620e33ba8692f893efbb2f958b4424":96:28:MBEDTLS_MPI_MOD_EXT_REP_LE:0:0

Test mbedtls_mpi_mod_raw_io #4 BE (Buffer just fits, extra limbs, input unaligned)
mpi_mod_raw_io:"0941379d00fed1491fe15df284dfde4a142f68aa8d412023195cee66883e6290ffe703f4ea5963bf212713cee46b107c09182b5edcd955adac418bf4918e2889af48e1099d513830cec85c26ac1e158b52620e33ba8692f893efbb2f958b":94:28:MBEDTLS_MPI_MOD_EXT_REP_BE:0:0

Test mbedtls_mpi_mod_raw_io #4 LE (Buffer just fits, extra limbs, input unaligned)
mpi_mod_raw_io:"0941379d00fed1491fe15df284dfde4a142f68aa8d412023195cee66883e6290ffe703f4ea5963bf212713cee46b107c09182b5edcd955adac418bf4918e2889af48e1099d513830cec85c26ac1e158b52620e33ba8692f893efbb2f958b":94:28:MBEDTLS_MPI_MOD_EXT_REP_LE:0:0

Test mbedtls_mpi_mod_raw_io #5 BE (Extra limbs, buffer aligned to extra limbs, input limb-aligned)
mpi_mod_raw_io:"0941379d00fed1491fe15df284dfde4a142f68aa8d412023195cee66883e6290ffe703f4ea5963bf212713cee46b107c09182b5edcd955adac418bf4918e2889af48e1099d513830cec85c26ac1e158b52620e33ba8692f893efbb2f958b4424":112:28:MBEDTLS_MPI_MOD_EXT_REP_BE:0:0

Test mbedtls_mpi_mod_raw_io #5 LE (Extra limbs, buffer aligned to extra limbs, input limb-aligned)
mpi_mod_raw_io:"0941379d00fed1491fe15df284dfde4a142f68aa8d412023195cee66883e6290ffe703f4ea5963bf212713cee46b107c09182b5edcd955adac418bf4918e2889af48e1099d513830cec85c26ac1e158b52620e33ba8692f893efbb2f958b4424":112:28:MBEDTLS_MPI_MOD_EXT_REP_LE:0:0

Test mbedtls_mpi_mod_raw_io #6 BE (Extra limbs, buffer aligned to extra limbs, input unaligned)
mpi_mod_raw_io:"0941379d00fed1491fe15df284dfde4a142f68aa8d412023195cee66883e6290ffe703f4ea5963bf212713cee46b107c09182b5edcd955adac418bf4918e2889af48e1099d513830cec85c26ac1e158b52620e33ba8692f893efbb2f958b":112:28:MBEDTLS_MPI_MOD_EXT_REP_BE:0:0

Test mbedtls_mpi_mod_raw_io #6 LE (Extra limbs, buffer aligned to extra limbs, input unaligned)
mpi_mod_raw_io:"0941379d00fed1491fe15df284dfde4a142f68aa8d412023195cee66883e6290ffe703f4ea5963bf212713cee46b107c09182b5edcd955adac418bf4918e2889af48e1099d513830cec85c26ac1e158b52620e33ba8692f893efbb2f958b":112:28:MBEDTLS_MPI_MOD_EXT_REP_LE:0:0

Test mbedtls_mpi_mod_raw_io #7 BE (Buffer and limbs just fit, input limb-aligned with leading zeroes)
mpi_mod_raw_io:"00000000000000001fe15df284dfde4a142f68aa8d412023195cee66883e6290ffe703f4ea5963bf212713cee46b107c09182b5edcd955adac418bf4918e2889af48e1099d513830cec85c26ac1e158b52620e33ba8692f893efbb2f958b4424":88:24:MBEDTLS_MPI_MOD_EXT_REP_BE:0:0

Test mbedtls_mpi_mod_raw_io #7 LE (Buffer and limbs just fit, input limb-aligned with leading zeroes)
mpi_mod_raw_io:"1fe15df284dfde4a142f68aa8d412023195cee66883e6290ffe703f4ea5963bf212713cee46b107c09182b5edcd955adac418bf4918e2889af48e1099d513830cec85c26ac1e158b52620e33ba8692f893efbb2f958b44240000000000000000":88:24:MBEDTLS_MPI_MOD_EXT_REP_LE:0:0

Test mbedtls_mpi_mod_raw_io #8 BE (Buffer and limbs just fit, input unaligned with leading zeroes)
mpi_mod_raw_io:"00000000000000001fe15df284dfde4a142f68aa8d412023195cee66883e6290ffe703f4ea5963bf212713cee46b107c09182b5edcd955adac418bf4918e2889af48e1099d513830cec85c26ac1e158b52620e33ba8692f893efbb2f958b":86:24:MBEDTLS_MPI_MOD_EXT_REP_BE:0:0

Test mbedtls_mpi_mod_raw_io #8 LE (Buffer and limbs just fit, input unaligned with leading zeroes)
mpi_mod_raw_io:"1fe15df284dfde4a142f68aa8d412023195cee66883e6290ffe703f4ea5963bf212713cee46b107c09182b5edcd955adac418bf4918e2889af48e1099d513830cec85c26ac1e158b52620e33ba8692f893efbb2f958b0000000000000000":86:24:MBEDTLS_MPI_MOD_EXT_REP_LE:0:0

Test mbedtls_mpi_mod_raw_io #9 BE (Buffer just fits, extra limbs, input limb-aligned with leading zeroes)
mpi_mod_raw_io:"00000000000000001fe15df284dfde4a142f68aa8d412023195cee66883e6290ffe703f4ea5963bf212713cee46b107c09182b5edcd955adac418bf4918e2889af48e1099d513830cec85c26ac1e158b52620e33ba8692f893efbb2f958b4424":88:28:MBEDTLS_MPI_MOD_EXT_REP_BE:0:0

Test mbedtls_mpi_mod_raw_io #9 LE (Buffer just fits, extra limbs, input limb-aligned with leading zeroes)
mpi_mod_raw_io:"1fe15df284dfde4a142f68aa8d412023195cee66883e6290ffe703f4ea5963bf212713cee46b107c09182b5edcd955adac418bf4918e2889af48e1099d513830cec85c26ac1e158b52620e33ba8692f893efbb2f958b44240000000000000000":88:28:MBEDTLS_MPI_MOD_EXT_REP_LE:0:0

Test mbedtls_mpi_mod_raw_io #10 BE (Buffer just fits, extra limbs, input unaligned with leading zeroes)
mpi_mod_raw_io:"00000000000000001fe15df284dfde4a142f68aa8d412023195cee66883e6290ffe703f4ea5963bf212713cee46b107c09182b5edcd955adac418bf4918e2889af48e1099d513830cec85c26ac1e158b52620e33ba8692f893efbb2f958b":86:28:MBEDTLS_MPI_MOD_EXT_REP_BE:0:0

Test mbedtls_mpi_mod_raw_io #10 LE (Buffer just fits, extra limbs, input unaligned with leading zeroes)
mpi_mod_raw_io:"1fe15df284dfde4a142f68aa8d412023195cee66883e6290ffe703f4ea5963bf212713cee46b107c09182b5edcd955adac418bf4918e2889af48e1099d513830cec85c26ac1e158b52620e33ba8692f893efbb2f958b0000000000000000":86:28:MBEDTLS_MPI_MOD_EXT_REP_LE:0:0

Test mbedtls_mpi_mod_raw_io #11 BE (Zero)
mpi_mod_raw_io:"00":1:1:MBEDTLS_MPI_MOD_EXT_REP_BE:0:0

Test mbedtls_mpi_mod_raw_io #11 LE (Zero)
mpi_mod_raw_io:"00":1:1:MBEDTLS_MPI_MOD_EXT_REP_LE:0:0

Test mbedtls_mpi_mod_raw_io #12 BE (Zero, empty output)
mpi_mod_raw_io:"00":0:1:MBEDTLS_MPI_MOD_EXT_REP_BE:0:0

Test mbedtls_mpi_mod_raw_io #12 LE (Zero, empty output)
mpi_mod_raw_io:"00":0:1:MBEDTLS_MPI_MOD_EXT_REP_LE:0:0

Test mbedtls_mpi_mod_raw_io #13 BE (Zero, empty input)
mpi_mod_raw_io:"":1:1:MBEDTLS_MPI_MOD_EXT_REP_BE:0:0

Test mbedtls_mpi_mod_raw_io #13 LE (Zero, empty input)
mpi_mod_raw_io:"":1:1:MBEDTLS_MPI_MOD_EXT_REP_LE:0:0

Test mbedtls_mpi_mod_raw_io #14 BE (One)
mpi_mod_raw_io:"01":1:1:MBEDTLS_MPI_MOD_EXT_REP_BE:0:0

Test mbedtls_mpi_mod_raw_io #14 LE (One)
mpi_mod_raw_io:"01":1:1:MBEDTLS_MPI_MOD_EXT_REP_LE:0:0

Test mbedtls_mpi_mod_raw_io #14 BE (One limb)
mpi_mod_raw_io:"ff00000000000000":8:2:MBEDTLS_MPI_MOD_EXT_REP_BE:0:0

Test mbedtls_mpi_mod_raw_io #14 LE (One limb)
mpi_mod_raw_io:"00000000000000ff":8:2:MBEDTLS_MPI_MOD_EXT_REP_LE:0:0

Test mbedtls_mpi_mod_raw_io #15 BE (One limb)
depends_on:MBEDTLS_HAVE_INT32
mpi_mod_raw_io:"000000ff":4:1:MBEDTLS_MPI_MOD_EXT_REP_BE:0:0

Test mbedtls_mpi_mod_raw_io #15 LE (One limb)
depends_on:MBEDTLS_HAVE_INT32
mpi_mod_raw_io:"000000ff":4:1:MBEDTLS_MPI_MOD_EXT_REP_LE:0:0

Test mbedtls_mpi_mod_raw_io #16 BE (One limb)
depends_on:MBEDTLS_HAVE_INT64
mpi_mod_raw_io:"00000000000000ff":8:2:MBEDTLS_MPI_MOD_EXT_REP_BE:0:0

Test mbedtls_mpi_mod_raw_io #16 LE (One limb)
depends_on:MBEDTLS_HAVE_INT64
mpi_mod_raw_io:"00000000000000ff":8:2:MBEDTLS_MPI_MOD_EXT_REP_LE:0:0

Test mbedtls_mpi_mod_raw_io #17 BE (not enough limbs, input limb-aligned)
mpi_mod_raw_io:"0941379d00fed1491fe15df284dfde4a142f68aa8d412023195cee66883e6290ffe703f4ea5963bf212713cee46b107c09182b5edcd955adac418bf4918e2889af48e1099d513830cec85c26ac1e158b52620e33ba8692f893efbb2f958b4424":96:22:MBEDTLS_MPI_MOD_EXT_REP_BE:MBEDTLS_ERR_MPI_BUFFER_TOO_SMALL:0

Test mbedtls_mpi_mod_raw_io #17 LE (not enough limbs, input limb-aligned)
mpi_mod_raw_io:"0941379d00fed1491fe15df284dfde4a142f68aa8d412023195cee66883e6290ffe703f4ea5963bf212713cee46b107c09182b5edcd955adac418bf4918e2889af48e1099d513830cec85c26ac1e158b52620e33ba8692f893efbb2f958b4424":96:22:MBEDTLS_MPI_MOD_EXT_REP_LE:MBEDTLS_ERR_MPI_BUFFER_TOO_SMALL:0

Test mbedtls_mpi_mod_raw_io #18 BE (not enough limbs, input unaligned)
mpi_mod_raw_io:"0941379d00fed1491fe15df284dfde4a142f68aa8d412023195cee66883e6290ffe703f4ea5963bf212713cee46b107c09182b5edcd955adac418bf4918e2889af48e1099d513830cec85c26ac1e158b52620e33ba8692f893efbb2f958b":94:22:MBEDTLS_MPI_MOD_EXT_REP_BE:MBEDTLS_ERR_MPI_BUFFER_TOO_SMALL:0

Test mbedtls_mpi_mod_raw_io #18 LE (not enough limbs, input unaligned)
mpi_mod_raw_io:"0941379d00fed1491fe15df284dfde4a142f68aa8d412023195cee66883e6290ffe703f4ea5963bf212713cee46b107c09182b5edcd955adac418bf4918e2889af48e1099d513830cec85c26ac1e158b52620e33ba8692f893efbb2f958b":94:22:MBEDTLS_MPI_MOD_EXT_REP_LE:MBEDTLS_ERR_MPI_BUFFER_TOO_SMALL:0

Test mbedtls_mpi_mod_raw_io #19 BE (buffer too small, input limb-aligned)
mpi_mod_raw_io:"0941379d00fed1491fe15df284dfde4a142f68aa8d412023195cee66883e6290ffe703f4ea5963bf212713cee46b107c09182b5edcd955adac418bf4918e2889af48e1099d513830cec85c26ac1e158b52620e33ba8692f893efbb2f958b4424":95:24:MBEDTLS_MPI_MOD_EXT_REP_BE:0:MBEDTLS_ERR_MPI_BUFFER_TOO_SMALL

Test mbedtls_mpi_mod_raw_io #19 LE (buffer too small, input limb-aligned)
mpi_mod_raw_io:"0941379d00fed1491fe15df284dfde4a142f68aa8d412023195cee66883e6290ffe703f4ea5963bf212713cee46b107c09182b5edcd955adac418bf4918e2889af48e1099d513830cec85c26ac1e158b52620e33ba8692f893efbb2f958b4424":95:24:MBEDTLS_MPI_MOD_EXT_REP_LE:0:MBEDTLS_ERR_MPI_BUFFER_TOO_SMALL

Test mbedtls_mpi_mod_raw_io #20 BE (buffer too small, input unaligned)
mpi_mod_raw_io:"0941379d00fed1491fe15df284dfde4a142f68aa8d412023195cee66883e6290ffe703f4ea5963bf212713cee46b107c09182b5edcd955adac418bf4918e2889af48e1099d513830cec85c26ac1e158b52620e33ba8692f893efbb2f958b":93:24:MBEDTLS_MPI_MOD_EXT_REP_BE:0:MBEDTLS_ERR_MPI_BUFFER_TOO_SMALL

Test mbedtls_mpi_mod_raw_io #20 LE (buffer too small, input unaligned)
mpi_mod_raw_io:"0941379d00fed1491fe15df284dfde4a142f68aa8d412023195cee66883e6290ffe703f4ea5963bf212713cee46b107c09182b5edcd955adac418bf4918e2889af48e1099d513830cec85c26ac1e158b52620e33ba8692f893efbb2f958b":93:24:MBEDTLS_MPI_MOD_EXT_REP_LE:0:MBEDTLS_ERR_MPI_BUFFER_TOO_SMALL

Test mbedtls_mpi_mod_raw_io #21 BE (modulus is equal to input)
mpi_mod_raw_io:"FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF":1024:256:MBEDTLS_MPI_MOD_EXT_REP_BE:MBEDTLS_ERR_MPI_BAD_INPUT_DATA:0

Test mbedtls_mpi_mod_raw_io #21 LE (modulus is equal to input)
mpi_mod_raw_io:"FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF":1024:256:MBEDTLS_MPI_MOD_EXT_REP_LE:MBEDTLS_ERR_MPI_BAD_INPUT_DATA:0

Test mbedtls_mpi_mod_raw_io #22 (reading with invalid endianness)
mpi_mod_raw_io:"":1:1:MBEDTLS_MPI_MOD_EXT_REP_INVALID:MBEDTLS_ERR_MPI_BAD_INPUT_DATA:0

Test mbedtls_mpi_mod_raw_io #22 (writing with invalid endianness)
mpi_mod_raw_io:"":1:1:MBEDTLS_MPI_MOD_EXT_REP_INVALID:0:MBEDTLS_ERR_MPI_BAD_INPUT_DATA

mbedtls_mpi_mod_raw_cond_assign: 1 limb
mpi_mod_raw_cond_assign:"FFFFFFFF":"11111111":4

mbedtls_mpi_mod_raw_cond_assign: more limbs #1
mpi_mod_raw_cond_assign:"00000000FFFFFFFF55555555AAAAAAAA":"0123456789ABCDEF0123456789ABCDEF":16

mbedtls_mpi_mod_raw_cond_assign: more limbs #2
mpi_mod_raw_cond_assign:"11111111EEEEEEEE77777777CCCCCCCC":"FEDCBA9876543210FEDCBA9876543210":16

mbedtls_mpi_mod_raw_cond_assign: more limbs #3
mpi_mod_raw_cond_assign:"562D2B7E83BDC6FF783CEC0D6F46EAE7":"4C314E3B5CEB009C25F3300D5ECF670A":16

mbedtls_mpi_mod_raw_cond_assign: copy 256 bytes of limbs
mpi_mod_raw_cond_assign:"00000000111111112222222233333333444444445555555566666666777777778888888899999999AAAAAAAABBBBBBBBCCCCCCCCDDDDDDDDEEEEEEEEFFFFFFFF00000000111111112222222233333333444444445555555566666666777777778888888899999999AAAAAAAABBBBBBBBCCCCCCCCDDDDDDDDEEEEEEEEFFFFFFFF00000000111111112222222233333333444444445555555566666666777777778888888899999999AAAAAAAABBBBBBBBCCCCCCCCDDDDDDDDEEEEEEEEFFFFFFFF00000000111111112222222233333333444444445555555566666666777777778888888899999999AAAAAAAABBBBBBBBCCCCCCCCDDDDDDDDEEEEEEEEFFFFFFFF":"38AAE958A11CCE9F4B4D0EDF64D31DF6FEA451D56B3BAB3DB796739B96C5C9C619DAE2AD5394D41FC3746A4F35BA186F02D908E900A2BA5AC95D494378A6A6FC619801DA3F3B4C5BAAA49B16DD3B2EF9AFB4B82449F90628E1438405D321ACA8476EA3E7124D46D5F25CD57143F2C6A022F7D6EBF7C726AEF56D97E3944EACD627A814D2F546F5A659DD316AE57584BC46F0D976D537E78AB09D2599BB0F073366C541CEF4B6D76FE72D000A01167FCE506B805B295C12BFCAA3B768D8E6AA50D939BDB519A94000B7B18B2D36AE888EDC33DC933E9612D112BB7A95D0748F175E69F00B9D8470C64E27B4FF39551B09FF046F864E9FC22A3E1F73570DF31214":256

mbedtls_mpi_mod_raw_cond_assign: copy half of the limbs
mpi_mod_raw_cond_assign:"00000000FFFFFFFF55555555AAAAAAAA":"FEDCBA9876543210FEDCBA9876543210":8

mbedtls_mpi_mod_raw_cond_swap: same value
mpi_mod_raw_cond_swap:"FFFFFFFF":"FFFFFFFF":4

mbedtls_mpi_mod_raw_cond_swap: 1 limb
mpi_mod_raw_cond_swap:"FFFFFFFF":"11111111":4

mbedtls_mpi_mod_raw_cond_swap: more limbs #1
mpi_mod_raw_cond_swap:"00000000FFFFFFFF55555555AAAAAAAA":"0123456789ABCDEF0123456789ABCDEF":16

mbedtls_mpi_mod_raw_cond_swap: more limbs #2
mpi_mod_raw_cond_swap:"11111111EEEEEEEE77777777CCCCCCCC":"FEDCBA9876543210FEDCBA9876543210":16

mbedtls_mpi_mod_raw_cond_swap: more limbs #3
mpi_mod_raw_cond_swap:"562D2B7E83BDC6FF783CEC0D6F46EAE7":"4C314E3B5CEB009C25F3300D5ECF670A":16

mbedtls_mpi_mod_raw_cond_swap: copy 256 bytes of limbs
mpi_mod_raw_cond_swap:"00000000111111112222222233333333444444445555555566666666777777778888888899999999AAAAAAAABBBBBBBBCCCCCCCCDDDDDDDDEEEEEEEEFFFFFFFF00000000111111112222222233333333444444445555555566666666777777778888888899999999AAAAAAAABBBBBBBBCCCCCCCCDDDDDDDDEEEEEEEEFFFFFFFF00000000111111112222222233333333444444445555555566666666777777778888888899999999AAAAAAAABBBBBBBBCCCCCCCCDDDDDDDDEEEEEEEEFFFFFFFF00000000111111112222222233333333444444445555555566666666777777778888888899999999AAAAAAAABBBBBBBBCCCCCCCCDDDDDDDDEEEEEEEEFFFFFFFF":"A6154F6146E5AE06C705EF228DB022B4C0C908D9FF11E83CB0836A37DD11D72B5AE0D053B6D0B376B015E3B09DCC04644F12CBCBD0407300DEDA1D817E99A030BA128A57494F6C6BC650A840B70B3A1CDF8A75A387184C93DEF02DD538C60E8764AD2E4600952E09D24DC5524AB34070D25D204174D23B2BFDACBC67F8973C47FAF01CE9810130934AB6A94C5E6D223EDEAE904FD18368231AC3113C2CD73E879124CE21FC379C695C41D6861F5FB957570C3E6EF61F4FBA5D9945FB42931FC8E68370177DB313BEB7AF79B1F0F872368946FF35BD059E60677342EB3A0431714044AD508CD244B7CE200C5DC75B3BC7846A4CF0436C9288F2153124C9283171":256

mbedtls_mpi_mod_raw_cond_swap: copy half of the limbs
mpi_mod_raw_cond_swap:"00000000FFFFFFFF55555555AAAAAAAA":"FEDCBA9876543210FEDCBA9876543210":8

