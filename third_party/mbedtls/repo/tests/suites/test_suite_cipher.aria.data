Aria CBC Decrypt empty buffer
depends_on:MBEDTLS_ARIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
dec_empty_buf:MBEDTLS_CIPHER_ARIA_128_CBC:0:0

ARIA-128 CCM*-NO-TAG - Encrypt and decrypt 0 bytes
depends_on:MBEDTLS_ARIA_C:MBEDTLS_CCM_C
enc_dec_buf:MBEDTLS_CIPHER_ARIA_128_CCM_STAR_NO_TAG:"ARIA-128-CCM*-NO-TAG":128:0:-1

ARIA-128 CCM*-NO-TAG - Encrypt and decrypt 1 byte
depends_on:MBEDTLS_ARIA_C:MBEDTLS_CCM_C
enc_dec_buf:MBEDTLS_CIPHER_ARIA_128_CCM_STAR_NO_TAG:"ARIA-128-CCM*-NO-TAG":128:1:-1

ARIA-128 CCM*-NO-TAG - Encrypt and decrypt 2 bytes
depends_on:MBEDTLS_ARIA_C:MBEDTLS_CCM_C
enc_dec_buf:MBEDTLS_CIPHER_ARIA_128_CCM_STAR_NO_TAG:"ARIA-128-CCM*-NO-TAG":128:2:-1

ARIA-128 CCM*-NO-TAG - Encrypt and decrypt 7 bytes
depends_on:MBEDTLS_ARIA_C:MBEDTLS_CCM_C
enc_dec_buf:MBEDTLS_CIPHER_ARIA_128_CCM_STAR_NO_TAG:"ARIA-128-CCM*-NO-TAG":128:7:-1

ARIA-128 CCM*-NO-TAG - Encrypt and decrypt 8 bytes
depends_on:MBEDTLS_ARIA_C:MBEDTLS_CCM_C
enc_dec_buf:MBEDTLS_CIPHER_ARIA_128_CCM_STAR_NO_TAG:"ARIA-128-CCM*-NO-TAG":128:8:-1

ARIA-128 CCM*-NO-TAG - Encrypt and decrypt 9 bytes
depends_on:MBEDTLS_ARIA_C:MBEDTLS_CCM_C
enc_dec_buf:MBEDTLS_CIPHER_ARIA_128_CCM_STAR_NO_TAG:"ARIA-128-CCM*-NO-TAG":128:9:-1

ARIA-128 CCM*-NO-TAG - Encrypt and decrypt 15 bytes
depends_on:MBEDTLS_ARIA_C:MBEDTLS_CCM_C
enc_dec_buf:MBEDTLS_CIPHER_ARIA_128_CCM_STAR_NO_TAG:"ARIA-128-CCM*-NO-TAG":128:15:-1

ARIA-128 CCM*-NO-TAG - Encrypt and decrypt 16 bytes
depends_on:MBEDTLS_ARIA_C:MBEDTLS_CCM_C
enc_dec_buf:MBEDTLS_CIPHER_ARIA_128_CCM_STAR_NO_TAG:"ARIA-128-CCM*-NO-TAG":128:16:-1

ARIA-128 CCM*-NO-TAG - Encrypt and decrypt 17 bytes
depends_on:MBEDTLS_ARIA_C:MBEDTLS_CCM_C
enc_dec_buf:MBEDTLS_CIPHER_ARIA_128_CCM_STAR_NO_TAG:"ARIA-128-CCM*-NO-TAG":128:17:-1

ARIA-128 CCM*-NO-TAG - Encrypt and decrypt 31 bytes
depends_on:MBEDTLS_ARIA_C:MBEDTLS_CCM_C
enc_dec_buf:MBEDTLS_CIPHER_ARIA_128_CCM_STAR_NO_TAG:"ARIA-128-CCM*-NO-TAG":128:31:-1

ARIA-128 CCM*-NO-TAG - Encrypt and decrypt 32 bytes
depends_on:MBEDTLS_ARIA_C:MBEDTLS_CCM_C
enc_dec_buf:MBEDTLS_CIPHER_ARIA_128_CCM_STAR_NO_TAG:"ARIA-128-CCM*-NO-TAG":128:32:-1

ARIA-128 CCM*-NO-TAG - Encrypt and decrypt 33 bytes
depends_on:MBEDTLS_ARIA_C:MBEDTLS_CCM_C
enc_dec_buf:MBEDTLS_CIPHER_ARIA_128_CCM_STAR_NO_TAG:"ARIA-128-CCM*-NO-TAG":128:33:-1

ARIA-128 CCM*-NO-TAG - Encrypt and decrypt 47 bytes
depends_on:MBEDTLS_ARIA_C:MBEDTLS_CCM_C
enc_dec_buf:MBEDTLS_CIPHER_ARIA_128_CCM_STAR_NO_TAG:"ARIA-128-CCM*-NO-TAG":128:47:-1

ARIA-128 CCM*-NO-TAG - Encrypt and decrypt 48 bytes
depends_on:MBEDTLS_ARIA_C:MBEDTLS_CCM_C
enc_dec_buf:MBEDTLS_CIPHER_ARIA_128_CCM_STAR_NO_TAG:"ARIA-128-CCM*-NO-TAG":128:48:-1

ARIA-128 CCM*-NO-TAG - Encrypt and decrypt 49 bytes
depends_on:MBEDTLS_ARIA_C:MBEDTLS_CCM_C
enc_dec_buf:MBEDTLS_CIPHER_ARIA_128_CCM_STAR_NO_TAG:"ARIA-128-CCM*-NO-TAG":128:49:-1

ARIA-128 CCM*-NO-TAG - Encrypt and decrypt 0 bytes in multiple parts
depends_on:MBEDTLS_ARIA_C:MBEDTLS_CCM_C
enc_dec_buf_multipart:MBEDTLS_CIPHER_ARIA_128_CCM_STAR_NO_TAG:128:0:0:-1:0:0:0:0

ARIA-128 CCM*-NO-TAG - Encrypt and decrypt 1 bytes in multiple parts 1
depends_on:MBEDTLS_ARIA_C:MBEDTLS_CCM_C
enc_dec_buf_multipart:MBEDTLS_CIPHER_ARIA_128_CCM_STAR_NO_TAG:128:1:0:-1:1:0:1:0

ARIA-128 CCM*-NO-TAG - Encrypt and decrypt 1 bytes in multiple parts 2
depends_on:MBEDTLS_ARIA_C:MBEDTLS_CCM_C
enc_dec_buf_multipart:MBEDTLS_CIPHER_ARIA_128_CCM_STAR_NO_TAG:128:0:1:-1:0:1:0:1

ARIA-128 CCM*-NO-TAG - Encrypt and decrypt 16 bytes in multiple parts 1
depends_on:MBEDTLS_ARIA_C:MBEDTLS_CCM_C
enc_dec_buf_multipart:MBEDTLS_CIPHER_ARIA_128_CCM_STAR_NO_TAG:128:16:0:-1:16:0:16:0

ARIA-128 CCM*-NO-TAG - Encrypt and decrypt 16 bytes in multiple parts 2
depends_on:MBEDTLS_ARIA_C:MBEDTLS_CCM_C
enc_dec_buf_multipart:MBEDTLS_CIPHER_ARIA_128_CCM_STAR_NO_TAG:128:0:16:-1:0:16:0:16

ARIA-128 CCM*-NO-TAG - Encrypt and decrypt 16 bytes in multiple parts 3
depends_on:MBEDTLS_ARIA_C:MBEDTLS_CCM_C
enc_dec_buf_multipart:MBEDTLS_CIPHER_ARIA_128_CCM_STAR_NO_TAG:128:1:15:-1:1:15:1:15

ARIA-128 CCM*-NO-TAG - Encrypt and decrypt 16 bytes in multiple parts 4
depends_on:MBEDTLS_ARIA_C:MBEDTLS_CCM_C
enc_dec_buf_multipart:MBEDTLS_CIPHER_ARIA_128_CCM_STAR_NO_TAG:128:15:1:-1:15:1:15:1

ARIA-128 CCM*-NO-TAG - Encrypt and decrypt 22 bytes in multiple parts 1
depends_on:MBEDTLS_ARIA_C:MBEDTLS_CCM_C
enc_dec_buf_multipart:MBEDTLS_CIPHER_ARIA_128_CCM_STAR_NO_TAG:128:15:7:-1:15:7:15:7

ARIA-128-CCM*-NO-TAG - Encrypt and decrypt 22 bytes in multiple parts 2
depends_on:MBEDTLS_ARIA_C:MBEDTLS_CCM_C
enc_dec_buf_multipart:MBEDTLS_CIPHER_ARIA_128_CCM_STAR_NO_TAG:128:16:6:-1:16:6:16:6

ARIA-128-CCM*-NO-TAG - Encrypt and decrypt 23 bytes in multiple parts 1
depends_on:MBEDTLS_ARIA_C:MBEDTLS_CCM_C
enc_dec_buf_multipart:MBEDTLS_CIPHER_ARIA_128_CCM_STAR_NO_TAG:128:17:6:-1:17:6:17:6

ARIA-128-CCM*-NO-TAG - Encrypt and decrypt 32 bytes in multiple parts 1
depends_on:MBEDTLS_ARIA_C:MBEDTLS_CCM_C
enc_dec_buf_multipart:MBEDTLS_CIPHER_ARIA_128_CCM_STAR_NO_TAG:128:16:16:-1:16:16:16:16

ARIA-192-CCM*-NO-TAG - Encrypt and decrypt 0 bytes
depends_on:MBEDTLS_ARIA_C:MBEDTLS_CCM_C
enc_dec_buf:MBEDTLS_CIPHER_ARIA_192_CCM_STAR_NO_TAG:"ARIA-192-CCM*-NO-TAG":192:0:-1

ARIA-192-CCM*-NO-TAG - Encrypt and decrypt 1 byte
depends_on:MBEDTLS_ARIA_C:MBEDTLS_CCM_C
enc_dec_buf:MBEDTLS_CIPHER_ARIA_192_CCM_STAR_NO_TAG:"ARIA-192-CCM*-NO-TAG":192:1:-1

ARIA-192-CCM*-NO-TAG - Encrypt and decrypt 2 bytes
depends_on:MBEDTLS_ARIA_C:MBEDTLS_CCM_C
enc_dec_buf:MBEDTLS_CIPHER_ARIA_192_CCM_STAR_NO_TAG:"ARIA-192-CCM*-NO-TAG":192:2:-1

ARIA-192-CCM*-NO-TAG - Encrypt and decrypt 7 bytes
depends_on:MBEDTLS_ARIA_C:MBEDTLS_CCM_C
enc_dec_buf:MBEDTLS_CIPHER_ARIA_192_CCM_STAR_NO_TAG:"ARIA-192-CCM*-NO-TAG":192:7:-1

ARIA-192-CCM*-NO-TAG - Encrypt and decrypt 8 bytes
depends_on:MBEDTLS_ARIA_C:MBEDTLS_CCM_C
enc_dec_buf:MBEDTLS_CIPHER_ARIA_192_CCM_STAR_NO_TAG:"ARIA-192-CCM*-NO-TAG":192:8:-1

ARIA-192-CCM*-NO-TAG - Encrypt and decrypt 9 bytes
depends_on:MBEDTLS_ARIA_C:MBEDTLS_CCM_C
enc_dec_buf:MBEDTLS_CIPHER_ARIA_192_CCM_STAR_NO_TAG:"ARIA-192-CCM*-NO-TAG":192:9:-1

ARIA-192-CCM*-NO-TAG - Encrypt and decrypt 15 bytes
depends_on:MBEDTLS_ARIA_C:MBEDTLS_CCM_C
enc_dec_buf:MBEDTLS_CIPHER_ARIA_192_CCM_STAR_NO_TAG:"ARIA-192-CCM*-NO-TAG":192:15:-1

ARIA-192-CCM*-NO-TAG - Encrypt and decrypt 16 bytes
depends_on:MBEDTLS_ARIA_C:MBEDTLS_CCM_C
enc_dec_buf:MBEDTLS_CIPHER_ARIA_192_CCM_STAR_NO_TAG:"ARIA-192-CCM*-NO-TAG":192:16:-1

ARIA-192-CCM*-NO-TAG - Encrypt and decrypt 17 bytes
depends_on:MBEDTLS_ARIA_C:MBEDTLS_CCM_C
enc_dec_buf:MBEDTLS_CIPHER_ARIA_192_CCM_STAR_NO_TAG:"ARIA-192-CCM*-NO-TAG":192:17:-1

ARIA-192-CCM*-NO-TAG - Encrypt and decrypt 31 bytes
depends_on:MBEDTLS_ARIA_C:MBEDTLS_CCM_C
enc_dec_buf:MBEDTLS_CIPHER_ARIA_192_CCM_STAR_NO_TAG:"ARIA-192-CCM*-NO-TAG":192:31:-1

ARIA-192-CCM*-NO-TAG - Encrypt and decrypt 32 bytes
depends_on:MBEDTLS_ARIA_C:MBEDTLS_CCM_C
enc_dec_buf:MBEDTLS_CIPHER_ARIA_192_CCM_STAR_NO_TAG:"ARIA-192-CCM*-NO-TAG":192:32:-1

ARIA-192-CCM*-NO-TAG - Encrypt and decrypt 33 bytes
depends_on:MBEDTLS_ARIA_C:MBEDTLS_CCM_C
enc_dec_buf:MBEDTLS_CIPHER_ARIA_192_CCM_STAR_NO_TAG:"ARIA-192-CCM*-NO-TAG":192:33:-1

ARIA-192-CCM*-NO-TAG - Encrypt and decrypt 47 bytes
depends_on:MBEDTLS_ARIA_C:MBEDTLS_CCM_C
enc_dec_buf:MBEDTLS_CIPHER_ARIA_192_CCM_STAR_NO_TAG:"ARIA-192-CCM*-NO-TAG":192:47:-1

ARIA-192-CCM*-NO-TAG - Encrypt and decrypt 48 bytes
depends_on:MBEDTLS_ARIA_C:MBEDTLS_CCM_C
enc_dec_buf:MBEDTLS_CIPHER_ARIA_192_CCM_STAR_NO_TAG:"ARIA-192-CCM*-NO-TAG":192:48:-1

ARIA-192-CCM*-NO-TAG - Encrypt and decrypt 49 bytes
depends_on:MBEDTLS_ARIA_C:MBEDTLS_CCM_C
enc_dec_buf:MBEDTLS_CIPHER_ARIA_192_CCM_STAR_NO_TAG:"ARIA-192-CCM*-NO-TAG":192:49:-1

ARIA-192-CCM*-NO-TAG - Encrypt and decrypt 0 bytes in multiple parts
depends_on:MBEDTLS_ARIA_C:MBEDTLS_CCM_C
enc_dec_buf_multipart:MBEDTLS_CIPHER_ARIA_192_CCM_STAR_NO_TAG:192:0:0:-1:0:0:0:0

ARIA-192-CCM*-NO-TAG - Encrypt and decrypt 1 bytes in multiple parts 1
depends_on:MBEDTLS_ARIA_C:MBEDTLS_CCM_C
enc_dec_buf_multipart:MBEDTLS_CIPHER_ARIA_192_CCM_STAR_NO_TAG:192:1:0:-1:1:0:1:0

ARIA-192-CCM*-NO-TAG - Encrypt and decrypt 1 bytes in multiple parts 2
depends_on:MBEDTLS_ARIA_C:MBEDTLS_CCM_C
enc_dec_buf_multipart:MBEDTLS_CIPHER_ARIA_192_CCM_STAR_NO_TAG:192:0:1:-1:0:1:0:1

ARIA-192-CCM*-NO-TAG - Encrypt and decrypt 16 bytes in multiple parts 1
depends_on:MBEDTLS_ARIA_C:MBEDTLS_CCM_C
enc_dec_buf_multipart:MBEDTLS_CIPHER_ARIA_192_CCM_STAR_NO_TAG:192:16:0:-1:16:0:16:0

ARIA-192-CCM*-NO-TAG - Encrypt and decrypt 16 bytes in multiple parts 2
depends_on:MBEDTLS_ARIA_C:MBEDTLS_CCM_C
enc_dec_buf_multipart:MBEDTLS_CIPHER_ARIA_192_CCM_STAR_NO_TAG:192:0:16:-1:0:16:0:16

ARIA-192-CCM*-NO-TAG - Encrypt and decrypt 16 bytes in multiple parts 3
depends_on:MBEDTLS_ARIA_C:MBEDTLS_CCM_C
enc_dec_buf_multipart:MBEDTLS_CIPHER_ARIA_192_CCM_STAR_NO_TAG:192:1:15:-1:1:15:1:15

ARIA-192-CCM*-NO-TAG - Encrypt and decrypt 16 bytes in multiple parts 4
depends_on:MBEDTLS_ARIA_C:MBEDTLS_CCM_C
enc_dec_buf_multipart:MBEDTLS_CIPHER_ARIA_192_CCM_STAR_NO_TAG:192:15:1:-1:15:1:15:1

ARIA-192-CCM*-NO-TAG - Encrypt and decrypt 22 bytes in multiple parts 1
depends_on:MBEDTLS_ARIA_C:MBEDTLS_CCM_C
enc_dec_buf_multipart:MBEDTLS_CIPHER_ARIA_192_CCM_STAR_NO_TAG:192:15:7:-1:15:7:15:7

ARIA-192-CCM*-NO-TAG - Encrypt and decrypt 22 bytes in multiple parts 2
depends_on:MBEDTLS_ARIA_C:MBEDTLS_CCM_C
enc_dec_buf_multipart:MBEDTLS_CIPHER_ARIA_192_CCM_STAR_NO_TAG:192:16:6:-1:16:6:16:6

ARIA-192-CCM*-NO-TAG - Encrypt and decrypt 23 bytes in multiple parts 1
depends_on:MBEDTLS_ARIA_C:MBEDTLS_CCM_C
enc_dec_buf_multipart:MBEDTLS_CIPHER_ARIA_192_CCM_STAR_NO_TAG:192:17:6:-1:17:6:17:6

ARIA-192-CCM*-NO-TAG - Encrypt and decrypt 32 bytes in multiple parts 1
depends_on:MBEDTLS_ARIA_C:MBEDTLS_CCM_C
enc_dec_buf_multipart:MBEDTLS_CIPHER_ARIA_192_CCM_STAR_NO_TAG:192:16:16:-1:16:16:16:16

ARIA-256-CCM*-NO-TAG - Encrypt and decrypt 0 bytes
depends_on:MBEDTLS_ARIA_C:MBEDTLS_CCM_C
enc_dec_buf:MBEDTLS_CIPHER_ARIA_256_CCM_STAR_NO_TAG:"ARIA-256-CCM*-NO-TAG":256:0:-1

ARIA-256-CCM*-NO-TAG - Encrypt and decrypt 1 byte
depends_on:MBEDTLS_ARIA_C:MBEDTLS_CCM_C
enc_dec_buf:MBEDTLS_CIPHER_ARIA_256_CCM_STAR_NO_TAG:"ARIA-256-CCM*-NO-TAG":256:1:-1

ARIA-256-CCM*-NO-TAG - Encrypt and decrypt 2 bytes
depends_on:MBEDTLS_ARIA_C:MBEDTLS_CCM_C
enc_dec_buf:MBEDTLS_CIPHER_ARIA_256_CCM_STAR_NO_TAG:"ARIA-256-CCM*-NO-TAG":256:2:-1

ARIA-256-CCM*-NO-TAG - Encrypt and decrypt 7 bytes
depends_on:MBEDTLS_ARIA_C:MBEDTLS_CCM_C
enc_dec_buf:MBEDTLS_CIPHER_ARIA_256_CCM_STAR_NO_TAG:"ARIA-256-CCM*-NO-TAG":256:7:-1

ARIA-256-CCM*-NO-TAG - Encrypt and decrypt 8 bytes
depends_on:MBEDTLS_ARIA_C:MBEDTLS_CCM_C
enc_dec_buf:MBEDTLS_CIPHER_ARIA_256_CCM_STAR_NO_TAG:"ARIA-256-CCM*-NO-TAG":256:8:-1

ARIA-256-CCM*-NO-TAG - Encrypt and decrypt 9 bytes
depends_on:MBEDTLS_ARIA_C:MBEDTLS_CCM_C
enc_dec_buf:MBEDTLS_CIPHER_ARIA_256_CCM_STAR_NO_TAG:"ARIA-256-CCM*-NO-TAG":256:9:-1

ARIA-256-CCM*-NO-TAG - Encrypt and decrypt 15 bytes
depends_on:MBEDTLS_ARIA_C:MBEDTLS_CCM_C
enc_dec_buf:MBEDTLS_CIPHER_ARIA_256_CCM_STAR_NO_TAG:"ARIA-256-CCM*-NO-TAG":256:15:-1

ARIA-256-CCM*-NO-TAG - Encrypt and decrypt 16 bytes
depends_on:MBEDTLS_ARIA_C:MBEDTLS_CCM_C
enc_dec_buf:MBEDTLS_CIPHER_ARIA_256_CCM_STAR_NO_TAG:"ARIA-256-CCM*-NO-TAG":256:16:-1

ARIA-256-CCM*-NO-TAG - Encrypt and decrypt 17 bytes
depends_on:MBEDTLS_ARIA_C:MBEDTLS_CCM_C
enc_dec_buf:MBEDTLS_CIPHER_ARIA_256_CCM_STAR_NO_TAG:"ARIA-256-CCM*-NO-TAG":256:17:-1

ARIA-256-CCM*-NO-TAG - Encrypt and decrypt 31 bytes
depends_on:MBEDTLS_ARIA_C:MBEDTLS_CCM_C
enc_dec_buf:MBEDTLS_CIPHER_ARIA_256_CCM_STAR_NO_TAG:"ARIA-256-CCM*-NO-TAG":256:31:-1

ARIA-256-CCM*-NO-TAG - Encrypt and decrypt 32 bytes
depends_on:MBEDTLS_ARIA_C:MBEDTLS_CCM_C
enc_dec_buf:MBEDTLS_CIPHER_ARIA_256_CCM_STAR_NO_TAG:"ARIA-256-CCM*-NO-TAG":256:32:-1

ARIA-256-CCM*-NO-TAG - Encrypt and decrypt 33 bytes
depends_on:MBEDTLS_ARIA_C:MBEDTLS_CCM_C
enc_dec_buf:MBEDTLS_CIPHER_ARIA_256_CCM_STAR_NO_TAG:"ARIA-256-CCM*-NO-TAG":256:33:-1

ARIA-256-CCM*-NO-TAG - Encrypt and decrypt 47 bytes
depends_on:MBEDTLS_ARIA_C:MBEDTLS_CCM_C
enc_dec_buf:MBEDTLS_CIPHER_ARIA_256_CCM_STAR_NO_TAG:"ARIA-256-CCM*-NO-TAG":256:47:-1

ARIA-256-CCM*-NO-TAG - Encrypt and decrypt 48 bytes
depends_on:MBEDTLS_ARIA_C:MBEDTLS_CCM_C
enc_dec_buf:MBEDTLS_CIPHER_ARIA_256_CCM_STAR_NO_TAG:"ARIA-256-CCM*-NO-TAG":256:48:-1

ARIA-256-CCM*-NO-TAG - Encrypt and decrypt 49 bytes
depends_on:MBEDTLS_ARIA_C:MBEDTLS_CCM_C
enc_dec_buf:MBEDTLS_CIPHER_ARIA_256_CCM_STAR_NO_TAG:"ARIA-256-CCM*-NO-TAG":256:49:-1

ARIA-256-CCM*-NO-TAG - Encrypt and decrypt 0 bytes in multiple parts
depends_on:MBEDTLS_ARIA_C:MBEDTLS_CCM_C
enc_dec_buf_multipart:MBEDTLS_CIPHER_ARIA_256_CCM_STAR_NO_TAG:256:0:0:-1:0:0:0:0

ARIA-256-CCM*-NO-TAG - Encrypt and decrypt 1 bytes in multiple parts 1
depends_on:MBEDTLS_ARIA_C:MBEDTLS_CCM_C
enc_dec_buf_multipart:MBEDTLS_CIPHER_ARIA_256_CCM_STAR_NO_TAG:256:1:0:-1:1:0:1:0

ARIA-256-CCM*-NO-TAG - Encrypt and decrypt 1 bytes in multiple parts 2
depends_on:MBEDTLS_ARIA_C:MBEDTLS_CCM_C
enc_dec_buf_multipart:MBEDTLS_CIPHER_ARIA_256_CCM_STAR_NO_TAG:256:0:1:-1:0:1:0:1

ARIA-256-CCM*-NO-TAG - Encrypt and decrypt 16 bytes in multiple parts 1
depends_on:MBEDTLS_ARIA_C:MBEDTLS_CCM_C
enc_dec_buf_multipart:MBEDTLS_CIPHER_ARIA_256_CCM_STAR_NO_TAG:256:16:0:-1:16:0:16:0

ARIA-256-CCM*-NO-TAG - Encrypt and decrypt 16 bytes in multiple parts 2
depends_on:MBEDTLS_ARIA_C:MBEDTLS_CCM_C
enc_dec_buf_multipart:MBEDTLS_CIPHER_ARIA_256_CCM_STAR_NO_TAG:256:0:16:-1:0:16:0:16

ARIA-256-CCM*-NO-TAG - Encrypt and decrypt 16 bytes in multiple parts 3
depends_on:MBEDTLS_ARIA_C:MBEDTLS_CCM_C
enc_dec_buf_multipart:MBEDTLS_CIPHER_ARIA_256_CCM_STAR_NO_TAG:256:1:15:-1:1:15:1:15

ARIA-256-CCM*-NO-TAG - Encrypt and decrypt 16 bytes in multiple parts 4
depends_on:MBEDTLS_ARIA_C:MBEDTLS_CCM_C
enc_dec_buf_multipart:MBEDTLS_CIPHER_ARIA_256_CCM_STAR_NO_TAG:256:15:1:-1:15:1:15:1

ARIA-256-CCM*-NO-TAG - Encrypt and decrypt 22 bytes in multiple parts 1
depends_on:MBEDTLS_ARIA_C:MBEDTLS_CCM_C
enc_dec_buf_multipart:MBEDTLS_CIPHER_ARIA_256_CCM_STAR_NO_TAG:256:15:7:-1:15:7:15:7

ARIA-256-CCM*-NO-TAG - Encrypt and decrypt 22 bytes in multiple parts 2
depends_on:MBEDTLS_ARIA_C:MBEDTLS_CCM_C
enc_dec_buf_multipart:MBEDTLS_CIPHER_ARIA_256_CCM_STAR_NO_TAG:256:16:6:-1:16:6:16:6

ARIA-256-CCM*-NO-TAG - Encrypt and decrypt 23 bytes in multiple parts 1
depends_on:MBEDTLS_ARIA_C:MBEDTLS_CCM_C
enc_dec_buf_multipart:MBEDTLS_CIPHER_ARIA_256_CCM_STAR_NO_TAG:256:17:6:-1:17:6:17:6

ARIA-256-CCM*-NO-TAG - Encrypt and decrypt 32 bytes in multiple parts 1
depends_on:MBEDTLS_ARIA_C:MBEDTLS_CCM_C
enc_dec_buf_multipart:MBEDTLS_CIPHER_ARIA_256_CCM_STAR_NO_TAG:256:16:16:-1:16:16:16:16

ARIA-128-CCM*-NO-TAG crypt Encrypt
depends_on:MBEDTLS_ARIA_C:MBEDTLS_CCM_C
test_vec_crypt:MBEDTLS_CIPHER_ARIA_128_CCM_STAR_NO_TAG:MBEDTLS_ENCRYPT:"C0C1C2C3C4C5C6C7C8C9CACBCCCDCECF":"00000003020100A0A1A2A3A4A5":"08090A0B0C0D0E0F101112131415161718191A1B1C1D1E":"6781f39fdf8d1c44165fc40ee2fb11f1d6e2ddc8c6512b":0:0

ARIA-128-CCM*-NO-TAG crypt Decrypt
depends_on:MBEDTLS_ARIA_C:MBEDTLS_CCM_C
test_vec_crypt:MBEDTLS_CIPHER_ARIA_128_CCM_STAR_NO_TAG:MBEDTLS_DECRYPT:"C0C1C2C3C4C5C6C7C8C9CACBCCCDCECF":"00000003020100A0A1A2A3A4A5":"08090A0B0C0D0E0F101112131415161718191A1B1C1D1E":"6781f39fdf8d1c44165fc40ee2fb11f1d6e2ddc8c6512b":0:0

ARIA-192-CCM*-NO-TAG crypt Encrypt
depends_on:MBEDTLS_ARIA_C:MBEDTLS_CCM_C
test_vec_crypt:MBEDTLS_CIPHER_ARIA_192_CCM_STAR_NO_TAG:MBEDTLS_ENCRYPT:"C0C1C2C3C4C5C6C7C8C9CACBCCCDCECFC0C1C2C3C4C5C6C7":"00000003020100A0A1A2A3A4A5":"08090A0B0C0D0E0F101112131415161718191A1B1C1D1E":"993df86214d98ae70582c784903702e349dd64ece488c2":0:0

ARIA-192-CCM*-NO-TAG crypt Decrypt
depends_on:MBEDTLS_ARIA_C:MBEDTLS_CCM_C
test_vec_crypt:MBEDTLS_CIPHER_ARIA_192_CCM_STAR_NO_TAG:MBEDTLS_DECRYPT:"C0C1C2C3C4C5C6C7C8C9CACBCCCDCECFC0C1C2C3C4C5C6C7":"00000003020100A0A1A2A3A4A5":"08090A0B0C0D0E0F101112131415161718191A1B1C1D1E":"993df86214d98ae70582c784903702e349dd64ece488c2":0:0

ARIA-256-CCM*-NO-TAG crypt Encrypt
depends_on:MBEDTLS_ARIA_C:MBEDTLS_CCM_C
test_vec_crypt:MBEDTLS_CIPHER_ARIA_256_CCM_STAR_NO_TAG:MBEDTLS_ENCRYPT:"C0C1C2C3C4C5C6C7C8C9CACBCCCDCECFC0C1C2C3C4C5C6C7C8C9CACBCCCDCECF":"00000003020100A0A1A2A3A4A5":"08090A0B0C0D0E0F101112131415161718191A1B1C1D1E":"5fdd984a6aa77c1d9a204c08f28172c4b4528bee27c41f":0:0

ARIA-256-CCM*-NO-TAG crypt Decrypt
depends_on:MBEDTLS_ARIA_C:MBEDTLS_CCM_C
test_vec_crypt:MBEDTLS_CIPHER_ARIA_256_CCM_STAR_NO_TAG:MBEDTLS_DECRYPT:"C0C1C2C3C4C5C6C7C8C9CACBCCCDCECFC0C1C2C3C4C5C6C7C8C9CACBCCCDCECF":"00000003020100A0A1A2A3A4A5":"08090A0B0C0D0E0F101112131415161718191A1B1C1D1E":"5fdd984a6aa77c1d9a204c08f28172c4b4528bee27c41f":0:0

Check set padding - 128 bit key
depends_on:MBEDTLS_ARIA_C:MBEDTLS_CIPHER_MODE_CBC
check_set_padding:MBEDTLS_CIPHER_ARIA_128_CBC

Check set padding - 192 bit key
depends_on:MBEDTLS_ARIA_C:MBEDTLS_CIPHER_MODE_CBC
check_set_padding:MBEDTLS_CIPHER_ARIA_192_CBC

Check set padding - 256 bit key
depends_on:MBEDTLS_ARIA_C:MBEDTLS_CIPHER_MODE_CBC
check_set_padding:MBEDTLS_CIPHER_ARIA_256_CBC
