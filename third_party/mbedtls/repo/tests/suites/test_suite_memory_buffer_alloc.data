Memory buffer alloc self test
mbedtls_memory_buffer_alloc_self_test:

Memory buffer alloc - free in middle, alloc at end
memory_buffer_alloc_free_alloc:100:100:100:0:0:1:0:0:200:0

Memory buffer alloc - free in middle, realloc
memory_buffer_alloc_free_alloc:100:100:100:0:0:1:0:0:100:0

Memory buffer alloc - free in middle, merge, realloc
memory_buffer_alloc_free_alloc:100:100:100:100:0:1:1:0:201:0

Memory buffer alloc - free at end, merge, realloc
memory_buffer_alloc_free_alloc:100:64:100:100:0:0:0:1:200:0

Memory buffer alloc - Out of Memory test
memory_buffer_alloc_oom_test:

Memory buffer: heap too small (header verification should fail)
memory_buffer_heap_too_small:

Memory buffer: attempt to allocate SIZE_MAX
memory_buffer_underalloc:
