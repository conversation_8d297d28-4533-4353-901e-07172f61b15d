#
# This file is autogenerated by pip-compile with Python 3.9
# by the following command:
#
#    pip-compile requirements.in
#
alabaster==0.7.13
    # via sphinx
babel==2.12.1
    # via sphinx
breathe==4.35.0
    # via -r requirements.in
certifi==2022.12.7
    # via requests
charset-normalizer==3.1.0
    # via requests
click==8.1.3
    # via readthedocs-cli
docutils==0.17.1
    # via
    #   breathe
    #   sphinx
    #   sphinx-rtd-theme
idna==3.4
    # via requests
imagesize==1.4.1
    # via sphinx
importlib-metadata==6.0.0
    # via sphinx
jinja2==3.1.2
    # via sphinx
markdown-it-py==2.2.0
    # via rich
markupsafe==2.1.2
    # via jinja2
mdurl==0.1.2
    # via markdown-it-py
packaging==23.0
    # via sphinx
pygments==2.14.0
    # via
    #   rich
    #   sphinx
pyyaml==6.0
    # via readthedocs-cli
readthedocs-cli==4
    # via -r requirements.in
requests==2.28.2
    # via
    #   readthedocs-cli
    #   sphinx
rich==13.3.5
    # via readthedocs-cli
snowballstemmer==2.2.0
    # via sphinx
sphinx==4.5.0
    # via
    #   breathe
    #   sphinx-rtd-theme
sphinx-rtd-theme==1.2.0
    # via -r requirements.in
sphinxcontrib-applehelp==1.0.4
    # via sphinx
sphinxcontrib-devhelp==1.0.2
    # via sphinx
sphinxcontrib-htmlhelp==2.0.1
    # via sphinx
sphinxcontrib-jquery==2.0.0
    # via sphinx-rtd-theme
sphinxcontrib-jsmath==1.0.1
    # via sphinx
sphinxcontrib-qthelp==1.0.3
    # via sphinx
sphinxcontrib-serializinghtml==1.1.5
    # via sphinx
urllib3==1.26.15
    # via requests
zipp==3.15.0
    # via importlib-metadata

# The following packages are considered to be unsafe in a requirements file:
# setuptools
