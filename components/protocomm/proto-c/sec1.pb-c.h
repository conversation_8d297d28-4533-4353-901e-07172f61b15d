/* Generated by the protocol buffer compiler.  DO NOT EDIT! */
/* Generated from: sec1.proto */

#ifndef PROTOBUF_C_sec1_2eproto__INCLUDED
#define PROTOBUF_C_sec1_2eproto__INCLUDED

#include <protobuf-c/protobuf-c.h>

PROTOBUF_C__BEGIN_DECLS

#if PROTOBUF_C_VERSION_NUMBER < 1003000
# error This file was generated by a newer version of protoc-c which is incompatible with your libprotobuf-c headers. Please update your headers.
#elif 1004000 < PROTOBUF_C_MIN_COMPILER_VERSION
# error This file was generated by an older version of protoc-c which is incompatible with your libprotobuf-c headers. Please regenerate this file with a newer version of protoc-c.
#endif

#include "constants.pb-c.h"

typedef struct SessionCmd1 SessionCmd1;
typedef struct SessionResp1 SessionResp1;
typedef struct SessionCmd0 SessionCmd0;
typedef struct SessionResp0 SessionResp0;
typedef struct Sec1Payload Sec1Payload;


/* --- enums --- */

/*
 * A message must be of type Cmd0 / Cmd1 / Resp0 / Resp1 
 */
typedef enum _Sec1MsgType {
  SEC1_MSG_TYPE__Session_Command0 = 0,
  SEC1_MSG_TYPE__Session_Response0 = 1,
  SEC1_MSG_TYPE__Session_Command1 = 2,
  SEC1_MSG_TYPE__Session_Response1 = 3
    PROTOBUF_C__FORCE_ENUM_TO_BE_INT_SIZE(SEC1_MSG_TYPE)
} Sec1MsgType;

/* --- messages --- */

/*
 * Data structure of Session command1 packet 
 */
struct  SessionCmd1
{
  ProtobufCMessage base;
  ProtobufCBinaryData client_verify_data;
};
#define SESSION_CMD1__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&session_cmd1__descriptor) \
    , {0,NULL} }


/*
 * Data structure of Session response1 packet 
 */
struct  SessionResp1
{
  ProtobufCMessage base;
  Status status;
  ProtobufCBinaryData device_verify_data;
};
#define SESSION_RESP1__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&session_resp1__descriptor) \
    , STATUS__Success, {0,NULL} }


/*
 * Data structure of Session command0 packet 
 */
struct  SessionCmd0
{
  ProtobufCMessage base;
  ProtobufCBinaryData client_pubkey;
};
#define SESSION_CMD0__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&session_cmd0__descriptor) \
    , {0,NULL} }


/*
 * Data structure of Session response0 packet 
 */
struct  SessionResp0
{
  ProtobufCMessage base;
  Status status;
  ProtobufCBinaryData device_pubkey;
  ProtobufCBinaryData device_random;
};
#define SESSION_RESP0__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&session_resp0__descriptor) \
    , STATUS__Success, {0,NULL}, {0,NULL} }


typedef enum {
  SEC1_PAYLOAD__PAYLOAD__NOT_SET = 0,
  SEC1_PAYLOAD__PAYLOAD_SC0 = 20,
  SEC1_PAYLOAD__PAYLOAD_SR0 = 21,
  SEC1_PAYLOAD__PAYLOAD_SC1 = 22,
  SEC1_PAYLOAD__PAYLOAD_SR1 = 23
    PROTOBUF_C__FORCE_ENUM_TO_BE_INT_SIZE(SEC1_PAYLOAD__PAYLOAD__CASE)
} Sec1Payload__PayloadCase;

/*
 * Payload structure of session data 
 */
struct  Sec1Payload
{
  ProtobufCMessage base;
  /*
   *!< Type of message 
   */
  Sec1MsgType msg;
  Sec1Payload__PayloadCase payload_case;
  union {
    /*
     *!< Payload data interpreted as Cmd0 
     */
    SessionCmd0 *sc0;
    /*
     *!< Payload data interpreted as Resp0 
     */
    SessionResp0 *sr0;
    /*
     *!< Payload data interpreted as Cmd1 
     */
    SessionCmd1 *sc1;
    /*
     *!< Payload data interpreted as Resp1 
     */
    SessionResp1 *sr1;
  };
};
#define SEC1_PAYLOAD__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&sec1_payload__descriptor) \
    , SEC1_MSG_TYPE__Session_Command0, SEC1_PAYLOAD__PAYLOAD__NOT_SET, {0} }


/* SessionCmd1 methods */
void   session_cmd1__init
                     (SessionCmd1         *message);
size_t session_cmd1__get_packed_size
                     (const SessionCmd1   *message);
size_t session_cmd1__pack
                     (const SessionCmd1   *message,
                      uint8_t             *out);
size_t session_cmd1__pack_to_buffer
                     (const SessionCmd1   *message,
                      ProtobufCBuffer     *buffer);
SessionCmd1 *
       session_cmd1__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   session_cmd1__free_unpacked
                     (SessionCmd1 *message,
                      ProtobufCAllocator *allocator);
/* SessionResp1 methods */
void   session_resp1__init
                     (SessionResp1         *message);
size_t session_resp1__get_packed_size
                     (const SessionResp1   *message);
size_t session_resp1__pack
                     (const SessionResp1   *message,
                      uint8_t             *out);
size_t session_resp1__pack_to_buffer
                     (const SessionResp1   *message,
                      ProtobufCBuffer     *buffer);
SessionResp1 *
       session_resp1__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   session_resp1__free_unpacked
                     (SessionResp1 *message,
                      ProtobufCAllocator *allocator);
/* SessionCmd0 methods */
void   session_cmd0__init
                     (SessionCmd0         *message);
size_t session_cmd0__get_packed_size
                     (const SessionCmd0   *message);
size_t session_cmd0__pack
                     (const SessionCmd0   *message,
                      uint8_t             *out);
size_t session_cmd0__pack_to_buffer
                     (const SessionCmd0   *message,
                      ProtobufCBuffer     *buffer);
SessionCmd0 *
       session_cmd0__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   session_cmd0__free_unpacked
                     (SessionCmd0 *message,
                      ProtobufCAllocator *allocator);
/* SessionResp0 methods */
void   session_resp0__init
                     (SessionResp0         *message);
size_t session_resp0__get_packed_size
                     (const SessionResp0   *message);
size_t session_resp0__pack
                     (const SessionResp0   *message,
                      uint8_t             *out);
size_t session_resp0__pack_to_buffer
                     (const SessionResp0   *message,
                      ProtobufCBuffer     *buffer);
SessionResp0 *
       session_resp0__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   session_resp0__free_unpacked
                     (SessionResp0 *message,
                      ProtobufCAllocator *allocator);
/* Sec1Payload methods */
void   sec1_payload__init
                     (Sec1Payload         *message);
size_t sec1_payload__get_packed_size
                     (const Sec1Payload   *message);
size_t sec1_payload__pack
                     (const Sec1Payload   *message,
                      uint8_t             *out);
size_t sec1_payload__pack_to_buffer
                     (const Sec1Payload   *message,
                      ProtobufCBuffer     *buffer);
Sec1Payload *
       sec1_payload__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   sec1_payload__free_unpacked
                     (Sec1Payload *message,
                      ProtobufCAllocator *allocator);
/* --- per-message closures --- */

typedef void (*SessionCmd1_Closure)
                 (const SessionCmd1 *message,
                  void *closure_data);
typedef void (*SessionResp1_Closure)
                 (const SessionResp1 *message,
                  void *closure_data);
typedef void (*SessionCmd0_Closure)
                 (const SessionCmd0 *message,
                  void *closure_data);
typedef void (*SessionResp0_Closure)
                 (const SessionResp0 *message,
                  void *closure_data);
typedef void (*Sec1Payload_Closure)
                 (const Sec1Payload *message,
                  void *closure_data);

/* --- services --- */


/* --- descriptors --- */

extern const ProtobufCEnumDescriptor    sec1_msg_type__descriptor;
extern const ProtobufCMessageDescriptor session_cmd1__descriptor;
extern const ProtobufCMessageDescriptor session_resp1__descriptor;
extern const ProtobufCMessageDescriptor session_cmd0__descriptor;
extern const ProtobufCMessageDescriptor session_resp0__descriptor;
extern const ProtobufCMessageDescriptor sec1_payload__descriptor;

PROTOBUF_C__END_DECLS


#endif  /* PROTOBUF_C_sec1_2eproto__INCLUDED */
