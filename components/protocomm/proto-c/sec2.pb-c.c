/* Generated by the protocol buffer compiler.  DO NOT EDIT! */
/* Generated from: sec2.proto */

/* Do not generate deprecated warnings for self */
#ifndef PROTOBUF_C__NO_DEPRECATED
#define PROTOBUF_C__NO_DEPRECATED
#endif

#include "sec2.pb-c.h"
void   s2_session_cmd0__init
                     (S2SessionCmd0         *message)
{
  static const S2SessionCmd0 init_value = S2_SESSION_CMD0__INIT;
  *message = init_value;
}
size_t s2_session_cmd0__get_packed_size
                     (const S2SessionCmd0 *message)
{
  assert(message->base.descriptor == &s2_session_cmd0__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t s2_session_cmd0__pack
                     (const S2SessionCmd0 *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &s2_session_cmd0__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t s2_session_cmd0__pack_to_buffer
                     (const S2SessionCmd0 *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &s2_session_cmd0__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
S2SessionCmd0 *
       s2_session_cmd0__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (S2SessionCmd0 *)
     protobuf_c_message_unpack (&s2_session_cmd0__descriptor,
                                allocator, len, data);
}
void   s2_session_cmd0__free_unpacked
                     (S2SessionCmd0 *message,
                      ProtobufCAllocator *allocator)
{
  if(!message)
    return;
  assert(message->base.descriptor == &s2_session_cmd0__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   s2_session_resp0__init
                     (S2SessionResp0         *message)
{
  static const S2SessionResp0 init_value = S2_SESSION_RESP0__INIT;
  *message = init_value;
}
size_t s2_session_resp0__get_packed_size
                     (const S2SessionResp0 *message)
{
  assert(message->base.descriptor == &s2_session_resp0__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t s2_session_resp0__pack
                     (const S2SessionResp0 *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &s2_session_resp0__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t s2_session_resp0__pack_to_buffer
                     (const S2SessionResp0 *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &s2_session_resp0__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
S2SessionResp0 *
       s2_session_resp0__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (S2SessionResp0 *)
     protobuf_c_message_unpack (&s2_session_resp0__descriptor,
                                allocator, len, data);
}
void   s2_session_resp0__free_unpacked
                     (S2SessionResp0 *message,
                      ProtobufCAllocator *allocator)
{
  if(!message)
    return;
  assert(message->base.descriptor == &s2_session_resp0__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   s2_session_cmd1__init
                     (S2SessionCmd1         *message)
{
  static const S2SessionCmd1 init_value = S2_SESSION_CMD1__INIT;
  *message = init_value;
}
size_t s2_session_cmd1__get_packed_size
                     (const S2SessionCmd1 *message)
{
  assert(message->base.descriptor == &s2_session_cmd1__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t s2_session_cmd1__pack
                     (const S2SessionCmd1 *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &s2_session_cmd1__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t s2_session_cmd1__pack_to_buffer
                     (const S2SessionCmd1 *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &s2_session_cmd1__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
S2SessionCmd1 *
       s2_session_cmd1__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (S2SessionCmd1 *)
     protobuf_c_message_unpack (&s2_session_cmd1__descriptor,
                                allocator, len, data);
}
void   s2_session_cmd1__free_unpacked
                     (S2SessionCmd1 *message,
                      ProtobufCAllocator *allocator)
{
  if(!message)
    return;
  assert(message->base.descriptor == &s2_session_cmd1__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   s2_session_resp1__init
                     (S2SessionResp1         *message)
{
  static const S2SessionResp1 init_value = S2_SESSION_RESP1__INIT;
  *message = init_value;
}
size_t s2_session_resp1__get_packed_size
                     (const S2SessionResp1 *message)
{
  assert(message->base.descriptor == &s2_session_resp1__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t s2_session_resp1__pack
                     (const S2SessionResp1 *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &s2_session_resp1__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t s2_session_resp1__pack_to_buffer
                     (const S2SessionResp1 *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &s2_session_resp1__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
S2SessionResp1 *
       s2_session_resp1__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (S2SessionResp1 *)
     protobuf_c_message_unpack (&s2_session_resp1__descriptor,
                                allocator, len, data);
}
void   s2_session_resp1__free_unpacked
                     (S2SessionResp1 *message,
                      ProtobufCAllocator *allocator)
{
  if(!message)
    return;
  assert(message->base.descriptor == &s2_session_resp1__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   sec2_payload__init
                     (Sec2Payload         *message)
{
  static const Sec2Payload init_value = SEC2_PAYLOAD__INIT;
  *message = init_value;
}
size_t sec2_payload__get_packed_size
                     (const Sec2Payload *message)
{
  assert(message->base.descriptor == &sec2_payload__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t sec2_payload__pack
                     (const Sec2Payload *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &sec2_payload__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t sec2_payload__pack_to_buffer
                     (const Sec2Payload *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &sec2_payload__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
Sec2Payload *
       sec2_payload__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (Sec2Payload *)
     protobuf_c_message_unpack (&sec2_payload__descriptor,
                                allocator, len, data);
}
void   sec2_payload__free_unpacked
                     (Sec2Payload *message,
                      ProtobufCAllocator *allocator)
{
  if(!message)
    return;
  assert(message->base.descriptor == &sec2_payload__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
static const ProtobufCFieldDescriptor s2_session_cmd0__field_descriptors[2] =
{
  {
    "client_username",
    1,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_BYTES,
    0,   /* quantifier_offset */
    offsetof(S2SessionCmd0, client_username),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "client_pubkey",
    2,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_BYTES,
    0,   /* quantifier_offset */
    offsetof(S2SessionCmd0, client_pubkey),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned s2_session_cmd0__field_indices_by_name[] = {
  1,   /* field[1] = client_pubkey */
  0,   /* field[0] = client_username */
};
static const ProtobufCIntRange s2_session_cmd0__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 2 }
};
const ProtobufCMessageDescriptor s2_session_cmd0__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "S2SessionCmd0",
  "S2SessionCmd0",
  "S2SessionCmd0",
  "",
  sizeof(S2SessionCmd0),
  2,
  s2_session_cmd0__field_descriptors,
  s2_session_cmd0__field_indices_by_name,
  1,  s2_session_cmd0__number_ranges,
  (ProtobufCMessageInit) s2_session_cmd0__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor s2_session_resp0__field_descriptors[3] =
{
  {
    "status",
    1,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_ENUM,
    0,   /* quantifier_offset */
    offsetof(S2SessionResp0, status),
    &status__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "device_pubkey",
    2,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_BYTES,
    0,   /* quantifier_offset */
    offsetof(S2SessionResp0, device_pubkey),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "device_salt",
    3,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_BYTES,
    0,   /* quantifier_offset */
    offsetof(S2SessionResp0, device_salt),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned s2_session_resp0__field_indices_by_name[] = {
  1,   /* field[1] = device_pubkey */
  2,   /* field[2] = device_salt */
  0,   /* field[0] = status */
};
static const ProtobufCIntRange s2_session_resp0__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 3 }
};
const ProtobufCMessageDescriptor s2_session_resp0__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "S2SessionResp0",
  "S2SessionResp0",
  "S2SessionResp0",
  "",
  sizeof(S2SessionResp0),
  3,
  s2_session_resp0__field_descriptors,
  s2_session_resp0__field_indices_by_name,
  1,  s2_session_resp0__number_ranges,
  (ProtobufCMessageInit) s2_session_resp0__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor s2_session_cmd1__field_descriptors[1] =
{
  {
    "client_proof",
    1,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_BYTES,
    0,   /* quantifier_offset */
    offsetof(S2SessionCmd1, client_proof),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned s2_session_cmd1__field_indices_by_name[] = {
  0,   /* field[0] = client_proof */
};
static const ProtobufCIntRange s2_session_cmd1__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 1 }
};
const ProtobufCMessageDescriptor s2_session_cmd1__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "S2SessionCmd1",
  "S2SessionCmd1",
  "S2SessionCmd1",
  "",
  sizeof(S2SessionCmd1),
  1,
  s2_session_cmd1__field_descriptors,
  s2_session_cmd1__field_indices_by_name,
  1,  s2_session_cmd1__number_ranges,
  (ProtobufCMessageInit) s2_session_cmd1__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor s2_session_resp1__field_descriptors[3] =
{
  {
    "status",
    1,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_ENUM,
    0,   /* quantifier_offset */
    offsetof(S2SessionResp1, status),
    &status__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "device_proof",
    2,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_BYTES,
    0,   /* quantifier_offset */
    offsetof(S2SessionResp1, device_proof),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "device_nonce",
    3,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_BYTES,
    0,   /* quantifier_offset */
    offsetof(S2SessionResp1, device_nonce),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned s2_session_resp1__field_indices_by_name[] = {
  2,   /* field[2] = device_nonce */
  1,   /* field[1] = device_proof */
  0,   /* field[0] = status */
};
static const ProtobufCIntRange s2_session_resp1__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 3 }
};
const ProtobufCMessageDescriptor s2_session_resp1__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "S2SessionResp1",
  "S2SessionResp1",
  "S2SessionResp1",
  "",
  sizeof(S2SessionResp1),
  3,
  s2_session_resp1__field_descriptors,
  s2_session_resp1__field_indices_by_name,
  1,  s2_session_resp1__number_ranges,
  (ProtobufCMessageInit) s2_session_resp1__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor sec2_payload__field_descriptors[5] =
{
  {
    "msg",
    1,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_ENUM,
    0,   /* quantifier_offset */
    offsetof(Sec2Payload, msg),
    &sec2_msg_type__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "sc0",
    20,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(Sec2Payload, payload_case),
    offsetof(Sec2Payload, sc0),
    &s2_session_cmd0__descriptor,
    NULL,
    0 | PROTOBUF_C_FIELD_FLAG_ONEOF,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "sr0",
    21,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(Sec2Payload, payload_case),
    offsetof(Sec2Payload, sr0),
    &s2_session_resp0__descriptor,
    NULL,
    0 | PROTOBUF_C_FIELD_FLAG_ONEOF,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "sc1",
    22,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(Sec2Payload, payload_case),
    offsetof(Sec2Payload, sc1),
    &s2_session_cmd1__descriptor,
    NULL,
    0 | PROTOBUF_C_FIELD_FLAG_ONEOF,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "sr1",
    23,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(Sec2Payload, payload_case),
    offsetof(Sec2Payload, sr1),
    &s2_session_resp1__descriptor,
    NULL,
    0 | PROTOBUF_C_FIELD_FLAG_ONEOF,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned sec2_payload__field_indices_by_name[] = {
  0,   /* field[0] = msg */
  1,   /* field[1] = sc0 */
  3,   /* field[3] = sc1 */
  2,   /* field[2] = sr0 */
  4,   /* field[4] = sr1 */
};
static const ProtobufCIntRange sec2_payload__number_ranges[2 + 1] =
{
  { 1, 0 },
  { 20, 1 },
  { 0, 5 }
};
const ProtobufCMessageDescriptor sec2_payload__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "Sec2Payload",
  "Sec2Payload",
  "Sec2Payload",
  "",
  sizeof(Sec2Payload),
  5,
  sec2_payload__field_descriptors,
  sec2_payload__field_indices_by_name,
  2,  sec2_payload__number_ranges,
  (ProtobufCMessageInit) sec2_payload__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCEnumValue sec2_msg_type__enum_values_by_number[4] =
{
  { "S2Session_Command0", "SEC2_MSG_TYPE__S2Session_Command0", 0 },
  { "S2Session_Response0", "SEC2_MSG_TYPE__S2Session_Response0", 1 },
  { "S2Session_Command1", "SEC2_MSG_TYPE__S2Session_Command1", 2 },
  { "S2Session_Response1", "SEC2_MSG_TYPE__S2Session_Response1", 3 },
};
static const ProtobufCIntRange sec2_msg_type__value_ranges[] = {
{0, 0},{0, 4}
};
static const ProtobufCEnumValueIndex sec2_msg_type__enum_values_by_name[4] =
{
  { "S2Session_Command0", 0 },
  { "S2Session_Command1", 2 },
  { "S2Session_Response0", 1 },
  { "S2Session_Response1", 3 },
};
const ProtobufCEnumDescriptor sec2_msg_type__descriptor =
{
  PROTOBUF_C__ENUM_DESCRIPTOR_MAGIC,
  "Sec2MsgType",
  "Sec2MsgType",
  "Sec2MsgType",
  "",
  4,
  sec2_msg_type__enum_values_by_number,
  4,
  sec2_msg_type__enum_values_by_name,
  1,
  sec2_msg_type__value_ranges,
  NULL,NULL,NULL,NULL   /* reserved[1234] */
};
