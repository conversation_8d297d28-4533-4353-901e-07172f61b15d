/* Generated by the protocol buffer compiler.  DO NOT EDIT! */
/* Generated from: sec1.proto */

/* Do not generate deprecated warnings for self */
#ifndef PROTOBUF_C__NO_DEPRECATED
#define PROTOBUF_C__NO_DEPRECATED
#endif

#include "sec1.pb-c.h"
void   session_cmd1__init
                     (SessionCmd1         *message)
{
  static const SessionCmd1 init_value = SESSION_CMD1__INIT;
  *message = init_value;
}
size_t session_cmd1__get_packed_size
                     (const SessionCmd1 *message)
{
  assert(message->base.descriptor == &session_cmd1__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t session_cmd1__pack
                     (const SessionCmd1 *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &session_cmd1__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t session_cmd1__pack_to_buffer
                     (const SessionCmd1 *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &session_cmd1__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
SessionCmd1 *
       session_cmd1__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (SessionCmd1 *)
     protobuf_c_message_unpack (&session_cmd1__descriptor,
                                allocator, len, data);
}
void   session_cmd1__free_unpacked
                     (SessionCmd1 *message,
                      ProtobufCAllocator *allocator)
{
  if(!message)
    return;
  assert(message->base.descriptor == &session_cmd1__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   session_resp1__init
                     (SessionResp1         *message)
{
  static const SessionResp1 init_value = SESSION_RESP1__INIT;
  *message = init_value;
}
size_t session_resp1__get_packed_size
                     (const SessionResp1 *message)
{
  assert(message->base.descriptor == &session_resp1__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t session_resp1__pack
                     (const SessionResp1 *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &session_resp1__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t session_resp1__pack_to_buffer
                     (const SessionResp1 *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &session_resp1__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
SessionResp1 *
       session_resp1__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (SessionResp1 *)
     protobuf_c_message_unpack (&session_resp1__descriptor,
                                allocator, len, data);
}
void   session_resp1__free_unpacked
                     (SessionResp1 *message,
                      ProtobufCAllocator *allocator)
{
  if(!message)
    return;
  assert(message->base.descriptor == &session_resp1__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   session_cmd0__init
                     (SessionCmd0         *message)
{
  static const SessionCmd0 init_value = SESSION_CMD0__INIT;
  *message = init_value;
}
size_t session_cmd0__get_packed_size
                     (const SessionCmd0 *message)
{
  assert(message->base.descriptor == &session_cmd0__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t session_cmd0__pack
                     (const SessionCmd0 *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &session_cmd0__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t session_cmd0__pack_to_buffer
                     (const SessionCmd0 *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &session_cmd0__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
SessionCmd0 *
       session_cmd0__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (SessionCmd0 *)
     protobuf_c_message_unpack (&session_cmd0__descriptor,
                                allocator, len, data);
}
void   session_cmd0__free_unpacked
                     (SessionCmd0 *message,
                      ProtobufCAllocator *allocator)
{
  if(!message)
    return;
  assert(message->base.descriptor == &session_cmd0__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   session_resp0__init
                     (SessionResp0         *message)
{
  static const SessionResp0 init_value = SESSION_RESP0__INIT;
  *message = init_value;
}
size_t session_resp0__get_packed_size
                     (const SessionResp0 *message)
{
  assert(message->base.descriptor == &session_resp0__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t session_resp0__pack
                     (const SessionResp0 *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &session_resp0__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t session_resp0__pack_to_buffer
                     (const SessionResp0 *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &session_resp0__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
SessionResp0 *
       session_resp0__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (SessionResp0 *)
     protobuf_c_message_unpack (&session_resp0__descriptor,
                                allocator, len, data);
}
void   session_resp0__free_unpacked
                     (SessionResp0 *message,
                      ProtobufCAllocator *allocator)
{
  if(!message)
    return;
  assert(message->base.descriptor == &session_resp0__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   sec1_payload__init
                     (Sec1Payload         *message)
{
  static const Sec1Payload init_value = SEC1_PAYLOAD__INIT;
  *message = init_value;
}
size_t sec1_payload__get_packed_size
                     (const Sec1Payload *message)
{
  assert(message->base.descriptor == &sec1_payload__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t sec1_payload__pack
                     (const Sec1Payload *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &sec1_payload__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t sec1_payload__pack_to_buffer
                     (const Sec1Payload *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &sec1_payload__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
Sec1Payload *
       sec1_payload__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (Sec1Payload *)
     protobuf_c_message_unpack (&sec1_payload__descriptor,
                                allocator, len, data);
}
void   sec1_payload__free_unpacked
                     (Sec1Payload *message,
                      ProtobufCAllocator *allocator)
{
  if(!message)
    return;
  assert(message->base.descriptor == &sec1_payload__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
static const ProtobufCFieldDescriptor session_cmd1__field_descriptors[1] =
{
  {
    "client_verify_data",
    2,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_BYTES,
    0,   /* quantifier_offset */
    offsetof(SessionCmd1, client_verify_data),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned session_cmd1__field_indices_by_name[] = {
  0,   /* field[0] = client_verify_data */
};
static const ProtobufCIntRange session_cmd1__number_ranges[1 + 1] =
{
  { 2, 0 },
  { 0, 1 }
};
const ProtobufCMessageDescriptor session_cmd1__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "SessionCmd1",
  "SessionCmd1",
  "SessionCmd1",
  "",
  sizeof(SessionCmd1),
  1,
  session_cmd1__field_descriptors,
  session_cmd1__field_indices_by_name,
  1,  session_cmd1__number_ranges,
  (ProtobufCMessageInit) session_cmd1__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor session_resp1__field_descriptors[2] =
{
  {
    "status",
    1,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_ENUM,
    0,   /* quantifier_offset */
    offsetof(SessionResp1, status),
    &status__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "device_verify_data",
    3,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_BYTES,
    0,   /* quantifier_offset */
    offsetof(SessionResp1, device_verify_data),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned session_resp1__field_indices_by_name[] = {
  1,   /* field[1] = device_verify_data */
  0,   /* field[0] = status */
};
static const ProtobufCIntRange session_resp1__number_ranges[2 + 1] =
{
  { 1, 0 },
  { 3, 1 },
  { 0, 2 }
};
const ProtobufCMessageDescriptor session_resp1__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "SessionResp1",
  "SessionResp1",
  "SessionResp1",
  "",
  sizeof(SessionResp1),
  2,
  session_resp1__field_descriptors,
  session_resp1__field_indices_by_name,
  2,  session_resp1__number_ranges,
  (ProtobufCMessageInit) session_resp1__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor session_cmd0__field_descriptors[1] =
{
  {
    "client_pubkey",
    1,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_BYTES,
    0,   /* quantifier_offset */
    offsetof(SessionCmd0, client_pubkey),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned session_cmd0__field_indices_by_name[] = {
  0,   /* field[0] = client_pubkey */
};
static const ProtobufCIntRange session_cmd0__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 1 }
};
const ProtobufCMessageDescriptor session_cmd0__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "SessionCmd0",
  "SessionCmd0",
  "SessionCmd0",
  "",
  sizeof(SessionCmd0),
  1,
  session_cmd0__field_descriptors,
  session_cmd0__field_indices_by_name,
  1,  session_cmd0__number_ranges,
  (ProtobufCMessageInit) session_cmd0__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor session_resp0__field_descriptors[3] =
{
  {
    "status",
    1,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_ENUM,
    0,   /* quantifier_offset */
    offsetof(SessionResp0, status),
    &status__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "device_pubkey",
    2,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_BYTES,
    0,   /* quantifier_offset */
    offsetof(SessionResp0, device_pubkey),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "device_random",
    3,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_BYTES,
    0,   /* quantifier_offset */
    offsetof(SessionResp0, device_random),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned session_resp0__field_indices_by_name[] = {
  1,   /* field[1] = device_pubkey */
  2,   /* field[2] = device_random */
  0,   /* field[0] = status */
};
static const ProtobufCIntRange session_resp0__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 3 }
};
const ProtobufCMessageDescriptor session_resp0__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "SessionResp0",
  "SessionResp0",
  "SessionResp0",
  "",
  sizeof(SessionResp0),
  3,
  session_resp0__field_descriptors,
  session_resp0__field_indices_by_name,
  1,  session_resp0__number_ranges,
  (ProtobufCMessageInit) session_resp0__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor sec1_payload__field_descriptors[5] =
{
  {
    "msg",
    1,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_ENUM,
    0,   /* quantifier_offset */
    offsetof(Sec1Payload, msg),
    &sec1_msg_type__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "sc0",
    20,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(Sec1Payload, payload_case),
    offsetof(Sec1Payload, sc0),
    &session_cmd0__descriptor,
    NULL,
    0 | PROTOBUF_C_FIELD_FLAG_ONEOF,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "sr0",
    21,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(Sec1Payload, payload_case),
    offsetof(Sec1Payload, sr0),
    &session_resp0__descriptor,
    NULL,
    0 | PROTOBUF_C_FIELD_FLAG_ONEOF,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "sc1",
    22,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(Sec1Payload, payload_case),
    offsetof(Sec1Payload, sc1),
    &session_cmd1__descriptor,
    NULL,
    0 | PROTOBUF_C_FIELD_FLAG_ONEOF,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "sr1",
    23,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(Sec1Payload, payload_case),
    offsetof(Sec1Payload, sr1),
    &session_resp1__descriptor,
    NULL,
    0 | PROTOBUF_C_FIELD_FLAG_ONEOF,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned sec1_payload__field_indices_by_name[] = {
  0,   /* field[0] = msg */
  1,   /* field[1] = sc0 */
  3,   /* field[3] = sc1 */
  2,   /* field[2] = sr0 */
  4,   /* field[4] = sr1 */
};
static const ProtobufCIntRange sec1_payload__number_ranges[2 + 1] =
{
  { 1, 0 },
  { 20, 1 },
  { 0, 5 }
};
const ProtobufCMessageDescriptor sec1_payload__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "Sec1Payload",
  "Sec1Payload",
  "Sec1Payload",
  "",
  sizeof(Sec1Payload),
  5,
  sec1_payload__field_descriptors,
  sec1_payload__field_indices_by_name,
  2,  sec1_payload__number_ranges,
  (ProtobufCMessageInit) sec1_payload__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCEnumValue sec1_msg_type__enum_values_by_number[4] =
{
  { "Session_Command0", "SEC1_MSG_TYPE__Session_Command0", 0 },
  { "Session_Response0", "SEC1_MSG_TYPE__Session_Response0", 1 },
  { "Session_Command1", "SEC1_MSG_TYPE__Session_Command1", 2 },
  { "Session_Response1", "SEC1_MSG_TYPE__Session_Response1", 3 },
};
static const ProtobufCIntRange sec1_msg_type__value_ranges[] = {
{0, 0},{0, 4}
};
static const ProtobufCEnumValueIndex sec1_msg_type__enum_values_by_name[4] =
{
  { "Session_Command0", 0 },
  { "Session_Command1", 2 },
  { "Session_Response0", 1 },
  { "Session_Response1", 3 },
};
const ProtobufCEnumDescriptor sec1_msg_type__descriptor =
{
  PROTOBUF_C__ENUM_DESCRIPTOR_MAGIC,
  "Sec1MsgType",
  "Sec1MsgType",
  "Sec1MsgType",
  "",
  4,
  sec1_msg_type__enum_values_by_number,
  4,
  sec1_msg_type__enum_values_by_name,
  1,
  sec1_msg_type__value_ranges,
  NULL,NULL,NULL,NULL   /* reserved[1234] */
};
