# QMI8658 传感器驱动组件

## 概述

QMI8658是一款高性能的6轴惯性测量单元(IMU)，集成了三轴加速度计和三轴陀螺仪。本驱动组件为ESP32-S3平台提供了完整的QMI8658传感器支持。

## 硬件特性

- **传感器型号**: QMI8658A
- **通信接口**: I2C
- **I2C地址**: 0x6A
- **加速度计量程**: ±2g, ±4g, ±8g, ±16g
- **陀螺仪量程**: ±16dps ~ ±2048dps
- **输出数据率**: 8000Hz ~ 31.25Hz
- **工作电压**: 1.8V ~ 3.6V

## 硬件连接

| ESP32-S3引脚 | QMI8658引脚 | 功能 |
|-------------|-------------|------|
| GPIO1       | SDA         | I2C数据线 |
| GPIO2       | SCL         | I2C时钟线 |
| 3.3V        | VDD         | 电源正极 |
| GND         | GND         | 电源负极 |

## 软件架构

### 文件结构
```
components/qmi8658/
├── include/
│   └── qmi8658.h          # 头文件
├── qmi8658.c              # 实现文件
├── CMakeLists.txt         # 构建配置
└── README.md              # 说明文档
```

### 主要功能

1. **传感器初始化**: 自动检测传感器并进行基础配置
2. **数据读取**: 支持原始数据和结构体数据读取
3. **配置管理**: 支持动态配置量程和采样率
4. **温度读取**: 读取芯片内部温度
5. **状态监控**: 获取传感器工作状态

## API参考

### 数据结构

```c
typedef struct {
    float acc_x;    // X轴加速度 (g)
    float acc_y;    // Y轴加速度 (g)
    float acc_z;    // Z轴加速度 (g)
    float gyro_x;   // X轴角速度 (dps)
    float gyro_y;   // Y轴角速度 (dps)
    float gyro_z;   // Z轴角速度 (dps)
} qmi8658_data_t;

typedef struct {
    uint8_t acc_range;      // 加速度计量程
    uint8_t gyro_range;     // 陀螺仪量程
    uint8_t acc_odr;        // 加速度计输出数据率
    uint8_t gyro_odr;       // 陀螺仪输出数据率
} qmi8658_config_t;
```

### 核心函数

#### qmi8658_init()
```c
esp_err_t qmi8658_init(i2c_master_bus_handle_t bus_handle);
```
初始化QMI8658传感器，包括创建I2C设备句柄、验证通信和基础配置。

**参数**:
- `bus_handle`: I2C主机总线句柄

**返回值**:
- `ESP_OK`: 初始化成功
- `ESP_ERR_INVALID_ARG`: 参数无效
- `ESP_ERR_NOT_FOUND`: 传感器未找到

#### qmi8658_read_raw_data()
```c
esp_err_t qmi8658_read_raw_data(float* acc_x, float* acc_y, float* acc_z, 
                                float* gyro_x, float* gyro_y, float* gyro_z);
```
读取传感器原始数据并转换为标准单位。

#### qmi8658_read_data()
```c
esp_err_t qmi8658_read_data(qmi8658_data_t* data);
```
读取传感器数据到结构体。

#### qmi8658_configure()
```c
esp_err_t qmi8658_configure(const qmi8658_config_t* config);
```
配置传感器参数。

## 使用示例

### 基本使用

```c
#include "qmi8658.h"
#include "driver/i2c_master.h"

// 1. 初始化I2C总线
i2c_master_bus_config_t i2c_bus_config = {
    .clk_source = I2C_CLK_SRC_DEFAULT,
    .i2c_port = I2C_NUM_0,
    .scl_io_num = 2,
    .sda_io_num = 1,
    .glitch_ignore_cnt = 7,
    .flags.enable_internal_pullup = true,
};

i2c_master_bus_handle_t bus_handle;
i2c_new_master_bus(&i2c_bus_config, &bus_handle);

// 2. 初始化传感器
esp_err_t ret = qmi8658_init(bus_handle);
if (ret != ESP_OK) {
    ESP_LOGE("APP", "传感器初始化失败");
    return;
}

// 3. 读取数据
qmi8658_data_t sensor_data;
ret = qmi8658_read_data(&sensor_data);
if (ret == ESP_OK) {
    printf("加速度: X=%.3f, Y=%.3f, Z=%.3f g\n", 
           sensor_data.acc_x, sensor_data.acc_y, sensor_data.acc_z);
    printf("陀螺仪: X=%.1f, Y=%.1f, Z=%.1f dps\n", 
           sensor_data.gyro_x, sensor_data.gyro_y, sensor_data.gyro_z);
}
```

### 自定义配置

```c
// 配置高精度低速模式
qmi8658_config_t config = {
    .acc_range = QMI8658_ACC_RANGE_2G,      // ±2g
    .gyro_range = QMI8658_GYRO_RANGE_256DPS, // ±256dps
    .acc_odr = QMI8658_ODR_125HZ,           // 125Hz
    .gyro_odr = QMI8658_ODR_125HZ           // 125Hz
};

esp_err_t ret = qmi8658_configure(&config);
if (ret == ESP_OK) {
    ESP_LOGI("APP", "传感器配置更新成功");
}
```

## 测试程序

项目提供了两个测试程序：

1. **qmi8658_example.c**: 集成示例，可与主程序一起运行
2. **qmi8658_test_main.c**: 独立测试程序，专门用于传感器测试

### 使用独立测试程序

修改 `main/CMakeLists.txt`：
```cmake
# 使用QMI8658专用测试程序
set(MAIN_SRCS "qmi8658_test_main.c")
```

### 使用集成示例

修改 `main/CMakeLists.txt`：
```cmake
# 在主程序中添加QMI8658示例
set(MAIN_SRCS "Lichuang-ESP32.c" "qmi8658_example.c")
```

然后在主程序中调用：
```c
#include "qmi8658_example.h"

void app_main(void) {
    // 其他初始化代码...
    
    // 启动QMI8658示例
    qmi8658_example_main();
}
```

## 故障排除

### 常见问题

1. **传感器初始化失败**
   - 检查I2C连接是否正确
   - 确认传感器电源供应正常
   - 验证I2C地址是否正确(0x6A)

2. **数据读取异常**
   - 检查I2C时序是否正确
   - 确认传感器配置是否有效
   - 验证数据转换计算

3. **数据精度问题**
   - 选择合适的量程设置
   - 考虑环境温度影响
   - 进行传感器校准

### 调试建议

1. 启用详细日志输出
2. 使用示波器检查I2C信号
3. 验证传感器寄存器读写
4. 对比数据手册规格

## 技术支持

如有问题，请检查：
1. 硬件连接是否正确
2. ESP-IDF版本兼容性
3. 组件依赖关系
4. 编译配置选项

## 版本历史

- v1.0.0 (2025-07-27): 初始版本
  - 基本传感器驱动功能
  - I2C通信支持
  - 数据读取和配置
  - 示例程序和文档
