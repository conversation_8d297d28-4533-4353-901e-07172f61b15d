
# field_name,       |    efuse_block, | bit_start, | bit_count, |comment #
#                   |    (EFUSE_BLK0  | (0..255)   | (1-256)    |        #
#                   |     EFUSE_BLK1  |            |            |        #
#                   |        ...)     |            |            |        #
##########################################################################
# !!!!!!!!!!! #
# After editing this file, run the command manually "idf.py efuse-common-table"
# this will generate new source files, next rebuild all the sources.
# !!!!!!!!!!! #

# This file was generated by regtools.py based on the efuses.yaml file with the version: f75f74727101326a187188a23f4a6c70

WR_DIS,                                          EFUSE_BLK0,   0,  32, [] Disable programming of individual eFuses
WR_DIS.RD_DIS,                                   EFUSE_BLK0,   0,   1, [] wr_dis of RD_DIS
WR_DIS.DIS_ICACHE,                               EFUSE_BLK0,   2,   1, [] wr_dis of DIS_ICACHE
WR_DIS.DIS_DCACHE,                               EFUSE_BLK0,   2,   1, [] wr_dis of DIS_DCACHE
WR_DIS.DIS_DOWNLOAD_ICACHE,                      EFUSE_BLK0,   2,   1, [] wr_dis of DIS_DOWNLOAD_ICACHE
WR_DIS.DIS_DOWNLOAD_DCACHE,                      EFUSE_BLK0,   2,   1, [] wr_dis of DIS_DOWNLOAD_DCACHE
WR_DIS.DIS_FORCE_DOWNLOAD,                       EFUSE_BLK0,   2,   1, [] wr_dis of DIS_FORCE_DOWNLOAD
WR_DIS.DIS_USB_OTG,                              EFUSE_BLK0,   2,   1, [WR_DIS.DIS_USB] wr_dis of DIS_USB_OTG
WR_DIS.DIS_TWAI,                                 EFUSE_BLK0,   2,   1, [WR_DIS.DIS_CAN] wr_dis of DIS_TWAI
WR_DIS.DIS_APP_CPU,                              EFUSE_BLK0,   2,   1, [] wr_dis of DIS_APP_CPU
WR_DIS.DIS_PAD_JTAG,                             EFUSE_BLK0,   2,   1, [WR_DIS.HARD_DIS_JTAG] wr_dis of DIS_PAD_JTAG
WR_DIS.DIS_DOWNLOAD_MANUAL_ENCRYPT,              EFUSE_BLK0,   2,   1, [] wr_dis of DIS_DOWNLOAD_MANUAL_ENCRYPT
WR_DIS.DIS_USB_JTAG,                             EFUSE_BLK0,   2,   1, [] wr_dis of DIS_USB_JTAG
WR_DIS.DIS_USB_SERIAL_JTAG,                      EFUSE_BLK0,   2,   1, [WR_DIS.DIS_USB_DEVICE] wr_dis of DIS_USB_SERIAL_JTAG
WR_DIS.STRAP_JTAG_SEL,                           EFUSE_BLK0,   2,   1, [] wr_dis of STRAP_JTAG_SEL
WR_DIS.USB_PHY_SEL,                              EFUSE_BLK0,   2,   1, [] wr_dis of USB_PHY_SEL
WR_DIS.VDD_SPI_XPD,                              EFUSE_BLK0,   3,   1, [] wr_dis of VDD_SPI_XPD
WR_DIS.VDD_SPI_TIEH,                             EFUSE_BLK0,   3,   1, [] wr_dis of VDD_SPI_TIEH
WR_DIS.VDD_SPI_FORCE,                            EFUSE_BLK0,   3,   1, [] wr_dis of VDD_SPI_FORCE
WR_DIS.WDT_DELAY_SEL,                            EFUSE_BLK0,   3,   1, [] wr_dis of WDT_DELAY_SEL
WR_DIS.SPI_BOOT_CRYPT_CNT,                       EFUSE_BLK0,   4,   1, [] wr_dis of SPI_BOOT_CRYPT_CNT
WR_DIS.SECURE_BOOT_KEY_REVOKE0,                  EFUSE_BLK0,   5,   1, [] wr_dis of SECURE_BOOT_KEY_REVOKE0
WR_DIS.SECURE_BOOT_KEY_REVOKE1,                  EFUSE_BLK0,   6,   1, [] wr_dis of SECURE_BOOT_KEY_REVOKE1
WR_DIS.SECURE_BOOT_KEY_REVOKE2,                  EFUSE_BLK0,   7,   1, [] wr_dis of SECURE_BOOT_KEY_REVOKE2
WR_DIS.KEY_PURPOSE_0,                            EFUSE_BLK0,   8,   1, [WR_DIS.KEY0_PURPOSE] wr_dis of KEY_PURPOSE_0
WR_DIS.KEY_PURPOSE_1,                            EFUSE_BLK0,   9,   1, [WR_DIS.KEY1_PURPOSE] wr_dis of KEY_PURPOSE_1
WR_DIS.KEY_PURPOSE_2,                            EFUSE_BLK0,  10,   1, [WR_DIS.KEY2_PURPOSE] wr_dis of KEY_PURPOSE_2
WR_DIS.KEY_PURPOSE_3,                            EFUSE_BLK0,  11,   1, [WR_DIS.KEY3_PURPOSE] wr_dis of KEY_PURPOSE_3
WR_DIS.KEY_PURPOSE_4,                            EFUSE_BLK0,  12,   1, [WR_DIS.KEY4_PURPOSE] wr_dis of KEY_PURPOSE_4
WR_DIS.KEY_PURPOSE_5,                            EFUSE_BLK0,  13,   1, [WR_DIS.KEY5_PURPOSE] wr_dis of KEY_PURPOSE_5
WR_DIS.SECURE_BOOT_EN,                           EFUSE_BLK0,  15,   1, [] wr_dis of SECURE_BOOT_EN
WR_DIS.SECURE_BOOT_AGGRESSIVE_REVOKE,            EFUSE_BLK0,  16,   1, [] wr_dis of SECURE_BOOT_AGGRESSIVE_REVOKE
WR_DIS.FLASH_TPUW,                               EFUSE_BLK0,  18,   1, [] wr_dis of FLASH_TPUW
WR_DIS.DIS_DOWNLOAD_MODE,                        EFUSE_BLK0,  18,   1, [] wr_dis of DIS_DOWNLOAD_MODE
WR_DIS.DIS_DIRECT_BOOT,                          EFUSE_BLK0,  18,   1, [WR_DIS.DIS_LEGACY_SPI_BOOT] wr_dis of DIS_DIRECT_BOOT
WR_DIS.DIS_USB_SERIAL_JTAG_ROM_PRINT,            EFUSE_BLK0,  18,   1, [WR_DIS.UART_PRINT_CHANNEL] wr_dis of DIS_USB_SERIAL_JTAG_ROM_PRINT
WR_DIS.FLASH_ECC_MODE,                           EFUSE_BLK0,  18,   1, [] wr_dis of FLASH_ECC_MODE
WR_DIS.DIS_USB_SERIAL_JTAG_DOWNLOAD_MODE,        EFUSE_BLK0,  18,   1, [WR_DIS.DIS_USB_DOWNLOAD_MODE] wr_dis of DIS_USB_SERIAL_JTAG_DOWNLOAD_MODE
WR_DIS.ENABLE_SECURITY_DOWNLOAD,                 EFUSE_BLK0,  18,   1, [] wr_dis of ENABLE_SECURITY_DOWNLOAD
WR_DIS.UART_PRINT_CONTROL,                       EFUSE_BLK0,  18,   1, [] wr_dis of UART_PRINT_CONTROL
WR_DIS.PIN_POWER_SELECTION,                      EFUSE_BLK0,  18,   1, [] wr_dis of PIN_POWER_SELECTION
WR_DIS.FLASH_TYPE,                               EFUSE_BLK0,  18,   1, [] wr_dis of FLASH_TYPE
WR_DIS.FLASH_PAGE_SIZE,                          EFUSE_BLK0,  18,   1, [] wr_dis of FLASH_PAGE_SIZE
WR_DIS.FLASH_ECC_EN,                             EFUSE_BLK0,  18,   1, [] wr_dis of FLASH_ECC_EN
WR_DIS.FORCE_SEND_RESUME,                        EFUSE_BLK0,  18,   1, [] wr_dis of FORCE_SEND_RESUME
WR_DIS.SECURE_VERSION,                           EFUSE_BLK0,  18,   1, [] wr_dis of SECURE_VERSION
WR_DIS.DIS_USB_OTG_DOWNLOAD_MODE,                EFUSE_BLK0,  19,   1, [] wr_dis of DIS_USB_OTG_DOWNLOAD_MODE
WR_DIS.DISABLE_WAFER_VERSION_MAJOR,              EFUSE_BLK0,  19,   1, [] wr_dis of DISABLE_WAFER_VERSION_MAJOR
WR_DIS.DISABLE_BLK_VERSION_MAJOR,                EFUSE_BLK0,  19,   1, [] wr_dis of DISABLE_BLK_VERSION_MAJOR
WR_DIS.BLK1,                                     EFUSE_BLK0,  20,   1, [] wr_dis of BLOCK1
WR_DIS.MAC,                                      EFUSE_BLK0,  20,   1, [WR_DIS.MAC_FACTORY] wr_dis of MAC
WR_DIS.SPI_PAD_CONFIG_CLK,                       EFUSE_BLK0,  20,   1, [] wr_dis of SPI_PAD_CONFIG_CLK
WR_DIS.SPI_PAD_CONFIG_Q,                         EFUSE_BLK0,  20,   1, [] wr_dis of SPI_PAD_CONFIG_Q
WR_DIS.SPI_PAD_CONFIG_D,                         EFUSE_BLK0,  20,   1, [] wr_dis of SPI_PAD_CONFIG_D
WR_DIS.SPI_PAD_CONFIG_CS,                        EFUSE_BLK0,  20,   1, [] wr_dis of SPI_PAD_CONFIG_CS
WR_DIS.SPI_PAD_CONFIG_HD,                        EFUSE_BLK0,  20,   1, [] wr_dis of SPI_PAD_CONFIG_HD
WR_DIS.SPI_PAD_CONFIG_WP,                        EFUSE_BLK0,  20,   1, [] wr_dis of SPI_PAD_CONFIG_WP
WR_DIS.SPI_PAD_CONFIG_DQS,                       EFUSE_BLK0,  20,   1, [] wr_dis of SPI_PAD_CONFIG_DQS
WR_DIS.SPI_PAD_CONFIG_D4,                        EFUSE_BLK0,  20,   1, [] wr_dis of SPI_PAD_CONFIG_D4
WR_DIS.SPI_PAD_CONFIG_D5,                        EFUSE_BLK0,  20,   1, [] wr_dis of SPI_PAD_CONFIG_D5
WR_DIS.SPI_PAD_CONFIG_D6,                        EFUSE_BLK0,  20,   1, [] wr_dis of SPI_PAD_CONFIG_D6
WR_DIS.SPI_PAD_CONFIG_D7,                        EFUSE_BLK0,  20,   1, [] wr_dis of SPI_PAD_CONFIG_D7
WR_DIS.WAFER_VERSION_MINOR_LO,                   EFUSE_BLK0,  20,   1, [] wr_dis of WAFER_VERSION_MINOR_LO
WR_DIS.PKG_VERSION,                              EFUSE_BLK0,  20,   1, [] wr_dis of PKG_VERSION
WR_DIS.BLK_VERSION_MINOR,                        EFUSE_BLK0,  20,   1, [] wr_dis of BLK_VERSION_MINOR
WR_DIS.FLASH_CAP,                                EFUSE_BLK0,  20,   1, [] wr_dis of FLASH_CAP
WR_DIS.FLASH_TEMP,                               EFUSE_BLK0,  20,   1, [] wr_dis of FLASH_TEMP
WR_DIS.FLASH_VENDOR,                             EFUSE_BLK0,  20,   1, [] wr_dis of FLASH_VENDOR
WR_DIS.PSRAM_CAP,                                EFUSE_BLK0,  20,   1, [] wr_dis of PSRAM_CAP
WR_DIS.PSRAM_TEMP,                               EFUSE_BLK0,  20,   1, [] wr_dis of PSRAM_TEMP
WR_DIS.PSRAM_VENDOR,                             EFUSE_BLK0,  20,   1, [] wr_dis of PSRAM_VENDOR
WR_DIS.K_RTC_LDO,                                EFUSE_BLK0,  20,   1, [] wr_dis of K_RTC_LDO
WR_DIS.K_DIG_LDO,                                EFUSE_BLK0,  20,   1, [] wr_dis of K_DIG_LDO
WR_DIS.V_RTC_DBIAS20,                            EFUSE_BLK0,  20,   1, [] wr_dis of V_RTC_DBIAS20
WR_DIS.V_DIG_DBIAS20,                            EFUSE_BLK0,  20,   1, [] wr_dis of V_DIG_DBIAS20
WR_DIS.DIG_DBIAS_HVT,                            EFUSE_BLK0,  20,   1, [] wr_dis of DIG_DBIAS_HVT
WR_DIS.WAFER_VERSION_MINOR_HI,                   EFUSE_BLK0,  20,   1, [] wr_dis of WAFER_VERSION_MINOR_HI
WR_DIS.WAFER_VERSION_MAJOR,                      EFUSE_BLK0,  20,   1, [] wr_dis of WAFER_VERSION_MAJOR
WR_DIS.ADC2_CAL_VOL_ATTEN3,                      EFUSE_BLK0,  20,   1, [] wr_dis of ADC2_CAL_VOL_ATTEN3
WR_DIS.SYS_DATA_PART1,                           EFUSE_BLK0,  21,   1, [] wr_dis of BLOCK2
WR_DIS.OPTIONAL_UNIQUE_ID,                       EFUSE_BLK0,  21,   1, [] wr_dis of OPTIONAL_UNIQUE_ID
WR_DIS.BLK_VERSION_MAJOR,                        EFUSE_BLK0,  21,   1, [] wr_dis of BLK_VERSION_MAJOR
WR_DIS.TEMP_CALIB,                               EFUSE_BLK0,  21,   1, [] wr_dis of TEMP_CALIB
WR_DIS.OCODE,                                    EFUSE_BLK0,  21,   1, [] wr_dis of OCODE
WR_DIS.ADC1_INIT_CODE_ATTEN0,                    EFUSE_BLK0,  21,   1, [] wr_dis of ADC1_INIT_CODE_ATTEN0
WR_DIS.ADC1_INIT_CODE_ATTEN1,                    EFUSE_BLK0,  21,   1, [] wr_dis of ADC1_INIT_CODE_ATTEN1
WR_DIS.ADC1_INIT_CODE_ATTEN2,                    EFUSE_BLK0,  21,   1, [] wr_dis of ADC1_INIT_CODE_ATTEN2
WR_DIS.ADC1_INIT_CODE_ATTEN3,                    EFUSE_BLK0,  21,   1, [] wr_dis of ADC1_INIT_CODE_ATTEN3
WR_DIS.ADC2_INIT_CODE_ATTEN0,                    EFUSE_BLK0,  21,   1, [] wr_dis of ADC2_INIT_CODE_ATTEN0
WR_DIS.ADC2_INIT_CODE_ATTEN1,                    EFUSE_BLK0,  21,   1, [] wr_dis of ADC2_INIT_CODE_ATTEN1
WR_DIS.ADC2_INIT_CODE_ATTEN2,                    EFUSE_BLK0,  21,   1, [] wr_dis of ADC2_INIT_CODE_ATTEN2
WR_DIS.ADC2_INIT_CODE_ATTEN3,                    EFUSE_BLK0,  21,   1, [] wr_dis of ADC2_INIT_CODE_ATTEN3
WR_DIS.ADC1_CAL_VOL_ATTEN0,                      EFUSE_BLK0,  21,   1, [] wr_dis of ADC1_CAL_VOL_ATTEN0
WR_DIS.ADC1_CAL_VOL_ATTEN1,                      EFUSE_BLK0,  21,   1, [] wr_dis of ADC1_CAL_VOL_ATTEN1
WR_DIS.ADC1_CAL_VOL_ATTEN2,                      EFUSE_BLK0,  21,   1, [] wr_dis of ADC1_CAL_VOL_ATTEN2
WR_DIS.ADC1_CAL_VOL_ATTEN3,                      EFUSE_BLK0,  21,   1, [] wr_dis of ADC1_CAL_VOL_ATTEN3
WR_DIS.ADC2_CAL_VOL_ATTEN0,                      EFUSE_BLK0,  21,   1, [] wr_dis of ADC2_CAL_VOL_ATTEN0
WR_DIS.ADC2_CAL_VOL_ATTEN1,                      EFUSE_BLK0,  21,   1, [] wr_dis of ADC2_CAL_VOL_ATTEN1
WR_DIS.ADC2_CAL_VOL_ATTEN2,                      EFUSE_BLK0,  21,   1, [] wr_dis of ADC2_CAL_VOL_ATTEN2
WR_DIS.BLOCK_USR_DATA,                           EFUSE_BLK0,  22,   1, [WR_DIS.USER_DATA] wr_dis of BLOCK_USR_DATA
WR_DIS.CUSTOM_MAC,                               EFUSE_BLK0,  22,   1, [WR_DIS.MAC_CUSTOM WR_DIS.USER_DATA_MAC_CUSTOM] wr_dis of CUSTOM_MAC
WR_DIS.BLOCK_KEY0,                               EFUSE_BLK0,  23,   1, [WR_DIS.KEY0] wr_dis of BLOCK_KEY0
WR_DIS.BLOCK_KEY1,                               EFUSE_BLK0,  24,   1, [WR_DIS.KEY1] wr_dis of BLOCK_KEY1
WR_DIS.BLOCK_KEY2,                               EFUSE_BLK0,  25,   1, [WR_DIS.KEY2] wr_dis of BLOCK_KEY2
WR_DIS.BLOCK_KEY3,                               EFUSE_BLK0,  26,   1, [WR_DIS.KEY3] wr_dis of BLOCK_KEY3
WR_DIS.BLOCK_KEY4,                               EFUSE_BLK0,  27,   1, [WR_DIS.KEY4] wr_dis of BLOCK_KEY4
WR_DIS.BLOCK_KEY5,                               EFUSE_BLK0,  28,   1, [WR_DIS.KEY5] wr_dis of BLOCK_KEY5
WR_DIS.BLOCK_SYS_DATA2,                          EFUSE_BLK0,  29,   1, [WR_DIS.SYS_DATA_PART2] wr_dis of BLOCK_SYS_DATA2
WR_DIS.USB_EXCHG_PINS,                           EFUSE_BLK0,  30,   1, [] wr_dis of USB_EXCHG_PINS
WR_DIS.USB_EXT_PHY_ENABLE,                       EFUSE_BLK0,  30,   1, [WR_DIS.EXT_PHY_ENABLE] wr_dis of USB_EXT_PHY_ENABLE
WR_DIS.SOFT_DIS_JTAG,                            EFUSE_BLK0,  31,   1, [] wr_dis of SOFT_DIS_JTAG
RD_DIS,                                          EFUSE_BLK0,  32,   7, [] Disable reading from BlOCK4-10
RD_DIS.BLOCK_KEY0,                               EFUSE_BLK0,  32,   1, [RD_DIS.KEY0] rd_dis of BLOCK_KEY0
RD_DIS.BLOCK_KEY1,                               EFUSE_BLK0,  33,   1, [RD_DIS.KEY1] rd_dis of BLOCK_KEY1
RD_DIS.BLOCK_KEY2,                               EFUSE_BLK0,  34,   1, [RD_DIS.KEY2] rd_dis of BLOCK_KEY2
RD_DIS.BLOCK_KEY3,                               EFUSE_BLK0,  35,   1, [RD_DIS.KEY3] rd_dis of BLOCK_KEY3
RD_DIS.BLOCK_KEY4,                               EFUSE_BLK0,  36,   1, [RD_DIS.KEY4] rd_dis of BLOCK_KEY4
RD_DIS.BLOCK_KEY5,                               EFUSE_BLK0,  37,   1, [RD_DIS.KEY5] rd_dis of BLOCK_KEY5
RD_DIS.BLOCK_SYS_DATA2,                          EFUSE_BLK0,  38,   1, [RD_DIS.SYS_DATA_PART2] rd_dis of BLOCK_SYS_DATA2
DIS_ICACHE,                                      EFUSE_BLK0,  40,   1, [] Set this bit to disable Icache
DIS_DCACHE,                                      EFUSE_BLK0,  41,   1, [] Set this bit to disable Dcache
DIS_DOWNLOAD_ICACHE,                             EFUSE_BLK0,  42,   1, [] Set this bit to disable Icache in download mode (boot_mode[3:0] is 0; 1; 2; 3; 6; 7)
DIS_DOWNLOAD_DCACHE,                             EFUSE_BLK0,  43,   1, [] Set this bit to disable Dcache in download mode ( boot_mode[3:0] is 0; 1; 2; 3; 6; 7)
DIS_FORCE_DOWNLOAD,                              EFUSE_BLK0,  44,   1, [] Set this bit to disable the function that forces chip into download mode
DIS_USB_OTG,                                     EFUSE_BLK0,  45,   1, [DIS_USB] Set this bit to disable USB function
DIS_TWAI,                                        EFUSE_BLK0,  46,   1, [DIS_CAN] Set this bit to disable CAN function
DIS_APP_CPU,                                     EFUSE_BLK0,  47,   1, [] Disable app cpu
SOFT_DIS_JTAG,                                   EFUSE_BLK0,  48,   3, [] Set these bits to disable JTAG in the soft way (odd number 1 means disable ). JTAG can be enabled in HMAC module
DIS_PAD_JTAG,                                    EFUSE_BLK0,  51,   1, [HARD_DIS_JTAG] Set this bit to disable JTAG in the hard way. JTAG is disabled permanently
DIS_DOWNLOAD_MANUAL_ENCRYPT,                     EFUSE_BLK0,  52,   1, [] Set this bit to disable flash encryption when in download boot modes
USB_EXCHG_PINS,                                  EFUSE_BLK0,  57,   1, [] Set this bit to exchange USB D+ and D- pins
USB_EXT_PHY_ENABLE,                              EFUSE_BLK0,  58,   1, [EXT_PHY_ENABLE] Set this bit to enable external PHY
VDD_SPI_XPD,                                     EFUSE_BLK0,  68,   1, [] SPI regulator power up signal
VDD_SPI_TIEH,                                    EFUSE_BLK0,  69,   1, [] If VDD_SPI_FORCE is 1; determines VDD_SPI voltage {0: "VDD_SPI connects to 1.8 V LDO"; 1: "VDD_SPI connects to VDD3P3_RTC_IO"}
VDD_SPI_FORCE,                                   EFUSE_BLK0,  70,   1, [] Set this bit and force to use the configuration of eFuse to configure VDD_SPI
WDT_DELAY_SEL,                                   EFUSE_BLK0,  80,   2, [] RTC watchdog timeout threshold; in unit of slow clock cycle {0: "40000"; 1: "80000"; 2: "160000"; 3: "320000"}
SPI_BOOT_CRYPT_CNT,                              EFUSE_BLK0,  82,   3, [] Enables flash encryption when 1 or 3 bits are set and disabled otherwise {0: "Disable"; 1: "Enable"; 3: "Disable"; 7: "Enable"}
SECURE_BOOT_KEY_REVOKE0,                         EFUSE_BLK0,  85,   1, [] Revoke 1st secure boot key
SECURE_BOOT_KEY_REVOKE1,                         EFUSE_BLK0,  86,   1, [] Revoke 2nd secure boot key
SECURE_BOOT_KEY_REVOKE2,                         EFUSE_BLK0,  87,   1, [] Revoke 3rd secure boot key
KEY_PURPOSE_0,                                   EFUSE_BLK0,  88,   4, [KEY0_PURPOSE] Purpose of Key0
KEY_PURPOSE_1,                                   EFUSE_BLK0,  92,   4, [KEY1_PURPOSE] Purpose of Key1
KEY_PURPOSE_2,                                   EFUSE_BLK0,  96,   4, [KEY2_PURPOSE] Purpose of Key2
KEY_PURPOSE_3,                                   EFUSE_BLK0, 100,   4, [KEY3_PURPOSE] Purpose of Key3
KEY_PURPOSE_4,                                   EFUSE_BLK0, 104,   4, [KEY4_PURPOSE] Purpose of Key4
KEY_PURPOSE_5,                                   EFUSE_BLK0, 108,   4, [KEY5_PURPOSE] Purpose of Key5
SECURE_BOOT_EN,                                  EFUSE_BLK0, 116,   1, [] Set this bit to enable secure boot
SECURE_BOOT_AGGRESSIVE_REVOKE,                   EFUSE_BLK0, 117,   1, [] Set this bit to enable revoking aggressive secure boot
DIS_USB_JTAG,                                    EFUSE_BLK0, 118,   1, [] Set this bit to disable function of usb switch to jtag in module of usb device
DIS_USB_SERIAL_JTAG,                             EFUSE_BLK0, 119,   1, [DIS_USB_DEVICE] Set this bit to disable usb device
STRAP_JTAG_SEL,                                  EFUSE_BLK0, 120,   1, [] Set this bit to enable selection between usb_to_jtag and pad_to_jtag through strapping gpio10 when both reg_dis_usb_jtag and reg_dis_pad_jtag are equal to 0
USB_PHY_SEL,                                     EFUSE_BLK0, 121,   1, [] This bit is used to switch internal PHY and external PHY for USB OTG and USB Device {0: "internal PHY is assigned to USB Device while external PHY is assigned to USB OTG"; 1: "internal PHY is assigned to USB OTG while external PHY is assigned to USB Device"}
FLASH_TPUW,                                      EFUSE_BLK0, 124,   4, [] Configures flash waiting time after power-up; in unit of ms. If the value is less than 15; the waiting time is the configurable value.  Otherwise; the waiting time is twice the configurable value
DIS_DOWNLOAD_MODE,                               EFUSE_BLK0, 128,   1, [] Set this bit to disable download mode (boot_mode[3:0] = 0; 1; 2; 3; 6; 7)
DIS_DIRECT_BOOT,                                 EFUSE_BLK0, 129,   1, [DIS_LEGACY_SPI_BOOT] Disable direct boot mode
DIS_USB_SERIAL_JTAG_ROM_PRINT,                   EFUSE_BLK0, 130,   1, [UART_PRINT_CHANNEL] USB printing {0: "Enable"; 1: "Disable"}
FLASH_ECC_MODE,                                  EFUSE_BLK0, 131,   1, [] Flash ECC mode in ROM {0: "16to18 byte"; 1: "16to17 byte"}
DIS_USB_SERIAL_JTAG_DOWNLOAD_MODE,               EFUSE_BLK0, 132,   1, [DIS_USB_DOWNLOAD_MODE] Set this bit to disable UART download mode through USB
ENABLE_SECURITY_DOWNLOAD,                        EFUSE_BLK0, 133,   1, [] Set this bit to enable secure UART download mode
UART_PRINT_CONTROL,                              EFUSE_BLK0, 134,   2, [] Set the default UART boot message output mode {0: "Enable"; 1: "Enable when GPIO46 is low at reset"; 2: "Enable when GPIO46 is high at reset"; 3: "Disable"}
PIN_POWER_SELECTION,                             EFUSE_BLK0, 136,   1, [] Set default power supply for GPIO33-GPIO37; set when SPI flash is initialized {0: "VDD3P3_CPU"; 1: "VDD_SPI"}
FLASH_TYPE,                                      EFUSE_BLK0, 137,   1, [] SPI flash type {0: "4 data lines"; 1: "8 data lines"}
FLASH_PAGE_SIZE,                                 EFUSE_BLK0, 138,   2, [] Set Flash page size
FLASH_ECC_EN,                                    EFUSE_BLK0, 140,   1, [] Set 1 to enable ECC for flash boot
FORCE_SEND_RESUME,                               EFUSE_BLK0, 141,   1, [] Set this bit to force ROM code to send a resume command during SPI boot
SECURE_VERSION,                                  EFUSE_BLK0, 142,  16, [] Secure version (used by ESP-IDF anti-rollback feature)
DIS_USB_OTG_DOWNLOAD_MODE,                       EFUSE_BLK0, 159,   1, [] Set this bit to disable download through USB-OTG
DISABLE_WAFER_VERSION_MAJOR,                     EFUSE_BLK0, 160,   1, [] Disables check of wafer version major
DISABLE_BLK_VERSION_MAJOR,                       EFUSE_BLK0, 161,   1, [] Disables check of blk version major
MAC,                                             EFUSE_BLK1,  40,   8, [MAC_FACTORY] MAC address
,                                                EFUSE_BLK1,  32,   8, [MAC_FACTORY] MAC address
,                                                EFUSE_BLK1,  24,   8, [MAC_FACTORY] MAC address
,                                                EFUSE_BLK1,  16,   8, [MAC_FACTORY] MAC address
,                                                EFUSE_BLK1,   8,   8, [MAC_FACTORY] MAC address
,                                                EFUSE_BLK1,   0,   8, [MAC_FACTORY] MAC address
SPI_PAD_CONFIG_CLK,                              EFUSE_BLK1,  48,   6, [] SPI_PAD_configure CLK
SPI_PAD_CONFIG_Q,                                EFUSE_BLK1,  54,   6, [] SPI_PAD_configure Q(D1)
SPI_PAD_CONFIG_D,                                EFUSE_BLK1,  60,   6, [] SPI_PAD_configure D(D0)
SPI_PAD_CONFIG_CS,                               EFUSE_BLK1,  66,   6, [] SPI_PAD_configure CS
SPI_PAD_CONFIG_HD,                               EFUSE_BLK1,  72,   6, [] SPI_PAD_configure HD(D3)
SPI_PAD_CONFIG_WP,                               EFUSE_BLK1,  78,   6, [] SPI_PAD_configure WP(D2)
SPI_PAD_CONFIG_DQS,                              EFUSE_BLK1,  84,   6, [] SPI_PAD_configure DQS
SPI_PAD_CONFIG_D4,                               EFUSE_BLK1,  90,   6, [] SPI_PAD_configure D4
SPI_PAD_CONFIG_D5,                               EFUSE_BLK1,  96,   6, [] SPI_PAD_configure D5
SPI_PAD_CONFIG_D6,                               EFUSE_BLK1, 102,   6, [] SPI_PAD_configure D6
SPI_PAD_CONFIG_D7,                               EFUSE_BLK1, 108,   6, [] SPI_PAD_configure D7
WAFER_VERSION_MINOR_LO,                          EFUSE_BLK1, 114,   3, [] WAFER_VERSION_MINOR least significant bits
PKG_VERSION,                                     EFUSE_BLK1, 117,   3, [] Package version
BLK_VERSION_MINOR,                               EFUSE_BLK1, 120,   3, [] BLK_VERSION_MINOR
FLASH_CAP,                                       EFUSE_BLK1, 123,   3, [] Flash capacity {0: "None"; 1: "8M"; 2: "4M"}
FLASH_TEMP,                                      EFUSE_BLK1, 126,   2, [] Flash temperature {0: "None"; 1: "105C"; 2: "85C"}
FLASH_VENDOR,                                    EFUSE_BLK1, 128,   3, [] Flash vendor {0: "None"; 1: "XMC"; 2: "GD"; 3: "FM"; 4: "TT"; 5: "BY"}
PSRAM_CAP,                                       EFUSE_BLK1, 131,   2, [] PSRAM capacity {0: "None"; 1: "8M"; 2: "2M"}
PSRAM_TEMP,                                      EFUSE_BLK1, 133,   2, [] PSRAM temperature {0: "None"; 1: "105C"; 2: "85C"}
PSRAM_VENDOR,                                    EFUSE_BLK1, 135,   2, [] PSRAM vendor {0: "None"; 1: "AP_3v3"; 2: "AP_1v8"}
K_RTC_LDO,                                       EFUSE_BLK1, 141,   7, [] BLOCK1 K_RTC_LDO
K_DIG_LDO,                                       EFUSE_BLK1, 148,   7, [] BLOCK1 K_DIG_LDO
V_RTC_DBIAS20,                                   EFUSE_BLK1, 155,   8, [] BLOCK1 voltage of rtc dbias20
V_DIG_DBIAS20,                                   EFUSE_BLK1, 163,   8, [] BLOCK1 voltage of digital dbias20
DIG_DBIAS_HVT,                                   EFUSE_BLK1, 171,   5, [] BLOCK1 digital dbias when hvt
WAFER_VERSION_MINOR_HI,                          EFUSE_BLK1, 183,   1, [] WAFER_VERSION_MINOR most significant bit
WAFER_VERSION_MAJOR,                             EFUSE_BLK1, 184,   2, [] WAFER_VERSION_MAJOR
ADC2_CAL_VOL_ATTEN3,                             EFUSE_BLK1, 186,   6, [] ADC2 calibration voltage at atten3
OPTIONAL_UNIQUE_ID,                              EFUSE_BLK2,   0, 128, [] Optional unique 128-bit ID
BLK_VERSION_MAJOR,                               EFUSE_BLK2, 128,   2, [] BLK_VERSION_MAJOR of BLOCK2 {0: "No calib"; 1: "ADC calib V1"}
TEMP_CALIB,                                      EFUSE_BLK2, 132,   9, [] Temperature calibration data
OCODE,                                           EFUSE_BLK2, 141,   8, [] ADC OCode
ADC1_INIT_CODE_ATTEN0,                           EFUSE_BLK2, 149,   8, [] ADC1 init code at atten0
ADC1_INIT_CODE_ATTEN1,                           EFUSE_BLK2, 157,   6, [] ADC1 init code at atten1
ADC1_INIT_CODE_ATTEN2,                           EFUSE_BLK2, 163,   6, [] ADC1 init code at atten2
ADC1_INIT_CODE_ATTEN3,                           EFUSE_BLK2, 169,   6, [] ADC1 init code at atten3
ADC2_INIT_CODE_ATTEN0,                           EFUSE_BLK2, 175,   8, [] ADC2 init code at atten0
ADC2_INIT_CODE_ATTEN1,                           EFUSE_BLK2, 183,   6, [] ADC2 init code at atten1
ADC2_INIT_CODE_ATTEN2,                           EFUSE_BLK2, 189,   6, [] ADC2 init code at atten2
ADC2_INIT_CODE_ATTEN3,                           EFUSE_BLK2, 195,   6, [] ADC2 init code at atten3
ADC1_CAL_VOL_ATTEN0,                             EFUSE_BLK2, 201,   8, [] ADC1 calibration voltage at atten0
ADC1_CAL_VOL_ATTEN1,                             EFUSE_BLK2, 209,   8, [] ADC1 calibration voltage at atten1
ADC1_CAL_VOL_ATTEN2,                             EFUSE_BLK2, 217,   8, [] ADC1 calibration voltage at atten2
ADC1_CAL_VOL_ATTEN3,                             EFUSE_BLK2, 225,   8, [] ADC1 calibration voltage at atten3
ADC2_CAL_VOL_ATTEN0,                             EFUSE_BLK2, 233,   8, [] ADC2 calibration voltage at atten0
ADC2_CAL_VOL_ATTEN1,                             EFUSE_BLK2, 241,   7, [] ADC2 calibration voltage at atten1
ADC2_CAL_VOL_ATTEN2,                             EFUSE_BLK2, 248,   7, [] ADC2 calibration voltage at atten2
USER_DATA,                                       EFUSE_BLK3,   0, 256, [BLOCK_USR_DATA] User data
USER_DATA.MAC_CUSTOM,                            EFUSE_BLK3, 200,  48, [MAC_CUSTOM CUSTOM_MAC] Custom MAC
KEY0,                                            EFUSE_BLK4,   0, 256, [BLOCK_KEY0] Key0 or user data
KEY1,                                            EFUSE_BLK5,   0, 256, [BLOCK_KEY1] Key1 or user data
KEY2,                                            EFUSE_BLK6,   0, 256, [BLOCK_KEY2] Key2 or user data
KEY3,                                            EFUSE_BLK7,   0, 256, [BLOCK_KEY3] Key3 or user data
KEY4,                                            EFUSE_BLK8,   0, 256, [BLOCK_KEY4] Key4 or user data
KEY5,                                            EFUSE_BLK9,   0, 256, [BLOCK_KEY5] Key5 or user data
SYS_DATA_PART2,                                  EFUSE_BLK10,   0, 256, [BLOCK_SYS_DATA2] System data part 2 (reserved)
