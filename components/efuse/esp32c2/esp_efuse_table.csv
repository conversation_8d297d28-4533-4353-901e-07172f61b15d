
# field_name,       |    efuse_block, | bit_start, | bit_count, |comment #
#                   |    (EFUSE_BLK0  | (0..255)   | (1-256)    |        #
#                   |     EFUSE_BLK1  |            |            |        #
#                   |        ...)     |            |            |        #
##########################################################################
# !!!!!!!!!!! #
# After editing this file, run the command manually "idf.py efuse-common-table"
# this will generate new source files, next rebuild all the sources.
# !!!!!!!!!!! #

# This file was generated by regtools.py based on the efuses.yaml file with the version: 897499b0349a608b895d467abbcf006b

WR_DIS,                                          EFUSE_BLK0,   0,   8, [] Disable programming of individual eFuses
WR_DIS.RD_DIS,                                   EFUSE_BLK0,   0,   1, [] wr_dis of RD_DIS
WR_DIS.WDT_DELAY_SEL,                            EFUSE_BLK0,   1,   1, [] wr_dis of WDT_DELAY_SEL
WR_DIS.DIS_PAD_JTAG,                             EFUSE_BLK0,   1,   1, [] wr_dis of DIS_PAD_JTAG
WR_DIS.DIS_DOWNLOAD_ICACHE,                      EFUSE_BLK0,   1,   1, [] wr_dis of DIS_DOWNLOAD_ICACHE
WR_DIS.DIS_DOWNLOAD_MANUAL_ENCRYPT,              EFUSE_BLK0,   2,   1, [] wr_dis of DIS_DOWNLOAD_MANUAL_ENCRYPT
WR_DIS.SPI_BOOT_CRYPT_CNT,                       EFUSE_BLK0,   2,   1, [] wr_dis of SPI_BOOT_CRYPT_CNT
WR_DIS.XTS_KEY_LENGTH_256,                       EFUSE_BLK0,   2,   1, [] wr_dis of XTS_KEY_LENGTH_256
WR_DIS.SECURE_BOOT_EN,                           EFUSE_BLK0,   2,   1, [] wr_dis of SECURE_BOOT_EN
WR_DIS.UART_PRINT_CONTROL,                       EFUSE_BLK0,   3,   1, [] wr_dis of UART_PRINT_CONTROL
WR_DIS.FORCE_SEND_RESUME,                        EFUSE_BLK0,   3,   1, [] wr_dis of FORCE_SEND_RESUME
WR_DIS.DIS_DOWNLOAD_MODE,                        EFUSE_BLK0,   3,   1, [] wr_dis of DIS_DOWNLOAD_MODE
WR_DIS.DIS_DIRECT_BOOT,                          EFUSE_BLK0,   3,   1, [] wr_dis of DIS_DIRECT_BOOT
WR_DIS.ENABLE_SECURITY_DOWNLOAD,                 EFUSE_BLK0,   3,   1, [] wr_dis of ENABLE_SECURITY_DOWNLOAD
WR_DIS.FLASH_TPUW,                               EFUSE_BLK0,   3,   1, [] wr_dis of FLASH_TPUW
WR_DIS.SECURE_VERSION,                           EFUSE_BLK0,   4,   1, [] wr_dis of SECURE_VERSION
WR_DIS.CUSTOM_MAC_USED,                          EFUSE_BLK0,   4,   1, [WR_DIS.ENABLE_CUSTOM_MAC] wr_dis of CUSTOM_MAC_USED
WR_DIS.DISABLE_WAFER_VERSION_MAJOR,              EFUSE_BLK0,   4,   1, [] wr_dis of DISABLE_WAFER_VERSION_MAJOR
WR_DIS.DISABLE_BLK_VERSION_MAJOR,                EFUSE_BLK0,   4,   1, [] wr_dis of DISABLE_BLK_VERSION_MAJOR
WR_DIS.CUSTOM_MAC,                               EFUSE_BLK0,   5,   1, [WR_DIS.MAC_CUSTOM WR_DIS.USER_DATA_MAC_CUSTOM] wr_dis of CUSTOM_MAC
WR_DIS.MAC,                                      EFUSE_BLK0,   6,   1, [WR_DIS.MAC_FACTORY] wr_dis of MAC
WR_DIS.WAFER_VERSION_MINOR,                      EFUSE_BLK0,   6,   1, [] wr_dis of WAFER_VERSION_MINOR
WR_DIS.WAFER_VERSION_MAJOR,                      EFUSE_BLK0,   6,   1, [] wr_dis of WAFER_VERSION_MAJOR
WR_DIS.PKG_VERSION,                              EFUSE_BLK0,   6,   1, [] wr_dis of PKG_VERSION
WR_DIS.BLK_VERSION_MINOR,                        EFUSE_BLK0,   6,   1, [] wr_dis of BLK_VERSION_MINOR
WR_DIS.BLK_VERSION_MAJOR,                        EFUSE_BLK0,   6,   1, [] wr_dis of BLK_VERSION_MAJOR
WR_DIS.OCODE,                                    EFUSE_BLK0,   6,   1, [] wr_dis of OCODE
WR_DIS.TEMP_CALIB,                               EFUSE_BLK0,   6,   1, [] wr_dis of TEMP_CALIB
WR_DIS.ADC1_INIT_CODE_ATTEN0,                    EFUSE_BLK0,   6,   1, [] wr_dis of ADC1_INIT_CODE_ATTEN0
WR_DIS.ADC1_INIT_CODE_ATTEN3,                    EFUSE_BLK0,   6,   1, [] wr_dis of ADC1_INIT_CODE_ATTEN3
WR_DIS.ADC1_CAL_VOL_ATTEN0,                      EFUSE_BLK0,   6,   1, [] wr_dis of ADC1_CAL_VOL_ATTEN0
WR_DIS.ADC1_CAL_VOL_ATTEN3,                      EFUSE_BLK0,   6,   1, [] wr_dis of ADC1_CAL_VOL_ATTEN3
WR_DIS.DIG_DBIAS_HVT,                            EFUSE_BLK0,   6,   1, [] wr_dis of DIG_DBIAS_HVT
WR_DIS.DIG_LDO_SLP_DBIAS2,                       EFUSE_BLK0,   6,   1, [] wr_dis of DIG_LDO_SLP_DBIAS2
WR_DIS.DIG_LDO_SLP_DBIAS26,                      EFUSE_BLK0,   6,   1, [] wr_dis of DIG_LDO_SLP_DBIAS26
WR_DIS.DIG_LDO_ACT_DBIAS26,                      EFUSE_BLK0,   6,   1, [] wr_dis of DIG_LDO_ACT_DBIAS26
WR_DIS.DIG_LDO_ACT_STEPD10,                      EFUSE_BLK0,   6,   1, [] wr_dis of DIG_LDO_ACT_STEPD10
WR_DIS.RTC_LDO_SLP_DBIAS13,                      EFUSE_BLK0,   6,   1, [] wr_dis of RTC_LDO_SLP_DBIAS13
WR_DIS.RTC_LDO_SLP_DBIAS29,                      EFUSE_BLK0,   6,   1, [] wr_dis of RTC_LDO_SLP_DBIAS29
WR_DIS.RTC_LDO_SLP_DBIAS31,                      EFUSE_BLK0,   6,   1, [] wr_dis of RTC_LDO_SLP_DBIAS31
WR_DIS.RTC_LDO_ACT_DBIAS31,                      EFUSE_BLK0,   6,   1, [] wr_dis of RTC_LDO_ACT_DBIAS31
WR_DIS.RTC_LDO_ACT_DBIAS13,                      EFUSE_BLK0,   6,   1, [] wr_dis of RTC_LDO_ACT_DBIAS13
WR_DIS.ADC_CALIBRATION_3,                        EFUSE_BLK0,   6,   1, [] wr_dis of ADC_CALIBRATION_3
WR_DIS.BLOCK_KEY0,                               EFUSE_BLK0,   7,   1, [WR_DIS.KEY0] wr_dis of BLOCK_KEY0
RD_DIS,                                          EFUSE_BLK0,  32,   2, [] Disable reading from BlOCK3
RD_DIS.KEY0,                                     EFUSE_BLK0,  32,   2, [] Read protection for EFUSE_BLK3. KEY0
RD_DIS.KEY0.LOW,                                 EFUSE_BLK0,  32,   1, [] Read protection for EFUSE_BLK3. KEY0 lower 128-bit key
RD_DIS.KEY0.HI,                                  EFUSE_BLK0,  33,   1, [] Read protection for EFUSE_BLK3. KEY0 higher 128-bit key
WDT_DELAY_SEL,                                   EFUSE_BLK0,  34,   2, [] RTC watchdog timeout threshold; in unit of slow clock cycle {0: "40000"; 1: "80000"; 2: "160000"; 3: "320000"}
DIS_PAD_JTAG,                                    EFUSE_BLK0,  36,   1, [] Set this bit to disable pad jtag
DIS_DOWNLOAD_ICACHE,                             EFUSE_BLK0,  37,   1, [] The bit be set to disable icache in download mode
DIS_DOWNLOAD_MANUAL_ENCRYPT,                     EFUSE_BLK0,  38,   1, [] The bit be set to disable manual encryption
SPI_BOOT_CRYPT_CNT,                              EFUSE_BLK0,  39,   3, [] Enables flash encryption when 1 or 3 bits are set and disables otherwise {0: "Disable"; 1: "Enable"; 3: "Disable"; 7: "Enable"}
XTS_KEY_LENGTH_256,                              EFUSE_BLK0,  42,   1, [] Flash encryption key length {0: "128 bits key"; 1: "256 bits key"}
UART_PRINT_CONTROL,                              EFUSE_BLK0,  43,   2, [] Set the default UARTboot message output mode {0: "Enable"; 1: "Enable when GPIO8 is low at reset"; 2: "Enable when GPIO8 is high at reset"; 3: "Disable"}
FORCE_SEND_RESUME,                               EFUSE_BLK0,  45,   1, [] Set this bit to force ROM code to send a resume command during SPI boot
DIS_DOWNLOAD_MODE,                               EFUSE_BLK0,  46,   1, [] Set this bit to disable download mode (boot_mode[3:0] = 0; 1; 2; 4; 5; 6; 7)
DIS_DIRECT_BOOT,                                 EFUSE_BLK0,  47,   1, [] This bit set means disable direct_boot mode
ENABLE_SECURITY_DOWNLOAD,                        EFUSE_BLK0,  48,   1, [] Set this bit to enable secure UART download mode
FLASH_TPUW,                                      EFUSE_BLK0,  49,   4, [] Configures flash waiting time after power-up; in unit of ms. If the value is less than 15; the waiting time is the configurable value.  Otherwise; the waiting time is twice the configurable value
SECURE_BOOT_EN,                                  EFUSE_BLK0,  53,   1, [] The bit be set to enable secure boot
SECURE_VERSION,                                  EFUSE_BLK0,  54,   4, [] Secure version for anti-rollback
CUSTOM_MAC_USED,                                 EFUSE_BLK0,  58,   1, [ENABLE_CUSTOM_MAC] True if MAC_CUSTOM is burned
DISABLE_WAFER_VERSION_MAJOR,                     EFUSE_BLK0,  59,   1, [] Disables check of wafer version major
DISABLE_BLK_VERSION_MAJOR,                       EFUSE_BLK0,  60,   1, [] Disables check of blk version major
USER_DATA,                                       EFUSE_BLK1,   0,  88, [] User data block
USER_DATA.MAC_CUSTOM,                            EFUSE_BLK1,   0,  48, [MAC_CUSTOM CUSTOM_MAC] Custom MAC address
MAC,                                             EFUSE_BLK2,  40,   8, [MAC_FACTORY] MAC address
,                                                EFUSE_BLK2,  32,   8, [MAC_FACTORY] MAC address
,                                                EFUSE_BLK2,  24,   8, [MAC_FACTORY] MAC address
,                                                EFUSE_BLK2,  16,   8, [MAC_FACTORY] MAC address
,                                                EFUSE_BLK2,   8,   8, [MAC_FACTORY] MAC address
,                                                EFUSE_BLK2,   0,   8, [MAC_FACTORY] MAC address
WAFER_VERSION_MINOR,                             EFUSE_BLK2,  48,   4, [] WAFER_VERSION_MINOR
WAFER_VERSION_MAJOR,                             EFUSE_BLK2,  52,   2, [] WAFER_VERSION_MAJOR
PKG_VERSION,                                     EFUSE_BLK2,  54,   3, [] EFUSE_PKG_VERSION
BLK_VERSION_MINOR,                               EFUSE_BLK2,  57,   3, [] Minor version of BLOCK2 {0: "No calib"; 1: "With calib"}
BLK_VERSION_MAJOR,                               EFUSE_BLK2,  60,   2, [] Major version of BLOCK2
OCODE,                                           EFUSE_BLK2,  62,   7, [] OCode
TEMP_CALIB,                                      EFUSE_BLK2,  69,   9, [] Temperature calibration data
ADC1_INIT_CODE_ATTEN0,                           EFUSE_BLK2,  78,   8, [] ADC1 init code at atten0
ADC1_INIT_CODE_ATTEN3,                           EFUSE_BLK2,  86,   5, [] ADC1 init code at atten3
ADC1_CAL_VOL_ATTEN0,                             EFUSE_BLK2,  91,   8, [] ADC1 calibration voltage at atten0
ADC1_CAL_VOL_ATTEN3,                             EFUSE_BLK2,  99,   6, [] ADC1 calibration voltage at atten3
DIG_DBIAS_HVT,                                   EFUSE_BLK2, 105,   5, [] BLOCK2 digital dbias when hvt
DIG_LDO_SLP_DBIAS2,                              EFUSE_BLK2, 110,   7, [] BLOCK2 DIG_LDO_DBG0_DBIAS2
DIG_LDO_SLP_DBIAS26,                             EFUSE_BLK2, 117,   8, [] BLOCK2 DIG_LDO_DBG0_DBIAS26
DIG_LDO_ACT_DBIAS26,                             EFUSE_BLK2, 125,   6, [] BLOCK2 DIG_LDO_ACT_DBIAS26
DIG_LDO_ACT_STEPD10,                             EFUSE_BLK2, 131,   4, [] BLOCK2 DIG_LDO_ACT_STEPD10
RTC_LDO_SLP_DBIAS13,                             EFUSE_BLK2, 135,   7, [] BLOCK2 DIG_LDO_SLP_DBIAS13
RTC_LDO_SLP_DBIAS29,                             EFUSE_BLK2, 142,   9, [] BLOCK2 DIG_LDO_SLP_DBIAS29
RTC_LDO_SLP_DBIAS31,                             EFUSE_BLK2, 151,   6, [] BLOCK2 DIG_LDO_SLP_DBIAS31
RTC_LDO_ACT_DBIAS31,                             EFUSE_BLK2, 157,   6, [] BLOCK2 DIG_LDO_ACT_DBIAS31
RTC_LDO_ACT_DBIAS13,                             EFUSE_BLK2, 163,   8, [] BLOCK2 DIG_LDO_ACT_DBIAS13
ADC_CALIBRATION_3,                               EFUSE_BLK2, 192,  11, [] Store the bit [86:96] of ADC calibration data
KEY0,                                            EFUSE_BLK3,   0, 256, [BLOCK_KEY0] BLOCK_BLOCK_KEY0 - 256-bits. 256-bit key of Flash Encryption
KEY0.FE_256BIT,                                  EFUSE_BLK3,   0, 256, [] 256bit FE key
KEY0.FE_128BIT,                                  EFUSE_BLK3,   0, 128, [] 128bit FE key
KEY0.SB_128BIT,                                  EFUSE_BLK3, 128, 128, [] 128bit SB key
