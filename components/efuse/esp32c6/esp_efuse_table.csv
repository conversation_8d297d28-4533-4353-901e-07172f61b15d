
# field_name,       |    efuse_block, | bit_start, | bit_count, |comment #
#                   |    (EFUSE_BLK0  | (0..255)   | (1-256)    |        #
#                   |     EFUSE_BLK1  |            |            |        #
#                   |        ...)     |            |            |        #
##########################################################################
# !!!!!!!!!!! #
# After editing this file, run the command manually "idf.py efuse-common-table"
# this will generate new source files, next rebuild all the sources.
# !!!!!!!!!!! #

# This file was generated by regtools.py based on the efuses.yaml file with the version: df46b69f0ed3913114ba53d3a0b2b843

WR_DIS,                                          EFUSE_BLK0,   0,  32, [] Disable programming of individual eFuses
WR_DIS.RD_DIS,                                   EFUSE_BLK0,   0,   1, [] wr_dis of RD_DIS
WR_DIS.CRYPT_DPA_ENABLE,                         EFUSE_BLK0,   1,   1, [] wr_dis of CRYPT_DPA_ENABLE
WR_DIS.SWAP_UART_SDIO_EN,                        EFUSE_BLK0,   2,   1, [] wr_dis of SWAP_UART_SDIO_EN
WR_DIS.DIS_ICACHE,                               EFUSE_BLK0,   2,   1, [] wr_dis of DIS_ICACHE
WR_DIS.DIS_USB_JTAG,                             EFUSE_BLK0,   2,   1, [] wr_dis of DIS_USB_JTAG
WR_DIS.DIS_DOWNLOAD_ICACHE,                      EFUSE_BLK0,   2,   1, [] wr_dis of DIS_DOWNLOAD_ICACHE
WR_DIS.DIS_USB_SERIAL_JTAG,                      EFUSE_BLK0,   2,   1, [] wr_dis of DIS_USB_SERIAL_JTAG
WR_DIS.DIS_FORCE_DOWNLOAD,                       EFUSE_BLK0,   2,   1, [] wr_dis of DIS_FORCE_DOWNLOAD
WR_DIS.DIS_TWAI,                                 EFUSE_BLK0,   2,   1, [WR_DIS.DIS_CAN] wr_dis of DIS_TWAI
WR_DIS.JTAG_SEL_ENABLE,                          EFUSE_BLK0,   2,   1, [] wr_dis of JTAG_SEL_ENABLE
WR_DIS.DIS_PAD_JTAG,                             EFUSE_BLK0,   2,   1, [] wr_dis of DIS_PAD_JTAG
WR_DIS.DIS_DOWNLOAD_MANUAL_ENCRYPT,              EFUSE_BLK0,   2,   1, [] wr_dis of DIS_DOWNLOAD_MANUAL_ENCRYPT
WR_DIS.WDT_DELAY_SEL,                            EFUSE_BLK0,   3,   1, [] wr_dis of WDT_DELAY_SEL
WR_DIS.SPI_BOOT_CRYPT_CNT,                       EFUSE_BLK0,   4,   1, [] wr_dis of SPI_BOOT_CRYPT_CNT
WR_DIS.SECURE_BOOT_KEY_REVOKE0,                  EFUSE_BLK0,   5,   1, [] wr_dis of SECURE_BOOT_KEY_REVOKE0
WR_DIS.SECURE_BOOT_KEY_REVOKE1,                  EFUSE_BLK0,   6,   1, [] wr_dis of SECURE_BOOT_KEY_REVOKE1
WR_DIS.SECURE_BOOT_KEY_REVOKE2,                  EFUSE_BLK0,   7,   1, [] wr_dis of SECURE_BOOT_KEY_REVOKE2
WR_DIS.KEY_PURPOSE_0,                            EFUSE_BLK0,   8,   1, [WR_DIS.KEY0_PURPOSE] wr_dis of KEY_PURPOSE_0
WR_DIS.KEY_PURPOSE_1,                            EFUSE_BLK0,   9,   1, [WR_DIS.KEY1_PURPOSE] wr_dis of KEY_PURPOSE_1
WR_DIS.KEY_PURPOSE_2,                            EFUSE_BLK0,  10,   1, [WR_DIS.KEY2_PURPOSE] wr_dis of KEY_PURPOSE_2
WR_DIS.KEY_PURPOSE_3,                            EFUSE_BLK0,  11,   1, [WR_DIS.KEY3_PURPOSE] wr_dis of KEY_PURPOSE_3
WR_DIS.KEY_PURPOSE_4,                            EFUSE_BLK0,  12,   1, [WR_DIS.KEY4_PURPOSE] wr_dis of KEY_PURPOSE_4
WR_DIS.KEY_PURPOSE_5,                            EFUSE_BLK0,  13,   1, [WR_DIS.KEY5_PURPOSE] wr_dis of KEY_PURPOSE_5
WR_DIS.SEC_DPA_LEVEL,                            EFUSE_BLK0,  14,   1, [WR_DIS.DPA_SEC_LEVEL] wr_dis of SEC_DPA_LEVEL
WR_DIS.SECURE_BOOT_EN,                           EFUSE_BLK0,  15,   1, [] wr_dis of SECURE_BOOT_EN
WR_DIS.SECURE_BOOT_AGGRESSIVE_REVOKE,            EFUSE_BLK0,  16,   1, [] wr_dis of SECURE_BOOT_AGGRESSIVE_REVOKE
WR_DIS.SPI_DOWNLOAD_MSPI_DIS,                    EFUSE_BLK0,  17,   1, [] wr_dis of SPI_DOWNLOAD_MSPI_DIS
WR_DIS.FLASH_TPUW,                               EFUSE_BLK0,  18,   1, [] wr_dis of FLASH_TPUW
WR_DIS.DIS_DOWNLOAD_MODE,                        EFUSE_BLK0,  18,   1, [] wr_dis of DIS_DOWNLOAD_MODE
WR_DIS.DIS_DIRECT_BOOT,                          EFUSE_BLK0,  18,   1, [] wr_dis of DIS_DIRECT_BOOT
WR_DIS.DIS_USB_SERIAL_JTAG_ROM_PRINT,            EFUSE_BLK0,  18,   1, [WR_DIS.DIS_USB_PRINT] wr_dis of DIS_USB_SERIAL_JTAG_ROM_PRINT
WR_DIS.DIS_USB_SERIAL_JTAG_DOWNLOAD_MODE,        EFUSE_BLK0,  18,   1, [] wr_dis of DIS_USB_SERIAL_JTAG_DOWNLOAD_MODE
WR_DIS.ENABLE_SECURITY_DOWNLOAD,                 EFUSE_BLK0,  18,   1, [] wr_dis of ENABLE_SECURITY_DOWNLOAD
WR_DIS.UART_PRINT_CONTROL,                       EFUSE_BLK0,  18,   1, [] wr_dis of UART_PRINT_CONTROL
WR_DIS.FORCE_SEND_RESUME,                        EFUSE_BLK0,  18,   1, [] wr_dis of FORCE_SEND_RESUME
WR_DIS.SECURE_VERSION,                           EFUSE_BLK0,  18,   1, [] wr_dis of SECURE_VERSION
WR_DIS.SECURE_BOOT_DISABLE_FAST_WAKE,            EFUSE_BLK0,  19,   1, [] wr_dis of SECURE_BOOT_DISABLE_FAST_WAKE
WR_DIS.DISABLE_WAFER_VERSION_MAJOR,              EFUSE_BLK0,  19,   1, [] wr_dis of DISABLE_WAFER_VERSION_MAJOR
WR_DIS.DISABLE_BLK_VERSION_MAJOR,                EFUSE_BLK0,  19,   1, [] wr_dis of DISABLE_BLK_VERSION_MAJOR
WR_DIS.BLK1,                                     EFUSE_BLK0,  20,   1, [] wr_dis of BLOCK1
WR_DIS.MAC,                                      EFUSE_BLK0,  20,   1, [WR_DIS.MAC_FACTORY] wr_dis of MAC
WR_DIS.MAC_EXT,                                  EFUSE_BLK0,  20,   1, [] wr_dis of MAC_EXT
WR_DIS.ACTIVE_HP_DBIAS,                          EFUSE_BLK0,  20,   1, [] wr_dis of ACTIVE_HP_DBIAS
WR_DIS.ACTIVE_LP_DBIAS,                          EFUSE_BLK0,  20,   1, [] wr_dis of ACTIVE_LP_DBIAS
WR_DIS.LSLP_HP_DBG,                              EFUSE_BLK0,  20,   1, [] wr_dis of LSLP_HP_DBG
WR_DIS.LSLP_HP_DBIAS,                            EFUSE_BLK0,  20,   1, [] wr_dis of LSLP_HP_DBIAS
WR_DIS.DSLP_LP_DBG,                              EFUSE_BLK0,  20,   1, [] wr_dis of DSLP_LP_DBG
WR_DIS.DSLP_LP_DBIAS,                            EFUSE_BLK0,  20,   1, [] wr_dis of DSLP_LP_DBIAS
WR_DIS.DBIAS_VOL_GAP,                            EFUSE_BLK0,  20,   1, [] wr_dis of DBIAS_VOL_GAP
WR_DIS.WAFER_VERSION_MINOR,                      EFUSE_BLK0,  20,   1, [] wr_dis of WAFER_VERSION_MINOR
WR_DIS.WAFER_VERSION_MAJOR,                      EFUSE_BLK0,  20,   1, [] wr_dis of WAFER_VERSION_MAJOR
WR_DIS.PKG_VERSION,                              EFUSE_BLK0,  20,   1, [] wr_dis of PKG_VERSION
WR_DIS.BLK_VERSION_MINOR,                        EFUSE_BLK0,  20,   1, [] wr_dis of BLK_VERSION_MINOR
WR_DIS.BLK_VERSION_MAJOR,                        EFUSE_BLK0,  20,   1, [] wr_dis of BLK_VERSION_MAJOR
WR_DIS.FLASH_CAP,                                EFUSE_BLK0,  20,   1, [] wr_dis of FLASH_CAP
WR_DIS.FLASH_TEMP,                               EFUSE_BLK0,  20,   1, [] wr_dis of FLASH_TEMP
WR_DIS.FLASH_VENDOR,                             EFUSE_BLK0,  20,   1, [] wr_dis of FLASH_VENDOR
WR_DIS.SYS_DATA_PART1,                           EFUSE_BLK0,  21,   1, [] wr_dis of BLOCK2
WR_DIS.OPTIONAL_UNIQUE_ID,                       EFUSE_BLK0,  21,   1, [] wr_dis of OPTIONAL_UNIQUE_ID
WR_DIS.TEMP_CALIB,                               EFUSE_BLK0,  21,   1, [] wr_dis of TEMP_CALIB
WR_DIS.OCODE,                                    EFUSE_BLK0,  21,   1, [] wr_dis of OCODE
WR_DIS.ADC1_INIT_CODE_ATTEN0,                    EFUSE_BLK0,  21,   1, [] wr_dis of ADC1_INIT_CODE_ATTEN0
WR_DIS.ADC1_INIT_CODE_ATTEN1,                    EFUSE_BLK0,  21,   1, [] wr_dis of ADC1_INIT_CODE_ATTEN1
WR_DIS.ADC1_INIT_CODE_ATTEN2,                    EFUSE_BLK0,  21,   1, [] wr_dis of ADC1_INIT_CODE_ATTEN2
WR_DIS.ADC1_INIT_CODE_ATTEN3,                    EFUSE_BLK0,  21,   1, [] wr_dis of ADC1_INIT_CODE_ATTEN3
WR_DIS.ADC1_CAL_VOL_ATTEN0,                      EFUSE_BLK0,  21,   1, [] wr_dis of ADC1_CAL_VOL_ATTEN0
WR_DIS.ADC1_CAL_VOL_ATTEN1,                      EFUSE_BLK0,  21,   1, [] wr_dis of ADC1_CAL_VOL_ATTEN1
WR_DIS.ADC1_CAL_VOL_ATTEN2,                      EFUSE_BLK0,  21,   1, [] wr_dis of ADC1_CAL_VOL_ATTEN2
WR_DIS.ADC1_CAL_VOL_ATTEN3,                      EFUSE_BLK0,  21,   1, [] wr_dis of ADC1_CAL_VOL_ATTEN3
WR_DIS.ADC1_INIT_CODE_ATTEN0_CH0,                EFUSE_BLK0,  21,   1, [] wr_dis of ADC1_INIT_CODE_ATTEN0_CH0
WR_DIS.ADC1_INIT_CODE_ATTEN0_CH1,                EFUSE_BLK0,  21,   1, [] wr_dis of ADC1_INIT_CODE_ATTEN0_CH1
WR_DIS.ADC1_INIT_CODE_ATTEN0_CH2,                EFUSE_BLK0,  21,   1, [] wr_dis of ADC1_INIT_CODE_ATTEN0_CH2
WR_DIS.ADC1_INIT_CODE_ATTEN0_CH3,                EFUSE_BLK0,  21,   1, [] wr_dis of ADC1_INIT_CODE_ATTEN0_CH3
WR_DIS.ADC1_INIT_CODE_ATTEN0_CH4,                EFUSE_BLK0,  21,   1, [] wr_dis of ADC1_INIT_CODE_ATTEN0_CH4
WR_DIS.ADC1_INIT_CODE_ATTEN0_CH5,                EFUSE_BLK0,  21,   1, [] wr_dis of ADC1_INIT_CODE_ATTEN0_CH5
WR_DIS.ADC1_INIT_CODE_ATTEN0_CH6,                EFUSE_BLK0,  21,   1, [] wr_dis of ADC1_INIT_CODE_ATTEN0_CH6
WR_DIS.BLOCK_USR_DATA,                           EFUSE_BLK0,  22,   1, [WR_DIS.USER_DATA] wr_dis of BLOCK_USR_DATA
WR_DIS.CUSTOM_MAC,                               EFUSE_BLK0,  22,   1, [WR_DIS.MAC_CUSTOM WR_DIS.USER_DATA_MAC_CUSTOM] wr_dis of CUSTOM_MAC
WR_DIS.BLOCK_KEY0,                               EFUSE_BLK0,  23,   1, [WR_DIS.KEY0] wr_dis of BLOCK_KEY0
WR_DIS.BLOCK_KEY1,                               EFUSE_BLK0,  24,   1, [WR_DIS.KEY1] wr_dis of BLOCK_KEY1
WR_DIS.BLOCK_KEY2,                               EFUSE_BLK0,  25,   1, [WR_DIS.KEY2] wr_dis of BLOCK_KEY2
WR_DIS.BLOCK_KEY3,                               EFUSE_BLK0,  26,   1, [WR_DIS.KEY3] wr_dis of BLOCK_KEY3
WR_DIS.BLOCK_KEY4,                               EFUSE_BLK0,  27,   1, [WR_DIS.KEY4] wr_dis of BLOCK_KEY4
WR_DIS.BLOCK_KEY5,                               EFUSE_BLK0,  28,   1, [WR_DIS.KEY5] wr_dis of BLOCK_KEY5
WR_DIS.BLOCK_SYS_DATA2,                          EFUSE_BLK0,  29,   1, [WR_DIS.SYS_DATA_PART2] wr_dis of BLOCK_SYS_DATA2
WR_DIS.USB_EXCHG_PINS,                           EFUSE_BLK0,  30,   1, [] wr_dis of USB_EXCHG_PINS
WR_DIS.VDD_SPI_AS_GPIO,                          EFUSE_BLK0,  30,   1, [] wr_dis of VDD_SPI_AS_GPIO
WR_DIS.SOFT_DIS_JTAG,                            EFUSE_BLK0,  31,   1, [] wr_dis of SOFT_DIS_JTAG
RD_DIS,                                          EFUSE_BLK0,  32,   7, [] Disable reading from BlOCK4-10
RD_DIS.BLOCK_KEY0,                               EFUSE_BLK0,  32,   1, [RD_DIS.KEY0] rd_dis of BLOCK_KEY0
RD_DIS.BLOCK_KEY1,                               EFUSE_BLK0,  33,   1, [RD_DIS.KEY1] rd_dis of BLOCK_KEY1
RD_DIS.BLOCK_KEY2,                               EFUSE_BLK0,  34,   1, [RD_DIS.KEY2] rd_dis of BLOCK_KEY2
RD_DIS.BLOCK_KEY3,                               EFUSE_BLK0,  35,   1, [RD_DIS.KEY3] rd_dis of BLOCK_KEY3
RD_DIS.BLOCK_KEY4,                               EFUSE_BLK0,  36,   1, [RD_DIS.KEY4] rd_dis of BLOCK_KEY4
RD_DIS.BLOCK_KEY5,                               EFUSE_BLK0,  37,   1, [RD_DIS.KEY5] rd_dis of BLOCK_KEY5
RD_DIS.BLOCK_SYS_DATA2,                          EFUSE_BLK0,  38,   1, [RD_DIS.SYS_DATA_PART2] rd_dis of BLOCK_SYS_DATA2
SWAP_UART_SDIO_EN,                               EFUSE_BLK0,  39,   1, [] Represents whether pad of uart and sdio is swapped or not. 1: swapped. 0: not swapped
DIS_ICACHE,                                      EFUSE_BLK0,  40,   1, [] Represents whether icache is disabled or enabled. 1: disabled. 0: enabled
DIS_USB_JTAG,                                    EFUSE_BLK0,  41,   1, [] Represents whether the function of usb switch to jtag is disabled or enabled. 1: disabled. 0: enabled
DIS_DOWNLOAD_ICACHE,                             EFUSE_BLK0,  42,   1, [] Represents whether icache is disabled or enabled in Download mode. 1: disabled. 0: enabled
DIS_USB_SERIAL_JTAG,                             EFUSE_BLK0,  43,   1, [] Represents whether USB-Serial-JTAG is disabled or enabled. 1: disabled. 0: enabled
DIS_FORCE_DOWNLOAD,                              EFUSE_BLK0,  44,   1, [] Represents whether the function that forces chip into download mode is disabled or enabled. 1: disabled. 0: enabled
SPI_DOWNLOAD_MSPI_DIS,                           EFUSE_BLK0,  45,   1, [] Represents whether SPI0 controller during boot_mode_download is disabled or enabled. 1: disabled. 0: enabled
DIS_TWAI,                                        EFUSE_BLK0,  46,   1, [DIS_CAN] Represents whether TWAI function is disabled or enabled. 1: disabled. 0: enabled
JTAG_SEL_ENABLE,                                 EFUSE_BLK0,  47,   1, [] Represents whether the selection between usb_to_jtag and pad_to_jtag through strapping gpio15 when both EFUSE_DIS_PAD_JTAG and EFUSE_DIS_USB_JTAG are equal to 0 is enabled or disabled. 1: enabled. 0: disabled
SOFT_DIS_JTAG,                                   EFUSE_BLK0,  48,   3, [] Represents whether JTAG is disabled in soft way. Odd number: disabled. Even number: enabled
DIS_PAD_JTAG,                                    EFUSE_BLK0,  51,   1, [] Represents whether JTAG is disabled in the hard way(permanently). 1: disabled. 0: enabled
DIS_DOWNLOAD_MANUAL_ENCRYPT,                     EFUSE_BLK0,  52,   1, [] Represents whether flash encrypt function is disabled or enabled(except in SPI boot mode). 1: disabled. 0: enabled
USB_EXCHG_PINS,                                  EFUSE_BLK0,  57,   1, [] Represents whether the D+ and D- pins is exchanged. 1: exchanged. 0: not exchanged
VDD_SPI_AS_GPIO,                                 EFUSE_BLK0,  58,   1, [] Represents whether vdd spi pin is functioned as gpio. 1: functioned. 0: not functioned
WDT_DELAY_SEL,                                   EFUSE_BLK0,  80,   2, [] Represents whether RTC watchdog timeout threshold is selected at startup. 1: selected. 0: not selected
SPI_BOOT_CRYPT_CNT,                              EFUSE_BLK0,  82,   3, [] Enables flash encryption when 1 or 3 bits are set and disables otherwise {0: "Disable"; 1: "Enable"; 3: "Disable"; 7: "Enable"}
SECURE_BOOT_KEY_REVOKE0,                         EFUSE_BLK0,  85,   1, [] Revoke 1st secure boot key
SECURE_BOOT_KEY_REVOKE1,                         EFUSE_BLK0,  86,   1, [] Revoke 2nd secure boot key
SECURE_BOOT_KEY_REVOKE2,                         EFUSE_BLK0,  87,   1, [] Revoke 3rd secure boot key
KEY_PURPOSE_0,                                   EFUSE_BLK0,  88,   4, [KEY0_PURPOSE] Represents the purpose of Key0
KEY_PURPOSE_1,                                   EFUSE_BLK0,  92,   4, [KEY1_PURPOSE] Represents the purpose of Key1
KEY_PURPOSE_2,                                   EFUSE_BLK0,  96,   4, [KEY2_PURPOSE] Represents the purpose of Key2
KEY_PURPOSE_3,                                   EFUSE_BLK0, 100,   4, [KEY3_PURPOSE] Represents the purpose of Key3
KEY_PURPOSE_4,                                   EFUSE_BLK0, 104,   4, [KEY4_PURPOSE] Represents the purpose of Key4
KEY_PURPOSE_5,                                   EFUSE_BLK0, 108,   4, [KEY5_PURPOSE] Represents the purpose of Key5
SEC_DPA_LEVEL,                                   EFUSE_BLK0, 112,   2, [DPA_SEC_LEVEL] Represents the spa secure level by configuring the clock random divide mode
CRYPT_DPA_ENABLE,                                EFUSE_BLK0, 114,   1, [] Represents whether anti-dpa attack is enabled. 1:enabled. 0: disabled
SECURE_BOOT_EN,                                  EFUSE_BLK0, 116,   1, [] Represents whether secure boot is enabled or disabled. 1: enabled. 0: disabled
SECURE_BOOT_AGGRESSIVE_REVOKE,                   EFUSE_BLK0, 117,   1, [] Represents whether revoking aggressive secure boot is enabled or disabled. 1: enabled. 0: disabled
FLASH_TPUW,                                      EFUSE_BLK0, 124,   4, [] Represents the flash waiting time after power-up; in unit of ms. When the value less than 15; the waiting time is the programmed value. Otherwise; the waiting time is 2 times the programmed value
DIS_DOWNLOAD_MODE,                               EFUSE_BLK0, 128,   1, [] Represents whether Download mode is disabled or enabled. 1: disabled. 0: enabled
DIS_DIRECT_BOOT,                                 EFUSE_BLK0, 129,   1, [] Represents whether direct boot mode is disabled or enabled. 1: disabled. 0: enabled
DIS_USB_SERIAL_JTAG_ROM_PRINT,                   EFUSE_BLK0, 130,   1, [DIS_USB_PRINT] Represents whether print from USB-Serial-JTAG is disabled or enabled. 1: disabled. 0: enabled
DIS_USB_SERIAL_JTAG_DOWNLOAD_MODE,               EFUSE_BLK0, 132,   1, [] Represents whether the USB-Serial-JTAG download function is disabled or enabled. 1: disabled. 0: enabled
ENABLE_SECURITY_DOWNLOAD,                        EFUSE_BLK0, 133,   1, [] Represents whether security download is enabled or disabled. 1: enabled. 0: disabled
UART_PRINT_CONTROL,                              EFUSE_BLK0, 134,   2, [] Set the default UARTboot message output mode {0: "Enable"; 1: "Enable when GPIO8 is low at reset"; 2: "Enable when GPIO8 is high at reset"; 3: "Disable"}
FORCE_SEND_RESUME,                               EFUSE_BLK0, 141,   1, [] Represents whether ROM code is forced to send a resume command during SPI boot. 1: forced. 0:not forced
SECURE_VERSION,                                  EFUSE_BLK0, 142,  16, [] Represents the version used by ESP-IDF anti-rollback feature
SECURE_BOOT_DISABLE_FAST_WAKE,                   EFUSE_BLK0, 158,   1, [] Represents whether FAST VERIFY ON WAKE is disabled or enabled when Secure Boot is enabled. 1: disabled. 0: enabled
DISABLE_WAFER_VERSION_MAJOR,                     EFUSE_BLK0, 160,   1, [] Disables check of wafer version major
DISABLE_BLK_VERSION_MAJOR,                       EFUSE_BLK0, 161,   1, [] Disables check of blk version major
MAC,                                             EFUSE_BLK1,  40,   8, [MAC_FACTORY] MAC address
,                                                EFUSE_BLK1,  32,   8, [MAC_FACTORY] MAC address
,                                                EFUSE_BLK1,  24,   8, [MAC_FACTORY] MAC address
,                                                EFUSE_BLK1,  16,   8, [MAC_FACTORY] MAC address
,                                                EFUSE_BLK1,   8,   8, [MAC_FACTORY] MAC address
,                                                EFUSE_BLK1,   0,   8, [MAC_FACTORY] MAC address
MAC_EXT,                                         EFUSE_BLK1,  56,   8, [] Stores the extended bits of MAC address
,                                                EFUSE_BLK1,  48,   8, [] Stores the extended bits of MAC address
ACTIVE_HP_DBIAS,                                 EFUSE_BLK1,  64,   5, [] Stores the active hp dbias
ACTIVE_LP_DBIAS,                                 EFUSE_BLK1,  69,   5, [] Stores the active lp dbias
LSLP_HP_DBG,                                     EFUSE_BLK1,  74,   2, [] Stores the lslp hp dbg
LSLP_HP_DBIAS,                                   EFUSE_BLK1,  76,   4, [] Stores the lslp hp dbias
DSLP_LP_DBG,                                     EFUSE_BLK1,  80,   3, [] Stores the dslp lp dbg
DSLP_LP_DBIAS,                                   EFUSE_BLK1,  83,   4, [] Stores the dslp lp dbias
DBIAS_VOL_GAP,                                   EFUSE_BLK1,  87,   5, [] Stores the hp and lp dbias vol gap
WAFER_VERSION_MINOR,                             EFUSE_BLK1, 114,   4, []
WAFER_VERSION_MAJOR,                             EFUSE_BLK1, 118,   2, []
PKG_VERSION,                                     EFUSE_BLK1, 120,   3, [] Package version
BLK_VERSION_MINOR,                               EFUSE_BLK1, 123,   3, [] BLK_VERSION_MINOR of BLOCK2
BLK_VERSION_MAJOR,                               EFUSE_BLK1, 126,   2, [] BLK_VERSION_MAJOR of BLOCK2
FLASH_CAP,                                       EFUSE_BLK1, 128,   3, []
FLASH_TEMP,                                      EFUSE_BLK1, 131,   2, []
FLASH_VENDOR,                                    EFUSE_BLK1, 133,   3, []
OPTIONAL_UNIQUE_ID,                              EFUSE_BLK2,   0, 128, [] Optional unique 128-bit ID
TEMP_CALIB,                                      EFUSE_BLK2, 128,   9, [] Temperature calibration data
OCODE,                                           EFUSE_BLK2, 137,   8, [] ADC OCode
ADC1_INIT_CODE_ATTEN0,                           EFUSE_BLK2, 145,  10, [] ADC1 init code at atten0
ADC1_INIT_CODE_ATTEN1,                           EFUSE_BLK2, 155,  10, [] ADC1 init code at atten1
ADC1_INIT_CODE_ATTEN2,                           EFUSE_BLK2, 165,  10, [] ADC1 init code at atten2
ADC1_INIT_CODE_ATTEN3,                           EFUSE_BLK2, 175,  10, [] ADC1 init code at atten3
ADC1_CAL_VOL_ATTEN0,                             EFUSE_BLK2, 185,  10, [] ADC1 calibration voltage at atten0
ADC1_CAL_VOL_ATTEN1,                             EFUSE_BLK2, 195,  10, [] ADC1 calibration voltage at atten1
ADC1_CAL_VOL_ATTEN2,                             EFUSE_BLK2, 205,  10, [] ADC1 calibration voltage at atten2
ADC1_CAL_VOL_ATTEN3,                             EFUSE_BLK2, 215,  10, [] ADC1 calibration voltage at atten3
ADC1_INIT_CODE_ATTEN0_CH0,                       EFUSE_BLK2, 225,   4, [] ADC1 init code at atten0 ch0
ADC1_INIT_CODE_ATTEN0_CH1,                       EFUSE_BLK2, 229,   4, [] ADC1 init code at atten0 ch1
ADC1_INIT_CODE_ATTEN0_CH2,                       EFUSE_BLK2, 233,   4, [] ADC1 init code at atten0 ch2
ADC1_INIT_CODE_ATTEN0_CH3,                       EFUSE_BLK2, 237,   4, [] ADC1 init code at atten0 ch3
ADC1_INIT_CODE_ATTEN0_CH4,                       EFUSE_BLK2, 241,   4, [] ADC1 init code at atten0 ch4
ADC1_INIT_CODE_ATTEN0_CH5,                       EFUSE_BLK2, 245,   4, [] ADC1 init code at atten0 ch5
ADC1_INIT_CODE_ATTEN0_CH6,                       EFUSE_BLK2, 249,   4, [] ADC1 init code at atten0 ch6
USER_DATA,                                       EFUSE_BLK3,   0, 256, [BLOCK_USR_DATA] User data
USER_DATA.MAC_CUSTOM,                            EFUSE_BLK3, 200,  48, [MAC_CUSTOM CUSTOM_MAC] Custom MAC
KEY0,                                            EFUSE_BLK4,   0, 256, [BLOCK_KEY0] Key0 or user data
KEY1,                                            EFUSE_BLK5,   0, 256, [BLOCK_KEY1] Key1 or user data
KEY2,                                            EFUSE_BLK6,   0, 256, [BLOCK_KEY2] Key2 or user data
KEY3,                                            EFUSE_BLK7,   0, 256, [BLOCK_KEY3] Key3 or user data
KEY4,                                            EFUSE_BLK8,   0, 256, [BLOCK_KEY4] Key4 or user data
KEY5,                                            EFUSE_BLK9,   0, 256, [BLOCK_KEY5] Key5 or user data
SYS_DATA_PART2,                                  EFUSE_BLK10,   0, 256, [BLOCK_SYS_DATA2] System data part 2 (reserved)
