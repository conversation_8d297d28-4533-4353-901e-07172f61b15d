#ifndef GC0308_CAMERA_H
#define GC0308_CAMERA_H

#include "esp_err.h"
#include "esp_camera.h"

#ifdef __cplusplus
extern "C" {
#endif

/* GC0308摄像头I2C地址 */
#define GC0308_I2C_ADDR         0x21

/* DVP接口引脚定义 */
#define CAMERA_PIN_XCLK         5
#define CAMERA_PIN_PCLK         7
#define CAMERA_PIN_VSYNC        3
#define CAMERA_PIN_HREF         46
#define CAMERA_PIN_D0           16
#define CAMERA_PIN_D1           18
#define CAMERA_PIN_D2           8
#define CAMERA_PIN_D3           17
#define CAMERA_PIN_D4           15
#define CAMERA_PIN_D5           6
#define CAMERA_PIN_D6           4
#define CAMERA_PIN_D7           9

/* 摄像头配置参数 */
#define CAMERA_XCLK_FREQ        20000000    // 20MHz
#define CAMERA_LEDC_TIMER       LEDC_TIMER_0
#define CAMERA_LEDC_CHANNEL     LEDC_CHANNEL_0

/* 注意：摄像头电源控制通过display_driver中的dvp_pwdn()函数实现 */

/**
 * @brief 初始化GC0308摄像头
 * 
 * @param config 摄像头配置参数
 * @return esp_err_t ESP_OK成功，其他值失败
 */
esp_err_t gc0308_camera_init(camera_config_t *config);

/**
 * @brief 反初始化GC0308摄像头
 * 
 * @return esp_err_t ESP_OK成功，其他值失败
 */
esp_err_t gc0308_camera_deinit(void);

/**
 * @brief 获取默认的GC0308摄像头配置
 * 
 * @return camera_config_t 默认配置
 */
camera_config_t gc0308_get_default_config(void);

/**
 * @brief 启动摄像头
 * 
 * @return esp_err_t ESP_OK成功，其他值失败
 */
esp_err_t gc0308_camera_start(void);

/**
 * @brief 停止摄像头
 * 
 * @return esp_err_t ESP_OK成功，其他值失败
 */
esp_err_t gc0308_camera_stop(void);

/**
 * @brief 捕获一帧图像
 * 
 * @return camera_fb_t* 图像帧缓冲区指针，失败返回NULL
 */
camera_fb_t* gc0308_camera_capture(void);

/**
 * @brief 释放图像帧缓冲区
 * 
 * @param fb 图像帧缓冲区指针
 */
void gc0308_camera_fb_return(camera_fb_t *fb);

#ifdef __cplusplus
}
#endif

#endif /* GC0308_CAMERA_H */
