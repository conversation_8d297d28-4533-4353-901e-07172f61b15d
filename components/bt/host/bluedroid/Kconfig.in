config BT_BTC_TASK_STACK_SIZE
    int "Bluetooth event (callback to application) task stack size"
    depends on BT_BLUEDROID_ENABLED
    default 3072
    help
        This select btc task stack size

choice BT_BLUEDROID_PINNED_TO_CORE_CHOICE
    prompt "The cpu core which Bluedroid run"
    depends on BT_BLUEDROID_ENABLED && !FREERTOS_UNICORE
    help
        Which the cpu core to run Bluedroid. Can choose core0 and core1.
        Can not specify no-affinity.

    config BT_BLUEDROID_PINNED_TO_CORE_0
        bool "Core 0 (PRO CPU)"
    config BT_BLUEDROID_PINNED_TO_CORE_1
        bool "Core 1 (APP CPU)"
        depends on !FREERTOS_UNICORE
endchoice

config BT_BLUEDROID_PINNED_TO_CORE
    int
    depends on BT_BLUEDROID_ENABLED
    default 0 if BT_BLUEDROID_PINNED_TO_CORE_0
    default 1 if BT_BLUEDROID_PINNED_TO_CORE_1
    default 0

config BT_BTU_TASK_STACK_SIZE
    int "Bluetooth Bluedroid Host Stack task stack size"
    depends on BT_BLUEDROID_ENABLED
    default 4352
    help
        This select btu task stack size

config BT_BLUEDROID_MEM_DEBUG
    bool "Bluedroid memory debug"
    depends on BT_BLUEDROID_ENABLED
    default n
    help
        Bluedroid memory debug

config BT_BLUEDROID_ESP_COEX_VSC
    bool "Enable Espressif Vendor-specific HCI commands for coexist status configuration"
    depends on BT_BLUEDROID_ENABLED
    default y if (ESP_COEX_SW_COEXIST_ENABLE || BT_CONTROLLER_DISABLED)
    default n
    help
        Enable Espressif Vendor-specific HCI commands for coexist status configuration

config BT_CLASSIC_ENABLED
    bool "Classic Bluetooth"
    depends on BT_BLUEDROID_ENABLED && ((BT_CONTROLLER_ENABLED && SOC_BT_CLASSIC_SUPPORTED) || BT_CONTROLLER_DISABLED)
    default n
    help
        For now this option needs "SMP_ENABLE" to be set to yes

choice BT_ENC_KEY_SIZE_CTRL_ENABLED
    prompt "configure encryption key size"
    depends on BT_CLASSIC_ENABLED
    default BT_ENC_KEY_SIZE_CTRL_VSC
    help
        This chooses the support status of configuring encryption key size

    config BT_ENC_KEY_SIZE_CTRL_STD
        depends on (BT_CONTROLLER_DISABLED || (BT_CONTROLLER_ENABLED && SOC_BT_H2C_ENC_KEY_CTRL_ENH_STD_SUPPORTED))
        bool "Supported by standard HCI command"

    config BT_ENC_KEY_SIZE_CTRL_VSC
        depends on (BT_CONTROLLER_DISABLED || (BT_CONTROLLER_ENABLED && SOC_BT_H2C_ENC_KEY_CTRL_ENH_VSC_SUPPORTED))
        bool "Supported by Vendor-specific HCI command"

    config BT_ENC_KEY_SIZE_CTRL_NONE
        bool "Not supported"
endchoice

config BT_CLASSIC_BQB_ENABLED
    bool "Host Qualitifcation support for Classic Bluetooth"
    depends on BT_CLASSIC_ENABLED
    default n
    help
        This enables functionalities of Host qualification for Classic Bluetooth.

config BT_A2DP_ENABLE
    bool "A2DP"
    depends on BT_CLASSIC_ENABLED
    default n
    select BT_AVRCP_ENABLED
    help
        Advanced Audio Distribution Profile

config BT_AVRCP_ENABLED
    bool
    depends on BT_A2DP_ENABLE
    default y
    help
        Audio/Video Remote Control Profile, AVRCP and A2DP are coupled in Bluedroid,
        AVRCP still controlled by A2DP option, this is a dummy option currently

menu "AVRCP Features"
    depends on BT_AVRCP_ENABLED

    config BT_AVRCP_CT_COVER_ART_ENABLED
        bool "AVRCP CT Cover Art"
        default y
        select BT_GOEPC_ENABLED
        help
            This enable Cover Art feature of AVRCP CT role

endmenu

config BT_SPP_ENABLED
    bool "SPP"
    depends on BT_CLASSIC_ENABLED
    default n
    help
        This enables the Serial Port Profile

config BT_L2CAP_ENABLED
    bool "BT L2CAP"
    depends on BT_CLASSIC_ENABLED
    default n
    help
        This enables the Logical Link Control and Adaptation Layer Protocol.
        Only supported classic bluetooth.

config BT_SDP_COMMON_ENABLED
    bool "BT SDP COMMON"
    depends on BT_CLASSIC_ENABLED
    default y if BT_L2CAP_ENABLED
    default n
    help
        This enables common SDP operation, such as SDP record creation and deletion.

menuconfig BT_HFP_ENABLE
    bool "Hands Free/Handset Profile"
    depends on BT_CLASSIC_ENABLED
    default n
    help
        Hands Free Unit and Audio Gateway can be included simultaneously
        but they cannot run simultaneously due to internal limitations.

config BT_HFP_CLIENT_ENABLE
    bool "Hands Free Unit"
    depends on BT_HFP_ENABLE
    default y

config BT_HFP_AG_ENABLE
    bool "Audio Gateway"
    depends on BT_HFP_ENABLE
    default y

choice BT_HFP_AUDIO_DATA_PATH
    prompt "audio(SCO) data path"
    depends on BT_HFP_ENABLE
    help
        SCO data path, i.e. HCI or PCM. This option is set using API
        "esp_bredr_sco_datapath_set" in Bluetooth host. Default SCO data
        path can also be set in Bluetooth Controller.

    config BT_HFP_AUDIO_DATA_PATH_PCM
        bool "PCM"
    config BT_HFP_AUDIO_DATA_PATH_HCI
        bool "HCI"
endchoice

config BT_HFP_WBS_ENABLE
    bool "Wide Band Speech"
    depends on BT_HFP_ENABLE && BT_HFP_AUDIO_DATA_PATH_HCI
    default y
    help
        This enables Wide Band Speech. Should disable it when SCO data path is PCM.
        Otherwise there will be no data transmitted via GPIOs.

menuconfig BT_HID_ENABLED
    bool "Classic BT HID"
    depends on BT_CLASSIC_ENABLED
    default n
    help
        This enables the BT HID functionalities

config BT_HID_HOST_ENABLED
    bool "Classic BT HID Host"
    depends on BT_HID_ENABLED
    default n
    help
        This enables the BT HID Host

config BT_HID_DEVICE_ENABLED
    bool "Classic BT HID Device"
    depends on BT_HID_ENABLED
    help
        This enables the BT HID Device

config BT_GOEPC_ENABLED
    bool
    depends on BT_CLASSIC_ENABLED
    default n
    help
        This enables the BT GOEP Profile Client role

config BT_BLE_ENABLED
    bool "Bluetooth Low Energy"
    depends on BT_BLUEDROID_ENABLED
    default y
    help
        This enables Bluetooth Low Energy

config BT_GATTS_ENABLE
    bool "Include GATT server module(GATTS)"
    depends on BT_BLE_ENABLED
    default y
    help
        This option can be disabled when the app work only on gatt client mode

config BT_GATTS_PPCP_CHAR_GAP
    bool "Enable Peripheral Preferred Connection Parameters characteristic in GAP service"
    depends on BT_GATTS_ENABLE
    default n
    help
        This enables "Peripheral Preferred Connection Parameters" characteristic (UUID: 0x2A04) in GAP service that has
        connection parameters like min/max connection interval, slave latency and supervision timeout multiplier

config BT_BLE_BLUFI_ENABLE
    bool "Include blufi function"
    depends on BT_GATTS_ENABLE
    default n
    help
        This option can be close when the app does not require blufi function.

config BT_GATT_MAX_SR_PROFILES
    int "Max GATT Server Profiles"
    depends on BT_GATTS_ENABLE && BT_BLUEDROID_ENABLED
    range 1 32
    default 8
    help
        Maximum GATT Server Profiles Count

config BT_GATT_MAX_SR_ATTRIBUTES
    int "Max GATT Service Attributes"
    depends on BT_GATTS_ENABLE && BT_BLUEDROID_ENABLED
    range 1 500
    default 100
    help
        Maximum GATT Service Attributes Count


choice BT_GATTS_SEND_SERVICE_CHANGE_MODE
    prompt "GATTS Service Change Mode"
    default BT_GATTS_SEND_SERVICE_CHANGE_AUTO
    depends on BT_GATTS_ENABLE
    help
        Service change indication mode for GATT Server.

    config BT_GATTS_SEND_SERVICE_CHANGE_MANUAL
        bool "GATTS manually send service change indication"
        help
            Manually send service change indication through API esp_ble_gatts_send_service_change_indication()

    config BT_GATTS_SEND_SERVICE_CHANGE_AUTO
        bool "GATTS automatically send service change indication"
        help
            Let Bluedroid handle the service change indication internally

endchoice

config BT_GATTS_SEND_SERVICE_CHANGE_MODE
    int
    depends on BT_GATTS_ENABLE
    default 0 if BT_GATTS_SEND_SERVICE_CHANGE_AUTO
    default 1 if BT_GATTS_SEND_SERVICE_CHANGE_MANUAL
    default 0

config BT_GATTS_ROBUST_CACHING_ENABLED
    bool "Enable Robust Caching on Server Side"
    depends on BT_GATTS_ENABLE
    default n
    help
        This option enables the GATT robust caching feature on the server.
        if turned on, the Client Supported Features characteristic, Database Hash characteristic,
        and Server Supported Features characteristic will be included in the GAP SERVICE.


config BT_GATTS_DEVICE_NAME_WRITABLE
    bool "Allow to write device name by GATT clients"
    depends on BT_GATTS_ENABLE
    default n
    help
        Enabling this option allows remote GATT clients to write device name

config BT_GATTS_APPEARANCE_WRITABLE
    bool "Allow to write appearance by GATT clients"
    depends on BT_GATTS_ENABLE
    default n
    help
        Enabling this option allows remote GATT clients to write appearance

config BT_GATTC_ENABLE
    bool "Include GATT client module(GATTC)"
    depends on BT_BLE_ENABLED
    default y
    help
        This option can be close when the app work only on gatt server mode

config BT_GATTC_MAX_CACHE_CHAR
    int "Max gattc cache characteristic for discover"
    depends on BT_GATTC_ENABLE
    range 1 500
    default 40
    help
        Maximum GATTC cache characteristic count

config BT_GATTC_NOTIF_REG_MAX
    int "Max gattc notify(indication) register number"
    depends on BT_GATTC_ENABLE
    range 1 64
    default 5
    help
        Maximum GATTC notify(indication) register number

config BT_GATTC_CACHE_NVS_FLASH
    bool "Save gattc cache data to nvs flash"
    depends on BT_GATTC_ENABLE
    default n
    help
        This select can save gattc cache data to nvs flash

config BT_GATTC_CONNECT_RETRY_COUNT
    int "The number of attempts to reconnect if the connection establishment failed"
    depends on BT_GATTC_ENABLE
    range 0 255
    default 3
    help
        The number of attempts to reconnect if the connection establishment failed

config BT_BLE_SMP_ENABLE
    bool "Include BLE security module(SMP)"
    depends on BT_BLE_ENABLED
    default y
    help
        This option can be close when the app not used the ble security connect.

config BT_SMP_SLAVE_CON_PARAMS_UPD_ENABLE
    bool "Slave enable connection parameters update during pairing"
    depends on BT_BLE_SMP_ENABLE
    default n
    help
        In order to reduce the pairing time, slave actively initiates connection parameters
        update during pairing.

config BT_BLE_SMP_ID_RESET_ENABLE
    bool "Reset device identity when all bonding records are deleted"
    depends on BT_BLE_SMP_ENABLE
    default n
    help
        There are tracking risks associated with using a fixed or static IRK.
        If enabled this option, Bluedroid will assign a new randomly-generated IRK
        when all pairing and bonding records are deleted. This would decrease the ability
        of a previously paired peer to be used to determine whether a device
        with which it previously shared an IRK is within range.

config BT_BLE_SMP_BOND_NVS_FLASH
    bool "Save SMP bonding keys to nvs flash"
    depends on BT_BLE_SMP_ENABLE
    default y
    help
        This select can save SMP bonding keys to nvs flash

config BT_STACK_NO_LOG
    bool "Disable BT debug logs (minimize bin size)"
    depends on BT_BLUEDROID_ENABLED
    default n
    help
        This select can save the rodata code size

menu "BT DEBUG LOG LEVEL"
    depends on BT_BLUEDROID_ENABLED && !BT_STACK_NO_LOG

    choice BT_LOG_HCI_TRACE_LEVEL
        prompt "HCI layer"
        default BT_LOG_HCI_TRACE_LEVEL_WARNING
        depends on BT_BLUEDROID_ENABLED && !BT_STACK_NO_LOG
        help
            Define BT trace level for HCI layer

        config BT_LOG_HCI_TRACE_LEVEL_NONE
            bool "NONE"
        config BT_LOG_HCI_TRACE_LEVEL_ERROR
            bool "ERROR"
        config BT_LOG_HCI_TRACE_LEVEL_WARNING
            bool "WARNING"
        config BT_LOG_HCI_TRACE_LEVEL_API
            bool "API"
        config BT_LOG_HCI_TRACE_LEVEL_EVENT
            bool "EVENT"
        config BT_LOG_HCI_TRACE_LEVEL_DEBUG
            bool "DEBUG"
        config BT_LOG_HCI_TRACE_LEVEL_VERBOSE
            bool "VERBOSE"
    endchoice

    config BT_LOG_HCI_TRACE_LEVEL
        int
        depends on BT_BLUEDROID_ENABLED
        default 0 if BT_LOG_HCI_TRACE_LEVEL_NONE
        default 1 if BT_LOG_HCI_TRACE_LEVEL_ERROR
        default 2 if BT_LOG_HCI_TRACE_LEVEL_WARNING
        default 3 if BT_LOG_HCI_TRACE_LEVEL_API
        default 4 if BT_LOG_HCI_TRACE_LEVEL_EVENT
        default 5 if BT_LOG_HCI_TRACE_LEVEL_DEBUG
        default 6 if BT_LOG_HCI_TRACE_LEVEL_VERBOSE
        default 2

    choice BT_LOG_BTM_TRACE_LEVEL
        prompt "BTM layer"
        default BT_LOG_BTM_TRACE_LEVEL_WARNING
        depends on BT_BLUEDROID_ENABLED && !BT_STACK_NO_LOG
        help
            Define BT trace level for BTM layer

        config BT_LOG_BTM_TRACE_LEVEL_NONE
            bool "NONE"
        config BT_LOG_BTM_TRACE_LEVEL_ERROR
            bool "ERROR"
        config BT_LOG_BTM_TRACE_LEVEL_WARNING
            bool "WARNING"
        config BT_LOG_BTM_TRACE_LEVEL_API
            bool "API"
        config BT_LOG_BTM_TRACE_LEVEL_EVENT
            bool "EVENT"
        config BT_LOG_BTM_TRACE_LEVEL_DEBUG
            bool "DEBUG"
        config BT_LOG_BTM_TRACE_LEVEL_VERBOSE
            bool "VERBOSE"
    endchoice

    config BT_LOG_BTM_TRACE_LEVEL
        int
        depends on BT_BLUEDROID_ENABLED
        default 0 if BT_LOG_BTM_TRACE_LEVEL_NONE
        default 1 if BT_LOG_BTM_TRACE_LEVEL_ERROR
        default 2 if BT_LOG_BTM_TRACE_LEVEL_WARNING
        default 3 if BT_LOG_BTM_TRACE_LEVEL_API
        default 4 if BT_LOG_BTM_TRACE_LEVEL_EVENT
        default 5 if BT_LOG_BTM_TRACE_LEVEL_DEBUG
        default 6 if BT_LOG_BTM_TRACE_LEVEL_VERBOSE
        default 2

    choice BT_LOG_L2CAP_TRACE_LEVEL
        prompt "L2CAP layer"
        default BT_LOG_L2CAP_TRACE_LEVEL_WARNING
        depends on BT_BLUEDROID_ENABLED && !BT_STACK_NO_LOG
        help
            Define BT trace level for L2CAP layer

        config BT_LOG_L2CAP_TRACE_LEVEL_NONE
            bool "NONE"
        config BT_LOG_L2CAP_TRACE_LEVEL_ERROR
            bool "ERROR"
        config BT_LOG_L2CAP_TRACE_LEVEL_WARNING
            bool "WARNING"
        config BT_LOG_L2CAP_TRACE_LEVEL_API
            bool "API"
        config BT_LOG_L2CAP_TRACE_LEVEL_EVENT
            bool "EVENT"
        config BT_LOG_L2CAP_TRACE_LEVEL_DEBUG
            bool "DEBUG"
        config BT_LOG_L2CAP_TRACE_LEVEL_VERBOSE
            bool "VERBOSE"
    endchoice

    config BT_LOG_L2CAP_TRACE_LEVEL
        int
        depends on BT_BLUEDROID_ENABLED
        default 0 if BT_LOG_L2CAP_TRACE_LEVEL_NONE
        default 1 if BT_LOG_L2CAP_TRACE_LEVEL_ERROR
        default 2 if BT_LOG_L2CAP_TRACE_LEVEL_WARNING
        default 3 if BT_LOG_L2CAP_TRACE_LEVEL_API
        default 4 if BT_LOG_L2CAP_TRACE_LEVEL_EVENT
        default 5 if BT_LOG_L2CAP_TRACE_LEVEL_DEBUG
        default 6 if BT_LOG_L2CAP_TRACE_LEVEL_VERBOSE
        default 2

    choice BT_LOG_RFCOMM_TRACE_LEVEL
        prompt "RFCOMM layer"
        default BT_LOG_RFCOMM_TRACE_LEVEL_WARNING
        depends on BT_BLUEDROID_ENABLED && !BT_STACK_NO_LOG
        help
            Define BT trace level for RFCOMM layer

        config BT_LOG_RFCOMM_TRACE_LEVEL_NONE
            bool "NONE"
        config BT_LOG_RFCOMM_TRACE_LEVEL_ERROR
            bool "ERROR"
        config BT_LOG_RFCOMM_TRACE_LEVEL_WARNING
            bool "WARNING"
        config BT_LOG_RFCOMM_TRACE_LEVEL_API
            bool "API"
        config BT_LOG_RFCOMM_TRACE_LEVEL_EVENT
            bool "EVENT"
        config BT_LOG_RFCOMM_TRACE_LEVEL_DEBUG
            bool "DEBUG"
        config BT_LOG_RFCOMM_TRACE_LEVEL_VERBOSE
            bool "VERBOSE"
    endchoice

    config BT_LOG_RFCOMM_TRACE_LEVEL
        int
        depends on BT_BLUEDROID_ENABLED
        default 0 if BT_LOG_RFCOMM_TRACE_LEVEL_NONE
        default 1 if BT_LOG_RFCOMM_TRACE_LEVEL_ERROR
        default 2 if BT_LOG_RFCOMM_TRACE_LEVEL_WARNING
        default 3 if BT_LOG_RFCOMM_TRACE_LEVEL_API
        default 4 if BT_LOG_RFCOMM_TRACE_LEVEL_EVENT
        default 5 if BT_LOG_RFCOMM_TRACE_LEVEL_DEBUG
        default 6 if BT_LOG_RFCOMM_TRACE_LEVEL_VERBOSE
        default 2

    choice BT_LOG_SDP_TRACE_LEVEL
        prompt "SDP layer"
        default BT_LOG_SDP_TRACE_LEVEL_WARNING
        depends on BT_BLUEDROID_ENABLED && !BT_STACK_NO_LOG
        help
            Define BT trace level for SDP layer

        config BT_LOG_SDP_TRACE_LEVEL_NONE
            bool "NONE"
        config BT_LOG_SDP_TRACE_LEVEL_ERROR
            bool "ERROR"
        config BT_LOG_SDP_TRACE_LEVEL_WARNING
            bool "WARNING"
        config BT_LOG_SDP_TRACE_LEVEL_API
            bool "API"
        config BT_LOG_SDP_TRACE_LEVEL_EVENT
            bool "EVENT"
        config BT_LOG_SDP_TRACE_LEVEL_DEBUG
            bool "DEBUG"
        config BT_LOG_SDP_TRACE_LEVEL_VERBOSE
            bool "VERBOSE"
    endchoice

    config BT_LOG_SDP_TRACE_LEVEL
        int
        depends on BT_BLUEDROID_ENABLED
        default 0 if BT_LOG_SDP_TRACE_LEVEL_NONE
        default 1 if BT_LOG_SDP_TRACE_LEVEL_ERROR
        default 2 if BT_LOG_SDP_TRACE_LEVEL_WARNING
        default 3 if BT_LOG_SDP_TRACE_LEVEL_API
        default 4 if BT_LOG_SDP_TRACE_LEVEL_EVENT
        default 5 if BT_LOG_SDP_TRACE_LEVEL_DEBUG
        default 6 if BT_LOG_SDP_TRACE_LEVEL_VERBOSE
        default 2

    choice BT_LOG_GAP_TRACE_LEVEL
        prompt "GAP layer"
        default BT_LOG_GAP_TRACE_LEVEL_WARNING
        depends on BT_BLUEDROID_ENABLED && !BT_STACK_NO_LOG
        help
            Define BT trace level for GAP layer

        config BT_LOG_GAP_TRACE_LEVEL_NONE
            bool "NONE"
        config BT_LOG_GAP_TRACE_LEVEL_ERROR
            bool "ERROR"
        config BT_LOG_GAP_TRACE_LEVEL_WARNING
            bool "WARNING"
        config BT_LOG_GAP_TRACE_LEVEL_API
            bool "API"
        config BT_LOG_GAP_TRACE_LEVEL_EVENT
            bool "EVENT"
        config BT_LOG_GAP_TRACE_LEVEL_DEBUG
            bool "DEBUG"
        config BT_LOG_GAP_TRACE_LEVEL_VERBOSE
            bool "VERBOSE"
    endchoice

    config BT_LOG_GAP_TRACE_LEVEL
        int
        depends on BT_BLUEDROID_ENABLED
        default 0 if BT_LOG_GAP_TRACE_LEVEL_NONE
        default 1 if BT_LOG_GAP_TRACE_LEVEL_ERROR
        default 2 if BT_LOG_GAP_TRACE_LEVEL_WARNING
        default 3 if BT_LOG_GAP_TRACE_LEVEL_API
        default 4 if BT_LOG_GAP_TRACE_LEVEL_EVENT
        default 5 if BT_LOG_GAP_TRACE_LEVEL_DEBUG
        default 6 if BT_LOG_GAP_TRACE_LEVEL_VERBOSE
        default 2

    choice BT_LOG_BNEP_TRACE_LEVEL
        prompt "BNEP layer"
        default BT_LOG_BNEP_TRACE_LEVEL_WARNING
        depends on BT_BLUEDROID_ENABLED && !BT_STACK_NO_LOG
        help
            Define BT trace level for BNEP layer

        config BT_LOG_BNEP_TRACE_LEVEL_NONE
            bool "NONE"
        config BT_LOG_BNEP_TRACE_LEVEL_ERROR
            bool "ERROR"
        config BT_LOG_BNEP_TRACE_LEVEL_WARNING
            bool "WARNING"
        config BT_LOG_BNEP_TRACE_LEVEL_API
            bool "API"
        config BT_LOG_BNEP_TRACE_LEVEL_EVENT
            bool "EVENT"
        config BT_LOG_BNEP_TRACE_LEVEL_DEBUG
            bool "DEBUG"
        config BT_LOG_BNEP_TRACE_LEVEL_VERBOSE
            bool "VERBOSE"
    endchoice

    config BT_LOG_BNEP_TRACE_LEVEL
        int
        depends on BT_BLUEDROID_ENABLED
        default 0 if BT_LOG_BNEP_TRACE_LEVEL_NONE
        default 1 if BT_LOG_BNEP_TRACE_LEVEL_ERROR
        default 2 if BT_LOG_BNEP_TRACE_LEVEL_WARNING
        default 3 if BT_LOG_BNEP_TRACE_LEVEL_API
        default 4 if BT_LOG_BNEP_TRACE_LEVEL_EVENT
        default 5 if BT_LOG_BNEP_TRACE_LEVEL_DEBUG
        default 6 if BT_LOG_BNEP_TRACE_LEVEL_VERBOSE
        default 2

    choice BT_LOG_PAN_TRACE_LEVEL
        prompt "PAN layer"
        default BT_LOG_PAN_TRACE_LEVEL_WARNING
        depends on BT_BLUEDROID_ENABLED && !BT_STACK_NO_LOG
        help
            Define BT trace level for PAN layer

        config BT_LOG_PAN_TRACE_LEVEL_NONE
            bool "NONE"
        config BT_LOG_PAN_TRACE_LEVEL_ERROR
            bool "ERROR"
        config BT_LOG_PAN_TRACE_LEVEL_WARNING
            bool "WARNING"
        config BT_LOG_PAN_TRACE_LEVEL_API
            bool "API"
        config BT_LOG_PAN_TRACE_LEVEL_EVENT
            bool "EVENT"
        config BT_LOG_PAN_TRACE_LEVEL_DEBUG
            bool "DEBUG"
        config BT_LOG_PAN_TRACE_LEVEL_VERBOSE
            bool "VERBOSE"
    endchoice

    config BT_LOG_PAN_TRACE_LEVEL
        int
        depends on BT_BLUEDROID_ENABLED
        default 0 if BT_LOG_PAN_TRACE_LEVEL_NONE
        default 1 if BT_LOG_PAN_TRACE_LEVEL_ERROR
        default 2 if BT_LOG_PAN_TRACE_LEVEL_WARNING
        default 3 if BT_LOG_PAN_TRACE_LEVEL_API
        default 4 if BT_LOG_PAN_TRACE_LEVEL_EVENT
        default 5 if BT_LOG_PAN_TRACE_LEVEL_DEBUG
        default 6 if BT_LOG_PAN_TRACE_LEVEL_VERBOSE
        default 2

    choice BT_LOG_A2D_TRACE_LEVEL
        prompt "A2D layer"
        default BT_LOG_A2D_TRACE_LEVEL_WARNING
        depends on BT_BLUEDROID_ENABLED && !BT_STACK_NO_LOG
        help
            Define BT trace level for A2D layer

        config BT_LOG_A2D_TRACE_LEVEL_NONE
            bool "NONE"
        config BT_LOG_A2D_TRACE_LEVEL_ERROR
            bool "ERROR"
        config BT_LOG_A2D_TRACE_LEVEL_WARNING
            bool "WARNING"
        config BT_LOG_A2D_TRACE_LEVEL_API
            bool "API"
        config BT_LOG_A2D_TRACE_LEVEL_EVENT
            bool "EVENT"
        config BT_LOG_A2D_TRACE_LEVEL_DEBUG
            bool "DEBUG"
        config BT_LOG_A2D_TRACE_LEVEL_VERBOSE
            bool "VERBOSE"
    endchoice

    config BT_LOG_A2D_TRACE_LEVEL
        int
        depends on BT_BLUEDROID_ENABLED
        default 0 if BT_LOG_A2D_TRACE_LEVEL_NONE
        default 1 if BT_LOG_A2D_TRACE_LEVEL_ERROR
        default 2 if BT_LOG_A2D_TRACE_LEVEL_WARNING
        default 3 if BT_LOG_A2D_TRACE_LEVEL_API
        default 4 if BT_LOG_A2D_TRACE_LEVEL_EVENT
        default 5 if BT_LOG_A2D_TRACE_LEVEL_DEBUG
        default 6 if BT_LOG_A2D_TRACE_LEVEL_VERBOSE
        default 2

    choice BT_LOG_AVDT_TRACE_LEVEL
        prompt "AVDT layer"
        default BT_LOG_AVDT_TRACE_LEVEL_WARNING
        depends on BT_BLUEDROID_ENABLED && !BT_STACK_NO_LOG
        help
            Define BT trace level for AVDT layer

        config BT_LOG_AVDT_TRACE_LEVEL_NONE
            bool "NONE"
        config BT_LOG_AVDT_TRACE_LEVEL_ERROR
            bool "ERROR"
        config BT_LOG_AVDT_TRACE_LEVEL_WARNING
            bool "WARNING"
        config BT_LOG_AVDT_TRACE_LEVEL_API
            bool "API"
        config BT_LOG_AVDT_TRACE_LEVEL_EVENT
            bool "EVENT"
        config BT_LOG_AVDT_TRACE_LEVEL_DEBUG
            bool "DEBUG"
        config BT_LOG_AVDT_TRACE_LEVEL_VERBOSE
            bool "VERBOSE"
    endchoice

    config BT_LOG_AVDT_TRACE_LEVEL
        int
        depends on BT_BLUEDROID_ENABLED
        default 0 if BT_LOG_AVDT_TRACE_LEVEL_NONE
        default 1 if BT_LOG_AVDT_TRACE_LEVEL_ERROR
        default 2 if BT_LOG_AVDT_TRACE_LEVEL_WARNING
        default 3 if BT_LOG_AVDT_TRACE_LEVEL_API
        default 4 if BT_LOG_AVDT_TRACE_LEVEL_EVENT
        default 5 if BT_LOG_AVDT_TRACE_LEVEL_DEBUG
        default 6 if BT_LOG_AVDT_TRACE_LEVEL_VERBOSE
        default 2

    choice BT_LOG_AVCT_TRACE_LEVEL
        prompt "AVCT layer"
        default BT_LOG_AVCT_TRACE_LEVEL_WARNING
        depends on BT_BLUEDROID_ENABLED && !BT_STACK_NO_LOG
        help
            Define BT trace level for AVCT layer

        config BT_LOG_AVCT_TRACE_LEVEL_NONE
            bool "NONE"
        config BT_LOG_AVCT_TRACE_LEVEL_ERROR
            bool "ERROR"
        config BT_LOG_AVCT_TRACE_LEVEL_WARNING
            bool "WARNING"
        config BT_LOG_AVCT_TRACE_LEVEL_API
            bool "API"
        config BT_LOG_AVCT_TRACE_LEVEL_EVENT
            bool "EVENT"
        config BT_LOG_AVCT_TRACE_LEVEL_DEBUG
            bool "DEBUG"
        config BT_LOG_AVCT_TRACE_LEVEL_VERBOSE
            bool "VERBOSE"
    endchoice

    config BT_LOG_AVCT_TRACE_LEVEL
        int
        depends on BT_BLUEDROID_ENABLED
        default 0 if BT_LOG_AVCT_TRACE_LEVEL_NONE
        default 1 if BT_LOG_AVCT_TRACE_LEVEL_ERROR
        default 2 if BT_LOG_AVCT_TRACE_LEVEL_WARNING
        default 3 if BT_LOG_AVCT_TRACE_LEVEL_API
        default 4 if BT_LOG_AVCT_TRACE_LEVEL_EVENT
        default 5 if BT_LOG_AVCT_TRACE_LEVEL_DEBUG
        default 6 if BT_LOG_AVCT_TRACE_LEVEL_VERBOSE
        default 2

    choice BT_LOG_AVRC_TRACE_LEVEL
        prompt "AVRC layer"
        default BT_LOG_AVRC_TRACE_LEVEL_WARNING
        depends on BT_BLUEDROID_ENABLED && !BT_STACK_NO_LOG
        help
            Define BT trace level for AVRC layer

        config BT_LOG_AVRC_TRACE_LEVEL_NONE
            bool "NONE"
        config BT_LOG_AVRC_TRACE_LEVEL_ERROR
            bool "ERROR"
        config BT_LOG_AVRC_TRACE_LEVEL_WARNING
            bool "WARNING"
        config BT_LOG_AVRC_TRACE_LEVEL_API
            bool "API"
        config BT_LOG_AVRC_TRACE_LEVEL_EVENT
            bool "EVENT"
        config BT_LOG_AVRC_TRACE_LEVEL_DEBUG
            bool "DEBUG"
        config BT_LOG_AVRC_TRACE_LEVEL_VERBOSE
            bool "VERBOSE"
    endchoice

    config BT_LOG_AVRC_TRACE_LEVEL
        int
        depends on BT_BLUEDROID_ENABLED
        default 0 if BT_LOG_AVRC_TRACE_LEVEL_NONE
        default 1 if BT_LOG_AVRC_TRACE_LEVEL_ERROR
        default 2 if BT_LOG_AVRC_TRACE_LEVEL_WARNING
        default 3 if BT_LOG_AVRC_TRACE_LEVEL_API
        default 4 if BT_LOG_AVRC_TRACE_LEVEL_EVENT
        default 5 if BT_LOG_AVRC_TRACE_LEVEL_DEBUG
        default 6 if BT_LOG_AVRC_TRACE_LEVEL_VERBOSE
        default 2

    choice BT_LOG_MCA_TRACE_LEVEL
        prompt "MCA layer"
        default BT_LOG_MCA_TRACE_LEVEL_WARNING
        depends on BT_BLUEDROID_ENABLED && !BT_STACK_NO_LOG
        help
            Define BT trace level for MCA layer

        config BT_LOG_MCA_TRACE_LEVEL_NONE
            bool "NONE"
        config BT_LOG_MCA_TRACE_LEVEL_ERROR
            bool "ERROR"
        config BT_LOG_MCA_TRACE_LEVEL_WARNING
            bool "WARNING"
        config BT_LOG_MCA_TRACE_LEVEL_API
            bool "API"
        config BT_LOG_MCA_TRACE_LEVEL_EVENT
            bool "EVENT"
        config BT_LOG_MCA_TRACE_LEVEL_DEBUG
            bool "DEBUG"
        config BT_LOG_MCA_TRACE_LEVEL_VERBOSE
            bool "VERBOSE"
    endchoice

    config BT_LOG_MCA_TRACE_LEVEL
        int
        depends on BT_BLUEDROID_ENABLED
        default 0 if BT_LOG_MCA_TRACE_LEVEL_NONE
        default 1 if BT_LOG_MCA_TRACE_LEVEL_ERROR
        default 2 if BT_LOG_MCA_TRACE_LEVEL_WARNING
        default 3 if BT_LOG_MCA_TRACE_LEVEL_API
        default 4 if BT_LOG_MCA_TRACE_LEVEL_EVENT
        default 5 if BT_LOG_MCA_TRACE_LEVEL_DEBUG
        default 6 if BT_LOG_MCA_TRACE_LEVEL_VERBOSE
        default 2

    choice BT_LOG_HID_TRACE_LEVEL
        prompt "HID layer"
        default BT_LOG_HID_TRACE_LEVEL_WARNING
        depends on BT_BLUEDROID_ENABLED && !BT_STACK_NO_LOG
        help
            Define BT trace level for HID layer

        config BT_LOG_HID_TRACE_LEVEL_NONE
            bool "NONE"
        config BT_LOG_HID_TRACE_LEVEL_ERROR
            bool "ERROR"
        config BT_LOG_HID_TRACE_LEVEL_WARNING
            bool "WARNING"
        config BT_LOG_HID_TRACE_LEVEL_API
            bool "API"
        config BT_LOG_HID_TRACE_LEVEL_EVENT
            bool "EVENT"
        config BT_LOG_HID_TRACE_LEVEL_DEBUG
            bool "DEBUG"
        config BT_LOG_HID_TRACE_LEVEL_VERBOSE
            bool "VERBOSE"
    endchoice

    config BT_LOG_HID_TRACE_LEVEL
        int
        depends on BT_BLUEDROID_ENABLED
        default 0 if BT_LOG_HID_TRACE_LEVEL_NONE
        default 1 if BT_LOG_HID_TRACE_LEVEL_ERROR
        default 2 if BT_LOG_HID_TRACE_LEVEL_WARNING
        default 3 if BT_LOG_HID_TRACE_LEVEL_API
        default 4 if BT_LOG_HID_TRACE_LEVEL_EVENT
        default 5 if BT_LOG_HID_TRACE_LEVEL_DEBUG
        default 6 if BT_LOG_HID_TRACE_LEVEL_VERBOSE
        default 2

    choice BT_LOG_APPL_TRACE_LEVEL
        prompt "APPL layer"
        default BT_LOG_APPL_TRACE_LEVEL_WARNING
        depends on BT_BLUEDROID_ENABLED && !BT_STACK_NO_LOG
        help
            Define BT trace level for APPL layer

        config BT_LOG_APPL_TRACE_LEVEL_NONE
            bool "NONE"
        config BT_LOG_APPL_TRACE_LEVEL_ERROR
            bool "ERROR"
        config BT_LOG_APPL_TRACE_LEVEL_WARNING
            bool "WARNING"
        config BT_LOG_APPL_TRACE_LEVEL_API
            bool "API"
        config BT_LOG_APPL_TRACE_LEVEL_EVENT
            bool "EVENT"
        config BT_LOG_APPL_TRACE_LEVEL_DEBUG
            bool "DEBUG"
        config BT_LOG_APPL_TRACE_LEVEL_VERBOSE
            bool "VERBOSE"
    endchoice

    config BT_LOG_APPL_TRACE_LEVEL
        int
        depends on BT_BLUEDROID_ENABLED
        default 0 if BT_LOG_APPL_TRACE_LEVEL_NONE
        default 1 if BT_LOG_APPL_TRACE_LEVEL_ERROR
        default 2 if BT_LOG_APPL_TRACE_LEVEL_WARNING
        default 3 if BT_LOG_APPL_TRACE_LEVEL_API
        default 4 if BT_LOG_APPL_TRACE_LEVEL_EVENT
        default 5 if BT_LOG_APPL_TRACE_LEVEL_DEBUG
        default 6 if BT_LOG_APPL_TRACE_LEVEL_VERBOSE
        default 2

    choice BT_LOG_GATT_TRACE_LEVEL
        prompt "GATT layer"
        default BT_LOG_GATT_TRACE_LEVEL_WARNING
        depends on BT_BLUEDROID_ENABLED && !BT_STACK_NO_LOG
        help
            Define BT trace level for GATT layer

        config BT_LOG_GATT_TRACE_LEVEL_NONE
            bool "NONE"
        config BT_LOG_GATT_TRACE_LEVEL_ERROR
            bool "ERROR"
        config BT_LOG_GATT_TRACE_LEVEL_WARNING
            bool "WARNING"
        config BT_LOG_GATT_TRACE_LEVEL_API
            bool "API"
        config BT_LOG_GATT_TRACE_LEVEL_EVENT
            bool "EVENT"
        config BT_LOG_GATT_TRACE_LEVEL_DEBUG
            bool "DEBUG"
        config BT_LOG_GATT_TRACE_LEVEL_VERBOSE
            bool "VERBOSE"
    endchoice

    config BT_LOG_GATT_TRACE_LEVEL
        int
        depends on BT_BLUEDROID_ENABLED
        default 0 if BT_LOG_GATT_TRACE_LEVEL_NONE
        default 1 if BT_LOG_GATT_TRACE_LEVEL_ERROR
        default 2 if BT_LOG_GATT_TRACE_LEVEL_WARNING
        default 3 if BT_LOG_GATT_TRACE_LEVEL_API
        default 4 if BT_LOG_GATT_TRACE_LEVEL_EVENT
        default 5 if BT_LOG_GATT_TRACE_LEVEL_DEBUG
        default 6 if BT_LOG_GATT_TRACE_LEVEL_VERBOSE
        default 2

    choice BT_LOG_SMP_TRACE_LEVEL
        prompt "SMP layer"
        default BT_LOG_SMP_TRACE_LEVEL_WARNING
        depends on BT_BLUEDROID_ENABLED && !BT_STACK_NO_LOG
        help
            Define BT trace level for SMP layer

        config BT_LOG_SMP_TRACE_LEVEL_NONE
            bool "NONE"
        config BT_LOG_SMP_TRACE_LEVEL_ERROR
            bool "ERROR"
        config BT_LOG_SMP_TRACE_LEVEL_WARNING
            bool "WARNING"
        config BT_LOG_SMP_TRACE_LEVEL_API
            bool "API"
        config BT_LOG_SMP_TRACE_LEVEL_EVENT
            bool "EVENT"
        config BT_LOG_SMP_TRACE_LEVEL_DEBUG
            bool "DEBUG"
        config BT_LOG_SMP_TRACE_LEVEL_VERBOSE
            bool "VERBOSE"
    endchoice

    config BT_LOG_SMP_TRACE_LEVEL
        int
        depends on BT_BLUEDROID_ENABLED
        default 0 if BT_LOG_SMP_TRACE_LEVEL_NONE
        default 1 if BT_LOG_SMP_TRACE_LEVEL_ERROR
        default 2 if BT_LOG_SMP_TRACE_LEVEL_WARNING
        default 3 if BT_LOG_SMP_TRACE_LEVEL_API
        default 4 if BT_LOG_SMP_TRACE_LEVEL_EVENT
        default 5 if BT_LOG_SMP_TRACE_LEVEL_DEBUG
        default 6 if BT_LOG_SMP_TRACE_LEVEL_VERBOSE
        default 2

    choice BT_LOG_BTIF_TRACE_LEVEL
        prompt "BTIF layer"
        default BT_LOG_BTIF_TRACE_LEVEL_WARNING
        depends on BT_BLUEDROID_ENABLED && !BT_STACK_NO_LOG
        help
            Define BT trace level for BTIF layer

        config BT_LOG_BTIF_TRACE_LEVEL_NONE
            bool "NONE"
        config BT_LOG_BTIF_TRACE_LEVEL_ERROR
            bool "ERROR"
        config BT_LOG_BTIF_TRACE_LEVEL_WARNING
            bool "WARNING"
        config BT_LOG_BTIF_TRACE_LEVEL_API
            bool "API"
        config BT_LOG_BTIF_TRACE_LEVEL_EVENT
            bool "EVENT"
        config BT_LOG_BTIF_TRACE_LEVEL_DEBUG
            bool "DEBUG"
        config BT_LOG_BTIF_TRACE_LEVEL_VERBOSE
            bool "VERBOSE"
    endchoice

    config BT_LOG_BTIF_TRACE_LEVEL
        int
        depends on BT_BLUEDROID_ENABLED
        default 0 if BT_LOG_BTIF_TRACE_LEVEL_NONE
        default 1 if BT_LOG_BTIF_TRACE_LEVEL_ERROR
        default 2 if BT_LOG_BTIF_TRACE_LEVEL_WARNING
        default 3 if BT_LOG_BTIF_TRACE_LEVEL_API
        default 4 if BT_LOG_BTIF_TRACE_LEVEL_EVENT
        default 5 if BT_LOG_BTIF_TRACE_LEVEL_DEBUG
        default 6 if BT_LOG_BTIF_TRACE_LEVEL_VERBOSE
        default 2

    choice BT_LOG_BTC_TRACE_LEVEL
        prompt "BTC layer"
        default BT_LOG_BTC_TRACE_LEVEL_WARNING
        depends on BT_BLUEDROID_ENABLED && !BT_STACK_NO_LOG
        help
            Define BT trace level for BTC layer

        config BT_LOG_BTC_TRACE_LEVEL_NONE
            bool "NONE"
        config BT_LOG_BTC_TRACE_LEVEL_ERROR
            bool "ERROR"
        config BT_LOG_BTC_TRACE_LEVEL_WARNING
            bool "WARNING"
        config BT_LOG_BTC_TRACE_LEVEL_API
            bool "API"
        config BT_LOG_BTC_TRACE_LEVEL_EVENT
            bool "EVENT"
        config BT_LOG_BTC_TRACE_LEVEL_DEBUG
            bool "DEBUG"
        config BT_LOG_BTC_TRACE_LEVEL_VERBOSE
            bool "VERBOSE"
    endchoice

    config BT_LOG_BTC_TRACE_LEVEL
        int
        depends on BT_BLUEDROID_ENABLED
        default 0 if BT_LOG_BTC_TRACE_LEVEL_NONE
        default 1 if BT_LOG_BTC_TRACE_LEVEL_ERROR
        default 2 if BT_LOG_BTC_TRACE_LEVEL_WARNING
        default 3 if BT_LOG_BTC_TRACE_LEVEL_API
        default 4 if BT_LOG_BTC_TRACE_LEVEL_EVENT
        default 5 if BT_LOG_BTC_TRACE_LEVEL_DEBUG
        default 6 if BT_LOG_BTC_TRACE_LEVEL_VERBOSE
        default 2

    choice BT_LOG_OSI_TRACE_LEVEL
        prompt "OSI layer"
        default BT_LOG_OSI_TRACE_LEVEL_WARNING
        depends on BT_BLUEDROID_ENABLED && !BT_STACK_NO_LOG
        help
            Define BT trace level for OSI layer

        config BT_LOG_OSI_TRACE_LEVEL_NONE
            bool "NONE"
        config BT_LOG_OSI_TRACE_LEVEL_ERROR
            bool "ERROR"
        config BT_LOG_OSI_TRACE_LEVEL_WARNING
            bool "WARNING"
        config BT_LOG_OSI_TRACE_LEVEL_API
            bool "API"
        config BT_LOG_OSI_TRACE_LEVEL_EVENT
            bool "EVENT"
        config BT_LOG_OSI_TRACE_LEVEL_DEBUG
            bool "DEBUG"
        config BT_LOG_OSI_TRACE_LEVEL_VERBOSE
            bool "VERBOSE"
    endchoice

    config BT_LOG_OSI_TRACE_LEVEL
        int
        depends on BT_BLUEDROID_ENABLED
        default 0 if BT_LOG_OSI_TRACE_LEVEL_NONE
        default 1 if BT_LOG_OSI_TRACE_LEVEL_ERROR
        default 2 if BT_LOG_OSI_TRACE_LEVEL_WARNING
        default 3 if BT_LOG_OSI_TRACE_LEVEL_API
        default 4 if BT_LOG_OSI_TRACE_LEVEL_EVENT
        default 5 if BT_LOG_OSI_TRACE_LEVEL_DEBUG
        default 6 if BT_LOG_OSI_TRACE_LEVEL_VERBOSE
        default 2

    choice BT_LOG_BLUFI_TRACE_LEVEL
        prompt "BLUFI layer"
        default BT_LOG_BLUFI_TRACE_LEVEL_WARNING
        depends on BT_BLUEDROID_ENABLED && !BT_STACK_NO_LOG
        help
            Define BT trace level for BLUFI layer

        config BT_LOG_BLUFI_TRACE_LEVEL_NONE
            bool "NONE"
        config BT_LOG_BLUFI_TRACE_LEVEL_ERROR
            bool "ERROR"
        config BT_LOG_BLUFI_TRACE_LEVEL_WARNING
            bool "WARNING"
        config BT_LOG_BLUFI_TRACE_LEVEL_API
            bool "API"
        config BT_LOG_BLUFI_TRACE_LEVEL_EVENT
            bool "EVENT"
        config BT_LOG_BLUFI_TRACE_LEVEL_DEBUG
            bool "DEBUG"
        config BT_LOG_BLUFI_TRACE_LEVEL_VERBOSE
            bool "VERBOSE"
    endchoice

    config BT_LOG_BLUFI_TRACE_LEVEL
        int
        depends on BT_BLUEDROID_ENABLED
        default 0 if BT_LOG_BLUFI_TRACE_LEVEL_NONE
        default 1 if BT_LOG_BLUFI_TRACE_LEVEL_ERROR
        default 2 if BT_LOG_BLUFI_TRACE_LEVEL_WARNING
        default 3 if BT_LOG_BLUFI_TRACE_LEVEL_API
        default 4 if BT_LOG_BLUFI_TRACE_LEVEL_EVENT
        default 5 if BT_LOG_BLUFI_TRACE_LEVEL_DEBUG
        default 6 if BT_LOG_BLUFI_TRACE_LEVEL_VERBOSE
        default 2

endmenu #BT DEBUG LOG LEVEL

config BT_ACL_CONNECTIONS
    int "BT/BLE MAX ACL CONNECTIONS(1~9)"
    depends on BT_BLUEDROID_ENABLED
    range 1 9
    default 4
    help
        Maximum BT/BLE connection count. The ESP32-C3/S3 chip supports a maximum of 10 instances,
        including ADV, SCAN and connections. The ESP32-C3/S3 chip can connect up to 9 devices if
        ADV or SCAN uses only one. If ADV and SCAN are both used, The ESP32-C3/S3 chip is connected
        to a maximum of 8 devices. Because Bluetooth cannot reclaim used instances once ADV or SCAN
        is used.

config BT_MULTI_CONNECTION_ENBALE
    bool "Enable BLE multi-connections"
    depends on BT_BLE_ENABLED
    default y
    help
        Enable this option if there are multiple connections

config BT_ALLOCATION_FROM_SPIRAM_FIRST
    bool "BT/BLE will first malloc the memory from the PSRAM"
    depends on BT_BLUEDROID_ENABLED
    default n
    help
        This select can save the internal RAM if there have the PSRAM

config BT_BLE_DYNAMIC_ENV_MEMORY
    bool "Use dynamic memory allocation in BT/BLE stack"
    depends on BT_BLUEDROID_ENABLED
    default n
    help
        This select can make the allocation of memory will become more flexible

config BT_BLE_HOST_QUEUE_CONG_CHECK
    bool "BLE queue congestion check"
    depends on BT_BLE_ENABLED
    default n
    help
        When scanning and scan duplicate is not enabled, if there are a lot of adv packets around
        or application layer handling adv packets is slow, it will cause the controller memory
        to run out. if enabled, adv packets will be lost when host queue is congested.

config BT_SMP_ENABLE
    bool
    depends on BT_BLUEDROID_ENABLED
    default BT_CLASSIC_ENABLED || BT_BLE_SMP_ENABLE

config BT_SMP_MAX_BONDS
    int "BT/BLE maximum bond device count"
    depends on BT_SMP_ENABLE
    range 1 32
    default 15
    help
        The number of security records for peer devices.

config BT_BLE_ACT_SCAN_REP_ADV_SCAN
    bool "Report adv data and scan response individually when BLE active scan"
    depends on BT_BLUEDROID_ENABLED && BT_BLE_ENABLED
    default n
    help
        Originally, when doing BLE active scan, Bluedroid will not report adv to application layer
        until receive scan response. This option is used to disable the behavior. When enable this option,
        Bluedroid will report adv data or scan response to application layer immediately.

        # Memory reserved at start of DRAM for Bluetooth stack

config BT_BLE_ESTAB_LINK_CONN_TOUT
    int "Timeout of BLE connection establishment"
    depends on BT_BLE_ENABLED
    range 1 60
    default 30
    help
        Bluetooth Connection establishment maximum time, if connection time exceeds this value, the connection
        establishment fails, ESP_GATTC_OPEN_EVT or ESP_GATTS_OPEN_EVT is triggered.

config BT_MAX_DEVICE_NAME_LEN
    int "length of bluetooth device name"
    depends on BT_BLUEDROID_ENABLED
    range 32 248
    default 32
    help
        Bluetooth Device name length shall be no larger than 248 octets, If the broadcast data cannot contain
        the complete device name, then only the shortname will be displayed, the rest parts that can't fit in
        will be truncated.

config BT_BLE_RPA_SUPPORTED
    bool "Update RPA to Controller"
    depends on (BT_BLUEDROID_ENABLED && ((BT_CONTROLLER_ENABLED && !SOC_BLE_DEVICE_PRIVACY_SUPPORTED) || BT_CONTROLLER_DISABLED)) # NOERROR
    default n if (BT_CONTROLLER_ENABLED && !SOC_BLE_DEVICE_PRIVACY_SUPPORTED)
    default y if BT_CONTROLLER_DISABLED
    help
        This enables controller RPA list function.
        For ESP32, ESP32 only support network privacy mode. If this option is enabled, ESP32 will only accept
        advertising packets from peer devices that contain private address, HW will not receive the advertising
        packets contain identity address after IRK changed. If this option is disabled, address resolution will
        be performed in the host, so the functions that require controller to resolve address in the white list
        cannot be used. This option is disabled by default on ESP32, please enable or disable this option according
        to your own needs.

        For other BLE chips, devices support network privacy mode and device privacy mode,
        users can switch the two modes according to their own needs. So this option is enabled by default.

config BT_BLE_RPA_TIMEOUT
    int "Timeout of resolvable private address"
    depends on BT_BLE_ENABLED
    range 1 3600
    default 900
    help
        This set RPA timeout of Controller and Host.
        Default is 900 s (15 minutes). Range is 1 s to 1 hour (3600 s).

config BT_BLE_50_FEATURES_SUPPORTED
    bool "Enable BLE 5.0 features(please disable BLE 4.2 if enable BLE 5.0)"
    depends on (BT_BLE_ENABLED && ((BT_CONTROLLER_ENABLED && SOC_BLE_50_SUPPORTED) || BT_CONTROLLER_DISABLED))
    default y
    help
        Enabling this option activates BLE 5.0 features.
        This option is universally supported in chips that support BLE, except for ESP32.
        BLE 4.2 and BLE 5.0 cannot be used simultaneously.


config BT_BLE_42_FEATURES_SUPPORTED
    bool "Enable BLE 4.2 features(please disable BLE 5.0 if enable BLE 4.2)"
    depends on (BT_BLE_ENABLED && ((BT_CONTROLLER_ENABLED && SOC_BLE_50_SUPPORTED) || BT_CONTROLLER_DISABLED))
    default n
    help
        This enables BLE 4.2 features.
        This option is universally supported by all ESP chips with BLE capabilities.
        BLE 4.2 and BLE 5.0 cannot be used simultaneously.

config BT_BLE_FEAT_PERIODIC_ADV_SYNC_TRANSFER
    bool "Enable BLE periodic advertising sync transfer feature"
    depends on (BT_BLUEDROID_ENABLED && BT_BLE_50_FEATURES_SUPPORTED && ((BT_CONTROLLER_ENABLED && SOC_ESP_NIMBLE_CONTROLLER) || BT_CONTROLLER_DISABLED)) # NOERROR
    default n
    help
        This enables BLE periodic advertising sync transfer feature

config BT_BLE_FEAT_PERIODIC_ADV_ENH
    bool "Enable periodic adv enhancements(adi support)"
    depends on (BT_BLUEDROID_ENABLED && BT_BLE_50_FEATURES_SUPPORTED && ((BT_CONTROLLER_ENABLED && SOC_ESP_NIMBLE_CONTROLLER) || BT_CONTROLLER_DISABLED)) # NOERROR
    default n
    help
        Enable the periodic advertising enhancements

config BT_BLE_FEAT_CREATE_SYNC_ENH
    bool "Enable create sync enhancements(reporting disable and duplicate filtering enable support)"
    depends on (BT_BLUEDROID_ENABLED && BT_BLE_50_FEATURES_SUPPORTED && ((BT_CONTROLLER_ENABLED && SOC_ESP_NIMBLE_CONTROLLER) || BT_CONTROLLER_DISABLED)) # NOERROR
    default n
    help
        Enable the create sync enhancements

config BT_BLE_HIGH_DUTY_ADV_INTERVAL
    bool "Enable BLE high duty advertising interval feature"
    depends on BT_BLE_ENABLED
    default n
    help
        This enable BLE high duty advertising interval feature

config BT_ABORT_WHEN_ALLOCATION_FAILS
    bool "Abort when memory allocation fails in BT/BLE stack"
    depends on BT_BLUEDROID_ENABLED
    default n
    help
        This enables abort when memory allocation fails
