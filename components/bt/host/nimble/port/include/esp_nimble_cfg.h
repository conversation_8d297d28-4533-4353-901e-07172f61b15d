/*
 * SPDX-FileCopyrightText: 2015-2025 Espressif Systems (Shanghai) CO LTD
 *
 * SPDX-License-Identifier: Apache-2.0
 */

#ifndef __ESP_NIMBLE_CFG__
#define __ESP_NIMBLE_CFG__
#include "sdkconfig.h"

/**
 * This macro exists to ensure code includes this header when needed.  If code
 * checks the existence of a setting directly via ifdef without including this
 * header, the setting macro will silently evaluate to 0.  In contrast, an
 * attempt to use these macros without including this header will result in a
 * compiler error.
 */
#define MYNEWT_VAL(_name)                       MYNEWT_VAL_ ## _name
#define MYNEWT_VAL_CHOICE(_name, _val)          MYNEWT_VAL_ ## _name ## __ ## _val

#ifndef IRAM_ATTR_64MCPU
#define IRAM_ATTR_64MCPU IRAM_ATTR
#endif


#if CONFIG_IDF_TARGET_ESP32 || CONFIG_IDF_TARGET_ESP32C3 || CONFIG_IDF_TARGET_ESP32S3
#define NIMBLE_CFG_CONTROLLER    0
#else
#define NIMBLE_CFG_CONTROLLER    CONFIG_BT_CONTROLLER_ENABLED
#endif

/*** kernel/os */
#ifndef MYNEWT_VAL_MSYS_1_BLOCK_COUNT
#ifdef CONFIG_BT_NIMBLE_MESH
#define MYNEWT_VAL_MSYS_1_BLOCK_COUNT (CONFIG_BT_NIMBLE_MSYS_1_BLOCK_COUNT + 8)
#else
#define MYNEWT_VAL_MSYS_1_BLOCK_COUNT CONFIG_BT_NIMBLE_MSYS_1_BLOCK_COUNT
#endif
#endif

#ifndef MYNEWT_VAL_MSYS_1_BLOCK_SIZE
#define MYNEWT_VAL_MSYS_1_BLOCK_SIZE CONFIG_BT_NIMBLE_MSYS_1_BLOCK_SIZE
#endif

#ifndef MYNEWT_VAL_MSYS_2_BLOCK_COUNT
#define MYNEWT_VAL_MSYS_2_BLOCK_COUNT CONFIG_BT_NIMBLE_MSYS_2_BLOCK_COUNT
#endif

#ifndef MYNEWT_VAL_MSYS_2_BLOCK_SIZE
#define MYNEWT_VAL_MSYS_2_BLOCK_SIZE CONFIG_BT_NIMBLE_MSYS_2_BLOCK_SIZE
#endif

#ifndef MYNEWT_VAL_OS_CPUTIME_FREQ
//#define MYNEWT_VAL_OS_CPUTIME_FREQ (1000000)
#define MYNEWT_VAL_OS_CPUTIME_FREQ (32000)
#endif

#ifndef MYNEWT_VAL_OS_CPUTIME_TIMER_NUM
#define MYNEWT_VAL_OS_CPUTIME_TIMER_NUM (5)
#endif

#ifndef MYNEWT_VAL_TIMER_5
#define MYNEWT_VAL_TIMER_5 1
#endif

#ifndef MYNEWT_VAL_BLE_HS_LOG_LVL
#define MYNEWT_VAL_BLE_HS_LOG_LVL CONFIG_BT_NIMBLE_LOG_LEVEL
#endif

#ifndef MYNEWT_VAL_NEWT_FEATURE_LOGCFG
#define MYNEWT_VAL_NEWT_FEATURE_LOGCFG 1
#endif

#ifndef MYNEWT_VAL_OS_TICKS_PER_SEC
#define MYNEWT_VAL_OS_TICKS_PER_SEC (100)
#endif

/*** nimble */
#if CONFIG_BT_NIMBLE_ENABLED
#ifndef MYNEWT_VAL_BLE_TRANSPORT_HS__native
#define MYNEWT_VAL_BLE_TRANSPORT_HS__native (1)
#endif

#ifndef MYNEWT_VAL_BLE_TRANSPORT_LL__native
#define MYNEWT_VAL_BLE_TRANSPORT_LL__native (0)
#endif

#ifndef CONFIG_BT_NIMBLE_50_FEATURE_SUPPORT
#define BLE_50_FEATURE_SUPPORT (0)
#else
#define BLE_50_FEATURE_SUPPORT (CONFIG_BT_NIMBLE_50_FEATURE_SUPPORT)
#endif
#endif

#ifndef CONFIG_BT_NIMBLE_EXT_ADV
#define MYNEWT_VAL_BLE_EXT_ADV (0)
#else
#define MYNEWT_VAL_BLE_EXT_ADV (CONFIG_BT_NIMBLE_EXT_ADV)
#endif

#ifndef CONFIG_BT_NIMBLE_EXT_ADV_MAX_SIZE
#define MYNEWT_VAL_BLE_EXT_ADV_MAX_SIZE (31)
#else
#define MYNEWT_VAL_BLE_EXT_ADV_MAX_SIZE (CONFIG_BT_NIMBLE_EXT_ADV_MAX_SIZE)
#endif

#ifndef CONFIG_BT_NIMBLE_ENC_ADV_DATA
#define MYNEWT_VAL_ENC_ADV_DATA (0)
#else
#define MYNEWT_VAL_ENC_ADV_DATA (CONFIG_BT_NIMBLE_ENC_ADV_DATA)
#endif

#if MYNEWT_VAL(BLE_LL_CFG_FEAT_LL_EXT_ADV)
#define BLE_SCAN_RSP_DATA_MAX_LEN_N     (1650)
#else
/* In this case the value will be overwritten by 31 in controller. */
#define BLE_SCAN_RSP_DATA_MAX_LEN_N     (MYNEWT_VAL_BLE_EXT_ADV_MAX_SIZE)
#endif

#ifndef CONFIG_BT_NIMBLE_MAX_PERIODIC_SYNCS
#define MYNEWT_VAL_BLE_MAX_PERIODIC_SYNCS (0)
#else
#define MYNEWT_VAL_BLE_MAX_PERIODIC_SYNCS (CONFIG_BT_NIMBLE_MAX_PERIODIC_SYNCS)
#endif

#ifndef CONFIG_BT_NIMBLE_MAX_PERIODIC_ADVERTISER_LIST
#define MYNEWT_VAL_BLE_MAX_PERIODIC_ADVERTISER_LIST (0)
#else
#define MYNEWT_VAL_BLE_MAX_PERIODIC_ADVERTISER_LIST (CONFIG_BT_NIMBLE_MAX_PERIODIC_ADVERTISER_LIST)
#endif

#ifndef CONFIG_BT_NIMBLE_GATT_CACHING
#define MYNEWT_VAL_BLE_GATT_CACHING (0)
#else
#define MYNEWT_VAL_BLE_GATT_CACHING           (CONFIG_BT_NIMBLE_GATT_CACHING)

#ifdef CONFIG_BT_NIMBLE_GATT_CACHING_MAX_CONNS
#define MYNEWT_VAL_BLE_GATT_CACHING_MAX_CONNS (CONFIG_BT_NIMBLE_GATT_CACHING_MAX_CONNS)
#else
#define MYNEWT_VAL_BLE_GATT_CACHING_MAX_CONNS (0)
#endif

#ifdef CONFIG_BT_NIMBLE_GATT_CACHING_MAX_SVCS
#define MYNEWT_VAL_BLE_GATT_CACHING_MAX_SVCS  (CONFIG_BT_NIMBLE_GATT_CACHING_MAX_SVCS)
#else
#define MYNEWT_VAL_BLE_GATT_CACHING_MAX_SVCS  (0)
#endif

#ifdef CONFIG_BT_NIMBLE_GATT_CACHING_MAX_CHRS
#define MYNEWT_VAL_BLE_GATT_CACHING_MAX_CHRS  (CONFIG_BT_NIMBLE_GATT_CACHING_MAX_CHRS)
#else
#define MYNEWT_VAL_BLE_GATT_CACHING_MAX_CHRS  (0)
#endif

#ifdef CONFIG_BT_NIMBLE_GATT_CACHING_MAX_DSCS
#define MYNEWT_VAL_BLE_GATT_CACHING_MAX_DSCS  (CONFIG_BT_NIMBLE_GATT_CACHING_MAX_DSCS)
#else
#define MYNEWT_VAL_BLE_GATT_CACHING_MAX_DSCS  (0)
#endif

#ifdef CONFIG_BT_NIMBLE_GATT_CACHING_DISABLE_AUTO
#define MYNEWT_VAL_BLE_GATT_CACHING_DISABLE_AUTO (CONFIG_BT_NIMBLE_GATT_CACHING_DISABLE_AUTO)
#else
#define MYNEWT_VAL_BLE_GATT_CACHING_DISABLE_AUTO (0)
#endif

#endif

#ifndef MYNEWT_VAL_BLE_GATT_CSFC_SIZE
#define MYNEWT_VAL_BLE_GATT_CSFC_SIZE (1)
#endif

#ifndef CONFIG_BT_NIMBLE_MAX_EXT_ADV_INSTANCES
#define MYNEWT_VAL_BLE_MULTI_ADV_INSTANCES (1)
#else
#define MYNEWT_VAL_BLE_MULTI_ADV_INSTANCES (CONFIG_BT_NIMBLE_MAX_EXT_ADV_INSTANCES)
#endif

#ifndef CONFIG_BT_NIMBLE_ENABLE_PERIODIC_ADV
#define MYNEWT_VAL_BLE_PERIODIC_ADV (0)
#else
#define MYNEWT_VAL_BLE_PERIODIC_ADV (CONFIG_BT_NIMBLE_ENABLE_PERIODIC_ADV)
#endif

#ifndef CONFIG_NIMBLE_MAX_CONNECTIONS
#define MYNEWT_VAL_BLE_MAX_CONNECTIONS (4)
#else
#define MYNEWT_VAL_BLE_MAX_CONNECTIONS (CONFIG_NIMBLE_MAX_CONNECTIONS)
#endif

#ifndef CONFIG_BT_NIMBLE_ROLE_BROADCASTER
#define MYNEWT_VAL_BLE_ROLE_BROADCASTER (0)
#else
#define MYNEWT_VAL_BLE_ROLE_BROADCASTER (CONFIG_BT_NIMBLE_ROLE_BROADCASTER)
#endif

#ifndef CONFIG_BT_NIMBLE_ROLE_CENTRAL
#define MYNEWT_VAL_BLE_ROLE_CENTRAL (0)
#else
#define MYNEWT_VAL_BLE_ROLE_CENTRAL (CONFIG_BT_NIMBLE_ROLE_CENTRAL)
#endif

#ifndef CONFIG_BT_NIMBLE_ROLE_OBSERVER
#define MYNEWT_VAL_BLE_ROLE_OBSERVER (0)
#else
#define MYNEWT_VAL_BLE_ROLE_OBSERVER (CONFIG_BT_NIMBLE_ROLE_OBSERVER)
#endif

#ifndef CONFIG_BT_NIMBLE_ROLE_PERIPHERAL
#define MYNEWT_VAL_BLE_ROLE_PERIPHERAL (0)
#else
#define MYNEWT_VAL_BLE_ROLE_PERIPHERAL (CONFIG_BT_NIMBLE_ROLE_PERIPHERAL)
#endif

#ifndef MYNEWT_VAL_BLE_WHITELIST
#define MYNEWT_VAL_BLE_WHITELIST (1)
#endif

#ifndef CONFIG_BT_NIMBLE_53_FEATURE_SUPPORT
#define BLE_53_FEATURE_SUPPORT (0)
#else
#define BLE_53_FEATURE_SUPPORT (CONFIG_BT_NIMBLE_53_FEATURE_SUPPORT)
#endif

#ifndef CONFIG_BT_NIMBLE_SUBRATE
#define MYNEWT_VAL_BLE_CONN_SUBRATING (0)
#else
#define MYNEWT_VAL_BLE_CONN_SUBRATING (CONFIG_BT_NIMBLE_SUBRATE)
#endif
#ifndef CONFIG_BT_NIMBLE_PERIODIC_ADV_ENH
#define MYNEWT_VAL_BLE_PERIODIC_ADV_ENH (0)
#else
#define MYNEWT_VAL_BLE_PERIODIC_ADV_ENH (CONFIG_BT_NIMBLE_PERIODIC_ADV_ENH)
#endif

/*** @apache-mynewt-nimble/nimble/controller */
/*** @apache-mynewt-nimble/nimble/controller */
#ifndef MYNEWT_VAL_BLE_CONTROLLER
#if NIMBLE_CFG_CONTROLLER
#define MYNEWT_VAL_BLE_CONTROLLER (1)
#else
#define MYNEWT_VAL_BLE_CONTROLLER (0)
#endif
#endif

#ifndef MYNEWT_VAL_BLE_VERSION
#define MYNEWT_VAL_BLE_VERSION (54)
#endif

#ifndef MYNEWT_VAL_BLE_LL_ADD_STRICT_SCHED_PERIODS
#define MYNEWT_VAL_BLE_LL_ADD_STRICT_SCHED_PERIODS (0)
#endif

#ifndef MYNEWT_VAL_BLE_LL_CFG_FEAT_CONN_PARAM_REQ
#define MYNEWT_VAL_BLE_LL_CFG_FEAT_CONN_PARAM_REQ (1)
#endif

#ifndef MYNEWT_VAL_BLE_LL_CFG_FEAT_DATA_LEN_EXT
#define MYNEWT_VAL_BLE_LL_CFG_FEAT_DATA_LEN_EXT (1)
#endif

#ifndef MYNEWT_VAL_BLE_LL_CFG_FEAT_EXT_SCAN_FILT
#define MYNEWT_VAL_BLE_LL_CFG_FEAT_EXT_SCAN_FILT (0)
#endif

#ifndef CONFIG_BT_NIMBLE_LL_CFG_FEAT_LE_2M_PHY
#define MYNEWT_VAL_BLE_LL_CFG_FEAT_LE_2M_PHY (0)
#else
#define MYNEWT_VAL_BLE_LL_CFG_FEAT_LE_2M_PHY (CONFIG_BT_NIMBLE_LL_CFG_FEAT_LE_2M_PHY)
#endif

#ifndef CONFIG_BT_NIMBLE_LL_CFG_FEAT_LE_CODED_PHY
#define MYNEWT_VAL_BLE_LL_CFG_FEAT_LE_CODED_PHY (0)
#else
#define MYNEWT_VAL_BLE_LL_CFG_FEAT_LE_CODED_PHY (CONFIG_BT_NIMBLE_LL_CFG_FEAT_LE_CODED_PHY)
#endif

#ifndef MYNEWT_VAL_BLE_LL_CFG_FEAT_LE_CSA2
#define MYNEWT_VAL_BLE_LL_CFG_FEAT_LE_CSA2 (1)
#endif

#ifndef MYNEWT_VAL_BLE_PHY_DBG_TIME_ADDRESS_END_PIN
#define MYNEWT_VAL_BLE_PHY_DBG_TIME_ADDRESS_END_PIN (-1)
#endif

#ifndef MYNEWT_VAL_BLE_PHY_DBG_TIME_TXRXEN_READY_PIN
#define MYNEWT_VAL_BLE_PHY_DBG_TIME_TXRXEN_READY_PIN (-1)
#endif

#ifndef MYNEWT_VAL_BLE_PHY_DBG_TIME_WFR_PIN
#define MYNEWT_VAL_BLE_PHY_DBG_TIME_WFR_PIN (-1)
#endif


#ifndef MYNEWT_VAL_BLE_DEVICE
#define MYNEWT_VAL_BLE_DEVICE (0)
#endif

#ifndef MYNEWT_VAL_BLE_LL_CFG_FEAT_LL_PRIVACY
#define MYNEWT_VAL_BLE_LL_CFG_FEAT_LL_PRIVACY (1)
#endif

#ifndef MYNEWT_VAL_BLE_LL_CFG_FEAT_SLAVE_INIT_FEAT_XCHG
#define MYNEWT_VAL_BLE_LL_CFG_FEAT_SLAVE_INIT_FEAT_XCHG (1)
#endif

#ifndef MYNEWT_VAL_BLE_LL_CONN_INIT_MAX_TX_BYTES
#define MYNEWT_VAL_BLE_LL_CONN_INIT_MAX_TX_BYTES (27)
#endif

#ifndef MYNEWT_VAL_BLE_LL_CONN_INIT_MIN_WIN_OFFSET
#define MYNEWT_VAL_BLE_LL_CONN_INIT_MIN_WIN_OFFSET (0)
#endif

#ifndef MYNEWT_VAL_BLE_LL_CONN_INIT_SLOTS
#define MYNEWT_VAL_BLE_LL_CONN_INIT_SLOTS (4)
#endif

#ifndef MYNEWT_VAL_BLE_LL_DBG_HCI_CMD_PIN
#define MYNEWT_VAL_BLE_LL_DBG_HCI_CMD_PIN (-1)
#endif

/* Overridden by @apache-mynewt-nimble/nimble/controller (defined by @apache-mynewt-nimble/nimble/controller) */
#ifndef CONFIG_BT_NIMBLE_LL_CFG_FEAT_LE_ENCRYPTION
#define MYNEWT_VAL_BLE_LL_CFG_FEAT_LE_ENCRYPTION (0)
#else
#define MYNEWT_VAL_BLE_LL_CFG_FEAT_LE_ENCRYPTION (CONFIG_BT_NIMBLE_LL_CFG_FEAT_LE_ENCRYPTION)
#endif

/* Value copied from BLE_LL_OUR_SCA */
#ifndef MYNEWT_VAL_BLE_LL_SCA
#define MYNEWT_VAL_BLE_LL_SCA (60)
#endif

#ifndef MYNEWT_VAL_BLE_LL_ADD_STRICT_SCHED_PERIODS
#define MYNEWT_VAL_BLE_LL_ADD_STRICT_SCHED_PERIODS (0)
#endif

#ifndef MYNEWT_VAL_BLE_LL_CFG_FEAT_CONN_PARAM_REQ
#define MYNEWT_VAL_BLE_LL_CFG_FEAT_CONN_PARAM_REQ (1)
#endif

#ifndef MYNEWT_VAL_BLE_LL_CFG_FEAT_DATA_LEN_EXT
#define MYNEWT_VAL_BLE_LL_CFG_FEAT_DATA_LEN_EXT (1)
#endif

#ifndef MYNEWT_VAL_BLE_LL_CFG_FEAT_EXT_SCAN_FILT
#define MYNEWT_VAL_BLE_LL_CFG_FEAT_EXT_SCAN_FILT (0)
#endif

#ifndef MYNEWT_VAL_BLE_LL_STRICT_CONN_SCHEDULING
#define MYNEWT_VAL_BLE_LL_STRICT_CONN_SCHEDULING (0)
#endif

#ifndef CONFIG_BT_NIMBLE_HCI_UART_BAUD
#define MYNEWT_VAL_BLE_HCI_UART_BAUD (115200)
#else
#define MYNEWT_VAL_BLE_HCI_UART_BAUD (CONFIG_BT_NIMBLE_HCI_UART_BAUD)
#endif

#ifndef MYNEWT_VAL_BLE_HCI_UART_DATA_BITS
#define MYNEWT_VAL_BLE_HCI_UART_DATA_BITS (8)
#endif

#ifndef CONFIG_UART_FLOW_CTRL
#define MYNEWT_VAL_BLE_HCI_UART_FLOW_CTRL (1)
#else
#define MYNEWT_VAL_BLE_HCI_UART_FLOW_CTRL (CONFIG_UART_FLOW_CTRL)
#endif

#ifndef MYNEWT_VAL_BLE_HCI_UART_PARITY
#define MYNEWT_VAL_BLE_HCI_UART_PARITY (0) // HAL_UART_PARITY_NONE
#endif

#ifndef CONFIG_BT_NIMBLE_HCI_UART_PORT
#define MYNEWT_VAL_BLE_HCI_UART_PORT (0)
#else
#define MYNEWT_VAL_BLE_HCI_UART_PORT (CONFIG_BT_NIMBLE_HCI_UART_PORT)
#endif

/* Overridden by @apache-mynewt-nimble/nimble/controller (defined by @apache-mynewt-nimble/nimble/controller) */
#ifndef MYNEWT_VAL_BLE_LL_CFG_FEAT_LE_CSA2
#define MYNEWT_VAL_BLE_LL_CFG_FEAT_LE_CSA2 (1)
#endif

#ifndef MYNEWT_VAL_BLE_LL_CFG_FEAT_LE_ENCRYPTION
#define MYNEWT_VAL_BLE_LL_CFG_FEAT_LE_ENCRYPTION (1)
#endif

#ifndef MYNEWT_VAL_BLE_LL_CFG_FEAT_LE_PING
#define MYNEWT_VAL_BLE_LL_CFG_FEAT_LE_PING (MYNEWT_VAL_BLE_LL_CFG_FEAT_LE_ENCRYPTION)
#endif

#ifndef MYNEWT_VAL_BLE_LL_CFG_FEAT_LL_EXT_ADV
#define MYNEWT_VAL_BLE_LL_CFG_FEAT_LL_EXT_ADV (MYNEWT_VAL_BLE_EXT_ADV)
#endif

/* Value copied from BLE_PERIODIC_ADV */
#ifndef MYNEWT_VAL_BLE_LL_CFG_FEAT_LL_PERIODIC_ADV
#define MYNEWT_VAL_BLE_LL_CFG_FEAT_LL_PERIODIC_ADV (CONFIG_BT_NIMBLE_ENABLE_PERIODIC_ADV)
#endif

/* Value copied from BLE_MAX_PERIODIC_SYNCS */
#ifndef MYNEWT_VAL_BLE_LL_CFG_FEAT_LL_PERIODIC_ADV_SYNC_CNT
#define MYNEWT_VAL_BLE_LL_CFG_FEAT_LL_PERIODIC_ADV_SYNC_CNT (MYNEWT_VAL_BLE_MAX_PERIODIC_SYNCS)
#endif


#define MYNEWT_VAL_BLE_LL_CFG_FEAT_LL_PERIODIC_ADV_SYNC_LIST_CNT (MYNEWT_VAL_BLE_MAX_PERIODIC_SYNCS)

#ifndef CONFIG_BT_NIMBLE_PERIODIC_ADV_SYNC_TRANSFER
#define MYNEWT_VAL_BLE_PERIODIC_ADV_SYNC_TRANSFER           (0)
#else
#define MYNEWT_VAL_BLE_PERIODIC_ADV_SYNC_TRANSFER           (CONFIG_BT_NIMBLE_PERIODIC_ADV_SYNC_TRANSFER)
#endif

/* Value copied from BLE_PERIODIC_ADV_SYNC_TRANSFER */
#define MYNEWT_VAL_BLE_LL_CFG_FEAT_LL_PERIODIC_ADV_SYNC_TRANSFER (CONFIG_BT_NIMBLE_PERIODIC_ADV_SYNC_TRANSFER)


#ifndef MYNEWT_VAL_BLE_LL_CFG_FEAT_LL_PRIVACY
#define MYNEWT_VAL_BLE_LL_CFG_FEAT_LL_PRIVACY (1)
#endif

#ifndef MYNEWT_VAL_BLE_LL_CFG_FEAT_SLAVE_INIT_FEAT_XCHG
#define MYNEWT_VAL_BLE_LL_CFG_FEAT_SLAVE_INIT_FEAT_XCHG (1)
#endif

#ifndef MYNEWT_VAL_BLE_LL_CONN_INIT_MAX_TX_BYTES
#define MYNEWT_VAL_BLE_LL_CONN_INIT_MAX_TX_BYTES (27)
#endif

#ifndef MYNEWT_VAL_BLE_LL_CONN_INIT_MIN_WIN_OFFSET
#define MYNEWT_VAL_BLE_LL_CONN_INIT_MIN_WIN_OFFSET (0)
#endif

#ifndef MYNEWT_VAL_BLE_LL_CONN_INIT_SLOTS
#define MYNEWT_VAL_BLE_LL_CONN_INIT_SLOTS (4)
#endif

#ifndef MYNEWT_VAL_BLE_LL_DIRECT_TEST_MODE
#define MYNEWT_VAL_BLE_LL_DIRECT_TEST_MODE (0)
#endif

/* Overridden by @apache-mynewt-nimble/nimble/controller (defined by @apache-mynewt-nimble/nimble/controller) */
#ifndef MYNEWT_VAL_BLE_LL_EXT_ADV_AUX_PTR_CNT
#define MYNEWT_VAL_BLE_LL_EXT_ADV_AUX_PTR_CNT (5)
#endif

#ifndef MYNEWT_VAL_BLE_LL_MASTER_SCA
#define MYNEWT_VAL_BLE_LL_MASTER_SCA (60)
#endif

#ifndef MYNEWT_VAL_BLE_LL_MAX_PKT_SIZE
#define MYNEWT_VAL_BLE_LL_MAX_PKT_SIZE (251)
#endif

#ifndef MYNEWT_VAL_BLE_LL_PRIO
#define MYNEWT_VAL_BLE_LL_PRIO (0)
#endif

#ifndef MYNEWT_VAL_BLE_LL_RESOLV_LIST_SIZE
#define MYNEWT_VAL_BLE_LL_RESOLV_LIST_SIZE (4)
#endif

#ifndef MYNEWT_VAL_BLE_LL_RNG_BUFSIZE
#define MYNEWT_VAL_BLE_LL_RNG_BUFSIZE (32)
#endif

#ifndef MYNEWT_VAL_BLE_LL_STRICT_CONN_SCHEDULING
#define MYNEWT_VAL_BLE_LL_STRICT_CONN_SCHEDULING (0)
#endif

#ifndef MYNEWT_VAL_BLE_LL_SUPP_MAX_RX_BYTES
#define MYNEWT_VAL_BLE_LL_SUPP_MAX_RX_BYTES (MYNEWT_VAL_BLE_LL_MAX_PKT_SIZE)
#endif

#ifndef MYNEWT_VAL_BLE_LL_SUPP_MAX_TX_BYTES
#define MYNEWT_VAL_BLE_LL_SUPP_MAX_TX_BYTES (MYNEWT_VAL_BLE_LL_MAX_PKT_SIZE)
#endif

#ifndef MYNEWT_VAL_BLE_LL_SYSVIEW
#define MYNEWT_VAL_BLE_LL_SYSVIEW (0)
#endif

#ifndef MYNEWT_VAL_BLE_LL_TX_PWR_DBM
#define MYNEWT_VAL_BLE_LL_TX_PWR_DBM (0)
#endif

#ifndef MYNEWT_VAL_BLE_LL_USECS_PER_PERIOD
#define MYNEWT_VAL_BLE_LL_USECS_PER_PERIOD (3250)
#endif

#ifndef MYNEWT_VAL_BLE_LL_VND_EVENT_ON_ASSERT
#define MYNEWT_VAL_BLE_LL_VND_EVENT_ON_ASSERT (0)
#endif

#ifndef MYNEWT_VAL_BLE_LL_WHITELIST_SIZE
#define MYNEWT_VAL_BLE_LL_WHITELIST_SIZE CONFIG_BT_NIMBLE_WHITELIST_SIZE
#endif

#ifndef MYNEWT_VAL_BLE_LP_CLOCK
#define MYNEWT_VAL_BLE_LP_CLOCK (1)
#endif

#ifndef MYNEWT_VAL_BLE_NUM_COMP_PKT_RATE
#define MYNEWT_VAL_BLE_NUM_COMP_PKT_RATE ((2 * OS_TICKS_PER_SEC))
#endif

#ifndef MYNEWT_VAL_BLE_PUBLIC_DEV_ADDR
#define MYNEWT_VAL_BLE_PUBLIC_DEV_ADDR (((uint8_t[6]){0x01,0x21,0x03,0x66,0x05,0x30}))
#endif

#ifndef MYNEWT_VAL_BLE_XTAL_SETTLE_TIME
#define MYNEWT_VAL_BLE_XTAL_SETTLE_TIME (0)
#endif

/*** @apache-mynewt-nimble/nimble/host */

#ifndef MYNEWT_VAL_BLE_L2CAP_COC_SDU_BUFF_COUNT
#define MYNEWT_VAL_BLE_L2CAP_COC_SDU_BUFF_COUNT CONFIG_BT_NIMBLE_L2CAP_COC_SDU_BUFF_COUNT
#endif

#if CONFIG_BT_NIMBLE_L2CAP_ENHANCED_COC || CONFIG_BT_NIMBLE_EATT_CHAN_NUM
#define MYNEWT_VAL_BLE_L2CAP_ENHANCED_COC (1)
#else
#define MYNEWT_VAL_BLE_L2CAP_ENHANCED_COC (0)
#endif

#ifndef MYNEWT_VAL_BLE_DYNAMIC_SERVICE
#ifdef CONFIG_BT_NIMBLE_DYNAMIC_SERVICE
#define MYNEWT_VAL_BLE_DYNAMIC_SERVICE CONFIG_BT_NIMBLE_DYNAMIC_SERVICE
#else
#define MYNEWT_VAL_BLE_DYNAMIC_SERVICE (0)
#endif
#endif

#ifndef MYNEWT_VAL_BLE_ATT_PREFERRED_MTU
#define MYNEWT_VAL_BLE_ATT_PREFERRED_MTU CONFIG_BT_NIMBLE_ATT_PREFERRED_MTU
#endif

#ifndef MYNEWT_VAL_BLE_ATT_SVR_FIND_INFO
#define MYNEWT_VAL_BLE_ATT_SVR_FIND_INFO (1)
#endif

#ifndef MYNEWT_VAL_BLE_ATT_SVR_FIND_TYPE
#define MYNEWT_VAL_BLE_ATT_SVR_FIND_TYPE (1)
#endif

#ifndef MYNEWT_VAL_BLE_ATT_SVR_INDICATE
#define MYNEWT_VAL_BLE_ATT_SVR_INDICATE (1)
#endif

#ifndef MYNEWT_VAL_BLE_ATT_SVR_MAX_PREP_ENTRIES
#define MYNEWT_VAL_BLE_ATT_SVR_MAX_PREP_ENTRIES (64)
#endif

#ifndef MYNEWT_VAL_BLE_ATT_SVR_NOTIFY
#define MYNEWT_VAL_BLE_ATT_SVR_NOTIFY (1)
#endif

#ifndef MYNEWT_VAL_BLE_ATT_SVR_NOTIFY_MULTI
#define MYNEWT_VAL_BLE_ATT_SVR_NOTIFY_MULTI (1)
#endif

#ifndef MYNEWT_VAL_BLE_ATT_SVR_QUEUED_WRITE
#define MYNEWT_VAL_BLE_ATT_SVR_QUEUED_WRITE (1)
#endif

#ifndef MYNEWT_VAL_BLE_ATT_SVR_QUEUED_WRITE_TMO
#define MYNEWT_VAL_BLE_ATT_SVR_QUEUED_WRITE_TMO (30000)
#endif

#ifndef MYNEWT_VAL_BLE_ATT_SVR_READ
#define MYNEWT_VAL_BLE_ATT_SVR_READ (1)
#endif

#ifndef MYNEWT_VAL_BLE_ATT_SVR_READ_BLOB
#define MYNEWT_VAL_BLE_ATT_SVR_READ_BLOB (1)
#endif

#ifndef MYNEWT_VAL_BLE_ATT_SVR_READ_GROUP_TYPE
#define MYNEWT_VAL_BLE_ATT_SVR_READ_GROUP_TYPE (1)
#endif

#ifndef MYNEWT_VAL_BLE_ATT_SVR_READ_MULT
#define MYNEWT_VAL_BLE_ATT_SVR_READ_MULT (1)
#endif

#ifndef MYNEWT_VAL_BLE_ATT_SVR_READ_MULT_VAR
#define MYNEWT_VAL_BLE_ATT_SVR_READ_MULT_VAR (1)
#endif

#ifndef MYNEWT_VAL_BLE_ATT_SVR_READ_TYPE
#define MYNEWT_VAL_BLE_ATT_SVR_READ_TYPE (1)
#endif

#ifndef MYNEWT_VAL_BLE_ATT_SVR_SIGNED_WRITE
#define MYNEWT_VAL_BLE_ATT_SVR_SIGNED_WRITE (1)
#endif

#ifndef MYNEWT_VAL_BLE_ATT_SVR_WRITE
#define MYNEWT_VAL_BLE_ATT_SVR_WRITE (1)
#endif

#ifndef MYNEWT_VAL_BLE_ATT_SVR_WRITE_NO_RSP
#define MYNEWT_VAL_BLE_ATT_SVR_WRITE_NO_RSP (1)
#endif

#ifndef MYNEWT_VAL_BLE_EATT_CHAN_NUM
#define MYNEWT_VAL_BLE_EATT_CHAN_NUM (CONFIG_BT_NIMBLE_EATT_CHAN_NUM)
#endif

#ifndef MYNEWT_VAL_BLE_EATT_LOG_LVL
#define MYNEWT_VAL_BLE_EATT_LOG_LVL (1)
#endif

#ifndef MYNEWT_VAL_BLE_EATT_LOG_MOD
#define MYNEWT_VAL_BLE_EATT_LOG_MOD (27)
#endif

#ifndef MYNEWT_VAL_BLE_EATT_MTU
#define MYNEWT_VAL_BLE_EATT_MTU (128)
#endif

#ifndef MYNEWT_VAL_BLE_CLIENT_SUPPORTED_FEATURES

#if MYNEWT_VAL_BLE_GATT_CACHING
#define MYNEWT_VAL_BLE_CLIENT_SUPPORTED_FEATURES_ROBUST_CACHING (1)
#else
#define MYNEWT_VAL_BLE_CLIENT_SUPPORTED_FEATURES_ROBUST_CACHING (0)
#endif //MYNEWT_VAL_BLE_GATT_CACHING

#if CONFIG_BT_NIMBLE_EATT_CHAN_NUM
#define MYNEWT_VAL_BLE_CLIENT_SUPPORTED_FEATURES_EATT (2)
#else
#define MYNEWT_VAL_BLE_CLIENT_SUPPORTED_FEATURES_EATT (0)
#endif //CONFIG_BT_NIMBLE_EATT_CHAN_NUM

#if MYNEWT_VAL_BLE_ATT_SVR_NOTIFY_MULTI
#define MYNEWT_VAL_BLE_CLIENT_SUPPORTED_FEATURES_NOTIFY_MULTI (4)
#else
#define MYNEWT_VAL_BLE_CLIENT_SUPPORTED_FEATURES_NOTIFY_MULTI (0)
#endif //MYNEWT_VAL_BLE_ATT_SVR_NOTIFY_MULTI

#define MYNEWT_VAL_BLE_CLIENT_SUPPORTED_FEATURES (                  \
        MYNEWT_VAL_BLE_CLIENT_SUPPORTED_FEATURES_ROBUST_CACHING |   \
        MYNEWT_VAL_BLE_CLIENT_SUPPORTED_FEATURES_EATT |             \
        MYNEWT_VAL_BLE_CLIENT_SUPPORTED_FEATURES_NOTIFY_MULTI       \
        )
#endif //MYNEWT_VAL_CLIENT_SUPPORTED_FEATURES

#ifndef MYNEWT_VAL_BLE_GAP_MAX_PENDING_CONN_PARAM_UPDATE
#define MYNEWT_VAL_BLE_GAP_MAX_PENDING_CONN_PARAM_UPDATE (1)
#endif

#ifndef MYNEWT_VAL_BLE_GATT_DISC_ALL_CHRS
#define MYNEWT_VAL_BLE_GATT_DISC_ALL_CHRS (MYNEWT_VAL_BLE_ROLE_CENTRAL)
#endif

#ifndef MYNEWT_VAL_BLE_GATT_DISC_ALL_DSCS
#define MYNEWT_VAL_BLE_GATT_DISC_ALL_DSCS (MYNEWT_VAL_BLE_ROLE_CENTRAL)
#endif

#ifndef MYNEWT_VAL_BLE_GATT_DISC_ALL_SVCS
#define MYNEWT_VAL_BLE_GATT_DISC_ALL_SVCS (MYNEWT_VAL_BLE_ROLE_CENTRAL)
#endif

#ifndef MYNEWT_VAL_BLE_GATT_DISC_CHR_UUID
#define MYNEWT_VAL_BLE_GATT_DISC_CHR_UUID (MYNEWT_VAL_BLE_ROLE_CENTRAL)
#endif

#ifndef MYNEWT_VAL_BLE_GATT_DISC_SVC_UUID
#define MYNEWT_VAL_BLE_GATT_DISC_SVC_UUID (MYNEWT_VAL_BLE_ROLE_CENTRAL)
#endif

#ifndef MYNEWT_VAL_BLE_GATT_FIND_INC_SVCS
#define MYNEWT_VAL_BLE_GATT_FIND_INC_SVCS (MYNEWT_VAL_BLE_ROLE_CENTRAL)
#endif

#ifndef MYNEWT_VAL_BLE_GATT_INDICATE
#define MYNEWT_VAL_BLE_GATT_INDICATE (1)
#endif

#ifndef MYNEWT_VAL_BLE_GATT_MAX_PROCS
#define MYNEWT_VAL_BLE_GATT_MAX_PROCS (CONFIG_BT_NIMBLE_GATT_MAX_PROCS)
#endif

#ifndef MYNEWT_VAL_BLE_GATT_NOTIFY
#define MYNEWT_VAL_BLE_GATT_NOTIFY (1)
#endif

#ifndef MYNEWT_VAL_BLE_GATT_NOTIFY_MULTIPLE
#define MYNEWT_VAL_BLE_GATT_NOTIFY_MULTIPLE (1)
#endif

#ifndef MYNEWT_VAL_BLE_GATT_READ
#define MYNEWT_VAL_BLE_GATT_READ (MYNEWT_VAL_BLE_ROLE_CENTRAL)
#endif

#ifndef MYNEWT_VAL_BLE_GATT_READ_LONG
#define MYNEWT_VAL_BLE_GATT_READ_LONG (MYNEWT_VAL_BLE_ROLE_CENTRAL)
#endif

#ifndef MYNEWT_VAL_BLE_GATT_READ_MAX_ATTRS
#define MYNEWT_VAL_BLE_GATT_READ_MAX_ATTRS (8)
#endif

#ifndef MYNEWT_VAL_BLE_GATT_READ_MULT
#define MYNEWT_VAL_BLE_GATT_READ_MULT (MYNEWT_VAL_BLE_ROLE_CENTRAL)
#endif

#ifndef MYNEWT_VAL_BLE_GATT_READ_MULT_VAR
#define MYNEWT_VAL_BLE_GATT_READ_MULT_VAR (MYNEWT_VAL_BLE_ROLE_CENTRAL)
#endif

#ifndef MYNEWT_VAL_BLE_GATT_READ_UUID
#define MYNEWT_VAL_BLE_GATT_READ_UUID (MYNEWT_VAL_BLE_ROLE_CENTRAL)
#endif

#ifndef MYNEWT_VAL_BLE_GATT_RESUME_RATE
#define MYNEWT_VAL_BLE_GATT_RESUME_RATE (1000)
#endif

#ifndef MYNEWT_VAL_BLE_GATT_SIGNED_WRITE
#define MYNEWT_VAL_BLE_GATT_SIGNED_WRITE (MYNEWT_VAL_BLE_ROLE_CENTRAL)
#endif

#ifndef MYNEWT_VAL_BLE_GATT_WRITE
#define MYNEWT_VAL_BLE_GATT_WRITE (MYNEWT_VAL_BLE_ROLE_CENTRAL)
#endif

#ifndef MYNEWT_VAL_BLE_GATT_WRITE_LONG
#define MYNEWT_VAL_BLE_GATT_WRITE_LONG (MYNEWT_VAL_BLE_ROLE_CENTRAL)
#endif

#ifndef MYNEWT_VAL_BLE_GATT_WRITE_MAX_ATTRS
#define MYNEWT_VAL_BLE_GATT_WRITE_MAX_ATTRS (4)
#endif

#ifndef MYNEWT_VAL_BLE_GATT_WRITE_NO_RSP
#define MYNEWT_VAL_BLE_GATT_WRITE_NO_RSP (MYNEWT_VAL_BLE_ROLE_CENTRAL)
#endif

#ifndef MYNEWT_VAL_BLE_GATT_WRITE_RELIABLE
#define MYNEWT_VAL_BLE_GATT_WRITE_RELIABLE (MYNEWT_VAL_BLE_ROLE_CENTRAL)
#endif

#ifndef MYNEWT_VAL_BLE_GATT_BLOB_TRANSFER
#define MYNEWT_VAL_BLE_GATT_BLOB_TRANSFER (CONFIG_BT_NIMBLE_BLE_GATT_BLOB_TRANSFER)
#endif

#ifndef MYNEWT_VAL_BLE_HOST
#define MYNEWT_VAL_BLE_HOST (1)
#endif

#ifndef MYNEWT_VAL_ESP_BLE_MESH
#ifdef CONFIG_BLE_MESH_HCI_5_0
#define MYNEWT_VAL_ESP_BLE_MESH (1)
#else
#define MYNEWT_VAL_ESP_BLE_MESH (0)
#endif
#endif

#ifndef MYNEWT_VAL_BLE_HS_DEBUG
#ifdef CONFIG_BT_NIMBLE_DEBUG
#define MYNEWT_VAL_BLE_HS_DEBUG (1)
#else
#define MYNEWT_VAL_BLE_HS_DEBUG (0)
#endif
#endif

#ifndef MYNEWT_VAL_BLE_SM_SC_DEBUG_KEYS
#define MYNEWT_VAL_BLE_SM_SC_DEBUG_KEYS CONFIG_BT_NIMBLE_SM_SC_DEBUG_KEYS
#endif

#ifndef MYNEWT_VAL_BLE_HS_AUTO_START
#define MYNEWT_VAL_BLE_HS_AUTO_START (1)
#endif

#ifdef CONFIG_BT_NIMBLE_HS_FLOW_CTRL
#define MYNEWT_VAL_BLE_HS_FLOW_CTRL (1)
#else
#define MYNEWT_VAL_BLE_HS_FLOW_CTRL (0)
#endif

#ifndef CONFIG_BT_NIMBLE_HS_FLOW_CTRL_ITVL
#define MYNEWT_VAL_BLE_HS_FLOW_CTRL_ITVL (2000)
#else
#define MYNEWT_VAL_BLE_HS_FLOW_CTRL_ITVL CONFIG_BT_NIMBLE_HS_FLOW_CTRL_ITVL
#endif

#ifndef CONFIG_BT_NIMBLE_HS_FLOW_CTRL_THRESH
#define MYNEWT_VAL_BLE_HS_FLOW_CTRL_THRESH (2)
#else
#define MYNEWT_VAL_BLE_HS_FLOW_CTRL_THRESH CONFIG_BT_NIMBLE_HS_FLOW_CTRL_THRESH
#endif

#ifndef MYNEWT_VAL_BLE_HS_FLOW_CTRL_TX_ON_DISCONNECT
#define MYNEWT_VAL_BLE_HS_FLOW_CTRL_TX_ON_DISCONNECT CONFIG_BT_NIMBLE_HS_FLOW_CTRL_TX_ON_DISCONNECT
#endif

#ifndef MYNEWT_VAL_BLE_HS_PHONY_HCI_ACKS
#define MYNEWT_VAL_BLE_HS_PHONY_HCI_ACKS (0)
#endif

#ifndef MYNEWT_VAL_BLE_HS_REQUIRE_OS
#define MYNEWT_VAL_BLE_HS_REQUIRE_OS (1)
#endif

#ifndef MYNEWT_VAL_BLE_HS_STOP_ON_SHUTDOWN
#define MYNEWT_VAL_BLE_HS_STOP_ON_SHUTDOWN (1)
#endif

#ifndef MYNEWT_VAL_BLE_HS_STOP_ON_SHUTDOWN_TIMEOUT
#define MYNEWT_VAL_BLE_HS_STOP_ON_SHUTDOWN_TIMEOUT CONFIG_BT_NIMBLE_HS_STOP_TIMEOUT_MS
#endif

#ifndef MYNEWT_VAL_BLE_HS_SYSINIT_STAGE
#define MYNEWT_VAL_BLE_HS_SYSINIT_STAGE (200)
#endif

#if CONFIG_BT_NIMBLE_EATT_CHAN_NUM > CONFIG_BT_NIMBLE_L2CAP_COC_MAX_NUM
#define MYNEWT_VAL_BLE_L2CAP_COC_MAX_NUM (CONFIG_BT_NIMBLE_EATT_CHAN_NUM)
#else
#ifndef CONFIG_BT_NIMBLE_L2CAP_COC_MAX_NUM
#define MYNEWT_VAL_BLE_L2CAP_COC_MAX_NUM (2)
#else
#define MYNEWT_VAL_BLE_L2CAP_COC_MAX_NUM CONFIG_BT_NIMBLE_L2CAP_COC_MAX_NUM
#endif //CONFIG_BT_NIMBLE_L2CAP_COC_MAX_NUM
#endif //CONFIG_BT_NIMBLE_EATT_CHAN_NUM

#ifndef MYNEWT_VAL_BLE_L2CAP_COC_MPS
#define MYNEWT_VAL_BLE_L2CAP_COC_MPS (MYNEWT_VAL_MSYS_1_BLOCK_SIZE-8)
#endif

#ifndef MYNEWT_VAL_BLE_L2CAP_JOIN_RX_FRAGS
#define MYNEWT_VAL_BLE_L2CAP_JOIN_RX_FRAGS (1)
#endif

#ifndef MYNEWT_VAL_BLE_L2CAP_MAX_CHANS
#define MYNEWT_VAL_BLE_L2CAP_MAX_CHANS (3*CONFIG_BT_NIMBLE_MAX_CONNECTIONS)
#endif

#ifndef MYNEWT_VAL_BLE_L2CAP_RX_FRAG_TIMEOUT
#define MYNEWT_VAL_BLE_L2CAP_RX_FRAG_TIMEOUT (30000)
#endif

#if CONFIG_BT_NIMBLE_EATT_CHAN_NUM > CONFIG_BT_NIMBLE_L2CAP_COC_MAX_NUM
#define MYNEWT_VAL_BLE_L2CAP_SIG_MAX_PROCS (CONFIG_BT_NIMBLE_EATT_CHAN_NUM)
#elif CONFIG_BT_NIMBLE_L2CAP_COC_MAX_NUM
#define MYNEWT_VAL_BLE_L2CAP_SIG_MAX_PROCS (CONFIG_BT_NIMBLE_L2CAP_COC_MAX_NUM)
#else
#define MYNEWT_VAL_BLE_L2CAP_SIG_MAX_PROCS (1)
#endif

#ifndef MYNEWT_VAL_BLE_MESH
#ifdef CONFIG_BT_NIMBLE_MESH
#define MYNEWT_VAL_BLE_MESH (1)
#else
#define MYNEWT_VAL_BLE_MESH (0)
#endif
#endif

#ifndef MYNEWT_VAL_BLE_MONITOR_CONSOLE_BUFFER_SIZE
#define MYNEWT_VAL_BLE_MONITOR_CONSOLE_BUFFER_SIZE (128)
#endif

#ifndef MYNEWT_VAL_BLE_MONITOR_RTT
#define MYNEWT_VAL_BLE_MONITOR_RTT (0)
#endif

#ifndef MYNEWT_VAL_BLE_MONITOR_RTT_BUFFERED
#define MYNEWT_VAL_BLE_MONITOR_RTT_BUFFERED (1)
#endif

#ifndef MYNEWT_VAL_BLE_MONITOR_RTT_BUFFER_NAME
#define MYNEWT_VAL_BLE_MONITOR_RTT_BUFFER_NAME ("monitor")
#endif

#ifndef MYNEWT_VAL_BLE_MONITOR_RTT_BUFFER_SIZE
#define MYNEWT_VAL_BLE_MONITOR_RTT_BUFFER_SIZE (256)
#endif

#ifndef MYNEWT_VAL_BLE_MONITOR_UART
#define MYNEWT_VAL_BLE_MONITOR_UART (0)
#endif

#ifndef MYNEWT_VAL_BLE_MONITOR_UART_BAUDRATE
#define MYNEWT_VAL_BLE_MONITOR_UART_BAUDRATE (1000000)
#endif

#ifndef MYNEWT_VAL_BLE_MONITOR_UART_BUFFER_SIZE
#define MYNEWT_VAL_BLE_MONITOR_UART_BUFFER_SIZE (64)
#endif

#ifndef MYNEWT_VAL_BLE_MONITOR_UART_DEV
#define MYNEWT_VAL_BLE_MONITOR_UART_DEV ("uart0")
#endif

#if CONFIG_IDF_TARGET_ESP32
#define MYNEWT_VAL_BLE_HOST_BASED_PRIVACY (1)
#else
#ifndef MYNEWT_VAL_BLE_HOST_BASED_PRIVACY
#define MYNEWT_VAL_BLE_HOST_BASED_PRIVACY (CONFIG_BT_NIMBLE_HOST_BASED_PRIVACY)
#endif
#endif

#ifndef MYNEWT_VAL_BLE_RPA_TIMEOUT
#define MYNEWT_VAL_BLE_RPA_TIMEOUT (CONFIG_BT_NIMBLE_RPA_TIMEOUT)
#endif

#ifndef MYNEWT_VAL_BLE_SM_BONDING
#define MYNEWT_VAL_BLE_SM_BONDING (1)
#endif

#ifndef MYNEWT_VAL_BLE_SM_IO_CAP
#define MYNEWT_VAL_BLE_SM_IO_CAP (BLE_HS_IO_NO_INPUT_OUTPUT)
#endif

#ifndef MYNEWT_VAL_BLE_SM_KEYPRESS
#define MYNEWT_VAL_BLE_SM_KEYPRESS (0)
#endif

#ifndef MYNEWT_VAL_BLE_SM_LEGACY
#ifdef CONFIG_BT_NIMBLE_SM_LEGACY
#define MYNEWT_VAL_BLE_SM_LEGACY (1)
#else
#define MYNEWT_VAL_BLE_SM_LEGACY (0)
#endif
#endif

#ifndef MYNEWT_VAL_BLE_SM_MAX_PROCS
#define MYNEWT_VAL_BLE_SM_MAX_PROCS (1)
#endif

#ifndef MYNEWT_VAL_BLE_SM_MITM
#define MYNEWT_VAL_BLE_SM_MITM (0)
#endif

#ifndef MYNEWT_VAL_BLE_SM_OOB_DATA_FLAG
#define MYNEWT_VAL_BLE_SM_OOB_DATA_FLAG (0)
#endif

#ifndef MYNEWT_VAL_BLE_SM_OUR_KEY_DIST
#define MYNEWT_VAL_BLE_SM_OUR_KEY_DIST (0)
#endif

#ifndef MYNEWT_VAL_BLE_SM_SC
#ifdef CONFIG_BT_NIMBLE_SM_SC
#define MYNEWT_VAL_BLE_SM_SC (1)
#else
#define MYNEWT_VAL_BLE_SM_SC (0)
#endif
#endif


#ifndef MYNEWT_VAL_BLE_SM_LVL
#ifdef CONFIG_BT_NIMBLE_SM_LVL
#define MYNEWT_VAL_BLE_SM_LVL CONFIG_BT_NIMBLE_SM_LVL
#else
#define MYNEWT_VAL_BLE_SM_LVL (0)
#endif
#endif

#ifndef MYNEWT_VAL_BLE_SM_SC_ONLY
#ifdef CONFIG_BT_NIMBLE_SM_SC_ONLY
#define MYNEWT_VAL_BLE_SM_SC_ONLY (CONFIG_BT_NIMBLE_SM_SC_ONLY)
#else
#define MYNEWT_VAL_BLE_SM_SC_ONLY (0)
#endif
#endif

#ifndef MYNEWT_VAL_BLE_SM_THEIR_KEY_DIST
#define MYNEWT_VAL_BLE_SM_THEIR_KEY_DIST (0)
#endif

#ifndef MYNEWT_VAL_BLE_SMP_ID_RESET
#ifdef CONFIG_BT_NIMBLE_SMP_ID_RESET
#define MYNEWT_VAL_BLE_SMP_ID_RESET CONFIG_BT_NIMBLE_SMP_ID_RESET
#else
#define MYNEWT_VAL_BLE_SMP_ID_RESET (0)
#endif
#endif

#ifndef MYNEWT_VAL_BLE_CRYPTO_STACK_MBEDTLS
#define MYNEWT_VAL_BLE_CRYPTO_STACK_MBEDTLS (CONFIG_BT_NIMBLE_CRYPTO_STACK_MBEDTLS)
#endif

#ifndef MYNEWT_VAL_BLE_STORE_MAX_BONDS
#define MYNEWT_VAL_BLE_STORE_MAX_BONDS CONFIG_BT_NIMBLE_MAX_BONDS
#endif

#ifndef MYNEWT_VAL_BLE_STORE_MAX_CCCDS
#define MYNEWT_VAL_BLE_STORE_MAX_CCCDS CONFIG_BT_NIMBLE_MAX_CCCDS
#endif

#ifndef MYNEWT_VAL_BLE_STORE_MAX_CSFCS
#define MYNEWT_VAL_BLE_STORE_MAX_CSFCS CONFIG_BT_NIMBLE_MAX_BONDS
#endif

#ifdef CONFIG_BT_NIMBLE_MAX_EADS
#define MYNEWT_VAL_BLE_STORE_MAX_EADS CONFIG_BT_NIMBLE_MAX_EADS
#endif

#ifndef MYNEWT_VAL_BLE_STORE_CONFIG_PERSIST
#ifdef CONFIG_BT_NIMBLE_NVS_PERSIST
#define MYNEWT_VAL_BLE_STORE_CONFIG_PERSIST (1)
#else
#define MYNEWT_VAL_BLE_STORE_CONFIG_PERSIST (0)
#endif
#endif


/* Value copied from BLE_TRANSPORT_ACL_COUNT */
#ifndef MYNEWT_VAL_BLE_TRANSPORT_ACL_FROM_LL_COUNT
#define MYNEWT_VAL_BLE_TRANSPORT_ACL_FROM_LL_COUNT CONFIG_BT_NIMBLE_TRANSPORT_ACL_FROM_LL_COUNT
#endif

#ifndef MYNEWT_VAL_BLE_TRANSPORT_ACL_SIZE
#define MYNEWT_VAL_BLE_TRANSPORT_ACL_SIZE CONFIG_BT_NIMBLE_TRANSPORT_ACL_SIZE
#endif

#ifndef MYNEWT_VAL_BLE_TRANSPORT_EVT_COUNT
#define MYNEWT_VAL_BLE_TRANSPORT_EVT_COUNT CONFIG_BT_NIMBLE_TRANSPORT_EVT_COUNT
#endif


#ifndef MYNEWT_VAL_BLE_TRANSPORT_EVT_DISCARDABLE_COUNT
#define MYNEWT_VAL_BLE_TRANSPORT_EVT_DISCARDABLE_COUNT CONFIG_BT_NIMBLE_TRANSPORT_EVT_DISCARD_COUNT
#endif


/*** nimble/host/services/ans */
#ifndef MYNEWT_VAL_BLE_SVC_ANS_NEW_ALERT_CAT
#define MYNEWT_VAL_BLE_SVC_ANS_NEW_ALERT_CAT (0)
#endif


#ifndef MYNEWT_VAL_BLE_SVC_ANS_UNR_ALERT_CAT
#define MYNEWT_VAL_BLE_SVC_ANS_UNR_ALERT_CAT (0)
#endif

/*** nimble/host/services/bas */
#ifndef MYNEWT_VAL_BLE_SVC_BAS_BATTERY_LEVEL_NOTIFY_ENABLE
#define MYNEWT_VAL_BLE_SVC_BAS_BATTERY_LEVEL_NOTIFY_ENABLE (CONFIG_BT_NIMBLE_SVC_BAS_BATTERY_LEVEL_NOTIFY)
#endif

#ifndef MYNEWT_VAL_BLE_SVC_BAS_BATTERY_LEVEL_READ_PERM
#define MYNEWT_VAL_BLE_SVC_BAS_BATTERY_LEVEL_READ_PERM (0)
#endif

/*** nimble/host/services/hid */
#ifndef MYNEWT_VAL_BLE_SVC_HID_SERVICE
#define MYNEWT_VAL_BLE_SVC_HID_SERVICE CONFIG_BT_NIMBLE_HID_SERVICE
#endif

#ifndef MYNEWT_VAL_BLE_SVC_HID_MAX_RPTS
#define MYNEWT_VAL_BLE_SVC_HID_MAX_RPTS CONFIG_BT_NIMBLE_SVC_HID_MAX_RPTS
#endif

#ifndef MYNEWT_VAL_BLE_SVC_HID_MAX_SVC_INSTANCES
#define MYNEWT_VAL_BLE_SVC_HID_MAX_SVC_INSTANCES CONFIG_BT_NIMBLE_SVC_HID_MAX_INSTANCES
#endif

#ifndef MYNEWT_VAL_BLE_MESH_ADV_TASK_PRIO
#define MYNEWT_VAL_BLE_MESH_ADV_TASK_PRIO (9)
#endif

#ifndef MYNEWT_VAL_BLE_MESH_APP_KEY_COUNT
#define MYNEWT_VAL_BLE_MESH_APP_KEY_COUNT (4)
#endif

/*** @apache-mynewt-nimble/nimble/host/mesh */
/* Overridden by apps/blemesh (defined by @apache-mynewt-nimble/nimble/host/mesh) */
#ifndef MYNEWT_VAL_BLE_MESH_ADV_BUF_COUNT
#define MYNEWT_VAL_BLE_MESH_ADV_BUF_COUNT (20)
#endif

#ifndef MYNEWT_VAL_BLE_MESH_ADV_EXT
#define MYNEWT_VAL_BLE_MESH_ADV_EXT (0)
#endif

#ifndef MYNEWT_VAL_BLE_MESH_ADV_LEGACY
#define MYNEWT_VAL_BLE_MESH_ADV_LEGACY (1)
#endif

#ifndef MYNEWT_VAL_BLE_MESH_ADV_BUF_COUNT
#define MYNEWT_VAL_BLE_MESH_ADV_BUF_COUNT (20)
#endif

#ifndef MYNEWT_VAL_BLE_MESH_APP_KEY_COUNT
#define MYNEWT_VAL_BLE_MESH_APP_KEY_COUNT (1)
#endif

#ifndef MYNEWT_VAL_BLE_MESH_CFG_CLI
#define MYNEWT_VAL_BLE_MESH_CFG_CLI (0)
#endif
#ifndef MYNEWT_VAL_BLE_MESH_CRPL
#define MYNEWT_VAL_BLE_MESH_CRPL (10)
#endif

/* Overridden by apps/blemesh (defined by @apache-mynewt-nimble/nimble/host/mesh) */
#ifndef MYNEWT_VAL_BLE_MESH_DEBUG
#define MYNEWT_VAL_BLE_MESH_DEBUG (1)
#endif

/* Overridden by apps/blemesh (defined by @apache-mynewt-nimble/nimble/host/mesh) */
#ifndef MYNEWT_VAL_BLE_MESH_DEBUG_ACCESS
#define MYNEWT_VAL_BLE_MESH_DEBUG_ACCESS (1)
#endif

/* Overridden by apps/blemesh (defined by @apache-mynewt-nimble/nimble/host/mesh) */
#ifndef MYNEWT_VAL_BLE_MESH_DEBUG_ADV
#define MYNEWT_VAL_BLE_MESH_DEBUG_ADV (1)
#endif

/* Overridden by apps/blemesh (defined by @apache-mynewt-nimble/nimble/host/mesh) */
#ifndef MYNEWT_VAL_BLE_MESH_DEBUG
#define MYNEWT_VAL_BLE_MESH_DEBUG (1)
#endif

/* Overridden by apps/blemesh (defined by @apache-mynewt-nimble/nimble/host/mesh) */
#ifndef MYNEWT_VAL_BLE_MESH_DEBUG_ACCESS
#define MYNEWT_VAL_BLE_MESH_DEBUG_ACCESS (1)
#endif

/* Overridden by apps/blemesh (defined by @apache-mynewt-nimble/nimble/host/mesh) */
#ifndef MYNEWT_VAL_BLE_MESH_DEBUG_ADV
#define MYNEWT_VAL_BLE_MESH_DEBUG_ADV (1)
#endif

/* Overridden by apps/blemesh (defined by @apache-mynewt-nimble/nimble/host/mesh) */
#ifndef MYNEWT_VAL_BLE_MESH_DEBUG_BEACON
#define MYNEWT_VAL_BLE_MESH_DEBUG_BEACON (1)
#endif

/* Overridden by apps/blemesh (defined by @apache-mynewt-nimble/nimble/host/mesh) */
#ifndef MYNEWT_VAL_BLE_MESH_DEBUG_CRYPTO
#define MYNEWT_VAL_BLE_MESH_DEBUG_CRYPTO (1)
#endif

/* Overridden by apps/blemesh (defined by @apache-mynewt-nimble/nimble/host/mesh) */
#ifndef MYNEWT_VAL_BLE_MESH_DEBUG_FRIEND
#define MYNEWT_VAL_BLE_MESH_DEBUG_FRIEND (1)
#endif

/* Overridden by apps/blemesh (defined by @apache-mynewt-nimble/nimble/host/mesh) */
#ifndef MYNEWT_VAL_BLE_MESH_DEBUG_LOW_POWER
#define MYNEWT_VAL_BLE_MESH_DEBUG_LOW_POWER (1)
#endif

/* Overridden by apps/blemesh (defined by @apache-mynewt-nimble/nimble/host/mesh) */
#ifndef MYNEWT_VAL_BLE_MESH_DEBUG_MODEL
#define MYNEWT_VAL_BLE_MESH_DEBUG_MODEL (1)
#endif

/* Overridden by apps/blemesh (defined by @apache-mynewt-nimble/nimble/host/mesh) */
#ifndef MYNEWT_VAL_BLE_MESH_DEBUG_NET
#define MYNEWT_VAL_BLE_MESH_DEBUG_NET (1)
#endif

/* Overridden by apps/blemesh (defined by @apache-mynewt-nimble/nimble/host/mesh) */
#ifndef MYNEWT_VAL_BLE_MESH_DEBUG_PROV
#define MYNEWT_VAL_BLE_MESH_DEBUG_PROV (1)
#endif

/* Overridden by apps/blemesh (defined by @apache-mynewt-nimble/nimble/host/mesh) */
#ifndef MYNEWT_VAL_BLE_MESH_DEBUG_PROXY
#define MYNEWT_VAL_BLE_MESH_DEBUG_PROXY (1)
#endif

#ifndef MYNEWT_VAL_BLE_MESH_DEBUG_SETTINGS
#define MYNEWT_VAL_BLE_MESH_DEBUG_SETTINGS (1)
#endif

/* Overridden by apps/blemesh (defined by @apache-mynewt-nimble/nimble/host/mesh) */
#ifndef MYNEWT_VAL_BLE_MESH_DEBUG_TRANS
#define MYNEWT_VAL_BLE_MESH_DEBUG_TRANS (1)
#endif

#ifndef MYNEWT_VAL_BLE_MESH_DEVICE_NAME
#define MYNEWT_VAL_BLE_MESH_DEVICE_NAME CONFIG_BT_NIMBLE_MESH_DEVICE_NAME
#endif

#ifndef MYNEWT_VAL_BLE_MESH_DEV_UUID
#define MYNEWT_VAL_BLE_MESH_DEV_UUID (((uint8_t[16]){0x11, 0x22, 0}))
#endif

#ifndef MYNEWT_VAL_BLE_MESH_FRIEND
#ifdef CONFIG_BT_NIMBLE_MESH_FRIEND
#define MYNEWT_VAL_BLE_MESH_FRIEND (1)
#else
#define MYNEWT_VAL_BLE_MESH_FRIEND (0)
#endif
#endif

#ifndef MYNEWT_VAL_BLE_MESH_FRIEND_LOG_LVL
#define MYNEWT_VAL_BLE_MESH_FRIEND_LOG_LVL (1)
#endif

#ifndef MYNEWT_VAL_BLE_MESH_FRIEND_LOG_MOD
#define MYNEWT_VAL_BLE_MESH_FRIEND_LOG_MOD (14)
#endif

#ifndef MYNEWT_VAL_BLE_MESH_FRIEND_LPN_COUNT
#define MYNEWT_VAL_BLE_MESH_FRIEND_LPN_COUNT (2)
#endif

#ifndef MYNEWT_VAL_BLE_MESH_FRIEND_QUEUE_SIZE
#define MYNEWT_VAL_BLE_MESH_FRIEND_QUEUE_SIZE (16)
#endif

#ifndef MYNEWT_VAL_BLE_MESH_FRIEND_RECV_WIN
#define MYNEWT_VAL_BLE_MESH_FRIEND_RECV_WIN (255)
#endif

#ifndef MYNEWT_VAL_BLE_MESH_FRIEND_SEG_RX
#define MYNEWT_VAL_BLE_MESH_FRIEND_SEG_RX (1)
#endif

#ifndef MYNEWT_VAL_BLE_MESH_FRIEND_SUB_LIST_SIZE
#define MYNEWT_VAL_BLE_MESH_FRIEND_SUB_LIST_SIZE (3)
#endif

#ifndef MYNEWT_VAL_BLE_MESH_GATT_PROXY
#ifdef CONFIG_BT_NIMBLE_MESH_GATT_PROXY
#define MYNEWT_VAL_BLE_MESH_GATT_PROXY (1)
#else
#define MYNEWT_VAL_BLE_MESH_GATT_PROXY (0)
#endif
#endif

#ifndef MYNEWT_VAL_BLE_MESH_HEALTH_CLI
#define MYNEWT_VAL_BLE_MESH_HEALTH_CLI (0)
#endif

#ifndef MYNEWT_VAL_BLE_MESH_IVU_DIVIDER
#define MYNEWT_VAL_BLE_MESH_IVU_DIVIDER (4)
#endif

#ifndef MYNEWT_VAL_BLE_MESH_IV_UPDATE_SEQ_LIMIT
#define MYNEWT_VAL_BLE_MESH_IV_UPDATE_SEQ_LIMIT (0x800000)
#endif

#ifndef MYNEWT_VAL_BLE_MESH_IV_UPDATE_TEST
#define MYNEWT_VAL_BLE_MESH_IV_UPDATE_TEST (0)
#endif

#ifndef MYNEWT_VAL_BLE_MESH_LABEL_COUNT
#define MYNEWT_VAL_BLE_MESH_LABEL_COUNT (1)
#endif

#ifndef MYNEWT_VAL_BLE_MESH_LOG_LVL
#define MYNEWT_VAL_BLE_MESH_LOG_LVL (1)
#endif

#ifndef MYNEWT_VAL_BLE_MESH_LOG_MOD
#define MYNEWT_VAL_BLE_MESH_LOG_MOD (9)
#endif

#ifndef MYNEWT_VAL_BLE_MESH_PROXY_MSG_LEN
#define MYNEWT_VAL_BLE_MESH_PROXY_MSG_LEN (33)
#endif

#ifndef MYNEWT_VAL_BLE_MESH_LOW_POWER
#ifdef CONFIG_BT_NIMBLE_MESH_LOW_POWER
#define MYNEWT_VAL_BLE_MESH_LOW_POWER (1)
#else
#define MYNEWT_VAL_BLE_MESH_LOW_POWER (0)
#endif
#endif

#ifndef MYNEWT_VAL_BLE_MESH_LOW_POWER_LOG_LVL
#define MYNEWT_VAL_BLE_MESH_LOW_POWER_LOG_LVL (1)
#endif

#ifndef MYNEWT_VAL_BLE_MESH_LOW_POWER_LOG_MOD
#define MYNEWT_VAL_BLE_MESH_LOW_POWER_LOG_MOD (15)
#endif

#ifndef MYNEWT_VAL_BLE_MESH_LPN_AUTO
#define MYNEWT_VAL_BLE_MESH_LPN_AUTO (1)
#endif

#ifndef MYNEWT_VAL_BLE_MESH_LPN_AUTO_TIMEOUT
#define MYNEWT_VAL_BLE_MESH_LPN_AUTO_TIMEOUT (15)
#endif

#ifndef MYNEWT_VAL_BLE_MESH_LPN_ESTABLISHMENT
#define MYNEWT_VAL_BLE_MESH_LPN_ESTABLISHMENT (1)
#endif

#ifndef MYNEWT_VAL_BLE_MESH_LPN_GROUPS
#define MYNEWT_VAL_BLE_MESH_LPN_GROUPS (10)
#endif

#ifndef MYNEWT_VAL_BLE_MESH_LPN_SUB_ALL_NODES_ADDR
#define MYNEWT_VAL_BLE_MESH_LPN_SUB_ALL_NODES_ADDR (1)
#endif

#ifndef MYNEWT_VAL_BLE_MESH_LPN_INIT_POLL_TIMEOUT
#define MYNEWT_VAL_BLE_MESH_LPN_INIT_POLL_TIMEOUT (MYNEWT_VAL_BLE_MESH_LPN_POLL_TIMEOUT)
#endif

#ifndef MYNEWT_VAL_BLE_MESH_LPN_MIN_QUEUE_SIZE
#define MYNEWT_VAL_BLE_MESH_LPN_MIN_QUEUE_SIZE (1)
#endif

#ifndef MYNEWT_VAL_BLE_MESH_LPN_POLL_TIMEOUT
#define MYNEWT_VAL_BLE_MESH_LPN_POLL_TIMEOUT (300)
#endif

#ifndef MYNEWT_VAL_BLE_MESH_LPN_RECV_DELAY
#define MYNEWT_VAL_BLE_MESH_LPN_RECV_DELAY (100)
#endif

#ifndef MYNEWT_VAL_BLE_MESH_LPN_RECV_WIN_FACTOR
#define MYNEWT_VAL_BLE_MESH_LPN_RECV_WIN_FACTOR (0)
#endif

#ifndef MYNEWT_VAL_BLE_MESH_LPN_RETRY_TIMEOUT
#define MYNEWT_VAL_BLE_MESH_LPN_RETRY_TIMEOUT (8)
#endif

#ifndef MYNEWT_VAL_BLE_MESH_LPN_RSSI_FACTOR
#define MYNEWT_VAL_BLE_MESH_LPN_RSSI_FACTOR (0)
#endif

#ifndef MYNEWT_VAL_BLE_MESH_LPN_SCAN_LATENCY
#define MYNEWT_VAL_BLE_MESH_LPN_SCAN_LATENCY (10)
#endif

#ifndef MYNEWT_VAL_BLE_MESH_MODEL_EXTENSIONS
#define MYNEWT_VAL_BLE_MESH_MODEL_EXTENSIONS (0)
#endif

#ifndef MYNEWT_VAL_BLE_MESH_MODEL_GROUP_COUNT
#define MYNEWT_VAL_BLE_MESH_MODEL_GROUP_COUNT (1)
#endif

#ifndef MYNEWT_VAL_BLE_MESH_MODEL_KEY_COUNT
#define MYNEWT_VAL_BLE_MESH_MODEL_KEY_COUNT (1)
#endif

#ifndef MYNEWT_VAL_BLE_MESH_MODEL_LOG_LVL
#define MYNEWT_VAL_BLE_MESH_MODEL_LOG_LVL (1)
#endif

#ifndef MYNEWT_VAL_BLE_MESH_MODEL_LOG_MOD
#define MYNEWT_VAL_BLE_MESH_MODEL_LOG_MOD (16)
#endif

#ifndef MYNEWT_VAL_BLE_MESH_MSG_CACHE_SIZE
#define MYNEWT_VAL_BLE_MESH_MSG_CACHE_SIZE (10)
#endif

#ifndef MYNEWT_VAL_BLE_MESH_NET_LOG_LVL
#define MYNEWT_VAL_BLE_MESH_NET_LOG_LVL (1)
#endif

#ifndef MYNEWT_VAL_BLE_MESH_NET_LOG_MOD
#define MYNEWT_VAL_BLE_MESH_NET_LOG_MOD (17)
#endif

#ifndef MYNEWT_VAL_BLE_MESH_NODE_COUNT
#define MYNEWT_VAL_BLE_MESH_NODE_COUNT CONFIG_BT_NIMBLE_MESH_NODE_COUNT
#endif

#ifndef MYNEWT_VAL_BLE_MESH_NODE_ID_TIMEOUT
#define MYNEWT_VAL_BLE_MESH_NODE_ID_TIMEOUT (60)
#endif

#ifndef MYNEWT_VAL_BLE_MESH_OOB_INPUT_ACTIONS
#define MYNEWT_VAL_BLE_MESH_OOB_INPUT_ACTIONS (((BT_MESH_NO_INPUT)))
#endif

#ifndef MYNEWT_VAL_BLE_MESH_OOB_INPUT_SIZE
#define MYNEWT_VAL_BLE_MESH_OOB_INPUT_SIZE (4)
#endif

#ifndef MYNEWT_VAL_BLE_MESH_OOB_OUTPUT_ACTIONS
#define MYNEWT_VAL_BLE_MESH_OOB_OUTPUT_ACTIONS (((BT_MESH_DISPLAY_NUMBER)))
#endif

#ifndef MYNEWT_VAL_BLE_MESH_OOB_OUTPUT_SIZE
#define MYNEWT_VAL_BLE_MESH_OOB_OUTPUT_SIZE (4)
#endif

#ifndef MYNEWT_VAL_BLE_MESH_PB_ADV
#ifdef CONFIG_BT_NIMBLE_MESH_PB_ADV
#define MYNEWT_VAL_BLE_MESH_PB_ADV (1)
#else
#define MYNEWT_VAL_BLE_MESH_PB_ADV (0)
#endif
#endif

#ifndef MYNEWT_VAL_BLE_MESH_PB_GATT
#ifdef CONFIG_BT_NIMBLE_MESH_PB_GATT
#define MYNEWT_VAL_BLE_MESH_PB_GATT (1)
#else
#define MYNEWT_VAL_BLE_MESH_PB_GATT (0)
#endif
#endif

/* Overridden by @apache-mynewt-nimble/nimble/host/mesh (defined by @apache-mynewt-nimble/nimble/host/mesh) */
#ifndef MYNEWT_VAL_BLE_MESH_PROV
#ifdef CONFIG_BT_NIMBLE_MESH_PROV
#define MYNEWT_VAL_BLE_MESH_PROV (1)
#else
#define MYNEWT_VAL_BLE_MESH_PROV (0)
#endif
#endif

#ifndef MYNEWT_VAL_BLE_MESH_PROVISIONER
#ifdef CONFIG_BT_NIMBLE_MESH_PROVISIONER
#define MYNEWT_VAL_BLE_MESH_PROVISIONER (1)
#else
#define MYNEWT_VAL_BLE_MESH_PROVISIONER (0)
#endif
#endif

#ifndef MYNEWT_VAL_BLE_MESH_CDB
#define MYNEWT_VAL_BLE_MESH_CDB (0)
#endif

#ifndef MYNEWT_VAL_BLE_MESH_PROV_LOG_LVL
#define MYNEWT_VAL_BLE_MESH_PROV_LOG_LVL (1)
#endif

#ifndef MYNEWT_VAL_BLE_MESH_PROV_LOG_MOD
#define MYNEWT_VAL_BLE_MESH_PROV_LOG_MOD (18)
#endif

/* Overridden by @apache-mynewt-nimble/nimble/host/mesh (defined by @apache-mynewt-nimble/nimble/host/mesh) */
#ifndef MYNEWT_VAL_BLE_MESH_PROXY
#ifdef CONFIG_BT_NIMBLE_MESH_PROXY
#define MYNEWT_VAL_BLE_MESH_PROXY (1)
#else
#define MYNEWT_VAL_BLE_MESH_PROXY (0)
#endif
#endif

#ifndef MYNEWT_VAL_BLE_MESH_PROXY_FILTER_SIZE
#define MYNEWT_VAL_BLE_MESH_PROXY_FILTER_SIZE (1)
#endif

#ifndef MYNEWT_VAL_BLE_MESH_PROXY_LOG_LVL
#define MYNEWT_VAL_BLE_MESH_PROXY_LOG_LVL (1)
#endif

#ifndef MYNEWT_VAL_BLE_MESH_PROXY_LOG_MOD
#define MYNEWT_VAL_BLE_MESH_PROXY_LOG_MOD (19)
#endif

#ifndef MYNEWT_VAL_BLE_MESH_RELAY
#ifdef CONFIG_BT_NIMBLE_MESH_RELAY
#define MYNEWT_VAL_BLE_MESH_RELAY (1)
#else
#define MYNEWT_VAL_BLE_MESH_RELAY (0)
#endif
#endif

#ifndef MYNEWT_VAL_BLE_MESH_RPL_STORE_TIMEOUT
#define MYNEWT_VAL_BLE_MESH_RPL_STORE_TIMEOUT (5)
#endif

#ifndef MYNEWT_VAL_BLE_MESH_RX_SDU_MAX
#define MYNEWT_VAL_BLE_MESH_RX_SDU_MAX (72)
#endif

#ifndef MYNEWT_VAL_BLE_MESH_SEG_BUFS
#define MYNEWT_VAL_BLE_MESH_SEG_BUFS (72)
#endif

#ifndef MYNEWT_VAL_BLE_MESH_RX_SEG_MAX
#define MYNEWT_VAL_BLE_MESH_RX_SEG_MAX (3)
#endif


#ifndef MYNEWT_VAL_BLE_MESH_RX_SEG_MSG_COUNT
#define MYNEWT_VAL_BLE_MESH_RX_SEG_MSG_COUNT (2)
#endif

#ifndef MYNEWT_VAL_BLE_MESH_SEG_RETRANSMIT_ATTEMPTS
#define MYNEWT_VAL_BLE_MESH_SEG_RETRANSMIT_ATTEMPTS (4)
#endif

#ifndef MYNEWT_VAL_BLE_MESH_SEQ_STORE_RATE
#define MYNEWT_VAL_BLE_MESH_SEQ_STORE_RATE (128)
#endif

#ifndef MYNEWT_VAL_BLE_MESH_TX_SEG_RETRANS_COUNT
#define MYNEWT_VAL_BLE_MESH_TX_SEG_RETRANS_COUNT (4)
#endif

#ifndef MYNEWT_VAL_BLE_MESH_TX_SEG_RETRANS_TIMEOUT_UNICAST
#define MYNEWT_VAL_BLE_MESH_TX_SEG_RETRANS_TIMEOUT_UNICAST (400)
#endif

#ifndef MYNEWT_VAL_BLE_MESH_TX_SEG_RETRANS_TIMEOUT_GROUP
#define MYNEWT_VAL_BLE_MESH_TX_SEG_RETRANS_TIMEOUT_GROUP (50)
#endif

#ifndef MYNEWT_VAL_BLE_MESH_LOOPBACK_BUFS
#define MYNEWT_VAL_BLE_MESH_LOOPBACK_BUFS (3)
#endif

#ifndef MYNEWT_VAL_BLE_MESH_DEFAULT_TTL
#define MYNEWT_VAL_BLE_MESH_DEFAULT_TTL (7)
#endif

#ifndef MYNEWT_VAL_BLE_MESH_NETWORK_TRANSMIT_COUNT
#define MYNEWT_VAL_BLE_MESH_NETWORK_TRANSMIT_COUNT (2)
#endif

#ifndef MYNEWT_VAL_BLE_MESH_NETWORK_TRANSMIT_INTERVAL
#define MYNEWT_VAL_BLE_MESH_NETWORK_TRANSMIT_INTERVAL (20)
#endif

#ifndef MYNEWT_VAL_BLE_MESH_NET_BUF_USER_DATA_SIZE
#define MYNEWT_VAL_BLE_MESH_NET_BUF_USER_DATA_SIZE (4)
#endif

#ifndef MYNEWT_VAL_BLE_MESH_MODEL_VND_MSG_CID_FORCE
#define MYNEWT_VAL_BLE_MESH_MODEL_VND_MSG_CID_FORCE (1)
#endif

#ifndef MYNEWT_VAL_BLE_MESH_RELAY_RETRANSMIT_COUNT
#define MYNEWT_VAL_BLE_MESH_RELAY_RETRANSMIT_COUNT (2)
#endif

#ifndef MYNEWT_VAL_BLE_MESH_PB_ADV_RETRANS_TIMEOUT
#define MYNEWT_VAL_BLE_MESH_PB_ADV_RETRANS_TIMEOUT (500)
#endif

#ifndef MYNEWT_VAL_BLE_MESH_RELAY_ENABLED
#define MYNEWT_VAL_BLE_MESH_RELAY_ENABLED (1)
#endif

#ifndef MYNEWT_VAL_BLE_MESH_GATT_PROXY_ENABLED
#define MYNEWT_VAL_BLE_MESH_GATT_PROXY_ENABLED (1)
#endif

#ifndef MYNEWT_VAL_BLE_MESH_GATT_SERVER
#define MYNEWT_VAL_BLE_MESH_GATT_SERVER (1)
#endif

#ifndef MYNEWT_VAL_BLE_MESH_FRIEND_ENABLED
#define MYNEWT_VAL_BLE_MESH_FRIEND_ENABLED (1)
#endif

#ifndef MYNEWT_VAL_BLE_MESH_BEACON_ENABLED
#define MYNEWT_VAL_BLE_MESH_BEACON_ENABLED (1)
#endif

#ifndef MYNEWT_VAL_BLE_MESH_RELAY_RETRANSMIT_INTERVAL
#define MYNEWT_VAL_BLE_MESH_RELAY_RETRANSMIT_INTERVAL (20)
#endif

#ifndef MYNEWT_VAL_BLE_MESH_PROV_OOB_PUBLIC_KEY
#define MYNEWT_VAL_BLE_MESH_PROV_OOB_PUBLIC_KEY (0)
#endif

/* Overridden by apps/blemesh (defined by @apache-mynewt-nimble/nimble/host/mesh) */
#ifndef MYNEWT_VAL_BLE_MESH_SETTINGS
#define MYNEWT_VAL_BLE_MESH_SETTINGS (0)
#endif

#ifndef MYNEWT_VAL_BLE_MESH_ACCESS_LAYER_MSG
#define MYNEWT_VAL_BLE_MESH_ACCESS_LAYER_MSG (1)
#endif

#ifndef MYNEWT_VAL_BLE_MESH_SETTINGS_LOG_LVL
#define MYNEWT_VAL_BLE_MESH_SETTINGS_LOG_LVL (1)
#endif

#ifndef MYNEWT_VAL_BLE_MESH_SETTINGS_LOG_MOD
#define MYNEWT_VAL_BLE_MESH_SETTINGS_LOG_MOD (20)
#endif

#ifndef MYNEWT_VAL_BLE_MESH_SHELL
#define MYNEWT_VAL_BLE_MESH_SHELL (0)
#endif

#ifndef MYNEWT_VAL_BLE_MESH_SHELL_MODELS
#define MYNEWT_VAL_BLE_MESH_SHELL_MODELS (0)
#endif

#ifndef MYNEWT_VAL_BLE_MESH_STORE_TIMEOUT
#define MYNEWT_VAL_BLE_MESH_STORE_TIMEOUT (2)
#endif

#ifndef MYNEWT_VAL_BLE_MESH_SUBNET_COUNT
#define MYNEWT_VAL_BLE_MESH_SUBNET_COUNT (1)
#endif

#ifndef MYNEWT_VAL_BLE_MESH_SYSINIT_STAGE
#define MYNEWT_VAL_BLE_MESH_SYSINIT_STAGE (500)
#endif

#ifndef MYNEWT_VAL_BLE_MESH_SYSINIT_STAGE_SHELL
#define MYNEWT_VAL_BLE_MESH_SYSINIT_STAGE_SHELL (1000)
#endif

#ifndef MYNEWT_VAL_BLE_MESH_TESTING
#define MYNEWT_VAL_BLE_MESH_TESTING (0)
#endif

#ifndef MYNEWT_VAL_BLE_MESH_TRANS_LOG_LVL
#define MYNEWT_VAL_BLE_MESH_TRANS_LOG_LVL (1)
#endif

#ifndef MYNEWT_VAL_BLE_MESH_TRANS_LOG_MOD
#define MYNEWT_VAL_BLE_MESH_TRANS_LOG_MOD (21)
#endif

/* Overridden by apps/blemesh (defined by @apache-mynewt-nimble/nimble/host/mesh) */
#ifndef MYNEWT_VAL_BLE_MESH_TX_SEG_MAX
#define MYNEWT_VAL_BLE_MESH_TX_SEG_MAX (6)
#endif

#ifndef MYNEWT_VAL_BLE_MESH_TX_SEG_MSG_COUNT
#define MYNEWT_VAL_BLE_MESH_TX_SEG_MSG_COUNT (4)
#endif

#ifndef MYNEWT_VAL_BLE_MESH_UNPROV_BEACON_INT
#define MYNEWT_VAL_BLE_MESH_UNPROV_BEACON_INT (5)
#endif

/*** @apache-mynewt-nimble/nimble/host/services/ans */
#ifndef MYNEWT_VAL_BLE_SVC_ANS_NEW_ALERT_CAT
#define MYNEWT_VAL_BLE_SVC_ANS_NEW_ALERT_CAT (0)
#endif

#ifndef MYNEWT_VAL_BLE_SVC_ANS_SYSINIT_STAGE
#define MYNEWT_VAL_BLE_SVC_ANS_SYSINIT_STAGE (303)
#endif

#ifndef MYNEWT_VAL_BLE_SVC_ANS_UNR_ALERT_CAT
#define MYNEWT_VAL_BLE_SVC_ANS_UNR_ALERT_CAT (0)
#endif

/*** @apache-mynewt-nimble/nimble/host/services/bas */
#ifndef MYNEWT_VAL_BLE_SVC_BAS_BATTERY_LEVEL_NOTIFY_ENABLE
#define MYNEWT_VAL_BLE_SVC_BAS_BATTERY_LEVEL_NOTIFY_ENABLE (CONFIG_BT_NIMBLE_SVC_BAS_BATTERY_LEVEL_NOTIFY)
#endif

#ifndef MYNEWT_VAL_BLE_SVC_BAS_BATTERY_LEVEL_READ_PERM
#define MYNEWT_VAL_BLE_SVC_BAS_BATTERY_LEVEL_READ_PERM (0)
#endif

#ifndef MYNEWT_VAL_BLE_SVC_BAS_SYSINIT_STAGE
#define MYNEWT_VAL_BLE_SVC_BAS_SYSINIT_STAGE (303)
#endif

/*** @apache-mynewt-nimble/nimble/host/services/dis */
#ifndef MYNEWT_VAL_BLE_SVC_DIS_DEFAULT_READ_PERM
#define MYNEWT_VAL_BLE_SVC_DIS_DEFAULT_READ_PERM (-1)
#endif

#ifndef MYNEWT_VAL_BLE_SVC_DIS_FIRMWARE_REVISION_DEFAULT
#define MYNEWT_VAL_BLE_SVC_DIS_FIRMWARE_REVISION_DEFAULT ("0000")
#endif

/* Value copied from BLE_SVC_DIS_DEFAULT_READ_PERM */
#if CONFIG_BT_NIMBLE_SVC_DIS_FIRMWARE_REVISION
#define MYNEWT_VAL_BLE_SVC_DIS_FIRMWARE_REVISION_READ_PERM (0)
#else
#define MYNEWT_VAL_BLE_SVC_DIS_FIRMWARE_REVISION_READ_PERM (-1)
#endif

#ifndef MYNEWT_VAL_BLE_SVC_DIS_HARDWARE_REVISION_DEFAULT
#define MYNEWT_VAL_BLE_SVC_DIS_HARDWARE_REVISION_DEFAULT ("0000")
#endif

/* Value copied from BLE_SVC_DIS_DEFAULT_READ_PERM */
#if CONFIG_BT_NIMBLE_SVC_DIS_HARDWARE_REVISION
#define MYNEWT_VAL_BLE_SVC_DIS_HARDWARE_REVISION_READ_PERM (0)
#else
#define MYNEWT_VAL_BLE_SVC_DIS_HARDWARE_REVISION_READ_PERM (-1)
#endif

#ifndef MYNEWT_VAL_BLE_SVC_DIS_MANUFACTURER_NAME_DEFAULT
#define MYNEWT_VAL_BLE_SVC_DIS_MANUFACTURER_NAME_DEFAULT ("espressif")
#endif

#if CONFIG_BT_NIMBLE_SVC_DIS_MANUFACTURER_NAME
#define MYNEWT_VAL_BLE_SVC_DIS_MANUFACTURER_NAME_READ_PERM (0)
#else
#define MYNEWT_VAL_BLE_SVC_DIS_MANUFACTURER_NAME_READ_PERM (-1)
#endif

#ifndef MYNEWT_VAL_BLE_SVC_DIS_MODEL_NUMBER_DEFAULT
#define MYNEWT_VAL_BLE_SVC_DIS_MODEL_NUMBER_DEFAULT ("NimBLE")
#endif

#ifndef MYNEWT_VAL_BLE_SVC_DIS_MODEL_NUMBER_READ_PERM
#define MYNEWT_VAL_BLE_SVC_DIS_MODEL_NUMBER_READ_PERM (0)
#endif

#ifndef MYNEWT_VAL_BLE_SVC_DIS_SERIAL_NUMBER_DEFAULT
#define MYNEWT_VAL_BLE_SVC_DIS_SERIAL_NUMBER_DEFAULT ("0000")
#endif

/* Value copied from BLE_SVC_DIS_DEFAULT_READ_PERM */
#if CONFIG_BT_NIMBLE_SVC_DIS_SERIAL_NUMBER
#define MYNEWT_VAL_BLE_SVC_DIS_SERIAL_NUMBER_READ_PERM (0)
#else
#define MYNEWT_VAL_BLE_SVC_DIS_SERIAL_NUMBER_READ_PERM (-1)
#endif

#ifndef MYNEWT_VAL_BLE_SVC_DIS_SOFTWARE_REVISION_DEFAULT
#define MYNEWT_VAL_BLE_SVC_DIS_SOFTWARE_REVISION_DEFAULT ("0000")
#endif

/* Value copied from BLE_SVC_DIS_DEFAULT_READ_PERM */
#if CONFIG_BT_NIMBLE_SVC_DIS_SOFTWARE_REVISION
#define MYNEWT_VAL_BLE_SVC_DIS_SOFTWARE_REVISION_READ_PERM (0)
#else
#define MYNEWT_VAL_BLE_SVC_DIS_SOFTWARE_REVISION_READ_PERM (-1)
#endif

#ifndef MYNEWT_VAL_BLE_SVC_DIS_SYSINIT_STAGE
#define MYNEWT_VAL_BLE_SVC_DIS_SYSINIT_STAGE (303)
#endif

#ifndef MYNEWT_VAL_BLE_SVC_DIS_SYSTEM_ID_DEFAULT
#define MYNEWT_VAL_BLE_SVC_DIS_SYSTEM_ID_DEFAULT ("00000000")
#endif

/* Value copied from BLE_SVC_DIS_DEFAULT_READ_PERM */
#if CONFIG_BT_NIMBLE_SVC_DIS_SYSTEM_ID
#define MYNEWT_VAL_BLE_SVC_DIS_SYSTEM_ID_READ_PERM (0)
#else
#define MYNEWT_VAL_BLE_SVC_DIS_SYSTEM_ID_READ_PERM (-1)
#endif

#ifndef MYNEWT_VAL_BLE_SVC_DIS_PNP_ID_DEFAULT
#define MYNEWT_VAL_BLE_SVC_DIS_PNP_ID_DEFAULT ("000000")
#endif

/* Value copied from BLE_SVC_DIS_DEFAULT_READ_PERM */
#if CONFIG_BT_NIMBLE_SVC_DIS_PNP_ID
#define MYNEWT_VAL_BLE_SVC_DIS_PNP_ID_READ_PERM (0)
#else
#define MYNEWT_VAL_BLE_SVC_DIS_PNP_ID_READ_PERM (-1)
#endif

#ifndef MYNEWT_VAL_BLE_SVC_DIS_INCLUDED
#define MYNEWT_VAL_BLE_SVC_DIS_INCLUDED (CONFIG_BT_NIMBLE_SVC_DIS_INCLUDED)
#endif

/*** @apache-mynewt-nimble/nimble/host/services/gap */
#ifndef MYNEWT_VAL_BLE_SVC_GAP_APPEARANCE
#define MYNEWT_VAL_BLE_SVC_GAP_APPEARANCE CONFIG_BT_NIMBLE_SVC_GAP_APPEARANCE
#endif

#ifndef MYNEWT_VAL_BLE_SVC_GAP_APPEARANCE_WRITE_PERM
#if CONFIG_BT_NIMBLE_SVC_GAP_APPEAR_WRITE_PERM
#define MYNEWT_VAL_BLE_SVC_GAP_APPEARANCE_WRITE_PERM ( \
        CONFIG_BT_NIMBLE_SVC_GAP_APPEAR_WRITE_PERM_ENC | \
        CONFIG_BT_NIMBLE_SVC_GAP_APPEAR_WRITE_PERM_ATHN | \
        CONFIG_BT_NIMBLE_SVC_GAP_APPEAR_WRITE_PERM_ATHR)
#else
#define MYNEWT_VAL_BLE_SVC_GAP_APPEARANCE_WRITE_PERM (-1)
#endif //CONFIG_BT_NIMBLE_SVC_GAP_APPEAR_WRITE_PERM
#endif //MYNEWT_VAL_BLE_SVC_GAP_APPEARANCE_WRITE_PERM

#ifndef MYNEWT_VAL_BLE_SVC_GAP_CENTRAL_ADDRESS_RESOLUTION
#define MYNEWT_VAL_BLE_SVC_GAP_CENTRAL_ADDRESS_RESOLUTION \
        CONFIG_BT_NIMBLE_SVC_GAP_CENT_ADDR_RESOLUTION
#endif

#ifndef CONFIG_BT_NIMBLE_SVC_GAP_DEVICE_NAME
#define MYNEWT_VAL_BLE_SVC_GAP_DEVICE_NAME "nimble"
#else
#define MYNEWT_VAL_BLE_SVC_GAP_DEVICE_NAME CONFIG_BT_NIMBLE_SVC_GAP_DEVICE_NAME
#endif

#ifndef MYNEWT_VAL_BLE_SVC_GAP_DEVICE_NAME_MAX_LENGTH
#define MYNEWT_VAL_BLE_SVC_GAP_DEVICE_NAME_MAX_LENGTH CONFIG_BT_NIMBLE_GAP_DEVICE_NAME_MAX_LEN // According to the specification, the maximum length should be 248
#endif

#ifndef MYNEWT_VAL_BLE_SVC_GAP_DEVICE_NAME_WRITE_PERM
#if CONFIG_BT_NIMBLE_SVC_GAP_NAME_WRITE_PERM
#define MYNEWT_VAL_BLE_SVC_GAP_DEVICE_NAME_WRITE_PERM ( \
        CONFIG_BT_NIMBLE_SVC_GAP_NAME_WRITE_PERM_ENC | \
        CONFIG_BT_NIMBLE_SVC_GAP_NAME_WRITE_PERM_AUTHEN | \
        CONFIG_BT_NIMBLE_SVC_GAP_NAME_WRITE_PERM_AUTHOR)
#else
#define MYNEWT_VAL_BLE_SVC_GAP_DEVICE_NAME_WRITE_PERM (-1)
#endif //CONFIG_BT_NIMBLE_SVC_GAP_NAME_WRITE_PERM
#endif //MYNEWT_VAL_BLE_SVC_GAP_DEVICE_NAME_WRITE_PERM

#ifndef MYNEWT_VAL_BLE_SVC_GAP_PPCP_MAX_CONN_INTERVAL
#define MYNEWT_VAL_BLE_SVC_GAP_PPCP_MAX_CONN_INTERVAL \
        CONFIG_BT_NIMBLE_SVC_GAP_PPCP_MAX_CONN_INTERVAL
#endif

#ifndef MYNEWT_VAL_BLE_SVC_GAP_PPCP_MIN_CONN_INTERVAL
#define MYNEWT_VAL_BLE_SVC_GAP_PPCP_MIN_CONN_INTERVAL \
        CONFIG_BT_NIMBLE_SVC_GAP_PPCP_MIN_CONN_INTERVAL
#endif

#ifndef MYNEWT_VAL_BLE_SVC_GAP_PPCP_SLAVE_LATENCY
#define MYNEWT_VAL_BLE_SVC_GAP_PPCP_SLAVE_LATENCY \
        CONFIG_BT_NIMBLE_SVC_GAP_PPCP_SLAVE_LATENCY
#endif

#ifndef MYNEWT_VAL_BLE_SVC_GAP_PPCP_SUPERVISION_TMO
#define MYNEWT_VAL_BLE_SVC_GAP_PPCP_SUPERVISION_TMO \
        CONFIG_BT_NIMBLE_SVC_GAP_PPCP_SUPERVISION_TMO
#endif

#ifndef MYNEWT_VAL_BLE_SVC_GAP_GATT_SECURITY_LEVEL
#define MYNEWT_VAL_BLE_SVC_GAP_GATT_SECURITY_LEVEL \
        CONFIG_BT_NIMBLE_SVC_GAP_GATT_SECURITY_LEVEL
#endif

/*** nimble/transport */
#ifndef MYNEWT_VAL_BLE_HCI_TRANSPORT_EMSPI
#define MYNEWT_VAL_BLE_HCI_TRANSPORT_EMSPI (0)
#endif

/* Overridden by targets/porting-nimble (defined by nimble/transport) */
#ifndef MYNEWT_VAL_BLE_HCI_TRANSPORT_NIMBLE_BUILTIN
#define MYNEWT_VAL_BLE_HCI_TRANSPORT_NIMBLE_BUILTIN (0)
#endif

#ifndef MYNEWT_VAL_BLE_HCI_TRANSPORT_RAM
#define MYNEWT_VAL_BLE_HCI_TRANSPORT_RAM (0)
#endif

#ifndef MYNEWT_VAL_BLE_HCI_TRANSPORT_SOCKET
#define MYNEWT_VAL_BLE_HCI_TRANSPORT_SOCKET (0)
#endif

#ifndef MYNEWT_VAL_BLE_TRANSPORT_EVT_SIZE
#define MYNEWT_VAL_BLE_TRANSPORT_EVT_SIZE CONFIG_BT_NIMBLE_TRANSPORT_EVT_SIZE
#endif

#ifndef MYNEWT_VAL_BLE_TRANSPORT_ACL_FROM_HS_COUNT
#define MYNEWT_VAL_BLE_TRANSPORT_ACL_FROM_HS_COUNT (0)
#endif


/* Overridden by targets/porting-nimble (defined by nimble/transport) */
#ifndef MYNEWT_VAL_BLE_HCI_TRANSPORT_UART
#define MYNEWT_VAL_BLE_HCI_TRANSPORT_UART (1)
#endif


/*** nimble/transport/uart */
#ifndef MYNEWT_VAL_BLE_ACL_BU24
#endif

#ifndef MYNEWT_VAL_BLE_HCI_ACL_OUT_COUNT
#define MYNEWT_VAL_BLE_HCI_ACL_OUT_COUNT (20)
#endif

/* Overridden by targets/porting-nimble (defined by nimble/transport/uart) */
#ifndef MYNEWT_VAL_BLE_HCI_UART_BAUD
#define MYNEWT_VAL_BLE_HCI_UART_BAUD (115200)
#endif

#ifndef MYNEWT_VAL_BLE_HCI_UART_DATA_BITS
#define MYNEWT_VAL_BLE_HCI_UART_DATA_BITS (8)
#endif

/* Overridden by targets/porting-nimble (defined by nimble/transport/uart) */
#ifndef MYNEWT_VAL_BLE_HCI_UART_FLOW_CTRL
#define MYNEWT_VAL_BLE_HCI_UART_FLOW_CTRL (0)
#endif

#ifndef MYNEWT_VAL_BLE_HCI_UART_PARITY
#define MYNEWT_VAL_BLE_HCI_UART_PARITY (HAL_UART_PARITY_NONE)
#endif

#ifndef MYNEWT_VAL_BLE_HCI_UART_PORT
#define MYNEWT_VAL_BLE_HCI_UART_PORT (0)
#endif

#ifndef MYNEWT_VAL_BLE_HCI_UART_STOP_BITS
#define MYNEWT_VAL_BLE_HCI_UART_STOP_BITS (1)
#endif


#ifndef MYNEWT_VAL_NEWT_FEATURE_LOGCFG
#define MYNEWT_VAL_NEWT_FEATURE_LOGCFG (1)
#endif

#ifndef MYNEWT_VAL_BLE_USE_ESP_TIMER
#ifdef CONFIG_BT_NIMBLE_USE_ESP_TIMER
#define MYNEWT_VAL_BLE_USE_ESP_TIMER (1)
#else
#define MYNEWT_VAL_BLE_USE_ESP_TIMER (0)
#endif
#endif

#ifndef MYNEWT_VAL_BLE_POWER_CONTROL
#ifdef CONFIG_BT_NIMBLE_BLE_POWER_CONTROL
#define MYNEWT_VAL_BLE_POWER_CONTROL   CONFIG_BT_NIMBLE_BLE_POWER_CONTROL
#else
#define MYNEWT_VAL_BLE_POWER_CONTROL (0)
#endif
#endif

#ifndef MYNEWT_VAL_BLE_HCI_VS
#define MYNEWT_VAL_BLE_HCI_VS    CONFIG_BT_NIMBLE_VS_SUPPORT
#define MYNEWT_VAL_BLE_HCI_VS_OCF_OFFSET (0)
#else
#define MYNEWT_VAL_BLE_HCI_VS    (0)
#endif

#ifndef MYNEWT_VAL_OPTIMIZE_MULTI_CONN
#ifdef CONFIG_BT_NIMBLE_OPTIMIZE_MULTI_CONN
#define MYNEWT_VAL_OPTIMIZE_MULTI_CONN  CONFIG_BT_NIMBLE_OPTIMIZE_MULTI_CONN
#else
#define MYNEWT_VAL_OPTIMIZE_MULTI_CONN (0)
#endif
#endif

#ifndef MYNEWT_VAL_BLE_HIGH_DUTY_ADV_ITVL
#ifdef CONFIG_BT_NIMBLE_HIGH_DUTY_ADV_ITVL
#define MYNEWT_VAL_BLE_HIGH_DUTY_ADV_ITVL  CONFIG_BT_NIMBLE_HIGH_DUTY_ADV_ITVL
#else
#define MYNEWT_VAL_BLE_HIGH_DUTY_ADV_ITVL  (0)
#endif
#endif

#ifndef MYNEWT_VAL_BLE_QUEUE_CONG_CHECK
#ifdef CONFIG_BT_NIMBLE_HOST_QUEUE_CONG_CHECK
#define MYNEWT_VAL_BLE_QUEUE_CONG_CHECK   CONFIG_BT_NIMBLE_HOST_QUEUE_CONG_CHECK
#else
#define MYNEWT_VAL_BLE_QUEUE_CONG_CHECK   (0)
#endif
#endif

#ifndef MYNEWT_VAL_BLE_ENABLE_CONN_REATTEMPT
#ifdef CONFIG_BT_NIMBLE_ENABLE_CONN_REATTEMPT
#define MYNEWT_VAL_BLE_ENABLE_CONN_REATTEMPT CONFIG_BT_NIMBLE_ENABLE_CONN_REATTEMPT
#else
#define MYNEWT_VAL_BLE_ENABLE_CONN_REATTEMPT (0)
#endif
#endif

#ifndef MYNEWT_VAL_BLE_AOA_AOD
#ifdef CONFIG_BT_NIMBLE_AOA_AOD
#define MYNEWT_VAL_BLE_AOA_AOD CONFIG_BT_NIMBLE_AOA_AOD
#else
#define MYNEWT_VAL_BLE_AOA_AOD (0)
#endif
#endif

#ifndef MYNEWT_VAL_BLE_GATTC_PROC_PREEMPTION_PROTECT
#ifdef CONFIG_BT_NIMBLE_GATTC_PROC_PREEMPTION_PROTECT
#define MYNEWT_VAL_BLE_GATTC_PROC_PREEMPTION_PROTECT CONFIG_BT_NIMBLE_GATTC_PROC_PREEMPTION_PROTECT
#else
#define MYNEWT_VAL_BLE_GATTC_PROC_PREEMPTION_PROTECT (0)
#endif
#endif

#ifndef MYNEWT_VAL_BLE_HOST_ALLOW_CONNECT_WITH_SCAN
#ifdef CONFIG_BT_NIMBLE_HOST_ALLOW_CONNECT_WITH_SCAN
#define MYNEWT_VAL_BLE_HOST_ALLOW_CONNECT_WITH_SCAN CONFIG_BT_NIMBLE_HOST_ALLOW_CONNECT_WITH_SCAN
#else
#define MYNEWT_VAL_BLE_HOST_ALLOW_CONNECT_WITH_SCAN (0)
#endif
#endif

#ifndef MYNEWT_VAL_BT_HCI_LOG_INCLUDED
#ifdef CONFIG_BT_HCI_LOG_DEBUG_EN
#define MYNEWT_VAL_BT_HCI_LOG_INCLUDED   CONFIG_BT_HCI_LOG_DEBUG_EN
#else
#define MYNEWT_VAL_BT_HCI_LOG_INCLUDED   (0)
#endif
#endif

#if CONFIG_BT_CONTROLLER_DISABLED && CONFIG_BT_NIMBLE_TRANSPORT_UART
#ifndef MYNEWT_VAL_BLE_TRANSPORT_UART_PORT
#define MYNEWT_VAL_BLE_TRANSPORT_UART_PORT CONFIG_BT_NIMBLE_TRANSPORT_UART_PORT
#endif

#ifndef MYNEWT_VAL_BLE_TRANSPORT_UART_PARITY__none
#define MYNEWT_VAL_BLE_TRANSPORT_UART_PARITY__none CONFIG_BT_NIMBLE_TRANSPORT_UART_PARITY_NONE
#endif

#ifndef MYNEWT_VAL_BLE_TRANSPORT_UART_PARITY__odd
#define MYNEWT_VAL_BLE_TRANSPORT_UART_PARITY__odd CONFIG_BT_NIMBLE_TRANSPORT_UART_PARITY_ODD
#endif

#ifndef MYNEWT_VAL_BLE_TRANSPORT_UART_PARITY__even
#define MYNEWT_VAL_BLE_TRANSPORT_UART_PARITY__even CONFIG_BT_NIMBLE_TRANSPORT_UART_PARITY_EVEN
#endif

#ifndef MYNEWT_VAL_BLE_TRANSPORT_UART_FLOW_CONTROL__rtscts
#define MYNEWT_VAL_BLE_TRANSPORT_UART_FLOW_CONTROL__rtscts CONFIG_BT_NIMBLE_HCI_UART_FLOW_CTRL
#endif

#ifndef MYNEWT_VAL_BLE_TRANSPORT_UART_BAUDRATE
#define MYNEWT_VAL_BLE_TRANSPORT_UART_BAUDRATE CONFIG_BT_NIMBLE_HCI_UART_BAUDRATE
#endif

#ifndef MYNEWT_VAL_BLE_TRANSPORT_UART_DATA_BITS
#define MYNEWT_VAL_BLE_TRANSPORT_UART_DATA_BITS (3)
#endif

#ifndef MYNEWT_VAL_BLE_TRANSPORT_UART_STOP_BITS
#define MYNEWT_VAL_BLE_TRANSPORT_UART_STOP_BITS (1)
#endif
#endif

#ifndef MYNEWT_VAL_BLE_PERIODIC_ADV_WITH_RESPONSES
#ifdef CONFIG_BT_NIMBLE_PERIODIC_ADV_WITH_RESPONSES
#define MYNEWT_VAL_BLE_PERIODIC_ADV_WITH_RESPONSES (CONFIG_BT_NIMBLE_PERIODIC_ADV_WITH_RESPONSES)
#else
#define MYNEWT_VAL_BLE_PERIODIC_ADV_WITH_RESPONSES (0)
#endif
#endif

#endif
