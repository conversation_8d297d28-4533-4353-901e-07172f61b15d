
menu "HCI Config"

    choice BT_LE_HCI_INTERFACE
        prompt "HCI mode"
        default BT_LE_HCI_INTERFACE_USE_RAM

        config BT_LE_HCI_INTERFACE_USE_RAM
            bool "VHCI"
            help
                Use RAM as HCI interface
        config BT_LE_HCI_INTERFACE_USE_UART
            bool "UART(H4)"
            help
                Use UART as HCI interface
    endchoice

    config BT_LE_HCI_UART_PORT
        int "HCI UART port"
        depends on BT_LE_HCI_INTERFACE_USE_UART
        default 1
        help
            Set the port number of HCI UART

    config BT_LE_HCI_UART_FLOWCTRL
        bool "HCI uart Hardware Flow ctrl"
        depends on BT_LE_HCI_INTERFACE_USE_UART
        default n

    config BT_LE_HCI_UART_TX_PIN
        int "HCI uart Tx gpio"
        depends on BT_LE_HCI_INTERFACE_USE_UART
        default 19

    config BT_LE_HCI_UART_RX_PIN
        int "HCI uart Rx gpio"
        depends on BT_LE_HCI_INTERFACE_USE_UART
        default 10

    config BT_LE_HCI_UART_RTS_PIN
        int "HCI uart RTS gpio"
        depends on BT_LE_HCI_UART_FLOWCTRL
        default 4

    config BT_LE_HCI_UART_CTS_PIN
        int "HCI uart CTS gpio"
        depends on BT_LE_HCI_UART_FLOWCTRL
        default 5

    config BT_LE_HCI_UART_BAUD
        int "HCI uart baudrate"
        depends on BT_LE_HCI_INTERFACE_USE_UART
        default 921600
        help
            HCI uart baud rate 115200 ~ 1000000

    choice BT_LE_HCI_UART_PARITY
        prompt "select uart parity"
        depends on BT_LE_HCI_INTERFACE_USE_UART
        default BT_LE_HCI_UART_UART_PARITY_DISABLE

        config BT_LE_HCI_UART_UART_PARITY_DISABLE
            bool "PARITY_DISABLE"
            help
                UART_PARITY_DISABLE
        config BT_LE_HCI_UART_UART_PARITY_EVEN
            bool "PARITY_EVEN"
            help
                UART_PARITY_EVEN
        config BT_LE_HCI_UART_UART_PARITY_ODD
            bool "PARITY_ODD"
            help
                UART_PARITY_ODD
    endchoice

    config BT_LE_HCI_UART_RX_BUFFER_SIZE
        int "The size of rx ring buffer memory"
        depends on !BT_LE_HCI_INTERFACE_USE_RAM
        default 512
        help
            The size of rx ring buffer memory

    config BT_LE_HCI_UART_TX_BUFFER_SIZE
        int "The size of tx ring buffer memory"
        depends on !BT_LE_HCI_INTERFACE_USE_RAM
        default 256
        help
            The size of tx ring buffer memory

    config BT_LE_HCI_TRANS_TASK_STACK_SIZE
        int "HCI transport task stack size"
        depends on !BT_LE_HCI_INTERFACE_USE_RAM
        default 1024
        help
            This configures stack size of hci transport task
endmenu

config BT_LE_CONTROLLER_NPL_OS_PORTING_SUPPORT
    bool
    default y
    help
        Enable NPL porting for controller.


menuconfig BT_LE_50_FEATURE_SUPPORT
    bool "Enable BLE 5 feature"
    depends on !BT_NIMBLE_ENABLED
    default y
    help
        Enable BLE 5 feature

if BT_LE_50_FEATURE_SUPPORT
    config BT_LE_LL_CFG_FEAT_LE_2M_PHY
        bool "Enable 2M Phy"
        depends on BT_LE_50_FEATURE_SUPPORT
        default y
        help
            Enable 2M-PHY

    config BT_LE_LL_CFG_FEAT_LE_CODED_PHY
        bool "Enable coded Phy"
        depends on BT_LE_50_FEATURE_SUPPORT
        default y
        help
            Enable coded-PHY

    config BT_LE_EXT_ADV
        bool "Enable extended advertising"
        depends on BT_LE_50_FEATURE_SUPPORT
        default y
        help
            Enable this option to do extended advertising. Extended advertising
            will be supported from BLE 5.0 onwards.

    if BT_LE_EXT_ADV
        config BT_LE_MAX_EXT_ADV_INSTANCES
            int "Maximum number of extended advertising instances."
            range 0 4
            default 1
            depends on BT_LE_EXT_ADV
            help
                Change this option to set maximum number of extended advertising
                instances. Minimum there is always one instance of
                advertising. Enter how many more advertising instances you
                want.
                Each extended advertising instance will take about 0.5k DRAM.

        config BT_LE_EXT_ADV_MAX_SIZE
            int "Maximum length of the advertising data."
            range 0 1650
            default 1650
            depends on BT_LE_EXT_ADV
            help
                Defines the length of the extended adv data. The value should not
                exceed 1650.

        config BT_LE_ENABLE_PERIODIC_ADV
            bool "Enable periodic advertisement."
            default y
            depends on BT_LE_EXT_ADV
            help
                Enable this option to start periodic advertisement.

        config BT_LE_PERIODIC_ADV_SYNC_TRANSFER
            bool "Enable Transfer Sync Events"
            depends on BT_LE_ENABLE_PERIODIC_ADV
            default y
            help
                This enables controller transfer periodic sync events to host
    endif

    config BT_LE_EXT_SCAN
        bool "Enable extended scanning"
        depends on BT_LE_50_FEATURE_SUPPORT && BT_LE_ROLE_OBSERVER_ENABLE
        default y
        help
            Enable this option to do extended scanning.

    config BT_LE_ENABLE_PERIODIC_SYNC
        bool "Enable periodic sync"
        default y
        depends on BT_LE_EXT_SCAN
        help
            Enable this option to receive periodic advertisement.

    if BT_LE_ENABLE_PERIODIC_SYNC
        config BT_LE_MAX_PERIODIC_SYNCS
            int "Maximum number of periodic advertising syncs"
            range 0 3
            default 1 if BT_LE_ENABLE_PERIODIC_ADV
            default 0
            help
                Set this option to set the upper limit for number of periodic sync
                connections. This should be less than maximum connections allowed by
                controller.

        config BT_LE_MAX_PERIODIC_ADVERTISER_LIST
            int "Maximum number of periodic advertiser list"
            range 1 5
            default 5
            help
                Set this option to set the upper limit for number of periodic advertiser list.
    endif
endif

menu "Memory Settings"
    depends on !BT_NIMBLE_ENABLED

    config BT_LE_MSYS_1_BLOCK_COUNT
        int "MSYS_1 Block Count"
        default 12
        help
            MSYS is a system level mbuf registry. For prepare write & prepare
            responses MBUFs are allocated out of msys_1 pool. For NIMBLE_MESH
            enabled cases, this block count is increased by 8 than user defined
            count.

    config BT_LE_MSYS_1_BLOCK_SIZE
        int "MSYS_1 Block Size"
        default 256
        help
            Dynamic memory size of block 1

    config BT_LE_MSYS_2_BLOCK_COUNT
        int "MSYS_2 Block Count"
        default 24
        help
            Dynamic memory count

    config BT_LE_MSYS_2_BLOCK_SIZE
        int "MSYS_2 Block Size"
        default 320
        help
            Dynamic memory size of block 2

    config BT_LE_ACL_BUF_COUNT
        int "ACL Buffer count"
        default 10
        help
            The number of ACL data buffers.

    config BT_LE_ACL_BUF_SIZE
        int "ACL Buffer size"
        default 517
        help
            This is the maximum size of the data portion of HCI ACL data packets.
            It does not include the HCI data header (of 4 bytes)

    config BT_LE_HCI_EVT_BUF_SIZE
        int "HCI Event Buffer size"
        default 257 if BT_LE_EXT_ADV
        default 70
        help
            This is the size of each HCI event buffer in bytes. In case of
            extended advertising, packets can be fragmented. 257 bytes is the
            maximum size of a packet.

    config BT_LE_HCI_EVT_HI_BUF_COUNT
        int "High Priority HCI Event Buffer count"
        default 30
        help
            This is the high priority HCI events' buffer size. High-priority
            event buffers are for everything except advertising reports. If there
            are no free high-priority event buffers then host will try to allocate a
            low-priority buffer instead

    config BT_LE_HCI_EVT_LO_BUF_COUNT
        int "Low Priority HCI Event Buffer count"
        default 8
        help
            This is the low priority HCI events' buffer size. Low-priority event
            buffers are only used for advertising reports. If there are no free
            low-priority event buffers, then an incoming advertising report will
            get dropped
endmenu

config BT_LE_CONTROLLER_TASK_STACK_SIZE
    int "Controller task stack size"
    default 5120 if BLE_MESH
    default 4096
    help
        This configures stack size of NimBLE controller task

menuconfig BT_LE_CONTROLLER_LOG_ENABLED
    bool "Controller log enable"
    default n
    help
        Enable controller log

config BT_LE_CONTROLLER_LOG_CTRL_ENABLED
    bool "enable controller log module"
    depends on BT_LE_CONTROLLER_LOG_ENABLED
    default y
    help
            Enable controller log module

config BT_LE_CONTROLLER_LOG_HCI_ENABLED
    bool "enable HCI log module"
    depends on BT_LE_CONTROLLER_LOG_ENABLED
    default y
    help
            Enable hci log module

config BT_LE_CONTROLLER_LOG_DUMP_ONLY
    bool "Controller log dump mode only"
    depends on BT_LE_CONTROLLER_LOG_ENABLED
    default y
    help
            Only operate in dump mode

config BT_LE_CONTROLLER_LOG_SPI_OUT_ENABLED
    bool "Output ble controller logs to SPI bus (Experimental)"
    depends on BT_LE_CONTROLLER_LOG_ENABLED
    depends on !BT_LE_CONTROLLER_LOG_DUMP_ONLY
    select BT_BLE_LOG_SPI_OUT_ENABLED
    default n
    help
        Output ble controller logs to SPI bus

config BT_LE_CONTROLLER_LOG_STORAGE_ENABLE
    bool "Store ble controller logs to flash(Experimental)"
    depends on !BT_LE_CONTROLLER_LOG_DUMP_ONLY
    depends on BT_LE_CONTROLLER_LOG_ENABLED
    default n
    help
            Store ble controller logs to flash memory.

config BT_LE_CONTROLLER_LOG_PARTITION_SIZE
    int "size of ble controller log partition(Multiples of 4K)"
    depends on BT_LE_CONTROLLER_LOG_STORAGE_ENABLE
    default 65536
    help
            The size of ble controller log partition shall be a multiples of 4K.
            The name of log partition shall be "bt_ctrl_log".
            The partition type shall be ESP_PARTITION_TYPE_DATA.
            The partition sub_type shall be ESP_PARTITION_SUBTYPE_ANY.

config BT_LE_LOG_CTRL_BUF1_SIZE
    int "size of the first BLE controller LOG buffer"
    depends on BT_LE_CONTROLLER_LOG_ENABLED
    default 4096
    help
            Configure the size of the first BLE controller LOG buffer.

config BT_LE_LOG_CTRL_BUF2_SIZE
    int "size of the second BLE controller LOG buffer"
    depends on BT_LE_CONTROLLER_LOG_ENABLED
    default 1024
    help
            Configure the size of the second BLE controller LOG buffer.

config BT_LE_LOG_HCI_BUF_SIZE
    int "size of the BLE HCI LOG buffer"
    depends on BT_LE_CONTROLLER_LOG_ENABLED
    default 4096
    help
            Configure the size of the BLE HCI LOG buffer.

config BT_LE_CONTROLLER_LOG_WRAP_PANIC_HANDLER_ENABLE
    bool "Enable wrap panic handler"
    depends on BT_LE_CONTROLLER_LOG_ENABLED
    default n
    help
        Wrap esp_panic_handler to get controller logs when PC pointer exception crashes.

config BT_LE_CONTROLLER_LOG_TASK_WDT_USER_HANDLER_ENABLE
    bool "Enable esp_task_wdt_isr_user_handler implementation"
    depends on BT_LE_CONTROLLER_LOG_ENABLED
    default n
    help
        Implement esp_task_wdt_isr_user_handler to get controller logs when task wdt issue is triggered.
config BT_LE_LL_RESOLV_LIST_SIZE
    int "BLE LL Resolving list size"
    range 1 5
    default 4
    help
        Configure the size of resolving list used in link layer.

menuconfig BT_LE_SECURITY_ENABLE
    bool "Enable BLE SM feature"
    depends on !BT_NIMBLE_ENABLED
    default y
    help
        Enable BLE sm feature

config BT_LE_SM_LEGACY
    bool "Security manager legacy pairing"
    depends on BT_LE_SECURITY_ENABLE
    default y
    help
        Enable security manager legacy pairing

config BT_LE_SM_SC
    bool "Security manager secure connections (4.2)"
    depends on BT_LE_SECURITY_ENABLE
    default y
    help
        Enable security manager secure connections

config BT_LE_SM_SC_DEBUG_KEYS
    bool "Use predefined public-private key pair"
    default n
    depends on BT_LE_SECURITY_ENABLE && BT_LE_SM_SC
    help
        If this option is enabled, SM uses predefined DH key pair as described
        in Core Specification, Vol. 3, Part H, 2.3.5.6.1. This allows to
        decrypt air traffic easily and thus should only be used for debugging.

config BT_LE_LL_CFG_FEAT_LE_ENCRYPTION
    bool "Enable LE encryption"
    depends on BT_LE_SECURITY_ENABLE
    default y
    help
        Enable encryption connection

config BT_LE_CRYPTO_STACK_MBEDTLS
    bool "Override TinyCrypt with mbedTLS for crypto computations"
    default y
    depends on !BT_NIMBLE_ENABLED
    select MBEDTLS_CMAC_C
    help
        Enable this option to choose mbedTLS instead of TinyCrypt for crypto
        computations.

config BT_LE_WHITELIST_SIZE
    int "BLE white list size"
    range 1 15
    default 12
    depends on !BT_NIMBLE_ENABLED

    help
        BLE list size

config BT_LE_LL_DUP_SCAN_LIST_COUNT
    int "BLE duplicate scan list count"
    range 1 100
    default 20
    help
        config the max count of duplicate scan list

config BT_LE_LL_SCA
    int "BLE Sleep clock accuracy"
    range 0 500
    default 60
    help
        Sleep clock accuracy of our device (in ppm)

config BT_LE_MAX_CONNECTIONS
    int "Maximum number of concurrent connections"
    depends on !BT_NIMBLE_ENABLED
    range 1 2
    default 2
    help
        Defines maximum number of concurrent BLE connections. For ESP32, user
        is expected to configure BTDM_CTRL_BLE_MAX_CONN from controller menu
        along with this option. Similarly for ESP32-C3 or ESP32-S3, user is expected to
        configure BT_CTRL_BLE_MAX_ACT from controller menu.
        Each connection will take about 1k DRAM.

choice BT_LE_COEX_PHY_CODED_TX_RX_TLIM
    prompt "Coexistence: limit on MAX Tx/Rx time for coded-PHY connection"
    default BT_LE_COEX_PHY_CODED_TX_RX_TLIM_DIS
    depends on ESP_COEX_SW_COEXIST_ENABLE
    help
        When using PHY-Coded in BLE connection, limitation on max tx/rx time can be applied to
        better avoid dramatic performance deterioration of Wi-Fi.

    config BT_LE_COEX_PHY_CODED_TX_RX_TLIM_EN
        bool "Force Enable"
        help
            Always enable the limitation on max tx/rx time for Coded-PHY connection

    config BT_LE_COEX_PHY_CODED_TX_RX_TLIM_DIS
        bool "Force Disable"
        help
            Disable the limitation on max tx/rx time for Coded-PHY connection
endchoice

config BT_LE_COEX_PHY_CODED_TX_RX_TLIM_EFF
    int
    default 0 if !ESP_COEX_SW_COEXIST_ENABLE
    default 1 if BT_LE_COEX_PHY_CODED_TX_RX_TLIM_EN
    default 0 if BT_LE_COEX_PHY_CODED_TX_RX_TLIM_DIS

config BT_LE_SLEEP_ENABLE
    bool "Enable BLE sleep"
    default n
    help
        Enable BLE sleep

choice BT_LE_LP_CLK_SRC
    prompt "BLE low power clock source"
    default BT_LE_LP_CLK_SRC_MAIN_XTAL
    config BT_LE_LP_CLK_SRC_MAIN_XTAL
        bool "Use main XTAL as RTC clock source"
        help
            User main XTAL as RTC clock source.
            This option is recommended if external 32.768k XTAL is not available.
            Using the external 32.768 kHz XTAL will have lower current consumption
            in light sleep compared to using the main XTAL.

    config BT_LE_LP_CLK_SRC_DEFAULT
        bool "Use system RTC slow clock source"
        help
            Use the same slow clock source as system RTC
            Using any clock source other than external 32.768 kHz XTAL at pin0 supports only
            legacy ADV and SCAN due to low clock accuracy.

endchoice

config BT_LE_USE_ESP_TIMER
    bool "Use Esp Timer for callout"
    depends on !BT_NIMBLE_ENABLED
    default y
    help
        Set this option to use Esp Timer which has higher priority timer instead of FreeRTOS timer

config BT_CTRL_BLE_ADV_REPORT_FLOW_CTRL_SUPP
    bool "BLE adv report flow control supported"
    depends on BT_LE_ROLE_OBSERVER_ENABLE
    default y
    help
        The function is mainly used to enable flow control for advertising reports. When it is enabled,
        advertising reports will be discarded by the controller if the number of unprocessed advertising
        reports exceeds the size of BLE adv report flow control.

config BT_CTRL_BLE_ADV_REPORT_FLOW_CTRL_NUM
    int "BLE adv report flow control number"
    depends on BT_CTRL_BLE_ADV_REPORT_FLOW_CTRL_SUPP
    range 50 1000
    default 100
    help
        The number of unprocessed advertising report that bluetooth host can save.If you set
        `BT_CTRL_BLE_ADV_REPORT_FLOW_CTRL_NUM` to a small value, this may cause adv packets lost.
        If you set `BT_CTRL_BLE_ADV_REPORT_FLOW_CTRL_NUM` to a large value, bluetooth host may cache a
        lot of adv packets and this may cause system memory run out. For example, if you set
        it to 50, the maximum memory consumed by host is 35 * 50 bytes. Please set
        `BT_CTRL_BLE_ADV_REPORT_FLOW_CTRL_NUM` according to your system free memory and handle adv
        packets as fast as possible, otherwise it will cause adv packets lost.

config BT_CTRL_BLE_ADV_REPORT_DISCARD_THRSHOLD
    int "BLE adv lost event threshold value"
    depends on BT_CTRL_BLE_ADV_REPORT_FLOW_CTRL_SUPP
    range 1 1000
    default 20
    help
        When adv report flow control is enabled, The ADV lost event will be generated when the number
        of ADV packets lost in the controller reaches this threshold. It is better to set a larger value.
        If you set `BT_CTRL_BLE_ADV_REPORT_DISCARD_THRSHOLD` to a small value or printf every adv lost event, it
        may cause adv packets lost more.

config BT_LE_RELEASE_IRAM_SUPPORTED
    bool
    default y

config BT_LE_TX_CCA_ENABLED
    bool "Enable TX CCA feature"
    default n
    help
        Enable CCA feature to cancel sending the packet if the signal power is stronger than CCA threshold.

config BT_LE_DTM_ENABLED
    bool "Enable Direct Test Mode (DTM) feature"
    default n

config BT_LE_CCA_RSSI_THRESH
    int "CCA RSSI threshold value"
    depends on BT_LE_TX_CCA_ENABLED
    range 20 100
    default 20
    help
        Power threshold of CCA in unit of -1 dBm.

config BT_LE_FEAT_LL_ENCRYPTION
    bool "Enable controller ACL encryption"
    default y

config BT_LE_ROLE_CENTROL_ENABLE
    bool "Enable BLE Centrol role function"
    depends on !BT_NIMBLE_ENABLED
    default y
    help
        Enable centrol role function.

config BT_LE_ROLE_PERIPHERAL_ENABLE
    bool "Enable BLE Peripheral role function"
    depends on !BT_NIMBLE_ENABLED
    default y
    help
        Enable Peripheral role function.

config BT_LE_ROLE_BROADCASTER_ENABLE
    bool "Enable BLE Broadcaster role function"
    depends on !BT_NIMBLE_ENABLED
    default y
    help
        Enable broadcaster role function.

config BT_LE_ROLE_OBSERVER_ENABLE
    bool "Enable BLE Observer role function"
    depends on !BT_NIMBLE_ENABLED
    default y
    help
        Enable observer role function.

choice BT_LE_DFT_TX_POWER_LEVEL_DBM
    prompt "BLE default Tx power level(dBm)"
    default BT_LE_DFT_TX_POWER_LEVEL_P9
    help
        Specify default Tx power level(dBm).
    config BT_LE_DFT_TX_POWER_LEVEL_N24
        bool "-24dBm"
    config BT_LE_DFT_TX_POWER_LEVEL_N21
        bool "-21dBm"
    config BT_LE_DFT_TX_POWER_LEVEL_N18
        bool "-18dBm"
    config BT_LE_DFT_TX_POWER_LEVEL_N15
        bool "-15dBm"
    config BT_LE_DFT_TX_POWER_LEVEL_N12
        bool "-12dBm"
    config BT_LE_DFT_TX_POWER_LEVEL_N9
        bool "-9dBm"
    config BT_LE_DFT_TX_POWER_LEVEL_N6
        bool "-6dBm"
    config BT_LE_DFT_TX_POWER_LEVEL_N3
        bool "-3dBm"
    config BT_LE_DFT_TX_POWER_LEVEL_N0
        bool "0dBm"
    config BT_LE_DFT_TX_POWER_LEVEL_P3
        bool "+3dBm"
    config BT_LE_DFT_TX_POWER_LEVEL_P6
        bool "+6dBm"
    config BT_LE_DFT_TX_POWER_LEVEL_P9
        bool "+9dBm"
    config BT_LE_DFT_TX_POWER_LEVEL_P12
        bool "+12dBm"
    config BT_LE_DFT_TX_POWER_LEVEL_P15
        bool "+15dBm"
    config BT_LE_DFT_TX_POWER_LEVEL_P18
        bool "+18dBm"
    config BT_LE_DFT_TX_POWER_LEVEL_P20
        bool "+20dBm"
endchoice

config BT_LE_DFT_TX_POWER_LEVEL_DBM_EFF
    int
    default -24 if BT_LE_DFT_TX_POWER_LEVEL_N24
    default -21 if BT_LE_DFT_TX_POWER_LEVEL_N21
    default -18 if BT_LE_DFT_TX_POWER_LEVEL_N18
    default -15 if BT_LE_DFT_TX_POWER_LEVEL_N15
    default -12 if BT_LE_DFT_TX_POWER_LEVEL_N12
    default -9 if BT_LE_DFT_TX_POWER_LEVEL_N9
    default -6 if BT_LE_DFT_TX_POWER_LEVEL_N6
    default -3 if BT_LE_DFT_TX_POWER_LEVEL_N3
    default 0 if BT_LE_DFT_TX_POWER_LEVEL_N0
    default 3 if BT_LE_DFT_TX_POWER_LEVEL_P3
    default 6 if BT_LE_DFT_TX_POWER_LEVEL_P6
    default 9 if BT_LE_DFT_TX_POWER_LEVEL_P9
    default 12 if BT_LE_DFT_TX_POWER_LEVEL_P12
    default 15 if BT_LE_DFT_TX_POWER_LEVEL_P15
    default 18 if BT_LE_DFT_TX_POWER_LEVEL_P18
    default 20 if BT_LE_DFT_TX_POWER_LEVEL_P20
    default 0

config BT_CTRL_RUN_IN_FLASH_ONLY
    bool "Reduce BLE IRAM usage (READ DOCS FIRST) (EXPERIMENTAL)"
    default n
    help
        Move most IRAM into flash. This will increase the usage of flash and reduce ble performance.
        Because the code is moved to the flash, the execution speed of the code is reduced.
        To have a small impact on performance, you need to enable flash suspend (SPI_FLASH_AUTO_SUSPEND).

        - Only one Tx-Rx can be performed in each connection interval. Therefore, reduce the connection interval
          as much as possible to improve the throughput. If you want higher connection performance, you can
          enable BT_LE_PLACE_CONN_RELATED_INTO_IRAM to put the connection-related code into iram.
        - For HCI_LE_Extended_Create_Connection command, only 1M phy's connection parameters will be applied.
          Other phys' will be ignored.
        - For extended scanning, we may be unable to receive the extended adv with 300us MAFS.

config BT_LE_PLACE_CONN_RELATED_INTO_IRAM
    bool "Place the connection-related code into IRAM"
    depends on BT_CTRL_RUN_IN_FLASH_ONLY
    default n

config BT_LE_CTRL_CHECK_CONNECT_IND_ACCESS_ADDRESS
    bool "Enable enhanced Access Address check in CONNECT_IND"
    default n
    help
        Enabling this option will add stricter verification of the Access Address in the CONNECT_IND PDU.
        This improves security by ensuring that only connection requests with valid Access Addresses are accepted.
        If disabled, only basic checks are applied, improving compatibility.
