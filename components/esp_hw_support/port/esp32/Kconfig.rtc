choice RTC_CLK_SRC
    prompt "RTC clock source"
    default RTC_CLK_SRC_INT_RC
    help
        Choose which clock is used as RTC clock source.

        - "Internal 150kHz oscillator" option provides lowest deep sleep current
            consumption, and does not require extra external components. However
            frequency stability with respect to temperature is poor, so time may
            drift in deep/light sleep modes.
        - "External 32kHz crystal" provides better frequency stability, at the
            expense of slightly higher (1uA) deep sleep current consumption.
        - "External 32kHz oscillator" allows using 32kHz clock generated by an
            external circuit. In this case, external clock signal must be connected
            to 32K_XN pin. Amplitude should be <1.2V in case of sine wave signal,
            and <1V in case of square wave signal. Common mode voltage should be
            0.1 < Vcm < 0.5Vamp, where Vamp is the signal amplitude.
            Additionally, 1nF capacitor must be connected between 32K_XP pin and
            ground. 32K_XP pin can not be used as a GPIO in this case.
        - "Internal 8.5MHz oscillator divided by 256" option results in higher
            deep sleep current (by 5uA) but has better frequency stability than
            the internal 150kHz oscillator. It does not require external components.

    config RTC_CLK_SRC_INT_RC
        bool "Internal 150 kHz RC oscillator"
    config RTC_CLK_SRC_EXT_CRYS
        bool "External 32kHz crystal"
        select ESP_SYSTEM_RTC_EXT_XTAL
    config RTC_CLK_SRC_EXT_OSC
        bool "External 32kHz oscillator at 32K_XN pin"
        select ESP_SYSTEM_RTC_EXT_OSC
    config RTC_CLK_SRC_INT_8MD256
        bool "Internal 8.5MHz oscillator, divided by 256 (~33kHz)"
endchoice

choice RTC_EXT_CRYST_ADDIT_CURRENT_METHOD
    prompt "Additional current for external 32kHz crystal"
    depends on RTC_CLK_SRC_EXT_CRYS
    depends on ESP32_REV_MIN_FULL < 200
    default RTC_EXT_CRYST_ADDIT_CURRENT_NONE
    help
        With some 32kHz crystal configurations, the X32N and X32P pins may not have enough
        drive strength to keep the crystal oscillating. Choose the method to provide
        additional current from touchpad 9 to the external 32kHz crystal. Note that
        the deep sleep current is slightly high (4-5uA) and the touchpad and the
        wakeup sources of both touchpad and ULP are not available in method 1 and method 2.

        This problem is fixed in ESP32 ECO 3, so this workaround is not needed. Setting the
        project configuration to minimum revision ECO3 will disable this option, , allow
        all wakeup sources, and save some code size.

        - "None" option will not provide additional current to external crystal
        - "Method 1" option can't ensure 100% to solve the external 32k crystal start failed
            issue, but the touchpad can work in this method.
        - "Method 2" option can solve the external 32k issue, but the touchpad can't work
            in this method.

    config RTC_EXT_CRYST_ADDIT_CURRENT_NONE
        bool "None"
    config RTC_EXT_CRYST_ADDIT_CURRENT
        bool "Method 1"
    config RTC_EXT_CRYST_ADDIT_CURRENT_V2
        bool "Method 2"
endchoice

config RTC_CLK_CAL_CYCLES
    int "Number of cycles for RTC_SLOW_CLK calibration"
    default 3000 if RTC_CLK_SRC_EXT_CRYS || RTC_CLK_SRC_EXT_OSC || RTC_CLK_SRC_INT_8MD256
    default 1024 if RTC_CLK_SRC_INT_RC
    range 0 27000 if RTC_CLK_SRC_EXT_CRYS || RTC_CLK_SRC_EXT_OSC || RTC_CLK_SRC_INT_8MD256
    range 0 32766 if RTC_CLK_SRC_INT_RC
    help
        When the startup code initializes RTC_SLOW_CLK, it can perform
        calibration by comparing the RTC_SLOW_CLK frequency with main XTAL
        frequency. This option sets the number of RTC_SLOW_CLK cycles measured
        by the calibration routine. Higher numbers increase calibration
        precision, which may be important for applications which spend a lot of
        time in deep sleep. Lower numbers reduce startup time.

        When this option is set to 0, clock calibration will not be performed at
        startup, and approximate clock frequencies will be assumed:

        - 150000 Hz if internal RC oscillator is used as clock source. For this use value 1024.
        - 32768 Hz if the 32k crystal oscillator is used. For this use value 3000 or more.
            In case more value will help improve the definition of the launch of the crystal.
            If the crystal could not start, it will be switched to internal RC.

config RTC_XTAL_CAL_RETRY
    int "Number of attempts to repeat 32k XTAL calibration"
    default 1
    depends on RTC_CLK_SRC_EXT_CRYS
    help
        Number of attempts to repeat 32k XTAL calibration
        before giving up and switching to the internal RC.
        Increase this option if the 32k crystal oscillator
        does not start and switches to internal RC.
