menu "Brownout Detector"
    config ESP_BROWNOUT_DET
        bool "Hardware brownout detect & reset"
        depends on !IDF_ENV_FPGA
        default y
        help
            The ESP32-P4 has a built-in brownout detector which can detect if the voltage is lower than
            a specific value. If this happens, it will reset the chip in order to prevent unintended
            behaviour.

    choice ESP_BROWNOUT_DET_LVL_SEL
        prompt "Brownout voltage level"
        default ESP_BROWNOUT_DET_LVL_SEL_7
        depends on ESP_BROWNOUT_DET
        help
            The brownout detector will reset the chip when the supply voltage is approximately
            below this level. Note that there may be some variation of brownout voltage level
            between each chip.

            Please note that this config is only valid when P4 SOC version is above v1.0. When you are using a
            earlier P4 SOC version (v0.x), the brownout value should be fixed around 2.52V and not configurable.

            #The voltage levels here are estimates, more work needs to be done to figure out the exact voltages
            #of the brownout threshold levels.
        config ESP_BROWNOUT_DET_LVL_SEL_7
            bool "2.6V"
        config ESP_BROWNOUT_DET_LVL_SEL_6
            bool "2.52V"
        config ESP_BROWNOUT_DET_LVL_SEL_5
            bool "2.42V"
    endchoice

    config ESP_BROWNOUT_DET_LVL
        int
        default 5 if ESP_BROWNOUT_DET_LVL_SEL_5
        default 6 if ESP_BROWNOUT_DET_LVL_SEL_6
        default 7 if ESP_BROWNOUT_DET_LVL_SEL_7

endmenu
