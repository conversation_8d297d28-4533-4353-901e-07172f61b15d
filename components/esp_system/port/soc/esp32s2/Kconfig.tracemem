menu "Trace memory"

    config ESP32S2_MEMMAP_TRACEMEM
        bool
        default "n"

    config ESP32S2_MEMMAP_TRACEMEM_TWOBANKS
        bool
        default "n"

    config ESP32S2_TRAX
        bool "Use TRAX tracing feature"
        default "n"
        select <PERSON>SP32S2_MEMMAP_TRACEMEM
        help
            The ESP32S2 contains a feature which allows you to trace the execution path the processor
            has taken through the program. This is stored in a chunk of 32K (16K for single-processor)
            of memory that can't be used for general purposes anymore. Disable this if you do not know
            what this is.

    config ESP32S2_TRACEMEM_RESERVE_DRAM
        hex
        default 0x8000 if ESP32S2_MEMMAP_TRACEMEM && ESP32S2_MEMMAP_TRACEMEM_TWOBANKS
        default 0x4000 if ESP32S2_MEMMAP_TRACEMEM && !ESP32S2_MEMMAP_TRACEMEM_TWOBANKS
        default 0x0

endmenu  # Trace memory
