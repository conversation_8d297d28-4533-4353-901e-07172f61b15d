menu "Brownout Detector"

    config ESP_BROWNOUT_DET
        bool "Hardware brownout detect & reset"
        depends on !IDF_ENV_FPGA
        default y
        help
            The ESP32-S3 has a built-in brownout detector which can detect if the voltage is lower than
            a specific value. If this happens, it will reset the chip in order to prevent unintended
            behaviour.

    choice ESP_BROWNOUT_DET_LVL_SEL
        prompt "Brownout voltage level"
        depends on ESP_BROWNOUT_DET
        default ESP_BROWNOUT_DET_LVL_SEL_7
        help
            The brownout detector will reset the chip when the supply voltage is approximately
            below this level. Note that there may be some variation of brownout voltage level
            between each ESP3-S3 chip.

            #The voltage levels here are estimates, more work needs to be done to figure out the exact voltages
            #of the brownout threshold levels.
        config ESP_BROWNOUT_DET_LVL_SEL_7
            bool "2.44V"
        config ESP_BROWNOUT_DET_LVL_SEL_6
            bool "2.56V"
        config ESP_BROWNOUT_DET_LVL_SEL_5
            bool "2.67V"
        config ESP_BROWNOUT_DET_LVL_SEL_4
            bool "2.84V"
        config ESP_BROWNOUT_DET_LVL_SEL_3
            bool "2.98V"
        config ESP_BROWNOUT_DET_LVL_SEL_2
            bool "3.19V"
        config ESP_BROWNOUT_DET_LVL_SEL_1
            bool "3.30V"
    endchoice

    config ESP_BROWNOUT_DET_LVL
        int
        default 1 if ESP_BROWNOUT_DET_LVL_SEL_1
        default 2 if ESP_BROWNOUT_DET_LVL_SEL_2
        default 3 if ESP_BROWNOUT_DET_LVL_SEL_3
        default 4 if ESP_BROWNOUT_DET_LVL_SEL_4
        default 5 if ESP_BROWNOUT_DET_LVL_SEL_5
        default 6 if ESP_BROWNOUT_DET_LVL_SEL_6
        default 7 if ESP_BROWNOUT_DET_LVL_SEL_7

endmenu
