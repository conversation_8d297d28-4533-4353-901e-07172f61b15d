menu "Trace memory"

    config ESP32S3_MEMMAP_TRACEMEM
        bool
        default "n"

    config ESP32S3_MEMMAP_TRACEMEM_TWOBANKS
        bool
        default "n"

    config ESP32S3_TRAX
        bool "Use TRAX tracing feature"
        default "n"
        select ESP32S3_MEMMAP_TRACEMEM
        help
            The esp32-s3 contains a feature which allows you to trace the execution path the processor
            has taken through the program. This is stored in a chunk of 32K (16K for single-processor)
            of memory that can't be used for general purposes anymore. Disable this if you do not know
            what this is.

    config ESP32S3_TRAX_TWOBANKS
        bool "Reserve memory for tracing both pro as well as app cpu execution"
        default "n"
        depends on ESP32S3_TRAX && !ESP_SYSTEM_SINGLE_CORE_MODE
        select ESP32S3_MEMMAP_TRACEMEM_TWOBANKS
        help
            The esp32-s3 contains a feature which allows you to trace the execution path the processor
            has taken through the program. This is stored in a chunk of 32K (16K for single-processor)
            of memory that can't be used for general purposes anymore. Disable this if you do not know
            what this is.

    config ESP32S3_TRACEMEM_RESERVE_DRAM
        hex
        default 0x8000 if ESP32S3_MEMMAP_TRACEMEM && ESP32S3_MEMMAP_TRACEMEM_TWOBANKS
        default 0x4000 if ESP32S3_MEMMAP_TRACEMEM && !ESP32S3_MEMMAP_TRACEMEM_TWOBANKS
        default 0x0

endmenu  # Trace memory
