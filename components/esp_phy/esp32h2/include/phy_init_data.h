/*
 * SPDX-FileCopyrightText: 2016-2022 Espressif Systems (Shanghai) CO LTD
 *
 * SPDX-License-Identifier: Apache-2.0
 */

#ifndef PHY_INIT_DATA_H
#define PHY_INIT_DATA_H /* don't use #pragma once here, we compile this file sometimes */
#include "esp_phy_init.h"
#include "sdkconfig.h"

#ifdef __cplusplus
extern "C" {
#endif

// There is no init data for ESP32H2 right now, could be added when necessary.

#ifdef __cplusplus
}
#endif

#endif /* PHY_INIT_DATA_H */
