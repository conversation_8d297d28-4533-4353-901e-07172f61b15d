/*
 * SPDX-FileCopyrightText: 2021-2025 Espressif Systems (Shanghai) CO LTD
 *
 * SPDX-License-Identifier: Apache-2.0
 */

/* ROM function interface esp32c2.rom.ld for esp32c2
 *
 *
 * Generated from ./interface-esp32c2.yml md5sum c679b6ed5e9f0a9c3e7b93e5e0f2a1a3
 *
 * Compatible with ROM where ECO version equal or greater to 1.
 *
 * THIS FILE WAS AUTOMATICALLY GENERATED. DO NOT EDIT.
 */


/***************************************
 Group bluetooth
 ***************************************/

/* Functions */
ble_controller_rom_data_init = 0x40000aa8;
ble_osi_coex_funcs_register = 0x40000aac;
bt_rf_coex_cfg_get_default = 0x40000ab0;
bt_rf_coex_dft_pti_get_default = 0x40000ab4;
bt_rf_coex_hooks_p_set = 0x40000ab8;
r__os_mbuf_copypkthdr = 0x40000abc;
r__os_msys_find_pool = 0x40000ac0;
r_ble_controller_get_rom_compile_version = 0x40000ac4;
//r_ble_hci_ram_hs_acl_tx = 0x40000ac8;
//r_ble_hci_ram_hs_cmd_tx = 0x40000acc;
//r_ble_hci_ram_ll_acl_tx = 0x40000ad0;
//r_ble_hci_ram_ll_evt_tx = 0x40000ad4;
r_ble_hci_ram_reset = 0x40000ad8;
r_ble_hci_ram_set_acl_free_cb = 0x40000adc;
r_ble_hci_trans_acl_buf_alloc = 0x40000ae0;
r_ble_hci_trans_buf_alloc = 0x40000ae4;
r_ble_hci_trans_buf_free = 0x40000ae8;
r_ble_hci_trans_cfg_hs = 0x40000aec;
r_ble_hci_trans_cfg_ll = 0x40000af0;
r_ble_hci_trans_deinit = 0x40000af4;
//r_ble_hci_trans_env_init = 0x40000af8;
r_ble_hci_trans_init = 0x40000afc;
r_ble_hci_uart_acl_tx = 0x40000b00;
r_ble_hci_uart_cmdevt_tx = 0x40000b04;
r_ble_hci_uart_config = 0x40000b08;
r_ble_hci_uart_free_pkt = 0x40000b0c;
r_ble_hci_uart_hs_acl_tx = 0x40000b10;
r_ble_hci_uart_hs_cmd_tx = 0x40000b14;
r_ble_hci_uart_ll_acl_tx = 0x40000b18;
r_ble_hci_uart_ll_evt_tx = 0x40000b1c;
r_ble_hci_uart_rx_acl = 0x40000b20;
r_ble_hci_uart_rx_char = 0x40000b24;
r_ble_hci_uart_rx_cmd = 0x40000b28;
r_ble_hci_uart_rx_evt = 0x40000b2c;
r_ble_hci_uart_rx_evt_cb = 0x40000b30;
r_ble_hci_uart_rx_le_evt = 0x40000b34;
r_ble_hci_uart_rx_pkt_type = 0x40000b38;
r_ble_hci_uart_rx_skip_acl = 0x40000b3c;
r_ble_hci_uart_rx_skip_cmd = 0x40000b40;
r_ble_hci_uart_rx_skip_evt = 0x40000b44;
r_ble_hci_uart_rx_sync_loss = 0x40000b48;
r_ble_hci_uart_set_acl_free_cb = 0x40000b4c;
r_ble_hci_uart_sync_lost = 0x40000b50;
r_ble_hci_uart_trans_reset = 0x40000b54;
r_ble_hci_uart_tx_char = 0x40000b58;
r_ble_hci_uart_tx_pkt_type = 0x40000b5c;
r_ble_hw_driver_deinit = 0x40000b60;
r_ble_hw_driver_env_init = 0x40000b64;
r_ble_hw_encrypt_block = 0x40000b68;
r_ble_hw_get_public_addr = 0x40000b6c;
r_ble_hw_get_static_addr = 0x40000b70;
r_ble_hw_periodiclist_add = 0x40000b74;
r_ble_hw_periodiclist_clear = 0x40000b78;
r_ble_hw_periodiclist_rmv = 0x40000b7c;
r_ble_hw_resolv_list_cur_entry = 0x40000b80;
r_ble_hw_resolv_list_get_cur_entry = 0x40000b84;
r_ble_hw_resolv_list_set = 0x40000b88;
r_ble_hw_rng_init = 0x40000b8c;
r_ble_hw_rng_start = 0x40000b90;
r_ble_hw_rng_stop = 0x40000b94;
r_ble_hw_rx_local_is_resolved = 0x40000b98;
r_ble_hw_rx_local_is_rpa = 0x40000b9c;
r_ble_hw_whitelist_add = 0x40000ba0;
r_ble_hw_whitelist_clear = 0x40000ba4;
r_ble_hw_whitelist_dev_num = 0x40000ba8;
r_ble_hw_whitelist_get_base = 0x40000bac;
r_ble_hw_whitelist_rmv = 0x40000bb0;
r_ble_hw_whitelist_search = 0x40000bb4;
r_ble_hw_whitelist_sort = 0x40000bb8;
r_ble_ll_acl_data_in = 0x40000bbc;
r_ble_ll_addr_is_id = 0x40000bc0;
r_ble_ll_addr_subtype = 0x40000bc4;
r_ble_ll_adv_active_chanset_clear = 0x40000bc8;
r_ble_ll_adv_active_chanset_is_pri = 0x40000bcc;
r_ble_ll_adv_active_chanset_is_sec = 0x40000bd0;
r_ble_ll_adv_active_chanset_set_pri = 0x40000bd4;
r_ble_ll_adv_active_chanset_set_sec = 0x40000bd8;
r_ble_ll_adv_aux_calculate = 0x40000bdc;
r_ble_ll_adv_aux_conn_rsp_pdu_make = 0x40000be0;
r_ble_ll_adv_aux_pdu_make = 0x40000be4;
r_ble_ll_adv_aux_scannable_pdu_make = 0x40000be8;
r_ble_ll_adv_aux_txed = 0x40000bec;
r_ble_ll_adv_can_chg_whitelist = 0x40000bf0;
r_ble_ll_adv_chk_rpa_timeout = 0x40000bf4;
r_ble_ll_adv_clear_all = 0x40000bf8;
r_ble_ll_adv_conn_req_rxd = 0x40000bfc;
r_ble_ll_adv_deinit = 0x40000c00;
r_ble_ll_adv_enabled = 0x40000c04;
r_ble_ll_adv_env_init = 0x40000c08;
r_ble_ll_adv_ext_set_adv_data = 0x40000c0c;
r_ble_ll_adv_ext_set_enable = 0x40000c10;
//r_ble_ll_adv_ext_set_param = 0x40000c14;
r_ble_ll_adv_ext_set_scan_rsp = 0x40000c18;
r_ble_ll_adv_final_chan = 0x40000c1c;
r_ble_ll_adv_first_chan = 0x40000c20;
r_ble_ll_adv_flags_clear = 0x40000c24;
r_ble_ll_adv_flags_set = 0x40000c28;
r_ble_ll_adv_get_chan_num = 0x40000c2c;
r_ble_ll_adv_get_local_rpa = 0x40000c30;
r_ble_ll_adv_get_peer_rpa = 0x40000c34;
r_ble_ll_adv_hci_set_random_addr = 0x40000c38;
r_ble_ll_adv_init = 0x40000c3c;
r_ble_ll_adv_legacy_pdu_make = 0x40000c40;
r_ble_ll_adv_next_chan = 0x40000c44;
r_ble_ll_adv_pdu_make = 0x40000c48;
r_ble_ll_adv_periodic_check_data_itvl = 0x40000c4c;
r_ble_ll_adv_periodic_enable = 0x40000c50;
r_ble_ll_adv_periodic_estimate_data_itvl = 0x40000c54;
r_ble_ll_adv_periodic_send_sync_ind = 0x40000c58;
r_ble_ll_adv_periodic_set_data = 0x40000c5c;
r_ble_ll_adv_periodic_set_info_transfer = 0x40000c60;
r_ble_ll_adv_periodic_set_param = 0x40000c64;
r_ble_ll_adv_pre_process = 0x40000c68;
r_ble_ll_adv_put_acad_chM_update_ind = 0x40000c6c;
r_ble_ll_adv_put_aux_ptr = 0x40000c70;
r_ble_ll_adv_put_syncinfo = 0x40000c74;
r_ble_ll_adv_rd_max_adv_data_len = 0x40000c78;
r_ble_ll_adv_rd_sup_adv_sets = 0x40000c7c;
r_ble_ll_adv_read_txpwr = 0x40000c80;
r_ble_ll_adv_remove = 0x40000c84;
r_ble_ll_adv_reset = 0x40000c88;
r_ble_ll_adv_rpa_timeout = 0x40000c8c;
r_ble_ll_adv_rpa_update = 0x40000c90;
r_ble_ll_adv_rx_pkt_in = 0x40000c94;
r_ble_ll_adv_scan_req_rxd = 0x40000c98;
r_ble_ll_adv_scan_rsp_legacy_pdu_make = 0x40000c9c;
r_ble_ll_adv_scan_rsp_pdu_make = 0x40000ca0;
r_ble_ll_adv_scheduled = 0x40000ca4;
r_ble_ll_adv_send_conn_comp_ev = 0x40000ca8;
//r_ble_ll_adv_set_adv_data = 0x40000cac;
//r_ble_ll_adv_set_adv_params = 0x40000cb0;
//r_ble_ll_adv_set_enable = 0x40000cb4;
r_ble_ll_adv_set_random_addr = 0x40000cb8;
//r_ble_ll_adv_set_scan_rsp_data = 0x40000cbc;
r_ble_ll_adv_set_sched = 0x40000cc0;
r_ble_ll_adv_sm_deinit = 0x40000cc4;
r_ble_ll_adv_sm_event_init = 0x40000cc8;
r_ble_ll_adv_sm_find_configured = 0x40000ccc;
r_ble_ll_adv_sm_get = 0x40000cd0;
r_ble_ll_adv_sm_init = 0x40000cd4;
r_ble_ll_adv_sm_reset = 0x40000cd8;
r_ble_ll_adv_sm_start = 0x40000cdc;
r_ble_ll_adv_sm_start_periodic = 0x40000ce0;
r_ble_ll_adv_sm_stop = 0x40000ce4;
r_ble_ll_adv_sm_stop_limit_reached = 0x40000ce8;
r_ble_ll_adv_sm_stop_periodic = 0x40000cec;
r_ble_ll_adv_sm_stop_timeout = 0x40000cf0;
r_ble_ll_adv_sync_calculate = 0x40000cf4;
r_ble_ll_adv_sync_get_pdu_len = 0x40000cf8;
r_ble_ll_adv_sync_pdu_make = 0x40000cfc;
r_ble_ll_adv_update_adv_scan_rsp_data = 0x40000d00;
r_ble_ll_adv_update_data_mbuf = 0x40000d04;
r_ble_ll_adv_update_did = 0x40000d08;
r_ble_ll_adv_update_periodic_data = 0x40000d0c;
r_ble_ll_arr_pool_init = 0x40000d10;
r_ble_ll_auth_pyld_tmo_event_send = 0x40000d14;
r_ble_ll_calc_offset_ticks_us_for_rampup = 0x40000d18;
r_ble_ll_calc_session_key = 0x40000d1c;
r_ble_ll_calc_ticks_per_slot = 0x40000d20;
//r_ble_ll_check_scan_params = 0x40000d24;
r_ble_ll_chk_txrx_octets = 0x40000d28;
r_ble_ll_chk_txrx_time = 0x40000d2c;
r_ble_ll_conn_adjust_pyld_len = 0x40000d30;
r_ble_ll_conn_auth_pyld_timer_cb = 0x40000d34;
r_ble_ll_conn_auth_pyld_timer_start = 0x40000d38;
r_ble_ll_conn_calc_dci = 0x40000d3c;
r_ble_ll_conn_calc_dci_csa1 = 0x40000d40;
r_ble_ll_conn_calc_itvl_ticks = 0x40000d44;
r_ble_ll_conn_chk_csm_flags = 0x40000d48;
r_ble_ll_conn_chk_phy_upd_start = 0x40000d4c;
r_ble_ll_conn_comp_event_send = 0x40000d50;
r_ble_ll_conn_connect_ind_pdu_make = 0x40000d54;
//r_ble_ll_conn_create = 0x40000d58;
r_ble_ll_conn_create_cancel = 0x40000d5c;
r_ble_ll_conn_created = 0x40000d60;
r_ble_ll_conn_cth_flow_enable = 0x40000d64;
r_ble_ll_conn_cth_flow_error_fn = 0x40000d68;
r_ble_ll_conn_cth_flow_have_credit = 0x40000d6c;
r_ble_ll_conn_cth_flow_is_enabled = 0x40000d70;
r_ble_ll_conn_cth_flow_process_cmd = 0x40000d74;
r_ble_ll_conn_cth_flow_set_buffers = 0x40000d78;
r_ble_ll_conn_enqueue_pkt = 0x40000d7c;
r_ble_ll_conn_env_init = 0x40000d80;
r_ble_ll_conn_ext_master_init = 0x40000d84;
r_ble_ll_conn_find_active_conn = 0x40000d88;
r_ble_ll_conn_get_active_conn = 0x40000d8c;
r_ble_ll_conn_get_anchor = 0x40000d90;
r_ble_ll_conn_hcc_params_set_fallback = 0x40000d94;
r_ble_ll_conn_hci_cancel_conn_complete_event = 0x40000d98;
r_ble_ll_conn_hci_chk_conn_params = 0x40000d9c;
r_ble_ll_conn_hci_chk_scan_params = 0x40000da0;
r_ble_ll_conn_hci_disconnect_cmd = 0x40000da4;
r_ble_ll_conn_hci_le_ltk_neg_reply = 0x40000da8;
r_ble_ll_conn_hci_le_ltk_reply = 0x40000dac;
r_ble_ll_conn_hci_le_rd_phy = 0x40000db0;
r_ble_ll_conn_hci_le_set_phy = 0x40000db4;
r_ble_ll_conn_hci_le_start_encrypt = 0x40000db8;
r_ble_ll_conn_hci_param_nrr = 0x40000dbc;
r_ble_ll_conn_hci_param_rr = 0x40000dc0;
r_ble_ll_conn_hci_rd_auth_pyld_tmo = 0x40000dc4;
r_ble_ll_conn_hci_rd_chan_map = 0x40000dc8;
r_ble_ll_conn_hci_rd_rem_ver_cmd = 0x40000dcc;
r_ble_ll_conn_hci_rd_rssi = 0x40000dd0;
r_ble_ll_conn_hci_read_rem_features = 0x40000dd4;
r_ble_ll_conn_hci_set_chan_class = 0x40000dd8;
r_ble_ll_conn_hci_set_data_len = 0x40000ddc;
r_ble_ll_conn_hci_update = 0x40000de0;
r_ble_ll_conn_hci_wr_auth_pyld_tmo = 0x40000de4;
r_ble_ll_conn_init_phy = 0x40000de8;
r_ble_ll_conn_is_dev_connected = 0x40000dec;
r_ble_ll_conn_is_empty_pdu = 0x40000df0;
r_ble_ll_conn_is_lru = 0x40000df4;
r_ble_ll_conn_master_init = 0x40000df8;
r_ble_ll_conn_module_deinit = 0x40000dfc;
r_ble_ll_conn_module_init = 0x40000e00;
r_ble_ll_conn_module_reset = 0x40000e04;
r_ble_ll_conn_next_event = 0x40000e08;
r_ble_ll_conn_num_comp_pkts_event_send = 0x40000e0c;
r_ble_ll_conn_prepare_tx_pdu = 0x40000e10;
r_ble_ll_conn_process_conn_params = 0x40000e14;
r_ble_ll_conn_req_peer_sca = 0x40000e18;
//r_ble_ll_conn_rx_data_pdu = 0x40000e1c;
r_ble_ll_conn_set_csa = 0x40000e20;
r_ble_ll_conn_set_ext_con_params = 0x40000e24;
r_ble_ll_conn_set_global_chanmap = 0x40000e28;
r_ble_ll_conn_set_phy = 0x40000e2c;
r_ble_ll_conn_set_txpwr_by_handle = 0x40000e30;
r_ble_ll_conn_set_unknown_rx_octets = 0x40000e34;
//r_ble_ll_conn_slave_start = 0x40000e38;
r_ble_ll_conn_sm_get = 0x40000e3c;
r_ble_ll_conn_sm_new = 0x40000e40;
r_ble_ll_conn_sm_npl_deinit = 0x40000e44;
r_ble_ll_conn_sm_npl_init = 0x40000e48;
r_ble_ll_conn_tx_pkt_in = 0x40000e4c;
r_ble_ll_conn_update_eff_data_len = 0x40000e50;
r_ble_ll_ctrl_chanmap_req_make = 0x40000e54;
r_ble_ll_ctrl_chk_proc_start = 0x40000e58;
r_ble_ll_ctrl_conn_param_pdu_make = 0x40000e5c;
r_ble_ll_ctrl_conn_param_pdu_proc = 0x40000e60;
r_ble_ll_ctrl_conn_param_reply = 0x40000e64;
r_ble_ll_ctrl_conn_upd_make = 0x40000e68;
r_ble_ll_ctrl_datalen_upd_make = 0x40000e6c;
r_ble_ll_ctrl_enc_allowed_pdu = 0x40000e70;
r_ble_ll_ctrl_enc_allowed_pdu_rx = 0x40000e74;
r_ble_ll_ctrl_enc_allowed_pdu_tx = 0x40000e78;
r_ble_ll_ctrl_enc_req_make = 0x40000e7c;
r_ble_ll_ctrl_find_new_phy = 0x40000e80;
r_ble_ll_ctrl_initiate_dle = 0x40000e84;
r_ble_ll_ctrl_len_proc = 0x40000e88;
r_ble_ll_ctrl_min_used_chan_rsp = 0x40000e8c;
r_ble_ll_ctrl_phy_from_phy_mask = 0x40000e90;
r_ble_ll_ctrl_phy_req_rsp_make = 0x40000e94;
r_ble_ll_ctrl_phy_tx_transition_get = 0x40000e98;
r_ble_ll_ctrl_phy_update_cancel = 0x40000e9c;
r_ble_ll_ctrl_phy_update_ind_make = 0x40000ea0;
r_ble_ll_ctrl_phy_update_proc_complete = 0x40000ea4;
r_ble_ll_ctrl_proc_init = 0x40000ea8;
r_ble_ll_ctrl_proc_rsp_timer_cb = 0x40000eac;
r_ble_ll_ctrl_proc_start = 0x40000eb0;
r_ble_ll_ctrl_proc_stop = 0x40000eb4;
r_ble_ll_ctrl_proc_unk_rsp = 0x40000eb8;
r_ble_ll_ctrl_proc_with_instant_initiated = 0x40000ebc;
r_ble_ll_ctrl_rej_ext_ind_make = 0x40000ec0;
r_ble_ll_ctrl_reject_ind_send = 0x40000ec4;
r_ble_ll_ctrl_rx_chanmap_req = 0x40000ec8;
r_ble_ll_ctrl_rx_conn_param_req = 0x40000ecc;
//r_ble_ll_ctrl_rx_conn_param_rsp = 0x40000ed0;
//r_ble_ll_ctrl_rx_conn_update = 0x40000ed4;
r_ble_ll_ctrl_rx_enc_req = 0x40000ed8;
r_ble_ll_ctrl_rx_enc_rsp = 0x40000edc;
r_ble_ll_ctrl_rx_feature_req = 0x40000ee0;
r_ble_ll_ctrl_rx_feature_rsp = 0x40000ee4;
r_ble_ll_ctrl_rx_pause_enc_req = 0x40000ee8;
r_ble_ll_ctrl_rx_pause_enc_rsp = 0x40000eec;
r_ble_ll_ctrl_rx_pdu = 0x40000ef0;
r_ble_ll_ctrl_rx_periodic_sync_ind = 0x40000ef4;
r_ble_ll_ctrl_rx_phy_req = 0x40000ef8;
r_ble_ll_ctrl_rx_phy_rsp = 0x40000efc;
r_ble_ll_ctrl_rx_phy_update_ind = 0x40000f00;
r_ble_ll_ctrl_rx_ping_rsp = 0x40000f04;
r_ble_ll_ctrl_rx_reject_ind = 0x40000f08;
r_ble_ll_ctrl_rx_sca_req = 0x40000f0c;
r_ble_ll_ctrl_rx_sca_rsp = 0x40000f10;
r_ble_ll_ctrl_rx_start_enc_req = 0x40000f14;
r_ble_ll_ctrl_rx_start_enc_rsp = 0x40000f18;
r_ble_ll_ctrl_rx_version_ind = 0x40000f1c;
r_ble_ll_ctrl_sca_req_rsp_make = 0x40000f20;
r_ble_ll_ctrl_start_enc_send = 0x40000f24;
r_ble_ll_ctrl_start_rsp_timer = 0x40000f28;
r_ble_ll_ctrl_terminate_start = 0x40000f2c;
r_ble_ll_ctrl_tx_done = 0x40000f30;
r_ble_ll_ctrl_update_features = 0x40000f34;
r_ble_ll_ctrl_version_ind_make = 0x40000f38;
r_ble_ll_data_buffer_overflow = 0x40000f3c;
r_ble_ll_deinit = 0x40000f40;
r_ble_ll_disconn_comp_event_send = 0x40000f44;
//r_ble_ll_env_init = 0x40000f48;
r_ble_ll_event_comp_pkts = 0x40000f4c;
r_ble_ll_event_dbuf_overflow = 0x40000f50;
r_ble_ll_event_send = 0x40000f54;
r_ble_ll_event_tx_pkt = 0x40000f58;
r_ble_ll_ext_adv_phy_mode_to_local_phy = 0x40000f5c;
//r_ble_ll_ext_conn_create = 0x40000f60;
r_ble_ll_ext_scan_parse_adv_info = 0x40000f64;
r_ble_ll_ext_scan_parse_aux_ptr = 0x40000f68;
r_ble_ll_flush_pkt_queue = 0x40000f6c;
r_ble_ll_generate_dh_key_v1 = 0x40000f70;
r_ble_ll_generate_dh_key_v2 = 0x40000f74;
r_ble_ll_generic_data_init = 0x40000f78;
r_ble_ll_get_addr_type = 0x40000f7c;
r_ble_ll_get_chan_to_scan = 0x40000f80;
r_ble_ll_get_our_devaddr = 0x40000f84;
r_ble_ll_get_tx_pwr_compensation = 0x40000f88;
r_ble_ll_hci_acl_rx = 0x40000f8c;
r_ble_ll_hci_adv_mode_ext = 0x40000f90;
r_ble_ll_hci_adv_set_enable = 0x40000f94;
r_ble_ll_hci_cb_host_buf_size = 0x40000f98;
r_ble_ll_hci_cb_set_ctrlr_to_host_fc = 0x40000f9c;
r_ble_ll_hci_cb_set_event_mask = 0x40000fa0;
r_ble_ll_hci_cb_set_event_mask2 = 0x40000fa4;
r_ble_ll_hci_chk_phy_masks = 0x40000fa8;
r_ble_ll_hci_cmd_proc = 0x40000fac;
r_ble_ll_hci_cmd_rx = 0x40000fb0;
r_ble_ll_hci_ctlr_bb_cmd_proc = 0x40000fb4;
r_ble_ll_hci_deinit = 0x40000fb8;
r_ble_ll_hci_disconnect = 0x40000fbc;
r_ble_ll_hci_env_init = 0x40000fc0;
r_ble_ll_hci_ev_conn_update = 0x40000fc4;
r_ble_ll_hci_ev_databuf_overflow = 0x40000fc8;
r_ble_ll_hci_ev_datalen_chg = 0x40000fcc;
r_ble_ll_hci_ev_encrypt_chg = 0x40000fd0;
r_ble_ll_hci_ev_hw_err = 0x40000fd4;
r_ble_ll_hci_ev_le_csa = 0x40000fd8;
r_ble_ll_hci_ev_ltk_req = 0x40000fdc;
r_ble_ll_hci_ev_phy_update = 0x40000fe0;
r_ble_ll_hci_ev_rd_rem_used_feat = 0x40000fe4;
r_ble_ll_hci_ev_rd_rem_ver = 0x40000fe8;
r_ble_ll_hci_ev_rem_conn_parm_req = 0x40000fec;
r_ble_ll_hci_ev_sca_update = 0x40000ff0;
r_ble_ll_hci_ev_send_adv_set_terminated = 0x40000ff4;
r_ble_ll_hci_ev_send_scan_req_recv = 0x40000ff8;
r_ble_ll_hci_ev_send_scan_timeout = 0x40000ffc;
r_ble_ll_hci_ev_send_vendor_err = 0x40001000;
//r_ble_ll_hci_event_send = 0x40001004;
r_ble_ll_hci_ext_scan_set_enable = 0x40001008;
r_ble_ll_hci_get_num_cmd_pkts = 0x4000100c;
r_ble_ll_hci_info_params_cmd_proc = 0x40001010;
r_ble_ll_hci_init = 0x40001014;
r_ble_ll_hci_init_support_cmd_base_on_lmp_ver = 0x40001018;
r_ble_ll_hci_is_event_enabled = 0x4000101c;
r_ble_ll_hci_is_le_event_enabled = 0x40001020;
r_ble_ll_hci_le_cmd_proc = 0x40001024;
r_ble_ll_hci_le_cmd_send_cmd_status = 0x40001028;
r_ble_ll_hci_le_encrypt = 0x4000102c;
r_ble_ll_hci_le_rand = 0x40001030;
r_ble_ll_hci_le_rd_max_data_len = 0x40001034;
r_ble_ll_hci_le_rd_sugg_data_len = 0x40001038;
r_ble_ll_hci_le_read_bufsize = 0x4000103c;
r_ble_ll_hci_le_read_local_features = 0x40001040;
r_ble_ll_hci_le_read_supp_states = 0x40001044;
r_ble_ll_hci_le_set_def_phy = 0x40001048;
r_ble_ll_hci_le_wr_sugg_data_len = 0x4000104c;
r_ble_ll_hci_link_ctrl_cmd_proc = 0x40001050;
r_ble_ll_hci_npl_init = 0x40001054;
r_ble_ll_hci_post_gen_dhkey_cmp_evt = 0x40001058;
r_ble_ll_hci_post_rd_p256_pubkey_cmp_evt = 0x4000105c;
r_ble_ll_hci_rd_bd_addr = 0x40001060;
r_ble_ll_hci_rd_local_supp_cmd = 0x40001064;
r_ble_ll_hci_rd_local_supp_feat = 0x40001068;
r_ble_ll_hci_rd_local_version = 0x4000106c;
r_ble_ll_hci_scan_set_enable = 0x40001070;
r_ble_ll_hci_send_adv_report = 0x40001074;
r_ble_ll_hci_send_dir_adv_report = 0x40001078;
//r_ble_ll_hci_send_ext_adv_report = 0x4000107c;
//r_ble_ll_hci_send_legacy_ext_adv_report = 0x40001080;
r_ble_ll_hci_send_noop = 0x40001084;
r_ble_ll_hci_set_adv_data = 0x40001088;
r_ble_ll_hci_set_le_event_mask = 0x4000108c;
r_ble_ll_hci_set_scan_rsp_data = 0x40001090;
r_ble_ll_hci_status_params_cmd_proc = 0x40001094;
//r_ble_ll_hci_vs_cmd_proc = 0x40001098;
r_ble_ll_hci_vs_rd_static_addr = 0x4000109c;
r_ble_ll_hw_err_timer_cb = 0x400010a0;
r_ble_ll_hw_error = 0x400010a4;
//r_ble_ll_init = 0x400010a8;
r_ble_ll_init_alloc_conn_comp_ev = 0x400010ac;
r_ble_ll_init_get_conn_comp_ev = 0x400010b0;
r_ble_ll_init_rx_pkt_in = 0x400010b4;
r_ble_ll_is_addr_empty = 0x400010b8;
r_ble_ll_is_controller_busy = 0x400010bc;
r_ble_ll_is_on_resolv_list = 0x400010c0;
r_ble_ll_is_our_devaddr = 0x400010c4;
r_ble_ll_is_rpa = 0x400010c8;
r_ble_ll_is_valid_adv_mode = 0x400010cc;
r_ble_ll_is_valid_own_addr_type = 0x400010d0;
r_ble_ll_is_valid_public_addr = 0x400010d4;
r_ble_ll_is_valid_random_addr = 0x400010d8;
r_ble_ll_mbuf_init = 0x400010dc;
r_ble_ll_misc_options_set = 0x400010e0;
r_ble_ll_modify_sca = 0x400010e4;
r_ble_ll_modify_sca_action = 0x400010e8;
r_ble_ll_pdu_max_tx_octets_get = 0x400010ec;
r_ble_ll_pdu_tx_time_get = 0x400010f0;
r_ble_ll_phy_to_phy_mode = 0x400010f4;
r_ble_ll_qa_enable = 0x400010f8;
r_ble_ll_rand = 0x400010fc;
r_ble_ll_rand_data_get = 0x40001100;
r_ble_ll_rand_deinit = 0x40001104;
r_ble_ll_rand_env_init = 0x40001108;
r_ble_ll_rand_init = 0x4000110c;
r_ble_ll_rand_prand_get = 0x40001110;
r_ble_ll_rand_sample = 0x40001114;
r_ble_ll_rand_start = 0x40001118;
r_ble_ll_read_local_p256_pub_key = 0x4000111c;
r_ble_ll_read_rf_path_compensation = 0x40001120;
r_ble_ll_read_supp_features = 0x40001124;
r_ble_ll_read_supp_states = 0x40001128;
r_ble_ll_read_tx_power = 0x4000112c;
r_ble_ll_reset = 0x40001130;
r_ble_ll_resolv_clear_all_pl_bit = 0x40001134;
r_ble_ll_resolv_clear_all_wl_bit = 0x40001138;
r_ble_ll_resolv_deinit = 0x4000113c;
r_ble_ll_resolv_enable_cmd = 0x40001140;
r_ble_ll_resolv_enabled = 0x40001144;
r_ble_ll_resolv_env_init = 0x40001148;
r_ble_ll_resolv_gen_priv_addr = 0x4000114c;
r_ble_ll_resolv_gen_rpa = 0x40001150;
r_ble_ll_resolv_get_addr_pointer = 0x40001154;
r_ble_ll_resolv_get_index = 0x40001158;
r_ble_ll_resolv_get_irk_pointer = 0x4000115c;
r_ble_ll_resolv_get_list = 0x40001160;
r_ble_ll_resolv_get_priv_addr = 0x40001164;
r_ble_ll_resolv_get_rpa_tmo = 0x40001168;
r_ble_ll_resolv_init = 0x4000116c;
r_ble_ll_resolv_irk_nonzero = 0x40001170;
r_ble_ll_resolv_list_add = 0x40001174;
r_ble_ll_resolv_list_chg_allowed = 0x40001178;
r_ble_ll_resolv_list_clr = 0x4000117c;
r_ble_ll_resolv_list_find = 0x40001180;
r_ble_ll_resolv_list_read_size = 0x40001184;
r_ble_ll_resolv_list_reset = 0x40001188;
r_ble_ll_resolv_list_rmv = 0x4000118c;
r_ble_ll_resolv_local_addr_rd = 0x40001190;
r_ble_ll_resolv_peer_addr_rd = 0x40001194;
r_ble_ll_resolv_peer_rpa_any = 0x40001198;
r_ble_ll_resolv_reset = 0x4000119c;
r_ble_ll_resolv_rpa = 0x400011a0;
r_ble_ll_resolv_rpa_timer_cb = 0x400011a4;
r_ble_ll_resolv_set_local_rpa = 0x400011a8;
r_ble_ll_resolv_set_peer_rpa = 0x400011ac;
r_ble_ll_resolv_set_rpa_tmo = 0x400011b0;
r_ble_ll_resolve_set_priv_mode = 0x400011b4;
r_ble_ll_rxpdu_alloc = 0x400011b8;
r_ble_ll_scan_add_scan_rsp_adv = 0x400011bc;
r_ble_ll_scan_adv_decode_addr = 0x400011c0;
r_ble_ll_scan_aux_data_ref = 0x400011c4;
r_ble_ll_scan_aux_data_unref = 0x400011c8;
r_ble_ll_scan_can_chg_whitelist = 0x400011cc;
r_ble_ll_scan_check_periodic_sync = 0x400011d0;
r_ble_ll_scan_classify_filter_aux_init = 0x400011d4;
r_ble_ll_scan_classify_filter_init = 0x400011d8;
//r_ble_ll_scan_common_init = 0x400011dc;
r_ble_ll_scan_continue_en = 0x400011e0;
r_ble_ll_scan_deinit = 0x400011e4;
r_ble_ll_scan_dup_check_ext = 0x400011e8;
r_ble_ll_scan_dup_check_legacy = 0x400011ec;
r_ble_ll_scan_dup_move_to_head = 0x400011f0;
r_ble_ll_scan_dup_new = 0x400011f4;
r_ble_ll_scan_dup_update_ext = 0x400011f8;
//r_ble_ll_scan_dup_update_legacy = 0x400011fc;
r_ble_ll_scan_enabled = 0x40001200;
r_ble_ll_scan_end_adv_evt = 0x40001204;
//r_ble_ll_scan_env_init = 0x40001208;
r_ble_ll_scan_ext_initiator_start = 0x4000120c;
r_ble_ll_scan_get_addr_data_from_legacy = 0x40001210;
r_ble_ll_scan_get_addr_from_ext_adv = 0x40001214;
r_ble_ll_scan_get_cur_sm = 0x40001218;
r_ble_ll_scan_get_ext_adv_report = 0x4000121c;
r_ble_ll_scan_get_local_rpa = 0x40001220;
r_ble_ll_scan_get_next_adv_prim_chan = 0x40001224;
r_ble_ll_scan_get_peer_rpa = 0x40001228;
r_ble_ll_scan_have_rxd_scan_rsp = 0x4000122c;
r_ble_ll_scan_init = 0x40001230;
r_ble_ll_scan_initiator_start = 0x40001234;
r_ble_ll_scan_is_inside_window = 0x40001238;
r_ble_ll_scan_move_window_to = 0x4000123c;
r_ble_ll_scan_npl_reset = 0x40001240;
r_ble_ll_scan_parse_auxptr = 0x40001244;
r_ble_ll_scan_parse_ext_hdr = 0x40001248;
r_ble_ll_scan_pre_process = 0x4000124c;
r_ble_ll_scan_record_new_adv = 0x40001250;
r_ble_ll_scan_refresh_nrpa = 0x40001254;
r_ble_ll_scan_reset = 0x40001258;
r_ble_ll_scan_rx_pkt_in = 0x4000125c;
//r_ble_ll_scan_rx_pkt_in_on_aux = 0x40001260;
r_ble_ll_scan_rx_pkt_in_on_legacy = 0x40001264;
r_ble_ll_scan_rx_pkt_in_restore_addr_data = 0x40001268;
r_ble_ll_scan_rxed = 0x4000126c;
//r_ble_ll_scan_send_adv_report = 0x40001270;
r_ble_ll_scan_send_truncated = 0x40001274;
r_ble_ll_scan_set_enable = 0x40001278;
r_ble_ll_scan_set_peer_rpa = 0x4000127c;
r_ble_ll_scan_set_perfer_addr = 0x40001280;
r_ble_ll_scan_set_scan_params = 0x40001284;
r_ble_ll_scan_sm_start = 0x40001288;
r_ble_ll_scan_sm_stop = 0x4000128c;
r_ble_ll_scan_time_hci_to_ticks = 0x40001290;
r_ble_ll_scan_update_aux_data = 0x40001294;
r_ble_ll_scan_whitelist_enabled = 0x40001298;
r_ble_ll_set_default_privacy_mode = 0x4000129c;
r_ble_ll_set_default_sync_transfer_params = 0x400012a0;
r_ble_ll_set_ext_scan_params = 0x400012a4;
r_ble_ll_set_host_feat = 0x400012a8;
r_ble_ll_set_public_addr = 0x400012ac;
r_ble_ll_set_random_addr = 0x400012b0;
r_ble_ll_set_sync_transfer_params = 0x400012b4;
r_ble_ll_state_get = 0x400012b8;
r_ble_ll_state_set = 0x400012bc;
r_ble_ll_sync_adjust_ext_hdr = 0x400012c0;
r_ble_ll_sync_cancel = 0x400012c4;
r_ble_ll_sync_cancel_complete_event = 0x400012c8;
r_ble_ll_sync_check_acad = 0x400012cc;
r_ble_ll_sync_check_failed = 0x400012d0;
r_ble_ll_sync_create = 0x400012d4;
r_ble_ll_sync_deinit = 0x400012d8;
r_ble_ll_sync_enabled = 0x400012dc;
r_ble_ll_sync_env_init = 0x400012e0;
r_ble_ll_sync_est_event_failed = 0x400012e4;
r_ble_ll_sync_est_event_success = 0x400012e8;
r_ble_ll_sync_established = 0x400012ec;
r_ble_ll_sync_filter_enabled = 0x400012f0;
r_ble_ll_sync_find = 0x400012f4;
r_ble_ll_sync_get_cur_sm = 0x400012f8;
r_ble_ll_sync_get_handle = 0x400012fc;
r_ble_ll_sync_get_sm = 0x40001300;
r_ble_ll_sync_info_event = 0x40001304;
r_ble_ll_sync_init = 0x40001308;
r_ble_ll_sync_list_add = 0x4000130c;
r_ble_ll_sync_list_clear = 0x40001310;
r_ble_ll_sync_list_empty = 0x40001314;
r_ble_ll_sync_list_get_free = 0x40001318;
r_ble_ll_sync_list_remove = 0x4000131c;
r_ble_ll_sync_list_search = 0x40001320;
r_ble_ll_sync_list_size = 0x40001324;
r_ble_ll_sync_lost_event = 0x40001328;
r_ble_ll_sync_next_event = 0x4000132c;
r_ble_ll_sync_on_list = 0x40001330;
//r_ble_ll_sync_parse_ext_hdr = 0x40001334;
r_ble_ll_sync_periodic_ind = 0x40001338;
r_ble_ll_sync_phy_mode_to_aux_phy = 0x4000133c;
r_ble_ll_sync_phy_mode_to_hci = 0x40001340;
r_ble_ll_sync_put_syncinfo = 0x40001344;
r_ble_ll_sync_receive_enable = 0x40001348;
r_ble_ll_sync_reserve = 0x4000134c;
r_ble_ll_sync_reset = 0x40001350;
r_ble_ll_sync_reset_sm = 0x40001354;
r_ble_ll_sync_rx_pkt_in = 0x40001358;
r_ble_ll_sync_send_per_adv_rpt = 0x4000135c;
r_ble_ll_sync_send_sync_ind = 0x40001360;
r_ble_ll_sync_send_truncated_per_adv_rpt = 0x40001364;
r_ble_ll_sync_sm_clear = 0x40001368;
r_ble_ll_sync_terminate = 0x4000136c;
r_ble_ll_sync_transfer = 0x40001370;
r_ble_ll_sync_transfer_get = 0x40001374;
r_ble_ll_sync_transfer_received = 0x40001378;
r_ble_ll_task = 0x4000137c;
r_ble_ll_trace_set_func = 0x40001380;
r_ble_ll_trace_u32 = 0x40001384;
r_ble_ll_trace_u32x2 = 0x40001388;
r_ble_ll_trace_u32x3 = 0x4000138c;
r_ble_ll_tx_flat_mbuf_pducb = 0x40001390;
r_ble_ll_tx_mbuf_pducb = 0x40001394;
r_ble_ll_tx_pkt_in = 0x40001398;
r_ble_ll_update_max_tx_octets_phy_mode = 0x4000139c;
r_ble_ll_usecs_to_ticks_round_up = 0x400013a0;
r_ble_ll_utils_calc_access_addr = 0x400013a4;
r_ble_ll_utils_calc_dci_csa2 = 0x400013a8;
r_ble_ll_utils_calc_num_used_chans = 0x400013ac;
r_ble_ll_utils_calc_window_widening = 0x400013b0;
r_ble_ll_utils_csa2_perm = 0x400013b4;
r_ble_ll_utils_csa2_prng = 0x400013b8;
r_ble_ll_utils_remapped_channel = 0x400013bc;
r_ble_ll_whitelist_add = 0x400013c0;
r_ble_ll_whitelist_chg_allowed = 0x400013c4;
r_ble_ll_whitelist_clear = 0x400013c8;
r_ble_ll_whitelist_read_size = 0x400013cc;
r_ble_ll_whitelist_rmv = 0x400013d0;
r_ble_ll_whitelist_search = 0x400013d4;
r_ble_ll_write_rf_path_compensation = 0x400013d8;
r_ble_lll_adv_aux_scannable_pdu_payload_len = 0x400013dc;
r_ble_lll_adv_aux_schedule = 0x400013e0;
r_ble_lll_adv_aux_schedule_first = 0x400013e4;
r_ble_lll_adv_aux_schedule_next = 0x400013e8;
r_ble_lll_adv_aux_scheduled = 0x400013ec;
r_ble_lll_adv_aux_set_start_time = 0x400013f0;
r_ble_lll_adv_coex_dpc_calc_pti_update_itvl = 0x400013f4;
r_ble_lll_adv_coex_dpc_process_pri = 0x400013f8;
r_ble_lll_adv_coex_dpc_process_sec = 0x400013fc;
r_ble_lll_adv_coex_dpc_pti_get = 0x40001400;
r_ble_lll_adv_coex_dpc_update = 0x40001404;
r_ble_lll_adv_coex_dpc_update_on_adv_start = 0x40001408;
r_ble_lll_adv_coex_dpc_update_on_aux_scheduled = 0x4000140c;
r_ble_lll_adv_coex_dpc_update_on_data_updated = 0x40001410;
r_ble_lll_adv_coex_dpc_update_on_event_end = 0x40001414;
r_ble_lll_adv_coex_dpc_update_on_event_scheduled = 0x40001418;
r_ble_lll_adv_done = 0x4000141c;
r_ble_lll_adv_drop_event = 0x40001420;
r_ble_lll_adv_event_done = 0x40001424;
r_ble_lll_adv_event_rmvd_from_sched = 0x40001428;
r_ble_lll_adv_ext_estimate_data_itvl = 0x4000142c;
r_ble_lll_adv_get_sec_pdu_len = 0x40001430;
r_ble_lll_adv_halt = 0x40001434;
r_ble_lll_adv_make_done = 0x40001438;
r_ble_lll_adv_periodic_done = 0x4000143c;
r_ble_lll_adv_periodic_event_done = 0x40001440;
r_ble_lll_adv_periodic_rmvd_from_sched = 0x40001444;
r_ble_lll_adv_periodic_schedule_first = 0x40001448;
r_ble_lll_adv_periodic_schedule_next = 0x4000144c;
r_ble_lll_adv_periodic_start = 0x40001450;
r_ble_lll_adv_periodic_stop = 0x40001454;
r_ble_lll_adv_pri_schedule_tx_pdu = 0x40001458;
r_ble_lll_adv_reschedule_event = 0x4000145c;
r_ble_lll_adv_reschedule_periodic_event = 0x40001460;
r_ble_lll_adv_rx_pkt_isr = 0x40001464;
r_ble_lll_adv_sec_done = 0x40001468;
r_ble_lll_adv_sec_event_done = 0x4000146c;
r_ble_lll_adv_sec_schedule_next_aux = 0x40001470;
r_ble_lll_adv_secondary_tx_start_cb = 0x40001474;
r_ble_lll_adv_sm_deinit = 0x40001478;
r_ble_lll_adv_sm_event_init = 0x4000147c;
r_ble_lll_adv_sm_event_restore = 0x40001480;
r_ble_lll_adv_sm_event_store = 0x40001484;
r_ble_lll_adv_sm_init = 0x40001488;
r_ble_lll_adv_sm_reset = 0x4000148c;
r_ble_lll_adv_start = 0x40001490;
r_ble_lll_adv_stop = 0x40001494;
r_ble_lll_adv_sync_next_scheduled = 0x40001498;
r_ble_lll_adv_sync_schedule = 0x4000149c;
r_ble_lll_adv_sync_tx_done = 0x400014a0;
r_ble_lll_adv_sync_tx_end = 0x400014a4;
r_ble_lll_adv_sync_tx_start_cb = 0x400014a8;
r_ble_lll_adv_tx_done = 0x400014ac;
r_ble_lll_adv_tx_start_cb = 0x400014b0;
r_ble_lll_adv_update_rsp_offset = 0x400014b4;
r_ble_lll_aux_scan_cb = 0x400014b8;
r_ble_lll_aux_scan_drop = 0x400014bc;
r_ble_lll_aux_scan_drop_event_cb = 0x400014c0;
r_ble_lll_calc_us_convert_tick_unit = 0x400014c4;
r_ble_lll_conn_append_tx_buffer = 0x400014c8;
r_ble_lll_conn_can_send_next_pdu = 0x400014cc;
r_ble_lll_conn_check_opcode_matched = 0x400014d0;
r_ble_lll_conn_coex_dpc_process = 0x400014d4;
r_ble_lll_conn_coex_dpc_pti_get = 0x400014d8;
r_ble_lll_conn_coex_dpc_update = 0x400014dc;
r_ble_lll_conn_coex_dpc_update_on_event_end = 0x400014e0;
r_ble_lll_conn_coex_dpc_update_on_event_scheduled = 0x400014e4;
r_ble_lll_conn_coex_dpc_update_on_event_started = 0x400014e8;
r_ble_lll_conn_cth_flow_alloc_credit = 0x400014ec;
r_ble_lll_conn_cth_flow_free_credit = 0x400014f0;
r_ble_lll_conn_current_sm_over = 0x400014f4;
r_ble_lll_conn_end = 0x400014f8;
r_ble_lll_conn_env_deinit = 0x400014fc;
r_ble_lll_conn_env_init = 0x40001500;
r_ble_lll_conn_event_end = 0x40001504;
r_ble_lll_conn_event_end_timer_cb = 0x40001508;
r_ble_lll_conn_event_halt = 0x4000150c;
r_ble_lll_conn_event_is_over = 0x40001510;
r_ble_lll_conn_event_start_cb = 0x40001514;
r_ble_lll_conn_free_rx_mbuf = 0x40001518;
r_ble_lll_conn_get_addr_info_from_rx_buf = 0x4000151c;
r_ble_lll_conn_get_ce_end_time = 0x40001520;
r_ble_lll_conn_get_next_sched_time = 0x40001524;
r_ble_lll_conn_get_rx_mbuf = 0x40001528;
r_ble_lll_conn_halt = 0x4000152c;
r_ble_lll_conn_master_common_init = 0x40001530;
r_ble_lll_conn_master_new = 0x40001534;
r_ble_lll_conn_module_deinit = 0x40001538;
r_ble_lll_conn_module_init = 0x4000153c;
r_ble_lll_conn_module_reset = 0x40001540;
r_ble_lll_conn_no_mem_evt_pre_cb = 0x40001544;
r_ble_lll_conn_pre_process = 0x40001548;
r_ble_lll_conn_process_acked_pdu = 0x4000154c;
r_ble_lll_conn_process_in_isr = 0x40001550;
r_ble_lll_conn_recv_ack = 0x40001554;
r_ble_lll_conn_recv_valid_packet = 0x40001558;
r_ble_lll_conn_reset_pending_sched = 0x4000155c;
//r_ble_lll_conn_rx_pkt_isr = 0x40001560;
r_ble_lll_conn_sched_next_anchor = 0x40001564;
r_ble_lll_conn_sched_next_event = 0x40001568;
r_ble_lll_conn_set_slave_flow_control = 0x4000156c;
r_ble_lll_conn_slave_new = 0x40001570;
r_ble_lll_conn_sm_new = 0x40001574;
r_ble_lll_conn_sm_npl_deinit = 0x40001578;
r_ble_lll_conn_sm_npl_init = 0x4000157c;
r_ble_lll_conn_superversion_timer_cb = 0x40001580;
r_ble_lll_conn_timeout = 0x40001584;
r_ble_lll_conn_update_anchor = 0x40001588;
r_ble_lll_conn_update_conn_ind_params = 0x4000158c;
r_ble_lll_conn_update_encryption = 0x40001590;
r_ble_lll_conn_update_tx_buffer = 0x40001594;
r_ble_lll_deinit = 0x40001598;
r_ble_lll_dtm_calculate_itvl = 0x4000159c;
r_ble_lll_dtm_ctx_free = 0x400015a0;
r_ble_lll_dtm_deinit = 0x400015a4;
r_ble_lll_dtm_end_test = 0x400015a8;
r_ble_lll_dtm_ev_rx_restart_cb = 0x400015ac;
r_ble_lll_dtm_ev_tx_resched_cb = 0x400015b0;
r_ble_lll_dtm_init = 0x400015b4;
r_ble_lll_dtm_reset = 0x400015b8;
r_ble_lll_dtm_rx_create_ctx = 0x400015bc;
r_ble_lll_dtm_rx_isr_end = 0x400015c0;
r_ble_lll_dtm_rx_isr_start = 0x400015c4;
r_ble_lll_dtm_rx_pkt_in = 0x400015c8;
r_ble_lll_dtm_rx_sched_cb = 0x400015cc;
r_ble_lll_dtm_rx_start = 0x400015d0;
r_ble_lll_dtm_rx_test = 0x400015d4;
r_ble_lll_dtm_set_next = 0x400015d8;
r_ble_lll_dtm_tx_create_ctx = 0x400015dc;
r_ble_lll_dtm_tx_done = 0x400015e0;
r_ble_lll_dtm_tx_sched_cb = 0x400015e4;
r_ble_lll_dtm_tx_test = 0x400015e8;
r_ble_lll_dtm_wfr_timer_exp = 0x400015ec;
r_ble_lll_event_rx_pkt = 0x400015f0;
r_ble_lll_ext_scan_coex_dpc_process = 0x400015f4;
r_ble_lll_ext_scan_coex_dpc_pti_get = 0x400015f8;
r_ble_lll_ext_scan_coex_dpc_update = 0x400015fc;
r_ble_lll_ext_scan_coex_dpc_update_on_start = 0x40001600;
r_ble_lll_hci_dtm_rx_test = 0x40001604;
r_ble_lll_hci_dtm_rx_test_v2 = 0x40001608;
r_ble_lll_hci_dtm_tx_test = 0x4000160c;
r_ble_lll_hci_dtm_tx_test_ext = 0x40001610;
r_ble_lll_hci_dtm_tx_test_v2 = 0x40001614;
r_ble_lll_hci_dtm_tx_test_v2_ext = 0x40001618;
r_ble_lll_init = 0x4000161c;
r_ble_lll_init_pre_process = 0x40001620;
r_ble_lll_init_rx_pkt_isr = 0x40001624;
r_ble_lll_per_adv_coex_dpc_calc_pti_update_itvl = 0x40001628;
r_ble_lll_per_adv_coex_dpc_process = 0x4000162c;
r_ble_lll_per_adv_coex_dpc_pti_get = 0x40001630;
r_ble_lll_per_adv_coex_dpc_update = 0x40001634;
r_ble_lll_per_adv_coex_dpc_update_on_data_updated = 0x40001638;
r_ble_lll_per_adv_coex_dpc_update_on_scheduled = 0x4000163c;
r_ble_lll_per_adv_coex_dpc_update_on_start = 0x40001640;
r_ble_lll_reset = 0x40001644;
r_ble_lll_rfmgmt_controller_sleep_en = 0x40001648;
r_ble_lll_rfmgmt_deinit = 0x4000164c;
//r_ble_lll_rfmgmt_disable = 0x40001650;
//r_ble_lll_rfmgmt_enable = 0x40001654;
//r_ble_lll_rfmgmt_enable_now = 0x40001658;
r_ble_lll_rfmgmt_init = 0x4000165c;
r_ble_lll_rfmgmt_is_enabled = 0x40001660;
r_ble_lll_rfmgmt_release = 0x40001664;
r_ble_lll_rfmgmt_release_ev = 0x40001668;
r_ble_lll_rfmgmt_reset = 0x4000166c;
r_ble_lll_rfmgmt_scan_changed = 0x40001670;
r_ble_lll_rfmgmt_sched_changed = 0x40001674;
r_ble_lll_rfmgmt_set_sleep_cb = 0x40001678;
r_ble_lll_rfmgmt_ticks_to_enabled = 0x4000167c;
//r_ble_lll_rfmgmt_timer_exp = 0x40001680;
//r_ble_lll_rfmgmt_timer_reschedule = 0x40001684;
r_ble_lll_rx_pdu_in = 0x40001688;
r_ble_lll_rx_pkt_in = 0x4000168c;
r_ble_lll_rx_pkt_isr = 0x40001690;
r_ble_lll_scan_abort_aux_sched = 0x40001694;
r_ble_lll_scan_aux_data_free = 0x40001698;
r_ble_lll_scan_chk_resume = 0x4000169c;
r_ble_lll_scan_clean_cur_aux_data = 0x400016a0;
//r_ble_lll_scan_coex_event_cb = 0x400016a4;
r_ble_lll_scan_common_init = 0x400016a8;
r_ble_lll_scan_deinit = 0x400016ac;
r_ble_lll_scan_duration_period_timers_restart = 0x400016b0;
r_ble_lll_scan_duration_period_timers_stop = 0x400016b4;
r_ble_lll_scan_duration_timer_cb = 0x400016b8;
r_ble_lll_scan_event_proc = 0x400016bc;
r_ble_lll_scan_ext_adv_init = 0x400016c0;
r_ble_lll_scan_halt = 0x400016c4;
r_ble_lll_scan_has_sent_scan_req = 0x400016c8;
r_ble_lll_scan_init = 0x400016cc;
r_ble_lll_scan_npl_init = 0x400016d0;
r_ble_lll_scan_npl_reset = 0x400016d4;
r_ble_lll_scan_npl_restore = 0x400016d8;
r_ble_lll_scan_npl_store = 0x400016dc;
r_ble_lll_scan_period_timer_cb = 0x400016e0;
r_ble_lll_scan_process_adv_in_isr = 0x400016e4;
r_ble_lll_scan_process_rsp_in_isr = 0x400016e8;
r_ble_lll_scan_req_backoff = 0x400016ec;
r_ble_lll_scan_restart = 0x400016f0;
r_ble_lll_scan_rx_isr_on_aux = 0x400016f4;
r_ble_lll_scan_rx_isr_on_legacy = 0x400016f8;
r_ble_lll_scan_rx_pkt_isr = 0x400016fc;
r_ble_lll_scan_sched_next_aux = 0x40001700;
r_ble_lll_scan_sched_remove = 0x40001704;
r_ble_lll_scan_start = 0x40001708;
//r_ble_lll_scan_start_rx = 0x4000170c;
r_ble_lll_scan_stop = 0x40001710;
r_ble_lll_scan_targeta_is_matched = 0x40001714;
r_ble_lll_scan_timer_cb = 0x40001718;
r_ble_lll_sched_adv_new = 0x4000171c;
r_ble_lll_sched_adv_resched_pdu = 0x40001720;
r_ble_lll_sched_adv_reschedule = 0x40001724;
r_ble_lll_sched_aux_scan = 0x40001728;
r_ble_lll_sched_conn_overlap = 0x4000172c;
r_ble_lll_sched_conn_reschedule = 0x40001730;
r_ble_lll_sched_deinit = 0x40001734;
r_ble_lll_sched_dtm = 0x40001738;
r_ble_lll_sched_env_init = 0x4000173c;
r_ble_lll_sched_execute_check = 0x40001740;
r_ble_lll_sched_execute_item = 0x40001744;
r_ble_lll_sched_init = 0x40001748;
r_ble_lll_sched_insert_if_empty = 0x4000174c;
r_ble_lll_sched_is_overlap = 0x40001750;
r_ble_lll_sched_master_new = 0x40001754;
r_ble_lll_sched_next_time = 0x40001758;
r_ble_lll_sched_overlaps_current = 0x4000175c;
r_ble_lll_sched_periodic_adv = 0x40001760;
r_ble_lll_sched_rmv_elem = 0x40001764;
r_ble_lll_sched_rmv_elem_type = 0x40001768;
r_ble_lll_sched_run = 0x4000176c;
r_ble_lll_sched_scan_req_over_aux_ptr = 0x40001770;
r_ble_lll_sched_slave_new = 0x40001774;
r_ble_lll_sched_stop = 0x40001778;
r_ble_lll_sched_sync = 0x4000177c;
r_ble_lll_sched_sync_overlaps_current = 0x40001780;
r_ble_lll_sched_sync_reschedule = 0x40001784;
r_ble_lll_sync_chain_start_cb = 0x40001788;
r_ble_lll_sync_coex_dpc_process = 0x4000178c;
r_ble_lll_sync_coex_dpc_pti_get = 0x40001790;
r_ble_lll_sync_coex_dpc_update = 0x40001794;
r_ble_lll_sync_current_sm_over = 0x40001798;
r_ble_lll_sync_deinit = 0x4000179c;
r_ble_lll_sync_event_end = 0x400017a0;
r_ble_lll_sync_event_end_cb = 0x400017a4;
r_ble_lll_sync_event_start_cb = 0x400017a8;
r_ble_lll_sync_get_event_end_time = 0x400017ac;
r_ble_lll_sync_halt = 0x400017b0;
r_ble_lll_sync_init = 0x400017b4;
r_ble_lll_sync_new = 0x400017b8;
r_ble_lll_sync_reset = 0x400017bc;
r_ble_lll_sync_reset_sm = 0x400017c0;
r_ble_lll_sync_rmvd_from_sched = 0x400017c4;
r_ble_lll_sync_rx_pkt_isr = 0x400017c8;
r_ble_lll_sync_schedule_chain = 0x400017cc;
r_ble_lll_sync_stop = 0x400017d0;
r_ble_lll_sync_trnasfer_sched = 0x400017d4;
r_ble_phy_access_addr_get = 0x400017d8;
r_ble_phy_calculate_rxtx_ifs = 0x400017dc;
r_ble_phy_calculate_rxwindow = 0x400017e0;
r_ble_phy_calculate_txrx_ifs = 0x400017e4;
r_ble_phy_check_bb_status = 0x400017e8;
r_ble_phy_complete_rx_info = 0x400017ec;
r_ble_phy_config_access_addr = 0x400017f0;
r_ble_phy_data_make = 0x400017f4;
r_ble_phy_disable = 0x400017f8;
r_ble_phy_disable_irq = 0x400017fc;
r_ble_phy_disable_whitening = 0x40001800;
r_ble_phy_enable_whitening = 0x40001804;
r_ble_phy_encrypt_disable = 0x40001808;
r_ble_phy_env_init = 0x4000180c;
r_ble_phy_get_current_phy = 0x40001810;
r_ble_phy_get_packet_counter = 0x40001814;
r_ble_phy_get_packet_status = 0x40001818;
r_ble_phy_get_pyld_time_offset = 0x4000181c;
r_ble_phy_get_rx_phy_mode = 0x40001820;
r_ble_phy_get_seq_end_st = 0x40001824;
r_ble_phy_init = 0x40001828;
r_ble_phy_isr = 0x4000182c;
r_ble_phy_max_data_pdu_pyld = 0x40001830;
r_ble_phy_mode_config = 0x40001834;
r_ble_phy_mode_convert = 0x40001838;
r_ble_phy_mode_write = 0x4000183c;
r_ble_phy_module_deinit = 0x40001840;
r_ble_phy_module_init = 0x40001844;
r_ble_phy_monitor_bb_sync = 0x40001848;
r_ble_phy_reset_bb_monitor = 0x4000184c;
r_ble_phy_resolv_list_disable = 0x40001850;
r_ble_phy_resolv_list_enable = 0x40001854;
r_ble_phy_restart_sequence = 0x40001858;
r_ble_phy_rx_set_start_time_forcibly = 0x4000185c;
r_ble_phy_rxpdu_copy = 0x40001860;
r_ble_phy_seq_encrypt_enable = 0x40001864;
r_ble_phy_seq_encrypt_set_pkt_cntr = 0x40001868;
r_ble_phy_sequence_end_isr = 0x4000186c;
r_ble_phy_sequence_get_mode = 0x40001870;
r_ble_phy_sequence_is_running = 0x40001874;
r_ble_phy_sequence_is_waiting_rsp = 0x40001878;
r_ble_phy_sequence_single_end = 0x4000187c;
r_ble_phy_sequence_tx_end_invoke = 0x40001880;
r_ble_phy_sequence_update_conn_ind_params = 0x40001884;
r_ble_phy_set_adv_mode = 0x40001888;
r_ble_phy_set_coex_pti = 0x4000188c;
r_ble_phy_set_conn_ind_pdu = 0x40001890;
r_ble_phy_set_conn_mode = 0x40001894;
r_ble_phy_set_dev_address = 0x40001898;
r_ble_phy_set_rx_pwr_compensation = 0x4000189c;
r_ble_phy_set_rxhdr = 0x400018a0;
r_ble_phy_set_scan_mode = 0x400018a4;
r_ble_phy_set_sequence_mode = 0x400018a8;
r_ble_phy_set_single_packet_rx_sequence = 0x400018ac;
r_ble_phy_set_single_packet_tx_sequence = 0x400018b0;
r_ble_phy_set_tx_rx_transition = 0x400018b4;
r_ble_phy_set_txend_cb = 0x400018b8;
r_ble_phy_setchan = 0x400018bc;
r_ble_phy_start_rx_immediately = 0x400018c0;
r_ble_phy_state_get = 0x400018c4;
r_ble_phy_timer_config_start_time = 0x400018c8;
r_ble_phy_timer_start_now = 0x400018cc;
r_ble_phy_timer_stop = 0x400018d0;
r_ble_phy_tx_set_start_time = 0x400018d4;
//r_ble_phy_txpower_round = 0x400018d8;
r_ble_phy_txpwr_set = 0x400018dc;
//r_ble_phy_update_conn_sequence = 0x400018e0;
r_ble_phy_update_encryption = 0x400018e4;
r_ble_phy_update_ifs = 0x400018e8;
r_ble_phy_xcvr_state_get = 0x400018ec;
r_ble_plf_set_log_level = 0x400018f0;
r_ble_rtc_wake_up_cpu_init = 0x400018f4;
r_ble_rtc_wake_up_state_clr = 0x400018f8;
//r_ble_vendor_hci_register = 0x400018fc;
r_bt_rf_coex_cfg_set = 0x40001900;
r_bt_rf_coex_coded_txrx_time_upper_lim = 0x40001904;
r_bt_rf_coex_dft_pti_set = 0x40001908;
r_bt_rf_coex_hook_deinit = 0x4000190c;
r_bt_rf_coex_hook_init = 0x40001910;
r_bt_rf_coex_hook_st_set = 0x40001914;
r_bt_rf_coex_hooks_p_set_default = 0x40001918;
r_btdm_disable_adv_delay = 0x4000191c;
r_btdm_switch_phy_coded = 0x40001920;
r_esp_wait_disabled = 0x40001924;
r_get_be16 = 0x40001928;
r_get_be24 = 0x4000192c;
r_get_be32 = 0x40001930;
r_get_be64 = 0x40001934;
r_get_le16 = 0x40001938;
r_get_le24 = 0x4000193c;
r_get_le32 = 0x40001940;
r_get_le64 = 0x40001944;
r_get_local_irk_offset = 0x40001948;
r_get_local_rpa_offset = 0x4000194c;
r_get_max_skip = 0x40001950;
r_get_peer_id_offset = 0x40001954;
r_get_peer_irk_offset = 0x40001958;
r_get_peer_rpa_offset = 0x4000195c;
r_hal_rtc_intr_init = 0x40001960;
r_hal_rtc_irq_handler = 0x40001964;
r_hal_timer_deinit = 0x40001968;
r_hal_timer_disable_irq = 0x4000196c;
r_hal_timer_env_init = 0x40001970;
r_hal_timer_init = 0x40001974;
r_hal_timer_process = 0x40001978;
r_hal_timer_read = 0x4000197c;
r_hal_timer_read_tick = 0x40001980;
r_hal_timer_set_cb = 0x40001984;
r_hal_timer_set_exp_tick = 0x40001988;
r_hal_timer_start = 0x4000198c;
r_hal_timer_start_at = 0x40001990;
r_hal_timer_stop = 0x40001994;
r_hal_timer_task_start = 0x40001998;
r_ll_assert = 0x4000199c;
r_mem_init_mbuf_pool = 0x400019a0;
r_mem_malloc_mbuf_pool = 0x400019a4;
r_mem_malloc_mbufpkt_pool = 0x400019a8;
r_mem_malloc_mempool = 0x400019ac;
r_mem_malloc_mempool_ext = 0x400019b0;
r_mem_malloc_mempool_gen = 0x400019b4;
r_mem_pullup_obj = 0x400019b8;
r_mem_split_frag = 0x400019bc;
r_os_cputime_get32 = 0x400019c0;
r_os_cputime_ticks_to_usecs = 0x400019c4;
r_os_cputime_timer_init = 0x400019c8;
r_os_cputime_timer_relative = 0x400019cc;
r_os_cputime_timer_start = 0x400019d0;
r_os_cputime_timer_stop = 0x400019d4;
r_os_cputime_usecs_to_ticks = 0x400019d8;
r_os_mbuf_adj = 0x400019dc;
r_os_mbuf_append = 0x400019e0;
r_os_mbuf_appendfrom = 0x400019e4;
r_os_mbuf_cmpf = 0x400019e8;
r_os_mbuf_cmpm = 0x400019ec;
r_os_mbuf_concat = 0x400019f0;
r_os_mbuf_copydata = 0x400019f4;
r_os_mbuf_copyinto = 0x400019f8;
r_os_mbuf_dup = 0x400019fc;
r_os_mbuf_extend = 0x40001a00;
r_os_mbuf_free = 0x40001a04;
r_os_mbuf_free_chain = 0x40001a08;
r_os_mbuf_get = 0x40001a0c;
r_os_mbuf_get_pkthdr = 0x40001a10;
r_os_mbuf_leadingspace = 0x40001a14;
r_os_mbuf_len = 0x40001a18;
r_os_mbuf_off = 0x40001a1c;
r_os_mbuf_pack_chains = 0x40001a20;
r_os_mbuf_pool_init = 0x40001a24;
r_os_mbuf_prepend = 0x40001a28;
r_os_mbuf_prepend_pullup = 0x40001a2c;
r_os_mbuf_pullup = 0x40001a30;
r_os_mbuf_trailingspace = 0x40001a34;
r_os_mbuf_trim_front = 0x40001a38;
r_os_mbuf_widen = 0x40001a3c;
r_os_memblock_from = 0x40001a40;
r_os_memblock_get = 0x40001a44;
r_os_memblock_put = 0x40001a48;
r_os_memblock_put_from_cb = 0x40001a4c;
r_os_mempool_clear = 0x40001a50;
r_os_mempool_ext_clear = 0x40001a54;
r_os_mempool_ext_init = 0x40001a58;
r_os_mempool_init = 0x40001a60;
r_os_mempool_init_internal = 0x40001a64;
r_os_mempool_is_sane = 0x40001a68;
r_os_mempool_module_init = 0x40001a6c;
r_os_mqueue_get = 0x40001a74;
r_os_mqueue_init = 0x40001a78;
r_os_mqueue_put = 0x40001a7c;
r_os_msys_count = 0x40001a80;
r_os_msys_get = 0x40001a84;
r_os_msys_get_pkthdr = 0x40001a88;
r_os_msys_num_free = 0x40001a8c;
r_os_msys_register = 0x40001a90;
r_os_msys_reset = 0x40001a94;
r_pri_phy_valid = 0x40001a98;
r_put_be16 = 0x40001a9c;
r_put_be24 = 0x40001aa0;
r_put_be32 = 0x40001aa4;
r_put_be64 = 0x40001aa8;
r_put_le16 = 0x40001aac;
r_put_le24 = 0x40001ab0;
r_put_le32 = 0x40001ab4;
r_put_le64 = 0x40001ab8;
r_rtc0_timer_handler = 0x40001abc;
r_sdkconfig_get_opts = 0x40001ac0;
//r_sdkconfig_set_opts = 0x40001ac4;
r_sec_phy_valid = 0x40001ac8;
r_swap_buf = 0x40001acc;
r_swap_in_place = 0x40001ad0;
/* Data (.data, .bss, .rodata) */
ble_lll_dtm_module_env_p = 0x3fcdffc4;
g_ble_lll_dtm_prbs15_data = 0x3ff4fee4;
g_ble_lll_dtm_prbs9_data = 0x3ff4fde4;
g_channel_rf_to_index = 0x3ff4fdbc;
g_ble_lll_rfmgmt_data = 0x3fcdff7c;
g_ble_sleep_enter_cb = 0x3fcdff78;
g_ble_sleep_exit_cb = 0x3fcdff74;
ble_lll_sched_env_p = 0x3fcdff70;
ble_ll_env_p = 0x3fcdff6c;
g_ble_ll_pdu_header_tx_time_ro = 0x3ff4fdb4;
ble_ll_adv_env_p = 0x3fcdff68;
ble_ll_conn_env_p = 0x3fcdff64;
ble_ll_conn_required_phy_mask = 0x3ff4fdb0;
ble_ll_valid_conn_phy_mask = 0x3ff4fdaf;
g_ble_ll_ctrl_pkt_lengths_ro = 0x3ff4fd8c;
ble_ll_hci_env_p = 0x3fcdff60;
g_debug_le_private_key = 0x3ff4fd6c;
g_ecc_key = 0x3fcdfefc;
ble_ll_rand_env_p = 0x3fcdfef8;
ble_ll_resolv_env_p = 0x3fcdfef4;
g_ble_ll_resolve_hdr = 0x3fcdfeec;
g_device_mode_default = 0x3fcdfe68;
ble_ll_scan_classify_filter_aux_check_cb = 0x3fcdfee8;
ble_ll_scan_classify_filter_check_cb = 0x3fcdfee4;
ble_ll_scan_env_p = 0x3fcdfee0;
g_ble_ll_supp_cmds_ro = 0x3ff4fd3c;
ble_ll_sync_env_p = 0x3fcdfedc;
g_ble_sca_ppm_tbl_ro = 0x3ff4fd2c;
priv_config_opts = 0x3fcdfe48;
ble_hci_uart_reset_cmd = 0x3ff4fd28;
ble_hci_trans_env_p = 0x3fcdfed8;
ble_hci_trans_mode = 0x3fcdfe44;
ble_hci_trans_funcs_ptr = 0x3fcdfed4;
r_ble_lll_stub_funcs_ptr = 0x3fcdfed0;
r_ble_stub_funcs_ptr = 0x3fcdfecc;
r_ext_funcs_p = 0x3fcdfec8;
r_npl_funcs = 0x3fcdfec4;
ble_hw_env_p = 0x3fcdfec0;
ble_phy_module_env_p = 0x3fcdfebc;
g_ble_phy_chan_freq_ro = 0x3ff4fd00;
g_ble_phy_mode_pkt_start_off_ro = 0x3ff4fcf8;
g_ble_phy_rxtx_ifs_compensation_ro = 0x3ff4fce8;
g_ble_phy_t_rxaddrdelay_ro = 0x3ff4fce4;
g_ble_phy_t_rxenddelay_ro = 0x3ff4fce0;
g_ble_phy_t_txdelay_ro = 0x3ff4fcdc;
g_ble_phy_t_txenddelay_ro = 0x3ff4fcd8;
g_ble_phy_txrx_ifs_compensation_ro = 0x3ff4fcc8;
hal_timer_env_p = 0x3fcdfeb8;
r_osi_coex_funcs_p = 0x3fcdfeb4;
bt_rf_coex_hooks = 0x3fcdfeac;
bt_rf_coex_hooks_p = 0x3fcdfea8;
coex_hook_st_group_tab = 0x3ff4fcbc;
coex_hook_st_group_to_coex_schm_st_tab = 0x3ff4fcb8;
s_ble_act_count_by_group = 0x3fcdfea4;
s_ble_coex_st_map = 0x3fcdfe90;
bt_rf_coex_cfg_cb = 0x3fcdfe74;
bt_rf_coex_cfg_p = 0x3fcdfe70;
bt_rf_coex_cfg_rom = 0x3ff4fc9c;
bt_rf_coex_pti_dft_p = 0x3fcdfe6c;
bt_rf_coex_pti_dft_rom = 0x3fcdfe04;
conn_dynamic_pti_param_rom = 0x3ff4fc84;
conn_phy_coded_max_data_time_param_rom = 0x3ff4fc80;
ext_adv_dynamic_pti_param_rom = 0x3ff4fc4c;
ext_scan_dynamic_param_rom = 0x3ff4fc14;
legacy_adv_dynamic_pti_param_rom = 0x3ff4fbf4;
per_adv_dynamic_pti_param_rom = 0x3ff4fbd8;
sync_dynamic_param_rom = 0x3ff4fbc0;
g_ble_plf_log_level = 0x3fcdfe00;
g_msys_pool_list = 0x3fcdfdf8;
g_os_mempool_list = 0x3fcdfdf0;


/***************************************
 Group eco4_bluetooth
 ***************************************/

/* Functions */
r_ble_ll_adv_ext_pdu_tx_len = 0x40002eb0;
//r_ble_ll_adv_ext_estimate_data_itvl = 0x40002eb4;
//r_ble_ll_adv_ext_check_data_itvl = 0x40002eb8;
r_ble_ll_ctrl_channel_status_report_timer_cb = 0x40002ebc;
r_ble_ll_ctrl_channel_class_enable_make = 0x40002ec0;
r_ble_ll_ctrl_channel_class_reporting_make = 0x40002ec4;
r_ble_ll_ctrl_rx_channel_reporting_ind = 0x40002ec8;
r_ble_ll_ctrl_rx_channel_status_ind = 0x40002ecc;
r_ble_ll_ctrl_channel_class_info_update = 0x40002ed0;
r_ble_ll_adv_set_data_related_addr_change = 0x40002ed4;
r_ble_ll_misc_additional_options_set = 0x40002ed8;
r_ble_phy_get_txdbm_by_level = 0x40002edc;
r_hal_timer_disable_intr = 0x40002ee0;
r_hal_timer_enable_intr = 0x40002ee4;
r_hal_timer_task_stop = 0x40002ee8;
//r_ble_lll_rfmgmt_env_init = 0x40002eec;
r_ble_ll_scan_set_aux_ll_flag = 0x40002ef0;
r_ble_ll_rf_temp_calibration = 0x40002ef4;
r_ble_ll_adv_env_deinit = 0x40002ef8;
r_ble_ll_conn_env_deinit = 0x40002efc;
r_ble_ll_hci_env_deinit = 0x40002f00;
r_ble_ll_rand_env_deinit = 0x40002f04;
r_ble_ll_resolv_env_deinit = 0x40002f08;
r_ble_ll_scan_env_deinit = 0x40002f0c;
r_ble_ll_sync_env_deinit = 0x40002f10;
r_ble_lll_rfmgmt_env_deinit = 0x40002f14;
r_hal_timer_env_deinit = 0x40002f18;
r_ble_ll_env_deinit = 0x40002f1c;
r_ble_ll_generic_data_deinit = 0x40002f20;
//r_ble_hci_trans_env_deinit = 0x40002f24;
r_ble_ll_conn_callout_env_init = 0x40002f28;
r_ble_ll_conn_callout_env_deinit = 0x40002f2c;
r_ble_ll_scan_callout_env_init = 0x40002f30;
r_ble_ll_scan_callout_env_deinit = 0x40002f34;
r_ble_ll_callout_env_init = 0x40002f38;
r_ble_ll_callout_env_deinit = 0x40002f3c;
r_ble_ll_resolv_callout_env_init = 0x40002f40;
r_ble_ll_resolv_callout_env_deinit = 0x40002f44;
r_ble_ll_get_npl_element_info = 0x40002f48;
r_ble_rtc_wake_up_cpu_clr = 0x40002f4c;
r_ble_ll_scan_hci_set_adv_report_flow_ctrl = 0x40002f50;
r_ble_ll_scan_hci_update_adv_report_flow_ctrl = 0x40002f54;
//r_ble_ll_scan_send_adv_lost_report = 0x40002f58;
r_ble_ll_scan_adv_report_cth_flow_is_enabled = 0x40002f5c;
r_ble_ll_scan_adv_report_cth_flow_alloc_credit = 0x40002f60;
r_ble_ll_scan_adv_report_cth_flow_free_credit = 0x40002f64;
r_ble_ll_scan_adv_report_cth_flow_have_credit = 0x40002f68;
r_ble_ll_scan_adv_report_lost_cnt_threshold_arrived = 0x40002f6c;
r_ble_ll_scan_adv_report_lost_cnt_clear = 0x40002f70;
r_ble_ll_scan_adv_report_lost_cnt_add = 0x40002f74;
r_ble_ll_trace_hex = 0x40002f78;
r_ble_ll_conn_calc_closest_event_cntr = 0x40002f7c;
r_ble_ll_trace_buffer_select = 0x40002f80;
r_ble_ll_adv_vendor_hci_legacy_adv_clear = 0x40002f84;
r_ble_ll_conn_is_lru_compare_with_sync = 0x40002f88;
r_ble_ll_conn_rollback_last_unmapped_chan = 0x40002f8c;
//r_ble_ll_hci_vs_csa_set = 0x40002f90;
r_ble_ll_hci_reset = 0x40002f94;
r_ble_ll_adv_status_check = 0x40002f98;
r_ble_ll_conn_status_check = 0x40002f9c;
r_ble_ll_scan_status_check = 0x40002fa0;
r_ble_ll_sync_status_check = 0x40002fa4;
r_ble_ll_resolv_status_check = 0x40002fa8;
r_ble_ll_whitelist_status_check = 0x40002fac;
r_ble_ll_adv_delay_get = 0x40002fb0;
r_ble_ll_scan_continue_status_get = 0x40002fb4;
r_ble_ll_default_privacy_mode_get = 0x40002fb8;
r_ble_ll_adv_periodic_status_check = 0x40002fbc;
r_ble_ll_sync_list_status_check = 0x40002fc0;
r_ble_lll_rfmgmt_wake_up_ev = 0x40002fc4;
r_ble_lll_sched_env_deinit = 0x40002fc8;
r_ble_phy_env_deinit = 0x40002fcc;
r_ble_hw_driver_env_deinit = 0x40002fd0;
r_ble_lll_dtm_env_init = 0x40002fd4;
r_ble_lll_dtm_env_deinit = 0x40002fd8;
r_ble_lll_scan_callout_env_init = 0x40002fdc;
r_ble_lll_scan_callout_env_deinit = 0x40002fe0;
r_ble_lll_scan_env_init = 0x40002fe4;
r_ble_lll_scan_env_deinit = 0x40002fe8;
r_ble_lll_get_npl_element_info = 0x40002fec;
r_ble_lll_conn_rxpdu_alloc = 0x40002ff0;
r_ble_lll_scan_filter_out_useless_adv = 0x40002ff4;
r_ble_hw_whitelist_check_in_wl = 0x40002ff8;
r_ble_phy_stats_reset = 0x40002ffc;
r_ble_phy_ramup_time_set = 0x40003000;
r_ble_lll_rfmgmt_should_skip_light_sleep_check = 0x40003004;
r_ble_phy_rx_err_record = 0x40003008;
r_ble_lll_rfmgmt_wake_up_overhead_set = 0x4000300c;
//r_ble_lll_conn_event_delete_and_reschedule = 0x40003010;
r_ble_lll_conn_should_reschedule = 0x40003014;
r_ble_lll_adv_ext_event_rmvd_from_sched = 0x40003018;
r_ble_lll_conn_process_rx_data_after_halt = 0x4000301c;
r_ble_phy_global_rxbuf_get = 0x40003020;
/* Data (.data, .bss, .rodata) */
g_ble_lll_rfmgmt_env_p = 0x3fcdf3cc;
priv_config_additional_opts_ptr = 0x3fcdfa70;
g_ble_ll_ctrl_pkt_lengths_eco4_ro = 0x3ff4fbac;
