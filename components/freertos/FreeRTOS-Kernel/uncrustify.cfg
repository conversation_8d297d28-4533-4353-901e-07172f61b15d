# Uncrustify-0.69.0

newlines                        = auto     # lf/crlf/cr/auto
input_tab_size                  = 4        # unsigned number
output_tab_size                 = 4        # unsigned number
string_escape_char              = 92       # unsigned number
string_escape_char2             = 0        # unsigned number
string_replace_tab_chars        = false    # true/false
tok_split_gte                   = false    # true/false
disable_processing_cmt          = " *INDENT-OFF*"      # string
enable_processing_cmt           = " *INDENT-ON*"     # string
enable_digraphs                 = false    # true/false
utf8_bom                        = ignore   # ignore/add/remove/force
utf8_byte                       = false    # true/false
utf8_force                      = false    # true/false
sp_arith                        = force    # ignore/add/remove/force
sp_arith_additive               = ignore   # ignore/add/remove/force
sp_assign                       = force    # ignore/add/remove/force
sp_cpp_lambda_assign            = ignore   # ignore/add/remove/force
sp_cpp_lambda_paren             = ignore   # ignore/add/remove/force
sp_assign_default               = force    # ignore/add/remove/force
sp_before_assign                = force    # ignore/add/remove/force
sp_after_assign                 = force    # ignore/add/remove/force
sp_enum_paren                   = ignore   # ignore/add/remove/force
sp_enum_assign                  = force    # ignore/add/remove/force
sp_enum_before_assign           = force    # ignore/add/remove/force
sp_enum_after_assign            = force    # ignore/add/remove/force
sp_enum_colon                   = ignore   # ignore/add/remove/force
sp_pp_concat                    = add      # ignore/add/remove/force
sp_pp_stringify                 = add      # ignore/add/remove/force
sp_before_pp_stringify          = ignore   # ignore/add/remove/force
sp_bool                         = force    # ignore/add/remove/force
sp_compare                      = force    # ignore/add/remove/force
sp_inside_paren                 = force    # ignore/add/remove/force
sp_paren_paren                  = force    # ignore/add/remove/force
sp_cparen_oparen                = ignore   # ignore/add/remove/force
sp_balance_nested_parens        = false    # true/false
sp_paren_brace                  = force    # ignore/add/remove/force
sp_brace_brace                  = ignore   # ignore/add/remove/force
sp_before_ptr_star              = force    # ignore/add/remove/force
sp_before_unnamed_ptr_star      = force    # ignore/add/remove/force
sp_between_ptr_star             = remove   # ignore/add/remove/force
sp_after_ptr_star               = force    # ignore/add/remove/force
sp_after_ptr_block_caret        = ignore   # ignore/add/remove/force
sp_after_ptr_star_qualifier     = ignore   # ignore/add/remove/force
sp_after_ptr_star_func          = ignore   # ignore/add/remove/force
sp_ptr_star_paren               = ignore   # ignore/add/remove/force
sp_before_ptr_star_func         = ignore   # ignore/add/remove/force
sp_before_byref                 = force    # ignore/add/remove/force
sp_before_unnamed_byref         = ignore   # ignore/add/remove/force
sp_after_byref                  = remove   # ignore/add/remove/force
sp_after_byref_func             = remove   # ignore/add/remove/force
sp_before_byref_func            = ignore   # ignore/add/remove/force
sp_after_type                   = force    # ignore/add/remove/force
sp_after_decltype               = ignore   # ignore/add/remove/force
sp_before_template_paren        = ignore   # ignore/add/remove/force
sp_template_angle               = ignore   # ignore/add/remove/force
sp_before_angle                 = remove   # ignore/add/remove/force
sp_inside_angle                 = remove   # ignore/add/remove/force
sp_inside_angle_empty           = ignore   # ignore/add/remove/force
sp_angle_colon                  = ignore   # ignore/add/remove/force
sp_after_angle                  = force    # ignore/add/remove/force
sp_angle_paren                  = ignore   # ignore/add/remove/force
sp_angle_paren_empty            = ignore   # ignore/add/remove/force
sp_angle_word                   = ignore   # ignore/add/remove/force
sp_angle_shift                  = add      # ignore/add/remove/force
sp_permit_cpp11_shift           = false    # true/false
sp_before_sparen                = remove   # ignore/add/remove/force
sp_inside_sparen                = force    # ignore/add/remove/force
sp_inside_sparen_open           = ignore   # ignore/add/remove/force
sp_inside_sparen_close          = ignore   # ignore/add/remove/force
sp_after_sparen                 = force    # ignore/add/remove/force
sp_sparen_brace                 = force    # ignore/add/remove/force
sp_invariant_paren              = ignore   # ignore/add/remove/force
sp_after_invariant_paren        = ignore   # ignore/add/remove/force
sp_special_semi                 = ignore   # ignore/add/remove/force
sp_before_semi                  = remove   # ignore/add/remove/force
sp_before_semi_for              = remove   # ignore/add/remove/force
sp_before_semi_for_empty        = add      # ignore/add/remove/force
sp_after_semi                   = add      # ignore/add/remove/force
sp_after_semi_for               = force    # ignore/add/remove/force
sp_after_semi_for_empty         = force    # ignore/add/remove/force
sp_before_square                = remove   # ignore/add/remove/force
sp_before_squares               = remove   # ignore/add/remove/force
sp_cpp_before_struct_binding    = ignore   # ignore/add/remove/force
sp_inside_square                = force    # ignore/add/remove/force
sp_inside_square_oc_array       = ignore   # ignore/add/remove/force
sp_after_comma                  = force    # ignore/add/remove/force
sp_before_comma                 = remove   # ignore/add/remove/force
sp_after_mdatype_commas         = ignore   # ignore/add/remove/force
sp_before_mdatype_commas        = ignore   # ignore/add/remove/force
sp_between_mdatype_commas       = ignore   # ignore/add/remove/force
sp_paren_comma                  = force    # ignore/add/remove/force
sp_before_ellipsis              = ignore   # ignore/add/remove/force
sp_type_ellipsis                = ignore   # ignore/add/remove/force
sp_type_question                = ignore   # ignore/add/remove/force
sp_paren_ellipsis               = ignore   # ignore/add/remove/force
sp_paren_qualifier              = ignore   # ignore/add/remove/force
sp_paren_noexcept               = ignore   # ignore/add/remove/force
sp_after_class_colon            = ignore   # ignore/add/remove/force
sp_before_class_colon           = ignore   # ignore/add/remove/force
sp_after_constr_colon           = ignore   # ignore/add/remove/force
sp_before_constr_colon          = ignore   # ignore/add/remove/force
sp_before_case_colon            = remove   # ignore/add/remove/force
sp_after_operator               = ignore   # ignore/add/remove/force
sp_after_operator_sym           = ignore   # ignore/add/remove/force
sp_after_operator_sym_empty     = ignore   # ignore/add/remove/force
sp_after_cast                   = force    # ignore/add/remove/force
sp_inside_paren_cast            = force    # ignore/add/remove/force
sp_cpp_cast_paren               = ignore   # ignore/add/remove/force
sp_sizeof_paren                 = remove   # ignore/add/remove/force
sp_sizeof_ellipsis              = ignore   # ignore/add/remove/force
sp_sizeof_ellipsis_paren        = ignore   # ignore/add/remove/force
sp_decltype_paren               = ignore   # ignore/add/remove/force
sp_after_tag                    = ignore   # ignore/add/remove/force
sp_inside_braces_enum           = force    # ignore/add/remove/force
sp_inside_braces_struct         = force    # ignore/add/remove/force
sp_inside_braces_oc_dict        = ignore   # ignore/add/remove/force
sp_after_type_brace_init_lst_open = ignore   # ignore/add/remove/force
sp_before_type_brace_init_lst_close = ignore   # ignore/add/remove/force
sp_inside_type_brace_init_lst   = ignore   # ignore/add/remove/force
sp_inside_braces                = force    # ignore/add/remove/force
sp_inside_braces_empty          = remove   # ignore/add/remove/force
sp_type_func                    = force    # ignore/add/remove/force
sp_type_brace_init_lst          = ignore   # ignore/add/remove/force
sp_func_proto_paren             = remove   # ignore/add/remove/force
sp_func_proto_paren_empty       = ignore   # ignore/add/remove/force
sp_func_def_paren               = remove   # ignore/add/remove/force
sp_func_def_paren_empty         = ignore   # ignore/add/remove/force
sp_inside_fparens               = remove   # ignore/add/remove/force
sp_inside_fparen                = force    # ignore/add/remove/force
sp_inside_tparen                = ignore   # ignore/add/remove/force
sp_after_tparen_close           = ignore   # ignore/add/remove/force
sp_square_fparen                = ignore   # ignore/add/remove/force
sp_fparen_brace                 = add      # ignore/add/remove/force
sp_fparen_brace_initializer     = ignore   # ignore/add/remove/force
sp_fparen_dbrace                = ignore   # ignore/add/remove/force
sp_func_call_paren              = remove   # ignore/add/remove/force
sp_func_call_paren_empty        = ignore   # ignore/add/remove/force
sp_func_call_user_paren         = ignore   # ignore/add/remove/force
sp_func_call_user_inside_fparen = ignore   # ignore/add/remove/force
sp_func_call_user_paren_paren   = ignore   # ignore/add/remove/force
sp_func_class_paren             = remove   # ignore/add/remove/force
sp_func_class_paren_empty       = ignore   # ignore/add/remove/force
sp_return_paren                 = remove   # ignore/add/remove/force
sp_return_brace                 = ignore   # ignore/add/remove/force
sp_attribute_paren              = remove   # ignore/add/remove/force
sp_defined_paren                = remove   # ignore/add/remove/force
sp_throw_paren                  = ignore   # ignore/add/remove/force
sp_after_throw                  = ignore   # ignore/add/remove/force
sp_catch_paren                  = ignore   # ignore/add/remove/force
sp_oc_catch_paren               = ignore   # ignore/add/remove/force
sp_oc_classname_paren           = ignore   # ignore/add/remove/force
sp_version_paren                = ignore   # ignore/add/remove/force
sp_scope_paren                  = ignore   # ignore/add/remove/force
sp_super_paren                  = remove   # ignore/add/remove/force
sp_this_paren                   = remove   # ignore/add/remove/force
sp_macro                        = force    # ignore/add/remove/force
sp_macro_func                   = force    # ignore/add/remove/force
sp_else_brace                   = ignore   # ignore/add/remove/force
sp_brace_else                   = ignore   # ignore/add/remove/force
sp_brace_typedef                = force    # ignore/add/remove/force
sp_catch_brace                  = ignore   # ignore/add/remove/force
sp_oc_catch_brace               = ignore   # ignore/add/remove/force
sp_brace_catch                  = ignore   # ignore/add/remove/force
sp_oc_brace_catch               = ignore   # ignore/add/remove/force
sp_finally_brace                = ignore   # ignore/add/remove/force
sp_brace_finally                = ignore   # ignore/add/remove/force
sp_try_brace                    = ignore   # ignore/add/remove/force
sp_getset_brace                 = ignore   # ignore/add/remove/force
sp_word_brace                   = add      # ignore/add/remove/force
sp_word_brace_ns                = add      # ignore/add/remove/force
sp_before_dc                    = remove   # ignore/add/remove/force
sp_after_dc                     = remove   # ignore/add/remove/force
sp_d_array_colon                = ignore   # ignore/add/remove/force
sp_not                          = remove   # ignore/add/remove/force
sp_inv                          = remove   # ignore/add/remove/force
sp_addr                         = remove   # ignore/add/remove/force
sp_member                       = remove   # ignore/add/remove/force
sp_deref                        = remove   # ignore/add/remove/force
sp_sign                         = remove   # ignore/add/remove/force
sp_incdec                       = remove   # ignore/add/remove/force
sp_before_nl_cont               = add      # ignore/add/remove/force
sp_after_oc_scope               = ignore   # ignore/add/remove/force
sp_after_oc_colon               = ignore   # ignore/add/remove/force
sp_before_oc_colon              = ignore   # ignore/add/remove/force
sp_after_oc_dict_colon          = ignore   # ignore/add/remove/force
sp_before_oc_dict_colon         = ignore   # ignore/add/remove/force
sp_after_send_oc_colon          = ignore   # ignore/add/remove/force
sp_before_send_oc_colon         = ignore   # ignore/add/remove/force
sp_after_oc_type                = ignore   # ignore/add/remove/force
sp_after_oc_return_type         = ignore   # ignore/add/remove/force
sp_after_oc_at_sel              = ignore   # ignore/add/remove/force
sp_after_oc_at_sel_parens       = ignore   # ignore/add/remove/force
sp_inside_oc_at_sel_parens      = ignore   # ignore/add/remove/force
sp_before_oc_block_caret        = ignore   # ignore/add/remove/force
sp_after_oc_block_caret         = ignore   # ignore/add/remove/force
sp_after_oc_msg_receiver        = ignore   # ignore/add/remove/force
sp_after_oc_property            = ignore   # ignore/add/remove/force
sp_after_oc_synchronized        = ignore   # ignore/add/remove/force
sp_cond_colon                   = force    # ignore/add/remove/force
sp_cond_colon_before            = ignore   # ignore/add/remove/force
sp_cond_colon_after             = ignore   # ignore/add/remove/force
sp_cond_question                = force    # ignore/add/remove/force
sp_cond_question_before         = ignore   # ignore/add/remove/force
sp_cond_question_after          = ignore   # ignore/add/remove/force
sp_cond_ternary_short           = ignore   # ignore/add/remove/force
sp_case_label                   = force    # ignore/add/remove/force
sp_range                        = ignore   # ignore/add/remove/force
sp_after_for_colon              = ignore   # ignore/add/remove/force
sp_before_for_colon             = ignore   # ignore/add/remove/force
sp_extern_paren                 = ignore   # ignore/add/remove/force
sp_cmt_cpp_start                = ignore   # ignore/add/remove/force
sp_cmt_cpp_doxygen              = false    # true/false
sp_cmt_cpp_qttr                 = false    # true/false
sp_endif_cmt                    = force    # ignore/add/remove/force
sp_after_new                    = ignore   # ignore/add/remove/force
sp_between_new_paren            = ignore   # ignore/add/remove/force
sp_after_newop_paren            = ignore   # ignore/add/remove/force
sp_inside_newop_paren           = ignore   # ignore/add/remove/force
sp_inside_newop_paren_open      = ignore   # ignore/add/remove/force
sp_inside_newop_paren_close     = ignore   # ignore/add/remove/force
sp_before_tr_emb_cmt            = force    # ignore/add/remove/force
sp_num_before_tr_emb_cmt        = 1        # unsigned number
sp_annotation_paren             = ignore   # ignore/add/remove/force
sp_skip_vbrace_tokens           = false    # true/false
sp_after_noexcept               = ignore   # ignore/add/remove/force
sp_vala_after_translation       = ignore   # ignore/add/remove/force
force_tab_after_define          = false    # true/false
indent_columns                  = 4        # unsigned number
indent_continue                 = 0        # number
indent_continue_class_head      = 0        # unsigned number
indent_single_newlines          = false    # true/false
indent_param                    = 0        # unsigned number
indent_with_tabs                = 0        # unsigned number
indent_cmt_with_tabs            = false    # true/false
indent_align_string             = true     # true/false
indent_xml_string               = 0        # unsigned number
indent_brace                    = 0        # unsigned number
indent_braces                   = false    # true/false
indent_braces_no_func           = false    # true/false
indent_braces_no_class          = false    # true/false
indent_braces_no_struct         = false    # true/false
indent_brace_parent             = false    # true/false
indent_paren_open_brace         = false    # true/false
indent_cs_delegate_brace        = false    # true/false
indent_cs_delegate_body         = false    # true/false
indent_namespace                = false    # true/false
indent_namespace_single_indent  = false    # true/false
indent_namespace_level          = 0        # unsigned number
indent_namespace_limit          = 0        # unsigned number
indent_extern                   = false    # true/false
indent_class                    = true     # true/false
indent_class_colon              = true     # true/false
indent_class_on_colon           = false    # true/false
indent_constr_colon             = false    # true/false
indent_ctor_init_leading        = 2        # unsigned number
indent_ctor_init                = 0        # number
indent_else_if                  = false    # true/false
indent_var_def_blk              = 0        # number
indent_var_def_cont             = false    # true/false
indent_shift                    = false    # true/false
indent_func_def_force_col1      = false    # true/false
indent_func_call_param          = false    # true/false
indent_func_def_param           = false    # true/false
indent_func_proto_param         = false    # true/false
indent_func_class_param         = false    # true/false
indent_func_ctor_var_param      = false    # true/false
indent_template_param           = false    # true/false
indent_func_param_double        = false    # true/false
indent_func_const               = 0        # unsigned number
indent_func_throw               = 0        # unsigned number
indent_member                   = 3        # unsigned number
indent_member_single            = false    # true/false
indent_sing_line_comments       = 0        # unsigned number
indent_relative_single_line_comments = false    # true/false
indent_switch_case              = 4        # unsigned number
indent_switch_pp                = true     # true/false
indent_case_shift               = 0        # unsigned number
indent_case_brace               = 3        # number
indent_col1_comment             = false    # true/false
indent_col1_multi_string_literal = false    # true/false
indent_label                    = 1        # number
indent_access_spec              = 1        # number
indent_access_spec_body         = false    # true/false
indent_paren_nl                 = false    # true/false
indent_paren_close              = 0        # unsigned number
indent_paren_after_func_def     = false    # true/false
indent_paren_after_func_decl    = false    # true/false
indent_paren_after_func_call    = false    # true/false
indent_comma_paren              = false    # true/false
indent_bool_paren               = false    # true/false
indent_semicolon_for_paren      = false    # true/false
indent_first_bool_expr          = false    # true/false
indent_first_for_expr           = false    # true/false
indent_square_nl                = false    # true/false
indent_preserve_sql             = false    # true/false
indent_align_assign             = true     # true/false
indent_align_paren              = true     # true/false
indent_oc_block                 = false    # true/false
indent_oc_block_msg             = 0        # unsigned number
indent_oc_msg_colon             = 0        # unsigned number
indent_oc_msg_prioritize_first_colon = true     # true/false
indent_oc_block_msg_xcode_style = false    # true/false
indent_oc_block_msg_from_keyword = false    # true/false
indent_oc_block_msg_from_colon  = false    # true/false
indent_oc_block_msg_from_caret  = false    # true/false
indent_oc_block_msg_from_brace  = false    # true/false
indent_min_vbrace_open          = 0        # unsigned number
indent_vbrace_open_on_tabstop   = false    # true/false
indent_token_after_brace        = true     # true/false
indent_cpp_lambda_body          = false    # true/false
indent_using_block              = true     # true/false
indent_ternary_operator         = 0        # unsigned number
indent_off_after_return_new     = false    # true/false
indent_single_after_return      = false    # true/false
indent_ignore_asm_block         = false    # true/false
nl_collapse_empty_body          = false    # true/false
nl_assign_leave_one_liners      = true     # true/false
nl_class_leave_one_liners       = true     # true/false
nl_enum_leave_one_liners        = false    # true/false
nl_getset_leave_one_liners      = false    # true/false
nl_cs_property_leave_one_liners = false    # true/false
nl_func_leave_one_liners        = false    # true/false
nl_cpp_lambda_leave_one_liners  = false    # true/false
nl_if_leave_one_liners          = false    # true/false
nl_while_leave_one_liners       = false    # true/false
nl_for_leave_one_liners         = false    # true/false
nl_oc_msg_leave_one_liner       = false    # true/false
nl_oc_mdef_brace                = ignore   # ignore/add/remove/force
nl_oc_block_brace               = ignore   # ignore/add/remove/force
nl_oc_interface_brace           = ignore   # ignore/add/remove/force
nl_oc_implementation_brace      = ignore   # ignore/add/remove/force
nl_start_of_file                = remove   # ignore/add/remove/force
nl_start_of_file_min            = 0        # unsigned number
nl_end_of_file                  = force    # ignore/add/remove/force
nl_end_of_file_min              = 1        # unsigned number
nl_assign_brace                 = add      # ignore/add/remove/force
nl_assign_square                = ignore   # ignore/add/remove/force
nl_tsquare_brace                = ignore   # ignore/add/remove/force
nl_after_square_assign          = ignore   # ignore/add/remove/force
nl_fcall_brace                  = add      # ignore/add/remove/force
nl_enum_brace                   = force    # ignore/add/remove/force
nl_enum_class                   = ignore   # ignore/add/remove/force
nl_enum_class_identifier        = ignore   # ignore/add/remove/force
nl_enum_identifier_colon        = ignore   # ignore/add/remove/force
nl_enum_colon_type              = ignore   # ignore/add/remove/force
nl_struct_brace                 = force    # ignore/add/remove/force
nl_union_brace                  = force    # ignore/add/remove/force
nl_if_brace                     = add      # ignore/add/remove/force
nl_brace_else                   = add      # ignore/add/remove/force
nl_elseif_brace                 = ignore   # ignore/add/remove/force
nl_else_brace                   = add      # ignore/add/remove/force
nl_else_if                      = ignore   # ignore/add/remove/force
nl_before_if_closing_paren      = ignore   # ignore/add/remove/force
nl_brace_finally                = ignore   # ignore/add/remove/force
nl_finally_brace                = ignore   # ignore/add/remove/force
nl_try_brace                    = ignore   # ignore/add/remove/force
nl_getset_brace                 = force    # ignore/add/remove/force
nl_for_brace                    = add      # ignore/add/remove/force
nl_catch_brace                  = ignore   # ignore/add/remove/force
nl_oc_catch_brace               = ignore   # ignore/add/remove/force
nl_brace_catch                  = ignore   # ignore/add/remove/force
nl_oc_brace_catch               = ignore   # ignore/add/remove/force
nl_brace_square                 = ignore   # ignore/add/remove/force
nl_brace_fparen                 = ignore   # ignore/add/remove/force
nl_while_brace                  = add      # ignore/add/remove/force
nl_scope_brace                  = ignore   # ignore/add/remove/force
nl_unittest_brace               = ignore   # ignore/add/remove/force
nl_version_brace                = ignore   # ignore/add/remove/force
nl_using_brace                  = ignore   # ignore/add/remove/force
nl_brace_brace                  = ignore   # ignore/add/remove/force
nl_do_brace                     = add      # ignore/add/remove/force
nl_brace_while                  = ignore   # ignore/add/remove/force
nl_switch_brace                 = add      # ignore/add/remove/force
nl_synchronized_brace           = ignore   # ignore/add/remove/force
nl_multi_line_cond              = false    # true/false
nl_multi_line_define            = true     # true/false
nl_before_case                  = true     # true/false
nl_after_case                   = true     # true/false
nl_case_colon_brace             = ignore   # ignore/add/remove/force
nl_before_throw                 = ignore   # ignore/add/remove/force
nl_namespace_brace              = ignore   # ignore/add/remove/force
nl_template_class               = ignore   # ignore/add/remove/force
nl_class_brace                  = ignore   # ignore/add/remove/force
nl_class_init_args              = ignore   # ignore/add/remove/force
nl_constr_init_args             = ignore   # ignore/add/remove/force
nl_enum_own_lines               = ignore   # ignore/add/remove/force
nl_func_type_name               = remove   # ignore/add/remove/force
nl_func_type_name_class         = ignore   # ignore/add/remove/force
nl_func_class_scope             = ignore   # ignore/add/remove/force
nl_func_scope_name              = ignore   # ignore/add/remove/force
nl_func_proto_type_name         = remove   # ignore/add/remove/force
nl_func_paren                   = remove   # ignore/add/remove/force
nl_func_paren_empty             = ignore   # ignore/add/remove/force
nl_func_def_paren               = remove   # ignore/add/remove/force
nl_func_def_paren_empty         = ignore   # ignore/add/remove/force
nl_func_call_paren              = ignore   # ignore/add/remove/force
nl_func_call_paren_empty        = ignore   # ignore/add/remove/force
nl_func_decl_start              = remove   # ignore/add/remove/force
nl_func_def_start               = remove   # ignore/add/remove/force
nl_func_decl_start_single       = ignore   # ignore/add/remove/force
nl_func_def_start_single        = ignore   # ignore/add/remove/force
nl_func_decl_start_multi_line   = false    # true/false
nl_func_def_start_multi_line    = false    # true/false
nl_func_decl_args               = add      # ignore/add/remove/force
nl_func_def_args                = add      # ignore/add/remove/force
nl_func_decl_args_multi_line    = false    # true/false
nl_func_def_args_multi_line     = false    # true/false
nl_func_decl_end                = remove   # ignore/add/remove/force
nl_func_def_end                 = remove   # ignore/add/remove/force
nl_func_decl_end_single         = ignore   # ignore/add/remove/force
nl_func_def_end_single          = ignore   # ignore/add/remove/force
nl_func_decl_end_multi_line     = false    # true/false
nl_func_def_end_multi_line      = false    # true/false
nl_func_decl_empty              = ignore   # ignore/add/remove/force
nl_func_def_empty               = ignore   # ignore/add/remove/force
nl_func_call_empty              = ignore   # ignore/add/remove/force
nl_func_call_start              = ignore   # ignore/add/remove/force
nl_func_call_start_multi_line   = false    # true/false
nl_func_call_args_multi_line    = false    # true/false
nl_func_call_end_multi_line     = false    # true/false
nl_oc_msg_args                  = false    # true/false
nl_fdef_brace                   = add      # ignore/add/remove/force
nl_fdef_brace_cond              = ignore   # ignore/add/remove/force
nl_cpp_ldef_brace               = ignore   # ignore/add/remove/force
nl_return_expr                  = ignore   # ignore/add/remove/force
nl_after_semicolon              = true     # true/false
nl_paren_dbrace_open            = ignore   # ignore/add/remove/force
nl_type_brace_init_lst          = ignore   # ignore/add/remove/force
nl_type_brace_init_lst_open     = ignore   # ignore/add/remove/force
nl_type_brace_init_lst_close    = ignore   # ignore/add/remove/force
nl_after_brace_open             = true     # true/false
nl_after_brace_open_cmt         = false    # true/false
nl_after_vbrace_open            = false    # true/false
nl_after_vbrace_open_empty      = false    # true/false
nl_after_brace_close            = true     # true/false
nl_after_vbrace_close           = false    # true/false
nl_brace_struct_var             = ignore   # ignore/add/remove/force
nl_define_macro                 = false    # true/false
nl_squeeze_paren_close          = false    # true/false
nl_squeeze_ifdef                = true     # true/false
nl_squeeze_ifdef_top_level      = false    # true/false
nl_before_if                    = force    # ignore/add/remove/force
nl_after_if                     = force    # ignore/add/remove/force
nl_before_for                   = force    # ignore/add/remove/force
nl_after_for                    = force    # ignore/add/remove/force
nl_before_while                 = force    # ignore/add/remove/force
nl_after_while                  = force    # ignore/add/remove/force
nl_before_switch                = force    # ignore/add/remove/force
nl_after_switch                 = force    # ignore/add/remove/force
nl_before_synchronized          = ignore   # ignore/add/remove/force
nl_after_synchronized           = ignore   # ignore/add/remove/force
nl_before_do                    = force    # ignore/add/remove/force
nl_after_do                     = force    # ignore/add/remove/force
nl_before_return                = false    # true/false
nl_after_return                 = true     # true/false
nl_ds_struct_enum_cmt           = false    # true/false
nl_ds_struct_enum_close_brace   = false    # true/false
nl_class_colon                  = ignore   # ignore/add/remove/force
nl_constr_colon                 = ignore   # ignore/add/remove/force
nl_namespace_two_to_one_liner   = false    # true/false
nl_create_if_one_liner          = false    # true/false
nl_create_for_one_liner         = false    # true/false
nl_create_while_one_liner       = false    # true/false
nl_create_func_def_one_liner    = false    # true/false
nl_split_if_one_liner           = false    # true/false
nl_split_for_one_liner          = false    # true/false
nl_split_while_one_liner        = false    # true/false
nl_max                          = 4        # unsigned number
nl_max_blank_in_func            = 0        # unsigned number
nl_before_func_body_proto       = 0        # unsigned number
nl_before_func_body_def         = 0        # unsigned number
nl_before_func_class_proto      = 0        # unsigned number
nl_before_func_class_def        = 0        # unsigned number
nl_after_func_proto             = 0        # unsigned number
nl_after_func_proto_group       = 1        # unsigned number
nl_after_func_class_proto       = 0        # unsigned number
nl_after_func_class_proto_group = 0        # unsigned number
nl_class_leave_one_liner_groups = false    # true/false
nl_after_func_body              = 0        # unsigned number
nl_after_func_body_class        = 2        # unsigned number
nl_after_func_body_one_liner    = 0        # unsigned number
nl_func_var_def_blk             = 1        # unsigned number
nl_typedef_blk_start            = 0        # unsigned number
nl_typedef_blk_end              = 0        # unsigned number
nl_typedef_blk_in               = 0        # unsigned number
nl_var_def_blk_start            = 0        # unsigned number
nl_var_def_blk_end              = 0        # unsigned number
nl_var_def_blk_in               = 0        # unsigned number
nl_before_block_comment         = 2        # unsigned number
nl_before_c_comment             = 0        # unsigned number
nl_before_cpp_comment           = 0        # unsigned number
nl_after_multiline_comment      = false    # true/false
nl_after_label_colon            = false    # true/false
nl_after_struct                 = 0        # unsigned number
nl_before_class                 = 0        # unsigned number
nl_after_class                  = 0        # unsigned number
nl_before_access_spec           = 0        # unsigned number
nl_after_access_spec            = 0        # unsigned number
nl_comment_func_def             = 0        # unsigned number
nl_after_try_catch_finally      = 0        # unsigned number
nl_around_cs_property           = 0        # unsigned number
nl_between_get_set              = 0        # unsigned number
nl_property_brace               = ignore   # ignore/add/remove/force
nl_inside_namespace             = 0        # unsigned number
eat_blanks_after_open_brace     = true     # true/false
eat_blanks_before_close_brace   = true     # true/false
nl_remove_extra_newlines        = 0        # unsigned number
nl_after_annotation             = ignore   # ignore/add/remove/force
nl_between_annotation           = ignore   # ignore/add/remove/force
pos_arith                       = ignore   # ignore/break/force/lead/trail/join/lead_break/lead_force/trail_break/trail_force
pos_assign                      = ignore   # ignore/break/force/lead/trail/join/lead_break/lead_force/trail_break/trail_force
pos_bool                        = trail    # ignore/break/force/lead/trail/join/lead_break/lead_force/trail_break/trail_force
pos_compare                     = ignore   # ignore/break/force/lead/trail/join/lead_break/lead_force/trail_break/trail_force
pos_conditional                 = ignore   # ignore/break/force/lead/trail/join/lead_break/lead_force/trail_break/trail_force
pos_comma                       = ignore   # ignore/break/force/lead/trail/join/lead_break/lead_force/trail_break/trail_force
pos_enum_comma                  = ignore   # ignore/break/force/lead/trail/join/lead_break/lead_force/trail_break/trail_force
pos_class_comma                 = ignore   # ignore/break/force/lead/trail/join/lead_break/lead_force/trail_break/trail_force
pos_constr_comma                = ignore   # ignore/break/force/lead/trail/join/lead_break/lead_force/trail_break/trail_force
pos_class_colon                 = ignore   # ignore/break/force/lead/trail/join/lead_break/lead_force/trail_break/trail_force
pos_constr_colon                = ignore   # ignore/break/force/lead/trail/join/lead_break/lead_force/trail_break/trail_force
code_width                      = 0        # unsigned number
ls_for_split_full               = false    # true/false
ls_func_split_full              = false    # true/false
ls_code_width                   = false    # true/false
align_keep_tabs                 = false    # true/false
align_with_tabs                 = false    # true/false
align_on_tabstop                = false    # true/false
align_number_right              = false    # true/false
align_keep_extra_space          = false    # true/false
align_func_params               = false    # true/false
align_func_params_span          = 0        # unsigned number
align_func_params_thresh        = 0        # number
align_func_params_gap           = 0        # unsigned number
align_constr_value_span         = 0        # unsigned number
align_constr_value_thresh       = 0        # number
align_constr_value_gap          = 0        # unsigned number
align_same_func_call_params     = false    # true/false
align_same_func_call_params_span = 0        # unsigned number
align_same_func_call_params_thresh = 0        # number
align_var_def_span              = 0        # unsigned number
align_var_def_star_style        = 0        # unsigned number
align_var_def_amp_style         = 1        # unsigned number
align_var_def_thresh            = 16       # number
align_var_def_gap               = 0        # unsigned number
align_var_def_colon             = false    # true/false
align_var_def_colon_gap         = 0        # unsigned number
align_var_def_attribute         = false    # true/false
align_var_def_inline            = false    # true/false
align_assign_span               = 0        # unsigned number
align_assign_func_proto_span    = 0        # unsigned number
align_assign_thresh             = 12       # number
align_assign_decl_func          = 0        # unsigned number
align_enum_equ_span             = 0        # unsigned number
align_enum_equ_thresh           = 0        # number
align_var_class_span            = 0        # unsigned number
align_var_class_thresh          = 0        # number
align_var_class_gap             = 0        # unsigned number
align_var_struct_span           = 0        # unsigned number
align_var_struct_thresh         = 0        # number
align_var_struct_gap            = 0        # unsigned number
align_struct_init_span          = 3        # unsigned number
align_typedef_span              = 5        # unsigned number
align_typedef_gap               = 3        # unsigned number
align_typedef_func              = 0        # unsigned number
align_typedef_star_style        = 1        # unsigned number
align_typedef_amp_style         = 1        # unsigned number
align_right_cmt_span            = 3        # unsigned number
align_right_cmt_gap             = 0        # unsigned number
align_right_cmt_mix             = false    # true/false
align_right_cmt_same_level      = false    # true/false
align_right_cmt_at_col          = 0        # unsigned number
align_func_proto_span           = 0        # unsigned number
align_func_proto_thresh         = 0        # number
align_func_proto_gap            = 0        # unsigned number
align_on_operator               = false    # true/false
align_mix_var_proto             = false    # true/false
align_single_line_func          = false    # true/false
align_single_line_brace         = false    # true/false
align_single_line_brace_gap     = 0        # unsigned number
align_oc_msg_spec_span          = 0        # unsigned number
align_nl_cont                   = true     # true/false
align_pp_define_together        = false    # true/false
align_pp_define_span            = 3        # unsigned number
align_pp_define_gap             = 4        # unsigned number
align_left_shift                = true     # true/false
align_asm_colon                 = false    # true/false
align_oc_msg_colon_span         = 0        # unsigned number
align_oc_msg_colon_first        = false    # true/false
align_oc_decl_colon             = false    # true/false
cmt_width                       = 0        # unsigned number
cmt_reflow_mode                 = 0        # unsigned number
cmt_convert_tab_to_spaces       = false    # true/false
cmt_indent_multi                = true     # true/false
cmt_c_group                     = false    # true/false
cmt_c_nl_start                  = false    # true/false
cmt_c_nl_end                    = false    # true/false
cmt_cpp_to_c                    = true     # true/false
cmt_cpp_group                   = false    # true/false
cmt_cpp_nl_start                = false    # true/false
cmt_cpp_nl_end                  = false    # true/false
cmt_star_cont                   = true     # true/false
cmt_sp_before_star_cont         = 0        # unsigned number
cmt_sp_after_star_cont          = 0        # unsigned number
cmt_multi_check_last            = true     # true/false
cmt_multi_first_len_minimum     = 4        # unsigned number
cmt_insert_file_header          = ""         # string
cmt_insert_file_footer          = ""         # string
cmt_insert_func_header          = ""         # string
cmt_insert_class_header         = ""         # string
cmt_insert_oc_msg_header        = ""         # string
cmt_insert_before_preproc       = false    # true/false
cmt_insert_before_inlines       = true     # true/false
cmt_insert_before_ctor_dtor     = false    # true/false
mod_full_brace_do               = add      # ignore/add/remove/force
mod_full_brace_for              = add      # ignore/add/remove/force
mod_full_brace_function         = ignore   # ignore/add/remove/force
mod_full_brace_if               = add      # ignore/add/remove/force
mod_full_brace_if_chain         = false    # true/false
mod_full_brace_if_chain_only    = false    # true/false
mod_full_brace_while            = add      # ignore/add/remove/force
mod_full_brace_using            = ignore   # ignore/add/remove/force
mod_full_brace_nl               = 0        # unsigned number
mod_full_brace_nl_block_rem_mlcond = false    # true/false
mod_paren_on_return             = ignore   # ignore/add/remove/force
mod_pawn_semicolon              = false    # true/false
mod_full_paren_if_bool          = true     # true/false
mod_remove_extra_semicolon      = true     # true/false
mod_add_long_function_closebrace_comment = 0        # unsigned number
mod_add_long_namespace_closebrace_comment = 0        # unsigned number
mod_add_long_class_closebrace_comment = 0        # unsigned number
mod_add_long_switch_closebrace_comment = 0        # unsigned number
mod_add_long_ifdef_endif_comment = 10       # unsigned number
mod_add_long_ifdef_else_comment = 10       # unsigned number
mod_sort_import                 = false    # true/false
mod_sort_using                  = false    # true/false
mod_sort_include                = false    # true/false
mod_move_case_break             = false    # true/false
mod_case_brace                  = remove   # ignore/add/remove/force
mod_remove_empty_return         = true     # true/false
mod_enum_last_comma             = ignore   # ignore/add/remove/force
mod_sort_oc_properties          = false    # true/false
mod_sort_oc_property_class_weight = 0        # number
mod_sort_oc_property_thread_safe_weight = 0        # number
mod_sort_oc_property_readwrite_weight = 0        # number
mod_sort_oc_property_reference_weight = 0        # number
mod_sort_oc_property_getter_weight = 0        # number
mod_sort_oc_property_setter_weight = 0        # number
mod_sort_oc_property_nullability_weight = 0        # number
pp_indent                       = force    # ignore/add/remove/force
pp_indent_at_level              = true     # true/false
pp_indent_count                 = 4        # unsigned number
pp_space                        = remove   # ignore/add/remove/force
pp_space_count                  = 0        # unsigned number
pp_indent_region                = 0        # number
pp_region_indent_code           = false    # true/false
pp_indent_if                    = 0        # number
pp_if_indent_code               = true     # true/false
pp_define_at_level              = false    # true/false
pp_ignore_define_body           = false    # true/false
pp_indent_case                  = true     # true/false
pp_indent_func_def              = true     # true/false
pp_indent_extern                = true     # true/false
pp_indent_brace                 = false    # true/false
include_category_0              = ""         # string
include_category_1              = ""         # string
include_category_2              = ""         # string
use_indent_func_call_param      = true     # true/false
use_indent_continue_only_once   = false    # true/false
indent_cpp_lambda_only_once     = false    # true/false
use_options_overriding_for_qt_macros = true     # true/false
warn_level_tabs_found_in_verbatim_string_literals = 2        # unsigned number
