syntax = "proto3";

import "constants.proto";

message CmdCtrlReset {

}

message RespCtrlReset {

}

message CmdCtrlReprov {

}

message RespCtrlReprov{

}

enum WiFiCtrlMsgType {
    TypeCtrlReserved = 0;
    TypeCmdCtrlReset = 1;
    TypeRespCtrlReset = 2;
    TypeCmdCtrlReprov = 3;
    TypeRespCtrlReprov = 4;
}

message WiFiCtrlPayload {
    WiFiCtrlMsgType msg = 1;
    Status status = 2;
    oneof payload {
        CmdCtrlReset cmd_ctrl_reset = 11;
        RespCtrlReset resp_ctrl_reset = 12;
        CmdCtrlReprov cmd_ctrl_reprov = 13;
        RespCtrlReprov resp_ctrl_reprov = 14;
    }
}
