syntax = "proto3";

enum WifiStationState {
    Connected = 0;
    Connecting = 1;
    Disconnected = 2;
    ConnectionFailed = 3;
}

enum WifiConnectFailedReason {
    AuthError = 0;
    NetworkNotFound = 1;
}

message WifiAttemptFailed {
    uint32 attempts_remaining = 1;
}

enum WifiAuthMode {
    Open = 0;
    WEP  = 1;
    WPA_PSK = 2;
    WPA2_PSK = 3;
    WPA_WPA2_PSK = 4;
    WPA2_ENTERPRISE = 5;
    WPA3_PSK = 6;
    WPA2_WPA3_PSK = 7;
}

message WifiConnectedState {
    string ip4_addr = 1;
    WifiAuthMode auth_mode = 2;
    bytes ssid = 3;
    bytes bssid = 4;
    int32 channel = 5;
}
