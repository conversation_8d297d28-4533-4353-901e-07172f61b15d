# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: wifi_ctrl.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


import constants_pb2 as constants__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x0fwifi_ctrl.proto\x1a\x0f\x63onstants.proto\"\x0e\n\x0c\x43mdCtrlReset\"\x0f\n\rRespCtrlReset\"\x0f\n\rCmdCtrlReprov\"\x10\n\x0eRespCtrlReprov\"\x80\x02\n\x0fWiFiCtrlPayload\x12\x1d\n\x03msg\x18\x01 \x01(\x0e\x32\x10.WiFiCtrlMsgType\x12\x17\n\x06status\x18\x02 \x01(\x0e\x32\x07.Status\x12\'\n\x0e\x63md_ctrl_reset\x18\x0b \x01(\x0b\x32\r.CmdCtrlResetH\x00\x12)\n\x0fresp_ctrl_reset\x18\x0c \x01(\x0b\x32\x0e.RespCtrlResetH\x00\x12)\n\x0f\x63md_ctrl_reprov\x18\r \x01(\x0b\x32\x0e.CmdCtrlReprovH\x00\x12+\n\x10resp_ctrl_reprov\x18\x0e \x01(\x0b\x32\x0f.RespCtrlReprovH\x00\x42\t\n\x07payload*\x83\x01\n\x0fWiFiCtrlMsgType\x12\x14\n\x10TypeCtrlReserved\x10\x00\x12\x14\n\x10TypeCmdCtrlReset\x10\x01\x12\x15\n\x11TypeRespCtrlReset\x10\x02\x12\x15\n\x11TypeCmdCtrlReprov\x10\x03\x12\x16\n\x12TypeRespCtrlReprov\x10\x04\x62\x06proto3')

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'wifi_ctrl_pb2', globals())
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _WIFICTRLMSGTYPE._serialized_start=364
  _WIFICTRLMSGTYPE._serialized_end=495
  _CMDCTRLRESET._serialized_start=36
  _CMDCTRLRESET._serialized_end=50
  _RESPCTRLRESET._serialized_start=52
  _RESPCTRLRESET._serialized_end=67
  _CMDCTRLREPROV._serialized_start=69
  _CMDCTRLREPROV._serialized_end=84
  _RESPCTRLREPROV._serialized_start=86
  _RESPCTRLREPROV._serialized_end=102
  _WIFICTRLPAYLOAD._serialized_start=105
  _WIFICTRLPAYLOAD._serialized_end=361
# @@protoc_insertion_point(module_scope)
