/* Generated by the protocol buffer compiler.  DO NOT EDIT! */
/* Generated from: wifi_ctrl.proto */

#ifndef PROTOBUF_C_wifi_5fctrl_2eproto__INCLUDED
#define PROTOBUF_C_wifi_5fctrl_2eproto__INCLUDED

#include <protobuf-c/protobuf-c.h>

PROTOBUF_C__BEGIN_DECLS

#if PROTOBUF_C_VERSION_NUMBER < 1003000
# error This file was generated by a newer version of protoc-c which is incompatible with your libprotobuf-c headers. Please update your headers.
#elif 1004001 < PROTOBUF_C_MIN_COMPILER_VERSION
# error This file was generated by an older version of protoc-c which is incompatible with your libprotobuf-c headers. Please regenerate this file with a newer version of protoc-c.
#endif

#include "constants.pb-c.h"

typedef struct CmdCtrlReset CmdCtrlReset;
typedef struct RespCtrlReset RespCtrlReset;
typedef struct CmdCtrlReprov CmdCtrlReprov;
typedef struct RespCtrlReprov RespCtrlReprov;
typedef struct WiFiCtrlPayload WiFiCtrlPayload;


/* --- enums --- */

typedef enum _WiFiCtrlMsgType {
  WI_FI_CTRL_MSG_TYPE__TypeCtrlReserved = 0,
  WI_FI_CTRL_MSG_TYPE__TypeCmdCtrlReset = 1,
  WI_FI_CTRL_MSG_TYPE__TypeRespCtrlReset = 2,
  WI_FI_CTRL_MSG_TYPE__TypeCmdCtrlReprov = 3,
  WI_FI_CTRL_MSG_TYPE__TypeRespCtrlReprov = 4
    PROTOBUF_C__FORCE_ENUM_TO_BE_INT_SIZE(WI_FI_CTRL_MSG_TYPE)
} WiFiCtrlMsgType;

/* --- messages --- */

struct  CmdCtrlReset
{
  ProtobufCMessage base;
};
#define CMD_CTRL_RESET__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&cmd_ctrl_reset__descriptor) \
     }


struct  RespCtrlReset
{
  ProtobufCMessage base;
};
#define RESP_CTRL_RESET__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&resp_ctrl_reset__descriptor) \
     }


struct  CmdCtrlReprov
{
  ProtobufCMessage base;
};
#define CMD_CTRL_REPROV__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&cmd_ctrl_reprov__descriptor) \
     }


struct  RespCtrlReprov
{
  ProtobufCMessage base;
};
#define RESP_CTRL_REPROV__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&resp_ctrl_reprov__descriptor) \
     }


typedef enum {
  WI_FI_CTRL_PAYLOAD__PAYLOAD__NOT_SET = 0,
  WI_FI_CTRL_PAYLOAD__PAYLOAD_CMD_CTRL_RESET = 11,
  WI_FI_CTRL_PAYLOAD__PAYLOAD_RESP_CTRL_RESET = 12,
  WI_FI_CTRL_PAYLOAD__PAYLOAD_CMD_CTRL_REPROV = 13,
  WI_FI_CTRL_PAYLOAD__PAYLOAD_RESP_CTRL_REPROV = 14
    PROTOBUF_C__FORCE_ENUM_TO_BE_INT_SIZE(WI_FI_CTRL_PAYLOAD__PAYLOAD__CASE)
} WiFiCtrlPayload__PayloadCase;

struct  WiFiCtrlPayload
{
  ProtobufCMessage base;
  WiFiCtrlMsgType msg;
  Status status;
  WiFiCtrlPayload__PayloadCase payload_case;
  union {
    CmdCtrlReset *cmd_ctrl_reset;
    RespCtrlReset *resp_ctrl_reset;
    CmdCtrlReprov *cmd_ctrl_reprov;
    RespCtrlReprov *resp_ctrl_reprov;
  };
};
#define WI_FI_CTRL_PAYLOAD__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&wi_fi_ctrl_payload__descriptor) \
    , WI_FI_CTRL_MSG_TYPE__TypeCtrlReserved, STATUS__Success, WI_FI_CTRL_PAYLOAD__PAYLOAD__NOT_SET, {0} }


/* CmdCtrlReset methods */
void   cmd_ctrl_reset__init
                     (CmdCtrlReset         *message);
size_t cmd_ctrl_reset__get_packed_size
                     (const CmdCtrlReset   *message);
size_t cmd_ctrl_reset__pack
                     (const CmdCtrlReset   *message,
                      uint8_t             *out);
size_t cmd_ctrl_reset__pack_to_buffer
                     (const CmdCtrlReset   *message,
                      ProtobufCBuffer     *buffer);
CmdCtrlReset *
       cmd_ctrl_reset__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   cmd_ctrl_reset__free_unpacked
                     (CmdCtrlReset *message,
                      ProtobufCAllocator *allocator);
/* RespCtrlReset methods */
void   resp_ctrl_reset__init
                     (RespCtrlReset         *message);
size_t resp_ctrl_reset__get_packed_size
                     (const RespCtrlReset   *message);
size_t resp_ctrl_reset__pack
                     (const RespCtrlReset   *message,
                      uint8_t             *out);
size_t resp_ctrl_reset__pack_to_buffer
                     (const RespCtrlReset   *message,
                      ProtobufCBuffer     *buffer);
RespCtrlReset *
       resp_ctrl_reset__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   resp_ctrl_reset__free_unpacked
                     (RespCtrlReset *message,
                      ProtobufCAllocator *allocator);
/* CmdCtrlReprov methods */
void   cmd_ctrl_reprov__init
                     (CmdCtrlReprov         *message);
size_t cmd_ctrl_reprov__get_packed_size
                     (const CmdCtrlReprov   *message);
size_t cmd_ctrl_reprov__pack
                     (const CmdCtrlReprov   *message,
                      uint8_t             *out);
size_t cmd_ctrl_reprov__pack_to_buffer
                     (const CmdCtrlReprov   *message,
                      ProtobufCBuffer     *buffer);
CmdCtrlReprov *
       cmd_ctrl_reprov__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   cmd_ctrl_reprov__free_unpacked
                     (CmdCtrlReprov *message,
                      ProtobufCAllocator *allocator);
/* RespCtrlReprov methods */
void   resp_ctrl_reprov__init
                     (RespCtrlReprov         *message);
size_t resp_ctrl_reprov__get_packed_size
                     (const RespCtrlReprov   *message);
size_t resp_ctrl_reprov__pack
                     (const RespCtrlReprov   *message,
                      uint8_t             *out);
size_t resp_ctrl_reprov__pack_to_buffer
                     (const RespCtrlReprov   *message,
                      ProtobufCBuffer     *buffer);
RespCtrlReprov *
       resp_ctrl_reprov__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   resp_ctrl_reprov__free_unpacked
                     (RespCtrlReprov *message,
                      ProtobufCAllocator *allocator);
/* WiFiCtrlPayload methods */
void   wi_fi_ctrl_payload__init
                     (WiFiCtrlPayload         *message);
size_t wi_fi_ctrl_payload__get_packed_size
                     (const WiFiCtrlPayload   *message);
size_t wi_fi_ctrl_payload__pack
                     (const WiFiCtrlPayload   *message,
                      uint8_t             *out);
size_t wi_fi_ctrl_payload__pack_to_buffer
                     (const WiFiCtrlPayload   *message,
                      ProtobufCBuffer     *buffer);
WiFiCtrlPayload *
       wi_fi_ctrl_payload__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   wi_fi_ctrl_payload__free_unpacked
                     (WiFiCtrlPayload *message,
                      ProtobufCAllocator *allocator);
/* --- per-message closures --- */

typedef void (*CmdCtrlReset_Closure)
                 (const CmdCtrlReset *message,
                  void *closure_data);
typedef void (*RespCtrlReset_Closure)
                 (const RespCtrlReset *message,
                  void *closure_data);
typedef void (*CmdCtrlReprov_Closure)
                 (const CmdCtrlReprov *message,
                  void *closure_data);
typedef void (*RespCtrlReprov_Closure)
                 (const RespCtrlReprov *message,
                  void *closure_data);
typedef void (*WiFiCtrlPayload_Closure)
                 (const WiFiCtrlPayload *message,
                  void *closure_data);

/* --- services --- */


/* --- descriptors --- */

extern const ProtobufCEnumDescriptor    wi_fi_ctrl_msg_type__descriptor;
extern const ProtobufCMessageDescriptor cmd_ctrl_reset__descriptor;
extern const ProtobufCMessageDescriptor resp_ctrl_reset__descriptor;
extern const ProtobufCMessageDescriptor cmd_ctrl_reprov__descriptor;
extern const ProtobufCMessageDescriptor resp_ctrl_reprov__descriptor;
extern const ProtobufCMessageDescriptor wi_fi_ctrl_payload__descriptor;

PROTOBUF_C__END_DECLS


#endif  /* PROTOBUF_C_wifi_5fctrl_2eproto__INCLUDED */
