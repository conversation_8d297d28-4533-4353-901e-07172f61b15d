磨损均衡 API
==================

:link_to_translation:`en:[English]`

概述
--------

{IDF_TARGET_NAME} 所使用的 flash，特别是 SPI flash，多数具备扇区结构，且每个扇区仅允许有限次数的擦除/修改操作。为了避免过度使用某一扇区，乐鑫提供了磨损均衡组件，无需用户介入即可帮助用户均衡各个扇区之间的磨损。

磨损均衡组件包含了通过分区组件对外部 SPI flash 进行数据读取、写入、擦除和存储器映射相关的 API 函数。磨损均衡组件还具有软件上更高级别的 API 函数，与 :doc:`FAT 文件系统 </api-reference/storage/fatfs>` 协同工作。

磨损均衡组件与 FAT 文件系统组件共用 FAT 文件系统的扇区，扇区大小为 4096 字节，是标准 flash 扇区的大小。在这种模式下，磨损均衡组件性能达到最佳，但需要在 RAM 中占用更多内存。

为了节省内存，磨损均衡组件还提供了另外两种模式，均使用 512 字节大小的扇区：

- **性能模式**：先将数据保存在 RAM 中，擦除扇区，然后将数据存储回 flash。如果设备在扇区擦写过程中突然断电，则整个扇区（4096 字节）数据将全部丢失。
- **安全模式**：数据先保存在 flash 中空余扇区，擦除扇区后，数据即存储回去。如果设备断电，上电后可立即恢复数据。

设备默认设置如下：

- 定义扇区大小为 512 字节 
- 默认使用性能模式

您可以使用配置菜单更改设置。

磨损均衡组件不会将数据缓存在 RAM 中。写入和擦除函数直接修改 flash，函数返回后，flash 即完成修改。


磨损均衡访问 API
-----------------------------------

处理 flash 数据常用的 API 如下所示：

- ``wl_mount`` - 为指定分区挂载并初始化磨损均衡模块
- ``wl_unmount`` - 卸载分区并释放磨损均衡模块
- ``wl_erase_range`` - 擦除 flash 中指定的地址范围
- ``wl_write`` - 将数据写入分区
- ``wl_read`` - 从分区读取数据
- ``wl_size`` - 返回可用内存的大小（以字节为单位）
- ``wl_sector_size`` - 返回一个扇区的大小

请尽量避免直接使用原始磨损均衡函数，建议您使用文件系统特定的函数。


内存大小
-----------

内存大小是根据分区参数在磨损均衡模块中计算所得，由于模块使用 flash 部分扇区存储内部数据，因此计算所得内存大小有少许偏差。
