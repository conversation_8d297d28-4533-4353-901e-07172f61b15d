/*
 * SPDX-FileCopyrightText: 2020-2021 Espressif Systems (Shanghai) CO LTD
 *
 * SPDX-License-Identifier: Apache-2.0
 */

#include "unity.h"
#include "esp_efuse.h"
#include "esp_efuse_table.h"
#include "esp_log.h"
#include "esp_hmac.h"

#if CONFIG_ESP_SECURITY_ENABLE_FPGA_TESTS

/* Allow testing varying message lengths (truncating the same message)
   for various results */
typedef struct {
    int msglen;
    uint8_t result[32];
} hmac_result;

static void setup_keyblock(esp_efuse_block_t key_block, esp_efuse_purpose_t purpose)
{
    const uint8_t key_data[32] = {
        1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24,
        25, 26, 27, 28, 29, 30, 31, 32
    };
    esp_err_t status = esp_efuse_write_key(key_block, purpose, key_data, sizeof(key_data));

    if (status == ESP_OK) {
        printf("Written key!\n");
    } else if (ESP_ERR_EFUSE_REPEATED_PROG) {
        printf("Key written already.\n");
    } else {
        printf("ERROR while writing key.\n");
    }
}

TEST_CASE("HMAC 'downstream' JTAG Enable mode", "[hw_crypto]")
{
    int ets_status;

    setup_keyblock(EFUSE_BLK_KEY4, ESP_EFUSE_KEY_PURPOSE_HMAC_DOWN_JTAG);

    // Results calculated with Python:
    //
    // import hmac, hashlib, binascii
    // key = b"".join([chr(x).encode() for x in range(1,33)])
    // ", ".join("0x%x" % x for x in hmac.HMAC(key, b"\x00" * 32, hashlib.sha256).digest() )
    const uint8_t token_data[32] = {
        0xb2, 0xa4, 0x9b, 0x1c, 0xce, 0x1b, 0xe9, 0x22, 0xbb, 0x7e, 0x43, 0x12, 0x77, 0x41, 0x3e, 0x3e,
        0x8e, 0x6c, 0x3e, 0x8e, 0x6e, 0x17, 0x62, 0x5c, 0x50, 0xac, 0x66, 0xa9, 0xa8, 0x57, 0x94, 0x9b
    };

    TEST_ASSERT_MESSAGE(ESP_OK == esp_efuse_batch_write_begin(),
                        "Error programming security efuse.\n");

    ets_status = esp_efuse_write_field_cnt(ESP_EFUSE_SOFT_DIS_JTAG, ESP_EFUSE_SOFT_DIS_JTAG[0]->bit_count);

    TEST_ASSERT_MESSAGE(ets_status == ESP_OK || ets_status == ESP_ERR_EFUSE_CNT_IS_FULL,
                        "JTAG Disable temporarily failed.\n");

    TEST_ASSERT_MESSAGE(ESP_OK == esp_efuse_batch_write_commit(),
                        "Error programming security efuse.\n");

    TEST_ASSERT_EQUAL_HEX32_MESSAGE(ESP_OK, esp_hmac_jtag_enable(HMAC_KEY4, token_data),
                                    "JTAG should be re-enabled now, please manually verify");
}

TEST_CASE("HMAC 'downstream' JTAG Disable", "[hw_crypto]")
{
    TEST_ASSERT_EQUAL_HEX32_MESSAGE(ESP_OK, esp_hmac_jtag_disable(),
                                    "JTAG should be disabled now, please manually verify");
}

TEST_CASE("HMAC 'upstream' MAC generation with zeroes", "[hw_crypto]")
{
    uint8_t hmac[32];

    setup_keyblock(EFUSE_BLK_KEY5, ESP_EFUSE_KEY_PURPOSE_HMAC_UP);

    const uint8_t zeroes[128] = { };
    // Produce the HMAC of various numbers of zeroes
    //
    // Results calculated with Python:
    //
    // import hmac, hashlib, binascii
    // key = b"".join([chr(x).encode() for x in range(1,33)])
    // ", ".join("0x%x" % x for x in hmac.HMAC(key, b"\x00" * 128, hashlib.sha256).digest() )

    static const hmac_result zero_results[] = {
        {
            .msglen = 64,
            .result = {
                0x4f, 0x34, 0x31, 0x8a, 0x45, 0x74, 0x4d, 0x71, 0x53, 0xb7, 0x18, 0xf4, 0x78, 0x1c, 0xbb, 0x10,
                0x19, 0x60, 0xba, 0x9c, 0x8c, 0xe2, 0x3b, 0xc1, 0x1d, 0x79, 0xb6, 0x3c, 0xae, 0x0f, 0x30, 0xc8,
            },
        },
        {
            .msglen = 128,
            .result = {
                0x40, 0xd5, 0xb9, 0xe6, 0x25, 0x8f, 0x3c, 0xd0, 0x3f, 0xb9, 0x6c, 0xa3, 0xa7, 0x2b, 0x84, 0xe3,
                0x1d, 0x4b, 0x4e, 0x65, 0xf8, 0x7b, 0x3e, 0x3, 0x26, 0x2, 0xcd, 0x49, 0x73, 0xf0, 0xac, 0x25
            },
        },
        {
            .msglen = 48,
            .result = {
                0x84, 0x4e, 0x45, 0xcd, 0xb3, 0x8f, 0xf8, 0x96, 0xe7, 0xe7, 0x80, 0x48, 0x31, 0x89, 0x79, 0xa7,
                0x5d, 0x80, 0xd1, 0xbf, 0x3, 0xca, 0x9b, 0x78, 0x4f, 0x3b, 0x42, 0x80, 0xb9, 0x6, 0x19, 0x7d
            },
        },
        {
            .msglen = 32,
            .result = {
                0xb2, 0xa4, 0x9b, 0x1c, 0xce, 0x1b, 0xe9, 0x22, 0xbb, 0x7e, 0x43, 0x12, 0x77, 0x41, 0x3e, 0x3e,
                0x8e, 0x6c, 0x3e, 0x8e, 0x6e, 0x17, 0x62, 0x5c, 0x50, 0xac, 0x66, 0xa9, 0xa8, 0x57, 0x94, 0x9b
            },
        },
        {
            .msglen = 33,
            .result = {
                0x98, 0xd7, 0x44, 0xab, 0xbb, 0x89, 0xca, 0x51, 0x3e, 0x2, 0x8e, 0x5c, 0xa1, 0x61, 0x25, 0xd2,
                0x93, 0x3e, 0x85, 0x4b, 0x9f, 0x73, 0x63, 0x57, 0xab, 0xbc, 0x7a, 0x66, 0x51, 0xd2, 0x39, 0xb9
            },
        },
        {
            .msglen = 1,
            .result = {
                0xab, 0x7d, 0x90, 0x85, 0x8, 0xb3, 0xf3, 0x7, 0x45, 0x6c, 0x85, 0x40, 0xbf, 0xcd, 0xb4, 0x52,
                0x54, 0x2c, 0x2, 0xe0, 0x53, 0xdc, 0x16, 0x12, 0x90, 0xf1, 0x5b, 0x5b, 0xf8, 0x71, 0x65, 0x44
            },
        },
        {
            .msglen = 127,
            .result = {
                0x19, 0x38, 0x88, 0xb, 0x30, 0xac, 0xef, 0x4e, 0xd, 0x38, 0x7d, 0x7e, 0x42, 0x5c, 0x90, 0xc4,
                0x9b, 0xc1, 0xbd, 0x9e, 0x30, 0xc6, 0x16, 0x1f, 0x36, 0x7e, 0x46, 0xcd, 0xb2, 0xd7, 0x37, 0x70
            },
        },
        {
            .msglen = 126,
            .result = {
                0xf, 0xa4, 0xb5, 0x16, 0x3b, 0xf5, 0xe8, 0x6e, 0xaf, 0x38, 0xc6, 0x27, 0x9a, 0xc, 0x88, 0xaf,
                0xb5, 0x10, 0x75, 0x3d, 0x4a, 0x85, 0x10, 0x4e, 0x60, 0xe4, 0x61, 0x30, 0x8, 0x46, 0x98, 0xc7
            },
        },
        {
            .msglen = 125,
            .result = {
                0x3f, 0x1a, 0x90, 0x47, 0xeb, 0x44, 0xcc, 0x27, 0xfa, 0x22, 0xb3, 0x5d, 0xa2, 0x22, 0x30, 0x54,
                0x61, 0x15, 0xe5, 0x54, 0x55, 0x13, 0x7c, 0xb8, 0xc7, 0xc0, 0x28, 0xa4, 0xd4, 0xbc, 0x1c, 0xad
            },
        },
        {
            .msglen = 124,
            .result = {
                0x14, 0xdf, 0x13, 0xa2, 0xe4, 0xfd, 0xa3, 0xa8, 0x9b, 0x71, 0x78, 0x2e, 0x24, 0xb6, 0x61, 0x13,
                0xff, 0x6c, 0x6d, 0xe8, 0x95, 0xf9, 0x68, 0xb4, 0x92, 0x7c, 0xc9, 0xf7, 0x5e, 0x14, 0x44, 0x8
            },
        },
        {
            .msglen = 123,
            .result = {
                0x6, 0xd0, 0xe, 0xbe, 0x90, 0x3b, 0x52, 0x85, 0xd4, 0x25, 0x7e, 0xbe, 0x71, 0x92, 0xd0, 0xf0,
                0x6a, 0x99, 0x93, 0x64, 0xe6, 0x9a, 0x27, 0xfa, 0x57, 0xcb, 0x6f, 0x9f, 0x44, 0x30, 0xf5, 0xcc
            },
        },
        {
            .msglen = 122,
            .result = {
                0x76, 0x7a, 0x86, 0x80, 0x1e, 0x54, 0x11, 0xef, 0x2f, 0x4e, 0xf9, 0x7, 0xda, 0x42, 0xd6, 0x71,
                0x3b, 0xb9, 0x92, 0xfb, 0x8, 0x1d, 0xf2, 0x41, 0x96, 0x5f, 0x28, 0x10, 0x20, 0x1a, 0x7b, 0xef
            },
        },
        {
            .msglen = 121,
            .result = {
                0x59, 0xb0, 0xdb, 0x73, 0xee, 0x43, 0xb9, 0x63, 0x82, 0x36, 0x11, 0x5a, 0x6b, 0x46, 0x8, 0xbb,
                0x18, 0xdd, 0x74, 0x82, 0x8f, 0xf3, 0xb3, 0x5d, 0xd1, 0xad, 0xe, 0x8e, 0x77, 0x90, 0xde, 0x70
            },
        },
        {
            .msglen = 120,
            .result = {
                0xe0, 0x24, 0xc5, 0x2, 0x6b, 0xe, 0xe3, 0x9b, 0x1, 0x95, 0x6, 0x21, 0xc6, 0xad, 0x0, 0x72,
                0x36, 0x9, 0x75, 0xcd, 0x10, 0xa3, 0xf, 0xa2, 0xe5, 0xcd, 0x27, 0x6b, 0x95, 0x23, 0x6, 0x72
            },
        },
        {
            .msglen = 119,
            .result = {
                0x70, 0x4, 0x2c, 0x78, 0xc5, 0x40, 0x3f, 0xfb, 0x71, 0xfb, 0x3e, 0xbd, 0x9f, 0x4e, 0x2f, 0xf8,
                0x3c, 0x9b, 0xd1, 0xad, 0xee, 0xc8, 0x4f, 0x40, 0xec, 0x29, 0x5a, 0xb9, 0x9a, 0xa7, 0xe9, 0x51
            },
        },
        {
            .msglen = 118,
            .result = {
                0x1a, 0x4b, 0x49, 0xd3, 0x6, 0x1, 0x75, 0xca, 0x3, 0x12, 0x2e, 0x9a, 0xd4, 0xda, 0xb8, 0x23,
                0xf9, 0xa0, 0xa6, 0xbc, 0xbc, 0xcc, 0xa1, 0x6f, 0xd8, 0x3b, 0x2a, 0x37, 0xd3, 0xc3, 0xca, 0x5f
            },
        },
        {
            .msglen = 0,
            .result = {
                0x46, 0x24, 0x76, 0xa8, 0x97, 0xdd, 0xfd, 0xbd, 0x40, 0xd1, 0x42, 0xe, 0x8, 0xa5, 0xbc, 0xfe,
                0xeb, 0x25, 0xc3, 0xe2, 0xad, 0xe6, 0xa0, 0xa9, 0x8, 0x3b, 0x32, 0x7b, 0x9e, 0xf9, 0xfc, 0xa1
            },
        },
    };

    const size_t num_zero_results = sizeof(zero_results) / sizeof(hmac_result);

    for (int i = 0; i < num_zero_results; i++) {
        TEST_ESP_OK(esp_hmac_calculate(HMAC_KEY5, zeroes, zero_results[i].msglen, hmac));
        TEST_ASSERT_EQUAL_HEX8_ARRAY(zero_results[i].result, hmac, sizeof(hmac));
    }
}

TEST_CASE("HMAC 'upstream' MAC generation from data", "[hw_crypto]")
{
    uint8_t hmac[32];

    setup_keyblock(EFUSE_BLK_KEY5, ESP_EFUSE_KEY_PURPOSE_HMAC_UP);

    // 257 characters of pseudo-Latin from lipsum.com (not Copyright)
    const char *message = "Deleniti voluptas explicabo et assumenda. Sed et aliquid minus quis. Praesentium cupiditate quia nemo est. Laboriosam pariatur ut distinctio tenetur. Sunt architecto iure aspernatur soluta ut recusandae. Ut quibusdam occaecati ut qui sit dignissimos eaque..";

    // 256 different HMAC results for different length portions of the above. Generated as follows:
    //
    // import hmac, hashlib, binascii
    // key = b"".join([chr(x).encode() for x in range(1,33)])
    // assert len(message) == 257
    // for l in range(1, len(message)):
    //     print(" // %d" % l)
    //     mac = hmac.HMAC(key, message[:l], hashlib.sha256).digest()
    //     print("{ " + ", ".join("0x%x" % ord(x) for x in mac) + " }, ")
    //
    // (Note: the zero length case is handled in the other unit test.)
    static const  hmac_result results[] = {
        {
            .msglen = 1,
            .result = { 0xf2, 0x1a, 0x8e, 0x60, 0xea, 0xd9, 0x36, 0xd1, 0xc2, 0x74, 0x24, 0xae, 0x6, 0x2d, 0x81, 0x28, 0x16, 0xa6, 0x33, 0xca, 0x9d, 0x55, 0xc0, 0x82, 0x28, 0xd9, 0x79, 0x8f, 0x5a, 0xaa, 0xfd, 0x25 },
        },
        {
            .msglen = 2,
            .result = { 0x1f, 0x6, 0xfc, 0x68, 0x5c, 0xa8, 0xbf, 0x3e, 0x57, 0x6d, 0x2, 0x56, 0x4a, 0x35, 0x31, 0xbd, 0xeb, 0xf4, 0x5d, 0xd5, 0x71, 0xf9, 0x65, 0x7d, 0xa9, 0x32, 0x1d, 0x68, 0x21, 0xd2, 0x9b, 0xaf },
        },
        {
            .msglen = 3,
            .result = { 0x2b, 0xb4, 0x4e, 0x80, 0x6e, 0xd2, 0xcb, 0xee, 0x4b, 0x40, 0xfb, 0xf9, 0x61, 0x76, 0x78, 0x2a, 0xb1, 0xc8, 0xea, 0xa3, 0x3e, 0xf7, 0x24, 0x86, 0xa0, 0x73, 0xda, 0xe, 0xaf, 0x98, 0xb, 0xf0 },
        },
        {
            .msglen = 4,
            .result = { 0xf, 0x39, 0xd1, 0x1a, 0x3a, 0xe9, 0xa9, 0xf8, 0xe9, 0x39, 0xed, 0x1b, 0x32, 0x3, 0xbc, 0x24, 0x32, 0xe8, 0x12, 0xd7, 0xc4, 0xed, 0x2a, 0x8a, 0xed, 0x46, 0xbf, 0xa7, 0x21, 0x31, 0x37, 0xb5 },
        },
        {
            .msglen = 5,
            .result = { 0x6c, 0xfc, 0xf8, 0x9a, 0x56, 0xce, 0xe6, 0x1, 0x36, 0xca, 0x36, 0x37, 0xa8, 0xb9, 0xca, 0x99, 0x3b, 0xb1, 0xf8, 0x24, 0xa5, 0xf6, 0x20, 0x4d, 0xff, 0x8c, 0x60, 0xd3, 0x9, 0x92, 0xef, 0xb3 },
        },
        {
            .msglen = 6,
            .result = { 0x86, 0x99, 0x7, 0xd7, 0x5b, 0xb3, 0x93, 0x95, 0x3a, 0x9e, 0xf5, 0x95, 0x9d, 0x5e, 0x7b, 0xaf, 0x9b, 0x4b, 0x19, 0x35, 0x31, 0x34, 0xba, 0x29, 0xa, 0x56, 0xb, 0xe4, 0xa4, 0xbf, 0xb8, 0x9f },
        },
        {
            .msglen = 7,
            .result = { 0xa9, 0x9, 0x85, 0x26, 0x7b, 0x92, 0x23, 0xe7, 0x3d, 0x44, 0xa1, 0xcc, 0xba, 0x5c, 0xda, 0xdb, 0x5a, 0xe2, 0x76, 0x78, 0xd7, 0x42, 0x77, 0x4a, 0x8e, 0x2b, 0x79, 0x73, 0x4c, 0x29, 0x6, 0x1c },
        },
        {
            .msglen = 8,
            .result = { 0x26, 0x15, 0x1f, 0xe3, 0x53, 0xd9, 0x8, 0xc5, 0xf0, 0x11, 0x7, 0x7, 0x5f, 0x8c, 0xf6, 0x61, 0xd2, 0x21, 0x16, 0xe4, 0xb9, 0x61, 0x29, 0x1c, 0x4d, 0x2b, 0x6d, 0x9a, 0x88, 0x8c, 0xdc, 0xa9 },
        },
        {
            .msglen = 9,
            .result = { 0x2, 0xf2, 0xe6, 0xc8, 0x9c, 0xdc, 0x1b, 0x64, 0xd6, 0x32, 0xc2, 0x48, 0x62, 0x51, 0x9c, 0x0, 0x90, 0xf4, 0xf1, 0x2a, 0x0, 0x2, 0xce, 0x32, 0xbb, 0x8f, 0x38, 0x9c, 0x8b, 0xaa, 0xdd, 0x5d },
        },
        {
            .msglen = 10,
            .result = { 0x44, 0x76, 0x8a, 0x53, 0xcf, 0xbb, 0xc1, 0xd0, 0x36, 0x96, 0xb5, 0xe9, 0xe, 0xfb, 0xce, 0xb3, 0x47, 0x21, 0xaa, 0xea, 0xac, 0x76, 0x54, 0x91, 0xc1, 0xcb, 0x88, 0x32, 0xb1, 0xea, 0xd5, 0x4c },
        },
        {
            .msglen = 11,
            .result = { 0x95, 0xf9, 0x78, 0x12, 0xe5, 0x7a, 0xf4, 0xc5, 0xee, 0x5e, 0x27, 0xe7, 0x5e, 0x8, 0x56, 0x60, 0x97, 0xc1, 0xee, 0xf, 0xf5, 0x24, 0x5c, 0x20, 0xbe, 0x95, 0x3c, 0xa2, 0xf5, 0x68, 0x69, 0x6f },
        },
        {
            .msglen = 12,
            .result = { 0x8e, 0x71, 0xa8, 0x23, 0x7a, 0x8, 0x6a, 0xf4, 0x2e, 0xab, 0x58, 0x56, 0x37, 0x55, 0x26, 0x57, 0x13, 0xc, 0x6b, 0x6b, 0x65, 0xb6, 0x4f, 0xec, 0xde, 0xc2, 0xe6, 0xb0, 0x34, 0xc0, 0x4a, 0xba },
        },
        {
            .msglen = 13,
            .result = { 0x75, 0xc8, 0x29, 0x60, 0x89, 0xb7, 0xba, 0xc7, 0x40, 0x18, 0x73, 0x8a, 0xa3, 0x92, 0xb2, 0x2c, 0x79, 0x74, 0x1c, 0xdc, 0xc0, 0x97, 0x14, 0xdb, 0x26, 0xcc, 0xad, 0x7f, 0x9d, 0x2f, 0xd, 0x5 },
        },
        {
            .msglen = 14,
            .result = { 0xe9, 0xf2, 0x97, 0x12, 0x4d, 0xc3, 0x22, 0xca, 0x7, 0xc, 0xac, 0xae, 0xcb, 0x63, 0xe2, 0x88, 0xa2, 0xf8, 0xb0, 0x94, 0xfc, 0x21, 0xf4, 0x69, 0x8e, 0xa0, 0x95, 0x3, 0x54, 0xa1, 0x61, 0xed },
        },
        {
            .msglen = 15,
            .result = { 0x34, 0xd4, 0x3e, 0xcd, 0xcc, 0xa, 0x5f, 0xaf, 0xf5, 0x3d, 0xb8, 0xaa, 0x18, 0x4a, 0x18, 0xef, 0x22, 0x75, 0x29, 0x17, 0x1b, 0x33, 0xf2, 0x50, 0x4e, 0x90, 0xd5, 0xa3, 0x10, 0xff, 0x79, 0xbc },
        },
        {
            .msglen = 16,
            .result = { 0xcf, 0x6, 0x59, 0x8d, 0x2d, 0x7b, 0xb, 0x3d, 0x1a, 0xa3, 0x9c, 0xfa, 0xa2, 0xf1, 0x88, 0x6, 0xd2, 0xb4, 0xb5, 0x2d, 0x4c, 0x56, 0x7, 0xf5, 0x20, 0xce, 0x9a, 0x79, 0x3a, 0x2e, 0x11, 0xfa },
        },
        {
            .msglen = 17,
            .result = { 0x55, 0x5f, 0x37, 0xd9, 0x7, 0x3a, 0x3a, 0x6b, 0x22, 0xf8, 0x1c, 0x8f, 0xd8, 0xf6, 0xf7, 0x18, 0x77, 0xf3, 0x52, 0x32, 0xc6, 0x9b, 0xe0, 0xc7, 0xa6, 0xf0, 0x6b, 0xb0, 0x6a, 0xad, 0xb2, 0x7e },
        },
        {
            .msglen = 18,
            .result = { 0xa0, 0x60, 0x25, 0x34, 0xd7, 0xe1, 0x66, 0x7e, 0xdd, 0xec, 0x8, 0x95, 0xb5, 0xd9, 0x2d, 0x4e, 0x29, 0x89, 0x39, 0xb9, 0xc1, 0xe4, 0xc1, 0x86, 0x10, 0x16, 0x5, 0x17, 0xd0, 0x6, 0x91, 0x2d },
        },
        {
            .msglen = 19,
            .result = { 0xb4, 0x45, 0xbc, 0xae, 0xb3, 0x5b, 0x61, 0xd3, 0x50, 0x8c, 0x38, 0x7a, 0x4c, 0x4c, 0xbc, 0x38, 0x89, 0x98, 0x75, 0x23, 0xa9, 0x92, 0xa4, 0xea, 0xfe, 0xe6, 0x88, 0x61, 0xb0, 0xf0, 0x8, 0x7b },
        },
        {
            .msglen = 20,
            .result = { 0x1d, 0x2e, 0x5c, 0x69, 0x7b, 0x2a, 0xa0, 0x9d, 0xe6, 0x5b, 0xc, 0x3, 0x53, 0x9, 0x66, 0x74, 0xd8, 0xf6, 0xe8, 0x87, 0x5d, 0xc7, 0x1, 0xf0, 0xce, 0xaf, 0xb1, 0x15, 0x34, 0x22, 0x8e, 0x83 },
        },
        {
            .msglen = 21,
            .result = { 0x8e, 0xd9, 0xb1, 0xc7, 0x99, 0x91, 0x1, 0x9a, 0xe, 0xfa, 0xd2, 0xed, 0xc4, 0xae, 0x47, 0xf2, 0xa2, 0x48, 0x53, 0xa8, 0x92, 0xff, 0xe4, 0xcc, 0x95, 0x5e, 0x25, 0x1a, 0x2a, 0x49, 0x6c, 0xfd },
        },
        {
            .msglen = 22,
            .result = { 0x14, 0xba, 0x32, 0xbe, 0x21, 0x6, 0x6e, 0x6a, 0x28, 0x2b, 0x4e, 0xfc, 0x97, 0xa5, 0x6, 0x32, 0x62, 0x7, 0xf3, 0x61, 0x41, 0x43, 0x5e, 0x34, 0x93, 0x0, 0xa8, 0xb3, 0x9, 0x55, 0x37, 0x3f },
        },
        {
            .msglen = 23,
            .result = { 0xa1, 0xf0, 0xe6, 0xf1, 0x9d, 0x5d, 0x30, 0x10, 0x17, 0xce, 0x39, 0x5e, 0x93, 0x2b, 0xe6, 0xeb, 0x6b, 0x5b, 0x64, 0x47, 0x65, 0xe8, 0x93, 0x2e, 0x39, 0x74, 0x6a, 0x71, 0xf3, 0xdb, 0xb6, 0x8d },
        },
        {
            .msglen = 24,
            .result = { 0xab, 0xf4, 0xe1, 0x78, 0xc1, 0x63, 0x95, 0xf2, 0x6c, 0x54, 0xc, 0xac, 0xce, 0x88, 0x17, 0xf6, 0x1c, 0x84, 0x5b, 0x26, 0xa3, 0x5c, 0xea, 0xf7, 0x66, 0xcb, 0x84, 0xed, 0xbe, 0x52, 0xd, 0x25 },
        },
        {
            .msglen = 25,
            .result = { 0xd8, 0x94, 0x77, 0xa7, 0x6c, 0x51, 0x30, 0x3c, 0xd7, 0x4f, 0xc4, 0x6e, 0x1a, 0x25, 0xf8, 0x87, 0x93, 0x49, 0x28, 0x6b, 0x1b, 0x3, 0x79, 0x5, 0x14, 0x15, 0xd1, 0xee, 0x51, 0x7b, 0x9f, 0x94 },
        },
        {
            .msglen = 26,
            .result = { 0xbb, 0x8a, 0x5f, 0x73, 0x8d, 0x4e, 0x8a, 0x11, 0x95, 0x5d, 0xf5, 0xcf, 0x25, 0xea, 0x79, 0x38, 0xc4, 0x4b, 0xb4, 0x6f, 0xa3, 0x1f, 0x18, 0x23, 0x73, 0x1e, 0x46, 0xcb, 0x5d, 0x97, 0xcf, 0x6c },
        },
        {
            .msglen = 27,
            .result = { 0xe2, 0xad, 0x4b, 0x4e, 0x43, 0xf1, 0x6d, 0x62, 0x1a, 0xb4, 0x65, 0xbb, 0xb3, 0x34, 0x8, 0xf7, 0x14, 0x14, 0xd2, 0x6f, 0x41, 0x8f, 0xa7, 0x6f, 0xab, 0x6e, 0x61, 0xe2, 0x5, 0x5b, 0x2a, 0xe6 },
        },
        {
            .msglen = 28,
            .result = { 0xd9, 0xea, 0x5, 0x72, 0x59, 0x12, 0xff, 0xb, 0x33, 0x87, 0x17, 0x9d, 0xb2, 0x9, 0x4f, 0xfc, 0xba, 0xd7, 0xc, 0x45, 0x3b, 0xbe, 0x6a, 0x12, 0x59, 0x38, 0x40, 0x30, 0x8c, 0xa4, 0xf, 0x7d },
        },
        {
            .msglen = 29,
            .result = { 0x29, 0xe, 0x6d, 0x59, 0x2a, 0xaf, 0x5f, 0x93, 0xc7, 0x97, 0xbb, 0x29, 0x92, 0x2c, 0xba, 0x6b, 0xa5, 0xcb, 0x7e, 0x88, 0x5b, 0xcd, 0xd4, 0xfe, 0xb4, 0xc7, 0x65, 0xae, 0x6b, 0x7f, 0x78, 0xb6 },
        },
        {
            .msglen = 30,
            .result = { 0x5e, 0xea, 0x58, 0xb5, 0x93, 0x4b, 0xb7, 0x32, 0x50, 0xcb, 0xc6, 0x6c, 0x63, 0x9d, 0x5d, 0xa9, 0x3f, 0x80, 0xc4, 0x91, 0xbc, 0xe3, 0x2a, 0xd6, 0x20, 0xfb, 0xf9, 0x43, 0x59, 0xcf, 0x86, 0x1d },
        },
        {
            .msglen = 31,
            .result = { 0x46, 0xee, 0x3a, 0x2a, 0x1, 0xf6, 0x43, 0xe, 0xbb, 0xc6, 0x90, 0x4f, 0x66, 0xa5, 0xe9, 0xd7, 0xa8, 0x29, 0x7e, 0x16, 0x4, 0x57, 0xee, 0x5c, 0xf4, 0x6c, 0xc8, 0x4a, 0x92, 0x27, 0x83, 0x42 },
        },
        {
            .msglen = 32,
            .result = { 0x82, 0x36, 0xe6, 0xf, 0xa, 0x37, 0x2d, 0x7b, 0x2, 0x75, 0xc4, 0x48, 0x36, 0xbf, 0xdf, 0x79, 0x35, 0xdf, 0xcb, 0x65, 0x25, 0xae, 0x11, 0x2c, 0xfa, 0x54, 0x4a, 0x99, 0xfe, 0x2a, 0x63, 0xbd },
        },
        {
            .msglen = 33,
            .result = { 0x9e, 0x35, 0xc8, 0x6c, 0xa7, 0xe8, 0x8b, 0xb2, 0x0, 0x4c, 0x41, 0x51, 0x5b, 0xd2, 0x4b, 0x9f, 0x10, 0xea, 0xfe, 0xd7, 0xc1, 0xd1, 0x36, 0xfb, 0x52, 0xd6, 0xe3, 0xe2, 0x23, 0xc9, 0x53, 0x33 },
        },
        {
            .msglen = 34,
            .result = { 0x52, 0xe9, 0x15, 0x90, 0x65, 0xb, 0x75, 0x89, 0xb3, 0xed, 0x7a, 0xb6, 0x12, 0xe6, 0xe, 0xeb, 0x7c, 0x25, 0xb4, 0xf3, 0x2a, 0xf6, 0xfe, 0x6a, 0x3c, 0xd9, 0xf0, 0x2a, 0xaf, 0xc7, 0x1b, 0xbc },
        },
        {
            .msglen = 35,
            .result = { 0xfc, 0xa5, 0xf1, 0x9a, 0xa0, 0xfa, 0x42, 0x7b, 0x49, 0xf, 0xd7, 0x76, 0xe0, 0xf9, 0x31, 0x17, 0x87, 0x70, 0x19, 0x90, 0x26, 0x96, 0xcd, 0xd6, 0xf, 0xa8, 0xb, 0x1f, 0x31, 0x45, 0x9c, 0xd0 },
        },
        {
            .msglen = 36,
            .result = { 0x85, 0x76, 0xf4, 0xfc, 0x96, 0xfb, 0x69, 0x31, 0x6, 0x5c, 0x6e, 0xd9, 0x75, 0xda, 0xf0, 0x14, 0xba, 0x4b, 0x74, 0x96, 0xf7, 0xe6, 0xd0, 0x3d, 0x36, 0x9b, 0x94, 0x8c, 0x1a, 0xb9, 0x7a, 0x88 },
        },
        {
            .msglen = 37,
            .result = { 0x8b, 0xc7, 0xad, 0xb8, 0xce, 0xc6, 0xd7, 0x8e, 0xd6, 0xfa, 0xa5, 0xd3, 0x59, 0x3d, 0x39, 0xdc, 0x74, 0x56, 0x3c, 0xd9, 0xc8, 0x0, 0xd2, 0xb0, 0x21, 0x7b, 0x93, 0xcb, 0x18, 0xec, 0x5f, 0xdd },
        },
        {
            .msglen = 38,
            .result = { 0x95, 0x7a, 0xf3, 0x9c, 0x5b, 0x40, 0x86, 0xdf, 0xa6, 0xf7, 0x34, 0x40, 0xb0, 0x7a, 0x34, 0x6c, 0xd5, 0x3d, 0x6, 0x9d, 0xc7, 0x9f, 0x11, 0x32, 0x98, 0x78, 0xee, 0xed, 0xb6, 0xb4, 0x1f, 0x34 },
        },
        {
            .msglen = 39,
            .result = { 0xf3, 0x50, 0x40, 0xd2, 0xbb, 0x54, 0xb3, 0xcf, 0x37, 0x55, 0xff, 0xc8, 0x41, 0x30, 0xde, 0x33, 0x2f, 0x4, 0xe3, 0xe7, 0x42, 0x31, 0x68, 0xe6, 0x6d, 0x5d, 0xdc, 0x14, 0x5a, 0x58, 0xf4, 0x46 },
        },
        {
            .msglen = 40,
            .result = { 0xda, 0x9d, 0xeb, 0xf7, 0xa7, 0xa1, 0x4, 0xd1, 0xfb, 0xe3, 0xd8, 0x11, 0x8d, 0x54, 0x88, 0x65, 0x42, 0x6e, 0x78, 0x7a, 0x8f, 0x1b, 0xc5, 0x13, 0x98, 0x53, 0x38, 0x26, 0xf3, 0x3d, 0xd2, 0xff },
        },
        {
            .msglen = 41,
            .result = { 0x9c, 0x23, 0x67, 0xa3, 0xeb, 0xed, 0xca, 0x21, 0x4b, 0x4c, 0x68, 0x95, 0xaf, 0xfb, 0x9b, 0x17, 0xf0, 0x2a, 0x5c, 0x78, 0x58, 0x65, 0xf1, 0x20, 0xfd, 0x3c, 0xd9, 0x66, 0x94, 0x66, 0x4d, 0xcc },
        },
        {
            .msglen = 42,
            .result = { 0xfa, 0x61, 0xf5, 0xd7, 0x58, 0xd2, 0x32, 0xb0, 0xb, 0x95, 0x88, 0xfc, 0x8a, 0x79, 0x15, 0x61, 0x1e, 0xa7, 0xf7, 0xf2, 0xf1, 0x91, 0xe7, 0xb3, 0x55, 0xa0, 0x65, 0x3c, 0xca, 0xf6, 0xac, 0x19 },
        },
        {
            .msglen = 43,
            .result = { 0xae, 0xc1, 0xb4, 0xa9, 0x88, 0xf3, 0x6d, 0xfe, 0xc2, 0x48, 0x19, 0x2a, 0x68, 0x41, 0x5e, 0x3f, 0xbe, 0x32, 0x20, 0xc7, 0x90, 0x6f, 0x23, 0x6d, 0x42, 0xaa, 0x38, 0xbb, 0xd5, 0x34, 0x7e, 0x21 },
        },
        {
            .msglen = 44,
            .result = { 0x12, 0xb0, 0xb, 0x1, 0xcf, 0xc9, 0x20, 0x8d, 0x59, 0xc1, 0xb7, 0xe9, 0x23, 0x53, 0x12, 0xd4, 0x41, 0x99, 0x7f, 0xb9, 0x57, 0x11, 0x5d, 0x9c, 0x60, 0xa8, 0x0, 0x3, 0x68, 0x9, 0x4d, 0x1d },
        },
        {
            .msglen = 45,
            .result = { 0xee, 0x85, 0x2, 0xed, 0xa1, 0x41, 0x71, 0xd4, 0x32, 0x2, 0x33, 0xec, 0x26, 0x31, 0x7b, 0xb8, 0xd0, 0xb0, 0xd6, 0xb0, 0x60, 0x52, 0xe1, 0xd9, 0xd7, 0x33, 0x72, 0x5c, 0xb9, 0xc3, 0x6c, 0x9f },
        },
        {
            .msglen = 46,
            .result = { 0x90, 0x3e, 0x7, 0x17, 0xa2, 0x18, 0xd, 0xa1, 0x71, 0xfe, 0x4b, 0x6d, 0x24, 0x40, 0x5e, 0xe2, 0xd1, 0x45, 0xd6, 0x18, 0xe8, 0xa3, 0x2d, 0x12, 0xe8, 0x11, 0xae, 0x1, 0xc4, 0x77, 0xa9, 0xab },
        },
        {
            .msglen = 47,
            .result = { 0x4a, 0x4f, 0x5a, 0xd1, 0xd0, 0xfb, 0xf7, 0x60, 0x9c, 0xbf, 0x23, 0x99, 0x95, 0xea, 0x51, 0xdb, 0x70, 0xc4, 0xa, 0xaf, 0x41, 0x13, 0x7d, 0x3d, 0xd1, 0x50, 0xa7, 0x13, 0x4f, 0xa0, 0xbf, 0xf4 },
        },
        {
            .msglen = 48,
            .result = { 0x63, 0x50, 0x7a, 0x54, 0xd8, 0xa5, 0xf, 0x96, 0x45, 0x3a, 0x85, 0x8e, 0x8e, 0xc6, 0x5d, 0xe0, 0xe8, 0xfd, 0xce, 0xa8, 0x3f, 0x59, 0x19, 0x81, 0x13, 0xd1, 0xf7, 0xd, 0x45, 0xe3, 0xf3, 0x31 },
        },
        {
            .msglen = 49,
            .result = { 0x3c, 0x7a, 0x1, 0xdd, 0x3a, 0x96, 0xae, 0x2, 0x2e, 0x6a, 0x7f, 0xd, 0x1e, 0x2f, 0x32, 0xfd, 0x5, 0x33, 0xae, 0x54, 0xa2, 0xa6, 0x89, 0x32, 0x9a, 0x7, 0xb7, 0xe9, 0x66, 0xaf, 0xf8, 0xc },
        },
        {
            .msglen = 50,
            .result = { 0x57, 0xd8, 0xed, 0xc4, 0xec, 0x23, 0xf, 0xf9, 0x55, 0xc1, 0x36, 0xde, 0xc3, 0xbd, 0x54, 0x53, 0x2f, 0xfa, 0xd1, 0xb3, 0xe1, 0x87, 0xc2, 0x39, 0x54, 0x59, 0xa9, 0xb9, 0xac, 0xed, 0xa0, 0x49 },
        },
        {
            .msglen = 51,
            .result = { 0xdf, 0xd5, 0x2e, 0xd2, 0xd2, 0xb0, 0x90, 0x12, 0x71, 0x37, 0x51, 0xba, 0x79, 0xd4, 0x43, 0xc7, 0x74, 0x12, 0xf3, 0x71, 0x74, 0x63, 0xc, 0x4d, 0x59, 0x2e, 0x5, 0xb5, 0xa2, 0x17, 0xe, 0xe9 },
        },
        {
            .msglen = 52,
            .result = { 0xbd, 0xa3, 0x12, 0x94, 0xea, 0xa7, 0xc4, 0xd3, 0x1f, 0x99, 0xcb, 0xbc, 0x53, 0x80, 0x45, 0xfd, 0x17, 0x13, 0xd7, 0x2b, 0x26, 0x5b, 0x23, 0x3d, 0x2d, 0xd8, 0x7f, 0x9, 0xcc, 0x9c, 0xa7, 0xfd },
        },
        {
            .msglen = 53,
            .result = { 0x66, 0x3a, 0xc1, 0x3b, 0x1f, 0x7d, 0x0, 0xf5, 0x9a, 0x5e, 0x92, 0x61, 0x16, 0xad, 0x2b, 0x15, 0x2f, 0x65, 0x89, 0xd2, 0xa3, 0xbd, 0x33, 0x71, 0x31, 0xe8, 0x37, 0x3c, 0xb0, 0x6d, 0x13, 0xc9 },
        },
        {
            .msglen = 54,
            .result = { 0xff, 0x17, 0xef, 0x42, 0x67, 0xba, 0xcf, 0xe7, 0xfe, 0xf5, 0x76, 0x96, 0x9e, 0xf0, 0x61, 0xe5, 0xd, 0xc3, 0xbb, 0x63, 0xd3, 0xcd, 0x4a, 0x10, 0x63, 0xa3, 0x3c, 0xe, 0xf2, 0xfc, 0xa, 0x33 },
        },
        {
            .msglen = 55,
            .result = { 0x78, 0xa8, 0xe5, 0x15, 0x18, 0x49, 0x4, 0xba, 0x34, 0xb6, 0xb3, 0x96, 0x3a, 0x6, 0xaa, 0x93, 0xad, 0x82, 0x5b, 0x87, 0x0, 0x3f, 0x5, 0x1, 0xe7, 0xe1, 0x22, 0x16, 0x3d, 0xb5, 0x5b, 0xb8 },
        },
        {
            .msglen = 56,
            .result = { 0x0, 0xad, 0xbf, 0x7d, 0x51, 0xc4, 0xed, 0x5, 0x6c, 0x81, 0x15, 0x5c, 0xa5, 0xe3, 0x6b, 0x39, 0x59, 0x10, 0x8e, 0xbc, 0x3f, 0xb8, 0x21, 0xbc, 0xd9, 0x9e, 0x35, 0x7d, 0x23, 0x48, 0x5, 0x8a },
        },
        {
            .msglen = 57,
            .result = { 0xf7, 0x5e, 0xa3, 0x88, 0x1f, 0x82, 0xf0, 0xc4, 0x39, 0x7e, 0xed, 0x22, 0x78, 0xd6, 0x65, 0x94, 0x4f, 0x8, 0x2e, 0x96, 0x7e, 0x44, 0x8f, 0x0, 0x3b, 0x92, 0xf9, 0xea, 0x83, 0x72, 0xc7, 0xe2 },
        },
        {
            .msglen = 58,
            .result = { 0x84, 0xa5, 0x85, 0x95, 0x0, 0x7e, 0xc4, 0x98, 0x36, 0xc9, 0xe5, 0xd4, 0xda, 0x59, 0xab, 0x22, 0x2f, 0xa8, 0xb7, 0x46, 0x55, 0x91, 0x2, 0xc, 0x5b, 0x64, 0x5c, 0x5b, 0x42, 0x8b, 0x7e, 0xa },
        },
        {
            .msglen = 59,
            .result = { 0x4e, 0x1c, 0x16, 0x99, 0xd8, 0x4, 0xb, 0x6, 0x91, 0x98, 0x87, 0xb0, 0xa3, 0x63, 0x9, 0xdf, 0xfb, 0xa6, 0xd6, 0xe4, 0x58, 0x27, 0xf5, 0x73, 0x9, 0x81, 0x4f, 0x5d, 0x88, 0x2c, 0xb8, 0x7 },
        },
        {
            .msglen = 60,
            .result = { 0x2f, 0x97, 0xbf, 0x70, 0x70, 0x4b, 0xfe, 0x5a, 0x2f, 0x91, 0x8f, 0x28, 0x8b, 0xee, 0xf6, 0xee, 0x41, 0x7d, 0x36, 0x14, 0x86, 0x69, 0x42, 0x9c, 0x4d, 0x5, 0xcc, 0x53, 0x71, 0x61, 0x78, 0xe },
        },
        {
            .msglen = 61,
            .result = { 0xe6, 0xba, 0x7f, 0x8, 0xb3, 0xdc, 0x7b, 0x95, 0xcb, 0xd1, 0x33, 0x64, 0x25, 0x5a, 0xa3, 0x70, 0x10, 0x4a, 0xe6, 0x2c, 0x54, 0x25, 0xfa, 0x7e, 0xd0, 0x47, 0x65, 0x8d, 0xa1, 0xa8, 0x80, 0x5 },
        },
        {
            .msglen = 62,
            .result = { 0x9b, 0x48, 0x8d, 0x79, 0x16, 0xe8, 0x32, 0x63, 0x89, 0xaa, 0x4b, 0x7d, 0xdb, 0x46, 0xcd, 0x80, 0x40, 0x9d, 0x8c, 0x6, 0xa5, 0xed, 0xb4, 0xe1, 0xdd, 0x87, 0x6b, 0xb3, 0x90, 0x2b, 0x77, 0xc8 },
        },
        {
            .msglen = 63,
            .result = { 0x72, 0xfb, 0x8c, 0xf1, 0xb9, 0xb3, 0x50, 0x55, 0x5c, 0xac, 0x93, 0x38, 0x73, 0x1c, 0xd0, 0x93, 0x6, 0x5e, 0xcd, 0x0, 0x24, 0x83, 0x4b, 0xef, 0xdc, 0xfd, 0x27, 0x58, 0xfc, 0xa1, 0x4a, 0x32 },
        },
        {
            .msglen = 64,
            .result = { 0x19, 0x5b, 0x88, 0x8d, 0x75, 0x97, 0x8a, 0x8c, 0x5d, 0xd4, 0xe7, 0xe4, 0xa, 0x4d, 0x5e, 0xcd, 0xe7, 0x88, 0xab, 0xb9, 0x6b, 0xd8, 0xd3, 0x80, 0x25, 0x3e, 0xa4, 0xfd, 0xc1, 0x83, 0x6e, 0x74 },
        },
        {
            .msglen = 65,
            .result = { 0x14, 0x3, 0xcc, 0x1f, 0xa5, 0xed, 0x5e, 0x3c, 0x45, 0x2d, 0x66, 0x9a, 0x36, 0xb7, 0x9a, 0xb, 0x1c, 0x83, 0x4d, 0xbe, 0xc9, 0x41, 0x7e, 0x7, 0x54, 0x97, 0x76, 0x25, 0x96, 0x76, 0xce, 0xd4 },
        },
        {
            .msglen = 66,
            .result = { 0x44, 0x62, 0x56, 0x7e, 0x68, 0x10, 0xed, 0xd9, 0x35, 0x8a, 0xe3, 0xd, 0x20, 0xf, 0xe6, 0x45, 0x89, 0x6c, 0x8c, 0x18, 0x13, 0xe5, 0xef, 0x28, 0x2d, 0xc1, 0x6a, 0x95, 0x9e, 0x3d, 0x81, 0x51 },
        },
        {
            .msglen = 67,
            .result = { 0x94, 0xac, 0xc2, 0xe0, 0x8, 0xb7, 0xe5, 0xe2, 0x18, 0xf0, 0x59, 0x47, 0xda, 0xf4, 0xb4, 0xb1, 0xfe, 0x11, 0xe4, 0x3c, 0x2c, 0xa8, 0x1, 0x30, 0x5e, 0x8e, 0x89, 0x4, 0xff, 0xf2, 0xc1, 0x88 },
        },
        {
            .msglen = 68,
            .result = { 0xa2, 0x73, 0x97, 0x2, 0xd7, 0xc4, 0x9b, 0x39, 0x20, 0xdc, 0xac, 0x6d, 0xca, 0x81, 0xdd, 0x83, 0xac, 0xd4, 0xf, 0x89, 0x94, 0x10, 0x8b, 0xf6, 0xb9, 0x10, 0xab, 0x24, 0xd3, 0xf9, 0xc8, 0x4f },
        },
        {
            .msglen = 69,
            .result = { 0x68, 0xa9, 0x9b, 0xc2, 0xb6, 0x1e, 0x4c, 0xe3, 0xc6, 0x89, 0xc7, 0x40, 0x2f, 0xee, 0x8f, 0x50, 0xf4, 0x9d, 0x56, 0x5, 0xba, 0x0, 0x30, 0xaa, 0xd6, 0xa6, 0x4d, 0x94, 0x46, 0xc, 0x3c, 0x3 },
        },
        {
            .msglen = 70,
            .result = { 0x4f, 0x71, 0x4e, 0x2f, 0x89, 0xce, 0x84, 0x3b, 0x9a, 0xab, 0x6c, 0x93, 0xac, 0xa8, 0x51, 0xf7, 0x72, 0x91, 0xd5, 0xad, 0xf7, 0x91, 0x5a, 0x3a, 0xa6, 0x16, 0x61, 0x6b, 0x9f, 0xba, 0xe3, 0x51 },
        },
        {
            .msglen = 71,
            .result = { 0x25, 0x50, 0x50, 0x55, 0x50, 0x6a, 0x55, 0x8d, 0x54, 0x61, 0x60, 0x44, 0x5a, 0xb0, 0x4f, 0x77, 0x7, 0xbe, 0xcf, 0x49, 0xb6, 0x68, 0x9b, 0x6d, 0x79, 0xd5, 0xb9, 0xb1, 0x45, 0x29, 0xcc, 0xc1 },
        },
        {
            .msglen = 72,
            .result = { 0x59, 0x47, 0xf5, 0x99, 0xad, 0xac, 0x9, 0x15, 0xdc, 0x67, 0x2f, 0x4e, 0x38, 0x83, 0xab, 0x53, 0x8d, 0xc2, 0x71, 0xf5, 0xb9, 0x4e, 0x59, 0xd5, 0x32, 0x10, 0x90, 0xd8, 0x5b, 0xb6, 0x5, 0xc0 },
        },
        {
            .msglen = 73,
            .result = { 0x68, 0x9, 0x2a, 0x7d, 0x49, 0x8f, 0xc1, 0xd4, 0x93, 0xc9, 0xf8, 0xe9, 0xd8, 0xc4, 0xc2, 0x34, 0xc8, 0xac, 0xc9, 0xb4, 0x9, 0x5d, 0x46, 0x51, 0xd4, 0x2e, 0x4, 0xbb, 0x8f, 0x66, 0x75, 0x15 },
        },
        {
            .msglen = 74,
            .result = { 0xc, 0xf, 0x3c, 0x79, 0x8b, 0x80, 0x93, 0xd9, 0x7f, 0x3, 0xb4, 0x5f, 0xfe, 0x66, 0xcf, 0xbe, 0xea, 0xe1, 0xa0, 0xfd, 0xf0, 0x49, 0x3d, 0x19, 0x54, 0xdc, 0x38, 0x73, 0x11, 0xb3, 0x8, 0xa3 },
        },
        {
            .msglen = 75,
            .result = { 0x33, 0xae, 0x39, 0xab, 0x8, 0x54, 0x48, 0x9e, 0x2b, 0xbe, 0x89, 0x7a, 0x32, 0xdb, 0x81, 0xc5, 0xbd, 0x39, 0x19, 0xc1, 0x87, 0x6f, 0x64, 0xb3, 0x70, 0xea, 0x9, 0xd2, 0xea, 0x72, 0x53, 0x6e },
        },
        {
            .msglen = 76,
            .result = { 0x37, 0xae, 0xae, 0xed, 0x35, 0xd1, 0x97, 0x88, 0x78, 0x57, 0x19, 0xdd, 0xbc, 0x3c, 0xa3, 0x10, 0x79, 0x21, 0x3a, 0xf9, 0xce, 0x34, 0xf3, 0xad, 0x85, 0x4f, 0xf2, 0xac, 0xd7, 0x24, 0x7b, 0x80 },
        },
        {
            .msglen = 77,
            .result = { 0xc0, 0xd0, 0xb7, 0xff, 0x19, 0x7a, 0xfe, 0x6c, 0x6, 0x4b, 0xf6, 0x12, 0x4c, 0xe6, 0xe8, 0x2, 0xf3, 0x32, 0xc3, 0x77, 0xf, 0x10, 0xab, 0x89, 0xde, 0x18, 0xc5, 0xe, 0x25, 0xab, 0x23, 0xdb },
        },
        {
            .msglen = 78,
            .result = { 0x5e, 0xe7, 0x3b, 0xa0, 0x6, 0x8e, 0x40, 0x26, 0xaf, 0x6f, 0xba, 0xf9, 0xa6, 0x23, 0xab, 0x49, 0x89, 0x15, 0xd4, 0x15, 0xc0, 0x6c, 0x1f, 0xfc, 0x6d, 0x3e, 0x51, 0x3a, 0x6e, 0xef, 0x3d, 0x17 },
        },
        {
            .msglen = 79,
            .result = { 0x2, 0xfd, 0xd7, 0x48, 0x76, 0x5b, 0x25, 0x26, 0xd4, 0x87, 0x94, 0x14, 0x9b, 0x13, 0x91, 0xa4, 0x39, 0x5, 0x4c, 0x4e, 0x6a, 0xdd, 0x60, 0x66, 0x4e, 0x23, 0xeb, 0xa5, 0xfd, 0xad, 0x5a, 0xda },
        },
        {
            .msglen = 80,
            .result = { 0x84, 0xf3, 0x92, 0xf1, 0xc0, 0xc0, 0x5a, 0x63, 0xec, 0x16, 0xf4, 0xfc, 0x4c, 0xc6, 0xb6, 0x62, 0x1e, 0x9f, 0xdd, 0xcb, 0xd8, 0x4a, 0x12, 0xf2, 0x9, 0x11, 0x88, 0x66, 0x4e, 0x85, 0xef, 0x9f },
        },
        {
            .msglen = 81,
            .result = { 0x8b, 0x28, 0x66, 0x9d, 0xe2, 0x96, 0x58, 0x21, 0x3f, 0xcd, 0xd7, 0xe0, 0xce, 0x9b, 0x51, 0x32, 0x68, 0xac, 0x1c, 0x9e, 0x38, 0x7d, 0x60, 0x5a, 0x32, 0xe1, 0x14, 0x91, 0xee, 0x36, 0x39, 0xbd },
        },
        {
            .msglen = 82,
            .result = { 0x5c, 0x16, 0xe3, 0xff, 0x6b, 0x51, 0xc2, 0x59, 0x8a, 0x24, 0xc2, 0xba, 0xc0, 0xd7, 0xd4, 0xac, 0xd9, 0x3e, 0x38, 0x1f, 0x7f, 0x2d, 0xb7, 0x85, 0x8b, 0xf1, 0xd2, 0x42, 0x28, 0xa9, 0xd7, 0x94 },
        },
        {
            .msglen = 83,
            .result = { 0xde, 0x40, 0xe0, 0xde, 0xf9, 0xe4, 0x75, 0x3d, 0x61, 0x91, 0x38, 0xac, 0x31, 0xa9, 0xba, 0x31, 0x18, 0x41, 0x57, 0x2e, 0x89, 0xdf, 0x26, 0x83, 0x40, 0x71, 0xce, 0xdc, 0x18, 0x3f, 0xe7, 0xd7 },
        },
        {
            .msglen = 84,
            .result = { 0x85, 0xf, 0x58, 0x5f, 0x4d, 0x81, 0x5b, 0x54, 0x6a, 0xa4, 0xd4, 0xfb, 0x3, 0x4e, 0x71, 0xa7, 0xc5, 0x11, 0xf, 0x1a, 0xa8, 0x44, 0x10, 0x15, 0x2e, 0xdf, 0x1e, 0xea, 0x4d, 0x86, 0x31, 0x33 },
        },
        {
            .msglen = 85,
            .result = { 0x7f, 0xe2, 0x1b, 0x95, 0x7b, 0x19, 0x2f, 0x4e, 0x72, 0x1d, 0x6c, 0x2c, 0xf3, 0x74, 0x6e, 0x99, 0x2b, 0x10, 0x76, 0x55, 0x25, 0xe4, 0x89, 0x19, 0x98, 0xb4, 0xdc, 0xea, 0xfa, 0x68, 0xec, 0x3 },
        },
        {
            .msglen = 86,
            .result = { 0x5c, 0x95, 0x3d, 0xcc, 0x6, 0x58, 0x9b, 0xc4, 0x71, 0x61, 0xc2, 0x5a, 0xac, 0xaf, 0x54, 0xbf, 0xcc, 0x93, 0x3d, 0x2e, 0xdf, 0x99, 0x74, 0xc8, 0xc8, 0x36, 0xe9, 0x44, 0x7e, 0x6d, 0x3d, 0xe7 },
        },
        {
            .msglen = 87,
            .result = { 0xf2, 0x33, 0xbc, 0x61, 0xc6, 0x2c, 0x19, 0x26, 0x80, 0x9e, 0x1e, 0x76, 0x8e, 0x9, 0x61, 0x6, 0xfb, 0xc3, 0xc8, 0xcb, 0x35, 0x9d, 0xaa, 0x73, 0xa8, 0xe6, 0xd2, 0x89, 0xc5, 0x5d, 0xee, 0xa4 },
        },
        {
            .msglen = 88,
            .result = { 0x3f, 0x13, 0xf7, 0x63, 0x67, 0x8d, 0x65, 0xcd, 0xa8, 0x5b, 0xcd, 0xc4, 0xb7, 0x25, 0x35, 0xbe, 0xd3, 0xc1, 0x6d, 0x36, 0xb9, 0x8c, 0x8c, 0x6a, 0x79, 0xd1, 0x12, 0xe8, 0xdc, 0x6c, 0xe3, 0x6d },
        },
        {
            .msglen = 89,
            .result = { 0x8f, 0x21, 0x5a, 0x7a, 0x84, 0x79, 0x7d, 0x33, 0xa, 0x7e, 0x7a, 0x7, 0x2a, 0xa9, 0xa6, 0x58, 0x33, 0xdf, 0xec, 0x40, 0x88, 0xf2, 0x9f, 0x8d, 0x12, 0xb5, 0x5e, 0xb2, 0x88, 0x2d, 0xc1, 0x7c },
        },
        {
            .msglen = 90,
            .result = { 0x73, 0xfc, 0xc6, 0x9, 0x5f, 0xc2, 0xab, 0x2d, 0xd5, 0x84, 0x6c, 0xdd, 0x1f, 0x70, 0x9e, 0x5c, 0x30, 0x36, 0xb4, 0xe7, 0x86, 0xab, 0x89, 0xc6, 0xc9, 0xed, 0x7d, 0x2e, 0x26, 0x13, 0x37, 0x4a },
        },
        {
            .msglen = 91,
            .result = { 0x3e, 0x41, 0x8a, 0x37, 0xbd, 0xec, 0x53, 0x52, 0x47, 0xd2, 0x71, 0xa4, 0x5b, 0xc7, 0x11, 0x8e, 0x8c, 0xb8, 0x2c, 0x36, 0xc9, 0xa7, 0x15, 0x21, 0x15, 0xde, 0x7f, 0x54, 0xc7, 0xb3, 0x8d, 0x34 },
        },
        {
            .msglen = 92,
            .result = { 0xca, 0x1a, 0xf9, 0xfd, 0xf4, 0xb9, 0xd, 0xdd, 0x91, 0xd8, 0x7b, 0x7c, 0xf4, 0x82, 0xe7, 0x57, 0xc0, 0xb9, 0xfe, 0x2f, 0x3c, 0xc7, 0x5a, 0x3d, 0xb, 0x79, 0xb3, 0x7a, 0x7e, 0xfd, 0x25, 0x20 },
        },
        {
            .msglen = 93,
            .result = { 0xf6, 0x54, 0xd6, 0x3a, 0x93, 0xe3, 0x7b, 0x42, 0x11, 0xa8, 0xda, 0xa4, 0x2f, 0xbf, 0xcb, 0xd0, 0x58, 0x59, 0xf8, 0xda, 0x8c, 0x5e, 0xdc, 0x1e, 0xb0, 0x64, 0x15, 0x31, 0x92, 0xe6, 0xcc, 0xc8 },
        },
        {
            .msglen = 94,
            .result = { 0x7b, 0x8f, 0x93, 0xa3, 0x55, 0x11, 0x1e, 0x18, 0x77, 0xd9, 0x12, 0x7c, 0x54, 0x4e, 0x3f, 0x36, 0x18, 0x2f, 0xc7, 0xba, 0xd3, 0xe7, 0xc, 0xa3, 0xb2, 0xb1, 0x66, 0x7a, 0xfe, 0x30, 0x1b, 0x4e },
        },
        {
            .msglen = 95,
            .result = { 0x16, 0xea, 0xe9, 0xae, 0x5d, 0x2f, 0x88, 0x87, 0x41, 0x43, 0x3d, 0xfc, 0x35, 0x7f, 0x2c, 0x4c, 0x63, 0x36, 0xf3, 0x36, 0x51, 0x84, 0xb1, 0x64, 0xc5, 0x19, 0xc0, 0xd6, 0x57, 0xef, 0x2a, 0xa4 },
        },
        {
            .msglen = 96,
            .result = { 0xc9, 0x79, 0x20, 0xa1, 0x14, 0xba, 0xbe, 0x88, 0x7d, 0x6f, 0x4, 0xe3, 0xfd, 0x2d, 0xfd, 0xc3, 0x8a, 0x1, 0xea, 0x12, 0x9d, 0x4c, 0x14, 0xc3, 0x82, 0x7f, 0xb6, 0x1e, 0x8d, 0xcc, 0x11, 0x84 },
        },
        {
            .msglen = 97,
            .result = { 0x20, 0xa9, 0xb7, 0x5a, 0x5b, 0x76, 0xaa, 0x7b, 0xed, 0x70, 0x16, 0xfb, 0xea, 0x93, 0x55, 0xd1, 0x9f, 0x95, 0xa, 0xe0, 0x79, 0x51, 0x12, 0x2c, 0xfd, 0x7d, 0x6c, 0x94, 0x4f, 0xb6, 0x5f, 0x14 },
        },
        {
            .msglen = 98,
            .result = { 0xfc, 0xf4, 0x1, 0xcc, 0x9f, 0xce, 0xc2, 0x50, 0x0, 0x1f, 0xf8, 0x3f, 0xe4, 0x87, 0x2e, 0x79, 0x94, 0xdb, 0x86, 0x85, 0x8a, 0x7d, 0xb4, 0x6a, 0x84, 0xb8, 0x6c, 0x32, 0xad, 0x8c, 0x20, 0x63 },
        },
        {
            .msglen = 99,
            .result = { 0x17, 0xa1, 0xf5, 0x6, 0xaa, 0xb0, 0xe3, 0x82, 0x2e, 0x9c, 0xf2, 0xb9, 0x75, 0x75, 0xe9, 0x36, 0x90, 0xa9, 0xb2, 0xb9, 0x97, 0xb0, 0x10, 0xbb, 0xdd, 0x65, 0xd0, 0xa8, 0xbf, 0x69, 0x1d, 0x43 },
        },
        {
            .msglen = 100,
            .result = { 0xb5, 0x6c, 0xca, 0xca, 0xe1, 0xc5, 0x74, 0x19, 0xb0, 0x89, 0x58, 0x5e, 0x2e, 0x61, 0xcb, 0xa1, 0x23, 0xd0, 0x25, 0x37, 0x47, 0xe3, 0xac, 0x3e, 0x9a, 0xf6, 0x5d, 0x0, 0x77, 0xc2, 0xdc, 0x47 },
        },
        {
            .msglen = 101,
            .result = { 0x8, 0xa4, 0x3b, 0xe7, 0x1e, 0xeb, 0x4c, 0x3a, 0x82, 0xf1, 0xe4, 0x9d, 0xb1, 0xcc, 0xbc, 0x30, 0xb7, 0xa6, 0xfe, 0x47, 0x43, 0x86, 0xb8, 0x10, 0x33, 0xef, 0x8e, 0x35, 0x34, 0xf1, 0x52, 0xae },
        },
        {
            .msglen = 102,
            .result = { 0x0, 0xdb, 0x9b, 0xb7, 0xfc, 0x8a, 0x55, 0x81, 0x49, 0x94, 0x5c, 0xc2, 0x63, 0xd2, 0x56, 0x85, 0xab, 0x5a, 0xa1, 0x89, 0x66, 0xa1, 0x4d, 0x39, 0x1b, 0xe2, 0x12, 0x9a, 0x51, 0x79, 0xc0, 0x1c },
        },
        {
            .msglen = 103,
            .result = { 0x4c, 0xeb, 0xaa, 0xec, 0xb8, 0x31, 0xef, 0xb6, 0x89, 0x8e, 0x42, 0xdf, 0x9d, 0x57, 0x42, 0xf7, 0x53, 0x5d, 0x11, 0xc0, 0x29, 0xf7, 0x64, 0x30, 0x42, 0x32, 0x23, 0xda, 0x19, 0xe, 0x8d, 0xf3 },
        },
        {
            .msglen = 104,
            .result = { 0x32, 0xf5, 0x6, 0x53, 0xf8, 0x18, 0x94, 0x3d, 0xaa, 0x5e, 0xb9, 0x9e, 0x95, 0x8, 0x4, 0x29, 0xea, 0x76, 0xa1, 0xe5, 0x60, 0xa, 0x29, 0xd0, 0x78, 0x7e, 0x0, 0x27, 0x4e, 0x63, 0xc3, 0x69 },
        },
        {
            .msglen = 105,
            .result = { 0xa9, 0xf1, 0x2b, 0xaa, 0x5e, 0xb3, 0xc9, 0xf3, 0x25, 0x6e, 0x6f, 0x8, 0xc3, 0xb4, 0xab, 0xd1, 0x20, 0x3c, 0xb1, 0x82, 0x6b, 0xfa, 0x8, 0x62, 0xaf, 0x7a, 0xa9, 0x0, 0x79, 0x9a, 0x2b, 0x12 },
        },
        {
            .msglen = 106,
            .result = { 0xc2, 0x5e, 0x53, 0x66, 0x14, 0x5, 0x26, 0x6a, 0xf1, 0x70, 0x50, 0xb3, 0x9b, 0x40, 0x99, 0x7a, 0x73, 0xe2, 0xed, 0x3d, 0x4c, 0xcc, 0xf9, 0xf6, 0x1b, 0x4c, 0x6, 0xdc, 0x9, 0x7a, 0xc2, 0x4a },
        },
        {
            .msglen = 107,
            .result = { 0x68, 0xeb, 0x96, 0xb6, 0x9, 0x7b, 0xe2, 0x4a, 0x18, 0x3a, 0xe8, 0xf8, 0xe8, 0xc7, 0x4e, 0x27, 0x8c, 0x18, 0x8a, 0xa6, 0x23, 0xfe, 0xc2, 0xb, 0xbd, 0x72, 0x1, 0x56, 0x21, 0x6b, 0x6c, 0x56 },
        },
        {
            .msglen = 108,
            .result = { 0xe1, 0x7, 0xeb, 0xc3, 0x3a, 0x5e, 0x28, 0x65, 0x14, 0xcb, 0x73, 0x19, 0xef, 0x32, 0x78, 0x96, 0xf3, 0xda, 0x1e, 0x5a, 0x89, 0xf4, 0x29, 0x7c, 0xa0, 0xfd, 0x3b, 0xe8, 0xb, 0xf0, 0x72, 0xf1 },
        },
        {
            .msglen = 109,
            .result = { 0x88, 0xac, 0xe5, 0xe7, 0xdb, 0x26, 0xa5, 0xa5, 0xef, 0x66, 0x92, 0xb4, 0xab, 0x6b, 0xed, 0x4b, 0x1a, 0x9e, 0x19, 0xb0, 0xb6, 0xd6, 0x11, 0xb1, 0x33, 0x40, 0x50, 0x69, 0x48, 0x75, 0x4, 0xe5 },
        },
        {
            .msglen = 110,
            .result = { 0x7d, 0x25, 0x12, 0x5, 0x37, 0x8c, 0x89, 0x8a, 0x43, 0xd9, 0x97, 0x26, 0xb8, 0xaf, 0xb3, 0x21, 0x4d, 0xde, 0x24, 0x58, 0x4f, 0xc8, 0xd, 0x31, 0x22, 0xb2, 0xdf, 0x34, 0xd5, 0xb, 0x25, 0xb9 },
        },
        {
            .msglen = 111,
            .result = { 0x4e, 0xfa, 0x3f, 0x64, 0x15, 0xf8, 0xe2, 0xb9, 0x10, 0x70, 0xeb, 0x6a, 0xf6, 0xf2, 0x14, 0x33, 0x0, 0xd1, 0x19, 0xf2, 0x8c, 0x50, 0x57, 0x17, 0xf0, 0xc5, 0x5d, 0xa1, 0xe, 0x22, 0xa0, 0x53 },
        },
        {
            .msglen = 112,
            .result = { 0x12, 0xac, 0x71, 0xa0, 0x72, 0x7a, 0x45, 0x74, 0x82, 0xf6, 0xde, 0x75, 0xe, 0xb9, 0xb2, 0x65, 0x76, 0x86, 0x13, 0x77, 0x4a, 0x30, 0xbe, 0xfa, 0x1, 0x38, 0x74, 0x49, 0xc9, 0x7f, 0x43, 0x9a },
        },
        {
            .msglen = 113,
            .result = { 0xfe, 0x25, 0x6b, 0xbf, 0x17, 0x61, 0x29, 0xce, 0x9e, 0xc2, 0x42, 0x9a, 0xb8, 0x29, 0xdb, 0x88, 0xef, 0x75, 0x3b, 0xad, 0xba, 0x9c, 0xd5, 0x9, 0x40, 0x2f, 0x49, 0xbd, 0x3, 0x43, 0xa7, 0x3 },
        },
        {
            .msglen = 114,
            .result = { 0xe5, 0x36, 0x48, 0xb5, 0x9e, 0xb3, 0x3c, 0x5f, 0x86, 0xab, 0x5, 0xe4, 0xc0, 0xe3, 0x94, 0xe4, 0x4c, 0x90, 0xcd, 0xa1, 0x97, 0x12, 0xdf, 0x33, 0x7a, 0x1a, 0x1e, 0xcd, 0xd4, 0x61, 0xa8, 0x3c },
        },
        {
            .msglen = 115,
            .result = { 0x52, 0xbd, 0xc6, 0x84, 0x65, 0x32, 0x88, 0x1, 0x85, 0x64, 0xfd, 0xc9, 0x98, 0x18, 0x27, 0x52, 0xe9, 0x30, 0xa2, 0x9e, 0xc2, 0xcf, 0xa9, 0x98, 0xbc, 0x47, 0x3a, 0xc7, 0xd5, 0xb1, 0x81, 0x78 },
        },
        {
            .msglen = 116,
            .result = { 0xab, 0xb1, 0xb3, 0xa5, 0x23, 0xea, 0x45, 0xe8, 0xe0, 0xa9, 0x3d, 0xe9, 0xe9, 0x86, 0x15, 0x10, 0x90, 0xca, 0xf7, 0xcc, 0x92, 0x64, 0x10, 0x3d, 0x63, 0x2f, 0x17, 0xec, 0x54, 0x9e, 0xbd, 0x3b },
        },
        {
            .msglen = 117,
            .result = { 0x82, 0xf8, 0x45, 0xcd, 0x8b, 0xfc, 0xd8, 0x81, 0x6b, 0x1f, 0x91, 0x30, 0x6d, 0xbb, 0x91, 0x70, 0x8b, 0xfe, 0x8d, 0x33, 0x35, 0x4f, 0x62, 0x7f, 0xba, 0xb6, 0x99, 0x3b, 0x79, 0xe6, 0x8f, 0x58 },
        },
        {
            .msglen = 118,
            .result = { 0xa6, 0x59, 0x67, 0x5e, 0x49, 0xe2, 0x95, 0x28, 0xa3, 0x4b, 0x4a, 0x10, 0x20, 0xd1, 0xd6, 0x7b, 0x86, 0x8a, 0x5c, 0x81, 0x31, 0x68, 0xea, 0xb7, 0x61, 0xbb, 0xb6, 0x3a, 0x6d, 0x78, 0xaa, 0x37 },
        },
        {
            .msglen = 119,
            .result = { 0x64, 0x9, 0xaf, 0x42, 0xff, 0xb1, 0xc7, 0xe6, 0xbf, 0x6f, 0xa1, 0xbb, 0x5a, 0x6, 0xea, 0xd, 0x57, 0x7d, 0x24, 0x41, 0x2b, 0xf9, 0x63, 0x82, 0x1f, 0x9, 0xa5, 0xa7, 0x58, 0xc, 0x4, 0x55 },
        },
        {
            .msglen = 120,
            .result = { 0x43, 0x33, 0x90, 0x81, 0xb6, 0x23, 0x50, 0xdf, 0xa2, 0x7e, 0x41, 0xa2, 0x40, 0x40, 0xa9, 0x34, 0xd6, 0x5, 0x6c, 0x44, 0x8, 0x3f, 0x2b, 0xaa, 0xff, 0x3, 0xc3, 0x49, 0x1b, 0x15, 0x5e, 0x7c },
        },
        {
            .msglen = 121,
            .result = { 0xe, 0xd7, 0x6d, 0x29, 0xdd, 0x8, 0xee, 0x2f, 0x1d, 0x5a, 0xb2, 0x70, 0x90, 0x8d, 0xa9, 0xf2, 0x5c, 0x5b, 0xc5, 0xe1, 0x7c, 0xec, 0x49, 0x8f, 0x46, 0x94, 0xe1, 0xc8, 0xd4, 0x68, 0xa2, 0x34 },
        },
        {
            .msglen = 122,
            .result = { 0x4f, 0x25, 0x61, 0xb1, 0x29, 0xb7, 0x41, 0x50, 0xc7, 0xfa, 0xab, 0x6c, 0x92, 0xff, 0x4, 0xc, 0xa3, 0xa1, 0x10, 0xf6, 0xa6, 0xa4, 0x5a, 0xbf, 0xe, 0x9a, 0x61, 0xa8, 0x24, 0xa3, 0x93, 0x1e },
        },
        {
            .msglen = 123,
            .result = { 0x7c, 0x8c, 0xf4, 0x5d, 0x45, 0xcf, 0x61, 0x22, 0xc9, 0x8e, 0x3a, 0xeb, 0x85, 0x55, 0x66, 0x2e, 0x1e, 0x33, 0x88, 0xab, 0x74, 0xf2, 0x66, 0x4f, 0x43, 0xfa, 0x3e, 0x25, 0xcc, 0xd2, 0x0, 0x71 },
        },
        {
            .msglen = 124,
            .result = { 0x8b, 0x51, 0x99, 0x90, 0x84, 0xda, 0x7a, 0xb3, 0xb1, 0x31, 0x1b, 0x1a, 0x66, 0xe2, 0x53, 0xac, 0x45, 0x94, 0x5c, 0xa6, 0x8c, 0x44, 0x45, 0x4, 0x58, 0x13, 0xcb, 0x44, 0x9e, 0x6c, 0x6a, 0x10 },
        },
        {
            .msglen = 125,
            .result = { 0xc5, 0xb0, 0x36, 0x55, 0x4d, 0x15, 0xf0, 0x67, 0xf8, 0x26, 0x45, 0x48, 0xa7, 0x65, 0xcc, 0xa8, 0x14, 0x1e, 0x63, 0x22, 0x36, 0xc3, 0xf3, 0x99, 0x5b, 0x77, 0xb2, 0xa8, 0xc3, 0x62, 0xb4, 0xdd },
        },
        {
            .msglen = 126,
            .result = { 0x38, 0xcb, 0x97, 0x81, 0x62, 0xde, 0x32, 0x5f, 0xc5, 0x27, 0x39, 0x82, 0x36, 0x77, 0x5a, 0xc4, 0xbf, 0x45, 0xf4, 0xe8, 0x17, 0xa9, 0x17, 0xfc, 0x34, 0xb, 0xa1, 0x11, 0x79, 0xf5, 0xc, 0x79 },
        },
        {
            .msglen = 127,
            .result = { 0x2e, 0x16, 0xab, 0xf4, 0x56, 0xea, 0x6a, 0x7e, 0x97, 0x25, 0xf9, 0x9a, 0x0, 0x5d, 0xe3, 0x70, 0x76, 0xa9, 0x2a, 0xab, 0xae, 0x32, 0xe5, 0xe8, 0xb6, 0xe1, 0x22, 0xc8, 0x74, 0xe1, 0x1f, 0x22 },
        },
        {
            .msglen = 128,
            .result = { 0x6e, 0x54, 0xeb, 0x50, 0x85, 0x7b, 0xd2, 0x64, 0x32, 0x2c, 0xb0, 0xd0, 0xc2, 0x3, 0x31, 0x95, 0xa8, 0x6a, 0x50, 0x49, 0xf4, 0x77, 0x27, 0x1a, 0x89, 0x10, 0x3e, 0x98, 0x45, 0x1e, 0xe7, 0xeb },
        },
        {
            .msglen = 129,
            .result = { 0x7, 0x2e, 0xdf, 0xe9, 0x2e, 0x73, 0xf4, 0x1e, 0x65, 0x31, 0x64, 0x25, 0xc1, 0x59, 0x46, 0xd7, 0xf9, 0x12, 0xfe, 0xc7, 0x9f, 0x24, 0xa, 0xd9, 0xfb, 0x8, 0xcb, 0x5f, 0xf2, 0x77, 0x11, 0xce },
        },
        {
            .msglen = 130,
            .result = { 0x6, 0x46, 0xc7, 0x42, 0xc2, 0xc3, 0xf2, 0x4c, 0x82, 0x7d, 0x1c, 0x2c, 0x68, 0xfd, 0x29, 0x6d, 0xc2, 0xac, 0x4f, 0xbc, 0x8d, 0xb2, 0xf7, 0x78, 0xb7, 0x8f, 0x4, 0x3f, 0x49, 0xb5, 0xc5, 0xd },
        },
        {
            .msglen = 131,
            .result = { 0x9c, 0x1f, 0xdb, 0x89, 0x3b, 0x8b, 0x5c, 0x3d, 0x1e, 0xd2, 0x9f, 0x74, 0x63, 0xb, 0x4f, 0x59, 0x72, 0x13, 0x4f, 0x40, 0x59, 0x35, 0xc6, 0x64, 0x5a, 0xb5, 0x43, 0xaf, 0x43, 0x7a, 0x13, 0xb0 },
        },
        {
            .msglen = 132,
            .result = { 0x25, 0xa9, 0x97, 0x9e, 0x4f, 0x52, 0xe6, 0xe7, 0xf, 0x38, 0xc6, 0xfb, 0x9e, 0x77, 0xe7, 0x1c, 0xb9, 0x8a, 0xf3, 0x4d, 0x76, 0xb3, 0x58, 0x8e, 0xab, 0x7d, 0xd6, 0xbb, 0x78, 0x26, 0xb1, 0x7f },
        },
        {
            .msglen = 133,
            .result = { 0x2b, 0x8, 0xfa, 0x23, 0x7d, 0xb2, 0xa1, 0x16, 0x85, 0x7b, 0x3c, 0xa4, 0xde, 0x19, 0xf7, 0x81, 0xb6, 0xaf, 0xbc, 0x11, 0x31, 0x10, 0x6f, 0x14, 0x4a, 0x62, 0x48, 0x1e, 0x43, 0x95, 0xa4, 0xb4 },
        },
        {
            .msglen = 134,
            .result = { 0x9c, 0x93, 0x30, 0x3f, 0x4c, 0x8f, 0xa7, 0x56, 0xd1, 0x6c, 0x9a, 0x28, 0xa1, 0x21, 0x7d, 0x5b, 0x12, 0x6b, 0x5a, 0xd4, 0xb9, 0x62, 0x5, 0xe2, 0xd5, 0xc8, 0x2a, 0xd1, 0xb0, 0x46, 0x2a, 0x2c },
        },
        {
            .msglen = 135,
            .result = { 0xd7, 0x17, 0xd, 0xc0, 0x46, 0x24, 0x9e, 0x14, 0x21, 0xac, 0xf3, 0xd0, 0xda, 0xf4, 0xd9, 0xf1, 0xf2, 0x96, 0xbd, 0x55, 0xff, 0x3c, 0x8d, 0x90, 0xc8, 0x2f, 0x1e, 0x87, 0xad, 0xd7, 0x8a, 0x8a },
        },
        {
            .msglen = 136,
            .result = { 0x90, 0xd6, 0xa4, 0x46, 0x75, 0x8c, 0xc3, 0xda, 0x26, 0x8a, 0xe1, 0xb7, 0xa4, 0xe, 0xef, 0x33, 0x65, 0x41, 0xa3, 0x60, 0x6a, 0xce, 0xfb, 0xc4, 0x1c, 0xa4, 0x95, 0x6d, 0x43, 0x49, 0x69, 0xd5 },
        },
        {
            .msglen = 137,
            .result = { 0xd6, 0xf5, 0xfe, 0xb0, 0x9a, 0xca, 0xec, 0xeb, 0xbe, 0x4b, 0xbd, 0x96, 0x65, 0xfe, 0x65, 0x31, 0xef, 0x36, 0x3a, 0xfc, 0x3a, 0x8, 0x3, 0xb0, 0xbf, 0xeb, 0xd, 0x2c, 0xd9, 0x27, 0x14, 0x4f },
        },
        {
            .msglen = 138,
            .result = { 0x97, 0x4f, 0x86, 0x11, 0xf9, 0x61, 0xad, 0xc, 0xba, 0x2d, 0xfa, 0xa4, 0x6f, 0x19, 0xb3, 0x66, 0xf8, 0xcc, 0x74, 0x2c, 0x76, 0x29, 0xdd, 0x6b, 0x5, 0xa3, 0x3a, 0x3b, 0xa6, 0xec, 0x81, 0xc0 },
        },
        {
            .msglen = 139,
            .result = { 0x9a, 0xe3, 0xc, 0x22, 0x5d, 0xb3, 0x71, 0x5d, 0x30, 0xcf, 0x6, 0xc1, 0x86, 0xf6, 0x84, 0xac, 0x25, 0x45, 0xb2, 0xa0, 0xd2, 0x93, 0x8b, 0x9e, 0x77, 0x55, 0x16, 0x51, 0x61, 0x4a, 0x91, 0xcd },
        },
        {
            .msglen = 140,
            .result = { 0xf1, 0x30, 0xf4, 0xc7, 0x6c, 0xe3, 0x8d, 0x63, 0x41, 0x62, 0x64, 0xe1, 0xc3, 0x97, 0xf0, 0xd6, 0xf3, 0xfd, 0x17, 0xe, 0xca, 0xd8, 0x9e, 0x6d, 0xa0, 0xa1, 0x27, 0x60, 0xff, 0xf, 0x7a, 0xca },
        },
        {
            .msglen = 141,
            .result = { 0x5, 0xab, 0x87, 0x58, 0xa8, 0x5e, 0x7d, 0xec, 0x23, 0x90, 0xd8, 0xfa, 0x87, 0x1, 0x4b, 0x32, 0xe9, 0x0, 0x7b, 0x92, 0x3b, 0x51, 0x44, 0x86, 0xa2, 0x55, 0xf0, 0xb7, 0x13, 0x3b, 0x58, 0x70 },
        },
        {
            .msglen = 142,
            .result = { 0xff, 0x4e, 0x8a, 0xca, 0xce, 0xe0, 0x84, 0x2a, 0x70, 0x82, 0x52, 0x1e, 0x67, 0xc, 0x67, 0x36, 0x76, 0x74, 0xed, 0x49, 0x21, 0x44, 0x0, 0x1a, 0x75, 0xcd, 0x4f, 0x17, 0xb8, 0xa9, 0xdf, 0xb9 },
        },
        {
            .msglen = 143,
            .result = { 0xf2, 0x36, 0x6f, 0xf3, 0x8e, 0xf3, 0xbd, 0x51, 0xec, 0xc4, 0x96, 0xc1, 0x88, 0xfe, 0x68, 0x5e, 0x1b, 0xdf, 0x71, 0x6b, 0xf3, 0x78, 0x84, 0x16, 0x11, 0xac, 0x37, 0x4d, 0x8f, 0x6e, 0x7b, 0x40 },
        },
        {
            .msglen = 144,
            .result = { 0x24, 0x20, 0x1d, 0x2, 0x16, 0x1, 0xc4, 0xe5, 0xdf, 0x81, 0xca, 0xa9, 0xf2, 0x4d, 0xe2, 0x77, 0x11, 0xf6, 0x43, 0xd3, 0x36, 0x36, 0x1c, 0xe6, 0x6c, 0x96, 0xc9, 0x74, 0x8e, 0x39, 0x1c, 0xb1 },
        },
        {
            .msglen = 145,
            .result = { 0xfc, 0x13, 0x13, 0x5c, 0x87, 0x9a, 0x95, 0xcd, 0x66, 0xac, 0x85, 0xda, 0x8c, 0x28, 0x38, 0x3f, 0x43, 0xe1, 0x40, 0x8e, 0x8f, 0x49, 0x17, 0xda, 0x75, 0xd0, 0xc4, 0xf5, 0x9e, 0x3, 0xf0, 0x62 },
        },
        {
            .msglen = 146,
            .result = { 0xcc, 0x3e, 0x74, 0x6a, 0x82, 0xb0, 0x67, 0x77, 0xcf, 0x6e, 0x93, 0x40, 0x84, 0xc7, 0xa1, 0x85, 0xa5, 0x38, 0x23, 0xa5, 0x4f, 0x9e, 0xee, 0x3d, 0xa7, 0xab, 0x88, 0x3b, 0x3, 0x1d, 0xdb, 0xbd },
        },
        {
            .msglen = 147,
            .result = { 0xaa, 0x4f, 0x52, 0xf0, 0xe8, 0x49, 0x80, 0x5e, 0xe4, 0xd4, 0x8f, 0xc1, 0x5c, 0x7d, 0x36, 0x2, 0xdb, 0xda, 0x45, 0xab, 0x6e, 0x90, 0xc1, 0x55, 0x4f, 0x8, 0xaf, 0x55, 0x4d, 0x45, 0x6b, 0x91 },
        },
        {
            .msglen = 148,
            .result = { 0xfb, 0x92, 0xef, 0x68, 0xc4, 0xa, 0xd9, 0xd8, 0x86, 0xb5, 0x63, 0x34, 0xa7, 0x42, 0x5a, 0xef, 0x13, 0xc0, 0xc9, 0x44, 0x8f, 0x9e, 0x40, 0x51, 0x1b, 0xcf, 0xbb, 0x8c, 0xb0, 0x2c, 0x56, 0xc },
        },
        {
            .msglen = 149,
            .result = { 0x88, 0xdb, 0x71, 0x51, 0x85, 0x63, 0xb6, 0xb4, 0xeb, 0x7c, 0x11, 0xaa, 0x96, 0x8, 0x9e, 0x60, 0x70, 0xa5, 0x19, 0xa4, 0x2, 0xf1, 0xa0, 0x4f, 0x16, 0xde, 0xd6, 0x8d, 0xd3, 0xc5, 0xc, 0x6c },
        },
        {
            .msglen = 150,
            .result = { 0x4a, 0xad, 0x96, 0xea, 0x90, 0xb7, 0x76, 0x9d, 0x92, 0x60, 0x35, 0x59, 0x1c, 0x89, 0x67, 0x54, 0xfd, 0x50, 0x20, 0x97, 0xc8, 0x16, 0xc8, 0xc2, 0x50, 0x40, 0x63, 0xba, 0xfb, 0xbc, 0x5e, 0xcd },
        },
        {
            .msglen = 151,
            .result = { 0xca, 0x4c, 0x3d, 0x26, 0x75, 0x82, 0x48, 0x5b, 0xa4, 0xd3, 0x28, 0x9, 0x8d, 0xe0, 0x67, 0x6d, 0xe1, 0x5, 0x8a, 0xbf, 0x17, 0xbb, 0x6e, 0x48, 0x2e, 0xd, 0xcd, 0x90, 0x7c, 0x4e, 0xa4, 0x94 },
        },
        {
            .msglen = 152,
            .result = { 0x6d, 0x27, 0x5e, 0x91, 0xeb, 0xc7, 0x1a, 0x31, 0x42, 0xfa, 0x2c, 0x91, 0xdc, 0x6a, 0xdc, 0x1b, 0x2b, 0x66, 0xf2, 0x9a, 0xf6, 0x11, 0x36, 0x96, 0x2a, 0xfc, 0x73, 0x89, 0x7e, 0xd4, 0x55, 0x3d },
        },
        {
            .msglen = 153,
            .result = { 0x76, 0xba, 0x1d, 0x4b, 0xc1, 0xa2, 0x1b, 0x56, 0xbe, 0x5, 0x38, 0x90, 0x47, 0xe9, 0x9, 0xc2, 0xd1, 0x2, 0x6d, 0x44, 0x81, 0xf7, 0x6e, 0x28, 0x88, 0x79, 0xb5, 0x10, 0x75, 0xfb, 0xc6, 0xe7 },
        },
        {
            .msglen = 154,
            .result = { 0x6d, 0xbc, 0x90, 0xa6, 0x2f, 0xb5, 0x45, 0x5f, 0x4a, 0xcb, 0x85, 0x32, 0x90, 0x57, 0x86, 0x75, 0x51, 0xa5, 0x7e, 0x4b, 0x6b, 0xb3, 0xeb, 0xfe, 0xba, 0x75, 0xfc, 0x55, 0x1a, 0x85, 0x3, 0x54 },
        },
        {
            .msglen = 155,
            .result = { 0x7d, 0x59, 0x19, 0xb6, 0xf2, 0x7f, 0x7, 0x7a, 0xaa, 0xf7, 0xdf, 0x2e, 0x2d, 0xfb, 0x57, 0x14, 0xd6, 0x9f, 0x9c, 0xcc, 0x56, 0x88, 0xe, 0x85, 0xcc, 0x30, 0x6, 0x32, 0x1f, 0x70, 0x47, 0x59 },
        },
        {
            .msglen = 156,
            .result = { 0xea, 0xfa, 0x44, 0x81, 0x83, 0xec, 0x2d, 0x63, 0xa6, 0xea, 0xf0, 0x6, 0x5d, 0x15, 0x32, 0x89, 0xed, 0x52, 0xf, 0x23, 0x66, 0x8b, 0x1c, 0x7c, 0x13, 0xd0, 0x35, 0xc1, 0x7d, 0x27, 0xfe, 0x80 },
        },
        {
            .msglen = 157,
            .result = { 0xab, 0x31, 0xe1, 0x44, 0x3d, 0x3a, 0xf5, 0x6a, 0x8b, 0x5d, 0xa6, 0xd5, 0x63, 0x23, 0x7e, 0x17, 0x24, 0xc9, 0x41, 0x53, 0x86, 0x90, 0xb7, 0x6f, 0xbc, 0x39, 0x5d, 0x1b, 0x19, 0xd9, 0xef, 0x9c },
        },
        {
            .msglen = 158,
            .result = { 0x3e, 0x34, 0xec, 0xd9, 0x26, 0xe7, 0x27, 0x3b, 0xc5, 0xc3, 0x8e, 0x11, 0xba, 0x58, 0xcf, 0x2b, 0x29, 0xc4, 0x49, 0xfc, 0xf6, 0x9d, 0xcc, 0x2a, 0x0, 0xff, 0xe6, 0xbc, 0x7d, 0x2a, 0x48, 0x22 },
        },
        {
            .msglen = 159,
            .result = { 0xc8, 0x59, 0x27, 0xa2, 0x58, 0x9e, 0xfe, 0x8c, 0x5a, 0x7f, 0x99, 0xd0, 0x58, 0xd4, 0x8c, 0xae, 0xa9, 0x4, 0x24, 0x50, 0x34, 0x23, 0x45, 0xfe, 0x87, 0xa7, 0x53, 0x5a, 0x27, 0x9d, 0x1b, 0x4f },
        },
        {
            .msglen = 160,
            .result = { 0xd7, 0x76, 0xaa, 0x3b, 0x97, 0x5e, 0xac, 0xbe, 0x46, 0x45, 0x80, 0x8e, 0x2c, 0xa2, 0xe1, 0xd4, 0x75, 0xb5, 0xe0, 0xd2, 0x4c, 0x59, 0xf0, 0xc3, 0xe8, 0x8c, 0x96, 0xbd, 0x85, 0x23, 0x8b, 0x40 },
        },
        {
            .msglen = 161,
            .result = { 0x40, 0x8e, 0xa5, 0xbd, 0x32, 0x7, 0x19, 0xc8, 0x31, 0x22, 0x32, 0xdf, 0xe0, 0xf5, 0xe2, 0x5f, 0x91, 0xf4, 0x95, 0x7a, 0xa7, 0x91, 0x9d, 0xed, 0x35, 0x2f, 0x0, 0x4, 0x62, 0x63, 0x4, 0x80 },
        },
        {
            .msglen = 162,
            .result = { 0xa6, 0x1b, 0x63, 0x3, 0xbf, 0x2, 0xbd, 0xbb, 0x69, 0x1b, 0xac, 0x54, 0x99, 0xee, 0x8b, 0xe, 0xb, 0x41, 0x78, 0xda, 0x8f, 0x72, 0x97, 0xdf, 0x9, 0xfa, 0x35, 0xe1, 0x8a, 0xb3, 0x80, 0xc3 },
        },
        {
            .msglen = 163,
            .result = { 0x7d, 0xd3, 0x5e, 0x1a, 0x9, 0x94, 0x6e, 0x30, 0xc6, 0xbb, 0xda, 0xe7, 0x92, 0x49, 0xf6, 0x37, 0xe, 0xb3, 0x30, 0x96, 0xb2, 0xaf, 0x57, 0x22, 0xe4, 0x8d, 0x9f, 0x83, 0x51, 0xbf, 0x98, 0xb0 },
        },
        {
            .msglen = 164,
            .result = { 0x3e, 0x10, 0xe4, 0x81, 0xa2, 0x1d, 0x92, 0x7a, 0x1a, 0x73, 0xfe, 0xd4, 0x8b, 0x28, 0x4c, 0x85, 0xcc, 0xfe, 0xe0, 0x4f, 0x55, 0xfd, 0x47, 0x95, 0x1f, 0x1b, 0x60, 0x38, 0x7, 0xe9, 0xc9, 0x4f },
        },
        {
            .msglen = 165,
            .result = { 0x75, 0x68, 0x14, 0xf8, 0x3c, 0x94, 0x2b, 0xe9, 0x6c, 0x49, 0xfc, 0x77, 0xa8, 0x6a, 0x5d, 0xf6, 0x4e, 0x45, 0xa0, 0x45, 0x2c, 0xfc, 0xec, 0xb1, 0xaf, 0xf5, 0x77, 0x6d, 0xbf, 0x96, 0x2b, 0x88 },
        },
        {
            .msglen = 166,
            .result = { 0x81, 0x1e, 0x33, 0x1c, 0xeb, 0xcf, 0x34, 0x58, 0x18, 0xa7, 0x2c, 0x60, 0x9, 0x36, 0x6e, 0xba, 0x46, 0x27, 0xee, 0x25, 0x60, 0xa9, 0x9f, 0x90, 0x4, 0x65, 0xa6, 0x92, 0x48, 0x4a, 0xf7, 0x1b },
        },
        {
            .msglen = 167,
            .result = { 0xdc, 0x3d, 0x6, 0x9e, 0x2c, 0x8c, 0x2d, 0x84, 0xf7, 0x97, 0xa, 0xeb, 0xe3, 0x68, 0xa7, 0x59, 0x3d, 0xa, 0xba, 0x6f, 0xfb, 0x11, 0x5a, 0xf8, 0x99, 0xae, 0x84, 0x1f, 0x43, 0xf8, 0xfd, 0xac },
        },
        {
            .msglen = 168,
            .result = { 0x4e, 0xd7, 0x72, 0x7d, 0xfe, 0x4d, 0x8a, 0x9a, 0xfa, 0x39, 0xb7, 0x74, 0x83, 0x61, 0xd5, 0xb6, 0xd1, 0x56, 0x88, 0xe7, 0x85, 0x15, 0xcf, 0x2d, 0x91, 0x73, 0xa4, 0xc9, 0xad, 0x76, 0x47, 0x2f },
        },
        {
            .msglen = 169,
            .result = { 0x33, 0x25, 0x94, 0xbc, 0xa4, 0xd, 0x39, 0x84, 0x4a, 0xe5, 0x30, 0x1f, 0x70, 0x41, 0xf0, 0xa7, 0x6d, 0x52, 0x3f, 0xa9, 0x17, 0x83, 0x46, 0xe, 0x26, 0x77, 0xe9, 0xfd, 0xe, 0x46, 0x21, 0xee },
        },
        {
            .msglen = 170,
            .result = { 0x84, 0xb8, 0x3c, 0x8d, 0x89, 0xd6, 0x55, 0x75, 0xbd, 0x5d, 0xcb, 0x76, 0xfe, 0x42, 0x98, 0xf9, 0xff, 0x8b, 0x4d, 0xe6, 0xe8, 0xd6, 0x1f, 0x37, 0xa6, 0xe0, 0xa8, 0x37, 0x28, 0xed, 0xf4, 0x38 },
        },
        {
            .msglen = 171,
            .result = { 0xf, 0x48, 0x6c, 0x1f, 0x99, 0x6d, 0x3c, 0x11, 0xd9, 0x7a, 0x33, 0xa, 0xdf, 0xb1, 0xa3, 0x59, 0x3f, 0xe3, 0xf0, 0x3, 0xab, 0x5e, 0x3f, 0x68, 0xe5, 0xa5, 0xd1, 0xbb, 0x8, 0x85, 0x50, 0x45 },
        },
        {
            .msglen = 172,
            .result = { 0x58, 0x25, 0xc1, 0x9e, 0x9d, 0x79, 0xa9, 0x71, 0xe7, 0xc5, 0x6, 0x8d, 0x23, 0x65, 0xf4, 0xbc, 0x9e, 0xa8, 0xab, 0xcc, 0x2f, 0x93, 0xac, 0x64, 0xb5, 0x6d, 0x28, 0x34, 0xe, 0x78, 0x9a, 0xfa },
        },
        {
            .msglen = 173,
            .result = { 0x10, 0xc6, 0xca, 0x3a, 0xc9, 0xe3, 0x82, 0x6e, 0x76, 0x5b, 0x8d, 0x42, 0x56, 0x4c, 0xc5, 0xf2, 0x2c, 0xaa, 0x95, 0x4, 0xc4, 0xef, 0x5f, 0xda, 0x10, 0xd3, 0x77, 0x9b, 0xc5, 0x95, 0x72, 0x14 },
        },
        {
            .msglen = 174,
            .result = { 0x7b, 0x64, 0xa, 0x83, 0xbf, 0x35, 0x85, 0xbf, 0xc2, 0xee, 0xdd, 0x95, 0xca, 0x5c, 0xa0, 0xee, 0xca, 0xa2, 0xb8, 0x33, 0xc1, 0x7d, 0xf3, 0x4d, 0x35, 0x2e, 0x27, 0xb8, 0x56, 0x5, 0x7a, 0x73 },
        },
        {
            .msglen = 175,
            .result = { 0xdb, 0x32, 0x29, 0x7c, 0xfd, 0xa5, 0x91, 0xae, 0x9b, 0xb0, 0xbd, 0xb4, 0x34, 0x4f, 0x60, 0x98, 0x65, 0x35, 0xe4, 0x60, 0xf8, 0x8b, 0x57, 0x47, 0x55, 0x14, 0xa4, 0x5e, 0xcc, 0xa6, 0xbd, 0xf4 },
        },
        {
            .msglen = 176,
            .result = { 0x7c, 0x6f, 0xd4, 0x3f, 0xd0, 0xa7, 0x7d, 0xd5, 0x34, 0xe0, 0x8d, 0x91, 0x5d, 0xbe, 0x92, 0xb8, 0x67, 0xbe, 0xf5, 0x80, 0xa4, 0xd5, 0x1b, 0x86, 0x12, 0xa7, 0x6, 0x43, 0x1c, 0x61, 0xa3, 0xd5 },
        },
        {
            .msglen = 177,
            .result = { 0x39, 0x4e, 0x86, 0xdf, 0xbd, 0x7f, 0x6a, 0x8d, 0xe7, 0x55, 0xce, 0x4b, 0x69, 0x90, 0xbc, 0x20, 0xf7, 0xd1, 0xba, 0x93, 0x2b, 0x57, 0x16, 0x5d, 0xc3, 0x16, 0xa, 0xf3, 0x62, 0x7, 0x66, 0xac },
        },
        {
            .msglen = 178,
            .result = { 0x5f, 0xfd, 0x40, 0x99, 0xa8, 0xdc, 0xe5, 0x2, 0x9e, 0x36, 0xf, 0x5d, 0x47, 0x53, 0xe4, 0x48, 0xcf, 0xd5, 0x5f, 0xd5, 0x29, 0x95, 0x5d, 0x92, 0x6d, 0x41, 0x22, 0x7d, 0x10, 0x8a, 0xc9, 0x88 },
        },
        {
            .msglen = 179,
            .result = { 0x58, 0x91, 0x6b, 0x40, 0x4d, 0x99, 0xf7, 0x78, 0xa5, 0xc7, 0x62, 0x49, 0x6c, 0x37, 0xd5, 0x90, 0x93, 0x3c, 0x59, 0x34, 0x36, 0xd1, 0x97, 0xcd, 0x85, 0x7d, 0x7a, 0x6d, 0x65, 0xce, 0x7f, 0xdb },
        },
        {
            .msglen = 180,
            .result = { 0x86, 0xc3, 0xa0, 0xa4, 0x46, 0x8a, 0xa8, 0x69, 0x8e, 0x6d, 0x5e, 0x79, 0x9f, 0xca, 0x9c, 0x2a, 0x94, 0xb0, 0xf8, 0x3f, 0xad, 0xa0, 0xfc, 0x50, 0x68, 0x39, 0x6c, 0xb5, 0xfc, 0x31, 0xaf, 0x74 },
        },
        {
            .msglen = 181,
            .result = { 0xce, 0x7, 0x9, 0x65, 0x72, 0xf3, 0x8c, 0x51, 0x6b, 0xf7, 0x2d, 0x46, 0xfc, 0xbc, 0xa6, 0xdd, 0x75, 0xe4, 0xd9, 0x54, 0x70, 0xcd, 0xa5, 0x5c, 0xa1, 0xb6, 0xd4, 0x3a, 0xe5, 0x4c, 0x18, 0x9e },
        },
        {
            .msglen = 182,
            .result = { 0x96, 0xbc, 0xcc, 0xbd, 0xb5, 0xf8, 0xd7, 0xef, 0xc7, 0x85, 0x44, 0x89, 0x6b, 0xd5, 0xbf, 0x78, 0xb7, 0x9d, 0xde, 0xf8, 0x72, 0xd1, 0xd9, 0xb3, 0xb0, 0xfd, 0x7f, 0x4e, 0x2a, 0xaa, 0x3d, 0x7c },
        },
        {
            .msglen = 183,
            .result = { 0x0, 0xdf, 0xb9, 0xd2, 0x99, 0x8e, 0x5c, 0xed, 0x85, 0x8b, 0x3d, 0x35, 0xdf, 0x35, 0x8c, 0x82, 0x3a, 0x20, 0x36, 0x60, 0x48, 0x7e, 0x9e, 0x8b, 0x60, 0xf9, 0x88, 0xb1, 0x8d, 0x5c, 0x41, 0xef },
        },
        {
            .msglen = 184,
            .result = { 0x99, 0xdf, 0xe5, 0xf6, 0xdf, 0xb3, 0x88, 0x88, 0x45, 0x34, 0x4a, 0x61, 0xc8, 0x87, 0x49, 0x20, 0xdf, 0xe0, 0x84, 0x5b, 0x35, 0xfe, 0x8c, 0xf8, 0x4a, 0x9c, 0xb1, 0x2d, 0x30, 0x5f, 0xfe, 0x3c },
        },
        {
            .msglen = 185,
            .result = { 0xfc, 0x53, 0x78, 0x1f, 0x4d, 0x43, 0xf4, 0xe9, 0xdc, 0x74, 0x8, 0xc3, 0xb5, 0xfc, 0x82, 0x88, 0x53, 0x25, 0xc, 0x81, 0x14, 0xa7, 0xff, 0xff, 0x9f, 0xda, 0xc8, 0x45, 0x38, 0x71, 0xdc, 0xd9 },
        },
        {
            .msglen = 186,
            .result = { 0xa5, 0x3f, 0x40, 0x24, 0x78, 0x12, 0xfc, 0xa7, 0x5b, 0x8a, 0xea, 0x5f, 0x83, 0xf9, 0x42, 0xc6, 0x21, 0xd, 0xa1, 0x46, 0x3, 0x3a, 0x62, 0xb9, 0x39, 0x39, 0xad, 0x6d, 0x70, 0x8e, 0xa2, 0x97 },
        },
        {
            .msglen = 187,
            .result = { 0x52, 0x23, 0x8a, 0xe, 0x32, 0xd5, 0x99, 0xd2, 0x7e, 0x77, 0x89, 0x70, 0x75, 0xdf, 0xf1, 0x90, 0x92, 0x1d, 0x14, 0xf7, 0xea, 0xa9, 0xdf, 0x5, 0x72, 0xf5, 0x8a, 0xd0, 0xdc, 0xee, 0x40, 0xd0 },
        },
        {
            .msglen = 188,
            .result = { 0x56, 0x86, 0xee, 0xbb, 0xde, 0xcc, 0x9d, 0xb2, 0x26, 0xa7, 0xb4, 0xef, 0x3d, 0x3f, 0x3e, 0x7d, 0xe3, 0x27, 0xea, 0x79, 0xd5, 0x82, 0x8f, 0x2f, 0x44, 0xa0, 0xe1, 0x4c, 0x67, 0xa6, 0x6b, 0x8f },
        },
        {
            .msglen = 189,
            .result = { 0xc0, 0x4c, 0xf2, 0x5c, 0x93, 0x4b, 0x36, 0xcb, 0xff, 0xc2, 0x15, 0x9, 0x77, 0x2f, 0xc1, 0xb0, 0x9, 0x98, 0x2b, 0x48, 0x3e, 0xcf, 0x6f, 0x4f, 0xe, 0x7d, 0xd9, 0xae, 0xdb, 0xeb, 0xd8, 0xe1 },
        },
        {
            .msglen = 190,
            .result = { 0x1f, 0xf1, 0x1c, 0x2c, 0x4e, 0xd0, 0x9c, 0x5e, 0x8e, 0xb4, 0x71, 0x88, 0xc8, 0xcc, 0xd8, 0x4b, 0x25, 0x6d, 0xad, 0xe7, 0x6d, 0x8d, 0x3b, 0x4d, 0x3a, 0xe9, 0xd, 0xa7, 0x69, 0xa8, 0x98, 0xde },
        },
        {
            .msglen = 191,
            .result = { 0x27, 0x4, 0x31, 0x8, 0x73, 0xf1, 0x5f, 0x15, 0x61, 0x2c, 0x62, 0x49, 0xa9, 0xe8, 0x11, 0xf3, 0xac, 0x54, 0x48, 0x98, 0x62, 0xf2, 0x7d, 0xd6, 0x12, 0xcf, 0x4, 0x1, 0x4e, 0xa9, 0x24, 0xc8 },
        },
        {
            .msglen = 192,
            .result = { 0xe4, 0x39, 0x59, 0x7e, 0x60, 0x5a, 0x45, 0x4b, 0x89, 0xa8, 0x97, 0x74, 0xfe, 0xb7, 0xeb, 0xd5, 0x6, 0xdb, 0xdc, 0x4d, 0x85, 0x3a, 0x38, 0x4c, 0x5e, 0x74, 0x86, 0xeb, 0xf0, 0xc4, 0x5, 0x32 },
        },
        {
            .msglen = 193,
            .result = { 0x9a, 0x14, 0xf6, 0x6f, 0xce, 0x82, 0xa0, 0xbb, 0xde, 0xb8, 0x24, 0x2b, 0xaf, 0xae, 0x18, 0xd4, 0xbb, 0x45, 0x9e, 0xe4, 0x87, 0x3b, 0x35, 0xca, 0xeb, 0x37, 0xd1, 0x52, 0x6, 0x6e, 0x84, 0x3b },
        },
        {
            .msglen = 194,
            .result = { 0x64, 0x9f, 0xab, 0x84, 0xaf, 0x83, 0xa6, 0xcb, 0x23, 0x0, 0xd5, 0x4c, 0xd3, 0x76, 0x4b, 0x57, 0x76, 0x70, 0xdb, 0xea, 0x3a, 0xcb, 0xb7, 0x9c, 0x16, 0x80, 0xbf, 0x66, 0x2b, 0x56, 0xc, 0x2c },
        },
        {
            .msglen = 195,
            .result = { 0xab, 0xa8, 0x11, 0x2b, 0x2, 0x99, 0xb0, 0x15, 0x36, 0x4d, 0x5b, 0x5d, 0x55, 0xc3, 0xa5, 0xbc, 0x70, 0x9, 0x36, 0xd9, 0x9f, 0xfe, 0x49, 0x3d, 0x5a, 0x2b, 0x8d, 0x69, 0xe2, 0x6, 0x1a, 0x6f },
        },
        {
            .msglen = 196,
            .result = { 0xd7, 0x6b, 0x15, 0x6c, 0xc1, 0x21, 0x84, 0x78, 0x7c, 0x5e, 0xbe, 0x16, 0x70, 0xe2, 0x83, 0x7f, 0x2c, 0x3, 0x5f, 0xb9, 0x2f, 0x17, 0x46, 0xb0, 0x5a, 0xaf, 0xac, 0x2b, 0x80, 0xcf, 0xa5, 0x9c },
        },
        {
            .msglen = 197,
            .result = { 0x15, 0x9c, 0x70, 0x75, 0x5a, 0x4e, 0xd2, 0x94, 0x7b, 0x6, 0xe8, 0xdd, 0x55, 0x21, 0xe9, 0x1b, 0x97, 0xe, 0x95, 0x5b, 0x18, 0xa7, 0x26, 0xc0, 0x0, 0xed, 0xe7, 0x55, 0xc8, 0x3d, 0xf3, 0xe7 },
        },
        {
            .msglen = 198,
            .result = { 0x2c, 0xdf, 0x2e, 0x88, 0xf, 0xee, 0xcf, 0x19, 0x1e, 0x72, 0x8b, 0x15, 0x7d, 0x18, 0xa1, 0xcf, 0x6, 0xa6, 0xc2, 0xec, 0x35, 0xe5, 0x2a, 0x0, 0x7f, 0x75, 0x9b, 0x63, 0x99, 0x9e, 0x2f, 0x66 },
        },
        {
            .msglen = 199,
            .result = { 0x28, 0xeb, 0xc7, 0x79, 0x5d, 0x7c, 0x29, 0xb9, 0x8a, 0xe8, 0x7b, 0xe4, 0x74, 0x3d, 0x3a, 0x74, 0xab, 0x2e, 0x67, 0xde, 0xe4, 0x28, 0x1b, 0xd0, 0xe3, 0x1b, 0xc9, 0xd0, 0xc0, 0x6c, 0xda, 0xba },
        },
        {
            .msglen = 200,
            .result = { 0x72, 0x17, 0x1e, 0xca, 0x67, 0xed, 0x83, 0xe9, 0x8d, 0x9b, 0x44, 0xe, 0xd4, 0x27, 0x2c, 0x6b, 0x7d, 0xe2, 0x22, 0x54, 0xf3, 0x72, 0xd1, 0x2, 0xaf, 0x47, 0x20, 0xc0, 0x47, 0x8a, 0x5, 0x9c },
        },
        {
            .msglen = 201,
            .result = { 0xb8, 0x2c, 0xea, 0xd9, 0xc4, 0x17, 0x66, 0xa6, 0x5a, 0xc1, 0xb8, 0x24, 0xf5, 0x5e, 0x1a, 0x2c, 0x85, 0xf0, 0xa3, 0xcd, 0xd3, 0x7e, 0xfa, 0x23, 0xd2, 0x90, 0x4f, 0xf3, 0x55, 0xfa, 0x17, 0x5c },
        },
        {
            .msglen = 202,
            .result = { 0x9d, 0x7f, 0x23, 0x8c, 0x92, 0xe5, 0x37, 0xb, 0xc7, 0x97, 0xa4, 0x68, 0xe5, 0xc, 0xd8, 0xfe, 0xb0, 0x4d, 0xc7, 0x79, 0x25, 0xd, 0xe6, 0xa6, 0xe6, 0x4, 0xc5, 0xfd, 0x2d, 0x25, 0x3f, 0xd2 },
        },
        {
            .msglen = 203,
            .result = { 0xdf, 0xdf, 0x13, 0xc0, 0x82, 0x73, 0x7b, 0xec, 0x2a, 0x4d, 0x9e, 0x62, 0xfc, 0x88, 0xa9, 0x47, 0xfd, 0x47, 0x59, 0xf2, 0x58, 0x5, 0xf9, 0x2e, 0xce, 0x5b, 0x1, 0xae, 0x37, 0x9e, 0xa8, 0x3f },
        },
        {
            .msglen = 204,
            .result = { 0x60, 0x10, 0x29, 0xc3, 0x82, 0x91, 0x8b, 0x74, 0xa5, 0x6a, 0x3f, 0x6a, 0x76, 0xaf, 0x50, 0x9e, 0xf1, 0x38, 0xf5, 0xd2, 0xdc, 0xe5, 0x5a, 0x3d, 0xa9, 0x8a, 0x56, 0xca, 0xeb, 0x57, 0x79, 0x5a },
        },
        {
            .msglen = 205,
            .result = { 0xd2, 0x71, 0x22, 0xe1, 0x91, 0x69, 0x5d, 0x9f, 0x6, 0xff, 0x51, 0xea, 0x7e, 0x8a, 0x5d, 0xb2, 0xae, 0x66, 0xf5, 0x22, 0x65, 0xa7, 0xfe, 0xf0, 0xce, 0xf5, 0x2c, 0xbf, 0x4e, 0x41, 0x19, 0x72 },
        },
        {
            .msglen = 206,
            .result = { 0x66, 0x41, 0x4d, 0xd9, 0xff, 0x1, 0x27, 0xda, 0xae, 0xee, 0x41, 0x34, 0x41, 0xd1, 0xb9, 0xac, 0x11, 0xf3, 0x5b, 0xc4, 0xa2, 0xd5, 0x3, 0x47, 0x87, 0xf4, 0x88, 0xee, 0x61, 0x22, 0x1, 0x6a },
        },
        {
            .msglen = 207,
            .result = { 0x59, 0xaf, 0xa5, 0x9a, 0xac, 0x1, 0x0, 0x73, 0x57, 0xa4, 0xdb, 0x22, 0x9, 0x3b, 0x3a, 0x66, 0x19, 0xd6, 0xe4, 0xf7, 0x1d, 0x2a, 0x37, 0x5f, 0x71, 0xc5, 0x54, 0x8e, 0xb, 0x73, 0xd5, 0x3c },
        },
        {
            .msglen = 208,
            .result = { 0xd1, 0xa7, 0x61, 0x34, 0xc5, 0x65, 0xb8, 0xa4, 0x8e, 0x3a, 0x9f, 0x5b, 0x11, 0x8b, 0x21, 0x5e, 0x7c, 0x9c, 0xfb, 0xd3, 0xe2, 0xe5, 0x92, 0x41, 0x6f, 0x4, 0x61, 0x30, 0x79, 0xf2, 0x8, 0xc4 },
        },
        {
            .msglen = 209,
            .result = { 0x84, 0x79, 0x2, 0x39, 0x2d, 0x2a, 0x11, 0xa5, 0xff, 0xa1, 0x3a, 0xfd, 0x70, 0x90, 0xb4, 0xa5, 0x85, 0xda, 0xd5, 0x4c, 0x8d, 0xaa, 0x97, 0x5e, 0x3b, 0x45, 0x89, 0xd0, 0xfd, 0x85, 0x57, 0xf3 },
        },
        {
            .msglen = 210,
            .result = { 0xc6, 0x3b, 0x4b, 0xbd, 0x70, 0x65, 0x2, 0x2f, 0xfc, 0xc2, 0x42, 0x4e, 0x77, 0xa0, 0xc8, 0x4d, 0x3d, 0x65, 0x4a, 0xb3, 0x47, 0xf5, 0x21, 0x39, 0xb0, 0x61, 0x8a, 0xae, 0x58, 0x18, 0x7e, 0xbe },
        },
        {
            .msglen = 211,
            .result = { 0x3c, 0x50, 0xdb, 0xa6, 0x45, 0x69, 0x8b, 0x58, 0x22, 0x1f, 0x3c, 0xa3, 0x1a, 0x79, 0x90, 0xef, 0xd4, 0x21, 0xf6, 0x9e, 0xfd, 0xe5, 0xb9, 0xb2, 0xbb, 0xef, 0x27, 0x36, 0x35, 0xd8, 0x6f, 0x69 },
        },
        {
            .msglen = 212,
            .result = { 0xd1, 0x5b, 0x4f, 0x10, 0xc6, 0x1, 0x2b, 0xe5, 0x4b, 0x8f, 0xba, 0x55, 0x36, 0x60, 0x26, 0xf3, 0x93, 0x40, 0xb7, 0xd, 0xbd, 0xf6, 0x80, 0x51, 0xbe, 0x52, 0x9, 0xdd, 0x78, 0x30, 0x47, 0xbd },
        },
        {
            .msglen = 213,
            .result = { 0xe6, 0x2e, 0x97, 0xbc, 0x93, 0xaf, 0x61, 0xb8, 0x40, 0x9d, 0x70, 0x90, 0xdc, 0x34, 0xb0, 0x7e, 0xad, 0x1d, 0x50, 0x55, 0x9e, 0x42, 0x92, 0x92, 0xaf, 0x54, 0x5c, 0x57, 0xa, 0x49, 0x39, 0xd },
        },
        {
            .msglen = 214,
            .result = { 0x6d, 0x72, 0xde, 0xd8, 0x72, 0x65, 0xc9, 0xaf, 0x36, 0x3b, 0x75, 0xf6, 0xf7, 0x71, 0x15, 0xa6, 0xad, 0x73, 0xa2, 0x9b, 0x1c, 0x86, 0xf3, 0xc1, 0xc1, 0x7a, 0x85, 0x87, 0x70, 0x44, 0x83, 0xe8 },
        },
        {
            .msglen = 215,
            .result = { 0x6c, 0xfb, 0xa2, 0xb9, 0xc1, 0xf0, 0xbf, 0xef, 0x33, 0x60, 0x55, 0x30, 0x5b, 0x2b, 0x4, 0x7b, 0xfb, 0x8f, 0xe0, 0x5d, 0x11, 0x90, 0x3c, 0x28, 0xcf, 0xd4, 0x5, 0x70, 0x88, 0x1a, 0x53, 0xa },
        },
        {
            .msglen = 216,
            .result = { 0xe6, 0x2a, 0x80, 0x77, 0xb, 0x8d, 0xc8, 0x79, 0x72, 0x32, 0xf9, 0xd, 0x41, 0xcc, 0x6e, 0x77, 0xe5, 0x5f, 0x22, 0xd2, 0xdb, 0x1, 0xf9, 0x96, 0xce, 0x13, 0x95, 0x51, 0x6, 0x95, 0x51, 0x24 },
        },
        {
            .msglen = 217,
            .result = { 0xf1, 0x79, 0x81, 0xb2, 0x3b, 0xb7, 0xc1, 0x12, 0x24, 0x6e, 0x97, 0xed, 0xe4, 0x87, 0xf4, 0xcf, 0x62, 0xf8, 0xb8, 0xa9, 0xb8, 0x9d, 0xf4, 0x5d, 0x17, 0xc7, 0x27, 0xe7, 0x84, 0x7, 0xb9, 0x56 },
        },
        {
            .msglen = 218,
            .result = { 0x6c, 0xe2, 0x55, 0x53, 0x36, 0x41, 0xe2, 0x99, 0xac, 0xb2, 0x67, 0xc1, 0xfe, 0x63, 0xb4, 0x93, 0x26, 0x50, 0x2e, 0xe, 0xac, 0xf7, 0x66, 0x20, 0x6, 0xb7, 0xb9, 0x98, 0x20, 0x17, 0xfa, 0x2d },
        },
        {
            .msglen = 219,
            .result = { 0xef, 0x47, 0xb1, 0x44, 0x60, 0xf2, 0x1a, 0xa5, 0x99, 0x8d, 0xd8, 0x7c, 0x80, 0x22, 0x2a, 0xac, 0x9, 0x27, 0xf, 0xc9, 0xb8, 0xa8, 0xfb, 0xec, 0x41, 0x23, 0x2b, 0x9b, 0x20, 0xed, 0xef, 0x5e },
        },
        {
            .msglen = 220,
            .result = { 0xea, 0x10, 0xab, 0xd, 0x8a, 0x2f, 0xff, 0xe2, 0x21, 0xb7, 0x8b, 0xff, 0x13, 0xa0, 0xa9, 0x4c, 0x78, 0x85, 0x35, 0xdf, 0x8c, 0xb8, 0x82, 0x57, 0x98, 0x5e, 0xc8, 0xe5, 0x95, 0x43, 0x14, 0x7e },
        },
        {
            .msglen = 221,
            .result = { 0xb6, 0x3b, 0x58, 0x81, 0xab, 0x62, 0xa9, 0xf3, 0x19, 0x1f, 0x3c, 0xe1, 0x21, 0x2f, 0x2, 0xc9, 0xdb, 0x8d, 0xd4, 0x7f, 0x38, 0x11, 0x1b, 0x18, 0x5c, 0xb, 0xa3, 0xa8, 0x38, 0xe4, 0xc4, 0x55 },
        },
        {
            .msglen = 222,
            .result = { 0xb, 0xc3, 0x3f, 0xaa, 0x9c, 0xff, 0x53, 0xb8, 0xe1, 0xa6, 0xd3, 0x43, 0x8e, 0xa6, 0x48, 0x7b, 0x5b, 0x4, 0x31, 0xc1, 0xad, 0x9d, 0x8, 0x39, 0xac, 0xee, 0x1, 0x87, 0xd3, 0x91, 0x7e, 0x7b },
        },
        {
            .msglen = 223,
            .result = { 0xb3, 0xd0, 0x23, 0x54, 0xc2, 0x1d, 0xd6, 0x23, 0x70, 0xe0, 0xc2, 0x61, 0x52, 0xa2, 0xbc, 0x3b, 0x6, 0x11, 0x80, 0xb2, 0xfa, 0x17, 0x26, 0x74, 0x90, 0x61, 0x46, 0x35, 0x66, 0x1a, 0xe9, 0x51 },
        },
        {
            .msglen = 224,
            .result = { 0x0, 0xab, 0x82, 0xf2, 0xaf, 0x44, 0x95, 0x58, 0x31, 0x2a, 0x20, 0x4c, 0x2c, 0xdc, 0x80, 0xfd, 0x71, 0x7a, 0xf4, 0x34, 0xe6, 0x7e, 0xe2, 0xff, 0x6e, 0xc2, 0xf, 0x82, 0x65, 0x1e, 0x6d, 0xe6 },
        },
        {
            .msglen = 225,
            .result = { 0x6, 0x67, 0x32, 0x34, 0x96, 0xb5, 0x7a, 0x86, 0xda, 0x89, 0xd6, 0x9b, 0x1e, 0x23, 0x40, 0xfb, 0xd, 0xdf, 0xce, 0xdf, 0x83, 0x14, 0xb3, 0xe2, 0x2c, 0xd, 0x70, 0x18, 0xde, 0x87, 0xc6, 0x65 },
        },
        {
            .msglen = 226,
            .result = { 0x7b, 0x2a, 0x1c, 0xc5, 0xc6, 0x2e, 0xb4, 0xed, 0x19, 0x75, 0x50, 0x8d, 0xe5, 0x74, 0xe9, 0x31, 0x32, 0x88, 0x36, 0x6a, 0x61, 0x10, 0x6c, 0xad, 0x25, 0xb5, 0x72, 0x8a, 0x4a, 0x9, 0x18, 0x7c },
        },
        {
            .msglen = 227,
            .result = { 0xa6, 0x34, 0x75, 0xcb, 0x1a, 0xbb, 0x6d, 0x4a, 0xfb, 0x81, 0x67, 0xd1, 0x46, 0xac, 0x59, 0xd, 0x86, 0x5b, 0xd5, 0x71, 0xb6, 0x52, 0xd2, 0xca, 0xdb, 0x31, 0x45, 0x97, 0x61, 0x5f, 0x77, 0x62 },
        },
        {
            .msglen = 228,
            .result = { 0x4, 0x7d, 0x3a, 0x50, 0x35, 0x1f, 0x7e, 0x94, 0x18, 0xe0, 0xeb, 0xc, 0xe7, 0x27, 0x85, 0x54, 0x4c, 0x33, 0x51, 0xf5, 0x51, 0x2e, 0x40, 0x88, 0x67, 0x36, 0x6b, 0x5d, 0x7c, 0xb1, 0x6, 0xc7 },
        },
        {
            .msglen = 229,
            .result = { 0x59, 0x7f, 0x6c, 0x70, 0x8c, 0x79, 0xf8, 0x9f, 0xb2, 0xb1, 0x5e, 0xc4, 0xe9, 0x26, 0x8c, 0xae, 0xc0, 0xfc, 0x48, 0x18, 0xe0, 0xa5, 0xab, 0xb4, 0x68, 0x23, 0x2c, 0x11, 0xf9, 0x1e, 0x11, 0x62 },
        },
        {
            .msglen = 230,
            .result = { 0x16, 0xe9, 0x5, 0x1c, 0xfd, 0x35, 0x95, 0xda, 0xad, 0xb4, 0xbd, 0xdc, 0x7b, 0xdb, 0xc3, 0x19, 0x56, 0x88, 0x42, 0x38, 0x58, 0x2f, 0x63, 0x5b, 0x73, 0x6e, 0xfd, 0x68, 0x24, 0x27, 0xce, 0xa3 },
        },
        {
            .msglen = 231,
            .result = { 0x7b, 0xba, 0x9e, 0x1a, 0xdb, 0xbb, 0x9b, 0x17, 0xe, 0x7, 0xbc, 0x8b, 0xbd, 0xdf, 0xab, 0x8b, 0xe7, 0xea, 0x39, 0xba, 0x20, 0xce, 0xd5, 0x83, 0x89, 0xe3, 0x27, 0xa3, 0xbe, 0x25, 0xb0, 0x4 },
        },
        {
            .msglen = 232,
            .result = { 0x11, 0x4a, 0xc5, 0xc9, 0x7f, 0x98, 0xeb, 0x92, 0xd3, 0x76, 0x4f, 0xc0, 0xd8, 0x8c, 0xfd, 0xe7, 0xf3, 0x1d, 0xfd, 0xbd, 0x36, 0x88, 0x48, 0xd6, 0xc7, 0x8c, 0x9f, 0x2c, 0xb2, 0x72, 0x14, 0xb9 },
        },
        {
            .msglen = 233,
            .result = { 0xb0, 0x67, 0xf9, 0x62, 0xb9, 0xa1, 0xc0, 0xe5, 0xb, 0x22, 0x4b, 0xe7, 0x84, 0x6b, 0x8c, 0xa9, 0xa, 0xfa, 0xd7, 0xe7, 0x5b, 0x7b, 0xc, 0xe0, 0x6e, 0x47, 0x24, 0xf8, 0xc7, 0xf5, 0x6b, 0x14 },
        },
        {
            .msglen = 234,
            .result = { 0x77, 0x25, 0x61, 0xfd, 0xc8, 0x39, 0x3a, 0x72, 0x74, 0x9, 0x8b, 0x49, 0x7d, 0xa3, 0x42, 0x66, 0xb, 0x5a, 0xa5, 0x53, 0x70, 0xbe, 0x69, 0x96, 0x69, 0xbb, 0x7a, 0x8a, 0x12, 0xa, 0x7, 0x14 },
        },
        {
            .msglen = 235,
            .result = { 0x10, 0x16, 0xbb, 0x95, 0x96, 0xb3, 0x87, 0xf6, 0x12, 0x5d, 0xcc, 0x9, 0xd3, 0xcd, 0xf8, 0x44, 0x2, 0x62, 0x96, 0xde, 0x7c, 0xa2, 0x66, 0x12, 0x4e, 0x86, 0x32, 0x11, 0xf1, 0x18, 0x38, 0x79 },
        },
        {
            .msglen = 236,
            .result = { 0x42, 0x91, 0x46, 0x5c, 0xa6, 0xae, 0x5c, 0x68, 0xc8, 0xb9, 0xa8, 0x38, 0xea, 0x18, 0x71, 0x63, 0xae, 0x9c, 0xc6, 0xd9, 0x22, 0xe4, 0xfa, 0x85, 0x9c, 0x94, 0xd4, 0xb8, 0xab, 0x98, 0xe, 0xf0 },
        },
        {
            .msglen = 237,
            .result = { 0x2b, 0x6b, 0xc, 0x10, 0x13, 0x80, 0x8e, 0xc3, 0x8a, 0xcd, 0xe7, 0xf0, 0x21, 0x1c, 0x1c, 0x7e, 0x61, 0x5e, 0x5, 0x37, 0xde, 0x5e, 0x9f, 0x2f, 0x8a, 0xe4, 0x31, 0x0, 0x63, 0x90, 0xc8, 0xa3 },
        },
        {
            .msglen = 238,
            .result = { 0xf5, 0x12, 0xb6, 0x60, 0xfe, 0x6c, 0xe5, 0xd2, 0x8b, 0x2e, 0xc9, 0x37, 0x66, 0xf8, 0xd5, 0x52, 0x28, 0x76, 0x35, 0xba, 0x4b, 0xf0, 0x2c, 0xbd, 0x47, 0x14, 0x3, 0x6b, 0x47, 0x58, 0xf8, 0x16 },
        },
        {
            .msglen = 239,
            .result = { 0xfa, 0x6c, 0x94, 0x22, 0xb, 0x98, 0x3b, 0x74, 0x18, 0xfa, 0xb8, 0xda, 0x55, 0x6c, 0xa2, 0xf5, 0x5b, 0xe4, 0x8, 0x99, 0xfa, 0xf7, 0xb4, 0x57, 0xfa, 0x7d, 0x8a, 0xc, 0x6a, 0xe1, 0xca, 0xc2 },
        },
        {
            .msglen = 240,
            .result = { 0x2b, 0x59, 0x71, 0x1e, 0x37, 0x8b, 0x24, 0x39, 0x98, 0x50, 0x79, 0x3b, 0xde, 0x2c, 0xce, 0xc6, 0xd, 0xf7, 0x53, 0xb6, 0x12, 0x2d, 0x70, 0x53, 0xdf, 0xbe, 0x57, 0x56, 0x87, 0xee, 0x9f, 0x12 },
        },
        {
            .msglen = 241,
            .result = { 0x38, 0xf6, 0x60, 0x9d, 0x7, 0x23, 0x85, 0xcc, 0x8e, 0x4a, 0xe5, 0xf1, 0xaf, 0x31, 0x9b, 0x97, 0xda, 0x34, 0x80, 0x6c, 0x23, 0x79, 0x69, 0x33, 0x87, 0xa6, 0x2d, 0x8d, 0xd4, 0x8e, 0x66, 0x2e },
        },
        {
            .msglen = 242,
            .result = { 0x88, 0x5d, 0xc8, 0x2, 0xf4, 0x76, 0xe4, 0x4e, 0xae, 0xcc, 0x9, 0x49, 0xc, 0x56, 0x7, 0x22, 0xd, 0x8f, 0xb0, 0xb4, 0x68, 0x1d, 0xb5, 0xaf, 0x89, 0xb0, 0xab, 0x3b, 0x37, 0x39, 0x88, 0x37 },
        },
        {
            .msglen = 243,
            .result = { 0x10, 0xb1, 0x44, 0x4a, 0xe2, 0xfc, 0x17, 0xc0, 0x7d, 0x39, 0x62, 0xb3, 0x45, 0x9b, 0xbe, 0xf8, 0x79, 0x2e, 0xe2, 0xfd, 0x59, 0x8e, 0xb5, 0x6c, 0x1d, 0xf5, 0x8b, 0x50, 0xad, 0x28, 0x93, 0xf },
        },
        {
            .msglen = 244,
            .result = { 0xf6, 0x5d, 0xce, 0x54, 0x13, 0x2, 0x3f, 0x5b, 0x4e, 0x72, 0xc8, 0xe1, 0x42, 0x83, 0xe7, 0x52, 0xde, 0x6f, 0xe6, 0x93, 0x15, 0xab, 0xe8, 0x1f, 0x53, 0x1d, 0xc5, 0xcd, 0xf3, 0x98, 0xf0, 0x9e },
        },
        {
            .msglen = 245,
            .result = { 0x56, 0xf1, 0x6f, 0xe1, 0xd7, 0x0, 0x33, 0x65, 0xe1, 0x61, 0x79, 0x15, 0x78, 0x5f, 0x98, 0x32, 0x4d, 0x11, 0x99, 0xf1, 0x14, 0xf2, 0xdc, 0x10, 0x7, 0x0, 0xaf, 0xea, 0x4, 0xd, 0x99, 0x33 },
        },
        {
            .msglen = 246,
            .result = { 0xfc, 0xcc, 0x27, 0x84, 0xc0, 0xbf, 0xf4, 0x52, 0xf2, 0x39, 0x1f, 0xb5, 0x5e, 0x8a, 0x17, 0xd2, 0x53, 0xff, 0x3c, 0x9b, 0x53, 0x25, 0x14, 0xed, 0xca, 0x1c, 0x88, 0xa9, 0x86, 0x5c, 0x50, 0xfe },
        },
        {
            .msglen = 247,
            .result = { 0xf3, 0x9f, 0x18, 0x4a, 0x35, 0xf4, 0xb3, 0x6, 0xac, 0xc4, 0x8c, 0x8e, 0xe7, 0x87, 0x0, 0x11, 0x63, 0xea, 0xba, 0x6a, 0x3f, 0x12, 0xa1, 0x4b, 0xf5, 0x6d, 0xbd, 0xeb, 0xcf, 0xfe, 0x6, 0x6d },
        },
        {
            .msglen = 248,
            .result = { 0x78, 0x69, 0xfc, 0x51, 0x5e, 0x48, 0xda, 0xb3, 0x81, 0xcb, 0x88, 0xf1, 0xc, 0x7, 0xeb, 0x16, 0x18, 0x2e, 0x6d, 0x85, 0x9d, 0xc0, 0x80, 0xea, 0xab, 0x39, 0xd3, 0x6a, 0xa8, 0x10, 0xe3, 0x26 },
        },
        {
            .msglen = 249,
            .result = { 0xee, 0xfb, 0xd, 0x72, 0x20, 0x48, 0x71, 0x10, 0x46, 0xc2, 0xb0, 0x5c, 0x11, 0xcf, 0x82, 0x2e, 0x6e, 0x3e, 0x3f, 0x16, 0xfa, 0x71, 0x80, 0x5f, 0x51, 0x50, 0x3e, 0xa2, 0x5d, 0xa1, 0x66, 0xbe },
        },
        {
            .msglen = 250,
            .result = { 0x72, 0x9c, 0x3, 0xe9, 0x25, 0xda, 0xaf, 0x7b, 0x40, 0x10, 0xe2, 0x85, 0xe0, 0x5b, 0xce, 0x55, 0x72, 0x1f, 0xfa, 0x54, 0x1c, 0x96, 0x48, 0xb, 0x55, 0x26, 0x53, 0x7d, 0xa9, 0xc5, 0x27, 0xd0 },
        },
        {
            .msglen = 251,
            .result = { 0x6f, 0x40, 0xd5, 0x72, 0x3b, 0x1f, 0x99, 0x9e, 0x7, 0x57, 0xbb, 0x57, 0x6c, 0x7e, 0x47, 0xb2, 0x76, 0x37, 0x5e, 0x64, 0x21, 0x51, 0x3c, 0xe8, 0x16, 0xda, 0x5f, 0x9f, 0xc9, 0x5b, 0xc3, 0x70 },
        },
        {
            .msglen = 252,
            .result = { 0x77, 0x3b, 0x47, 0x74, 0x0, 0xfd, 0x74, 0x95, 0xe, 0x59, 0xf6, 0xf, 0xcf, 0x9a, 0xb3, 0xbc, 0x9f, 0xd9, 0x3c, 0x3a, 0x30, 0x1c, 0x1f, 0x4d, 0x53, 0xbe, 0xce, 0x4c, 0xa1, 0x8b, 0xc1, 0x22 },
        },
        {
            .msglen = 253,
            .result = { 0xd0, 0xbe, 0x9, 0xb3, 0xa9, 0xa4, 0xb2, 0x46, 0x99, 0x40, 0x40, 0x6b, 0x52, 0x54, 0x3b, 0xfe, 0x94, 0x37, 0xf9, 0xc, 0xc2, 0xc2, 0x66, 0x3, 0xa5, 0x8c, 0x42, 0xae, 0x9d, 0xf8, 0x47, 0x87 },
        },
        {
            .msglen = 254,
            .result = { 0x85, 0x78, 0x21, 0xf4, 0xa, 0xee, 0xa3, 0x59, 0xe0, 0xb2, 0xd7, 0x7, 0x34, 0x5e, 0x57, 0xdc, 0xdd, 0x9a, 0xfa, 0x2c, 0xfd, 0xb6, 0xee, 0xa9, 0x14, 0xc0, 0x17, 0x86, 0xbf, 0x7b, 0xfe, 0x4b },
        },
        {
            .msglen = 255,
            .result = { 0x59, 0x52, 0x50, 0x4, 0xb6, 0x28, 0xf9, 0x28, 0x7f, 0x6c, 0x37, 0xba, 0xfb, 0xb2, 0x58, 0xe7, 0xa, 0xac, 0x6c, 0x4a, 0xef, 0x66, 0x6, 0x7b, 0x1, 0x1f, 0x4c, 0xa4, 0xe5, 0xe5, 0x29, 0x5d },
        },
        {
            .msglen = 256,
            .result = { 0x52, 0x1a, 0x14, 0xc3, 0xb4, 0x85, 0xf0, 0xb5, 0x82, 0xe9, 0x40, 0xc2, 0xc, 0x6d, 0x59, 0x64, 0x86, 0x7d, 0xa, 0x3b, 0x7f, 0x85, 0x78, 0xea, 0xea, 0x66, 0x48, 0xfa, 0x4a, 0xde, 0x5c, 0xd3 },
        },
    };

    for (int i = 0; i < sizeof(results) / sizeof(hmac_result); i++) {
        TEST_ESP_OK(esp_hmac_calculate(HMAC_KEY5, message, results[i].msglen, hmac));
        TEST_ASSERT_EQUAL_HEX8_ARRAY(results[i].result, hmac, sizeof(hmac));
    }
}

TEST_CASE("HMAC 'upstream' wait lock", "[hw_crypto]")
{
    uint8_t hmac[32];

    // 257 characters of pseudo-Latin from lipsum.com (not Copyright)
    const char *message = "Deleniti voluptas explicabo et assumenda. Sed et aliquid minus quis. Praesentium cupiditate quia nemo est. Laboriosam pariatur ut distinctio tenetur. Sunt architecto iure aspernatur soluta ut recusandae. Ut quibusdam occaecati ut qui sit dignissimos eaque..";

    setup_keyblock(EFUSE_BLK_KEY5, ESP_EFUSE_KEY_PURPOSE_HMAC_UP);

    static const  hmac_result results[] = {
        {
            .msglen = 255,
            .result = { 0x59, 0x52, 0x50, 0x4, 0xb6, 0x28, 0xf9, 0x28, 0x7f, 0x6c, 0x37, 0xba, 0xfb, 0xb2, 0x58, 0xe7, 0xa, 0xac, 0x6c, 0x4a, 0xef, 0x66, 0x6, 0x7b, 0x1, 0x1f, 0x4c, 0xa4, 0xe5, 0xe5, 0x29, 0x5d },
        },
        {
            .msglen = 256,
            .result = { 0x52, 0x1a, 0x14, 0xc3, 0xb4, 0x85, 0xf0, 0xb5, 0x82, 0xe9, 0x40, 0xc2, 0xc, 0x6d, 0x59, 0x64, 0x86, 0x7d, 0xa, 0x3b, 0x7f, 0x85, 0x78, 0xea, 0xea, 0x66, 0x48, 0xfa, 0x4a, 0xde, 0x5c, 0xd3 },
        },
    };

    for (int i = 0; i < sizeof(results) / sizeof(hmac_result); i++) {
        TEST_ESP_OK(esp_hmac_calculate(HMAC_KEY5, message, results[i].msglen, hmac));
        TEST_ASSERT_EQUAL_HEX8_ARRAY(results[i].result, hmac, sizeof(hmac));
    }
}

#endif // CONFIG_ESP_SECURITY_ENABLE_FPGA_TESTS

/**
 * This test is just a parameter test and does not write any keys to efuse.
 * It can be done safely on any chip which supports HMAC.
 */
TEST_CASE("HMAC key out of range", "[hw_crypto]")
{
    uint8_t hmac[32];

    // 257 characters of pseudo-Latin from lipsum.com (not Copyright)
    const char *message = "Deleniti voluptas explicabo et assumenda. Sed et aliquid minus quis.";

    TEST_ASSERT_EQUAL(ESP_ERR_INVALID_ARG, esp_hmac_calculate(HMAC_KEY0 - 1, message, 47, hmac));
    TEST_ASSERT_EQUAL(ESP_ERR_INVALID_ARG, esp_hmac_calculate(HMAC_KEY5 + 1, message, 47, hmac));
}
