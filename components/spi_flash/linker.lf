[mapping:spi_flash]
archive: libspi_flash.a
entries:
    if APP_BUILD_TYPE_PURE_RAM_APP = n:
        spi_flash_chip_generic (noflash)
        spi_flash_chip_issi (noflash)
        spi_flash_chip_mxic (noflash)
        spi_flash_chip_gd (noflash)
        spi_flash_chip_winbond (noflash)
        spi_flash_chip_boya (noflash)
        spi_flash_chip_th (noflash)
        memspi_host_driver (noflash)
        flash_brownout_hook (noflash)
        spi_flash_wrap (noflash)

        if IDF_TARGET_ESP32S3 = y:
            spi_flash_chip_mxic_opi (noflash)

        if SPI_FLASH_HPM_ON = y:
            spi_flash_hpm_enable (noflash)

        if ESPTOOLPY_OCT_FLASH = y || ESPTOOLPY_FLASH_MODE_AUTO_DETECT = y:
            spi_flash_oct_flash_init (noflash)
