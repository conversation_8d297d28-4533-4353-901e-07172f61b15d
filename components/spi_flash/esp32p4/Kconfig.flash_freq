choice ESPTOOLPY_FLASHFREQ
    prompt "Flash SPI speed"
    default ESPTOOLPY_FLASHFREQ_40M if ESP32P4_REV_MIN_0
    default ESPTOOLPY_FLASHFREQ_80M
    config ESPTOOLPY_FLASHFREQ_80M
        bool "80 MHz"
        depends on !ESP32P4_REV_MIN_0
    config ESPTOOLPY_FLASHFREQ_40M
        bool "40 MHz"
    config ESPTOOLPY_FLASHFREQ_20M
        bool "20 MHz"
endchoice

config ESPTOOLPY_FLASHFREQ_VAL
    int
    default 20 if ESPTOOLPY_FLASHFREQ_20M
    default 40 if ESPTOOLPY_FLASHFREQ_40M
    default 80 if ESPTOOLPY_FLASHFREQ_80M
    default 120 if ESPTOOLPY_FLASHFREQ_120M
