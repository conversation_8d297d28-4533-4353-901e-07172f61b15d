/*
 * SPDX-FileCopyrightText: 2022-2023 Espressif Systems (Shanghai) CO LTD
 *
 * SPDX-License-Identifier: Apache-2.0
 *
 * File generated by gen_digital_signature_tests.py
 */

#include "hal_crypto_common.h"

#define NUM_HMAC_KEYS 3

static const uint8_t test_hmac_keys[NUM_HMAC_KEYS][32] = {
     { 0x28, 0x5e, 0x24, 0x5c, 0x93, 0x57, 0x14, 0x75, 0x3b, 0x2d, 0x80, 0x31, 0x4d, 0x6f, 0xe2, 0x55, 0x4e, 0x2c, 0xcd, 0xd5, 0xec, 0xfa, 0x1c, 0x93, 0xac, 0xe8, 0x57, 0x90, 0x93, 0x9f, 0x4b, 0xf9 },
     { 0xee, 0x94, 0xa4, 0x30, 0xb6, 0x3d, 0xf2, 0x4f, 0x00, 0x45, 0xa8, 0xca, 0x22, 0xff, 0xc8, 0xad, 0xb8, 0x0c, 0xe7, 0x38, 0x1f, 0xbc, 0x50, 0xde, 0xde, 0xa1, 0x2a, 0x1e, 0xed, 0x9f, 0x38, 0x62 },
     { 0x5f, 0xce, 0xad, 0x48, 0x15, 0xfa, 0xdc, 0xb0, 0xcc, 0xb8, 0x43, 0xd5, 0x31, 0x73, 0xaa, 0x7f, 0xeb, 0x4a, 0x87, 0x24, 0x74, 0x09, 0x61, 0x40, 0x87, 0xb2, 0xf7, 0x12, 0xa5, 0x21, 0xbd, 0x82 },
};

#define NUM_MESSAGES 10

static const uint32_t test_messages[NUM_MESSAGES][3072/32] = {
        // Message 0
        { 0xc3eeaca0, 0x145f499b, 0x4f6672c8, 0xe3be2eb4, 0x9bfc988d, 0x71ec0875, 0x8164f886, 0x17764392, 0x9739494d, 0x157fbaff, 0x22261fcd, 0xda824830, 0x463fb24c, 0x82264d6d, 0xf44cbee0, 0xfa2b8f70, 0x690ada68, 0x9a6f2fa1, 0x90e906d7, 0xbb77c9ee, 0x8cf45369, 0x97c74667, 0x65e31e23, 0xfbcd0d1a, 0x48743eeb, 0xd712ef62, 0x9c63d708, 0x776f25f6, 0x3eb30e2f, 0x428cbb76, 0xfd52cdbe, 0x0ab79293, 0x92444804, 0x989b940f, 0x49a74f17, 0xf2e98a77, 0x95682db5, 0xcda9c931, 0xe08ef8e0, 0x96e13ff5, 0x98475c25, 0x65667a05, 0xf3fca982, 0x1859b057, 0x01c91f26, 0xbf65df10, 0xa780ce07, 0x1fdb409a, 0x103d3382, 0xa369797c, 0xf028dfb6, 0xc4818df7, 0x08e01187, 0x2ea49432, 0x8174befe, 0xd7c91f9a, 0xc639985a, 0x1516863d, 0xfbaf413b, 0x5dc0faa9, 0x2248ade2, 0xc9cbb936, 0x91498a3d, 0xe72c22ff, 0xdfec98cf, 0x22ae559f, 0x5f93e271, 0xf879f63e, 0x6e41f357, 0x88cdfbee, 0xe5079bf1, 0xcf583f87, 0x67df5bee, 0xfbf2176c, 0x200f5f17, 0xd65e292e, 0x55018a0b, 0x1d89b23f, 0xfe702a98, 0x67dc3045, 0xbcde65d9, 0xa5b02ce4, 0x3f5475c2, 0x6ece0e1d, 0x64173690, 0xef552745, 0xde23a986, 0x6b2074f5, 0x781083ec, 0x42a8e469, 0x9075a526, 0x5fe2a8fc, 0xd3eef969, 0x3e270a4b, 0x01f0a7c8, 0x1ea42d07 },
        // Message 1
        { 0x7a1c7286, 0x7073526a, 0xa3b5dee2, 0xb20475b8, 0xe2ef8cb6, 0x3f3101e0, 0xb4008e2a, 0x3bb40a0e, 0x0f02dc62, 0x829a1f98, 0xa597c0b8, 0x015621ff, 0x34dec9d2, 0xd71bf2a2, 0xeeb0ca8e, 0x2b808183, 0x0ffad76f, 0x251ccbe1, 0xcc7a1d6c, 0x37cc14ae, 0x83ea4a30, 0xf57572aa, 0x15534228, 0xd9663dbb, 0x12e5fe52, 0x7b55d2cd, 0x5e303152, 0xe8665060, 0x14a16b0d, 0xffcc6665, 0xedb83d5c, 0xc02fc630, 0x67257bc7, 0x3890d4ca, 0x8e99f454, 0x84e52fbc, 0x99e09a32, 0xa431f208, 0x9b01d2dc, 0xd494be38, 0xdd536f81, 0x459b70a1, 0x8bdf94c7, 0xb11fbce6, 0x799fedce, 0x9f79cc5d, 0x68848129, 0x8629a36b, 0x5cf0dd2d, 0xb6cf3609, 0x64a1584f, 0x819a68f7, 0x77ad79b3, 0x9a8e8478, 0x25b94b80, 0xa2e9c895, 0xb0bbc126, 0x6291b5a7, 0x2ebf03e6, 0x7a080b9b, 0x3f9c8b9e, 0xa1ac613a, 0xe0315545, 0xeefbb06e, 0x7a0d621f, 0xc9983872, 0x40a42164, 0xb45363bb, 0xdc601819, 0xf596576e, 0xc96ab311, 0x3bbf9993, 0x96735365, 0xf8640fa2, 0x298597e6, 0xa42e997e, 0xc9fcd5e2, 0x0e69250e, 0x01a07595, 0x4904c73a, 0x0a940d6e, 0x83ecabbc, 0x10eb1454, 0x58cafd34, 0xe6c43dd7, 0x31168a19, 0xa24a1e81, 0x3b3de706, 0x68774552, 0x88175387, 0xef6cfc05, 0x4f060806, 0x56ae9157, 0xf7d4ad91, 0x2bb5f817, 0x5f7d11d6 },
        // Message 2
        { 0xa1dea256, 0x76df4ce8, 0x623f895d, 0x47276dbf, 0xa07b8313, 0xbe6514c0, 0x7b4de323, 0x1ff58fd6, 0xd7bf4cb0, 0xe3f241c6, 0x7875dd19, 0x656978e1, 0x922c9782, 0x8d944859, 0xb7a5019f, 0x0e0e74c3, 0x44d2f82e, 0x6671cc9a, 0x97ef9c1c, 0x3a900f53, 0x3246be9a, 0xa2d44e99, 0x1ed77873, 0xa139826d, 0xa7849972, 0x5e1b37e4, 0xf5979ce1, 0xd116a9e0, 0x9948fdac, 0xb08d0cac, 0x59a640a0, 0xebffeac6, 0x944b3101, 0x3d7b9cf2, 0x98404104, 0x712b4df0, 0x9b96bad7, 0x297dea1f, 0xe49df71a, 0xb8a4e00f, 0x69cc679c, 0x4f3fe52b, 0x67cf293c, 0xf28fe411, 0x504fe6c6, 0xe4484e46, 0xe15598e5, 0x45fc270f, 0x176f441c, 0xca57dcfb, 0xb1c85752, 0x339085d6, 0x4512048f, 0x52990e46, 0xb7f320b0, 0x681f291f, 0x8896c088, 0x227c4037, 0x576f7cdb, 0x7401fa6c, 0xc619e211, 0x0a9858de, 0x39d5ee5b, 0x554b6b6d, 0xa677922b, 0xdecd414c, 0x3126c77d, 0x07d038a6, 0x9e530272, 0x31b1b76e, 0x472ebb79, 0x22c6a00d, 0x0acce5e2, 0xf14ddc6d, 0x0a0ec86f, 0xf7358b56, 0xfab446eb, 0x75fff60e, 0x648c06a8, 0x6727ad03, 0xf754d2e7, 0x8f2c6fa6, 0x8982916d, 0x95103f6d, 0x4e89ee28, 0x7b6fb0cd, 0x71f211a4, 0xd1412fad, 0x416c4623, 0x7ded29b6, 0x6e03e6b3, 0x7f1b0af8, 0x38129536, 0xd42af72d, 0xa88fcdd2, 0x56f52ffb },
        // Message 3
        { 0xcba3ec40, 0x8e9870af, 0x435e826a, 0x24f0043a, 0xe135b65d, 0xa5269a0a, 0xae1e5c71, 0x5f92ddae, 0x468ea421, 0xe3fa1cae, 0x13753cef, 0xb5f38fc5, 0x657323cb, 0x03f917be, 0x43be579e, 0xc377242b, 0x143ba2d1, 0x930eefbc, 0xed0ce77b, 0xa68cb224, 0x5119d5e5, 0x1d29ba06, 0xea6a19a3, 0x58561410, 0x61d4249c, 0x06c89a90, 0x7cc56f0b, 0x696825e3, 0x2c6c1f6c, 0x7132b88a, 0x2e3afcc2, 0x3de924ab, 0x9ebaf1b7, 0xf1bd2a4b, 0x695af713, 0x50c8d4fc, 0x7b3ba186, 0x4f117ed4, 0x499e9aeb, 0x47bcc734, 0x5d220224, 0x2cd763f6, 0xefe6ce2b, 0xd2c71a86, 0x16f45158, 0xfe216ee5, 0x363fbef9, 0xc3ddf66a, 0x6ae95b6f, 0x57ee7f8d, 0xcb022b41, 0x5ae22c6d, 0xbdcce81d, 0xd4285f21, 0xa84a2825, 0x3b9addfa, 0x63ae24d3, 0x15cc9e4a, 0xe55c69d7, 0xb0e3b2ba, 0x79644a64, 0xfc027806, 0xf44cdf5f, 0x57d978a5, 0x62db858d, 0x4f316e10, 0x3f592569, 0xe77939af, 0xb1a58024, 0xef48c542, 0xd5f3bbcf, 0x717535b9, 0x7f2a5491, 0xc74b15e5, 0x30bd7b9f, 0xa9ab7827, 0x59a2845e, 0xc2926b40, 0xc2402dbe, 0xaf3c7ff8, 0x919cce5a, 0x66e21bc8, 0xd6095e37, 0x3d3ebcbb, 0xc0b8c2f7, 0xea5a3027, 0xb1610dbe, 0x55d5a073, 0x42c7f62b, 0x1daac5d7, 0xc0add0c4, 0x5d0898ee, 0xa11cc859, 0x68beb29c, 0x902c7eb0, 0x7b202e0f },
        // Message 4
        { 0xf07c2643, 0x38e2657b, 0xae5f2b4b, 0xdb2e2d35, 0x03c4fee6, 0x6ef7341d, 0xe16ea957, 0x8fc81e9d, 0x9b15db0e, 0x2a1a5c6b, 0x4d5a6630, 0x8e3b7905, 0x9ccfbad6, 0x8e86717e, 0x35043adb, 0xe3026b2b, 0x843f4b71, 0x63f4f111, 0x2e185601, 0x3b9916d3, 0x8a736073, 0xa801feb1, 0x46548286, 0xb64e9b7e, 0xf87fa1d9, 0x20928964, 0x8379106f, 0x2a857cc8, 0x0e219f79, 0x5929ab6c, 0x7beae2c5, 0xbf6a0fc2, 0xf7be862b, 0xeaaf8ea2, 0xf2d6c3e1, 0x570499fe, 0x475ecbd1, 0xbd49866a, 0x8dcbaeeb, 0xfa3f75ae, 0x6e9cbe8a, 0x25a4d6c3, 0x8d9fd682, 0x8dce79d6, 0x24b64f90, 0x8b1b8cea, 0x8e590935, 0xd003ecbb, 0xe7045354, 0x349643c7, 0xf4065cb9, 0xe12f3326, 0x491d220d, 0xe6b6278a, 0xe569bee5, 0x63caa061, 0x26d866bc, 0x42f25022, 0xd343f63c, 0x6f0a3c27, 0xfa81f733, 0x761c2631, 0xc6d0893f, 0xcd292cab, 0x176c2807, 0xa09d5b50, 0x02ac2015, 0x5ca43818, 0x119c633b, 0x223a9e33, 0x56a37a80, 0x5509dd1d, 0x059214fb, 0x5cdc01d6, 0x85240177, 0xc0cd83ae, 0xa535a45b, 0x3eb873d8, 0x7b9391ce, 0x5d096154, 0x0171c488, 0xf2103cff, 0x4803f890, 0xcbd5c5b1, 0x11f1b655, 0xd5561b02, 0x7f0316a3, 0xaea54093, 0x8d0e5137, 0x72366487, 0xb948ce91, 0x01e2dc76, 0x8e9e16c2, 0x9b6e2a78, 0x7c43df35, 0xbe408f76 },
        // Message 5
        { 0xfabd701b, 0xa1590dcc, 0x3db459a7, 0x2308dcf6, 0xace3e018, 0xaabd3ec9, 0xba381222, 0xbdd3d0d8, 0x1966a84b, 0x6d5b8e49, 0xbaabe2da, 0x8c49e82c, 0x401fe8fc, 0x1c27a0c3, 0x49c79172, 0x62cb5bff, 0x01e739a4, 0x357b356f, 0x82ae4212, 0xa1aada88, 0x11a256d9, 0xa46ca099, 0xefb03762, 0x08a5f2c4, 0x8812d7a6, 0x4d04d25f, 0x92dbaa4c, 0x1bb2bd96, 0xf7fb234f, 0xb34985a4, 0xef0ab5fb, 0x00931f0c, 0x176d6f41, 0xa3548661, 0x0229ff38, 0x223c30b9, 0xf7f924ef, 0x738ec0cc, 0xb7476ccf, 0x6ba09e97, 0xefa6d0d8, 0x0bf56501, 0x2ffa1674, 0xecb4ce9b, 0xd79d4f08, 0x47e61a5a, 0xbedc035d, 0xc5531c75, 0xc7dc0f8a, 0x1da1bda4, 0xccd6e0bd, 0x0dd1e5ea, 0xe4a9e539, 0x5ae2519b, 0x7361ec5a, 0x470990f7, 0x5dba9caf, 0x070b4f11, 0xddf7378f, 0xfd9ba564, 0x5130e86a, 0x5c69b196, 0xf9546de9, 0xb26bfb69, 0xd03c9bf3, 0x03322049, 0x5e16cbcd, 0xb7fbdd01, 0xd210b244, 0x8f580d9b, 0x09f0b90c, 0x4b740685, 0x78cb56ed, 0x75b29bfd, 0x432292c4, 0x157e6c5a, 0x361b1c48, 0xbe6fea21, 0x9008ad79, 0xcb01783c, 0xa73d5cd8, 0x7e62c315, 0xfa975554, 0x9a17934b, 0x22722bc9, 0x0c28a821, 0xd677b3b6, 0x2893fb37, 0x2e3e6509, 0x80a66f3f, 0x86f7cdaf, 0x1264e0dc, 0xf9b137d6, 0x0a8ba672, 0xbd33497c, 0x25a1b012 },
        // Message 6
        { 0x12d6a3bc, 0x56a34948, 0xcbf451e6, 0x00ddf5e7, 0xc4fdabaf, 0x98f72ae6, 0x06b1f2eb, 0xef80d089, 0x893bb2b9, 0xe585a2c7, 0x0a896cf4, 0xce868b8f, 0xd9d05320, 0x7191e6c3, 0x4fb79d31, 0x6bb85c41, 0x8c120e2e, 0xb91a5ecf, 0xb8f02bff, 0x9936daa0, 0x0df082e9, 0xf5f04457, 0x064941a1, 0x261bedcd, 0x7243821b, 0x39bbd76f, 0xac8ae627, 0x7d778bc9, 0xaf251cbd, 0x4dbd0780, 0x59e2ad8d, 0xb936f81f, 0xcfc2cbd5, 0xe5002e14, 0xf31941ea, 0xcda8badc, 0x107bb5d7, 0xdf904d88, 0x03f63fe9, 0xdcc00578, 0x23f099c0, 0xbc1dfb32, 0xee9f4fb8, 0x938a5166, 0x79589870, 0xeda6c78d, 0x613e9d94, 0xbf8be0c8, 0x2abebfde, 0x595655cc, 0x7f79eb8f, 0xee9cad71, 0xe31cb989, 0xe534b1b2, 0x1ec3e119, 0x8c17938c, 0xc8e8336f, 0x5edc4a60, 0xdf24f1c2, 0xcf066496, 0x58e4cad1, 0x651fbd95, 0xd0e0ebd6, 0x6ff25dff, 0xd19e7c2a, 0x9169f615, 0x19b1c6e0, 0x6f499521, 0xd69b20b6, 0x4860ddaa, 0x43cccd20, 0x3ea7f614, 0xaa5faabc, 0x65b79084, 0x6bfbf24d, 0x8741a5b7, 0x248702ea, 0x519dc058, 0x1fda81e2, 0x633b1f54, 0x793fdb10, 0x1b0a3669, 0xfc945e8b, 0x5f791d7f, 0xc000c3e0, 0xcabd4658, 0x1fae5fe0, 0xf69fc0d8, 0xf7f27ee6, 0x8e3d83ba, 0x8c5d20fc, 0x2af41708, 0xa9b0f827, 0xbe14eb78, 0xa7be7525, 0xf36d5783 },
        // Message 7
        { 0xffb93c13, 0xaa171f5c, 0x2dac1faa, 0x10efc463, 0xa18d50e3, 0x6fdffe88, 0x17ad6a67, 0x66d6e9ad, 0x0416d8de, 0x40b42d2d, 0x4fb5b2aa, 0xd0151eac, 0xc889dd2f, 0x58a64806, 0x4159d67a, 0x48953359, 0xd998d5b1, 0xe407a4fd, 0x3c578d6c, 0x2b7aa2ae, 0xb6947e1d, 0xa3c06991, 0xaa9199b6, 0xe64378ea, 0xe00f8347, 0x8e58515d, 0x2aa64dd9, 0x179fdffb, 0x91078aec, 0xf9d0c9ea, 0x9842f003, 0x973f8a2f, 0xc14fb956, 0x0e6b26ce, 0x619cae5e, 0x4a64ff47, 0xf104baaf, 0x107f5859, 0x25dd465a, 0xcb29219b, 0x87f6e43b, 0x27a41fe2, 0xb8f9664e, 0x4affb7ef, 0x0792bc28, 0xd006ad75, 0x4c9c8848, 0xbaf0fa0e, 0x44d46be4, 0xddffc7cd, 0x6a5c52bb, 0xe1322f3e, 0x56fdce09, 0x4e1badba, 0xe12e1c1e, 0xceba61d2, 0xbe3ab4c3, 0x048dc48c, 0x26d2b6e0, 0x7bef1532, 0x7a9ff8e6, 0xb0284b7e, 0xd6a57380, 0x28159f16, 0x5d75f5a4, 0x9de3116b, 0xc8911421, 0xbbe4b5c0, 0x04abebc7, 0xb98df244, 0xb5a960c2, 0xd1244e73, 0x5ef3579f, 0xb35f692f, 0x32d9cb9c, 0xb750939d, 0x8f52c776, 0xe227d5b6, 0xaaff1475, 0xdbb5d059, 0x35ed9b80, 0x80497958, 0x87288c3d, 0x8547ae37, 0x74583b51, 0x258cec13, 0xb044d0d1, 0x4ed71f6f, 0x4a6441dd, 0x536bbb71, 0x61d3b810, 0xaa095fe0, 0xffa278ba, 0x0c51a517, 0xaa173979, 0x9e26a393 },
        // Message 8
        { 0x9350abcc, 0x502e32d1, 0x3a3fc643, 0xca7d86e4, 0xec2c2945, 0xc6b09c1d, 0x014c68ef, 0xce9add3b, 0x9153d36b, 0xa6086b85, 0x7e306fa0, 0x6ee123a7, 0x3966e2f4, 0x87a87bd4, 0x30e541b4, 0x032d3fea, 0x3e6c9ba9, 0x267286ec, 0xb3794fe1, 0xecba7ca0, 0xea348df6, 0x46573190, 0x8acd3daf, 0x8edb0c5b, 0x241b1242, 0x5f5f7f93, 0x36f12384, 0xff61d3de, 0x8af09ddb, 0x8897efc0, 0x1d7fb2e6, 0x5871330e, 0xd1fbc501, 0xb2f1435f, 0x4a10ceea, 0x773b27d9, 0xd81c83d0, 0xb58bd832, 0xd82027a3, 0x3854683a, 0x42238bfb, 0x86daaa64, 0x44455fdd, 0xdb3763b1, 0x30faa197, 0x301c802d, 0x7f9f34c0, 0xf55dbd2d, 0x4abdc1a0, 0x30cd3a3a, 0x1aa671c4, 0xf43a1b22, 0x3ae81713, 0xc79d036d, 0x2b8ae850, 0x5e013388, 0x444d730f, 0xf7719d16, 0xf858d332, 0xde80d0e5, 0xba906252, 0x0ae3d9f0, 0xe9e064fe, 0x5473529d, 0x2634047c, 0x4e7904d2, 0xc50d57ea, 0xfc7b5873, 0xc0ec20ac, 0x9f73c76b, 0x8a6b3e96, 0x9f0e17ec, 0x13e1654b, 0x1ac7edef, 0x08fdcf5f, 0x51f67ca8, 0xb34e6cf0, 0x79f18b36, 0x8226238d, 0xb948e89d, 0xba6d6fcc, 0x6b3a2bdd, 0xfd373842, 0x69a07fd7, 0x568a3836, 0x0b67ac69, 0x7c522039, 0x2133df29, 0x5a35ef14, 0x596a234d, 0x620e2cc5, 0x2be5feeb, 0xce7cb8b4, 0xd04752cf, 0x0820b469, 0xaff3d9f3 },
        // Message 9
        { 0x08b2bdd7, 0xb9e4f5e9, 0x2298b215, 0xed9042e0, 0xd93a60dd, 0x14f08d95, 0xe9f669fe, 0x196b10fb, 0xceb6f5a5, 0xe19255c5, 0x1ebb83b2, 0xb4655473, 0xa6b79c9d, 0x88fe4dd9, 0x3546e18a, 0x6dc61f68, 0xc45d3b39, 0x061ee6ff, 0x9c85aaa3, 0x5ad24b87, 0x8ee06618, 0xc5292e42, 0x92bf8b73, 0x23124856, 0x85272862, 0xb284fbed, 0x6212d764, 0x7ee3f935, 0x3b386c35, 0xc694dd1f, 0xa6e068c2, 0xc8cad857, 0x5efd0a73, 0xceec5084, 0xfbb88a02, 0xb09d042d, 0x4e45d49f, 0xb430b6e7, 0xc63934a8, 0x212701f8, 0x6220074a, 0xd88103c3, 0x3312698b, 0x4e0bd933, 0x37b85cbd, 0x2cd97e09, 0x5014b36e, 0x57c8399f, 0xa6427d45, 0x57381c5d, 0x689adc4b, 0x48f893c3, 0x40b902a1, 0x130048cd, 0xa158e363, 0xf3a58a8a, 0x6b3ea545, 0xd6b51dc4, 0xe65db148, 0x5bf8ad29, 0x44257142, 0x92e0deb9, 0xb9cfbe30, 0x83b5cc31, 0x401feebc, 0x70d8ed1a, 0x6a77f25b, 0xed4bb4ba, 0x6a69e8a3, 0xe72c1f3a, 0xf9f67b2d, 0x64a13cdd, 0xc3268e68, 0xa5a33132, 0x6b10626a, 0xb48a00a3, 0xffc1ab2e, 0x4ae98114, 0xf571d6d5, 0x1c068302, 0xca5fe2a9, 0xc7f43921, 0x5b82d397, 0x43a210b2, 0x7b58af9e, 0x6232c22a, 0xc51f2306, 0xfcd5d566, 0x76c5e2e7, 0x8b77942e, 0xd9323119, 0xe3eb7011, 0xe8a1bc8e, 0xc02bf571, 0x90799da8, 0x76f308ea },
    };

#define NUM_CASES 6

static const encrypt_testcase_t test_cases[NUM_CASES] = {
    { /* Case 0 */
        .iv = { 0xde, 0x38, 0x70, 0x94, 0x99, 0x2f, 0x4f, 0x87, 0x43, 0x2e, 0x33, 0x7a, 0x6b, 0x32, 0x63, 0x69 },
        .p_data = {
            .Y = { 0x5e368321, 0xb41f3db3, 0x2e711e37, 0x4f6a97f5, 0x8b4fc752, 0x11cbfe55, 0x4ae73421, 0x455c1d4f, 0xcb3da5bf, 0xb382a289, 0x2e567e33, 0x4c1c820c, 0x87b0b3c7, 0x1f050f7d, 0x0494cb56, 0x5a11fd4a, 0x86ef3bc5, 0x31dec07a, 0xf1870b04, 0x212ad84f, 0xc3a9dcf6, 0x8d8cd74c, 0xf9bd58bd, 0x45256646, 0x7164175a, 0x1435f74d, 0xf7d8227b, 0x25aaa313, 0x827bc632, 0x1fdb460c, 0x7abfccd3, 0x1bc754a6, 0x27aadc29, 0xd8731ab4, 0x15001a86, 0x2b2c743b, 0x20651413, 0xfb32bbae, 0x5f24c259, 0x84fceb8d, 0xb38b15f6, 0xe0a22735, 0x77b560d0, 0x527c9771, 0x6e8878b4, 0xa713137e, 0x0393ff5f, 0xc1f2b5f8, 0x3af8c8b9, 0x6a83dae7, 0xb74f799a, 0x3efae935, 0x381a4969, 0xd9cb8ffc, 0xef7dda78, 0xe1b54951, 0x245d31e0, 0x1d700233, 0x290d4195, 0x31c9cf6b, 0xf2e50a26, 0x17c489a4, 0xde0740a4, 0xb25135ea, 0x24da6162, 0xfd8089e7, 0x3a4fa0eb, 0xcaaa255c, 0xcbdd0934, 0x1657fbe0, 0x80a92d9d, 0xb75a6f8d, 0xc35e1bcd, 0xbcf982ed, 0x1bd0891e, 0xaeece009, 0x3517a054, 0x72eefdee, 0xe2f7f4e7, 0x5f87d3b3, 0x7933ab03, 0x9d101b47, 0x237c4419, 0xcedda455, 0xe19ce6b0, 0xdb38462a, 0xd01f7fc7, 0x47eb973d, 0x0eff34c3, 0x79854fe4, 0x89bf271c, 0x558e7c1f, 0xa0ab9082, 0x99886ac6, 0xa8b4d773, 0x4f77abe5 },
            .M = { 0xfa1cc905, 0x3b3b8099, 0x9cd6aa18, 0xbd834d66, 0x57f356e3, 0xf7913de3, 0x24c91d3c, 0x151c425a, 0x0bce3548, 0x2660dd66, 0x781941a9, 0x322716f0, 0x67bf0d12, 0xe5fabedb, 0xd8648e1f, 0xc5f45815, 0xcf16b995, 0x64cd8cf0, 0x7d794a12, 0xc65ff0b8, 0x00228531, 0xc34052ff, 0xb1ed4e1a, 0x16a6be6d, 0xf6f2dc9d, 0x438401c7, 0x71c779ea, 0x351d6f25, 0x990dfb87, 0xf65effb7, 0x7376a282, 0xbe5f234a, 0x28e6970c, 0x44c72524, 0xf71b8acf, 0x549bb8b7, 0x473263ce, 0x57b8d011, 0xdb9ee8f4, 0x0d47f16e, 0x8771601f, 0x7134a5e6, 0x3c6ad49b, 0xe64cab4b, 0xa98a6360, 0x6f232933, 0xe947433b, 0xf2318bae, 0xc6c61c01, 0x957bdaba, 0xa7af31b6, 0xfc8d24c7, 0x76e2059a, 0x619c8dab, 0x6e064992, 0x8b4c6dc3, 0x2cc71b4f, 0xa87a6014, 0x3d358aa7, 0x648fccae, 0x64de212b, 0x476b8e56, 0x9fbbaf50, 0x332f9d59, 0x35d05097, 0x7f0a60c7, 0x4ef57eaf, 0x5cad3e1c, 0xe28ff283, 0x2eafcadb, 0x160dcd56, 0xee567ad3, 0xfdb0d160, 0x75c38dfc, 0x131bbb42, 0x7069e3ac, 0x6aff3592, 0xa0f5d29f, 0x0714a409, 0x9ebdb32c, 0x4bddfb68, 0x4acbdf77, 0xa9bac847, 0x65fd0812, 0x34a41c79, 0x8a23d5b3, 0x1c723419, 0x92ea70f1, 0x13dfe5cf, 0x39331d9f, 0x4e2e0e60, 0x1eb35cab, 0x19aa50ae, 0x08f7e72c, 0x321b6b40, 0xa84fb74e },
            .Rb = { 0x7c02cddb, 0x19697554, 0x0d31f3c6, 0x2802fb0e, 0xc1bb5344, 0x82103679, 0x93268545, 0x3d312b0b, 0xded71147, 0xe99a94e9, 0x55c28c7a, 0xf253b01f, 0xe0fe2595, 0xc9394b53, 0x734c18f4, 0xa00422da, 0x62f656e9, 0x37ea0ce3, 0xec4d41f1, 0x34c0f57f, 0x3e6352e2, 0xb42da306, 0x3d551e83, 0xc5c3baa9, 0x578bf289, 0x25cb5721, 0xd3a347f4, 0x0f89ea46, 0x3afe6a80, 0x7c9634f0, 0x31cffbb6, 0xfef31641, 0x2980390c, 0xe5da117c, 0xc80b3b47, 0xa663eb0d, 0x77d3fee4, 0x7675821a, 0x05050f4d, 0x6e2c3ed0, 0x35d3a323, 0xc39fdc4b, 0x6312ad9e, 0x05269923, 0xca994d01, 0xa9ea8209, 0xe4427423, 0x8ae15587, 0x6d3fcbfe, 0x1204db0f, 0x8d124281, 0xdf781402, 0x43c215ff, 0xd32e8a75, 0x2bbe35e2, 0x38de5ccf, 0x44c36089, 0xb920345c, 0x0ca74652, 0x15d47252, 0xaa8d5481, 0x409a18bb, 0x9b4a9fa7, 0xc1b92241, 0x8149e03f, 0x447d1dab, 0x34321b88, 0x44daf229, 0xaff7c8b3, 0x11f6481d, 0xd60a23fb, 0xbe24e4c6, 0xc387786e, 0x613731f3, 0x048f20b1, 0x4de7c157, 0x2d890d82, 0x29c40000, 0x8a589cbd, 0xca3d10a0, 0x2c8ae668, 0xf8f0fb2a, 0xdca76c1a, 0x3d17676f, 0x459f2b7e, 0xd4bf0cb2, 0x21672274, 0xcc0359c8, 0x96a8cf6c, 0x6a0bfab6, 0x0a4ee05e, 0x1f2f0027, 0x32776c5f, 0x3cbd6ace, 0x077b67a1, 0x75035b42 },
            .M_prime = 0x6b266433,
            .length = 95, // 3072 bit
        },
        .expected_c = { 0xa7, 0x3e, 0x23, 0x1f, 0x22, 0x91, 0xfa, 0xfe, 0x59, 0x13, 0x9d, 0x3d, 0x2b, 0x40, 0xdc, 0xa8, 0xe6, 0x7d, 0xf2, 0x4d, 0x03, 0x4c, 0x80, 0x87, 0x7d, 0x05, 0x71, 0x8b, 0x04, 0xee, 0xd6, 0x95, 0x8d, 0x87, 0xe4, 0xbf, 0xe3, 0x1e, 0x38, 0x84, 0x0f, 0x8c, 0x8b, 0x5e, 0x09, 0x4a, 0x22, 0xaa, 0x80, 0x7c, 0x01, 0x18, 0xcb, 0xa1, 0x3c, 0x5a, 0x4e, 0x5e, 0x3a, 0x00, 0x33, 0x40, 0x86, 0x70, 0x02, 0x4f, 0x7d, 0xd9, 0xe2, 0x91, 0xd0, 0xe5, 0x39, 0x6a, 0xcf, 0xc5, 0xa7, 0x5b, 0xbe, 0x8d, 0x41, 0xa6, 0xc0, 0xeb, 0xaa, 0x47, 0xf0, 0x34, 0x61, 0x88, 0x58, 0x37, 0x16, 0xed, 0x04, 0x38, 0x5b, 0x9e, 0xa2, 0xa5, 0x0b, 0x17, 0xce, 0x72, 0x62, 0x55, 0x3c, 0x89, 0xae, 0xb7, 0xde, 0x86, 0xff, 0xeb, 0x9f, 0x10, 0x9c, 0xd4, 0x4f, 0xd6, 0xb0, 0xa9, 0x66, 0xf6, 0x0f, 0xb7, 0x36, 0xac, 0x8d, 0x98, 0x57, 0xbc, 0x15, 0xc6, 0x85, 0x30, 0xce, 0xdd, 0x45, 0xea, 0x9b, 0x30, 0xee, 0x40, 0xb1, 0xf3, 0xb5, 0xc4, 0x4b, 0xb2, 0x07, 0xa6, 0xfb, 0x46, 0xd6, 0xe1, 0xa2, 0x5e, 0x40, 0x43, 0xf7, 0x7d, 0x20, 0x7d, 0x25, 0x70, 0x7b, 0x59, 0x57, 0xcd, 0x8f, 0xc6, 0xf9, 0x42, 0x14, 0xa5, 0x2b, 0xfa, 0x21, 0x70, 0x7b, 0xc5, 0x81, 0x69, 0x34, 0x8a, 0x16, 0x63, 0xcf, 0xbf, 0x50, 0x57, 0xa5, 0x30, 0x86, 0x62, 0xc6, 0x47, 0x38, 0x9c, 0xe6, 0xdc, 0xfe, 0x31, 0x28, 0xa3, 0x79, 0x8b, 0x2f, 0x37, 0xbf, 0x41, 0x01, 0x69, 0x71, 0xe7, 0x1b, 0x8d, 0x0f, 0x3b, 0x59, 0xac, 0x29, 0xa0, 0x15, 0xe0, 0x12, 0x0a, 0x42, 0x3e, 0x3e, 0xf4, 0x8c, 0x1f, 0x62, 0x4b, 0x3e, 0xc7, 0x2e, 0x65, 0xd5, 0x9b, 0x93, 0xd5, 0x2f, 0x04, 0x03, 0x68, 0x6b, 0xea, 0xa1, 0x6c, 0x5c, 0xa6, 0x91, 0xa5, 0x91, 0x91, 0x14, 0xca, 0xec, 0xa8, 0x01, 0x03, 0x0e, 0x68, 0x13, 0xca, 0x69, 0x56, 0x61, 0xaa, 0xea, 0x60, 0x15, 0x18, 0xd9, 0x91, 0xdf, 0x14, 0xcb, 0xe5, 0x21, 0xfa, 0x9c, 0x8e, 0xae, 0x4d, 0xff, 0xa8, 0x09, 0x45, 0xca, 0x23, 0xc1, 0x13, 0x2c, 0x1e, 0x31, 0x13, 0xf1, 0xd8, 0x39, 0x38, 0x2d, 0xbf, 0xa1, 0x23, 0xf2, 0x1a, 0x4d, 0x5f, 0xc2, 0xe7, 0xeb, 0xe5, 0x4a, 0x05, 0x24, 0x90, 0x5b, 0xdd, 0x95, 0xa4, 0x2d, 0x6b, 0xae, 0x16, 0x81, 0xce, 0x22, 0xd1, 0x16, 0xfd, 0x93, 0x7b, 0x5c, 0x3d, 0x5c, 0xa4, 0x7f, 0xab, 0x82, 0xe6, 0x13, 0x39, 0x10, 0x4f, 0xfd, 0x3b, 0xe9, 0x0d, 0x3a, 0xb8, 0x69, 0xc7, 0x26, 0x5f, 0x83, 0xd2, 0x25, 0xac, 0x07, 0x6e, 0x13, 0x25, 0xbf, 0xe2, 0xd3, 0x2c, 0x63, 0x5f, 0x2a, 0x21, 0x3b, 0x52, 0x57, 0xb5, 0xb5, 0xec, 0x47, 0xad, 0x6b, 0xdc, 0xbe, 0x1c, 0x4b, 0x70, 0xf3, 0x45, 0x85, 0xbc, 0x93, 0x25, 0xc4, 0xae, 0x6f, 0xcf, 0x3e, 0x87, 0x89, 0xfc, 0xfb, 0x8f, 0x75, 0x44, 0x46, 0x82, 0x63, 0xc3, 0xa3, 0x2a, 0x2f, 0x84, 0xa7, 0xac, 0xcf, 0x30, 0x09, 0x24, 0x32, 0x2a, 0xe8, 0x26, 0x55, 0x23, 0x5e, 0x70, 0x60, 0xf4, 0xbd, 0xae, 0x33, 0x16, 0xc1, 0x13, 0x69, 0x12, 0x87, 0x40, 0x5c, 0x7d, 0x37, 0x27, 0xad, 0xf5, 0xfd, 0xfd, 0x69, 0x53, 0x86, 0x2a, 0xee, 0x1c, 0xd0, 0xd1, 0xf9, 0x88, 0x31, 0x03, 0xca, 0x18, 0x8d, 0x70, 0x13, 0x57, 0x36, 0x9f, 0x0e, 0x76, 0x20, 0x78, 0x85, 0x2a, 0x45, 0x7c, 0x6f, 0x2e, 0x50, 0x2b, 0xe2, 0xb5, 0x53, 0x84, 0x49, 0xdf, 0x40, 0x3b, 0x21, 0xd6, 0x44, 0x7d, 0xad, 0xd9, 0x38, 0xf4, 0xfa, 0xbd, 0x87, 0xd9, 0xea, 0x36, 0x95, 0xf8, 0x23, 0xcc, 0xd1, 0x7b, 0x88, 0xb5, 0xb5, 0xef, 0x84, 0x54, 0xfa, 0x6f, 0x82, 0x47, 0x7c, 0x3e, 0xa2, 0x8c, 0x58, 0x13, 0xae, 0xc8, 0xd6, 0x7b, 0xa6, 0x6f, 0x36, 0xc3, 0x88, 0x3f, 0x7c, 0x6a, 0xdf, 0xb3, 0x34, 0x81, 0x5b, 0x14, 0xd9, 0x3c, 0x00, 0x35, 0xbb, 0xdc, 0xe2, 0x91, 0xe9, 0x0a, 0xd6, 0xcc, 0x4b, 0x46, 0xe8, 0x0d, 0xd2, 0x8c, 0x12, 0x32, 0x19, 0x77, 0x3a, 0x79, 0x94, 0xd1, 0x1d, 0xef, 0xeb, 0xab, 0xdb, 0xea, 0x43, 0x42, 0xd2, 0xd1, 0x8f, 0xe1, 0x5c, 0x89, 0x49, 0x84, 0xd1, 0x91, 0x8f, 0x60, 0x2d, 0x30, 0xea, 0xe8, 0x34, 0xca, 0x38, 0x43, 0x87, 0x9e, 0x7d, 0xf0, 0x16, 0x9d, 0xa1, 0xf3, 0xa7, 0x12, 0x21, 0x24, 0x9b, 0xd5, 0x37, 0x7f, 0x15, 0x68, 0x0d, 0xfa, 0x5c, 0x78, 0x7c, 0x03, 0x57, 0xe8, 0x0d, 0xc4, 0x07, 0xca, 0x2a, 0x04, 0xcd, 0x18, 0x81, 0x8c, 0x5f, 0x7a, 0x37, 0x32, 0x77, 0xe9, 0x82, 0x23, 0x00, 0x76, 0x32, 0x80, 0xe7, 0x9f, 0xd9, 0xb7, 0x81, 0x60, 0x48, 0x51, 0xb2, 0xdc, 0x65, 0x7a, 0xa4, 0xee, 0xf0, 0x1b, 0xc9, 0x90, 0x82, 0x34, 0xce, 0x06, 0x57, 0xdf, 0x01, 0xe7, 0x43, 0xf0, 0xd1, 0xeb, 0x82, 0x76, 0xec, 0xe8, 0xe1, 0x46, 0x34, 0xf2, 0xc7, 0xb0, 0xab, 0xfb, 0x58, 0x61, 0xce, 0xe5, 0x86, 0xb3, 0x5e, 0x2e, 0xde, 0xa5, 0x13, 0x8b, 0xc5, 0xfc, 0xe5, 0xc2, 0xb4, 0xfb, 0x8d, 0xaa, 0x04, 0xbb, 0x75, 0xac, 0x00, 0x9b, 0xbd, 0xf9, 0x4a, 0x3a, 0x06, 0x93, 0x6b, 0x5f, 0x24, 0x53, 0xe8, 0x22, 0x15, 0xda, 0xe1, 0x8a, 0xda, 0xc7, 0x64, 0x9c, 0x59, 0xe6, 0x5d, 0x72, 0xa4, 0xc2, 0x56, 0x10, 0x47, 0xa6, 0x71, 0xf5, 0x3d, 0x20, 0x63, 0xe1, 0x80, 0x59, 0xe1, 0x2f, 0x34, 0xb6, 0x71, 0x06, 0xbb, 0x19, 0x8e, 0x3d, 0x31, 0x57, 0xfc, 0x62, 0xed, 0xf8, 0xb0, 0xd6, 0x5b, 0x75, 0x7a, 0xaa, 0xd6, 0xa5, 0x92, 0x43, 0x63, 0x06, 0x62, 0x66, 0x99, 0x84, 0x87, 0x2c, 0xb1, 0xf2, 0x25, 0x41, 0xbe, 0x52, 0xd6, 0xdf, 0x6e, 0x5d, 0x29, 0xe5, 0x7b, 0xfd, 0x27, 0x6c, 0x72, 0x89, 0x17, 0xf5, 0xb5, 0xab, 0x26, 0x0a, 0xbf, 0x7f, 0x8f, 0x54, 0xf9, 0x15, 0x32, 0x28, 0x7f, 0x84, 0x3d, 0x3b, 0xc9, 0x01, 0x25, 0x1a, 0x0e, 0x29, 0xfe, 0x27, 0x37, 0x25, 0x14, 0xb0, 0x6e, 0xa8, 0xdc, 0x25, 0x5b, 0xda, 0xfb, 0xed, 0x3e, 0xf2, 0xc5, 0x53, 0x42, 0x90, 0xb7, 0xf0, 0xa7, 0x48, 0x49, 0x78, 0x14, 0xdd, 0x45, 0x6d, 0xa9, 0xa3, 0x86, 0x80, 0x3e, 0xbd, 0x97, 0x79, 0x0f, 0xe8, 0xb7, 0xc2, 0x32, 0x7b, 0x6d, 0xd0, 0xca, 0x84, 0x04, 0x2a, 0xc9, 0x9e, 0xd9, 0x20, 0xaf, 0x3f, 0x79, 0xce, 0x1e, 0x72, 0xe6, 0x27, 0x2b, 0x0b, 0xe6, 0x14, 0x11, 0x7d, 0x26, 0x59, 0x58, 0x29, 0x86, 0x42, 0x92, 0x7c, 0x13, 0xe2, 0xfc, 0x92, 0xf3, 0xbf, 0x71, 0x1f, 0x55, 0xe4, 0xe8, 0x98, 0x05, 0x4d, 0x82, 0xb1, 0xce, 0xa9, 0x90, 0xe0, 0xd3, 0x38, 0x3d, 0x11, 0x89, 0xf0, 0x00, 0x06, 0x0d, 0xfd, 0x00, 0xda, 0xb7, 0xc3, 0x58, 0xc1, 0x8f, 0x3c, 0x2a, 0xe8, 0xfa, 0xa3, 0xda, 0x0a, 0x22, 0x7a, 0x19, 0xe4, 0x2e, 0x9b, 0xb0, 0x0a, 0xaa, 0xa7, 0x6a, 0x61, 0x1a, 0x50, 0x74, 0x66, 0x50, 0xab, 0x84, 0x49, 0xcf, 0x18, 0x15, 0xa6, 0xb8, 0x64, 0x0a, 0x09, 0xfc, 0x2a, 0x91, 0x81, 0x5f, 0xbf, 0x77, 0x6b, 0x2e, 0xc4, 0xe0, 0xe0, 0xf9, 0x45, 0x0f, 0xcb, 0xae, 0xa0, 0xf9, 0xf6, 0xed, 0x55, 0x0a, 0xed, 0x32, 0xde, 0x5c, 0xd7, 0xd1, 0xa4, 0x21, 0x53, 0xa6, 0xbb, 0x5e, 0x3a, 0x1d, 0x0c, 0x26, 0xd1, 0x82, 0x92, 0x01, 0x4c, 0x90, 0xad, 0x99, 0xa3, 0x71, 0x13, 0x54, 0x47, 0x0b, 0xfa, 0xe9, 0xd5, 0x71, 0x45, 0x4d, 0x0b, 0xaf, 0xc0, 0x84, 0x77, 0x74, 0x9b, 0xe9, 0xc8, 0x93, 0x4b, 0xc7, 0x15, 0x6e, 0xf9, 0x93, 0x35, 0xbd, 0xa5, 0x4b, 0xc6, 0xd8, 0x95, 0x45, 0xb0, 0x34, 0xb1, 0xd8, 0x4d, 0x0a, 0xf7, 0x25, 0xa9, 0x9f, 0x3a, 0x4d, 0x5c, 0x98, 0xa4, 0xc2, 0x52, 0xdd, 0x4a, 0xd0, 0x25, 0xff, 0x4e, 0xf1, 0x81, 0xfc, 0x44, 0xdb, 0x62, 0xab, 0x09, 0xa4, 0x35, 0x88, 0x31, 0xb7, 0xe7, 0xf6, 0x32, 0x8f, 0x81, 0x1e, 0xf7, 0x1a, 0x59, 0x3f, 0x12, 0x62, 0x33, 0x47, 0xbf, 0xc9, 0x5f, 0xfd, 0x3a, 0x2a, 0x25, 0x1f, 0x26, 0x6d, 0x19, 0x14, 0x2f, 0x0b, 0xba, 0xf1, 0xd6, 0x53, 0x0d, 0xe0, 0x6a, 0x49, 0x8a, 0xd8, 0xae, 0x44, 0x8a, 0x52, 0x36, 0x9b, 0xe8, 0x0d, 0xd4, 0x46, 0x91, 0x34, 0x5b, 0xa0, 0x04, 0x2f, 0xc3, 0x7b, 0x07, 0x70, 0x73, 0x01, 0x1c, 0x89, 0x87, 0x7c, 0x2e, 0x92, 0x26, 0xbd, 0x21, 0xaf, 0x74, 0x04, 0x63, 0x29, 0x10, 0x26, 0xd8, 0x4c, 0x58, 0x85, 0xbb, 0x11, 0xdf, 0x6c, 0x25, 0xdc, 0xb1, 0x22, 0x72, 0x86, 0x16, 0xa2, 0xd0, 0xbc, 0x89, 0x0c, 0x13, 0xe8, 0x2e, 0xb1, 0x55, 0x90, 0x44, 0x0f, 0x18, 0xd8, 0x7f },
        .hmac_key_idx = DS_KEY_BLOCK_1,
        // results of message array encrypted with these keys
        .expected_results = {
        // Message 0
      { 0x1dd30b07, 0x7a20e1b4, 0x115af11c, 0x97802d6b, 0x71544abc, 0xf2bc3fa7, 0xf6e0503b, 0x78cb7e48, 0xa30c7b6c, 0x3fa57779, 0x62af2846, 0x7145e0fb, 0x7f4e2832, 0x27b691a8, 0x4f51c10a, 0xc3bf3c35, 0x3590d49f, 0x1a3ce71a, 0xd6aefc2d, 0x973b26e5, 0x3cd1f9e5, 0xd16d72d6, 0xec083586, 0x4bc5c167, 0xb5cf09e7, 0xdb73999d, 0x14bb9526, 0xb8b457e7, 0x0a880269, 0x421ef4c2, 0x61b1e1d8, 0x388b344e, 0x5ce460de, 0x5fba2b59, 0x1542c556, 0x22c67e3f, 0x4e4b85cd, 0x8397db1d, 0xe947eae6, 0x3624eb45, 0xaa90759b, 0xf22563b4, 0xe9fdea74, 0xd377c19d, 0x9c40d81c, 0x414329b6, 0x236b963e, 0x177b7ae6, 0xfc784ccb, 0xf5997978, 0x3abb91d2, 0xb9022814, 0x6c52f545, 0x6887d3ee, 0x19c31382, 0x77891c70, 0x31a4eb0c, 0xf91d8fc7, 0xdd7c5a2c, 0xd70d36b7, 0xcbd2177f, 0xeef25eae, 0xd1e28214, 0x5ae777a6, 0x2ed2dff6, 0x3519453d, 0xb905e964, 0x46f77558, 0x2863e62e, 0x9c248fbb, 0x8b520908, 0xca900ef0, 0x3259bd4a, 0x3f0f7f41, 0xe2f276d8, 0xd82e9c89, 0x24a83a1e, 0x86accf96, 0xaf0bde50, 0xaa575f80, 0x7a707413, 0x864d4cc3, 0x10a1b440, 0xbddbaa40, 0x6491bded, 0x5e771fc6, 0x0ac3e261, 0x4e438a97, 0x28695772, 0xe01782b6, 0x05b7d3b4, 0xa9e86035, 0xdc32d354, 0xdf89442a, 0xe28124c6, 0x04029f58 },        // Message 1
      { 0x33848dd8, 0xe7836e09, 0xca5a0f41, 0x06005392, 0x07c4e3cb, 0xa0fdb69d, 0xafeb2b08, 0xef413195, 0xf3d1cd01, 0x716d2dbc, 0xe2494e22, 0x16e54706, 0x5a5e12f7, 0xdc95ca93, 0xdce75ef0, 0x84ef183d, 0x3b7807fb, 0x1c2a5170, 0x97d9f580, 0x087fae04, 0x60dc0c3f, 0x23c74ba7, 0x5d7a88ab, 0xb43a453a, 0x030407e7, 0x778f2049, 0x61dbff41, 0x05b64f7b, 0xecdee671, 0x5552ec2b, 0xeb87e7ed, 0xefffb3f8, 0x1dbd29bb, 0x2f351041, 0xae962fa7, 0x6ff2a155, 0x4b8e1f6a, 0x8008bccd, 0xadc038b6, 0xb6d261fc, 0x32d87543, 0xd69f8d81, 0x956ddcaf, 0x46496335, 0x78923048, 0x41d10e82, 0xfac5bb0d, 0xff0fead0, 0x4ba5b498, 0xde213e07, 0x95f5de08, 0x98fc3216, 0x2390d74b, 0xe66b3354, 0x3c9a737f, 0xd68f4a21, 0xb86ec19c, 0xb54ef2b4, 0x7ae7b726, 0x317edea5, 0xdb52f3da, 0xf2c8ddf6, 0x8975c934, 0x5ffb553d, 0x16fa5cc8, 0x44be6a69, 0x83e7d672, 0x2feadeb6, 0xad79641f, 0xc6f9ca2b, 0xd8e0b8cc, 0x4ccfd352, 0x05f8ac86, 0x7f1cf362, 0xeaa7ddb7, 0x347814d9, 0xddbadb11, 0x6e16fdbb, 0xeea66534, 0x0a045c01, 0x82157a88, 0xb191b3c4, 0xdc8fa781, 0x7800bed7, 0x18f48d72, 0xbd65317b, 0xb2e1b61a, 0xfa219cab, 0xe8c56ed0, 0xd38d2fdd, 0xbe94d8e1, 0x93674a1a, 0xcf61399c, 0xf2512581, 0x5fdd17d4, 0x966136bf },        // Message 2
      { 0x9bbb2c24, 0x3e809a67, 0x09589246, 0x766acc7f, 0x7b2655cd, 0x21b9eb2a, 0xc09b52ff, 0x2689e2e3, 0x875030a1, 0x3a6f11b5, 0xfe225d07, 0x4e764d22, 0xe97ee0ab, 0x1c370cdf, 0x42373ac2, 0xf9b5abeb, 0x993f16dc, 0x49ae4679, 0xafc3b152, 0x9ac97356, 0xa8b39c0d, 0x95563462, 0x69c54cad, 0x6b2d0c90, 0xe01ffaef, 0x80090674, 0xccbdc920, 0x0f67c36f, 0xcfc2cf7f, 0x88d605ff, 0xe00975fa, 0x8dd33c0a, 0x1f5827a4, 0x3c653584, 0x7e83b1c2, 0x329890a7, 0x84ee6eb0, 0xdedd32bb, 0xe96b9c7d, 0x33f258e9, 0xdc72ac85, 0x19999994, 0x0ce022f3, 0x1b0f9343, 0xa09ed3ec, 0x26131e20, 0xd59dcdf4, 0xf36220b3, 0x3e5e4370, 0x27e76689, 0x5035ef5c, 0x4a00f78c, 0x62f02bef, 0x72ba9ea4, 0x5e69343c, 0xdd760c30, 0x0d9413c7, 0x167832c7, 0x20f1dc0c, 0x6c04bd22, 0xecc4ef61, 0x9aa0e468, 0xda1b320b, 0x87cdddc9, 0x9edd9520, 0xc8b860de, 0xd0595061, 0x4ea05219, 0x53ffc1ab, 0x1403fd6a, 0xd671d660, 0xaeb31f22, 0x2081c4b2, 0x3ad34de7, 0x10e38026, 0x35a99f89, 0x80cdb6bc, 0x961b7f67, 0xc308357f, 0x6afa371b, 0x5536d81b, 0x88ba626f, 0x58245cc3, 0x1d074b80, 0x31f33e10, 0x0d2796b1, 0xa87dbc31, 0xb4fafefb, 0x9cc4dd5d, 0x1392f1c9, 0x6608a408, 0x29bcf835, 0xf4012433, 0xf9fb1170, 0x2e211738, 0x6b27913a },        // Message 3
      { 0x89ab0030, 0xc4250e0f, 0xbc90454a, 0xd7b805c6, 0x8f3379d1, 0x02fdcdfd, 0x1cee1096, 0x91fa7ca0, 0x16659c1c, 0x85e9e9af, 0x96a5a844, 0x6a9e23e8, 0xde241e7d, 0xdbf47cef, 0x2f763c3b, 0xc37d0b3e, 0x29df1f63, 0xc6af9e35, 0x72ce3496, 0x86999ab8, 0xf86c3abb, 0x61e26c79, 0x0eeb9522, 0x88d69b72, 0xb800d4e4, 0x43584df9, 0x76cd4f85, 0xfd228340, 0x14b7fa54, 0x8d38f2d0, 0x6acd0758, 0xa07ebbb2, 0x4bcbff9c, 0x0d563e80, 0xcbe4d8a2, 0x3187ee90, 0x77118bfd, 0x7c81c15a, 0x0fc2860f, 0xd27952a9, 0x3e777af9, 0xed0e28a1, 0x12260ac5, 0xdda7c661, 0x2138f6d3, 0x53dd74ef, 0x89d96332, 0x83cd74ec, 0x39bb794f, 0x2648ead6, 0x941388dc, 0x32b2a9be, 0xbe5e87aa, 0xd997d17a, 0xd0614ac4, 0x7f2be785, 0x2bfccc52, 0x2cc2b2b4, 0xb6ae74b4, 0x9f84ff27, 0x886a9cd8, 0xb5cc7f0b, 0xe73ef16a, 0x4e028c43, 0xf1e43014, 0xef1c72af, 0x3dbc6a82, 0xe2e5757e, 0x909c23cb, 0x115826f6, 0xda086c8a, 0x5fdcd239, 0xc27e9057, 0x57f9893f, 0xf9ff2755, 0x41ee1548, 0xe3c9b4bb, 0x4bda5e41, 0xa635f5b0, 0xa94bbabc, 0x862d93bc, 0x31b3a8df, 0x5485c1fe, 0xf0124216, 0x4b619dd2, 0x8df5ff9b, 0x50bb155e, 0xa968ea40, 0x0859e718, 0x8a3199c6, 0xe4553bbb, 0x9baf44d9, 0xb12d6498, 0xb878a59e, 0xe0d13c6f, 0x2bb74cb7 },        // Message 4
      { 0xabf9a766, 0xc7598b0b, 0x1866649d, 0xcd24127d, 0x2cd2e2fc, 0x19c31e2b, 0x1c7f56cf, 0x77c76e57, 0x62a49e90, 0xa5a89ca9, 0x5294b82d, 0xe8f182c4, 0x8b40ebe7, 0x515666c1, 0xa430a24c, 0x43260ab3, 0x1c6e4a17, 0x1fbaea29, 0x7070679f, 0xce931dc5, 0x9df42bf6, 0x5e873d07, 0x520c6633, 0x49aaf80a, 0x49c6b5a9, 0x15074f1c, 0xa6b9f5a6, 0x6e2585f4, 0xe09aea9b, 0x305607a8, 0x6df358bf, 0xd804d2bf, 0x48ee8c78, 0x9309cae5, 0x9198601a, 0x6fbff8d8, 0xcc7896fe, 0x81cccec8, 0xd8185ea2, 0x23fa8bf7, 0x0ce2865c, 0xf015bcc4, 0xd71fb2a9, 0x4598d445, 0x3133fce4, 0x1e179697, 0x64c56bd2, 0x68af22de, 0xa0aa42a8, 0xb8184fc9, 0x3ec21f1d, 0x30f8fac4, 0x1536c2a9, 0x2645cf02, 0x17ef7159, 0xf7309c3e, 0x85e6a098, 0xe6b4066d, 0x19964d30, 0x389af5c9, 0x3d739fb7, 0xab72767e, 0x60d546f7, 0xda585399, 0xe955e994, 0x72b840fb, 0x9971491b, 0x61e07743, 0x1a471634, 0x075d9d61, 0x24921c9d, 0x8e1b5979, 0xae0fdd3e, 0x8f71903b, 0x4e373f40, 0xa8500ae7, 0x1f225fe7, 0x188d47ce, 0x618da90f, 0x1716bfa6, 0x566164c7, 0x10555f1b, 0x5b3b9cc8, 0x77921856, 0x6014c5b0, 0xda27653d, 0xb0943d38, 0x336cf13a, 0xd9febd34, 0x1657ddcc, 0xd7e1aada, 0x54b10d97, 0x88f13732, 0x7310cd55, 0xaa5f6269, 0x85cfa06f },        // Message 5
      { 0xde9ec0bb, 0x71ce275d, 0xcea33e85, 0x99501280, 0xd956176e, 0xc883165c, 0x8f77016b, 0x030f10d4, 0x2b3ac35c, 0x939f3cd4, 0x71ab5924, 0x950ca9f0, 0xdd9eab26, 0x024ddcb1, 0xd3adfc61, 0x32f8a9a8, 0xf4678564, 0x6e538709, 0xc4f9e00a, 0x9236af28, 0x5efb1831, 0xa3c6430f, 0xb5e25317, 0x5f7ee309, 0xfa99c2ae, 0x1e03ff49, 0x20f94781, 0xb01e6ff6, 0x0b8dd51d, 0x3c8a19db, 0x0a2c1876, 0xa0dff0eb, 0x7389d213, 0x4143634f, 0x521993bf, 0x65bd40f3, 0xe2f746d1, 0x8bc6007d, 0xd24cd0c2, 0xf153f376, 0xe26d5393, 0x3f644a47, 0xd7426e05, 0x6b06f344, 0xbf5cab80, 0x82b44d11, 0x37d9093e, 0xd33a1761, 0xff94c832, 0xc614d1bc, 0x5a1dd0c7, 0x1cc645c5, 0xd91db784, 0x32efe6ae, 0x09ff24ec, 0x329781fb, 0x66831dac, 0x2f1a3647, 0xd986dc9a, 0x535fb6e4, 0xbf0a1211, 0x9d9c5e0c, 0x1566d6a0, 0x401a616a, 0x1253eb46, 0x4771ff4e, 0x11601897, 0x9f2f7cac, 0x6219f12e, 0x53e53137, 0x5f878279, 0x53040f96, 0xfdeaf516, 0xd6a00205, 0xd02c307a, 0x3bfc99c9, 0x3e3dd8f8, 0x6abb1c4d, 0x20e7270b, 0xafe26ccd, 0x0704d6c9, 0x24b55dbf, 0x1da0683d, 0xa152edc8, 0xbfe87bb3, 0x648eedb7, 0x2e4c8cb3, 0x130c0d1b, 0x14db85c7, 0x58a5093f, 0x0dd0e4a2, 0x669057bd, 0x67a8d73a, 0x42934819, 0x997f7e48, 0x4a79a2fb },        // Message 6
      { 0x3091af00, 0x3e88a63f, 0xc1e5a42e, 0xef4136ab, 0x019914f4, 0xe5679a1d, 0x53398d88, 0x0adbfe54, 0x18b045db, 0xc192c25a, 0x42d66a41, 0x44fe7c2f, 0x24c949c3, 0x9a9a1a24, 0x72a30041, 0x1d4d0c7f, 0xda007af0, 0x0f8ebd86, 0x9d75b6d6, 0x63c92aff, 0x43689bcb, 0xbe3c99be, 0xec00e4b6, 0x3c1b6d9e, 0x09e61956, 0xe32bc167, 0xeef7d112, 0xa5b6aaca, 0xd4920f01, 0x9d2a805f, 0x045b38ff, 0xf85b8e76, 0xcd5eb94d, 0x65a26556, 0xc67f6cd8, 0xc0b8e974, 0x7d82557e, 0xd9818bbd, 0xeb5ba0fb, 0x5c9a90f0, 0xed2b3d8c, 0x2c4012b0, 0xaaa83528, 0x27f8ca81, 0x1fa868eb, 0xf6f1ffd8, 0x0ebb2a64, 0x65f33892, 0xa83b6286, 0xdc8f84e7, 0x0082e31f, 0xa94c437a, 0x273e38dd, 0x18cbba3e, 0x33d142c1, 0x9652fa61, 0x38c770ef, 0x456110a9, 0x1cc26e20, 0x35440d20, 0x2bc8c13f, 0x934c3e3b, 0xc7592d8a, 0x4ff51143, 0xd528f834, 0x08a081b2, 0x008da561, 0xf66f5ad5, 0x304ef7ba, 0x4cbb841b, 0xafc3093f, 0x8f4f2bcd, 0x0dd051cc, 0xfd9cdd1b, 0x3171189c, 0x1690ca02, 0x3e6fbbd7, 0x0c2a98b5, 0x1362d451, 0x7e2edb82, 0x33e6eeed, 0x4e71a3a6, 0xf51d3acc, 0x444e4e06, 0xeb50c285, 0x66881abb, 0x2d3690f8, 0x574b7709, 0xe6e57584, 0x16c95cae, 0xba2dbd54, 0x5a5d693a, 0xc823016c, 0xf0089ae6, 0x415e0902, 0x67c1b0c1 },        // Message 7
      { 0x7e8b2e2a, 0x289b46a4, 0xa6a37b8c, 0xc567df73, 0x55018796, 0x4e9d9d18, 0x0d31eb7e, 0x5d74d159, 0x841ebae2, 0xe1973167, 0xd61418e6, 0x5cb89cbc, 0x1acb9a42, 0xf82e0e7f, 0x05e631bc, 0xba43f7f9, 0xd79c942b, 0x16628593, 0xcbb9d614, 0x8614cc8b, 0xf7945887, 0xca968cfe, 0x5b92abbf, 0x18abb020, 0x4ca4d94d, 0x652b2efd, 0xb38d885c, 0xd727ce25, 0xd0accfb8, 0x8b1498c4, 0xa9788ae1, 0x87b60400, 0x72ee888c, 0x3cc8b5be, 0xd01b4fda, 0xe4214afd, 0xd709bd20, 0xe17b0353, 0x7fffe766, 0x1cf3c00e, 0x1ba37716, 0x0e412e6e, 0xcfe26c33, 0xd4e73eef, 0xcfe108b0, 0x9da923e4, 0xf309b4f6, 0xf7553306, 0x96001a08, 0x18392a1e, 0x10f14241, 0x1c5b6398, 0xe14efe32, 0xa1fa1f3d, 0xd30a00e3, 0x2220b1b5, 0xd21b950f, 0xffbe55a0, 0xab511ea4, 0x1d508c36, 0x11766260, 0x93f08d28, 0x2b55f5d2, 0x3b31e88a, 0x901cc723, 0xffb83098, 0x0cc75b00, 0xfdf6ddfa, 0x8c61275f, 0x7c3d5f35, 0x6e947893, 0x8d78a4ef, 0x3715a9b2, 0x5df48a9b, 0xced6b5ac, 0xc365d317, 0xf0751afa, 0x51b70a61, 0xb961f7ab, 0x00e2ec20, 0xddc4bcd5, 0x3575bb7c, 0xfc3e41e6, 0xabd80278, 0x560cd8db, 0x37c6f978, 0xbecaf8b6, 0x8e9370b8, 0x6e419061, 0x123a0d8e, 0xafc8bf2f, 0x110335cb, 0x0e2dd6ff, 0xdff2ac5b, 0xc4c40ded, 0x71c828d5 },        // Message 8
      { 0x5f4d6c0f, 0x07cbb6c0, 0xc5d29098, 0xaa4b7646, 0x9ad67706, 0xc24cb90a, 0x0e3b474d, 0x570aa38b, 0x609c6eea, 0x35acb442, 0xfb4ecb48, 0xc0bca95d, 0xca13fb93, 0xce8c161c, 0xcb587f70, 0x5be94979, 0xa8d864f1, 0x46d3a2cd, 0x376e1880, 0xed9e9acc, 0xad3671d8, 0xf07acd26, 0xd4fcd4b2, 0x8592f218, 0x4215a2ee, 0xeba03531, 0x5979f6e3, 0xce0435fa, 0xda72ea66, 0x02503771, 0x9734cb8d, 0x7ecc8a3a, 0x5b7e0c2f, 0x952a10c9, 0xbb8ac3e7, 0x07691411, 0x6a4ec582, 0x5720909f, 0x6dfbd1f8, 0x87a2ade8, 0x4fa2084c, 0x47cef5ef, 0x68295cb4, 0xf126dd97, 0x147ad2d0, 0x8350c426, 0x5f996e6e, 0xb984046f, 0xdfe837e9, 0xc90ba36b, 0x74f4144c, 0x96b87bf6, 0x2a854c34, 0x4aecade4, 0x51262c73, 0x59ef8591, 0x5e8a1852, 0xb18e1501, 0x96fb63a7, 0x99766d88, 0x4eae8963, 0xdc86ecbe, 0x394414c8, 0x4b680710, 0xf1f31913, 0x44decb2a, 0xbc328965, 0xeb85a7a5, 0x3123199b, 0x029a3109, 0xf7ff3e08, 0x6624ddd6, 0x25f6bc05, 0x1dafd418, 0x0d07a121, 0x3c8c10aa, 0x0d367ea7, 0xbc97bccc, 0x4c3c5bd0, 0x6a412808, 0xdd549ed9, 0xeac0470f, 0x9bab1f29, 0x22fcbbf9, 0x844313ad, 0x020ad2d7, 0x738d245d, 0x0e0de0e0, 0x7ca6cb06, 0x02c4cc89, 0x8bf7b00b, 0x3b19f999, 0xd8acac9d, 0xdba5a5c7, 0x79e558bd, 0x98cdad76 },        // Message 9
      { 0x3cb7cf11, 0xa6105ab5, 0xab76fba2, 0x83b390f6, 0x2c8b342f, 0x067cb33b, 0x1408d6ee, 0x11fac21f, 0x3acf1455, 0x0b5a24b0, 0xfd77a460, 0x94c6fd74, 0x58f4b525, 0xf942d753, 0x66cc39a2, 0x49c9739e, 0x6403a808, 0xc991f39f, 0x32b96bad, 0x251f2197, 0x39e921b7, 0x0678add2, 0xeea0ac3b, 0x73ca6627, 0xb471fc30, 0x5a3af2cd, 0x2e295cb3, 0xf69dabd3, 0xe91b9a66, 0x3a22cfcd, 0xc19a3cac, 0x6558aa8a, 0x1d35ee0e, 0x3fe4bbbc, 0xa502e579, 0xeef25dd8, 0x852ee0e2, 0x691a50b6, 0xc24f814b, 0x4057d02e, 0x1a7027c2, 0x940128ca, 0xe524bd60, 0xdfc42a34, 0x5c660828, 0x625f0bc0, 0x0736e130, 0xdb1f4eb8, 0x93dd97bb, 0xab978162, 0xa892631e, 0x32344a00, 0x422976a3, 0x25779ed5, 0xb7320ebd, 0x24ae7deb, 0xff1f30e1, 0x90a022e1, 0xb553ada7, 0x3a00c75d, 0x3b14b336, 0x80ac9445, 0x76b35ce5, 0x825a665d, 0x4e1e0923, 0x0c69aebc, 0x3e15c3e6, 0x1bca6e19, 0x1cfeef07, 0xe174c35c, 0x4fbda286, 0x2c7bbf73, 0x96f754dc, 0x5c63252a, 0x95c04e0d, 0x77ca359a, 0xc18676fb, 0x3da32215, 0xc2e9e2af, 0xdc786295, 0x37b467e6, 0x2de4b870, 0x981d8fcb, 0x1c55875a, 0x25b97c90, 0xcffc9df3, 0xf8f07656, 0xfc55a341, 0x7df87dfe, 0x075a4077, 0xd0c81ca3, 0xd3d4b477, 0x3ac9b98f, 0xad6ddd0c, 0x0887bc80, 0x8a4211d9 },     },
     },
    { /* Case 1 */
        .iv = { 0x1a, 0x74, 0x1a, 0x0d, 0x12, 0x6a, 0x41, 0x95, 0xad, 0x9d, 0xcf, 0x50, 0x36, 0xcd, 0xd2, 0x2f },
        .p_data = {
            .Y = { 0x5e7f3901, 0x83dff5a9, 0x3f5d79f2, 0x315f01f4, 0xfe6ddbfc, 0x93ffc59b, 0x74a4d669, 0xb6266869, 0xfd7a29e6, 0x37402bf2, 0x8004bad5, 0x12e646ee, 0x9c7d1ef8, 0x08c00cb2, 0xf6e7c47f, 0xd64e6fb7, 0xddf23b35, 0xfac7f07e, 0xc908b144, 0x79d146ea, 0x8bddf439, 0xe3559772, 0xcf7bf43d, 0x8fc7e826, 0x59cfbff4, 0xca0117fc, 0x7794778c, 0xd6d0b3c9, 0xa3834bd7, 0xc66a1396, 0x31d8e079, 0xa383ce75, 0x6de2a3ab, 0xa2f2bd6d, 0xa886b53e, 0x8bb8888c, 0x1a4caf59, 0x2855dfd6, 0x6328c77f, 0x1f92288d, 0xb323d703, 0x5b77a41c, 0xc2e8bc8c, 0x1a14ea13, 0x3ccab9d5, 0x42a24220, 0x62d35a62, 0x00413791, 0x31067cd0, 0x35c0c136, 0xb3784eb9, 0x3721deaa, 0x2ff540b2, 0x434636cd, 0xabdf3394, 0x157ffc22, 0x076402c3, 0x542baf67, 0x88fde6f4, 0x525dd4b0, 0x14a7a55e, 0x8b49af86, 0xf63a2fb1, 0x0c1221e3 },
            .M = { 0xe480a2dd, 0x0d6f5b04, 0x607bc451, 0x21e483e6, 0xece0f348, 0x99de70d2, 0x1bdad457, 0x7c383f15, 0xf2f95ba5, 0xd85bbfd2, 0x68bb46bb, 0x25a473b5, 0xfae5f83e, 0x1c788bac, 0xb0eda25b, 0x04878405, 0x9ff3f9a5, 0x0c02764a, 0x84389716, 0x94a17034, 0x416512d5, 0x61989147, 0xc46a553a, 0x188bc27e, 0xd7f919fc, 0x61ac03bb, 0x396caa95, 0xa1701433, 0x692daa04, 0x3a99564f, 0xc912193c, 0xf72b4431, 0x1441b2f4, 0x0f764c29, 0xa30181e4, 0x248f9bce, 0x89dcd7cf, 0x114476e2, 0x91532c26, 0xd0784f5e, 0xbf379a46, 0x1a20623e, 0x33abb828, 0x6695461c, 0xc49b11d0, 0x74e31391, 0xdaf960b0, 0x992cf3fa, 0x9b604bab, 0x8b094397, 0x86a222e9, 0xed92ed31, 0x16ce9d8a, 0x1b43c02c, 0x1a04d53d, 0xd2c9cde8, 0x580d79e0, 0x93719940, 0xf563a491, 0x7af1c665, 0x2c711d77, 0xd8df550d, 0x0801eb87, 0x9c76fc42 },
            .Rb = { 0xef104f5f, 0x522d724d, 0xeacdf743, 0xea659e52, 0x4fd7cd65, 0xa2c583c6, 0xdf3e4c40, 0xe0d401d7, 0x3eea7474, 0x68ae25e6, 0xd1dcb981, 0x2c7d55ca, 0xe814b4cd, 0xf289c66a, 0xebbe0eb6, 0x952994f0, 0x9efce8b1, 0x9db9b426, 0x981d73e6, 0xf31bc607, 0x61750f63, 0x7cd1473e, 0x2eb60080, 0x7af61e87, 0x94d67e42, 0xfef09cfe, 0x55165a75, 0x59acb914, 0x59814d2c, 0xa568e43b, 0xf9b93ecc, 0x765625d1, 0x7bed3402, 0x71d441a5, 0x64667a9e, 0x9e3f6d38, 0xdceac567, 0xedb3d144, 0x98ff93f0, 0x43baa393, 0xefa14235, 0xf6b6ef0a, 0x8db23ac8, 0xf664248d, 0x848638e6, 0x39c5506f, 0xc4b9e3ef, 0xc48e05c8, 0x7f10188b, 0x1d2a6961, 0x7963e344, 0x0efb7f3f, 0x76574e1a, 0x5e032e2c, 0x3bcf0cf9, 0x3f57bfb9, 0xd040404d, 0xacd65aad, 0x2da4d405, 0x187338f3, 0xd0b0feac, 0x0dc6873e, 0x8c06d2f9, 0x216491cc },
            .M_prime = 0xa26fba8b,
            .length = 63, // 2048 bit
        },
        .expected_c = { 0x42, 0x29, 0xab, 0xdc, 0x8a, 0x08, 0x04, 0x91, 0x78, 0x2a, 0xa5, 0x6b, 0x3f, 0x2a, 0x1c, 0xf1, 0xd6, 0xda, 0x35, 0x54, 0x4a, 0xa1, 0x78, 0xc3, 0x6d, 0x95, 0xda, 0x0d, 0xe3, 0xd4, 0xfc, 0x36, 0xcd, 0x65, 0x7c, 0x86, 0xdf, 0x5b, 0x19, 0xe7, 0x90, 0x2a, 0x7a, 0xa9, 0x23, 0x38, 0xb5, 0xa1, 0x95, 0x55, 0xb7, 0xa0, 0x27, 0xb1, 0xa1, 0x32, 0xd3, 0xad, 0x42, 0xd5, 0x73, 0x51, 0xbe, 0x6f, 0x6e, 0x3a, 0x37, 0x1e, 0x71, 0x56, 0xa2, 0x2f, 0x7b, 0xf3, 0xa7, 0x42, 0xc7, 0x98, 0xfd, 0x33, 0xcc, 0x55, 0x8d, 0x56, 0x5b, 0xfb, 0x4b, 0x6b, 0x18, 0x28, 0xad, 0xf9, 0x14, 0x16, 0x49, 0xd5, 0x75, 0x6b, 0xef, 0xc3, 0x51, 0x89, 0x2c, 0x53, 0xfa, 0x91, 0x09, 0xc2, 0x18, 0x99, 0xcc, 0x1e, 0x31, 0xb1, 0x5d, 0xcf, 0x6b, 0x94, 0xbe, 0x5c, 0xa4, 0x45, 0xae, 0x82, 0x7f, 0xd3, 0x9c, 0xf7, 0xb9, 0xee, 0xb8, 0x52, 0xb0, 0x2f, 0xce, 0xd6, 0x34, 0x22, 0xdd, 0x69, 0x72, 0x3a, 0x51, 0xe9, 0x2e, 0x70, 0x2c, 0x00, 0x3f, 0x71, 0x11, 0x56, 0xc5, 0x83, 0x12, 0x64, 0xf5, 0x1a, 0xb4, 0x0f, 0xcb, 0x79, 0xb7, 0x8a, 0xf8, 0x07, 0xc5, 0x96, 0x1a, 0xbc, 0xd1, 0xd9, 0xad, 0x83, 0x6c, 0x37, 0xac, 0xa9, 0xc4, 0x1c, 0xcd, 0x7a, 0xfc, 0x39, 0xee, 0xf4, 0xfd, 0xee, 0x18, 0x02, 0x69, 0x9a, 0xdf, 0x2d, 0x2b, 0xbe, 0x87, 0xc6, 0xec, 0xd3, 0xd3, 0xf5, 0xc4, 0x4c, 0x84, 0xe2, 0xf3, 0xcc, 0xc5, 0xe8, 0x9b, 0x3e, 0x9b, 0x16, 0x45, 0xd8, 0xcd, 0x90, 0xf1, 0x92, 0xf1, 0x35, 0x30, 0xcf, 0x5a, 0xf8, 0xb9, 0x52, 0x00, 0xb8, 0x36, 0xde, 0x3c, 0x85, 0x8d, 0xcc, 0x62, 0x2c, 0x04, 0x56, 0x41, 0x29, 0x96, 0x66, 0xdf, 0xbe, 0xb3, 0x95, 0x49, 0xda, 0xed, 0x6c, 0x28, 0x6a, 0x71, 0xf4, 0x2a, 0x8f, 0xc5, 0x22, 0x58, 0xc9, 0x2c, 0x8a, 0x4c, 0xc3, 0xe8, 0xc5, 0x90, 0x78, 0xea, 0x1b, 0x6e, 0xf6, 0x97, 0xa7, 0x9b, 0x72, 0x07, 0xec, 0xbf, 0xbb, 0x48, 0x2a, 0x85, 0xdb, 0xf9, 0x58, 0x51, 0xcc, 0x23, 0x7c, 0x32, 0x24, 0xb6, 0xdd, 0xa8, 0xbd, 0xfb, 0x27, 0x8f, 0xf0, 0x74, 0xec, 0x6b, 0xed, 0x36, 0x68, 0x30, 0xec, 0x6d, 0x0f, 0x6d, 0xe4, 0x9c, 0x31, 0x80, 0x32, 0x66, 0x80, 0x89, 0x5f, 0x3e, 0x3d, 0xba, 0x3b, 0xba, 0xec, 0x56, 0xbe, 0x60, 0xdb, 0x2e, 0xad, 0xd4, 0xb7, 0xf7, 0xf6, 0x48, 0x54, 0x47, 0xc3, 0x69, 0x72, 0xf0, 0x40, 0x93, 0xb6, 0x7e, 0x91, 0x76, 0x68, 0x32, 0xcb, 0xcb, 0x70, 0x10, 0x26, 0xa9, 0xfa, 0x44, 0x1b, 0x03, 0x3c, 0x24, 0x0f, 0xa2, 0xa2, 0xb1, 0x0c, 0xf5, 0x6c, 0x1d, 0x14, 0xd1, 0x10, 0x44, 0x27, 0x4f, 0x5b, 0xa9, 0x2d, 0xd8, 0x82, 0x6b, 0xce, 0xc7, 0xdf, 0x7a, 0x55, 0x68, 0x57, 0xb7, 0xc9, 0xf0, 0x7a, 0x35, 0xd8, 0x34, 0x3d, 0x2e, 0xf8, 0x9e, 0x1d, 0x27, 0x0c, 0xdc, 0xa0, 0x9e, 0x40, 0xa3, 0x01, 0x7e, 0xef, 0x94, 0x08, 0x4a, 0xa1, 0x22, 0xe1, 0xe7, 0x89, 0x9d, 0x18, 0x60, 0x92, 0xf7, 0xfc, 0x95, 0x96, 0xd8, 0x9e, 0x59, 0x6c, 0xcd, 0xd6, 0xec, 0xde, 0xe8, 0xf3, 0x0e, 0xb5, 0x24, 0x85, 0x6a, 0xb7, 0x2b, 0x82, 0xdb, 0x09, 0x5d, 0xd9, 0x4e, 0xd5, 0x09, 0xd6, 0x15, 0x18, 0x62, 0x01, 0x34, 0x7f, 0x65, 0x5f, 0x44, 0xc4, 0x47, 0xa2, 0x0f, 0x7d, 0xf4, 0xa8, 0xa8, 0x87, 0xef, 0x11, 0x2a, 0x48, 0xef, 0x10, 0xad, 0x62, 0xf6, 0x9d, 0x15, 0x9f, 0x41, 0x3d, 0xac, 0x15, 0x29, 0xa5, 0x91, 0x5c, 0x10, 0xa6, 0x77, 0xe8, 0x6c, 0x0a, 0xa2, 0x79, 0xf1, 0x0c, 0x48, 0x04, 0x4a, 0xb3, 0x6b, 0xf3, 0x5c, 0xa6, 0x33, 0x84, 0x28, 0xde, 0xc6, 0x0a, 0x6c, 0x97, 0xbf, 0x09, 0xc8, 0xd1, 0x6a, 0x68, 0x70, 0x24, 0x89, 0xd7, 0xbb, 0x01, 0x34, 0xe1, 0xed, 0x10, 0x71, 0xac, 0xf3, 0xfd, 0x76, 0xeb, 0x56, 0x8a, 0xe0, 0x5e, 0x48, 0xee, 0xc9, 0xe8, 0x9c, 0x06, 0x26, 0xcc, 0x94, 0x70, 0x5c, 0x7c, 0x36, 0xc0, 0xf5, 0x29, 0xb3, 0xbb, 0x6a, 0x58, 0x31, 0x8d, 0x6f, 0xd1, 0x45, 0xc5, 0x11, 0xae, 0x4e, 0x2b, 0xf1, 0x29, 0xbf, 0x38, 0xc4, 0x48, 0x5c, 0x8b, 0x01, 0xe7, 0xce, 0x4c, 0xf7, 0x78, 0xfb, 0x98, 0x6f, 0x64, 0x3e, 0x96, 0x82, 0x0c, 0xc8, 0xc7, 0x64, 0x7e, 0x31, 0x5b, 0xdc, 0x43, 0x83, 0xfd, 0x97, 0xd7, 0x25, 0x43, 0x23, 0x72, 0x1e, 0x61, 0xa6, 0x51, 0x77, 0x44, 0x02, 0x3f, 0xee, 0x26, 0xf1, 0xa9, 0xc6, 0x6b, 0x97, 0x2e, 0x25, 0x4b, 0xc8, 0xff, 0x35, 0x15, 0x6b, 0x6f, 0xbd, 0x5e, 0x19, 0x58, 0xa0, 0x23, 0xee, 0x80, 0xd4, 0xd9, 0xbc, 0x52, 0xc8, 0xfc, 0xb5, 0x8c, 0x8e, 0xaf, 0x36, 0x81, 0x36, 0x96, 0xca, 0x5e, 0xdf, 0xe1, 0xa2, 0xda, 0x17, 0x90, 0x40, 0xe5, 0x7f, 0x0b, 0x2b, 0x02, 0xea, 0x79, 0x4c, 0xad, 0x74, 0x3b, 0x82, 0x93, 0x74, 0xec, 0x1b, 0xa1, 0xcd, 0xcd, 0x1a, 0xe3, 0x2e, 0xdd, 0x80, 0xf3, 0x32, 0x76, 0x9b, 0xf4, 0x1d, 0xa4, 0xf1, 0xe2, 0x0e, 0xc3, 0x7f, 0xdc, 0xdf, 0x06, 0xcd, 0x79, 0xfc, 0xde, 0x0b, 0xfd, 0x65, 0x7a, 0xdf, 0x39, 0xe3, 0x68, 0x02, 0x8e, 0x2a, 0x5d, 0xe2, 0x0f, 0x15, 0x7b, 0x2f, 0x9e, 0xc7, 0xa7, 0x95, 0xac, 0x4d, 0xb6, 0xad, 0x15, 0xff, 0x6a, 0x9e, 0x5d, 0xe3, 0xca, 0x33, 0xb3, 0x9f, 0x38, 0x1c, 0x6b, 0x32, 0xfc, 0x7c, 0xdb, 0x88, 0xf2, 0xfe, 0xe8, 0x5b, 0x82, 0x67, 0x60, 0x5a, 0x0e, 0xdc, 0xb8, 0xc6, 0x5d, 0x4e, 0x39, 0x19, 0xea, 0x85, 0x33, 0xd2, 0xf1, 0xc1, 0xc9, 0xee, 0x07, 0xae, 0xc2, 0xfe, 0x1d, 0xa4, 0x78, 0xef, 0x68, 0xf8, 0x86, 0xd3, 0xe4, 0xa7, 0x13, 0x21, 0x95, 0x9d, 0xfb, 0xc7, 0x70, 0x7d, 0xd2, 0xba, 0xb2, 0x0f, 0x83, 0x64, 0xc1, 0x0b, 0xbc, 0xca, 0xc2, 0x97, 0x1b, 0x5a, 0x8e, 0x97, 0xa7, 0x75, 0x2f, 0x9f, 0xb1, 0x48, 0xc7, 0x47, 0x77, 0xff, 0x69, 0x2a, 0xc5, 0x7d, 0x1f, 0x30, 0x27, 0x57, 0x55, 0x6c, 0xea, 0x60, 0x48, 0xeb, 0x57, 0x48, 0x4e, 0xb9, 0xa5, 0x96, 0x00, 0xe2, 0x37, 0x31, 0x21, 0xc5, 0x3e, 0x51, 0x0d, 0x16, 0xa4, 0x3e, 0x9a, 0x10, 0xf9, 0xe3, 0xa8, 0x55, 0xb8, 0x76, 0x66, 0x27, 0xce, 0x83, 0x06, 0x9d, 0xd2, 0xd2, 0x6f, 0x47, 0x6b, 0x16, 0x39, 0xb9, 0xf4, 0x0d, 0xa0, 0x50, 0x53, 0x46, 0x44, 0xec, 0xa3, 0xf9, 0xb0, 0x4e, 0x47, 0x39, 0xd6, 0x4b, 0xe6, 0xe6, 0x48, 0x7f, 0x90, 0xa6, 0x31, 0x20, 0xd7, 0x21, 0x39, 0x7a, 0x9b, 0x60, 0x87, 0xa3, 0xf8, 0x26, 0xda, 0xb0, 0xc1, 0xa8, 0x42, 0x6b, 0xba, 0xdb, 0xc9, 0xd4, 0x84, 0x23, 0x7f, 0x10, 0xcb, 0x84, 0xfa, 0x88, 0xa9, 0x41, 0xa2, 0x69, 0x94, 0x8c, 0x9c, 0xdf, 0xc6, 0x68, 0x2a, 0x89, 0x8e, 0x7a, 0x9f, 0x85, 0x9c, 0xe4, 0x7a, 0xac, 0x76, 0x87, 0x47, 0x4d, 0x45, 0x2d, 0x05, 0x4f, 0x23, 0x9d, 0x3a, 0x42, 0x83, 0xdb, 0x5c, 0xef, 0xaa, 0xfd, 0xc9, 0x26, 0x35, 0xa9, 0x03, 0x5f, 0xc4, 0x67, 0xc0, 0xb6, 0x97, 0x06, 0xf6, 0x90, 0xbe, 0xc7, 0x68, 0x53, 0xc6, 0xf0, 0x42, 0x5f, 0x1b, 0x4f, 0x2e, 0x13, 0x63, 0xc6, 0x3d, 0x01, 0xbd, 0x42, 0x43, 0xe3, 0x9b, 0xc9, 0x2f, 0x59, 0xf3, 0x84, 0xd6, 0xaa, 0x76, 0x06, 0xb3, 0x89, 0x4f, 0x9a, 0xdd, 0xf6, 0xb0, 0x05, 0xf8, 0x82, 0xfe, 0x2a, 0x29, 0x19, 0x29, 0xf7, 0x1a, 0x07, 0x2f, 0xba, 0x9f, 0xee, 0xdb, 0x7d, 0x20, 0x43, 0x00, 0x94, 0xac, 0xe1, 0x5d, 0x78, 0x07, 0x55, 0x65, 0xb4, 0x95, 0x70, 0x5e, 0xc5, 0x51, 0x78, 0xde, 0x4b, 0x6d, 0x28, 0x6f, 0x97, 0xa8, 0x31, 0x39, 0xbf, 0x2c, 0x7a, 0xbd, 0x2c, 0x98, 0x67, 0xf8, 0xcb, 0x5d, 0x4b, 0xbd, 0x20, 0x44, 0x07, 0x3b, 0x94, 0x1a, 0x1d, 0x7e, 0x63, 0xf6, 0xa8, 0xf7, 0x0a, 0x5b, 0x08, 0xe8, 0xd1, 0xa3, 0x0c, 0x37, 0xd2, 0x32, 0xe5, 0x36, 0x26, 0x24, 0x56, 0xe6, 0xb2, 0x3a, 0xae, 0x87, 0x16, 0xd9, 0xd5, 0x66, 0x3c, 0x06, 0x3a, 0x24, 0x9f, 0x8b, 0x7a, 0xe3, 0x35, 0xe1, 0xb0, 0x77, 0x69, 0xd9, 0x37, 0x42, 0x7f, 0xb0, 0xd6, 0x10, 0x18, 0xee, 0x27, 0x2b, 0xe2, 0x5b, 0xe7, 0xcc, 0x1f, 0x8f, 0x78, 0x8a, 0x28, 0x5d, 0x1c, 0xc5, 0xca, 0xf5, 0xf8, 0x10, 0x3d, 0xf4, 0x71, 0xc9, 0x40, 0xe3, 0x13, 0xaa, 0x0e, 0x2a, 0x1f, 0xe5, 0x8e, 0x9e, 0x8a, 0x1b, 0xbb, 0x6c, 0x7f, 0x1b, 0x97, 0x2e, 0xa9, 0xc0, 0x8d, 0x03, 0x8f, 0x41, 0x55, 0xc2, 0xa2, 0x46, 0xd3, 0x89, 0xad, 0xe4, 0xf4, 0xbf, 0x1c, 0xc4, 0xd7, 0x7e, 0x77, 0xce, 0x48, 0x00 },
        .hmac_key_idx = DS_KEY_BLOCK_1,
        // results of message array encrypted with these keys
        .expected_results = {
        // Message 0
      { 0x835d38bf, 0xe5d63f67, 0x9969a043, 0xdf213e48, 0x5395a017, 0x78987b04, 0xec668fef, 0x138cea9d, 0xd2959a98, 0xca4bcc37, 0x73bb1fb9, 0xcfbf5c65, 0xac990953, 0x1010be27, 0x409fe4a2, 0x1a088f8e, 0x8a6129e2, 0x28b0f377, 0x95fe4361, 0x31c102ed, 0x9d56607e, 0x4a0e1bc6, 0x845492e7, 0x85041a2c, 0x11674b84, 0x5c7c5581, 0x9dcd3abc, 0x55787571, 0x7ebdb8d1, 0x35e919ef, 0x75bc464c, 0xc9a8f462, 0xbb6f7aea, 0x468b755d, 0xb1fed0b0, 0x9de6970d, 0x59b00212, 0x59072464, 0x45ca23fa, 0xad6b2f35, 0x204def10, 0x740ed293, 0x7bc9091a, 0xff564d15, 0x5dac25fc, 0xb71f9b49, 0xf4d4e929, 0xf43cb14d, 0xdb65ea58, 0x2e2b7709, 0x48ec8318, 0x5e2ac1b7, 0x434e5675, 0x941b94ea, 0xf8383423, 0x7ab130a4, 0x7ab3f1a7, 0x0f29b325, 0x37e2a0fb, 0x8f4a69fa, 0x5300ee3c, 0x60e2b8ea, 0xcee689b3, 0x4fb2e0d4 },        // Message 1
      { 0xaea0f0cc, 0x1a04cde4, 0x130e1cdb, 0x602d49ba, 0x3905c581, 0xef7076bb, 0x97cf179c, 0xb8d34ef3, 0xf67fed8d, 0xf83b3124, 0x03844cf5, 0x4b94e59e, 0xd4bc08ed, 0x29751177, 0x05a7bed1, 0xeaf15db4, 0xfb5c176a, 0x0022a082, 0x875783a1, 0xc7b0c8e3, 0xbb306d42, 0xaa940cbe, 0x24601cf1, 0x6a5f3b51, 0xb510d1c0, 0xe04a1c02, 0x7a75519a, 0x62898080, 0x072fb3c0, 0x8ea64421, 0x1d69910e, 0xce8f69d3, 0x4178728d, 0xf6a0ac68, 0xe912aef9, 0x399c8a67, 0x510b4530, 0x051870e4, 0xa88a8e9d, 0x58b9beb2, 0xe745be00, 0xc73e7aaf, 0x753a3b8d, 0x77e4e5f3, 0x11b9c826, 0x6ac2a4bb, 0xd586fcd5, 0xaa00a3f4, 0x6ba0891a, 0x9280c3e2, 0x2619978c, 0x6e84589a, 0x08619b2c, 0x56292b20, 0x61231305, 0x0fc4ed35, 0x3b98feb5, 0xe58c13e0, 0xd5be0160, 0xf8ecb215, 0xdcf2e362, 0x9e33ae28, 0xc321c2fb, 0x502a5838 },        // Message 2
      { 0x3ddfd386, 0x6e970ebe, 0xeb7ea383, 0xa669530d, 0x9d8932f3, 0xe6136de0, 0x1025104b, 0x0705d132, 0x6092beca, 0x3bcebea3, 0xd132717f, 0x75ff1b2f, 0xb3650121, 0x81978768, 0x69818c9c, 0xdcd7f2b3, 0xb833662e, 0xfd6c05e0, 0x5a60ffb3, 0x20b17ace, 0x0783c2cf, 0x9f9b2bfb, 0x00a33a3a, 0x9c578fff, 0x4bcb51d1, 0x97c65525, 0x201b4cce, 0x7cda0c91, 0xbfcba599, 0x205da6a5, 0xad5139b3, 0xdd371516, 0x3f163e24, 0xf3302b85, 0x3644d76e, 0xa4cb54e5, 0x6bd17357, 0x1b592082, 0x9f9d16ff, 0xed911666, 0x4da7e0a8, 0x3794bfad, 0x6cc87568, 0x91a3b09c, 0xf616aa63, 0xe3e613a5, 0x4e9f27ca, 0x2dfe69e3, 0xab52b446, 0xb470e27c, 0xc29267c5, 0xf8196031, 0xd62a42d0, 0xd23e3ece, 0xf57beacf, 0x60320b3d, 0x19482bbc, 0xe2565c30, 0xd2be65a7, 0xcf4c9ed7, 0xab283b90, 0x73665ce1, 0x17c472b3, 0x8f84065d },        // Message 3
      { 0x7a03a04f, 0xbaace918, 0x251a548a, 0xaef94e57, 0x044bb78c, 0x418e7bd2, 0x2baeb277, 0x9316a3d8, 0x09c55923, 0xc3847913, 0x9489f08b, 0x4202255a, 0x0052468a, 0x787c3556, 0xadcc0f35, 0x46905e61, 0x733c9627, 0x0adb7185, 0x5628be9f, 0x64acdebe, 0x0d65d855, 0xe910b82c, 0x93d6e9d7, 0x30e78e83, 0xe480a591, 0xb6a272a1, 0x439d1a60, 0x9ba73f8c, 0x4e47e26c, 0x77546854, 0x4b213196, 0x6c0a9ce5, 0xb1a1bf69, 0x0f4ec13f, 0x115b8e29, 0xe57ad59e, 0xd13f4088, 0x15b6ab73, 0xf850462e, 0x235e658a, 0xb67dda70, 0xf869a31c, 0x7a68d45c, 0x83798c24, 0x9b41ea3d, 0x51d3c878, 0xb0a09fd8, 0x5b43262e, 0xcad9a0ab, 0xa468879e, 0x7f587d0a, 0xf7b48bac, 0x4cf4d7a5, 0x749cbe80, 0x4232863f, 0xbaa0de28, 0x2f794252, 0x156ac039, 0x503fd973, 0x4636fe18, 0x8c814348, 0xe9b16601, 0xcd83fa21, 0x273c9388 },        // Message 4
      { 0xf98eb364, 0x99c9caff, 0x2600738b, 0x0624e1ee, 0x6ef8c7f0, 0x36acef55, 0x5fcbc2c7, 0x5da49157, 0xd66beabf, 0x424c2003, 0x58d5eed0, 0xf5b15fd2, 0x4d7d7608, 0x5841ca15, 0xfa323fad, 0x5ba1c2b5, 0x2d3b328d, 0xb0b4bef6, 0x28274fbd, 0xcd749d15, 0x82168019, 0xe5c7aaee, 0xc8859b8b, 0x42b4fdfd, 0x4cfa650c, 0x2458b829, 0x973b8891, 0x9694ed16, 0xaa2e8784, 0x34b134a9, 0x1d5ad164, 0xd292b460, 0x106883ff, 0x82e7a1a3, 0x4e7efe87, 0x82b91b3a, 0x509172ee, 0xecdb302b, 0xf3d844b0, 0x6033ce4d, 0xcb0e8db3, 0x48cc61fa, 0x21671b05, 0x87c4c8f7, 0xbd803bc9, 0xcc8cb60c, 0x52c671af, 0x7bf68d07, 0xbfd04b93, 0xa53221c2, 0x199415aa, 0x7ea79094, 0x8b633d2d, 0xf5e5307c, 0xe95ae38a, 0x2f56ef98, 0xfe788444, 0x75b2517e, 0x98fc8260, 0x8457cace, 0x3b54e191, 0x4db57540, 0x955b2f2d, 0x24c93908 },        // Message 5
      { 0x1f28ddc2, 0x15a2dc71, 0x8e7252e6, 0x52639b27, 0x52ce60f2, 0xd61531d8, 0xaaf57cca, 0x74f38591, 0x86a1308c, 0x2f6e64d6, 0x88e5c0b4, 0x151a998e, 0xdb9acdd4, 0xd34f328a, 0xb3de0215, 0xac48737e, 0xaddb7e17, 0x493a053b, 0x78e711a2, 0x45731b42, 0x92c2db4e, 0x6c053e5c, 0xc619cf31, 0x96a24999, 0xf77ac300, 0xdd0e2da7, 0x78b417d4, 0x238081fd, 0x2a145fa3, 0x8873f774, 0xf53a2d43, 0x6ed14716, 0x4eee7203, 0xdb7edc1e, 0x5ff789a4, 0xbfb9bfc3, 0x507dd2b1, 0x65aa4379, 0x0972c296, 0x7e69b5f8, 0x6ed7b232, 0xb92d76f9, 0xc22c4366, 0xba7c2143, 0x8f2c34af, 0x8a1bdf99, 0xcbedc3ce, 0xd232c905, 0x17efee6c, 0x9250bea0, 0x7ea0911e, 0x20dcb8a8, 0x33e8db59, 0xbd45664c, 0x1e62d9c9, 0xd50aa2a1, 0x7792a770, 0xb8b9fae7, 0xfa2fef77, 0x52a29eec, 0x618a5954, 0xd9236e6c, 0xa696f270, 0x7836803c },        // Message 6
      { 0xec64ca3c, 0x3678f40f, 0xe3d00448, 0x8404a50a, 0x101223df, 0x1aaa5b1e, 0x5f64bec9, 0x5d0ea2d2, 0x56a60d03, 0x13a8b3bb, 0x1c3e9a21, 0xf35e9061, 0xabe87d10, 0x21ee3db4, 0x7bed23da, 0x85bc8040, 0xfe8526cd, 0x8d23fec0, 0x8688adce, 0x007978c9, 0xa93f75f5, 0xaecc2565, 0x60fa222a, 0x6c3dca6e, 0x56a91183, 0xab8afb43, 0x8ddf1e0d, 0x94fbcbee, 0x255d9618, 0x3ee98bbc, 0x1ff43eb2, 0xfdb7edd7, 0xd352a38e, 0x82f977f3, 0x50873873, 0x12678596, 0x973fed11, 0xcf419c3b, 0xff80eb70, 0x56bf5e82, 0x3e45e09e, 0x20104ca2, 0xb350417e, 0xf7ba8440, 0xef3f6adc, 0x86077d78, 0xe63480fb, 0xcdb5f14b, 0x604e3b49, 0x82bb1259, 0x99764293, 0x7276ec12, 0x03d28b92, 0x324ff0c3, 0x95c31851, 0xd3d5d247, 0xbabf0578, 0x642bb38b, 0x62188f4c, 0x93ce0c9e, 0x1bfb9d63, 0xdd5d248c, 0x2316198e, 0x072e73f7 },        // Message 7
      { 0x00a3e1e2, 0x66ce9c3b, 0x0d21bcea, 0xc051866e, 0x7e25880c, 0x5e17f3f7, 0x3e7925e8, 0x04b1d12a, 0xf11510d6, 0xafc765d6, 0x4532e9c6, 0x6939cee0, 0x2634d48b, 0xa03e3cb6, 0x060d39ad, 0xe2a95e89, 0x856f7fba, 0xcb429a2f, 0xfb2c2ce1, 0x8d950c85, 0x508af1f4, 0xfbdf22cf, 0x6b7258ab, 0x36bd4d6e, 0x55603283, 0x235e2a6d, 0xe3faff0c, 0xb8b0dbef, 0x7cd8a9be, 0x1de43b98, 0xf3ed9652, 0x1a5451e6, 0xa33a57bb, 0xc09ea9b6, 0x3d882bb0, 0xb3bc50f2, 0xd14e9fe5, 0xe123aa3a, 0x42a552b5, 0x3e675022, 0x78362ba8, 0x1d85e30f, 0xdcbe7506, 0xc5916a41, 0x55508d5f, 0x55f3fe0c, 0x988e98fd, 0x57ca4d0e, 0xa10feaef, 0x7e457772, 0xe71324cc, 0x499dff0c, 0x59200869, 0x7748d113, 0x049f9382, 0x3a27952a, 0x11e88cda, 0x9f0be192, 0x91a5336e, 0x65baea68, 0xa6185492, 0x51e9990b, 0x654144a9, 0x8af5b175 },        // Message 8
      { 0x62c3edd8, 0x6fe563b4, 0xb55f8c9d, 0xeb212894, 0x544f427b, 0xdde12726, 0x25f49372, 0x58f31f93, 0xedae370a, 0x8bd35034, 0xe354465d, 0x734ffaf6, 0xecf5a0ce, 0x90d132ae, 0xdae05950, 0x4a98c3da, 0x22816397, 0x76cd5223, 0x959de8ce, 0xa6c70b75, 0x27f32d4b, 0x9b3b0e33, 0x25fb512a, 0xbd2bd4cd, 0x05b35f37, 0x88d625ea, 0x01f015a7, 0x703fe63e, 0x26a82c25, 0x58e42a0d, 0xe1a36751, 0x3a407afb, 0xccbfedd9, 0x6ccc2afd, 0x64a91a52, 0xcce659f6, 0x21bd8b9a, 0x70bafab3, 0x0bf349ef, 0xbc9ca563, 0x773eb021, 0xbdf4b1da, 0xc5d38991, 0x7f571195, 0x68c85949, 0x85a54e7e, 0x2af5550a, 0xd22049e4, 0x2e349d14, 0x44e65417, 0x74d2fcb3, 0xf476c998, 0x3a9ba228, 0xc172a514, 0x90c7c270, 0xd79c9b83, 0x831ae841, 0x3a7f9283, 0x372bae1d, 0xae1a6eeb, 0xbb459f0e, 0x0f6dafa7, 0x76d9fddc, 0x5cc22a15 },        // Message 9
      { 0x70f405b7, 0x465029b2, 0x077202c6, 0xd31c7f2b, 0xd190f66b, 0xb9dc1726, 0xe5fca329, 0xac39c8c0, 0xf4404de0, 0x7ceb2dae, 0xe6e76432, 0xb3f0da25, 0xdf2dfbac, 0x3fe0d5e6, 0x10250326, 0xb5a89d4b, 0x26e384c9, 0x94bf486a, 0xb83a42ed, 0x6a7584dc, 0xdbba6ce6, 0x0dc2a4b0, 0xfc1ec776, 0xa9e7207e, 0x3321d690, 0xd67dcff8, 0xfa440ece, 0x2cbe27e0, 0xae3b3f56, 0x01ad071f, 0x25e3f648, 0x2c09f3f9, 0x504061a4, 0x5e1d77b7, 0x48858bfc, 0x9c05d8cf, 0x57eab353, 0x10a7809f, 0xf28e5ff9, 0xe1e06bbf, 0xa483c0b6, 0x0bb3e25a, 0xb32a6b03, 0x81e6d0bf, 0x64cc457a, 0xbb514952, 0x84b0b2ea, 0x9b7f44f5, 0x14b1f423, 0x93614063, 0xb75e831d, 0xae508c35, 0x07d9de51, 0xcb5a9404, 0xca5593bf, 0xd3a6f1c8, 0xfe0578ca, 0xbc175972, 0xf83c64cf, 0xfb612553, 0xebb70016, 0x0ded9f6e, 0xc12eb1e9, 0x90d391d3 },     },
     },
    { /* Case 2 */
        .iv = { 0xf8, 0x40, 0x6d, 0xc2, 0x14, 0xcf, 0x51, 0xfa, 0xba, 0x22, 0x6c, 0x84, 0x62, 0xe8, 0x55, 0x1f },
        .p_data = {
            .Y = { 0x535e76e1, 0xbb4a17dd, 0x20d3dbb5, 0x5f7ec17c, 0x6977a611, 0xa1ae5fa4, 0xc594568c, 0x2d438edf, 0x4d509cf3, 0x25cef8dd, 0x7d2697c6, 0xa1f3fea3, 0xfe25c7d3, 0x4bfd0fe8, 0x3eca8236, 0x03000348, 0x86569a0d, 0x8b0267f9, 0xb3d48ba5, 0xf89fa10c, 0x8a3cb13f, 0xfb0c6b32, 0xe19d3352, 0xc0333a31, 0x461cb688, 0x9fc1a127, 0x999086c4, 0x426c9af4, 0x3b84d7c2, 0x595e840d, 0x03059a3a, 0x67da7f8c },
            .M = { 0xe1606627, 0x9d297304, 0x855cff73, 0x8f54d6a8, 0x7f4980cf, 0x4f9fc48a, 0xc28854ba, 0x15d3dadf, 0xb69701da, 0x71f59dba, 0x936d7087, 0x7b0fb5cc, 0xae90157b, 0x573300e1, 0x0dd851d2, 0x95b77694, 0x9f396f9f, 0x0535f83e, 0xec2059aa, 0x52f7d90d, 0x71b6cb43, 0x9b908e0d, 0x5b7f74ce, 0xb4d23d02, 0x6274ac91, 0x9f9226e7, 0x65dbd20b, 0x79a093fe, 0x25c33d7a, 0x5ddae9d5, 0xa4033194, 0xa4280185 },
            .Rb = { 0xbb5205ff, 0xbccc83fb, 0x1a5ae426, 0x1b690d94, 0xd086ba61, 0x61d76113, 0x4751acf6, 0x4017acbe, 0x885976fb, 0xf935b4ce, 0x732bf238, 0x9a4ee3cd, 0xd6b686e8, 0xd4672def, 0xa31974cb, 0x261fa313, 0x9dda81f7, 0x034671e9, 0xc38a0d95, 0x765fca05, 0x117bb854, 0x292584f8, 0xd5b23c9e, 0x9a394586, 0x35947983, 0xb0da4c47, 0x386421c1, 0x6d9c8ba4, 0xaf409892, 0x37893d40, 0x31ede7fa, 0x9199f876 },
            .M_prime = 0x0dd35669,
            .length = 31, // 1024 bit
        },
        .expected_c = { 0xe0, 0x77, 0x7a, 0xd4, 0xf2, 0xdc, 0x37, 0x20, 0xd9, 0xc7, 0x46, 0x3a, 0xf8, 0x36, 0x1f, 0x17, 0x1c, 0x28, 0x5e, 0xa3, 0x12, 0x8e, 0x60, 0x15, 0xdd, 0x11, 0xd9, 0xdb, 0x1c, 0xf1, 0x02, 0x32, 0x82, 0xff, 0xe0, 0x29, 0x30, 0x7d, 0x1a, 0x68, 0x98, 0x3a, 0x7b, 0x18, 0xd2, 0x37, 0x55, 0x1f, 0x4c, 0xf3, 0x8a, 0x9e, 0xe8, 0xed, 0x40, 0xf4, 0x95, 0xce, 0xc3, 0xa8, 0x9d, 0xfe, 0x39, 0x41, 0x39, 0x96, 0x43, 0xcd, 0x45, 0x06, 0x53, 0xac, 0x9e, 0x5e, 0x9d, 0x75, 0x34, 0x19, 0xec, 0x19, 0x79, 0xd8, 0x39, 0x9d, 0x98, 0x67, 0x83, 0x77, 0x70, 0xc9, 0x38, 0x22, 0x5f, 0x3e, 0x40, 0xfe, 0x53, 0x3e, 0xf8, 0x97, 0xd3, 0x1c, 0xe1, 0xeb, 0x04, 0x59, 0xf5, 0xd6, 0x10, 0xdf, 0x6d, 0xd4, 0x52, 0xae, 0x13, 0x32, 0x63, 0x0b, 0xdf, 0x0a, 0xfa, 0x5f, 0xa2, 0x87, 0x17, 0xb9, 0xe4, 0x37, 0xed, 0xa7, 0xf7, 0x8b, 0x42, 0xf7, 0x94, 0x13, 0x7e, 0x0f, 0x6a, 0x1a, 0x56, 0x1d, 0x52, 0x09, 0x1d, 0x07, 0x25, 0x90, 0xa8, 0xd7, 0x4f, 0xf6, 0xe8, 0xa2, 0x5a, 0x28, 0xb3, 0xac, 0x5f, 0x8b, 0xe1, 0x01, 0xeb, 0x30, 0x3e, 0x41, 0xd9, 0x99, 0x8a, 0x40, 0x88, 0x1e, 0xd2, 0x58, 0x82, 0x74, 0x66, 0x85, 0x10, 0x90, 0x70, 0x70, 0x3c, 0x98, 0x44, 0xa8, 0xa6, 0x5b, 0x92, 0x89, 0x81, 0x08, 0x69, 0x3c, 0xe4, 0x5e, 0xf4, 0x2b, 0xec, 0xf5, 0x46, 0x28, 0xba, 0x52, 0xce, 0xac, 0xe7, 0x1b, 0xa5, 0x85, 0x47, 0xd2, 0x6f, 0x20, 0xe0, 0x13, 0xf5, 0xa5, 0xe9, 0x91, 0x3c, 0x3b, 0xe5, 0xaa, 0x1f, 0xeb, 0x31, 0xf7, 0x00, 0x57, 0xbb, 0x07, 0x97, 0x4f, 0xdf, 0x60, 0xfb, 0xdc, 0x83, 0xf3, 0xb0, 0xc9, 0xe9, 0x6c, 0x86, 0x91, 0x1f, 0xa5, 0xd3, 0xd5, 0x23, 0x93, 0x95, 0x0d, 0x0f, 0x45, 0x2c, 0x94, 0x1e, 0x29, 0x54, 0xa3, 0xee, 0x67, 0xf0, 0xca, 0x28, 0xcc, 0x69, 0xc6, 0x10, 0xd6, 0xe9, 0x92, 0x4b, 0xfa, 0xaa, 0xae, 0xd2, 0x21, 0x32, 0x7a, 0xf2, 0x28, 0xdb, 0x30, 0xff, 0x06, 0x23, 0x2b, 0xad, 0xa6, 0xb7, 0xd1, 0x24, 0x83, 0x5e, 0x19, 0x56, 0xb8, 0x73, 0xf6, 0x34, 0x08, 0xef, 0x1f, 0x46, 0xd6, 0x3b, 0xb0, 0xfc, 0x43, 0x87, 0xd6, 0xf3, 0x91, 0x97, 0x1d, 0xcd, 0xa0, 0x04, 0x9f, 0xcf, 0xe9, 0xa3, 0x60, 0x68, 0x32, 0x06, 0xe1, 0x33, 0xeb, 0xf1, 0x63, 0x4f, 0x9a, 0xc9, 0xc3, 0xcd, 0x34, 0x07, 0x6d, 0x3b, 0x9e, 0xc3, 0xd5, 0xb1, 0x9c, 0xb1, 0x46, 0x32, 0xe5, 0x8d, 0x96, 0xb5, 0xdc, 0x64, 0x04, 0xd0, 0x89, 0x8e, 0xd7, 0xc7, 0x82, 0x4e, 0x30, 0x8f, 0xde, 0xce, 0x9f, 0xac, 0x17, 0xf3, 0xc0, 0x31, 0x9f, 0x72, 0xd3, 0x27, 0x42, 0xcb, 0x7f, 0x76, 0x75, 0xec, 0x75, 0xe2, 0x33, 0x1b, 0xca, 0xb2, 0x16, 0x18, 0xe5, 0x41, 0xcc, 0x7b, 0x2b, 0x5d, 0x04, 0x43, 0x77, 0x9d, 0x72, 0x53, 0xfb, 0x56, 0xb2, 0xf7, 0x4b, 0x03, 0x08, 0xf6, 0xa7, 0x23, 0x39, 0x17, 0x44, 0x91, 0xc9, 0x8b, 0xab, 0x8b, 0x40, 0xf3, 0x43, 0xb5, 0x04, 0x69, 0xca, 0x11, 0x3f, 0xca, 0x39, 0xa2, 0xbd, 0xea, 0x84, 0x15, 0xe0, 0x2e, 0x1c, 0x6e, 0xe4, 0x7c, 0x23, 0x1e, 0xd9, 0xb9, 0x91, 0xe3, 0x26, 0x49, 0xdb, 0x53, 0x66, 0xbc, 0xe7, 0x3e, 0xb9, 0xa8, 0xa7, 0x7c, 0xed, 0xe1, 0xf5, 0xaf, 0x7a, 0xd2, 0x53, 0x88, 0x4e, 0x2b, 0x98, 0x81, 0x56, 0xbe, 0xae, 0xcc, 0x96, 0x4d, 0x50, 0x22, 0x8d, 0xdc, 0x04, 0x31, 0xf3, 0xf7, 0x7d, 0x2a, 0xfa, 0xbb, 0x9d, 0x23, 0x4e, 0xc1, 0xb7, 0x3a, 0xfb, 0xae, 0x4f, 0x3f, 0xf0, 0x1b, 0xd3, 0xe1, 0x50, 0x41, 0xf4, 0x91, 0x58, 0xcb, 0x01, 0x43, 0xaa, 0xff, 0xde, 0x71, 0xaf, 0x49, 0x59, 0x9e, 0x9b, 0xc4, 0x48, 0xb1, 0xfa, 0x82, 0xd0, 0xe2, 0x96, 0xee, 0x88, 0xf3, 0x63, 0xdc, 0x52, 0x9c, 0x49, 0x08, 0xb0, 0x17, 0xa6, 0xcc, 0x7c, 0xbd, 0xc4, 0x8d, 0x5e, 0x40, 0x81, 0x2a, 0x04, 0xf5, 0x22, 0x7c, 0x87, 0xab, 0xa4, 0xfb, 0x0f, 0x0b, 0xed, 0xe3, 0xd7, 0x09, 0x69, 0xab, 0x28, 0xa0, 0x76, 0x01, 0xdc, 0xb3, 0xb4, 0x89, 0x0b, 0x37, 0x86, 0xb9, 0x45, 0x6d, 0x35, 0xe2, 0x82, 0x45, 0xf3, 0x6f, 0xab, 0xdb, 0xd6, 0xdb, 0xd2, 0x68, 0x10, 0xf0, 0x7e, 0xba, 0x77, 0x2b, 0x31, 0x9b, 0x7d, 0x9f, 0xb0, 0xf7, 0xad, 0x88, 0x72, 0x37, 0x45, 0x99, 0xff, 0xe6, 0xdd, 0x98, 0x1b, 0xf7, 0x08, 0x2d, 0x85, 0xa6, 0x05, 0x61, 0xa7, 0x44, 0x5e, 0xce, 0x47, 0x9a, 0xba, 0x7a, 0xc6, 0xb0, 0x94, 0xd2, 0x95, 0x3f, 0x77, 0x73, 0xcd, 0x81, 0xa7, 0x5b, 0xcc, 0xf0, 0x85, 0xae, 0xd8, 0x72, 0xfa, 0xdf, 0x12, 0x64, 0xec, 0x0a, 0xec, 0xcc, 0xce, 0x7b, 0xd7, 0x6f, 0x0b, 0x40, 0x49, 0xef, 0x0c, 0x20, 0x4a, 0x68, 0x7b, 0x92, 0xf7, 0xd0, 0x30, 0x5c, 0x5c, 0xe5, 0x8c, 0xcc, 0x7a, 0x9d, 0xad, 0x6d, 0xf8, 0xcb, 0xdd, 0xfd, 0x84, 0x0f, 0x65, 0x86, 0x54, 0xb8, 0x75, 0xf1, 0x9c, 0x3c, 0xb3, 0xd0, 0x43, 0x65, 0x88, 0x2e, 0xcb, 0xd4, 0x4c, 0x82, 0x54, 0x81, 0x44, 0x5c, 0x44, 0x16, 0xf9, 0x64, 0xa0, 0x8e, 0xf5, 0x47, 0xba, 0x25, 0x52, 0xb0, 0x5a, 0xa1, 0xaf, 0x0c, 0xfc, 0x8f, 0x8b, 0x9b, 0x4a, 0x7e, 0x4e, 0xdd, 0x5b, 0x55, 0xb0, 0x01, 0x5f, 0xe9, 0xfd, 0x4e, 0x01, 0xe9, 0x9c, 0x71, 0x3f, 0x58, 0xad, 0x21, 0x5e, 0xb5, 0x5e, 0xf9, 0x4b, 0xd2, 0x61, 0x48, 0x1f, 0xa9, 0x77, 0xbb, 0x85, 0x9b, 0x09, 0x84, 0xda, 0xb5, 0x57, 0x27, 0x73, 0xd5, 0xa3, 0xa5, 0xf1, 0x2c, 0xbb, 0x53, 0x01, 0x6a, 0x97, 0x26, 0xe8, 0x97, 0xa3, 0x62, 0xea, 0xf8, 0x9f, 0x30, 0xfd, 0xe9, 0xa8, 0x35, 0x67, 0x54, 0xff, 0x6b, 0x2d, 0x79, 0x12, 0xad, 0x3c, 0x14, 0x6d, 0x9a, 0x62, 0xe1, 0x60, 0x3c, 0x73, 0xa0, 0x73, 0xa7, 0xe4, 0xb6, 0x37, 0x90, 0x2b, 0xec, 0xba, 0xa2, 0x59, 0xbc, 0xfa, 0x45, 0x78, 0x05, 0x45, 0xfb, 0x25, 0xac, 0xa1, 0x78, 0xbf, 0xa2, 0xe7, 0xa3, 0xb7, 0x94, 0x00, 0x65, 0xf3, 0xb6, 0x44, 0xc6, 0x51, 0xc0, 0x50, 0xe5, 0x58, 0xf5, 0x5b, 0x0d, 0xb2, 0xa7, 0x00, 0xa8, 0xa7, 0x21, 0x6a, 0x6e, 0x9f, 0x11, 0xbc, 0x48, 0x01, 0x78, 0x86, 0x4f, 0x64, 0x27, 0xd6, 0x8c, 0xa4, 0xc0, 0xfb, 0x80, 0x50, 0x32, 0x21, 0xc8, 0xce, 0xd3, 0xe7, 0x79, 0x8e, 0xe8, 0x10, 0x3f, 0x3a, 0x36, 0x92, 0x1e, 0x18, 0x08, 0x70, 0x8d, 0x62, 0xcf, 0x67, 0x0f, 0xdc, 0xd1, 0xcb, 0xeb, 0x18, 0x53, 0xb7, 0xfe, 0x1a, 0xbf, 0xbc, 0x4f, 0x04, 0xc1, 0xb1, 0x7b, 0x02, 0x0f, 0x3f, 0xd7, 0xe4, 0xb6, 0x0e, 0x7e, 0x64, 0x8f, 0x53, 0x4a, 0x47, 0x96, 0x18, 0x8b, 0x10, 0x61, 0x60, 0xb1, 0xaa, 0xfb, 0xfb, 0x79, 0x8e, 0x15, 0xe5, 0xb9, 0xc0, 0x95, 0xf5, 0xeb, 0x8a, 0xff, 0x5d, 0xde, 0xea, 0x50, 0xe9, 0x61, 0xf0, 0x19, 0x9e, 0x51, 0xd5, 0x1c, 0xba, 0x5c, 0x18, 0x24, 0x6d, 0x0e, 0x16, 0x73, 0x65, 0xc2, 0x33, 0x1b, 0x17, 0xa2, 0xe1, 0xb2, 0x8b, 0xd0, 0x8f, 0x79, 0x35, 0x75, 0xd5, 0xfe, 0x80, 0xfc, 0xf0, 0x83, 0xc0, 0xff, 0xfd, 0x64, 0x54, 0x77, 0xb3, 0x68, 0xc1, 0xc5, 0x9d, 0x17, 0xd4, 0x7d, 0xd0, 0xdb, 0x51, 0x29, 0xaa, 0xe8, 0x23, 0x21, 0x9c, 0xa8, 0x86, 0x9e, 0xd5, 0xd7, 0x5d, 0x70, 0xfb, 0xeb, 0x57, 0x03, 0x5b, 0x46, 0x95, 0x1e, 0x5c, 0xb6, 0xdd, 0x87, 0x1d, 0x3a, 0x82, 0x0c, 0x4f, 0x2b, 0x7b, 0x23, 0x09, 0x61, 0xf1, 0xd3, 0x3f, 0xcb, 0xf1, 0x19, 0xf2, 0x9d, 0x8e, 0xcb, 0x17, 0x51, 0x56, 0x03, 0x36, 0xf7, 0x66, 0x43, 0x4f, 0x02, 0xe3, 0xde, 0xf7, 0xab, 0xbd, 0x60, 0xb0, 0x07, 0x97, 0x26, 0xf0, 0x0e, 0xe6, 0x26, 0x31, 0x0c, 0x67, 0x49, 0x10, 0xe8, 0x78, 0x6c, 0x38, 0x2e, 0xbe, 0x1b, 0x6c, 0xd2, 0x61, 0x57, 0x62, 0x15, 0x28, 0xae, 0x61, 0x34, 0xef, 0x50, 0x4e, 0x43, 0x09, 0x38, 0xf8, 0xaa, 0x85, 0xd0, 0xae, 0x3f, 0xc0, 0x4c, 0xb5, 0x0a, 0xff, 0x90, 0x7b, 0x82, 0xf6, 0x2e, 0xa2, 0x44, 0x13, 0x08, 0x02, 0x13, 0x3a, 0xe8, 0xf4, 0x0d, 0x81, 0x62, 0x65, 0x4b, 0xb9, 0xb5, 0x73, 0x3c, 0x4c, 0xf1, 0x93, 0x7d, 0x84, 0xaa, 0x84, 0x66, 0x44, 0x38, 0x0b, 0xb4, 0xdb, 0x08, 0xfa, 0x24, 0x73, 0xbf, 0xfe, 0xfc, 0x1a, 0x4e, 0x7d, 0x72, 0x5e, 0x1b, 0xd4, 0x2d, 0xf2, 0x45, 0x27, 0x52, 0x9a, 0x22, 0x7a, 0xc0, 0xba, 0x92, 0x4b, 0xad, 0x39, 0x20, 0x85, 0x49, 0x88, 0x60, 0x09, 0x1f, 0x40, 0x23, 0xe7, 0xcc, 0xca, 0x3d, 0xe1, 0xe6, 0x4e },
        .hmac_key_idx = DS_KEY_BLOCK_1,
        // results of message array encrypted with these keys
        .expected_results = {
        // Message 0
      { 0xf310f445, 0xef8a7e16, 0x3305705e, 0x8425f05d, 0x7c52803b, 0x3683f157, 0x66aed50c, 0xf25c6a91, 0x7f527392, 0x12961c88, 0x36371944, 0x559457b5, 0xd00a275b, 0x5e5d03d6, 0xbd69fbce, 0xeb255ff9, 0x19282ae3, 0x783e7db8, 0xa6fae8fe, 0x31ab084e, 0x91089b30, 0x067cfadc, 0x30f9978d, 0xb20c9992, 0x4c9e106d, 0x315504fd, 0xe0b32d13, 0x4e806332, 0xbf3b26a0, 0x0328d962, 0x58a5b7bb, 0x48f7370c },        // Message 1
      { 0xd61b5930, 0x10ac25c2, 0xf9c7c5a6, 0x42910e3b, 0xda9de774, 0xd7db92d6, 0x9a2aeac8, 0x7680d452, 0xb4cfd2a1, 0xef63641b, 0x46f78247, 0x0bead2e5, 0xa7f8ba66, 0x9eaab78a, 0x12758fa0, 0xa3597102, 0x3d6adcdc, 0xa0757ffd, 0x21cf3f44, 0x2e8138d7, 0xf0b43523, 0xcfe41517, 0x7089a941, 0x93a9170a, 0xbec7fc51, 0x5de82ec3, 0xb27b0153, 0x3807e930, 0xbd574483, 0xf2d1adeb, 0x87caf02f, 0x66e571af },        // Message 2
      { 0x187e5c38, 0x54c69610, 0xc0b0cbd0, 0x12bd0199, 0x1bec2bfa, 0xcaf3840c, 0xe399adda, 0x0a30609a, 0xbdaa98dc, 0x04e68f02, 0x2a2d27a1, 0x3cb09660, 0xa4d205b4, 0x18083a97, 0xc1d2f725, 0x94c3a5f0, 0x5c86722d, 0x01fc30bb, 0xc9ceade1, 0xcd39318f, 0x63708412, 0x4949dc3b, 0x8c2b6ca0, 0x122a091a, 0x3b840e52, 0x69ebf753, 0x50fcbc3a, 0x7884da4c, 0x6c4af9e1, 0x3714260c, 0x2cdcc350, 0xa112c15b },        // Message 3
      { 0x7aa0add9, 0x19745c35, 0x94da41fe, 0x7ad60b93, 0x5dfb248d, 0xd85228e7, 0xb54b3877, 0xb42f3cb8, 0xf80f3d95, 0x3fd69b4a, 0x5c9a82e3, 0x2450d3a7, 0x3286b6c2, 0x2829dec8, 0x79a78536, 0x71889db4, 0x4d525cdc, 0x9bd01eb8, 0x2e7f72c0, 0x4b6a9ea7, 0xa5d69db2, 0x67612452, 0x26cd107e, 0xf7383cac, 0xeca5d8a4, 0xb4f08dcd, 0x1820038c, 0xf0f6faee, 0x1b77ca61, 0xcfac29fe, 0x0092447d, 0x29de2f24 },        // Message 4
      { 0xb7a5df0f, 0xc75373f1, 0x3ea72c51, 0xdd9ef451, 0x94707b36, 0x19e17306, 0xbf103cdb, 0x95fe4561, 0xe3db7633, 0x4cd2d7a0, 0x510023f5, 0xbb78e70b, 0x914482c4, 0xbffccb98, 0x9526656a, 0x97ae7b13, 0xec2db60c, 0xc0d2ddff, 0x41d2f4d3, 0xfa1af1fc, 0x4cf33e46, 0x1b5cc385, 0x3a0675a8, 0x1994449b, 0xe36a3227, 0x33e75082, 0xc134accb, 0x53e2791d, 0xc8b0035e, 0x8a2153fe, 0xc17700d4, 0x798b90e7 },        // Message 5
      { 0x7b2ebde1, 0x90b72133, 0x64e70d23, 0xead12105, 0xcb83cbf4, 0x8abc79c4, 0xf535af57, 0x8d6546e2, 0xbf5dbf6d, 0x6c14834c, 0x737b764b, 0x98b7e61b, 0x52b7837e, 0xc0af477e, 0x8279583a, 0x695b8504, 0xdbd8518f, 0x7c10ba2d, 0xb3a62c34, 0xc44fa7b7, 0xea6a7173, 0xaf390343, 0x0fecdf96, 0x460eac91, 0x092ab7a7, 0xea1f05cb, 0x14e5e350, 0x37afa09b, 0x6192d38e, 0xa5a84a2e, 0x60ff8653, 0x61b5139c },        // Message 6
      { 0xf9c1f8c3, 0xa9cab7eb, 0x5c4bf6b0, 0x72b5fc95, 0xc135d95a, 0x9a0e9274, 0x7e5f01c9, 0x96f545f1, 0x75f80b45, 0xa4e86727, 0x83726459, 0x202c1901, 0xcd7501cf, 0x6645988c, 0x001fcf80, 0xcdc47212, 0x00c1eb3d, 0x46325c5f, 0x2dbfce7c, 0xe1abcf75, 0x3383ff3c, 0x2eea981f, 0x8575e22c, 0x328364d3, 0x2e9e4f77, 0x29699d6f, 0x87553675, 0x680c6dd5, 0xd13cefb1, 0xe1d92518, 0x96f6d4fb, 0x1f77b77d },        // Message 7
      { 0x5c87dd11, 0x15c93747, 0xbcfe22f1, 0x21dd758d, 0xef45f74c, 0x5ee8ecd6, 0xadf3bd88, 0xf121ac92, 0xc9f2ecf2, 0xb15dc716, 0xdbc9e90f, 0xc806ffdb, 0x847ab647, 0xeb332783, 0x58cc1ae1, 0xe6141bc3, 0xda692ef1, 0xbd34d333, 0x6f132b8e, 0x628bc926, 0xfd5b168d, 0xc0ea4851, 0x51e30761, 0x6acdefad, 0x445084b7, 0x951871b7, 0x36224984, 0xfcaaf34a, 0x63e22ba1, 0xdb08fa93, 0x4d6e1866, 0x484a40b7 },        // Message 8
      { 0x865fa089, 0x79f6170d, 0xf418880c, 0xa20da1bd, 0xf76f4041, 0xbc53972d, 0xeaa4560f, 0x39dd7056, 0x43dd5a60, 0xfaffb757, 0x8e8f2e2e, 0xb1ebb81d, 0xe5a114d3, 0xe0420859, 0x37b94b75, 0x7edf1b31, 0x1a7c257f, 0xfed4fd79, 0xbfa6a733, 0x1ef1f749, 0xe3798bf5, 0xa3c4e95b, 0xaaeae176, 0xb413d684, 0xcfafb071, 0xfd8cfa58, 0x42144301, 0x221408ce, 0x7d191498, 0xfff720b2, 0x80ed8829, 0x939da9d6 },        // Message 9
      { 0x98059557, 0x487b9a53, 0x1fe77a9f, 0x2f04419c, 0x1e25f53c, 0x85824732, 0x2f2a2a6b, 0x6784ce75, 0xc7a6e268, 0x167c1182, 0x330ad69c, 0x0c5bb9db, 0xaa23d157, 0xce49071a, 0x5bb53063, 0x76dc0f9d, 0xa4c01941, 0x38961aef, 0xdc7281c1, 0x22ae6c1d, 0xe3c0e5cb, 0xa01e3dd9, 0xdfef9f3c, 0x9dcdbcdb, 0xba7c9d06, 0x01258f94, 0x9603960e, 0x7bd2480f, 0x0a73d886, 0xb9dc2b12, 0x324e7747, 0x0b2fbdb9 },     },
     },
    { /* Case 3 */
        .iv = { 0x07, 0x26, 0x01, 0x00, 0x07, 0x30, 0x8f, 0x4b, 0x20, 0x54, 0x05, 0x88, 0xcb, 0xf6, 0x05, 0xe5 },
        .p_data = {
            .Y = { 0xb97e9089, 0x92bb938b, 0x9892be06, 0xc1a2a5ca, 0xa9cb1028, 0x55c8f928, 0x7665d7ff, 0x287b020f, 0xc503d1ea, 0x0843b294, 0x8a412553, 0xc4652f7e, 0xf1b761df, 0xe8153cfd, 0x4425f8fb, 0x583ae85d, 0x8735f46f, 0x9483fd15, 0xd46df014, 0xdac59c17, 0xa46d45fb, 0x26fc826e, 0xe4e9519f, 0x7462c0aa, 0xed541ed2, 0xbf06061a, 0x74aab167, 0xe4c49d25, 0x14fb8999, 0x2d00e9c7, 0x1cccb430, 0xa9c67c2d, 0xea975455, 0x73833878, 0xa7beb775, 0x92f70430, 0x40d6a9d1, 0x54395566, 0x2f9c7599, 0xe659849e, 0x14128321, 0x4db8cca1, 0xd7c52f4a, 0xd7892ae0, 0x92c835b5, 0x23ff7c38, 0x82dd6682, 0x77f93b02, 0xeb697fa7, 0x2f6f6e00, 0xfdda7bd1, 0x82340641, 0x2875cd5d, 0x02056859, 0x2935657e, 0x92f1ab2b, 0x59948432, 0x30928514, 0x796328b4, 0x5761a734, 0x2346afdf, 0x6c4eb0a4, 0xccce2ce7, 0x68e15c95, 0x9d95baae, 0x9409e78a, 0x41bec6e5, 0xa3652e6d, 0xd269be1a, 0x3d4eea59, 0x4f005ed0, 0xdd91c023, 0xef1334e4, 0x42e7c240, 0x8b946495, 0x85a23d76, 0xdcc4188e, 0x1110ee63, 0x48de7ff7, 0x822a8e1c, 0x685dbd77, 0xb7422ded, 0xb92e6f85, 0xd358f417, 0xbdd2cec7, 0x8a41ab3a, 0x246fbed3, 0xd9297ebc, 0xf54e6aa1, 0x4e781094, 0x29fb8e67, 0x5e79fe73, 0xd6d674a0, 0xf6813b9b, 0x3317a38f, 0x4594eb55 },
            .M = { 0xbbd1af17, 0x0be7a601, 0x53608285, 0x4eb25cc2, 0xf3119d6b, 0x94919f21, 0x2397852d, 0x52c5be66, 0x025c7cfa, 0x1ba0c334, 0xc79f18ba, 0x3de18e0a, 0x6c72a97e, 0xac7e9991, 0x4bed0b9b, 0x1ab7ff4c, 0x4fb33c28, 0x27d6712c, 0xe43b3b2c, 0x8e633f56, 0x71e67903, 0xa2678cfc, 0x734ab7e9, 0xb9451207, 0xcdccf854, 0x6caaf571, 0x01d3d6a4, 0xa4d68a05, 0x10dea759, 0xb8023815, 0x8de8a468, 0xc9e91a71, 0x869aa211, 0x054b71e9, 0x084555a7, 0xbce52630, 0xcebdcff2, 0xa2358d0a, 0xb2a380a8, 0x09949a46, 0xae9b505f, 0x8a810b78, 0xfe3f3937, 0xbf410864, 0xdea8d687, 0xa2ea5869, 0xc7b11593, 0x2b8e6c62, 0xda1668df, 0x76a95201, 0x33d9f672, 0xd691a839, 0xc546c77f, 0x85ceb3d5, 0x266c5eb1, 0xcb2b63e1, 0xe27ae747, 0x617931bd, 0xb364f680, 0xa0769171, 0xc1a38b1c, 0xf75eb916, 0x8435f7e8, 0x45a0ebf3, 0xd966bb0e, 0xc0f9036e, 0xe099a0dc, 0x19ff118e, 0xdc7b783a, 0xefc2f020, 0xa1697d80, 0x2b7a31a9, 0xbef58278, 0x1a118226, 0xa9831315, 0x17bdf59f, 0xf11267a1, 0x0ab5efb8, 0xe5dc1136, 0x2899fef0, 0x07c5fbcf, 0x2c77ce76, 0x2a732184, 0x51ace300, 0x47b79082, 0x171bb930, 0x9a9e6010, 0x86a49bf6, 0xa6a8cab1, 0x0cc56db5, 0x4d2dcf44, 0x6116064f, 0x3e1b62e1, 0x658e7b50, 0x34ba15ff, 0xceeec86a },
            .Rb = { 0xd1451d74, 0x2526ed62, 0x9804f511, 0x84be11f7, 0xc7c65580, 0x100c8136, 0xa968d71c, 0x11711eba, 0x006ea4cc, 0x8694d270, 0xadf746f7, 0x887b34af, 0x7e9029d5, 0x3ad9959c, 0xae5fbb3c, 0x8192414e, 0x94a82f47, 0xdb34be1d, 0x1acd0d11, 0x54c1d13f, 0x56825835, 0x6a0150c6, 0xbebb3296, 0x85a152c0, 0x4349e49c, 0xcb9c766a, 0x49e426bb, 0x090f0059, 0xb6021234, 0x73dc6699, 0x18baa9c2, 0xdb32f151, 0xb1b5fd99, 0xee486147, 0x8ed92966, 0xe6c8b9a7, 0xc48b8d86, 0x265f6214, 0x6f9e5f76, 0x989813e0, 0xe2d23dba, 0x750944a5, 0xe53b4b52, 0xba02c503, 0x4009526c, 0xc4a6789d, 0x04a34302, 0xaf0b9dea, 0x71280cb6, 0x7970de82, 0x6ff65599, 0x3c5ac5ee, 0x735e7641, 0x7a618852, 0x82b53751, 0x4e6841d8, 0x8d127d35, 0xcdd3d8d0, 0xac5bad4d, 0x122e8ea6, 0xe63b2e31, 0x8ef66708, 0xdaa8dd0a, 0xe285b783, 0x77a85818, 0x5abf73f7, 0xfeb55474, 0x7f3d3b77, 0x14d14da3, 0xba56a8d8, 0xf9b6c032, 0x2f9e4ebe, 0xab6feba3, 0x62357349, 0x24c08581, 0xcb0ba934, 0xea517072, 0xbdd16d91, 0x72c112cf, 0x98eb2eec, 0x293b13c2, 0x26573c43, 0x68a2c908, 0xbd894b59, 0xdd04bc62, 0x840b34fa, 0xe0179957, 0x4389031c, 0xb955ece8, 0xbb158537, 0xac5a986c, 0x3e11b79a, 0x2e5d08e8, 0x8e144e63, 0xddcc4cd9, 0x0baa168f },
            .M_prime = 0xb8838759,
            .length = 95, // 3072 bit
        },
        .expected_c = { 0x74, 0x3a, 0x44, 0x62, 0xa7, 0x7a, 0x9a, 0x00, 0xe6, 0x18, 0xdd, 0x5e, 0xaf, 0x37, 0x77, 0x31, 0x1b, 0x19, 0x6b, 0xc1, 0x67, 0x2c, 0x19, 0x23, 0xed, 0xa8, 0x49, 0x08, 0xf7, 0x98, 0x24, 0x8f, 0x24, 0x5b, 0xfb, 0x11, 0x35, 0x1a, 0x19, 0x72, 0x3b, 0x72, 0x77, 0x3f, 0xcf, 0x93, 0x9a, 0xc0, 0xc8, 0x81, 0xc9, 0xab, 0x5f, 0xe2, 0x7d, 0xe7, 0xec, 0x6d, 0x63, 0xb7, 0x01, 0xe7, 0x86, 0x1f, 0x3e, 0x5e, 0x72, 0x67, 0x0e, 0xc8, 0x81, 0x98, 0xf3, 0x52, 0x14, 0xb4, 0xf9, 0x8a, 0xd2, 0x92, 0x63, 0xf2, 0xa9, 0xc5, 0x60, 0xd9, 0x27, 0xc8, 0x97, 0x13, 0x75, 0x7e, 0x58, 0x60, 0x41, 0x13, 0xe0, 0xf6, 0xb5, 0x04, 0x65, 0xaa, 0x05, 0x5b, 0xea, 0xb7, 0x2b, 0xef, 0x17, 0xf1, 0x81, 0x72, 0x6d, 0x32, 0x91, 0x25, 0xb4, 0x84, 0x71, 0x7a, 0x76, 0x60, 0x01, 0x9b, 0x49, 0x1f, 0x7a, 0x63, 0x85, 0xc8, 0x0e, 0x46, 0x1e, 0x3a, 0xbd, 0x19, 0x7e, 0x17, 0x0e, 0x0b, 0xa8, 0x6d, 0x64, 0x29, 0xc6, 0x94, 0xcf, 0x4d, 0x38, 0xc2, 0x14, 0x45, 0x40, 0xc2, 0xd6, 0x24, 0xc7, 0x5c, 0x6e, 0x45, 0x2c, 0xf4, 0xb0, 0xd8, 0x7c, 0x38, 0x9f, 0x0f, 0xc2, 0x1d, 0x42, 0x3f, 0xcf, 0x6e, 0xf4, 0x91, 0xe3, 0x88, 0xf9, 0x1c, 0xc8, 0x01, 0xe6, 0x0c, 0x21, 0x4c, 0xd8, 0xda, 0xa5, 0xe5, 0x83, 0xbb, 0x80, 0xe7, 0xfc, 0x51, 0x0a, 0x47, 0x70, 0x40, 0x79, 0xc0, 0x5d, 0x0b, 0x3b, 0xeb, 0x58, 0x13, 0x19, 0xfe, 0x75, 0x64, 0x30, 0xd6, 0x3c, 0x79, 0xd6, 0xd5, 0x2e, 0x5e, 0x84, 0x2a, 0xb5, 0x9d, 0xad, 0xde, 0x3b, 0x09, 0xe2, 0x28, 0x48, 0x34, 0x2d, 0xf2, 0x09, 0x05, 0x1b, 0x9f, 0x2e, 0x00, 0xf3, 0xb3, 0x58, 0x06, 0xc1, 0x68, 0xe1, 0x80, 0x06, 0x4c, 0x7d, 0x52, 0x6d, 0xff, 0x3c, 0x11, 0xe4, 0x48, 0x72, 0x85, 0x7a, 0x6e, 0xff, 0x1f, 0x04, 0xe9, 0x9e, 0x17, 0x84, 0x59, 0x00, 0xc6, 0x18, 0xce, 0x9c, 0xf3, 0x2d, 0xf2, 0x02, 0x7f, 0xae, 0xd2, 0x7b, 0xf2, 0xf8, 0xf0, 0xff, 0xab, 0xaa, 0x7c, 0x9c, 0x5f, 0xa0, 0x8c, 0x44, 0x65, 0x71, 0x68, 0x86, 0xec, 0x12, 0x64, 0xa4, 0x4b, 0x14, 0xc1, 0x9b, 0xf1, 0xfd, 0xd1, 0xc6, 0x3e, 0x62, 0xff, 0x99, 0xcd, 0x18, 0x3d, 0x89, 0x1f, 0xae, 0xb7, 0x1d, 0xaa, 0xfa, 0x14, 0x82, 0x72, 0x68, 0xd7, 0x4d, 0x09, 0x01, 0xa7, 0xb2, 0xc8, 0xad, 0x16, 0xd7, 0x0a, 0xa9, 0xd9, 0xf9, 0xaf, 0x4c, 0x35, 0x91, 0x56, 0x61, 0x05, 0x14, 0xd5, 0x8b, 0x53, 0x29, 0x0f, 0x1c, 0x7d, 0xfb, 0xc5, 0x3a, 0x73, 0x97, 0x92, 0x0b, 0x66, 0x02, 0xe1, 0xdb, 0x25, 0x13, 0x11, 0x71, 0x3f, 0x1d, 0xaa, 0x84, 0x44, 0xb1, 0x0a, 0xe2, 0x46, 0x06, 0xee, 0xf4, 0x71, 0x7f, 0xf1, 0xed, 0xc3, 0x0a, 0x32, 0x63, 0xe5, 0x8f, 0x8b, 0x60, 0x01, 0x82, 0x0d, 0x68, 0x29, 0xbc, 0x53, 0x36, 0xfc, 0xcb, 0x05, 0x79, 0x0a, 0x6b, 0x25, 0xec, 0x71, 0x1e, 0x3a, 0xdc, 0x87, 0xcb, 0xc7, 0x64, 0x93, 0x64, 0x66, 0x90, 0xbf, 0x83, 0xc4, 0xef, 0x56, 0xc2, 0x61, 0xe2, 0x34, 0x9d, 0xb0, 0x21, 0x19, 0x7e, 0xc3, 0xe9, 0xc4, 0x9c, 0x25, 0x0a, 0x48, 0x0f, 0xe4, 0x1e, 0x3a, 0x2d, 0x5b, 0x71, 0xa3, 0x54, 0xb0, 0xec, 0x02, 0x8f, 0x13, 0x77, 0x44, 0xbe, 0x68, 0x01, 0x54, 0x1d, 0x4c, 0xb8, 0xbf, 0x57, 0x02, 0x3a, 0x58, 0xe0, 0x7a, 0xc2, 0x90, 0x43, 0x4d, 0x9b, 0xfe, 0xfe, 0x86, 0x95, 0x58, 0x1b, 0x2e, 0xcd, 0xe2, 0x87, 0x09, 0xea, 0x10, 0x0a, 0x7b, 0xc3, 0x83, 0xd8, 0x42, 0xc0, 0xf6, 0x69, 0xb1, 0x65, 0xca, 0xb3, 0x75, 0x39, 0x33, 0x27, 0x79, 0x27, 0xc8, 0xab, 0x4b, 0x2e, 0x35, 0x98, 0x4c, 0xe7, 0x4a, 0x9d, 0x10, 0x91, 0x9a, 0xed, 0x65, 0x82, 0x58, 0x6d, 0x8a, 0xf3, 0x70, 0x8d, 0x2c, 0xd4, 0x7d, 0xf4, 0x11, 0x72, 0x62, 0x76, 0x77, 0x55, 0x3b, 0x6b, 0x0c, 0xf9, 0x9b, 0xab, 0x53, 0xc0, 0xb4, 0x23, 0xfa, 0x96, 0x94, 0xc5, 0x3e, 0x66, 0x2e, 0x41, 0x3b, 0x8b, 0xb5, 0x9d, 0x1b, 0x5d, 0x96, 0x48, 0xa4, 0xcb, 0x68, 0xbd, 0xa9, 0xca, 0x69, 0x3e, 0x8c, 0xe9, 0x57, 0x14, 0x83, 0xae, 0xb7, 0xea, 0x5e, 0x32, 0x76, 0xaa, 0xd4, 0xbb, 0x49, 0xff, 0x1e, 0x20, 0x6a, 0xcc, 0x17, 0x13, 0xf2, 0x5c, 0x97, 0x31, 0x5e, 0xd3, 0x8f, 0x65, 0xc7, 0xb8, 0x34, 0x0f, 0x0e, 0x5d, 0x53, 0x5e, 0x5f, 0xe3, 0x64, 0x43, 0xdc, 0xf8, 0x72, 0xc8, 0xb5, 0x1e, 0x11, 0x7d, 0xdd, 0xb9, 0xac, 0x42, 0x97, 0x51, 0xbc, 0x2f, 0x62, 0x6f, 0x1a, 0xfd, 0x68, 0xf7, 0x99, 0x38, 0x46, 0x37, 0x3c, 0x1b, 0xdc, 0x45, 0xef, 0xd4, 0xde, 0x1a, 0x82, 0xd9, 0xb3, 0xdf, 0x3b, 0xb3, 0x78, 0x97, 0x5c, 0x21, 0xb4, 0xb7, 0x01, 0x2e, 0xdd, 0xff, 0xdb, 0x28, 0x44, 0xbe, 0x8f, 0x6c, 0x8a, 0x16, 0x1b, 0x57, 0x0e, 0xa8, 0xac, 0x51, 0xb5, 0xb7, 0x21, 0xec, 0xf0, 0x24, 0x25, 0x1a, 0x8b, 0x0a, 0xe5, 0x53, 0x19, 0xf1, 0x75, 0x57, 0xc9, 0xe3, 0xa6, 0x2e, 0xe6, 0x8a, 0xf9, 0x7d, 0xec, 0xa6, 0x32, 0x58, 0xfd, 0x77, 0x41, 0x24, 0x0a, 0xc1, 0x1b, 0xb6, 0x42, 0xab, 0x6f, 0xc2, 0x3f, 0x4f, 0x68, 0x64, 0xad, 0x52, 0x1e, 0x4e, 0x43, 0x77, 0x79, 0xe9, 0x41, 0xc3, 0x42, 0xfa, 0x03, 0xbb, 0xdf, 0xc0, 0xa8, 0x51, 0x07, 0x5f, 0xc1, 0xa6, 0xb7, 0x97, 0xbb, 0xa3, 0x06, 0xdc, 0x03, 0x72, 0x08, 0x89, 0xb1, 0x16, 0x37, 0xef, 0xed, 0x00, 0xf1, 0xc7, 0x2e, 0x9e, 0xfe, 0x6c, 0xb3, 0xa8, 0x65, 0x76, 0x1b, 0xa3, 0x37, 0x66, 0xf0, 0xf6, 0xd3, 0xd5, 0x99, 0x6e, 0xc9, 0x53, 0xf0, 0x74, 0xc6, 0x0e, 0xdc, 0x09, 0xb7, 0x93, 0x03, 0x11, 0x4b, 0x88, 0xe8, 0x4f, 0xe0, 0xc0, 0x5d, 0xd6, 0x72, 0x4b, 0x11, 0x70, 0x4a, 0x62, 0x4e, 0xc8, 0x98, 0x44, 0x0f, 0x08, 0x64, 0xc1, 0xcf, 0x56, 0x39, 0xf2, 0x9d, 0xb6, 0x01, 0x93, 0x4b, 0x0a, 0x59, 0x7a, 0xcc, 0x1b, 0x22, 0x6a, 0x04, 0xc6, 0xb8, 0xf5, 0x90, 0xae, 0x36, 0xa9, 0x9c, 0x4a, 0x91, 0xcf, 0x10, 0x1b, 0x82, 0xd2, 0xfc, 0x40, 0x0b, 0xb8, 0xc4, 0xd8, 0x42, 0x9f, 0x79, 0xf2, 0x85, 0xa9, 0xb8, 0xd5, 0x56, 0xdd, 0x2d, 0x7d, 0xf5, 0xf7, 0x30, 0x5d, 0x28, 0x2e, 0x1f, 0x15, 0x79, 0x76, 0xeb, 0x0a, 0xf1, 0x19, 0xed, 0x6b, 0x15, 0x61, 0xe2, 0x4a, 0xad, 0x04, 0xce, 0x96, 0x86, 0x2f, 0xc9, 0xaf, 0x8f, 0x79, 0x80, 0xd8, 0xa1, 0x32, 0xca, 0x41, 0x1f, 0xc5, 0x1f, 0x44, 0x82, 0xa8, 0x13, 0x52, 0x67, 0xb9, 0xf8, 0xe4, 0x23, 0x54, 0x4f, 0x20, 0xee, 0xef, 0x37, 0xa4, 0x88, 0x23, 0x56, 0xf0, 0xe5, 0x79, 0x43, 0x24, 0x04, 0xb5, 0xcc, 0x61, 0xc2, 0xb7, 0x4b, 0xbd, 0x8e, 0xcf, 0x81, 0x6b, 0x00, 0x1d, 0x7d, 0x76, 0x50, 0xe4, 0xb4, 0x32, 0x0c, 0x14, 0xb6, 0x2f, 0xfe, 0x00, 0x48, 0xdd, 0x4e, 0x6c, 0xec, 0xed, 0x05, 0x28, 0x2e, 0x8d, 0xd0, 0x7c, 0xc8, 0xaf, 0xb6, 0xc7, 0xf9, 0x6d, 0x63, 0x6a, 0x3c, 0x53, 0x08, 0x84, 0x4e, 0x65, 0xec, 0xbb, 0x5a, 0xdc, 0x37, 0x3b, 0x52, 0x72, 0xb7, 0xf7, 0xca, 0x42, 0xa1, 0x03, 0x28, 0x55, 0xb5, 0x75, 0x8e, 0x14, 0x54, 0x0b, 0xa6, 0x27, 0xf3, 0x87, 0x4b, 0xdf, 0xf0, 0x56, 0xa5, 0x3f, 0x88, 0xe1, 0x2f, 0xf4, 0x92, 0x0c, 0x10, 0x18, 0x36, 0x71, 0x72, 0x8f, 0x28, 0xbe, 0xd0, 0x75, 0xd1, 0x75, 0x1e, 0x6e, 0x01, 0xd1, 0x63, 0x50, 0x92, 0x0b, 0xb3, 0xba, 0x09, 0xef, 0x14, 0xb8, 0xb4, 0xac, 0xe6, 0x96, 0xf7, 0x2a, 0x28, 0x99, 0x6c, 0x72, 0x0f, 0x2c, 0x0d, 0x44, 0x4b, 0x23, 0x0d, 0x96, 0x9b, 0x2a, 0x09, 0x75, 0xcc, 0x19, 0x4f, 0xf5, 0xea, 0xad, 0x89, 0xc0, 0xee, 0x54, 0xdf, 0x56, 0x18, 0xf8, 0x2c, 0xef, 0xc4, 0x15, 0x70, 0xf7, 0x9c, 0x86, 0x7e, 0x4d, 0x6e, 0xc0, 0x0f, 0xbf, 0x82, 0x6f, 0xbe, 0x98, 0x11, 0x7a, 0x0c, 0x06, 0x9d, 0xd3, 0xe2, 0xcd, 0xb6, 0xef, 0xa1, 0x9e, 0x29, 0x43, 0xc4, 0xa4, 0x12, 0x36, 0xfa, 0x78, 0xdd, 0x24, 0x52, 0xb6, 0x04, 0x74, 0x06, 0x00, 0x85, 0xb3, 0x91, 0x80, 0xa1, 0x1e, 0x36, 0x87, 0x7d, 0xe4, 0x78, 0x3d, 0x7b, 0x8e, 0xc1, 0x19, 0xce, 0xb5, 0x02, 0x53, 0x6a, 0xa7, 0xc3, 0x37, 0x8a, 0x64, 0xa4, 0x0d, 0x82, 0xf5, 0x3e, 0x1c, 0xba, 0xe6, 0x97, 0x01, 0x0a, 0xf1, 0x49, 0x12, 0xef, 0x91, 0x5c, 0x5d, 0x05, 0xbb, 0xc4, 0xac, 0x2b, 0x61, 0x8c, 0xe1, 0xe9, 0x5c, 0xb8, 0x85, 0x85, 0x4f, 0x6f, 0x8c, 0x7c, 0x77, 0x12, 0x62, 0xb2, 0x45, 0x95 },
        .hmac_key_idx = DS_KEY_BLOCK_2,
        // results of message array encrypted with these keys
        .expected_results = {
        // Message 0
      { 0xa1aa1dbc, 0x6bfeca2e, 0x52579368, 0x9917aee2, 0xb0e80764, 0x37f9a8b7, 0x481eb269, 0x464b4d3d, 0xcf71f4db, 0x7351a7ca, 0xeb1ec130, 0xb83fac88, 0x3138bc61, 0x4a512440, 0x72740e8a, 0xe7323f77, 0x217cef40, 0x935a2a3e, 0x0bab13d8, 0xd0240460, 0x75049abd, 0xf3b962c4, 0x11bc2a99, 0xc6bb6684, 0xde379ed5, 0x2ef02cbe, 0x12fd546d, 0x9604b4f3, 0x8d689bb6, 0x7e74892e, 0x6f8ab328, 0xb9f08d16, 0x2e8fb46b, 0xcbc54f3c, 0x3b6907c6, 0x8ab3f3cc, 0xdcbc09a0, 0x3454447e, 0x1370cff9, 0xed567e09, 0x090f14e8, 0x4e12eed6, 0x36245f6f, 0x7bd05722, 0x1b2c8e2d, 0xb4ca04af, 0xb4e7820a, 0xcc2a73f7, 0x9c0df46e, 0x6c9074ba, 0xd4146d11, 0x24b31195, 0x8ce24eb8, 0x28f7726f, 0x07514d9b, 0x64db81e1, 0x65534881, 0x0464d3f7, 0xd747ae2c, 0xbe054cd8, 0xa80bfc3d, 0xba019963, 0x0ec9b06e, 0x4dad02e4, 0x0836f67c, 0x2feb04f5, 0x4b94a15f, 0x1dfcd6f4, 0xad469ebc, 0x92c737e0, 0x2cd35b60, 0xa4c0125f, 0x3332462f, 0xf91f1d37, 0x636d70fa, 0xd230cc56, 0x18685f86, 0xee78afad, 0x59959835, 0x03fa4338, 0x73ddf35b, 0x48d63529, 0x662f3813, 0xe6da64fd, 0x1f96d323, 0xf42067b6, 0x9b82ea14, 0x493feb0a, 0x465b8ec6, 0x9a044790, 0x3fa84e21, 0x6d1ec28e, 0xe5040cec, 0xf2ef9141, 0x4a22c045, 0x83b612f4 },        // Message 1
      { 0xd25ae7cd, 0x54e5b1b6, 0xf68e64db, 0xb2df6bce, 0x828a606c, 0xe86db627, 0xedce94ba, 0x48c16185, 0x8588ab0a, 0xa6bbdcbb, 0x265c52e7, 0xce115342, 0x1c3755ac, 0xcb015c8e, 0xdba14eec, 0xe44bd94a, 0xaa76e009, 0x7a617e88, 0x474b6831, 0xef4355e7, 0x42349fba, 0x2a219307, 0x6a8f73e0, 0x2b317e12, 0xb3e9ea66, 0x9fb18f45, 0xcab09e84, 0x3e0d9a5c, 0x45ed64f6, 0x21a00d3c, 0x01093476, 0xf6a930b2, 0x9a365af0, 0x831c489d, 0xe60a2820, 0xad1a3182, 0x1f6d33a5, 0x1c032e8e, 0x9475e04e, 0xdee6ec81, 0x1221c448, 0xc80ca10b, 0xc9e06b14, 0xf254d191, 0x03dbafb4, 0xda4203d4, 0x101ca395, 0xa20183ac, 0xc8abeadc, 0xd3a828b0, 0xef8aa316, 0xb4a12c62, 0x8eea38f8, 0x4941cfdd, 0x1e4a9784, 0x753a5faa, 0x66c9aa73, 0xc2b8e3a4, 0x4c45c4bd, 0x86f654bf, 0x63e7f803, 0xea1df1a1, 0x019c3e09, 0x56d2abb5, 0x76d59820, 0x0a0c1c61, 0x5de18f7a, 0x782d60e5, 0x045c7e91, 0x29a43ceb, 0xb615c899, 0x584b92ce, 0x851579c3, 0x21e60283, 0x387e27fe, 0x42ca1101, 0x61dee134, 0xbe160a71, 0x1a382b27, 0x9ec820cd, 0x55f76d51, 0x016eda6f, 0xe1a15112, 0xaaa55421, 0x35b229f7, 0x1662dd27, 0x6f5ab29a, 0x38149deb, 0xcc6576a4, 0xe24570f1, 0x7c1dff79, 0x2af8e8ee, 0xa41d31bf, 0x7c8d03e4, 0xc5ea8098, 0xb2177890 },        // Message 2
      { 0x23d80d6f, 0x332e59dd, 0x709354e4, 0x53ebfded, 0x47145d88, 0x48ed2093, 0x8ca19e05, 0xab0c66f2, 0x34a7783f, 0x6494eeba, 0x6b155cb9, 0x904a7a36, 0xdf18f8a8, 0x037809b6, 0x024b12df, 0xdacaa4a1, 0x01ef567c, 0x641f4be5, 0xd65daaba, 0x7dd1aaab, 0xa9bfcbdb, 0xe2de9585, 0x08b6c38e, 0xabba57f2, 0xc378cfe3, 0x88f14d3c, 0xb55afc8a, 0x48d89429, 0x324dcc61, 0xd1d29081, 0x54531f8d, 0x914ba825, 0xf106c060, 0x800783d6, 0x66beb72e, 0x6bfdd2cf, 0xd6b8bc19, 0x37ace02d, 0xf7c1009b, 0x04b9df44, 0xa51416d6, 0xe5a046ef, 0x54db4532, 0xcef4e543, 0x79abc75f, 0x0cc2bf0e, 0x3632ff8a, 0x3ff421f2, 0x545cd824, 0x2bb7108d, 0x20ab0941, 0xe6a5a3c4, 0x936b1188, 0x5a0adfe8, 0xa80ec542, 0xbbddd9b0, 0x619cc3b4, 0xa8fb5288, 0x65f6baeb, 0x114cd217, 0x292974c0, 0x33234e17, 0x8fb30d0d, 0x37f6209e, 0x2125bb9b, 0xe092d255, 0xa5932237, 0xf5c63429, 0x8e24a0cb, 0xfec2118a, 0x8d1484af, 0x6b57f383, 0xee5fe75e, 0xd56bb0fc, 0x768fd62c, 0x2e362b9e, 0xaf61c7df, 0x2b3dab4e, 0x20f1f647, 0xc31882b9, 0xce6d3204, 0x4aa764ec, 0x5f102b8d, 0x321bf8d9, 0xfaef3696, 0xa9d43fa9, 0x117b5edb, 0xa20927eb, 0x09622d86, 0x5fe27475, 0xc01f4ea5, 0x4b6a5fc6, 0x65dc765c, 0x05233680, 0x2152c86c, 0x1eb53b5c },        // Message 3
      { 0xfd79cd47, 0xb0c2f2a5, 0x253a06c5, 0x869705d0, 0x604740cd, 0xdbd15125, 0xeea6be32, 0x7a13359d, 0x738ef862, 0x262c425a, 0x0874ecfd, 0x8d8f3054, 0x622fd315, 0x4449e6ba, 0x7e0f7e08, 0x4c0de6f5, 0x74e097df, 0xb97d0146, 0xc52a7f22, 0x75bb02bb, 0x4135c7b7, 0x08e029c8, 0x0d403e96, 0xdfeaf402, 0x2f00807c, 0x2204a84a, 0x7bcf47c2, 0xe7b7ba13, 0x48ab1e23, 0x99f12f76, 0x204dbae9, 0xb939bb2e, 0x0b820116, 0x97f43da2, 0x1dd7eb77, 0x5e5c968f, 0x446baa5e, 0xbba35824, 0x9ac8f4ef, 0x0f206963, 0x940b1a97, 0x2ef98261, 0x8c04c982, 0x27d81ecd, 0x595392b9, 0x19612b04, 0xd0ce5730, 0xadb036f5, 0x9b7faba1, 0x5bf385ad, 0xbd0d8845, 0x4baf51d1, 0xa32a329b, 0xcb06beec, 0xfd63360d, 0x078e4d91, 0x13957485, 0xef0284c7, 0x1d5b76c3, 0xed596c0e, 0xfafa7dd2, 0x9fda0275, 0x6c7ac160, 0x638863b1, 0xcf72cef5, 0xac45dba2, 0x605224e9, 0x14e713a8, 0xfe6e0c57, 0x487b1788, 0xaf26e692, 0xe419b371, 0xf709e56f, 0xf0b17219, 0x5e3cf8dd, 0xdb160c73, 0xbe9b73ea, 0x4b28962f, 0x6f1c0a76, 0xb99387d2, 0x48a73c3a, 0xe3b8c5c5, 0x9a9ba733, 0x59493059, 0x2767a3ae, 0x3afa8f8e, 0x6044c6f1, 0xf595a693, 0x41f9df83, 0xaa3a2670, 0x967d1de5, 0x4e9d9f32, 0x03f61fe7, 0x11229d4f, 0xf6e73aa6, 0x6afae6c1 },        // Message 4
      { 0x2a558672, 0x1adec7de, 0x823156bd, 0x1adae6af, 0xe619ffae, 0x74873ef4, 0x682795bd, 0x9cff58a8, 0xb777343f, 0xb451d609, 0x64dc8e63, 0x739685df, 0x476948a5, 0xaf7d41b3, 0x8e468f9b, 0x494a1638, 0xa9eea76e, 0xa9e387b1, 0x8c031cec, 0x00ac3674, 0xb68e95ce, 0xd62da688, 0x73ad08f3, 0xeb6f7ec1, 0x4cb4c0dd, 0x1f77a781, 0x86437938, 0xfc80e94f, 0x4a433afb, 0xbe26dba7, 0x81272fb8, 0x85455b6e, 0x6a0b65b4, 0x719169b4, 0xb347abe1, 0x144a985a, 0x2c2fa0f6, 0x94e0806c, 0xe8f0e94e, 0x6946ca3d, 0x3a84aad2, 0xf1638aac, 0x7a85fb0b, 0xc71056f1, 0xb83fdf09, 0xf1f44e0f, 0x1d10713e, 0x8206b586, 0x03ee00f0, 0xb518ae35, 0x77083069, 0x7dc1d5b8, 0xd01978bf, 0x5aca4eb0, 0x599f30a6, 0xa31578cc, 0xd7d4258c, 0xd67b4b4c, 0xdf217acc, 0x3ed8f3c3, 0xa5cb9b86, 0x30d568d4, 0x0a48834d, 0xbcaac227, 0x6ea6598e, 0x6be51ad0, 0x902b50ac, 0xa1e68cf7, 0xe05f3b22, 0xcfa5bd26, 0x5ee78d43, 0x7ea72d53, 0x59d83fc1, 0xe6c5430f, 0xd795b862, 0xef9ebc03, 0xc0795a04, 0xae1162a6, 0x8490e7e2, 0xe772eb99, 0xeb1a911b, 0xe5bf3f80, 0xeaeeef1c, 0xc2bfe0b0, 0x589bd638, 0x687cd03d, 0xb4fa3386, 0x02df2cbe, 0xec97aa45, 0x14014328, 0x1fddbf0e, 0x405611b1, 0x99d67838, 0x3e075db2, 0x231c4336, 0x7d50bc5d },        // Message 5
      { 0x28c750c9, 0x01966fd0, 0x7f3d06cb, 0xb8abad79, 0x3e432f4c, 0xc975e29c, 0xfc7ca255, 0x7ada66f9, 0x8192c217, 0x413ce2bb, 0x8969851e, 0xd7693097, 0x76f5a31c, 0x89874462, 0x726437fe, 0xb194c5a9, 0x99a23cf6, 0x6b56f271, 0xd23bf902, 0x0c552db9, 0xc77855df, 0xda586622, 0x45c147c0, 0xd77a985d, 0x92b82b9e, 0xc016518f, 0xd64e6351, 0x60364340, 0x7b577f18, 0x55f842f6, 0xb8bb2ef2, 0xb97aebab, 0xb66c7dd8, 0x438a5780, 0x695d2cd4, 0x0186bff7, 0x74ca8980, 0x0bacd9ec, 0x61f10b13, 0xad107025, 0xc68e5dd8, 0xc4d3052f, 0x39ff203a, 0xe4e7249b, 0xe4d6061a, 0x79ccaf7b, 0xe5c3a1b9, 0xa5c98ea7, 0x2c00215e, 0x3b080ece, 0x61188199, 0xd15cb516, 0xe1606226, 0xe71cb2a0, 0x87ca1bc0, 0x6e711b80, 0x0ad5457a, 0xa3810eb5, 0x6ec99efe, 0xd452a9d6, 0x59a3e975, 0x7bb9e908, 0x8ad801bc, 0xd040a337, 0xef7831b2, 0xab76502f, 0x8edef374, 0x225cb7cb, 0xcbb4d767, 0xc2eac91e, 0xce739d00, 0x06d8aa3e, 0x88a94e37, 0x165b2472, 0x86a607d2, 0xb8eef8d7, 0x1a7619f5, 0xf3004692, 0x732e2e14, 0x8049c34a, 0x1aaf4a3b, 0x6aca5518, 0x2d3ca896, 0x55a791fd, 0x108d5ea8, 0xea722dcf, 0xdc674b3a, 0xb0480771, 0xba5f9cdc, 0xa7d15653, 0xd0719366, 0x8384ceef, 0x08f63a8e, 0xac4852e3, 0xe499b669, 0x71f900d4 },        // Message 6
      { 0x191fa132, 0xec529575, 0xd350bc5a, 0x1dd01a08, 0x464fbc64, 0x3030ef77, 0xd3ae8199, 0x3a93d837, 0x95ba1b2d, 0x87281d33, 0xc71b420f, 0x0f869a92, 0x9395277d, 0x265f9014, 0x7ca1dcc2, 0x3a8aa517, 0x8ca06b79, 0x935d8c7f, 0x9846142c, 0x7fd38710, 0xa213679f, 0xccb22b7d, 0xfced5250, 0xe2ee7998, 0x1b74127d, 0x98b6f139, 0xa7b8d576, 0x795743f3, 0x003ca859, 0x98e4ff16, 0x865e5d7b, 0xc58ff71b, 0x51dd5f7c, 0x77d45d59, 0xdef44b51, 0x35ee735f, 0xbb2fd64e, 0x14ecc9f9, 0xee26206f, 0x16c40a0b, 0xe26da793, 0xe0ed59c4, 0x28cca504, 0xf8ad4257, 0xf7176ec4, 0xc305229c, 0xab835cf0, 0xb96f092f, 0x19ffd5b7, 0xb8316374, 0xb4cb7fe8, 0x5a433227, 0xb719f9a5, 0x03457109, 0x72b4e00a, 0x99cf6b58, 0xab3851f9, 0xbf07ee85, 0x9ee9abe6, 0xe610c900, 0x25b37bde, 0x8eee2d7d, 0xad082045, 0xa678a249, 0x1c74f6b7, 0x642b8c51, 0xee633a06, 0xe46bafc2, 0x5b257036, 0x08018ca6, 0xab2af844, 0x29febacc, 0x3eeae805, 0xc03721c0, 0x1650b290, 0xa8e2073c, 0x55f64445, 0x1fb6cf2b, 0x84b242de, 0x18a68269, 0x35c4e174, 0xa128d976, 0x19ef3319, 0xa23db9e1, 0x8d676a01, 0x86304fbb, 0x1066317e, 0x9e7029d3, 0x032cd540, 0x956f4738, 0x55241be1, 0x9e8331ba, 0xf929c836, 0x6d6209fe, 0x2a5b1d9e, 0x16c37c16 },        // Message 7
      { 0xdb1c9a9c, 0x5deff40c, 0xb3373d4c, 0x9d628ea4, 0xd12262b6, 0xcbe805eb, 0xa0477188, 0x96ec467a, 0xea0884a7, 0x87e2cd54, 0x25617ab9, 0x7e9d0276, 0x7f796d2a, 0xc8d0eb0a, 0xf1e23aca, 0x471a4455, 0x50d35ea4, 0x633d0a89, 0xa6ebd368, 0xde3ad74d, 0x2774b9e0, 0xefb2422a, 0x23aa3274, 0x2bb5823a, 0xe654ffb5, 0xfaf11941, 0x0735094a, 0x623fe270, 0xd0605432, 0xb5f362e7, 0xd7e65ae9, 0x34332131, 0x4ed4ce90, 0xfd0d83aa, 0x125f0267, 0x566ac135, 0xe141ba65, 0x5213145f, 0xa46299ad, 0x75c5de69, 0x02d2d762, 0x2486b2af, 0x9aef897c, 0x4de5a642, 0xb56e1a99, 0x6a349e07, 0x219ee3b1, 0x7ad30cf9, 0x231e1d02, 0xc4ffb405, 0xecb691d7, 0xdba127a1, 0x453fed84, 0xb1a70dcc, 0xb0a31df7, 0xec814977, 0x2e1fbc59, 0x9811d77d, 0x1f6e8c8b, 0xe99e3675, 0x8057b8d7, 0xfe26053b, 0xca417fd1, 0x564ad062, 0x37d1df83, 0x77ad67bd, 0x9b60e003, 0x5b03ca19, 0x1d1d3c04, 0xa093d050, 0x35d1d54f, 0x0c212c6e, 0x8882c6b8, 0xc898c4e2, 0xd9c17559, 0xf502bac4, 0x279e8ae5, 0xa4f1aab6, 0x8301bab1, 0x743c84c4, 0xd5822a78, 0xb0af1dab, 0x2734861a, 0x68ba0d30, 0x402d8daf, 0xa63e25b6, 0xfe7060fb, 0xc8175ac8, 0xdff77344, 0xf3e5abc4, 0xfeb0c548, 0x9c409afd, 0xf451d5f5, 0x27861421, 0xc00dfe2a, 0x07e603fd },        // Message 8
      { 0x79341110, 0x3eaa9a3e, 0x3c085c32, 0xb1a1add1, 0x11bbcfe1, 0xf70a0bc3, 0x988de2e3, 0x8cfecb83, 0x82a40d9c, 0xa1593ce9, 0x8ed03d21, 0xf3b8c633, 0x43d85b47, 0x878a92f3, 0x4d74b0b9, 0x976e8c95, 0x276d1d83, 0x7e0204d0, 0xc6718b91, 0xcb38068d, 0x1f8da0cc, 0x50c1b479, 0xff7f188b, 0xff1ab5d9, 0x46d9e9de, 0xaad3e1c1, 0x276f355f, 0x59f71ebe, 0x59bf872f, 0x00400dde, 0xe1e9eefa, 0x977098de, 0x309bf8bd, 0xeadcac01, 0x1ea8a66d, 0x62f2d4c6, 0x891afc3a, 0x4f3b0951, 0xdf2dd664, 0xe86f4b73, 0x1f1e1661, 0xe387443b, 0x1213e4cd, 0x45a5c758, 0x4b41af3d, 0x3b0b41dc, 0xab8f30c0, 0x17c6b450, 0x17e441f3, 0x455ba5e4, 0x0f21b9ee, 0x1aedcbcc, 0x2809d947, 0x39c30825, 0x7dc22fa6, 0x819098c1, 0x4ef74d7d, 0xa06d2712, 0xc80faaf2, 0xbd113791, 0x310fc8ec, 0x80d28dc1, 0x80fc8028, 0xa5f7868b, 0xd7f84266, 0xad7f7e1a, 0xb056eb1c, 0xe2405b9a, 0x6db21321, 0xe5d9cb1c, 0xfd0e9679, 0xb57560a8, 0x5b8a721b, 0xeb084e9f, 0x033fa371, 0xddd04527, 0x4c145eea, 0xa470244a, 0xc6eca4bb, 0x19aac9d1, 0x499c8362, 0xa19aee86, 0xac1bcd4c, 0xe23a4bad, 0xc1ee2115, 0x59e7f143, 0xed3917d8, 0xfc43b95f, 0xde31c771, 0x67f6ee54, 0xdc44c991, 0x4f1fe1de, 0x9cf4257e, 0x4ac95e18, 0x0676bc73, 0x6dc235f7 },        // Message 9
      { 0xd1eb33f6, 0x927aa895, 0xc13d7b18, 0x20c54dd5, 0x21b7a967, 0x51d17dd6, 0x934d625d, 0x7437e19d, 0x5e1a4d84, 0x92722dcf, 0x3875608b, 0x536bb522, 0x8351e5e4, 0x74e732d1, 0x9edfec21, 0x2777d97c, 0x70df7f29, 0x6963e91c, 0x85391f32, 0x50ae8d40, 0xdf4af0a4, 0x1320f154, 0x56baaf09, 0xc90df7b2, 0x6fb6a16b, 0xf07c2cdd, 0x889aed44, 0x2540a3a9, 0xc81eb672, 0xc9b36b81, 0xf8f1f29d, 0x43faf5f2, 0x5f29013b, 0xfbb7e46c, 0x584c228b, 0x3b50bc1b, 0x9897c7fe, 0x0139e884, 0x243cd901, 0x76f81c72, 0x38f64dcb, 0x26db109a, 0xd7391f7a, 0x7bcd517f, 0xc72a5b4b, 0xb07f6e62, 0x40c01c5b, 0x7608fbeb, 0x0334fcb7, 0xe8ac74dc, 0x0478c091, 0x65a667f8, 0xf23a6d82, 0x488fa619, 0x471dc831, 0x54f3b664, 0x9f11e50d, 0x7ad8dd31, 0x3a7e9ba0, 0xc468fa21, 0x8dda1f65, 0x88b9d1fa, 0xeb9b57da, 0xcec0d814, 0x3456663b, 0x607418cf, 0x130fba8f, 0x64397053, 0x7b56732e, 0xa84f74e9, 0x353950d6, 0x8eb42599, 0xfe2c8159, 0xcc2a2650, 0x498862ae, 0xf7269bbc, 0xc62a4d59, 0xf3c1a9cd, 0xc79b25d3, 0x1fa0f53a, 0xc1187788, 0x874e3ad2, 0x38ddff67, 0x86a40e76, 0x2964e3ad, 0x835a1fa2, 0x3b7cb7de, 0x8f9737e4, 0xbc318a9c, 0x1f9fdbe9, 0x559a63c5, 0x4acbee44, 0x51d2a044, 0xdeed124e, 0x09fe623b, 0x78433ad2 },     },
     },
    { /* Case 4 */
        .iv = { 0xc6, 0x09, 0xa6, 0x69, 0x05, 0xf2, 0x42, 0xa4, 0xd4, 0x92, 0x33, 0xc2, 0xe3, 0x09, 0x09, 0x56 },
        .p_data = {
            .Y = { 0xfb66eac1, 0x26184a9b, 0xaf48c7ba, 0xd1cb4e8e, 0x92838968, 0x7017a7c8, 0xa580dad8, 0x6e2d45a9, 0xda9642f7, 0x07a9da71, 0x9c0f85c1, 0x9b7985aa, 0xc60c6e44, 0x04e48499, 0x19ca1ad7, 0x97087ae6, 0x3d2b123b, 0xae6b044e, 0xa736a849, 0xdc49169a, 0x815a006e, 0x5d811f54, 0x4666b09e, 0x45a0a59b, 0xd2ee9778, 0xa144d6be, 0x49d7a64f, 0x6f5e5b95, 0x375a6a91, 0x007e4654, 0x4b22b41b, 0xddac30c8, 0x162c6912, 0x5e8061a9, 0x7cef1f62, 0x24198c63, 0x159ba18a, 0x270c6472, 0xda93f59a, 0x27ce099f, 0x5ea1326b, 0xe13ab171, 0x84092c50, 0x266edc1c, 0x58b21eb7, 0x1808252c, 0xf31cb379, 0x0c74f61a, 0x775355d6, 0xbeb00c48, 0xfc9e28a5, 0xf5721f05, 0xd14d984e, 0x448366f1, 0xee209ca9, 0xa79b63db, 0xb1b0ca9b, 0x74f8cabf, 0x72e09160, 0xf5da5ee1, 0x8a946fe7, 0xada4dc9c, 0xa9464d7d, 0x09032a03 },
            .M = { 0x79994dc7, 0x7a440f1c, 0x26c2c623, 0x423c6db4, 0x78ddfc2f, 0x6c2041eb, 0xaf471114, 0x10558ebf, 0xce69dfef, 0xcb646a3d, 0x14742478, 0x8129b9d9, 0xb3f50996, 0x437db51c, 0x32f3e950, 0x3251d3b0, 0xa636db1a, 0xd553d925, 0xa386b4ab, 0x08f755c0, 0xdb9e1e6e, 0x08317464, 0x86169bf2, 0x3528d1dd, 0x8912d982, 0x806bd83a, 0xa85c7174, 0xfc951c80, 0xafc92f76, 0x104a2835, 0x03b94d95, 0xc716c867, 0x4e326354, 0x3a3444fb, 0x7f107c90, 0x1c5ee2a0, 0xf2d6b42a, 0x98957be1, 0x5a744eff, 0x2626785d, 0x7d18ffe0, 0xe9501913, 0x97ea4723, 0xb19d6abb, 0xbe08b725, 0xa0f170db, 0xe9519a09, 0xdbd4178d, 0xb6954eca, 0x7168e3cb, 0x9901c1c4, 0x506e4a03, 0x983243b4, 0xa24269d0, 0x78dded1c, 0x0692c362, 0xa482d110, 0x629145a9, 0x330cdb60, 0xb9f22911, 0x00ad92ae, 0x54ebc7ec, 0x6e8d9341, 0xae3b2197 },
            .Rb = { 0xd268e1d4, 0x63c4157b, 0x5cdca862, 0x75f9f81c, 0x2f7f0531, 0xc9d99227, 0x9b344bea, 0x7ffbe131, 0x3e0f9e90, 0x4d88cef6, 0xc3461df7, 0x6961aff4, 0x8eb212eb, 0x8a8845e2, 0x0a47e1c0, 0xae5cfc5a, 0x48a1f7d8, 0xc215bd10, 0x1c98a17f, 0xac34eb25, 0x0ef594f2, 0x4da5ffb7, 0x86a62fab, 0xca408fe7, 0xf050fae6, 0xcd5d780a, 0xef93aefb, 0xd675d922, 0xaa820ad0, 0x98da8044, 0x73f09735, 0x78ffc927, 0x17126dfb, 0x2f576c2f, 0xd19db8f9, 0x4d7f665f, 0xe63c091c, 0x685a819f, 0x19130a45, 0x262132c9, 0x739d1cee, 0x2675ce25, 0x14be86b2, 0xaef69cb5, 0x0110430e, 0x0c98a188, 0xe49c1a93, 0xeddfdb51, 0xf060d987, 0x3facd5ca, 0x3fd50040, 0x668ae316, 0x04eaf527, 0xa2c1746f, 0xa3e11712, 0xf8d930c0, 0x25fb6477, 0x0a3657fa, 0x46de9af5, 0xc7407325, 0x5fd7ea14, 0x7530097a, 0x84ffef3b, 0x441cba6e },
            .M_prime = 0xbc119c09,
            .length = 63, // 2048 bit
        },
        .expected_c = { 0xd2, 0xec, 0x2c, 0x28, 0x4b, 0xea, 0xb8, 0x4b, 0xd8, 0xb4, 0x42, 0xff, 0xea, 0x04, 0x44, 0x22, 0x69, 0xd0, 0x9a, 0xfb, 0x86, 0xfd, 0x10, 0x00, 0x7a, 0x33, 0x9c, 0x26, 0x3b, 0x6b, 0x71, 0x8d, 0x53, 0xc8, 0xc1, 0xa0, 0x95, 0xda, 0x67, 0x3b, 0x3f, 0xa7, 0x45, 0xad, 0x33, 0xfd, 0x81, 0x39, 0x9d, 0x16, 0x1b, 0x35, 0xed, 0x24, 0xc3, 0x99, 0xe9, 0x70, 0x0d, 0xd4, 0x91, 0xda, 0x53, 0xea, 0x0b, 0xfe, 0x86, 0xb1, 0x49, 0xa7, 0x42, 0x0c, 0x22, 0xb7, 0x47, 0xff, 0xb7, 0x10, 0x7f, 0x30, 0x4f, 0x93, 0x52, 0xce, 0x70, 0x5e, 0x14, 0x4f, 0xb2, 0x5e, 0xb0, 0x7b, 0x3b, 0x49, 0x57, 0x62, 0x4e, 0x99, 0xf8, 0x44, 0xbc, 0x93, 0xf6, 0xed, 0xfc, 0x85, 0xc5, 0x03, 0x8e, 0x6f, 0x3e, 0xf1, 0x61, 0xbb, 0x7d, 0x0c, 0x85, 0xfc, 0xbe, 0x9c, 0xef, 0x4e, 0x22, 0x45, 0x46, 0xbb, 0x99, 0xe1, 0x97, 0x00, 0xd3, 0xa0, 0xe5, 0x3c, 0x60, 0x88, 0x66, 0x26, 0xd6, 0xf7, 0x25, 0xa0, 0xba, 0x7e, 0x83, 0x82, 0xc4, 0xcd, 0xe9, 0x83, 0xbb, 0x35, 0xcf, 0x51, 0x4c, 0xea, 0x8c, 0x3f, 0x42, 0xc4, 0x40, 0x7a, 0x2d, 0x19, 0xd1, 0xe8, 0xff, 0x4d, 0x5f, 0x9a, 0x68, 0x75, 0x50, 0x2e, 0x4e, 0x75, 0xff, 0x00, 0xbb, 0x82, 0x04, 0x57, 0x47, 0x92, 0x7e, 0xe7, 0xc3, 0x0a, 0x42, 0x8a, 0x0a, 0x61, 0x95, 0x89, 0xde, 0xaf, 0x1d, 0xd0, 0x5d, 0x8d, 0x46, 0x14, 0x7e, 0x19, 0xf5, 0x9b, 0xc1, 0x05, 0x4d, 0x86, 0xe1, 0x29, 0x28, 0x27, 0xdb, 0x3c, 0xa6, 0xbb, 0x96, 0x3d, 0xd7, 0x3b, 0xaa, 0xa3, 0x9a, 0x44, 0x39, 0x6f, 0xb7, 0x1c, 0xdd, 0x68, 0x16, 0x68, 0x77, 0x5d, 0x8d, 0x2c, 0xb7, 0x68, 0xd1, 0xdb, 0xa4, 0xab, 0x88, 0x1d, 0xb2, 0x0b, 0xc7, 0x65, 0xcb, 0x0c, 0xe3, 0x66, 0x6b, 0x15, 0xdd, 0x2d, 0x08, 0x57, 0xe5, 0xcf, 0x04, 0xc8, 0xda, 0x26, 0x8e, 0x90, 0x9e, 0x21, 0x1a, 0xe1, 0x37, 0x4b, 0x5d, 0x47, 0x31, 0xbc, 0xb1, 0x67, 0x26, 0xa1, 0x6e, 0x84, 0xde, 0x03, 0xc3, 0x48, 0x64, 0x93, 0x7e, 0x49, 0x7d, 0x8a, 0x3f, 0x07, 0x7c, 0x81, 0x1b, 0xb3, 0xb7, 0x07, 0xe7, 0xc2, 0xa1, 0x59, 0xe7, 0x2b, 0xd7, 0x31, 0x64, 0x17, 0x73, 0xeb, 0x40, 0xf8, 0x86, 0x5f, 0xf7, 0x56, 0xbe, 0x7d, 0x6a, 0xb8, 0x3f, 0x51, 0xdc, 0xc6, 0xc8, 0x82, 0x2b, 0xfb, 0x78, 0xd7, 0x7a, 0xe8, 0x58, 0x04, 0x93, 0xbe, 0xfa, 0x0e, 0xd0, 0xe4, 0xfa, 0xe0, 0x9e, 0x35, 0xcf, 0x5a, 0x1c, 0x17, 0xc0, 0x39, 0xe2, 0xc7, 0xd2, 0x81, 0x37, 0x59, 0xa0, 0x64, 0xeb, 0x11, 0xa9, 0x13, 0xe9, 0x65, 0x71, 0x93, 0x94, 0xe2, 0x27, 0xd0, 0xaf, 0x88, 0x9c, 0x64, 0xcc, 0x38, 0x1b, 0x0b, 0xf3, 0xe4, 0xd9, 0xef, 0xc5, 0x63, 0xfd, 0xc3, 0xc4, 0x7c, 0xd2, 0x2e, 0xb1, 0x15, 0x59, 0x6a, 0x64, 0x8c, 0x50, 0x53, 0x89, 0x41, 0xfd, 0x90, 0x15, 0x54, 0x26, 0x7c, 0x5e, 0xd7, 0x66, 0x6b, 0xf4, 0x15, 0x54, 0x95, 0xed, 0x55, 0x61, 0x94, 0x61, 0x84, 0xa3, 0x1f, 0x0d, 0x7b, 0xbd, 0x4d, 0xc4, 0xfd, 0xcd, 0xce, 0x9d, 0x13, 0x2f, 0xcf, 0xb2, 0x8d, 0xce, 0x06, 0xad, 0x11, 0xf9, 0xa0, 0xb5, 0x9d, 0x72, 0x17, 0x42, 0xa1, 0xee, 0x31, 0xbc, 0xe5, 0x0d, 0xfe, 0x88, 0x22, 0x1b, 0x0f, 0x07, 0x2f, 0xb9, 0xca, 0x0d, 0xa9, 0x9d, 0x8a, 0x96, 0x15, 0xce, 0x7b, 0x83, 0xb7, 0x0c, 0x5e, 0x2e, 0xcb, 0x3a, 0x52, 0xef, 0x2f, 0xfe, 0x98, 0xd0, 0xd2, 0x9e, 0x32, 0x0d, 0x0c, 0x99, 0x0a, 0xde, 0x8e, 0xf4, 0x82, 0x99, 0x40, 0xb0, 0xbe, 0x0c, 0x55, 0x90, 0x36, 0x4a, 0xd5, 0x43, 0xc8, 0x8e, 0x60, 0xc7, 0x94, 0x15, 0xfe, 0x0a, 0xf3, 0x72, 0x07, 0x91, 0x66, 0xb1, 0x7b, 0x5e, 0xd7, 0xca, 0x95, 0x6e, 0x69, 0xdc, 0x65, 0xd2, 0xcc, 0x60, 0x76, 0xc5, 0x9c, 0x1e, 0xb0, 0x6d, 0x9d, 0x84, 0xef, 0x39, 0xcf, 0x80, 0x6d, 0xf8, 0x0c, 0x83, 0xa0, 0x50, 0x55, 0x9f, 0x89, 0xab, 0x5b, 0x80, 0x69, 0x6e, 0x94, 0x3e, 0x4b, 0xf1, 0x5d, 0x0a, 0x72, 0x77, 0x24, 0x96, 0x64, 0x28, 0x49, 0x42, 0xbe, 0x1b, 0x50, 0x09, 0x9c, 0xde, 0x55, 0xa4, 0xab, 0x5a, 0x01, 0x55, 0x91, 0x64, 0x31, 0xb4, 0xe9, 0x6c, 0xb6, 0xa4, 0xa3, 0x39, 0x7c, 0x6d, 0x39, 0x58, 0x0b, 0xdf, 0x38, 0xb3, 0xe1, 0xd7, 0x4f, 0xb3, 0xba, 0x97, 0xb8, 0x18, 0x4b, 0xb8, 0xe3, 0x27, 0x39, 0xe2, 0x45, 0xc7, 0xb2, 0x46, 0x29, 0xa3, 0x67, 0x37, 0xfc, 0x7a, 0x53, 0x28, 0x3a, 0xa8, 0x8b, 0x12, 0x16, 0xf4, 0x38, 0x62, 0xfd, 0x67, 0xc8, 0x4a, 0x71, 0x7e, 0x30, 0xb9, 0x6c, 0x2a, 0xbf, 0xf5, 0x74, 0xbe, 0x68, 0x5a, 0xa0, 0x78, 0xd7, 0xc0, 0xa9, 0x73, 0x93, 0xe2, 0x9d, 0x5a, 0xeb, 0x38, 0x63, 0x82, 0x76, 0x4a, 0x2b, 0x94, 0xf7, 0x42, 0x06, 0x79, 0x9b, 0x23, 0xf0, 0x97, 0xfe, 0xa9, 0x12, 0xc9, 0x53, 0x93, 0xf3, 0xd0, 0x31, 0x2a, 0x15, 0x96, 0x58, 0x16, 0x26, 0x32, 0x56, 0x95, 0x7c, 0x02, 0x99, 0x07, 0x48, 0xde, 0x74, 0xd0, 0x78, 0x35, 0x55, 0x6b, 0x03, 0x8f, 0x67, 0xd3, 0x51, 0x58, 0x26, 0xf1, 0x9c, 0x57, 0x91, 0x15, 0x46, 0x96, 0x20, 0xf0, 0xa9, 0x27, 0x44, 0xe6, 0x39, 0x06, 0x56, 0x62, 0x70, 0x54, 0x55, 0xf7, 0x86, 0xe7, 0xe3, 0xf7, 0x34, 0xeb, 0x91, 0x34, 0x22, 0xcb, 0xb5, 0xb7, 0xd9, 0xdc, 0x83, 0xb8, 0x55, 0x4a, 0x25, 0x2c, 0x73, 0x13, 0x73, 0x40, 0x94, 0xb9, 0x1f, 0x7a, 0x39, 0x8b, 0xe7, 0x4d, 0x3d, 0xfc, 0xd1, 0x61, 0x65, 0x81, 0x5e, 0xe3, 0x4b, 0x47, 0xa0, 0x86, 0xe2, 0x23, 0xb0, 0x7e, 0x1c, 0x60, 0x18, 0xf2, 0xa8, 0x65, 0xfa, 0x2b, 0xcf, 0x6e, 0x59, 0x76, 0x00, 0xbd, 0xdf, 0xcd, 0x3d, 0xa3, 0xcd, 0x2b, 0xd4, 0x36, 0xd4, 0x6f, 0xe3, 0xd8, 0x63, 0x5b, 0xab, 0xa4, 0xdb, 0x55, 0x7f, 0x81, 0x04, 0x96, 0xf9, 0xd3, 0x2c, 0x05, 0xf3, 0xd4, 0x26, 0x1e, 0x37, 0x27, 0x45, 0x6e, 0x7e, 0x46, 0xfb, 0xe5, 0x10, 0xa3, 0xf6, 0xcd, 0xee, 0x9c, 0xfd, 0x73, 0x28, 0xff, 0x89, 0x3f, 0x9f, 0x75, 0xaf, 0xb4, 0xd8, 0xcd, 0xb5, 0xbb, 0x53, 0x07, 0xeb, 0x3d, 0x35, 0xe6, 0xb3, 0x58, 0x56, 0x6b, 0x9e, 0xb0, 0xd3, 0x55, 0x3c, 0x2e, 0x05, 0x26, 0xe3, 0xf9, 0xa5, 0x21, 0x73, 0x76, 0x57, 0x17, 0x4a, 0x2f, 0xce, 0x11, 0x8e, 0x2e, 0xdf, 0xa6, 0x74, 0x77, 0x02, 0x6f, 0x07, 0x5f, 0xf7, 0xba, 0x62, 0xf5, 0x89, 0x28, 0x53, 0xf9, 0x99, 0x94, 0x66, 0xbd, 0x17, 0x83, 0x3d, 0xfa, 0xb5, 0x2f, 0x2d, 0x30, 0x0f, 0x00, 0x70, 0x57, 0x50, 0x91, 0xe3, 0xc7, 0xc1, 0xd7, 0xaa, 0x9e, 0x63, 0xc6, 0xfd, 0x7d, 0xc3, 0x8e, 0x6a, 0x71, 0x30, 0xfc, 0xd5, 0x4f, 0xc1, 0x1c, 0x03, 0x97, 0xe3, 0xcd, 0x0b, 0xe3, 0x86, 0x7f, 0x98, 0x00, 0x9d, 0x71, 0x9a, 0x69, 0xb4, 0x74, 0x4f, 0xd4, 0xc5, 0x39, 0xad, 0xa2, 0xd9, 0x2a, 0x88, 0xa9, 0xdb, 0xde, 0x7b, 0x5b, 0x41, 0xf1, 0xde, 0x7b, 0xad, 0xda, 0xbb, 0xbf, 0x42, 0x88, 0x92, 0x3c, 0x9e, 0x60, 0x6c, 0xea, 0x32, 0xf1, 0xa6, 0x41, 0x74, 0x1c, 0x07, 0xe4, 0x17, 0xe8, 0xdf, 0x38, 0x7d, 0x81, 0x2c, 0x80, 0xfb, 0x20, 0x1b, 0x35, 0x7f, 0x74, 0x0a, 0x22, 0xc8, 0xd9, 0x60, 0x01, 0xd3, 0xee, 0x76, 0x54, 0xb1, 0x0b, 0x2b, 0x4d, 0x04, 0x6d, 0x53, 0x79, 0xf1, 0x7c, 0x04, 0x0c, 0x13, 0xba, 0x1b, 0xee, 0x58, 0xb9, 0xd4, 0xb5, 0xb7, 0xd9, 0x23, 0x8e, 0xc7, 0xac, 0x9f, 0x78, 0xe8, 0xdb, 0x05, 0x7f, 0xf3, 0xda, 0xcd, 0xb2, 0xce, 0x3e, 0xd9, 0xb5, 0x57, 0x99, 0x56, 0xc0, 0xdb, 0x4b, 0x5e, 0x48, 0x38, 0x39, 0xd8, 0x54, 0xf4, 0x01, 0x0f, 0x4d, 0x2e, 0x05, 0xe6, 0x08, 0x28, 0xea, 0xfa, 0x76, 0x07, 0x13, 0xa3, 0xa8, 0x7b, 0x1d, 0xaf, 0x68, 0x93, 0x48, 0x4f, 0x9c, 0xa7, 0xd1, 0xcd, 0x6f, 0x06, 0xee, 0x7d, 0xbd, 0x28, 0xc8, 0xf4, 0xf0, 0x6e, 0x05, 0x37, 0x57, 0x88, 0xed, 0x9c, 0x4f, 0x6a, 0x90, 0x27, 0xa5, 0x55, 0xd8, 0x62, 0xd0, 0xe7, 0xe8, 0x58, 0x8e, 0xf7, 0x28, 0xfe, 0x57, 0x12, 0x00, 0xd7, 0x58, 0x3c, 0xcc, 0xfa, 0x19, 0x39, 0x41, 0x5d, 0x48, 0x47, 0x8a, 0x7f, 0xed, 0xbc, 0xf5, 0xfc, 0x12, 0x77, 0xfb, 0x0d, 0x00, 0xe2, 0xc4, 0xa3, 0x6e, 0x60, 0x98, 0x1c, 0xbc, 0xe3, 0x7c, 0xb7, 0x1b, 0x51, 0x4a, 0x20, 0xee, 0x26, 0x30, 0x9e, 0xb9, 0x8f, 0x47, 0x83, 0x10, 0xb0, 0x31, 0xd1, 0x59, 0xcc, 0x2d, 0xab, 0x35, 0x24, 0xdf, 0x4f, 0x0b, 0x92, 0xad, 0x78, 0x42, 0x0b },
        .hmac_key_idx = DS_KEY_BLOCK_1,
        // results of message array encrypted with these keys
        .expected_results = {
        // Message 0
      { 0xfc598d13, 0x1d473088, 0x7ffd4112, 0x05e516ab, 0xc7a44c14, 0x613d4f1d, 0xea1783c6, 0xe5a2af06, 0x4606a9a4, 0x8df1d612, 0x33e34d14, 0x86c3c728, 0x6c207e61, 0xd99dbf4e, 0x6aa1e65a, 0x6bb8bec4, 0x86c9d4a2, 0x0f26f926, 0xb4c8a1b2, 0x362af515, 0x71c39eab, 0xb7503223, 0xba49d5cb, 0x5f124681, 0xb0ed8bc1, 0x67b5e609, 0xe0ec19b2, 0xf31e7004, 0x5dfdb706, 0xd008a52a, 0xb75c99cb, 0x8902c79c, 0x204f092f, 0x2fce2179, 0x2d1f3dbb, 0x0fd74ad6, 0xd1c9e71c, 0xb074fb79, 0x35238f28, 0xd8b391af, 0x3e6d2ec3, 0x55844db8, 0xe6e75b8a, 0x03a99ee4, 0xf005dac2, 0xefa7498e, 0x61c2b890, 0xfbe2cf97, 0x3b71c7db, 0x01c5ef6d, 0x1ee832d7, 0xce99d538, 0x68f0814a, 0x6453dcb8, 0x4f68f0fe, 0xcdfef4c7, 0xce3e0294, 0xcd4e7ce4, 0x62e7e380, 0x0124b9d8, 0x36d8e0e4, 0xc4ba6d84, 0x814d3306, 0x33415f8f },        // Message 1
      { 0x18bf9474, 0x988428b3, 0x81ddcad6, 0xc2b68ef7, 0xf4a1f91e, 0x232e183d, 0x4fb1b4f7, 0x3ac70b4b, 0x144d1f9e, 0xd9118e07, 0x8ca7ad8d, 0x6ade88cf, 0xafbd08b6, 0x15206556, 0x52aaaf8a, 0x884c18c6, 0x96015c1a, 0x11830e50, 0x08a180f7, 0x38b52d92, 0x10fdd6e4, 0x971d4085, 0xb51d438e, 0x90351016, 0x911800de, 0x394b0906, 0x177c2f74, 0xf8cdda9d, 0x9cbdea30, 0xfc9c0362, 0x28bf1a50, 0x6105d3df, 0x33189ece, 0x94a16d59, 0x9b061e79, 0xd9a6e6a5, 0x1e888d3a, 0x6d478584, 0xb7790739, 0x66766e30, 0x346510ae, 0x3cbb226b, 0x136cd135, 0x850dec39, 0xe8a0ed14, 0x5b20b0e5, 0xdd1d228f, 0xbbfd809f, 0xaf438780, 0xf4febe0a, 0xe286350b, 0xad8e6121, 0xbe86682c, 0xb9c34739, 0x43d476a2, 0x6aba25ad, 0xf21e3d12, 0x599b3597, 0xccdf84ab, 0xb0f3fb04, 0x9b06b5d3, 0xa601e1e4, 0x84758b74, 0x9fbedc5d },        // Message 2
      { 0xc2b5ae36, 0x1aeb9b5b, 0x0b508123, 0x7ee7b308, 0xcebcc0fb, 0xfa397624, 0x6363bc9d, 0x0f2b272e, 0x7f06060b, 0x9a359730, 0xc8b30728, 0xd5366056, 0x6703f08e, 0x6f4aca2a, 0xf236fe5f, 0x7fc5dd15, 0xe211c3d6, 0xef1da620, 0xd7935897, 0x53399468, 0x8fdee1dc, 0x5027d4ca, 0xc624cbb6, 0xba38e77f, 0x904be5db, 0xafe206a3, 0x24e95377, 0xf2e72a73, 0x2288b1a5, 0x2c7f95fd, 0xb679dc58, 0xe3a0b721, 0xe0702e95, 0xc01864f1, 0x786296ef, 0x139882ca, 0x4c435986, 0xcd065d9e, 0x68f7facb, 0xbd9919ca, 0x8609fdcc, 0xeca268d5, 0x7aef2981, 0xfbd0368a, 0xedddf78e, 0xe8a1d08a, 0x63f18b00, 0x215b4061, 0x30dc86c8, 0x20e39320, 0xd1f81146, 0x0c7fcf7e, 0xb336613a, 0x5e21c11a, 0xb0f69848, 0x7da07291, 0xd028be8e, 0xa02763a7, 0x0ca407ee, 0x5383620c, 0x22e616a7, 0xffef43c8, 0x2e598c30, 0x41d5f744 },        // Message 3
      { 0x9ed3569e, 0xcff8e1fc, 0xb8316201, 0x8ec76542, 0xe2bd71d9, 0x1e6c1bc0, 0xbbb7be3d, 0xf205c2a4, 0xb6dd52ab, 0x79b86266, 0x54f9112e, 0xde54a79b, 0x17afd240, 0xdf138244, 0x081c244d, 0xeac3196d, 0x2c501b40, 0x7c023353, 0x067d08fe, 0x34a95f0d, 0x8518421f, 0xbea1845a, 0x2637c534, 0xd9a55f19, 0xf0073dbc, 0xec397237, 0x6d44c410, 0x6f1536ca, 0x5f8a0b81, 0x9cd9f9ba, 0x9c83a9ef, 0xc7916db2, 0xa53fc34c, 0xb970252d, 0xfae0026a, 0xa7fdbb29, 0x361cd47f, 0xdc982214, 0x511fa34e, 0xd95af0aa, 0xe27a0af0, 0x7668a551, 0x81777273, 0x7d1ad474, 0xab3ad53d, 0xac7dfbcf, 0x3b1bfabd, 0xbe544df8, 0xf265d550, 0xeb6fbb7e, 0x7d5641f9, 0x9fc4030d, 0xed76648b, 0x892f297b, 0x7de0c713, 0x599fe0d5, 0x117990d8, 0xee47aaa3, 0x1a8e7731, 0xd09c1b8c, 0xbd6b98c6, 0xc94cbf1b, 0x3dd4a4dd, 0x037acf37 },        // Message 4
      { 0x1fc95528, 0x63b7d75f, 0x74765e81, 0x8d211f05, 0xdf5813bb, 0x56ff66e5, 0x0f07e11f, 0xac2880aa, 0x5fe06824, 0x9828add3, 0xa69e9842, 0xf4e31018, 0xc69b1e36, 0xfd3a4f81, 0x89eeaa63, 0x47427df2, 0xd5cc698b, 0x9f01c11a, 0x7f773008, 0xcecbaf2b, 0xc8078035, 0x684c771b, 0xaa7f78d4, 0xdf679f8b, 0xd0c244a0, 0xc64be458, 0x83ea0d53, 0x31b442ed, 0x762016b2, 0xc1f7883b, 0x209f72ab, 0xde5cbb55, 0x815389eb, 0xb2191364, 0xcd35817f, 0x980e1486, 0xe452e660, 0x47907753, 0xa2315ae0, 0x2311f1c3, 0x58693740, 0x30ce0572, 0x06bb610a, 0x6e6b97d3, 0x95379f07, 0x998bcf16, 0xda7b2f7f, 0xfe2af4bc, 0xcfe90490, 0x67f1324e, 0xd9b87c2d, 0xf877ea6f, 0xc4faa30b, 0x007fb2aa, 0x6bfb185e, 0x55c8b8b3, 0xda50b63c, 0xb5e0d575, 0xac9a08be, 0x39411815, 0x6063cb1e, 0x3fb7f751, 0x011cb226, 0x8e017cf5 },        // Message 5
      { 0xfaf04f88, 0x9c03a39d, 0x5537d484, 0x04061d78, 0x9026ed61, 0x9105ad03, 0x9772d112, 0xd9a2fc31, 0x85fd5fcb, 0x13ea0d40, 0x13ca95bd, 0xdcdd0824, 0x6080c33f, 0x372a934e, 0x815240b9, 0x945793d7, 0x4cc48bac, 0x964dc4e7, 0x9b7865c6, 0xca216981, 0x4a78c295, 0xcc0e6805, 0xcecd3d96, 0x02277e39, 0xcb932093, 0x50f811dd, 0x84d10aeb, 0x0bcc1472, 0xc76a79d4, 0xc0b23e66, 0x15dd87e1, 0x7a94e3f9, 0xaa09a8b8, 0xbdd5a37b, 0x5b5619e8, 0xe9d9639a, 0x1bcffa7d, 0xf95efe4b, 0x527542cb, 0xce3abb49, 0xebfaf581, 0x0cacc6c1, 0xe8b3236c, 0x9d89ea8b, 0xff5bcc27, 0xab1730d1, 0xc3b1f798, 0x746246a6, 0x2e0b7462, 0x73ff914c, 0x10a9f4c2, 0x8be4541a, 0xdc439d20, 0x4fe5d3c5, 0xd24d95ab, 0x9a8f38e5, 0xf0163d6a, 0xbdc721c8, 0x616bbdd6, 0x13241387, 0x892b9c14, 0xff16c7a7, 0x4a9d5086, 0x6fdabbf7 },        // Message 6
      { 0x99f67aaf, 0x4599443a, 0xeaac250f, 0x64e642a5, 0xc90870ac, 0xfaad4d28, 0xcd7a418e, 0x8abe5c09, 0x54fdb68e, 0x5a690863, 0xc0244737, 0xcfb554ea, 0x19f591e3, 0xc63737c2, 0xfef6af07, 0x589d8e38, 0xd1792c9c, 0x5804026c, 0x62f0c6c8, 0x94f3dd2d, 0xc3078ba0, 0x00d87497, 0x54e1c383, 0x545bbdfb, 0x901d305f, 0xb1bcd27b, 0x9a7e39fd, 0x479d39dc, 0x1a41841d, 0x51336abf, 0xec4e7c5e, 0x025424aa, 0x0ac9809f, 0x11034663, 0x3c25c619, 0x40052b07, 0xec885c50, 0xcc54068d, 0xc83ba064, 0x3dc4224b, 0xeea736d7, 0xba652070, 0xcce7a1d5, 0x7cf447af, 0xc5758174, 0xee446e38, 0xb39bf8a3, 0x82034077, 0xc06e33fc, 0xb9f3a31c, 0x8cfda584, 0xb6eeb236, 0xc6f30415, 0x2d1319ef, 0x2adea19f, 0x9a5789ac, 0x276ba414, 0x82d564c3, 0x6c4e2ac9, 0x86efe4ca, 0xadf6a7c3, 0x49dffebd, 0x4ec9b334, 0x525cc81a },        // Message 7
      { 0x2f8cc27e, 0x350875fb, 0x39dd5df5, 0x89b03399, 0x7061995d, 0xcdff4a8d, 0x8ad7164a, 0xa94b3d47, 0xa27a52df, 0x81defbc8, 0x3bcab908, 0xe1ab51aa, 0x5e255d58, 0xa86e03c0, 0xb12e7d2d, 0xeb4ddb39, 0xe0b20ff1, 0xa1a8a804, 0xa1e63720, 0x5cdf514c, 0x20470756, 0x80ff396e, 0xe1a222fb, 0x1416a149, 0xf107da73, 0xb1f7266e, 0x004b334e, 0xa48d77d3, 0x4f1c1163, 0x2026e047, 0x516e5e8c, 0xc2e3b1dd, 0xa92a39d9, 0xc9912ad5, 0xe2a5d15b, 0x441a8d3d, 0xff6d2498, 0x3cc68abc, 0x31eeb23f, 0xa13cfe83, 0x9bdaab5f, 0xdd044d15, 0x26bd0f7a, 0xa42c0703, 0x6a5abd71, 0x037cf6a3, 0x6ac04323, 0x891bc22a, 0x40c30a53, 0x203fbde5, 0x6548de6b, 0x434b16d8, 0x16312b1e, 0x1cc818d2, 0x922fc3eb, 0xdb9afa7f, 0x826fb620, 0xac29c255, 0x535a649d, 0x295c40b1, 0x4371c19a, 0x98ad6d9a, 0xabbf3324, 0x99ac0444 },        // Message 8
      { 0x6e83f692, 0xbc0e6bda, 0x7e72510e, 0x4e944578, 0xc65967b7, 0x32f2d6c0, 0x02e369aa, 0x67658c2f, 0x8c9b40b7, 0x48cb3f6c, 0xe44438c3, 0xa789ffc2, 0x58d58fd3, 0xb44dc061, 0xb41b3551, 0x7cb5a176, 0xc275d51f, 0x6b1b5da9, 0x084057ce, 0x5335f8da, 0xe3d68679, 0x0e2c0f9c, 0xc74d3251, 0x8614ea1d, 0xd24d3fbb, 0x19f1c101, 0x2b8428fe, 0x7e124e3b, 0xe5691058, 0xb81eaa06, 0x8cec76a6, 0x89b1c345, 0x2847f64f, 0xdc1c91dd, 0xb8b4b126, 0x929da99a, 0x53aef56c, 0x0790c430, 0x9c47900b, 0x2b14de91, 0x94a0c514, 0x36199986, 0x4c0da7ed, 0xe546f058, 0x49dd9fe7, 0x31f8858c, 0x2c0c6222, 0xf8fa5b53, 0x976128c4, 0x19cd831c, 0x9a62299c, 0x53e300c9, 0x88df91d0, 0x940f1558, 0xbd7fdce8, 0x8789041c, 0xefa7f207, 0x9a2eb6e8, 0xea2d8b7f, 0x5d1f5954, 0x1b20a908, 0x8ff17818, 0x428676fe, 0x1d93063a },        // Message 9
      { 0x382d84b6, 0x9d2697cb, 0xf39c39f1, 0x874b431d, 0x4add73d1, 0x3af49e97, 0xed375698, 0x8a19b492, 0xe722e314, 0x3a83ce5d, 0xd9e4fd2f, 0x363f8acc, 0xf9b56768, 0xb646b2be, 0x593eb44c, 0x1866c5a8, 0xa8f2b7f0, 0x81f6de65, 0xc86441c3, 0xcd3cd74d, 0x3e0aa6b2, 0x620d0e63, 0x6e9337b8, 0xcd97bef4, 0xb8808dc3, 0x0a996a55, 0xcfc0fafe, 0xc20b72cd, 0x6dd75c42, 0x0a416689, 0xe175ba09, 0xfc1e76a4, 0xd257e752, 0x2ab8c17f, 0x528354f7, 0x0240e4ea, 0x545cafbb, 0x0b06f1ff, 0xdb305649, 0x581cee55, 0x21adaed2, 0x4ff0aa40, 0x99ba5d0d, 0x0fce658a, 0x97c55b62, 0x820b80b3, 0xd0186a18, 0x5e5a70bd, 0xb8c4c251, 0x883f43b2, 0x8fe14b9e, 0x3a12687b, 0x0c3552b5, 0x88506482, 0x2f8aad3a, 0x51a11aaa, 0x6199cbe3, 0x48fd82ec, 0x92818dc1, 0xfb3e4fcd, 0x4b5e76bf, 0xf7ded2ad, 0x241b6fae, 0xa617617f },     },
     },
    { /* Case 5 */
        .iv = { 0xdc, 0x0f, 0x35, 0x44, 0x22, 0x00, 0x92, 0x16, 0xb6, 0x05, 0x77, 0x21, 0x81, 0x7b, 0x04, 0x91 },
        .p_data = {
            .Y = { 0xb8e66fe1, 0xfb4b3a40, 0x8efd30fa, 0x5c7aeb64, 0x1b1c0d2d, 0x670be326, 0x75ae690a, 0x7923ae77, 0x6a656153, 0xf69d0c47, 0xad23d132, 0x9ecd650e, 0x583ce8d7, 0xae5eb003, 0xfbc594b5, 0x46ddce6c, 0x6557f37a, 0x148c199e, 0x1e256956, 0xa2aae11d, 0xddbc2d90, 0x811626e6, 0x9cd39a2b, 0x261ec25a, 0x2e44b719, 0x48677ed6, 0xc299ec62, 0x1e0372ec, 0xfcdc35b6, 0x13f5b7f9, 0x9859d29f, 0x7e68d81f },
            .M = { 0xf40f99b7, 0xe877b887, 0xbf9b5385, 0xad94cdac, 0x9f462df5, 0xccf6a157, 0x1b754997, 0xf94ed6e4, 0x93ab9fde, 0xad4c1a0b, 0xa9579b24, 0x1c7378e2, 0xf6b823ce, 0xde2d3726, 0xa8e23815, 0x8c432c73, 0x29eccd94, 0xd3982010, 0x1a95825f, 0xcc2e5134, 0x0b5f76ba, 0x2af95b97, 0xea6fe7ef, 0xdb8fdc03, 0xd9e65a58, 0xe19cde4c, 0xaa2e0208, 0x843ffad6, 0x555b5b1f, 0x98499192, 0xe2a8611f, 0xc5052c0c },
            .Rb = { 0xa2bdb777, 0x3c23d986, 0xe23b0a5a, 0x50c5dd1e, 0x618de269, 0x4d1f2bdc, 0x58f59904, 0x5bb3816f, 0x6298f3f0, 0xad52c80f, 0x8276c522, 0xa2985661, 0xab005c67, 0x985ec922, 0x19569975, 0x20fd4aac, 0x6058b0d7, 0x6606d163, 0x89f84c04, 0xfc5434ed, 0x1595f1af, 0xdc222b8a, 0x1db4fe21, 0xeaa2dcc0, 0xe06ad1c3, 0x0d7143f8, 0xbeac8384, 0xf39ef498, 0x628db739, 0xfdc6e50e, 0xbb1a6725, 0x454a49a1 },
            .M_prime = 0x350c6bf9,
            .length = 31, // 1024 bit
        },
        .expected_c = { 0xf8, 0x04, 0xa8, 0xfb, 0x42, 0x1d, 0x28, 0x97, 0x84, 0xb7, 0x31, 0xe4, 0x0d, 0xdd, 0xe5, 0xd3, 0xf8, 0x25, 0x0d, 0x3b, 0x50, 0x3b, 0xa5, 0x3f, 0xdc, 0xbc, 0x01, 0xf5, 0xd4, 0x5f, 0x10, 0x7e, 0xb9, 0xe4, 0xaf, 0x7b, 0x2a, 0xd4, 0x4a, 0x71, 0xed, 0xbb, 0x8f, 0x47, 0x07, 0x1c, 0xdc, 0x0e, 0x1b, 0xdc, 0x35, 0xce, 0x30, 0x8b, 0x79, 0x74, 0x27, 0x85, 0x44, 0xc3, 0x19, 0xdf, 0x47, 0x2d, 0x3f, 0xe9, 0x45, 0x9a, 0xa4, 0x07, 0xc2, 0xf1, 0x6a, 0x78, 0x6f, 0xed, 0x79, 0x54, 0x1a, 0xdd, 0x2d, 0xa4, 0x2c, 0x0c, 0x9a, 0xb2, 0xb1, 0x24, 0x4a, 0xdf, 0x1c, 0xfc, 0xa9, 0x4d, 0x6b, 0xc0, 0x09, 0x01, 0x07, 0x14, 0x8c, 0x51, 0xd5, 0x0f, 0xcc, 0xd3, 0x27, 0x05, 0x6b, 0x47, 0xbf, 0xe2, 0xe5, 0x2e, 0xa1, 0x63, 0x80, 0xdd, 0x5d, 0x9f, 0x38, 0x71, 0x5c, 0x7a, 0x17, 0x21, 0x3b, 0x2c, 0x6d, 0xe6, 0xe0, 0x02, 0x80, 0x5f, 0x91, 0xc4, 0x6d, 0x2b, 0x1d, 0x28, 0xeb, 0xbf, 0x86, 0xb0, 0xbd, 0x9b, 0xd7, 0x82, 0xb7, 0xa0, 0x89, 0x16, 0x7e, 0xa5, 0xc7, 0x78, 0xdb, 0xb9, 0xeb, 0xcd, 0x6d, 0x21, 0xf4, 0xa5, 0x63, 0xde, 0xb7, 0xbe, 0x7c, 0x56, 0x9e, 0xa4, 0xd8, 0x74, 0xff, 0xcd, 0x5d, 0x25, 0xce, 0xaa, 0xf9, 0x23, 0x25, 0xc3, 0x97, 0x04, 0xe5, 0xf4, 0xf4, 0xf4, 0x3e, 0x07, 0x9b, 0xe0, 0x30, 0x09, 0xf7, 0xd2, 0x05, 0x6e, 0xb9, 0x84, 0x3e, 0x79, 0xe1, 0xd7, 0xbf, 0x6f, 0xfa, 0x04, 0xba, 0x14, 0xfd, 0x05, 0xc7, 0x1a, 0x9d, 0xf7, 0x68, 0x7d, 0xe2, 0x76, 0x41, 0x83, 0x3b, 0x09, 0x0a, 0x54, 0xcd, 0xf5, 0x3e, 0x22, 0xfc, 0xc1, 0xc9, 0x80, 0x19, 0x86, 0xc3, 0xb7, 0x9f, 0x14, 0x84, 0x04, 0x33, 0x17, 0xcd, 0xfc, 0x55, 0x54, 0x22, 0x06, 0x2e, 0x79, 0x1d, 0xe9, 0xd8, 0x5d, 0xc3, 0xf1, 0xcc, 0x38, 0x57, 0xb5, 0xa1, 0x9d, 0x42, 0x7b, 0xfc, 0x9f, 0xa9, 0xea, 0x25, 0x1c, 0xd1, 0xbf, 0xc4, 0xd5, 0x0d, 0xc1, 0xc9, 0xa5, 0x05, 0x8e, 0x54, 0xfa, 0xe2, 0x65, 0xac, 0xb3, 0x01, 0x50, 0xbf, 0x9b, 0xc2, 0x39, 0x34, 0xd8, 0xf2, 0x57, 0x3a, 0xcb, 0x6f, 0x02, 0x86, 0x70, 0xd4, 0xad, 0x03, 0xf1, 0xa8, 0x32, 0x9d, 0x96, 0xba, 0x8d, 0xce, 0x08, 0x96, 0x58, 0x36, 0xc7, 0x8e, 0x1f, 0x40, 0xc6, 0x9b, 0x2b, 0x33, 0xab, 0x36, 0x13, 0xdb, 0xa6, 0xf4, 0x73, 0x2c, 0xd3, 0x4e, 0x58, 0xf9, 0x9e, 0xc7, 0x6e, 0x38, 0x4d, 0x73, 0xe5, 0xbb, 0xc0, 0x0c, 0x80, 0x17, 0x4d, 0x9d, 0x87, 0xa9, 0x12, 0xa3, 0x17, 0xc5, 0x69, 0x97, 0x7a, 0x87, 0x3b, 0x78, 0x25, 0x5d, 0x4b, 0x0a, 0x6a, 0x93, 0x44, 0x27, 0x83, 0x3a, 0x12, 0x6c, 0x35, 0x32, 0x3d, 0xfb, 0x73, 0xf8, 0xe1, 0x82, 0xcf, 0x5c, 0xb5, 0x90, 0xab, 0x31, 0xe4, 0xe9, 0x6a, 0x52, 0x76, 0x3b, 0x99, 0x9e, 0x27, 0xdb, 0x00, 0x44, 0x3e, 0xd2, 0x54, 0xfa, 0xe2, 0xce, 0x1b, 0x94, 0x0e, 0xf0, 0x42, 0xe7, 0x18, 0x54, 0x8f, 0x01, 0x0c, 0xcf, 0x13, 0x69, 0xc1, 0x48, 0x2c, 0x6e, 0x5a, 0x0e, 0x52, 0xa0, 0x7a, 0x82, 0x3a, 0xd7, 0xa7, 0xef, 0xd2, 0x2c, 0x77, 0x35, 0xdc, 0x31, 0x6a, 0x6f, 0xb2, 0xf2, 0x4e, 0x25, 0x3b, 0x6d, 0x16, 0x46, 0xd9, 0xd9, 0x81, 0xcb, 0x8e, 0x6f, 0x65, 0x33, 0x31, 0x67, 0xc2, 0xf6, 0x3a, 0xe8, 0xb3, 0x3f, 0x9a, 0x11, 0x56, 0xa2, 0x4a, 0xb3, 0x55, 0x2b, 0x8d, 0x39, 0x14, 0x33, 0xf9, 0xac, 0x53, 0xf9, 0x4d, 0xaf, 0xe8, 0xde, 0x3d, 0xef, 0x82, 0xaa, 0xd3, 0x21, 0x19, 0xb0, 0xd6, 0x6c, 0x3d, 0x78, 0xd9, 0xa8, 0xef, 0x2d, 0x81, 0xcc, 0x0f, 0x72, 0xba, 0xc3, 0x49, 0x81, 0x66, 0xc2, 0x42, 0xaf, 0x27, 0xb3, 0xb4, 0x86, 0x4e, 0x25, 0x35, 0x3e, 0x42, 0xac, 0x44, 0x87, 0x34, 0xf5, 0x3f, 0x3d, 0xfb, 0x69, 0x5f, 0x42, 0x6d, 0x13, 0x2d, 0x21, 0xaa, 0x41, 0xdc, 0x61, 0x06, 0x19, 0x62, 0x4a, 0x03, 0xf5, 0xa3, 0x83, 0x6c, 0xa6, 0x28, 0x23, 0x2b, 0x22, 0xb7, 0x07, 0x73, 0x29, 0x06, 0xf2, 0x44, 0x36, 0x90, 0xaa, 0xb9, 0x40, 0xc7, 0x87, 0xef, 0x82, 0x09, 0xc7, 0x0a, 0xab, 0x17, 0xb9, 0x5a, 0x7b, 0x28, 0xfb, 0xc3, 0x5d, 0xf6, 0xeb, 0x3c, 0xdd, 0x87, 0xbb, 0xa7, 0x27, 0x3e, 0xdb, 0xd5, 0x07, 0xea, 0xc5, 0x75, 0x94, 0xfc, 0x5a, 0xfb, 0xd6, 0xba, 0x5e, 0x5c, 0xc9, 0xa7, 0xaf, 0x6c, 0xb7, 0xe2, 0x62, 0x16, 0x3b, 0x9e, 0x43, 0xbf, 0x37, 0x7d, 0x4b, 0xb9, 0xa7, 0x0c, 0x42, 0x67, 0x4d, 0x4e, 0xf3, 0x04, 0xbf, 0xf1, 0x51, 0xb3, 0x6e, 0xb7, 0x12, 0x93, 0x44, 0x08, 0x38, 0x29, 0x39, 0x9d, 0xe1, 0x96, 0x35, 0x88, 0x73, 0x56, 0xf0, 0x30, 0xd0, 0x6c, 0xdb, 0xab, 0xd5, 0xa9, 0xfc, 0x02, 0xbf, 0x6c, 0x34, 0xf2, 0xef, 0xd9, 0x54, 0x09, 0x66, 0xf0, 0x18, 0x4c, 0xcf, 0xc8, 0x15, 0x08, 0x14, 0x18, 0xfe, 0x65, 0xbb, 0xe7, 0x55, 0xe8, 0x5c, 0xf8, 0xc8, 0xf5, 0x70, 0x0f, 0xc6, 0x21, 0x30, 0x77, 0xaf, 0xc5, 0x7c, 0x98, 0xfe, 0x53, 0x69, 0x9d, 0xaf, 0x79, 0xbd, 0x39, 0xfe, 0x70, 0x26, 0x83, 0xff, 0xde, 0x6c, 0xf9, 0x80, 0x91, 0xb1, 0x15, 0x56, 0xd0, 0xcf, 0xcd, 0xfa, 0x23, 0xea, 0x79, 0x52, 0xbf, 0x55, 0x94, 0x2f, 0x5e, 0xb0, 0x40, 0x47, 0x02, 0xbf, 0x70, 0xc5, 0x6b, 0xf5, 0xf3, 0xee, 0x3f, 0x36, 0x7d, 0xcf, 0x66, 0x73, 0x4a, 0x8f, 0x87, 0x28, 0x54, 0x0a, 0xe3, 0xe9, 0xaa, 0xc9, 0x95, 0xf6, 0xe4, 0x60, 0xa2, 0xa1, 0x13, 0x12, 0x02, 0x36, 0x84, 0x4c, 0xde, 0x68, 0xe1, 0x55, 0x4a, 0x0c, 0x54, 0x73, 0x94, 0x03, 0xaa, 0xfd, 0x77, 0x36, 0xb3, 0xf3, 0xc2, 0x75, 0x13, 0x1f, 0xf1, 0xcd, 0x6d, 0xad, 0x68, 0x6a, 0x3f, 0xaf, 0x24, 0x37, 0x5b, 0x23, 0x3d, 0xf5, 0x98, 0x8b, 0x72, 0x0a, 0xe0, 0x8a, 0x1c, 0x10, 0x4d, 0x68, 0xae, 0xe6, 0xa0, 0x6c, 0x0a, 0x6a, 0x11, 0xce, 0xa4, 0xfc, 0x3e, 0x62, 0xf6, 0x1e, 0x5a, 0x83, 0x27, 0xe7, 0x6c, 0x05, 0x2a, 0x6b, 0xc7, 0x5e, 0x38, 0x6d, 0x39, 0x2f, 0x2c, 0xb6, 0x46, 0xef, 0x6d, 0x30, 0x41, 0x22, 0xf3, 0x86, 0xa0, 0x65, 0xfc, 0xfd, 0x26, 0x60, 0x51, 0x17, 0xfe, 0x02, 0x6c, 0xbd, 0x07, 0x63, 0x73, 0x88, 0xf2, 0xa7, 0xad, 0x96, 0xef, 0x1f, 0xa8, 0x4e, 0xd9, 0xfb, 0x92, 0xc1, 0x14, 0xce, 0x48, 0x88, 0xa0, 0xfc, 0xc2, 0x5a, 0xfc, 0xf1, 0x10, 0xed, 0x43, 0x77, 0x3e, 0x6a, 0x14, 0x2d, 0x93, 0xd3, 0x5d, 0x18, 0x26, 0x25, 0xa5, 0xd9, 0xc6, 0x06, 0x9e, 0xe3, 0x82, 0x69, 0x51, 0x31, 0x50, 0xd0, 0xbc, 0x10, 0x9a, 0x26, 0x31, 0xe2, 0xc9, 0x16, 0x67, 0x04, 0xf1, 0xf2, 0x7d, 0xe6, 0x72, 0x7f, 0xfb, 0x20, 0x76, 0x8d, 0xca, 0x0b, 0x66, 0xd2, 0x1a, 0x67, 0xc1, 0x5a, 0x76, 0x8e, 0xe2, 0x34, 0xdf, 0x5b, 0x0a, 0xd1, 0x58, 0x98, 0x66, 0x59, 0xed, 0xad, 0x47, 0x4d, 0xe0, 0xb1, 0xe9, 0x73, 0x07, 0xb6, 0x58, 0xd3, 0xc6, 0x0d, 0xa4, 0x9c, 0x51, 0xf7, 0x68, 0x53, 0x48, 0x22, 0xd4, 0xae, 0x13, 0xd4, 0x8a, 0x47, 0xc8, 0x60, 0x02, 0x53, 0x0b, 0x77, 0x17, 0xd0, 0xfd, 0x16, 0xdf, 0xf7, 0x2d, 0x8f, 0x41, 0xa1, 0x27, 0x40, 0x3b, 0x63, 0xf0, 0x4f, 0x7b, 0xcf, 0x4b, 0x80, 0x99, 0x9b, 0xd6, 0x62, 0x30, 0x33, 0x31, 0xe7, 0xca, 0x15, 0xee, 0x02, 0x29, 0xe3, 0x06, 0x39, 0xef, 0x87, 0xc3, 0xe5, 0xb5, 0x85, 0x1a, 0x7b, 0x36, 0xc1, 0xff, 0x78, 0xad, 0x5c, 0xbd, 0xe1, 0xfa, 0x75, 0x15, 0x2f, 0x85, 0xa3, 0xcf, 0xc9, 0x89, 0x13, 0xa5, 0x4d, 0x92, 0x3f, 0x17, 0x2f, 0x19, 0x57, 0x76, 0x88, 0x26, 0xee, 0x4c, 0xe4, 0x31, 0x57, 0xbe, 0xe6, 0x6c, 0x53, 0x89, 0x5f, 0x3b, 0x18, 0xd7, 0x83, 0x40, 0xe4, 0xb2, 0x70, 0x4c, 0xaf, 0x82, 0xb5, 0xe7, 0x84, 0x84, 0xdd, 0x9e, 0x24, 0x45, 0xbe, 0x7f, 0x7d, 0xf6, 0xad, 0x71, 0x7b, 0xa0, 0xf4, 0x03, 0x32, 0x0f, 0xd5, 0x91, 0x89, 0xd1, 0xd7, 0x73, 0x30, 0xc3, 0xf9, 0x1d, 0xef, 0x93, 0x0c, 0xfe, 0x30, 0xc8, 0xdd, 0xe0, 0xee, 0x68, 0xf1, 0x65, 0x56, 0x15, 0x38, 0xb1, 0x55, 0x2f, 0x51, 0x21, 0x11, 0x0f, 0x43, 0x18, 0x2f, 0x01, 0x9f, 0x17, 0x57, 0x0f, 0xfa, 0xd3, 0x05, 0xea, 0x17, 0xbc, 0x5a, 0x80, 0x5b, 0xb0, 0x63, 0x61, 0x78, 0xd0, 0x18, 0xa7, 0xd8, 0x8a, 0xff, 0x83, 0x35, 0xaa, 0x56, 0xc2, 0x8b, 0x70, 0x0a, 0x2a, 0xd8, 0x5b, 0x67, 0xf5, 0xa4, 0x77, 0x49, 0xf4, 0x96, 0x63, 0xd2, 0x0e, 0x85, 0x5a, 0xa9, 0xaf, 0x49, 0xa7, 0x45, 0x60 },
        .hmac_key_idx = DS_KEY_BLOCK_3,
        // results of message array encrypted with these keys
        .expected_results = {
        // Message 0
      { 0x01e2dc01, 0x633fdca6, 0x7cd9d77d, 0x98ab8b40, 0x90c880f5, 0xb8ac2b6c, 0x9f1c1674, 0x8e588407, 0xc161ea0d, 0x72650a07, 0xa0dc2aea, 0x90a933b8, 0x45c3a758, 0xc028546e, 0x1a71d733, 0x90904a2d, 0x75c1e6d5, 0xbafd9de8, 0x3c59bb42, 0xb8509064, 0x9c195816, 0x535a89ac, 0x91586a13, 0xce511155, 0x0ccd474e, 0xada5982c, 0x1826227e, 0x13f35e5a, 0xaac45ec8, 0x7a9da79a, 0x95aac0f0, 0xc3f7e0d9 },        // Message 1
      { 0x7439408f, 0x4f559c0f, 0x53abcf59, 0x0afe23bb, 0xd7968c8e, 0xbdcd5bad, 0x62e6a085, 0x07bdf181, 0xdae1ae5a, 0x9c4f6678, 0x9e7ba556, 0xbfa17b23, 0x4e18253a, 0xf8e0580a, 0x4ba0b485, 0xaaf0f898, 0xb96658dc, 0x815303b4, 0xf968440d, 0xf5332fe4, 0x99ed2be9, 0xfbe4df33, 0xa7c184f2, 0x7f8d12fc, 0x4d463fcd, 0x7935979e, 0xdf3b14f6, 0xc4c4882f, 0xe6ffec7e, 0xd7763619, 0x18014ec2, 0x46990fb3 },        // Message 2
      { 0x50a0b5cb, 0x997c13a9, 0xb0f6ffd3, 0xe9413733, 0x0e7ab25c, 0xddc6a245, 0xfbd0f854, 0x6adb24d0, 0x5db4e56e, 0x8a61bca0, 0x803b66a5, 0xdad11d26, 0xe2906044, 0x73cc5d4a, 0x229d10ba, 0x4804e398, 0xbd9ac917, 0xf808270f, 0xba08ba25, 0x4355b4f4, 0xbc8b74d4, 0x1a59fcae, 0xcf7b5091, 0x4d6f5787, 0xcb372f9a, 0xb6a06799, 0xadf1f839, 0x5b64cb8d, 0x6407d3a3, 0x97c952bb, 0xa72f607f, 0x8d16d14a },        // Message 3
      { 0x03e5e08f, 0xcc577a1b, 0x0e76860c, 0x02cf67e2, 0x456289ca, 0x87960178, 0x54612711, 0x6318dd0c, 0x713f3c87, 0xad64c7f9, 0xda797afb, 0x1b126889, 0x6af88ba2, 0x8fff6375, 0xc8d3f032, 0xd32e5e42, 0x6e5e8de2, 0xfd90e475, 0xf77c9234, 0x450b1958, 0x24889574, 0xc00ce37f, 0x5f0f0292, 0xbcb5c6ef, 0xfbac4efb, 0x0d4e62bd, 0x38385be3, 0x418bd60b, 0x68ef088d, 0x382a89a0, 0x204afb17, 0x46a2dec9 },        // Message 4
      { 0xde71c441, 0xcbc9c834, 0x6c88deda, 0x62d21e99, 0xb0d50d51, 0x9d7409ed, 0x7a7b2210, 0xe4c44565, 0xbcb6c5c9, 0xe26a2e8a, 0x7db936fa, 0x60a36028, 0xb9a38900, 0x252fe056, 0xe37511d0, 0x9ff6c6f0, 0xdd8c9bae, 0xb790246a, 0x80b6886b, 0xb7c640b8, 0x2e444acf, 0x50e4ae96, 0xa3e3fa0d, 0x5da3b5e9, 0x0e46b2b1, 0xd19d6374, 0xfe0211f5, 0xabefeb51, 0x1d311220, 0x18ddcb78, 0x256a708b, 0xb72a624b },        // Message 5
      { 0xec841b54, 0x77810071, 0x5942ee0e, 0x4bf98f6e, 0xec6db923, 0xf10470e9, 0x51efa255, 0x8880447d, 0xec49dd41, 0x277acea7, 0x8a6671dd, 0x04aec75b, 0xd5590a8e, 0x827ebeb0, 0x122ea66a, 0x3934a777, 0x85259b34, 0xc74af398, 0xbd555666, 0x42f671f7, 0x90344a8e, 0x03451800, 0x0bb86241, 0xb6b5998d, 0x9231117a, 0x09de2a15, 0xe02df98c, 0xf4670c0d, 0x8242fb53, 0x2fecd8b1, 0x685ad637, 0x3d375ca1 },        // Message 6
      { 0x601f9888, 0x324f03a7, 0x76816efa, 0x09f31fc2, 0x4fe54aaf, 0x8399f6b5, 0x116572eb, 0x0f72fb48, 0xbcc9bdee, 0x7ce8c882, 0xf2dae59a, 0xa0f7623c, 0xa97bb554, 0xc353fe65, 0xce3091d5, 0xe5aebe3b, 0xe4d813ce, 0x54f4d525, 0x2efb8057, 0x3f4d489e, 0xcc060bed, 0x8fc0df8f, 0x314948a9, 0x168d95a9, 0xaeee97d6, 0xfb4fb17f, 0xf4f7f17d, 0x69bf9713, 0xf7a9a28b, 0x79012b09, 0x4ee7e70a, 0x1a094462 },        // Message 7
      { 0x4bc2312b, 0x5619fac2, 0x1032aede, 0x1c197418, 0x098c3bca, 0xba6a75b5, 0x6f914c9f, 0xfcb330fa, 0xd054a7a6, 0x741a1085, 0x21a6aa90, 0x4937d82b, 0xd51cf1af, 0x9f7c568a, 0x47f983be, 0x8c6cd2f9, 0xc6694a58, 0x9ef95f86, 0x5fa02a5a, 0x9a0e7f70, 0xc08e256a, 0x05be92fb, 0x303198c9, 0xd5c30c38, 0x039f7e31, 0xbb9f0b6e, 0x8b176868, 0xa92f0eaa, 0x3afee460, 0x23da6824, 0x008a0231, 0x83235c9c },        // Message 8
      { 0x427d58e6, 0xc0bcaa51, 0xdebac30b, 0x08f1a13b, 0x96ec5ef0, 0x114d0d1f, 0xedaafc04, 0xf8c49654, 0xb6e3b23c, 0x2dbdc932, 0x28401da1, 0xca20457a, 0x0015d8f4, 0x3c809988, 0x9f6fcc0e, 0x225df98d, 0x258efa9f, 0x05c2b486, 0x35b9fb97, 0xfecb6e6f, 0x14540fac, 0x721b786f, 0x0f2e8738, 0x9811ad77, 0x5ee7eb36, 0xd3dbedb1, 0x4f1686fd, 0x31390953, 0x45e100c7, 0x49485e7a, 0xc586b794, 0x853b42d3 },        // Message 9
      { 0x82cb45e5, 0x393db1f2, 0xd22e78cb, 0xcf57d0a4, 0x387e16ff, 0x7db3454f, 0x249c492f, 0xda86d50b, 0x7895254a, 0x1c12f919, 0x7b0be80c, 0x36a10ee4, 0x0666df05, 0xdd901597, 0x674c62ae, 0xe1b6c5fb, 0x1cffbbd9, 0x2fd45bdf, 0x30310492, 0xf2a60213, 0xadf46362, 0x28a65efa, 0xafaf03d4, 0x9bf58710, 0xef74f30a, 0x97092e4b, 0xd2c5deb1, 0x760b434e, 0x824fb397, 0x40de33ec, 0x815bd4eb, 0x32498475 },     },
     },
};
