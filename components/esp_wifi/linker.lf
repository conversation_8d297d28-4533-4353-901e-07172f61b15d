[sections:wifi_iram]
entries:
    .wifi0iram+

[sections:wifi_rx_iram]
entries:
    .wifirxiram+

[sections:wifi_slp_iram]
entries:
    .wifislpiram+

[sections:wifi_or_slp_iram]
entries:
    .wifiorslpiram+

[sections:wifi_slp_rx_iram]
entries:
    .wifislprxiram+

[sections:wifi_extra_iram]
entries:
    .wifiextrairam+

[scheme:wifi_iram]
entries:
    wifi_iram -> iram0_text

[scheme:wifi_rx_iram]
entries:
    wifi_rx_iram -> iram0_text

[scheme:extram_bss]
entries:
    bss -> extern_ram
    common -> extern_ram

[scheme:wifi_slp_iram]
entries:
    wifi_slp_iram -> iram0_text

[scheme:wifi_or_slp_iram]
entries:
    wifi_or_slp_iram -> iram0_text

[scheme:wifi_slp_rx_iram]
entries:
    wifi_slp_rx_iram -> iram0_text

[scheme:wifi_extra_iram]
entries:
    wifi_extra_iram -> iram0_text

[sections:wifi_log_error]
entries:
    .rodata_wlog_error+

[sections:wifi_log_warning]
entries:
    .rodata_wlog_warning+

[sections:wifi_log_info]
entries:
    .rodata_wlog_info+

[sections:wifi_log_debug]
entries:
    .rodata_wlog_debug+

[sections:wifi_log_verbose]
entries:
    .rodata_wlog_verbose+

[scheme:wifi_default]
entries:
    wifi_slp_iram -> flash_text
    wifi_or_slp_iram -> flash_text
    wifi_slp_rx_iram -> flash_text
    wifi_iram -> flash_text
    wifi_rx_iram -> flash_text
    wifi_extra_iram -> flash_text
    if LOG_MAXIMUM_LEVEL <= 0:
        wifi_log_error -> rodata_noload
    else:
        wifi_log_error -> flash_rodata
    if LOG_MAXIMUM_LEVEL <= 1:
        wifi_log_warning -> rodata_noload
    else:
        wifi_log_warning -> flash_rodata
    if LOG_MAXIMUM_LEVEL <= 2:
        wifi_log_info -> rodata_noload
    else:
        wifi_log_info -> flash_rodata
    if LOG_MAXIMUM_LEVEL <= 3:
        wifi_log_debug -> rodata_noload
    else:
        wifi_log_debug -> flash_rodata
    if LOG_MAXIMUM_LEVEL <= 4:
        wifi_log_verbose -> rodata_noload
    else:
        wifi_log_verbose -> flash_rodata

[mapping:wifi_default]
archive: *
entries:
    * (wifi_default)

[mapping:pp]
archive: libpp.a
entries:
    if ESP_WIFI_IRAM_OPT = y:
        * (wifi_iram)

    if ESP_WIFI_RX_IRAM_OPT = y:
        * (wifi_rx_iram)

    if ESP_WIFI_SLP_IRAM_OPT = y:
        * (wifi_slp_iram)

    if ESP_WIFI_IRAM_OPT || ESP_WIFI_SLP_IRAM_OPT:
        * (wifi_or_slp_iram)

    if ESP_WIFI_RX_IRAM_OPT || ESP_WIFI_SLP_IRAM_OPT:
        * (wifi_slp_rx_iram)

    if ESP_ALLOW_BSS_SEG_EXTERNAL_MEMORY = y:
        * (extram_bss)

    if ESP_WIFI_EXTRA_IRAM_OPT = y:
        * (wifi_extra_iram)

[mapping:net80211]
archive: libnet80211.a
entries:
    if ESP_WIFI_IRAM_OPT = y:
        * (wifi_iram)

    if ESP_WIFI_RX_IRAM_OPT = y:
        * (wifi_rx_iram)

    if ESP_ALLOW_BSS_SEG_EXTERNAL_MEMORY = y:
        * (extram_bss)

    if ESP_WIFI_SLP_IRAM_OPT = y:
        * (wifi_slp_iram)

    if ESP_WIFI_RX_IRAM_OPT || ESP_WIFI_SLP_IRAM_OPT:
        * (wifi_slp_rx_iram)

    if ESP_WIFI_EXTRA_IRAM_OPT = y:
        * (wifi_extra_iram)

[mapping:esp_wifi]
archive: libesp_wifi.a
entries:
    if ESP_WIFI_IRAM_OPT = y:
        esp_adapter:coex_pti_get_wrapper (noflash)
        wifi_netif:wifi_sta_receive (noflash)
        wifi_netif:wifi_transmit_wrap (noflash)
    if ESP_WIFI_SLP_IRAM_OPT =y:
        esp_adapter:wifi_clock_enable_wrapper (noflash)
        esp_adapter:wifi_clock_disable_wrapper (noflash)
        if PM_ENABLE = y:
            wifi_init:wifi_apb80m_request (noflash)
            wifi_init:wifi_apb80m_release (noflash)

[mapping:esp_timer_wifi_pm]
archive: libesp_timer.a
entries:
    if ESP_WIFI_SLP_IRAM_OPT = y && IDF_TARGET_ESP32 = y:
        esp_timer:timer_task (noflash)
        esp_timer:timer_process_alarm (noflash)
