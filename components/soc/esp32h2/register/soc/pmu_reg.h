/**
 * SPDX-FileCopyrightText: 2022 Espressif Systems (Shanghai) CO LTD
 *
 *  SPDX-License-Identifier: Apache-2.0
 */
#pragma once

#include <stdint.h>
#include "soc/soc.h"
#ifdef __cplusplus
extern "C" {
#endif

/** PMU_HP_ACTIVE_DIG_POWER_REG register
 *  need_des
 */
#define PMU_HP_ACTIVE_DIG_POWER_REG (DR_REG_PMU_BASE + 0x0)
/** PMU_HP_ACTIVE_VDD_SPI_PD_EN : R/W; bitpos: [21]; default: 0;
 *  need_des
 */
#define PMU_HP_ACTIVE_VDD_SPI_PD_EN    (BIT(21))
#define PMU_HP_ACTIVE_VDD_SPI_PD_EN_M  (PMU_HP_ACTIVE_VDD_SPI_PD_EN_V << PMU_HP_ACTIVE_VDD_SPI_PD_EN_S)
#define PMU_HP_ACTIVE_VDD_SPI_PD_EN_V  0x00000001U
#define PMU_HP_ACTIVE_VDD_SPI_PD_EN_S  21
/** PMU_HP_ACTIVE_HP_MEM_DSLP : R/W; bitpos: [22]; default: 0;
 *  need_des
 */
#define PMU_HP_ACTIVE_HP_MEM_DSLP    (BIT(22))
#define PMU_HP_ACTIVE_HP_MEM_DSLP_M  (PMU_HP_ACTIVE_HP_MEM_DSLP_V << PMU_HP_ACTIVE_HP_MEM_DSLP_S)
#define PMU_HP_ACTIVE_HP_MEM_DSLP_V  0x00000001U
#define PMU_HP_ACTIVE_HP_MEM_DSLP_S  22
/** PMU_HP_ACTIVE_PD_HP_MEM_PD_EN : R/W; bitpos: [26:23]; default: 0;
 *  need_des
 */
#define PMU_HP_ACTIVE_PD_HP_MEM_PD_EN    0x0000000FU
#define PMU_HP_ACTIVE_PD_HP_MEM_PD_EN_M  (PMU_HP_ACTIVE_PD_HP_MEM_PD_EN_V << PMU_HP_ACTIVE_PD_HP_MEM_PD_EN_S)
#define PMU_HP_ACTIVE_PD_HP_MEM_PD_EN_V  0x0000000FU
#define PMU_HP_ACTIVE_PD_HP_MEM_PD_EN_S  23
/** PMU_HP_ACTIVE_PD_HP_WIFI_PD_EN : R/W; bitpos: [27]; default: 0;
 *  need_des
 */
#define PMU_HP_ACTIVE_PD_HP_WIFI_PD_EN    (BIT(27))
#define PMU_HP_ACTIVE_PD_HP_WIFI_PD_EN_M  (PMU_HP_ACTIVE_PD_HP_WIFI_PD_EN_V << PMU_HP_ACTIVE_PD_HP_WIFI_PD_EN_S)
#define PMU_HP_ACTIVE_PD_HP_WIFI_PD_EN_V  0x00000001U
#define PMU_HP_ACTIVE_PD_HP_WIFI_PD_EN_S  27
/** PMU_HP_ACTIVE_PD_HP_CPU_PD_EN : R/W; bitpos: [29]; default: 0;
 *  need_des
 */
#define PMU_HP_ACTIVE_PD_HP_CPU_PD_EN    (BIT(29))
#define PMU_HP_ACTIVE_PD_HP_CPU_PD_EN_M  (PMU_HP_ACTIVE_PD_HP_CPU_PD_EN_V << PMU_HP_ACTIVE_PD_HP_CPU_PD_EN_S)
#define PMU_HP_ACTIVE_PD_HP_CPU_PD_EN_V  0x00000001U
#define PMU_HP_ACTIVE_PD_HP_CPU_PD_EN_S  29
/** PMU_HP_ACTIVE_PD_HP_AON_PD_EN : R/W; bitpos: [30]; default: 0;
 *  need_des
 */
#define PMU_HP_ACTIVE_PD_HP_AON_PD_EN    (BIT(30))
#define PMU_HP_ACTIVE_PD_HP_AON_PD_EN_M  (PMU_HP_ACTIVE_PD_HP_AON_PD_EN_V << PMU_HP_ACTIVE_PD_HP_AON_PD_EN_S)
#define PMU_HP_ACTIVE_PD_HP_AON_PD_EN_V  0x00000001U
#define PMU_HP_ACTIVE_PD_HP_AON_PD_EN_S  30
/** PMU_HP_ACTIVE_PD_TOP_PD_EN : R/W; bitpos: [31]; default: 0;
 *  need_des
 */
#define PMU_HP_ACTIVE_PD_TOP_PD_EN    (BIT(31))
#define PMU_HP_ACTIVE_PD_TOP_PD_EN_M  (PMU_HP_ACTIVE_PD_TOP_PD_EN_V << PMU_HP_ACTIVE_PD_TOP_PD_EN_S)
#define PMU_HP_ACTIVE_PD_TOP_PD_EN_V  0x00000001U
#define PMU_HP_ACTIVE_PD_TOP_PD_EN_S  31

/** PMU_HP_ACTIVE_ICG_HP_FUNC_REG register
 *  need_des
 */
#define PMU_HP_ACTIVE_ICG_HP_FUNC_REG (DR_REG_PMU_BASE + 0x4)
/** PMU_HP_ACTIVE_DIG_ICG_FUNC_EN : R/W; bitpos: [31:0]; default: 4294967295;
 *  need_des
 */
#define PMU_HP_ACTIVE_DIG_ICG_FUNC_EN    0xFFFFFFFFU
#define PMU_HP_ACTIVE_DIG_ICG_FUNC_EN_M  (PMU_HP_ACTIVE_DIG_ICG_FUNC_EN_V << PMU_HP_ACTIVE_DIG_ICG_FUNC_EN_S)
#define PMU_HP_ACTIVE_DIG_ICG_FUNC_EN_V  0xFFFFFFFFU
#define PMU_HP_ACTIVE_DIG_ICG_FUNC_EN_S  0

/** PMU_HP_ACTIVE_ICG_HP_APB_REG register
 *  need_des
 */
#define PMU_HP_ACTIVE_ICG_HP_APB_REG (DR_REG_PMU_BASE + 0x8)
/** PMU_HP_ACTIVE_DIG_ICG_APB_EN : R/W; bitpos: [31:0]; default: 4294967295;
 *  need_des
 */
#define PMU_HP_ACTIVE_DIG_ICG_APB_EN    0xFFFFFFFFU
#define PMU_HP_ACTIVE_DIG_ICG_APB_EN_M  (PMU_HP_ACTIVE_DIG_ICG_APB_EN_V << PMU_HP_ACTIVE_DIG_ICG_APB_EN_S)
#define PMU_HP_ACTIVE_DIG_ICG_APB_EN_V  0xFFFFFFFFU
#define PMU_HP_ACTIVE_DIG_ICG_APB_EN_S  0

/** PMU_HP_ACTIVE_ICG_MODEM_REG register
 *  need_des
 */
#define PMU_HP_ACTIVE_ICG_MODEM_REG (DR_REG_PMU_BASE + 0xc)
/** PMU_HP_ACTIVE_DIG_ICG_MODEM_CODE : R/W; bitpos: [31:30]; default: 0;
 *  need_des
 */
#define PMU_HP_ACTIVE_DIG_ICG_MODEM_CODE    0x00000003U
#define PMU_HP_ACTIVE_DIG_ICG_MODEM_CODE_M  (PMU_HP_ACTIVE_DIG_ICG_MODEM_CODE_V << PMU_HP_ACTIVE_DIG_ICG_MODEM_CODE_S)
#define PMU_HP_ACTIVE_DIG_ICG_MODEM_CODE_V  0x00000003U
#define PMU_HP_ACTIVE_DIG_ICG_MODEM_CODE_S  30

/** PMU_HP_ACTIVE_HP_SYS_CNTL_REG register
 *  need_des
 */
#define PMU_HP_ACTIVE_HP_SYS_CNTL_REG (DR_REG_PMU_BASE + 0x10)
/** PMU_HP_ACTIVE_UART_WAKEUP_EN : R/W; bitpos: [24]; default: 0;
 *  need_des
 */
#define PMU_HP_ACTIVE_UART_WAKEUP_EN    (BIT(24))
#define PMU_HP_ACTIVE_UART_WAKEUP_EN_M  (PMU_HP_ACTIVE_UART_WAKEUP_EN_V << PMU_HP_ACTIVE_UART_WAKEUP_EN_S)
#define PMU_HP_ACTIVE_UART_WAKEUP_EN_V  0x00000001U
#define PMU_HP_ACTIVE_UART_WAKEUP_EN_S  24
/** PMU_HP_ACTIVE_LP_PAD_HOLD_ALL : R/W; bitpos: [25]; default: 0;
 *  need_des
 */
#define PMU_HP_ACTIVE_LP_PAD_HOLD_ALL    (BIT(25))
#define PMU_HP_ACTIVE_LP_PAD_HOLD_ALL_M  (PMU_HP_ACTIVE_LP_PAD_HOLD_ALL_V << PMU_HP_ACTIVE_LP_PAD_HOLD_ALL_S)
#define PMU_HP_ACTIVE_LP_PAD_HOLD_ALL_V  0x00000001U
#define PMU_HP_ACTIVE_LP_PAD_HOLD_ALL_S  25
/** PMU_HP_ACTIVE_HP_PAD_HOLD_ALL : R/W; bitpos: [26]; default: 0;
 *  need_des
 */
#define PMU_HP_ACTIVE_HP_PAD_HOLD_ALL    (BIT(26))
#define PMU_HP_ACTIVE_HP_PAD_HOLD_ALL_M  (PMU_HP_ACTIVE_HP_PAD_HOLD_ALL_V << PMU_HP_ACTIVE_HP_PAD_HOLD_ALL_S)
#define PMU_HP_ACTIVE_HP_PAD_HOLD_ALL_V  0x00000001U
#define PMU_HP_ACTIVE_HP_PAD_HOLD_ALL_S  26
/** PMU_HP_ACTIVE_DIG_PAD_SLP_SEL : R/W; bitpos: [27]; default: 0;
 *  need_des
 */
#define PMU_HP_ACTIVE_DIG_PAD_SLP_SEL    (BIT(27))
#define PMU_HP_ACTIVE_DIG_PAD_SLP_SEL_M  (PMU_HP_ACTIVE_DIG_PAD_SLP_SEL_V << PMU_HP_ACTIVE_DIG_PAD_SLP_SEL_S)
#define PMU_HP_ACTIVE_DIG_PAD_SLP_SEL_V  0x00000001U
#define PMU_HP_ACTIVE_DIG_PAD_SLP_SEL_S  27
/** PMU_HP_ACTIVE_DIG_PAUSE_WDT : R/W; bitpos: [28]; default: 0;
 *  need_des
 */
#define PMU_HP_ACTIVE_DIG_PAUSE_WDT    (BIT(28))
#define PMU_HP_ACTIVE_DIG_PAUSE_WDT_M  (PMU_HP_ACTIVE_DIG_PAUSE_WDT_V << PMU_HP_ACTIVE_DIG_PAUSE_WDT_S)
#define PMU_HP_ACTIVE_DIG_PAUSE_WDT_V  0x00000001U
#define PMU_HP_ACTIVE_DIG_PAUSE_WDT_S  28
/** PMU_HP_ACTIVE_DIG_CPU_STALL : R/W; bitpos: [29]; default: 0;
 *  need_des
 */
#define PMU_HP_ACTIVE_DIG_CPU_STALL    (BIT(29))
#define PMU_HP_ACTIVE_DIG_CPU_STALL_M  (PMU_HP_ACTIVE_DIG_CPU_STALL_V << PMU_HP_ACTIVE_DIG_CPU_STALL_S)
#define PMU_HP_ACTIVE_DIG_CPU_STALL_V  0x00000001U
#define PMU_HP_ACTIVE_DIG_CPU_STALL_S  29

/** PMU_HP_ACTIVE_HP_CK_POWER_REG register
 *  need_des
 */
#define PMU_HP_ACTIVE_HP_CK_POWER_REG (DR_REG_PMU_BASE + 0x14)
/** PMU_HP_ACTIVE_I2C_ISO_EN : R/W; bitpos: [26]; default: 0;
 *  need_des
 */
#define PMU_HP_ACTIVE_I2C_ISO_EN    (BIT(26))
#define PMU_HP_ACTIVE_I2C_ISO_EN_M  (PMU_HP_ACTIVE_I2C_ISO_EN_V << PMU_HP_ACTIVE_I2C_ISO_EN_S)
#define PMU_HP_ACTIVE_I2C_ISO_EN_V  0x00000001U
#define PMU_HP_ACTIVE_I2C_ISO_EN_S  26
/** PMU_HP_ACTIVE_I2C_RETENTION : R/W; bitpos: [27]; default: 0;
 *  need_des
 */
#define PMU_HP_ACTIVE_I2C_RETENTION    (BIT(27))
#define PMU_HP_ACTIVE_I2C_RETENTION_M  (PMU_HP_ACTIVE_I2C_RETENTION_V << PMU_HP_ACTIVE_I2C_RETENTION_S)
#define PMU_HP_ACTIVE_I2C_RETENTION_V  0x00000001U
#define PMU_HP_ACTIVE_I2C_RETENTION_S  27
/** PMU_HP_ACTIVE_XPD_BB_I2C : R/W; bitpos: [28]; default: 0;
 *  need_des
 */
#define PMU_HP_ACTIVE_XPD_BB_I2C    (BIT(28))
#define PMU_HP_ACTIVE_XPD_BB_I2C_M  (PMU_HP_ACTIVE_XPD_BB_I2C_V << PMU_HP_ACTIVE_XPD_BB_I2C_S)
#define PMU_HP_ACTIVE_XPD_BB_I2C_V  0x00000001U
#define PMU_HP_ACTIVE_XPD_BB_I2C_S  28
/** PMU_HP_ACTIVE_XPD_BBPLL_I2C : R/W; bitpos: [29]; default: 0;
 *  need_des
 */
#define PMU_HP_ACTIVE_XPD_BBPLL_I2C    (BIT(29))
#define PMU_HP_ACTIVE_XPD_BBPLL_I2C_M  (PMU_HP_ACTIVE_XPD_BBPLL_I2C_V << PMU_HP_ACTIVE_XPD_BBPLL_I2C_S)
#define PMU_HP_ACTIVE_XPD_BBPLL_I2C_V  0x00000001U
#define PMU_HP_ACTIVE_XPD_BBPLL_I2C_S  29
/** PMU_HP_ACTIVE_XPD_BBPLL : R/W; bitpos: [30]; default: 0;
 *  need_des
 */
#define PMU_HP_ACTIVE_XPD_BBPLL    (BIT(30))
#define PMU_HP_ACTIVE_XPD_BBPLL_M  (PMU_HP_ACTIVE_XPD_BBPLL_V << PMU_HP_ACTIVE_XPD_BBPLL_S)
#define PMU_HP_ACTIVE_XPD_BBPLL_V  0x00000001U
#define PMU_HP_ACTIVE_XPD_BBPLL_S  30

/** PMU_HP_ACTIVE_BIAS_REG register
 *  need_des
 */
#define PMU_HP_ACTIVE_BIAS_REG (DR_REG_PMU_BASE + 0x18)
/** PMU_HP_ACTIVE_XPD_TRX : R/W; bitpos: [24]; default: 1;
 *  need_des
 */
#define PMU_HP_ACTIVE_XPD_TRX    (BIT(24))
#define PMU_HP_ACTIVE_XPD_TRX_M  (PMU_HP_ACTIVE_XPD_TRX_V << PMU_HP_ACTIVE_XPD_TRX_S)
#define PMU_HP_ACTIVE_XPD_TRX_V  0x00000001U
#define PMU_HP_ACTIVE_XPD_TRX_S  24
/** PMU_HP_ACTIVE_XPD_BIAS : R/W; bitpos: [25]; default: 0;
 *  need_des
 */
#define PMU_HP_ACTIVE_XPD_BIAS    (BIT(25))
#define PMU_HP_ACTIVE_XPD_BIAS_M  (PMU_HP_ACTIVE_XPD_BIAS_V << PMU_HP_ACTIVE_XPD_BIAS_S)
#define PMU_HP_ACTIVE_XPD_BIAS_V  0x00000001U
#define PMU_HP_ACTIVE_XPD_BIAS_S  25
/** PMU_HP_ACTIVE_PD_CUR : R/W; bitpos: [30]; default: 0;
 *  need_des
 */
#define PMU_HP_ACTIVE_PD_CUR    (BIT(30))
#define PMU_HP_ACTIVE_PD_CUR_M  (PMU_HP_ACTIVE_PD_CUR_V << PMU_HP_ACTIVE_PD_CUR_S)
#define PMU_HP_ACTIVE_PD_CUR_V  0x00000001U
#define PMU_HP_ACTIVE_PD_CUR_S  30
/** PMU_HP_ACTIVE_BIAS_SLEEP : R/W; bitpos: [31]; default: 0;
 *  need_des
 */
#define PMU_HP_ACTIVE_BIAS_SLEEP    (BIT(31))
#define PMU_HP_ACTIVE_BIAS_SLEEP_M  (PMU_HP_ACTIVE_BIAS_SLEEP_V << PMU_HP_ACTIVE_BIAS_SLEEP_S)
#define PMU_HP_ACTIVE_BIAS_SLEEP_V  0x00000001U
#define PMU_HP_ACTIVE_BIAS_SLEEP_S  31

/** PMU_HP_ACTIVE_BACKUP_REG register
 *  need_des
 */
#define PMU_HP_ACTIVE_BACKUP_REG (DR_REG_PMU_BASE + 0x1c)
/** PMU_HP_SLEEP2ACTIVE_BACKUP_MODEM_CLK_CODE : R/W; bitpos: [5:4]; default: 0;
 *  need_des
 */
#define PMU_HP_SLEEP2ACTIVE_BACKUP_MODEM_CLK_CODE    0x00000003U
#define PMU_HP_SLEEP2ACTIVE_BACKUP_MODEM_CLK_CODE_M  (PMU_HP_SLEEP2ACTIVE_BACKUP_MODEM_CLK_CODE_V << PMU_HP_SLEEP2ACTIVE_BACKUP_MODEM_CLK_CODE_S)
#define PMU_HP_SLEEP2ACTIVE_BACKUP_MODEM_CLK_CODE_V  0x00000003U
#define PMU_HP_SLEEP2ACTIVE_BACKUP_MODEM_CLK_CODE_S  4
/** PMU_HP_MODEM2ACTIVE_BACKUP_MODEM_CLK_CODE : R/W; bitpos: [7:6]; default: 0;
 *  need_des
 */
#define PMU_HP_MODEM2ACTIVE_BACKUP_MODEM_CLK_CODE    0x00000003U
#define PMU_HP_MODEM2ACTIVE_BACKUP_MODEM_CLK_CODE_M  (PMU_HP_MODEM2ACTIVE_BACKUP_MODEM_CLK_CODE_V << PMU_HP_MODEM2ACTIVE_BACKUP_MODEM_CLK_CODE_S)
#define PMU_HP_MODEM2ACTIVE_BACKUP_MODEM_CLK_CODE_V  0x00000003U
#define PMU_HP_MODEM2ACTIVE_BACKUP_MODEM_CLK_CODE_S  6
/** PMU_HP_ACTIVE_RETENTION_MODE : R/W; bitpos: [10]; default: 0;
 *  need_des
 */
#define PMU_HP_ACTIVE_RETENTION_MODE    (BIT(10))
#define PMU_HP_ACTIVE_RETENTION_MODE_M  (PMU_HP_ACTIVE_RETENTION_MODE_V << PMU_HP_ACTIVE_RETENTION_MODE_S)
#define PMU_HP_ACTIVE_RETENTION_MODE_V  0x00000001U
#define PMU_HP_ACTIVE_RETENTION_MODE_S  10
/** PMU_HP_SLEEP2ACTIVE_RETENTION_EN : R/W; bitpos: [11]; default: 0;
 *  need_des
 */
#define PMU_HP_SLEEP2ACTIVE_RETENTION_EN    (BIT(11))
#define PMU_HP_SLEEP2ACTIVE_RETENTION_EN_M  (PMU_HP_SLEEP2ACTIVE_RETENTION_EN_V << PMU_HP_SLEEP2ACTIVE_RETENTION_EN_S)
#define PMU_HP_SLEEP2ACTIVE_RETENTION_EN_V  0x00000001U
#define PMU_HP_SLEEP2ACTIVE_RETENTION_EN_S  11
/** PMU_HP_MODEM2ACTIVE_RETENTION_EN : R/W; bitpos: [12]; default: 0;
 *  need_des
 */
#define PMU_HP_MODEM2ACTIVE_RETENTION_EN    (BIT(12))
#define PMU_HP_MODEM2ACTIVE_RETENTION_EN_M  (PMU_HP_MODEM2ACTIVE_RETENTION_EN_V << PMU_HP_MODEM2ACTIVE_RETENTION_EN_S)
#define PMU_HP_MODEM2ACTIVE_RETENTION_EN_V  0x00000001U
#define PMU_HP_MODEM2ACTIVE_RETENTION_EN_S  12
/** PMU_HP_SLEEP2ACTIVE_BACKUP_CLK_SEL : R/W; bitpos: [15:14]; default: 0;
 *  need_des
 */
#define PMU_HP_SLEEP2ACTIVE_BACKUP_CLK_SEL    0x00000003U
#define PMU_HP_SLEEP2ACTIVE_BACKUP_CLK_SEL_M  (PMU_HP_SLEEP2ACTIVE_BACKUP_CLK_SEL_V << PMU_HP_SLEEP2ACTIVE_BACKUP_CLK_SEL_S)
#define PMU_HP_SLEEP2ACTIVE_BACKUP_CLK_SEL_V  0x00000003U
#define PMU_HP_SLEEP2ACTIVE_BACKUP_CLK_SEL_S  14
/** PMU_HP_MODEM2ACTIVE_BACKUP_CLK_SEL : R/W; bitpos: [17:16]; default: 0;
 *  need_des
 */
#define PMU_HP_MODEM2ACTIVE_BACKUP_CLK_SEL    0x00000003U
#define PMU_HP_MODEM2ACTIVE_BACKUP_CLK_SEL_M  (PMU_HP_MODEM2ACTIVE_BACKUP_CLK_SEL_V << PMU_HP_MODEM2ACTIVE_BACKUP_CLK_SEL_S)
#define PMU_HP_MODEM2ACTIVE_BACKUP_CLK_SEL_V  0x00000003U
#define PMU_HP_MODEM2ACTIVE_BACKUP_CLK_SEL_S  16
/** PMU_HP_SLEEP2ACTIVE_BACKUP_MODE : R/W; bitpos: [22:20]; default: 0;
 *  need_des
 */
#define PMU_HP_SLEEP2ACTIVE_BACKUP_MODE    0x00000007U
#define PMU_HP_SLEEP2ACTIVE_BACKUP_MODE_M  (PMU_HP_SLEEP2ACTIVE_BACKUP_MODE_V << PMU_HP_SLEEP2ACTIVE_BACKUP_MODE_S)
#define PMU_HP_SLEEP2ACTIVE_BACKUP_MODE_V  0x00000007U
#define PMU_HP_SLEEP2ACTIVE_BACKUP_MODE_S  20
/** PMU_HP_MODEM2ACTIVE_BACKUP_MODE : R/W; bitpos: [25:23]; default: 0;
 *  need_des
 */
#define PMU_HP_MODEM2ACTIVE_BACKUP_MODE    0x00000007U
#define PMU_HP_MODEM2ACTIVE_BACKUP_MODE_M  (PMU_HP_MODEM2ACTIVE_BACKUP_MODE_V << PMU_HP_MODEM2ACTIVE_BACKUP_MODE_S)
#define PMU_HP_MODEM2ACTIVE_BACKUP_MODE_V  0x00000007U
#define PMU_HP_MODEM2ACTIVE_BACKUP_MODE_S  23
/** PMU_HP_SLEEP2ACTIVE_BACKUP_EN : R/W; bitpos: [29]; default: 0;
 *  need_des
 */
#define PMU_HP_SLEEP2ACTIVE_BACKUP_EN    (BIT(29))
#define PMU_HP_SLEEP2ACTIVE_BACKUP_EN_M  (PMU_HP_SLEEP2ACTIVE_BACKUP_EN_V << PMU_HP_SLEEP2ACTIVE_BACKUP_EN_S)
#define PMU_HP_SLEEP2ACTIVE_BACKUP_EN_V  0x00000001U
#define PMU_HP_SLEEP2ACTIVE_BACKUP_EN_S  29
/** PMU_HP_MODEM2ACTIVE_BACKUP_EN : R/W; bitpos: [30]; default: 0;
 *  need_des
 */
#define PMU_HP_MODEM2ACTIVE_BACKUP_EN    (BIT(30))
#define PMU_HP_MODEM2ACTIVE_BACKUP_EN_M  (PMU_HP_MODEM2ACTIVE_BACKUP_EN_V << PMU_HP_MODEM2ACTIVE_BACKUP_EN_S)
#define PMU_HP_MODEM2ACTIVE_BACKUP_EN_V  0x00000001U
#define PMU_HP_MODEM2ACTIVE_BACKUP_EN_S  30

/** PMU_HP_ACTIVE_BACKUP_CLK_REG register
 *  need_des
 */
#define PMU_HP_ACTIVE_BACKUP_CLK_REG (DR_REG_PMU_BASE + 0x20)
/** PMU_HP_ACTIVE_BACKUP_ICG_FUNC_EN : R/W; bitpos: [31:0]; default: 0;
 *  need_des
 */
#define PMU_HP_ACTIVE_BACKUP_ICG_FUNC_EN    0xFFFFFFFFU
#define PMU_HP_ACTIVE_BACKUP_ICG_FUNC_EN_M  (PMU_HP_ACTIVE_BACKUP_ICG_FUNC_EN_V << PMU_HP_ACTIVE_BACKUP_ICG_FUNC_EN_S)
#define PMU_HP_ACTIVE_BACKUP_ICG_FUNC_EN_V  0xFFFFFFFFU
#define PMU_HP_ACTIVE_BACKUP_ICG_FUNC_EN_S  0

/** PMU_HP_ACTIVE_SYSCLK_REG register
 *  need_des
 */
#define PMU_HP_ACTIVE_SYSCLK_REG (DR_REG_PMU_BASE + 0x24)
/** PMU_HP_ACTIVE_DIG_SYS_CLK_NO_DIV : R/W; bitpos: [26]; default: 0;
 *  need_des
 */
#define PMU_HP_ACTIVE_DIG_SYS_CLK_NO_DIV    (BIT(26))
#define PMU_HP_ACTIVE_DIG_SYS_CLK_NO_DIV_M  (PMU_HP_ACTIVE_DIG_SYS_CLK_NO_DIV_V << PMU_HP_ACTIVE_DIG_SYS_CLK_NO_DIV_S)
#define PMU_HP_ACTIVE_DIG_SYS_CLK_NO_DIV_V  0x00000001U
#define PMU_HP_ACTIVE_DIG_SYS_CLK_NO_DIV_S  26
/** PMU_HP_ACTIVE_ICG_SYS_CLOCK_EN : R/W; bitpos: [27]; default: 0;
 *  need_des
 */
#define PMU_HP_ACTIVE_ICG_SYS_CLOCK_EN    (BIT(27))
#define PMU_HP_ACTIVE_ICG_SYS_CLOCK_EN_M  (PMU_HP_ACTIVE_ICG_SYS_CLOCK_EN_V << PMU_HP_ACTIVE_ICG_SYS_CLOCK_EN_S)
#define PMU_HP_ACTIVE_ICG_SYS_CLOCK_EN_V  0x00000001U
#define PMU_HP_ACTIVE_ICG_SYS_CLOCK_EN_S  27
/** PMU_HP_ACTIVE_SYS_CLK_SLP_SEL : R/W; bitpos: [28]; default: 0;
 *  need_des
 */
#define PMU_HP_ACTIVE_SYS_CLK_SLP_SEL    (BIT(28))
#define PMU_HP_ACTIVE_SYS_CLK_SLP_SEL_M  (PMU_HP_ACTIVE_SYS_CLK_SLP_SEL_V << PMU_HP_ACTIVE_SYS_CLK_SLP_SEL_S)
#define PMU_HP_ACTIVE_SYS_CLK_SLP_SEL_V  0x00000001U
#define PMU_HP_ACTIVE_SYS_CLK_SLP_SEL_S  28
/** PMU_HP_ACTIVE_ICG_SLP_SEL : R/W; bitpos: [29]; default: 0;
 *  need_des
 */
#define PMU_HP_ACTIVE_ICG_SLP_SEL    (BIT(29))
#define PMU_HP_ACTIVE_ICG_SLP_SEL_M  (PMU_HP_ACTIVE_ICG_SLP_SEL_V << PMU_HP_ACTIVE_ICG_SLP_SEL_S)
#define PMU_HP_ACTIVE_ICG_SLP_SEL_V  0x00000001U
#define PMU_HP_ACTIVE_ICG_SLP_SEL_S  29
/** PMU_HP_ACTIVE_DIG_SYS_CLK_SEL : R/W; bitpos: [31:30]; default: 0;
 *  need_des
 */
#define PMU_HP_ACTIVE_DIG_SYS_CLK_SEL    0x00000003U
#define PMU_HP_ACTIVE_DIG_SYS_CLK_SEL_M  (PMU_HP_ACTIVE_DIG_SYS_CLK_SEL_V << PMU_HP_ACTIVE_DIG_SYS_CLK_SEL_S)
#define PMU_HP_ACTIVE_DIG_SYS_CLK_SEL_V  0x00000003U
#define PMU_HP_ACTIVE_DIG_SYS_CLK_SEL_S  30

/** PMU_HP_ACTIVE_HP_REGULATOR0_REG register
 *  need_des
 */
#define PMU_HP_ACTIVE_HP_REGULATOR0_REG (DR_REG_PMU_BASE + 0x28)
/** PMU_HP_ACTIVE_HP_POWER_DET_BYPASS : R/W; bitpos: [0]; default: 0;
 *  need_des
 */
#define PMU_HP_ACTIVE_HP_POWER_DET_BYPASS    (BIT(0))
#define PMU_HP_ACTIVE_HP_POWER_DET_BYPASS_M  (PMU_HP_ACTIVE_HP_POWER_DET_BYPASS_V << PMU_HP_ACTIVE_HP_POWER_DET_BYPASS_S)
#define PMU_HP_ACTIVE_HP_POWER_DET_BYPASS_V  0x00000001U
#define PMU_HP_ACTIVE_HP_POWER_DET_BYPASS_S  0
/** PMU_LP_DBIAS_VOL : RO; bitpos: [8:4]; default: 17;
 *  need_des
 */
#define PMU_LP_DBIAS_VOL    0x0000001FU
#define PMU_LP_DBIAS_VOL_M  (PMU_LP_DBIAS_VOL_V << PMU_LP_DBIAS_VOL_S)
#define PMU_LP_DBIAS_VOL_V  0x0000001FU
#define PMU_LP_DBIAS_VOL_S  4
/** PMU_HP_DBIAS_VOL : RO; bitpos: [13:9]; default: 16;
 *  need_des
 */
#define PMU_HP_DBIAS_VOL    0x0000001FU
#define PMU_HP_DBIAS_VOL_M  (PMU_HP_DBIAS_VOL_V << PMU_HP_DBIAS_VOL_S)
#define PMU_HP_DBIAS_VOL_V  0x0000001FU
#define PMU_HP_DBIAS_VOL_S  9
/** PMU_DIG_REGULATOR0_DBIAS_SEL : R/W; bitpos: [14]; default: 1;
 *  need_des
 */
#define PMU_DIG_REGULATOR0_DBIAS_SEL    (BIT(14))
#define PMU_DIG_REGULATOR0_DBIAS_SEL_M  (PMU_DIG_REGULATOR0_DBIAS_SEL_V << PMU_DIG_REGULATOR0_DBIAS_SEL_S)
#define PMU_DIG_REGULATOR0_DBIAS_SEL_V  0x00000001U
#define PMU_DIG_REGULATOR0_DBIAS_SEL_S  14
/** PMU_DIG_DBIAS_INIT : WT; bitpos: [15]; default: 0;
 *  need_des
 */
#define PMU_DIG_DBIAS_INIT    (BIT(15))
#define PMU_DIG_DBIAS_INIT_M  (PMU_DIG_DBIAS_INIT_V << PMU_DIG_DBIAS_INIT_S)
#define PMU_DIG_DBIAS_INIT_V  0x00000001U
#define PMU_DIG_DBIAS_INIT_S  15
/** PMU_HP_ACTIVE_HP_REGULATOR_SLP_MEM_XPD : R/W; bitpos: [16]; default: 1;
 *  need_des
 */
#define PMU_HP_ACTIVE_HP_REGULATOR_SLP_MEM_XPD    (BIT(16))
#define PMU_HP_ACTIVE_HP_REGULATOR_SLP_MEM_XPD_M  (PMU_HP_ACTIVE_HP_REGULATOR_SLP_MEM_XPD_V << PMU_HP_ACTIVE_HP_REGULATOR_SLP_MEM_XPD_S)
#define PMU_HP_ACTIVE_HP_REGULATOR_SLP_MEM_XPD_V  0x00000001U
#define PMU_HP_ACTIVE_HP_REGULATOR_SLP_MEM_XPD_S  16
/** PMU_HP_ACTIVE_HP_REGULATOR_SLP_LOGIC_XPD : R/W; bitpos: [17]; default: 1;
 *  need_des
 */
#define PMU_HP_ACTIVE_HP_REGULATOR_SLP_LOGIC_XPD    (BIT(17))
#define PMU_HP_ACTIVE_HP_REGULATOR_SLP_LOGIC_XPD_M  (PMU_HP_ACTIVE_HP_REGULATOR_SLP_LOGIC_XPD_V << PMU_HP_ACTIVE_HP_REGULATOR_SLP_LOGIC_XPD_S)
#define PMU_HP_ACTIVE_HP_REGULATOR_SLP_LOGIC_XPD_V  0x00000001U
#define PMU_HP_ACTIVE_HP_REGULATOR_SLP_LOGIC_XPD_S  17
/** PMU_HP_ACTIVE_HP_REGULATOR_XPD : R/W; bitpos: [18]; default: 1;
 *  need_des
 */
#define PMU_HP_ACTIVE_HP_REGULATOR_XPD    (BIT(18))
#define PMU_HP_ACTIVE_HP_REGULATOR_XPD_M  (PMU_HP_ACTIVE_HP_REGULATOR_XPD_V << PMU_HP_ACTIVE_HP_REGULATOR_XPD_S)
#define PMU_HP_ACTIVE_HP_REGULATOR_XPD_V  0x00000001U
#define PMU_HP_ACTIVE_HP_REGULATOR_XPD_S  18
/** PMU_HP_ACTIVE_HP_REGULATOR_SLP_MEM_DBIAS : R/W; bitpos: [22:19]; default: 8;
 *  need_des
 */
#define PMU_HP_ACTIVE_HP_REGULATOR_SLP_MEM_DBIAS    0x0000000FU
#define PMU_HP_ACTIVE_HP_REGULATOR_SLP_MEM_DBIAS_M  (PMU_HP_ACTIVE_HP_REGULATOR_SLP_MEM_DBIAS_V << PMU_HP_ACTIVE_HP_REGULATOR_SLP_MEM_DBIAS_S)
#define PMU_HP_ACTIVE_HP_REGULATOR_SLP_MEM_DBIAS_V  0x0000000FU
#define PMU_HP_ACTIVE_HP_REGULATOR_SLP_MEM_DBIAS_S  19
/** PMU_HP_ACTIVE_HP_REGULATOR_SLP_LOGIC_DBIAS : R/W; bitpos: [26:23]; default: 8;
 *  need_des
 */
#define PMU_HP_ACTIVE_HP_REGULATOR_SLP_LOGIC_DBIAS    0x0000000FU
#define PMU_HP_ACTIVE_HP_REGULATOR_SLP_LOGIC_DBIAS_M  (PMU_HP_ACTIVE_HP_REGULATOR_SLP_LOGIC_DBIAS_V << PMU_HP_ACTIVE_HP_REGULATOR_SLP_LOGIC_DBIAS_S)
#define PMU_HP_ACTIVE_HP_REGULATOR_SLP_LOGIC_DBIAS_V  0x0000000FU
#define PMU_HP_ACTIVE_HP_REGULATOR_SLP_LOGIC_DBIAS_S  23
/** PMU_HP_ACTIVE_HP_REGULATOR_DBIAS : R/W; bitpos: [31:27]; default: 16;
 *  need_des
 */
#define PMU_HP_ACTIVE_HP_REGULATOR_DBIAS    0x0000001FU
#define PMU_HP_ACTIVE_HP_REGULATOR_DBIAS_M  (PMU_HP_ACTIVE_HP_REGULATOR_DBIAS_V << PMU_HP_ACTIVE_HP_REGULATOR_DBIAS_S)
#define PMU_HP_ACTIVE_HP_REGULATOR_DBIAS_V  0x0000001FU
#define PMU_HP_ACTIVE_HP_REGULATOR_DBIAS_S  27

/** PMU_HP_ACTIVE_HP_REGULATOR1_REG register
 *  need_des
 */
#define PMU_HP_ACTIVE_HP_REGULATOR1_REG (DR_REG_PMU_BASE + 0x2c)
/** PMU_HP_ACTIVE_HP_REGULATOR_DRV_B : R/W; bitpos: [31:8]; default: 0;
 *  need_des
 */
#define PMU_HP_ACTIVE_HP_REGULATOR_DRV_B    0x00FFFFFFU
#define PMU_HP_ACTIVE_HP_REGULATOR_DRV_B_M  (PMU_HP_ACTIVE_HP_REGULATOR_DRV_B_V << PMU_HP_ACTIVE_HP_REGULATOR_DRV_B_S)
#define PMU_HP_ACTIVE_HP_REGULATOR_DRV_B_V  0x00FFFFFFU
#define PMU_HP_ACTIVE_HP_REGULATOR_DRV_B_S  8

/** PMU_HP_ACTIVE_XTAL_REG register
 *  need_des
 */
#define PMU_HP_ACTIVE_XTAL_REG (DR_REG_PMU_BASE + 0x30)
/** PMU_HP_ACTIVE_XPD_XTAL : R/W; bitpos: [31]; default: 1;
 *  need_des
 */
#define PMU_HP_ACTIVE_XPD_XTAL    (BIT(31))
#define PMU_HP_ACTIVE_XPD_XTAL_M  (PMU_HP_ACTIVE_XPD_XTAL_V << PMU_HP_ACTIVE_XPD_XTAL_S)
#define PMU_HP_ACTIVE_XPD_XTAL_V  0x00000001U
#define PMU_HP_ACTIVE_XPD_XTAL_S  31

/** PMU_HP_MODEM_DIG_POWER_REG register
 *  need_des
 */
#define PMU_HP_MODEM_DIG_POWER_REG (DR_REG_PMU_BASE + 0x34)
/** PMU_HP_MODEM_VDD_SPI_PD_EN : R/W; bitpos: [21]; default: 0;
 *  need_des
 */
#define PMU_HP_MODEM_VDD_SPI_PD_EN    (BIT(21))
#define PMU_HP_MODEM_VDD_SPI_PD_EN_M  (PMU_HP_MODEM_VDD_SPI_PD_EN_V << PMU_HP_MODEM_VDD_SPI_PD_EN_S)
#define PMU_HP_MODEM_VDD_SPI_PD_EN_V  0x00000001U
#define PMU_HP_MODEM_VDD_SPI_PD_EN_S  21
/** PMU_HP_MODEM_HP_MEM_DSLP : R/W; bitpos: [22]; default: 0;
 *  need_des
 */
#define PMU_HP_MODEM_HP_MEM_DSLP    (BIT(22))
#define PMU_HP_MODEM_HP_MEM_DSLP_M  (PMU_HP_MODEM_HP_MEM_DSLP_V << PMU_HP_MODEM_HP_MEM_DSLP_S)
#define PMU_HP_MODEM_HP_MEM_DSLP_V  0x00000001U
#define PMU_HP_MODEM_HP_MEM_DSLP_S  22
/** PMU_HP_MODEM_PD_HP_MEM_PD_EN : R/W; bitpos: [26:23]; default: 0;
 *  need_des
 */
#define PMU_HP_MODEM_PD_HP_MEM_PD_EN    0x0000000FU
#define PMU_HP_MODEM_PD_HP_MEM_PD_EN_M  (PMU_HP_MODEM_PD_HP_MEM_PD_EN_V << PMU_HP_MODEM_PD_HP_MEM_PD_EN_S)
#define PMU_HP_MODEM_PD_HP_MEM_PD_EN_V  0x0000000FU
#define PMU_HP_MODEM_PD_HP_MEM_PD_EN_S  23
/** PMU_HP_MODEM_PD_HP_WIFI_PD_EN : R/W; bitpos: [27]; default: 0;
 *  need_des
 */
#define PMU_HP_MODEM_PD_HP_WIFI_PD_EN    (BIT(27))
#define PMU_HP_MODEM_PD_HP_WIFI_PD_EN_M  (PMU_HP_MODEM_PD_HP_WIFI_PD_EN_V << PMU_HP_MODEM_PD_HP_WIFI_PD_EN_S)
#define PMU_HP_MODEM_PD_HP_WIFI_PD_EN_V  0x00000001U
#define PMU_HP_MODEM_PD_HP_WIFI_PD_EN_S  27
/** PMU_HP_MODEM_PD_HP_CPU_PD_EN : R/W; bitpos: [29]; default: 0;
 *  need_des
 */
#define PMU_HP_MODEM_PD_HP_CPU_PD_EN    (BIT(29))
#define PMU_HP_MODEM_PD_HP_CPU_PD_EN_M  (PMU_HP_MODEM_PD_HP_CPU_PD_EN_V << PMU_HP_MODEM_PD_HP_CPU_PD_EN_S)
#define PMU_HP_MODEM_PD_HP_CPU_PD_EN_V  0x00000001U
#define PMU_HP_MODEM_PD_HP_CPU_PD_EN_S  29
/** PMU_HP_MODEM_PD_HP_AON_PD_EN : R/W; bitpos: [30]; default: 0;
 *  need_des
 */
#define PMU_HP_MODEM_PD_HP_AON_PD_EN    (BIT(30))
#define PMU_HP_MODEM_PD_HP_AON_PD_EN_M  (PMU_HP_MODEM_PD_HP_AON_PD_EN_V << PMU_HP_MODEM_PD_HP_AON_PD_EN_S)
#define PMU_HP_MODEM_PD_HP_AON_PD_EN_V  0x00000001U
#define PMU_HP_MODEM_PD_HP_AON_PD_EN_S  30
/** PMU_HP_MODEM_PD_TOP_PD_EN : R/W; bitpos: [31]; default: 0;
 *  need_des
 */
#define PMU_HP_MODEM_PD_TOP_PD_EN    (BIT(31))
#define PMU_HP_MODEM_PD_TOP_PD_EN_M  (PMU_HP_MODEM_PD_TOP_PD_EN_V << PMU_HP_MODEM_PD_TOP_PD_EN_S)
#define PMU_HP_MODEM_PD_TOP_PD_EN_V  0x00000001U
#define PMU_HP_MODEM_PD_TOP_PD_EN_S  31

/** PMU_HP_MODEM_ICG_HP_FUNC_REG register
 *  need_des
 */
#define PMU_HP_MODEM_ICG_HP_FUNC_REG (DR_REG_PMU_BASE + 0x38)
/** PMU_HP_MODEM_DIG_ICG_FUNC_EN : R/W; bitpos: [31:0]; default: 4294967295;
 *  need_des
 */
#define PMU_HP_MODEM_DIG_ICG_FUNC_EN    0xFFFFFFFFU
#define PMU_HP_MODEM_DIG_ICG_FUNC_EN_M  (PMU_HP_MODEM_DIG_ICG_FUNC_EN_V << PMU_HP_MODEM_DIG_ICG_FUNC_EN_S)
#define PMU_HP_MODEM_DIG_ICG_FUNC_EN_V  0xFFFFFFFFU
#define PMU_HP_MODEM_DIG_ICG_FUNC_EN_S  0

/** PMU_HP_MODEM_ICG_HP_APB_REG register
 *  need_des
 */
#define PMU_HP_MODEM_ICG_HP_APB_REG (DR_REG_PMU_BASE + 0x3c)
/** PMU_HP_MODEM_DIG_ICG_APB_EN : R/W; bitpos: [31:0]; default: 4294967295;
 *  need_des
 */
#define PMU_HP_MODEM_DIG_ICG_APB_EN    0xFFFFFFFFU
#define PMU_HP_MODEM_DIG_ICG_APB_EN_M  (PMU_HP_MODEM_DIG_ICG_APB_EN_V << PMU_HP_MODEM_DIG_ICG_APB_EN_S)
#define PMU_HP_MODEM_DIG_ICG_APB_EN_V  0xFFFFFFFFU
#define PMU_HP_MODEM_DIG_ICG_APB_EN_S  0

/** PMU_HP_MODEM_ICG_MODEM_REG register
 *  need_des
 */
#define PMU_HP_MODEM_ICG_MODEM_REG (DR_REG_PMU_BASE + 0x40)
/** PMU_HP_MODEM_DIG_ICG_MODEM_CODE : R/W; bitpos: [31:30]; default: 0;
 *  need_des
 */
#define PMU_HP_MODEM_DIG_ICG_MODEM_CODE    0x00000003U
#define PMU_HP_MODEM_DIG_ICG_MODEM_CODE_M  (PMU_HP_MODEM_DIG_ICG_MODEM_CODE_V << PMU_HP_MODEM_DIG_ICG_MODEM_CODE_S)
#define PMU_HP_MODEM_DIG_ICG_MODEM_CODE_V  0x00000003U
#define PMU_HP_MODEM_DIG_ICG_MODEM_CODE_S  30

/** PMU_HP_MODEM_HP_SYS_CNTL_REG register
 *  need_des
 */
#define PMU_HP_MODEM_HP_SYS_CNTL_REG (DR_REG_PMU_BASE + 0x44)
/** PMU_HP_MODEM_UART_WAKEUP_EN : R/W; bitpos: [24]; default: 0;
 *  need_des
 */
#define PMU_HP_MODEM_UART_WAKEUP_EN    (BIT(24))
#define PMU_HP_MODEM_UART_WAKEUP_EN_M  (PMU_HP_MODEM_UART_WAKEUP_EN_V << PMU_HP_MODEM_UART_WAKEUP_EN_S)
#define PMU_HP_MODEM_UART_WAKEUP_EN_V  0x00000001U
#define PMU_HP_MODEM_UART_WAKEUP_EN_S  24
/** PMU_HP_MODEM_LP_PAD_HOLD_ALL : R/W; bitpos: [25]; default: 0;
 *  need_des
 */
#define PMU_HP_MODEM_LP_PAD_HOLD_ALL    (BIT(25))
#define PMU_HP_MODEM_LP_PAD_HOLD_ALL_M  (PMU_HP_MODEM_LP_PAD_HOLD_ALL_V << PMU_HP_MODEM_LP_PAD_HOLD_ALL_S)
#define PMU_HP_MODEM_LP_PAD_HOLD_ALL_V  0x00000001U
#define PMU_HP_MODEM_LP_PAD_HOLD_ALL_S  25
/** PMU_HP_MODEM_HP_PAD_HOLD_ALL : R/W; bitpos: [26]; default: 0;
 *  need_des
 */
#define PMU_HP_MODEM_HP_PAD_HOLD_ALL    (BIT(26))
#define PMU_HP_MODEM_HP_PAD_HOLD_ALL_M  (PMU_HP_MODEM_HP_PAD_HOLD_ALL_V << PMU_HP_MODEM_HP_PAD_HOLD_ALL_S)
#define PMU_HP_MODEM_HP_PAD_HOLD_ALL_V  0x00000001U
#define PMU_HP_MODEM_HP_PAD_HOLD_ALL_S  26
/** PMU_HP_MODEM_DIG_PAD_SLP_SEL : R/W; bitpos: [27]; default: 0;
 *  need_des
 */
#define PMU_HP_MODEM_DIG_PAD_SLP_SEL    (BIT(27))
#define PMU_HP_MODEM_DIG_PAD_SLP_SEL_M  (PMU_HP_MODEM_DIG_PAD_SLP_SEL_V << PMU_HP_MODEM_DIG_PAD_SLP_SEL_S)
#define PMU_HP_MODEM_DIG_PAD_SLP_SEL_V  0x00000001U
#define PMU_HP_MODEM_DIG_PAD_SLP_SEL_S  27
/** PMU_HP_MODEM_DIG_PAUSE_WDT : R/W; bitpos: [28]; default: 0;
 *  need_des
 */
#define PMU_HP_MODEM_DIG_PAUSE_WDT    (BIT(28))
#define PMU_HP_MODEM_DIG_PAUSE_WDT_M  (PMU_HP_MODEM_DIG_PAUSE_WDT_V << PMU_HP_MODEM_DIG_PAUSE_WDT_S)
#define PMU_HP_MODEM_DIG_PAUSE_WDT_V  0x00000001U
#define PMU_HP_MODEM_DIG_PAUSE_WDT_S  28
/** PMU_HP_MODEM_DIG_CPU_STALL : R/W; bitpos: [29]; default: 0;
 *  need_des
 */
#define PMU_HP_MODEM_DIG_CPU_STALL    (BIT(29))
#define PMU_HP_MODEM_DIG_CPU_STALL_M  (PMU_HP_MODEM_DIG_CPU_STALL_V << PMU_HP_MODEM_DIG_CPU_STALL_S)
#define PMU_HP_MODEM_DIG_CPU_STALL_V  0x00000001U
#define PMU_HP_MODEM_DIG_CPU_STALL_S  29

/** PMU_HP_MODEM_HP_CK_POWER_REG register
 *  need_des
 */
#define PMU_HP_MODEM_HP_CK_POWER_REG (DR_REG_PMU_BASE + 0x48)
/** PMU_HP_MODEM_I2C_ISO_EN : R/W; bitpos: [26]; default: 0;
 *  need_des
 */
#define PMU_HP_MODEM_I2C_ISO_EN    (BIT(26))
#define PMU_HP_MODEM_I2C_ISO_EN_M  (PMU_HP_MODEM_I2C_ISO_EN_V << PMU_HP_MODEM_I2C_ISO_EN_S)
#define PMU_HP_MODEM_I2C_ISO_EN_V  0x00000001U
#define PMU_HP_MODEM_I2C_ISO_EN_S  26
/** PMU_HP_MODEM_I2C_RETENTION : R/W; bitpos: [27]; default: 0;
 *  need_des
 */
#define PMU_HP_MODEM_I2C_RETENTION    (BIT(27))
#define PMU_HP_MODEM_I2C_RETENTION_M  (PMU_HP_MODEM_I2C_RETENTION_V << PMU_HP_MODEM_I2C_RETENTION_S)
#define PMU_HP_MODEM_I2C_RETENTION_V  0x00000001U
#define PMU_HP_MODEM_I2C_RETENTION_S  27
/** PMU_HP_MODEM_XPD_BB_I2C : R/W; bitpos: [28]; default: 0;
 *  need_des
 */
#define PMU_HP_MODEM_XPD_BB_I2C    (BIT(28))
#define PMU_HP_MODEM_XPD_BB_I2C_M  (PMU_HP_MODEM_XPD_BB_I2C_V << PMU_HP_MODEM_XPD_BB_I2C_S)
#define PMU_HP_MODEM_XPD_BB_I2C_V  0x00000001U
#define PMU_HP_MODEM_XPD_BB_I2C_S  28
/** PMU_HP_MODEM_XPD_BBPLL_I2C : R/W; bitpos: [29]; default: 0;
 *  need_des
 */
#define PMU_HP_MODEM_XPD_BBPLL_I2C    (BIT(29))
#define PMU_HP_MODEM_XPD_BBPLL_I2C_M  (PMU_HP_MODEM_XPD_BBPLL_I2C_V << PMU_HP_MODEM_XPD_BBPLL_I2C_S)
#define PMU_HP_MODEM_XPD_BBPLL_I2C_V  0x00000001U
#define PMU_HP_MODEM_XPD_BBPLL_I2C_S  29
/** PMU_HP_MODEM_XPD_BBPLL : R/W; bitpos: [30]; default: 0;
 *  need_des
 */
#define PMU_HP_MODEM_XPD_BBPLL    (BIT(30))
#define PMU_HP_MODEM_XPD_BBPLL_M  (PMU_HP_MODEM_XPD_BBPLL_V << PMU_HP_MODEM_XPD_BBPLL_S)
#define PMU_HP_MODEM_XPD_BBPLL_V  0x00000001U
#define PMU_HP_MODEM_XPD_BBPLL_S  30

/** PMU_HP_MODEM_BIAS_REG register
 *  need_des
 */
#define PMU_HP_MODEM_BIAS_REG (DR_REG_PMU_BASE + 0x4c)
/** PMU_HP_MODEM_XPD_TRX : R/W; bitpos: [24]; default: 1;
 *  need_des
 */
#define PMU_HP_MODEM_XPD_TRX    (BIT(24))
#define PMU_HP_MODEM_XPD_TRX_M  (PMU_HP_MODEM_XPD_TRX_V << PMU_HP_MODEM_XPD_TRX_S)
#define PMU_HP_MODEM_XPD_TRX_V  0x00000001U
#define PMU_HP_MODEM_XPD_TRX_S  24
/** PMU_HP_MODEM_XPD_BIAS : R/W; bitpos: [25]; default: 0;
 *  need_des
 */
#define PMU_HP_MODEM_XPD_BIAS    (BIT(25))
#define PMU_HP_MODEM_XPD_BIAS_M  (PMU_HP_MODEM_XPD_BIAS_V << PMU_HP_MODEM_XPD_BIAS_S)
#define PMU_HP_MODEM_XPD_BIAS_V  0x00000001U
#define PMU_HP_MODEM_XPD_BIAS_S  25
/** PMU_HP_MODEM_PD_CUR : R/W; bitpos: [30]; default: 0;
 *  need_des
 */
#define PMU_HP_MODEM_PD_CUR    (BIT(30))
#define PMU_HP_MODEM_PD_CUR_M  (PMU_HP_MODEM_PD_CUR_V << PMU_HP_MODEM_PD_CUR_S)
#define PMU_HP_MODEM_PD_CUR_V  0x00000001U
#define PMU_HP_MODEM_PD_CUR_S  30
/** PMU_HP_MODEM_BIAS_SLEEP : R/W; bitpos: [31]; default: 0;
 *  need_des
 */
#define PMU_HP_MODEM_BIAS_SLEEP    (BIT(31))
#define PMU_HP_MODEM_BIAS_SLEEP_M  (PMU_HP_MODEM_BIAS_SLEEP_V << PMU_HP_MODEM_BIAS_SLEEP_S)
#define PMU_HP_MODEM_BIAS_SLEEP_V  0x00000001U
#define PMU_HP_MODEM_BIAS_SLEEP_S  31

/** PMU_HP_MODEM_BACKUP_REG register
 *  need_des
 */
#define PMU_HP_MODEM_BACKUP_REG (DR_REG_PMU_BASE + 0x50)
/** PMU_HP_SLEEP2MODEM_BACKUP_MODEM_CLK_CODE : R/W; bitpos: [5:4]; default: 0;
 *  need_des
 */
#define PMU_HP_SLEEP2MODEM_BACKUP_MODEM_CLK_CODE    0x00000003U
#define PMU_HP_SLEEP2MODEM_BACKUP_MODEM_CLK_CODE_M  (PMU_HP_SLEEP2MODEM_BACKUP_MODEM_CLK_CODE_V << PMU_HP_SLEEP2MODEM_BACKUP_MODEM_CLK_CODE_S)
#define PMU_HP_SLEEP2MODEM_BACKUP_MODEM_CLK_CODE_V  0x00000003U
#define PMU_HP_SLEEP2MODEM_BACKUP_MODEM_CLK_CODE_S  4
/** PMU_HP_MODEM_RETENTION_MODE : R/W; bitpos: [10]; default: 0;
 *  need_des
 */
#define PMU_HP_MODEM_RETENTION_MODE    (BIT(10))
#define PMU_HP_MODEM_RETENTION_MODE_M  (PMU_HP_MODEM_RETENTION_MODE_V << PMU_HP_MODEM_RETENTION_MODE_S)
#define PMU_HP_MODEM_RETENTION_MODE_V  0x00000001U
#define PMU_HP_MODEM_RETENTION_MODE_S  10
/** PMU_HP_SLEEP2MODEM_RETENTION_EN : R/W; bitpos: [11]; default: 0;
 *  need_des
 */
#define PMU_HP_SLEEP2MODEM_RETENTION_EN    (BIT(11))
#define PMU_HP_SLEEP2MODEM_RETENTION_EN_M  (PMU_HP_SLEEP2MODEM_RETENTION_EN_V << PMU_HP_SLEEP2MODEM_RETENTION_EN_S)
#define PMU_HP_SLEEP2MODEM_RETENTION_EN_V  0x00000001U
#define PMU_HP_SLEEP2MODEM_RETENTION_EN_S  11
/** PMU_HP_SLEEP2MODEM_BACKUP_CLK_SEL : R/W; bitpos: [15:14]; default: 0;
 *  need_des
 */
#define PMU_HP_SLEEP2MODEM_BACKUP_CLK_SEL    0x00000003U
#define PMU_HP_SLEEP2MODEM_BACKUP_CLK_SEL_M  (PMU_HP_SLEEP2MODEM_BACKUP_CLK_SEL_V << PMU_HP_SLEEP2MODEM_BACKUP_CLK_SEL_S)
#define PMU_HP_SLEEP2MODEM_BACKUP_CLK_SEL_V  0x00000003U
#define PMU_HP_SLEEP2MODEM_BACKUP_CLK_SEL_S  14
/** PMU_HP_SLEEP2MODEM_BACKUP_MODE : R/W; bitpos: [22:20]; default: 0;
 *  need_des
 */
#define PMU_HP_SLEEP2MODEM_BACKUP_MODE    0x00000007U
#define PMU_HP_SLEEP2MODEM_BACKUP_MODE_M  (PMU_HP_SLEEP2MODEM_BACKUP_MODE_V << PMU_HP_SLEEP2MODEM_BACKUP_MODE_S)
#define PMU_HP_SLEEP2MODEM_BACKUP_MODE_V  0x00000007U
#define PMU_HP_SLEEP2MODEM_BACKUP_MODE_S  20
/** PMU_HP_SLEEP2MODEM_BACKUP_EN : R/W; bitpos: [29]; default: 0;
 *  need_des
 */
#define PMU_HP_SLEEP2MODEM_BACKUP_EN    (BIT(29))
#define PMU_HP_SLEEP2MODEM_BACKUP_EN_M  (PMU_HP_SLEEP2MODEM_BACKUP_EN_V << PMU_HP_SLEEP2MODEM_BACKUP_EN_S)
#define PMU_HP_SLEEP2MODEM_BACKUP_EN_V  0x00000001U
#define PMU_HP_SLEEP2MODEM_BACKUP_EN_S  29

/** PMU_HP_MODEM_BACKUP_CLK_REG register
 *  need_des
 */
#define PMU_HP_MODEM_BACKUP_CLK_REG (DR_REG_PMU_BASE + 0x54)
/** PMU_HP_MODEM_BACKUP_ICG_FUNC_EN : R/W; bitpos: [31:0]; default: 0;
 *  need_des
 */
#define PMU_HP_MODEM_BACKUP_ICG_FUNC_EN    0xFFFFFFFFU
#define PMU_HP_MODEM_BACKUP_ICG_FUNC_EN_M  (PMU_HP_MODEM_BACKUP_ICG_FUNC_EN_V << PMU_HP_MODEM_BACKUP_ICG_FUNC_EN_S)
#define PMU_HP_MODEM_BACKUP_ICG_FUNC_EN_V  0xFFFFFFFFU
#define PMU_HP_MODEM_BACKUP_ICG_FUNC_EN_S  0

/** PMU_HP_MODEM_SYSCLK_REG register
 *  need_des
 */
#define PMU_HP_MODEM_SYSCLK_REG (DR_REG_PMU_BASE + 0x58)
/** PMU_HP_MODEM_DIG_SYS_CLK_NO_DIV : R/W; bitpos: [26]; default: 0;
 *  need_des
 */
#define PMU_HP_MODEM_DIG_SYS_CLK_NO_DIV    (BIT(26))
#define PMU_HP_MODEM_DIG_SYS_CLK_NO_DIV_M  (PMU_HP_MODEM_DIG_SYS_CLK_NO_DIV_V << PMU_HP_MODEM_DIG_SYS_CLK_NO_DIV_S)
#define PMU_HP_MODEM_DIG_SYS_CLK_NO_DIV_V  0x00000001U
#define PMU_HP_MODEM_DIG_SYS_CLK_NO_DIV_S  26
/** PMU_HP_MODEM_ICG_SYS_CLOCK_EN : R/W; bitpos: [27]; default: 0;
 *  need_des
 */
#define PMU_HP_MODEM_ICG_SYS_CLOCK_EN    (BIT(27))
#define PMU_HP_MODEM_ICG_SYS_CLOCK_EN_M  (PMU_HP_MODEM_ICG_SYS_CLOCK_EN_V << PMU_HP_MODEM_ICG_SYS_CLOCK_EN_S)
#define PMU_HP_MODEM_ICG_SYS_CLOCK_EN_V  0x00000001U
#define PMU_HP_MODEM_ICG_SYS_CLOCK_EN_S  27
/** PMU_HP_MODEM_SYS_CLK_SLP_SEL : R/W; bitpos: [28]; default: 0;
 *  need_des
 */
#define PMU_HP_MODEM_SYS_CLK_SLP_SEL    (BIT(28))
#define PMU_HP_MODEM_SYS_CLK_SLP_SEL_M  (PMU_HP_MODEM_SYS_CLK_SLP_SEL_V << PMU_HP_MODEM_SYS_CLK_SLP_SEL_S)
#define PMU_HP_MODEM_SYS_CLK_SLP_SEL_V  0x00000001U
#define PMU_HP_MODEM_SYS_CLK_SLP_SEL_S  28
/** PMU_HP_MODEM_ICG_SLP_SEL : R/W; bitpos: [29]; default: 0;
 *  need_des
 */
#define PMU_HP_MODEM_ICG_SLP_SEL    (BIT(29))
#define PMU_HP_MODEM_ICG_SLP_SEL_M  (PMU_HP_MODEM_ICG_SLP_SEL_V << PMU_HP_MODEM_ICG_SLP_SEL_S)
#define PMU_HP_MODEM_ICG_SLP_SEL_V  0x00000001U
#define PMU_HP_MODEM_ICG_SLP_SEL_S  29
/** PMU_HP_MODEM_DIG_SYS_CLK_SEL : R/W; bitpos: [31:30]; default: 0;
 *  need_des
 */
#define PMU_HP_MODEM_DIG_SYS_CLK_SEL    0x00000003U
#define PMU_HP_MODEM_DIG_SYS_CLK_SEL_M  (PMU_HP_MODEM_DIG_SYS_CLK_SEL_V << PMU_HP_MODEM_DIG_SYS_CLK_SEL_S)
#define PMU_HP_MODEM_DIG_SYS_CLK_SEL_V  0x00000003U
#define PMU_HP_MODEM_DIG_SYS_CLK_SEL_S  30

/** PMU_HP_MODEM_HP_REGULATOR0_REG register
 *  need_des
 */
#define PMU_HP_MODEM_HP_REGULATOR0_REG (DR_REG_PMU_BASE + 0x5c)
/** PMU_HP_MODEM_HP_POWER_DET_BYPASS : R/W; bitpos: [0]; default: 0;
 *  need_des
 */
#define PMU_HP_MODEM_HP_POWER_DET_BYPASS    (BIT(0))
#define PMU_HP_MODEM_HP_POWER_DET_BYPASS_M  (PMU_HP_MODEM_HP_POWER_DET_BYPASS_V << PMU_HP_MODEM_HP_POWER_DET_BYPASS_S)
#define PMU_HP_MODEM_HP_POWER_DET_BYPASS_V  0x00000001U
#define PMU_HP_MODEM_HP_POWER_DET_BYPASS_S  0
/** PMU_HP_MODEM_HP_REGULATOR_SLP_MEM_XPD : R/W; bitpos: [16]; default: 1;
 *  need_des
 */
#define PMU_HP_MODEM_HP_REGULATOR_SLP_MEM_XPD    (BIT(16))
#define PMU_HP_MODEM_HP_REGULATOR_SLP_MEM_XPD_M  (PMU_HP_MODEM_HP_REGULATOR_SLP_MEM_XPD_V << PMU_HP_MODEM_HP_REGULATOR_SLP_MEM_XPD_S)
#define PMU_HP_MODEM_HP_REGULATOR_SLP_MEM_XPD_V  0x00000001U
#define PMU_HP_MODEM_HP_REGULATOR_SLP_MEM_XPD_S  16
/** PMU_HP_MODEM_HP_REGULATOR_SLP_LOGIC_XPD : R/W; bitpos: [17]; default: 1;
 *  need_des
 */
#define PMU_HP_MODEM_HP_REGULATOR_SLP_LOGIC_XPD    (BIT(17))
#define PMU_HP_MODEM_HP_REGULATOR_SLP_LOGIC_XPD_M  (PMU_HP_MODEM_HP_REGULATOR_SLP_LOGIC_XPD_V << PMU_HP_MODEM_HP_REGULATOR_SLP_LOGIC_XPD_S)
#define PMU_HP_MODEM_HP_REGULATOR_SLP_LOGIC_XPD_V  0x00000001U
#define PMU_HP_MODEM_HP_REGULATOR_SLP_LOGIC_XPD_S  17
/** PMU_HP_MODEM_HP_REGULATOR_XPD : R/W; bitpos: [18]; default: 1;
 *  need_des
 */
#define PMU_HP_MODEM_HP_REGULATOR_XPD    (BIT(18))
#define PMU_HP_MODEM_HP_REGULATOR_XPD_M  (PMU_HP_MODEM_HP_REGULATOR_XPD_V << PMU_HP_MODEM_HP_REGULATOR_XPD_S)
#define PMU_HP_MODEM_HP_REGULATOR_XPD_V  0x00000001U
#define PMU_HP_MODEM_HP_REGULATOR_XPD_S  18
/** PMU_HP_MODEM_HP_REGULATOR_SLP_MEM_DBIAS : R/W; bitpos: [22:19]; default: 8;
 *  need_des
 */
#define PMU_HP_MODEM_HP_REGULATOR_SLP_MEM_DBIAS    0x0000000FU
#define PMU_HP_MODEM_HP_REGULATOR_SLP_MEM_DBIAS_M  (PMU_HP_MODEM_HP_REGULATOR_SLP_MEM_DBIAS_V << PMU_HP_MODEM_HP_REGULATOR_SLP_MEM_DBIAS_S)
#define PMU_HP_MODEM_HP_REGULATOR_SLP_MEM_DBIAS_V  0x0000000FU
#define PMU_HP_MODEM_HP_REGULATOR_SLP_MEM_DBIAS_S  19
/** PMU_HP_MODEM_HP_REGULATOR_SLP_LOGIC_DBIAS : R/W; bitpos: [26:23]; default: 8;
 *  need_des
 */
#define PMU_HP_MODEM_HP_REGULATOR_SLP_LOGIC_DBIAS    0x0000000FU
#define PMU_HP_MODEM_HP_REGULATOR_SLP_LOGIC_DBIAS_M  (PMU_HP_MODEM_HP_REGULATOR_SLP_LOGIC_DBIAS_V << PMU_HP_MODEM_HP_REGULATOR_SLP_LOGIC_DBIAS_S)
#define PMU_HP_MODEM_HP_REGULATOR_SLP_LOGIC_DBIAS_V  0x0000000FU
#define PMU_HP_MODEM_HP_REGULATOR_SLP_LOGIC_DBIAS_S  23
/** PMU_HP_MODEM_HP_REGULATOR_DBIAS : R/W; bitpos: [31:27]; default: 16;
 *  need_des
 */
#define PMU_HP_MODEM_HP_REGULATOR_DBIAS    0x0000001FU
#define PMU_HP_MODEM_HP_REGULATOR_DBIAS_M  (PMU_HP_MODEM_HP_REGULATOR_DBIAS_V << PMU_HP_MODEM_HP_REGULATOR_DBIAS_S)
#define PMU_HP_MODEM_HP_REGULATOR_DBIAS_V  0x0000001FU
#define PMU_HP_MODEM_HP_REGULATOR_DBIAS_S  27

/** PMU_HP_MODEM_HP_REGULATOR1_REG register
 *  need_des
 */
#define PMU_HP_MODEM_HP_REGULATOR1_REG (DR_REG_PMU_BASE + 0x60)
/** PMU_HP_MODEM_HP_REGULATOR_DRV_B : R/W; bitpos: [31:8]; default: 0;
 *  need_des
 */
#define PMU_HP_MODEM_HP_REGULATOR_DRV_B    0x00FFFFFFU
#define PMU_HP_MODEM_HP_REGULATOR_DRV_B_M  (PMU_HP_MODEM_HP_REGULATOR_DRV_B_V << PMU_HP_MODEM_HP_REGULATOR_DRV_B_S)
#define PMU_HP_MODEM_HP_REGULATOR_DRV_B_V  0x00FFFFFFU
#define PMU_HP_MODEM_HP_REGULATOR_DRV_B_S  8

/** PMU_HP_MODEM_XTAL_REG register
 *  need_des
 */
#define PMU_HP_MODEM_XTAL_REG (DR_REG_PMU_BASE + 0x64)
/** PMU_HP_MODEM_XPD_XTAL : R/W; bitpos: [31]; default: 1;
 *  need_des
 */
#define PMU_HP_MODEM_XPD_XTAL    (BIT(31))
#define PMU_HP_MODEM_XPD_XTAL_M  (PMU_HP_MODEM_XPD_XTAL_V << PMU_HP_MODEM_XPD_XTAL_S)
#define PMU_HP_MODEM_XPD_XTAL_V  0x00000001U
#define PMU_HP_MODEM_XPD_XTAL_S  31

/** PMU_HP_SLEEP_DIG_POWER_REG register
 *  need_des
 */
#define PMU_HP_SLEEP_DIG_POWER_REG (DR_REG_PMU_BASE + 0x68)
/** PMU_HP_SLEEP_VDD_SPI_PD_EN : R/W; bitpos: [21]; default: 0;
 *  need_des
 */
#define PMU_HP_SLEEP_VDD_SPI_PD_EN    (BIT(21))
#define PMU_HP_SLEEP_VDD_SPI_PD_EN_M  (PMU_HP_SLEEP_VDD_SPI_PD_EN_V << PMU_HP_SLEEP_VDD_SPI_PD_EN_S)
#define PMU_HP_SLEEP_VDD_SPI_PD_EN_V  0x00000001U
#define PMU_HP_SLEEP_VDD_SPI_PD_EN_S  21
/** PMU_HP_SLEEP_HP_MEM_DSLP : R/W; bitpos: [22]; default: 0;
 *  need_des
 */
#define PMU_HP_SLEEP_HP_MEM_DSLP    (BIT(22))
#define PMU_HP_SLEEP_HP_MEM_DSLP_M  (PMU_HP_SLEEP_HP_MEM_DSLP_V << PMU_HP_SLEEP_HP_MEM_DSLP_S)
#define PMU_HP_SLEEP_HP_MEM_DSLP_V  0x00000001U
#define PMU_HP_SLEEP_HP_MEM_DSLP_S  22
/** PMU_HP_SLEEP_PD_HP_MEM_PD_EN : R/W; bitpos: [26:23]; default: 0;
 *  need_des
 */
#define PMU_HP_SLEEP_PD_HP_MEM_PD_EN    0x0000000FU
#define PMU_HP_SLEEP_PD_HP_MEM_PD_EN_M  (PMU_HP_SLEEP_PD_HP_MEM_PD_EN_V << PMU_HP_SLEEP_PD_HP_MEM_PD_EN_S)
#define PMU_HP_SLEEP_PD_HP_MEM_PD_EN_V  0x0000000FU
#define PMU_HP_SLEEP_PD_HP_MEM_PD_EN_S  23
/** PMU_HP_SLEEP_PD_HP_WIFI_PD_EN : R/W; bitpos: [27]; default: 0;
 *  need_des
 */
#define PMU_HP_SLEEP_PD_HP_WIFI_PD_EN    (BIT(27))
#define PMU_HP_SLEEP_PD_HP_WIFI_PD_EN_M  (PMU_HP_SLEEP_PD_HP_WIFI_PD_EN_V << PMU_HP_SLEEP_PD_HP_WIFI_PD_EN_S)
#define PMU_HP_SLEEP_PD_HP_WIFI_PD_EN_V  0x00000001U
#define PMU_HP_SLEEP_PD_HP_WIFI_PD_EN_S  27
/** PMU_HP_SLEEP_PD_HP_CPU_PD_EN : R/W; bitpos: [29]; default: 0;
 *  need_des
 */
#define PMU_HP_SLEEP_PD_HP_CPU_PD_EN    (BIT(29))
#define PMU_HP_SLEEP_PD_HP_CPU_PD_EN_M  (PMU_HP_SLEEP_PD_HP_CPU_PD_EN_V << PMU_HP_SLEEP_PD_HP_CPU_PD_EN_S)
#define PMU_HP_SLEEP_PD_HP_CPU_PD_EN_V  0x00000001U
#define PMU_HP_SLEEP_PD_HP_CPU_PD_EN_S  29
/** PMU_HP_SLEEP_PD_HP_AON_PD_EN : R/W; bitpos: [30]; default: 0;
 *  need_des
 */
#define PMU_HP_SLEEP_PD_HP_AON_PD_EN    (BIT(30))
#define PMU_HP_SLEEP_PD_HP_AON_PD_EN_M  (PMU_HP_SLEEP_PD_HP_AON_PD_EN_V << PMU_HP_SLEEP_PD_HP_AON_PD_EN_S)
#define PMU_HP_SLEEP_PD_HP_AON_PD_EN_V  0x00000001U
#define PMU_HP_SLEEP_PD_HP_AON_PD_EN_S  30
/** PMU_HP_SLEEP_PD_TOP_PD_EN : R/W; bitpos: [31]; default: 0;
 *  need_des
 */
#define PMU_HP_SLEEP_PD_TOP_PD_EN    (BIT(31))
#define PMU_HP_SLEEP_PD_TOP_PD_EN_M  (PMU_HP_SLEEP_PD_TOP_PD_EN_V << PMU_HP_SLEEP_PD_TOP_PD_EN_S)
#define PMU_HP_SLEEP_PD_TOP_PD_EN_V  0x00000001U
#define PMU_HP_SLEEP_PD_TOP_PD_EN_S  31

/** PMU_HP_SLEEP_ICG_HP_FUNC_REG register
 *  need_des
 */
#define PMU_HP_SLEEP_ICG_HP_FUNC_REG (DR_REG_PMU_BASE + 0x6c)
/** PMU_HP_SLEEP_DIG_ICG_FUNC_EN : R/W; bitpos: [31:0]; default: 4294967295;
 *  need_des
 */
#define PMU_HP_SLEEP_DIG_ICG_FUNC_EN    0xFFFFFFFFU
#define PMU_HP_SLEEP_DIG_ICG_FUNC_EN_M  (PMU_HP_SLEEP_DIG_ICG_FUNC_EN_V << PMU_HP_SLEEP_DIG_ICG_FUNC_EN_S)
#define PMU_HP_SLEEP_DIG_ICG_FUNC_EN_V  0xFFFFFFFFU
#define PMU_HP_SLEEP_DIG_ICG_FUNC_EN_S  0

/** PMU_HP_SLEEP_ICG_HP_APB_REG register
 *  need_des
 */
#define PMU_HP_SLEEP_ICG_HP_APB_REG (DR_REG_PMU_BASE + 0x70)
/** PMU_HP_SLEEP_DIG_ICG_APB_EN : R/W; bitpos: [31:0]; default: 4294967295;
 *  need_des
 */
#define PMU_HP_SLEEP_DIG_ICG_APB_EN    0xFFFFFFFFU
#define PMU_HP_SLEEP_DIG_ICG_APB_EN_M  (PMU_HP_SLEEP_DIG_ICG_APB_EN_V << PMU_HP_SLEEP_DIG_ICG_APB_EN_S)
#define PMU_HP_SLEEP_DIG_ICG_APB_EN_V  0xFFFFFFFFU
#define PMU_HP_SLEEP_DIG_ICG_APB_EN_S  0

/** PMU_HP_SLEEP_ICG_MODEM_REG register
 *  need_des
 */
#define PMU_HP_SLEEP_ICG_MODEM_REG (DR_REG_PMU_BASE + 0x74)
/** PMU_HP_SLEEP_DIG_ICG_MODEM_CODE : R/W; bitpos: [31:30]; default: 0;
 *  need_des
 */
#define PMU_HP_SLEEP_DIG_ICG_MODEM_CODE    0x00000003U
#define PMU_HP_SLEEP_DIG_ICG_MODEM_CODE_M  (PMU_HP_SLEEP_DIG_ICG_MODEM_CODE_V << PMU_HP_SLEEP_DIG_ICG_MODEM_CODE_S)
#define PMU_HP_SLEEP_DIG_ICG_MODEM_CODE_V  0x00000003U
#define PMU_HP_SLEEP_DIG_ICG_MODEM_CODE_S  30

/** PMU_HP_SLEEP_HP_SYS_CNTL_REG register
 *  need_des
 */
#define PMU_HP_SLEEP_HP_SYS_CNTL_REG (DR_REG_PMU_BASE + 0x78)
/** PMU_HP_SLEEP_UART_WAKEUP_EN : R/W; bitpos: [24]; default: 0;
 *  need_des
 */
#define PMU_HP_SLEEP_UART_WAKEUP_EN    (BIT(24))
#define PMU_HP_SLEEP_UART_WAKEUP_EN_M  (PMU_HP_SLEEP_UART_WAKEUP_EN_V << PMU_HP_SLEEP_UART_WAKEUP_EN_S)
#define PMU_HP_SLEEP_UART_WAKEUP_EN_V  0x00000001U
#define PMU_HP_SLEEP_UART_WAKEUP_EN_S  24
/** PMU_HP_SLEEP_LP_PAD_HOLD_ALL : R/W; bitpos: [25]; default: 0;
 *  need_des
 */
#define PMU_HP_SLEEP_LP_PAD_HOLD_ALL    (BIT(25))
#define PMU_HP_SLEEP_LP_PAD_HOLD_ALL_M  (PMU_HP_SLEEP_LP_PAD_HOLD_ALL_V << PMU_HP_SLEEP_LP_PAD_HOLD_ALL_S)
#define PMU_HP_SLEEP_LP_PAD_HOLD_ALL_V  0x00000001U
#define PMU_HP_SLEEP_LP_PAD_HOLD_ALL_S  25
/** PMU_HP_SLEEP_HP_PAD_HOLD_ALL : R/W; bitpos: [26]; default: 0;
 *  need_des
 */
#define PMU_HP_SLEEP_HP_PAD_HOLD_ALL    (BIT(26))
#define PMU_HP_SLEEP_HP_PAD_HOLD_ALL_M  (PMU_HP_SLEEP_HP_PAD_HOLD_ALL_V << PMU_HP_SLEEP_HP_PAD_HOLD_ALL_S)
#define PMU_HP_SLEEP_HP_PAD_HOLD_ALL_V  0x00000001U
#define PMU_HP_SLEEP_HP_PAD_HOLD_ALL_S  26
/** PMU_HP_SLEEP_DIG_PAD_SLP_SEL : R/W; bitpos: [27]; default: 0;
 *  need_des
 */
#define PMU_HP_SLEEP_DIG_PAD_SLP_SEL    (BIT(27))
#define PMU_HP_SLEEP_DIG_PAD_SLP_SEL_M  (PMU_HP_SLEEP_DIG_PAD_SLP_SEL_V << PMU_HP_SLEEP_DIG_PAD_SLP_SEL_S)
#define PMU_HP_SLEEP_DIG_PAD_SLP_SEL_V  0x00000001U
#define PMU_HP_SLEEP_DIG_PAD_SLP_SEL_S  27
/** PMU_HP_SLEEP_DIG_PAUSE_WDT : R/W; bitpos: [28]; default: 0;
 *  need_des
 */
#define PMU_HP_SLEEP_DIG_PAUSE_WDT    (BIT(28))
#define PMU_HP_SLEEP_DIG_PAUSE_WDT_M  (PMU_HP_SLEEP_DIG_PAUSE_WDT_V << PMU_HP_SLEEP_DIG_PAUSE_WDT_S)
#define PMU_HP_SLEEP_DIG_PAUSE_WDT_V  0x00000001U
#define PMU_HP_SLEEP_DIG_PAUSE_WDT_S  28
/** PMU_HP_SLEEP_DIG_CPU_STALL : R/W; bitpos: [29]; default: 0;
 *  need_des
 */
#define PMU_HP_SLEEP_DIG_CPU_STALL    (BIT(29))
#define PMU_HP_SLEEP_DIG_CPU_STALL_M  (PMU_HP_SLEEP_DIG_CPU_STALL_V << PMU_HP_SLEEP_DIG_CPU_STALL_S)
#define PMU_HP_SLEEP_DIG_CPU_STALL_V  0x00000001U
#define PMU_HP_SLEEP_DIG_CPU_STALL_S  29

/** PMU_HP_SLEEP_HP_CK_POWER_REG register
 *  need_des
 */
#define PMU_HP_SLEEP_HP_CK_POWER_REG (DR_REG_PMU_BASE + 0x7c)
/** PMU_HP_SLEEP_I2C_ISO_EN : R/W; bitpos: [26]; default: 0;
 *  need_des
 */
#define PMU_HP_SLEEP_I2C_ISO_EN    (BIT(26))
#define PMU_HP_SLEEP_I2C_ISO_EN_M  (PMU_HP_SLEEP_I2C_ISO_EN_V << PMU_HP_SLEEP_I2C_ISO_EN_S)
#define PMU_HP_SLEEP_I2C_ISO_EN_V  0x00000001U
#define PMU_HP_SLEEP_I2C_ISO_EN_S  26
/** PMU_HP_SLEEP_I2C_RETENTION : R/W; bitpos: [27]; default: 0;
 *  need_des
 */
#define PMU_HP_SLEEP_I2C_RETENTION    (BIT(27))
#define PMU_HP_SLEEP_I2C_RETENTION_M  (PMU_HP_SLEEP_I2C_RETENTION_V << PMU_HP_SLEEP_I2C_RETENTION_S)
#define PMU_HP_SLEEP_I2C_RETENTION_V  0x00000001U
#define PMU_HP_SLEEP_I2C_RETENTION_S  27
/** PMU_HP_SLEEP_XPD_BB_I2C : R/W; bitpos: [28]; default: 0;
 *  need_des
 */
#define PMU_HP_SLEEP_XPD_BB_I2C    (BIT(28))
#define PMU_HP_SLEEP_XPD_BB_I2C_M  (PMU_HP_SLEEP_XPD_BB_I2C_V << PMU_HP_SLEEP_XPD_BB_I2C_S)
#define PMU_HP_SLEEP_XPD_BB_I2C_V  0x00000001U
#define PMU_HP_SLEEP_XPD_BB_I2C_S  28
/** PMU_HP_SLEEP_XPD_BBPLL_I2C : R/W; bitpos: [29]; default: 0;
 *  need_des
 */
#define PMU_HP_SLEEP_XPD_BBPLL_I2C    (BIT(29))
#define PMU_HP_SLEEP_XPD_BBPLL_I2C_M  (PMU_HP_SLEEP_XPD_BBPLL_I2C_V << PMU_HP_SLEEP_XPD_BBPLL_I2C_S)
#define PMU_HP_SLEEP_XPD_BBPLL_I2C_V  0x00000001U
#define PMU_HP_SLEEP_XPD_BBPLL_I2C_S  29
/** PMU_HP_SLEEP_XPD_BBPLL : R/W; bitpos: [30]; default: 0;
 *  need_des
 */
#define PMU_HP_SLEEP_XPD_BBPLL    (BIT(30))
#define PMU_HP_SLEEP_XPD_BBPLL_M  (PMU_HP_SLEEP_XPD_BBPLL_V << PMU_HP_SLEEP_XPD_BBPLL_S)
#define PMU_HP_SLEEP_XPD_BBPLL_V  0x00000001U
#define PMU_HP_SLEEP_XPD_BBPLL_S  30

/** PMU_HP_SLEEP_BIAS_REG register
 *  need_des
 */
#define PMU_HP_SLEEP_BIAS_REG (DR_REG_PMU_BASE + 0x80)
/** PMU_HP_SLEEP_XPD_TRX : R/W; bitpos: [24]; default: 1;
 *  need_des
 */
#define PMU_HP_SLEEP_XPD_TRX    (BIT(24))
#define PMU_HP_SLEEP_XPD_TRX_M  (PMU_HP_SLEEP_XPD_TRX_V << PMU_HP_SLEEP_XPD_TRX_S)
#define PMU_HP_SLEEP_XPD_TRX_V  0x00000001U
#define PMU_HP_SLEEP_XPD_TRX_S  24
/** PMU_HP_SLEEP_XPD_BIAS : R/W; bitpos: [25]; default: 0;
 *  need_des
 */
#define PMU_HP_SLEEP_XPD_BIAS    (BIT(25))
#define PMU_HP_SLEEP_XPD_BIAS_M  (PMU_HP_SLEEP_XPD_BIAS_V << PMU_HP_SLEEP_XPD_BIAS_S)
#define PMU_HP_SLEEP_XPD_BIAS_V  0x00000001U
#define PMU_HP_SLEEP_XPD_BIAS_S  25
/** PMU_HP_SLEEP_PD_CUR : R/W; bitpos: [30]; default: 0;
 *  need_des
 */
#define PMU_HP_SLEEP_PD_CUR    (BIT(30))
#define PMU_HP_SLEEP_PD_CUR_M  (PMU_HP_SLEEP_PD_CUR_V << PMU_HP_SLEEP_PD_CUR_S)
#define PMU_HP_SLEEP_PD_CUR_V  0x00000001U
#define PMU_HP_SLEEP_PD_CUR_S  30
/** PMU_HP_SLEEP_BIAS_SLEEP : R/W; bitpos: [31]; default: 0;
 *  need_des
 */
#define PMU_HP_SLEEP_BIAS_SLEEP    (BIT(31))
#define PMU_HP_SLEEP_BIAS_SLEEP_M  (PMU_HP_SLEEP_BIAS_SLEEP_V << PMU_HP_SLEEP_BIAS_SLEEP_S)
#define PMU_HP_SLEEP_BIAS_SLEEP_V  0x00000001U
#define PMU_HP_SLEEP_BIAS_SLEEP_S  31

/** PMU_HP_SLEEP_BACKUP_REG register
 *  need_des
 */
#define PMU_HP_SLEEP_BACKUP_REG (DR_REG_PMU_BASE + 0x84)
/** PMU_HP_MODEM2SLEEP_BACKUP_MODEM_CLK_CODE : R/W; bitpos: [7:6]; default: 0;
 *  need_des
 */
#define PMU_HP_MODEM2SLEEP_BACKUP_MODEM_CLK_CODE    0x00000003U
#define PMU_HP_MODEM2SLEEP_BACKUP_MODEM_CLK_CODE_M  (PMU_HP_MODEM2SLEEP_BACKUP_MODEM_CLK_CODE_V << PMU_HP_MODEM2SLEEP_BACKUP_MODEM_CLK_CODE_S)
#define PMU_HP_MODEM2SLEEP_BACKUP_MODEM_CLK_CODE_V  0x00000003U
#define PMU_HP_MODEM2SLEEP_BACKUP_MODEM_CLK_CODE_S  6
/** PMU_HP_ACTIVE2SLEEP_BACKUP_MODEM_CLK_CODE : R/W; bitpos: [9:8]; default: 0;
 *  need_des
 */
#define PMU_HP_ACTIVE2SLEEP_BACKUP_MODEM_CLK_CODE    0x00000003U
#define PMU_HP_ACTIVE2SLEEP_BACKUP_MODEM_CLK_CODE_M  (PMU_HP_ACTIVE2SLEEP_BACKUP_MODEM_CLK_CODE_V << PMU_HP_ACTIVE2SLEEP_BACKUP_MODEM_CLK_CODE_S)
#define PMU_HP_ACTIVE2SLEEP_BACKUP_MODEM_CLK_CODE_V  0x00000003U
#define PMU_HP_ACTIVE2SLEEP_BACKUP_MODEM_CLK_CODE_S  8
/** PMU_HP_SLEEP_RETENTION_MODE : R/W; bitpos: [10]; default: 0;
 *  need_des
 */
#define PMU_HP_SLEEP_RETENTION_MODE    (BIT(10))
#define PMU_HP_SLEEP_RETENTION_MODE_M  (PMU_HP_SLEEP_RETENTION_MODE_V << PMU_HP_SLEEP_RETENTION_MODE_S)
#define PMU_HP_SLEEP_RETENTION_MODE_V  0x00000001U
#define PMU_HP_SLEEP_RETENTION_MODE_S  10
/** PMU_HP_MODEM2SLEEP_RETENTION_EN : R/W; bitpos: [12]; default: 0;
 *  need_des
 */
#define PMU_HP_MODEM2SLEEP_RETENTION_EN    (BIT(12))
#define PMU_HP_MODEM2SLEEP_RETENTION_EN_M  (PMU_HP_MODEM2SLEEP_RETENTION_EN_V << PMU_HP_MODEM2SLEEP_RETENTION_EN_S)
#define PMU_HP_MODEM2SLEEP_RETENTION_EN_V  0x00000001U
#define PMU_HP_MODEM2SLEEP_RETENTION_EN_S  12
/** PMU_HP_ACTIVE2SLEEP_RETENTION_EN : R/W; bitpos: [13]; default: 0;
 *  need_des
 */
#define PMU_HP_ACTIVE2SLEEP_RETENTION_EN    (BIT(13))
#define PMU_HP_ACTIVE2SLEEP_RETENTION_EN_M  (PMU_HP_ACTIVE2SLEEP_RETENTION_EN_V << PMU_HP_ACTIVE2SLEEP_RETENTION_EN_S)
#define PMU_HP_ACTIVE2SLEEP_RETENTION_EN_V  0x00000001U
#define PMU_HP_ACTIVE2SLEEP_RETENTION_EN_S  13
/** PMU_HP_MODEM2SLEEP_BACKUP_CLK_SEL : R/W; bitpos: [17:16]; default: 0;
 *  need_des
 */
#define PMU_HP_MODEM2SLEEP_BACKUP_CLK_SEL    0x00000003U
#define PMU_HP_MODEM2SLEEP_BACKUP_CLK_SEL_M  (PMU_HP_MODEM2SLEEP_BACKUP_CLK_SEL_V << PMU_HP_MODEM2SLEEP_BACKUP_CLK_SEL_S)
#define PMU_HP_MODEM2SLEEP_BACKUP_CLK_SEL_V  0x00000003U
#define PMU_HP_MODEM2SLEEP_BACKUP_CLK_SEL_S  16
/** PMU_HP_ACTIVE2SLEEP_BACKUP_CLK_SEL : R/W; bitpos: [19:18]; default: 0;
 *  need_des
 */
#define PMU_HP_ACTIVE2SLEEP_BACKUP_CLK_SEL    0x00000003U
#define PMU_HP_ACTIVE2SLEEP_BACKUP_CLK_SEL_M  (PMU_HP_ACTIVE2SLEEP_BACKUP_CLK_SEL_V << PMU_HP_ACTIVE2SLEEP_BACKUP_CLK_SEL_S)
#define PMU_HP_ACTIVE2SLEEP_BACKUP_CLK_SEL_V  0x00000003U
#define PMU_HP_ACTIVE2SLEEP_BACKUP_CLK_SEL_S  18
/** PMU_HP_MODEM2SLEEP_BACKUP_MODE : R/W; bitpos: [25:23]; default: 0;
 *  need_des
 */
#define PMU_HP_MODEM2SLEEP_BACKUP_MODE    0x00000007U
#define PMU_HP_MODEM2SLEEP_BACKUP_MODE_M  (PMU_HP_MODEM2SLEEP_BACKUP_MODE_V << PMU_HP_MODEM2SLEEP_BACKUP_MODE_S)
#define PMU_HP_MODEM2SLEEP_BACKUP_MODE_V  0x00000007U
#define PMU_HP_MODEM2SLEEP_BACKUP_MODE_S  23
/** PMU_HP_ACTIVE2SLEEP_BACKUP_MODE : R/W; bitpos: [28:26]; default: 0;
 *  need_des
 */
#define PMU_HP_ACTIVE2SLEEP_BACKUP_MODE    0x00000007U
#define PMU_HP_ACTIVE2SLEEP_BACKUP_MODE_M  (PMU_HP_ACTIVE2SLEEP_BACKUP_MODE_V << PMU_HP_ACTIVE2SLEEP_BACKUP_MODE_S)
#define PMU_HP_ACTIVE2SLEEP_BACKUP_MODE_V  0x00000007U
#define PMU_HP_ACTIVE2SLEEP_BACKUP_MODE_S  26
/** PMU_HP_MODEM2SLEEP_BACKUP_EN : R/W; bitpos: [30]; default: 0;
 *  need_des
 */
#define PMU_HP_MODEM2SLEEP_BACKUP_EN    (BIT(30))
#define PMU_HP_MODEM2SLEEP_BACKUP_EN_M  (PMU_HP_MODEM2SLEEP_BACKUP_EN_V << PMU_HP_MODEM2SLEEP_BACKUP_EN_S)
#define PMU_HP_MODEM2SLEEP_BACKUP_EN_V  0x00000001U
#define PMU_HP_MODEM2SLEEP_BACKUP_EN_S  30
/** PMU_HP_ACTIVE2SLEEP_BACKUP_EN : R/W; bitpos: [31]; default: 0;
 *  need_des
 */
#define PMU_HP_ACTIVE2SLEEP_BACKUP_EN    (BIT(31))
#define PMU_HP_ACTIVE2SLEEP_BACKUP_EN_M  (PMU_HP_ACTIVE2SLEEP_BACKUP_EN_V << PMU_HP_ACTIVE2SLEEP_BACKUP_EN_S)
#define PMU_HP_ACTIVE2SLEEP_BACKUP_EN_V  0x00000001U
#define PMU_HP_ACTIVE2SLEEP_BACKUP_EN_S  31

/** PMU_HP_SLEEP_BACKUP_CLK_REG register
 *  need_des
 */
#define PMU_HP_SLEEP_BACKUP_CLK_REG (DR_REG_PMU_BASE + 0x88)
/** PMU_HP_SLEEP_BACKUP_ICG_FUNC_EN : R/W; bitpos: [31:0]; default: 0;
 *  need_des
 */
#define PMU_HP_SLEEP_BACKUP_ICG_FUNC_EN    0xFFFFFFFFU
#define PMU_HP_SLEEP_BACKUP_ICG_FUNC_EN_M  (PMU_HP_SLEEP_BACKUP_ICG_FUNC_EN_V << PMU_HP_SLEEP_BACKUP_ICG_FUNC_EN_S)
#define PMU_HP_SLEEP_BACKUP_ICG_FUNC_EN_V  0xFFFFFFFFU
#define PMU_HP_SLEEP_BACKUP_ICG_FUNC_EN_S  0

/** PMU_HP_SLEEP_SYSCLK_REG register
 *  need_des
 */
#define PMU_HP_SLEEP_SYSCLK_REG (DR_REG_PMU_BASE + 0x8c)
/** PMU_HP_SLEEP_DIG_SYS_CLK_NO_DIV : R/W; bitpos: [26]; default: 0;
 *  need_des
 */
#define PMU_HP_SLEEP_DIG_SYS_CLK_NO_DIV    (BIT(26))
#define PMU_HP_SLEEP_DIG_SYS_CLK_NO_DIV_M  (PMU_HP_SLEEP_DIG_SYS_CLK_NO_DIV_V << PMU_HP_SLEEP_DIG_SYS_CLK_NO_DIV_S)
#define PMU_HP_SLEEP_DIG_SYS_CLK_NO_DIV_V  0x00000001U
#define PMU_HP_SLEEP_DIG_SYS_CLK_NO_DIV_S  26
/** PMU_HP_SLEEP_ICG_SYS_CLOCK_EN : R/W; bitpos: [27]; default: 0;
 *  need_des
 */
#define PMU_HP_SLEEP_ICG_SYS_CLOCK_EN    (BIT(27))
#define PMU_HP_SLEEP_ICG_SYS_CLOCK_EN_M  (PMU_HP_SLEEP_ICG_SYS_CLOCK_EN_V << PMU_HP_SLEEP_ICG_SYS_CLOCK_EN_S)
#define PMU_HP_SLEEP_ICG_SYS_CLOCK_EN_V  0x00000001U
#define PMU_HP_SLEEP_ICG_SYS_CLOCK_EN_S  27
/** PMU_HP_SLEEP_SYS_CLK_SLP_SEL : R/W; bitpos: [28]; default: 0;
 *  need_des
 */
#define PMU_HP_SLEEP_SYS_CLK_SLP_SEL    (BIT(28))
#define PMU_HP_SLEEP_SYS_CLK_SLP_SEL_M  (PMU_HP_SLEEP_SYS_CLK_SLP_SEL_V << PMU_HP_SLEEP_SYS_CLK_SLP_SEL_S)
#define PMU_HP_SLEEP_SYS_CLK_SLP_SEL_V  0x00000001U
#define PMU_HP_SLEEP_SYS_CLK_SLP_SEL_S  28
/** PMU_HP_SLEEP_ICG_SLP_SEL : R/W; bitpos: [29]; default: 0;
 *  need_des
 */
#define PMU_HP_SLEEP_ICG_SLP_SEL    (BIT(29))
#define PMU_HP_SLEEP_ICG_SLP_SEL_M  (PMU_HP_SLEEP_ICG_SLP_SEL_V << PMU_HP_SLEEP_ICG_SLP_SEL_S)
#define PMU_HP_SLEEP_ICG_SLP_SEL_V  0x00000001U
#define PMU_HP_SLEEP_ICG_SLP_SEL_S  29
/** PMU_HP_SLEEP_DIG_SYS_CLK_SEL : R/W; bitpos: [31:30]; default: 0;
 *  need_des
 */
#define PMU_HP_SLEEP_DIG_SYS_CLK_SEL    0x00000003U
#define PMU_HP_SLEEP_DIG_SYS_CLK_SEL_M  (PMU_HP_SLEEP_DIG_SYS_CLK_SEL_V << PMU_HP_SLEEP_DIG_SYS_CLK_SEL_S)
#define PMU_HP_SLEEP_DIG_SYS_CLK_SEL_V  0x00000003U
#define PMU_HP_SLEEP_DIG_SYS_CLK_SEL_S  30

/** PMU_HP_SLEEP_HP_REGULATOR0_REG register
 *  need_des
 */
#define PMU_HP_SLEEP_HP_REGULATOR0_REG (DR_REG_PMU_BASE + 0x90)
/** PMU_HP_SLEEP_HP_POWER_DET_BYPASS : R/W; bitpos: [0]; default: 0;
 *  need_des
 */
#define PMU_HP_SLEEP_HP_POWER_DET_BYPASS    (BIT(0))
#define PMU_HP_SLEEP_HP_POWER_DET_BYPASS_M  (PMU_HP_SLEEP_HP_POWER_DET_BYPASS_V << PMU_HP_SLEEP_HP_POWER_DET_BYPASS_S)
#define PMU_HP_SLEEP_HP_POWER_DET_BYPASS_V  0x00000001U
#define PMU_HP_SLEEP_HP_POWER_DET_BYPASS_S  0
/** PMU_HP_SLEEP_HP_REGULATOR_SLP_MEM_XPD : R/W; bitpos: [16]; default: 1;
 *  need_des
 */
#define PMU_HP_SLEEP_HP_REGULATOR_SLP_MEM_XPD    (BIT(16))
#define PMU_HP_SLEEP_HP_REGULATOR_SLP_MEM_XPD_M  (PMU_HP_SLEEP_HP_REGULATOR_SLP_MEM_XPD_V << PMU_HP_SLEEP_HP_REGULATOR_SLP_MEM_XPD_S)
#define PMU_HP_SLEEP_HP_REGULATOR_SLP_MEM_XPD_V  0x00000001U
#define PMU_HP_SLEEP_HP_REGULATOR_SLP_MEM_XPD_S  16
/** PMU_HP_SLEEP_HP_REGULATOR_SLP_LOGIC_XPD : R/W; bitpos: [17]; default: 1;
 *  need_des
 */
#define PMU_HP_SLEEP_HP_REGULATOR_SLP_LOGIC_XPD    (BIT(17))
#define PMU_HP_SLEEP_HP_REGULATOR_SLP_LOGIC_XPD_M  (PMU_HP_SLEEP_HP_REGULATOR_SLP_LOGIC_XPD_V << PMU_HP_SLEEP_HP_REGULATOR_SLP_LOGIC_XPD_S)
#define PMU_HP_SLEEP_HP_REGULATOR_SLP_LOGIC_XPD_V  0x00000001U
#define PMU_HP_SLEEP_HP_REGULATOR_SLP_LOGIC_XPD_S  17
/** PMU_HP_SLEEP_HP_REGULATOR_XPD : R/W; bitpos: [18]; default: 1;
 *  need_des
 */
#define PMU_HP_SLEEP_HP_REGULATOR_XPD    (BIT(18))
#define PMU_HP_SLEEP_HP_REGULATOR_XPD_M  (PMU_HP_SLEEP_HP_REGULATOR_XPD_V << PMU_HP_SLEEP_HP_REGULATOR_XPD_S)
#define PMU_HP_SLEEP_HP_REGULATOR_XPD_V  0x00000001U
#define PMU_HP_SLEEP_HP_REGULATOR_XPD_S  18
/** PMU_HP_SLEEP_HP_REGULATOR_SLP_MEM_DBIAS : R/W; bitpos: [22:19]; default: 8;
 *  need_des
 */
#define PMU_HP_SLEEP_HP_REGULATOR_SLP_MEM_DBIAS    0x0000000FU
#define PMU_HP_SLEEP_HP_REGULATOR_SLP_MEM_DBIAS_M  (PMU_HP_SLEEP_HP_REGULATOR_SLP_MEM_DBIAS_V << PMU_HP_SLEEP_HP_REGULATOR_SLP_MEM_DBIAS_S)
#define PMU_HP_SLEEP_HP_REGULATOR_SLP_MEM_DBIAS_V  0x0000000FU
#define PMU_HP_SLEEP_HP_REGULATOR_SLP_MEM_DBIAS_S  19
/** PMU_HP_SLEEP_HP_REGULATOR_SLP_LOGIC_DBIAS : R/W; bitpos: [26:23]; default: 8;
 *  need_des
 */
#define PMU_HP_SLEEP_HP_REGULATOR_SLP_LOGIC_DBIAS    0x0000000FU
#define PMU_HP_SLEEP_HP_REGULATOR_SLP_LOGIC_DBIAS_M  (PMU_HP_SLEEP_HP_REGULATOR_SLP_LOGIC_DBIAS_V << PMU_HP_SLEEP_HP_REGULATOR_SLP_LOGIC_DBIAS_S)
#define PMU_HP_SLEEP_HP_REGULATOR_SLP_LOGIC_DBIAS_V  0x0000000FU
#define PMU_HP_SLEEP_HP_REGULATOR_SLP_LOGIC_DBIAS_S  23
/** PMU_HP_SLEEP_HP_REGULATOR_DBIAS : R/W; bitpos: [31:27]; default: 16;
 *  need_des
 */
#define PMU_HP_SLEEP_HP_REGULATOR_DBIAS    0x0000001FU
#define PMU_HP_SLEEP_HP_REGULATOR_DBIAS_M  (PMU_HP_SLEEP_HP_REGULATOR_DBIAS_V << PMU_HP_SLEEP_HP_REGULATOR_DBIAS_S)
#define PMU_HP_SLEEP_HP_REGULATOR_DBIAS_V  0x0000001FU
#define PMU_HP_SLEEP_HP_REGULATOR_DBIAS_S  27

/** PMU_HP_SLEEP_HP_REGULATOR1_REG register
 *  need_des
 */
#define PMU_HP_SLEEP_HP_REGULATOR1_REG (DR_REG_PMU_BASE + 0x94)
/** PMU_HP_SLEEP_HP_REGULATOR_DRV_B : R/W; bitpos: [31:8]; default: 0;
 *  need_des
 */
#define PMU_HP_SLEEP_HP_REGULATOR_DRV_B    0x00FFFFFFU
#define PMU_HP_SLEEP_HP_REGULATOR_DRV_B_M  (PMU_HP_SLEEP_HP_REGULATOR_DRV_B_V << PMU_HP_SLEEP_HP_REGULATOR_DRV_B_S)
#define PMU_HP_SLEEP_HP_REGULATOR_DRV_B_V  0x00FFFFFFU
#define PMU_HP_SLEEP_HP_REGULATOR_DRV_B_S  8

/** PMU_HP_SLEEP_XTAL_REG register
 *  need_des
 */
#define PMU_HP_SLEEP_XTAL_REG (DR_REG_PMU_BASE + 0x98)
/** PMU_HP_SLEEP_XPD_XTAL : R/W; bitpos: [31]; default: 1;
 *  need_des
 */
#define PMU_HP_SLEEP_XPD_XTAL    (BIT(31))
#define PMU_HP_SLEEP_XPD_XTAL_M  (PMU_HP_SLEEP_XPD_XTAL_V << PMU_HP_SLEEP_XPD_XTAL_S)
#define PMU_HP_SLEEP_XPD_XTAL_V  0x00000001U
#define PMU_HP_SLEEP_XPD_XTAL_S  31

/** PMU_HP_SLEEP_LP_REGULATOR0_REG register
 *  need_des
 */
#define PMU_HP_SLEEP_LP_REGULATOR0_REG (DR_REG_PMU_BASE + 0x9c)
/** PMU_HP_SLEEP_LP_REGULATOR_SLP_XPD : R/W; bitpos: [21]; default: 1;
 *  need_des
 */
#define PMU_HP_SLEEP_LP_REGULATOR_SLP_XPD    (BIT(21))
#define PMU_HP_SLEEP_LP_REGULATOR_SLP_XPD_M  (PMU_HP_SLEEP_LP_REGULATOR_SLP_XPD_V << PMU_HP_SLEEP_LP_REGULATOR_SLP_XPD_S)
#define PMU_HP_SLEEP_LP_REGULATOR_SLP_XPD_V  0x00000001U
#define PMU_HP_SLEEP_LP_REGULATOR_SLP_XPD_S  21
/** PMU_HP_SLEEP_LP_REGULATOR_XPD : R/W; bitpos: [22]; default: 1;
 *  need_des
 */
#define PMU_HP_SLEEP_LP_REGULATOR_XPD    (BIT(22))
#define PMU_HP_SLEEP_LP_REGULATOR_XPD_M  (PMU_HP_SLEEP_LP_REGULATOR_XPD_V << PMU_HP_SLEEP_LP_REGULATOR_XPD_S)
#define PMU_HP_SLEEP_LP_REGULATOR_XPD_V  0x00000001U
#define PMU_HP_SLEEP_LP_REGULATOR_XPD_S  22
/** PMU_HP_SLEEP_LP_REGULATOR_SLP_DBIAS : R/W; bitpos: [26:23]; default: 8;
 *  need_des
 */
#define PMU_HP_SLEEP_LP_REGULATOR_SLP_DBIAS    0x0000000FU
#define PMU_HP_SLEEP_LP_REGULATOR_SLP_DBIAS_M  (PMU_HP_SLEEP_LP_REGULATOR_SLP_DBIAS_V << PMU_HP_SLEEP_LP_REGULATOR_SLP_DBIAS_S)
#define PMU_HP_SLEEP_LP_REGULATOR_SLP_DBIAS_V  0x0000000FU
#define PMU_HP_SLEEP_LP_REGULATOR_SLP_DBIAS_S  23
/** PMU_HP_SLEEP_LP_REGULATOR_DBIAS : R/W; bitpos: [31:27]; default: 17;
 *  need_des
 */
#define PMU_HP_SLEEP_LP_REGULATOR_DBIAS    0x0000001FU
#define PMU_HP_SLEEP_LP_REGULATOR_DBIAS_M  (PMU_HP_SLEEP_LP_REGULATOR_DBIAS_V << PMU_HP_SLEEP_LP_REGULATOR_DBIAS_S)
#define PMU_HP_SLEEP_LP_REGULATOR_DBIAS_V  0x0000001FU
#define PMU_HP_SLEEP_LP_REGULATOR_DBIAS_S  27

/** PMU_HP_SLEEP_LP_REGULATOR1_REG register
 *  need_des
 */
#define PMU_HP_SLEEP_LP_REGULATOR1_REG (DR_REG_PMU_BASE + 0xa0)
/** PMU_HP_SLEEP_LP_REGULATOR_DRV_B : R/W; bitpos: [31:28]; default: 0;
 *  need_des
 */
#define PMU_HP_SLEEP_LP_REGULATOR_DRV_B    0x0000000FU
#define PMU_HP_SLEEP_LP_REGULATOR_DRV_B_M  (PMU_HP_SLEEP_LP_REGULATOR_DRV_B_V << PMU_HP_SLEEP_LP_REGULATOR_DRV_B_S)
#define PMU_HP_SLEEP_LP_REGULATOR_DRV_B_V  0x0000000FU
#define PMU_HP_SLEEP_LP_REGULATOR_DRV_B_S  28

/** PMU_HP_SLEEP_LP_DCDC_RESERVE_REG register
 *  need_des
 */
#define PMU_HP_SLEEP_LP_DCDC_RESERVE_REG (DR_REG_PMU_BASE + 0xa4)
/** PMU_HP_SLEEP_LP_DCDC_RESERVE : WT; bitpos: [31:0]; default: 0;
 *  need_des
 */
#define PMU_HP_SLEEP_LP_DCDC_RESERVE    0xFFFFFFFFU
#define PMU_HP_SLEEP_LP_DCDC_RESERVE_M  (PMU_HP_SLEEP_LP_DCDC_RESERVE_V << PMU_HP_SLEEP_LP_DCDC_RESERVE_S)
#define PMU_HP_SLEEP_LP_DCDC_RESERVE_V  0xFFFFFFFFU
#define PMU_HP_SLEEP_LP_DCDC_RESERVE_S  0

/** PMU_HP_SLEEP_LP_DIG_POWER_REG register
 *  need_des
 */
#define PMU_HP_SLEEP_LP_DIG_POWER_REG (DR_REG_PMU_BASE + 0xa8)
/** PMU_HP_SLEEP_BOD_SOURCE_SEL : R/W; bitpos: [27]; default: 0;
 *  need_des
 */
#define PMU_HP_SLEEP_BOD_SOURCE_SEL    (BIT(27))
#define PMU_HP_SLEEP_BOD_SOURCE_SEL_M  (PMU_HP_SLEEP_BOD_SOURCE_SEL_V << PMU_HP_SLEEP_BOD_SOURCE_SEL_S)
#define PMU_HP_SLEEP_BOD_SOURCE_SEL_V  0x00000001U
#define PMU_HP_SLEEP_BOD_SOURCE_SEL_S  27
/** PMU_HP_SLEEP_VDDBAT_MODE : R/W; bitpos: [29:28]; default: 0;
 *  need_des
 */
#define PMU_HP_SLEEP_VDDBAT_MODE    0x00000003U
#define PMU_HP_SLEEP_VDDBAT_MODE_M  (PMU_HP_SLEEP_VDDBAT_MODE_V << PMU_HP_SLEEP_VDDBAT_MODE_S)
#define PMU_HP_SLEEP_VDDBAT_MODE_V  0x00000003U
#define PMU_HP_SLEEP_VDDBAT_MODE_S  28
/** PMU_HP_SLEEP_LP_MEM_DSLP : R/W; bitpos: [30]; default: 0;
 *  need_des
 */
#define PMU_HP_SLEEP_LP_MEM_DSLP    (BIT(30))
#define PMU_HP_SLEEP_LP_MEM_DSLP_M  (PMU_HP_SLEEP_LP_MEM_DSLP_V << PMU_HP_SLEEP_LP_MEM_DSLP_S)
#define PMU_HP_SLEEP_LP_MEM_DSLP_V  0x00000001U
#define PMU_HP_SLEEP_LP_MEM_DSLP_S  30
/** PMU_HP_SLEEP_PD_LP_PERI_PD_EN : R/W; bitpos: [31]; default: 0;
 *  need_des
 */
#define PMU_HP_SLEEP_PD_LP_PERI_PD_EN    (BIT(31))
#define PMU_HP_SLEEP_PD_LP_PERI_PD_EN_M  (PMU_HP_SLEEP_PD_LP_PERI_PD_EN_V << PMU_HP_SLEEP_PD_LP_PERI_PD_EN_S)
#define PMU_HP_SLEEP_PD_LP_PERI_PD_EN_V  0x00000001U
#define PMU_HP_SLEEP_PD_LP_PERI_PD_EN_S  31

/** PMU_HP_SLEEP_LP_CK_POWER_REG register
 *  need_des
 */
#define PMU_HP_SLEEP_LP_CK_POWER_REG (DR_REG_PMU_BASE + 0xac)
/** PMU_HP_SLEEP_XPD_LPPLL : R/W; bitpos: [27]; default: 0;
 *  need_des
 */
#define PMU_HP_SLEEP_XPD_LPPLL    (BIT(27))
#define PMU_HP_SLEEP_XPD_LPPLL_M  (PMU_HP_SLEEP_XPD_LPPLL_V << PMU_HP_SLEEP_XPD_LPPLL_S)
#define PMU_HP_SLEEP_XPD_LPPLL_V  0x00000001U
#define PMU_HP_SLEEP_XPD_LPPLL_S  27
/** PMU_HP_SLEEP_XPD_XTAL32K : R/W; bitpos: [28]; default: 0;
 *  need_des
 */
#define PMU_HP_SLEEP_XPD_XTAL32K    (BIT(28))
#define PMU_HP_SLEEP_XPD_XTAL32K_M  (PMU_HP_SLEEP_XPD_XTAL32K_V << PMU_HP_SLEEP_XPD_XTAL32K_S)
#define PMU_HP_SLEEP_XPD_XTAL32K_V  0x00000001U
#define PMU_HP_SLEEP_XPD_XTAL32K_S  28
/** PMU_HP_SLEEP_XPD_RC32K : R/W; bitpos: [29]; default: 0;
 *  need_des
 */
#define PMU_HP_SLEEP_XPD_RC32K    (BIT(29))
#define PMU_HP_SLEEP_XPD_RC32K_M  (PMU_HP_SLEEP_XPD_RC32K_V << PMU_HP_SLEEP_XPD_RC32K_S)
#define PMU_HP_SLEEP_XPD_RC32K_V  0x00000001U
#define PMU_HP_SLEEP_XPD_RC32K_S  29
/** PMU_HP_SLEEP_XPD_FOSC_CLK : R/W; bitpos: [30]; default: 1;
 *  need_des
 */
#define PMU_HP_SLEEP_XPD_FOSC_CLK    (BIT(30))
#define PMU_HP_SLEEP_XPD_FOSC_CLK_M  (PMU_HP_SLEEP_XPD_FOSC_CLK_V << PMU_HP_SLEEP_XPD_FOSC_CLK_S)
#define PMU_HP_SLEEP_XPD_FOSC_CLK_V  0x00000001U
#define PMU_HP_SLEEP_XPD_FOSC_CLK_S  30
/** PMU_HP_SLEEP_PD_OSC_CLK : R/W; bitpos: [31]; default: 0;
 *  need_des
 */
#define PMU_HP_SLEEP_PD_OSC_CLK    (BIT(31))
#define PMU_HP_SLEEP_PD_OSC_CLK_M  (PMU_HP_SLEEP_PD_OSC_CLK_V << PMU_HP_SLEEP_PD_OSC_CLK_S)
#define PMU_HP_SLEEP_PD_OSC_CLK_V  0x00000001U
#define PMU_HP_SLEEP_PD_OSC_CLK_S  31

/** PMU_LP_SLEEP_LP_BIAS_RESERVE_REG register
 *  need_des
 */
#define PMU_LP_SLEEP_LP_BIAS_RESERVE_REG (DR_REG_PMU_BASE + 0xb0)
/** PMU_LP_SLEEP_LP_BIAS_RESERVE : WT; bitpos: [31:0]; default: 0;
 *  need_des
 */
#define PMU_LP_SLEEP_LP_BIAS_RESERVE    0xFFFFFFFFU
#define PMU_LP_SLEEP_LP_BIAS_RESERVE_M  (PMU_LP_SLEEP_LP_BIAS_RESERVE_V << PMU_LP_SLEEP_LP_BIAS_RESERVE_S)
#define PMU_LP_SLEEP_LP_BIAS_RESERVE_V  0xFFFFFFFFU
#define PMU_LP_SLEEP_LP_BIAS_RESERVE_S  0

/** PMU_LP_SLEEP_LP_REGULATOR0_REG register
 *  need_des
 */
#define PMU_LP_SLEEP_LP_REGULATOR0_REG (DR_REG_PMU_BASE + 0xb4)
/** PMU_LP_SLEEP_LP_REGULATOR_SLP_XPD : R/W; bitpos: [21]; default: 1;
 *  need_des
 */
#define PMU_LP_SLEEP_LP_REGULATOR_SLP_XPD    (BIT(21))
#define PMU_LP_SLEEP_LP_REGULATOR_SLP_XPD_M  (PMU_LP_SLEEP_LP_REGULATOR_SLP_XPD_V << PMU_LP_SLEEP_LP_REGULATOR_SLP_XPD_S)
#define PMU_LP_SLEEP_LP_REGULATOR_SLP_XPD_V  0x00000001U
#define PMU_LP_SLEEP_LP_REGULATOR_SLP_XPD_S  21
/** PMU_LP_SLEEP_LP_REGULATOR_XPD : R/W; bitpos: [22]; default: 1;
 *  need_des
 */
#define PMU_LP_SLEEP_LP_REGULATOR_XPD    (BIT(22))
#define PMU_LP_SLEEP_LP_REGULATOR_XPD_M  (PMU_LP_SLEEP_LP_REGULATOR_XPD_V << PMU_LP_SLEEP_LP_REGULATOR_XPD_S)
#define PMU_LP_SLEEP_LP_REGULATOR_XPD_V  0x00000001U
#define PMU_LP_SLEEP_LP_REGULATOR_XPD_S  22
/** PMU_LP_SLEEP_LP_REGULATOR_SLP_DBIAS : R/W; bitpos: [26:23]; default: 8;
 *  need_des
 */
#define PMU_LP_SLEEP_LP_REGULATOR_SLP_DBIAS    0x0000000FU
#define PMU_LP_SLEEP_LP_REGULATOR_SLP_DBIAS_M  (PMU_LP_SLEEP_LP_REGULATOR_SLP_DBIAS_V << PMU_LP_SLEEP_LP_REGULATOR_SLP_DBIAS_S)
#define PMU_LP_SLEEP_LP_REGULATOR_SLP_DBIAS_V  0x0000000FU
#define PMU_LP_SLEEP_LP_REGULATOR_SLP_DBIAS_S  23
/** PMU_LP_SLEEP_LP_REGULATOR_DBIAS : R/W; bitpos: [31:27]; default: 17;
 *  need_des
 */
#define PMU_LP_SLEEP_LP_REGULATOR_DBIAS    0x0000001FU
#define PMU_LP_SLEEP_LP_REGULATOR_DBIAS_M  (PMU_LP_SLEEP_LP_REGULATOR_DBIAS_V << PMU_LP_SLEEP_LP_REGULATOR_DBIAS_S)
#define PMU_LP_SLEEP_LP_REGULATOR_DBIAS_V  0x0000001FU
#define PMU_LP_SLEEP_LP_REGULATOR_DBIAS_S  27

/** PMU_LP_SLEEP_LP_REGULATOR1_REG register
 *  need_des
 */
#define PMU_LP_SLEEP_LP_REGULATOR1_REG (DR_REG_PMU_BASE + 0xb8)
/** PMU_LP_SLEEP_LP_REGULATOR_DRV_B : R/W; bitpos: [31:28]; default: 0;
 *  need_des
 */
#define PMU_LP_SLEEP_LP_REGULATOR_DRV_B    0x0000000FU
#define PMU_LP_SLEEP_LP_REGULATOR_DRV_B_M  (PMU_LP_SLEEP_LP_REGULATOR_DRV_B_V << PMU_LP_SLEEP_LP_REGULATOR_DRV_B_S)
#define PMU_LP_SLEEP_LP_REGULATOR_DRV_B_V  0x0000000FU
#define PMU_LP_SLEEP_LP_REGULATOR_DRV_B_S  28

/** PMU_LP_SLEEP_XTAL_REG register
 *  need_des
 */
#define PMU_LP_SLEEP_XTAL_REG (DR_REG_PMU_BASE + 0xbc)
/** PMU_LP_SLEEP_XPD_XTAL : R/W; bitpos: [31]; default: 1;
 *  need_des
 */
#define PMU_LP_SLEEP_XPD_XTAL    (BIT(31))
#define PMU_LP_SLEEP_XPD_XTAL_M  (PMU_LP_SLEEP_XPD_XTAL_V << PMU_LP_SLEEP_XPD_XTAL_S)
#define PMU_LP_SLEEP_XPD_XTAL_V  0x00000001U
#define PMU_LP_SLEEP_XPD_XTAL_S  31

/** PMU_LP_SLEEP_LP_DIG_POWER_REG register
 *  need_des
 */
#define PMU_LP_SLEEP_LP_DIG_POWER_REG (DR_REG_PMU_BASE + 0xc0)
/** PMU_LP_SLEEP_BOD_SOURCE_SEL : R/W; bitpos: [27]; default: 0;
 *  need_des
 */
#define PMU_LP_SLEEP_BOD_SOURCE_SEL    (BIT(27))
#define PMU_LP_SLEEP_BOD_SOURCE_SEL_M  (PMU_LP_SLEEP_BOD_SOURCE_SEL_V << PMU_LP_SLEEP_BOD_SOURCE_SEL_S)
#define PMU_LP_SLEEP_BOD_SOURCE_SEL_V  0x00000001U
#define PMU_LP_SLEEP_BOD_SOURCE_SEL_S  27
/** PMU_LP_SLEEP_VDDBAT_MODE : R/W; bitpos: [29:28]; default: 0;
 *  need_des
 */
#define PMU_LP_SLEEP_VDDBAT_MODE    0x00000003U
#define PMU_LP_SLEEP_VDDBAT_MODE_M  (PMU_LP_SLEEP_VDDBAT_MODE_V << PMU_LP_SLEEP_VDDBAT_MODE_S)
#define PMU_LP_SLEEP_VDDBAT_MODE_V  0x00000003U
#define PMU_LP_SLEEP_VDDBAT_MODE_S  28
/** PMU_LP_SLEEP_LP_MEM_DSLP : R/W; bitpos: [30]; default: 0;
 *  need_des
 */
#define PMU_LP_SLEEP_LP_MEM_DSLP    (BIT(30))
#define PMU_LP_SLEEP_LP_MEM_DSLP_M  (PMU_LP_SLEEP_LP_MEM_DSLP_V << PMU_LP_SLEEP_LP_MEM_DSLP_S)
#define PMU_LP_SLEEP_LP_MEM_DSLP_V  0x00000001U
#define PMU_LP_SLEEP_LP_MEM_DSLP_S  30
/** PMU_LP_SLEEP_PD_LP_PERI_PD_EN : R/W; bitpos: [31]; default: 0;
 *  need_des
 */
#define PMU_LP_SLEEP_PD_LP_PERI_PD_EN    (BIT(31))
#define PMU_LP_SLEEP_PD_LP_PERI_PD_EN_M  (PMU_LP_SLEEP_PD_LP_PERI_PD_EN_V << PMU_LP_SLEEP_PD_LP_PERI_PD_EN_S)
#define PMU_LP_SLEEP_PD_LP_PERI_PD_EN_V  0x00000001U
#define PMU_LP_SLEEP_PD_LP_PERI_PD_EN_S  31

/** PMU_LP_SLEEP_LP_CK_POWER_REG register
 *  need_des
 */
#define PMU_LP_SLEEP_LP_CK_POWER_REG (DR_REG_PMU_BASE + 0xc4)
/** PMU_LP_SLEEP_XPD_LPPLL : R/W; bitpos: [27]; default: 0;
 *  need_des
 */
#define PMU_LP_SLEEP_XPD_LPPLL    (BIT(27))
#define PMU_LP_SLEEP_XPD_LPPLL_M  (PMU_LP_SLEEP_XPD_LPPLL_V << PMU_LP_SLEEP_XPD_LPPLL_S)
#define PMU_LP_SLEEP_XPD_LPPLL_V  0x00000001U
#define PMU_LP_SLEEP_XPD_LPPLL_S  27
/** PMU_LP_SLEEP_XPD_XTAL32K : R/W; bitpos: [28]; default: 0;
 *  need_des
 */
#define PMU_LP_SLEEP_XPD_XTAL32K    (BIT(28))
#define PMU_LP_SLEEP_XPD_XTAL32K_M  (PMU_LP_SLEEP_XPD_XTAL32K_V << PMU_LP_SLEEP_XPD_XTAL32K_S)
#define PMU_LP_SLEEP_XPD_XTAL32K_V  0x00000001U
#define PMU_LP_SLEEP_XPD_XTAL32K_S  28
/** PMU_LP_SLEEP_XPD_RC32K : R/W; bitpos: [29]; default: 0;
 *  need_des
 */
#define PMU_LP_SLEEP_XPD_RC32K    (BIT(29))
#define PMU_LP_SLEEP_XPD_RC32K_M  (PMU_LP_SLEEP_XPD_RC32K_V << PMU_LP_SLEEP_XPD_RC32K_S)
#define PMU_LP_SLEEP_XPD_RC32K_V  0x00000001U
#define PMU_LP_SLEEP_XPD_RC32K_S  29
/** PMU_LP_SLEEP_XPD_FOSC_CLK : R/W; bitpos: [30]; default: 1;
 *  need_des
 */
#define PMU_LP_SLEEP_XPD_FOSC_CLK    (BIT(30))
#define PMU_LP_SLEEP_XPD_FOSC_CLK_M  (PMU_LP_SLEEP_XPD_FOSC_CLK_V << PMU_LP_SLEEP_XPD_FOSC_CLK_S)
#define PMU_LP_SLEEP_XPD_FOSC_CLK_V  0x00000001U
#define PMU_LP_SLEEP_XPD_FOSC_CLK_S  30
/** PMU_LP_SLEEP_PD_OSC_CLK : R/W; bitpos: [31]; default: 0;
 *  need_des
 */
#define PMU_LP_SLEEP_PD_OSC_CLK    (BIT(31))
#define PMU_LP_SLEEP_PD_OSC_CLK_M  (PMU_LP_SLEEP_PD_OSC_CLK_V << PMU_LP_SLEEP_PD_OSC_CLK_S)
#define PMU_LP_SLEEP_PD_OSC_CLK_V  0x00000001U
#define PMU_LP_SLEEP_PD_OSC_CLK_S  31

/** PMU_LP_SLEEP_BIAS_REG register
 *  need_des
 */
#define PMU_LP_SLEEP_BIAS_REG (DR_REG_PMU_BASE + 0xc8)
/** PMU_LP_SLEEP_XPD_BIAS : R/W; bitpos: [25]; default: 0;
 *  need_des
 */
#define PMU_LP_SLEEP_XPD_BIAS    (BIT(25))
#define PMU_LP_SLEEP_XPD_BIAS_M  (PMU_LP_SLEEP_XPD_BIAS_V << PMU_LP_SLEEP_XPD_BIAS_S)
#define PMU_LP_SLEEP_XPD_BIAS_V  0x00000001U
#define PMU_LP_SLEEP_XPD_BIAS_S  25
/** PMU_LP_SLEEP_PD_CUR : R/W; bitpos: [30]; default: 0;
 *  need_des
 */
#define PMU_LP_SLEEP_PD_CUR    (BIT(30))
#define PMU_LP_SLEEP_PD_CUR_M  (PMU_LP_SLEEP_PD_CUR_V << PMU_LP_SLEEP_PD_CUR_S)
#define PMU_LP_SLEEP_PD_CUR_V  0x00000001U
#define PMU_LP_SLEEP_PD_CUR_S  30
/** PMU_LP_SLEEP_BIAS_SLEEP : R/W; bitpos: [31]; default: 0;
 *  need_des
 */
#define PMU_LP_SLEEP_BIAS_SLEEP    (BIT(31))
#define PMU_LP_SLEEP_BIAS_SLEEP_M  (PMU_LP_SLEEP_BIAS_SLEEP_V << PMU_LP_SLEEP_BIAS_SLEEP_S)
#define PMU_LP_SLEEP_BIAS_SLEEP_V  0x00000001U
#define PMU_LP_SLEEP_BIAS_SLEEP_S  31

/** PMU_IMM_HP_CK_POWER_REG register
 *  need_des
 */
#define PMU_IMM_HP_CK_POWER_REG (DR_REG_PMU_BASE + 0xcc)
/** PMU_TIE_LOW_GLOBAL_BBPLL_ICG : WT; bitpos: [0]; default: 0;
 *  need_des
 */
#define PMU_TIE_LOW_GLOBAL_BBPLL_ICG    (BIT(0))
#define PMU_TIE_LOW_GLOBAL_BBPLL_ICG_M  (PMU_TIE_LOW_GLOBAL_BBPLL_ICG_V << PMU_TIE_LOW_GLOBAL_BBPLL_ICG_S)
#define PMU_TIE_LOW_GLOBAL_BBPLL_ICG_V  0x00000001U
#define PMU_TIE_LOW_GLOBAL_BBPLL_ICG_S  0
/** PMU_TIE_LOW_GLOBAL_XTAL_ICG : WT; bitpos: [1]; default: 0;
 *  need_des
 */
#define PMU_TIE_LOW_GLOBAL_XTAL_ICG    (BIT(1))
#define PMU_TIE_LOW_GLOBAL_XTAL_ICG_M  (PMU_TIE_LOW_GLOBAL_XTAL_ICG_V << PMU_TIE_LOW_GLOBAL_XTAL_ICG_S)
#define PMU_TIE_LOW_GLOBAL_XTAL_ICG_V  0x00000001U
#define PMU_TIE_LOW_GLOBAL_XTAL_ICG_S  1
/** PMU_TIE_LOW_I2C_RETENTION : WT; bitpos: [2]; default: 0;
 *  need_des
 */
#define PMU_TIE_LOW_I2C_RETENTION    (BIT(2))
#define PMU_TIE_LOW_I2C_RETENTION_M  (PMU_TIE_LOW_I2C_RETENTION_V << PMU_TIE_LOW_I2C_RETENTION_S)
#define PMU_TIE_LOW_I2C_RETENTION_V  0x00000001U
#define PMU_TIE_LOW_I2C_RETENTION_S  2
/** PMU_TIE_LOW_XPD_BB_I2C : WT; bitpos: [3]; default: 0;
 *  need_des
 */
#define PMU_TIE_LOW_XPD_BB_I2C    (BIT(3))
#define PMU_TIE_LOW_XPD_BB_I2C_M  (PMU_TIE_LOW_XPD_BB_I2C_V << PMU_TIE_LOW_XPD_BB_I2C_S)
#define PMU_TIE_LOW_XPD_BB_I2C_V  0x00000001U
#define PMU_TIE_LOW_XPD_BB_I2C_S  3
/** PMU_TIE_LOW_XPD_BBPLL_I2C : WT; bitpos: [4]; default: 0;
 *  need_des
 */
#define PMU_TIE_LOW_XPD_BBPLL_I2C    (BIT(4))
#define PMU_TIE_LOW_XPD_BBPLL_I2C_M  (PMU_TIE_LOW_XPD_BBPLL_I2C_V << PMU_TIE_LOW_XPD_BBPLL_I2C_S)
#define PMU_TIE_LOW_XPD_BBPLL_I2C_V  0x00000001U
#define PMU_TIE_LOW_XPD_BBPLL_I2C_S  4
/** PMU_TIE_LOW_XPD_BBPLL : WT; bitpos: [5]; default: 0;
 *  need_des
 */
#define PMU_TIE_LOW_XPD_BBPLL    (BIT(5))
#define PMU_TIE_LOW_XPD_BBPLL_M  (PMU_TIE_LOW_XPD_BBPLL_V << PMU_TIE_LOW_XPD_BBPLL_S)
#define PMU_TIE_LOW_XPD_BBPLL_V  0x00000001U
#define PMU_TIE_LOW_XPD_BBPLL_S  5
/** PMU_TIE_LOW_XPD_XTAL : WT; bitpos: [6]; default: 0;
 *  need_des
 */
#define PMU_TIE_LOW_XPD_XTAL    (BIT(6))
#define PMU_TIE_LOW_XPD_XTAL_M  (PMU_TIE_LOW_XPD_XTAL_V << PMU_TIE_LOW_XPD_XTAL_S)
#define PMU_TIE_LOW_XPD_XTAL_V  0x00000001U
#define PMU_TIE_LOW_XPD_XTAL_S  6
/** PMU_TIE_HIGH_GLOBAL_BBPLL_ICG : WT; bitpos: [25]; default: 0;
 *  need_des
 */
#define PMU_TIE_HIGH_GLOBAL_BBPLL_ICG    (BIT(25))
#define PMU_TIE_HIGH_GLOBAL_BBPLL_ICG_M  (PMU_TIE_HIGH_GLOBAL_BBPLL_ICG_V << PMU_TIE_HIGH_GLOBAL_BBPLL_ICG_S)
#define PMU_TIE_HIGH_GLOBAL_BBPLL_ICG_V  0x00000001U
#define PMU_TIE_HIGH_GLOBAL_BBPLL_ICG_S  25
/** PMU_TIE_HIGH_GLOBAL_XTAL_ICG : WT; bitpos: [26]; default: 0;
 *  need_des
 */
#define PMU_TIE_HIGH_GLOBAL_XTAL_ICG    (BIT(26))
#define PMU_TIE_HIGH_GLOBAL_XTAL_ICG_M  (PMU_TIE_HIGH_GLOBAL_XTAL_ICG_V << PMU_TIE_HIGH_GLOBAL_XTAL_ICG_S)
#define PMU_TIE_HIGH_GLOBAL_XTAL_ICG_V  0x00000001U
#define PMU_TIE_HIGH_GLOBAL_XTAL_ICG_S  26
/** PMU_TIE_HIGH_I2C_RETENTION : WT; bitpos: [27]; default: 0;
 *  need_des
 */
#define PMU_TIE_HIGH_I2C_RETENTION    (BIT(27))
#define PMU_TIE_HIGH_I2C_RETENTION_M  (PMU_TIE_HIGH_I2C_RETENTION_V << PMU_TIE_HIGH_I2C_RETENTION_S)
#define PMU_TIE_HIGH_I2C_RETENTION_V  0x00000001U
#define PMU_TIE_HIGH_I2C_RETENTION_S  27
/** PMU_TIE_HIGH_XPD_BB_I2C : WT; bitpos: [28]; default: 0;
 *  need_des
 */
#define PMU_TIE_HIGH_XPD_BB_I2C    (BIT(28))
#define PMU_TIE_HIGH_XPD_BB_I2C_M  (PMU_TIE_HIGH_XPD_BB_I2C_V << PMU_TIE_HIGH_XPD_BB_I2C_S)
#define PMU_TIE_HIGH_XPD_BB_I2C_V  0x00000001U
#define PMU_TIE_HIGH_XPD_BB_I2C_S  28
/** PMU_TIE_HIGH_XPD_BBPLL_I2C : WT; bitpos: [29]; default: 0;
 *  need_des
 */
#define PMU_TIE_HIGH_XPD_BBPLL_I2C    (BIT(29))
#define PMU_TIE_HIGH_XPD_BBPLL_I2C_M  (PMU_TIE_HIGH_XPD_BBPLL_I2C_V << PMU_TIE_HIGH_XPD_BBPLL_I2C_S)
#define PMU_TIE_HIGH_XPD_BBPLL_I2C_V  0x00000001U
#define PMU_TIE_HIGH_XPD_BBPLL_I2C_S  29
/** PMU_TIE_HIGH_XPD_BBPLL : WT; bitpos: [30]; default: 0;
 *  need_des
 */
#define PMU_TIE_HIGH_XPD_BBPLL    (BIT(30))
#define PMU_TIE_HIGH_XPD_BBPLL_M  (PMU_TIE_HIGH_XPD_BBPLL_V << PMU_TIE_HIGH_XPD_BBPLL_S)
#define PMU_TIE_HIGH_XPD_BBPLL_V  0x00000001U
#define PMU_TIE_HIGH_XPD_BBPLL_S  30
/** PMU_TIE_HIGH_XPD_XTAL : WT; bitpos: [31]; default: 0;
 *  need_des
 */
#define PMU_TIE_HIGH_XPD_XTAL    (BIT(31))
#define PMU_TIE_HIGH_XPD_XTAL_M  (PMU_TIE_HIGH_XPD_XTAL_V << PMU_TIE_HIGH_XPD_XTAL_S)
#define PMU_TIE_HIGH_XPD_XTAL_V  0x00000001U
#define PMU_TIE_HIGH_XPD_XTAL_S  31

/** PMU_IMM_SLEEP_SYSCLK_REG register
 *  need_des
 */
#define PMU_IMM_SLEEP_SYSCLK_REG (DR_REG_PMU_BASE + 0xd0)
/** PMU_UPDATE_DIG_ICG_SWITCH : WT; bitpos: [28]; default: 0;
 *  need_des
 */
#define PMU_UPDATE_DIG_ICG_SWITCH    (BIT(28))
#define PMU_UPDATE_DIG_ICG_SWITCH_M  (PMU_UPDATE_DIG_ICG_SWITCH_V << PMU_UPDATE_DIG_ICG_SWITCH_S)
#define PMU_UPDATE_DIG_ICG_SWITCH_V  0x00000001U
#define PMU_UPDATE_DIG_ICG_SWITCH_S  28
/** PMU_TIE_LOW_ICG_SLP_SEL : WT; bitpos: [29]; default: 0;
 *  need_des
 */
#define PMU_TIE_LOW_ICG_SLP_SEL    (BIT(29))
#define PMU_TIE_LOW_ICG_SLP_SEL_M  (PMU_TIE_LOW_ICG_SLP_SEL_V << PMU_TIE_LOW_ICG_SLP_SEL_S)
#define PMU_TIE_LOW_ICG_SLP_SEL_V  0x00000001U
#define PMU_TIE_LOW_ICG_SLP_SEL_S  29
/** PMU_TIE_HIGH_ICG_SLP_SEL : WT; bitpos: [30]; default: 0;
 *  need_des
 */
#define PMU_TIE_HIGH_ICG_SLP_SEL    (BIT(30))
#define PMU_TIE_HIGH_ICG_SLP_SEL_M  (PMU_TIE_HIGH_ICG_SLP_SEL_V << PMU_TIE_HIGH_ICG_SLP_SEL_S)
#define PMU_TIE_HIGH_ICG_SLP_SEL_V  0x00000001U
#define PMU_TIE_HIGH_ICG_SLP_SEL_S  30
/** PMU_UPDATE_DIG_SYS_CLK_SEL : WT; bitpos: [31]; default: 0;
 *  need_des
 */
#define PMU_UPDATE_DIG_SYS_CLK_SEL    (BIT(31))
#define PMU_UPDATE_DIG_SYS_CLK_SEL_M  (PMU_UPDATE_DIG_SYS_CLK_SEL_V << PMU_UPDATE_DIG_SYS_CLK_SEL_S)
#define PMU_UPDATE_DIG_SYS_CLK_SEL_V  0x00000001U
#define PMU_UPDATE_DIG_SYS_CLK_SEL_S  31

/** PMU_IMM_HP_FUNC_ICG_REG register
 *  need_des
 */
#define PMU_IMM_HP_FUNC_ICG_REG (DR_REG_PMU_BASE + 0xd4)
/** PMU_UPDATE_DIG_ICG_FUNC_EN : WT; bitpos: [31]; default: 0;
 *  need_des
 */
#define PMU_UPDATE_DIG_ICG_FUNC_EN    (BIT(31))
#define PMU_UPDATE_DIG_ICG_FUNC_EN_M  (PMU_UPDATE_DIG_ICG_FUNC_EN_V << PMU_UPDATE_DIG_ICG_FUNC_EN_S)
#define PMU_UPDATE_DIG_ICG_FUNC_EN_V  0x00000001U
#define PMU_UPDATE_DIG_ICG_FUNC_EN_S  31

/** PMU_IMM_HP_APB_ICG_REG register
 *  need_des
 */
#define PMU_IMM_HP_APB_ICG_REG (DR_REG_PMU_BASE + 0xd8)
/** PMU_UPDATE_DIG_ICG_APB_EN : WT; bitpos: [31]; default: 0;
 *  need_des
 */
#define PMU_UPDATE_DIG_ICG_APB_EN    (BIT(31))
#define PMU_UPDATE_DIG_ICG_APB_EN_M  (PMU_UPDATE_DIG_ICG_APB_EN_V << PMU_UPDATE_DIG_ICG_APB_EN_S)
#define PMU_UPDATE_DIG_ICG_APB_EN_V  0x00000001U
#define PMU_UPDATE_DIG_ICG_APB_EN_S  31

/** PMU_IMM_MODEM_ICG_REG register
 *  need_des
 */
#define PMU_IMM_MODEM_ICG_REG (DR_REG_PMU_BASE + 0xdc)
/** PMU_UPDATE_DIG_ICG_MODEM_EN : WT; bitpos: [31]; default: 0;
 *  need_des
 */
#define PMU_UPDATE_DIG_ICG_MODEM_EN    (BIT(31))
#define PMU_UPDATE_DIG_ICG_MODEM_EN_M  (PMU_UPDATE_DIG_ICG_MODEM_EN_V << PMU_UPDATE_DIG_ICG_MODEM_EN_S)
#define PMU_UPDATE_DIG_ICG_MODEM_EN_V  0x00000001U
#define PMU_UPDATE_DIG_ICG_MODEM_EN_S  31

/** PMU_IMM_LP_ICG_REG register
 *  need_des
 */
#define PMU_IMM_LP_ICG_REG (DR_REG_PMU_BASE + 0xe0)
/** PMU_TIE_LOW_LP_ROOTCLK_SEL : WT; bitpos: [30]; default: 0;
 *  need_des
 */
#define PMU_TIE_LOW_LP_ROOTCLK_SEL    (BIT(30))
#define PMU_TIE_LOW_LP_ROOTCLK_SEL_M  (PMU_TIE_LOW_LP_ROOTCLK_SEL_V << PMU_TIE_LOW_LP_ROOTCLK_SEL_S)
#define PMU_TIE_LOW_LP_ROOTCLK_SEL_V  0x00000001U
#define PMU_TIE_LOW_LP_ROOTCLK_SEL_S  30
/** PMU_TIE_HIGH_LP_ROOTCLK_SEL : WT; bitpos: [31]; default: 0;
 *  need_des
 */
#define PMU_TIE_HIGH_LP_ROOTCLK_SEL    (BIT(31))
#define PMU_TIE_HIGH_LP_ROOTCLK_SEL_M  (PMU_TIE_HIGH_LP_ROOTCLK_SEL_V << PMU_TIE_HIGH_LP_ROOTCLK_SEL_S)
#define PMU_TIE_HIGH_LP_ROOTCLK_SEL_V  0x00000001U
#define PMU_TIE_HIGH_LP_ROOTCLK_SEL_S  31

/** PMU_IMM_PAD_HOLD_ALL_REG register
 *  need_des
 */
#define PMU_IMM_PAD_HOLD_ALL_REG (DR_REG_PMU_BASE + 0xe4)
/** PMU_TIE_HIGH_LP_PAD_HOLD_ALL : WT; bitpos: [28]; default: 0;
 *  need_des
 */
#define PMU_TIE_HIGH_LP_PAD_HOLD_ALL    (BIT(28))
#define PMU_TIE_HIGH_LP_PAD_HOLD_ALL_M  (PMU_TIE_HIGH_LP_PAD_HOLD_ALL_V << PMU_TIE_HIGH_LP_PAD_HOLD_ALL_S)
#define PMU_TIE_HIGH_LP_PAD_HOLD_ALL_V  0x00000001U
#define PMU_TIE_HIGH_LP_PAD_HOLD_ALL_S  28
/** PMU_TIE_LOW_LP_PAD_HOLD_ALL : WT; bitpos: [29]; default: 0;
 *  need_des
 */
#define PMU_TIE_LOW_LP_PAD_HOLD_ALL    (BIT(29))
#define PMU_TIE_LOW_LP_PAD_HOLD_ALL_M  (PMU_TIE_LOW_LP_PAD_HOLD_ALL_V << PMU_TIE_LOW_LP_PAD_HOLD_ALL_S)
#define PMU_TIE_LOW_LP_PAD_HOLD_ALL_V  0x00000001U
#define PMU_TIE_LOW_LP_PAD_HOLD_ALL_S  29
/** PMU_TIE_HIGH_HP_PAD_HOLD_ALL : WT; bitpos: [30]; default: 0;
 *  need_des
 */
#define PMU_TIE_HIGH_HP_PAD_HOLD_ALL    (BIT(30))
#define PMU_TIE_HIGH_HP_PAD_HOLD_ALL_M  (PMU_TIE_HIGH_HP_PAD_HOLD_ALL_V << PMU_TIE_HIGH_HP_PAD_HOLD_ALL_S)
#define PMU_TIE_HIGH_HP_PAD_HOLD_ALL_V  0x00000001U
#define PMU_TIE_HIGH_HP_PAD_HOLD_ALL_S  30
/** PMU_TIE_LOW_HP_PAD_HOLD_ALL : WT; bitpos: [31]; default: 0;
 *  need_des
 */
#define PMU_TIE_LOW_HP_PAD_HOLD_ALL    (BIT(31))
#define PMU_TIE_LOW_HP_PAD_HOLD_ALL_M  (PMU_TIE_LOW_HP_PAD_HOLD_ALL_V << PMU_TIE_LOW_HP_PAD_HOLD_ALL_S)
#define PMU_TIE_LOW_HP_PAD_HOLD_ALL_V  0x00000001U
#define PMU_TIE_LOW_HP_PAD_HOLD_ALL_S  31

/** PMU_IMM_I2C_ISO_REG register
 *  need_des
 */
#define PMU_IMM_I2C_ISO_REG (DR_REG_PMU_BASE + 0xe8)
/** PMU_TIE_HIGH_I2C_ISO_EN : WT; bitpos: [30]; default: 0;
 *  need_des
 */
#define PMU_TIE_HIGH_I2C_ISO_EN    (BIT(30))
#define PMU_TIE_HIGH_I2C_ISO_EN_M  (PMU_TIE_HIGH_I2C_ISO_EN_V << PMU_TIE_HIGH_I2C_ISO_EN_S)
#define PMU_TIE_HIGH_I2C_ISO_EN_V  0x00000001U
#define PMU_TIE_HIGH_I2C_ISO_EN_S  30
/** PMU_TIE_LOW_I2C_ISO_EN : WT; bitpos: [31]; default: 0;
 *  need_des
 */
#define PMU_TIE_LOW_I2C_ISO_EN    (BIT(31))
#define PMU_TIE_LOW_I2C_ISO_EN_M  (PMU_TIE_LOW_I2C_ISO_EN_V << PMU_TIE_LOW_I2C_ISO_EN_S)
#define PMU_TIE_LOW_I2C_ISO_EN_V  0x00000001U
#define PMU_TIE_LOW_I2C_ISO_EN_S  31

/** PMU_POWER_WAIT_TIMER0_REG register
 *  need_des
 */
#define PMU_POWER_WAIT_TIMER0_REG (DR_REG_PMU_BASE + 0xec)
/** PMU_DG_HP_POWERDOWN_TIMER : R/W; bitpos: [13:5]; default: 255;
 *  need_des
 */
#define PMU_DG_HP_POWERDOWN_TIMER    0x000001FFU
#define PMU_DG_HP_POWERDOWN_TIMER_M  (PMU_DG_HP_POWERDOWN_TIMER_V << PMU_DG_HP_POWERDOWN_TIMER_S)
#define PMU_DG_HP_POWERDOWN_TIMER_V  0x000001FFU
#define PMU_DG_HP_POWERDOWN_TIMER_S  5
/** PMU_DG_HP_POWERUP_TIMER : R/W; bitpos: [22:14]; default: 255;
 *  need_des
 */
#define PMU_DG_HP_POWERUP_TIMER    0x000001FFU
#define PMU_DG_HP_POWERUP_TIMER_M  (PMU_DG_HP_POWERUP_TIMER_V << PMU_DG_HP_POWERUP_TIMER_S)
#define PMU_DG_HP_POWERUP_TIMER_V  0x000001FFU
#define PMU_DG_HP_POWERUP_TIMER_S  14
/** PMU_DG_HP_WAIT_TIMER : R/W; bitpos: [31:23]; default: 255;
 *  need_des
 */
#define PMU_DG_HP_WAIT_TIMER    0x000001FFU
#define PMU_DG_HP_WAIT_TIMER_M  (PMU_DG_HP_WAIT_TIMER_V << PMU_DG_HP_WAIT_TIMER_S)
#define PMU_DG_HP_WAIT_TIMER_V  0x000001FFU
#define PMU_DG_HP_WAIT_TIMER_S  23

/** PMU_POWER_WAIT_TIMER1_REG register
 *  need_des
 */
#define PMU_POWER_WAIT_TIMER1_REG (DR_REG_PMU_BASE + 0xf0)
/** PMU_DG_LP_POWERDOWN_TIMER : R/W; bitpos: [15:9]; default: 255;
 *  need_des
 */
#define PMU_DG_LP_POWERDOWN_TIMER    0x0000007FU
#define PMU_DG_LP_POWERDOWN_TIMER_M  (PMU_DG_LP_POWERDOWN_TIMER_V << PMU_DG_LP_POWERDOWN_TIMER_S)
#define PMU_DG_LP_POWERDOWN_TIMER_V  0x0000007FU
#define PMU_DG_LP_POWERDOWN_TIMER_S  9
/** PMU_DG_LP_POWERUP_TIMER : R/W; bitpos: [22:16]; default: 255;
 *  need_des
 */
#define PMU_DG_LP_POWERUP_TIMER    0x0000007FU
#define PMU_DG_LP_POWERUP_TIMER_M  (PMU_DG_LP_POWERUP_TIMER_V << PMU_DG_LP_POWERUP_TIMER_S)
#define PMU_DG_LP_POWERUP_TIMER_V  0x0000007FU
#define PMU_DG_LP_POWERUP_TIMER_S  16
/** PMU_DG_LP_WAIT_TIMER : R/W; bitpos: [31:23]; default: 255;
 *  need_des
 */
#define PMU_DG_LP_WAIT_TIMER    0x000001FFU
#define PMU_DG_LP_WAIT_TIMER_M  (PMU_DG_LP_WAIT_TIMER_V << PMU_DG_LP_WAIT_TIMER_S)
#define PMU_DG_LP_WAIT_TIMER_V  0x000001FFU
#define PMU_DG_LP_WAIT_TIMER_S  23

/** PMU_POWER_PD_TOP_CNTL_REG register
 *  need_des
 */
#define PMU_POWER_PD_TOP_CNTL_REG (DR_REG_PMU_BASE + 0xf4)
/** PMU_FORCE_TOP_RESET : R/W; bitpos: [0]; default: 0;
 *  need_des
 */
#define PMU_FORCE_TOP_RESET    (BIT(0))
#define PMU_FORCE_TOP_RESET_M  (PMU_FORCE_TOP_RESET_V << PMU_FORCE_TOP_RESET_S)
#define PMU_FORCE_TOP_RESET_V  0x00000001U
#define PMU_FORCE_TOP_RESET_S  0
/** PMU_FORCE_TOP_ISO : R/W; bitpos: [1]; default: 0;
 *  need_des
 */
#define PMU_FORCE_TOP_ISO    (BIT(1))
#define PMU_FORCE_TOP_ISO_M  (PMU_FORCE_TOP_ISO_V << PMU_FORCE_TOP_ISO_S)
#define PMU_FORCE_TOP_ISO_V  0x00000001U
#define PMU_FORCE_TOP_ISO_S  1
/** PMU_FORCE_TOP_PU : R/W; bitpos: [2]; default: 1;
 *  need_des
 */
#define PMU_FORCE_TOP_PU    (BIT(2))
#define PMU_FORCE_TOP_PU_M  (PMU_FORCE_TOP_PU_V << PMU_FORCE_TOP_PU_S)
#define PMU_FORCE_TOP_PU_V  0x00000001U
#define PMU_FORCE_TOP_PU_S  2
/** PMU_FORCE_TOP_NO_RESET : R/W; bitpos: [3]; default: 1;
 *  need_des
 */
#define PMU_FORCE_TOP_NO_RESET    (BIT(3))
#define PMU_FORCE_TOP_NO_RESET_M  (PMU_FORCE_TOP_NO_RESET_V << PMU_FORCE_TOP_NO_RESET_S)
#define PMU_FORCE_TOP_NO_RESET_V  0x00000001U
#define PMU_FORCE_TOP_NO_RESET_S  3
/** PMU_FORCE_TOP_NO_ISO : R/W; bitpos: [4]; default: 1;
 *  need_des
 */
#define PMU_FORCE_TOP_NO_ISO    (BIT(4))
#define PMU_FORCE_TOP_NO_ISO_M  (PMU_FORCE_TOP_NO_ISO_V << PMU_FORCE_TOP_NO_ISO_S)
#define PMU_FORCE_TOP_NO_ISO_V  0x00000001U
#define PMU_FORCE_TOP_NO_ISO_S  4
/** PMU_FORCE_TOP_PD : R/W; bitpos: [5]; default: 0;
 *  need_des
 */
#define PMU_FORCE_TOP_PD    (BIT(5))
#define PMU_FORCE_TOP_PD_M  (PMU_FORCE_TOP_PD_V << PMU_FORCE_TOP_PD_S)
#define PMU_FORCE_TOP_PD_V  0x00000001U
#define PMU_FORCE_TOP_PD_S  5
/** PMU_PD_TOP_MASK : R/W; bitpos: [10:6]; default: 0;
 *  need_des
 */
#define PMU_PD_TOP_MASK    0x0000001FU
#define PMU_PD_TOP_MASK_M  (PMU_PD_TOP_MASK_V << PMU_PD_TOP_MASK_S)
#define PMU_PD_TOP_MASK_V  0x0000001FU
#define PMU_PD_TOP_MASK_S  6
/** PMU_PD_TOP_PD_MASK : R/W; bitpos: [31:27]; default: 0;
 *  need_des
 */
#define PMU_PD_TOP_PD_MASK    0x0000001FU
#define PMU_PD_TOP_PD_MASK_M  (PMU_PD_TOP_PD_MASK_V << PMU_PD_TOP_PD_MASK_S)
#define PMU_PD_TOP_PD_MASK_V  0x0000001FU
#define PMU_PD_TOP_PD_MASK_S  27

/** PMU_POWER_PD_HPAON_CNTL_REG register
 *  need_des
 */
#define PMU_POWER_PD_HPAON_CNTL_REG (DR_REG_PMU_BASE + 0xf8)
/** PMU_FORCE_HP_AON_RESET : R/W; bitpos: [0]; default: 0;
 *  need_des
 */
#define PMU_FORCE_HP_AON_RESET    (BIT(0))
#define PMU_FORCE_HP_AON_RESET_M  (PMU_FORCE_HP_AON_RESET_V << PMU_FORCE_HP_AON_RESET_S)
#define PMU_FORCE_HP_AON_RESET_V  0x00000001U
#define PMU_FORCE_HP_AON_RESET_S  0
/** PMU_FORCE_HP_AON_ISO : R/W; bitpos: [1]; default: 0;
 *  need_des
 */
#define PMU_FORCE_HP_AON_ISO    (BIT(1))
#define PMU_FORCE_HP_AON_ISO_M  (PMU_FORCE_HP_AON_ISO_V << PMU_FORCE_HP_AON_ISO_S)
#define PMU_FORCE_HP_AON_ISO_V  0x00000001U
#define PMU_FORCE_HP_AON_ISO_S  1
/** PMU_FORCE_HP_AON_PU : R/W; bitpos: [2]; default: 1;
 *  need_des
 */
#define PMU_FORCE_HP_AON_PU    (BIT(2))
#define PMU_FORCE_HP_AON_PU_M  (PMU_FORCE_HP_AON_PU_V << PMU_FORCE_HP_AON_PU_S)
#define PMU_FORCE_HP_AON_PU_V  0x00000001U
#define PMU_FORCE_HP_AON_PU_S  2
/** PMU_FORCE_HP_AON_NO_RESET : R/W; bitpos: [3]; default: 1;
 *  need_des
 */
#define PMU_FORCE_HP_AON_NO_RESET    (BIT(3))
#define PMU_FORCE_HP_AON_NO_RESET_M  (PMU_FORCE_HP_AON_NO_RESET_V << PMU_FORCE_HP_AON_NO_RESET_S)
#define PMU_FORCE_HP_AON_NO_RESET_V  0x00000001U
#define PMU_FORCE_HP_AON_NO_RESET_S  3
/** PMU_FORCE_HP_AON_NO_ISO : R/W; bitpos: [4]; default: 1;
 *  need_des
 */
#define PMU_FORCE_HP_AON_NO_ISO    (BIT(4))
#define PMU_FORCE_HP_AON_NO_ISO_M  (PMU_FORCE_HP_AON_NO_ISO_V << PMU_FORCE_HP_AON_NO_ISO_S)
#define PMU_FORCE_HP_AON_NO_ISO_V  0x00000001U
#define PMU_FORCE_HP_AON_NO_ISO_S  4
/** PMU_FORCE_HP_AON_PD : R/W; bitpos: [5]; default: 0;
 *  need_des
 */
#define PMU_FORCE_HP_AON_PD    (BIT(5))
#define PMU_FORCE_HP_AON_PD_M  (PMU_FORCE_HP_AON_PD_V << PMU_FORCE_HP_AON_PD_S)
#define PMU_FORCE_HP_AON_PD_V  0x00000001U
#define PMU_FORCE_HP_AON_PD_S  5
/** PMU_PD_HP_AON_MASK : R/W; bitpos: [10:6]; default: 0;
 *  need_des
 */
#define PMU_PD_HP_AON_MASK    0x0000001FU
#define PMU_PD_HP_AON_MASK_M  (PMU_PD_HP_AON_MASK_V << PMU_PD_HP_AON_MASK_S)
#define PMU_PD_HP_AON_MASK_V  0x0000001FU
#define PMU_PD_HP_AON_MASK_S  6
/** PMU_PD_HP_AON_PD_MASK : R/W; bitpos: [31:27]; default: 0;
 *  need_des
 */
#define PMU_PD_HP_AON_PD_MASK    0x0000001FU
#define PMU_PD_HP_AON_PD_MASK_M  (PMU_PD_HP_AON_PD_MASK_V << PMU_PD_HP_AON_PD_MASK_S)
#define PMU_PD_HP_AON_PD_MASK_V  0x0000001FU
#define PMU_PD_HP_AON_PD_MASK_S  27

/** PMU_POWER_PD_HPCPU_CNTL_REG register
 *  need_des
 */
#define PMU_POWER_PD_HPCPU_CNTL_REG (DR_REG_PMU_BASE + 0xfc)
/** PMU_FORCE_HP_CPU_RESET : R/W; bitpos: [0]; default: 0;
 *  need_des
 */
#define PMU_FORCE_HP_CPU_RESET    (BIT(0))
#define PMU_FORCE_HP_CPU_RESET_M  (PMU_FORCE_HP_CPU_RESET_V << PMU_FORCE_HP_CPU_RESET_S)
#define PMU_FORCE_HP_CPU_RESET_V  0x00000001U
#define PMU_FORCE_HP_CPU_RESET_S  0
/** PMU_FORCE_HP_CPU_ISO : R/W; bitpos: [1]; default: 0;
 *  need_des
 */
#define PMU_FORCE_HP_CPU_ISO    (BIT(1))
#define PMU_FORCE_HP_CPU_ISO_M  (PMU_FORCE_HP_CPU_ISO_V << PMU_FORCE_HP_CPU_ISO_S)
#define PMU_FORCE_HP_CPU_ISO_V  0x00000001U
#define PMU_FORCE_HP_CPU_ISO_S  1
/** PMU_FORCE_HP_CPU_PU : R/W; bitpos: [2]; default: 1;
 *  need_des
 */
#define PMU_FORCE_HP_CPU_PU    (BIT(2))
#define PMU_FORCE_HP_CPU_PU_M  (PMU_FORCE_HP_CPU_PU_V << PMU_FORCE_HP_CPU_PU_S)
#define PMU_FORCE_HP_CPU_PU_V  0x00000001U
#define PMU_FORCE_HP_CPU_PU_S  2
/** PMU_FORCE_HP_CPU_NO_RESET : R/W; bitpos: [3]; default: 1;
 *  need_des
 */
#define PMU_FORCE_HP_CPU_NO_RESET    (BIT(3))
#define PMU_FORCE_HP_CPU_NO_RESET_M  (PMU_FORCE_HP_CPU_NO_RESET_V << PMU_FORCE_HP_CPU_NO_RESET_S)
#define PMU_FORCE_HP_CPU_NO_RESET_V  0x00000001U
#define PMU_FORCE_HP_CPU_NO_RESET_S  3
/** PMU_FORCE_HP_CPU_NO_ISO : R/W; bitpos: [4]; default: 1;
 *  need_des
 */
#define PMU_FORCE_HP_CPU_NO_ISO    (BIT(4))
#define PMU_FORCE_HP_CPU_NO_ISO_M  (PMU_FORCE_HP_CPU_NO_ISO_V << PMU_FORCE_HP_CPU_NO_ISO_S)
#define PMU_FORCE_HP_CPU_NO_ISO_V  0x00000001U
#define PMU_FORCE_HP_CPU_NO_ISO_S  4
/** PMU_FORCE_HP_CPU_PD : R/W; bitpos: [5]; default: 0;
 *  need_des
 */
#define PMU_FORCE_HP_CPU_PD    (BIT(5))
#define PMU_FORCE_HP_CPU_PD_M  (PMU_FORCE_HP_CPU_PD_V << PMU_FORCE_HP_CPU_PD_S)
#define PMU_FORCE_HP_CPU_PD_V  0x00000001U
#define PMU_FORCE_HP_CPU_PD_S  5
/** PMU_PD_HP_CPU_MASK : R/W; bitpos: [10:6]; default: 0;
 *  need_des
 */
#define PMU_PD_HP_CPU_MASK    0x0000001FU
#define PMU_PD_HP_CPU_MASK_M  (PMU_PD_HP_CPU_MASK_V << PMU_PD_HP_CPU_MASK_S)
#define PMU_PD_HP_CPU_MASK_V  0x0000001FU
#define PMU_PD_HP_CPU_MASK_S  6
/** PMU_PD_HP_CPU_PD_MASK : R/W; bitpos: [31:27]; default: 0;
 *  need_des
 */
#define PMU_PD_HP_CPU_PD_MASK    0x0000001FU
#define PMU_PD_HP_CPU_PD_MASK_M  (PMU_PD_HP_CPU_PD_MASK_V << PMU_PD_HP_CPU_PD_MASK_S)
#define PMU_PD_HP_CPU_PD_MASK_V  0x0000001FU
#define PMU_PD_HP_CPU_PD_MASK_S  27

/** PMU_POWER_PD_HPPERI_RESERVE_REG register
 *  need_des
 */
#define PMU_POWER_PD_HPPERI_RESERVE_REG (DR_REG_PMU_BASE + 0x100)
/** PMU_HP_PERI_RESERVE : WT; bitpos: [31:0]; default: 0;
 *  need_des
 */
#define PMU_HP_PERI_RESERVE    0xFFFFFFFFU
#define PMU_HP_PERI_RESERVE_M  (PMU_HP_PERI_RESERVE_V << PMU_HP_PERI_RESERVE_S)
#define PMU_HP_PERI_RESERVE_V  0xFFFFFFFFU
#define PMU_HP_PERI_RESERVE_S  0

/** PMU_POWER_PD_HPWIFI_CNTL_REG register
 *  need_des
 */
#define PMU_POWER_PD_HPWIFI_CNTL_REG (DR_REG_PMU_BASE + 0x104)
/** PMU_FORCE_HP_WIFI_RESET : R/W; bitpos: [0]; default: 0;
 *  need_des
 */
#define PMU_FORCE_HP_WIFI_RESET    (BIT(0))
#define PMU_FORCE_HP_WIFI_RESET_M  (PMU_FORCE_HP_WIFI_RESET_V << PMU_FORCE_HP_WIFI_RESET_S)
#define PMU_FORCE_HP_WIFI_RESET_V  0x00000001U
#define PMU_FORCE_HP_WIFI_RESET_S  0
/** PMU_FORCE_HP_WIFI_ISO : R/W; bitpos: [1]; default: 0;
 *  need_des
 */
#define PMU_FORCE_HP_WIFI_ISO    (BIT(1))
#define PMU_FORCE_HP_WIFI_ISO_M  (PMU_FORCE_HP_WIFI_ISO_V << PMU_FORCE_HP_WIFI_ISO_S)
#define PMU_FORCE_HP_WIFI_ISO_V  0x00000001U
#define PMU_FORCE_HP_WIFI_ISO_S  1
/** PMU_FORCE_HP_WIFI_PU : R/W; bitpos: [2]; default: 1;
 *  need_des
 */
#define PMU_FORCE_HP_WIFI_PU    (BIT(2))
#define PMU_FORCE_HP_WIFI_PU_M  (PMU_FORCE_HP_WIFI_PU_V << PMU_FORCE_HP_WIFI_PU_S)
#define PMU_FORCE_HP_WIFI_PU_V  0x00000001U
#define PMU_FORCE_HP_WIFI_PU_S  2
/** PMU_FORCE_HP_WIFI_NO_RESET : R/W; bitpos: [3]; default: 1;
 *  need_des
 */
#define PMU_FORCE_HP_WIFI_NO_RESET    (BIT(3))
#define PMU_FORCE_HP_WIFI_NO_RESET_M  (PMU_FORCE_HP_WIFI_NO_RESET_V << PMU_FORCE_HP_WIFI_NO_RESET_S)
#define PMU_FORCE_HP_WIFI_NO_RESET_V  0x00000001U
#define PMU_FORCE_HP_WIFI_NO_RESET_S  3
/** PMU_FORCE_HP_WIFI_NO_ISO : R/W; bitpos: [4]; default: 1;
 *  need_des
 */
#define PMU_FORCE_HP_WIFI_NO_ISO    (BIT(4))
#define PMU_FORCE_HP_WIFI_NO_ISO_M  (PMU_FORCE_HP_WIFI_NO_ISO_V << PMU_FORCE_HP_WIFI_NO_ISO_S)
#define PMU_FORCE_HP_WIFI_NO_ISO_V  0x00000001U
#define PMU_FORCE_HP_WIFI_NO_ISO_S  4
/** PMU_FORCE_HP_WIFI_PD : R/W; bitpos: [5]; default: 0;
 *  need_des
 */
#define PMU_FORCE_HP_WIFI_PD    (BIT(5))
#define PMU_FORCE_HP_WIFI_PD_M  (PMU_FORCE_HP_WIFI_PD_V << PMU_FORCE_HP_WIFI_PD_S)
#define PMU_FORCE_HP_WIFI_PD_V  0x00000001U
#define PMU_FORCE_HP_WIFI_PD_S  5
/** PMU_PD_HP_WIFI_MASK : R/W; bitpos: [10:6]; default: 0;
 *  need_des
 */
#define PMU_PD_HP_WIFI_MASK    0x0000001FU
#define PMU_PD_HP_WIFI_MASK_M  (PMU_PD_HP_WIFI_MASK_V << PMU_PD_HP_WIFI_MASK_S)
#define PMU_PD_HP_WIFI_MASK_V  0x0000001FU
#define PMU_PD_HP_WIFI_MASK_S  6
/** PMU_PD_HP_WIFI_PD_MASK : R/W; bitpos: [31:27]; default: 0;
 *  need_des
 */
#define PMU_PD_HP_WIFI_PD_MASK    0x0000001FU
#define PMU_PD_HP_WIFI_PD_MASK_M  (PMU_PD_HP_WIFI_PD_MASK_V << PMU_PD_HP_WIFI_PD_MASK_S)
#define PMU_PD_HP_WIFI_PD_MASK_V  0x0000001FU
#define PMU_PD_HP_WIFI_PD_MASK_S  27

/** PMU_POWER_PD_LPPERI_CNTL_REG register
 *  need_des
 */
#define PMU_POWER_PD_LPPERI_CNTL_REG (DR_REG_PMU_BASE + 0x108)
/** PMU_FORCE_LP_PERI_RESET : R/W; bitpos: [0]; default: 0;
 *  need_des
 */
#define PMU_FORCE_LP_PERI_RESET    (BIT(0))
#define PMU_FORCE_LP_PERI_RESET_M  (PMU_FORCE_LP_PERI_RESET_V << PMU_FORCE_LP_PERI_RESET_S)
#define PMU_FORCE_LP_PERI_RESET_V  0x00000001U
#define PMU_FORCE_LP_PERI_RESET_S  0
/** PMU_FORCE_LP_PERI_ISO : R/W; bitpos: [1]; default: 0;
 *  need_des
 */
#define PMU_FORCE_LP_PERI_ISO    (BIT(1))
#define PMU_FORCE_LP_PERI_ISO_M  (PMU_FORCE_LP_PERI_ISO_V << PMU_FORCE_LP_PERI_ISO_S)
#define PMU_FORCE_LP_PERI_ISO_V  0x00000001U
#define PMU_FORCE_LP_PERI_ISO_S  1
/** PMU_FORCE_LP_PERI_PU : R/W; bitpos: [2]; default: 1;
 *  need_des
 */
#define PMU_FORCE_LP_PERI_PU    (BIT(2))
#define PMU_FORCE_LP_PERI_PU_M  (PMU_FORCE_LP_PERI_PU_V << PMU_FORCE_LP_PERI_PU_S)
#define PMU_FORCE_LP_PERI_PU_V  0x00000001U
#define PMU_FORCE_LP_PERI_PU_S  2
/** PMU_FORCE_LP_PERI_NO_RESET : R/W; bitpos: [3]; default: 1;
 *  need_des
 */
#define PMU_FORCE_LP_PERI_NO_RESET    (BIT(3))
#define PMU_FORCE_LP_PERI_NO_RESET_M  (PMU_FORCE_LP_PERI_NO_RESET_V << PMU_FORCE_LP_PERI_NO_RESET_S)
#define PMU_FORCE_LP_PERI_NO_RESET_V  0x00000001U
#define PMU_FORCE_LP_PERI_NO_RESET_S  3
/** PMU_FORCE_LP_PERI_NO_ISO : R/W; bitpos: [4]; default: 1;
 *  need_des
 */
#define PMU_FORCE_LP_PERI_NO_ISO    (BIT(4))
#define PMU_FORCE_LP_PERI_NO_ISO_M  (PMU_FORCE_LP_PERI_NO_ISO_V << PMU_FORCE_LP_PERI_NO_ISO_S)
#define PMU_FORCE_LP_PERI_NO_ISO_V  0x00000001U
#define PMU_FORCE_LP_PERI_NO_ISO_S  4
/** PMU_FORCE_LP_PERI_PD : R/W; bitpos: [5]; default: 0;
 *  need_des
 */
#define PMU_FORCE_LP_PERI_PD    (BIT(5))
#define PMU_FORCE_LP_PERI_PD_M  (PMU_FORCE_LP_PERI_PD_V << PMU_FORCE_LP_PERI_PD_S)
#define PMU_FORCE_LP_PERI_PD_V  0x00000001U
#define PMU_FORCE_LP_PERI_PD_S  5

/** PMU_POWER_PD_MEM_CNTL_REG register
 *  need_des
 */
#define PMU_POWER_PD_MEM_CNTL_REG (DR_REG_PMU_BASE + 0x10c)
/** PMU_FORCE_HP_MEM_ISO : R/W; bitpos: [3:0]; default: 0;
 *  need_des
 */
#define PMU_FORCE_HP_MEM_ISO    0x0000000FU
#define PMU_FORCE_HP_MEM_ISO_M  (PMU_FORCE_HP_MEM_ISO_V << PMU_FORCE_HP_MEM_ISO_S)
#define PMU_FORCE_HP_MEM_ISO_V  0x0000000FU
#define PMU_FORCE_HP_MEM_ISO_S  0
/** PMU_FORCE_HP_MEM_PD : R/W; bitpos: [7:4]; default: 0;
 *  need_des
 */
#define PMU_FORCE_HP_MEM_PD    0x0000000FU
#define PMU_FORCE_HP_MEM_PD_M  (PMU_FORCE_HP_MEM_PD_V << PMU_FORCE_HP_MEM_PD_S)
#define PMU_FORCE_HP_MEM_PD_V  0x0000000FU
#define PMU_FORCE_HP_MEM_PD_S  4
/** PMU_FORCE_HP_MEM_NO_ISO : R/W; bitpos: [27:24]; default: 15;
 *  need_des
 */
#define PMU_FORCE_HP_MEM_NO_ISO    0x0000000FU
#define PMU_FORCE_HP_MEM_NO_ISO_M  (PMU_FORCE_HP_MEM_NO_ISO_V << PMU_FORCE_HP_MEM_NO_ISO_S)
#define PMU_FORCE_HP_MEM_NO_ISO_V  0x0000000FU
#define PMU_FORCE_HP_MEM_NO_ISO_S  24
/** PMU_FORCE_HP_MEM_PU : R/W; bitpos: [31:28]; default: 15;
 *  need_des
 */
#define PMU_FORCE_HP_MEM_PU    0x0000000FU
#define PMU_FORCE_HP_MEM_PU_M  (PMU_FORCE_HP_MEM_PU_V << PMU_FORCE_HP_MEM_PU_S)
#define PMU_FORCE_HP_MEM_PU_V  0x0000000FU
#define PMU_FORCE_HP_MEM_PU_S  28

/** PMU_POWER_PD_MEM_MASK_REG register
 *  need_des
 */
#define PMU_POWER_PD_MEM_MASK_REG (DR_REG_PMU_BASE + 0x110)
/** PMU_PD_HP_MEM2_PD_MASK : R/W; bitpos: [4:0]; default: 0;
 *  need_des
 */
#define PMU_PD_HP_MEM2_PD_MASK    0x0000001FU
#define PMU_PD_HP_MEM2_PD_MASK_M  (PMU_PD_HP_MEM2_PD_MASK_V << PMU_PD_HP_MEM2_PD_MASK_S)
#define PMU_PD_HP_MEM2_PD_MASK_V  0x0000001FU
#define PMU_PD_HP_MEM2_PD_MASK_S  0
/** PMU_PD_HP_MEM1_PD_MASK : R/W; bitpos: [9:5]; default: 0;
 *  need_des
 */
#define PMU_PD_HP_MEM1_PD_MASK    0x0000001FU
#define PMU_PD_HP_MEM1_PD_MASK_M  (PMU_PD_HP_MEM1_PD_MASK_V << PMU_PD_HP_MEM1_PD_MASK_S)
#define PMU_PD_HP_MEM1_PD_MASK_V  0x0000001FU
#define PMU_PD_HP_MEM1_PD_MASK_S  5
/** PMU_PD_HP_MEM0_PD_MASK : R/W; bitpos: [14:10]; default: 0;
 *  need_des
 */
#define PMU_PD_HP_MEM0_PD_MASK    0x0000001FU
#define PMU_PD_HP_MEM0_PD_MASK_M  (PMU_PD_HP_MEM0_PD_MASK_V << PMU_PD_HP_MEM0_PD_MASK_S)
#define PMU_PD_HP_MEM0_PD_MASK_V  0x0000001FU
#define PMU_PD_HP_MEM0_PD_MASK_S  10
/** PMU_PD_HP_MEM2_MASK : R/W; bitpos: [21:17]; default: 0;
 *  need_des
 */
#define PMU_PD_HP_MEM2_MASK    0x0000001FU
#define PMU_PD_HP_MEM2_MASK_M  (PMU_PD_HP_MEM2_MASK_V << PMU_PD_HP_MEM2_MASK_S)
#define PMU_PD_HP_MEM2_MASK_V  0x0000001FU
#define PMU_PD_HP_MEM2_MASK_S  17
/** PMU_PD_HP_MEM1_MASK : R/W; bitpos: [26:22]; default: 0;
 *  need_des
 */
#define PMU_PD_HP_MEM1_MASK    0x0000001FU
#define PMU_PD_HP_MEM1_MASK_M  (PMU_PD_HP_MEM1_MASK_V << PMU_PD_HP_MEM1_MASK_S)
#define PMU_PD_HP_MEM1_MASK_V  0x0000001FU
#define PMU_PD_HP_MEM1_MASK_S  22
/** PMU_PD_HP_MEM0_MASK : R/W; bitpos: [31:27]; default: 0;
 *  need_des
 */
#define PMU_PD_HP_MEM0_MASK    0x0000001FU
#define PMU_PD_HP_MEM0_MASK_M  (PMU_PD_HP_MEM0_MASK_V << PMU_PD_HP_MEM0_MASK_S)
#define PMU_PD_HP_MEM0_MASK_V  0x0000001FU
#define PMU_PD_HP_MEM0_MASK_S  27

/** PMU_POWER_HP_PAD_REG register
 *  need_des
 */
#define PMU_POWER_HP_PAD_REG (DR_REG_PMU_BASE + 0x114)
/** PMU_FORCE_HP_PAD_NO_ISO_ALL : R/W; bitpos: [0]; default: 0;
 *  need_des
 */
#define PMU_FORCE_HP_PAD_NO_ISO_ALL    (BIT(0))
#define PMU_FORCE_HP_PAD_NO_ISO_ALL_M  (PMU_FORCE_HP_PAD_NO_ISO_ALL_V << PMU_FORCE_HP_PAD_NO_ISO_ALL_S)
#define PMU_FORCE_HP_PAD_NO_ISO_ALL_V  0x00000001U
#define PMU_FORCE_HP_PAD_NO_ISO_ALL_S  0
/** PMU_FORCE_HP_PAD_ISO_ALL : R/W; bitpos: [1]; default: 0;
 *  need_des
 */
#define PMU_FORCE_HP_PAD_ISO_ALL    (BIT(1))
#define PMU_FORCE_HP_PAD_ISO_ALL_M  (PMU_FORCE_HP_PAD_ISO_ALL_V << PMU_FORCE_HP_PAD_ISO_ALL_S)
#define PMU_FORCE_HP_PAD_ISO_ALL_V  0x00000001U
#define PMU_FORCE_HP_PAD_ISO_ALL_S  1

/** PMU_POWER_VDD_SPI_CNTL_REG register
 *  need_des
 */
#define PMU_POWER_VDD_SPI_CNTL_REG (DR_REG_PMU_BASE + 0x118)
/** PMU_VDD_SPI_PWR_WAIT : R/W; bitpos: [28:18]; default: 255;
 *  need_des
 */
#define PMU_VDD_SPI_PWR_WAIT    0x000007FFU
#define PMU_VDD_SPI_PWR_WAIT_M  (PMU_VDD_SPI_PWR_WAIT_V << PMU_VDD_SPI_PWR_WAIT_S)
#define PMU_VDD_SPI_PWR_WAIT_V  0x000007FFU
#define PMU_VDD_SPI_PWR_WAIT_S  18
/** PMU_VDD_SPI_PWR_SW : R/W; bitpos: [30:29]; default: 3;
 *  need_des
 */
#define PMU_VDD_SPI_PWR_SW    0x00000003U
#define PMU_VDD_SPI_PWR_SW_M  (PMU_VDD_SPI_PWR_SW_V << PMU_VDD_SPI_PWR_SW_S)
#define PMU_VDD_SPI_PWR_SW_V  0x00000003U
#define PMU_VDD_SPI_PWR_SW_S  29
/** PMU_VDD_SPI_PWR_SEL_SW : R/W; bitpos: [31]; default: 0;
 *  need_des
 */
#define PMU_VDD_SPI_PWR_SEL_SW    (BIT(31))
#define PMU_VDD_SPI_PWR_SEL_SW_M  (PMU_VDD_SPI_PWR_SEL_SW_V << PMU_VDD_SPI_PWR_SEL_SW_S)
#define PMU_VDD_SPI_PWR_SEL_SW_V  0x00000001U
#define PMU_VDD_SPI_PWR_SEL_SW_S  31

/** PMU_POWER_CK_WAIT_CNTL_REG register
 *  need_des
 */
#define PMU_POWER_CK_WAIT_CNTL_REG (DR_REG_PMU_BASE + 0x11c)
/** PMU_WAIT_XTL_STABLE : R/W; bitpos: [15:0]; default: 256;
 *  need_des
 */
#define PMU_WAIT_XTL_STABLE    0x0000FFFFU
#define PMU_WAIT_XTL_STABLE_M  (PMU_WAIT_XTL_STABLE_V << PMU_WAIT_XTL_STABLE_S)
#define PMU_WAIT_XTL_STABLE_V  0x0000FFFFU
#define PMU_WAIT_XTL_STABLE_S  0
/** PMU_WAIT_PLL_STABLE : R/W; bitpos: [31:16]; default: 256;
 *  need_des
 */
#define PMU_WAIT_PLL_STABLE    0x0000FFFFU
#define PMU_WAIT_PLL_STABLE_M  (PMU_WAIT_PLL_STABLE_V << PMU_WAIT_PLL_STABLE_S)
#define PMU_WAIT_PLL_STABLE_V  0x0000FFFFU
#define PMU_WAIT_PLL_STABLE_S  16

/** PMU_SLP_WAKEUP_CNTL0_REG register
 *  need_des
 */
#define PMU_SLP_WAKEUP_CNTL0_REG (DR_REG_PMU_BASE + 0x120)
/** PMU_SLEEP_REQ : WT; bitpos: [31]; default: 0;
 *  need_des
 */
#define PMU_SLEEP_REQ    (BIT(31))
#define PMU_SLEEP_REQ_M  (PMU_SLEEP_REQ_V << PMU_SLEEP_REQ_S)
#define PMU_SLEEP_REQ_V  0x00000001U
#define PMU_SLEEP_REQ_S  31

/** PMU_SLP_WAKEUP_CNTL1_REG register
 *  need_des
 */
#define PMU_SLP_WAKEUP_CNTL1_REG (DR_REG_PMU_BASE + 0x124)
/** PMU_SLEEP_REJECT_ENA : R/W; bitpos: [30:0]; default: 0;
 *  need_des
 */
#define PMU_SLEEP_REJECT_ENA    0x7FFFFFFFU
#define PMU_SLEEP_REJECT_ENA_M  (PMU_SLEEP_REJECT_ENA_V << PMU_SLEEP_REJECT_ENA_S)
#define PMU_SLEEP_REJECT_ENA_V  0x7FFFFFFFU
#define PMU_SLEEP_REJECT_ENA_S  0
/** PMU_SLP_REJECT_EN : R/W; bitpos: [31]; default: 0;
 *  need_des
 */
#define PMU_SLP_REJECT_EN    (BIT(31))
#define PMU_SLP_REJECT_EN_M  (PMU_SLP_REJECT_EN_V << PMU_SLP_REJECT_EN_S)
#define PMU_SLP_REJECT_EN_V  0x00000001U
#define PMU_SLP_REJECT_EN_S  31

/** PMU_SLP_WAKEUP_CNTL2_REG register
 *  need_des
 */
#define PMU_SLP_WAKEUP_CNTL2_REG (DR_REG_PMU_BASE + 0x128)
/** PMU_WAKEUP_ENA : R/W; bitpos: [31:0]; default: 0;
 *  need_des
 */
#define PMU_WAKEUP_ENA    0xFFFFFFFFU
#define PMU_WAKEUP_ENA_M  (PMU_WAKEUP_ENA_V << PMU_WAKEUP_ENA_S)
#define PMU_WAKEUP_ENA_V  0xFFFFFFFFU
#define PMU_WAKEUP_ENA_S  0

/** PMU_SLP_WAKEUP_CNTL3_REG register
 *  need_des
 */
#define PMU_SLP_WAKEUP_CNTL3_REG (DR_REG_PMU_BASE + 0x12c)
/** PMU_LP_MIN_SLP_VAL : R/W; bitpos: [7:0]; default: 0;
 *  need_des
 */
#define PMU_LP_MIN_SLP_VAL    0x000000FFU
#define PMU_LP_MIN_SLP_VAL_M  (PMU_LP_MIN_SLP_VAL_V << PMU_LP_MIN_SLP_VAL_S)
#define PMU_LP_MIN_SLP_VAL_V  0x000000FFU
#define PMU_LP_MIN_SLP_VAL_S  0
/** PMU_HP_MIN_SLP_VAL : R/W; bitpos: [15:8]; default: 0;
 *  need_des
 */
#define PMU_HP_MIN_SLP_VAL    0x000000FFU
#define PMU_HP_MIN_SLP_VAL_M  (PMU_HP_MIN_SLP_VAL_V << PMU_HP_MIN_SLP_VAL_S)
#define PMU_HP_MIN_SLP_VAL_V  0x000000FFU
#define PMU_HP_MIN_SLP_VAL_S  8
/** PMU_SLEEP_PRT_SEL : R/W; bitpos: [17:16]; default: 0;
 *  need_des
 */
#define PMU_SLEEP_PRT_SEL    0x00000003U
#define PMU_SLEEP_PRT_SEL_M  (PMU_SLEEP_PRT_SEL_V << PMU_SLEEP_PRT_SEL_S)
#define PMU_SLEEP_PRT_SEL_V  0x00000003U
#define PMU_SLEEP_PRT_SEL_S  16

/** PMU_SLP_WAKEUP_CNTL4_REG register
 *  need_des
 */
#define PMU_SLP_WAKEUP_CNTL4_REG (DR_REG_PMU_BASE + 0x130)
/** PMU_SLP_REJECT_CAUSE_CLR : WT; bitpos: [31]; default: 0;
 *  need_des
 */
#define PMU_SLP_REJECT_CAUSE_CLR    (BIT(31))
#define PMU_SLP_REJECT_CAUSE_CLR_M  (PMU_SLP_REJECT_CAUSE_CLR_V << PMU_SLP_REJECT_CAUSE_CLR_S)
#define PMU_SLP_REJECT_CAUSE_CLR_V  0x00000001U
#define PMU_SLP_REJECT_CAUSE_CLR_S  31

/** PMU_SLP_WAKEUP_CNTL5_REG register
 *  need_des
 */
#define PMU_SLP_WAKEUP_CNTL5_REG (DR_REG_PMU_BASE + 0x134)
/** PMU_MODEM_WAIT_TARGET : R/W; bitpos: [19:0]; default: 128;
 *  need_des
 */
#define PMU_MODEM_WAIT_TARGET    0x000FFFFFU
#define PMU_MODEM_WAIT_TARGET_M  (PMU_MODEM_WAIT_TARGET_V << PMU_MODEM_WAIT_TARGET_S)
#define PMU_MODEM_WAIT_TARGET_V  0x000FFFFFU
#define PMU_MODEM_WAIT_TARGET_S  0
/** PMU_LP_ANA_WAIT_TARGET : R/W; bitpos: [31:24]; default: 1;
 *  need_des
 */
#define PMU_LP_ANA_WAIT_TARGET    0x000000FFU
#define PMU_LP_ANA_WAIT_TARGET_M  (PMU_LP_ANA_WAIT_TARGET_V << PMU_LP_ANA_WAIT_TARGET_S)
#define PMU_LP_ANA_WAIT_TARGET_V  0x000000FFU
#define PMU_LP_ANA_WAIT_TARGET_S  24

/** PMU_SLP_WAKEUP_CNTL6_REG register
 *  need_des
 */
#define PMU_SLP_WAKEUP_CNTL6_REG (DR_REG_PMU_BASE + 0x138)
/** PMU_SOC_WAKEUP_WAIT : R/W; bitpos: [19:0]; default: 128;
 *  need_des
 */
#define PMU_SOC_WAKEUP_WAIT    0x000FFFFFU
#define PMU_SOC_WAKEUP_WAIT_M  (PMU_SOC_WAKEUP_WAIT_V << PMU_SOC_WAKEUP_WAIT_S)
#define PMU_SOC_WAKEUP_WAIT_V  0x000FFFFFU
#define PMU_SOC_WAKEUP_WAIT_S  0
/** PMU_SOC_WAKEUP_WAIT_CFG : R/W; bitpos: [31:30]; default: 0;
 *  need_des
 */
#define PMU_SOC_WAKEUP_WAIT_CFG    0x00000003U
#define PMU_SOC_WAKEUP_WAIT_CFG_M  (PMU_SOC_WAKEUP_WAIT_CFG_V << PMU_SOC_WAKEUP_WAIT_CFG_S)
#define PMU_SOC_WAKEUP_WAIT_CFG_V  0x00000003U
#define PMU_SOC_WAKEUP_WAIT_CFG_S  30

/** PMU_SLP_WAKEUP_CNTL7_REG register
 *  need_des
 */
#define PMU_SLP_WAKEUP_CNTL7_REG (DR_REG_PMU_BASE + 0x13c)
/** PMU_ANA_WAIT_TARGET : R/W; bitpos: [31:16]; default: 1;
 *  need_des
 */
#define PMU_ANA_WAIT_TARGET    0x0000FFFFU
#define PMU_ANA_WAIT_TARGET_M  (PMU_ANA_WAIT_TARGET_V << PMU_ANA_WAIT_TARGET_S)
#define PMU_ANA_WAIT_TARGET_V  0x0000FFFFU
#define PMU_ANA_WAIT_TARGET_S  16

/** PMU_SLP_WAKEUP_STATUS0_REG register
 *  need_des
 */
#define PMU_SLP_WAKEUP_STATUS0_REG (DR_REG_PMU_BASE + 0x140)
/** PMU_WAKEUP_CAUSE : RO; bitpos: [31:0]; default: 0;
 *  need_des
 */
#define PMU_WAKEUP_CAUSE    0xFFFFFFFFU
#define PMU_WAKEUP_CAUSE_M  (PMU_WAKEUP_CAUSE_V << PMU_WAKEUP_CAUSE_S)
#define PMU_WAKEUP_CAUSE_V  0xFFFFFFFFU
#define PMU_WAKEUP_CAUSE_S  0

/** PMU_SLP_WAKEUP_STATUS1_REG register
 *  need_des
 */
#define PMU_SLP_WAKEUP_STATUS1_REG (DR_REG_PMU_BASE + 0x144)
/** PMU_REJECT_CAUSE : RO; bitpos: [31:0]; default: 0;
 *  need_des
 */
#define PMU_REJECT_CAUSE    0xFFFFFFFFU
#define PMU_REJECT_CAUSE_M  (PMU_REJECT_CAUSE_V << PMU_REJECT_CAUSE_S)
#define PMU_REJECT_CAUSE_V  0xFFFFFFFFU
#define PMU_REJECT_CAUSE_S  0

/** PMU_HP_CK_POWERON_REG register
 *  need_des
 */
#define PMU_HP_CK_POWERON_REG (DR_REG_PMU_BASE + 0x148)
/** PMU_I2C_POR_WAIT_TARGET : R/W; bitpos: [7:0]; default: 50;
 *  need_des
 */
#define PMU_I2C_POR_WAIT_TARGET    0x000000FFU
#define PMU_I2C_POR_WAIT_TARGET_M  (PMU_I2C_POR_WAIT_TARGET_V << PMU_I2C_POR_WAIT_TARGET_S)
#define PMU_I2C_POR_WAIT_TARGET_V  0x000000FFU
#define PMU_I2C_POR_WAIT_TARGET_S  0

/** PMU_HP_CK_CNTL_REG register
 *  need_des
 */
#define PMU_HP_CK_CNTL_REG (DR_REG_PMU_BASE + 0x14c)
/** PMU_MODIFY_ICG_CNTL_WAIT : R/W; bitpos: [7:0]; default: 10;
 *  need_des
 */
#define PMU_MODIFY_ICG_CNTL_WAIT    0x000000FFU
#define PMU_MODIFY_ICG_CNTL_WAIT_M  (PMU_MODIFY_ICG_CNTL_WAIT_V << PMU_MODIFY_ICG_CNTL_WAIT_S)
#define PMU_MODIFY_ICG_CNTL_WAIT_V  0x000000FFU
#define PMU_MODIFY_ICG_CNTL_WAIT_S  0
/** PMU_SWITCH_ICG_CNTL_WAIT : R/W; bitpos: [15:8]; default: 10;
 *  need_des
 */
#define PMU_SWITCH_ICG_CNTL_WAIT    0x000000FFU
#define PMU_SWITCH_ICG_CNTL_WAIT_M  (PMU_SWITCH_ICG_CNTL_WAIT_V << PMU_SWITCH_ICG_CNTL_WAIT_S)
#define PMU_SWITCH_ICG_CNTL_WAIT_V  0x000000FFU
#define PMU_SWITCH_ICG_CNTL_WAIT_S  8

/** PMU_POR_STATUS_REG register
 *  need_des
 */
#define PMU_POR_STATUS_REG (DR_REG_PMU_BASE + 0x150)
/** PMU_POR_DONE : RO; bitpos: [31]; default: 1;
 *  need_des
 */
#define PMU_POR_DONE    (BIT(31))
#define PMU_POR_DONE_M  (PMU_POR_DONE_V << PMU_POR_DONE_S)
#define PMU_POR_DONE_V  0x00000001U
#define PMU_POR_DONE_S  31

/** PMU_RF_PWC_REG register
 *  need_des
 */
#define PMU_RF_PWC_REG (DR_REG_PMU_BASE + 0x154)
/** PMU_XPD_PERIF_I2C : R/W; bitpos: [27]; default: 1;
 *  need_des
 */
#define PMU_XPD_PERIF_I2C    (BIT(27))
#define PMU_XPD_PERIF_I2C_M  (PMU_XPD_PERIF_I2C_V << PMU_XPD_PERIF_I2C_S)
#define PMU_XPD_PERIF_I2C_V  0x00000001U
#define PMU_XPD_PERIF_I2C_S  27
/** PMU_XPD_RFTX_I2C : R/W; bitpos: [28]; default: 0;
 *  need_des
 */
#define PMU_XPD_RFTX_I2C    (BIT(28))
#define PMU_XPD_RFTX_I2C_M  (PMU_XPD_RFTX_I2C_V << PMU_XPD_RFTX_I2C_S)
#define PMU_XPD_RFTX_I2C_V  0x00000001U
#define PMU_XPD_RFTX_I2C_S  28
/** PMU_XPD_RFRX_I2C : R/W; bitpos: [29]; default: 0;
 *  need_des
 */
#define PMU_XPD_RFRX_I2C    (BIT(29))
#define PMU_XPD_RFRX_I2C_M  (PMU_XPD_RFRX_I2C_V << PMU_XPD_RFRX_I2C_S)
#define PMU_XPD_RFRX_I2C_V  0x00000001U
#define PMU_XPD_RFRX_I2C_S  29
/** PMU_XPD_RFPLL : R/W; bitpos: [30]; default: 0;
 *  need_des
 */
#define PMU_XPD_RFPLL    (BIT(30))
#define PMU_XPD_RFPLL_M  (PMU_XPD_RFPLL_V << PMU_XPD_RFPLL_S)
#define PMU_XPD_RFPLL_V  0x00000001U
#define PMU_XPD_RFPLL_S  30
/** PMU_XPD_FORCE_RFPLL : R/W; bitpos: [31]; default: 0;
 *  need_des
 */
#define PMU_XPD_FORCE_RFPLL    (BIT(31))
#define PMU_XPD_FORCE_RFPLL_M  (PMU_XPD_FORCE_RFPLL_V << PMU_XPD_FORCE_RFPLL_S)
#define PMU_XPD_FORCE_RFPLL_V  0x00000001U
#define PMU_XPD_FORCE_RFPLL_S  31

/** PMU_VDDBAT_CFG_REG register
 *  need_des
 */
#define PMU_VDDBAT_CFG_REG (DR_REG_PMU_BASE + 0x158)
/** PMU_VDDBAT_MODE : RO; bitpos: [1:0]; default: 0;
 *  need_des
 */
#define PMU_VDDBAT_MODE    0x00000003U
#define PMU_VDDBAT_MODE_M  (PMU_VDDBAT_MODE_V << PMU_VDDBAT_MODE_S)
#define PMU_VDDBAT_MODE_V  0x00000003U
#define PMU_VDDBAT_MODE_S  0
/** PMU_VDDBAT_SW_UPDATE : WT; bitpos: [31]; default: 0;
 *  need_des
 */
#define PMU_VDDBAT_SW_UPDATE    (BIT(31))
#define PMU_VDDBAT_SW_UPDATE_M  (PMU_VDDBAT_SW_UPDATE_V << PMU_VDDBAT_SW_UPDATE_S)
#define PMU_VDDBAT_SW_UPDATE_V  0x00000001U
#define PMU_VDDBAT_SW_UPDATE_S  31

/** PMU_BACKUP_CFG_REG register
 *  need_des
 */
#define PMU_BACKUP_CFG_REG (DR_REG_PMU_BASE + 0x15c)
/** PMU_BACKUP_SYS_CLK_NO_DIV : R/W; bitpos: [31]; default: 1;
 *  need_des
 */
#define PMU_BACKUP_SYS_CLK_NO_DIV    (BIT(31))
#define PMU_BACKUP_SYS_CLK_NO_DIV_M  (PMU_BACKUP_SYS_CLK_NO_DIV_V << PMU_BACKUP_SYS_CLK_NO_DIV_S)
#define PMU_BACKUP_SYS_CLK_NO_DIV_V  0x00000001U
#define PMU_BACKUP_SYS_CLK_NO_DIV_S  31

/** PMU_INT_RAW_REG register
 *  need_des
 */
#define PMU_INT_RAW_REG (DR_REG_PMU_BASE + 0x160)
/** PMU_LP_CPU_EXC_INT_RAW : R/WTC/SS; bitpos: [27]; default: 0;
 *  need_des
 */
#define PMU_LP_CPU_EXC_INT_RAW    (BIT(27))
#define PMU_LP_CPU_EXC_INT_RAW_M  (PMU_LP_CPU_EXC_INT_RAW_V << PMU_LP_CPU_EXC_INT_RAW_S)
#define PMU_LP_CPU_EXC_INT_RAW_V  0x00000001U
#define PMU_LP_CPU_EXC_INT_RAW_S  27
/** PMU_SDIO_IDLE_INT_RAW : R/WTC/SS; bitpos: [28]; default: 0;
 *  need_des
 */
#define PMU_SDIO_IDLE_INT_RAW    (BIT(28))
#define PMU_SDIO_IDLE_INT_RAW_M  (PMU_SDIO_IDLE_INT_RAW_V << PMU_SDIO_IDLE_INT_RAW_S)
#define PMU_SDIO_IDLE_INT_RAW_V  0x00000001U
#define PMU_SDIO_IDLE_INT_RAW_S  28
/** PMU_SW_INT_RAW : R/WTC/SS; bitpos: [29]; default: 0;
 *  need_des
 */
#define PMU_SW_INT_RAW    (BIT(29))
#define PMU_SW_INT_RAW_M  (PMU_SW_INT_RAW_V << PMU_SW_INT_RAW_S)
#define PMU_SW_INT_RAW_V  0x00000001U
#define PMU_SW_INT_RAW_S  29
/** PMU_SOC_SLEEP_REJECT_INT_RAW : R/WTC/SS; bitpos: [30]; default: 0;
 *  need_des
 */
#define PMU_SOC_SLEEP_REJECT_INT_RAW    (BIT(30))
#define PMU_SOC_SLEEP_REJECT_INT_RAW_M  (PMU_SOC_SLEEP_REJECT_INT_RAW_V << PMU_SOC_SLEEP_REJECT_INT_RAW_S)
#define PMU_SOC_SLEEP_REJECT_INT_RAW_V  0x00000001U
#define PMU_SOC_SLEEP_REJECT_INT_RAW_S  30
/** PMU_SOC_WAKEUP_INT_RAW : R/WTC/SS; bitpos: [31]; default: 0;
 *  need_des
 */
#define PMU_SOC_WAKEUP_INT_RAW    (BIT(31))
#define PMU_SOC_WAKEUP_INT_RAW_M  (PMU_SOC_WAKEUP_INT_RAW_V << PMU_SOC_WAKEUP_INT_RAW_S)
#define PMU_SOC_WAKEUP_INT_RAW_V  0x00000001U
#define PMU_SOC_WAKEUP_INT_RAW_S  31

/** PMU_HP_INT_ST_REG register
 *  need_des
 */
#define PMU_HP_INT_ST_REG (DR_REG_PMU_BASE + 0x164)
/** PMU_LP_CPU_EXC_INT_ST : RO; bitpos: [27]; default: 0;
 *  need_des
 */
#define PMU_LP_CPU_EXC_INT_ST    (BIT(27))
#define PMU_LP_CPU_EXC_INT_ST_M  (PMU_LP_CPU_EXC_INT_ST_V << PMU_LP_CPU_EXC_INT_ST_S)
#define PMU_LP_CPU_EXC_INT_ST_V  0x00000001U
#define PMU_LP_CPU_EXC_INT_ST_S  27
/** PMU_SDIO_IDLE_INT_ST : RO; bitpos: [28]; default: 0;
 *  need_des
 */
#define PMU_SDIO_IDLE_INT_ST    (BIT(28))
#define PMU_SDIO_IDLE_INT_ST_M  (PMU_SDIO_IDLE_INT_ST_V << PMU_SDIO_IDLE_INT_ST_S)
#define PMU_SDIO_IDLE_INT_ST_V  0x00000001U
#define PMU_SDIO_IDLE_INT_ST_S  28
/** PMU_SW_INT_ST : RO; bitpos: [29]; default: 0;
 *  need_des
 */
#define PMU_SW_INT_ST    (BIT(29))
#define PMU_SW_INT_ST_M  (PMU_SW_INT_ST_V << PMU_SW_INT_ST_S)
#define PMU_SW_INT_ST_V  0x00000001U
#define PMU_SW_INT_ST_S  29
/** PMU_SOC_SLEEP_REJECT_INT_ST : RO; bitpos: [30]; default: 0;
 *  need_des
 */
#define PMU_SOC_SLEEP_REJECT_INT_ST    (BIT(30))
#define PMU_SOC_SLEEP_REJECT_INT_ST_M  (PMU_SOC_SLEEP_REJECT_INT_ST_V << PMU_SOC_SLEEP_REJECT_INT_ST_S)
#define PMU_SOC_SLEEP_REJECT_INT_ST_V  0x00000001U
#define PMU_SOC_SLEEP_REJECT_INT_ST_S  30
/** PMU_SOC_WAKEUP_INT_ST : RO; bitpos: [31]; default: 0;
 *  need_des
 */
#define PMU_SOC_WAKEUP_INT_ST    (BIT(31))
#define PMU_SOC_WAKEUP_INT_ST_M  (PMU_SOC_WAKEUP_INT_ST_V << PMU_SOC_WAKEUP_INT_ST_S)
#define PMU_SOC_WAKEUP_INT_ST_V  0x00000001U
#define PMU_SOC_WAKEUP_INT_ST_S  31

/** PMU_HP_INT_ENA_REG register
 *  need_des
 */
#define PMU_HP_INT_ENA_REG (DR_REG_PMU_BASE + 0x168)
/** PMU_LP_CPU_EXC_INT_ENA : R/W; bitpos: [27]; default: 0;
 *  need_des
 */
#define PMU_LP_CPU_EXC_INT_ENA    (BIT(27))
#define PMU_LP_CPU_EXC_INT_ENA_M  (PMU_LP_CPU_EXC_INT_ENA_V << PMU_LP_CPU_EXC_INT_ENA_S)
#define PMU_LP_CPU_EXC_INT_ENA_V  0x00000001U
#define PMU_LP_CPU_EXC_INT_ENA_S  27
/** PMU_SDIO_IDLE_INT_ENA : R/W; bitpos: [28]; default: 0;
 *  need_des
 */
#define PMU_SDIO_IDLE_INT_ENA    (BIT(28))
#define PMU_SDIO_IDLE_INT_ENA_M  (PMU_SDIO_IDLE_INT_ENA_V << PMU_SDIO_IDLE_INT_ENA_S)
#define PMU_SDIO_IDLE_INT_ENA_V  0x00000001U
#define PMU_SDIO_IDLE_INT_ENA_S  28
/** PMU_SW_INT_ENA : R/W; bitpos: [29]; default: 0;
 *  need_des
 */
#define PMU_SW_INT_ENA    (BIT(29))
#define PMU_SW_INT_ENA_M  (PMU_SW_INT_ENA_V << PMU_SW_INT_ENA_S)
#define PMU_SW_INT_ENA_V  0x00000001U
#define PMU_SW_INT_ENA_S  29
/** PMU_SOC_SLEEP_REJECT_INT_ENA : R/W; bitpos: [30]; default: 0;
 *  need_des
 */
#define PMU_SOC_SLEEP_REJECT_INT_ENA    (BIT(30))
#define PMU_SOC_SLEEP_REJECT_INT_ENA_M  (PMU_SOC_SLEEP_REJECT_INT_ENA_V << PMU_SOC_SLEEP_REJECT_INT_ENA_S)
#define PMU_SOC_SLEEP_REJECT_INT_ENA_V  0x00000001U
#define PMU_SOC_SLEEP_REJECT_INT_ENA_S  30
/** PMU_SOC_WAKEUP_INT_ENA : R/W; bitpos: [31]; default: 0;
 *  need_des
 */
#define PMU_SOC_WAKEUP_INT_ENA    (BIT(31))
#define PMU_SOC_WAKEUP_INT_ENA_M  (PMU_SOC_WAKEUP_INT_ENA_V << PMU_SOC_WAKEUP_INT_ENA_S)
#define PMU_SOC_WAKEUP_INT_ENA_V  0x00000001U
#define PMU_SOC_WAKEUP_INT_ENA_S  31

/** PMU_HP_INT_CLR_REG register
 *  need_des
 */
#define PMU_HP_INT_CLR_REG (DR_REG_PMU_BASE + 0x16c)
/** PMU_LP_CPU_EXC_INT_CLR : WT; bitpos: [27]; default: 0;
 *  need_des
 */
#define PMU_LP_CPU_EXC_INT_CLR    (BIT(27))
#define PMU_LP_CPU_EXC_INT_CLR_M  (PMU_LP_CPU_EXC_INT_CLR_V << PMU_LP_CPU_EXC_INT_CLR_S)
#define PMU_LP_CPU_EXC_INT_CLR_V  0x00000001U
#define PMU_LP_CPU_EXC_INT_CLR_S  27
/** PMU_SDIO_IDLE_INT_CLR : WT; bitpos: [28]; default: 0;
 *  need_des
 */
#define PMU_SDIO_IDLE_INT_CLR    (BIT(28))
#define PMU_SDIO_IDLE_INT_CLR_M  (PMU_SDIO_IDLE_INT_CLR_V << PMU_SDIO_IDLE_INT_CLR_S)
#define PMU_SDIO_IDLE_INT_CLR_V  0x00000001U
#define PMU_SDIO_IDLE_INT_CLR_S  28
/** PMU_SW_INT_CLR : WT; bitpos: [29]; default: 0;
 *  need_des
 */
#define PMU_SW_INT_CLR    (BIT(29))
#define PMU_SW_INT_CLR_M  (PMU_SW_INT_CLR_V << PMU_SW_INT_CLR_S)
#define PMU_SW_INT_CLR_V  0x00000001U
#define PMU_SW_INT_CLR_S  29
/** PMU_SOC_SLEEP_REJECT_INT_CLR : WT; bitpos: [30]; default: 0;
 *  need_des
 */
#define PMU_SOC_SLEEP_REJECT_INT_CLR    (BIT(30))
#define PMU_SOC_SLEEP_REJECT_INT_CLR_M  (PMU_SOC_SLEEP_REJECT_INT_CLR_V << PMU_SOC_SLEEP_REJECT_INT_CLR_S)
#define PMU_SOC_SLEEP_REJECT_INT_CLR_V  0x00000001U
#define PMU_SOC_SLEEP_REJECT_INT_CLR_S  30
/** PMU_SOC_WAKEUP_INT_CLR : WT; bitpos: [31]; default: 0;
 *  need_des
 */
#define PMU_SOC_WAKEUP_INT_CLR    (BIT(31))
#define PMU_SOC_WAKEUP_INT_CLR_M  (PMU_SOC_WAKEUP_INT_CLR_V << PMU_SOC_WAKEUP_INT_CLR_S)
#define PMU_SOC_WAKEUP_INT_CLR_V  0x00000001U
#define PMU_SOC_WAKEUP_INT_CLR_S  31

/** PMU_LP_INT_RAW_REG register
 *  need_des
 */
#define PMU_LP_INT_RAW_REG (DR_REG_PMU_BASE + 0x170)
/** PMU_LP_CPU_WAKEUP_INT_RAW : R/WTC/SS; bitpos: [20]; default: 0;
 *  need_des
 */
#define PMU_LP_CPU_WAKEUP_INT_RAW    (BIT(20))
#define PMU_LP_CPU_WAKEUP_INT_RAW_M  (PMU_LP_CPU_WAKEUP_INT_RAW_V << PMU_LP_CPU_WAKEUP_INT_RAW_S)
#define PMU_LP_CPU_WAKEUP_INT_RAW_V  0x00000001U
#define PMU_LP_CPU_WAKEUP_INT_RAW_S  20
/** PMU_MODEM_SWITCH_ACTIVE_END_INT_RAW : R/WTC/SS; bitpos: [21]; default: 0;
 *  need_des
 */
#define PMU_MODEM_SWITCH_ACTIVE_END_INT_RAW    (BIT(21))
#define PMU_MODEM_SWITCH_ACTIVE_END_INT_RAW_M  (PMU_MODEM_SWITCH_ACTIVE_END_INT_RAW_V << PMU_MODEM_SWITCH_ACTIVE_END_INT_RAW_S)
#define PMU_MODEM_SWITCH_ACTIVE_END_INT_RAW_V  0x00000001U
#define PMU_MODEM_SWITCH_ACTIVE_END_INT_RAW_S  21
/** PMU_SLEEP_SWITCH_ACTIVE_END_INT_RAW : R/WTC/SS; bitpos: [22]; default: 0;
 *  need_des
 */
#define PMU_SLEEP_SWITCH_ACTIVE_END_INT_RAW    (BIT(22))
#define PMU_SLEEP_SWITCH_ACTIVE_END_INT_RAW_M  (PMU_SLEEP_SWITCH_ACTIVE_END_INT_RAW_V << PMU_SLEEP_SWITCH_ACTIVE_END_INT_RAW_S)
#define PMU_SLEEP_SWITCH_ACTIVE_END_INT_RAW_V  0x00000001U
#define PMU_SLEEP_SWITCH_ACTIVE_END_INT_RAW_S  22
/** PMU_SLEEP_SWITCH_MODEM_END_INT_RAW : R/WTC/SS; bitpos: [23]; default: 0;
 *  need_des
 */
#define PMU_SLEEP_SWITCH_MODEM_END_INT_RAW    (BIT(23))
#define PMU_SLEEP_SWITCH_MODEM_END_INT_RAW_M  (PMU_SLEEP_SWITCH_MODEM_END_INT_RAW_V << PMU_SLEEP_SWITCH_MODEM_END_INT_RAW_S)
#define PMU_SLEEP_SWITCH_MODEM_END_INT_RAW_V  0x00000001U
#define PMU_SLEEP_SWITCH_MODEM_END_INT_RAW_S  23
/** PMU_MODEM_SWITCH_SLEEP_END_INT_RAW : R/WTC/SS; bitpos: [24]; default: 0;
 *  need_des
 */
#define PMU_MODEM_SWITCH_SLEEP_END_INT_RAW    (BIT(24))
#define PMU_MODEM_SWITCH_SLEEP_END_INT_RAW_M  (PMU_MODEM_SWITCH_SLEEP_END_INT_RAW_V << PMU_MODEM_SWITCH_SLEEP_END_INT_RAW_S)
#define PMU_MODEM_SWITCH_SLEEP_END_INT_RAW_V  0x00000001U
#define PMU_MODEM_SWITCH_SLEEP_END_INT_RAW_S  24
/** PMU_ACTIVE_SWITCH_SLEEP_END_INT_RAW : R/WTC/SS; bitpos: [25]; default: 0;
 *  need_des
 */
#define PMU_ACTIVE_SWITCH_SLEEP_END_INT_RAW    (BIT(25))
#define PMU_ACTIVE_SWITCH_SLEEP_END_INT_RAW_M  (PMU_ACTIVE_SWITCH_SLEEP_END_INT_RAW_V << PMU_ACTIVE_SWITCH_SLEEP_END_INT_RAW_S)
#define PMU_ACTIVE_SWITCH_SLEEP_END_INT_RAW_V  0x00000001U
#define PMU_ACTIVE_SWITCH_SLEEP_END_INT_RAW_S  25
/** PMU_MODEM_SWITCH_ACTIVE_START_INT_RAW : R/WTC/SS; bitpos: [26]; default: 0;
 *  need_des
 */
#define PMU_MODEM_SWITCH_ACTIVE_START_INT_RAW    (BIT(26))
#define PMU_MODEM_SWITCH_ACTIVE_START_INT_RAW_M  (PMU_MODEM_SWITCH_ACTIVE_START_INT_RAW_V << PMU_MODEM_SWITCH_ACTIVE_START_INT_RAW_S)
#define PMU_MODEM_SWITCH_ACTIVE_START_INT_RAW_V  0x00000001U
#define PMU_MODEM_SWITCH_ACTIVE_START_INT_RAW_S  26
/** PMU_SLEEP_SWITCH_ACTIVE_START_INT_RAW : R/WTC/SS; bitpos: [27]; default: 0;
 *  need_des
 */
#define PMU_SLEEP_SWITCH_ACTIVE_START_INT_RAW    (BIT(27))
#define PMU_SLEEP_SWITCH_ACTIVE_START_INT_RAW_M  (PMU_SLEEP_SWITCH_ACTIVE_START_INT_RAW_V << PMU_SLEEP_SWITCH_ACTIVE_START_INT_RAW_S)
#define PMU_SLEEP_SWITCH_ACTIVE_START_INT_RAW_V  0x00000001U
#define PMU_SLEEP_SWITCH_ACTIVE_START_INT_RAW_S  27
/** PMU_SLEEP_SWITCH_MODEM_START_INT_RAW : R/WTC/SS; bitpos: [28]; default: 0;
 *  need_des
 */
#define PMU_SLEEP_SWITCH_MODEM_START_INT_RAW    (BIT(28))
#define PMU_SLEEP_SWITCH_MODEM_START_INT_RAW_M  (PMU_SLEEP_SWITCH_MODEM_START_INT_RAW_V << PMU_SLEEP_SWITCH_MODEM_START_INT_RAW_S)
#define PMU_SLEEP_SWITCH_MODEM_START_INT_RAW_V  0x00000001U
#define PMU_SLEEP_SWITCH_MODEM_START_INT_RAW_S  28
/** PMU_MODEM_SWITCH_SLEEP_START_INT_RAW : R/WTC/SS; bitpos: [29]; default: 0;
 *  need_des
 */
#define PMU_MODEM_SWITCH_SLEEP_START_INT_RAW    (BIT(29))
#define PMU_MODEM_SWITCH_SLEEP_START_INT_RAW_M  (PMU_MODEM_SWITCH_SLEEP_START_INT_RAW_V << PMU_MODEM_SWITCH_SLEEP_START_INT_RAW_S)
#define PMU_MODEM_SWITCH_SLEEP_START_INT_RAW_V  0x00000001U
#define PMU_MODEM_SWITCH_SLEEP_START_INT_RAW_S  29
/** PMU_ACTIVE_SWITCH_SLEEP_START_INT_RAW : R/WTC/SS; bitpos: [30]; default: 0;
 *  need_des
 */
#define PMU_ACTIVE_SWITCH_SLEEP_START_INT_RAW    (BIT(30))
#define PMU_ACTIVE_SWITCH_SLEEP_START_INT_RAW_M  (PMU_ACTIVE_SWITCH_SLEEP_START_INT_RAW_V << PMU_ACTIVE_SWITCH_SLEEP_START_INT_RAW_S)
#define PMU_ACTIVE_SWITCH_SLEEP_START_INT_RAW_V  0x00000001U
#define PMU_ACTIVE_SWITCH_SLEEP_START_INT_RAW_S  30
/** PMU_HP_SW_TRIGGER_INT_RAW : R/WTC/SS; bitpos: [31]; default: 0;
 *  need_des
 */
#define PMU_HP_SW_TRIGGER_INT_RAW    (BIT(31))
#define PMU_HP_SW_TRIGGER_INT_RAW_M  (PMU_HP_SW_TRIGGER_INT_RAW_V << PMU_HP_SW_TRIGGER_INT_RAW_S)
#define PMU_HP_SW_TRIGGER_INT_RAW_V  0x00000001U
#define PMU_HP_SW_TRIGGER_INT_RAW_S  31

/** PMU_LP_INT_ST_REG register
 *  need_des
 */
#define PMU_LP_INT_ST_REG (DR_REG_PMU_BASE + 0x174)
/** PMU_LP_CPU_WAKEUP_INT_ST : RO; bitpos: [20]; default: 0;
 *  need_des
 */
#define PMU_LP_CPU_WAKEUP_INT_ST    (BIT(20))
#define PMU_LP_CPU_WAKEUP_INT_ST_M  (PMU_LP_CPU_WAKEUP_INT_ST_V << PMU_LP_CPU_WAKEUP_INT_ST_S)
#define PMU_LP_CPU_WAKEUP_INT_ST_V  0x00000001U
#define PMU_LP_CPU_WAKEUP_INT_ST_S  20
/** PMU_MODEM_SWITCH_ACTIVE_END_INT_ST : RO; bitpos: [21]; default: 0;
 *  need_des
 */
#define PMU_MODEM_SWITCH_ACTIVE_END_INT_ST    (BIT(21))
#define PMU_MODEM_SWITCH_ACTIVE_END_INT_ST_M  (PMU_MODEM_SWITCH_ACTIVE_END_INT_ST_V << PMU_MODEM_SWITCH_ACTIVE_END_INT_ST_S)
#define PMU_MODEM_SWITCH_ACTIVE_END_INT_ST_V  0x00000001U
#define PMU_MODEM_SWITCH_ACTIVE_END_INT_ST_S  21
/** PMU_SLEEP_SWITCH_ACTIVE_END_INT_ST : RO; bitpos: [22]; default: 0;
 *  need_des
 */
#define PMU_SLEEP_SWITCH_ACTIVE_END_INT_ST    (BIT(22))
#define PMU_SLEEP_SWITCH_ACTIVE_END_INT_ST_M  (PMU_SLEEP_SWITCH_ACTIVE_END_INT_ST_V << PMU_SLEEP_SWITCH_ACTIVE_END_INT_ST_S)
#define PMU_SLEEP_SWITCH_ACTIVE_END_INT_ST_V  0x00000001U
#define PMU_SLEEP_SWITCH_ACTIVE_END_INT_ST_S  22
/** PMU_SLEEP_SWITCH_MODEM_END_INT_ST : RO; bitpos: [23]; default: 0;
 *  need_des
 */
#define PMU_SLEEP_SWITCH_MODEM_END_INT_ST    (BIT(23))
#define PMU_SLEEP_SWITCH_MODEM_END_INT_ST_M  (PMU_SLEEP_SWITCH_MODEM_END_INT_ST_V << PMU_SLEEP_SWITCH_MODEM_END_INT_ST_S)
#define PMU_SLEEP_SWITCH_MODEM_END_INT_ST_V  0x00000001U
#define PMU_SLEEP_SWITCH_MODEM_END_INT_ST_S  23
/** PMU_MODEM_SWITCH_SLEEP_END_INT_ST : RO; bitpos: [24]; default: 0;
 *  need_des
 */
#define PMU_MODEM_SWITCH_SLEEP_END_INT_ST    (BIT(24))
#define PMU_MODEM_SWITCH_SLEEP_END_INT_ST_M  (PMU_MODEM_SWITCH_SLEEP_END_INT_ST_V << PMU_MODEM_SWITCH_SLEEP_END_INT_ST_S)
#define PMU_MODEM_SWITCH_SLEEP_END_INT_ST_V  0x00000001U
#define PMU_MODEM_SWITCH_SLEEP_END_INT_ST_S  24
/** PMU_ACTIVE_SWITCH_SLEEP_END_INT_ST : RO; bitpos: [25]; default: 0;
 *  need_des
 */
#define PMU_ACTIVE_SWITCH_SLEEP_END_INT_ST    (BIT(25))
#define PMU_ACTIVE_SWITCH_SLEEP_END_INT_ST_M  (PMU_ACTIVE_SWITCH_SLEEP_END_INT_ST_V << PMU_ACTIVE_SWITCH_SLEEP_END_INT_ST_S)
#define PMU_ACTIVE_SWITCH_SLEEP_END_INT_ST_V  0x00000001U
#define PMU_ACTIVE_SWITCH_SLEEP_END_INT_ST_S  25
/** PMU_MODEM_SWITCH_ACTIVE_START_INT_ST : RO; bitpos: [26]; default: 0;
 *  need_des
 */
#define PMU_MODEM_SWITCH_ACTIVE_START_INT_ST    (BIT(26))
#define PMU_MODEM_SWITCH_ACTIVE_START_INT_ST_M  (PMU_MODEM_SWITCH_ACTIVE_START_INT_ST_V << PMU_MODEM_SWITCH_ACTIVE_START_INT_ST_S)
#define PMU_MODEM_SWITCH_ACTIVE_START_INT_ST_V  0x00000001U
#define PMU_MODEM_SWITCH_ACTIVE_START_INT_ST_S  26
/** PMU_SLEEP_SWITCH_ACTIVE_START_INT_ST : RO; bitpos: [27]; default: 0;
 *  need_des
 */
#define PMU_SLEEP_SWITCH_ACTIVE_START_INT_ST    (BIT(27))
#define PMU_SLEEP_SWITCH_ACTIVE_START_INT_ST_M  (PMU_SLEEP_SWITCH_ACTIVE_START_INT_ST_V << PMU_SLEEP_SWITCH_ACTIVE_START_INT_ST_S)
#define PMU_SLEEP_SWITCH_ACTIVE_START_INT_ST_V  0x00000001U
#define PMU_SLEEP_SWITCH_ACTIVE_START_INT_ST_S  27
/** PMU_SLEEP_SWITCH_MODEM_START_INT_ST : RO; bitpos: [28]; default: 0;
 *  need_des
 */
#define PMU_SLEEP_SWITCH_MODEM_START_INT_ST    (BIT(28))
#define PMU_SLEEP_SWITCH_MODEM_START_INT_ST_M  (PMU_SLEEP_SWITCH_MODEM_START_INT_ST_V << PMU_SLEEP_SWITCH_MODEM_START_INT_ST_S)
#define PMU_SLEEP_SWITCH_MODEM_START_INT_ST_V  0x00000001U
#define PMU_SLEEP_SWITCH_MODEM_START_INT_ST_S  28
/** PMU_MODEM_SWITCH_SLEEP_START_INT_ST : RO; bitpos: [29]; default: 0;
 *  need_des
 */
#define PMU_MODEM_SWITCH_SLEEP_START_INT_ST    (BIT(29))
#define PMU_MODEM_SWITCH_SLEEP_START_INT_ST_M  (PMU_MODEM_SWITCH_SLEEP_START_INT_ST_V << PMU_MODEM_SWITCH_SLEEP_START_INT_ST_S)
#define PMU_MODEM_SWITCH_SLEEP_START_INT_ST_V  0x00000001U
#define PMU_MODEM_SWITCH_SLEEP_START_INT_ST_S  29
/** PMU_ACTIVE_SWITCH_SLEEP_START_INT_ST : RO; bitpos: [30]; default: 0;
 *  need_des
 */
#define PMU_ACTIVE_SWITCH_SLEEP_START_INT_ST    (BIT(30))
#define PMU_ACTIVE_SWITCH_SLEEP_START_INT_ST_M  (PMU_ACTIVE_SWITCH_SLEEP_START_INT_ST_V << PMU_ACTIVE_SWITCH_SLEEP_START_INT_ST_S)
#define PMU_ACTIVE_SWITCH_SLEEP_START_INT_ST_V  0x00000001U
#define PMU_ACTIVE_SWITCH_SLEEP_START_INT_ST_S  30
/** PMU_HP_SW_TRIGGER_INT_ST : RO; bitpos: [31]; default: 0;
 *  need_des
 */
#define PMU_HP_SW_TRIGGER_INT_ST    (BIT(31))
#define PMU_HP_SW_TRIGGER_INT_ST_M  (PMU_HP_SW_TRIGGER_INT_ST_V << PMU_HP_SW_TRIGGER_INT_ST_S)
#define PMU_HP_SW_TRIGGER_INT_ST_V  0x00000001U
#define PMU_HP_SW_TRIGGER_INT_ST_S  31

/** PMU_LP_INT_ENA_REG register
 *  need_des
 */
#define PMU_LP_INT_ENA_REG (DR_REG_PMU_BASE + 0x178)
/** PMU_LP_CPU_WAKEUP_INT_ENA : R/W; bitpos: [20]; default: 0;
 *  need_des
 */
#define PMU_LP_CPU_WAKEUP_INT_ENA    (BIT(20))
#define PMU_LP_CPU_WAKEUP_INT_ENA_M  (PMU_LP_CPU_WAKEUP_INT_ENA_V << PMU_LP_CPU_WAKEUP_INT_ENA_S)
#define PMU_LP_CPU_WAKEUP_INT_ENA_V  0x00000001U
#define PMU_LP_CPU_WAKEUP_INT_ENA_S  20
/** PMU_MODEM_SWITCH_ACTIVE_END_INT_ENA : R/W; bitpos: [21]; default: 0;
 *  need_des
 */
#define PMU_MODEM_SWITCH_ACTIVE_END_INT_ENA    (BIT(21))
#define PMU_MODEM_SWITCH_ACTIVE_END_INT_ENA_M  (PMU_MODEM_SWITCH_ACTIVE_END_INT_ENA_V << PMU_MODEM_SWITCH_ACTIVE_END_INT_ENA_S)
#define PMU_MODEM_SWITCH_ACTIVE_END_INT_ENA_V  0x00000001U
#define PMU_MODEM_SWITCH_ACTIVE_END_INT_ENA_S  21
/** PMU_SLEEP_SWITCH_ACTIVE_END_INT_ENA : R/W; bitpos: [22]; default: 0;
 *  need_des
 */
#define PMU_SLEEP_SWITCH_ACTIVE_END_INT_ENA    (BIT(22))
#define PMU_SLEEP_SWITCH_ACTIVE_END_INT_ENA_M  (PMU_SLEEP_SWITCH_ACTIVE_END_INT_ENA_V << PMU_SLEEP_SWITCH_ACTIVE_END_INT_ENA_S)
#define PMU_SLEEP_SWITCH_ACTIVE_END_INT_ENA_V  0x00000001U
#define PMU_SLEEP_SWITCH_ACTIVE_END_INT_ENA_S  22
/** PMU_SLEEP_SWITCH_MODEM_END_INT_ENA : R/W; bitpos: [23]; default: 0;
 *  need_des
 */
#define PMU_SLEEP_SWITCH_MODEM_END_INT_ENA    (BIT(23))
#define PMU_SLEEP_SWITCH_MODEM_END_INT_ENA_M  (PMU_SLEEP_SWITCH_MODEM_END_INT_ENA_V << PMU_SLEEP_SWITCH_MODEM_END_INT_ENA_S)
#define PMU_SLEEP_SWITCH_MODEM_END_INT_ENA_V  0x00000001U
#define PMU_SLEEP_SWITCH_MODEM_END_INT_ENA_S  23
/** PMU_MODEM_SWITCH_SLEEP_END_INT_ENA : R/W; bitpos: [24]; default: 0;
 *  need_des
 */
#define PMU_MODEM_SWITCH_SLEEP_END_INT_ENA    (BIT(24))
#define PMU_MODEM_SWITCH_SLEEP_END_INT_ENA_M  (PMU_MODEM_SWITCH_SLEEP_END_INT_ENA_V << PMU_MODEM_SWITCH_SLEEP_END_INT_ENA_S)
#define PMU_MODEM_SWITCH_SLEEP_END_INT_ENA_V  0x00000001U
#define PMU_MODEM_SWITCH_SLEEP_END_INT_ENA_S  24
/** PMU_ACTIVE_SWITCH_SLEEP_END_INT_ENA : R/W; bitpos: [25]; default: 0;
 *  need_des
 */
#define PMU_ACTIVE_SWITCH_SLEEP_END_INT_ENA    (BIT(25))
#define PMU_ACTIVE_SWITCH_SLEEP_END_INT_ENA_M  (PMU_ACTIVE_SWITCH_SLEEP_END_INT_ENA_V << PMU_ACTIVE_SWITCH_SLEEP_END_INT_ENA_S)
#define PMU_ACTIVE_SWITCH_SLEEP_END_INT_ENA_V  0x00000001U
#define PMU_ACTIVE_SWITCH_SLEEP_END_INT_ENA_S  25
/** PMU_MODEM_SWITCH_ACTIVE_START_INT_ENA : R/W; bitpos: [26]; default: 0;
 *  need_des
 */
#define PMU_MODEM_SWITCH_ACTIVE_START_INT_ENA    (BIT(26))
#define PMU_MODEM_SWITCH_ACTIVE_START_INT_ENA_M  (PMU_MODEM_SWITCH_ACTIVE_START_INT_ENA_V << PMU_MODEM_SWITCH_ACTIVE_START_INT_ENA_S)
#define PMU_MODEM_SWITCH_ACTIVE_START_INT_ENA_V  0x00000001U
#define PMU_MODEM_SWITCH_ACTIVE_START_INT_ENA_S  26
/** PMU_SLEEP_SWITCH_ACTIVE_START_INT_ENA : R/W; bitpos: [27]; default: 0;
 *  need_des
 */
#define PMU_SLEEP_SWITCH_ACTIVE_START_INT_ENA    (BIT(27))
#define PMU_SLEEP_SWITCH_ACTIVE_START_INT_ENA_M  (PMU_SLEEP_SWITCH_ACTIVE_START_INT_ENA_V << PMU_SLEEP_SWITCH_ACTIVE_START_INT_ENA_S)
#define PMU_SLEEP_SWITCH_ACTIVE_START_INT_ENA_V  0x00000001U
#define PMU_SLEEP_SWITCH_ACTIVE_START_INT_ENA_S  27
/** PMU_SLEEP_SWITCH_MODEM_START_INT_ENA : R/W; bitpos: [28]; default: 0;
 *  need_des
 */
#define PMU_SLEEP_SWITCH_MODEM_START_INT_ENA    (BIT(28))
#define PMU_SLEEP_SWITCH_MODEM_START_INT_ENA_M  (PMU_SLEEP_SWITCH_MODEM_START_INT_ENA_V << PMU_SLEEP_SWITCH_MODEM_START_INT_ENA_S)
#define PMU_SLEEP_SWITCH_MODEM_START_INT_ENA_V  0x00000001U
#define PMU_SLEEP_SWITCH_MODEM_START_INT_ENA_S  28
/** PMU_MODEM_SWITCH_SLEEP_START_INT_ENA : R/W; bitpos: [29]; default: 0;
 *  need_des
 */
#define PMU_MODEM_SWITCH_SLEEP_START_INT_ENA    (BIT(29))
#define PMU_MODEM_SWITCH_SLEEP_START_INT_ENA_M  (PMU_MODEM_SWITCH_SLEEP_START_INT_ENA_V << PMU_MODEM_SWITCH_SLEEP_START_INT_ENA_S)
#define PMU_MODEM_SWITCH_SLEEP_START_INT_ENA_V  0x00000001U
#define PMU_MODEM_SWITCH_SLEEP_START_INT_ENA_S  29
/** PMU_ACTIVE_SWITCH_SLEEP_START_INT_ENA : R/W; bitpos: [30]; default: 0;
 *  need_des
 */
#define PMU_ACTIVE_SWITCH_SLEEP_START_INT_ENA    (BIT(30))
#define PMU_ACTIVE_SWITCH_SLEEP_START_INT_ENA_M  (PMU_ACTIVE_SWITCH_SLEEP_START_INT_ENA_V << PMU_ACTIVE_SWITCH_SLEEP_START_INT_ENA_S)
#define PMU_ACTIVE_SWITCH_SLEEP_START_INT_ENA_V  0x00000001U
#define PMU_ACTIVE_SWITCH_SLEEP_START_INT_ENA_S  30
/** PMU_HP_SW_TRIGGER_INT_ENA : R/W; bitpos: [31]; default: 0;
 *  need_des
 */
#define PMU_HP_SW_TRIGGER_INT_ENA    (BIT(31))
#define PMU_HP_SW_TRIGGER_INT_ENA_M  (PMU_HP_SW_TRIGGER_INT_ENA_V << PMU_HP_SW_TRIGGER_INT_ENA_S)
#define PMU_HP_SW_TRIGGER_INT_ENA_V  0x00000001U
#define PMU_HP_SW_TRIGGER_INT_ENA_S  31

/** PMU_LP_INT_CLR_REG register
 *  need_des
 */
#define PMU_LP_INT_CLR_REG (DR_REG_PMU_BASE + 0x17c)
/** PMU_LP_CPU_WAKEUP_INT_CLR : WT; bitpos: [20]; default: 0;
 *  need_des
 */
#define PMU_LP_CPU_WAKEUP_INT_CLR    (BIT(20))
#define PMU_LP_CPU_WAKEUP_INT_CLR_M  (PMU_LP_CPU_WAKEUP_INT_CLR_V << PMU_LP_CPU_WAKEUP_INT_CLR_S)
#define PMU_LP_CPU_WAKEUP_INT_CLR_V  0x00000001U
#define PMU_LP_CPU_WAKEUP_INT_CLR_S  20
/** PMU_MODEM_SWITCH_ACTIVE_END_INT_CLR : WT; bitpos: [21]; default: 0;
 *  need_des
 */
#define PMU_MODEM_SWITCH_ACTIVE_END_INT_CLR    (BIT(21))
#define PMU_MODEM_SWITCH_ACTIVE_END_INT_CLR_M  (PMU_MODEM_SWITCH_ACTIVE_END_INT_CLR_V << PMU_MODEM_SWITCH_ACTIVE_END_INT_CLR_S)
#define PMU_MODEM_SWITCH_ACTIVE_END_INT_CLR_V  0x00000001U
#define PMU_MODEM_SWITCH_ACTIVE_END_INT_CLR_S  21
/** PMU_SLEEP_SWITCH_ACTIVE_END_INT_CLR : WT; bitpos: [22]; default: 0;
 *  need_des
 */
#define PMU_SLEEP_SWITCH_ACTIVE_END_INT_CLR    (BIT(22))
#define PMU_SLEEP_SWITCH_ACTIVE_END_INT_CLR_M  (PMU_SLEEP_SWITCH_ACTIVE_END_INT_CLR_V << PMU_SLEEP_SWITCH_ACTIVE_END_INT_CLR_S)
#define PMU_SLEEP_SWITCH_ACTIVE_END_INT_CLR_V  0x00000001U
#define PMU_SLEEP_SWITCH_ACTIVE_END_INT_CLR_S  22
/** PMU_SLEEP_SWITCH_MODEM_END_INT_CLR : WT; bitpos: [23]; default: 0;
 *  need_des
 */
#define PMU_SLEEP_SWITCH_MODEM_END_INT_CLR    (BIT(23))
#define PMU_SLEEP_SWITCH_MODEM_END_INT_CLR_M  (PMU_SLEEP_SWITCH_MODEM_END_INT_CLR_V << PMU_SLEEP_SWITCH_MODEM_END_INT_CLR_S)
#define PMU_SLEEP_SWITCH_MODEM_END_INT_CLR_V  0x00000001U
#define PMU_SLEEP_SWITCH_MODEM_END_INT_CLR_S  23
/** PMU_MODEM_SWITCH_SLEEP_END_INT_CLR : WT; bitpos: [24]; default: 0;
 *  need_des
 */
#define PMU_MODEM_SWITCH_SLEEP_END_INT_CLR    (BIT(24))
#define PMU_MODEM_SWITCH_SLEEP_END_INT_CLR_M  (PMU_MODEM_SWITCH_SLEEP_END_INT_CLR_V << PMU_MODEM_SWITCH_SLEEP_END_INT_CLR_S)
#define PMU_MODEM_SWITCH_SLEEP_END_INT_CLR_V  0x00000001U
#define PMU_MODEM_SWITCH_SLEEP_END_INT_CLR_S  24
/** PMU_ACTIVE_SWITCH_SLEEP_END_INT_CLR : WT; bitpos: [25]; default: 0;
 *  need_des
 */
#define PMU_ACTIVE_SWITCH_SLEEP_END_INT_CLR    (BIT(25))
#define PMU_ACTIVE_SWITCH_SLEEP_END_INT_CLR_M  (PMU_ACTIVE_SWITCH_SLEEP_END_INT_CLR_V << PMU_ACTIVE_SWITCH_SLEEP_END_INT_CLR_S)
#define PMU_ACTIVE_SWITCH_SLEEP_END_INT_CLR_V  0x00000001U
#define PMU_ACTIVE_SWITCH_SLEEP_END_INT_CLR_S  25
/** PMU_MODEM_SWITCH_ACTIVE_START_INT_CLR : WT; bitpos: [26]; default: 0;
 *  need_des
 */
#define PMU_MODEM_SWITCH_ACTIVE_START_INT_CLR    (BIT(26))
#define PMU_MODEM_SWITCH_ACTIVE_START_INT_CLR_M  (PMU_MODEM_SWITCH_ACTIVE_START_INT_CLR_V << PMU_MODEM_SWITCH_ACTIVE_START_INT_CLR_S)
#define PMU_MODEM_SWITCH_ACTIVE_START_INT_CLR_V  0x00000001U
#define PMU_MODEM_SWITCH_ACTIVE_START_INT_CLR_S  26
/** PMU_SLEEP_SWITCH_ACTIVE_START_INT_CLR : WT; bitpos: [27]; default: 0;
 *  need_des
 */
#define PMU_SLEEP_SWITCH_ACTIVE_START_INT_CLR    (BIT(27))
#define PMU_SLEEP_SWITCH_ACTIVE_START_INT_CLR_M  (PMU_SLEEP_SWITCH_ACTIVE_START_INT_CLR_V << PMU_SLEEP_SWITCH_ACTIVE_START_INT_CLR_S)
#define PMU_SLEEP_SWITCH_ACTIVE_START_INT_CLR_V  0x00000001U
#define PMU_SLEEP_SWITCH_ACTIVE_START_INT_CLR_S  27
/** PMU_SLEEP_SWITCH_MODEM_START_INT_CLR : WT; bitpos: [28]; default: 0;
 *  need_des
 */
#define PMU_SLEEP_SWITCH_MODEM_START_INT_CLR    (BIT(28))
#define PMU_SLEEP_SWITCH_MODEM_START_INT_CLR_M  (PMU_SLEEP_SWITCH_MODEM_START_INT_CLR_V << PMU_SLEEP_SWITCH_MODEM_START_INT_CLR_S)
#define PMU_SLEEP_SWITCH_MODEM_START_INT_CLR_V  0x00000001U
#define PMU_SLEEP_SWITCH_MODEM_START_INT_CLR_S  28
/** PMU_MODEM_SWITCH_SLEEP_START_INT_CLR : WT; bitpos: [29]; default: 0;
 *  need_des
 */
#define PMU_MODEM_SWITCH_SLEEP_START_INT_CLR    (BIT(29))
#define PMU_MODEM_SWITCH_SLEEP_START_INT_CLR_M  (PMU_MODEM_SWITCH_SLEEP_START_INT_CLR_V << PMU_MODEM_SWITCH_SLEEP_START_INT_CLR_S)
#define PMU_MODEM_SWITCH_SLEEP_START_INT_CLR_V  0x00000001U
#define PMU_MODEM_SWITCH_SLEEP_START_INT_CLR_S  29
/** PMU_ACTIVE_SWITCH_SLEEP_START_INT_CLR : WT; bitpos: [30]; default: 0;
 *  need_des
 */
#define PMU_ACTIVE_SWITCH_SLEEP_START_INT_CLR    (BIT(30))
#define PMU_ACTIVE_SWITCH_SLEEP_START_INT_CLR_M  (PMU_ACTIVE_SWITCH_SLEEP_START_INT_CLR_V << PMU_ACTIVE_SWITCH_SLEEP_START_INT_CLR_S)
#define PMU_ACTIVE_SWITCH_SLEEP_START_INT_CLR_V  0x00000001U
#define PMU_ACTIVE_SWITCH_SLEEP_START_INT_CLR_S  30
/** PMU_HP_SW_TRIGGER_INT_CLR : WT; bitpos: [31]; default: 0;
 *  need_des
 */
#define PMU_HP_SW_TRIGGER_INT_CLR    (BIT(31))
#define PMU_HP_SW_TRIGGER_INT_CLR_M  (PMU_HP_SW_TRIGGER_INT_CLR_V << PMU_HP_SW_TRIGGER_INT_CLR_S)
#define PMU_HP_SW_TRIGGER_INT_CLR_V  0x00000001U
#define PMU_HP_SW_TRIGGER_INT_CLR_S  31

/** PMU_LP_CPU_PWR0_REG register
 *  need_des
 */
#define PMU_LP_CPU_PWR0_REG (DR_REG_PMU_BASE + 0x180)
/** PMU_LP_CPU_WAITI_RDY : RO; bitpos: [0]; default: 0;
 *  need_des
 */
#define PMU_LP_CPU_WAITI_RDY    (BIT(0))
#define PMU_LP_CPU_WAITI_RDY_M  (PMU_LP_CPU_WAITI_RDY_V << PMU_LP_CPU_WAITI_RDY_S)
#define PMU_LP_CPU_WAITI_RDY_V  0x00000001U
#define PMU_LP_CPU_WAITI_RDY_S  0
/** PMU_LP_CPU_STALL_RDY : RO; bitpos: [1]; default: 0;
 *  need_des
 */
#define PMU_LP_CPU_STALL_RDY    (BIT(1))
#define PMU_LP_CPU_STALL_RDY_M  (PMU_LP_CPU_STALL_RDY_V << PMU_LP_CPU_STALL_RDY_S)
#define PMU_LP_CPU_STALL_RDY_V  0x00000001U
#define PMU_LP_CPU_STALL_RDY_S  1
/** PMU_LP_CPU_FORCE_STALL : R/W; bitpos: [18]; default: 0;
 *  need_des
 */
#define PMU_LP_CPU_FORCE_STALL    (BIT(18))
#define PMU_LP_CPU_FORCE_STALL_M  (PMU_LP_CPU_FORCE_STALL_V << PMU_LP_CPU_FORCE_STALL_S)
#define PMU_LP_CPU_FORCE_STALL_V  0x00000001U
#define PMU_LP_CPU_FORCE_STALL_S  18
/** PMU_LP_CPU_SLP_WAITI_FLAG_EN : R/W; bitpos: [19]; default: 0;
 *  need_des
 */
#define PMU_LP_CPU_SLP_WAITI_FLAG_EN    (BIT(19))
#define PMU_LP_CPU_SLP_WAITI_FLAG_EN_M  (PMU_LP_CPU_SLP_WAITI_FLAG_EN_V << PMU_LP_CPU_SLP_WAITI_FLAG_EN_S)
#define PMU_LP_CPU_SLP_WAITI_FLAG_EN_V  0x00000001U
#define PMU_LP_CPU_SLP_WAITI_FLAG_EN_S  19
/** PMU_LP_CPU_SLP_STALL_FLAG_EN : R/W; bitpos: [20]; default: 1;
 *  need_des
 */
#define PMU_LP_CPU_SLP_STALL_FLAG_EN    (BIT(20))
#define PMU_LP_CPU_SLP_STALL_FLAG_EN_M  (PMU_LP_CPU_SLP_STALL_FLAG_EN_V << PMU_LP_CPU_SLP_STALL_FLAG_EN_S)
#define PMU_LP_CPU_SLP_STALL_FLAG_EN_V  0x00000001U
#define PMU_LP_CPU_SLP_STALL_FLAG_EN_S  20
/** PMU_LP_CPU_SLP_STALL_WAIT : R/W; bitpos: [28:21]; default: 255;
 *  need_des
 */
#define PMU_LP_CPU_SLP_STALL_WAIT    0x000000FFU
#define PMU_LP_CPU_SLP_STALL_WAIT_M  (PMU_LP_CPU_SLP_STALL_WAIT_V << PMU_LP_CPU_SLP_STALL_WAIT_S)
#define PMU_LP_CPU_SLP_STALL_WAIT_V  0x000000FFU
#define PMU_LP_CPU_SLP_STALL_WAIT_S  21
/** PMU_LP_CPU_SLP_STALL_EN : R/W; bitpos: [29]; default: 0;
 *  need_des
 */
#define PMU_LP_CPU_SLP_STALL_EN    (BIT(29))
#define PMU_LP_CPU_SLP_STALL_EN_M  (PMU_LP_CPU_SLP_STALL_EN_V << PMU_LP_CPU_SLP_STALL_EN_S)
#define PMU_LP_CPU_SLP_STALL_EN_V  0x00000001U
#define PMU_LP_CPU_SLP_STALL_EN_S  29
/** PMU_LP_CPU_SLP_RESET_EN : R/W; bitpos: [30]; default: 0;
 *  need_des
 */
#define PMU_LP_CPU_SLP_RESET_EN    (BIT(30))
#define PMU_LP_CPU_SLP_RESET_EN_M  (PMU_LP_CPU_SLP_RESET_EN_V << PMU_LP_CPU_SLP_RESET_EN_S)
#define PMU_LP_CPU_SLP_RESET_EN_V  0x00000001U
#define PMU_LP_CPU_SLP_RESET_EN_S  30
/** PMU_LP_CPU_SLP_BYPASS_INTR_EN : R/W; bitpos: [31]; default: 0;
 *  need_des
 */
#define PMU_LP_CPU_SLP_BYPASS_INTR_EN    (BIT(31))
#define PMU_LP_CPU_SLP_BYPASS_INTR_EN_M  (PMU_LP_CPU_SLP_BYPASS_INTR_EN_V << PMU_LP_CPU_SLP_BYPASS_INTR_EN_S)
#define PMU_LP_CPU_SLP_BYPASS_INTR_EN_V  0x00000001U
#define PMU_LP_CPU_SLP_BYPASS_INTR_EN_S  31

/** PMU_LP_CPU_PWR1_REG register
 *  need_des
 */
#define PMU_LP_CPU_PWR1_REG (DR_REG_PMU_BASE + 0x184)
/** PMU_LP_CPU_WAKEUP_EN : R/W; bitpos: [15:0]; default: 0;
 *  need_des
 */
#define PMU_LP_CPU_WAKEUP_EN    0x0000FFFFU
#define PMU_LP_CPU_WAKEUP_EN_M  (PMU_LP_CPU_WAKEUP_EN_V << PMU_LP_CPU_WAKEUP_EN_S)
#define PMU_LP_CPU_WAKEUP_EN_V  0x0000FFFFU
#define PMU_LP_CPU_WAKEUP_EN_S  0
/** PMU_LP_CPU_SLEEP_REQ : WT; bitpos: [31]; default: 0;
 *  need_des
 */
#define PMU_LP_CPU_SLEEP_REQ    (BIT(31))
#define PMU_LP_CPU_SLEEP_REQ_M  (PMU_LP_CPU_SLEEP_REQ_V << PMU_LP_CPU_SLEEP_REQ_S)
#define PMU_LP_CPU_SLEEP_REQ_V  0x00000001U
#define PMU_LP_CPU_SLEEP_REQ_S  31

/** PMU_HP_LP_CPU_COMM_REG register
 *  need_des
 */
#define PMU_HP_LP_CPU_COMM_REG (DR_REG_PMU_BASE + 0x188)
/** PMU_LP_TRIGGER_HP : WT; bitpos: [30]; default: 0;
 *  need_des
 */
#define PMU_LP_TRIGGER_HP    (BIT(30))
#define PMU_LP_TRIGGER_HP_M  (PMU_LP_TRIGGER_HP_V << PMU_LP_TRIGGER_HP_S)
#define PMU_LP_TRIGGER_HP_V  0x00000001U
#define PMU_LP_TRIGGER_HP_S  30
/** PMU_HP_TRIGGER_LP : WT; bitpos: [31]; default: 0;
 *  need_des
 */
#define PMU_HP_TRIGGER_LP    (BIT(31))
#define PMU_HP_TRIGGER_LP_M  (PMU_HP_TRIGGER_LP_V << PMU_HP_TRIGGER_LP_S)
#define PMU_HP_TRIGGER_LP_V  0x00000001U
#define PMU_HP_TRIGGER_LP_S  31

/** PMU_HP_REGULATOR_CFG_REG register
 *  need_des
 */
#define PMU_HP_REGULATOR_CFG_REG (DR_REG_PMU_BASE + 0x18c)
/** PMU_DIG_REGULATOR_EN_CAL : R/W; bitpos: [31]; default: 0;
 *  need_des
 */
#define PMU_DIG_REGULATOR_EN_CAL    (BIT(31))
#define PMU_DIG_REGULATOR_EN_CAL_M  (PMU_DIG_REGULATOR_EN_CAL_V << PMU_DIG_REGULATOR_EN_CAL_S)
#define PMU_DIG_REGULATOR_EN_CAL_V  0x00000001U
#define PMU_DIG_REGULATOR_EN_CAL_S  31

/** PMU_MAIN_STATE_REG register
 *  need_des
 */
#define PMU_MAIN_STATE_REG (DR_REG_PMU_BASE + 0x190)
/** PMU_MAIN_LAST_ST_STATE : RO; bitpos: [17:11]; default: 1;
 *  need_des
 */
#define PMU_MAIN_LAST_ST_STATE    0x0000007FU
#define PMU_MAIN_LAST_ST_STATE_M  (PMU_MAIN_LAST_ST_STATE_V << PMU_MAIN_LAST_ST_STATE_S)
#define PMU_MAIN_LAST_ST_STATE_V  0x0000007FU
#define PMU_MAIN_LAST_ST_STATE_S  11
/** PMU_MAIN_TAR_ST_STATE : RO; bitpos: [24:18]; default: 4;
 *  need_des
 */
#define PMU_MAIN_TAR_ST_STATE    0x0000007FU
#define PMU_MAIN_TAR_ST_STATE_M  (PMU_MAIN_TAR_ST_STATE_V << PMU_MAIN_TAR_ST_STATE_S)
#define PMU_MAIN_TAR_ST_STATE_V  0x0000007FU
#define PMU_MAIN_TAR_ST_STATE_S  18
/** PMU_MAIN_CUR_ST_STATE : RO; bitpos: [31:25]; default: 4;
 *  need_des
 */
#define PMU_MAIN_CUR_ST_STATE    0x0000007FU
#define PMU_MAIN_CUR_ST_STATE_M  (PMU_MAIN_CUR_ST_STATE_V << PMU_MAIN_CUR_ST_STATE_S)
#define PMU_MAIN_CUR_ST_STATE_V  0x0000007FU
#define PMU_MAIN_CUR_ST_STATE_S  25

/** PMU_PWR_STATE_REG register
 *  need_des
 */
#define PMU_PWR_STATE_REG (DR_REG_PMU_BASE + 0x194)
/** PMU_BACKUP_ST_STATE : RO; bitpos: [17:13]; default: 1;
 *  need_des
 */
#define PMU_BACKUP_ST_STATE    0x0000001FU
#define PMU_BACKUP_ST_STATE_M  (PMU_BACKUP_ST_STATE_V << PMU_BACKUP_ST_STATE_S)
#define PMU_BACKUP_ST_STATE_V  0x0000001FU
#define PMU_BACKUP_ST_STATE_S  13
/** PMU_LP_PWR_ST_STATE : RO; bitpos: [22:18]; default: 0;
 *  need_des
 */
#define PMU_LP_PWR_ST_STATE    0x0000001FU
#define PMU_LP_PWR_ST_STATE_M  (PMU_LP_PWR_ST_STATE_V << PMU_LP_PWR_ST_STATE_S)
#define PMU_LP_PWR_ST_STATE_V  0x0000001FU
#define PMU_LP_PWR_ST_STATE_S  18
/** PMU_HP_PWR_ST_STATE : RO; bitpos: [31:23]; default: 1;
 *  need_des
 */
#define PMU_HP_PWR_ST_STATE    0x000001FFU
#define PMU_HP_PWR_ST_STATE_M  (PMU_HP_PWR_ST_STATE_V << PMU_HP_PWR_ST_STATE_S)
#define PMU_HP_PWR_ST_STATE_V  0x000001FFU
#define PMU_HP_PWR_ST_STATE_S  23

/** PMU_CLK_STATE0_REG register
 *  need_des
 */
#define PMU_CLK_STATE0_REG (DR_REG_PMU_BASE + 0x198)
/** PMU_STABLE_XPD_BBPLL_STATE : RO; bitpos: [0]; default: 1;
 *  need_des
 */
#define PMU_STABLE_XPD_BBPLL_STATE    (BIT(0))
#define PMU_STABLE_XPD_BBPLL_STATE_M  (PMU_STABLE_XPD_BBPLL_STATE_V << PMU_STABLE_XPD_BBPLL_STATE_S)
#define PMU_STABLE_XPD_BBPLL_STATE_V  0x00000001U
#define PMU_STABLE_XPD_BBPLL_STATE_S  0
/** PMU_STABLE_XPD_XTAL_STATE : RO; bitpos: [1]; default: 1;
 *  need_des
 */
#define PMU_STABLE_XPD_XTAL_STATE    (BIT(1))
#define PMU_STABLE_XPD_XTAL_STATE_M  (PMU_STABLE_XPD_XTAL_STATE_V << PMU_STABLE_XPD_XTAL_STATE_S)
#define PMU_STABLE_XPD_XTAL_STATE_V  0x00000001U
#define PMU_STABLE_XPD_XTAL_STATE_S  1
/** PMU_SYS_CLK_SLP_SEL_STATE : RO; bitpos: [15]; default: 0;
 *  need_des
 */
#define PMU_SYS_CLK_SLP_SEL_STATE    (BIT(15))
#define PMU_SYS_CLK_SLP_SEL_STATE_M  (PMU_SYS_CLK_SLP_SEL_STATE_V << PMU_SYS_CLK_SLP_SEL_STATE_S)
#define PMU_SYS_CLK_SLP_SEL_STATE_V  0x00000001U
#define PMU_SYS_CLK_SLP_SEL_STATE_S  15
/** PMU_SYS_CLK_SEL_STATE : RO; bitpos: [17:16]; default: 0;
 *  need_des
 */
#define PMU_SYS_CLK_SEL_STATE    0x00000003U
#define PMU_SYS_CLK_SEL_STATE_M  (PMU_SYS_CLK_SEL_STATE_V << PMU_SYS_CLK_SEL_STATE_S)
#define PMU_SYS_CLK_SEL_STATE_V  0x00000003U
#define PMU_SYS_CLK_SEL_STATE_S  16
/** PMU_SYS_CLK_NO_DIV_STATE : RO; bitpos: [18]; default: 0;
 *  need_des
 */
#define PMU_SYS_CLK_NO_DIV_STATE    (BIT(18))
#define PMU_SYS_CLK_NO_DIV_STATE_M  (PMU_SYS_CLK_NO_DIV_STATE_V << PMU_SYS_CLK_NO_DIV_STATE_S)
#define PMU_SYS_CLK_NO_DIV_STATE_V  0x00000001U
#define PMU_SYS_CLK_NO_DIV_STATE_S  18
/** PMU_ICG_SYS_CLK_EN_STATE : RO; bitpos: [19]; default: 0;
 *  need_des
 */
#define PMU_ICG_SYS_CLK_EN_STATE    (BIT(19))
#define PMU_ICG_SYS_CLK_EN_STATE_M  (PMU_ICG_SYS_CLK_EN_STATE_V << PMU_ICG_SYS_CLK_EN_STATE_S)
#define PMU_ICG_SYS_CLK_EN_STATE_V  0x00000001U
#define PMU_ICG_SYS_CLK_EN_STATE_S  19
/** PMU_ICG_MODEM_SWITCH_STATE : RO; bitpos: [20]; default: 0;
 *  need_des
 */
#define PMU_ICG_MODEM_SWITCH_STATE    (BIT(20))
#define PMU_ICG_MODEM_SWITCH_STATE_M  (PMU_ICG_MODEM_SWITCH_STATE_V << PMU_ICG_MODEM_SWITCH_STATE_S)
#define PMU_ICG_MODEM_SWITCH_STATE_V  0x00000001U
#define PMU_ICG_MODEM_SWITCH_STATE_S  20
/** PMU_ICG_MODEM_CODE_STATE : RO; bitpos: [22:21]; default: 0;
 *  need_des
 */
#define PMU_ICG_MODEM_CODE_STATE    0x00000003U
#define PMU_ICG_MODEM_CODE_STATE_M  (PMU_ICG_MODEM_CODE_STATE_V << PMU_ICG_MODEM_CODE_STATE_S)
#define PMU_ICG_MODEM_CODE_STATE_V  0x00000003U
#define PMU_ICG_MODEM_CODE_STATE_S  21
/** PMU_ICG_SLP_SEL_STATE : RO; bitpos: [23]; default: 0;
 *  need_des
 */
#define PMU_ICG_SLP_SEL_STATE    (BIT(23))
#define PMU_ICG_SLP_SEL_STATE_M  (PMU_ICG_SLP_SEL_STATE_V << PMU_ICG_SLP_SEL_STATE_S)
#define PMU_ICG_SLP_SEL_STATE_V  0x00000001U
#define PMU_ICG_SLP_SEL_STATE_S  23
/** PMU_ICG_GLOBAL_XTAL_STATE : RO; bitpos: [24]; default: 0;
 *  need_des
 */
#define PMU_ICG_GLOBAL_XTAL_STATE    (BIT(24))
#define PMU_ICG_GLOBAL_XTAL_STATE_M  (PMU_ICG_GLOBAL_XTAL_STATE_V << PMU_ICG_GLOBAL_XTAL_STATE_S)
#define PMU_ICG_GLOBAL_XTAL_STATE_V  0x00000001U
#define PMU_ICG_GLOBAL_XTAL_STATE_S  24
/** PMU_ICG_GLOBAL_PLL_STATE : RO; bitpos: [25]; default: 0;
 *  need_des
 */
#define PMU_ICG_GLOBAL_PLL_STATE    (BIT(25))
#define PMU_ICG_GLOBAL_PLL_STATE_M  (PMU_ICG_GLOBAL_PLL_STATE_V << PMU_ICG_GLOBAL_PLL_STATE_S)
#define PMU_ICG_GLOBAL_PLL_STATE_V  0x00000001U
#define PMU_ICG_GLOBAL_PLL_STATE_S  25
/** PMU_ANA_I2C_ISO_EN_STATE : RO; bitpos: [26]; default: 0;
 *  need_des
 */
#define PMU_ANA_I2C_ISO_EN_STATE    (BIT(26))
#define PMU_ANA_I2C_ISO_EN_STATE_M  (PMU_ANA_I2C_ISO_EN_STATE_V << PMU_ANA_I2C_ISO_EN_STATE_S)
#define PMU_ANA_I2C_ISO_EN_STATE_V  0x00000001U
#define PMU_ANA_I2C_ISO_EN_STATE_S  26
/** PMU_ANA_I2C_RETENTION_STATE : RO; bitpos: [27]; default: 0;
 *  need_des
 */
#define PMU_ANA_I2C_RETENTION_STATE    (BIT(27))
#define PMU_ANA_I2C_RETENTION_STATE_M  (PMU_ANA_I2C_RETENTION_STATE_V << PMU_ANA_I2C_RETENTION_STATE_S)
#define PMU_ANA_I2C_RETENTION_STATE_V  0x00000001U
#define PMU_ANA_I2C_RETENTION_STATE_S  27
/** PMU_ANA_XPD_BB_I2C_STATE : RO; bitpos: [28]; default: 0;
 *  need_des
 */
#define PMU_ANA_XPD_BB_I2C_STATE    (BIT(28))
#define PMU_ANA_XPD_BB_I2C_STATE_M  (PMU_ANA_XPD_BB_I2C_STATE_V << PMU_ANA_XPD_BB_I2C_STATE_S)
#define PMU_ANA_XPD_BB_I2C_STATE_V  0x00000001U
#define PMU_ANA_XPD_BB_I2C_STATE_S  28
/** PMU_ANA_XPD_BBPLL_I2C_STATE : RO; bitpos: [29]; default: 0;
 *  need_des
 */
#define PMU_ANA_XPD_BBPLL_I2C_STATE    (BIT(29))
#define PMU_ANA_XPD_BBPLL_I2C_STATE_M  (PMU_ANA_XPD_BBPLL_I2C_STATE_V << PMU_ANA_XPD_BBPLL_I2C_STATE_S)
#define PMU_ANA_XPD_BBPLL_I2C_STATE_V  0x00000001U
#define PMU_ANA_XPD_BBPLL_I2C_STATE_S  29
/** PMU_ANA_XPD_BBPLL_STATE : RO; bitpos: [30]; default: 0;
 *  need_des
 */
#define PMU_ANA_XPD_BBPLL_STATE    (BIT(30))
#define PMU_ANA_XPD_BBPLL_STATE_M  (PMU_ANA_XPD_BBPLL_STATE_V << PMU_ANA_XPD_BBPLL_STATE_S)
#define PMU_ANA_XPD_BBPLL_STATE_V  0x00000001U
#define PMU_ANA_XPD_BBPLL_STATE_S  30
/** PMU_ANA_XPD_XTAL_STATE : RO; bitpos: [31]; default: 0;
 *  need_des
 */
#define PMU_ANA_XPD_XTAL_STATE    (BIT(31))
#define PMU_ANA_XPD_XTAL_STATE_M  (PMU_ANA_XPD_XTAL_STATE_V << PMU_ANA_XPD_XTAL_STATE_S)
#define PMU_ANA_XPD_XTAL_STATE_V  0x00000001U
#define PMU_ANA_XPD_XTAL_STATE_S  31

/** PMU_CLK_STATE1_REG register
 *  need_des
 */
#define PMU_CLK_STATE1_REG (DR_REG_PMU_BASE + 0x19c)
/** PMU_ICG_FUNC_EN_STATE : RO; bitpos: [31:0]; default: 4294967295;
 *  need_des
 */
#define PMU_ICG_FUNC_EN_STATE    0xFFFFFFFFU
#define PMU_ICG_FUNC_EN_STATE_M  (PMU_ICG_FUNC_EN_STATE_V << PMU_ICG_FUNC_EN_STATE_S)
#define PMU_ICG_FUNC_EN_STATE_V  0xFFFFFFFFU
#define PMU_ICG_FUNC_EN_STATE_S  0

/** PMU_CLK_STATE2_REG register
 *  need_des
 */
#define PMU_CLK_STATE2_REG (DR_REG_PMU_BASE + 0x1a0)
/** PMU_ICG_APB_EN_STATE : RO; bitpos: [31:0]; default: 4294967295;
 *  need_des
 */
#define PMU_ICG_APB_EN_STATE    0xFFFFFFFFU
#define PMU_ICG_APB_EN_STATE_M  (PMU_ICG_APB_EN_STATE_V << PMU_ICG_APB_EN_STATE_S)
#define PMU_ICG_APB_EN_STATE_V  0xFFFFFFFFU
#define PMU_ICG_APB_EN_STATE_S  0

/** PMU_VDD_SPI_STATUS_REG register
 *  need_des
 */
#define PMU_VDD_SPI_STATUS_REG (DR_REG_PMU_BASE + 0x1a4)
/** PMU_STABLE_VDD_SPI_PWR_DRV : RO; bitpos: [31]; default: 0;
 *  need_des
 */
#define PMU_STABLE_VDD_SPI_PWR_DRV    (BIT(31))
#define PMU_STABLE_VDD_SPI_PWR_DRV_M  (PMU_STABLE_VDD_SPI_PWR_DRV_V << PMU_STABLE_VDD_SPI_PWR_DRV_S)
#define PMU_STABLE_VDD_SPI_PWR_DRV_V  0x00000001U
#define PMU_STABLE_VDD_SPI_PWR_DRV_S  31

/** PMU_DATE_REG register
 *  need_des
 */
#define PMU_DATE_REG (DR_REG_PMU_BASE + 0x3fc)
/** PMU_PMU_DATE : R/W; bitpos: [30:0]; default: 35688960;
 *  need_des
 */
#define PMU_PMU_DATE    0x7FFFFFFFU
#define PMU_PMU_DATE_M  (PMU_PMU_DATE_V << PMU_PMU_DATE_S)
#define PMU_PMU_DATE_V  0x7FFFFFFFU
#define PMU_PMU_DATE_S  0
/** PMU_CLK_EN : R/W; bitpos: [31]; default: 0;
 *  need_des
 */
#define PMU_CLK_EN    (BIT(31))
#define PMU_CLK_EN_M  (PMU_CLK_EN_V << PMU_CLK_EN_S)
#define PMU_CLK_EN_V  0x00000001U
#define PMU_CLK_EN_S  31

#ifdef __cplusplus
}
#endif
