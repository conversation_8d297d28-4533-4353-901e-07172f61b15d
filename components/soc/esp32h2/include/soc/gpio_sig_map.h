/*
 * SPDX-FileCopyrightText: 2022-2023 Espressif Systems (Shanghai) CO LTD
 *
 * SPDX-License-Identifier: Apache-2.0
 */
#pragma once

#define EXT_ADC_START_IDX             0
#define LEDC_LS_SIG_OUT0_IDX          0
#define MODEM_DIAG0_IDX               0
#define LEDC_LS_SIG_OUT1_IDX          1
#define MODEM_DIAG1_IDX               1
#define LEDC_LS_SIG_OUT2_IDX          2
#define MODEM_DIAG2_IDX               2
#define LEDC_LS_SIG_OUT3_IDX          3
#define MODEM_DIAG3_IDX               3
#define LEDC_LS_SIG_OUT4_IDX          4
#define MODEM_DIAG4_IDX               4
#define LEDC_LS_SIG_OUT5_IDX          5
#define MODEM_DIAG5_IDX               5
#define U0RXD_IN_IDX                  6
#define U0TXD_OUT_IDX                 6
#define U0CTS_IN_IDX                  7
#define U0RTS_OUT_IDX                 7
#define U0DSR_IN_IDX                  8
#define U0DTR_OUT_IDX                 8
#define U1RXD_IN_IDX                  9
#define U1TXD_OUT_IDX                 9
#define U1CTS_IN_IDX                  10
#define U1RTS_OUT_IDX                 10
#define MODEM_DIAG6_IDX               10
#define U1DSR_IN_IDX                  11
#define U1DTR_OUT_IDX                 11
#define I2S_MCLK_IN_IDX               12
#define I2S_MCLK_OUT_IDX              12
#define I2SO_BCK_IN_IDX               13
#define I2SO_BCK_OUT_IDX              13
#define I2SO_WS_IN_IDX                14
#define I2SO_WS_OUT_IDX               14
#define I2SI_SD_IN_IDX                15
#define I2SO_SD_OUT_IDX               15
#define I2SI_BCK_IN_IDX               16
#define I2SI_BCK_OUT_IDX              16
#define I2SI_WS_IN_IDX                17
#define I2SI_WS_OUT_IDX               17
#define I2SO_SD1_OUT_IDX              18
#define USB_JTAG_TDO_BRIDGE_IDX       19
#define USB_JTAG_TRST_IDX             19
#define CPU_TESTBUS0_IDX              20
#define CPU_TESTBUS1_IDX              21
#define CPU_TESTBUS2_IDX              22
#define CPU_TESTBUS3_IDX              23
#define CPU_TESTBUS4_IDX              24
#define CPU_TESTBUS5_IDX              25
#define CPU_TESTBUS6_IDX              26
#define CPU_TESTBUS7_IDX              27
#define CPU_GPIO_IN0_IDX              28
#define CPU_GPIO_OUT0_IDX             28
#define CPU_GPIO_IN1_IDX              29
#define CPU_GPIO_OUT1_IDX             29
#define CPU_GPIO_IN2_IDX              30
#define CPU_GPIO_OUT2_IDX             30
#define CPU_GPIO_IN3_IDX              31
#define CPU_GPIO_OUT3_IDX             31
#define CPU_GPIO_IN4_IDX              32
#define CPU_GPIO_OUT4_IDX             32
#define CPU_GPIO_IN5_IDX              33
#define CPU_GPIO_OUT5_IDX             33
#define CPU_GPIO_IN6_IDX              34
#define CPU_GPIO_OUT6_IDX             34
#define CPU_GPIO_IN7_IDX              35
#define CPU_GPIO_OUT7_IDX             35
#define USB_JTAG_TCK_IDX              36
#define USB_JTAG_TMS_IDX              37
#define USB_JTAG_TDI_IDX              38
#define USB_JTAG_TDO_IDX              39
#define USB_EXTPHY_VP_IDX             40
#define USB_EXTPHY_OEN_IDX            40
#define USB_EXTPHY_VM_IDX             41
#define USB_EXTPHY_SPEED_IDX          41
#define USB_EXTPHY_RCV_IDX            42
#define USB_EXTPHY_VPO_IDX            42
#define USB_EXTPHY_VMO_IDX            43
#define USB_EXTPHY_SUSPND_IDX         44
#define I2CEXT0_SCL_IN_IDX            45
#define I2CEXT0_SCL_OUT_IDX           45
#define I2CEXT0_SDA_IN_IDX            46
#define I2CEXT0_SDA_OUT_IDX           46
#define PARL_RX_DATA0_IDX             47
#define PARL_TX_DATA0_IDX             47
#define PARL_RX_DATA1_IDX             48
#define PARL_TX_DATA1_IDX             48
#define PARL_RX_DATA2_IDX             49
#define PARL_TX_DATA2_IDX             49
#define PARL_RX_DATA3_IDX             50
#define PARL_TX_DATA3_IDX             50
#define PARL_RX_DATA4_IDX             51
#define PARL_TX_DATA4_IDX             51
#define PARL_RX_DATA5_IDX             52
#define PARL_TX_DATA5_IDX             52
#define PARL_RX_DATA6_IDX             53
#define PARL_TX_DATA6_IDX             53
#define PARL_RX_DATA7_IDX             54
#define PARL_TX_DATA7_IDX             54
#define I2CEXT1_SCL_IN_IDX            55
#define I2CEXT1_SCL_OUT_IDX           55
#define I2CEXT1_SDA_IN_IDX            56
#define I2CEXT1_SDA_OUT_IDX           56
#define CTE_ANT0_IDX                  57
#define CTE_ANT1_IDX                  58
#define CTE_ANT2_IDX                  59
#define CTE_ANT3_IDX                  60
#define CTE_ANT4_IDX                  61
#define CTE_ANT5_IDX                  62
#define FSPICLK_IN_IDX                63
#define FSPICLK_OUT_IDX               63
#define FSPIQ_IN_IDX                  64
#define FSPIQ_OUT_IDX                 64
#define FSPID_IN_IDX                  65
#define FSPID_OUT_IDX                 65
#define FSPIHD_IN_IDX                 66
#define FSPIHD_OUT_IDX                66
#define FSPIWP_IN_IDX                 67
#define FSPIWP_OUT_IDX                67
#define FSPICS0_IN_IDX                68
#define FSPICS0_OUT_IDX               68
#define MODEM_DIAG7_IDX               68
#define PARL_RX_CLK_IN_IDX            69
#define PARL_RX_CLK_OUT_IDX           69
#define PARL_TX_CLK_IN_IDX            70
#define PARL_TX_CLK_OUT_IDX           70
#define RMT_SIG_IN0_IDX               71
#define RMT_SIG_OUT0_IDX              71
#define MODEM_DIAG8_IDX               71
#define RMT_SIG_IN1_IDX               72
#define RMT_SIG_OUT1_IDX              72
#define MODEM_DIAG9_IDX               72
#define TWAI_RX_IDX                   73
#define TWAI_TX_IDX                   73
#define MODEM_DIAG10_IDX              73
#define TWAI_BUS_OFF_ON_IDX           74
#define MODEM_DIAG11_IDX              74
#define TWAI_CLKOUT_IDX               75
#define MODEM_DIAG12_IDX              75
#define TWAI_STANDBY_IDX              76
#define MODEM_DIAG13_IDX              76
#define CTE_ANT6_IDX                  77
#define CTE_ANT7_IDX                  78
#define CTE_ANT8_IDX                  79
#define CTE_ANT9_IDX                  80
#define EXTERN_PRIORITY_I_IDX         81
#define EXTERN_PRIORITY_O_IDX         81
#define EXTERN_ACTIVE_I_IDX           82
#define EXTERN_ACTIVE_O_IDX           82
#define GPIO_SD0_OUT_IDX              83
#define GPIO_SD1_OUT_IDX              84
#define GPIO_SD2_OUT_IDX              85
#define GPIO_SD3_OUT_IDX              86
#define PWM0_SYNC0_IN_IDX             87
#define PWM0_OUT0A_IDX                87
#define MODEM_DIAG14_IDX              87
#define PWM0_SYNC1_IN_IDX             88
#define PWM0_OUT0B_IDX                88
#define MODEM_DIAG15_IDX              88
#define PWM0_SYNC2_IN_IDX             89
#define PWM0_OUT1A_IDX                89
#define MODEM_DIAG16_IDX              89
#define PWM0_F0_IN_IDX                90
#define PWM0_OUT1B_IDX                90
#define MODEM_DIAG17_IDX              90
#define PWM0_F1_IN_IDX                91
#define PWM0_OUT2A_IDX                91
#define MODEM_DIAG18_IDX              91
#define PWM0_F2_IN_IDX                92
#define PWM0_OUT2B_IDX                92
#define MODEM_DIAG19_IDX              92
#define PWM0_CAP0_IN_IDX              93
#define ANT_SEL0_IDX                  93
#define PWM0_CAP1_IN_IDX              94
#define ANT_SEL1_IDX                  94
#define PWM0_CAP2_IN_IDX              95
#define ANT_SEL2_IDX                  95
#define ANT_SEL3_IDX                  96
#define SIG_IN_FUNC_97_IDX            97
#define SIG_IN_FUNC97_IDX             97
#define SIG_IN_FUNC_98_IDX            98
#define SIG_IN_FUNC98_IDX             98
#define SIG_IN_FUNC_99_IDX            99
#define SIG_IN_FUNC99_IDX             99
#define SIG_IN_FUNC_100_IDX           100
#define SIG_IN_FUNC100_IDX            100
#define PCNT_SIG_CH0_IN0_IDX          101
#define FSPICS1_OUT_IDX               101
#define MODEM_DIAG20_IDX              101
#define PCNT_SIG_CH1_IN0_IDX          102
#define FSPICS2_OUT_IDX               102
#define MODEM_DIAG21_IDX              102
#define PCNT_CTRL_CH0_IN0_IDX         103
#define FSPICS3_OUT_IDX               103
#define MODEM_DIAG22_IDX              103
#define PCNT_CTRL_CH1_IN0_IDX         104
#define FSPICS4_OUT_IDX               104
#define MODEM_DIAG23_IDX              104
#define PCNT_SIG_CH0_IN1_IDX          105
#define FSPICS5_OUT_IDX               105
#define MODEM_DIAG24_IDX              105
#define PCNT_SIG_CH1_IN1_IDX          106
#define CTE_ANT10_IDX                 106
#define PCNT_CTRL_CH0_IN1_IDX         107
#define CTE_ANT11_IDX                 107
#define PCNT_CTRL_CH1_IN1_IDX         108
#define CTE_ANT12_IDX                 108
#define PCNT_SIG_CH0_IN2_IDX          109
#define CTE_ANT13_IDX                 109
#define PCNT_SIG_CH1_IN2_IDX          110
#define CTE_ANT14_IDX                 110
#define PCNT_CTRL_CH0_IN2_IDX         111
#define CTE_ANT15_IDX                 111
#define PCNT_CTRL_CH1_IN2_IDX         112
#define MODEM_DIAG25_IDX              112
#define PCNT_SIG_CH0_IN3_IDX          113
#define MODEM_DIAG26_IDX              113
#define PCNT_SIG_CH1_IN3_IDX          114
#define SPICLK_OUT_IDX                114
#define PCNT_CTRL_CH0_IN3_IDX         115
#define SPICS0_OUT_IDX                115
#define MODEM_DIAG27_IDX              115
#define PCNT_CTRL_CH1_IN3_IDX         116
#define SPICS1_OUT_IDX                116
#define MODEM_DIAG28_IDX              116
#define GPIO_EVENT_MATRIX_IN0_IDX     117
#define GPIO_TASK_MATRIX_OUT0_IDX     117
#define GPIO_EVENT_MATRIX_IN1_IDX     118
#define GPIO_TASK_MATRIX_OUT1_IDX     118
#define GPIO_EVENT_MATRIX_IN2_IDX     119
#define GPIO_TASK_MATRIX_OUT2_IDX     119
#define GPIO_EVENT_MATRIX_IN3_IDX     120
#define GPIO_TASK_MATRIX_OUT3_IDX     120
#define SPIQ_IN_IDX                   121
#define SPIQ_OUT_IDX                  121
#define SPID_IN_IDX                   122
#define SPID_OUT_IDX                  122
#define SPIHD_IN_IDX                  123
#define SPIHD_OUT_IDX                 123
#define SPIWP_IN_IDX                  124
#define SPIWP_OUT_IDX                 124
#define CLK_OUT_OUT1_IDX              125
#define MODEM_DIAG29_IDX              125
#define CLK_OUT_OUT2_IDX              126
#define MODEM_DIAG30_IDX              126
#define CLK_OUT_OUT3_IDX              127
#define MODEM_DIAG31_IDX              127
#define SIG_GPIO_OUT_IDX              128
#define GPIO_MAP_DATE_IDX             0x2201120
