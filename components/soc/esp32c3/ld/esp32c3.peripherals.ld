/*
 * SPDX-FileCopyrightText: 2021-2022 Espressif Systems (Shanghai) CO LTD
 *
 * SPDX-License-Identifier: Apache-2.0
 */
PROVIDE ( UART0 = 0x60000000 );
PROVIDE ( UART1 = 0x60010000 );
PROVIDE ( SPIMEM1 = 0x60002000 );
PROVIDE ( SPIMEM0 = 0x60003000 );
PROVIDE ( GPIO = 0x60004000 );
PROVIDE ( SDM = 0x60004f00 );
PROVIDE ( RTCCNTL = 0x60008000 );
PROVIDE ( RTCIO = 0x60008400 );
PROVIDE ( EFUSE = 0x60008800 );
PROVIDE ( HINF = 0x6000B000 );
PROVIDE ( I2S0  = 0x6002d000 );
PROVIDE ( I2C0  = 0x60013000 );
PROVIDE ( UHCI0  = 0x60014000 );
PROVIDE ( HOST = 0x60015000 );
PROVIDE ( RMT = 0x60016000 );
PROVIDE ( RMTMEM = 0x60016400 );
PROVIDE ( SLC = 0x60018000 );
PROVIDE ( LEDC = 0x60019000 );
PROVIDE ( TIMERG0 = 0x6001F000 );
PROVIDE ( TIMERG1 = 0x60020000 );
PROVIDE ( SYSTIMER = 0x60023000 );
PROVIDE ( GPSPI2  = 0x60024000 );
PROVIDE ( SYSCON = 0x60026000 );
PROVIDE ( TWAI   = 0x6002B000 );
PROVIDE ( APB_SARADC = 0x60040000 );
PROVIDE ( USB_SERIAL_JTAG = 0x60043000 );
PROVIDE ( GDMA    = 0x6003F000 );
PROVIDE ( SYSTEM = 0x600c0000 );
