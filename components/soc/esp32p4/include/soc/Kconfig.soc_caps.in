#####################################################
# This file is auto-generated from SoC caps
# using gen_soc_caps_kconfig.py, do not edit manually
#####################################################

config SOC_ADC_SUPPORTED
    bool
    default y

config SOC_ANA_CMPR_SUPPORTED
    bool
    default y

config SOC_DEDICATED_GPIO_SUPPORTED
    bool
    default y

config SOC_UART_SUPPORTED
    bool
    default y

config SOC_GDMA_SUPPORTED
    bool
    default y

config SOC_AHB_GDMA_SUPPORTED
    bool
    default y

config SOC_AXI_GDMA_SUPPORTED
    bool
    default y

config SOC_DW_GDMA_SUPPORTED
    bool
    default y

config SOC_DMA2D_SUPPORTED
    bool
    default y

config SOC_GPTIMER_SUPPORTED
    bool
    default y

config SOC_PCNT_SUPPORTED
    bool
    default y

config SOC_LCDCAM_SUPPORTED
    bool
    default y

config SOC_LCDCAM_CAM_SUPPORTED
    bool
    default y

config SOC_LCDCAM_I80_LCD_SUPPORTED
    bool
    default y

config SOC_LCDCAM_RGB_LCD_SUPPORTED
    bool
    default y

config SOC_MIPI_CSI_SUPPORTED
    bool
    default y

config SOC_MIPI_DSI_SUPPORTED
    bool
    default y

config SOC_MCPWM_SUPPORTED
    bool
    default y

config SOC_TWAI_SUPPORTED
    bool
    default y

config SOC_ETM_SUPPORTED
    bool
    default y

config SOC_PARLIO_SUPPORTED
    bool
    default y

config SOC_ASYNC_MEMCPY_SUPPORTED
    bool
    default y

config SOC_EMAC_SUPPORTED
    bool
    default y

config SOC_USB_OTG_SUPPORTED
    bool
    default y

config SOC_WIRELESS_HOST_SUPPORTED
    bool
    default y

config SOC_USB_SERIAL_JTAG_SUPPORTED
    bool
    default y

config SOC_TEMP_SENSOR_SUPPORTED
    bool
    default y

config SOC_SUPPORTS_SECURE_DL_MODE
    bool
    default y

config SOC_ULP_SUPPORTED
    bool
    default y

config SOC_LP_CORE_SUPPORTED
    bool
    default y

config SOC_EFUSE_KEY_PURPOSE_FIELD
    bool
    default y

config SOC_EFUSE_SUPPORTED
    bool
    default y

config SOC_RTC_FAST_MEM_SUPPORTED
    bool
    default y

config SOC_RTC_MEM_SUPPORTED
    bool
    default y

config SOC_RMT_SUPPORTED
    bool
    default y

config SOC_I2S_SUPPORTED
    bool
    default y

config SOC_SDM_SUPPORTED
    bool
    default y

config SOC_GPSPI_SUPPORTED
    bool
    default y

config SOC_LEDC_SUPPORTED
    bool
    default y

config SOC_ISP_SUPPORTED
    bool
    default y

config SOC_I2C_SUPPORTED
    bool
    default y

config SOC_SYSTIMER_SUPPORTED
    bool
    default y

config SOC_AES_SUPPORTED
    bool
    default y

config SOC_MPI_SUPPORTED
    bool
    default y

config SOC_SHA_SUPPORTED
    bool
    default y

config SOC_HMAC_SUPPORTED
    bool
    default y

config SOC_DIG_SIGN_SUPPORTED
    bool
    default y

config SOC_ECC_SUPPORTED
    bool
    default y

config SOC_ECC_EXTENDED_MODES_SUPPORTED
    bool
    default y

config SOC_ECDSA_SUPPORTED
    bool
    default n

config SOC_KEY_MANAGER_SUPPORTED
    bool
    default n

config SOC_FLASH_ENC_SUPPORTED
    bool
    default y

config SOC_SECURE_BOOT_SUPPORTED
    bool
    default y

config SOC_BOD_SUPPORTED
    bool
    default y

config SOC_APM_SUPPORTED
    bool
    default y

config SOC_PMU_SUPPORTED
    bool
    default y

config SOC_DCDC_SUPPORTED
    bool
    default y

config SOC_PAU_SUPPORTED
    bool
    default y

config SOC_LP_TIMER_SUPPORTED
    bool
    default y

config SOC_ULP_LP_UART_SUPPORTED
    bool
    default y

config SOC_LP_GPIO_MATRIX_SUPPORTED
    bool
    default y

config SOC_LP_PERIPHERALS_SUPPORTED
    bool
    default y

config SOC_LP_I2C_SUPPORTED
    bool
    default y

config SOC_LP_I2S_SUPPORTED
    bool
    default y

config SOC_LP_SPI_SUPPORTED
    bool
    default y

config SOC_LP_ADC_SUPPORTED
    bool
    default y

config SOC_LP_VAD_SUPPORTED
    bool
    default y

config SOC_SPIRAM_SUPPORTED
    bool
    default y

config SOC_PSRAM_DMA_CAPABLE
    bool
    default y

config SOC_SDMMC_HOST_SUPPORTED
    bool
    default y

config SOC_CLK_TREE_SUPPORTED
    bool
    default y

config SOC_ASSIST_DEBUG_SUPPORTED
    bool
    default y

config SOC_DEBUG_PROBE_SUPPORTED
    bool
    default y

config SOC_WDT_SUPPORTED
    bool
    default y

config SOC_SPI_FLASH_SUPPORTED
    bool
    default y

config SOC_TOUCH_SENSOR_SUPPORTED
    bool
    default y

config SOC_RNG_SUPPORTED
    bool
    default y

config SOC_GP_LDO_SUPPORTED
    bool
    default y

config SOC_PPA_SUPPORTED
    bool
    default y

config SOC_LIGHT_SLEEP_SUPPORTED
    bool
    default y

config SOC_DEEP_SLEEP_SUPPORTED
    bool
    default y

config SOC_PM_SUPPORTED
    bool
    default y

config SOC_SIMD_INSTRUCTION_SUPPORTED
    bool
    default y

config SOC_XTAL_SUPPORT_40M
    bool
    default y

config SOC_AES_SUPPORT_DMA
    bool
    default y

config SOC_AES_SUPPORT_GCM
    bool
    default y

config SOC_AES_GDMA
    bool
    default y

config SOC_AES_SUPPORT_AES_128
    bool
    default y

config SOC_AES_SUPPORT_AES_256
    bool
    default y

config SOC_ADC_RTC_CTRL_SUPPORTED
    bool
    default y

config SOC_ADC_DIG_CTRL_SUPPORTED
    bool
    default y

config SOC_ADC_DMA_SUPPORTED
    bool
    default y

config SOC_ADC_PERIPH_NUM
    int
    default 2

config SOC_ADC_MAX_CHANNEL_NUM
    int
    default 8

config SOC_ADC_ATTEN_NUM
    int
    default 4

config SOC_ADC_DIGI_CONTROLLER_NUM
    int
    default 2

config SOC_ADC_PATT_LEN_MAX
    int
    default 16

config SOC_ADC_DIGI_MAX_BITWIDTH
    int
    default 12

config SOC_ADC_DIGI_MIN_BITWIDTH
    int
    default 12

config SOC_ADC_DIGI_IIR_FILTER_NUM
    int
    default 2

config SOC_ADC_DIGI_MONITOR_NUM
    int
    default 2

config SOC_ADC_DIGI_RESULT_BYTES
    int
    default 4

config SOC_ADC_DIGI_DATA_BYTES_PER_CONV
    int
    default 4

config SOC_ADC_SAMPLE_FREQ_THRES_HIGH
    int
    default 83333

config SOC_ADC_SAMPLE_FREQ_THRES_LOW
    int
    default 611

config SOC_ADC_RTC_MIN_BITWIDTH
    int
    default 12

config SOC_ADC_RTC_MAX_BITWIDTH
    int
    default 12

config SOC_ADC_CALIBRATION_V1_SUPPORTED
    bool
    default n

config SOC_ADC_SHARED_POWER
    bool
    default y

config SOC_APB_BACKUP_DMA
    bool
    default n

config SOC_BROWNOUT_RESET_SUPPORTED
    bool
    default y

config SOC_SHARED_IDCACHE_SUPPORTED
    bool
    default y

config SOC_CACHE_WRITEBACK_SUPPORTED
    bool
    default y

config SOC_CACHE_FREEZE_SUPPORTED
    bool
    default y

config SOC_CACHE_INTERNAL_MEM_VIA_L1CACHE
    bool
    default y

config SOC_CPU_CORES_NUM
    int
    default 2

config SOC_CPU_INTR_NUM
    int
    default 32

config SOC_CPU_HAS_FLEXIBLE_INTC
    bool
    default y

config SOC_INT_CLIC_SUPPORTED
    bool
    default y

config SOC_INT_HW_NESTED_SUPPORTED
    bool
    default y

config SOC_BRANCH_PREDICTOR_SUPPORTED
    bool
    default y

config SOC_CPU_COPROC_NUM
    int
    default 3

config SOC_CPU_HAS_FPU
    bool
    default y

config SOC_CPU_HAS_FPU_EXT_ILL_BUG
    bool
    default y

config SOC_CPU_HAS_HWLOOP
    bool
    default y

config SOC_CPU_HAS_PIE
    bool
    default y

config SOC_HP_CPU_HAS_MULTIPLE_CORES
    bool
    default y

config SOC_CPU_BREAKPOINTS_NUM
    int
    default 3

config SOC_CPU_WATCHPOINTS_NUM
    int
    default 3

config SOC_CPU_WATCHPOINT_MAX_REGION_SIZE
    hex
    default 0x100

config SOC_CPU_HAS_PMA
    bool
    default y

config SOC_CPU_IDRAM_SPLIT_USING_PMP
    bool
    default y

config SOC_CPU_PMP_REGION_GRANULARITY
    int
    default 128

config SOC_CPU_HAS_LOCKUP_RESET
    bool
    default y

config SOC_SIMD_PREFERRED_DATA_ALIGNMENT
    int
    default 16

config SOC_DS_SIGNATURE_MAX_BIT_LEN
    int
    default 4096

config SOC_DS_KEY_PARAM_MD_IV_LENGTH
    int
    default 16

config SOC_DS_KEY_CHECK_MAX_WAIT_US
    int
    default 1100

config SOC_DMA_CAN_ACCESS_FLASH
    bool
    default y

config SOC_AHB_GDMA_VERSION
    int
    default 2

config SOC_GDMA_SUPPORT_CRC
    bool
    default y

config SOC_GDMA_NUM_GROUPS_MAX
    int
    default 2

config SOC_GDMA_PAIRS_PER_GROUP_MAX
    int
    default 3

config SOC_AXI_GDMA_SUPPORT_PSRAM
    bool
    default y

config SOC_GDMA_SUPPORT_ETM
    bool
    default y

config SOC_GDMA_SUPPORT_SLEEP_RETENTION
    bool
    default y

config SOC_AXI_DMA_EXT_MEM_ENC_ALIGNMENT
    int
    default 16

config SOC_DMA2D_GROUPS
    int
    default 1

config SOC_DMA2D_TX_CHANNELS_PER_GROUP
    int
    default 3

config SOC_DMA2D_RX_CHANNELS_PER_GROUP
    int
    default 2

config SOC_ETM_GROUPS
    int
    default 1

config SOC_ETM_CHANNELS_PER_GROUP
    int
    default 50

config SOC_ETM_SUPPORT_SLEEP_RETENTION
    bool
    default y

config SOC_GPIO_PORT
    int
    default 1

config SOC_GPIO_PIN_COUNT
    int
    default 55

config SOC_GPIO_SUPPORT_PIN_GLITCH_FILTER
    bool
    default y

config SOC_GPIO_FLEX_GLITCH_FILTER_NUM
    int
    default 8

config SOC_GPIO_SUPPORT_PIN_HYS_FILTER
    bool
    default y

config SOC_GPIO_SUPPORT_ETM
    bool
    default y

config SOC_GPIO_SUPPORT_RTC_INDEPENDENT
    bool
    default y

config SOC_GPIO_SUPPORT_DEEPSLEEP_WAKEUP
    bool
    default y

config SOC_LP_IO_HAS_INDEPENDENT_WAKEUP_SOURCE
    bool
    default y

config SOC_LP_IO_CLOCK_IS_INDEPENDENT
    bool
    default y

config SOC_GPIO_VALID_GPIO_MASK
    hex
    default 0x007FFFFFFFFFFFFF

config SOC_GPIO_IN_RANGE_MAX
    int
    default 54

config SOC_GPIO_OUT_RANGE_MAX
    int
    default 54

config SOC_GPIO_DEEP_SLEEP_WAKE_VALID_GPIO_MASK
    int
    default 0

config SOC_GPIO_DEEP_SLEEP_WAKE_SUPPORTED_PIN_CNT
    int
    default 16

config SOC_GPIO_VALID_DIGITAL_IO_PAD_MASK
    hex
    default 0x007FFFFFFFFF0000

config SOC_GPIO_CLOCKOUT_BY_GPIO_MATRIX
    bool
    default y

config SOC_GPIO_CLOCKOUT_CHANNEL_NUM
    int
    default 2

config SOC_CLOCKOUT_SUPPORT_CHANNEL_DIVIDER
    bool
    default y

config SOC_DEBUG_PROBE_NUM_UNIT
    int
    default 1

config SOC_DEBUG_PROBE_MAX_OUTPUT_WIDTH
    int
    default 16

config SOC_GPIO_SUPPORT_FORCE_HOLD
    bool
    default y

config SOC_RTCIO_PIN_COUNT
    int
    default 16

config SOC_RTCIO_INPUT_OUTPUT_SUPPORTED
    bool
    default y

config SOC_RTCIO_HOLD_SUPPORTED
    bool
    default y

config SOC_RTCIO_WAKE_SUPPORTED
    bool
    default y

config SOC_DEDIC_GPIO_OUT_CHANNELS_NUM
    int
    default 8

config SOC_DEDIC_GPIO_IN_CHANNELS_NUM
    int
    default 8

config SOC_DEDIC_PERIPH_ALWAYS_ENABLE
    bool
    default y

config SOC_ANA_CMPR_NUM
    int
    default 2

config SOC_ANA_CMPR_CAN_DISTINGUISH_EDGE
    bool
    default y

config SOC_ANA_CMPR_SUPPORT_ETM
    bool
    default y

config SOC_I2C_NUM
    int
    default 3

config SOC_HP_I2C_NUM
    int
    default 2

config SOC_I2C_FIFO_LEN
    int
    default 32

config SOC_I2C_CMD_REG_NUM
    int
    default 8

config SOC_I2C_SUPPORT_SLAVE
    bool
    default y

config SOC_I2C_SUPPORT_HW_FSM_RST
    bool
    default y

config SOC_I2C_SUPPORT_HW_CLR_BUS
    bool
    default y

config SOC_I2C_SUPPORT_XTAL
    bool
    default y

config SOC_I2C_SUPPORT_RTC
    bool
    default y

config SOC_I2C_SUPPORT_10BIT_ADDR
    bool
    default y

config SOC_I2C_SLAVE_SUPPORT_BROADCAST
    bool
    default y

config SOC_I2C_SLAVE_CAN_GET_STRETCH_CAUSE
    bool
    default y

config SOC_I2C_SLAVE_SUPPORT_I2CRAM_ACCESS
    bool
    default y

config SOC_I2C_SLAVE_SUPPORT_SLAVE_UNMATCH
    bool
    default y

config SOC_I2C_SUPPORT_SLEEP_RETENTION
    bool
    default y

config SOC_LP_I2C_NUM
    int
    default 1

config SOC_LP_I2C_FIFO_LEN
    int
    default 16

config SOC_I2S_NUM
    int
    default 3

config SOC_I2S_HW_VERSION_2
    bool
    default y

config SOC_I2S_SUPPORTS_ETM
    bool
    default y

config SOC_I2S_SUPPORTS_XTAL
    bool
    default y

config SOC_I2S_SUPPORTS_APLL
    bool
    default y

config SOC_I2S_SUPPORTS_PCM
    bool
    default y

config SOC_I2S_SUPPORTS_PDM
    bool
    default y

config SOC_I2S_SUPPORTS_PDM_TX
    bool
    default y

config SOC_I2S_SUPPORTS_PDM_RX
    bool
    default y

config SOC_I2S_SUPPORTS_PDM_RX_HP_FILTER
    bool
    default y

config SOC_I2S_SUPPORTS_TX_SYNC_CNT
    bool
    default y

config SOC_I2S_SUPPORTS_TDM
    bool
    default y

config SOC_I2S_PDM_MAX_TX_LINES
    int
    default 2

config SOC_I2S_PDM_MAX_RX_LINES
    int
    default 4

config SOC_I2S_TDM_FULL_DATA_WIDTH
    bool
    default y

config SOC_I2S_SUPPORT_SLEEP_RETENTION
    bool
    default y

config SOC_LP_I2S_NUM
    int
    default 1

config SOC_ISP_BF_SUPPORTED
    bool
    default y

config SOC_ISP_CCM_SUPPORTED
    bool
    default y

config SOC_ISP_DEMOSAIC_SUPPORTED
    bool
    default y

config SOC_ISP_DVP_SUPPORTED
    bool
    default y

config SOC_ISP_SHARPEN_SUPPORTED
    bool
    default y

config SOC_ISP_COLOR_SUPPORTED
    bool
    default y

config SOC_ISP_LSC_SUPPORTED
    bool
    default y

config SOC_ISP_SHARE_CSI_BRG
    bool
    default y

config SOC_ISP_NUMS
    int
    default 1

config SOC_ISP_DVP_CTLR_NUMS
    int
    default 1

config SOC_ISP_AE_CTLR_NUMS
    int
    default 1

config SOC_ISP_AE_BLOCK_X_NUMS
    int
    default 5

config SOC_ISP_AE_BLOCK_Y_NUMS
    int
    default 5

config SOC_ISP_AF_CTLR_NUMS
    int
    default 1

config SOC_ISP_AF_WINDOW_NUMS
    int
    default 3

config SOC_ISP_BF_TEMPLATE_X_NUMS
    int
    default 3

config SOC_ISP_BF_TEMPLATE_Y_NUMS
    int
    default 3

config SOC_ISP_CCM_DIMENSION
    int
    default 3

config SOC_ISP_DEMOSAIC_GRAD_RATIO_INT_BITS
    int
    default 2

config SOC_ISP_DEMOSAIC_GRAD_RATIO_DEC_BITS
    int
    default 4

config SOC_ISP_DEMOSAIC_GRAD_RATIO_RES_BITS
    int
    default 26

config SOC_ISP_DVP_DATA_WIDTH_MAX
    int
    default 16

config SOC_ISP_SHARPEN_TEMPLATE_X_NUMS
    int
    default 3

config SOC_ISP_SHARPEN_TEMPLATE_Y_NUMS
    int
    default 3

config SOC_ISP_SHARPEN_H_FREQ_COEF_INT_BITS
    int
    default 3

config SOC_ISP_SHARPEN_H_FREQ_COEF_DEC_BITS
    int
    default 5

config SOC_ISP_SHARPEN_H_FREQ_COEF_RES_BITS
    int
    default 24

config SOC_ISP_SHARPEN_M_FREQ_COEF_INT_BITS
    int
    default 3

config SOC_ISP_SHARPEN_M_FREQ_COEF_DEC_BITS
    int
    default 5

config SOC_ISP_SHARPEN_M_FREQ_COEF_RES_BITS
    int
    default 24

config SOC_ISP_HIST_CTLR_NUMS
    int
    default 1

config SOC_ISP_HIST_BLOCK_X_NUMS
    int
    default 5

config SOC_ISP_HIST_BLOCK_Y_NUMS
    int
    default 5

config SOC_ISP_HIST_SEGMENT_NUMS
    int
    default 16

config SOC_ISP_HIST_INTERVAL_NUMS
    int
    default 15

config SOC_ISP_LSC_GRAD_RATIO_INT_BITS
    int
    default 2

config SOC_ISP_LSC_GRAD_RATIO_DEC_BITS
    int
    default 8

config SOC_ISP_LSC_GRAD_RATIO_RES_BITS
    int
    default 22

config SOC_LEDC_SUPPORT_PLL_DIV_CLOCK
    bool
    default y

config SOC_LEDC_SUPPORT_XTAL_CLOCK
    bool
    default y

config SOC_LEDC_TIMER_NUM
    int
    default 4

config SOC_LEDC_CHANNEL_NUM
    int
    default 8

config SOC_LEDC_TIMER_BIT_WIDTH
    int
    default 20

config SOC_LEDC_GAMMA_CURVE_FADE_SUPPORTED
    bool
    default y

config SOC_LEDC_GAMMA_CURVE_FADE_RANGE_MAX
    int
    default 16

config SOC_LEDC_SUPPORT_FADE_STOP
    bool
    default y

config SOC_LEDC_FADE_PARAMS_BIT_WIDTH
    int
    default 10

config SOC_LEDC_SUPPORT_SLEEP_RETENTION
    bool
    default y

config SOC_MMU_PERIPH_NUM
    int
    default 2

config SOC_MMU_LINEAR_ADDRESS_REGION_NUM
    int
    default 2

config SOC_MMU_DI_VADDR_SHARED
    bool
    default y

config SOC_MMU_PER_EXT_MEM_TARGET
    bool
    default y

config SOC_MPU_CONFIGURABLE_REGIONS_SUPPORTED
    bool
    default n

config SOC_MPU_MIN_REGION_SIZE
    hex
    default 0x20000000

config SOC_MPU_REGIONS_MAX_NUM
    int
    default 8

config SOC_MPU_REGION_RO_SUPPORTED
    bool
    default n

config SOC_MPU_REGION_WO_SUPPORTED
    bool
    default n

config SOC_PCNT_GROUPS
    int
    default 1

config SOC_PCNT_UNITS_PER_GROUP
    int
    default 4

config SOC_PCNT_CHANNELS_PER_UNIT
    int
    default 2

config SOC_PCNT_THRES_POINT_PER_UNIT
    int
    default 2

config SOC_PCNT_SUPPORT_RUNTIME_THRES_UPDATE
    bool
    default y

config SOC_PCNT_SUPPORT_CLEAR_SIGNAL
    bool
    default y

config SOC_PCNT_SUPPORT_SLEEP_RETENTION
    bool
    default y

config SOC_RMT_GROUPS
    int
    default 1

config SOC_RMT_TX_CANDIDATES_PER_GROUP
    int
    default 4

config SOC_RMT_RX_CANDIDATES_PER_GROUP
    int
    default 4

config SOC_RMT_CHANNELS_PER_GROUP
    int
    default 8

config SOC_RMT_MEM_WORDS_PER_CHANNEL
    int
    default 48

config SOC_RMT_SUPPORT_RX_PINGPONG
    bool
    default y

config SOC_RMT_SUPPORT_RX_DEMODULATION
    bool
    default y

config SOC_RMT_SUPPORT_TX_ASYNC_STOP
    bool
    default y

config SOC_RMT_SUPPORT_TX_LOOP_COUNT
    bool
    default y

config SOC_RMT_SUPPORT_TX_LOOP_AUTO_STOP
    bool
    default y

config SOC_RMT_SUPPORT_TX_SYNCHRO
    bool
    default y

config SOC_RMT_SUPPORT_TX_CARRIER_DATA_ONLY
    bool
    default y

config SOC_RMT_SUPPORT_XTAL
    bool
    default y

config SOC_RMT_SUPPORT_RC_FAST
    bool
    default y

config SOC_RMT_SUPPORT_DMA
    bool
    default y

config SOC_RMT_SUPPORT_SLEEP_RETENTION
    bool
    default y

config SOC_LCD_I80_SUPPORTED
    bool
    default y

config SOC_LCD_RGB_SUPPORTED
    bool
    default y

config SOC_LCDCAM_I80_NUM_BUSES
    int
    default 1

config SOC_LCDCAM_I80_BUS_WIDTH
    int
    default 24

config SOC_LCDCAM_RGB_NUM_PANELS
    int
    default 1

config SOC_LCDCAM_RGB_DATA_WIDTH
    int
    default 24

config SOC_LCD_SUPPORT_RGB_YUV_CONV
    bool
    default y

config SOC_MCPWM_GROUPS
    int
    default 2

config SOC_MCPWM_TIMERS_PER_GROUP
    int
    default 3

config SOC_MCPWM_OPERATORS_PER_GROUP
    int
    default 3

config SOC_MCPWM_COMPARATORS_PER_OPERATOR
    int
    default 2

config SOC_MCPWM_EVENT_COMPARATORS_PER_OPERATOR
    int
    default 2

config SOC_MCPWM_GENERATORS_PER_OPERATOR
    int
    default 2

config SOC_MCPWM_TRIGGERS_PER_OPERATOR
    int
    default 2

config SOC_MCPWM_GPIO_FAULTS_PER_GROUP
    int
    default 3

config SOC_MCPWM_CAPTURE_TIMERS_PER_GROUP
    bool
    default y

config SOC_MCPWM_CAPTURE_CHANNELS_PER_TIMER
    int
    default 3

config SOC_MCPWM_GPIO_SYNCHROS_PER_GROUP
    int
    default 3

config SOC_MCPWM_SWSYNC_CAN_PROPAGATE
    bool
    default y

config SOC_MCPWM_SUPPORT_ETM
    bool
    default y

config SOC_MCPWM_SUPPORT_EVENT_COMPARATOR
    bool
    default y

config SOC_MCPWM_CAPTURE_CLK_FROM_GROUP
    bool
    default y

config SOC_MCPWM_SUPPORT_SLEEP_RETENTION
    bool
    default y

config SOC_USB_OTG_PERIPH_NUM
    int
    default 2

config SOC_USB_UTMI_PHY_NUM
    int
    default 1

config SOC_PARLIO_GROUPS
    int
    default 1

config SOC_PARLIO_TX_UNITS_PER_GROUP
    int
    default 1

config SOC_PARLIO_RX_UNITS_PER_GROUP
    int
    default 1

config SOC_PARLIO_TX_UNIT_MAX_DATA_WIDTH
    int
    default 16

config SOC_PARLIO_RX_UNIT_MAX_DATA_WIDTH
    int
    default 16

config SOC_PARLIO_TX_CLK_SUPPORT_GATING
    bool
    default y

config SOC_PARLIO_RX_CLK_SUPPORT_GATING
    bool
    default y

config SOC_PARLIO_RX_CLK_SUPPORT_OUTPUT
    bool
    default y

config SOC_PARLIO_TRANS_BIT_ALIGN
    bool
    default y

config SOC_PARLIO_TX_SIZE_BY_DMA
    bool
    default y

config SOC_PARLIO_SUPPORT_SLEEP_RETENTION
    bool
    default y

config SOC_MPI_MEM_BLOCKS_NUM
    int
    default 4

config SOC_MPI_OPERATIONS_NUM
    int
    default 3

config SOC_RSA_MAX_BIT_LEN
    int
    default 4096

config SOC_SDMMC_USE_IOMUX
    bool
    default y

config SOC_SDMMC_USE_GPIO_MATRIX
    bool
    default y

config SOC_SDMMC_NUM_SLOTS
    int
    default 2

config SOC_SDMMC_DELAY_PHASE_NUM
    int
    default 4

config SOC_SDMMC_IO_POWER_EXTERNAL
    bool
    default y

config SOC_SDMMC_PSRAM_DMA_CAPABLE
    bool
    default y

config SOC_SDMMC_UHS_I_SUPPORTED
    bool
    default y

config SOC_SHA_DMA_MAX_BUFFER_SIZE
    int
    default 3968

config SOC_SHA_SUPPORT_DMA
    bool
    default y

config SOC_SHA_SUPPORT_RESUME
    bool
    default y

config SOC_SHA_GDMA
    bool
    default y

config SOC_SHA_SUPPORT_SHA1
    bool
    default y

config SOC_SHA_SUPPORT_SHA224
    bool
    default y

config SOC_SHA_SUPPORT_SHA256
    bool
    default y

config SOC_SHA_SUPPORT_SHA384
    bool
    default y

config SOC_SHA_SUPPORT_SHA512
    bool
    default y

config SOC_SHA_SUPPORT_SHA512_224
    bool
    default y

config SOC_SHA_SUPPORT_SHA512_256
    bool
    default y

config SOC_SHA_SUPPORT_SHA512_T
    bool
    default y

config SOC_ECDSA_SUPPORT_EXPORT_PUBKEY
    bool
    default y

config SOC_ECDSA_SUPPORT_DETERMINISTIC_MODE
    bool
    default y

config SOC_ECDSA_USES_MPI
    bool
    default y

config SOC_SDM_GROUPS
    int
    default 1

config SOC_SDM_CHANNELS_PER_GROUP
    int
    default 8

config SOC_SDM_CLK_SUPPORT_PLL_F80M
    bool
    default y

config SOC_SDM_CLK_SUPPORT_XTAL
    bool
    default y

config SOC_SPI_PERIPH_NUM
    int
    default 3

config SOC_SPI_MAX_CS_NUM
    int
    default 6

config SOC_SPI_MAXIMUM_BUFFER_SIZE
    int
    default 64

config SOC_SPI_SUPPORT_SLEEP_RETENTION
    bool
    default y

config SOC_SPI_SUPPORT_SLAVE_HD_VER2
    bool
    default y

config SOC_SPI_SLAVE_SUPPORT_SEG_TRANS
    bool
    default y

config SOC_SPI_SUPPORT_DDRCLK
    bool
    default y

config SOC_SPI_SUPPORT_CD_SIG
    bool
    default y

config SOC_SPI_SUPPORT_OCT
    bool
    default y

config SOC_SPI_SUPPORT_CLK_XTAL
    bool
    default y

config SOC_SPI_SUPPORT_CLK_RC_FAST
    bool
    default y

config SOC_SPI_SUPPORT_CLK_SPLL
    bool
    default y

config SOC_MEMSPI_IS_INDEPENDENT
    bool
    default y

config SOC_SPI_MAX_PRE_DIVIDER
    int
    default 16

config SOC_LP_SPI_PERIPH_NUM
    bool
    default y

config SOC_LP_SPI_MAXIMUM_BUFFER_SIZE
    int
    default 64

config SOC_SPIRAM_XIP_SUPPORTED
    bool
    default y

config SOC_SPI_MEM_SUPPORT_AUTO_WAIT_IDLE
    bool
    default y

config SOC_SPI_MEM_SUPPORT_AUTO_SUSPEND
    bool
    default y

config SOC_SPI_MEM_SUPPORT_AUTO_RESUME
    bool
    default y

config SOC_SPI_MEM_SUPPORT_IDLE_INTR
    bool
    default y

config SOC_SPI_MEM_SUPPORT_SW_SUSPEND
    bool
    default y

config SOC_SPI_MEM_SUPPORT_CHECK_SUS
    bool
    default y

config SOC_SPI_MEM_SUPPORT_TIMING_TUNING
    bool
    default y

config SOC_MEMSPI_TIMING_TUNING_BY_DQS
    bool
    default y

config SOC_SPI_MEM_SUPPORT_CACHE_32BIT_ADDR_MAP
    bool
    default y

config SOC_SPI_PERIPH_SUPPORT_CONTROL_DUMMY_OUT
    bool
    default y

config SOC_MEMSPI_SRC_FREQ_80M_SUPPORTED
    bool
    default y

config SOC_MEMSPI_SRC_FREQ_40M_SUPPORTED
    bool
    default y

config SOC_MEMSPI_SRC_FREQ_20M_SUPPORTED
    bool
    default y

config SOC_MEMSPI_FLASH_PSRAM_INDEPENDENT
    bool
    default y

config SOC_SYSTIMER_COUNTER_NUM
    int
    default 2

config SOC_SYSTIMER_ALARM_NUM
    int
    default 3

config SOC_SYSTIMER_BIT_WIDTH_LO
    int
    default 32

config SOC_SYSTIMER_BIT_WIDTH_HI
    int
    default 20

config SOC_SYSTIMER_FIXED_DIVIDER
    bool
    default y

config SOC_SYSTIMER_SUPPORT_RC_FAST
    bool
    default y

config SOC_SYSTIMER_INT_LEVEL
    bool
    default y

config SOC_SYSTIMER_ALARM_MISS_COMPENSATE
    bool
    default y

config SOC_SYSTIMER_SUPPORT_ETM
    bool
    default y

config SOC_LP_TIMER_BIT_WIDTH_LO
    int
    default 32

config SOC_LP_TIMER_BIT_WIDTH_HI
    int
    default 16

config SOC_TIMER_GROUPS
    int
    default 2

config SOC_TIMER_GROUP_TIMERS_PER_GROUP
    int
    default 2

config SOC_TIMER_GROUP_COUNTER_BIT_WIDTH
    int
    default 54

config SOC_TIMER_GROUP_SUPPORT_XTAL
    bool
    default y

config SOC_TIMER_GROUP_SUPPORT_RC_FAST
    bool
    default y

config SOC_TIMER_GROUP_TOTAL_TIMERS
    int
    default 4

config SOC_TIMER_SUPPORT_ETM
    bool
    default y

config SOC_TIMER_SUPPORT_SLEEP_RETENTION
    bool
    default y

config SOC_MWDT_SUPPORT_XTAL
    bool
    default y

config SOC_MWDT_SUPPORT_SLEEP_RETENTION
    bool
    default y

config SOC_TOUCH_SENSOR_VERSION
    int
    default 3

config SOC_TOUCH_SENSOR_NUM
    int
    default 14

config SOC_TOUCH_SUPPORT_SLEEP_WAKEUP
    bool
    default y

config SOC_TOUCH_SUPPORT_WATERPROOF
    bool
    default y

config SOC_TOUCH_SUPPORT_PROX_SENSING
    bool
    default y

config SOC_TOUCH_PROXIMITY_CHANNEL_NUM
    int
    default 3

config SOC_TOUCH_PROXIMITY_MEAS_DONE_SUPPORTED
    bool
    default y

config SOC_TOUCH_SUPPORT_FREQ_HOP
    bool
    default y

config SOC_TOUCH_SAMPLE_CFG_NUM
    int
    default 3

config SOC_TWAI_CONTROLLER_NUM
    int
    default 3

config SOC_TWAI_CLK_SUPPORT_XTAL
    bool
    default y

config SOC_TWAI_BRP_MIN
    int
    default 2

config SOC_TWAI_BRP_MAX
    int
    default 32768

config SOC_TWAI_SUPPORTS_RX_STATUS
    bool
    default y

config SOC_TWAI_SUPPORT_SLEEP_RETENTION
    bool
    default y

config SOC_EFUSE_DIS_PAD_JTAG
    bool
    default y

config SOC_EFUSE_DIS_USB_JTAG
    bool
    default y

config SOC_EFUSE_DIS_DIRECT_BOOT
    bool
    default y

config SOC_EFUSE_SOFT_DIS_JTAG
    bool
    default y

config SOC_EFUSE_DIS_DOWNLOAD_MSPI
    bool
    default y

config SOC_EFUSE_ECDSA_KEY
    bool
    default y

config SOC_KEY_MANAGER_ECDSA_KEY_DEPLOY
    bool
    default y

config SOC_KEY_MANAGER_FE_KEY_DEPLOY
    bool
    default y

config SOC_SECURE_BOOT_V2_RSA
    bool
    default y

config SOC_SECURE_BOOT_V2_ECC
    bool
    default y

config SOC_EFUSE_SECURE_BOOT_KEY_DIGESTS
    int
    default 3

config SOC_EFUSE_REVOKE_BOOT_KEY_DIGESTS
    bool
    default y

config SOC_SUPPORT_SECURE_BOOT_REVOKE_KEY
    bool
    default y

config SOC_FLASH_ENCRYPTED_XTS_AES_BLOCK_MAX
    int
    default 64

config SOC_FLASH_ENCRYPTION_XTS_AES
    bool
    default y

config SOC_FLASH_ENCRYPTION_XTS_AES_OPTIONS
    bool
    default y

config SOC_FLASH_ENCRYPTION_XTS_AES_128
    bool
    default y

config SOC_FLASH_ENCRYPTION_XTS_AES_256
    bool
    default y

config SOC_UART_NUM
    int
    default 6

config SOC_UART_HP_NUM
    int
    default 5

config SOC_UART_LP_NUM
    int
    default 1

config SOC_UART_FIFO_LEN
    int
    default 128

config SOC_LP_UART_FIFO_LEN
    int
    default 16

config SOC_UART_BITRATE_MAX
    int
    default 5000000

config SOC_UART_SUPPORT_PLL_F80M_CLK
    bool
    default y

config SOC_UART_SUPPORT_RTC_CLK
    bool
    default y

config SOC_UART_SUPPORT_XTAL_CLK
    bool
    default y

config SOC_UART_SUPPORT_WAKEUP_INT
    bool
    default y

config SOC_UART_HAS_LP_UART
    bool
    default y

config SOC_UART_SUPPORT_SLEEP_RETENTION
    bool
    default y

config SOC_UART_SUPPORT_FSM_TX_WAIT_SEND
    bool
    default y

config SOC_LP_I2S_SUPPORT_VAD
    bool
    default y

config SOC_COEX_HW_PTI
    bool
    default y

config SOC_PHY_DIG_REGS_MEM_SIZE
    int
    default 21

config SOC_WIFI_LIGHT_SLEEP_CLK_WIDTH
    int
    default 12

config SOC_PM_SUPPORT_EXT1_WAKEUP
    bool
    default y

config SOC_PM_SUPPORT_EXT1_WAKEUP_MODE_PER_PIN
    bool
    default y

config SOC_PM_EXT1_WAKEUP_BY_PMU
    bool
    default y

config SOC_PM_SUPPORT_WIFI_WAKEUP
    bool
    default y

config SOC_PM_SUPPORT_TOUCH_SENSOR_WAKEUP
    bool
    default y

config SOC_PM_SUPPORT_XTAL32K_PD
    bool
    default y

config SOC_PM_SUPPORT_RC32K_PD
    bool
    default y

config SOC_PM_SUPPORT_RC_FAST_PD
    bool
    default y

config SOC_PM_SUPPORT_VDDSDIO_PD
    bool
    default y

config SOC_PM_SUPPORT_TOP_PD
    bool
    default y

config SOC_PM_SUPPORT_CNNT_PD
    bool
    default y

config SOC_PM_SUPPORT_RTC_PERIPH_PD
    bool
    default y

config SOC_PM_SUPPORT_DEEPSLEEP_CHECK_STUB_ONLY
    bool
    default y

config SOC_PM_CPU_RETENTION_BY_SW
    bool
    default y

config SOC_PM_CACHE_RETENTION_BY_PAU
    bool
    default y

config SOC_PM_PAU_LINK_NUM
    int
    default 4

config SOC_PM_PAU_REGDMA_LINK_MULTI_ADDR
    bool
    default y

config SOC_PAU_IN_TOP_DOMAIN
    bool
    default y

config SOC_CPU_IN_TOP_DOMAIN
    bool
    default y

config SOC_PM_PAU_REGDMA_UPDATE_CACHE_BEFORE_WAIT_COMPARE
    bool
    default y

config SOC_SLEEP_SYSTIMER_STALL_WORKAROUND
    bool
    default y

config SOC_SLEEP_TGWDT_STOP_WORKAROUND
    bool
    default y

config SOC_PM_RETENTION_MODULE_NUM
    int
    default 64

config SOC_PSRAM_VDD_POWER_MPLL
    bool
    default y

config SOC_CLK_RC_FAST_SUPPORT_CALIBRATION
    bool
    default y

config SOC_MODEM_CLOCK_IS_INDEPENDENT
    bool
    default n

config SOC_CLK_APLL_SUPPORTED
    bool
    default y

config SOC_CLK_MPLL_SUPPORTED
    bool
    default y

config SOC_CLK_SDIO_PLL_SUPPORTED
    bool
    default y

config SOC_CLK_XTAL32K_SUPPORTED
    bool
    default y

config SOC_CLK_RC32K_SUPPORTED
    bool
    default y

config SOC_CLK_LP_FAST_SUPPORT_LP_PLL
    bool
    default y

config SOC_CLK_LP_FAST_SUPPORT_XTAL
    bool
    default y

config SOC_PERIPH_CLK_CTRL_SHARED
    bool
    default y

config SOC_TEMPERATURE_SENSOR_LP_PLL_SUPPORT
    bool
    default y

config SOC_TEMPERATURE_SENSOR_INTR_SUPPORT
    bool
    default y

config SOC_TSENS_IS_INDEPENDENT_FROM_ADC
    bool
    default y

config SOC_TEMPERATURE_SENSOR_SUPPORT_ETM
    bool
    default y

config SOC_TEMPERATURE_SENSOR_SUPPORT_SLEEP_RETENTION
    bool
    default y

config SOC_MEM_TCM_SUPPORTED
    bool
    default y

config SOC_MEM_NON_CONTIGUOUS_SRAM
    bool
    default y

config SOC_ASYNCHRONOUS_BUS_ERROR_MODE
    bool
    default y

config SOC_EMAC_IEEE_1588_SUPPORT
    bool
    default y

config SOC_EMAC_USE_MULTI_IO_MUX
    bool
    default y

config SOC_EMAC_MII_USE_GPIO_MATRIX
    bool
    default y

config SOC_JPEG_CODEC_SUPPORTED
    bool
    default y

config SOC_JPEG_DECODE_SUPPORTED
    bool
    default y

config SOC_JPEG_ENCODE_SUPPORTED
    bool
    default y

config SOC_LCDCAM_CAM_SUPPORT_RGB_YUV_CONV
    bool
    default y

config SOC_LCDCAM_CAM_PERIPH_NUM
    int
    default 1

config SOC_LCDCAM_CAM_DATA_WIDTH_MAX
    int
    default 16

config SOC_LP_CORE_SUPPORT_ETM
    bool
    default y

config SOC_LP_CORE_SUPPORT_LP_ADC
    bool
    default y

config SOC_LP_CORE_SUPPORT_LP_VAD
    bool
    default y
