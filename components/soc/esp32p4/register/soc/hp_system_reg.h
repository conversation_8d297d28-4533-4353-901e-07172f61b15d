/**
 * SPDX-FileCopyrightText: 2023 Espressif Systems (Shanghai) CO LTD
 *
 *  SPDX-License-Identifier: Apache-2.0
 */
#pragma once

#include "soc/soc.h"
#ifdef __cplusplus
extern "C" {
#endif

/** HP_SYSTEM_VER_DATE_REG register
 *  NA
 */
#define HP_SYSTEM_VER_DATE_REG (DR_REG_HP_SYS_BASE + 0x0)
/** HP_SYSTEM_REG_VER_DATE : R/W; bitpos: [31:0]; default: 539165977;
 *  NA
 */
#define HP_SYSTEM_REG_VER_DATE    0xFFFFFFFFU
#define HP_SYSTEM_REG_VER_DATE_M  (HP_SYSTEM_REG_VER_DATE_V << HP_SYSTEM_REG_VER_DATE_S)
#define HP_SYSTEM_REG_VER_DATE_V  0xFFFFFFFFU
#define HP_SYSTEM_REG_VER_DATE_S  0

/** HP_SYSTEM_CLK_EN_REG register
 *  NA
 */
#define HP_SYSTEM_CLK_EN_REG (DR_REG_HP_SYS_BASE + 0x4)
/** HP_SYSTEM_REG_CLK_EN : R/W; bitpos: [0]; default: 0;
 *  NA
 */
#define HP_SYSTEM_REG_CLK_EN    (BIT(0))
#define HP_SYSTEM_REG_CLK_EN_M  (HP_SYSTEM_REG_CLK_EN_V << HP_SYSTEM_REG_CLK_EN_S)
#define HP_SYSTEM_REG_CLK_EN_V  0x00000001U
#define HP_SYSTEM_REG_CLK_EN_S  0

/** HP_SYSTEM_CPU_INT_FROM_CPU_0_REG register
 *  NA
 */
#define HP_SYSTEM_CPU_INT_FROM_CPU_0_REG (DR_REG_HP_SYS_BASE + 0x10)
/** HP_SYSTEM_CPU_INT_FROM_CPU_0 : R/W; bitpos: [0]; default: 0;
 *  set 1 will trigger a interrupt
 */
#define HP_SYSTEM_CPU_INT_FROM_CPU_0    (BIT(0))
#define HP_SYSTEM_CPU_INT_FROM_CPU_0_M  (HP_SYSTEM_CPU_INT_FROM_CPU_0_V << HP_SYSTEM_CPU_INT_FROM_CPU_0_S)
#define HP_SYSTEM_CPU_INT_FROM_CPU_0_V  0x00000001U
#define HP_SYSTEM_CPU_INT_FROM_CPU_0_S  0

/** HP_SYSTEM_CPU_INT_FROM_CPU_1_REG register
 *  NA
 */
#define HP_SYSTEM_CPU_INT_FROM_CPU_1_REG (DR_REG_HP_SYS_BASE + 0x14)
/** HP_SYSTEM_CPU_INT_FROM_CPU_1 : R/W; bitpos: [0]; default: 0;
 *  set 1 will trigger a interrupt
 */
#define HP_SYSTEM_CPU_INT_FROM_CPU_1    (BIT(0))
#define HP_SYSTEM_CPU_INT_FROM_CPU_1_M  (HP_SYSTEM_CPU_INT_FROM_CPU_1_V << HP_SYSTEM_CPU_INT_FROM_CPU_1_S)
#define HP_SYSTEM_CPU_INT_FROM_CPU_1_V  0x00000001U
#define HP_SYSTEM_CPU_INT_FROM_CPU_1_S  0

/** HP_SYSTEM_CPU_INT_FROM_CPU_2_REG register
 *  NA
 */
#define HP_SYSTEM_CPU_INT_FROM_CPU_2_REG (DR_REG_HP_SYS_BASE + 0x18)
/** HP_SYSTEM_CPU_INT_FROM_CPU_2 : R/W; bitpos: [0]; default: 0;
 *  set 1 will trigger a interrupt
 */
#define HP_SYSTEM_CPU_INT_FROM_CPU_2    (BIT(0))
#define HP_SYSTEM_CPU_INT_FROM_CPU_2_M  (HP_SYSTEM_CPU_INT_FROM_CPU_2_V << HP_SYSTEM_CPU_INT_FROM_CPU_2_S)
#define HP_SYSTEM_CPU_INT_FROM_CPU_2_V  0x00000001U
#define HP_SYSTEM_CPU_INT_FROM_CPU_2_S  0

/** HP_SYSTEM_CPU_INT_FROM_CPU_3_REG register
 *  NA
 */
#define HP_SYSTEM_CPU_INT_FROM_CPU_3_REG (DR_REG_HP_SYS_BASE + 0x1c)
/** HP_SYSTEM_CPU_INT_FROM_CPU_3 : R/W; bitpos: [0]; default: 0;
 *  set 1 will trigger a interrupt
 */
#define HP_SYSTEM_CPU_INT_FROM_CPU_3    (BIT(0))
#define HP_SYSTEM_CPU_INT_FROM_CPU_3_M  (HP_SYSTEM_CPU_INT_FROM_CPU_3_V << HP_SYSTEM_CPU_INT_FROM_CPU_3_S)
#define HP_SYSTEM_CPU_INT_FROM_CPU_3_V  0x00000001U
#define HP_SYSTEM_CPU_INT_FROM_CPU_3_S  0

/** HP_SYSTEM_CACHE_CLK_CONFIG_REG register
 *  NA
 */
#define HP_SYSTEM_CACHE_CLK_CONFIG_REG (DR_REG_HP_SYS_BASE + 0x20)
/** HP_SYSTEM_REG_L2_CACHE_CLK_ON : R/W; bitpos: [0]; default: 1;
 *  l2 cache clk enable
 */
#define HP_SYSTEM_REG_L2_CACHE_CLK_ON    (BIT(0))
#define HP_SYSTEM_REG_L2_CACHE_CLK_ON_M  (HP_SYSTEM_REG_L2_CACHE_CLK_ON_V << HP_SYSTEM_REG_L2_CACHE_CLK_ON_S)
#define HP_SYSTEM_REG_L2_CACHE_CLK_ON_V  0x00000001U
#define HP_SYSTEM_REG_L2_CACHE_CLK_ON_S  0
/** HP_SYSTEM_REG_L1_D_CACHE_CLK_ON : R/W; bitpos: [1]; default: 1;
 *  l1 dcahce clk enable
 */
#define HP_SYSTEM_REG_L1_D_CACHE_CLK_ON    (BIT(1))
#define HP_SYSTEM_REG_L1_D_CACHE_CLK_ON_M  (HP_SYSTEM_REG_L1_D_CACHE_CLK_ON_V << HP_SYSTEM_REG_L1_D_CACHE_CLK_ON_S)
#define HP_SYSTEM_REG_L1_D_CACHE_CLK_ON_V  0x00000001U
#define HP_SYSTEM_REG_L1_D_CACHE_CLK_ON_S  1
/** HP_SYSTEM_REG_L1_I1_CACHE_CLK_ON : R/W; bitpos: [4]; default: 1;
 *  l1 icahce1 clk enable
 */
#define HP_SYSTEM_REG_L1_I1_CACHE_CLK_ON    (BIT(4))
#define HP_SYSTEM_REG_L1_I1_CACHE_CLK_ON_M  (HP_SYSTEM_REG_L1_I1_CACHE_CLK_ON_V << HP_SYSTEM_REG_L1_I1_CACHE_CLK_ON_S)
#define HP_SYSTEM_REG_L1_I1_CACHE_CLK_ON_V  0x00000001U
#define HP_SYSTEM_REG_L1_I1_CACHE_CLK_ON_S  4
/** HP_SYSTEM_REG_L1_I0_CACHE_CLK_ON : R/W; bitpos: [5]; default: 1;
 *  l1 icahce0 clk enable
 */
#define HP_SYSTEM_REG_L1_I0_CACHE_CLK_ON    (BIT(5))
#define HP_SYSTEM_REG_L1_I0_CACHE_CLK_ON_M  (HP_SYSTEM_REG_L1_I0_CACHE_CLK_ON_V << HP_SYSTEM_REG_L1_I0_CACHE_CLK_ON_S)
#define HP_SYSTEM_REG_L1_I0_CACHE_CLK_ON_V  0x00000001U
#define HP_SYSTEM_REG_L1_I0_CACHE_CLK_ON_S  5

/** HP_SYSTEM_CACHE_RESET_CONFIG_REG register
 *  NA
 */
#define HP_SYSTEM_CACHE_RESET_CONFIG_REG (DR_REG_HP_SYS_BASE + 0x24)
/** HP_SYSTEM_REG_L1_D_CACHE_RESET : R/W; bitpos: [1]; default: 0;
 *  set 1 to reset l1 dcahce
 */
#define HP_SYSTEM_REG_L1_D_CACHE_RESET    (BIT(1))
#define HP_SYSTEM_REG_L1_D_CACHE_RESET_M  (HP_SYSTEM_REG_L1_D_CACHE_RESET_V << HP_SYSTEM_REG_L1_D_CACHE_RESET_S)
#define HP_SYSTEM_REG_L1_D_CACHE_RESET_V  0x00000001U
#define HP_SYSTEM_REG_L1_D_CACHE_RESET_S  1
/** HP_SYSTEM_REG_L1_I1_CACHE_RESET : R/W; bitpos: [4]; default: 0;
 *  set 1 to reset l1 icahce1
 */
#define HP_SYSTEM_REG_L1_I1_CACHE_RESET    (BIT(4))
#define HP_SYSTEM_REG_L1_I1_CACHE_RESET_M  (HP_SYSTEM_REG_L1_I1_CACHE_RESET_V << HP_SYSTEM_REG_L1_I1_CACHE_RESET_S)
#define HP_SYSTEM_REG_L1_I1_CACHE_RESET_V  0x00000001U
#define HP_SYSTEM_REG_L1_I1_CACHE_RESET_S  4
/** HP_SYSTEM_REG_L1_I0_CACHE_RESET : R/W; bitpos: [5]; default: 0;
 *  set 1 to reset l1 icahce0
 */
#define HP_SYSTEM_REG_L1_I0_CACHE_RESET    (BIT(5))
#define HP_SYSTEM_REG_L1_I0_CACHE_RESET_M  (HP_SYSTEM_REG_L1_I0_CACHE_RESET_V << HP_SYSTEM_REG_L1_I0_CACHE_RESET_S)
#define HP_SYSTEM_REG_L1_I0_CACHE_RESET_V  0x00000001U
#define HP_SYSTEM_REG_L1_I0_CACHE_RESET_S  5

/** HP_SYSTEM_DMA_ADDR_CTRL_REG register
 *  NA
 */
#define HP_SYSTEM_DMA_ADDR_CTRL_REG (DR_REG_HP_SYS_BASE + 0x2c)
/** HP_SYSTEM_REG_SYS_DMA_ADDR_SEL : R/W; bitpos: [0]; default: 0;
 *  0 means dma access extmem use 8xxx_xxxx else use 4xxx_xxxx
 */
#define HP_SYSTEM_REG_SYS_DMA_ADDR_SEL    (BIT(0))
#define HP_SYSTEM_REG_SYS_DMA_ADDR_SEL_M  (HP_SYSTEM_REG_SYS_DMA_ADDR_SEL_V << HP_SYSTEM_REG_SYS_DMA_ADDR_SEL_S)
#define HP_SYSTEM_REG_SYS_DMA_ADDR_SEL_V  0x00000001U
#define HP_SYSTEM_REG_SYS_DMA_ADDR_SEL_S  0

/** HP_SYSTEM_TCM_RAM_WRR_CONFIG_REG register
 *  NA
 */
#define HP_SYSTEM_TCM_RAM_WRR_CONFIG_REG (DR_REG_HP_SYS_BASE + 0x34)
/** HP_SYSTEM_REG_TCM_RAM_IBUS0_WT : R/W; bitpos: [2:0]; default: 7;
 *  weight value of ibus0
 */
#define HP_SYSTEM_REG_TCM_RAM_IBUS0_WT    0x00000007U
#define HP_SYSTEM_REG_TCM_RAM_IBUS0_WT_M  (HP_SYSTEM_REG_TCM_RAM_IBUS0_WT_V << HP_SYSTEM_REG_TCM_RAM_IBUS0_WT_S)
#define HP_SYSTEM_REG_TCM_RAM_IBUS0_WT_V  0x00000007U
#define HP_SYSTEM_REG_TCM_RAM_IBUS0_WT_S  0
/** HP_SYSTEM_REG_TCM_RAM_IBUS1_WT : R/W; bitpos: [5:3]; default: 7;
 *  weight value of ibus1
 */
#define HP_SYSTEM_REG_TCM_RAM_IBUS1_WT    0x00000007U
#define HP_SYSTEM_REG_TCM_RAM_IBUS1_WT_M  (HP_SYSTEM_REG_TCM_RAM_IBUS1_WT_V << HP_SYSTEM_REG_TCM_RAM_IBUS1_WT_S)
#define HP_SYSTEM_REG_TCM_RAM_IBUS1_WT_V  0x00000007U
#define HP_SYSTEM_REG_TCM_RAM_IBUS1_WT_S  3
/** HP_SYSTEM_REG_TCM_RAM_IBUS2_WT : R/W; bitpos: [8:6]; default: 4;
 *  weight value of ibus2
 */
#define HP_SYSTEM_REG_TCM_RAM_IBUS2_WT    0x00000007U
#define HP_SYSTEM_REG_TCM_RAM_IBUS2_WT_M  (HP_SYSTEM_REG_TCM_RAM_IBUS2_WT_V << HP_SYSTEM_REG_TCM_RAM_IBUS2_WT_S)
#define HP_SYSTEM_REG_TCM_RAM_IBUS2_WT_V  0x00000007U
#define HP_SYSTEM_REG_TCM_RAM_IBUS2_WT_S  6
/** HP_SYSTEM_REG_TCM_RAM_IBUS3_WT : R/W; bitpos: [11:9]; default: 4;
 *  weight value of ibus3
 */
#define HP_SYSTEM_REG_TCM_RAM_IBUS3_WT    0x00000007U
#define HP_SYSTEM_REG_TCM_RAM_IBUS3_WT_M  (HP_SYSTEM_REG_TCM_RAM_IBUS3_WT_V << HP_SYSTEM_REG_TCM_RAM_IBUS3_WT_S)
#define HP_SYSTEM_REG_TCM_RAM_IBUS3_WT_V  0x00000007U
#define HP_SYSTEM_REG_TCM_RAM_IBUS3_WT_S  9
/** HP_SYSTEM_REG_TCM_RAM_DBUS0_WT : R/W; bitpos: [14:12]; default: 5;
 *  weight value of dbus0
 */
#define HP_SYSTEM_REG_TCM_RAM_DBUS0_WT    0x00000007U
#define HP_SYSTEM_REG_TCM_RAM_DBUS0_WT_M  (HP_SYSTEM_REG_TCM_RAM_DBUS0_WT_V << HP_SYSTEM_REG_TCM_RAM_DBUS0_WT_S)
#define HP_SYSTEM_REG_TCM_RAM_DBUS0_WT_V  0x00000007U
#define HP_SYSTEM_REG_TCM_RAM_DBUS0_WT_S  12
/** HP_SYSTEM_REG_TCM_RAM_DBUS1_WT : R/W; bitpos: [17:15]; default: 5;
 *  weight value of dbus1
 */
#define HP_SYSTEM_REG_TCM_RAM_DBUS1_WT    0x00000007U
#define HP_SYSTEM_REG_TCM_RAM_DBUS1_WT_M  (HP_SYSTEM_REG_TCM_RAM_DBUS1_WT_V << HP_SYSTEM_REG_TCM_RAM_DBUS1_WT_S)
#define HP_SYSTEM_REG_TCM_RAM_DBUS1_WT_V  0x00000007U
#define HP_SYSTEM_REG_TCM_RAM_DBUS1_WT_S  15
/** HP_SYSTEM_REG_TCM_RAM_DBUS2_WT : R/W; bitpos: [20:18]; default: 3;
 *  weight value of dbus2
 */
#define HP_SYSTEM_REG_TCM_RAM_DBUS2_WT    0x00000007U
#define HP_SYSTEM_REG_TCM_RAM_DBUS2_WT_M  (HP_SYSTEM_REG_TCM_RAM_DBUS2_WT_V << HP_SYSTEM_REG_TCM_RAM_DBUS2_WT_S)
#define HP_SYSTEM_REG_TCM_RAM_DBUS2_WT_V  0x00000007U
#define HP_SYSTEM_REG_TCM_RAM_DBUS2_WT_S  18
/** HP_SYSTEM_REG_TCM_RAM_DBUS3_WT : R/W; bitpos: [23:21]; default: 3;
 *  weight value of dbus3
 */
#define HP_SYSTEM_REG_TCM_RAM_DBUS3_WT    0x00000007U
#define HP_SYSTEM_REG_TCM_RAM_DBUS3_WT_M  (HP_SYSTEM_REG_TCM_RAM_DBUS3_WT_V << HP_SYSTEM_REG_TCM_RAM_DBUS3_WT_S)
#define HP_SYSTEM_REG_TCM_RAM_DBUS3_WT_V  0x00000007U
#define HP_SYSTEM_REG_TCM_RAM_DBUS3_WT_S  21
/** HP_SYSTEM_REG_TCM_RAM_DMA_WT : R/W; bitpos: [26:24]; default: 2;
 *  weight value of dma
 */
#define HP_SYSTEM_REG_TCM_RAM_DMA_WT    0x00000007U
#define HP_SYSTEM_REG_TCM_RAM_DMA_WT_M  (HP_SYSTEM_REG_TCM_RAM_DMA_WT_V << HP_SYSTEM_REG_TCM_RAM_DMA_WT_S)
#define HP_SYSTEM_REG_TCM_RAM_DMA_WT_V  0x00000007U
#define HP_SYSTEM_REG_TCM_RAM_DMA_WT_S  24
/** HP_SYSTEM_REG_TCM_RAM_WRR_HIGH : R/W; bitpos: [31]; default: 1;
 *  enable weighted round robin arbitration
 */
#define HP_SYSTEM_REG_TCM_RAM_WRR_HIGH    (BIT(31))
#define HP_SYSTEM_REG_TCM_RAM_WRR_HIGH_M  (HP_SYSTEM_REG_TCM_RAM_WRR_HIGH_V << HP_SYSTEM_REG_TCM_RAM_WRR_HIGH_S)
#define HP_SYSTEM_REG_TCM_RAM_WRR_HIGH_V  0x00000001U
#define HP_SYSTEM_REG_TCM_RAM_WRR_HIGH_S  31

/** HP_SYSTEM_TCM_SW_PARITY_BWE_MASK_REG register
 *  NA
 */
#define HP_SYSTEM_TCM_SW_PARITY_BWE_MASK_REG (DR_REG_HP_SYS_BASE + 0x38)
/** HP_SYSTEM_REG_TCM_SW_PARITY_BWE_MASK_CTRL : R/W; bitpos: [0]; default: 0;
 *  Set 1 to mask tcm bwe parity code bit
 */
#define HP_SYSTEM_REG_TCM_SW_PARITY_BWE_MASK_CTRL    (BIT(0))
#define HP_SYSTEM_REG_TCM_SW_PARITY_BWE_MASK_CTRL_M  (HP_SYSTEM_REG_TCM_SW_PARITY_BWE_MASK_CTRL_V << HP_SYSTEM_REG_TCM_SW_PARITY_BWE_MASK_CTRL_S)
#define HP_SYSTEM_REG_TCM_SW_PARITY_BWE_MASK_CTRL_V  0x00000001U
#define HP_SYSTEM_REG_TCM_SW_PARITY_BWE_MASK_CTRL_S  0

/** HP_SYSTEM_TCM_RAM_PWR_CTRL0_REG register
 *  NA
 */
#define HP_SYSTEM_TCM_RAM_PWR_CTRL0_REG (DR_REG_HP_SYS_BASE + 0x3c)
/** HP_SYSTEM_REG_HP_SYSTEM_TCM_CLK_FORCE_ON : R/W; bitpos: [0]; default: 0;
 *  HP_SYSTEM_tcm clk gatig force on
 */
#define HP_SYSTEM_REG_HP_SYSTEM_TCM_CLK_FORCE_ON    (BIT(0))
#define HP_SYSTEM_REG_HP_SYSTEM_TCM_CLK_FORCE_ON_M  (HP_SYSTEM_REG_HP_SYSTEM_TCM_CLK_FORCE_ON_V << HP_SYSTEM_REG_HP_SYSTEM_TCM_CLK_FORCE_ON_S)
#define HP_SYSTEM_REG_HP_SYSTEM_TCM_CLK_FORCE_ON_V  0x00000001U
#define HP_SYSTEM_REG_HP_SYSTEM_TCM_CLK_FORCE_ON_S  0

/** HP_SYSTEM_L2_ROM_PWR_CTRL0_REG register
 *  NA
 */
#define HP_SYSTEM_L2_ROM_PWR_CTRL0_REG (DR_REG_HP_SYS_BASE + 0x40)
/** HP_SYSTEM_REG_L2_ROM_CLK_FORCE_ON : R/W; bitpos: [0]; default: 0;
 *  l2_rom clk gating force on
 */
#define HP_SYSTEM_REG_L2_ROM_CLK_FORCE_ON    (BIT(0))
#define HP_SYSTEM_REG_L2_ROM_CLK_FORCE_ON_M  (HP_SYSTEM_REG_L2_ROM_CLK_FORCE_ON_V << HP_SYSTEM_REG_L2_ROM_CLK_FORCE_ON_S)
#define HP_SYSTEM_REG_L2_ROM_CLK_FORCE_ON_V  0x00000001U
#define HP_SYSTEM_REG_L2_ROM_CLK_FORCE_ON_S  0

/** HP_SYSTEM_PROBEA_CTRL_REG register
 *  NA
 */
#define HP_SYSTEM_PROBEA_CTRL_REG (DR_REG_HP_SYS_BASE + 0x50)
/** HP_SYSTEM_REG_PROBE_A_MOD_SEL : R/W; bitpos: [15:0]; default: 0;
 *  This field is used to selec probe_group from probe_group0 to probe_group15 for
 *  module's probe_out[31:0] in a mode
 */
#define HP_SYSTEM_REG_PROBE_A_MOD_SEL    0x0000FFFFU
#define HP_SYSTEM_REG_PROBE_A_MOD_SEL_M  (HP_SYSTEM_REG_PROBE_A_MOD_SEL_V << HP_SYSTEM_REG_PROBE_A_MOD_SEL_S)
#define HP_SYSTEM_REG_PROBE_A_MOD_SEL_V  0x0000FFFFU
#define HP_SYSTEM_REG_PROBE_A_MOD_SEL_S  0
/** HP_SYSTEM_REG_PROBE_A_TOP_SEL : R/W; bitpos: [23:16]; default: 0;
 *  This field is used to selec module's probe_out[31:0] as probe out in a mode
 */
#define HP_SYSTEM_REG_PROBE_A_TOP_SEL    0x000000FFU
#define HP_SYSTEM_REG_PROBE_A_TOP_SEL_M  (HP_SYSTEM_REG_PROBE_A_TOP_SEL_V << HP_SYSTEM_REG_PROBE_A_TOP_SEL_S)
#define HP_SYSTEM_REG_PROBE_A_TOP_SEL_V  0x000000FFU
#define HP_SYSTEM_REG_PROBE_A_TOP_SEL_S  16
/** HP_SYSTEM_REG_PROBE_L_SEL : R/W; bitpos: [25:24]; default: 0;
 *  This field is used to selec probe_out[31:16]
 */
#define HP_SYSTEM_REG_PROBE_L_SEL    0x00000003U
#define HP_SYSTEM_REG_PROBE_L_SEL_M  (HP_SYSTEM_REG_PROBE_L_SEL_V << HP_SYSTEM_REG_PROBE_L_SEL_S)
#define HP_SYSTEM_REG_PROBE_L_SEL_V  0x00000003U
#define HP_SYSTEM_REG_PROBE_L_SEL_S  24
/** HP_SYSTEM_REG_PROBE_H_SEL : R/W; bitpos: [27:26]; default: 0;
 *  This field is used to selec probe_out[31:16]
 */
#define HP_SYSTEM_REG_PROBE_H_SEL    0x00000003U
#define HP_SYSTEM_REG_PROBE_H_SEL_M  (HP_SYSTEM_REG_PROBE_H_SEL_V << HP_SYSTEM_REG_PROBE_H_SEL_S)
#define HP_SYSTEM_REG_PROBE_H_SEL_V  0x00000003U
#define HP_SYSTEM_REG_PROBE_H_SEL_S  26
/** HP_SYSTEM_REG_PROBE_GLOBAL_EN : R/W; bitpos: [28]; default: 0;
 *  Set this bit to enable global debug probe in hp system.
 */
#define HP_SYSTEM_REG_PROBE_GLOBAL_EN    (BIT(28))
#define HP_SYSTEM_REG_PROBE_GLOBAL_EN_M  (HP_SYSTEM_REG_PROBE_GLOBAL_EN_V << HP_SYSTEM_REG_PROBE_GLOBAL_EN_S)
#define HP_SYSTEM_REG_PROBE_GLOBAL_EN_V  0x00000001U
#define HP_SYSTEM_REG_PROBE_GLOBAL_EN_S  28

/** HP_SYSTEM_PROBEB_CTRL_REG register
 *  NA
 */
#define HP_SYSTEM_PROBEB_CTRL_REG (DR_REG_HP_SYS_BASE + 0x54)
/** HP_SYSTEM_REG_PROBE_B_MOD_SEL : R/W; bitpos: [15:0]; default: 0;
 *  This field is used to selec probe_group from probe_group0 to probe_group15 for
 *  module's probe_out[31:0] in b mode.
 */
#define HP_SYSTEM_REG_PROBE_B_MOD_SEL    0x0000FFFFU
#define HP_SYSTEM_REG_PROBE_B_MOD_SEL_M  (HP_SYSTEM_REG_PROBE_B_MOD_SEL_V << HP_SYSTEM_REG_PROBE_B_MOD_SEL_S)
#define HP_SYSTEM_REG_PROBE_B_MOD_SEL_V  0x0000FFFFU
#define HP_SYSTEM_REG_PROBE_B_MOD_SEL_S  0
/** HP_SYSTEM_REG_PROBE_B_TOP_SEL : R/W; bitpos: [23:16]; default: 0;
 *  This field is used to select module's probe_out[31:0]  as probe_out in b mode
 */
#define HP_SYSTEM_REG_PROBE_B_TOP_SEL    0x000000FFU
#define HP_SYSTEM_REG_PROBE_B_TOP_SEL_M  (HP_SYSTEM_REG_PROBE_B_TOP_SEL_V << HP_SYSTEM_REG_PROBE_B_TOP_SEL_S)
#define HP_SYSTEM_REG_PROBE_B_TOP_SEL_V  0x000000FFU
#define HP_SYSTEM_REG_PROBE_B_TOP_SEL_S  16
/** HP_SYSTEM_REG_PROBE_B_EN : R/W; bitpos: [24]; default: 0;
 *  Set this bit to enable b mode for debug probe. 1:  b mode, 0: a mode.
 */
#define HP_SYSTEM_REG_PROBE_B_EN    (BIT(24))
#define HP_SYSTEM_REG_PROBE_B_EN_M  (HP_SYSTEM_REG_PROBE_B_EN_V << HP_SYSTEM_REG_PROBE_B_EN_S)
#define HP_SYSTEM_REG_PROBE_B_EN_V  0x00000001U
#define HP_SYSTEM_REG_PROBE_B_EN_S  24

/** HP_SYSTEM_PROBE_OUT_REG register
 *  NA
 */
#define HP_SYSTEM_PROBE_OUT_REG (DR_REG_HP_SYS_BASE + 0x5c)
/** HP_SYSTEM_REG_PROBE_TOP_OUT : RO; bitpos: [31:0]; default: 0;
 *  NA
 */
#define HP_SYSTEM_REG_PROBE_TOP_OUT    0xFFFFFFFFU
#define HP_SYSTEM_REG_PROBE_TOP_OUT_M  (HP_SYSTEM_REG_PROBE_TOP_OUT_V << HP_SYSTEM_REG_PROBE_TOP_OUT_S)
#define HP_SYSTEM_REG_PROBE_TOP_OUT_V  0xFFFFFFFFU
#define HP_SYSTEM_REG_PROBE_TOP_OUT_S  0

/** HP_SYSTEM_L2_MEM_RAM_PWR_CTRL0_REG register
 *  NA
 */
#define HP_SYSTEM_L2_MEM_RAM_PWR_CTRL0_REG (DR_REG_HP_SYS_BASE + 0x60)
/** HP_SYSTEM_REG_L2_MEM_CLK_FORCE_ON : R/W; bitpos: [0]; default: 0;
 *  l2ram clk_gating force on
 */
#define HP_SYSTEM_REG_L2_MEM_CLK_FORCE_ON    (BIT(0))
#define HP_SYSTEM_REG_L2_MEM_CLK_FORCE_ON_M  (HP_SYSTEM_REG_L2_MEM_CLK_FORCE_ON_V << HP_SYSTEM_REG_L2_MEM_CLK_FORCE_ON_S)
#define HP_SYSTEM_REG_L2_MEM_CLK_FORCE_ON_V  0x00000001U
#define HP_SYSTEM_REG_L2_MEM_CLK_FORCE_ON_S  0

/** HP_SYSTEM_CPU_CORESTALLED_ST_REG register
 *  NA
 */
#define HP_SYSTEM_CPU_CORESTALLED_ST_REG (DR_REG_HP_SYS_BASE + 0x64)
/** HP_SYSTEM_REG_CORE0_CORESTALLED_ST : RO; bitpos: [0]; default: 0;
 *  hp core0 corestalled status
 */
#define HP_SYSTEM_REG_CORE0_CORESTALLED_ST    (BIT(0))
#define HP_SYSTEM_REG_CORE0_CORESTALLED_ST_M  (HP_SYSTEM_REG_CORE0_CORESTALLED_ST_V << HP_SYSTEM_REG_CORE0_CORESTALLED_ST_S)
#define HP_SYSTEM_REG_CORE0_CORESTALLED_ST_V  0x00000001U
#define HP_SYSTEM_REG_CORE0_CORESTALLED_ST_S  0
/** HP_SYSTEM_REG_CORE1_CORESTALLED_ST : RO; bitpos: [1]; default: 0;
 *  hp core1 corestalled status
 */
#define HP_SYSTEM_REG_CORE1_CORESTALLED_ST    (BIT(1))
#define HP_SYSTEM_REG_CORE1_CORESTALLED_ST_M  (HP_SYSTEM_REG_CORE1_CORESTALLED_ST_V << HP_SYSTEM_REG_CORE1_CORESTALLED_ST_S)
#define HP_SYSTEM_REG_CORE1_CORESTALLED_ST_V  0x00000001U
#define HP_SYSTEM_REG_CORE1_CORESTALLED_ST_S  1

/** HP_SYSTEM_CRYPTO_CTRL_REG register
 *  NA
 */
#define HP_SYSTEM_CRYPTO_CTRL_REG (DR_REG_HP_SYS_BASE + 0x70)
/** HP_SYSTEM_REG_ENABLE_SPI_MANUAL_ENCRYPT : R/W; bitpos: [0]; default: 0;
 *  NA
 */
#define HP_SYSTEM_REG_ENABLE_SPI_MANUAL_ENCRYPT    (BIT(0))
#define HP_SYSTEM_REG_ENABLE_SPI_MANUAL_ENCRYPT_M  (HP_SYSTEM_REG_ENABLE_SPI_MANUAL_ENCRYPT_V << HP_SYSTEM_REG_ENABLE_SPI_MANUAL_ENCRYPT_S)
#define HP_SYSTEM_REG_ENABLE_SPI_MANUAL_ENCRYPT_V  0x00000001U
#define HP_SYSTEM_REG_ENABLE_SPI_MANUAL_ENCRYPT_S  0
/** HP_SYSTEM_REG_ENABLE_DOWNLOAD_DB_ENCRYPT : R/W; bitpos: [1]; default: 0;
 *  NA
 */
#define HP_SYSTEM_REG_ENABLE_DOWNLOAD_DB_ENCRYPT    (BIT(1))
#define HP_SYSTEM_REG_ENABLE_DOWNLOAD_DB_ENCRYPT_M  (HP_SYSTEM_REG_ENABLE_DOWNLOAD_DB_ENCRYPT_V << HP_SYSTEM_REG_ENABLE_DOWNLOAD_DB_ENCRYPT_S)
#define HP_SYSTEM_REG_ENABLE_DOWNLOAD_DB_ENCRYPT_V  0x00000001U
#define HP_SYSTEM_REG_ENABLE_DOWNLOAD_DB_ENCRYPT_S  1
/** HP_SYSTEM_REG_ENABLE_DOWNLOAD_G0CB_DECRYPT : R/W; bitpos: [2]; default: 0;
 *  NA
 */
#define HP_SYSTEM_REG_ENABLE_DOWNLOAD_G0CB_DECRYPT    (BIT(2))
#define HP_SYSTEM_REG_ENABLE_DOWNLOAD_G0CB_DECRYPT_M  (HP_SYSTEM_REG_ENABLE_DOWNLOAD_G0CB_DECRYPT_V << HP_SYSTEM_REG_ENABLE_DOWNLOAD_G0CB_DECRYPT_S)
#define HP_SYSTEM_REG_ENABLE_DOWNLOAD_G0CB_DECRYPT_V  0x00000001U
#define HP_SYSTEM_REG_ENABLE_DOWNLOAD_G0CB_DECRYPT_S  2
/** HP_SYSTEM_REG_ENABLE_DOWNLOAD_MANUAL_ENCRYPT : R/W; bitpos: [3]; default: 0;
 *  NA
 */
#define HP_SYSTEM_REG_ENABLE_DOWNLOAD_MANUAL_ENCRYPT    (BIT(3))
#define HP_SYSTEM_REG_ENABLE_DOWNLOAD_MANUAL_ENCRYPT_M  (HP_SYSTEM_REG_ENABLE_DOWNLOAD_MANUAL_ENCRYPT_V << HP_SYSTEM_REG_ENABLE_DOWNLOAD_MANUAL_ENCRYPT_S)
#define HP_SYSTEM_REG_ENABLE_DOWNLOAD_MANUAL_ENCRYPT_V  0x00000001U
#define HP_SYSTEM_REG_ENABLE_DOWNLOAD_MANUAL_ENCRYPT_S  3

/** HP_SYSTEM_GPIO_O_HOLD_CTRL0_REG register
 *  NA
 */
#define HP_SYSTEM_GPIO_O_HOLD_CTRL0_REG (DR_REG_HP_SYS_BASE + 0x74)
/** HP_SYSTEM_REG_GPIO_0_HOLD_LOW : R/W; bitpos: [31:0]; default: 0;
 *  hold control for gpio47~16
 */
#define HP_SYSTEM_REG_GPIO_0_HOLD_LOW    0xFFFFFFFFU
#define HP_SYSTEM_REG_GPIO_0_HOLD_LOW_M  (HP_SYSTEM_REG_GPIO_0_HOLD_LOW_V << HP_SYSTEM_REG_GPIO_0_HOLD_LOW_S)
#define HP_SYSTEM_REG_GPIO_0_HOLD_LOW_V  0xFFFFFFFFU
#define HP_SYSTEM_REG_GPIO_0_HOLD_LOW_S  0

/** HP_SYSTEM_GPIO_O_HOLD_CTRL1_REG register
 *  NA
 */
#define HP_SYSTEM_GPIO_O_HOLD_CTRL1_REG (DR_REG_HP_SYS_BASE + 0x78)
/** HP_SYSTEM_REG_GPIO_0_HOLD_HIGH : R/W; bitpos: [8:0]; default: 0;
 *  hold control for gpio56~48
 */
#define HP_SYSTEM_REG_GPIO_0_HOLD_HIGH    0x000001FFU
#define HP_SYSTEM_REG_GPIO_0_HOLD_HIGH_M  (HP_SYSTEM_REG_GPIO_0_HOLD_HIGH_V << HP_SYSTEM_REG_GPIO_0_HOLD_HIGH_S)
#define HP_SYSTEM_REG_GPIO_0_HOLD_HIGH_V  0x000001FFU
#define HP_SYSTEM_REG_GPIO_0_HOLD_HIGH_S  0

/** HP_SYSTEM_RDN_ECO_CS_REG register
 *  NA
 */
#define HP_SYSTEM_RDN_ECO_CS_REG (DR_REG_HP_SYS_BASE + 0x7c)
/** HP_SYSTEM_REG_HP_SYSTEM_RDN_ECO_EN : R/W; bitpos: [0]; default: 0;
 *  NA
 */
#define HP_SYSTEM_REG_HP_SYSTEM_RDN_ECO_EN    (BIT(0))
#define HP_SYSTEM_REG_HP_SYSTEM_RDN_ECO_EN_M  (HP_SYSTEM_REG_HP_SYSTEM_RDN_ECO_EN_V << HP_SYSTEM_REG_HP_SYSTEM_RDN_ECO_EN_S)
#define HP_SYSTEM_REG_HP_SYSTEM_RDN_ECO_EN_V  0x00000001U
#define HP_SYSTEM_REG_HP_SYSTEM_RDN_ECO_EN_S  0
/** HP_SYSTEM_REG_HP_SYSTEM_RDN_ECO_RESULT : RO; bitpos: [1]; default: 0;
 *  NA
 */
#define HP_SYSTEM_REG_HP_SYSTEM_RDN_ECO_RESULT    (BIT(1))
#define HP_SYSTEM_REG_HP_SYSTEM_RDN_ECO_RESULT_M  (HP_SYSTEM_REG_HP_SYSTEM_RDN_ECO_RESULT_V << HP_SYSTEM_REG_HP_SYSTEM_RDN_ECO_RESULT_S)
#define HP_SYSTEM_REG_HP_SYSTEM_RDN_ECO_RESULT_V  0x00000001U
#define HP_SYSTEM_REG_HP_SYSTEM_RDN_ECO_RESULT_S  1

/** HP_SYSTEM_CACHE_APB_POSTW_EN_REG register
 *  NA
 */
#define HP_SYSTEM_CACHE_APB_POSTW_EN_REG (DR_REG_HP_SYS_BASE + 0x80)
/** HP_SYSTEM_REG_CACHE_APB_POSTW_EN : R/W; bitpos: [0]; default: 0;
 *  cache apb register interface post write enable, 1 will speed up write, but will
 *  take some time to update value to register
 */
#define HP_SYSTEM_REG_CACHE_APB_POSTW_EN    (BIT(0))
#define HP_SYSTEM_REG_CACHE_APB_POSTW_EN_M  (HP_SYSTEM_REG_CACHE_APB_POSTW_EN_V << HP_SYSTEM_REG_CACHE_APB_POSTW_EN_S)
#define HP_SYSTEM_REG_CACHE_APB_POSTW_EN_V  0x00000001U
#define HP_SYSTEM_REG_CACHE_APB_POSTW_EN_S  0

/** HP_SYSTEM_L2_MEM_SUBSIZE_REG register
 *  NA
 */
#define HP_SYSTEM_L2_MEM_SUBSIZE_REG (DR_REG_HP_SYS_BASE + 0x84)
/** HP_SYSTEM_REG_L2_MEM_SUB_BLKSIZE : R/W; bitpos: [1:0]; default: 0;
 *  l2mem sub block size 00=>32 01=>64 10=>128 11=>256
 */
#define HP_SYSTEM_REG_L2_MEM_SUB_BLKSIZE    0x00000003U
#define HP_SYSTEM_REG_L2_MEM_SUB_BLKSIZE_M  (HP_SYSTEM_REG_L2_MEM_SUB_BLKSIZE_V << HP_SYSTEM_REG_L2_MEM_SUB_BLKSIZE_S)
#define HP_SYSTEM_REG_L2_MEM_SUB_BLKSIZE_V  0x00000003U
#define HP_SYSTEM_REG_L2_MEM_SUB_BLKSIZE_S  0

/** HP_SYSTEM_L2_MEM_INT_RAW_REG register
 *  NA
 */
#define HP_SYSTEM_L2_MEM_INT_RAW_REG (DR_REG_HP_SYS_BASE + 0x9c)
/** HP_SYSTEM_REG_L2_MEM_ECC_ERR_INT_RAW : R/WTC/SS; bitpos: [0]; default: 0;
 *  intr triggered when two bit error detected and corrected from ecc
 */
#define HP_SYSTEM_REG_L2_MEM_ECC_ERR_INT_RAW    (BIT(0))
#define HP_SYSTEM_REG_L2_MEM_ECC_ERR_INT_RAW_M  (HP_SYSTEM_REG_L2_MEM_ECC_ERR_INT_RAW_V << HP_SYSTEM_REG_L2_MEM_ECC_ERR_INT_RAW_S)
#define HP_SYSTEM_REG_L2_MEM_ECC_ERR_INT_RAW_V  0x00000001U
#define HP_SYSTEM_REG_L2_MEM_ECC_ERR_INT_RAW_S  0
/** HP_SYSTEM_REG_L2_MEM_EXCEED_ADDR_INT_RAW : R/WTC/SS; bitpos: [1]; default: 0;
 *  intr triggered when access addr exceeds 0xff9ffff at bypass mode or exceeds
 *  0xff80000 at l2cache 128kb mode or exceeds 0xff60000 at l2cache 256kb mode
 */
#define HP_SYSTEM_REG_L2_MEM_EXCEED_ADDR_INT_RAW    (BIT(1))
#define HP_SYSTEM_REG_L2_MEM_EXCEED_ADDR_INT_RAW_M  (HP_SYSTEM_REG_L2_MEM_EXCEED_ADDR_INT_RAW_V << HP_SYSTEM_REG_L2_MEM_EXCEED_ADDR_INT_RAW_S)
#define HP_SYSTEM_REG_L2_MEM_EXCEED_ADDR_INT_RAW_V  0x00000001U
#define HP_SYSTEM_REG_L2_MEM_EXCEED_ADDR_INT_RAW_S  1
/** HP_SYSTEM_REG_L2_MEM_ERR_RESP_INT_RAW : R/WTC/SS; bitpos: [2]; default: 0;
 *  intr triggered when err response occurs
 */
#define HP_SYSTEM_REG_L2_MEM_ERR_RESP_INT_RAW    (BIT(2))
#define HP_SYSTEM_REG_L2_MEM_ERR_RESP_INT_RAW_M  (HP_SYSTEM_REG_L2_MEM_ERR_RESP_INT_RAW_V << HP_SYSTEM_REG_L2_MEM_ERR_RESP_INT_RAW_S)
#define HP_SYSTEM_REG_L2_MEM_ERR_RESP_INT_RAW_V  0x00000001U
#define HP_SYSTEM_REG_L2_MEM_ERR_RESP_INT_RAW_S  2

/** HP_SYSTEM_L2_MEM_INT_ST_REG register
 *  NA
 */
#define HP_SYSTEM_L2_MEM_INT_ST_REG (DR_REG_HP_SYS_BASE + 0xa0)
/** HP_SYSTEM_REG_L2_MEM_ECC_ERR_INT_ST : RO; bitpos: [0]; default: 0;
 *  NA
 */
#define HP_SYSTEM_REG_L2_MEM_ECC_ERR_INT_ST    (BIT(0))
#define HP_SYSTEM_REG_L2_MEM_ECC_ERR_INT_ST_M  (HP_SYSTEM_REG_L2_MEM_ECC_ERR_INT_ST_V << HP_SYSTEM_REG_L2_MEM_ECC_ERR_INT_ST_S)
#define HP_SYSTEM_REG_L2_MEM_ECC_ERR_INT_ST_V  0x00000001U
#define HP_SYSTEM_REG_L2_MEM_ECC_ERR_INT_ST_S  0
/** HP_SYSTEM_REG_L2_MEM_EXCEED_ADDR_INT_ST : RO; bitpos: [1]; default: 0;
 *  NA
 */
#define HP_SYSTEM_REG_L2_MEM_EXCEED_ADDR_INT_ST    (BIT(1))
#define HP_SYSTEM_REG_L2_MEM_EXCEED_ADDR_INT_ST_M  (HP_SYSTEM_REG_L2_MEM_EXCEED_ADDR_INT_ST_V << HP_SYSTEM_REG_L2_MEM_EXCEED_ADDR_INT_ST_S)
#define HP_SYSTEM_REG_L2_MEM_EXCEED_ADDR_INT_ST_V  0x00000001U
#define HP_SYSTEM_REG_L2_MEM_EXCEED_ADDR_INT_ST_S  1
/** HP_SYSTEM_REG_L2_MEM_ERR_RESP_INT_ST : RO; bitpos: [2]; default: 0;
 *  NA
 */
#define HP_SYSTEM_REG_L2_MEM_ERR_RESP_INT_ST    (BIT(2))
#define HP_SYSTEM_REG_L2_MEM_ERR_RESP_INT_ST_M  (HP_SYSTEM_REG_L2_MEM_ERR_RESP_INT_ST_V << HP_SYSTEM_REG_L2_MEM_ERR_RESP_INT_ST_S)
#define HP_SYSTEM_REG_L2_MEM_ERR_RESP_INT_ST_V  0x00000001U
#define HP_SYSTEM_REG_L2_MEM_ERR_RESP_INT_ST_S  2

/** HP_SYSTEM_L2_MEM_INT_ENA_REG register
 *  NA
 */
#define HP_SYSTEM_L2_MEM_INT_ENA_REG (DR_REG_HP_SYS_BASE + 0xa4)
/** HP_SYSTEM_REG_L2_MEM_ECC_ERR_INT_ENA : R/W; bitpos: [0]; default: 0;
 *  NA
 */
#define HP_SYSTEM_REG_L2_MEM_ECC_ERR_INT_ENA    (BIT(0))
#define HP_SYSTEM_REG_L2_MEM_ECC_ERR_INT_ENA_M  (HP_SYSTEM_REG_L2_MEM_ECC_ERR_INT_ENA_V << HP_SYSTEM_REG_L2_MEM_ECC_ERR_INT_ENA_S)
#define HP_SYSTEM_REG_L2_MEM_ECC_ERR_INT_ENA_V  0x00000001U
#define HP_SYSTEM_REG_L2_MEM_ECC_ERR_INT_ENA_S  0
/** HP_SYSTEM_REG_L2_MEM_EXCEED_ADDR_INT_ENA : R/W; bitpos: [1]; default: 0;
 *  NA
 */
#define HP_SYSTEM_REG_L2_MEM_EXCEED_ADDR_INT_ENA    (BIT(1))
#define HP_SYSTEM_REG_L2_MEM_EXCEED_ADDR_INT_ENA_M  (HP_SYSTEM_REG_L2_MEM_EXCEED_ADDR_INT_ENA_V << HP_SYSTEM_REG_L2_MEM_EXCEED_ADDR_INT_ENA_S)
#define HP_SYSTEM_REG_L2_MEM_EXCEED_ADDR_INT_ENA_V  0x00000001U
#define HP_SYSTEM_REG_L2_MEM_EXCEED_ADDR_INT_ENA_S  1
/** HP_SYSTEM_REG_L2_MEM_ERR_RESP_INT_ENA : R/W; bitpos: [2]; default: 0;
 *  NA
 */
#define HP_SYSTEM_REG_L2_MEM_ERR_RESP_INT_ENA    (BIT(2))
#define HP_SYSTEM_REG_L2_MEM_ERR_RESP_INT_ENA_M  (HP_SYSTEM_REG_L2_MEM_ERR_RESP_INT_ENA_V << HP_SYSTEM_REG_L2_MEM_ERR_RESP_INT_ENA_S)
#define HP_SYSTEM_REG_L2_MEM_ERR_RESP_INT_ENA_V  0x00000001U
#define HP_SYSTEM_REG_L2_MEM_ERR_RESP_INT_ENA_S  2

/** HP_SYSTEM_L2_MEM_INT_CLR_REG register
 *  NA
 */
#define HP_SYSTEM_L2_MEM_INT_CLR_REG (DR_REG_HP_SYS_BASE + 0xa8)
/** HP_SYSTEM_REG_L2_MEM_ECC_ERR_INT_CLR : WT; bitpos: [0]; default: 0;
 *  NA
 */
#define HP_SYSTEM_REG_L2_MEM_ECC_ERR_INT_CLR    (BIT(0))
#define HP_SYSTEM_REG_L2_MEM_ECC_ERR_INT_CLR_M  (HP_SYSTEM_REG_L2_MEM_ECC_ERR_INT_CLR_V << HP_SYSTEM_REG_L2_MEM_ECC_ERR_INT_CLR_S)
#define HP_SYSTEM_REG_L2_MEM_ECC_ERR_INT_CLR_V  0x00000001U
#define HP_SYSTEM_REG_L2_MEM_ECC_ERR_INT_CLR_S  0
/** HP_SYSTEM_REG_L2_MEM_EXCEED_ADDR_INT_CLR : WT; bitpos: [1]; default: 0;
 *  NA
 */
#define HP_SYSTEM_REG_L2_MEM_EXCEED_ADDR_INT_CLR    (BIT(1))
#define HP_SYSTEM_REG_L2_MEM_EXCEED_ADDR_INT_CLR_M  (HP_SYSTEM_REG_L2_MEM_EXCEED_ADDR_INT_CLR_V << HP_SYSTEM_REG_L2_MEM_EXCEED_ADDR_INT_CLR_S)
#define HP_SYSTEM_REG_L2_MEM_EXCEED_ADDR_INT_CLR_V  0x00000001U
#define HP_SYSTEM_REG_L2_MEM_EXCEED_ADDR_INT_CLR_S  1
/** HP_SYSTEM_REG_L2_MEM_ERR_RESP_INT_CLR : WT; bitpos: [2]; default: 0;
 *  NA
 */
#define HP_SYSTEM_REG_L2_MEM_ERR_RESP_INT_CLR    (BIT(2))
#define HP_SYSTEM_REG_L2_MEM_ERR_RESP_INT_CLR_M  (HP_SYSTEM_REG_L2_MEM_ERR_RESP_INT_CLR_V << HP_SYSTEM_REG_L2_MEM_ERR_RESP_INT_CLR_S)
#define HP_SYSTEM_REG_L2_MEM_ERR_RESP_INT_CLR_V  0x00000001U
#define HP_SYSTEM_REG_L2_MEM_ERR_RESP_INT_CLR_S  2

/** HP_SYSTEM_L2_MEM_L2_RAM_ECC_REG register
 *  NA
 */
#define HP_SYSTEM_L2_MEM_L2_RAM_ECC_REG (DR_REG_HP_SYS_BASE + 0xac)
/** HP_SYSTEM_REG_L2_RAM_UNIT0_ECC_EN : R/W; bitpos: [0]; default: 0;
 *  NA
 */
#define HP_SYSTEM_REG_L2_RAM_UNIT0_ECC_EN    (BIT(0))
#define HP_SYSTEM_REG_L2_RAM_UNIT0_ECC_EN_M  (HP_SYSTEM_REG_L2_RAM_UNIT0_ECC_EN_V << HP_SYSTEM_REG_L2_RAM_UNIT0_ECC_EN_S)
#define HP_SYSTEM_REG_L2_RAM_UNIT0_ECC_EN_V  0x00000001U
#define HP_SYSTEM_REG_L2_RAM_UNIT0_ECC_EN_S  0
/** HP_SYSTEM_REG_L2_RAM_UNIT1_ECC_EN : R/W; bitpos: [1]; default: 0;
 *  NA
 */
#define HP_SYSTEM_REG_L2_RAM_UNIT1_ECC_EN    (BIT(1))
#define HP_SYSTEM_REG_L2_RAM_UNIT1_ECC_EN_M  (HP_SYSTEM_REG_L2_RAM_UNIT1_ECC_EN_V << HP_SYSTEM_REG_L2_RAM_UNIT1_ECC_EN_S)
#define HP_SYSTEM_REG_L2_RAM_UNIT1_ECC_EN_V  0x00000001U
#define HP_SYSTEM_REG_L2_RAM_UNIT1_ECC_EN_S  1
/** HP_SYSTEM_REG_L2_RAM_UNIT2_ECC_EN : R/W; bitpos: [2]; default: 0;
 *  NA
 */
#define HP_SYSTEM_REG_L2_RAM_UNIT2_ECC_EN    (BIT(2))
#define HP_SYSTEM_REG_L2_RAM_UNIT2_ECC_EN_M  (HP_SYSTEM_REG_L2_RAM_UNIT2_ECC_EN_V << HP_SYSTEM_REG_L2_RAM_UNIT2_ECC_EN_S)
#define HP_SYSTEM_REG_L2_RAM_UNIT2_ECC_EN_V  0x00000001U
#define HP_SYSTEM_REG_L2_RAM_UNIT2_ECC_EN_S  2
/** HP_SYSTEM_REG_L2_RAM_UNIT3_ECC_EN : R/W; bitpos: [3]; default: 0;
 *  NA
 */
#define HP_SYSTEM_REG_L2_RAM_UNIT3_ECC_EN    (BIT(3))
#define HP_SYSTEM_REG_L2_RAM_UNIT3_ECC_EN_M  (HP_SYSTEM_REG_L2_RAM_UNIT3_ECC_EN_V << HP_SYSTEM_REG_L2_RAM_UNIT3_ECC_EN_S)
#define HP_SYSTEM_REG_L2_RAM_UNIT3_ECC_EN_V  0x00000001U
#define HP_SYSTEM_REG_L2_RAM_UNIT3_ECC_EN_S  3
/** HP_SYSTEM_REG_L2_RAM_UNIT4_ECC_EN : R/W; bitpos: [4]; default: 0;
 *  NA
 */
#define HP_SYSTEM_REG_L2_RAM_UNIT4_ECC_EN    (BIT(4))
#define HP_SYSTEM_REG_L2_RAM_UNIT4_ECC_EN_M  (HP_SYSTEM_REG_L2_RAM_UNIT4_ECC_EN_V << HP_SYSTEM_REG_L2_RAM_UNIT4_ECC_EN_S)
#define HP_SYSTEM_REG_L2_RAM_UNIT4_ECC_EN_V  0x00000001U
#define HP_SYSTEM_REG_L2_RAM_UNIT4_ECC_EN_S  4
/** HP_SYSTEM_REG_L2_RAM_UNIT5_ECC_EN : R/W; bitpos: [5]; default: 0;
 *  NA
 */
#define HP_SYSTEM_REG_L2_RAM_UNIT5_ECC_EN    (BIT(5))
#define HP_SYSTEM_REG_L2_RAM_UNIT5_ECC_EN_M  (HP_SYSTEM_REG_L2_RAM_UNIT5_ECC_EN_V << HP_SYSTEM_REG_L2_RAM_UNIT5_ECC_EN_S)
#define HP_SYSTEM_REG_L2_RAM_UNIT5_ECC_EN_V  0x00000001U
#define HP_SYSTEM_REG_L2_RAM_UNIT5_ECC_EN_S  5

/** HP_SYSTEM_L2_MEM_INT_RECORD0_REG register
 *  NA
 */
#define HP_SYSTEM_L2_MEM_INT_RECORD0_REG (DR_REG_HP_SYS_BASE + 0xb0)
/** HP_SYSTEM_REG_L2_MEM_EXCEED_ADDR_INT_ADDR : RO; bitpos: [20:0]; default: 0;
 *  NA
 */
#define HP_SYSTEM_REG_L2_MEM_EXCEED_ADDR_INT_ADDR    0x001FFFFFU
#define HP_SYSTEM_REG_L2_MEM_EXCEED_ADDR_INT_ADDR_M  (HP_SYSTEM_REG_L2_MEM_EXCEED_ADDR_INT_ADDR_V << HP_SYSTEM_REG_L2_MEM_EXCEED_ADDR_INT_ADDR_S)
#define HP_SYSTEM_REG_L2_MEM_EXCEED_ADDR_INT_ADDR_V  0x001FFFFFU
#define HP_SYSTEM_REG_L2_MEM_EXCEED_ADDR_INT_ADDR_S  0
/** HP_SYSTEM_REG_L2_MEM_EXCEED_ADDR_INT_WE : RO; bitpos: [21]; default: 0;
 *  NA
 */
#define HP_SYSTEM_REG_L2_MEM_EXCEED_ADDR_INT_WE    (BIT(21))
#define HP_SYSTEM_REG_L2_MEM_EXCEED_ADDR_INT_WE_M  (HP_SYSTEM_REG_L2_MEM_EXCEED_ADDR_INT_WE_V << HP_SYSTEM_REG_L2_MEM_EXCEED_ADDR_INT_WE_S)
#define HP_SYSTEM_REG_L2_MEM_EXCEED_ADDR_INT_WE_V  0x00000001U
#define HP_SYSTEM_REG_L2_MEM_EXCEED_ADDR_INT_WE_S  21
/** HP_SYSTEM_REG_L2_MEM_EXCEED_ADDR_INT_MASTER : RO; bitpos: [24:22]; default: 0;
 *  NA
 */
#define HP_SYSTEM_REG_L2_MEM_EXCEED_ADDR_INT_MASTER    0x00000007U
#define HP_SYSTEM_REG_L2_MEM_EXCEED_ADDR_INT_MASTER_M  (HP_SYSTEM_REG_L2_MEM_EXCEED_ADDR_INT_MASTER_V << HP_SYSTEM_REG_L2_MEM_EXCEED_ADDR_INT_MASTER_S)
#define HP_SYSTEM_REG_L2_MEM_EXCEED_ADDR_INT_MASTER_V  0x00000007U
#define HP_SYSTEM_REG_L2_MEM_EXCEED_ADDR_INT_MASTER_S  22

/** HP_SYSTEM_L2_MEM_INT_RECORD1_REG register
 *  NA
 */
#define HP_SYSTEM_L2_MEM_INT_RECORD1_REG (DR_REG_HP_SYS_BASE + 0xb4)
/** HP_SYSTEM_REG_L2_MEM_ECC_ERR_INT_ADDR : RO; bitpos: [14:0]; default: 0;
 *  NA
 */
#define HP_SYSTEM_REG_L2_MEM_ECC_ERR_INT_ADDR    0x00007FFFU
#define HP_SYSTEM_REG_L2_MEM_ECC_ERR_INT_ADDR_M  (HP_SYSTEM_REG_L2_MEM_ECC_ERR_INT_ADDR_V << HP_SYSTEM_REG_L2_MEM_ECC_ERR_INT_ADDR_S)
#define HP_SYSTEM_REG_L2_MEM_ECC_ERR_INT_ADDR_V  0x00007FFFU
#define HP_SYSTEM_REG_L2_MEM_ECC_ERR_INT_ADDR_S  0
/** HP_SYSTEM_REG_L2_MEM_ECC_ONE_BIT_ERR : RO; bitpos: [15]; default: 0;
 *  NA
 */
#define HP_SYSTEM_REG_L2_MEM_ECC_ONE_BIT_ERR    (BIT(15))
#define HP_SYSTEM_REG_L2_MEM_ECC_ONE_BIT_ERR_M  (HP_SYSTEM_REG_L2_MEM_ECC_ONE_BIT_ERR_V << HP_SYSTEM_REG_L2_MEM_ECC_ONE_BIT_ERR_S)
#define HP_SYSTEM_REG_L2_MEM_ECC_ONE_BIT_ERR_V  0x00000001U
#define HP_SYSTEM_REG_L2_MEM_ECC_ONE_BIT_ERR_S  15
/** HP_SYSTEM_REG_L2_MEM_ECC_TWO_BIT_ERR : RO; bitpos: [16]; default: 0;
 *  NA
 */
#define HP_SYSTEM_REG_L2_MEM_ECC_TWO_BIT_ERR    (BIT(16))
#define HP_SYSTEM_REG_L2_MEM_ECC_TWO_BIT_ERR_M  (HP_SYSTEM_REG_L2_MEM_ECC_TWO_BIT_ERR_V << HP_SYSTEM_REG_L2_MEM_ECC_TWO_BIT_ERR_S)
#define HP_SYSTEM_REG_L2_MEM_ECC_TWO_BIT_ERR_V  0x00000001U
#define HP_SYSTEM_REG_L2_MEM_ECC_TWO_BIT_ERR_S  16
/** HP_SYSTEM_REG_L2_MEM_ECC_ERR_BIT : RO; bitpos: [25:17]; default: 0;
 *  NA
 */
#define HP_SYSTEM_REG_L2_MEM_ECC_ERR_BIT    0x000001FFU
#define HP_SYSTEM_REG_L2_MEM_ECC_ERR_BIT_M  (HP_SYSTEM_REG_L2_MEM_ECC_ERR_BIT_V << HP_SYSTEM_REG_L2_MEM_ECC_ERR_BIT_S)
#define HP_SYSTEM_REG_L2_MEM_ECC_ERR_BIT_V  0x000001FFU
#define HP_SYSTEM_REG_L2_MEM_ECC_ERR_BIT_S  17
/** HP_SYSTEM_REG_L2_CACHE_ERR_BANK : RO; bitpos: [26]; default: 0;
 *  NA
 */
#define HP_SYSTEM_REG_L2_CACHE_ERR_BANK    (BIT(26))
#define HP_SYSTEM_REG_L2_CACHE_ERR_BANK_M  (HP_SYSTEM_REG_L2_CACHE_ERR_BANK_V << HP_SYSTEM_REG_L2_CACHE_ERR_BANK_S)
#define HP_SYSTEM_REG_L2_CACHE_ERR_BANK_V  0x00000001U
#define HP_SYSTEM_REG_L2_CACHE_ERR_BANK_S  26

/** HP_SYSTEM_L2_MEM_L2_CACHE_ECC_REG register
 *  NA
 */
#define HP_SYSTEM_L2_MEM_L2_CACHE_ECC_REG (DR_REG_HP_SYS_BASE + 0xc4)
/** HP_SYSTEM_REG_L2_CACHE_ECC_EN : R/W; bitpos: [0]; default: 0;
 *  NA
 */
#define HP_SYSTEM_REG_L2_CACHE_ECC_EN    (BIT(0))
#define HP_SYSTEM_REG_L2_CACHE_ECC_EN_M  (HP_SYSTEM_REG_L2_CACHE_ECC_EN_V << HP_SYSTEM_REG_L2_CACHE_ECC_EN_S)
#define HP_SYSTEM_REG_L2_CACHE_ECC_EN_V  0x00000001U
#define HP_SYSTEM_REG_L2_CACHE_ECC_EN_S  0

/** HP_SYSTEM_L1CACHE_BUS0_ID_REG register
 *  NA
 */
#define HP_SYSTEM_L1CACHE_BUS0_ID_REG (DR_REG_HP_SYS_BASE + 0xc8)
/** HP_SYSTEM_REG_L1_CACHE_BUS0_ID : R/W; bitpos: [3:0]; default: 0;
 *  NA
 */
#define HP_SYSTEM_REG_L1_CACHE_BUS0_ID    0x0000000FU
#define HP_SYSTEM_REG_L1_CACHE_BUS0_ID_M  (HP_SYSTEM_REG_L1_CACHE_BUS0_ID_V << HP_SYSTEM_REG_L1_CACHE_BUS0_ID_S)
#define HP_SYSTEM_REG_L1_CACHE_BUS0_ID_V  0x0000000FU
#define HP_SYSTEM_REG_L1_CACHE_BUS0_ID_S  0

/** HP_SYSTEM_L1CACHE_BUS1_ID_REG register
 *  NA
 */
#define HP_SYSTEM_L1CACHE_BUS1_ID_REG (DR_REG_HP_SYS_BASE + 0xcc)
/** HP_SYSTEM_REG_L1_CACHE_BUS1_ID : R/W; bitpos: [3:0]; default: 0;
 *  NA
 */
#define HP_SYSTEM_REG_L1_CACHE_BUS1_ID    0x0000000FU
#define HP_SYSTEM_REG_L1_CACHE_BUS1_ID_M  (HP_SYSTEM_REG_L1_CACHE_BUS1_ID_V << HP_SYSTEM_REG_L1_CACHE_BUS1_ID_S)
#define HP_SYSTEM_REG_L1_CACHE_BUS1_ID_V  0x0000000FU
#define HP_SYSTEM_REG_L1_CACHE_BUS1_ID_S  0

/** HP_SYSTEM_L2_MEM_RDN_ECO_CS_REG register
 *  NA
 */
#define HP_SYSTEM_L2_MEM_RDN_ECO_CS_REG (DR_REG_HP_SYS_BASE + 0xd8)
/** HP_SYSTEM_REG_L2_MEM_RDN_ECO_EN : R/W; bitpos: [0]; default: 0;
 *  NA
 */
#define HP_SYSTEM_REG_L2_MEM_RDN_ECO_EN    (BIT(0))
#define HP_SYSTEM_REG_L2_MEM_RDN_ECO_EN_M  (HP_SYSTEM_REG_L2_MEM_RDN_ECO_EN_V << HP_SYSTEM_REG_L2_MEM_RDN_ECO_EN_S)
#define HP_SYSTEM_REG_L2_MEM_RDN_ECO_EN_V  0x00000001U
#define HP_SYSTEM_REG_L2_MEM_RDN_ECO_EN_S  0
/** HP_SYSTEM_REG_L2_MEM_RDN_ECO_RESULT : RO; bitpos: [1]; default: 0;
 *  NA
 */
#define HP_SYSTEM_REG_L2_MEM_RDN_ECO_RESULT    (BIT(1))
#define HP_SYSTEM_REG_L2_MEM_RDN_ECO_RESULT_M  (HP_SYSTEM_REG_L2_MEM_RDN_ECO_RESULT_V << HP_SYSTEM_REG_L2_MEM_RDN_ECO_RESULT_S)
#define HP_SYSTEM_REG_L2_MEM_RDN_ECO_RESULT_V  0x00000001U
#define HP_SYSTEM_REG_L2_MEM_RDN_ECO_RESULT_S  1

/** HP_SYSTEM_L2_MEM_RDN_ECO_LOW_REG register
 *  NA
 */
#define HP_SYSTEM_L2_MEM_RDN_ECO_LOW_REG (DR_REG_HP_SYS_BASE + 0xdc)
/** HP_SYSTEM_REG_L2_MEM_RDN_ECO_LOW : R/W; bitpos: [31:0]; default: 0;
 *  NA
 */
#define HP_SYSTEM_REG_L2_MEM_RDN_ECO_LOW    0xFFFFFFFFU
#define HP_SYSTEM_REG_L2_MEM_RDN_ECO_LOW_M  (HP_SYSTEM_REG_L2_MEM_RDN_ECO_LOW_V << HP_SYSTEM_REG_L2_MEM_RDN_ECO_LOW_S)
#define HP_SYSTEM_REG_L2_MEM_RDN_ECO_LOW_V  0xFFFFFFFFU
#define HP_SYSTEM_REG_L2_MEM_RDN_ECO_LOW_S  0

/** HP_SYSTEM_L2_MEM_RDN_ECO_HIGH_REG register
 *  NA
 */
#define HP_SYSTEM_L2_MEM_RDN_ECO_HIGH_REG (DR_REG_HP_SYS_BASE + 0xe0)
/** HP_SYSTEM_REG_L2_MEM_RDN_ECO_HIGH : R/W; bitpos: [31:0]; default: 4294967295;
 *  NA
 */
#define HP_SYSTEM_REG_L2_MEM_RDN_ECO_HIGH    0xFFFFFFFFU
#define HP_SYSTEM_REG_L2_MEM_RDN_ECO_HIGH_M  (HP_SYSTEM_REG_L2_MEM_RDN_ECO_HIGH_V << HP_SYSTEM_REG_L2_MEM_RDN_ECO_HIGH_S)
#define HP_SYSTEM_REG_L2_MEM_RDN_ECO_HIGH_V  0xFFFFFFFFU
#define HP_SYSTEM_REG_L2_MEM_RDN_ECO_HIGH_S  0

/** HP_SYSTEM_TCM_RDN_ECO_CS_REG register
 *  NA
 */
#define HP_SYSTEM_TCM_RDN_ECO_CS_REG (DR_REG_HP_SYS_BASE + 0xe4)
/** HP_SYSTEM_REG_HP_SYSTEM_TCM_RDN_ECO_EN : R/W; bitpos: [0]; default: 0;
 *  NA
 */
#define HP_SYSTEM_REG_HP_SYSTEM_TCM_RDN_ECO_EN    (BIT(0))
#define HP_SYSTEM_REG_HP_SYSTEM_TCM_RDN_ECO_EN_M  (HP_SYSTEM_REG_HP_SYSTEM_TCM_RDN_ECO_EN_V << HP_SYSTEM_REG_HP_SYSTEM_TCM_RDN_ECO_EN_S)
#define HP_SYSTEM_REG_HP_SYSTEM_TCM_RDN_ECO_EN_V  0x00000001U
#define HP_SYSTEM_REG_HP_SYSTEM_TCM_RDN_ECO_EN_S  0
/** HP_SYSTEM_REG_HP_SYSTEM_TCM_RDN_ECO_RESULT : RO; bitpos: [1]; default: 0;
 *  NA
 */
#define HP_SYSTEM_REG_HP_SYSTEM_TCM_RDN_ECO_RESULT    (BIT(1))
#define HP_SYSTEM_REG_HP_SYSTEM_TCM_RDN_ECO_RESULT_M  (HP_SYSTEM_REG_HP_SYSTEM_TCM_RDN_ECO_RESULT_V << HP_SYSTEM_REG_HP_SYSTEM_TCM_RDN_ECO_RESULT_S)
#define HP_SYSTEM_REG_HP_SYSTEM_TCM_RDN_ECO_RESULT_V  0x00000001U
#define HP_SYSTEM_REG_HP_SYSTEM_TCM_RDN_ECO_RESULT_S  1

/** HP_SYSTEM_TCM_RDN_ECO_LOW_REG register
 *  NA
 */
#define HP_SYSTEM_TCM_RDN_ECO_LOW_REG (DR_REG_HP_SYS_BASE + 0xe8)
/** HP_SYSTEM_REG_HP_SYSTEM_TCM_RDN_ECO_LOW : R/W; bitpos: [31:0]; default: 0;
 *  NA
 */
#define HP_SYSTEM_REG_HP_SYSTEM_TCM_RDN_ECO_LOW    0xFFFFFFFFU
#define HP_SYSTEM_REG_HP_SYSTEM_TCM_RDN_ECO_LOW_M  (HP_SYSTEM_REG_HP_SYSTEM_TCM_RDN_ECO_LOW_V << HP_SYSTEM_REG_HP_SYSTEM_TCM_RDN_ECO_LOW_S)
#define HP_SYSTEM_REG_HP_SYSTEM_TCM_RDN_ECO_LOW_V  0xFFFFFFFFU
#define HP_SYSTEM_REG_HP_SYSTEM_TCM_RDN_ECO_LOW_S  0

/** HP_SYSTEM_TCM_RDN_ECO_HIGH_REG register
 *  NA
 */
#define HP_SYSTEM_TCM_RDN_ECO_HIGH_REG (DR_REG_HP_SYS_BASE + 0xec)
/** HP_SYSTEM_REG_HP_SYSTEM_TCM_RDN_ECO_HIGH : R/W; bitpos: [31:0]; default: 4294967295;
 *  NA
 */
#define HP_SYSTEM_REG_HP_SYSTEM_TCM_RDN_ECO_HIGH    0xFFFFFFFFU
#define HP_SYSTEM_REG_HP_SYSTEM_TCM_RDN_ECO_HIGH_M  (HP_SYSTEM_REG_HP_SYSTEM_TCM_RDN_ECO_HIGH_V << HP_SYSTEM_REG_HP_SYSTEM_TCM_RDN_ECO_HIGH_S)
#define HP_SYSTEM_REG_HP_SYSTEM_TCM_RDN_ECO_HIGH_V  0xFFFFFFFFU
#define HP_SYSTEM_REG_HP_SYSTEM_TCM_RDN_ECO_HIGH_S  0

/** HP_SYSTEM_GPIO_DED_HOLD_CTRL_REG register
 *  NA
 */
#define HP_SYSTEM_GPIO_DED_HOLD_CTRL_REG (DR_REG_HP_SYS_BASE + 0xf0)
/** HP_SYSTEM_REG_GPIO_DED_HOLD : R/W; bitpos: [25:0]; default: 0;
 *  hold control for gpio63~56
 */
#define HP_SYSTEM_REG_GPIO_DED_HOLD    0x03FFFFFFU
#define HP_SYSTEM_REG_GPIO_DED_HOLD_M  (HP_SYSTEM_REG_GPIO_DED_HOLD_V << HP_SYSTEM_REG_GPIO_DED_HOLD_S)
#define HP_SYSTEM_REG_GPIO_DED_HOLD_V  0x03FFFFFFU
#define HP_SYSTEM_REG_GPIO_DED_HOLD_S  0

/** HP_SYSTEM_L2_MEM_SW_ECC_BWE_MASK_REG register
 *  NA
 */
#define HP_SYSTEM_L2_MEM_SW_ECC_BWE_MASK_REG (DR_REG_HP_SYS_BASE + 0xf4)
/** HP_SYSTEM_REG_L2_MEM_SW_ECC_BWE_MASK_CTRL : R/W; bitpos: [0]; default: 0;
 *  Set 1 to mask bwe hamming code bit
 */
#define HP_SYSTEM_REG_L2_MEM_SW_ECC_BWE_MASK_CTRL    (BIT(0))
#define HP_SYSTEM_REG_L2_MEM_SW_ECC_BWE_MASK_CTRL_M  (HP_SYSTEM_REG_L2_MEM_SW_ECC_BWE_MASK_CTRL_V << HP_SYSTEM_REG_L2_MEM_SW_ECC_BWE_MASK_CTRL_S)
#define HP_SYSTEM_REG_L2_MEM_SW_ECC_BWE_MASK_CTRL_V  0x00000001U
#define HP_SYSTEM_REG_L2_MEM_SW_ECC_BWE_MASK_CTRL_S  0

/** HP_SYSTEM_USB20OTG_MEM_CTRL_REG register
 *  NA
 */
#define HP_SYSTEM_USB20OTG_MEM_CTRL_REG (DR_REG_HP_SYS_BASE + 0xf8)
/** HP_SYSTEM_REG_USB20_MEM_CLK_FORCE_ON : R/W; bitpos: [0]; default: 0;
 *  NA
 */
#define HP_SYSTEM_REG_USB20_MEM_CLK_FORCE_ON    (BIT(0))
#define HP_SYSTEM_REG_USB20_MEM_CLK_FORCE_ON_M  (HP_SYSTEM_REG_USB20_MEM_CLK_FORCE_ON_V << HP_SYSTEM_REG_USB20_MEM_CLK_FORCE_ON_S)
#define HP_SYSTEM_REG_USB20_MEM_CLK_FORCE_ON_V  0x00000001U
#define HP_SYSTEM_REG_USB20_MEM_CLK_FORCE_ON_S  0

/** HP_SYSTEM_TCM_INT_RAW_REG register
 *  need_des
 */
#define HP_SYSTEM_TCM_INT_RAW_REG (DR_REG_HP_SYS_BASE + 0xfc)
/** HP_SYSTEM_TCM_PARITY_ERR_INT_RAW : R/WTC/SS; bitpos: [31]; default: 0;
 *  need_des
 */
#define HP_SYSTEM_TCM_PARITY_ERR_INT_RAW    (BIT(31))
#define HP_SYSTEM_TCM_PARITY_ERR_INT_RAW_M  (HP_SYSTEM_TCM_PARITY_ERR_INT_RAW_V << HP_SYSTEM_TCM_PARITY_ERR_INT_RAW_S)
#define HP_SYSTEM_TCM_PARITY_ERR_INT_RAW_V  0x00000001U
#define HP_SYSTEM_TCM_PARITY_ERR_INT_RAW_S  31

/** HP_SYSTEM_TCM_INT_ST_REG register
 *  need_des
 */
#define HP_SYSTEM_TCM_INT_ST_REG (DR_REG_HP_SYS_BASE + 0x100)
/** HP_SYSTEM_TCM_PARITY_ERR_INT_ST : RO; bitpos: [31]; default: 0;
 *  need_des
 */
#define HP_SYSTEM_TCM_PARITY_ERR_INT_ST    (BIT(31))
#define HP_SYSTEM_TCM_PARITY_ERR_INT_ST_M  (HP_SYSTEM_TCM_PARITY_ERR_INT_ST_V << HP_SYSTEM_TCM_PARITY_ERR_INT_ST_S)
#define HP_SYSTEM_TCM_PARITY_ERR_INT_ST_V  0x00000001U
#define HP_SYSTEM_TCM_PARITY_ERR_INT_ST_S  31

/** HP_SYSTEM_TCM_INT_ENA_REG register
 *  need_des
 */
#define HP_SYSTEM_TCM_INT_ENA_REG (DR_REG_HP_SYS_BASE + 0x104)
/** HP_SYSTEM_TCM_PARITY_ERR_INT_ENA : R/W; bitpos: [31]; default: 0;
 *  need_des
 */
#define HP_SYSTEM_TCM_PARITY_ERR_INT_ENA    (BIT(31))
#define HP_SYSTEM_TCM_PARITY_ERR_INT_ENA_M  (HP_SYSTEM_TCM_PARITY_ERR_INT_ENA_V << HP_SYSTEM_TCM_PARITY_ERR_INT_ENA_S)
#define HP_SYSTEM_TCM_PARITY_ERR_INT_ENA_V  0x00000001U
#define HP_SYSTEM_TCM_PARITY_ERR_INT_ENA_S  31

/** HP_SYSTEM_TCM_INT_CLR_REG register
 *  need_des
 */
#define HP_SYSTEM_TCM_INT_CLR_REG (DR_REG_HP_SYS_BASE + 0x108)
/** HP_SYSTEM_TCM_PARITY_ERR_INT_CLR : WT; bitpos: [31]; default: 0;
 *  need_des
 */
#define HP_SYSTEM_TCM_PARITY_ERR_INT_CLR    (BIT(31))
#define HP_SYSTEM_TCM_PARITY_ERR_INT_CLR_M  (HP_SYSTEM_TCM_PARITY_ERR_INT_CLR_V << HP_SYSTEM_TCM_PARITY_ERR_INT_CLR_S)
#define HP_SYSTEM_TCM_PARITY_ERR_INT_CLR_V  0x00000001U
#define HP_SYSTEM_TCM_PARITY_ERR_INT_CLR_S  31

/** HP_SYSTEM_TCM_PARITY_INT_RECORD_REG register
 *  need_des
 */
#define HP_SYSTEM_TCM_PARITY_INT_RECORD_REG (DR_REG_HP_SYS_BASE + 0x10c)
/** HP_SYSTEM_TCM_PARITY_ERR_INT_ADDR : RO; bitpos: [12:0]; default: 0;
 *  hp tcm_parity_err_addr
 */
#define HP_SYSTEM_TCM_PARITY_ERR_INT_ADDR    0x00001FFFU
#define HP_SYSTEM_TCM_PARITY_ERR_INT_ADDR_M  (HP_SYSTEM_TCM_PARITY_ERR_INT_ADDR_V << HP_SYSTEM_TCM_PARITY_ERR_INT_ADDR_S)
#define HP_SYSTEM_TCM_PARITY_ERR_INT_ADDR_V  0x00001FFFU
#define HP_SYSTEM_TCM_PARITY_ERR_INT_ADDR_S  0

/** HP_SYSTEM_L1_CACHE_PWR_CTRL_REG register
 *  NA
 */
#define HP_SYSTEM_L1_CACHE_PWR_CTRL_REG (DR_REG_HP_SYS_BASE + 0x110)
/** HP_SYSTEM_REG_L1_CACHE_MEM_FO : R/W; bitpos: [5:0]; default: 0;
 *  need_des
 */
#define HP_SYSTEM_REG_L1_CACHE_MEM_FO    0x0000003FU
#define HP_SYSTEM_REG_L1_CACHE_MEM_FO_M  (HP_SYSTEM_REG_L1_CACHE_MEM_FO_V << HP_SYSTEM_REG_L1_CACHE_MEM_FO_S)
#define HP_SYSTEM_REG_L1_CACHE_MEM_FO_V  0x0000003FU
#define HP_SYSTEM_REG_L1_CACHE_MEM_FO_S  0

/** HP_SYSTEM_L2_CACHE_PWR_CTRL_REG register
 *  NA
 */
#define HP_SYSTEM_L2_CACHE_PWR_CTRL_REG (DR_REG_HP_SYS_BASE + 0x114)
/** HP_SYSTEM_REG_L2_CACHE_MEM_FO : R/W; bitpos: [1:0]; default: 0;
 *  need_des
 */
#define HP_SYSTEM_REG_L2_CACHE_MEM_FO    0x00000003U
#define HP_SYSTEM_REG_L2_CACHE_MEM_FO_M  (HP_SYSTEM_REG_L2_CACHE_MEM_FO_V << HP_SYSTEM_REG_L2_CACHE_MEM_FO_S)
#define HP_SYSTEM_REG_L2_CACHE_MEM_FO_V  0x00000003U
#define HP_SYSTEM_REG_L2_CACHE_MEM_FO_S  0

/** HP_SYSTEM_CPU_WAITI_CONF_REG register
 *  CPU_WAITI configuration register
 */
#define HP_SYSTEM_CPU_WAITI_CONF_REG (DR_REG_HP_SYS_BASE + 0x118)
/** HP_SYSTEM_CPU_WAIT_MODE_FORCE_ON : R/W; bitpos: [0]; default: 1;
 *  Set 1 to force cpu_waiti_clk enable.
 */
#define HP_SYSTEM_CPU_WAIT_MODE_FORCE_ON    (BIT(0))
#define HP_SYSTEM_CPU_WAIT_MODE_FORCE_ON_M  (HP_SYSTEM_CPU_WAIT_MODE_FORCE_ON_V << HP_SYSTEM_CPU_WAIT_MODE_FORCE_ON_S)
#define HP_SYSTEM_CPU_WAIT_MODE_FORCE_ON_V  0x00000001U
#define HP_SYSTEM_CPU_WAIT_MODE_FORCE_ON_S  0
/** HP_SYSTEM_CPU_WAITI_DELAY_NUM : R/W; bitpos: [4:1]; default: 0;
 *  This field used to set delay cycle when cpu enter waiti mode, after delay waiti_clk
 *  will close
 */
#define HP_SYSTEM_CPU_WAITI_DELAY_NUM    0x0000000FU
#define HP_SYSTEM_CPU_WAITI_DELAY_NUM_M  (HP_SYSTEM_CPU_WAITI_DELAY_NUM_V << HP_SYSTEM_CPU_WAITI_DELAY_NUM_S)
#define HP_SYSTEM_CPU_WAITI_DELAY_NUM_V  0x0000000FU
#define HP_SYSTEM_CPU_WAITI_DELAY_NUM_S  1

/** HP_SYSTEM_CORE_DEBUG_RUNSTALL_CONF_REG register
 *  Core Debug runstall configure register
 */
#define HP_SYSTEM_CORE_DEBUG_RUNSTALL_CONF_REG (DR_REG_HP_SYS_BASE + 0x11c)
/** HP_SYSTEM_CORE_DEBUG_RUNSTALL_ENABLE : R/W; bitpos: [0]; default: 0;
 *  Set this field to 1 to enable debug runstall feature between HP-core and LP-core.
 */
#define HP_SYSTEM_CORE_DEBUG_RUNSTALL_ENABLE    (BIT(0))
#define HP_SYSTEM_CORE_DEBUG_RUNSTALL_ENABLE_M  (HP_SYSTEM_CORE_DEBUG_RUNSTALL_ENABLE_V << HP_SYSTEM_CORE_DEBUG_RUNSTALL_ENABLE_S)
#define HP_SYSTEM_CORE_DEBUG_RUNSTALL_ENABLE_V  0x00000001U
#define HP_SYSTEM_CORE_DEBUG_RUNSTALL_ENABLE_S  0

/** HP_SYSTEM_CORE_AHB_TIMEOUT_REG register
 *  need_des
 */
#define HP_SYSTEM_CORE_AHB_TIMEOUT_REG (DR_REG_HP_SYS_BASE + 0x120)
/** HP_SYSTEM_CORE_AHB_TIMEOUT_EN : R/W; bitpos: [0]; default: 1;
 *  set this field to 1 to enable hp core0&1 ahb timeout handle
 */
#define HP_SYSTEM_CORE_AHB_TIMEOUT_EN    (BIT(0))
#define HP_SYSTEM_CORE_AHB_TIMEOUT_EN_M  (HP_SYSTEM_CORE_AHB_TIMEOUT_EN_V << HP_SYSTEM_CORE_AHB_TIMEOUT_EN_S)
#define HP_SYSTEM_CORE_AHB_TIMEOUT_EN_V  0x00000001U
#define HP_SYSTEM_CORE_AHB_TIMEOUT_EN_S  0
/** HP_SYSTEM_CORE_AHB_TIMEOUT_THRES : R/W; bitpos: [16:1]; default: 65535;
 *  This field used to set hp core0&1 ahb bus timeout  threshold
 */
#define HP_SYSTEM_CORE_AHB_TIMEOUT_THRES    0x0000FFFFU
#define HP_SYSTEM_CORE_AHB_TIMEOUT_THRES_M  (HP_SYSTEM_CORE_AHB_TIMEOUT_THRES_V << HP_SYSTEM_CORE_AHB_TIMEOUT_THRES_S)
#define HP_SYSTEM_CORE_AHB_TIMEOUT_THRES_V  0x0000FFFFU
#define HP_SYSTEM_CORE_AHB_TIMEOUT_THRES_S  1

/** HP_SYSTEM_CORE_IBUS_TIMEOUT_REG register
 *  need_des
 */
#define HP_SYSTEM_CORE_IBUS_TIMEOUT_REG (DR_REG_HP_SYS_BASE + 0x124)
/** HP_SYSTEM_CORE_IBUS_TIMEOUT_EN : R/W; bitpos: [0]; default: 1;
 *  set this field to 1 to enable hp core0&1 ibus timeout handle
 */
#define HP_SYSTEM_CORE_IBUS_TIMEOUT_EN    (BIT(0))
#define HP_SYSTEM_CORE_IBUS_TIMEOUT_EN_M  (HP_SYSTEM_CORE_IBUS_TIMEOUT_EN_V << HP_SYSTEM_CORE_IBUS_TIMEOUT_EN_S)
#define HP_SYSTEM_CORE_IBUS_TIMEOUT_EN_V  0x00000001U
#define HP_SYSTEM_CORE_IBUS_TIMEOUT_EN_S  0
/** HP_SYSTEM_CORE_IBUS_TIMEOUT_THRES : R/W; bitpos: [16:1]; default: 65535;
 *  This field used to set hp core0&1 ibus timeout  threshold
 */
#define HP_SYSTEM_CORE_IBUS_TIMEOUT_THRES    0x0000FFFFU
#define HP_SYSTEM_CORE_IBUS_TIMEOUT_THRES_M  (HP_SYSTEM_CORE_IBUS_TIMEOUT_THRES_V << HP_SYSTEM_CORE_IBUS_TIMEOUT_THRES_S)
#define HP_SYSTEM_CORE_IBUS_TIMEOUT_THRES_V  0x0000FFFFU
#define HP_SYSTEM_CORE_IBUS_TIMEOUT_THRES_S  1

/** HP_SYSTEM_CORE_DBUS_TIMEOUT_REG register
 *  need_des
 */
#define HP_SYSTEM_CORE_DBUS_TIMEOUT_REG (DR_REG_HP_SYS_BASE + 0x128)
/** HP_SYSTEM_CORE_DBUS_TIMEOUT_EN : R/W; bitpos: [0]; default: 1;
 *  set this field to 1 to enable hp core0&1 dbus timeout handle
 */
#define HP_SYSTEM_CORE_DBUS_TIMEOUT_EN    (BIT(0))
#define HP_SYSTEM_CORE_DBUS_TIMEOUT_EN_M  (HP_SYSTEM_CORE_DBUS_TIMEOUT_EN_V << HP_SYSTEM_CORE_DBUS_TIMEOUT_EN_S)
#define HP_SYSTEM_CORE_DBUS_TIMEOUT_EN_V  0x00000001U
#define HP_SYSTEM_CORE_DBUS_TIMEOUT_EN_S  0
/** HP_SYSTEM_CORE_DBUS_TIMEOUT_THRES : R/W; bitpos: [16:1]; default: 65535;
 *  This field used to set hp core0&1 dbus timeout  threshold
 */
#define HP_SYSTEM_CORE_DBUS_TIMEOUT_THRES    0x0000FFFFU
#define HP_SYSTEM_CORE_DBUS_TIMEOUT_THRES_M  (HP_SYSTEM_CORE_DBUS_TIMEOUT_THRES_V << HP_SYSTEM_CORE_DBUS_TIMEOUT_THRES_S)
#define HP_SYSTEM_CORE_DBUS_TIMEOUT_THRES_V  0x0000FFFFU
#define HP_SYSTEM_CORE_DBUS_TIMEOUT_THRES_S  1

/** HP_SYSTEM_ICM_CPU_H2X_CFG_REG register
 *  need_des
 */
#define HP_SYSTEM_ICM_CPU_H2X_CFG_REG (DR_REG_HP_SYS_BASE + 0x138)
/** HP_SYSTEM_CPU_ICM_H2X_POST_WR_EN : R/W; bitpos: [0]; default: 1;
 *  need_des
 */
#define HP_SYSTEM_CPU_ICM_H2X_POST_WR_EN    (BIT(0))
#define HP_SYSTEM_CPU_ICM_H2X_POST_WR_EN_M  (HP_SYSTEM_CPU_ICM_H2X_POST_WR_EN_V << HP_SYSTEM_CPU_ICM_H2X_POST_WR_EN_S)
#define HP_SYSTEM_CPU_ICM_H2X_POST_WR_EN_V  0x00000001U
#define HP_SYSTEM_CPU_ICM_H2X_POST_WR_EN_S  0
/** HP_SYSTEM_CPU_ICM_H2X_CUT_THROUGH_EN : R/W; bitpos: [1]; default: 1;
 *  need_des
 */
#define HP_SYSTEM_CPU_ICM_H2X_CUT_THROUGH_EN    (BIT(1))
#define HP_SYSTEM_CPU_ICM_H2X_CUT_THROUGH_EN_M  (HP_SYSTEM_CPU_ICM_H2X_CUT_THROUGH_EN_V << HP_SYSTEM_CPU_ICM_H2X_CUT_THROUGH_EN_S)
#define HP_SYSTEM_CPU_ICM_H2X_CUT_THROUGH_EN_V  0x00000001U
#define HP_SYSTEM_CPU_ICM_H2X_CUT_THROUGH_EN_S  1
/** HP_SYSTEM_CPU_ICM_H2X_BRIDGE_BUSY : RO; bitpos: [2]; default: 0;
 *  need_des
 */
#define HP_SYSTEM_CPU_ICM_H2X_BRIDGE_BUSY    (BIT(2))
#define HP_SYSTEM_CPU_ICM_H2X_BRIDGE_BUSY_M  (HP_SYSTEM_CPU_ICM_H2X_BRIDGE_BUSY_V << HP_SYSTEM_CPU_ICM_H2X_BRIDGE_BUSY_S)
#define HP_SYSTEM_CPU_ICM_H2X_BRIDGE_BUSY_V  0x00000001U
#define HP_SYSTEM_CPU_ICM_H2X_BRIDGE_BUSY_S  2

/** HP_SYSTEM_PERI1_APB_POSTW_EN_REG register
 *  NA
 */
#define HP_SYSTEM_PERI1_APB_POSTW_EN_REG (DR_REG_HP_SYS_BASE + 0x13c)
/** HP_SYSTEM_PERI1_APB_POSTW_EN : R/W; bitpos: [0]; default: 0;
 *  HP_SYSTEM_peri1 apb register interface post write enable, 1 will speed up write, but will
 *  take some time to update value to register
 */
#define HP_SYSTEM_PERI1_APB_POSTW_EN    (BIT(0))
#define HP_SYSTEM_PERI1_APB_POSTW_EN_M  (HP_SYSTEM_PERI1_APB_POSTW_EN_V << HP_SYSTEM_PERI1_APB_POSTW_EN_S)
#define HP_SYSTEM_PERI1_APB_POSTW_EN_V  0x00000001U
#define HP_SYSTEM_PERI1_APB_POSTW_EN_S  0

/** HP_SYSTEM_BITSCRAMBLER_PERI_SEL_REG register
 *  Bitscrambler Peri Sel
 */
#define HP_SYSTEM_BITSCRAMBLER_PERI_SEL_REG (DR_REG_HP_SYS_BASE + 0x140)
/** HP_SYSTEM_BITSCRAMBLER_PERI_RX_SEL : R/W; bitpos: [3:0]; default: 15;
 *  Set  this field to sel peri with DMA RX interface to connect with bitscrambler: 4'h0
 *  : lcd_cam, 4'h1: gpspi2, 4'h2: gpspi3, 4'h3: parl_io, 4'h4: aes, 4'h5: sha, 4'h6:
 *  adc, 4'h7: i2s0, 4'h8: i2s1, 4'h9: i2s2, 4'ha: i3c_mst, 4'hb: uhci0, 4'hc: RMT,
 *  else : none
 */
#define HP_SYSTEM_BITSCRAMBLER_PERI_RX_SEL    0x0000000FU
#define HP_SYSTEM_BITSCRAMBLER_PERI_RX_SEL_M  (HP_SYSTEM_BITSCRAMBLER_PERI_RX_SEL_V << HP_SYSTEM_BITSCRAMBLER_PERI_RX_SEL_S)
#define HP_SYSTEM_BITSCRAMBLER_PERI_RX_SEL_V  0x0000000FU
#define HP_SYSTEM_BITSCRAMBLER_PERI_RX_SEL_S  0
/** HP_SYSTEM_BITSCRAMBLER_PERI_TX_SEL : R/W; bitpos: [7:4]; default: 15;
 *  Set  this field to sel peri with DMA TX interface to connect with bitscrambler: 4'h0
 *  : lcd_cam, 4'h1: gpspi2, 4'h2: gpspi3, 4'h3: parl_io, 4'h4: aes, 4'h5: sha, 4'h6:
 *  adc, 4'h7: i2s0, 4'h8: i2s1, 4'h9: i2s2, 4'ha: i3c_mst, 4'hb: uhci0, 4'hc: RMT,
 *  else : none
 */
#define HP_SYSTEM_BITSCRAMBLER_PERI_TX_SEL    0x0000000FU
#define HP_SYSTEM_BITSCRAMBLER_PERI_TX_SEL_M  (HP_SYSTEM_BITSCRAMBLER_PERI_TX_SEL_V << HP_SYSTEM_BITSCRAMBLER_PERI_TX_SEL_S)
#define HP_SYSTEM_BITSCRAMBLER_PERI_TX_SEL_V  0x0000000FU
#define HP_SYSTEM_BITSCRAMBLER_PERI_TX_SEL_S  4

/** HP_SYSTEM_APB_SYNC_POSTW_EN_REG register
 *  N/A
 */
#define HP_SYSTEM_APB_SYNC_POSTW_EN_REG (DR_REG_HP_SYS_BASE + 0x144)
/** HP_SYSTEM_GMAC_APB_POSTW_EN : R/W; bitpos: [0]; default: 0;
 *  N/A
 */
#define HP_SYSTEM_GMAC_APB_POSTW_EN    (BIT(0))
#define HP_SYSTEM_GMAC_APB_POSTW_EN_M  (HP_SYSTEM_GMAC_APB_POSTW_EN_V << HP_SYSTEM_GMAC_APB_POSTW_EN_S)
#define HP_SYSTEM_GMAC_APB_POSTW_EN_V  0x00000001U
#define HP_SYSTEM_GMAC_APB_POSTW_EN_S  0
/** HP_SYSTEM_DSI_HOST_APB_POSTW_EN : R/W; bitpos: [1]; default: 0;
 *  N/A
 */
#define HP_SYSTEM_DSI_HOST_APB_POSTW_EN    (BIT(1))
#define HP_SYSTEM_DSI_HOST_APB_POSTW_EN_M  (HP_SYSTEM_DSI_HOST_APB_POSTW_EN_V << HP_SYSTEM_DSI_HOST_APB_POSTW_EN_S)
#define HP_SYSTEM_DSI_HOST_APB_POSTW_EN_V  0x00000001U
#define HP_SYSTEM_DSI_HOST_APB_POSTW_EN_S  1
/** HP_SYSTEM_CSI_HOST_APB_SYNC_POSTW_EN : R/W; bitpos: [2]; default: 0;
 *  N/A
 */
#define HP_SYSTEM_CSI_HOST_APB_SYNC_POSTW_EN    (BIT(2))
#define HP_SYSTEM_CSI_HOST_APB_SYNC_POSTW_EN_M  (HP_SYSTEM_CSI_HOST_APB_SYNC_POSTW_EN_V << HP_SYSTEM_CSI_HOST_APB_SYNC_POSTW_EN_S)
#define HP_SYSTEM_CSI_HOST_APB_SYNC_POSTW_EN_V  0x00000001U
#define HP_SYSTEM_CSI_HOST_APB_SYNC_POSTW_EN_S  2
/** HP_SYSTEM_CSI_HOST_APB_ASYNC_POSTW_EN : R/W; bitpos: [3]; default: 0;
 *  N/A
 */
#define HP_SYSTEM_CSI_HOST_APB_ASYNC_POSTW_EN    (BIT(3))
#define HP_SYSTEM_CSI_HOST_APB_ASYNC_POSTW_EN_M  (HP_SYSTEM_CSI_HOST_APB_ASYNC_POSTW_EN_V << HP_SYSTEM_CSI_HOST_APB_ASYNC_POSTW_EN_S)
#define HP_SYSTEM_CSI_HOST_APB_ASYNC_POSTW_EN_V  0x00000001U
#define HP_SYSTEM_CSI_HOST_APB_ASYNC_POSTW_EN_S  3

/** HP_SYSTEM_GDMA_CTRL_REG register
 *  N/A
 */
#define HP_SYSTEM_GDMA_CTRL_REG (DR_REG_HP_SYS_BASE + 0x148)
/** HP_SYSTEM_DEBUG_CH_NUM : R/W; bitpos: [1:0]; default: 0;
 *  N/A
 */
#define HP_SYSTEM_DEBUG_CH_NUM    0x00000003U
#define HP_SYSTEM_DEBUG_CH_NUM_M  (HP_SYSTEM_DEBUG_CH_NUM_V << HP_SYSTEM_DEBUG_CH_NUM_S)
#define HP_SYSTEM_DEBUG_CH_NUM_V  0x00000003U
#define HP_SYSTEM_DEBUG_CH_NUM_S  0

/** HP_SYSTEM_GMAC_CTRL0_REG register
 *  N/A
 */
#define HP_SYSTEM_GMAC_CTRL0_REG (DR_REG_HP_SYS_BASE + 0x14c)
/** HP_SYSTEM_PTP_PPS : RO; bitpos: [0]; default: 0;
 *  N/A
 */
#define HP_SYSTEM_PTP_PPS    (BIT(0))
#define HP_SYSTEM_PTP_PPS_M  (HP_SYSTEM_PTP_PPS_V << HP_SYSTEM_PTP_PPS_S)
#define HP_SYSTEM_PTP_PPS_V  0x00000001U
#define HP_SYSTEM_PTP_PPS_S  0
/** HP_SYSTEM_SBD_FLOWCTRL : R/W; bitpos: [1]; default: 0;
 *  N/A
 */
#define HP_SYSTEM_SBD_FLOWCTRL    (BIT(1))
#define HP_SYSTEM_SBD_FLOWCTRL_M  (HP_SYSTEM_SBD_FLOWCTRL_V << HP_SYSTEM_SBD_FLOWCTRL_S)
#define HP_SYSTEM_SBD_FLOWCTRL_V  0x00000001U
#define HP_SYSTEM_SBD_FLOWCTRL_S  1
/** HP_SYSTEM_PHY_INTF_SEL : R/W; bitpos: [4:2]; default: 0;
 *  N/A
 */
#define HP_SYSTEM_PHY_INTF_SEL    0x00000007U
#define HP_SYSTEM_PHY_INTF_SEL_M  (HP_SYSTEM_PHY_INTF_SEL_V << HP_SYSTEM_PHY_INTF_SEL_S)
#define HP_SYSTEM_PHY_INTF_SEL_V  0x00000007U
#define HP_SYSTEM_PHY_INTF_SEL_S  2
/** HP_SYSTEM_GMAC_MEM_CLK_FORCE_ON : R/W; bitpos: [5]; default: 0;
 *  N/A
 */
#define HP_SYSTEM_GMAC_MEM_CLK_FORCE_ON    (BIT(5))
#define HP_SYSTEM_GMAC_MEM_CLK_FORCE_ON_M  (HP_SYSTEM_GMAC_MEM_CLK_FORCE_ON_V << HP_SYSTEM_GMAC_MEM_CLK_FORCE_ON_S)
#define HP_SYSTEM_GMAC_MEM_CLK_FORCE_ON_V  0x00000001U
#define HP_SYSTEM_GMAC_MEM_CLK_FORCE_ON_S  5
/** HP_SYSTEM_GMAC_RST_CLK_TX_N : RO; bitpos: [6]; default: 0;
 *  N/A
 */
#define HP_SYSTEM_GMAC_RST_CLK_TX_N    (BIT(6))
#define HP_SYSTEM_GMAC_RST_CLK_TX_N_M  (HP_SYSTEM_GMAC_RST_CLK_TX_N_V << HP_SYSTEM_GMAC_RST_CLK_TX_N_S)
#define HP_SYSTEM_GMAC_RST_CLK_TX_N_V  0x00000001U
#define HP_SYSTEM_GMAC_RST_CLK_TX_N_S  6
/** HP_SYSTEM_GMAC_RST_CLK_RX_N : RO; bitpos: [7]; default: 0;
 *  N/A
 */
#define HP_SYSTEM_GMAC_RST_CLK_RX_N    (BIT(7))
#define HP_SYSTEM_GMAC_RST_CLK_RX_N_M  (HP_SYSTEM_GMAC_RST_CLK_RX_N_V << HP_SYSTEM_GMAC_RST_CLK_RX_N_S)
#define HP_SYSTEM_GMAC_RST_CLK_RX_N_V  0x00000001U
#define HP_SYSTEM_GMAC_RST_CLK_RX_N_S  7

/** HP_SYSTEM_GMAC_CTRL1_REG register
 *  N/A
 */
#define HP_SYSTEM_GMAC_CTRL1_REG (DR_REG_HP_SYS_BASE + 0x150)
/** HP_SYSTEM_PTP_TIMESTAMP_L : RO; bitpos: [31:0]; default: 0;
 *  N/A
 */
#define HP_SYSTEM_PTP_TIMESTAMP_L    0xFFFFFFFFU
#define HP_SYSTEM_PTP_TIMESTAMP_L_M  (HP_SYSTEM_PTP_TIMESTAMP_L_V << HP_SYSTEM_PTP_TIMESTAMP_L_S)
#define HP_SYSTEM_PTP_TIMESTAMP_L_V  0xFFFFFFFFU
#define HP_SYSTEM_PTP_TIMESTAMP_L_S  0

/** HP_SYSTEM_GMAC_CTRL2_REG register
 *  N/A
 */
#define HP_SYSTEM_GMAC_CTRL2_REG (DR_REG_HP_SYS_BASE + 0x154)
/** HP_SYSTEM_PTP_TIMESTAMP_H : RO; bitpos: [31:0]; default: 0;
 *  N/A
 */
#define HP_SYSTEM_PTP_TIMESTAMP_H    0xFFFFFFFFU
#define HP_SYSTEM_PTP_TIMESTAMP_H_M  (HP_SYSTEM_PTP_TIMESTAMP_H_V << HP_SYSTEM_PTP_TIMESTAMP_H_S)
#define HP_SYSTEM_PTP_TIMESTAMP_H_V  0xFFFFFFFFU
#define HP_SYSTEM_PTP_TIMESTAMP_H_S  0

/** HP_SYSTEM_VPU_CTRL_REG register
 *  N/A
 */
#define HP_SYSTEM_VPU_CTRL_REG (DR_REG_HP_SYS_BASE + 0x158)
/** HP_SYSTEM_PPA_LSLP_MEM_PD : R/W; bitpos: [0]; default: 0;
 *  N/A
 */
#define HP_SYSTEM_PPA_LSLP_MEM_PD    (BIT(0))
#define HP_SYSTEM_PPA_LSLP_MEM_PD_M  (HP_SYSTEM_PPA_LSLP_MEM_PD_V << HP_SYSTEM_PPA_LSLP_MEM_PD_S)
#define HP_SYSTEM_PPA_LSLP_MEM_PD_V  0x00000001U
#define HP_SYSTEM_PPA_LSLP_MEM_PD_S  0
/** HP_SYSTEM_JPEG_SDSLP_MEM_PD : R/W; bitpos: [1]; default: 0;
 *  N/A
 */
#define HP_SYSTEM_JPEG_SDSLP_MEM_PD    (BIT(1))
#define HP_SYSTEM_JPEG_SDSLP_MEM_PD_M  (HP_SYSTEM_JPEG_SDSLP_MEM_PD_V << HP_SYSTEM_JPEG_SDSLP_MEM_PD_S)
#define HP_SYSTEM_JPEG_SDSLP_MEM_PD_V  0x00000001U
#define HP_SYSTEM_JPEG_SDSLP_MEM_PD_S  1
/** HP_SYSTEM_JPEG_LSLP_MEM_PD : R/W; bitpos: [2]; default: 0;
 *  N/A
 */
#define HP_SYSTEM_JPEG_LSLP_MEM_PD    (BIT(2))
#define HP_SYSTEM_JPEG_LSLP_MEM_PD_M  (HP_SYSTEM_JPEG_LSLP_MEM_PD_V << HP_SYSTEM_JPEG_LSLP_MEM_PD_S)
#define HP_SYSTEM_JPEG_LSLP_MEM_PD_V  0x00000001U
#define HP_SYSTEM_JPEG_LSLP_MEM_PD_S  2
/** HP_SYSTEM_JPEG_DSLP_MEM_PD : R/W; bitpos: [3]; default: 0;
 *  N/A
 */
#define HP_SYSTEM_JPEG_DSLP_MEM_PD    (BIT(3))
#define HP_SYSTEM_JPEG_DSLP_MEM_PD_M  (HP_SYSTEM_JPEG_DSLP_MEM_PD_V << HP_SYSTEM_JPEG_DSLP_MEM_PD_S)
#define HP_SYSTEM_JPEG_DSLP_MEM_PD_V  0x00000001U
#define HP_SYSTEM_JPEG_DSLP_MEM_PD_S  3
/** HP_SYSTEM_DMA2D_LSLP_MEM_PD : R/W; bitpos: [4]; default: 0;
 *  N/A
 */
#define HP_SYSTEM_DMA2D_LSLP_MEM_PD    (BIT(4))
#define HP_SYSTEM_DMA2D_LSLP_MEM_PD_M  (HP_SYSTEM_DMA2D_LSLP_MEM_PD_V << HP_SYSTEM_DMA2D_LSLP_MEM_PD_S)
#define HP_SYSTEM_DMA2D_LSLP_MEM_PD_V  0x00000001U
#define HP_SYSTEM_DMA2D_LSLP_MEM_PD_S  4

/** HP_SYSTEM_USBOTG20_CTRL_REG register
 *  N/A
 */
#define HP_SYSTEM_USBOTG20_CTRL_REG (DR_REG_HP_SYS_BASE + 0x15c)
/** HP_SYSTEM_OTG_PHY_TEST_DONE : RO; bitpos: [0]; default: 0;
 *  N/A
 */
#define HP_SYSTEM_OTG_PHY_TEST_DONE    (BIT(0))
#define HP_SYSTEM_OTG_PHY_TEST_DONE_M  (HP_SYSTEM_OTG_PHY_TEST_DONE_V << HP_SYSTEM_OTG_PHY_TEST_DONE_S)
#define HP_SYSTEM_OTG_PHY_TEST_DONE_V  0x00000001U
#define HP_SYSTEM_OTG_PHY_TEST_DONE_S  0
/** HP_SYSTEM_USB_MEM_AUX_CTRL : R/W; bitpos: [14:1]; default: 4896;
 *  N/A
 */
#define HP_SYSTEM_USB_MEM_AUX_CTRL    0x00003FFFU
#define HP_SYSTEM_USB_MEM_AUX_CTRL_M  (HP_SYSTEM_USB_MEM_AUX_CTRL_V << HP_SYSTEM_USB_MEM_AUX_CTRL_S)
#define HP_SYSTEM_USB_MEM_AUX_CTRL_V  0x00003FFFU
#define HP_SYSTEM_USB_MEM_AUX_CTRL_S  1
/** HP_SYSTEM_PHY_SUSPENDM : R/W; bitpos: [15]; default: 0;
 *  N/A
 */
#define HP_SYSTEM_PHY_SUSPENDM    (BIT(15))
#define HP_SYSTEM_PHY_SUSPENDM_M  (HP_SYSTEM_PHY_SUSPENDM_V << HP_SYSTEM_PHY_SUSPENDM_S)
#define HP_SYSTEM_PHY_SUSPENDM_V  0x00000001U
#define HP_SYSTEM_PHY_SUSPENDM_S  15
/** HP_SYSTEM_PHY_SUSPEND_FORCE_EN : R/W; bitpos: [16]; default: 0;
 *  N/A
 */
#define HP_SYSTEM_PHY_SUSPEND_FORCE_EN    (BIT(16))
#define HP_SYSTEM_PHY_SUSPEND_FORCE_EN_M  (HP_SYSTEM_PHY_SUSPEND_FORCE_EN_V << HP_SYSTEM_PHY_SUSPEND_FORCE_EN_S)
#define HP_SYSTEM_PHY_SUSPEND_FORCE_EN_V  0x00000001U
#define HP_SYSTEM_PHY_SUSPEND_FORCE_EN_S  16
/** HP_SYSTEM_PHY_RSTN : R/W; bitpos: [17]; default: 1;
 *  N/A
 */
#define HP_SYSTEM_PHY_RSTN    (BIT(17))
#define HP_SYSTEM_PHY_RSTN_M  (HP_SYSTEM_PHY_RSTN_V << HP_SYSTEM_PHY_RSTN_S)
#define HP_SYSTEM_PHY_RSTN_V  0x00000001U
#define HP_SYSTEM_PHY_RSTN_S  17
/** HP_SYSTEM_PHY_RESET_FORCE_EN : R/W; bitpos: [18]; default: 0;
 *  N/A
 */
#define HP_SYSTEM_PHY_RESET_FORCE_EN    (BIT(18))
#define HP_SYSTEM_PHY_RESET_FORCE_EN_M  (HP_SYSTEM_PHY_RESET_FORCE_EN_V << HP_SYSTEM_PHY_RESET_FORCE_EN_S)
#define HP_SYSTEM_PHY_RESET_FORCE_EN_V  0x00000001U
#define HP_SYSTEM_PHY_RESET_FORCE_EN_S  18
/** HP_SYSTEM_PHY_PLL_FORCE_EN : R/W; bitpos: [19]; default: 0;
 *  N/A
 */
#define HP_SYSTEM_PHY_PLL_FORCE_EN    (BIT(19))
#define HP_SYSTEM_PHY_PLL_FORCE_EN_M  (HP_SYSTEM_PHY_PLL_FORCE_EN_V << HP_SYSTEM_PHY_PLL_FORCE_EN_S)
#define HP_SYSTEM_PHY_PLL_FORCE_EN_V  0x00000001U
#define HP_SYSTEM_PHY_PLL_FORCE_EN_S  19
/** HP_SYSTEM_PHY_PLL_EN : R/W; bitpos: [20]; default: 0;
 *  N/A
 */
#define HP_SYSTEM_PHY_PLL_EN    (BIT(20))
#define HP_SYSTEM_PHY_PLL_EN_M  (HP_SYSTEM_PHY_PLL_EN_V << HP_SYSTEM_PHY_PLL_EN_S)
#define HP_SYSTEM_PHY_PLL_EN_V  0x00000001U
#define HP_SYSTEM_PHY_PLL_EN_S  20
/** HP_SYSTEM_OTG_SUSPENDM : R/W; bitpos: [21]; default: 0;
 *  N/A
 */
#define HP_SYSTEM_OTG_SUSPENDM    (BIT(21))
#define HP_SYSTEM_OTG_SUSPENDM_M  (HP_SYSTEM_OTG_SUSPENDM_V << HP_SYSTEM_OTG_SUSPENDM_S)
#define HP_SYSTEM_OTG_SUSPENDM_V  0x00000001U
#define HP_SYSTEM_OTG_SUSPENDM_S  21
/** HP_SYSTEM_OTG_PHY_TXBITSTUFF_EN : R/W; bitpos: [22]; default: 0;
 *  N/A
 */
#define HP_SYSTEM_OTG_PHY_TXBITSTUFF_EN    (BIT(22))
#define HP_SYSTEM_OTG_PHY_TXBITSTUFF_EN_M  (HP_SYSTEM_OTG_PHY_TXBITSTUFF_EN_V << HP_SYSTEM_OTG_PHY_TXBITSTUFF_EN_S)
#define HP_SYSTEM_OTG_PHY_TXBITSTUFF_EN_V  0x00000001U
#define HP_SYSTEM_OTG_PHY_TXBITSTUFF_EN_S  22
/** HP_SYSTEM_OTG_PHY_REFCLK_MODE : R/W; bitpos: [23]; default: 1;
 *  N/A
 */
#define HP_SYSTEM_OTG_PHY_REFCLK_MODE    (BIT(23))
#define HP_SYSTEM_OTG_PHY_REFCLK_MODE_M  (HP_SYSTEM_OTG_PHY_REFCLK_MODE_V << HP_SYSTEM_OTG_PHY_REFCLK_MODE_S)
#define HP_SYSTEM_OTG_PHY_REFCLK_MODE_V  0x00000001U
#define HP_SYSTEM_OTG_PHY_REFCLK_MODE_S  23
/** HP_SYSTEM_OTG_PHY_BISTEN : R/W; bitpos: [24]; default: 0;
 *  N/A
 */
#define HP_SYSTEM_OTG_PHY_BISTEN    (BIT(24))
#define HP_SYSTEM_OTG_PHY_BISTEN_M  (HP_SYSTEM_OTG_PHY_BISTEN_V << HP_SYSTEM_OTG_PHY_BISTEN_S)
#define HP_SYSTEM_OTG_PHY_BISTEN_V  0x00000001U
#define HP_SYSTEM_OTG_PHY_BISTEN_S  24

/** HP_SYSTEM_TCM_ERR_RESP_CTRL_REG register
 *  need_des
 */
#define HP_SYSTEM_TCM_ERR_RESP_CTRL_REG (DR_REG_HP_SYS_BASE + 0x160)
/** HP_SYSTEM_TCM_ERR_RESP_EN : R/W; bitpos: [0]; default: 0;
 *  Set 1 to turn on tcm error response
 */
#define HP_SYSTEM_TCM_ERR_RESP_EN    (BIT(0))
#define HP_SYSTEM_TCM_ERR_RESP_EN_M  (HP_SYSTEM_TCM_ERR_RESP_EN_V << HP_SYSTEM_TCM_ERR_RESP_EN_S)
#define HP_SYSTEM_TCM_ERR_RESP_EN_V  0x00000001U
#define HP_SYSTEM_TCM_ERR_RESP_EN_S  0

/** HP_SYSTEM_L2_MEM_REFRESH_REG register
 *  NA
 */
#define HP_SYSTEM_L2_MEM_REFRESH_REG (DR_REG_HP_SYS_BASE + 0x164)
/** HP_SYSTEM_REG_L2_MEM_UNIT0_REFERSH_EN : R/W; bitpos: [0]; default: 0;
 *  NA
 */
#define HP_SYSTEM_REG_L2_MEM_UNIT0_REFERSH_EN    (BIT(0))
#define HP_SYSTEM_REG_L2_MEM_UNIT0_REFERSH_EN_M  (HP_SYSTEM_REG_L2_MEM_UNIT0_REFERSH_EN_V << HP_SYSTEM_REG_L2_MEM_UNIT0_REFERSH_EN_S)
#define HP_SYSTEM_REG_L2_MEM_UNIT0_REFERSH_EN_V  0x00000001U
#define HP_SYSTEM_REG_L2_MEM_UNIT0_REFERSH_EN_S  0
/** HP_SYSTEM_REG_L2_MEM_UNIT1_REFERSH_EN : R/W; bitpos: [1]; default: 0;
 *  NA
 */
#define HP_SYSTEM_REG_L2_MEM_UNIT1_REFERSH_EN    (BIT(1))
#define HP_SYSTEM_REG_L2_MEM_UNIT1_REFERSH_EN_M  (HP_SYSTEM_REG_L2_MEM_UNIT1_REFERSH_EN_V << HP_SYSTEM_REG_L2_MEM_UNIT1_REFERSH_EN_S)
#define HP_SYSTEM_REG_L2_MEM_UNIT1_REFERSH_EN_V  0x00000001U
#define HP_SYSTEM_REG_L2_MEM_UNIT1_REFERSH_EN_S  1
/** HP_SYSTEM_REG_L2_MEM_UNIT2_REFERSH_EN : R/W; bitpos: [2]; default: 0;
 *  NA
 */
#define HP_SYSTEM_REG_L2_MEM_UNIT2_REFERSH_EN    (BIT(2))
#define HP_SYSTEM_REG_L2_MEM_UNIT2_REFERSH_EN_M  (HP_SYSTEM_REG_L2_MEM_UNIT2_REFERSH_EN_V << HP_SYSTEM_REG_L2_MEM_UNIT2_REFERSH_EN_S)
#define HP_SYSTEM_REG_L2_MEM_UNIT2_REFERSH_EN_V  0x00000001U
#define HP_SYSTEM_REG_L2_MEM_UNIT2_REFERSH_EN_S  2
/** HP_SYSTEM_REG_L2_MEM_UNIT3_REFERSH_EN : R/W; bitpos: [3]; default: 0;
 *  NA
 */
#define HP_SYSTEM_REG_L2_MEM_UNIT3_REFERSH_EN    (BIT(3))
#define HP_SYSTEM_REG_L2_MEM_UNIT3_REFERSH_EN_M  (HP_SYSTEM_REG_L2_MEM_UNIT3_REFERSH_EN_V << HP_SYSTEM_REG_L2_MEM_UNIT3_REFERSH_EN_S)
#define HP_SYSTEM_REG_L2_MEM_UNIT3_REFERSH_EN_V  0x00000001U
#define HP_SYSTEM_REG_L2_MEM_UNIT3_REFERSH_EN_S  3
/** HP_SYSTEM_REG_L2_MEM_UNIT4_REFERSH_EN : R/W; bitpos: [4]; default: 0;
 *  NA
 */
#define HP_SYSTEM_REG_L2_MEM_UNIT4_REFERSH_EN    (BIT(4))
#define HP_SYSTEM_REG_L2_MEM_UNIT4_REFERSH_EN_M  (HP_SYSTEM_REG_L2_MEM_UNIT4_REFERSH_EN_V << HP_SYSTEM_REG_L2_MEM_UNIT4_REFERSH_EN_S)
#define HP_SYSTEM_REG_L2_MEM_UNIT4_REFERSH_EN_V  0x00000001U
#define HP_SYSTEM_REG_L2_MEM_UNIT4_REFERSH_EN_S  4
/** HP_SYSTEM_REG_L2_MEM_UNIT5_REFERSH_EN : R/W; bitpos: [5]; default: 0;
 *  NA
 */
#define HP_SYSTEM_REG_L2_MEM_UNIT5_REFERSH_EN    (BIT(5))
#define HP_SYSTEM_REG_L2_MEM_UNIT5_REFERSH_EN_M  (HP_SYSTEM_REG_L2_MEM_UNIT5_REFERSH_EN_V << HP_SYSTEM_REG_L2_MEM_UNIT5_REFERSH_EN_S)
#define HP_SYSTEM_REG_L2_MEM_UNIT5_REFERSH_EN_V  0x00000001U
#define HP_SYSTEM_REG_L2_MEM_UNIT5_REFERSH_EN_S  5
/** HP_SYSTEM_REG_L2_MEM_REFERSH_CNT_RESET : R/W; bitpos: [6]; default: 1;
 *  Set 1 to reset l2mem_refresh_cnt
 */
#define HP_SYSTEM_REG_L2_MEM_REFERSH_CNT_RESET    (BIT(6))
#define HP_SYSTEM_REG_L2_MEM_REFERSH_CNT_RESET_M  (HP_SYSTEM_REG_L2_MEM_REFERSH_CNT_RESET_V << HP_SYSTEM_REG_L2_MEM_REFERSH_CNT_RESET_S)
#define HP_SYSTEM_REG_L2_MEM_REFERSH_CNT_RESET_V  0x00000001U
#define HP_SYSTEM_REG_L2_MEM_REFERSH_CNT_RESET_S  6
/** HP_SYSTEM_REG_L2_MEM_UNIT0_REFRESH_DONE : RO; bitpos: [7]; default: 0;
 *  NA
 */
#define HP_SYSTEM_REG_L2_MEM_UNIT0_REFRESH_DONE    (BIT(7))
#define HP_SYSTEM_REG_L2_MEM_UNIT0_REFRESH_DONE_M  (HP_SYSTEM_REG_L2_MEM_UNIT0_REFRESH_DONE_V << HP_SYSTEM_REG_L2_MEM_UNIT0_REFRESH_DONE_S)
#define HP_SYSTEM_REG_L2_MEM_UNIT0_REFRESH_DONE_V  0x00000001U
#define HP_SYSTEM_REG_L2_MEM_UNIT0_REFRESH_DONE_S  7
/** HP_SYSTEM_REG_L2_MEM_UNIT1_REFRESH_DONE : RO; bitpos: [8]; default: 0;
 *  NA
 */
#define HP_SYSTEM_REG_L2_MEM_UNIT1_REFRESH_DONE    (BIT(8))
#define HP_SYSTEM_REG_L2_MEM_UNIT1_REFRESH_DONE_M  (HP_SYSTEM_REG_L2_MEM_UNIT1_REFRESH_DONE_V << HP_SYSTEM_REG_L2_MEM_UNIT1_REFRESH_DONE_S)
#define HP_SYSTEM_REG_L2_MEM_UNIT1_REFRESH_DONE_V  0x00000001U
#define HP_SYSTEM_REG_L2_MEM_UNIT1_REFRESH_DONE_S  8
/** HP_SYSTEM_REG_L2_MEM_UNIT2_REFRESH_DONE : RO; bitpos: [9]; default: 0;
 *  NA
 */
#define HP_SYSTEM_REG_L2_MEM_UNIT2_REFRESH_DONE    (BIT(9))
#define HP_SYSTEM_REG_L2_MEM_UNIT2_REFRESH_DONE_M  (HP_SYSTEM_REG_L2_MEM_UNIT2_REFRESH_DONE_V << HP_SYSTEM_REG_L2_MEM_UNIT2_REFRESH_DONE_S)
#define HP_SYSTEM_REG_L2_MEM_UNIT2_REFRESH_DONE_V  0x00000001U
#define HP_SYSTEM_REG_L2_MEM_UNIT2_REFRESH_DONE_S  9
/** HP_SYSTEM_REG_L2_MEM_UNIT3_REFRESH_DONE : RO; bitpos: [10]; default: 0;
 *  NA
 */
#define HP_SYSTEM_REG_L2_MEM_UNIT3_REFRESH_DONE    (BIT(10))
#define HP_SYSTEM_REG_L2_MEM_UNIT3_REFRESH_DONE_M  (HP_SYSTEM_REG_L2_MEM_UNIT3_REFRESH_DONE_V << HP_SYSTEM_REG_L2_MEM_UNIT3_REFRESH_DONE_S)
#define HP_SYSTEM_REG_L2_MEM_UNIT3_REFRESH_DONE_V  0x00000001U
#define HP_SYSTEM_REG_L2_MEM_UNIT3_REFRESH_DONE_S  10
/** HP_SYSTEM_REG_L2_MEM_UNIT4_REFRESH_DONE : RO; bitpos: [11]; default: 0;
 *  NA
 */
#define HP_SYSTEM_REG_L2_MEM_UNIT4_REFRESH_DONE    (BIT(11))
#define HP_SYSTEM_REG_L2_MEM_UNIT4_REFRESH_DONE_M  (HP_SYSTEM_REG_L2_MEM_UNIT4_REFRESH_DONE_V << HP_SYSTEM_REG_L2_MEM_UNIT4_REFRESH_DONE_S)
#define HP_SYSTEM_REG_L2_MEM_UNIT4_REFRESH_DONE_V  0x00000001U
#define HP_SYSTEM_REG_L2_MEM_UNIT4_REFRESH_DONE_S  11
/** HP_SYSTEM_REG_L2_MEM_UNIT5_REFRESH_DONE : RO; bitpos: [12]; default: 0;
 *  NA
 */
#define HP_SYSTEM_REG_L2_MEM_UNIT5_REFRESH_DONE    (BIT(12))
#define HP_SYSTEM_REG_L2_MEM_UNIT5_REFRESH_DONE_M  (HP_SYSTEM_REG_L2_MEM_UNIT5_REFRESH_DONE_V << HP_SYSTEM_REG_L2_MEM_UNIT5_REFRESH_DONE_S)
#define HP_SYSTEM_REG_L2_MEM_UNIT5_REFRESH_DONE_V  0x00000001U
#define HP_SYSTEM_REG_L2_MEM_UNIT5_REFRESH_DONE_S  12

/** HP_SYSTEM_TCM_INIT_REG register
 *  NA
 */
#define HP_SYSTEM_TCM_INIT_REG (DR_REG_HP_SYS_BASE + 0x168)
/** HP_SYSTEM_REG_TCM_INIT_EN : R/W; bitpos: [0]; default: 0;
 *  NA
 */
#define HP_SYSTEM_REG_TCM_INIT_EN    (BIT(0))
#define HP_SYSTEM_REG_TCM_INIT_EN_M  (HP_SYSTEM_REG_TCM_INIT_EN_V << HP_SYSTEM_REG_TCM_INIT_EN_S)
#define HP_SYSTEM_REG_TCM_INIT_EN_V  0x00000001U
#define HP_SYSTEM_REG_TCM_INIT_EN_S  0
/** HP_SYSTEM_REG_TCM_INIT_CNT_RESET : R/W; bitpos: [1]; default: 1;
 *  Set 1 to reset tcm init cnt
 */
#define HP_SYSTEM_REG_TCM_INIT_CNT_RESET    (BIT(1))
#define HP_SYSTEM_REG_TCM_INIT_CNT_RESET_M  (HP_SYSTEM_REG_TCM_INIT_CNT_RESET_V << HP_SYSTEM_REG_TCM_INIT_CNT_RESET_S)
#define HP_SYSTEM_REG_TCM_INIT_CNT_RESET_V  0x00000001U
#define HP_SYSTEM_REG_TCM_INIT_CNT_RESET_S  1
/** HP_SYSTEM_REG_TCM_INIT_DONE : RO; bitpos: [2]; default: 0;
 *  NA
 */
#define HP_SYSTEM_REG_TCM_INIT_DONE    (BIT(2))
#define HP_SYSTEM_REG_TCM_INIT_DONE_M  (HP_SYSTEM_REG_TCM_INIT_DONE_V << HP_SYSTEM_REG_TCM_INIT_DONE_S)
#define HP_SYSTEM_REG_TCM_INIT_DONE_V  0x00000001U
#define HP_SYSTEM_REG_TCM_INIT_DONE_S  2

/** HP_SYSTEM_TCM_PARITY_CHECK_CTRL_REG register
 *  need_des
 */
#define HP_SYSTEM_TCM_PARITY_CHECK_CTRL_REG (DR_REG_HP_SYS_BASE + 0x16c)
/** HP_SYSTEM_TCM_PARITY_CHECK_EN : R/W; bitpos: [0]; default: 0;
 *  Set 1 to turn on tcm parity check
 */
#define HP_SYSTEM_TCM_PARITY_CHECK_EN    (BIT(0))
#define HP_SYSTEM_TCM_PARITY_CHECK_EN_M  (HP_SYSTEM_TCM_PARITY_CHECK_EN_V << HP_SYSTEM_TCM_PARITY_CHECK_EN_S)
#define HP_SYSTEM_TCM_PARITY_CHECK_EN_V  0x00000001U
#define HP_SYSTEM_TCM_PARITY_CHECK_EN_S  0

/** HP_SYSTEM_DESIGN_FOR_VERIFICATION0_REG register
 *  need_des
 */
#define HP_SYSTEM_DESIGN_FOR_VERIFICATION0_REG (DR_REG_HP_SYS_BASE + 0x170)
/** HP_SYSTEM_DFV0 : R/W; bitpos: [31:0]; default: 0;
 *  register for DV
 */
#define HP_SYSTEM_DFV0    0xFFFFFFFFU
#define HP_SYSTEM_DFV0_M  (HP_SYSTEM_DFV0_V << HP_SYSTEM_DFV0_S)
#define HP_SYSTEM_DFV0_V  0xFFFFFFFFU
#define HP_SYSTEM_DFV0_S  0

/** HP_SYSTEM_DESIGN_FOR_VERIFICATION1_REG register
 *  need_des
 */
#define HP_SYSTEM_DESIGN_FOR_VERIFICATION1_REG (DR_REG_HP_SYS_BASE + 0x174)
/** HP_SYSTEM_DFV1 : R/W; bitpos: [31:0]; default: 0;
 *  register for DV
 */
#define HP_SYSTEM_DFV1    0xFFFFFFFFU
#define HP_SYSTEM_DFV1_M  (HP_SYSTEM_DFV1_V << HP_SYSTEM_DFV1_S)
#define HP_SYSTEM_DFV1_V  0xFFFFFFFFU
#define HP_SYSTEM_DFV1_S  0

/** HP_SYSTEM_PSRAM_FLASH_ADDR_INTERCHANGE_REG register
 *  need_des
 */
#define HP_SYSTEM_PSRAM_FLASH_ADDR_INTERCHANGE_REG (DR_REG_HP_SYS_BASE + 0x180)
/** HP_SYSTEM_PSRAM_FLASH_ADDR_INTERCHANGE_CPU : R/W; bitpos: [0]; default: 0;
 *  Set 1 to enable addr interchange between psram and flash in axi matrix when hp cpu
 *  access through cache
 */
#define HP_SYSTEM_PSRAM_FLASH_ADDR_INTERCHANGE_CPU    (BIT(0))
#define HP_SYSTEM_PSRAM_FLASH_ADDR_INTERCHANGE_CPU_M  (HP_SYSTEM_PSRAM_FLASH_ADDR_INTERCHANGE_CPU_V << HP_SYSTEM_PSRAM_FLASH_ADDR_INTERCHANGE_CPU_S)
#define HP_SYSTEM_PSRAM_FLASH_ADDR_INTERCHANGE_CPU_V  0x00000001U
#define HP_SYSTEM_PSRAM_FLASH_ADDR_INTERCHANGE_CPU_S  0
/** HP_SYSTEM_PSRAM_FLASH_ADDR_INTERCHANGE_DMA : R/W; bitpos: [1]; default: 0;
 *  Set 1 to enable addr interchange between psram and flash in axi matrix when dma
 *  device access, lp core access and hp core access through ahb
 */
#define HP_SYSTEM_PSRAM_FLASH_ADDR_INTERCHANGE_DMA    (BIT(1))
#define HP_SYSTEM_PSRAM_FLASH_ADDR_INTERCHANGE_DMA_M  (HP_SYSTEM_PSRAM_FLASH_ADDR_INTERCHANGE_DMA_V << HP_SYSTEM_PSRAM_FLASH_ADDR_INTERCHANGE_DMA_S)
#define HP_SYSTEM_PSRAM_FLASH_ADDR_INTERCHANGE_DMA_V  0x00000001U
#define HP_SYSTEM_PSRAM_FLASH_ADDR_INTERCHANGE_DMA_S  1

/** HP_SYSTEM_AHB2AXI_BRESP_ERR_INT_RAW_REG register
 *  NA
 */
#define HP_SYSTEM_AHB2AXI_BRESP_ERR_INT_RAW_REG (DR_REG_HP_SYS_BASE + 0x188)
/** HP_SYSTEM_CPU_ICM_H2X_BRESP_ERR_INT_RAW : R/WTC/SS; bitpos: [0]; default: 0;
 *  the raw interrupt status of bresp error,  triggered when if  bresp err occurs  in
 *  post write mode in ahb2axi.
 */
#define HP_SYSTEM_CPU_ICM_H2X_BRESP_ERR_INT_RAW    (BIT(0))
#define HP_SYSTEM_CPU_ICM_H2X_BRESP_ERR_INT_RAW_M  (HP_SYSTEM_CPU_ICM_H2X_BRESP_ERR_INT_RAW_V << HP_SYSTEM_CPU_ICM_H2X_BRESP_ERR_INT_RAW_S)
#define HP_SYSTEM_CPU_ICM_H2X_BRESP_ERR_INT_RAW_V  0x00000001U
#define HP_SYSTEM_CPU_ICM_H2X_BRESP_ERR_INT_RAW_S  0

/** HP_SYSTEM_AHB2AXI_BRESP_ERR_INT_ST_REG register
 *  need_des
 */
#define HP_SYSTEM_AHB2AXI_BRESP_ERR_INT_ST_REG (DR_REG_HP_SYS_BASE + 0x18c)
/** HP_SYSTEM_CPU_ICM_H2X_BRESP_ERR_INT_ST : RO; bitpos: [31]; default: 0;
 *  the masked interrupt status of  cpu_icm_h2x_bresp_err
 */
#define HP_SYSTEM_CPU_ICM_H2X_BRESP_ERR_INT_ST    (BIT(31))
#define HP_SYSTEM_CPU_ICM_H2X_BRESP_ERR_INT_ST_M  (HP_SYSTEM_CPU_ICM_H2X_BRESP_ERR_INT_ST_V << HP_SYSTEM_CPU_ICM_H2X_BRESP_ERR_INT_ST_S)
#define HP_SYSTEM_CPU_ICM_H2X_BRESP_ERR_INT_ST_V  0x00000001U
#define HP_SYSTEM_CPU_ICM_H2X_BRESP_ERR_INT_ST_S  31

/** HP_SYSTEM_AHB2AXI_BRESP_ERR_INT_ENA_REG register
 *  need_des
 */
#define HP_SYSTEM_AHB2AXI_BRESP_ERR_INT_ENA_REG (DR_REG_HP_SYS_BASE + 0x190)
/** HP_SYSTEM_CPU_ICM_H2X_BRESP_ERR_INT_ENA : R/W; bitpos: [31]; default: 0;
 *  Write 1 to enable cpu_icm_h2x_bresp_err int
 */
#define HP_SYSTEM_CPU_ICM_H2X_BRESP_ERR_INT_ENA    (BIT(31))
#define HP_SYSTEM_CPU_ICM_H2X_BRESP_ERR_INT_ENA_M  (HP_SYSTEM_CPU_ICM_H2X_BRESP_ERR_INT_ENA_V << HP_SYSTEM_CPU_ICM_H2X_BRESP_ERR_INT_ENA_S)
#define HP_SYSTEM_CPU_ICM_H2X_BRESP_ERR_INT_ENA_V  0x00000001U
#define HP_SYSTEM_CPU_ICM_H2X_BRESP_ERR_INT_ENA_S  31

/** HP_SYSTEM_AHB2AXI_BRESP_ERR_INT_CLR_REG register
 *  need_des
 */
#define HP_SYSTEM_AHB2AXI_BRESP_ERR_INT_CLR_REG (DR_REG_HP_SYS_BASE + 0x194)
/** HP_SYSTEM_CPU_ICM_H2X_BRESP_ERR_INT_CLR : WT; bitpos: [31]; default: 0;
 *  Write 1 to clear cpu_icm_h2x_bresp_err  int
 */
#define HP_SYSTEM_CPU_ICM_H2X_BRESP_ERR_INT_CLR    (BIT(31))
#define HP_SYSTEM_CPU_ICM_H2X_BRESP_ERR_INT_CLR_M  (HP_SYSTEM_CPU_ICM_H2X_BRESP_ERR_INT_CLR_V << HP_SYSTEM_CPU_ICM_H2X_BRESP_ERR_INT_CLR_S)
#define HP_SYSTEM_CPU_ICM_H2X_BRESP_ERR_INT_CLR_V  0x00000001U
#define HP_SYSTEM_CPU_ICM_H2X_BRESP_ERR_INT_CLR_S  31

/** HP_SYSTEM_L2_MEM_ERR_RESP_CTRL_REG register
 *  need_des
 */
#define HP_SYSTEM_L2_MEM_ERR_RESP_CTRL_REG (DR_REG_HP_SYS_BASE + 0x198)
/** HP_SYSTEM_L2_MEM_ERR_RESP_EN : R/W; bitpos: [0]; default: 0;
 *  Set 1 to turn on l2mem error response
 */
#define HP_SYSTEM_L2_MEM_ERR_RESP_EN    (BIT(0))
#define HP_SYSTEM_L2_MEM_ERR_RESP_EN_M  (HP_SYSTEM_L2_MEM_ERR_RESP_EN_V << HP_SYSTEM_L2_MEM_ERR_RESP_EN_S)
#define HP_SYSTEM_L2_MEM_ERR_RESP_EN_V  0x00000001U
#define HP_SYSTEM_L2_MEM_ERR_RESP_EN_S  0

/** HP_SYSTEM_L2_MEM_AHB_BUFFER_CTRL_REG register
 *  need_des
 */
#define HP_SYSTEM_L2_MEM_AHB_BUFFER_CTRL_REG (DR_REG_HP_SYS_BASE + 0x19c)
/** HP_SYSTEM_L2_MEM_AHB_WRBUFFER_EN : R/W; bitpos: [0]; default: 0;
 *  Set 1 to turn on l2mem ahb wr buffer
 */
#define HP_SYSTEM_L2_MEM_AHB_WRBUFFER_EN    (BIT(0))
#define HP_SYSTEM_L2_MEM_AHB_WRBUFFER_EN_M  (HP_SYSTEM_L2_MEM_AHB_WRBUFFER_EN_V << HP_SYSTEM_L2_MEM_AHB_WRBUFFER_EN_S)
#define HP_SYSTEM_L2_MEM_AHB_WRBUFFER_EN_V  0x00000001U
#define HP_SYSTEM_L2_MEM_AHB_WRBUFFER_EN_S  0
/** HP_SYSTEM_L2_MEM_AHB_RDBUFFER_EN : R/W; bitpos: [1]; default: 0;
 *  Set 1 to turn on l2mem ahb rd buffer
 */
#define HP_SYSTEM_L2_MEM_AHB_RDBUFFER_EN    (BIT(1))
#define HP_SYSTEM_L2_MEM_AHB_RDBUFFER_EN_M  (HP_SYSTEM_L2_MEM_AHB_RDBUFFER_EN_V << HP_SYSTEM_L2_MEM_AHB_RDBUFFER_EN_S)
#define HP_SYSTEM_L2_MEM_AHB_RDBUFFER_EN_V  0x00000001U
#define HP_SYSTEM_L2_MEM_AHB_RDBUFFER_EN_S  1

/** HP_SYSTEM_CORE_DMACTIVE_LPCORE_REG register
 *  need_des
 */
#define HP_SYSTEM_CORE_DMACTIVE_LPCORE_REG (DR_REG_HP_SYS_BASE + 0x1a0)
/** HP_SYSTEM_CORE_DMACTIVE_LPCORE : RO; bitpos: [0]; default: 0;
 *  hp core dmactive_lpcore value
 */
#define HP_SYSTEM_CORE_DMACTIVE_LPCORE    (BIT(0))
#define HP_SYSTEM_CORE_DMACTIVE_LPCORE_M  (HP_SYSTEM_CORE_DMACTIVE_LPCORE_V << HP_SYSTEM_CORE_DMACTIVE_LPCORE_S)
#define HP_SYSTEM_CORE_DMACTIVE_LPCORE_V  0x00000001U
#define HP_SYSTEM_CORE_DMACTIVE_LPCORE_S  0

/** HP_SYSTEM_CORE_ERR_RESP_DIS_REG register
 *  need_des
 */
#define HP_SYSTEM_CORE_ERR_RESP_DIS_REG (DR_REG_HP_SYS_BASE + 0x1a4)
/** HP_SYSTEM_CORE_ERR_RESP_DIS : R/W; bitpos: [2:0]; default: 0;
 *  Set bit0 to disable ibus err resp. Set bit1 to disable dbus err resp.  Set bit 2 to
 *  disable ahb err resp.
 */
#define HP_SYSTEM_CORE_ERR_RESP_DIS    0x00000007U
#define HP_SYSTEM_CORE_ERR_RESP_DIS_M  (HP_SYSTEM_CORE_ERR_RESP_DIS_V << HP_SYSTEM_CORE_ERR_RESP_DIS_S)
#define HP_SYSTEM_CORE_ERR_RESP_DIS_V  0x00000007U
#define HP_SYSTEM_CORE_ERR_RESP_DIS_S  0

/** HP_SYSTEM_CORE_TIMEOUT_INT_RAW_REG register
 *  Hp core bus timeout interrupt raw register
 */
#define HP_SYSTEM_CORE_TIMEOUT_INT_RAW_REG (DR_REG_HP_SYS_BASE + 0x1a8)
/** HP_SYSTEM_CORE0_AHB_TIMEOUT_INT_RAW : R/WTC/SS; bitpos: [0]; default: 0;
 *  the raw interrupt status of hp core0  ahb timeout
 */
#define HP_SYSTEM_CORE0_AHB_TIMEOUT_INT_RAW    (BIT(0))
#define HP_SYSTEM_CORE0_AHB_TIMEOUT_INT_RAW_M  (HP_SYSTEM_CORE0_AHB_TIMEOUT_INT_RAW_V << HP_SYSTEM_CORE0_AHB_TIMEOUT_INT_RAW_S)
#define HP_SYSTEM_CORE0_AHB_TIMEOUT_INT_RAW_V  0x00000001U
#define HP_SYSTEM_CORE0_AHB_TIMEOUT_INT_RAW_S  0
/** HP_SYSTEM_CORE1_AHB_TIMEOUT_INT_RAW : R/WTC/SS; bitpos: [1]; default: 0;
 *  the raw interrupt status of hp core1  ahb timeout
 */
#define HP_SYSTEM_CORE1_AHB_TIMEOUT_INT_RAW    (BIT(1))
#define HP_SYSTEM_CORE1_AHB_TIMEOUT_INT_RAW_M  (HP_SYSTEM_CORE1_AHB_TIMEOUT_INT_RAW_V << HP_SYSTEM_CORE1_AHB_TIMEOUT_INT_RAW_S)
#define HP_SYSTEM_CORE1_AHB_TIMEOUT_INT_RAW_V  0x00000001U
#define HP_SYSTEM_CORE1_AHB_TIMEOUT_INT_RAW_S  1
/** HP_SYSTEM_CORE0_IBUS_TIMEOUT_INT_RAW : R/WTC/SS; bitpos: [2]; default: 0;
 *  the raw interrupt status of hp core0  ibus timeout
 */
#define HP_SYSTEM_CORE0_IBUS_TIMEOUT_INT_RAW    (BIT(2))
#define HP_SYSTEM_CORE0_IBUS_TIMEOUT_INT_RAW_M  (HP_SYSTEM_CORE0_IBUS_TIMEOUT_INT_RAW_V << HP_SYSTEM_CORE0_IBUS_TIMEOUT_INT_RAW_S)
#define HP_SYSTEM_CORE0_IBUS_TIMEOUT_INT_RAW_V  0x00000001U
#define HP_SYSTEM_CORE0_IBUS_TIMEOUT_INT_RAW_S  2
/** HP_SYSTEM_CORE1_IBUS_TIMEOUT_INT_RAW : R/WTC/SS; bitpos: [3]; default: 0;
 *  the raw interrupt status of hp core1  ibus timeout
 */
#define HP_SYSTEM_CORE1_IBUS_TIMEOUT_INT_RAW    (BIT(3))
#define HP_SYSTEM_CORE1_IBUS_TIMEOUT_INT_RAW_M  (HP_SYSTEM_CORE1_IBUS_TIMEOUT_INT_RAW_V << HP_SYSTEM_CORE1_IBUS_TIMEOUT_INT_RAW_S)
#define HP_SYSTEM_CORE1_IBUS_TIMEOUT_INT_RAW_V  0x00000001U
#define HP_SYSTEM_CORE1_IBUS_TIMEOUT_INT_RAW_S  3
/** HP_SYSTEM_CORE0_DBUS_TIMEOUT_INT_RAW : R/WTC/SS; bitpos: [4]; default: 0;
 *  the raw interrupt status of hp core0  dbus timeout
 */
#define HP_SYSTEM_CORE0_DBUS_TIMEOUT_INT_RAW    (BIT(4))
#define HP_SYSTEM_CORE0_DBUS_TIMEOUT_INT_RAW_M  (HP_SYSTEM_CORE0_DBUS_TIMEOUT_INT_RAW_V << HP_SYSTEM_CORE0_DBUS_TIMEOUT_INT_RAW_S)
#define HP_SYSTEM_CORE0_DBUS_TIMEOUT_INT_RAW_V  0x00000001U
#define HP_SYSTEM_CORE0_DBUS_TIMEOUT_INT_RAW_S  4
/** HP_SYSTEM_CORE1_DBUS_TIMEOUT_INT_RAW : R/WTC/SS; bitpos: [5]; default: 0;
 *  the raw interrupt status of hp core1  dbus timeout
 */
#define HP_SYSTEM_CORE1_DBUS_TIMEOUT_INT_RAW    (BIT(5))
#define HP_SYSTEM_CORE1_DBUS_TIMEOUT_INT_RAW_M  (HP_SYSTEM_CORE1_DBUS_TIMEOUT_INT_RAW_V << HP_SYSTEM_CORE1_DBUS_TIMEOUT_INT_RAW_S)
#define HP_SYSTEM_CORE1_DBUS_TIMEOUT_INT_RAW_V  0x00000001U
#define HP_SYSTEM_CORE1_DBUS_TIMEOUT_INT_RAW_S  5

/** HP_SYSTEM_CORE_TIMEOUT_INT_ST_REG register
 *  masked interrupt register
 */
#define HP_SYSTEM_CORE_TIMEOUT_INT_ST_REG (DR_REG_HP_SYS_BASE + 0x1ac)
/** HP_SYSTEM_CORE0_AHB_TIMEOUT_INT_ST : RO; bitpos: [0]; default: 0;
 *  the masked interrupt status of hp core0  ahb timeout
 */
#define HP_SYSTEM_CORE0_AHB_TIMEOUT_INT_ST    (BIT(0))
#define HP_SYSTEM_CORE0_AHB_TIMEOUT_INT_ST_M  (HP_SYSTEM_CORE0_AHB_TIMEOUT_INT_ST_V << HP_SYSTEM_CORE0_AHB_TIMEOUT_INT_ST_S)
#define HP_SYSTEM_CORE0_AHB_TIMEOUT_INT_ST_V  0x00000001U
#define HP_SYSTEM_CORE0_AHB_TIMEOUT_INT_ST_S  0
/** HP_SYSTEM_CORE1_AHB_TIMEOUT_INT_ST : RO; bitpos: [1]; default: 0;
 *  the masked interrupt status of hp core1  ahb timeout
 */
#define HP_SYSTEM_CORE1_AHB_TIMEOUT_INT_ST    (BIT(1))
#define HP_SYSTEM_CORE1_AHB_TIMEOUT_INT_ST_M  (HP_SYSTEM_CORE1_AHB_TIMEOUT_INT_ST_V << HP_SYSTEM_CORE1_AHB_TIMEOUT_INT_ST_S)
#define HP_SYSTEM_CORE1_AHB_TIMEOUT_INT_ST_V  0x00000001U
#define HP_SYSTEM_CORE1_AHB_TIMEOUT_INT_ST_S  1
/** HP_SYSTEM_CORE0_IBUS_TIMEOUT_INT_ST : RO; bitpos: [2]; default: 0;
 *  the masked interrupt status of hp core0  ibus timeout
 */
#define HP_SYSTEM_CORE0_IBUS_TIMEOUT_INT_ST    (BIT(2))
#define HP_SYSTEM_CORE0_IBUS_TIMEOUT_INT_ST_M  (HP_SYSTEM_CORE0_IBUS_TIMEOUT_INT_ST_V << HP_SYSTEM_CORE0_IBUS_TIMEOUT_INT_ST_S)
#define HP_SYSTEM_CORE0_IBUS_TIMEOUT_INT_ST_V  0x00000001U
#define HP_SYSTEM_CORE0_IBUS_TIMEOUT_INT_ST_S  2
/** HP_SYSTEM_CORE1_IBUS_TIMEOUT_INT_ST : RO; bitpos: [3]; default: 0;
 *  the masked interrupt status of hp core1  ibus timeout
 */
#define HP_SYSTEM_CORE1_IBUS_TIMEOUT_INT_ST    (BIT(3))
#define HP_SYSTEM_CORE1_IBUS_TIMEOUT_INT_ST_M  (HP_SYSTEM_CORE1_IBUS_TIMEOUT_INT_ST_V << HP_SYSTEM_CORE1_IBUS_TIMEOUT_INT_ST_S)
#define HP_SYSTEM_CORE1_IBUS_TIMEOUT_INT_ST_V  0x00000001U
#define HP_SYSTEM_CORE1_IBUS_TIMEOUT_INT_ST_S  3
/** HP_SYSTEM_CORE0_DBUS_TIMEOUT_INT_ST : RO; bitpos: [4]; default: 0;
 *  the masked interrupt status of hp core0  dbus timeout
 */
#define HP_SYSTEM_CORE0_DBUS_TIMEOUT_INT_ST    (BIT(4))
#define HP_SYSTEM_CORE0_DBUS_TIMEOUT_INT_ST_M  (HP_SYSTEM_CORE0_DBUS_TIMEOUT_INT_ST_V << HP_SYSTEM_CORE0_DBUS_TIMEOUT_INT_ST_S)
#define HP_SYSTEM_CORE0_DBUS_TIMEOUT_INT_ST_V  0x00000001U
#define HP_SYSTEM_CORE0_DBUS_TIMEOUT_INT_ST_S  4
/** HP_SYSTEM_CORE1_DBUS_TIMEOUT_INT_ST : RO; bitpos: [5]; default: 0;
 *  the masked interrupt status of hp core1  dbus timeout
 */
#define HP_SYSTEM_CORE1_DBUS_TIMEOUT_INT_ST    (BIT(5))
#define HP_SYSTEM_CORE1_DBUS_TIMEOUT_INT_ST_M  (HP_SYSTEM_CORE1_DBUS_TIMEOUT_INT_ST_V << HP_SYSTEM_CORE1_DBUS_TIMEOUT_INT_ST_S)
#define HP_SYSTEM_CORE1_DBUS_TIMEOUT_INT_ST_V  0x00000001U
#define HP_SYSTEM_CORE1_DBUS_TIMEOUT_INT_ST_S  5

/** HP_SYSTEM_CORE_TIMEOUT_INT_ENA_REG register
 *  masked interrupt register
 */
#define HP_SYSTEM_CORE_TIMEOUT_INT_ENA_REG (DR_REG_HP_SYS_BASE + 0x1b0)
/** HP_SYSTEM_CORE0_AHB_TIMEOUT_INT_ENA : R/W; bitpos: [0]; default: 0;
 *  Write 1 to enable HP_SYSTEM_core0_ahb_timeout int
 */
#define HP_SYSTEM_CORE0_AHB_TIMEOUT_INT_ENA    (BIT(0))
#define HP_SYSTEM_CORE0_AHB_TIMEOUT_INT_ENA_M  (HP_SYSTEM_CORE0_AHB_TIMEOUT_INT_ENA_V << HP_SYSTEM_CORE0_AHB_TIMEOUT_INT_ENA_S)
#define HP_SYSTEM_CORE0_AHB_TIMEOUT_INT_ENA_V  0x00000001U
#define HP_SYSTEM_CORE0_AHB_TIMEOUT_INT_ENA_S  0
/** HP_SYSTEM_CORE1_AHB_TIMEOUT_INT_ENA : R/W; bitpos: [1]; default: 0;
 *  Write 1 to enable HP_SYSTEM_core1_ahb_timeout int
 */
#define HP_SYSTEM_CORE1_AHB_TIMEOUT_INT_ENA    (BIT(1))
#define HP_SYSTEM_CORE1_AHB_TIMEOUT_INT_ENA_M  (HP_SYSTEM_CORE1_AHB_TIMEOUT_INT_ENA_V << HP_SYSTEM_CORE1_AHB_TIMEOUT_INT_ENA_S)
#define HP_SYSTEM_CORE1_AHB_TIMEOUT_INT_ENA_V  0x00000001U
#define HP_SYSTEM_CORE1_AHB_TIMEOUT_INT_ENA_S  1
/** HP_SYSTEM_CORE0_IBUS_TIMEOUT_INT_ENA : R/W; bitpos: [2]; default: 0;
 *  Write 1 to enable HP_SYSTEM_core0_ibus_timeout int
 */
#define HP_SYSTEM_CORE0_IBUS_TIMEOUT_INT_ENA    (BIT(2))
#define HP_SYSTEM_CORE0_IBUS_TIMEOUT_INT_ENA_M  (HP_SYSTEM_CORE0_IBUS_TIMEOUT_INT_ENA_V << HP_SYSTEM_CORE0_IBUS_TIMEOUT_INT_ENA_S)
#define HP_SYSTEM_CORE0_IBUS_TIMEOUT_INT_ENA_V  0x00000001U
#define HP_SYSTEM_CORE0_IBUS_TIMEOUT_INT_ENA_S  2
/** HP_SYSTEM_CORE1_IBUS_TIMEOUT_INT_ENA : R/W; bitpos: [3]; default: 0;
 *  Write 1 to enable HP_SYSTEM_core1_ibus_timeout int
 */
#define HP_SYSTEM_CORE1_IBUS_TIMEOUT_INT_ENA    (BIT(3))
#define HP_SYSTEM_CORE1_IBUS_TIMEOUT_INT_ENA_M  (HP_SYSTEM_CORE1_IBUS_TIMEOUT_INT_ENA_V << HP_SYSTEM_CORE1_IBUS_TIMEOUT_INT_ENA_S)
#define HP_SYSTEM_CORE1_IBUS_TIMEOUT_INT_ENA_V  0x00000001U
#define HP_SYSTEM_CORE1_IBUS_TIMEOUT_INT_ENA_S  3
/** HP_SYSTEM_CORE0_DBUS_TIMEOUT_INT_ENA : R/W; bitpos: [4]; default: 0;
 *  Write 1 to enable HP_SYSTEM_core0_dbus_timeout int
 */
#define HP_SYSTEM_CORE0_DBUS_TIMEOUT_INT_ENA    (BIT(4))
#define HP_SYSTEM_CORE0_DBUS_TIMEOUT_INT_ENA_M  (HP_SYSTEM_CORE0_DBUS_TIMEOUT_INT_ENA_V << HP_SYSTEM_CORE0_DBUS_TIMEOUT_INT_ENA_S)
#define HP_SYSTEM_CORE0_DBUS_TIMEOUT_INT_ENA_V  0x00000001U
#define HP_SYSTEM_CORE0_DBUS_TIMEOUT_INT_ENA_S  4
/** HP_SYSTEM_CORE1_DBUS_TIMEOUT_INT_ENA : R/W; bitpos: [5]; default: 0;
 *  Write 1 to enable HP_SYSTEM_core1_dbus_timeout int
 */
#define HP_SYSTEM_CORE1_DBUS_TIMEOUT_INT_ENA    (BIT(5))
#define HP_SYSTEM_CORE1_DBUS_TIMEOUT_INT_ENA_M  (HP_SYSTEM_CORE1_DBUS_TIMEOUT_INT_ENA_V << HP_SYSTEM_CORE1_DBUS_TIMEOUT_INT_ENA_S)
#define HP_SYSTEM_CORE1_DBUS_TIMEOUT_INT_ENA_V  0x00000001U
#define HP_SYSTEM_CORE1_DBUS_TIMEOUT_INT_ENA_S  5

/** HP_SYSTEM_CORE_TIMEOUT_INT_CLR_REG register
 *  interrupt clear register
 */
#define HP_SYSTEM_CORE_TIMEOUT_INT_CLR_REG (DR_REG_HP_SYS_BASE + 0x1b4)
/** HP_SYSTEM_CORE0_AHB_TIMEOUT_INT_CLR : WT; bitpos: [0]; default: 0;
 *  Write 1 to clear HP_SYSTEM_core0_ahb_timeout int
 */
#define HP_SYSTEM_CORE0_AHB_TIMEOUT_INT_CLR    (BIT(0))
#define HP_SYSTEM_CORE0_AHB_TIMEOUT_INT_CLR_M  (HP_SYSTEM_CORE0_AHB_TIMEOUT_INT_CLR_V << HP_SYSTEM_CORE0_AHB_TIMEOUT_INT_CLR_S)
#define HP_SYSTEM_CORE0_AHB_TIMEOUT_INT_CLR_V  0x00000001U
#define HP_SYSTEM_CORE0_AHB_TIMEOUT_INT_CLR_S  0
/** HP_SYSTEM_CORE1_AHB_TIMEOUT_INT_CLR : WT; bitpos: [1]; default: 0;
 *  Write 1 to clear HP_SYSTEM_core1_ahb_timeout int
 */
#define HP_SYSTEM_CORE1_AHB_TIMEOUT_INT_CLR    (BIT(1))
#define HP_SYSTEM_CORE1_AHB_TIMEOUT_INT_CLR_M  (HP_SYSTEM_CORE1_AHB_TIMEOUT_INT_CLR_V << HP_SYSTEM_CORE1_AHB_TIMEOUT_INT_CLR_S)
#define HP_SYSTEM_CORE1_AHB_TIMEOUT_INT_CLR_V  0x00000001U
#define HP_SYSTEM_CORE1_AHB_TIMEOUT_INT_CLR_S  1
/** HP_SYSTEM_CORE0_IBUS_TIMEOUT_INT_CLR : WT; bitpos: [2]; default: 0;
 *  Write 1 to clear HP_SYSTEM_core0_ibus_timeout int
 */
#define HP_SYSTEM_CORE0_IBUS_TIMEOUT_INT_CLR    (BIT(2))
#define HP_SYSTEM_CORE0_IBUS_TIMEOUT_INT_CLR_M  (HP_SYSTEM_CORE0_IBUS_TIMEOUT_INT_CLR_V << HP_SYSTEM_CORE0_IBUS_TIMEOUT_INT_CLR_S)
#define HP_SYSTEM_CORE0_IBUS_TIMEOUT_INT_CLR_V  0x00000001U
#define HP_SYSTEM_CORE0_IBUS_TIMEOUT_INT_CLR_S  2
/** HP_SYSTEM_CORE1_IBUS_TIMEOUT_INT_CLR : WT; bitpos: [3]; default: 0;
 *  Write 1 to clear HP_SYSTEM_core1_ibus_timeout int
 */
#define HP_SYSTEM_CORE1_IBUS_TIMEOUT_INT_CLR    (BIT(3))
#define HP_SYSTEM_CORE1_IBUS_TIMEOUT_INT_CLR_M  (HP_SYSTEM_CORE1_IBUS_TIMEOUT_INT_CLR_V << HP_SYSTEM_CORE1_IBUS_TIMEOUT_INT_CLR_S)
#define HP_SYSTEM_CORE1_IBUS_TIMEOUT_INT_CLR_V  0x00000001U
#define HP_SYSTEM_CORE1_IBUS_TIMEOUT_INT_CLR_S  3
/** HP_SYSTEM_CORE0_DBUS_TIMEOUT_INT_CLR : WT; bitpos: [4]; default: 0;
 *  Write 1 to clear HP_SYSTEM_core0_dbus_timeout int
 */
#define HP_SYSTEM_CORE0_DBUS_TIMEOUT_INT_CLR    (BIT(4))
#define HP_SYSTEM_CORE0_DBUS_TIMEOUT_INT_CLR_M  (HP_SYSTEM_CORE0_DBUS_TIMEOUT_INT_CLR_V << HP_SYSTEM_CORE0_DBUS_TIMEOUT_INT_CLR_S)
#define HP_SYSTEM_CORE0_DBUS_TIMEOUT_INT_CLR_V  0x00000001U
#define HP_SYSTEM_CORE0_DBUS_TIMEOUT_INT_CLR_S  4
/** HP_SYSTEM_CORE1_DBUS_TIMEOUT_INT_CLR : WT; bitpos: [5]; default: 0;
 *  Write 1 to clear HP_SYSTEM_core1_dbus_timeout int
 */
#define HP_SYSTEM_CORE1_DBUS_TIMEOUT_INT_CLR    (BIT(5))
#define HP_SYSTEM_CORE1_DBUS_TIMEOUT_INT_CLR_M  (HP_SYSTEM_CORE1_DBUS_TIMEOUT_INT_CLR_V << HP_SYSTEM_CORE1_DBUS_TIMEOUT_INT_CLR_S)
#define HP_SYSTEM_CORE1_DBUS_TIMEOUT_INT_CLR_V  0x00000001U
#define HP_SYSTEM_CORE1_DBUS_TIMEOUT_INT_CLR_S  5

/** HP_SYSTEM_GPIO_O_HYS_CTRL0_REG register
 *  NA
 */
#define HP_SYSTEM_GPIO_O_HYS_CTRL0_REG (DR_REG_HP_SYS_BASE + 0x1c0)
/** HP_SYSTEM_REG_GPIO_0_HYS_LOW : R/W; bitpos: [31:0]; default: 0;
 *  hys control for gpio47~16
 */
#define HP_SYSTEM_REG_GPIO_0_HYS_LOW    0xFFFFFFFFU
#define HP_SYSTEM_REG_GPIO_0_HYS_LOW_M  (HP_SYSTEM_REG_GPIO_0_HYS_LOW_V << HP_SYSTEM_REG_GPIO_0_HYS_LOW_S)
#define HP_SYSTEM_REG_GPIO_0_HYS_LOW_V  0xFFFFFFFFU
#define HP_SYSTEM_REG_GPIO_0_HYS_LOW_S  0

/** HP_SYSTEM_GPIO_O_HYS_CTRL1_REG register
 *  NA
 */
#define HP_SYSTEM_GPIO_O_HYS_CTRL1_REG (DR_REG_HP_SYS_BASE + 0x1c4)
/** HP_SYSTEM_REG_GPIO_0_HYS_HIGH : R/W; bitpos: [8:0]; default: 0;
 *  hys control for gpio56~48
 */
#define HP_SYSTEM_REG_GPIO_0_HYS_HIGH    0x000001FFU
#define HP_SYSTEM_REG_GPIO_0_HYS_HIGH_M  (HP_SYSTEM_REG_GPIO_0_HYS_HIGH_V << HP_SYSTEM_REG_GPIO_0_HYS_HIGH_S)
#define HP_SYSTEM_REG_GPIO_0_HYS_HIGH_V  0x000001FFU
#define HP_SYSTEM_REG_GPIO_0_HYS_HIGH_S  0

/** HP_SYSTEM_RSA_PD_CTRL_REG register
 *  rsa pd ctrl register
 */
#define HP_SYSTEM_RSA_PD_CTRL_REG (DR_REG_HP_SYS_BASE + 0x1d0)
/** HP_SYSTEM_RSA_MEM_FORCE_PD : R/W; bitpos: [0]; default: 0;
 *  Set this bit to power down rsa internal memory.
 */
#define HP_SYSTEM_RSA_MEM_FORCE_PD    (BIT(0))
#define HP_SYSTEM_RSA_MEM_FORCE_PD_M  (HP_SYSTEM_RSA_MEM_FORCE_PD_V << HP_SYSTEM_RSA_MEM_FORCE_PD_S)
#define HP_SYSTEM_RSA_MEM_FORCE_PD_V  0x00000001U
#define HP_SYSTEM_RSA_MEM_FORCE_PD_S  0
/** HP_SYSTEM_RSA_MEM_FORCE_PU : R/W; bitpos: [1]; default: 1;
 *  Set this bit to force power up rsa internal memory
 */
#define HP_SYSTEM_RSA_MEM_FORCE_PU    (BIT(1))
#define HP_SYSTEM_RSA_MEM_FORCE_PU_M  (HP_SYSTEM_RSA_MEM_FORCE_PU_V << HP_SYSTEM_RSA_MEM_FORCE_PU_S)
#define HP_SYSTEM_RSA_MEM_FORCE_PU_V  0x00000001U
#define HP_SYSTEM_RSA_MEM_FORCE_PU_S  1
/** HP_SYSTEM_RSA_MEM_PD : R/W; bitpos: [2]; default: 0;
 *  Set this bit to force power down rsa internal memory.
 */
#define HP_SYSTEM_RSA_MEM_PD    (BIT(2))
#define HP_SYSTEM_RSA_MEM_PD_M  (HP_SYSTEM_RSA_MEM_PD_V << HP_SYSTEM_RSA_MEM_PD_S)
#define HP_SYSTEM_RSA_MEM_PD_V  0x00000001U
#define HP_SYSTEM_RSA_MEM_PD_S  2

/** HP_SYSTEM_ECC_PD_CTRL_REG register
 *  ecc pd ctrl register
 */
#define HP_SYSTEM_ECC_PD_CTRL_REG (DR_REG_HP_SYS_BASE + 0x1d4)
/** HP_SYSTEM_ECC_MEM_FORCE_PD : R/W; bitpos: [0]; default: 0;
 *  Set this bit to power down ecc internal memory.
 */
#define HP_SYSTEM_ECC_MEM_FORCE_PD    (BIT(0))
#define HP_SYSTEM_ECC_MEM_FORCE_PD_M  (HP_SYSTEM_ECC_MEM_FORCE_PD_V << HP_SYSTEM_ECC_MEM_FORCE_PD_S)
#define HP_SYSTEM_ECC_MEM_FORCE_PD_V  0x00000001U
#define HP_SYSTEM_ECC_MEM_FORCE_PD_S  0
/** HP_SYSTEM_ECC_MEM_FORCE_PU : R/W; bitpos: [1]; default: 1;
 *  Set this bit to force power up ecc internal memory
 */
#define HP_SYSTEM_ECC_MEM_FORCE_PU    (BIT(1))
#define HP_SYSTEM_ECC_MEM_FORCE_PU_M  (HP_SYSTEM_ECC_MEM_FORCE_PU_V << HP_SYSTEM_ECC_MEM_FORCE_PU_S)
#define HP_SYSTEM_ECC_MEM_FORCE_PU_V  0x00000001U
#define HP_SYSTEM_ECC_MEM_FORCE_PU_S  1
/** HP_SYSTEM_ECC_MEM_PD : R/W; bitpos: [2]; default: 0;
 *  Set this bit to force power down ecc internal memory.
 */
#define HP_SYSTEM_ECC_MEM_PD    (BIT(2))
#define HP_SYSTEM_ECC_MEM_PD_M  (HP_SYSTEM_ECC_MEM_PD_V << HP_SYSTEM_ECC_MEM_PD_S)
#define HP_SYSTEM_ECC_MEM_PD_V  0x00000001U
#define HP_SYSTEM_ECC_MEM_PD_S  2

/** HP_SYSTEM_RNG_CFG_REG register
 *  rng cfg register
 */
#define HP_SYSTEM_RNG_CFG_REG (DR_REG_HP_SYS_BASE + 0x1d8)
/** HP_SYSTEM_RNG_SAMPLE_ENABLE : R/W; bitpos: [0]; default: 0;
 *  enable rng sample chain
 */
#define HP_SYSTEM_RNG_SAMPLE_ENABLE    (BIT(0))
#define HP_SYSTEM_RNG_SAMPLE_ENABLE_M  (HP_SYSTEM_RNG_SAMPLE_ENABLE_V << HP_SYSTEM_RNG_SAMPLE_ENABLE_S)
#define HP_SYSTEM_RNG_SAMPLE_ENABLE_V  0x00000001U
#define HP_SYSTEM_RNG_SAMPLE_ENABLE_S  0
/** HP_SYSTEM_RNG_CHAIN_CLK_DIV_NUM : R/W; bitpos: [23:16]; default: 0;
 *  chain clk div num to pad for debug
 */
#define HP_SYSTEM_RNG_CHAIN_CLK_DIV_NUM    0x000000FFU
#define HP_SYSTEM_RNG_CHAIN_CLK_DIV_NUM_M  (HP_SYSTEM_RNG_CHAIN_CLK_DIV_NUM_V << HP_SYSTEM_RNG_CHAIN_CLK_DIV_NUM_S)
#define HP_SYSTEM_RNG_CHAIN_CLK_DIV_NUM_V  0x000000FFU
#define HP_SYSTEM_RNG_CHAIN_CLK_DIV_NUM_S  16
/** HP_SYSTEM_RNG_SAMPLE_CNT : RO; bitpos: [31:24]; default: 0;
 *  debug rng sample cnt
 */
#define HP_SYSTEM_RNG_SAMPLE_CNT    0x000000FFU
#define HP_SYSTEM_RNG_SAMPLE_CNT_M  (HP_SYSTEM_RNG_SAMPLE_CNT_V << HP_SYSTEM_RNG_SAMPLE_CNT_S)
#define HP_SYSTEM_RNG_SAMPLE_CNT_V  0x000000FFU
#define HP_SYSTEM_RNG_SAMPLE_CNT_S  24

/** HP_SYSTEM_UART_PD_CTRL_REG register
 *  ecc pd ctrl register
 */
#define HP_SYSTEM_UART_PD_CTRL_REG (DR_REG_HP_SYS_BASE + 0x1dc)
/** HP_SYSTEM_UART_MEM_FORCE_PD : R/W; bitpos: [0]; default: 0;
 *  Set this bit to power down hp uart internal memory.
 */
#define HP_SYSTEM_UART_MEM_FORCE_PD    (BIT(0))
#define HP_SYSTEM_UART_MEM_FORCE_PD_M  (HP_SYSTEM_UART_MEM_FORCE_PD_V << HP_SYSTEM_UART_MEM_FORCE_PD_S)
#define HP_SYSTEM_UART_MEM_FORCE_PD_V  0x00000001U
#define HP_SYSTEM_UART_MEM_FORCE_PD_S  0
/** HP_SYSTEM_UART_MEM_FORCE_PU : R/W; bitpos: [1]; default: 1;
 *  Set this bit to force power up hp uart  internal memory
 */
#define HP_SYSTEM_UART_MEM_FORCE_PU    (BIT(1))
#define HP_SYSTEM_UART_MEM_FORCE_PU_M  (HP_SYSTEM_UART_MEM_FORCE_PU_V << HP_SYSTEM_UART_MEM_FORCE_PU_S)
#define HP_SYSTEM_UART_MEM_FORCE_PU_V  0x00000001U
#define HP_SYSTEM_UART_MEM_FORCE_PU_S  1

/** HP_SYSTEM_PERI_MEM_CLK_FORCE_ON_REG register
 *  hp peri mem clk force on regpster
 */
#define HP_SYSTEM_PERI_MEM_CLK_FORCE_ON_REG (DR_REG_HP_SYS_BASE + 0x1e0)
/** HP_SYSTEM_RMT_MEM_CLK_FORCE_ON : R/W; bitpos: [0]; default: 0;
 *  Set this bit to force on mem clk in rmt
 */
#define HP_SYSTEM_RMT_MEM_CLK_FORCE_ON    (BIT(0))
#define HP_SYSTEM_RMT_MEM_CLK_FORCE_ON_M  (HP_SYSTEM_RMT_MEM_CLK_FORCE_ON_V << HP_SYSTEM_RMT_MEM_CLK_FORCE_ON_S)
#define HP_SYSTEM_RMT_MEM_CLK_FORCE_ON_V  0x00000001U
#define HP_SYSTEM_RMT_MEM_CLK_FORCE_ON_S  0
/** HP_SYSTEM_BITSCRAMBLER_TX_MEM_CLK_FORCE_ON : R/W; bitpos: [1]; default: 0;
 *  Set this bit to force on tx mem clk in bitscrambler
 */
#define HP_SYSTEM_BITSCRAMBLER_TX_MEM_CLK_FORCE_ON    (BIT(1))
#define HP_SYSTEM_BITSCRAMBLER_TX_MEM_CLK_FORCE_ON_M  (HP_SYSTEM_BITSCRAMBLER_TX_MEM_CLK_FORCE_ON_V << HP_SYSTEM_BITSCRAMBLER_TX_MEM_CLK_FORCE_ON_S)
#define HP_SYSTEM_BITSCRAMBLER_TX_MEM_CLK_FORCE_ON_V  0x00000001U
#define HP_SYSTEM_BITSCRAMBLER_TX_MEM_CLK_FORCE_ON_S  1
/** HP_SYSTEM_BITSCRAMBLER_RX_MEM_CLK_FORCE_ON : R/W; bitpos: [2]; default: 0;
 *  Set this bit to force on rx mem clk in bitscrambler
 */
#define HP_SYSTEM_BITSCRAMBLER_RX_MEM_CLK_FORCE_ON    (BIT(2))
#define HP_SYSTEM_BITSCRAMBLER_RX_MEM_CLK_FORCE_ON_M  (HP_SYSTEM_BITSCRAMBLER_RX_MEM_CLK_FORCE_ON_V << HP_SYSTEM_BITSCRAMBLER_RX_MEM_CLK_FORCE_ON_S)
#define HP_SYSTEM_BITSCRAMBLER_RX_MEM_CLK_FORCE_ON_V  0x00000001U
#define HP_SYSTEM_BITSCRAMBLER_RX_MEM_CLK_FORCE_ON_S  2
/** HP_SYSTEM_GDMA_MEM_CLK_FORCE_ON : R/W; bitpos: [3]; default: 0;
 *  Set this bit to force on mem clk in gdma
 */
#define HP_SYSTEM_GDMA_MEM_CLK_FORCE_ON    (BIT(3))
#define HP_SYSTEM_GDMA_MEM_CLK_FORCE_ON_M  (HP_SYSTEM_GDMA_MEM_CLK_FORCE_ON_V << HP_SYSTEM_GDMA_MEM_CLK_FORCE_ON_S)
#define HP_SYSTEM_GDMA_MEM_CLK_FORCE_ON_V  0x00000001U
#define HP_SYSTEM_GDMA_MEM_CLK_FORCE_ON_S  3

#ifdef __cplusplus
}
#endif
