/**
 * SPDX-FileCopyrightText: 2023 Espressif Systems (Shanghai) CO LTD
 *
 *  SPDX-License-Identifier: Apache-2.0
 */
#pragma once

#include <stdint.h>
#include "soc/soc.h"
#ifdef __cplusplus
extern "C" {
#endif

/** DSI_HOST_VERSION_REG register
 *  NA
 */
#define DSI_HOST_VERSION_REG (DR_REG_DSI_HOST_BASE + 0x0)
/** DSI_HOST_VERSION : RO; bitpos: [31:0]; default: 825504042;
 *  NA
 */
#define DSI_HOST_VERSION    0xFFFFFFFFU
#define DSI_HOST_VERSION_M  (DSI_HOST_VERSION_V << DSI_HOST_VERSION_S)
#define DSI_HOST_VERSION_V  0xFFFFFFFFU
#define DSI_HOST_VERSION_S  0

/** DSI_HOST_PWR_UP_REG register
 *  NA
 */
#define DSI_HOST_PWR_UP_REG (DR_REG_DSI_HOST_BASE + 0x4)
/** DSI_HOST_SHUTDOWNZ : R/W; bitpos: [0]; default: 0;
 *  NA
 */
#define DSI_HOST_SHUTDOWNZ    (BIT(0))
#define DSI_HOST_SHUTDOWNZ_M  (DSI_HOST_SHUTDOWNZ_V << DSI_HOST_SHUTDOWNZ_S)
#define DSI_HOST_SHUTDOWNZ_V  0x00000001U
#define DSI_HOST_SHUTDOWNZ_S  0

/** DSI_HOST_CLKMGR_CFG_REG register
 *  NA
 */
#define DSI_HOST_CLKMGR_CFG_REG (DR_REG_DSI_HOST_BASE + 0x8)
/** DSI_HOST_TX_ESC_CLK_DIVISION : R/W; bitpos: [7:0]; default: 0;
 *  NA
 */
#define DSI_HOST_TX_ESC_CLK_DIVISION    0x000000FFU
#define DSI_HOST_TX_ESC_CLK_DIVISION_M  (DSI_HOST_TX_ESC_CLK_DIVISION_V << DSI_HOST_TX_ESC_CLK_DIVISION_S)
#define DSI_HOST_TX_ESC_CLK_DIVISION_V  0x000000FFU
#define DSI_HOST_TX_ESC_CLK_DIVISION_S  0
/** DSI_HOST_TO_CLK_DIVISION : R/W; bitpos: [15:8]; default: 0;
 *  NA
 */
#define DSI_HOST_TO_CLK_DIVISION    0x000000FFU
#define DSI_HOST_TO_CLK_DIVISION_M  (DSI_HOST_TO_CLK_DIVISION_V << DSI_HOST_TO_CLK_DIVISION_S)
#define DSI_HOST_TO_CLK_DIVISION_V  0x000000FFU
#define DSI_HOST_TO_CLK_DIVISION_S  8

/** DSI_HOST_DPI_VCID_REG register
 *  NA
 */
#define DSI_HOST_DPI_VCID_REG (DR_REG_DSI_HOST_BASE + 0xc)
/** DSI_HOST_DPI_VCID : R/W; bitpos: [1:0]; default: 0;
 *  NA
 */
#define DSI_HOST_DPI_VCID    0x00000003U
#define DSI_HOST_DPI_VCID_M  (DSI_HOST_DPI_VCID_V << DSI_HOST_DPI_VCID_S)
#define DSI_HOST_DPI_VCID_V  0x00000003U
#define DSI_HOST_DPI_VCID_S  0

/** DSI_HOST_DPI_COLOR_CODING_REG register
 *  NA
 */
#define DSI_HOST_DPI_COLOR_CODING_REG (DR_REG_DSI_HOST_BASE + 0x10)
/** DSI_HOST_DPI_COLOR_CODING : R/W; bitpos: [3:0]; default: 0;
 *  NA
 */
#define DSI_HOST_DPI_COLOR_CODING    0x0000000FU
#define DSI_HOST_DPI_COLOR_CODING_M  (DSI_HOST_DPI_COLOR_CODING_V << DSI_HOST_DPI_COLOR_CODING_S)
#define DSI_HOST_DPI_COLOR_CODING_V  0x0000000FU
#define DSI_HOST_DPI_COLOR_CODING_S  0
/** DSI_HOST_LOOSELY18_EN : R/W; bitpos: [8]; default: 0;
 *  NA
 */
#define DSI_HOST_LOOSELY18_EN    (BIT(8))
#define DSI_HOST_LOOSELY18_EN_M  (DSI_HOST_LOOSELY18_EN_V << DSI_HOST_LOOSELY18_EN_S)
#define DSI_HOST_LOOSELY18_EN_V  0x00000001U
#define DSI_HOST_LOOSELY18_EN_S  8

/** DSI_HOST_DPI_CFG_POL_REG register
 *  NA
 */
#define DSI_HOST_DPI_CFG_POL_REG (DR_REG_DSI_HOST_BASE + 0x14)
/** DSI_HOST_DATAEN_ACTIVE_LOW : R/W; bitpos: [0]; default: 0;
 *  NA
 */
#define DSI_HOST_DATAEN_ACTIVE_LOW    (BIT(0))
#define DSI_HOST_DATAEN_ACTIVE_LOW_M  (DSI_HOST_DATAEN_ACTIVE_LOW_V << DSI_HOST_DATAEN_ACTIVE_LOW_S)
#define DSI_HOST_DATAEN_ACTIVE_LOW_V  0x00000001U
#define DSI_HOST_DATAEN_ACTIVE_LOW_S  0
/** DSI_HOST_VSYNC_ACTIVE_LOW : R/W; bitpos: [1]; default: 0;
 *  NA
 */
#define DSI_HOST_VSYNC_ACTIVE_LOW    (BIT(1))
#define DSI_HOST_VSYNC_ACTIVE_LOW_M  (DSI_HOST_VSYNC_ACTIVE_LOW_V << DSI_HOST_VSYNC_ACTIVE_LOW_S)
#define DSI_HOST_VSYNC_ACTIVE_LOW_V  0x00000001U
#define DSI_HOST_VSYNC_ACTIVE_LOW_S  1
/** DSI_HOST_HSYNC_ACTIVE_LOW : R/W; bitpos: [2]; default: 0;
 *  NA
 */
#define DSI_HOST_HSYNC_ACTIVE_LOW    (BIT(2))
#define DSI_HOST_HSYNC_ACTIVE_LOW_M  (DSI_HOST_HSYNC_ACTIVE_LOW_V << DSI_HOST_HSYNC_ACTIVE_LOW_S)
#define DSI_HOST_HSYNC_ACTIVE_LOW_V  0x00000001U
#define DSI_HOST_HSYNC_ACTIVE_LOW_S  2
/** DSI_HOST_SHUTD_ACTIVE_LOW : R/W; bitpos: [3]; default: 0;
 *  NA
 */
#define DSI_HOST_SHUTD_ACTIVE_LOW    (BIT(3))
#define DSI_HOST_SHUTD_ACTIVE_LOW_M  (DSI_HOST_SHUTD_ACTIVE_LOW_V << DSI_HOST_SHUTD_ACTIVE_LOW_S)
#define DSI_HOST_SHUTD_ACTIVE_LOW_V  0x00000001U
#define DSI_HOST_SHUTD_ACTIVE_LOW_S  3
/** DSI_HOST_COLORM_ACTIVE_LOW : R/W; bitpos: [4]; default: 0;
 *  NA
 */
#define DSI_HOST_COLORM_ACTIVE_LOW    (BIT(4))
#define DSI_HOST_COLORM_ACTIVE_LOW_M  (DSI_HOST_COLORM_ACTIVE_LOW_V << DSI_HOST_COLORM_ACTIVE_LOW_S)
#define DSI_HOST_COLORM_ACTIVE_LOW_V  0x00000001U
#define DSI_HOST_COLORM_ACTIVE_LOW_S  4

/** DSI_HOST_DPI_LP_CMD_TIM_REG register
 *  NA
 */
#define DSI_HOST_DPI_LP_CMD_TIM_REG (DR_REG_DSI_HOST_BASE + 0x18)
/** DSI_HOST_INVACT_LPCMD_TIME : R/W; bitpos: [7:0]; default: 0;
 *  NA
 */
#define DSI_HOST_INVACT_LPCMD_TIME    0x000000FFU
#define DSI_HOST_INVACT_LPCMD_TIME_M  (DSI_HOST_INVACT_LPCMD_TIME_V << DSI_HOST_INVACT_LPCMD_TIME_S)
#define DSI_HOST_INVACT_LPCMD_TIME_V  0x000000FFU
#define DSI_HOST_INVACT_LPCMD_TIME_S  0
/** DSI_HOST_OUTVACT_LPCMD_TIME : R/W; bitpos: [23:16]; default: 0;
 *  NA
 */
#define DSI_HOST_OUTVACT_LPCMD_TIME    0x000000FFU
#define DSI_HOST_OUTVACT_LPCMD_TIME_M  (DSI_HOST_OUTVACT_LPCMD_TIME_V << DSI_HOST_OUTVACT_LPCMD_TIME_S)
#define DSI_HOST_OUTVACT_LPCMD_TIME_V  0x000000FFU
#define DSI_HOST_OUTVACT_LPCMD_TIME_S  16

/** DSI_HOST_DBI_VCID_REG register
 *  NA
 */
#define DSI_HOST_DBI_VCID_REG (DR_REG_DSI_HOST_BASE + 0x1c)
/** DSI_HOST_DBI_VCID : R/W; bitpos: [1:0]; default: 0;
 *  NA
 */
#define DSI_HOST_DBI_VCID    0x00000003U
#define DSI_HOST_DBI_VCID_M  (DSI_HOST_DBI_VCID_V << DSI_HOST_DBI_VCID_S)
#define DSI_HOST_DBI_VCID_V  0x00000003U
#define DSI_HOST_DBI_VCID_S  0

/** DSI_HOST_DBI_CFG_REG register
 *  NA
 */
#define DSI_HOST_DBI_CFG_REG (DR_REG_DSI_HOST_BASE + 0x20)
/** DSI_HOST_IN_DBI_CONF : R/W; bitpos: [3:0]; default: 0;
 *  NA
 */
#define DSI_HOST_IN_DBI_CONF    0x0000000FU
#define DSI_HOST_IN_DBI_CONF_M  (DSI_HOST_IN_DBI_CONF_V << DSI_HOST_IN_DBI_CONF_S)
#define DSI_HOST_IN_DBI_CONF_V  0x0000000FU
#define DSI_HOST_IN_DBI_CONF_S  0
/** DSI_HOST_OUT_DBI_CONF : R/W; bitpos: [11:8]; default: 0;
 *  NA
 */
#define DSI_HOST_OUT_DBI_CONF    0x0000000FU
#define DSI_HOST_OUT_DBI_CONF_M  (DSI_HOST_OUT_DBI_CONF_V << DSI_HOST_OUT_DBI_CONF_S)
#define DSI_HOST_OUT_DBI_CONF_V  0x0000000FU
#define DSI_HOST_OUT_DBI_CONF_S  8
/** DSI_HOST_LUT_SIZE_CONF : R/W; bitpos: [17:16]; default: 0;
 *  NA
 */
#define DSI_HOST_LUT_SIZE_CONF    0x00000003U
#define DSI_HOST_LUT_SIZE_CONF_M  (DSI_HOST_LUT_SIZE_CONF_V << DSI_HOST_LUT_SIZE_CONF_S)
#define DSI_HOST_LUT_SIZE_CONF_V  0x00000003U
#define DSI_HOST_LUT_SIZE_CONF_S  16

/** DSI_HOST_DBI_PARTITIONING_EN_REG register
 *  NA
 */
#define DSI_HOST_DBI_PARTITIONING_EN_REG (DR_REG_DSI_HOST_BASE + 0x24)
/** DSI_HOST_PARTITIONING_EN : R/W; bitpos: [0]; default: 0;
 *  NA
 */
#define DSI_HOST_PARTITIONING_EN    (BIT(0))
#define DSI_HOST_PARTITIONING_EN_M  (DSI_HOST_PARTITIONING_EN_V << DSI_HOST_PARTITIONING_EN_S)
#define DSI_HOST_PARTITIONING_EN_V  0x00000001U
#define DSI_HOST_PARTITIONING_EN_S  0

/** DSI_HOST_DBI_CMDSIZE_REG register
 *  NA
 */
#define DSI_HOST_DBI_CMDSIZE_REG (DR_REG_DSI_HOST_BASE + 0x28)
/** DSI_HOST_WR_CMD_SIZE : R/W; bitpos: [15:0]; default: 0;
 *  NA
 */
#define DSI_HOST_WR_CMD_SIZE    0x0000FFFFU
#define DSI_HOST_WR_CMD_SIZE_M  (DSI_HOST_WR_CMD_SIZE_V << DSI_HOST_WR_CMD_SIZE_S)
#define DSI_HOST_WR_CMD_SIZE_V  0x0000FFFFU
#define DSI_HOST_WR_CMD_SIZE_S  0
/** DSI_HOST_ALLOWED_CMD_SIZE : R/W; bitpos: [31:16]; default: 0;
 *  NA
 */
#define DSI_HOST_ALLOWED_CMD_SIZE    0x0000FFFFU
#define DSI_HOST_ALLOWED_CMD_SIZE_M  (DSI_HOST_ALLOWED_CMD_SIZE_V << DSI_HOST_ALLOWED_CMD_SIZE_S)
#define DSI_HOST_ALLOWED_CMD_SIZE_V  0x0000FFFFU
#define DSI_HOST_ALLOWED_CMD_SIZE_S  16

/** DSI_HOST_PCKHDL_CFG_REG register
 *  NA
 */
#define DSI_HOST_PCKHDL_CFG_REG (DR_REG_DSI_HOST_BASE + 0x2c)
/** DSI_HOST_EOTP_TX_EN : R/W; bitpos: [0]; default: 0;
 *  NA
 */
#define DSI_HOST_EOTP_TX_EN    (BIT(0))
#define DSI_HOST_EOTP_TX_EN_M  (DSI_HOST_EOTP_TX_EN_V << DSI_HOST_EOTP_TX_EN_S)
#define DSI_HOST_EOTP_TX_EN_V  0x00000001U
#define DSI_HOST_EOTP_TX_EN_S  0
/** DSI_HOST_EOTP_RX_EN : R/W; bitpos: [1]; default: 0;
 *  NA
 */
#define DSI_HOST_EOTP_RX_EN    (BIT(1))
#define DSI_HOST_EOTP_RX_EN_M  (DSI_HOST_EOTP_RX_EN_V << DSI_HOST_EOTP_RX_EN_S)
#define DSI_HOST_EOTP_RX_EN_V  0x00000001U
#define DSI_HOST_EOTP_RX_EN_S  1
/** DSI_HOST_BTA_EN : R/W; bitpos: [2]; default: 0;
 *  NA
 */
#define DSI_HOST_BTA_EN    (BIT(2))
#define DSI_HOST_BTA_EN_M  (DSI_HOST_BTA_EN_V << DSI_HOST_BTA_EN_S)
#define DSI_HOST_BTA_EN_V  0x00000001U
#define DSI_HOST_BTA_EN_S  2
/** DSI_HOST_ECC_RX_EN : R/W; bitpos: [3]; default: 0;
 *  NA
 */
#define DSI_HOST_ECC_RX_EN    (BIT(3))
#define DSI_HOST_ECC_RX_EN_M  (DSI_HOST_ECC_RX_EN_V << DSI_HOST_ECC_RX_EN_S)
#define DSI_HOST_ECC_RX_EN_V  0x00000001U
#define DSI_HOST_ECC_RX_EN_S  3
/** DSI_HOST_CRC_RX_EN : R/W; bitpos: [4]; default: 0;
 *  NA
 */
#define DSI_HOST_CRC_RX_EN    (BIT(4))
#define DSI_HOST_CRC_RX_EN_M  (DSI_HOST_CRC_RX_EN_V << DSI_HOST_CRC_RX_EN_S)
#define DSI_HOST_CRC_RX_EN_V  0x00000001U
#define DSI_HOST_CRC_RX_EN_S  4
/** DSI_HOST_EOTP_TX_LP_EN : R/W; bitpos: [5]; default: 0;
 *  NA
 */
#define DSI_HOST_EOTP_TX_LP_EN    (BIT(5))
#define DSI_HOST_EOTP_TX_LP_EN_M  (DSI_HOST_EOTP_TX_LP_EN_V << DSI_HOST_EOTP_TX_LP_EN_S)
#define DSI_HOST_EOTP_TX_LP_EN_V  0x00000001U
#define DSI_HOST_EOTP_TX_LP_EN_S  5

/** DSI_HOST_GEN_VCID_REG register
 *  NA
 */
#define DSI_HOST_GEN_VCID_REG (DR_REG_DSI_HOST_BASE + 0x30)
/** DSI_HOST_GEN_VCID_RX : R/W; bitpos: [1:0]; default: 0;
 *  NA
 */
#define DSI_HOST_GEN_VCID_RX    0x00000003U
#define DSI_HOST_GEN_VCID_RX_M  (DSI_HOST_GEN_VCID_RX_V << DSI_HOST_GEN_VCID_RX_S)
#define DSI_HOST_GEN_VCID_RX_V  0x00000003U
#define DSI_HOST_GEN_VCID_RX_S  0
/** DSI_HOST_GEN_VCID_TEAR_AUTO : R/W; bitpos: [9:8]; default: 0;
 *  NA
 */
#define DSI_HOST_GEN_VCID_TEAR_AUTO    0x00000003U
#define DSI_HOST_GEN_VCID_TEAR_AUTO_M  (DSI_HOST_GEN_VCID_TEAR_AUTO_V << DSI_HOST_GEN_VCID_TEAR_AUTO_S)
#define DSI_HOST_GEN_VCID_TEAR_AUTO_V  0x00000003U
#define DSI_HOST_GEN_VCID_TEAR_AUTO_S  8
/** DSI_HOST_GEN_VCID_TX_AUTO : R/W; bitpos: [17:16]; default: 0;
 *  NA
 */
#define DSI_HOST_GEN_VCID_TX_AUTO    0x00000003U
#define DSI_HOST_GEN_VCID_TX_AUTO_M  (DSI_HOST_GEN_VCID_TX_AUTO_V << DSI_HOST_GEN_VCID_TX_AUTO_S)
#define DSI_HOST_GEN_VCID_TX_AUTO_V  0x00000003U
#define DSI_HOST_GEN_VCID_TX_AUTO_S  16

/** DSI_HOST_MODE_CFG_REG register
 *  NA
 */
#define DSI_HOST_MODE_CFG_REG (DR_REG_DSI_HOST_BASE + 0x34)
/** DSI_HOST_CMD_VIDEO_MODE : R/W; bitpos: [0]; default: 1;
 *  NA
 */
#define DSI_HOST_CMD_VIDEO_MODE    (BIT(0))
#define DSI_HOST_CMD_VIDEO_MODE_M  (DSI_HOST_CMD_VIDEO_MODE_V << DSI_HOST_CMD_VIDEO_MODE_S)
#define DSI_HOST_CMD_VIDEO_MODE_V  0x00000001U
#define DSI_HOST_CMD_VIDEO_MODE_S  0

/** DSI_HOST_VID_MODE_CFG_REG register
 *  NA
 */
#define DSI_HOST_VID_MODE_CFG_REG (DR_REG_DSI_HOST_BASE + 0x38)
/** DSI_HOST_VID_MODE_TYPE : R/W; bitpos: [1:0]; default: 0;
 *  NA
 */
#define DSI_HOST_VID_MODE_TYPE    0x00000003U
#define DSI_HOST_VID_MODE_TYPE_M  (DSI_HOST_VID_MODE_TYPE_V << DSI_HOST_VID_MODE_TYPE_S)
#define DSI_HOST_VID_MODE_TYPE_V  0x00000003U
#define DSI_HOST_VID_MODE_TYPE_S  0
/** DSI_HOST_LP_VSA_EN : R/W; bitpos: [8]; default: 0;
 *  NA
 */
#define DSI_HOST_LP_VSA_EN    (BIT(8))
#define DSI_HOST_LP_VSA_EN_M  (DSI_HOST_LP_VSA_EN_V << DSI_HOST_LP_VSA_EN_S)
#define DSI_HOST_LP_VSA_EN_V  0x00000001U
#define DSI_HOST_LP_VSA_EN_S  8
/** DSI_HOST_LP_VBP_EN : R/W; bitpos: [9]; default: 0;
 *  NA
 */
#define DSI_HOST_LP_VBP_EN    (BIT(9))
#define DSI_HOST_LP_VBP_EN_M  (DSI_HOST_LP_VBP_EN_V << DSI_HOST_LP_VBP_EN_S)
#define DSI_HOST_LP_VBP_EN_V  0x00000001U
#define DSI_HOST_LP_VBP_EN_S  9
/** DSI_HOST_LP_VFP_EN : R/W; bitpos: [10]; default: 0;
 *  NA
 */
#define DSI_HOST_LP_VFP_EN    (BIT(10))
#define DSI_HOST_LP_VFP_EN_M  (DSI_HOST_LP_VFP_EN_V << DSI_HOST_LP_VFP_EN_S)
#define DSI_HOST_LP_VFP_EN_V  0x00000001U
#define DSI_HOST_LP_VFP_EN_S  10
/** DSI_HOST_LP_VACT_EN : R/W; bitpos: [11]; default: 0;
 *  NA
 */
#define DSI_HOST_LP_VACT_EN    (BIT(11))
#define DSI_HOST_LP_VACT_EN_M  (DSI_HOST_LP_VACT_EN_V << DSI_HOST_LP_VACT_EN_S)
#define DSI_HOST_LP_VACT_EN_V  0x00000001U
#define DSI_HOST_LP_VACT_EN_S  11
/** DSI_HOST_LP_HBP_EN : R/W; bitpos: [12]; default: 0;
 *  NA
 */
#define DSI_HOST_LP_HBP_EN    (BIT(12))
#define DSI_HOST_LP_HBP_EN_M  (DSI_HOST_LP_HBP_EN_V << DSI_HOST_LP_HBP_EN_S)
#define DSI_HOST_LP_HBP_EN_V  0x00000001U
#define DSI_HOST_LP_HBP_EN_S  12
/** DSI_HOST_LP_HFP_EN : R/W; bitpos: [13]; default: 0;
 *  NA
 */
#define DSI_HOST_LP_HFP_EN    (BIT(13))
#define DSI_HOST_LP_HFP_EN_M  (DSI_HOST_LP_HFP_EN_V << DSI_HOST_LP_HFP_EN_S)
#define DSI_HOST_LP_HFP_EN_V  0x00000001U
#define DSI_HOST_LP_HFP_EN_S  13
/** DSI_HOST_FRAME_BTA_ACK_EN : R/W; bitpos: [14]; default: 0;
 *  NA
 */
#define DSI_HOST_FRAME_BTA_ACK_EN    (BIT(14))
#define DSI_HOST_FRAME_BTA_ACK_EN_M  (DSI_HOST_FRAME_BTA_ACK_EN_V << DSI_HOST_FRAME_BTA_ACK_EN_S)
#define DSI_HOST_FRAME_BTA_ACK_EN_V  0x00000001U
#define DSI_HOST_FRAME_BTA_ACK_EN_S  14
/** DSI_HOST_LP_CMD_EN : R/W; bitpos: [15]; default: 0;
 *  NA
 */
#define DSI_HOST_LP_CMD_EN    (BIT(15))
#define DSI_HOST_LP_CMD_EN_M  (DSI_HOST_LP_CMD_EN_V << DSI_HOST_LP_CMD_EN_S)
#define DSI_HOST_LP_CMD_EN_V  0x00000001U
#define DSI_HOST_LP_CMD_EN_S  15
/** DSI_HOST_VPG_EN : R/W; bitpos: [16]; default: 0;
 *  NA
 */
#define DSI_HOST_VPG_EN    (BIT(16))
#define DSI_HOST_VPG_EN_M  (DSI_HOST_VPG_EN_V << DSI_HOST_VPG_EN_S)
#define DSI_HOST_VPG_EN_V  0x00000001U
#define DSI_HOST_VPG_EN_S  16
/** DSI_HOST_VPG_MODE : R/W; bitpos: [20]; default: 0;
 *  NA
 */
#define DSI_HOST_VPG_MODE    (BIT(20))
#define DSI_HOST_VPG_MODE_M  (DSI_HOST_VPG_MODE_V << DSI_HOST_VPG_MODE_S)
#define DSI_HOST_VPG_MODE_V  0x00000001U
#define DSI_HOST_VPG_MODE_S  20
/** DSI_HOST_VPG_ORIENTATION : R/W; bitpos: [24]; default: 0;
 *  NA
 */
#define DSI_HOST_VPG_ORIENTATION    (BIT(24))
#define DSI_HOST_VPG_ORIENTATION_M  (DSI_HOST_VPG_ORIENTATION_V << DSI_HOST_VPG_ORIENTATION_S)
#define DSI_HOST_VPG_ORIENTATION_V  0x00000001U
#define DSI_HOST_VPG_ORIENTATION_S  24

/** DSI_HOST_VID_PKT_SIZE_REG register
 *  NA
 */
#define DSI_HOST_VID_PKT_SIZE_REG (DR_REG_DSI_HOST_BASE + 0x3c)
/** DSI_HOST_VID_PKT_SIZE : R/W; bitpos: [13:0]; default: 0;
 *  NA
 */
#define DSI_HOST_VID_PKT_SIZE    0x00003FFFU
#define DSI_HOST_VID_PKT_SIZE_M  (DSI_HOST_VID_PKT_SIZE_V << DSI_HOST_VID_PKT_SIZE_S)
#define DSI_HOST_VID_PKT_SIZE_V  0x00003FFFU
#define DSI_HOST_VID_PKT_SIZE_S  0

/** DSI_HOST_VID_NUM_CHUNKS_REG register
 *  NA
 */
#define DSI_HOST_VID_NUM_CHUNKS_REG (DR_REG_DSI_HOST_BASE + 0x40)
/** DSI_HOST_VID_NUM_CHUNKS : R/W; bitpos: [12:0]; default: 0;
 *  NA
 */
#define DSI_HOST_VID_NUM_CHUNKS    0x00001FFFU
#define DSI_HOST_VID_NUM_CHUNKS_M  (DSI_HOST_VID_NUM_CHUNKS_V << DSI_HOST_VID_NUM_CHUNKS_S)
#define DSI_HOST_VID_NUM_CHUNKS_V  0x00001FFFU
#define DSI_HOST_VID_NUM_CHUNKS_S  0

/** DSI_HOST_VID_NULL_SIZE_REG register
 *  NA
 */
#define DSI_HOST_VID_NULL_SIZE_REG (DR_REG_DSI_HOST_BASE + 0x44)
/** DSI_HOST_VID_NULL_SIZE : R/W; bitpos: [12:0]; default: 0;
 *  NA
 */
#define DSI_HOST_VID_NULL_SIZE    0x00001FFFU
#define DSI_HOST_VID_NULL_SIZE_M  (DSI_HOST_VID_NULL_SIZE_V << DSI_HOST_VID_NULL_SIZE_S)
#define DSI_HOST_VID_NULL_SIZE_V  0x00001FFFU
#define DSI_HOST_VID_NULL_SIZE_S  0

/** DSI_HOST_VID_HSA_TIME_REG register
 *  NA
 */
#define DSI_HOST_VID_HSA_TIME_REG (DR_REG_DSI_HOST_BASE + 0x48)
/** DSI_HOST_VID_HSA_TIME : R/W; bitpos: [11:0]; default: 0;
 *  NA
 */
#define DSI_HOST_VID_HSA_TIME    0x00000FFFU
#define DSI_HOST_VID_HSA_TIME_M  (DSI_HOST_VID_HSA_TIME_V << DSI_HOST_VID_HSA_TIME_S)
#define DSI_HOST_VID_HSA_TIME_V  0x00000FFFU
#define DSI_HOST_VID_HSA_TIME_S  0

/** DSI_HOST_VID_HBP_TIME_REG register
 *  NA
 */
#define DSI_HOST_VID_HBP_TIME_REG (DR_REG_DSI_HOST_BASE + 0x4c)
/** DSI_HOST_VID_HBP_TIME : R/W; bitpos: [11:0]; default: 0;
 *  NA
 */
#define DSI_HOST_VID_HBP_TIME    0x00000FFFU
#define DSI_HOST_VID_HBP_TIME_M  (DSI_HOST_VID_HBP_TIME_V << DSI_HOST_VID_HBP_TIME_S)
#define DSI_HOST_VID_HBP_TIME_V  0x00000FFFU
#define DSI_HOST_VID_HBP_TIME_S  0

/** DSI_HOST_VID_HLINE_TIME_REG register
 *  NA
 */
#define DSI_HOST_VID_HLINE_TIME_REG (DR_REG_DSI_HOST_BASE + 0x50)
/** DSI_HOST_VID_HLINE_TIME : R/W; bitpos: [14:0]; default: 0;
 *  NA
 */
#define DSI_HOST_VID_HLINE_TIME    0x00007FFFU
#define DSI_HOST_VID_HLINE_TIME_M  (DSI_HOST_VID_HLINE_TIME_V << DSI_HOST_VID_HLINE_TIME_S)
#define DSI_HOST_VID_HLINE_TIME_V  0x00007FFFU
#define DSI_HOST_VID_HLINE_TIME_S  0

/** DSI_HOST_VID_VSA_LINES_REG register
 *  NA
 */
#define DSI_HOST_VID_VSA_LINES_REG (DR_REG_DSI_HOST_BASE + 0x54)
/** DSI_HOST_VSA_LINES : R/W; bitpos: [9:0]; default: 0;
 *  NA
 */
#define DSI_HOST_VSA_LINES    0x000003FFU
#define DSI_HOST_VSA_LINES_M  (DSI_HOST_VSA_LINES_V << DSI_HOST_VSA_LINES_S)
#define DSI_HOST_VSA_LINES_V  0x000003FFU
#define DSI_HOST_VSA_LINES_S  0

/** DSI_HOST_VID_VBP_LINES_REG register
 *  NA
 */
#define DSI_HOST_VID_VBP_LINES_REG (DR_REG_DSI_HOST_BASE + 0x58)
/** DSI_HOST_VBP_LINES : R/W; bitpos: [9:0]; default: 0;
 *  NA
 */
#define DSI_HOST_VBP_LINES    0x000003FFU
#define DSI_HOST_VBP_LINES_M  (DSI_HOST_VBP_LINES_V << DSI_HOST_VBP_LINES_S)
#define DSI_HOST_VBP_LINES_V  0x000003FFU
#define DSI_HOST_VBP_LINES_S  0

/** DSI_HOST_VID_VFP_LINES_REG register
 *  NA
 */
#define DSI_HOST_VID_VFP_LINES_REG (DR_REG_DSI_HOST_BASE + 0x5c)
/** DSI_HOST_VFP_LINES : R/W; bitpos: [9:0]; default: 0;
 *  NA
 */
#define DSI_HOST_VFP_LINES    0x000003FFU
#define DSI_HOST_VFP_LINES_M  (DSI_HOST_VFP_LINES_V << DSI_HOST_VFP_LINES_S)
#define DSI_HOST_VFP_LINES_V  0x000003FFU
#define DSI_HOST_VFP_LINES_S  0

/** DSI_HOST_VID_VACTIVE_LINES_REG register
 *  NA
 */
#define DSI_HOST_VID_VACTIVE_LINES_REG (DR_REG_DSI_HOST_BASE + 0x60)
/** DSI_HOST_V_ACTIVE_LINES : R/W; bitpos: [13:0]; default: 0;
 *  NA
 */
#define DSI_HOST_V_ACTIVE_LINES    0x00003FFFU
#define DSI_HOST_V_ACTIVE_LINES_M  (DSI_HOST_V_ACTIVE_LINES_V << DSI_HOST_V_ACTIVE_LINES_S)
#define DSI_HOST_V_ACTIVE_LINES_V  0x00003FFFU
#define DSI_HOST_V_ACTIVE_LINES_S  0

/** DSI_HOST_EDPI_CMD_SIZE_REG register
 *  NA
 */
#define DSI_HOST_EDPI_CMD_SIZE_REG (DR_REG_DSI_HOST_BASE + 0x64)
/** DSI_HOST_EDPI_ALLOWED_CMD_SIZE : R/W; bitpos: [15:0]; default: 0;
 *  NA
 */
#define DSI_HOST_EDPI_ALLOWED_CMD_SIZE    0x0000FFFFU
#define DSI_HOST_EDPI_ALLOWED_CMD_SIZE_M  (DSI_HOST_EDPI_ALLOWED_CMD_SIZE_V << DSI_HOST_EDPI_ALLOWED_CMD_SIZE_S)
#define DSI_HOST_EDPI_ALLOWED_CMD_SIZE_V  0x0000FFFFU
#define DSI_HOST_EDPI_ALLOWED_CMD_SIZE_S  0

/** DSI_HOST_CMD_MODE_CFG_REG register
 *  NA
 */
#define DSI_HOST_CMD_MODE_CFG_REG (DR_REG_DSI_HOST_BASE + 0x68)
/** DSI_HOST_TEAR_FX_EN : R/W; bitpos: [0]; default: 0;
 *  NA
 */
#define DSI_HOST_TEAR_FX_EN    (BIT(0))
#define DSI_HOST_TEAR_FX_EN_M  (DSI_HOST_TEAR_FX_EN_V << DSI_HOST_TEAR_FX_EN_S)
#define DSI_HOST_TEAR_FX_EN_V  0x00000001U
#define DSI_HOST_TEAR_FX_EN_S  0
/** DSI_HOST_ACK_RQST_EN : R/W; bitpos: [1]; default: 0;
 *  NA
 */
#define DSI_HOST_ACK_RQST_EN    (BIT(1))
#define DSI_HOST_ACK_RQST_EN_M  (DSI_HOST_ACK_RQST_EN_V << DSI_HOST_ACK_RQST_EN_S)
#define DSI_HOST_ACK_RQST_EN_V  0x00000001U
#define DSI_HOST_ACK_RQST_EN_S  1
/** DSI_HOST_GEN_SW_0P_TX : R/W; bitpos: [8]; default: 0;
 *  NA
 */
#define DSI_HOST_GEN_SW_0P_TX    (BIT(8))
#define DSI_HOST_GEN_SW_0P_TX_M  (DSI_HOST_GEN_SW_0P_TX_V << DSI_HOST_GEN_SW_0P_TX_S)
#define DSI_HOST_GEN_SW_0P_TX_V  0x00000001U
#define DSI_HOST_GEN_SW_0P_TX_S  8
/** DSI_HOST_GEN_SW_1P_TX : R/W; bitpos: [9]; default: 0;
 *  NA
 */
#define DSI_HOST_GEN_SW_1P_TX    (BIT(9))
#define DSI_HOST_GEN_SW_1P_TX_M  (DSI_HOST_GEN_SW_1P_TX_V << DSI_HOST_GEN_SW_1P_TX_S)
#define DSI_HOST_GEN_SW_1P_TX_V  0x00000001U
#define DSI_HOST_GEN_SW_1P_TX_S  9
/** DSI_HOST_GEN_SW_2P_TX : R/W; bitpos: [10]; default: 0;
 *  NA
 */
#define DSI_HOST_GEN_SW_2P_TX    (BIT(10))
#define DSI_HOST_GEN_SW_2P_TX_M  (DSI_HOST_GEN_SW_2P_TX_V << DSI_HOST_GEN_SW_2P_TX_S)
#define DSI_HOST_GEN_SW_2P_TX_V  0x00000001U
#define DSI_HOST_GEN_SW_2P_TX_S  10
/** DSI_HOST_GEN_SR_0P_TX : R/W; bitpos: [11]; default: 0;
 *  NA
 */
#define DSI_HOST_GEN_SR_0P_TX    (BIT(11))
#define DSI_HOST_GEN_SR_0P_TX_M  (DSI_HOST_GEN_SR_0P_TX_V << DSI_HOST_GEN_SR_0P_TX_S)
#define DSI_HOST_GEN_SR_0P_TX_V  0x00000001U
#define DSI_HOST_GEN_SR_0P_TX_S  11
/** DSI_HOST_GEN_SR_1P_TX : R/W; bitpos: [12]; default: 0;
 *  NA
 */
#define DSI_HOST_GEN_SR_1P_TX    (BIT(12))
#define DSI_HOST_GEN_SR_1P_TX_M  (DSI_HOST_GEN_SR_1P_TX_V << DSI_HOST_GEN_SR_1P_TX_S)
#define DSI_HOST_GEN_SR_1P_TX_V  0x00000001U
#define DSI_HOST_GEN_SR_1P_TX_S  12
/** DSI_HOST_GEN_SR_2P_TX : R/W; bitpos: [13]; default: 0;
 *  NA
 */
#define DSI_HOST_GEN_SR_2P_TX    (BIT(13))
#define DSI_HOST_GEN_SR_2P_TX_M  (DSI_HOST_GEN_SR_2P_TX_V << DSI_HOST_GEN_SR_2P_TX_S)
#define DSI_HOST_GEN_SR_2P_TX_V  0x00000001U
#define DSI_HOST_GEN_SR_2P_TX_S  13
/** DSI_HOST_GEN_LW_TX : R/W; bitpos: [14]; default: 0;
 *  NA
 */
#define DSI_HOST_GEN_LW_TX    (BIT(14))
#define DSI_HOST_GEN_LW_TX_M  (DSI_HOST_GEN_LW_TX_V << DSI_HOST_GEN_LW_TX_S)
#define DSI_HOST_GEN_LW_TX_V  0x00000001U
#define DSI_HOST_GEN_LW_TX_S  14
/** DSI_HOST_DCS_SW_0P_TX : R/W; bitpos: [16]; default: 0;
 *  NA
 */
#define DSI_HOST_DCS_SW_0P_TX    (BIT(16))
#define DSI_HOST_DCS_SW_0P_TX_M  (DSI_HOST_DCS_SW_0P_TX_V << DSI_HOST_DCS_SW_0P_TX_S)
#define DSI_HOST_DCS_SW_0P_TX_V  0x00000001U
#define DSI_HOST_DCS_SW_0P_TX_S  16
/** DSI_HOST_DCS_SW_1P_TX : R/W; bitpos: [17]; default: 0;
 *  NA
 */
#define DSI_HOST_DCS_SW_1P_TX    (BIT(17))
#define DSI_HOST_DCS_SW_1P_TX_M  (DSI_HOST_DCS_SW_1P_TX_V << DSI_HOST_DCS_SW_1P_TX_S)
#define DSI_HOST_DCS_SW_1P_TX_V  0x00000001U
#define DSI_HOST_DCS_SW_1P_TX_S  17
/** DSI_HOST_DCS_SR_0P_TX : R/W; bitpos: [18]; default: 0;
 *  NA
 */
#define DSI_HOST_DCS_SR_0P_TX    (BIT(18))
#define DSI_HOST_DCS_SR_0P_TX_M  (DSI_HOST_DCS_SR_0P_TX_V << DSI_HOST_DCS_SR_0P_TX_S)
#define DSI_HOST_DCS_SR_0P_TX_V  0x00000001U
#define DSI_HOST_DCS_SR_0P_TX_S  18
/** DSI_HOST_DCS_LW_TX : R/W; bitpos: [19]; default: 0;
 *  NA
 */
#define DSI_HOST_DCS_LW_TX    (BIT(19))
#define DSI_HOST_DCS_LW_TX_M  (DSI_HOST_DCS_LW_TX_V << DSI_HOST_DCS_LW_TX_S)
#define DSI_HOST_DCS_LW_TX_V  0x00000001U
#define DSI_HOST_DCS_LW_TX_S  19
/** DSI_HOST_MAX_RD_PKT_SIZE : R/W; bitpos: [24]; default: 0;
 *  NA
 */
#define DSI_HOST_MAX_RD_PKT_SIZE    (BIT(24))
#define DSI_HOST_MAX_RD_PKT_SIZE_M  (DSI_HOST_MAX_RD_PKT_SIZE_V << DSI_HOST_MAX_RD_PKT_SIZE_S)
#define DSI_HOST_MAX_RD_PKT_SIZE_V  0x00000001U
#define DSI_HOST_MAX_RD_PKT_SIZE_S  24

/** DSI_HOST_GEN_HDR_REG register
 *  NA
 */
#define DSI_HOST_GEN_HDR_REG (DR_REG_DSI_HOST_BASE + 0x6c)
/** DSI_HOST_GEN_DT : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define DSI_HOST_GEN_DT    0x0000003FU
#define DSI_HOST_GEN_DT_M  (DSI_HOST_GEN_DT_V << DSI_HOST_GEN_DT_S)
#define DSI_HOST_GEN_DT_V  0x0000003FU
#define DSI_HOST_GEN_DT_S  0
/** DSI_HOST_GEN_VC : R/W; bitpos: [7:6]; default: 0;
 *  NA
 */
#define DSI_HOST_GEN_VC    0x00000003U
#define DSI_HOST_GEN_VC_M  (DSI_HOST_GEN_VC_V << DSI_HOST_GEN_VC_S)
#define DSI_HOST_GEN_VC_V  0x00000003U
#define DSI_HOST_GEN_VC_S  6
/** DSI_HOST_GEN_WC_LSBYTE : R/W; bitpos: [15:8]; default: 0;
 *  NA
 */
#define DSI_HOST_GEN_WC_LSBYTE    0x000000FFU
#define DSI_HOST_GEN_WC_LSBYTE_M  (DSI_HOST_GEN_WC_LSBYTE_V << DSI_HOST_GEN_WC_LSBYTE_S)
#define DSI_HOST_GEN_WC_LSBYTE_V  0x000000FFU
#define DSI_HOST_GEN_WC_LSBYTE_S  8
/** DSI_HOST_GEN_WC_MSBYTE : R/W; bitpos: [23:16]; default: 0;
 *  NA
 */
#define DSI_HOST_GEN_WC_MSBYTE    0x000000FFU
#define DSI_HOST_GEN_WC_MSBYTE_M  (DSI_HOST_GEN_WC_MSBYTE_V << DSI_HOST_GEN_WC_MSBYTE_S)
#define DSI_HOST_GEN_WC_MSBYTE_V  0x000000FFU
#define DSI_HOST_GEN_WC_MSBYTE_S  16

/** DSI_HOST_GEN_PLD_DATA_REG register
 *  NA
 */
#define DSI_HOST_GEN_PLD_DATA_REG (DR_REG_DSI_HOST_BASE + 0x70)
/** DSI_HOST_GEN_PLD_B1 : R/W; bitpos: [7:0]; default: 0;
 *  NA
 */
#define DSI_HOST_GEN_PLD_B1    0x000000FFU
#define DSI_HOST_GEN_PLD_B1_M  (DSI_HOST_GEN_PLD_B1_V << DSI_HOST_GEN_PLD_B1_S)
#define DSI_HOST_GEN_PLD_B1_V  0x000000FFU
#define DSI_HOST_GEN_PLD_B1_S  0
/** DSI_HOST_GEN_PLD_B2 : R/W; bitpos: [15:8]; default: 0;
 *  NA
 */
#define DSI_HOST_GEN_PLD_B2    0x000000FFU
#define DSI_HOST_GEN_PLD_B2_M  (DSI_HOST_GEN_PLD_B2_V << DSI_HOST_GEN_PLD_B2_S)
#define DSI_HOST_GEN_PLD_B2_V  0x000000FFU
#define DSI_HOST_GEN_PLD_B2_S  8
/** DSI_HOST_GEN_PLD_B3 : R/W; bitpos: [23:16]; default: 0;
 *  NA
 */
#define DSI_HOST_GEN_PLD_B3    0x000000FFU
#define DSI_HOST_GEN_PLD_B3_M  (DSI_HOST_GEN_PLD_B3_V << DSI_HOST_GEN_PLD_B3_S)
#define DSI_HOST_GEN_PLD_B3_V  0x000000FFU
#define DSI_HOST_GEN_PLD_B3_S  16
/** DSI_HOST_GEN_PLD_B4 : R/W; bitpos: [31:24]; default: 0;
 *  NA
 */
#define DSI_HOST_GEN_PLD_B4    0x000000FFU
#define DSI_HOST_GEN_PLD_B4_M  (DSI_HOST_GEN_PLD_B4_V << DSI_HOST_GEN_PLD_B4_S)
#define DSI_HOST_GEN_PLD_B4_V  0x000000FFU
#define DSI_HOST_GEN_PLD_B4_S  24

/** DSI_HOST_CMD_PKT_STATUS_REG register
 *  NA
 */
#define DSI_HOST_CMD_PKT_STATUS_REG (DR_REG_DSI_HOST_BASE + 0x74)
/** DSI_HOST_GEN_CMD_EMPTY : RO; bitpos: [0]; default: 1;
 *  NA
 */
#define DSI_HOST_GEN_CMD_EMPTY    (BIT(0))
#define DSI_HOST_GEN_CMD_EMPTY_M  (DSI_HOST_GEN_CMD_EMPTY_V << DSI_HOST_GEN_CMD_EMPTY_S)
#define DSI_HOST_GEN_CMD_EMPTY_V  0x00000001U
#define DSI_HOST_GEN_CMD_EMPTY_S  0
/** DSI_HOST_GEN_CMD_FULL : RO; bitpos: [1]; default: 0;
 *  NA
 */
#define DSI_HOST_GEN_CMD_FULL    (BIT(1))
#define DSI_HOST_GEN_CMD_FULL_M  (DSI_HOST_GEN_CMD_FULL_V << DSI_HOST_GEN_CMD_FULL_S)
#define DSI_HOST_GEN_CMD_FULL_V  0x00000001U
#define DSI_HOST_GEN_CMD_FULL_S  1
/** DSI_HOST_GEN_PLD_W_EMPTY : RO; bitpos: [2]; default: 1;
 *  NA
 */
#define DSI_HOST_GEN_PLD_W_EMPTY    (BIT(2))
#define DSI_HOST_GEN_PLD_W_EMPTY_M  (DSI_HOST_GEN_PLD_W_EMPTY_V << DSI_HOST_GEN_PLD_W_EMPTY_S)
#define DSI_HOST_GEN_PLD_W_EMPTY_V  0x00000001U
#define DSI_HOST_GEN_PLD_W_EMPTY_S  2
/** DSI_HOST_GEN_PLD_W_FULL : RO; bitpos: [3]; default: 0;
 *  NA
 */
#define DSI_HOST_GEN_PLD_W_FULL    (BIT(3))
#define DSI_HOST_GEN_PLD_W_FULL_M  (DSI_HOST_GEN_PLD_W_FULL_V << DSI_HOST_GEN_PLD_W_FULL_S)
#define DSI_HOST_GEN_PLD_W_FULL_V  0x00000001U
#define DSI_HOST_GEN_PLD_W_FULL_S  3
/** DSI_HOST_GEN_PLD_R_EMPTY : RO; bitpos: [4]; default: 1;
 *  NA
 */
#define DSI_HOST_GEN_PLD_R_EMPTY    (BIT(4))
#define DSI_HOST_GEN_PLD_R_EMPTY_M  (DSI_HOST_GEN_PLD_R_EMPTY_V << DSI_HOST_GEN_PLD_R_EMPTY_S)
#define DSI_HOST_GEN_PLD_R_EMPTY_V  0x00000001U
#define DSI_HOST_GEN_PLD_R_EMPTY_S  4
/** DSI_HOST_GEN_PLD_R_FULL : RO; bitpos: [5]; default: 0;
 *  NA
 */
#define DSI_HOST_GEN_PLD_R_FULL    (BIT(5))
#define DSI_HOST_GEN_PLD_R_FULL_M  (DSI_HOST_GEN_PLD_R_FULL_V << DSI_HOST_GEN_PLD_R_FULL_S)
#define DSI_HOST_GEN_PLD_R_FULL_V  0x00000001U
#define DSI_HOST_GEN_PLD_R_FULL_S  5
/** DSI_HOST_GEN_RD_CMD_BUSY : RO; bitpos: [6]; default: 0;
 *  NA
 */
#define DSI_HOST_GEN_RD_CMD_BUSY    (BIT(6))
#define DSI_HOST_GEN_RD_CMD_BUSY_M  (DSI_HOST_GEN_RD_CMD_BUSY_V << DSI_HOST_GEN_RD_CMD_BUSY_S)
#define DSI_HOST_GEN_RD_CMD_BUSY_V  0x00000001U
#define DSI_HOST_GEN_RD_CMD_BUSY_S  6
/** DSI_HOST_GEN_BUFF_CMD_EMPTY : RO; bitpos: [16]; default: 1;
 *  NA
 */
#define DSI_HOST_GEN_BUFF_CMD_EMPTY    (BIT(16))
#define DSI_HOST_GEN_BUFF_CMD_EMPTY_M  (DSI_HOST_GEN_BUFF_CMD_EMPTY_V << DSI_HOST_GEN_BUFF_CMD_EMPTY_S)
#define DSI_HOST_GEN_BUFF_CMD_EMPTY_V  0x00000001U
#define DSI_HOST_GEN_BUFF_CMD_EMPTY_S  16
/** DSI_HOST_GEN_BUFF_CMD_FULL : RO; bitpos: [17]; default: 0;
 *  NA
 */
#define DSI_HOST_GEN_BUFF_CMD_FULL    (BIT(17))
#define DSI_HOST_GEN_BUFF_CMD_FULL_M  (DSI_HOST_GEN_BUFF_CMD_FULL_V << DSI_HOST_GEN_BUFF_CMD_FULL_S)
#define DSI_HOST_GEN_BUFF_CMD_FULL_V  0x00000001U
#define DSI_HOST_GEN_BUFF_CMD_FULL_S  17
/** DSI_HOST_GEN_BUFF_PLD_EMPTY : RO; bitpos: [18]; default: 1;
 *  NA
 */
#define DSI_HOST_GEN_BUFF_PLD_EMPTY    (BIT(18))
#define DSI_HOST_GEN_BUFF_PLD_EMPTY_M  (DSI_HOST_GEN_BUFF_PLD_EMPTY_V << DSI_HOST_GEN_BUFF_PLD_EMPTY_S)
#define DSI_HOST_GEN_BUFF_PLD_EMPTY_V  0x00000001U
#define DSI_HOST_GEN_BUFF_PLD_EMPTY_S  18
/** DSI_HOST_GEN_BUFF_PLD_FULL : RO; bitpos: [19]; default: 0;
 *  NA
 */
#define DSI_HOST_GEN_BUFF_PLD_FULL    (BIT(19))
#define DSI_HOST_GEN_BUFF_PLD_FULL_M  (DSI_HOST_GEN_BUFF_PLD_FULL_V << DSI_HOST_GEN_BUFF_PLD_FULL_S)
#define DSI_HOST_GEN_BUFF_PLD_FULL_V  0x00000001U
#define DSI_HOST_GEN_BUFF_PLD_FULL_S  19

/** DSI_HOST_TO_CNT_CFG_REG register
 *  NA
 */
#define DSI_HOST_TO_CNT_CFG_REG (DR_REG_DSI_HOST_BASE + 0x78)
/** DSI_HOST_LPRX_TO_CNT : R/W; bitpos: [15:0]; default: 0;
 *  NA
 */
#define DSI_HOST_LPRX_TO_CNT    0x0000FFFFU
#define DSI_HOST_LPRX_TO_CNT_M  (DSI_HOST_LPRX_TO_CNT_V << DSI_HOST_LPRX_TO_CNT_S)
#define DSI_HOST_LPRX_TO_CNT_V  0x0000FFFFU
#define DSI_HOST_LPRX_TO_CNT_S  0
/** DSI_HOST_HSTX_TO_CNT : R/W; bitpos: [31:16]; default: 0;
 *  NA
 */
#define DSI_HOST_HSTX_TO_CNT    0x0000FFFFU
#define DSI_HOST_HSTX_TO_CNT_M  (DSI_HOST_HSTX_TO_CNT_V << DSI_HOST_HSTX_TO_CNT_S)
#define DSI_HOST_HSTX_TO_CNT_V  0x0000FFFFU
#define DSI_HOST_HSTX_TO_CNT_S  16

/** DSI_HOST_HS_RD_TO_CNT_REG register
 *  NA
 */
#define DSI_HOST_HS_RD_TO_CNT_REG (DR_REG_DSI_HOST_BASE + 0x7c)
/** DSI_HOST_HS_RD_TO_CNT : R/W; bitpos: [15:0]; default: 0;
 *  NA
 */
#define DSI_HOST_HS_RD_TO_CNT    0x0000FFFFU
#define DSI_HOST_HS_RD_TO_CNT_M  (DSI_HOST_HS_RD_TO_CNT_V << DSI_HOST_HS_RD_TO_CNT_S)
#define DSI_HOST_HS_RD_TO_CNT_V  0x0000FFFFU
#define DSI_HOST_HS_RD_TO_CNT_S  0

/** DSI_HOST_LP_RD_TO_CNT_REG register
 *  NA
 */
#define DSI_HOST_LP_RD_TO_CNT_REG (DR_REG_DSI_HOST_BASE + 0x80)
/** DSI_HOST_LP_RD_TO_CNT : R/W; bitpos: [15:0]; default: 0;
 *  NA
 */
#define DSI_HOST_LP_RD_TO_CNT    0x0000FFFFU
#define DSI_HOST_LP_RD_TO_CNT_M  (DSI_HOST_LP_RD_TO_CNT_V << DSI_HOST_LP_RD_TO_CNT_S)
#define DSI_HOST_LP_RD_TO_CNT_V  0x0000FFFFU
#define DSI_HOST_LP_RD_TO_CNT_S  0

/** DSI_HOST_HS_WR_TO_CNT_REG register
 *  NA
 */
#define DSI_HOST_HS_WR_TO_CNT_REG (DR_REG_DSI_HOST_BASE + 0x84)
/** DSI_HOST_HS_WR_TO_CNT : R/W; bitpos: [15:0]; default: 0;
 *  NA
 */
#define DSI_HOST_HS_WR_TO_CNT    0x0000FFFFU
#define DSI_HOST_HS_WR_TO_CNT_M  (DSI_HOST_HS_WR_TO_CNT_V << DSI_HOST_HS_WR_TO_CNT_S)
#define DSI_HOST_HS_WR_TO_CNT_V  0x0000FFFFU
#define DSI_HOST_HS_WR_TO_CNT_S  0

/** DSI_HOST_LP_WR_TO_CNT_REG register
 *  NA
 */
#define DSI_HOST_LP_WR_TO_CNT_REG (DR_REG_DSI_HOST_BASE + 0x88)
/** DSI_HOST_LP_WR_TO_CNT : R/W; bitpos: [15:0]; default: 0;
 *  NA
 */
#define DSI_HOST_LP_WR_TO_CNT    0x0000FFFFU
#define DSI_HOST_LP_WR_TO_CNT_M  (DSI_HOST_LP_WR_TO_CNT_V << DSI_HOST_LP_WR_TO_CNT_S)
#define DSI_HOST_LP_WR_TO_CNT_V  0x0000FFFFU
#define DSI_HOST_LP_WR_TO_CNT_S  0

/** DSI_HOST_BTA_TO_CNT_REG register
 *  NA
 */
#define DSI_HOST_BTA_TO_CNT_REG (DR_REG_DSI_HOST_BASE + 0x8c)
/** DSI_HOST_BTA_TO_CNT : R/W; bitpos: [15:0]; default: 0;
 *  NA
 */
#define DSI_HOST_BTA_TO_CNT    0x0000FFFFU
#define DSI_HOST_BTA_TO_CNT_M  (DSI_HOST_BTA_TO_CNT_V << DSI_HOST_BTA_TO_CNT_S)
#define DSI_HOST_BTA_TO_CNT_V  0x0000FFFFU
#define DSI_HOST_BTA_TO_CNT_S  0

/** DSI_HOST_SDF_3D_REG register
 *  NA
 */
#define DSI_HOST_SDF_3D_REG (DR_REG_DSI_HOST_BASE + 0x90)
/** DSI_HOST_MODE_3D : R/W; bitpos: [1:0]; default: 0;
 *  NA
 */
#define DSI_HOST_MODE_3D    0x00000003U
#define DSI_HOST_MODE_3D_M  (DSI_HOST_MODE_3D_V << DSI_HOST_MODE_3D_S)
#define DSI_HOST_MODE_3D_V  0x00000003U
#define DSI_HOST_MODE_3D_S  0
/** DSI_HOST_FORMAT_3D : R/W; bitpos: [3:2]; default: 0;
 *  NA
 */
#define DSI_HOST_FORMAT_3D    0x00000003U
#define DSI_HOST_FORMAT_3D_M  (DSI_HOST_FORMAT_3D_V << DSI_HOST_FORMAT_3D_S)
#define DSI_HOST_FORMAT_3D_V  0x00000003U
#define DSI_HOST_FORMAT_3D_S  2
/** DSI_HOST_SECOND_VSYNC : R/W; bitpos: [4]; default: 0;
 *  NA
 */
#define DSI_HOST_SECOND_VSYNC    (BIT(4))
#define DSI_HOST_SECOND_VSYNC_M  (DSI_HOST_SECOND_VSYNC_V << DSI_HOST_SECOND_VSYNC_S)
#define DSI_HOST_SECOND_VSYNC_V  0x00000001U
#define DSI_HOST_SECOND_VSYNC_S  4
/** DSI_HOST_RIGHT_FIRST : R/W; bitpos: [5]; default: 0;
 *  NA
 */
#define DSI_HOST_RIGHT_FIRST    (BIT(5))
#define DSI_HOST_RIGHT_FIRST_M  (DSI_HOST_RIGHT_FIRST_V << DSI_HOST_RIGHT_FIRST_S)
#define DSI_HOST_RIGHT_FIRST_V  0x00000001U
#define DSI_HOST_RIGHT_FIRST_S  5
/** DSI_HOST_SEND_3D_CFG : R/W; bitpos: [16]; default: 0;
 *  NA
 */
#define DSI_HOST_SEND_3D_CFG    (BIT(16))
#define DSI_HOST_SEND_3D_CFG_M  (DSI_HOST_SEND_3D_CFG_V << DSI_HOST_SEND_3D_CFG_S)
#define DSI_HOST_SEND_3D_CFG_V  0x00000001U
#define DSI_HOST_SEND_3D_CFG_S  16

/** DSI_HOST_LPCLK_CTRL_REG register
 *  NA
 */
#define DSI_HOST_LPCLK_CTRL_REG (DR_REG_DSI_HOST_BASE + 0x94)
/** DSI_HOST_PHY_TXREQUESTCLKHS : R/W; bitpos: [0]; default: 0;
 *  NA
 */
#define DSI_HOST_PHY_TXREQUESTCLKHS    (BIT(0))
#define DSI_HOST_PHY_TXREQUESTCLKHS_M  (DSI_HOST_PHY_TXREQUESTCLKHS_V << DSI_HOST_PHY_TXREQUESTCLKHS_S)
#define DSI_HOST_PHY_TXREQUESTCLKHS_V  0x00000001U
#define DSI_HOST_PHY_TXREQUESTCLKHS_S  0
/** DSI_HOST_AUTO_CLKLANE_CTRL : R/W; bitpos: [1]; default: 0;
 *  NA
 */
#define DSI_HOST_AUTO_CLKLANE_CTRL    (BIT(1))
#define DSI_HOST_AUTO_CLKLANE_CTRL_M  (DSI_HOST_AUTO_CLKLANE_CTRL_V << DSI_HOST_AUTO_CLKLANE_CTRL_S)
#define DSI_HOST_AUTO_CLKLANE_CTRL_V  0x00000001U
#define DSI_HOST_AUTO_CLKLANE_CTRL_S  1

/** DSI_HOST_PHY_TMR_LPCLK_CFG_REG register
 *  NA
 */
#define DSI_HOST_PHY_TMR_LPCLK_CFG_REG (DR_REG_DSI_HOST_BASE + 0x98)
/** DSI_HOST_PHY_CLKLP2HS_TIME : R/W; bitpos: [9:0]; default: 0;
 *  NA
 */
#define DSI_HOST_PHY_CLKLP2HS_TIME    0x000003FFU
#define DSI_HOST_PHY_CLKLP2HS_TIME_M  (DSI_HOST_PHY_CLKLP2HS_TIME_V << DSI_HOST_PHY_CLKLP2HS_TIME_S)
#define DSI_HOST_PHY_CLKLP2HS_TIME_V  0x000003FFU
#define DSI_HOST_PHY_CLKLP2HS_TIME_S  0
/** DSI_HOST_PHY_CLKHS2LP_TIME : R/W; bitpos: [25:16]; default: 0;
 *  NA
 */
#define DSI_HOST_PHY_CLKHS2LP_TIME    0x000003FFU
#define DSI_HOST_PHY_CLKHS2LP_TIME_M  (DSI_HOST_PHY_CLKHS2LP_TIME_V << DSI_HOST_PHY_CLKHS2LP_TIME_S)
#define DSI_HOST_PHY_CLKHS2LP_TIME_V  0x000003FFU
#define DSI_HOST_PHY_CLKHS2LP_TIME_S  16

/** DSI_HOST_PHY_TMR_CFG_REG register
 *  NA
 */
#define DSI_HOST_PHY_TMR_CFG_REG (DR_REG_DSI_HOST_BASE + 0x9c)
/** DSI_HOST_PHY_LP2HS_TIME : R/W; bitpos: [9:0]; default: 0;
 *  NA
 */
#define DSI_HOST_PHY_LP2HS_TIME    0x000003FFU
#define DSI_HOST_PHY_LP2HS_TIME_M  (DSI_HOST_PHY_LP2HS_TIME_V << DSI_HOST_PHY_LP2HS_TIME_S)
#define DSI_HOST_PHY_LP2HS_TIME_V  0x000003FFU
#define DSI_HOST_PHY_LP2HS_TIME_S  0
/** DSI_HOST_PHY_HS2LP_TIME : R/W; bitpos: [25:16]; default: 0;
 *  NA
 */
#define DSI_HOST_PHY_HS2LP_TIME    0x000003FFU
#define DSI_HOST_PHY_HS2LP_TIME_M  (DSI_HOST_PHY_HS2LP_TIME_V << DSI_HOST_PHY_HS2LP_TIME_S)
#define DSI_HOST_PHY_HS2LP_TIME_V  0x000003FFU
#define DSI_HOST_PHY_HS2LP_TIME_S  16

/** DSI_HOST_PHY_RSTZ_REG register
 *  NA
 */
#define DSI_HOST_PHY_RSTZ_REG (DR_REG_DSI_HOST_BASE + 0xa0)
/** DSI_HOST_PHY_SHUTDOWNZ : R/W; bitpos: [0]; default: 0;
 *  NA
 */
#define DSI_HOST_PHY_SHUTDOWNZ    (BIT(0))
#define DSI_HOST_PHY_SHUTDOWNZ_M  (DSI_HOST_PHY_SHUTDOWNZ_V << DSI_HOST_PHY_SHUTDOWNZ_S)
#define DSI_HOST_PHY_SHUTDOWNZ_V  0x00000001U
#define DSI_HOST_PHY_SHUTDOWNZ_S  0
/** DSI_HOST_PHY_RSTZ : R/W; bitpos: [1]; default: 0;
 *  NA
 */
#define DSI_HOST_PHY_RSTZ    (BIT(1))
#define DSI_HOST_PHY_RSTZ_M  (DSI_HOST_PHY_RSTZ_V << DSI_HOST_PHY_RSTZ_S)
#define DSI_HOST_PHY_RSTZ_V  0x00000001U
#define DSI_HOST_PHY_RSTZ_S  1
/** DSI_HOST_PHY_ENABLECLK : R/W; bitpos: [2]; default: 0;
 *  NA
 */
#define DSI_HOST_PHY_ENABLECLK    (BIT(2))
#define DSI_HOST_PHY_ENABLECLK_M  (DSI_HOST_PHY_ENABLECLK_V << DSI_HOST_PHY_ENABLECLK_S)
#define DSI_HOST_PHY_ENABLECLK_V  0x00000001U
#define DSI_HOST_PHY_ENABLECLK_S  2
/** DSI_HOST_PHY_FORCEPLL : R/W; bitpos: [3]; default: 0;
 *  NA
 */
#define DSI_HOST_PHY_FORCEPLL    (BIT(3))
#define DSI_HOST_PHY_FORCEPLL_M  (DSI_HOST_PHY_FORCEPLL_V << DSI_HOST_PHY_FORCEPLL_S)
#define DSI_HOST_PHY_FORCEPLL_V  0x00000001U
#define DSI_HOST_PHY_FORCEPLL_S  3

/** DSI_HOST_PHY_IF_CFG_REG register
 *  NA
 */
#define DSI_HOST_PHY_IF_CFG_REG (DR_REG_DSI_HOST_BASE + 0xa4)
/** DSI_HOST_N_LANES : R/W; bitpos: [1:0]; default: 1;
 *  NA
 */
#define DSI_HOST_N_LANES    0x00000003U
#define DSI_HOST_N_LANES_M  (DSI_HOST_N_LANES_V << DSI_HOST_N_LANES_S)
#define DSI_HOST_N_LANES_V  0x00000003U
#define DSI_HOST_N_LANES_S  0
/** DSI_HOST_PHY_STOP_WAIT_TIME : R/W; bitpos: [15:8]; default: 0;
 *  NA
 */
#define DSI_HOST_PHY_STOP_WAIT_TIME    0x000000FFU
#define DSI_HOST_PHY_STOP_WAIT_TIME_M  (DSI_HOST_PHY_STOP_WAIT_TIME_V << DSI_HOST_PHY_STOP_WAIT_TIME_S)
#define DSI_HOST_PHY_STOP_WAIT_TIME_V  0x000000FFU
#define DSI_HOST_PHY_STOP_WAIT_TIME_S  8

/** DSI_HOST_PHY_ULPS_CTRL_REG register
 *  NA
 */
#define DSI_HOST_PHY_ULPS_CTRL_REG (DR_REG_DSI_HOST_BASE + 0xa8)
/** DSI_HOST_PHY_TXREQULPSCLK : R/W; bitpos: [0]; default: 0;
 *  NA
 */
#define DSI_HOST_PHY_TXREQULPSCLK    (BIT(0))
#define DSI_HOST_PHY_TXREQULPSCLK_M  (DSI_HOST_PHY_TXREQULPSCLK_V << DSI_HOST_PHY_TXREQULPSCLK_S)
#define DSI_HOST_PHY_TXREQULPSCLK_V  0x00000001U
#define DSI_HOST_PHY_TXREQULPSCLK_S  0
/** DSI_HOST_PHY_TXEXITULPSCLK : R/W; bitpos: [1]; default: 0;
 *  NA
 */
#define DSI_HOST_PHY_TXEXITULPSCLK    (BIT(1))
#define DSI_HOST_PHY_TXEXITULPSCLK_M  (DSI_HOST_PHY_TXEXITULPSCLK_V << DSI_HOST_PHY_TXEXITULPSCLK_S)
#define DSI_HOST_PHY_TXEXITULPSCLK_V  0x00000001U
#define DSI_HOST_PHY_TXEXITULPSCLK_S  1
/** DSI_HOST_PHY_TXREQULPSLAN : R/W; bitpos: [2]; default: 0;
 *  NA
 */
#define DSI_HOST_PHY_TXREQULPSLAN    (BIT(2))
#define DSI_HOST_PHY_TXREQULPSLAN_M  (DSI_HOST_PHY_TXREQULPSLAN_V << DSI_HOST_PHY_TXREQULPSLAN_S)
#define DSI_HOST_PHY_TXREQULPSLAN_V  0x00000001U
#define DSI_HOST_PHY_TXREQULPSLAN_S  2
/** DSI_HOST_PHY_TXEXITULPSLAN : R/W; bitpos: [3]; default: 0;
 *  NA
 */
#define DSI_HOST_PHY_TXEXITULPSLAN    (BIT(3))
#define DSI_HOST_PHY_TXEXITULPSLAN_M  (DSI_HOST_PHY_TXEXITULPSLAN_V << DSI_HOST_PHY_TXEXITULPSLAN_S)
#define DSI_HOST_PHY_TXEXITULPSLAN_V  0x00000001U
#define DSI_HOST_PHY_TXEXITULPSLAN_S  3

/** DSI_HOST_PHY_TX_TRIGGERS_REG register
 *  NA
 */
#define DSI_HOST_PHY_TX_TRIGGERS_REG (DR_REG_DSI_HOST_BASE + 0xac)
/** DSI_HOST_PHY_TX_TRIGGERS : R/W; bitpos: [3:0]; default: 0;
 *  NA
 */
#define DSI_HOST_PHY_TX_TRIGGERS    0x0000000FU
#define DSI_HOST_PHY_TX_TRIGGERS_M  (DSI_HOST_PHY_TX_TRIGGERS_V << DSI_HOST_PHY_TX_TRIGGERS_S)
#define DSI_HOST_PHY_TX_TRIGGERS_V  0x0000000FU
#define DSI_HOST_PHY_TX_TRIGGERS_S  0

/** DSI_HOST_PHY_STATUS_REG register
 *  NA
 */
#define DSI_HOST_PHY_STATUS_REG (DR_REG_DSI_HOST_BASE + 0xb0)
/** DSI_HOST_PHY_LOCK : RO; bitpos: [0]; default: 0;
 *  NA
 */
#define DSI_HOST_PHY_LOCK    (BIT(0))
#define DSI_HOST_PHY_LOCK_M  (DSI_HOST_PHY_LOCK_V << DSI_HOST_PHY_LOCK_S)
#define DSI_HOST_PHY_LOCK_V  0x00000001U
#define DSI_HOST_PHY_LOCK_S  0
/** DSI_HOST_PHY_DIRECTION : RO; bitpos: [1]; default: 0;
 *  NA
 */
#define DSI_HOST_PHY_DIRECTION    (BIT(1))
#define DSI_HOST_PHY_DIRECTION_M  (DSI_HOST_PHY_DIRECTION_V << DSI_HOST_PHY_DIRECTION_S)
#define DSI_HOST_PHY_DIRECTION_V  0x00000001U
#define DSI_HOST_PHY_DIRECTION_S  1
/** DSI_HOST_PHY_STOPSTATECLKLANE : RO; bitpos: [2]; default: 0;
 *  NA
 */
#define DSI_HOST_PHY_STOPSTATECLKLANE    (BIT(2))
#define DSI_HOST_PHY_STOPSTATECLKLANE_M  (DSI_HOST_PHY_STOPSTATECLKLANE_V << DSI_HOST_PHY_STOPSTATECLKLANE_S)
#define DSI_HOST_PHY_STOPSTATECLKLANE_V  0x00000001U
#define DSI_HOST_PHY_STOPSTATECLKLANE_S  2
/** DSI_HOST_PHY_ULPSACTIVENOTCLK : RO; bitpos: [3]; default: 0;
 *  NA
 */
#define DSI_HOST_PHY_ULPSACTIVENOTCLK    (BIT(3))
#define DSI_HOST_PHY_ULPSACTIVENOTCLK_M  (DSI_HOST_PHY_ULPSACTIVENOTCLK_V << DSI_HOST_PHY_ULPSACTIVENOTCLK_S)
#define DSI_HOST_PHY_ULPSACTIVENOTCLK_V  0x00000001U
#define DSI_HOST_PHY_ULPSACTIVENOTCLK_S  3
/** DSI_HOST_PHY_STOPSTATE0LANE : RO; bitpos: [4]; default: 0;
 *  NA
 */
#define DSI_HOST_PHY_STOPSTATE0LANE    (BIT(4))
#define DSI_HOST_PHY_STOPSTATE0LANE_M  (DSI_HOST_PHY_STOPSTATE0LANE_V << DSI_HOST_PHY_STOPSTATE0LANE_S)
#define DSI_HOST_PHY_STOPSTATE0LANE_V  0x00000001U
#define DSI_HOST_PHY_STOPSTATE0LANE_S  4
/** DSI_HOST_PHY_ULPSACTIVENOT0LANE : RO; bitpos: [5]; default: 0;
 *  NA
 */
#define DSI_HOST_PHY_ULPSACTIVENOT0LANE    (BIT(5))
#define DSI_HOST_PHY_ULPSACTIVENOT0LANE_M  (DSI_HOST_PHY_ULPSACTIVENOT0LANE_V << DSI_HOST_PHY_ULPSACTIVENOT0LANE_S)
#define DSI_HOST_PHY_ULPSACTIVENOT0LANE_V  0x00000001U
#define DSI_HOST_PHY_ULPSACTIVENOT0LANE_S  5
/** DSI_HOST_PHY_RXULPSESC0LANE : RO; bitpos: [6]; default: 1;
 *  NA
 */
#define DSI_HOST_PHY_RXULPSESC0LANE    (BIT(6))
#define DSI_HOST_PHY_RXULPSESC0LANE_M  (DSI_HOST_PHY_RXULPSESC0LANE_V << DSI_HOST_PHY_RXULPSESC0LANE_S)
#define DSI_HOST_PHY_RXULPSESC0LANE_V  0x00000001U
#define DSI_HOST_PHY_RXULPSESC0LANE_S  6
/** DSI_HOST_PHY_STOPSTATE1LANE : RO; bitpos: [7]; default: 0;
 *  NA
 */
#define DSI_HOST_PHY_STOPSTATE1LANE    (BIT(7))
#define DSI_HOST_PHY_STOPSTATE1LANE_M  (DSI_HOST_PHY_STOPSTATE1LANE_V << DSI_HOST_PHY_STOPSTATE1LANE_S)
#define DSI_HOST_PHY_STOPSTATE1LANE_V  0x00000001U
#define DSI_HOST_PHY_STOPSTATE1LANE_S  7
/** DSI_HOST_PHY_ULPSACTIVENOT1LANE : RO; bitpos: [8]; default: 1;
 *  NA
 */
#define DSI_HOST_PHY_ULPSACTIVENOT1LANE    (BIT(8))
#define DSI_HOST_PHY_ULPSACTIVENOT1LANE_M  (DSI_HOST_PHY_ULPSACTIVENOT1LANE_V << DSI_HOST_PHY_ULPSACTIVENOT1LANE_S)
#define DSI_HOST_PHY_ULPSACTIVENOT1LANE_V  0x00000001U
#define DSI_HOST_PHY_ULPSACTIVENOT1LANE_S  8

/** DSI_HOST_PHY_TST_CTRL0_REG register
 *  NA
 */
#define DSI_HOST_PHY_TST_CTRL0_REG (DR_REG_DSI_HOST_BASE + 0xb4)
/** DSI_HOST_PHY_TESTCLR : R/W; bitpos: [0]; default: 1;
 *  NA
 */
#define DSI_HOST_PHY_TESTCLR    (BIT(0))
#define DSI_HOST_PHY_TESTCLR_M  (DSI_HOST_PHY_TESTCLR_V << DSI_HOST_PHY_TESTCLR_S)
#define DSI_HOST_PHY_TESTCLR_V  0x00000001U
#define DSI_HOST_PHY_TESTCLR_S  0
/** DSI_HOST_PHY_TESTCLK : R/W; bitpos: [1]; default: 0;
 *  NA
 */
#define DSI_HOST_PHY_TESTCLK    (BIT(1))
#define DSI_HOST_PHY_TESTCLK_M  (DSI_HOST_PHY_TESTCLK_V << DSI_HOST_PHY_TESTCLK_S)
#define DSI_HOST_PHY_TESTCLK_V  0x00000001U
#define DSI_HOST_PHY_TESTCLK_S  1

/** DSI_HOST_PHY_TST_CTRL1_REG register
 *  NA
 */
#define DSI_HOST_PHY_TST_CTRL1_REG (DR_REG_DSI_HOST_BASE + 0xb8)
/** DSI_HOST_PHY_TESTDIN : R/W; bitpos: [7:0]; default: 0;
 *  NA
 */
#define DSI_HOST_PHY_TESTDIN    0x000000FFU
#define DSI_HOST_PHY_TESTDIN_M  (DSI_HOST_PHY_TESTDIN_V << DSI_HOST_PHY_TESTDIN_S)
#define DSI_HOST_PHY_TESTDIN_V  0x000000FFU
#define DSI_HOST_PHY_TESTDIN_S  0
/** DSI_HOST_PHT_TESTDOUT : RO; bitpos: [15:8]; default: 0;
 *  NA
 */
#define DSI_HOST_PHT_TESTDOUT    0x000000FFU
#define DSI_HOST_PHT_TESTDOUT_M  (DSI_HOST_PHT_TESTDOUT_V << DSI_HOST_PHT_TESTDOUT_S)
#define DSI_HOST_PHT_TESTDOUT_V  0x000000FFU
#define DSI_HOST_PHT_TESTDOUT_S  8
/** DSI_HOST_PHY_TESTEN : R/W; bitpos: [16]; default: 0;
 *  NA
 */
#define DSI_HOST_PHY_TESTEN    (BIT(16))
#define DSI_HOST_PHY_TESTEN_M  (DSI_HOST_PHY_TESTEN_V << DSI_HOST_PHY_TESTEN_S)
#define DSI_HOST_PHY_TESTEN_V  0x00000001U
#define DSI_HOST_PHY_TESTEN_S  16

/** DSI_HOST_INT_ST0_REG register
 *  NA
 */
#define DSI_HOST_INT_ST0_REG (DR_REG_DSI_HOST_BASE + 0xbc)
/** DSI_HOST_ACK_WITH_ERR_0 : RO; bitpos: [0]; default: 0;
 *  NA
 */
#define DSI_HOST_ACK_WITH_ERR_0    (BIT(0))
#define DSI_HOST_ACK_WITH_ERR_0_M  (DSI_HOST_ACK_WITH_ERR_0_V << DSI_HOST_ACK_WITH_ERR_0_S)
#define DSI_HOST_ACK_WITH_ERR_0_V  0x00000001U
#define DSI_HOST_ACK_WITH_ERR_0_S  0
/** DSI_HOST_ACK_WITH_ERR_1 : RO; bitpos: [1]; default: 0;
 *  NA
 */
#define DSI_HOST_ACK_WITH_ERR_1    (BIT(1))
#define DSI_HOST_ACK_WITH_ERR_1_M  (DSI_HOST_ACK_WITH_ERR_1_V << DSI_HOST_ACK_WITH_ERR_1_S)
#define DSI_HOST_ACK_WITH_ERR_1_V  0x00000001U
#define DSI_HOST_ACK_WITH_ERR_1_S  1
/** DSI_HOST_ACK_WITH_ERR_2 : RO; bitpos: [2]; default: 0;
 *  NA
 */
#define DSI_HOST_ACK_WITH_ERR_2    (BIT(2))
#define DSI_HOST_ACK_WITH_ERR_2_M  (DSI_HOST_ACK_WITH_ERR_2_V << DSI_HOST_ACK_WITH_ERR_2_S)
#define DSI_HOST_ACK_WITH_ERR_2_V  0x00000001U
#define DSI_HOST_ACK_WITH_ERR_2_S  2
/** DSI_HOST_ACK_WITH_ERR_3 : RO; bitpos: [3]; default: 0;
 *  NA
 */
#define DSI_HOST_ACK_WITH_ERR_3    (BIT(3))
#define DSI_HOST_ACK_WITH_ERR_3_M  (DSI_HOST_ACK_WITH_ERR_3_V << DSI_HOST_ACK_WITH_ERR_3_S)
#define DSI_HOST_ACK_WITH_ERR_3_V  0x00000001U
#define DSI_HOST_ACK_WITH_ERR_3_S  3
/** DSI_HOST_ACK_WITH_ERR_4 : RO; bitpos: [4]; default: 0;
 *  NA
 */
#define DSI_HOST_ACK_WITH_ERR_4    (BIT(4))
#define DSI_HOST_ACK_WITH_ERR_4_M  (DSI_HOST_ACK_WITH_ERR_4_V << DSI_HOST_ACK_WITH_ERR_4_S)
#define DSI_HOST_ACK_WITH_ERR_4_V  0x00000001U
#define DSI_HOST_ACK_WITH_ERR_4_S  4
/** DSI_HOST_ACK_WITH_ERR_5 : RO; bitpos: [5]; default: 0;
 *  NA
 */
#define DSI_HOST_ACK_WITH_ERR_5    (BIT(5))
#define DSI_HOST_ACK_WITH_ERR_5_M  (DSI_HOST_ACK_WITH_ERR_5_V << DSI_HOST_ACK_WITH_ERR_5_S)
#define DSI_HOST_ACK_WITH_ERR_5_V  0x00000001U
#define DSI_HOST_ACK_WITH_ERR_5_S  5
/** DSI_HOST_ACK_WITH_ERR_6 : RO; bitpos: [6]; default: 0;
 *  NA
 */
#define DSI_HOST_ACK_WITH_ERR_6    (BIT(6))
#define DSI_HOST_ACK_WITH_ERR_6_M  (DSI_HOST_ACK_WITH_ERR_6_V << DSI_HOST_ACK_WITH_ERR_6_S)
#define DSI_HOST_ACK_WITH_ERR_6_V  0x00000001U
#define DSI_HOST_ACK_WITH_ERR_6_S  6
/** DSI_HOST_ACK_WITH_ERR_7 : RO; bitpos: [7]; default: 0;
 *  NA
 */
#define DSI_HOST_ACK_WITH_ERR_7    (BIT(7))
#define DSI_HOST_ACK_WITH_ERR_7_M  (DSI_HOST_ACK_WITH_ERR_7_V << DSI_HOST_ACK_WITH_ERR_7_S)
#define DSI_HOST_ACK_WITH_ERR_7_V  0x00000001U
#define DSI_HOST_ACK_WITH_ERR_7_S  7
/** DSI_HOST_ACK_WITH_ERR_8 : RO; bitpos: [8]; default: 0;
 *  NA
 */
#define DSI_HOST_ACK_WITH_ERR_8    (BIT(8))
#define DSI_HOST_ACK_WITH_ERR_8_M  (DSI_HOST_ACK_WITH_ERR_8_V << DSI_HOST_ACK_WITH_ERR_8_S)
#define DSI_HOST_ACK_WITH_ERR_8_V  0x00000001U
#define DSI_HOST_ACK_WITH_ERR_8_S  8
/** DSI_HOST_ACK_WITH_ERR_9 : RO; bitpos: [9]; default: 0;
 *  NA
 */
#define DSI_HOST_ACK_WITH_ERR_9    (BIT(9))
#define DSI_HOST_ACK_WITH_ERR_9_M  (DSI_HOST_ACK_WITH_ERR_9_V << DSI_HOST_ACK_WITH_ERR_9_S)
#define DSI_HOST_ACK_WITH_ERR_9_V  0x00000001U
#define DSI_HOST_ACK_WITH_ERR_9_S  9
/** DSI_HOST_ACK_WITH_ERR_10 : RO; bitpos: [10]; default: 0;
 *  NA
 */
#define DSI_HOST_ACK_WITH_ERR_10    (BIT(10))
#define DSI_HOST_ACK_WITH_ERR_10_M  (DSI_HOST_ACK_WITH_ERR_10_V << DSI_HOST_ACK_WITH_ERR_10_S)
#define DSI_HOST_ACK_WITH_ERR_10_V  0x00000001U
#define DSI_HOST_ACK_WITH_ERR_10_S  10
/** DSI_HOST_ACK_WITH_ERR_11 : RO; bitpos: [11]; default: 0;
 *  NA
 */
#define DSI_HOST_ACK_WITH_ERR_11    (BIT(11))
#define DSI_HOST_ACK_WITH_ERR_11_M  (DSI_HOST_ACK_WITH_ERR_11_V << DSI_HOST_ACK_WITH_ERR_11_S)
#define DSI_HOST_ACK_WITH_ERR_11_V  0x00000001U
#define DSI_HOST_ACK_WITH_ERR_11_S  11
/** DSI_HOST_ACK_WITH_ERR_12 : RO; bitpos: [12]; default: 0;
 *  NA
 */
#define DSI_HOST_ACK_WITH_ERR_12    (BIT(12))
#define DSI_HOST_ACK_WITH_ERR_12_M  (DSI_HOST_ACK_WITH_ERR_12_V << DSI_HOST_ACK_WITH_ERR_12_S)
#define DSI_HOST_ACK_WITH_ERR_12_V  0x00000001U
#define DSI_HOST_ACK_WITH_ERR_12_S  12
/** DSI_HOST_ACK_WITH_ERR_13 : RO; bitpos: [13]; default: 0;
 *  NA
 */
#define DSI_HOST_ACK_WITH_ERR_13    (BIT(13))
#define DSI_HOST_ACK_WITH_ERR_13_M  (DSI_HOST_ACK_WITH_ERR_13_V << DSI_HOST_ACK_WITH_ERR_13_S)
#define DSI_HOST_ACK_WITH_ERR_13_V  0x00000001U
#define DSI_HOST_ACK_WITH_ERR_13_S  13
/** DSI_HOST_ACK_WITH_ERR_14 : RO; bitpos: [14]; default: 0;
 *  NA
 */
#define DSI_HOST_ACK_WITH_ERR_14    (BIT(14))
#define DSI_HOST_ACK_WITH_ERR_14_M  (DSI_HOST_ACK_WITH_ERR_14_V << DSI_HOST_ACK_WITH_ERR_14_S)
#define DSI_HOST_ACK_WITH_ERR_14_V  0x00000001U
#define DSI_HOST_ACK_WITH_ERR_14_S  14
/** DSI_HOST_ACK_WITH_ERR_15 : RO; bitpos: [15]; default: 0;
 *  NA
 */
#define DSI_HOST_ACK_WITH_ERR_15    (BIT(15))
#define DSI_HOST_ACK_WITH_ERR_15_M  (DSI_HOST_ACK_WITH_ERR_15_V << DSI_HOST_ACK_WITH_ERR_15_S)
#define DSI_HOST_ACK_WITH_ERR_15_V  0x00000001U
#define DSI_HOST_ACK_WITH_ERR_15_S  15
/** DSI_HOST_DPHY_ERRORS_0 : RO; bitpos: [16]; default: 0;
 *  NA
 */
#define DSI_HOST_DPHY_ERRORS_0    (BIT(16))
#define DSI_HOST_DPHY_ERRORS_0_M  (DSI_HOST_DPHY_ERRORS_0_V << DSI_HOST_DPHY_ERRORS_0_S)
#define DSI_HOST_DPHY_ERRORS_0_V  0x00000001U
#define DSI_HOST_DPHY_ERRORS_0_S  16
/** DSI_HOST_DPHY_ERRORS_1 : RO; bitpos: [17]; default: 0;
 *  NA
 */
#define DSI_HOST_DPHY_ERRORS_1    (BIT(17))
#define DSI_HOST_DPHY_ERRORS_1_M  (DSI_HOST_DPHY_ERRORS_1_V << DSI_HOST_DPHY_ERRORS_1_S)
#define DSI_HOST_DPHY_ERRORS_1_V  0x00000001U
#define DSI_HOST_DPHY_ERRORS_1_S  17
/** DSI_HOST_DPHY_ERRORS_2 : RO; bitpos: [18]; default: 0;
 *  NA
 */
#define DSI_HOST_DPHY_ERRORS_2    (BIT(18))
#define DSI_HOST_DPHY_ERRORS_2_M  (DSI_HOST_DPHY_ERRORS_2_V << DSI_HOST_DPHY_ERRORS_2_S)
#define DSI_HOST_DPHY_ERRORS_2_V  0x00000001U
#define DSI_HOST_DPHY_ERRORS_2_S  18
/** DSI_HOST_DPHY_ERRORS_3 : RO; bitpos: [19]; default: 0;
 *  NA
 */
#define DSI_HOST_DPHY_ERRORS_3    (BIT(19))
#define DSI_HOST_DPHY_ERRORS_3_M  (DSI_HOST_DPHY_ERRORS_3_V << DSI_HOST_DPHY_ERRORS_3_S)
#define DSI_HOST_DPHY_ERRORS_3_V  0x00000001U
#define DSI_HOST_DPHY_ERRORS_3_S  19
/** DSI_HOST_DPHY_ERRORS_4 : RO; bitpos: [20]; default: 0;
 *  NA
 */
#define DSI_HOST_DPHY_ERRORS_4    (BIT(20))
#define DSI_HOST_DPHY_ERRORS_4_M  (DSI_HOST_DPHY_ERRORS_4_V << DSI_HOST_DPHY_ERRORS_4_S)
#define DSI_HOST_DPHY_ERRORS_4_V  0x00000001U
#define DSI_HOST_DPHY_ERRORS_4_S  20

/** DSI_HOST_INT_ST1_REG register
 *  NA
 */
#define DSI_HOST_INT_ST1_REG (DR_REG_DSI_HOST_BASE + 0xc0)
/** DSI_HOST_TO_HS_TX : RO; bitpos: [0]; default: 0;
 *  NA
 */
#define DSI_HOST_TO_HS_TX    (BIT(0))
#define DSI_HOST_TO_HS_TX_M  (DSI_HOST_TO_HS_TX_V << DSI_HOST_TO_HS_TX_S)
#define DSI_HOST_TO_HS_TX_V  0x00000001U
#define DSI_HOST_TO_HS_TX_S  0
/** DSI_HOST_TO_LP_RX : RO; bitpos: [1]; default: 0;
 *  NA
 */
#define DSI_HOST_TO_LP_RX    (BIT(1))
#define DSI_HOST_TO_LP_RX_M  (DSI_HOST_TO_LP_RX_V << DSI_HOST_TO_LP_RX_S)
#define DSI_HOST_TO_LP_RX_V  0x00000001U
#define DSI_HOST_TO_LP_RX_S  1
/** DSI_HOST_ECC_SINGLE_ERR : RO; bitpos: [2]; default: 0;
 *  NA
 */
#define DSI_HOST_ECC_SINGLE_ERR    (BIT(2))
#define DSI_HOST_ECC_SINGLE_ERR_M  (DSI_HOST_ECC_SINGLE_ERR_V << DSI_HOST_ECC_SINGLE_ERR_S)
#define DSI_HOST_ECC_SINGLE_ERR_V  0x00000001U
#define DSI_HOST_ECC_SINGLE_ERR_S  2
/** DSI_HOST_ECC_MILTI_ERR : RO; bitpos: [3]; default: 0;
 *  NA
 */
#define DSI_HOST_ECC_MILTI_ERR    (BIT(3))
#define DSI_HOST_ECC_MILTI_ERR_M  (DSI_HOST_ECC_MILTI_ERR_V << DSI_HOST_ECC_MILTI_ERR_S)
#define DSI_HOST_ECC_MILTI_ERR_V  0x00000001U
#define DSI_HOST_ECC_MILTI_ERR_S  3
/** DSI_HOST_CRC_ERR : RO; bitpos: [4]; default: 0;
 *  NA
 */
#define DSI_HOST_CRC_ERR    (BIT(4))
#define DSI_HOST_CRC_ERR_M  (DSI_HOST_CRC_ERR_V << DSI_HOST_CRC_ERR_S)
#define DSI_HOST_CRC_ERR_V  0x00000001U
#define DSI_HOST_CRC_ERR_S  4
/** DSI_HOST_PKT_SIZE_ERR : RO; bitpos: [5]; default: 0;
 *  NA
 */
#define DSI_HOST_PKT_SIZE_ERR    (BIT(5))
#define DSI_HOST_PKT_SIZE_ERR_M  (DSI_HOST_PKT_SIZE_ERR_V << DSI_HOST_PKT_SIZE_ERR_S)
#define DSI_HOST_PKT_SIZE_ERR_V  0x00000001U
#define DSI_HOST_PKT_SIZE_ERR_S  5
/** DSI_HOST_EOPT_ERR : RO; bitpos: [6]; default: 0;
 *  NA
 */
#define DSI_HOST_EOPT_ERR    (BIT(6))
#define DSI_HOST_EOPT_ERR_M  (DSI_HOST_EOPT_ERR_V << DSI_HOST_EOPT_ERR_S)
#define DSI_HOST_EOPT_ERR_V  0x00000001U
#define DSI_HOST_EOPT_ERR_S  6
/** DSI_HOST_DPI_PLD_WR_ERR : RO; bitpos: [7]; default: 0;
 *  NA
 */
#define DSI_HOST_DPI_PLD_WR_ERR    (BIT(7))
#define DSI_HOST_DPI_PLD_WR_ERR_M  (DSI_HOST_DPI_PLD_WR_ERR_V << DSI_HOST_DPI_PLD_WR_ERR_S)
#define DSI_HOST_DPI_PLD_WR_ERR_V  0x00000001U
#define DSI_HOST_DPI_PLD_WR_ERR_S  7
/** DSI_HOST_GEN_CMD_WR_ERR : RO; bitpos: [8]; default: 0;
 *  NA
 */
#define DSI_HOST_GEN_CMD_WR_ERR    (BIT(8))
#define DSI_HOST_GEN_CMD_WR_ERR_M  (DSI_HOST_GEN_CMD_WR_ERR_V << DSI_HOST_GEN_CMD_WR_ERR_S)
#define DSI_HOST_GEN_CMD_WR_ERR_V  0x00000001U
#define DSI_HOST_GEN_CMD_WR_ERR_S  8
/** DSI_HOST_GEN_PLD_WR_ERR : RO; bitpos: [9]; default: 0;
 *  NA
 */
#define DSI_HOST_GEN_PLD_WR_ERR    (BIT(9))
#define DSI_HOST_GEN_PLD_WR_ERR_M  (DSI_HOST_GEN_PLD_WR_ERR_V << DSI_HOST_GEN_PLD_WR_ERR_S)
#define DSI_HOST_GEN_PLD_WR_ERR_V  0x00000001U
#define DSI_HOST_GEN_PLD_WR_ERR_S  9
/** DSI_HOST_GEN_PLD_SEND_ERR : RO; bitpos: [10]; default: 0;
 *  NA
 */
#define DSI_HOST_GEN_PLD_SEND_ERR    (BIT(10))
#define DSI_HOST_GEN_PLD_SEND_ERR_M  (DSI_HOST_GEN_PLD_SEND_ERR_V << DSI_HOST_GEN_PLD_SEND_ERR_S)
#define DSI_HOST_GEN_PLD_SEND_ERR_V  0x00000001U
#define DSI_HOST_GEN_PLD_SEND_ERR_S  10
/** DSI_HOST_GEN_PLD_RD_ERR : RO; bitpos: [11]; default: 0;
 *  NA
 */
#define DSI_HOST_GEN_PLD_RD_ERR    (BIT(11))
#define DSI_HOST_GEN_PLD_RD_ERR_M  (DSI_HOST_GEN_PLD_RD_ERR_V << DSI_HOST_GEN_PLD_RD_ERR_S)
#define DSI_HOST_GEN_PLD_RD_ERR_V  0x00000001U
#define DSI_HOST_GEN_PLD_RD_ERR_S  11
/** DSI_HOST_GEN_PLD_RECEV_ERR : RO; bitpos: [12]; default: 0;
 *  NA
 */
#define DSI_HOST_GEN_PLD_RECEV_ERR    (BIT(12))
#define DSI_HOST_GEN_PLD_RECEV_ERR_M  (DSI_HOST_GEN_PLD_RECEV_ERR_V << DSI_HOST_GEN_PLD_RECEV_ERR_S)
#define DSI_HOST_GEN_PLD_RECEV_ERR_V  0x00000001U
#define DSI_HOST_GEN_PLD_RECEV_ERR_S  12
/** DSI_HOST_DPI_BUFF_PLD_UNDER : RO; bitpos: [19]; default: 0;
 *  NA
 */
#define DSI_HOST_DPI_BUFF_PLD_UNDER    (BIT(19))
#define DSI_HOST_DPI_BUFF_PLD_UNDER_M  (DSI_HOST_DPI_BUFF_PLD_UNDER_V << DSI_HOST_DPI_BUFF_PLD_UNDER_S)
#define DSI_HOST_DPI_BUFF_PLD_UNDER_V  0x00000001U
#define DSI_HOST_DPI_BUFF_PLD_UNDER_S  19

/** DSI_HOST_INT_MSK0_REG register
 *  NA
 */
#define DSI_HOST_INT_MSK0_REG (DR_REG_DSI_HOST_BASE + 0xc4)
/** DSI_HOST_MASK_ACK_WITH_ERR_0 : R/W; bitpos: [0]; default: 0;
 *  NA
 */
#define DSI_HOST_MASK_ACK_WITH_ERR_0    (BIT(0))
#define DSI_HOST_MASK_ACK_WITH_ERR_0_M  (DSI_HOST_MASK_ACK_WITH_ERR_0_V << DSI_HOST_MASK_ACK_WITH_ERR_0_S)
#define DSI_HOST_MASK_ACK_WITH_ERR_0_V  0x00000001U
#define DSI_HOST_MASK_ACK_WITH_ERR_0_S  0
/** DSI_HOST_MASK_ACK_WITH_ERR_1 : R/W; bitpos: [1]; default: 0;
 *  NA
 */
#define DSI_HOST_MASK_ACK_WITH_ERR_1    (BIT(1))
#define DSI_HOST_MASK_ACK_WITH_ERR_1_M  (DSI_HOST_MASK_ACK_WITH_ERR_1_V << DSI_HOST_MASK_ACK_WITH_ERR_1_S)
#define DSI_HOST_MASK_ACK_WITH_ERR_1_V  0x00000001U
#define DSI_HOST_MASK_ACK_WITH_ERR_1_S  1
/** DSI_HOST_MASK_ACK_WITH_ERR_2 : R/W; bitpos: [2]; default: 0;
 *  NA
 */
#define DSI_HOST_MASK_ACK_WITH_ERR_2    (BIT(2))
#define DSI_HOST_MASK_ACK_WITH_ERR_2_M  (DSI_HOST_MASK_ACK_WITH_ERR_2_V << DSI_HOST_MASK_ACK_WITH_ERR_2_S)
#define DSI_HOST_MASK_ACK_WITH_ERR_2_V  0x00000001U
#define DSI_HOST_MASK_ACK_WITH_ERR_2_S  2
/** DSI_HOST_MASK_ACK_WITH_ERR_3 : R/W; bitpos: [3]; default: 0;
 *  NA
 */
#define DSI_HOST_MASK_ACK_WITH_ERR_3    (BIT(3))
#define DSI_HOST_MASK_ACK_WITH_ERR_3_M  (DSI_HOST_MASK_ACK_WITH_ERR_3_V << DSI_HOST_MASK_ACK_WITH_ERR_3_S)
#define DSI_HOST_MASK_ACK_WITH_ERR_3_V  0x00000001U
#define DSI_HOST_MASK_ACK_WITH_ERR_3_S  3
/** DSI_HOST_MASK_ACK_WITH_ERR_4 : R/W; bitpos: [4]; default: 0;
 *  NA
 */
#define DSI_HOST_MASK_ACK_WITH_ERR_4    (BIT(4))
#define DSI_HOST_MASK_ACK_WITH_ERR_4_M  (DSI_HOST_MASK_ACK_WITH_ERR_4_V << DSI_HOST_MASK_ACK_WITH_ERR_4_S)
#define DSI_HOST_MASK_ACK_WITH_ERR_4_V  0x00000001U
#define DSI_HOST_MASK_ACK_WITH_ERR_4_S  4
/** DSI_HOST_MASK_ACK_WITH_ERR_5 : R/W; bitpos: [5]; default: 0;
 *  NA
 */
#define DSI_HOST_MASK_ACK_WITH_ERR_5    (BIT(5))
#define DSI_HOST_MASK_ACK_WITH_ERR_5_M  (DSI_HOST_MASK_ACK_WITH_ERR_5_V << DSI_HOST_MASK_ACK_WITH_ERR_5_S)
#define DSI_HOST_MASK_ACK_WITH_ERR_5_V  0x00000001U
#define DSI_HOST_MASK_ACK_WITH_ERR_5_S  5
/** DSI_HOST_MASK_ACK_WITH_ERR_6 : R/W; bitpos: [6]; default: 0;
 *  NA
 */
#define DSI_HOST_MASK_ACK_WITH_ERR_6    (BIT(6))
#define DSI_HOST_MASK_ACK_WITH_ERR_6_M  (DSI_HOST_MASK_ACK_WITH_ERR_6_V << DSI_HOST_MASK_ACK_WITH_ERR_6_S)
#define DSI_HOST_MASK_ACK_WITH_ERR_6_V  0x00000001U
#define DSI_HOST_MASK_ACK_WITH_ERR_6_S  6
/** DSI_HOST_MASK_ACK_WITH_ERR_7 : R/W; bitpos: [7]; default: 0;
 *  NA
 */
#define DSI_HOST_MASK_ACK_WITH_ERR_7    (BIT(7))
#define DSI_HOST_MASK_ACK_WITH_ERR_7_M  (DSI_HOST_MASK_ACK_WITH_ERR_7_V << DSI_HOST_MASK_ACK_WITH_ERR_7_S)
#define DSI_HOST_MASK_ACK_WITH_ERR_7_V  0x00000001U
#define DSI_HOST_MASK_ACK_WITH_ERR_7_S  7
/** DSI_HOST_MASK_ACK_WITH_ERR_8 : R/W; bitpos: [8]; default: 0;
 *  NA
 */
#define DSI_HOST_MASK_ACK_WITH_ERR_8    (BIT(8))
#define DSI_HOST_MASK_ACK_WITH_ERR_8_M  (DSI_HOST_MASK_ACK_WITH_ERR_8_V << DSI_HOST_MASK_ACK_WITH_ERR_8_S)
#define DSI_HOST_MASK_ACK_WITH_ERR_8_V  0x00000001U
#define DSI_HOST_MASK_ACK_WITH_ERR_8_S  8
/** DSI_HOST_MASK_ACK_WITH_ERR_9 : R/W; bitpos: [9]; default: 0;
 *  NA
 */
#define DSI_HOST_MASK_ACK_WITH_ERR_9    (BIT(9))
#define DSI_HOST_MASK_ACK_WITH_ERR_9_M  (DSI_HOST_MASK_ACK_WITH_ERR_9_V << DSI_HOST_MASK_ACK_WITH_ERR_9_S)
#define DSI_HOST_MASK_ACK_WITH_ERR_9_V  0x00000001U
#define DSI_HOST_MASK_ACK_WITH_ERR_9_S  9
/** DSI_HOST_MASK_ACK_WITH_ERR_10 : R/W; bitpos: [10]; default: 0;
 *  NA
 */
#define DSI_HOST_MASK_ACK_WITH_ERR_10    (BIT(10))
#define DSI_HOST_MASK_ACK_WITH_ERR_10_M  (DSI_HOST_MASK_ACK_WITH_ERR_10_V << DSI_HOST_MASK_ACK_WITH_ERR_10_S)
#define DSI_HOST_MASK_ACK_WITH_ERR_10_V  0x00000001U
#define DSI_HOST_MASK_ACK_WITH_ERR_10_S  10
/** DSI_HOST_MASK_ACK_WITH_ERR_11 : R/W; bitpos: [11]; default: 0;
 *  NA
 */
#define DSI_HOST_MASK_ACK_WITH_ERR_11    (BIT(11))
#define DSI_HOST_MASK_ACK_WITH_ERR_11_M  (DSI_HOST_MASK_ACK_WITH_ERR_11_V << DSI_HOST_MASK_ACK_WITH_ERR_11_S)
#define DSI_HOST_MASK_ACK_WITH_ERR_11_V  0x00000001U
#define DSI_HOST_MASK_ACK_WITH_ERR_11_S  11
/** DSI_HOST_MASK_ACK_WITH_ERR_12 : R/W; bitpos: [12]; default: 0;
 *  NA
 */
#define DSI_HOST_MASK_ACK_WITH_ERR_12    (BIT(12))
#define DSI_HOST_MASK_ACK_WITH_ERR_12_M  (DSI_HOST_MASK_ACK_WITH_ERR_12_V << DSI_HOST_MASK_ACK_WITH_ERR_12_S)
#define DSI_HOST_MASK_ACK_WITH_ERR_12_V  0x00000001U
#define DSI_HOST_MASK_ACK_WITH_ERR_12_S  12
/** DSI_HOST_MASK_ACK_WITH_ERR_13 : R/W; bitpos: [13]; default: 0;
 *  NA
 */
#define DSI_HOST_MASK_ACK_WITH_ERR_13    (BIT(13))
#define DSI_HOST_MASK_ACK_WITH_ERR_13_M  (DSI_HOST_MASK_ACK_WITH_ERR_13_V << DSI_HOST_MASK_ACK_WITH_ERR_13_S)
#define DSI_HOST_MASK_ACK_WITH_ERR_13_V  0x00000001U
#define DSI_HOST_MASK_ACK_WITH_ERR_13_S  13
/** DSI_HOST_MASK_ACK_WITH_ERR_14 : R/W; bitpos: [14]; default: 0;
 *  NA
 */
#define DSI_HOST_MASK_ACK_WITH_ERR_14    (BIT(14))
#define DSI_HOST_MASK_ACK_WITH_ERR_14_M  (DSI_HOST_MASK_ACK_WITH_ERR_14_V << DSI_HOST_MASK_ACK_WITH_ERR_14_S)
#define DSI_HOST_MASK_ACK_WITH_ERR_14_V  0x00000001U
#define DSI_HOST_MASK_ACK_WITH_ERR_14_S  14
/** DSI_HOST_MASK_ACK_WITH_ERR_15 : R/W; bitpos: [15]; default: 0;
 *  NA
 */
#define DSI_HOST_MASK_ACK_WITH_ERR_15    (BIT(15))
#define DSI_HOST_MASK_ACK_WITH_ERR_15_M  (DSI_HOST_MASK_ACK_WITH_ERR_15_V << DSI_HOST_MASK_ACK_WITH_ERR_15_S)
#define DSI_HOST_MASK_ACK_WITH_ERR_15_V  0x00000001U
#define DSI_HOST_MASK_ACK_WITH_ERR_15_S  15
/** DSI_HOST_MASK_DPHY_ERRORS_0 : R/W; bitpos: [16]; default: 0;
 *  NA
 */
#define DSI_HOST_MASK_DPHY_ERRORS_0    (BIT(16))
#define DSI_HOST_MASK_DPHY_ERRORS_0_M  (DSI_HOST_MASK_DPHY_ERRORS_0_V << DSI_HOST_MASK_DPHY_ERRORS_0_S)
#define DSI_HOST_MASK_DPHY_ERRORS_0_V  0x00000001U
#define DSI_HOST_MASK_DPHY_ERRORS_0_S  16
/** DSI_HOST_MASK_DPHY_ERRORS_1 : R/W; bitpos: [17]; default: 0;
 *  NA
 */
#define DSI_HOST_MASK_DPHY_ERRORS_1    (BIT(17))
#define DSI_HOST_MASK_DPHY_ERRORS_1_M  (DSI_HOST_MASK_DPHY_ERRORS_1_V << DSI_HOST_MASK_DPHY_ERRORS_1_S)
#define DSI_HOST_MASK_DPHY_ERRORS_1_V  0x00000001U
#define DSI_HOST_MASK_DPHY_ERRORS_1_S  17
/** DSI_HOST_MASK_DPHY_ERRORS_2 : R/W; bitpos: [18]; default: 0;
 *  NA
 */
#define DSI_HOST_MASK_DPHY_ERRORS_2    (BIT(18))
#define DSI_HOST_MASK_DPHY_ERRORS_2_M  (DSI_HOST_MASK_DPHY_ERRORS_2_V << DSI_HOST_MASK_DPHY_ERRORS_2_S)
#define DSI_HOST_MASK_DPHY_ERRORS_2_V  0x00000001U
#define DSI_HOST_MASK_DPHY_ERRORS_2_S  18
/** DSI_HOST_MASK_DPHY_ERRORS_3 : R/W; bitpos: [19]; default: 0;
 *  NA
 */
#define DSI_HOST_MASK_DPHY_ERRORS_3    (BIT(19))
#define DSI_HOST_MASK_DPHY_ERRORS_3_M  (DSI_HOST_MASK_DPHY_ERRORS_3_V << DSI_HOST_MASK_DPHY_ERRORS_3_S)
#define DSI_HOST_MASK_DPHY_ERRORS_3_V  0x00000001U
#define DSI_HOST_MASK_DPHY_ERRORS_3_S  19
/** DSI_HOST_MASK_DPHY_ERRORS_4 : R/W; bitpos: [20]; default: 0;
 *  NA
 */
#define DSI_HOST_MASK_DPHY_ERRORS_4    (BIT(20))
#define DSI_HOST_MASK_DPHY_ERRORS_4_M  (DSI_HOST_MASK_DPHY_ERRORS_4_V << DSI_HOST_MASK_DPHY_ERRORS_4_S)
#define DSI_HOST_MASK_DPHY_ERRORS_4_V  0x00000001U
#define DSI_HOST_MASK_DPHY_ERRORS_4_S  20

/** DSI_HOST_INT_MSK1_REG register
 *  NA
 */
#define DSI_HOST_INT_MSK1_REG (DR_REG_DSI_HOST_BASE + 0xc8)
/** DSI_HOST_MASK_TO_HS_TX : R/W; bitpos: [0]; default: 0;
 *  NA
 */
#define DSI_HOST_MASK_TO_HS_TX    (BIT(0))
#define DSI_HOST_MASK_TO_HS_TX_M  (DSI_HOST_MASK_TO_HS_TX_V << DSI_HOST_MASK_TO_HS_TX_S)
#define DSI_HOST_MASK_TO_HS_TX_V  0x00000001U
#define DSI_HOST_MASK_TO_HS_TX_S  0
/** DSI_HOST_MASK_TO_LP_RX : R/W; bitpos: [1]; default: 0;
 *  NA
 */
#define DSI_HOST_MASK_TO_LP_RX    (BIT(1))
#define DSI_HOST_MASK_TO_LP_RX_M  (DSI_HOST_MASK_TO_LP_RX_V << DSI_HOST_MASK_TO_LP_RX_S)
#define DSI_HOST_MASK_TO_LP_RX_V  0x00000001U
#define DSI_HOST_MASK_TO_LP_RX_S  1
/** DSI_HOST_MASK_ECC_SINGLE_ERR : R/W; bitpos: [2]; default: 0;
 *  NA
 */
#define DSI_HOST_MASK_ECC_SINGLE_ERR    (BIT(2))
#define DSI_HOST_MASK_ECC_SINGLE_ERR_M  (DSI_HOST_MASK_ECC_SINGLE_ERR_V << DSI_HOST_MASK_ECC_SINGLE_ERR_S)
#define DSI_HOST_MASK_ECC_SINGLE_ERR_V  0x00000001U
#define DSI_HOST_MASK_ECC_SINGLE_ERR_S  2
/** DSI_HOST_MASK_ECC_MILTI_ERR : R/W; bitpos: [3]; default: 0;
 *  NA
 */
#define DSI_HOST_MASK_ECC_MILTI_ERR    (BIT(3))
#define DSI_HOST_MASK_ECC_MILTI_ERR_M  (DSI_HOST_MASK_ECC_MILTI_ERR_V << DSI_HOST_MASK_ECC_MILTI_ERR_S)
#define DSI_HOST_MASK_ECC_MILTI_ERR_V  0x00000001U
#define DSI_HOST_MASK_ECC_MILTI_ERR_S  3
/** DSI_HOST_MASK_CRC_ERR : R/W; bitpos: [4]; default: 0;
 *  NA
 */
#define DSI_HOST_MASK_CRC_ERR    (BIT(4))
#define DSI_HOST_MASK_CRC_ERR_M  (DSI_HOST_MASK_CRC_ERR_V << DSI_HOST_MASK_CRC_ERR_S)
#define DSI_HOST_MASK_CRC_ERR_V  0x00000001U
#define DSI_HOST_MASK_CRC_ERR_S  4
/** DSI_HOST_MASK_PKT_SIZE_ERR : R/W; bitpos: [5]; default: 0;
 *  NA
 */
#define DSI_HOST_MASK_PKT_SIZE_ERR    (BIT(5))
#define DSI_HOST_MASK_PKT_SIZE_ERR_M  (DSI_HOST_MASK_PKT_SIZE_ERR_V << DSI_HOST_MASK_PKT_SIZE_ERR_S)
#define DSI_HOST_MASK_PKT_SIZE_ERR_V  0x00000001U
#define DSI_HOST_MASK_PKT_SIZE_ERR_S  5
/** DSI_HOST_MASK_EOPT_ERR : R/W; bitpos: [6]; default: 0;
 *  NA
 */
#define DSI_HOST_MASK_EOPT_ERR    (BIT(6))
#define DSI_HOST_MASK_EOPT_ERR_M  (DSI_HOST_MASK_EOPT_ERR_V << DSI_HOST_MASK_EOPT_ERR_S)
#define DSI_HOST_MASK_EOPT_ERR_V  0x00000001U
#define DSI_HOST_MASK_EOPT_ERR_S  6
/** DSI_HOST_MASK_DPI_PLD_WR_ERR : R/W; bitpos: [7]; default: 0;
 *  NA
 */
#define DSI_HOST_MASK_DPI_PLD_WR_ERR    (BIT(7))
#define DSI_HOST_MASK_DPI_PLD_WR_ERR_M  (DSI_HOST_MASK_DPI_PLD_WR_ERR_V << DSI_HOST_MASK_DPI_PLD_WR_ERR_S)
#define DSI_HOST_MASK_DPI_PLD_WR_ERR_V  0x00000001U
#define DSI_HOST_MASK_DPI_PLD_WR_ERR_S  7
/** DSI_HOST_MASK_GEN_CMD_WR_ERR : R/W; bitpos: [8]; default: 0;
 *  NA
 */
#define DSI_HOST_MASK_GEN_CMD_WR_ERR    (BIT(8))
#define DSI_HOST_MASK_GEN_CMD_WR_ERR_M  (DSI_HOST_MASK_GEN_CMD_WR_ERR_V << DSI_HOST_MASK_GEN_CMD_WR_ERR_S)
#define DSI_HOST_MASK_GEN_CMD_WR_ERR_V  0x00000001U
#define DSI_HOST_MASK_GEN_CMD_WR_ERR_S  8
/** DSI_HOST_MASK_GEN_PLD_WR_ERR : R/W; bitpos: [9]; default: 0;
 *  NA
 */
#define DSI_HOST_MASK_GEN_PLD_WR_ERR    (BIT(9))
#define DSI_HOST_MASK_GEN_PLD_WR_ERR_M  (DSI_HOST_MASK_GEN_PLD_WR_ERR_V << DSI_HOST_MASK_GEN_PLD_WR_ERR_S)
#define DSI_HOST_MASK_GEN_PLD_WR_ERR_V  0x00000001U
#define DSI_HOST_MASK_GEN_PLD_WR_ERR_S  9
/** DSI_HOST_MASK_GEN_PLD_SEND_ERR : R/W; bitpos: [10]; default: 0;
 *  NA
 */
#define DSI_HOST_MASK_GEN_PLD_SEND_ERR    (BIT(10))
#define DSI_HOST_MASK_GEN_PLD_SEND_ERR_M  (DSI_HOST_MASK_GEN_PLD_SEND_ERR_V << DSI_HOST_MASK_GEN_PLD_SEND_ERR_S)
#define DSI_HOST_MASK_GEN_PLD_SEND_ERR_V  0x00000001U
#define DSI_HOST_MASK_GEN_PLD_SEND_ERR_S  10
/** DSI_HOST_MASK_GEN_PLD_RD_ERR : R/W; bitpos: [11]; default: 0;
 *  NA
 */
#define DSI_HOST_MASK_GEN_PLD_RD_ERR    (BIT(11))
#define DSI_HOST_MASK_GEN_PLD_RD_ERR_M  (DSI_HOST_MASK_GEN_PLD_RD_ERR_V << DSI_HOST_MASK_GEN_PLD_RD_ERR_S)
#define DSI_HOST_MASK_GEN_PLD_RD_ERR_V  0x00000001U
#define DSI_HOST_MASK_GEN_PLD_RD_ERR_S  11
/** DSI_HOST_MASK_GEN_PLD_RECEV_ERR : R/W; bitpos: [12]; default: 0;
 *  NA
 */
#define DSI_HOST_MASK_GEN_PLD_RECEV_ERR    (BIT(12))
#define DSI_HOST_MASK_GEN_PLD_RECEV_ERR_M  (DSI_HOST_MASK_GEN_PLD_RECEV_ERR_V << DSI_HOST_MASK_GEN_PLD_RECEV_ERR_S)
#define DSI_HOST_MASK_GEN_PLD_RECEV_ERR_V  0x00000001U
#define DSI_HOST_MASK_GEN_PLD_RECEV_ERR_S  12
/** DSI_HOST_MASK_DPI_BUFF_PLD_UNDER : R/W; bitpos: [19]; default: 0;
 *  NA
 */
#define DSI_HOST_MASK_DPI_BUFF_PLD_UNDER    (BIT(19))
#define DSI_HOST_MASK_DPI_BUFF_PLD_UNDER_M  (DSI_HOST_MASK_DPI_BUFF_PLD_UNDER_V << DSI_HOST_MASK_DPI_BUFF_PLD_UNDER_S)
#define DSI_HOST_MASK_DPI_BUFF_PLD_UNDER_V  0x00000001U
#define DSI_HOST_MASK_DPI_BUFF_PLD_UNDER_S  19

/** DSI_HOST_PHY_CAL_REG register
 *  NA
 */
#define DSI_HOST_PHY_CAL_REG (DR_REG_DSI_HOST_BASE + 0xcc)
/** DSI_HOST_TXSKEWCALHS : R/W; bitpos: [0]; default: 0;
 *  NA
 */
#define DSI_HOST_TXSKEWCALHS    (BIT(0))
#define DSI_HOST_TXSKEWCALHS_M  (DSI_HOST_TXSKEWCALHS_V << DSI_HOST_TXSKEWCALHS_S)
#define DSI_HOST_TXSKEWCALHS_V  0x00000001U
#define DSI_HOST_TXSKEWCALHS_S  0

/** DSI_HOST_INT_FORCE0_REG register
 *  NA
 */
#define DSI_HOST_INT_FORCE0_REG (DR_REG_DSI_HOST_BASE + 0xd8)
/** DSI_HOST_FORCE_ACK_WITH_ERR_0 : R/W; bitpos: [0]; default: 0;
 *  NA
 */
#define DSI_HOST_FORCE_ACK_WITH_ERR_0    (BIT(0))
#define DSI_HOST_FORCE_ACK_WITH_ERR_0_M  (DSI_HOST_FORCE_ACK_WITH_ERR_0_V << DSI_HOST_FORCE_ACK_WITH_ERR_0_S)
#define DSI_HOST_FORCE_ACK_WITH_ERR_0_V  0x00000001U
#define DSI_HOST_FORCE_ACK_WITH_ERR_0_S  0
/** DSI_HOST_FORCE_ACK_WITH_ERR_1 : R/W; bitpos: [1]; default: 0;
 *  NA
 */
#define DSI_HOST_FORCE_ACK_WITH_ERR_1    (BIT(1))
#define DSI_HOST_FORCE_ACK_WITH_ERR_1_M  (DSI_HOST_FORCE_ACK_WITH_ERR_1_V << DSI_HOST_FORCE_ACK_WITH_ERR_1_S)
#define DSI_HOST_FORCE_ACK_WITH_ERR_1_V  0x00000001U
#define DSI_HOST_FORCE_ACK_WITH_ERR_1_S  1
/** DSI_HOST_FORCE_ACK_WITH_ERR_2 : R/W; bitpos: [2]; default: 0;
 *  NA
 */
#define DSI_HOST_FORCE_ACK_WITH_ERR_2    (BIT(2))
#define DSI_HOST_FORCE_ACK_WITH_ERR_2_M  (DSI_HOST_FORCE_ACK_WITH_ERR_2_V << DSI_HOST_FORCE_ACK_WITH_ERR_2_S)
#define DSI_HOST_FORCE_ACK_WITH_ERR_2_V  0x00000001U
#define DSI_HOST_FORCE_ACK_WITH_ERR_2_S  2
/** DSI_HOST_FORCE_ACK_WITH_ERR_3 : R/W; bitpos: [3]; default: 0;
 *  NA
 */
#define DSI_HOST_FORCE_ACK_WITH_ERR_3    (BIT(3))
#define DSI_HOST_FORCE_ACK_WITH_ERR_3_M  (DSI_HOST_FORCE_ACK_WITH_ERR_3_V << DSI_HOST_FORCE_ACK_WITH_ERR_3_S)
#define DSI_HOST_FORCE_ACK_WITH_ERR_3_V  0x00000001U
#define DSI_HOST_FORCE_ACK_WITH_ERR_3_S  3
/** DSI_HOST_FORCE_ACK_WITH_ERR_4 : R/W; bitpos: [4]; default: 0;
 *  NA
 */
#define DSI_HOST_FORCE_ACK_WITH_ERR_4    (BIT(4))
#define DSI_HOST_FORCE_ACK_WITH_ERR_4_M  (DSI_HOST_FORCE_ACK_WITH_ERR_4_V << DSI_HOST_FORCE_ACK_WITH_ERR_4_S)
#define DSI_HOST_FORCE_ACK_WITH_ERR_4_V  0x00000001U
#define DSI_HOST_FORCE_ACK_WITH_ERR_4_S  4
/** DSI_HOST_FORCE_ACK_WITH_ERR_5 : R/W; bitpos: [5]; default: 0;
 *  NA
 */
#define DSI_HOST_FORCE_ACK_WITH_ERR_5    (BIT(5))
#define DSI_HOST_FORCE_ACK_WITH_ERR_5_M  (DSI_HOST_FORCE_ACK_WITH_ERR_5_V << DSI_HOST_FORCE_ACK_WITH_ERR_5_S)
#define DSI_HOST_FORCE_ACK_WITH_ERR_5_V  0x00000001U
#define DSI_HOST_FORCE_ACK_WITH_ERR_5_S  5
/** DSI_HOST_FORCE_ACK_WITH_ERR_6 : R/W; bitpos: [6]; default: 0;
 *  NA
 */
#define DSI_HOST_FORCE_ACK_WITH_ERR_6    (BIT(6))
#define DSI_HOST_FORCE_ACK_WITH_ERR_6_M  (DSI_HOST_FORCE_ACK_WITH_ERR_6_V << DSI_HOST_FORCE_ACK_WITH_ERR_6_S)
#define DSI_HOST_FORCE_ACK_WITH_ERR_6_V  0x00000001U
#define DSI_HOST_FORCE_ACK_WITH_ERR_6_S  6
/** DSI_HOST_FORCE_ACK_WITH_ERR_7 : R/W; bitpos: [7]; default: 0;
 *  NA
 */
#define DSI_HOST_FORCE_ACK_WITH_ERR_7    (BIT(7))
#define DSI_HOST_FORCE_ACK_WITH_ERR_7_M  (DSI_HOST_FORCE_ACK_WITH_ERR_7_V << DSI_HOST_FORCE_ACK_WITH_ERR_7_S)
#define DSI_HOST_FORCE_ACK_WITH_ERR_7_V  0x00000001U
#define DSI_HOST_FORCE_ACK_WITH_ERR_7_S  7
/** DSI_HOST_FORCE_ACK_WITH_ERR_8 : R/W; bitpos: [8]; default: 0;
 *  NA
 */
#define DSI_HOST_FORCE_ACK_WITH_ERR_8    (BIT(8))
#define DSI_HOST_FORCE_ACK_WITH_ERR_8_M  (DSI_HOST_FORCE_ACK_WITH_ERR_8_V << DSI_HOST_FORCE_ACK_WITH_ERR_8_S)
#define DSI_HOST_FORCE_ACK_WITH_ERR_8_V  0x00000001U
#define DSI_HOST_FORCE_ACK_WITH_ERR_8_S  8
/** DSI_HOST_FORCE_ACK_WITH_ERR_9 : R/W; bitpos: [9]; default: 0;
 *  NA
 */
#define DSI_HOST_FORCE_ACK_WITH_ERR_9    (BIT(9))
#define DSI_HOST_FORCE_ACK_WITH_ERR_9_M  (DSI_HOST_FORCE_ACK_WITH_ERR_9_V << DSI_HOST_FORCE_ACK_WITH_ERR_9_S)
#define DSI_HOST_FORCE_ACK_WITH_ERR_9_V  0x00000001U
#define DSI_HOST_FORCE_ACK_WITH_ERR_9_S  9
/** DSI_HOST_FORCE_ACK_WITH_ERR_10 : R/W; bitpos: [10]; default: 0;
 *  NA
 */
#define DSI_HOST_FORCE_ACK_WITH_ERR_10    (BIT(10))
#define DSI_HOST_FORCE_ACK_WITH_ERR_10_M  (DSI_HOST_FORCE_ACK_WITH_ERR_10_V << DSI_HOST_FORCE_ACK_WITH_ERR_10_S)
#define DSI_HOST_FORCE_ACK_WITH_ERR_10_V  0x00000001U
#define DSI_HOST_FORCE_ACK_WITH_ERR_10_S  10
/** DSI_HOST_FORCE_ACK_WITH_ERR_11 : R/W; bitpos: [11]; default: 0;
 *  NA
 */
#define DSI_HOST_FORCE_ACK_WITH_ERR_11    (BIT(11))
#define DSI_HOST_FORCE_ACK_WITH_ERR_11_M  (DSI_HOST_FORCE_ACK_WITH_ERR_11_V << DSI_HOST_FORCE_ACK_WITH_ERR_11_S)
#define DSI_HOST_FORCE_ACK_WITH_ERR_11_V  0x00000001U
#define DSI_HOST_FORCE_ACK_WITH_ERR_11_S  11
/** DSI_HOST_FORCE_ACK_WITH_ERR_12 : R/W; bitpos: [12]; default: 0;
 *  NA
 */
#define DSI_HOST_FORCE_ACK_WITH_ERR_12    (BIT(12))
#define DSI_HOST_FORCE_ACK_WITH_ERR_12_M  (DSI_HOST_FORCE_ACK_WITH_ERR_12_V << DSI_HOST_FORCE_ACK_WITH_ERR_12_S)
#define DSI_HOST_FORCE_ACK_WITH_ERR_12_V  0x00000001U
#define DSI_HOST_FORCE_ACK_WITH_ERR_12_S  12
/** DSI_HOST_FORCE_ACK_WITH_ERR_13 : R/W; bitpos: [13]; default: 0;
 *  NA
 */
#define DSI_HOST_FORCE_ACK_WITH_ERR_13    (BIT(13))
#define DSI_HOST_FORCE_ACK_WITH_ERR_13_M  (DSI_HOST_FORCE_ACK_WITH_ERR_13_V << DSI_HOST_FORCE_ACK_WITH_ERR_13_S)
#define DSI_HOST_FORCE_ACK_WITH_ERR_13_V  0x00000001U
#define DSI_HOST_FORCE_ACK_WITH_ERR_13_S  13
/** DSI_HOST_FORCE_ACK_WITH_ERR_14 : R/W; bitpos: [14]; default: 0;
 *  NA
 */
#define DSI_HOST_FORCE_ACK_WITH_ERR_14    (BIT(14))
#define DSI_HOST_FORCE_ACK_WITH_ERR_14_M  (DSI_HOST_FORCE_ACK_WITH_ERR_14_V << DSI_HOST_FORCE_ACK_WITH_ERR_14_S)
#define DSI_HOST_FORCE_ACK_WITH_ERR_14_V  0x00000001U
#define DSI_HOST_FORCE_ACK_WITH_ERR_14_S  14
/** DSI_HOST_FORCE_ACK_WITH_ERR_15 : R/W; bitpos: [15]; default: 0;
 *  NA
 */
#define DSI_HOST_FORCE_ACK_WITH_ERR_15    (BIT(15))
#define DSI_HOST_FORCE_ACK_WITH_ERR_15_M  (DSI_HOST_FORCE_ACK_WITH_ERR_15_V << DSI_HOST_FORCE_ACK_WITH_ERR_15_S)
#define DSI_HOST_FORCE_ACK_WITH_ERR_15_V  0x00000001U
#define DSI_HOST_FORCE_ACK_WITH_ERR_15_S  15
/** DSI_HOST_FORCE_DPHY_ERRORS_0 : R/W; bitpos: [16]; default: 0;
 *  NA
 */
#define DSI_HOST_FORCE_DPHY_ERRORS_0    (BIT(16))
#define DSI_HOST_FORCE_DPHY_ERRORS_0_M  (DSI_HOST_FORCE_DPHY_ERRORS_0_V << DSI_HOST_FORCE_DPHY_ERRORS_0_S)
#define DSI_HOST_FORCE_DPHY_ERRORS_0_V  0x00000001U
#define DSI_HOST_FORCE_DPHY_ERRORS_0_S  16
/** DSI_HOST_FORCE_DPHY_ERRORS_1 : R/W; bitpos: [17]; default: 0;
 *  NA
 */
#define DSI_HOST_FORCE_DPHY_ERRORS_1    (BIT(17))
#define DSI_HOST_FORCE_DPHY_ERRORS_1_M  (DSI_HOST_FORCE_DPHY_ERRORS_1_V << DSI_HOST_FORCE_DPHY_ERRORS_1_S)
#define DSI_HOST_FORCE_DPHY_ERRORS_1_V  0x00000001U
#define DSI_HOST_FORCE_DPHY_ERRORS_1_S  17
/** DSI_HOST_FORCE_DPHY_ERRORS_2 : R/W; bitpos: [18]; default: 0;
 *  NA
 */
#define DSI_HOST_FORCE_DPHY_ERRORS_2    (BIT(18))
#define DSI_HOST_FORCE_DPHY_ERRORS_2_M  (DSI_HOST_FORCE_DPHY_ERRORS_2_V << DSI_HOST_FORCE_DPHY_ERRORS_2_S)
#define DSI_HOST_FORCE_DPHY_ERRORS_2_V  0x00000001U
#define DSI_HOST_FORCE_DPHY_ERRORS_2_S  18
/** DSI_HOST_FORCE_DPHY_ERRORS_3 : R/W; bitpos: [19]; default: 0;
 *  NA
 */
#define DSI_HOST_FORCE_DPHY_ERRORS_3    (BIT(19))
#define DSI_HOST_FORCE_DPHY_ERRORS_3_M  (DSI_HOST_FORCE_DPHY_ERRORS_3_V << DSI_HOST_FORCE_DPHY_ERRORS_3_S)
#define DSI_HOST_FORCE_DPHY_ERRORS_3_V  0x00000001U
#define DSI_HOST_FORCE_DPHY_ERRORS_3_S  19
/** DSI_HOST_FORCE_DPHY_ERRORS_4 : R/W; bitpos: [20]; default: 0;
 *  NA
 */
#define DSI_HOST_FORCE_DPHY_ERRORS_4    (BIT(20))
#define DSI_HOST_FORCE_DPHY_ERRORS_4_M  (DSI_HOST_FORCE_DPHY_ERRORS_4_V << DSI_HOST_FORCE_DPHY_ERRORS_4_S)
#define DSI_HOST_FORCE_DPHY_ERRORS_4_V  0x00000001U
#define DSI_HOST_FORCE_DPHY_ERRORS_4_S  20

/** DSI_HOST_INT_FORCE1_REG register
 *  NA
 */
#define DSI_HOST_INT_FORCE1_REG (DR_REG_DSI_HOST_BASE + 0xdc)
/** DSI_HOST_FORCE_TO_HS_TX : R/W; bitpos: [0]; default: 0;
 *  NA
 */
#define DSI_HOST_FORCE_TO_HS_TX    (BIT(0))
#define DSI_HOST_FORCE_TO_HS_TX_M  (DSI_HOST_FORCE_TO_HS_TX_V << DSI_HOST_FORCE_TO_HS_TX_S)
#define DSI_HOST_FORCE_TO_HS_TX_V  0x00000001U
#define DSI_HOST_FORCE_TO_HS_TX_S  0
/** DSI_HOST_FORCE_TO_LP_RX : R/W; bitpos: [1]; default: 0;
 *  NA
 */
#define DSI_HOST_FORCE_TO_LP_RX    (BIT(1))
#define DSI_HOST_FORCE_TO_LP_RX_M  (DSI_HOST_FORCE_TO_LP_RX_V << DSI_HOST_FORCE_TO_LP_RX_S)
#define DSI_HOST_FORCE_TO_LP_RX_V  0x00000001U
#define DSI_HOST_FORCE_TO_LP_RX_S  1
/** DSI_HOST_FORCE_ECC_SINGLE_ERR : R/W; bitpos: [2]; default: 0;
 *  NA
 */
#define DSI_HOST_FORCE_ECC_SINGLE_ERR    (BIT(2))
#define DSI_HOST_FORCE_ECC_SINGLE_ERR_M  (DSI_HOST_FORCE_ECC_SINGLE_ERR_V << DSI_HOST_FORCE_ECC_SINGLE_ERR_S)
#define DSI_HOST_FORCE_ECC_SINGLE_ERR_V  0x00000001U
#define DSI_HOST_FORCE_ECC_SINGLE_ERR_S  2
/** DSI_HOST_FORCE_ECC_MILTI_ERR : R/W; bitpos: [3]; default: 0;
 *  NA
 */
#define DSI_HOST_FORCE_ECC_MILTI_ERR    (BIT(3))
#define DSI_HOST_FORCE_ECC_MILTI_ERR_M  (DSI_HOST_FORCE_ECC_MILTI_ERR_V << DSI_HOST_FORCE_ECC_MILTI_ERR_S)
#define DSI_HOST_FORCE_ECC_MILTI_ERR_V  0x00000001U
#define DSI_HOST_FORCE_ECC_MILTI_ERR_S  3
/** DSI_HOST_FORCE_CRC_ERR : R/W; bitpos: [4]; default: 0;
 *  NA
 */
#define DSI_HOST_FORCE_CRC_ERR    (BIT(4))
#define DSI_HOST_FORCE_CRC_ERR_M  (DSI_HOST_FORCE_CRC_ERR_V << DSI_HOST_FORCE_CRC_ERR_S)
#define DSI_HOST_FORCE_CRC_ERR_V  0x00000001U
#define DSI_HOST_FORCE_CRC_ERR_S  4
/** DSI_HOST_FORCE_PKT_SIZE_ERR : R/W; bitpos: [5]; default: 0;
 *  NA
 */
#define DSI_HOST_FORCE_PKT_SIZE_ERR    (BIT(5))
#define DSI_HOST_FORCE_PKT_SIZE_ERR_M  (DSI_HOST_FORCE_PKT_SIZE_ERR_V << DSI_HOST_FORCE_PKT_SIZE_ERR_S)
#define DSI_HOST_FORCE_PKT_SIZE_ERR_V  0x00000001U
#define DSI_HOST_FORCE_PKT_SIZE_ERR_S  5
/** DSI_HOST_FORCE_EOPT_ERR : R/W; bitpos: [6]; default: 0;
 *  NA
 */
#define DSI_HOST_FORCE_EOPT_ERR    (BIT(6))
#define DSI_HOST_FORCE_EOPT_ERR_M  (DSI_HOST_FORCE_EOPT_ERR_V << DSI_HOST_FORCE_EOPT_ERR_S)
#define DSI_HOST_FORCE_EOPT_ERR_V  0x00000001U
#define DSI_HOST_FORCE_EOPT_ERR_S  6
/** DSI_HOST_FORCE_DPI_PLD_WR_ERR : R/W; bitpos: [7]; default: 0;
 *  NA
 */
#define DSI_HOST_FORCE_DPI_PLD_WR_ERR    (BIT(7))
#define DSI_HOST_FORCE_DPI_PLD_WR_ERR_M  (DSI_HOST_FORCE_DPI_PLD_WR_ERR_V << DSI_HOST_FORCE_DPI_PLD_WR_ERR_S)
#define DSI_HOST_FORCE_DPI_PLD_WR_ERR_V  0x00000001U
#define DSI_HOST_FORCE_DPI_PLD_WR_ERR_S  7
/** DSI_HOST_FORCE_GEN_CMD_WR_ERR : R/W; bitpos: [8]; default: 0;
 *  NA
 */
#define DSI_HOST_FORCE_GEN_CMD_WR_ERR    (BIT(8))
#define DSI_HOST_FORCE_GEN_CMD_WR_ERR_M  (DSI_HOST_FORCE_GEN_CMD_WR_ERR_V << DSI_HOST_FORCE_GEN_CMD_WR_ERR_S)
#define DSI_HOST_FORCE_GEN_CMD_WR_ERR_V  0x00000001U
#define DSI_HOST_FORCE_GEN_CMD_WR_ERR_S  8
/** DSI_HOST_FORCE_GEN_PLD_WR_ERR : R/W; bitpos: [9]; default: 0;
 *  NA
 */
#define DSI_HOST_FORCE_GEN_PLD_WR_ERR    (BIT(9))
#define DSI_HOST_FORCE_GEN_PLD_WR_ERR_M  (DSI_HOST_FORCE_GEN_PLD_WR_ERR_V << DSI_HOST_FORCE_GEN_PLD_WR_ERR_S)
#define DSI_HOST_FORCE_GEN_PLD_WR_ERR_V  0x00000001U
#define DSI_HOST_FORCE_GEN_PLD_WR_ERR_S  9
/** DSI_HOST_FORCE_GEN_PLD_SEND_ERR : R/W; bitpos: [10]; default: 0;
 *  NA
 */
#define DSI_HOST_FORCE_GEN_PLD_SEND_ERR    (BIT(10))
#define DSI_HOST_FORCE_GEN_PLD_SEND_ERR_M  (DSI_HOST_FORCE_GEN_PLD_SEND_ERR_V << DSI_HOST_FORCE_GEN_PLD_SEND_ERR_S)
#define DSI_HOST_FORCE_GEN_PLD_SEND_ERR_V  0x00000001U
#define DSI_HOST_FORCE_GEN_PLD_SEND_ERR_S  10
/** DSI_HOST_FORCE_GEN_PLD_RD_ERR : R/W; bitpos: [11]; default: 0;
 *  NA
 */
#define DSI_HOST_FORCE_GEN_PLD_RD_ERR    (BIT(11))
#define DSI_HOST_FORCE_GEN_PLD_RD_ERR_M  (DSI_HOST_FORCE_GEN_PLD_RD_ERR_V << DSI_HOST_FORCE_GEN_PLD_RD_ERR_S)
#define DSI_HOST_FORCE_GEN_PLD_RD_ERR_V  0x00000001U
#define DSI_HOST_FORCE_GEN_PLD_RD_ERR_S  11
/** DSI_HOST_FORCE_GEN_PLD_RECEV_ERR : R/W; bitpos: [12]; default: 0;
 *  NA
 */
#define DSI_HOST_FORCE_GEN_PLD_RECEV_ERR    (BIT(12))
#define DSI_HOST_FORCE_GEN_PLD_RECEV_ERR_M  (DSI_HOST_FORCE_GEN_PLD_RECEV_ERR_V << DSI_HOST_FORCE_GEN_PLD_RECEV_ERR_S)
#define DSI_HOST_FORCE_GEN_PLD_RECEV_ERR_V  0x00000001U
#define DSI_HOST_FORCE_GEN_PLD_RECEV_ERR_S  12
/** DSI_HOST_FORCE_DPI_BUFF_PLD_UNDER : R/W; bitpos: [19]; default: 0;
 *  NA
 */
#define DSI_HOST_FORCE_DPI_BUFF_PLD_UNDER    (BIT(19))
#define DSI_HOST_FORCE_DPI_BUFF_PLD_UNDER_M  (DSI_HOST_FORCE_DPI_BUFF_PLD_UNDER_V << DSI_HOST_FORCE_DPI_BUFF_PLD_UNDER_S)
#define DSI_HOST_FORCE_DPI_BUFF_PLD_UNDER_V  0x00000001U
#define DSI_HOST_FORCE_DPI_BUFF_PLD_UNDER_S  19

/** DSI_HOST_DSC_PARAMETER_REG register
 *  NA
 */
#define DSI_HOST_DSC_PARAMETER_REG (DR_REG_DSI_HOST_BASE + 0xf0)
/** DSI_HOST_COMPRESSION_MODE : R/W; bitpos: [0]; default: 0;
 *  NA
 */
#define DSI_HOST_COMPRESSION_MODE    (BIT(0))
#define DSI_HOST_COMPRESSION_MODE_M  (DSI_HOST_COMPRESSION_MODE_V << DSI_HOST_COMPRESSION_MODE_S)
#define DSI_HOST_COMPRESSION_MODE_V  0x00000001U
#define DSI_HOST_COMPRESSION_MODE_S  0
/** DSI_HOST_COMPRESS_ALGO : R/W; bitpos: [9:8]; default: 0;
 *  NA
 */
#define DSI_HOST_COMPRESS_ALGO    0x00000003U
#define DSI_HOST_COMPRESS_ALGO_M  (DSI_HOST_COMPRESS_ALGO_V << DSI_HOST_COMPRESS_ALGO_S)
#define DSI_HOST_COMPRESS_ALGO_V  0x00000003U
#define DSI_HOST_COMPRESS_ALGO_S  8
/** DSI_HOST_PPS_SEL : R/W; bitpos: [17:16]; default: 0;
 *  NA
 */
#define DSI_HOST_PPS_SEL    0x00000003U
#define DSI_HOST_PPS_SEL_M  (DSI_HOST_PPS_SEL_V << DSI_HOST_PPS_SEL_S)
#define DSI_HOST_PPS_SEL_V  0x00000003U
#define DSI_HOST_PPS_SEL_S  16

/** DSI_HOST_PHY_TMR_RD_CFG_REG register
 *  NA
 */
#define DSI_HOST_PHY_TMR_RD_CFG_REG (DR_REG_DSI_HOST_BASE + 0xf4)
/** DSI_HOST_MAX_RD_TIME : R/W; bitpos: [14:0]; default: 0;
 *  NA
 */
#define DSI_HOST_MAX_RD_TIME    0x00007FFFU
#define DSI_HOST_MAX_RD_TIME_M  (DSI_HOST_MAX_RD_TIME_V << DSI_HOST_MAX_RD_TIME_S)
#define DSI_HOST_MAX_RD_TIME_V  0x00007FFFU
#define DSI_HOST_MAX_RD_TIME_S  0

/** DSI_HOST_VID_SHADOW_CTRL_REG register
 *  NA
 */
#define DSI_HOST_VID_SHADOW_CTRL_REG (DR_REG_DSI_HOST_BASE + 0x100)
/** DSI_HOST_VID_SHADOW_EN : R/W; bitpos: [0]; default: 0;
 *  NA
 */
#define DSI_HOST_VID_SHADOW_EN    (BIT(0))
#define DSI_HOST_VID_SHADOW_EN_M  (DSI_HOST_VID_SHADOW_EN_V << DSI_HOST_VID_SHADOW_EN_S)
#define DSI_HOST_VID_SHADOW_EN_V  0x00000001U
#define DSI_HOST_VID_SHADOW_EN_S  0
/** DSI_HOST_VID_SHADOW_REQ : R/W; bitpos: [8]; default: 0;
 *  NA
 */
#define DSI_HOST_VID_SHADOW_REQ    (BIT(8))
#define DSI_HOST_VID_SHADOW_REQ_M  (DSI_HOST_VID_SHADOW_REQ_V << DSI_HOST_VID_SHADOW_REQ_S)
#define DSI_HOST_VID_SHADOW_REQ_V  0x00000001U
#define DSI_HOST_VID_SHADOW_REQ_S  8
/** DSI_HOST_VID_SHADOW_PIN_REQ : R/W; bitpos: [16]; default: 0;
 *  NA
 */
#define DSI_HOST_VID_SHADOW_PIN_REQ    (BIT(16))
#define DSI_HOST_VID_SHADOW_PIN_REQ_M  (DSI_HOST_VID_SHADOW_PIN_REQ_V << DSI_HOST_VID_SHADOW_PIN_REQ_S)
#define DSI_HOST_VID_SHADOW_PIN_REQ_V  0x00000001U
#define DSI_HOST_VID_SHADOW_PIN_REQ_S  16

/** DSI_HOST_DPI_VCID_ACT_REG register
 *  NA
 */
#define DSI_HOST_DPI_VCID_ACT_REG (DR_REG_DSI_HOST_BASE + 0x10c)
/** DSI_HOST_DPI_VCID_ACT : RO; bitpos: [1:0]; default: 0;
 *  NA
 */
#define DSI_HOST_DPI_VCID_ACT    0x00000003U
#define DSI_HOST_DPI_VCID_ACT_M  (DSI_HOST_DPI_VCID_ACT_V << DSI_HOST_DPI_VCID_ACT_S)
#define DSI_HOST_DPI_VCID_ACT_V  0x00000003U
#define DSI_HOST_DPI_VCID_ACT_S  0

/** DSI_HOST_DPI_COLOR_CODING_ACT_REG register
 *  NA
 */
#define DSI_HOST_DPI_COLOR_CODING_ACT_REG (DR_REG_DSI_HOST_BASE + 0x110)
/** DSI_HOST_DPI_COLOR_CODING_ACT : RO; bitpos: [3:0]; default: 0;
 *  NA
 */
#define DSI_HOST_DPI_COLOR_CODING_ACT    0x0000000FU
#define DSI_HOST_DPI_COLOR_CODING_ACT_M  (DSI_HOST_DPI_COLOR_CODING_ACT_V << DSI_HOST_DPI_COLOR_CODING_ACT_S)
#define DSI_HOST_DPI_COLOR_CODING_ACT_V  0x0000000FU
#define DSI_HOST_DPI_COLOR_CODING_ACT_S  0
/** DSI_HOST_LOOSELY18_EN_ACT : RO; bitpos: [8]; default: 0;
 *  NA
 */
#define DSI_HOST_LOOSELY18_EN_ACT    (BIT(8))
#define DSI_HOST_LOOSELY18_EN_ACT_M  (DSI_HOST_LOOSELY18_EN_ACT_V << DSI_HOST_LOOSELY18_EN_ACT_S)
#define DSI_HOST_LOOSELY18_EN_ACT_V  0x00000001U
#define DSI_HOST_LOOSELY18_EN_ACT_S  8

/** DSI_HOST_DPI_LP_CMD_TIM_ACT_REG register
 *  NA
 */
#define DSI_HOST_DPI_LP_CMD_TIM_ACT_REG (DR_REG_DSI_HOST_BASE + 0x118)
/** DSI_HOST_INVACT_LPCMD_TIME_ACT : RO; bitpos: [7:0]; default: 0;
 *  NA
 */
#define DSI_HOST_INVACT_LPCMD_TIME_ACT    0x000000FFU
#define DSI_HOST_INVACT_LPCMD_TIME_ACT_M  (DSI_HOST_INVACT_LPCMD_TIME_ACT_V << DSI_HOST_INVACT_LPCMD_TIME_ACT_S)
#define DSI_HOST_INVACT_LPCMD_TIME_ACT_V  0x000000FFU
#define DSI_HOST_INVACT_LPCMD_TIME_ACT_S  0
/** DSI_HOST_OUTVACT_LPCMD_TIME_ACT : RO; bitpos: [23:16]; default: 0;
 *  NA
 */
#define DSI_HOST_OUTVACT_LPCMD_TIME_ACT    0x000000FFU
#define DSI_HOST_OUTVACT_LPCMD_TIME_ACT_M  (DSI_HOST_OUTVACT_LPCMD_TIME_ACT_V << DSI_HOST_OUTVACT_LPCMD_TIME_ACT_S)
#define DSI_HOST_OUTVACT_LPCMD_TIME_ACT_V  0x000000FFU
#define DSI_HOST_OUTVACT_LPCMD_TIME_ACT_S  16

/** DSI_HOST_EDPI_TE_HW_CFG_REG register
 *  NA
 */
#define DSI_HOST_EDPI_TE_HW_CFG_REG (DR_REG_DSI_HOST_BASE + 0x11c)
/** DSI_HOST_HW_TEAR_EFFECT_ON : R/W; bitpos: [0]; default: 0;
 *  NA
 */
#define DSI_HOST_HW_TEAR_EFFECT_ON    (BIT(0))
#define DSI_HOST_HW_TEAR_EFFECT_ON_M  (DSI_HOST_HW_TEAR_EFFECT_ON_V << DSI_HOST_HW_TEAR_EFFECT_ON_S)
#define DSI_HOST_HW_TEAR_EFFECT_ON_V  0x00000001U
#define DSI_HOST_HW_TEAR_EFFECT_ON_S  0
/** DSI_HOST_HW_TEAR_EFFECT_GEN : R/W; bitpos: [1]; default: 0;
 *  NA
 */
#define DSI_HOST_HW_TEAR_EFFECT_GEN    (BIT(1))
#define DSI_HOST_HW_TEAR_EFFECT_GEN_M  (DSI_HOST_HW_TEAR_EFFECT_GEN_V << DSI_HOST_HW_TEAR_EFFECT_GEN_S)
#define DSI_HOST_HW_TEAR_EFFECT_GEN_V  0x00000001U
#define DSI_HOST_HW_TEAR_EFFECT_GEN_S  1
/** DSI_HOST_HW_SET_SCAN_LINE : R/W; bitpos: [4]; default: 0;
 *  NA
 */
#define DSI_HOST_HW_SET_SCAN_LINE    (BIT(4))
#define DSI_HOST_HW_SET_SCAN_LINE_M  (DSI_HOST_HW_SET_SCAN_LINE_V << DSI_HOST_HW_SET_SCAN_LINE_S)
#define DSI_HOST_HW_SET_SCAN_LINE_V  0x00000001U
#define DSI_HOST_HW_SET_SCAN_LINE_S  4
/** DSI_HOST_SCAN_LINE_PARAMETER : R/W; bitpos: [31:16]; default: 0;
 *  NA
 */
#define DSI_HOST_SCAN_LINE_PARAMETER    0x0000FFFFU
#define DSI_HOST_SCAN_LINE_PARAMETER_M  (DSI_HOST_SCAN_LINE_PARAMETER_V << DSI_HOST_SCAN_LINE_PARAMETER_S)
#define DSI_HOST_SCAN_LINE_PARAMETER_V  0x0000FFFFU
#define DSI_HOST_SCAN_LINE_PARAMETER_S  16

/** DSI_HOST_VID_MODE_CFG_ACT_REG register
 *  NA
 */
#define DSI_HOST_VID_MODE_CFG_ACT_REG (DR_REG_DSI_HOST_BASE + 0x138)
/** DSI_HOST_VID_MODE_TYPE_ACT : RO; bitpos: [1:0]; default: 0;
 *  NA
 */
#define DSI_HOST_VID_MODE_TYPE_ACT    0x00000003U
#define DSI_HOST_VID_MODE_TYPE_ACT_M  (DSI_HOST_VID_MODE_TYPE_ACT_V << DSI_HOST_VID_MODE_TYPE_ACT_S)
#define DSI_HOST_VID_MODE_TYPE_ACT_V  0x00000003U
#define DSI_HOST_VID_MODE_TYPE_ACT_S  0
/** DSI_HOST_LP_VSA_EN_ACT : RO; bitpos: [2]; default: 0;
 *  NA
 */
#define DSI_HOST_LP_VSA_EN_ACT    (BIT(2))
#define DSI_HOST_LP_VSA_EN_ACT_M  (DSI_HOST_LP_VSA_EN_ACT_V << DSI_HOST_LP_VSA_EN_ACT_S)
#define DSI_HOST_LP_VSA_EN_ACT_V  0x00000001U
#define DSI_HOST_LP_VSA_EN_ACT_S  2
/** DSI_HOST_LP_VBP_EN_ACT : RO; bitpos: [3]; default: 0;
 *  NA
 */
#define DSI_HOST_LP_VBP_EN_ACT    (BIT(3))
#define DSI_HOST_LP_VBP_EN_ACT_M  (DSI_HOST_LP_VBP_EN_ACT_V << DSI_HOST_LP_VBP_EN_ACT_S)
#define DSI_HOST_LP_VBP_EN_ACT_V  0x00000001U
#define DSI_HOST_LP_VBP_EN_ACT_S  3
/** DSI_HOST_LP_VFP_EN_ACT : RO; bitpos: [4]; default: 0;
 *  NA
 */
#define DSI_HOST_LP_VFP_EN_ACT    (BIT(4))
#define DSI_HOST_LP_VFP_EN_ACT_M  (DSI_HOST_LP_VFP_EN_ACT_V << DSI_HOST_LP_VFP_EN_ACT_S)
#define DSI_HOST_LP_VFP_EN_ACT_V  0x00000001U
#define DSI_HOST_LP_VFP_EN_ACT_S  4
/** DSI_HOST_LP_VACT_EN_ACT : RO; bitpos: [5]; default: 0;
 *  NA
 */
#define DSI_HOST_LP_VACT_EN_ACT    (BIT(5))
#define DSI_HOST_LP_VACT_EN_ACT_M  (DSI_HOST_LP_VACT_EN_ACT_V << DSI_HOST_LP_VACT_EN_ACT_S)
#define DSI_HOST_LP_VACT_EN_ACT_V  0x00000001U
#define DSI_HOST_LP_VACT_EN_ACT_S  5
/** DSI_HOST_LP_HBP_EN_ACT : RO; bitpos: [6]; default: 0;
 *  NA
 */
#define DSI_HOST_LP_HBP_EN_ACT    (BIT(6))
#define DSI_HOST_LP_HBP_EN_ACT_M  (DSI_HOST_LP_HBP_EN_ACT_V << DSI_HOST_LP_HBP_EN_ACT_S)
#define DSI_HOST_LP_HBP_EN_ACT_V  0x00000001U
#define DSI_HOST_LP_HBP_EN_ACT_S  6
/** DSI_HOST_LP_HFP_EN_ACT : RO; bitpos: [7]; default: 0;
 *  NA
 */
#define DSI_HOST_LP_HFP_EN_ACT    (BIT(7))
#define DSI_HOST_LP_HFP_EN_ACT_M  (DSI_HOST_LP_HFP_EN_ACT_V << DSI_HOST_LP_HFP_EN_ACT_S)
#define DSI_HOST_LP_HFP_EN_ACT_V  0x00000001U
#define DSI_HOST_LP_HFP_EN_ACT_S  7
/** DSI_HOST_FRAME_BTA_ACK_EN_ACT : RO; bitpos: [8]; default: 0;
 *  NA
 */
#define DSI_HOST_FRAME_BTA_ACK_EN_ACT    (BIT(8))
#define DSI_HOST_FRAME_BTA_ACK_EN_ACT_M  (DSI_HOST_FRAME_BTA_ACK_EN_ACT_V << DSI_HOST_FRAME_BTA_ACK_EN_ACT_S)
#define DSI_HOST_FRAME_BTA_ACK_EN_ACT_V  0x00000001U
#define DSI_HOST_FRAME_BTA_ACK_EN_ACT_S  8
/** DSI_HOST_LP_CMD_EN_ACT : RO; bitpos: [9]; default: 0;
 *  NA
 */
#define DSI_HOST_LP_CMD_EN_ACT    (BIT(9))
#define DSI_HOST_LP_CMD_EN_ACT_M  (DSI_HOST_LP_CMD_EN_ACT_V << DSI_HOST_LP_CMD_EN_ACT_S)
#define DSI_HOST_LP_CMD_EN_ACT_V  0x00000001U
#define DSI_HOST_LP_CMD_EN_ACT_S  9

/** DSI_HOST_VID_PKT_SIZE_ACT_REG register
 *  NA
 */
#define DSI_HOST_VID_PKT_SIZE_ACT_REG (DR_REG_DSI_HOST_BASE + 0x13c)
/** DSI_HOST_VID_PKT_SIZE_ACT : RO; bitpos: [13:0]; default: 0;
 *  NA
 */
#define DSI_HOST_VID_PKT_SIZE_ACT    0x00003FFFU
#define DSI_HOST_VID_PKT_SIZE_ACT_M  (DSI_HOST_VID_PKT_SIZE_ACT_V << DSI_HOST_VID_PKT_SIZE_ACT_S)
#define DSI_HOST_VID_PKT_SIZE_ACT_V  0x00003FFFU
#define DSI_HOST_VID_PKT_SIZE_ACT_S  0

/** DSI_HOST_VID_NUM_CHUNKS_ACT_REG register
 *  NA
 */
#define DSI_HOST_VID_NUM_CHUNKS_ACT_REG (DR_REG_DSI_HOST_BASE + 0x140)
/** DSI_HOST_VID_NUM_CHUNKS_ACT : RO; bitpos: [12:0]; default: 0;
 *  NA
 */
#define DSI_HOST_VID_NUM_CHUNKS_ACT    0x00001FFFU
#define DSI_HOST_VID_NUM_CHUNKS_ACT_M  (DSI_HOST_VID_NUM_CHUNKS_ACT_V << DSI_HOST_VID_NUM_CHUNKS_ACT_S)
#define DSI_HOST_VID_NUM_CHUNKS_ACT_V  0x00001FFFU
#define DSI_HOST_VID_NUM_CHUNKS_ACT_S  0

/** DSI_HOST_VID_NULL_SIZE_ACT_REG register
 *  NA
 */
#define DSI_HOST_VID_NULL_SIZE_ACT_REG (DR_REG_DSI_HOST_BASE + 0x144)
/** DSI_HOST_VID_NULL_SIZE_ACT : RO; bitpos: [12:0]; default: 0;
 *  NA
 */
#define DSI_HOST_VID_NULL_SIZE_ACT    0x00001FFFU
#define DSI_HOST_VID_NULL_SIZE_ACT_M  (DSI_HOST_VID_NULL_SIZE_ACT_V << DSI_HOST_VID_NULL_SIZE_ACT_S)
#define DSI_HOST_VID_NULL_SIZE_ACT_V  0x00001FFFU
#define DSI_HOST_VID_NULL_SIZE_ACT_S  0

/** DSI_HOST_VID_HSA_TIME_ACT_REG register
 *  NA
 */
#define DSI_HOST_VID_HSA_TIME_ACT_REG (DR_REG_DSI_HOST_BASE + 0x148)
/** DSI_HOST_VID_HSA_TIME_ACT : RO; bitpos: [11:0]; default: 0;
 *  NA
 */
#define DSI_HOST_VID_HSA_TIME_ACT    0x00000FFFU
#define DSI_HOST_VID_HSA_TIME_ACT_M  (DSI_HOST_VID_HSA_TIME_ACT_V << DSI_HOST_VID_HSA_TIME_ACT_S)
#define DSI_HOST_VID_HSA_TIME_ACT_V  0x00000FFFU
#define DSI_HOST_VID_HSA_TIME_ACT_S  0

/** DSI_HOST_VID_HBP_TIME_ACT_REG register
 *  NA
 */
#define DSI_HOST_VID_HBP_TIME_ACT_REG (DR_REG_DSI_HOST_BASE + 0x14c)
/** DSI_HOST_VID_HBP_TIME_ACT : RO; bitpos: [11:0]; default: 0;
 *  NA
 */
#define DSI_HOST_VID_HBP_TIME_ACT    0x00000FFFU
#define DSI_HOST_VID_HBP_TIME_ACT_M  (DSI_HOST_VID_HBP_TIME_ACT_V << DSI_HOST_VID_HBP_TIME_ACT_S)
#define DSI_HOST_VID_HBP_TIME_ACT_V  0x00000FFFU
#define DSI_HOST_VID_HBP_TIME_ACT_S  0

/** DSI_HOST_VID_HLINE_TIME_ACT_REG register
 *  NA
 */
#define DSI_HOST_VID_HLINE_TIME_ACT_REG (DR_REG_DSI_HOST_BASE + 0x150)
/** DSI_HOST_VID_HLINE_TIME_ACT : RO; bitpos: [14:0]; default: 0;
 *  NA
 */
#define DSI_HOST_VID_HLINE_TIME_ACT    0x00007FFFU
#define DSI_HOST_VID_HLINE_TIME_ACT_M  (DSI_HOST_VID_HLINE_TIME_ACT_V << DSI_HOST_VID_HLINE_TIME_ACT_S)
#define DSI_HOST_VID_HLINE_TIME_ACT_V  0x00007FFFU
#define DSI_HOST_VID_HLINE_TIME_ACT_S  0

/** DSI_HOST_VID_VSA_LINES_ACT_REG register
 *  NA
 */
#define DSI_HOST_VID_VSA_LINES_ACT_REG (DR_REG_DSI_HOST_BASE + 0x154)
/** DSI_HOST_VSA_LINES_ACT : RO; bitpos: [9:0]; default: 0;
 *  NA
 */
#define DSI_HOST_VSA_LINES_ACT    0x000003FFU
#define DSI_HOST_VSA_LINES_ACT_M  (DSI_HOST_VSA_LINES_ACT_V << DSI_HOST_VSA_LINES_ACT_S)
#define DSI_HOST_VSA_LINES_ACT_V  0x000003FFU
#define DSI_HOST_VSA_LINES_ACT_S  0

/** DSI_HOST_VID_VBP_LINES_ACT_REG register
 *  NA
 */
#define DSI_HOST_VID_VBP_LINES_ACT_REG (DR_REG_DSI_HOST_BASE + 0x158)
/** DSI_HOST_VBP_LINES_ACT : RO; bitpos: [9:0]; default: 0;
 *  NA
 */
#define DSI_HOST_VBP_LINES_ACT    0x000003FFU
#define DSI_HOST_VBP_LINES_ACT_M  (DSI_HOST_VBP_LINES_ACT_V << DSI_HOST_VBP_LINES_ACT_S)
#define DSI_HOST_VBP_LINES_ACT_V  0x000003FFU
#define DSI_HOST_VBP_LINES_ACT_S  0

/** DSI_HOST_VID_VFP_LINES_ACT_REG register
 *  NA
 */
#define DSI_HOST_VID_VFP_LINES_ACT_REG (DR_REG_DSI_HOST_BASE + 0x15c)
/** DSI_HOST_VFP_LINES_ACT : RO; bitpos: [9:0]; default: 0;
 *  NA
 */
#define DSI_HOST_VFP_LINES_ACT    0x000003FFU
#define DSI_HOST_VFP_LINES_ACT_M  (DSI_HOST_VFP_LINES_ACT_V << DSI_HOST_VFP_LINES_ACT_S)
#define DSI_HOST_VFP_LINES_ACT_V  0x000003FFU
#define DSI_HOST_VFP_LINES_ACT_S  0

/** DSI_HOST_VID_VACTIVE_LINES_ACT_REG register
 *  NA
 */
#define DSI_HOST_VID_VACTIVE_LINES_ACT_REG (DR_REG_DSI_HOST_BASE + 0x160)
/** DSI_HOST_V_ACTIVE_LINES_ACT : RO; bitpos: [13:0]; default: 0;
 *  NA
 */
#define DSI_HOST_V_ACTIVE_LINES_ACT    0x00003FFFU
#define DSI_HOST_V_ACTIVE_LINES_ACT_M  (DSI_HOST_V_ACTIVE_LINES_ACT_V << DSI_HOST_V_ACTIVE_LINES_ACT_S)
#define DSI_HOST_V_ACTIVE_LINES_ACT_V  0x00003FFFU
#define DSI_HOST_V_ACTIVE_LINES_ACT_S  0

/** DSI_HOST_VID_PKT_STATUS_REG register
 *  NA
 */
#define DSI_HOST_VID_PKT_STATUS_REG (DR_REG_DSI_HOST_BASE + 0x168)
/** DSI_HOST_DPI_CMD_W_EMPTY : RO; bitpos: [0]; default: 1;
 *  NA
 */
#define DSI_HOST_DPI_CMD_W_EMPTY    (BIT(0))
#define DSI_HOST_DPI_CMD_W_EMPTY_M  (DSI_HOST_DPI_CMD_W_EMPTY_V << DSI_HOST_DPI_CMD_W_EMPTY_S)
#define DSI_HOST_DPI_CMD_W_EMPTY_V  0x00000001U
#define DSI_HOST_DPI_CMD_W_EMPTY_S  0
/** DSI_HOST_DPI_CMD_W_FULL : RO; bitpos: [1]; default: 0;
 *  NA
 */
#define DSI_HOST_DPI_CMD_W_FULL    (BIT(1))
#define DSI_HOST_DPI_CMD_W_FULL_M  (DSI_HOST_DPI_CMD_W_FULL_V << DSI_HOST_DPI_CMD_W_FULL_S)
#define DSI_HOST_DPI_CMD_W_FULL_V  0x00000001U
#define DSI_HOST_DPI_CMD_W_FULL_S  1
/** DSI_HOST_DPI_PLD_W_EMPTY : RO; bitpos: [2]; default: 1;
 *  NA
 */
#define DSI_HOST_DPI_PLD_W_EMPTY    (BIT(2))
#define DSI_HOST_DPI_PLD_W_EMPTY_M  (DSI_HOST_DPI_PLD_W_EMPTY_V << DSI_HOST_DPI_PLD_W_EMPTY_S)
#define DSI_HOST_DPI_PLD_W_EMPTY_V  0x00000001U
#define DSI_HOST_DPI_PLD_W_EMPTY_S  2
/** DSI_HOST_DPI_PLD_W_FULL : RO; bitpos: [3]; default: 0;
 *  NA
 */
#define DSI_HOST_DPI_PLD_W_FULL    (BIT(3))
#define DSI_HOST_DPI_PLD_W_FULL_M  (DSI_HOST_DPI_PLD_W_FULL_V << DSI_HOST_DPI_PLD_W_FULL_S)
#define DSI_HOST_DPI_PLD_W_FULL_V  0x00000001U
#define DSI_HOST_DPI_PLD_W_FULL_S  3
/** DSI_HOST_DPI_BUFF_PLD_EMPTY : RO; bitpos: [16]; default: 1;
 *  NA
 */
#define DSI_HOST_DPI_BUFF_PLD_EMPTY    (BIT(16))
#define DSI_HOST_DPI_BUFF_PLD_EMPTY_M  (DSI_HOST_DPI_BUFF_PLD_EMPTY_V << DSI_HOST_DPI_BUFF_PLD_EMPTY_S)
#define DSI_HOST_DPI_BUFF_PLD_EMPTY_V  0x00000001U
#define DSI_HOST_DPI_BUFF_PLD_EMPTY_S  16
/** DSI_HOST_DPI_BUFF_PLD_FULL : RO; bitpos: [17]; default: 0;
 *  NA
 */
#define DSI_HOST_DPI_BUFF_PLD_FULL    (BIT(17))
#define DSI_HOST_DPI_BUFF_PLD_FULL_M  (DSI_HOST_DPI_BUFF_PLD_FULL_V << DSI_HOST_DPI_BUFF_PLD_FULL_S)
#define DSI_HOST_DPI_BUFF_PLD_FULL_V  0x00000001U
#define DSI_HOST_DPI_BUFF_PLD_FULL_S  17

/** DSI_HOST_SDF_3D_ACT_REG register
 *  NA
 */
#define DSI_HOST_SDF_3D_ACT_REG (DR_REG_DSI_HOST_BASE + 0x190)
/** DSI_HOST_MODE_3D_ACT : RO; bitpos: [1:0]; default: 0;
 *  NA
 */
#define DSI_HOST_MODE_3D_ACT    0x00000003U
#define DSI_HOST_MODE_3D_ACT_M  (DSI_HOST_MODE_3D_ACT_V << DSI_HOST_MODE_3D_ACT_S)
#define DSI_HOST_MODE_3D_ACT_V  0x00000003U
#define DSI_HOST_MODE_3D_ACT_S  0
/** DSI_HOST_FORMAT_3D_ACT : RO; bitpos: [3:2]; default: 0;
 *  NA
 */
#define DSI_HOST_FORMAT_3D_ACT    0x00000003U
#define DSI_HOST_FORMAT_3D_ACT_M  (DSI_HOST_FORMAT_3D_ACT_V << DSI_HOST_FORMAT_3D_ACT_S)
#define DSI_HOST_FORMAT_3D_ACT_V  0x00000003U
#define DSI_HOST_FORMAT_3D_ACT_S  2
/** DSI_HOST_SECOND_VSYNC_ACT : RO; bitpos: [4]; default: 0;
 *  NA
 */
#define DSI_HOST_SECOND_VSYNC_ACT    (BIT(4))
#define DSI_HOST_SECOND_VSYNC_ACT_M  (DSI_HOST_SECOND_VSYNC_ACT_V << DSI_HOST_SECOND_VSYNC_ACT_S)
#define DSI_HOST_SECOND_VSYNC_ACT_V  0x00000001U
#define DSI_HOST_SECOND_VSYNC_ACT_S  4
/** DSI_HOST_RIGHT_FIRST_ACT : RO; bitpos: [5]; default: 0;
 *  NA
 */
#define DSI_HOST_RIGHT_FIRST_ACT    (BIT(5))
#define DSI_HOST_RIGHT_FIRST_ACT_M  (DSI_HOST_RIGHT_FIRST_ACT_V << DSI_HOST_RIGHT_FIRST_ACT_S)
#define DSI_HOST_RIGHT_FIRST_ACT_V  0x00000001U
#define DSI_HOST_RIGHT_FIRST_ACT_S  5
/** DSI_HOST_SEND_3D_CFG_ACT : RO; bitpos: [16]; default: 0;
 *  NA
 */
#define DSI_HOST_SEND_3D_CFG_ACT    (BIT(16))
#define DSI_HOST_SEND_3D_CFG_ACT_M  (DSI_HOST_SEND_3D_CFG_ACT_V << DSI_HOST_SEND_3D_CFG_ACT_S)
#define DSI_HOST_SEND_3D_CFG_ACT_V  0x00000001U
#define DSI_HOST_SEND_3D_CFG_ACT_S  16

#ifdef __cplusplus
}
#endif
