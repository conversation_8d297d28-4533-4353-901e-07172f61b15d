/**
 * SPDX-FileCopyrightText: 2023 Espressif Systems (Shanghai) CO LTD
 *
 *  SPDX-License-Identifier: Apache-2.0
 */
#pragma once

#include <stdint.h>
#include "soc/soc.h"
#ifdef __cplusplus
extern "C" {
#endif

/** INTERRUPT_CORE1_LP_RTC_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_LP_RTC_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0x0)
/** INTERRUPT_CORE1_LP_RTC_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_LP_RTC_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_LP_RTC_INT_MAP_M  (INTERRUPT_CORE1_LP_RTC_INT_MAP_V << INTERRUPT_CORE1_LP_RTC_INT_MAP_S)
#define INTERRUPT_CORE1_LP_RTC_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_LP_RTC_INT_MAP_S  0

/** INTERRUPT_CORE1_LP_WDT_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_LP_WDT_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0x4)
/** INTERRUPT_CORE1_LP_WDT_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_LP_WDT_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_LP_WDT_INT_MAP_M  (INTERRUPT_CORE1_LP_WDT_INT_MAP_V << INTERRUPT_CORE1_LP_WDT_INT_MAP_S)
#define INTERRUPT_CORE1_LP_WDT_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_LP_WDT_INT_MAP_S  0

/** INTERRUPT_CORE1_LP_TIMER_REG_0_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_LP_TIMER_REG_0_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0x8)
/** INTERRUPT_CORE1_LP_TIMER_REG_0_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_LP_TIMER_REG_0_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_LP_TIMER_REG_0_INT_MAP_M  (INTERRUPT_CORE1_LP_TIMER_REG_0_INT_MAP_V << INTERRUPT_CORE1_LP_TIMER_REG_0_INT_MAP_S)
#define INTERRUPT_CORE1_LP_TIMER_REG_0_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_LP_TIMER_REG_0_INT_MAP_S  0

/** INTERRUPT_CORE1_LP_TIMER_REG_1_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_LP_TIMER_REG_1_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0xc)
/** INTERRUPT_CORE1_LP_TIMER_REG_1_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_LP_TIMER_REG_1_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_LP_TIMER_REG_1_INT_MAP_M  (INTERRUPT_CORE1_LP_TIMER_REG_1_INT_MAP_V << INTERRUPT_CORE1_LP_TIMER_REG_1_INT_MAP_S)
#define INTERRUPT_CORE1_LP_TIMER_REG_1_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_LP_TIMER_REG_1_INT_MAP_S  0

/** INTERRUPT_CORE1_MB_HP_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_MB_HP_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0x10)
/** INTERRUPT_CORE1_MB_HP_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_MB_HP_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_MB_HP_INT_MAP_M  (INTERRUPT_CORE1_MB_HP_INT_MAP_V << INTERRUPT_CORE1_MB_HP_INT_MAP_S)
#define INTERRUPT_CORE1_MB_HP_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_MB_HP_INT_MAP_S  0

/** INTERRUPT_CORE1_MB_LP_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_MB_LP_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0x14)
/** INTERRUPT_CORE1_MB_LP_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_MB_LP_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_MB_LP_INT_MAP_M  (INTERRUPT_CORE1_MB_LP_INT_MAP_V << INTERRUPT_CORE1_MB_LP_INT_MAP_S)
#define INTERRUPT_CORE1_MB_LP_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_MB_LP_INT_MAP_S  0

/** INTERRUPT_CORE1_PMU_REG_0_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_PMU_REG_0_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0x18)
/** INTERRUPT_CORE1_PMU_REG_0_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_PMU_REG_0_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_PMU_REG_0_INT_MAP_M  (INTERRUPT_CORE1_PMU_REG_0_INT_MAP_V << INTERRUPT_CORE1_PMU_REG_0_INT_MAP_S)
#define INTERRUPT_CORE1_PMU_REG_0_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_PMU_REG_0_INT_MAP_S  0

/** INTERRUPT_CORE1_PMU_REG_1_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_PMU_REG_1_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0x1c)
/** INTERRUPT_CORE1_PMU_REG_1_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_PMU_REG_1_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_PMU_REG_1_INT_MAP_M  (INTERRUPT_CORE1_PMU_REG_1_INT_MAP_V << INTERRUPT_CORE1_PMU_REG_1_INT_MAP_S)
#define INTERRUPT_CORE1_PMU_REG_1_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_PMU_REG_1_INT_MAP_S  0

/** INTERRUPT_CORE1_LP_ANAPERI_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_LP_ANAPERI_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0x20)
/** INTERRUPT_CORE1_LP_ANAPERI_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_LP_ANAPERI_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_LP_ANAPERI_INT_MAP_M  (INTERRUPT_CORE1_LP_ANAPERI_INT_MAP_V << INTERRUPT_CORE1_LP_ANAPERI_INT_MAP_S)
#define INTERRUPT_CORE1_LP_ANAPERI_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_LP_ANAPERI_INT_MAP_S  0

/** INTERRUPT_CORE1_LP_ADC_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_LP_ADC_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0x24)
/** INTERRUPT_CORE1_LP_ADC_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_LP_ADC_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_LP_ADC_INT_MAP_M  (INTERRUPT_CORE1_LP_ADC_INT_MAP_V << INTERRUPT_CORE1_LP_ADC_INT_MAP_S)
#define INTERRUPT_CORE1_LP_ADC_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_LP_ADC_INT_MAP_S  0

/** INTERRUPT_CORE1_LP_GPIO_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_LP_GPIO_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0x28)
/** INTERRUPT_CORE1_LP_GPIO_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_LP_GPIO_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_LP_GPIO_INT_MAP_M  (INTERRUPT_CORE1_LP_GPIO_INT_MAP_V << INTERRUPT_CORE1_LP_GPIO_INT_MAP_S)
#define INTERRUPT_CORE1_LP_GPIO_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_LP_GPIO_INT_MAP_S  0

/** INTERRUPT_CORE1_LP_I2C_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_LP_I2C_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0x2c)
/** INTERRUPT_CORE1_LP_I2C_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_LP_I2C_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_LP_I2C_INT_MAP_M  (INTERRUPT_CORE1_LP_I2C_INT_MAP_V << INTERRUPT_CORE1_LP_I2C_INT_MAP_S)
#define INTERRUPT_CORE1_LP_I2C_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_LP_I2C_INT_MAP_S  0

/** INTERRUPT_CORE1_LP_I2S_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_LP_I2S_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0x30)
/** INTERRUPT_CORE1_LP_I2S_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_LP_I2S_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_LP_I2S_INT_MAP_M  (INTERRUPT_CORE1_LP_I2S_INT_MAP_V << INTERRUPT_CORE1_LP_I2S_INT_MAP_S)
#define INTERRUPT_CORE1_LP_I2S_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_LP_I2S_INT_MAP_S  0

/** INTERRUPT_CORE1_LP_SPI_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_LP_SPI_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0x34)
/** INTERRUPT_CORE1_LP_SPI_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_LP_SPI_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_LP_SPI_INT_MAP_M  (INTERRUPT_CORE1_LP_SPI_INT_MAP_V << INTERRUPT_CORE1_LP_SPI_INT_MAP_S)
#define INTERRUPT_CORE1_LP_SPI_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_LP_SPI_INT_MAP_S  0

/** INTERRUPT_CORE1_LP_TOUCH_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_LP_TOUCH_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0x38)
/** INTERRUPT_CORE1_LP_TOUCH_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_LP_TOUCH_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_LP_TOUCH_INT_MAP_M  (INTERRUPT_CORE1_LP_TOUCH_INT_MAP_V << INTERRUPT_CORE1_LP_TOUCH_INT_MAP_S)
#define INTERRUPT_CORE1_LP_TOUCH_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_LP_TOUCH_INT_MAP_S  0

/** INTERRUPT_CORE1_LP_TSENS_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_LP_TSENS_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0x3c)
/** INTERRUPT_CORE1_LP_TSENS_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_LP_TSENS_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_LP_TSENS_INT_MAP_M  (INTERRUPT_CORE1_LP_TSENS_INT_MAP_V << INTERRUPT_CORE1_LP_TSENS_INT_MAP_S)
#define INTERRUPT_CORE1_LP_TSENS_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_LP_TSENS_INT_MAP_S  0

/** INTERRUPT_CORE1_LP_UART_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_LP_UART_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0x40)
/** INTERRUPT_CORE1_LP_UART_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_LP_UART_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_LP_UART_INT_MAP_M  (INTERRUPT_CORE1_LP_UART_INT_MAP_V << INTERRUPT_CORE1_LP_UART_INT_MAP_S)
#define INTERRUPT_CORE1_LP_UART_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_LP_UART_INT_MAP_S  0

/** INTERRUPT_CORE1_LP_EFUSE_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_LP_EFUSE_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0x44)
/** INTERRUPT_CORE1_LP_EFUSE_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_LP_EFUSE_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_LP_EFUSE_INT_MAP_M  (INTERRUPT_CORE1_LP_EFUSE_INT_MAP_V << INTERRUPT_CORE1_LP_EFUSE_INT_MAP_S)
#define INTERRUPT_CORE1_LP_EFUSE_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_LP_EFUSE_INT_MAP_S  0

/** INTERRUPT_CORE1_LP_SW_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_LP_SW_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0x48)
/** INTERRUPT_CORE1_LP_SW_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_LP_SW_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_LP_SW_INT_MAP_M  (INTERRUPT_CORE1_LP_SW_INT_MAP_V << INTERRUPT_CORE1_LP_SW_INT_MAP_S)
#define INTERRUPT_CORE1_LP_SW_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_LP_SW_INT_MAP_S  0

/** INTERRUPT_CORE1_LP_SYSREG_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_LP_SYSREG_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0x4c)
/** INTERRUPT_CORE1_LP_SYSREG_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_LP_SYSREG_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_LP_SYSREG_INT_MAP_M  (INTERRUPT_CORE1_LP_SYSREG_INT_MAP_V << INTERRUPT_CORE1_LP_SYSREG_INT_MAP_S)
#define INTERRUPT_CORE1_LP_SYSREG_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_LP_SYSREG_INT_MAP_S  0

/** INTERRUPT_CORE1_LP_HUK_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_LP_HUK_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0x50)
/** INTERRUPT_CORE1_LP_HUK_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_LP_HUK_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_LP_HUK_INT_MAP_M  (INTERRUPT_CORE1_LP_HUK_INT_MAP_V << INTERRUPT_CORE1_LP_HUK_INT_MAP_S)
#define INTERRUPT_CORE1_LP_HUK_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_LP_HUK_INT_MAP_S  0

/** INTERRUPT_CORE1_SYS_ICM_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_SYS_ICM_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0x54)
/** INTERRUPT_CORE1_SYS_ICM_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_SYS_ICM_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_SYS_ICM_INT_MAP_M  (INTERRUPT_CORE1_SYS_ICM_INT_MAP_V << INTERRUPT_CORE1_SYS_ICM_INT_MAP_S)
#define INTERRUPT_CORE1_SYS_ICM_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_SYS_ICM_INT_MAP_S  0

/** INTERRUPT_CORE1_USB_DEVICE_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_USB_DEVICE_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0x58)
/** INTERRUPT_CORE1_USB_DEVICE_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_USB_DEVICE_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_USB_DEVICE_INT_MAP_M  (INTERRUPT_CORE1_USB_DEVICE_INT_MAP_V << INTERRUPT_CORE1_USB_DEVICE_INT_MAP_S)
#define INTERRUPT_CORE1_USB_DEVICE_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_USB_DEVICE_INT_MAP_S  0

/** INTERRUPT_CORE1_SDIO_HOST_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_SDIO_HOST_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0x5c)
/** INTERRUPT_CORE1_SDIO_HOST_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_SDIO_HOST_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_SDIO_HOST_INT_MAP_M  (INTERRUPT_CORE1_SDIO_HOST_INT_MAP_V << INTERRUPT_CORE1_SDIO_HOST_INT_MAP_S)
#define INTERRUPT_CORE1_SDIO_HOST_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_SDIO_HOST_INT_MAP_S  0

/** INTERRUPT_CORE1_GDMA_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_GDMA_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0x60)
/** INTERRUPT_CORE1_GDMA_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_GDMA_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_GDMA_INT_MAP_M  (INTERRUPT_CORE1_GDMA_INT_MAP_V << INTERRUPT_CORE1_GDMA_INT_MAP_S)
#define INTERRUPT_CORE1_GDMA_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_GDMA_INT_MAP_S  0

/** INTERRUPT_CORE1_SPI2_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_SPI2_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0x64)
/** INTERRUPT_CORE1_SPI2_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_SPI2_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_SPI2_INT_MAP_M  (INTERRUPT_CORE1_SPI2_INT_MAP_V << INTERRUPT_CORE1_SPI2_INT_MAP_S)
#define INTERRUPT_CORE1_SPI2_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_SPI2_INT_MAP_S  0

/** INTERRUPT_CORE1_SPI3_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_SPI3_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0x68)
/** INTERRUPT_CORE1_SPI3_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_SPI3_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_SPI3_INT_MAP_M  (INTERRUPT_CORE1_SPI3_INT_MAP_V << INTERRUPT_CORE1_SPI3_INT_MAP_S)
#define INTERRUPT_CORE1_SPI3_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_SPI3_INT_MAP_S  0

/** INTERRUPT_CORE1_I2S0_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_I2S0_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0x6c)
/** INTERRUPT_CORE1_I2S0_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_I2S0_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_I2S0_INT_MAP_M  (INTERRUPT_CORE1_I2S0_INT_MAP_V << INTERRUPT_CORE1_I2S0_INT_MAP_S)
#define INTERRUPT_CORE1_I2S0_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_I2S0_INT_MAP_S  0

/** INTERRUPT_CORE1_I2S1_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_I2S1_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0x70)
/** INTERRUPT_CORE1_I2S1_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_I2S1_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_I2S1_INT_MAP_M  (INTERRUPT_CORE1_I2S1_INT_MAP_V << INTERRUPT_CORE1_I2S1_INT_MAP_S)
#define INTERRUPT_CORE1_I2S1_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_I2S1_INT_MAP_S  0

/** INTERRUPT_CORE1_I2S2_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_I2S2_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0x74)
/** INTERRUPT_CORE1_I2S2_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_I2S2_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_I2S2_INT_MAP_M  (INTERRUPT_CORE1_I2S2_INT_MAP_V << INTERRUPT_CORE1_I2S2_INT_MAP_S)
#define INTERRUPT_CORE1_I2S2_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_I2S2_INT_MAP_S  0

/** INTERRUPT_CORE1_UHCI0_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_UHCI0_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0x78)
/** INTERRUPT_CORE1_UHCI0_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_UHCI0_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_UHCI0_INT_MAP_M  (INTERRUPT_CORE1_UHCI0_INT_MAP_V << INTERRUPT_CORE1_UHCI0_INT_MAP_S)
#define INTERRUPT_CORE1_UHCI0_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_UHCI0_INT_MAP_S  0

/** INTERRUPT_CORE1_UART0_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_UART0_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0x7c)
/** INTERRUPT_CORE1_UART0_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_UART0_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_UART0_INT_MAP_M  (INTERRUPT_CORE1_UART0_INT_MAP_V << INTERRUPT_CORE1_UART0_INT_MAP_S)
#define INTERRUPT_CORE1_UART0_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_UART0_INT_MAP_S  0

/** INTERRUPT_CORE1_UART1_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_UART1_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0x80)
/** INTERRUPT_CORE1_UART1_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_UART1_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_UART1_INT_MAP_M  (INTERRUPT_CORE1_UART1_INT_MAP_V << INTERRUPT_CORE1_UART1_INT_MAP_S)
#define INTERRUPT_CORE1_UART1_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_UART1_INT_MAP_S  0

/** INTERRUPT_CORE1_UART2_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_UART2_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0x84)
/** INTERRUPT_CORE1_UART2_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_UART2_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_UART2_INT_MAP_M  (INTERRUPT_CORE1_UART2_INT_MAP_V << INTERRUPT_CORE1_UART2_INT_MAP_S)
#define INTERRUPT_CORE1_UART2_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_UART2_INT_MAP_S  0

/** INTERRUPT_CORE1_UART3_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_UART3_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0x88)
/** INTERRUPT_CORE1_UART3_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_UART3_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_UART3_INT_MAP_M  (INTERRUPT_CORE1_UART3_INT_MAP_V << INTERRUPT_CORE1_UART3_INT_MAP_S)
#define INTERRUPT_CORE1_UART3_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_UART3_INT_MAP_S  0

/** INTERRUPT_CORE1_UART4_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_UART4_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0x8c)
/** INTERRUPT_CORE1_UART4_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_UART4_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_UART4_INT_MAP_M  (INTERRUPT_CORE1_UART4_INT_MAP_V << INTERRUPT_CORE1_UART4_INT_MAP_S)
#define INTERRUPT_CORE1_UART4_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_UART4_INT_MAP_S  0

/** INTERRUPT_CORE1_LCD_CAM_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_LCD_CAM_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0x90)
/** INTERRUPT_CORE1_LCD_CAM_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_LCD_CAM_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_LCD_CAM_INT_MAP_M  (INTERRUPT_CORE1_LCD_CAM_INT_MAP_V << INTERRUPT_CORE1_LCD_CAM_INT_MAP_S)
#define INTERRUPT_CORE1_LCD_CAM_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_LCD_CAM_INT_MAP_S  0

/** INTERRUPT_CORE1_ADC_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_ADC_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0x94)
/** INTERRUPT_CORE1_ADC_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_ADC_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_ADC_INT_MAP_M  (INTERRUPT_CORE1_ADC_INT_MAP_V << INTERRUPT_CORE1_ADC_INT_MAP_S)
#define INTERRUPT_CORE1_ADC_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_ADC_INT_MAP_S  0

/** INTERRUPT_CORE1_PWM0_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_PWM0_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0x98)
/** INTERRUPT_CORE1_PWM0_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_PWM0_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_PWM0_INT_MAP_M  (INTERRUPT_CORE1_PWM0_INT_MAP_V << INTERRUPT_CORE1_PWM0_INT_MAP_S)
#define INTERRUPT_CORE1_PWM0_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_PWM0_INT_MAP_S  0

/** INTERRUPT_CORE1_PWM1_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_PWM1_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0x9c)
/** INTERRUPT_CORE1_PWM1_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_PWM1_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_PWM1_INT_MAP_M  (INTERRUPT_CORE1_PWM1_INT_MAP_V << INTERRUPT_CORE1_PWM1_INT_MAP_S)
#define INTERRUPT_CORE1_PWM1_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_PWM1_INT_MAP_S  0

/** INTERRUPT_CORE1_CAN0_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_CAN0_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0xa0)
/** INTERRUPT_CORE1_CAN0_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_CAN0_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_CAN0_INT_MAP_M  (INTERRUPT_CORE1_CAN0_INT_MAP_V << INTERRUPT_CORE1_CAN0_INT_MAP_S)
#define INTERRUPT_CORE1_CAN0_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_CAN0_INT_MAP_S  0

/** INTERRUPT_CORE1_CAN1_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_CAN1_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0xa4)
/** INTERRUPT_CORE1_CAN1_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_CAN1_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_CAN1_INT_MAP_M  (INTERRUPT_CORE1_CAN1_INT_MAP_V << INTERRUPT_CORE1_CAN1_INT_MAP_S)
#define INTERRUPT_CORE1_CAN1_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_CAN1_INT_MAP_S  0

/** INTERRUPT_CORE1_CAN2_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_CAN2_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0xa8)
/** INTERRUPT_CORE1_CAN2_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_CAN2_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_CAN2_INT_MAP_M  (INTERRUPT_CORE1_CAN2_INT_MAP_V << INTERRUPT_CORE1_CAN2_INT_MAP_S)
#define INTERRUPT_CORE1_CAN2_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_CAN2_INT_MAP_S  0

/** INTERRUPT_CORE1_RMT_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_RMT_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0xac)
/** INTERRUPT_CORE1_RMT_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_RMT_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_RMT_INT_MAP_M  (INTERRUPT_CORE1_RMT_INT_MAP_V << INTERRUPT_CORE1_RMT_INT_MAP_S)
#define INTERRUPT_CORE1_RMT_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_RMT_INT_MAP_S  0

/** INTERRUPT_CORE1_I2C0_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_I2C0_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0xb0)
/** INTERRUPT_CORE1_I2C0_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_I2C0_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_I2C0_INT_MAP_M  (INTERRUPT_CORE1_I2C0_INT_MAP_V << INTERRUPT_CORE1_I2C0_INT_MAP_S)
#define INTERRUPT_CORE1_I2C0_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_I2C0_INT_MAP_S  0

/** INTERRUPT_CORE1_I2C1_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_I2C1_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0xb4)
/** INTERRUPT_CORE1_I2C1_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_I2C1_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_I2C1_INT_MAP_M  (INTERRUPT_CORE1_I2C1_INT_MAP_V << INTERRUPT_CORE1_I2C1_INT_MAP_S)
#define INTERRUPT_CORE1_I2C1_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_I2C1_INT_MAP_S  0

/** INTERRUPT_CORE1_TIMERGRP0_T0_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_TIMERGRP0_T0_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0xb8)
/** INTERRUPT_CORE1_TIMERGRP0_T0_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_TIMERGRP0_T0_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_TIMERGRP0_T0_INT_MAP_M  (INTERRUPT_CORE1_TIMERGRP0_T0_INT_MAP_V << INTERRUPT_CORE1_TIMERGRP0_T0_INT_MAP_S)
#define INTERRUPT_CORE1_TIMERGRP0_T0_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_TIMERGRP0_T0_INT_MAP_S  0

/** INTERRUPT_CORE1_TIMERGRP0_T1_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_TIMERGRP0_T1_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0xbc)
/** INTERRUPT_CORE1_TIMERGRP0_T1_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_TIMERGRP0_T1_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_TIMERGRP0_T1_INT_MAP_M  (INTERRUPT_CORE1_TIMERGRP0_T1_INT_MAP_V << INTERRUPT_CORE1_TIMERGRP0_T1_INT_MAP_S)
#define INTERRUPT_CORE1_TIMERGRP0_T1_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_TIMERGRP0_T1_INT_MAP_S  0

/** INTERRUPT_CORE1_TIMERGRP0_WDT_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_TIMERGRP0_WDT_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0xc0)
/** INTERRUPT_CORE1_TIMERGRP0_WDT_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_TIMERGRP0_WDT_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_TIMERGRP0_WDT_INT_MAP_M  (INTERRUPT_CORE1_TIMERGRP0_WDT_INT_MAP_V << INTERRUPT_CORE1_TIMERGRP0_WDT_INT_MAP_S)
#define INTERRUPT_CORE1_TIMERGRP0_WDT_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_TIMERGRP0_WDT_INT_MAP_S  0

/** INTERRUPT_CORE1_TIMERGRP1_T0_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_TIMERGRP1_T0_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0xc4)
/** INTERRUPT_CORE1_TIMERGRP1_T0_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_TIMERGRP1_T0_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_TIMERGRP1_T0_INT_MAP_M  (INTERRUPT_CORE1_TIMERGRP1_T0_INT_MAP_V << INTERRUPT_CORE1_TIMERGRP1_T0_INT_MAP_S)
#define INTERRUPT_CORE1_TIMERGRP1_T0_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_TIMERGRP1_T0_INT_MAP_S  0

/** INTERRUPT_CORE1_TIMERGRP1_T1_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_TIMERGRP1_T1_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0xc8)
/** INTERRUPT_CORE1_TIMERGRP1_T1_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_TIMERGRP1_T1_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_TIMERGRP1_T1_INT_MAP_M  (INTERRUPT_CORE1_TIMERGRP1_T1_INT_MAP_V << INTERRUPT_CORE1_TIMERGRP1_T1_INT_MAP_S)
#define INTERRUPT_CORE1_TIMERGRP1_T1_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_TIMERGRP1_T1_INT_MAP_S  0

/** INTERRUPT_CORE1_TIMERGRP1_WDT_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_TIMERGRP1_WDT_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0xcc)
/** INTERRUPT_CORE1_TIMERGRP1_WDT_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_TIMERGRP1_WDT_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_TIMERGRP1_WDT_INT_MAP_M  (INTERRUPT_CORE1_TIMERGRP1_WDT_INT_MAP_V << INTERRUPT_CORE1_TIMERGRP1_WDT_INT_MAP_S)
#define INTERRUPT_CORE1_TIMERGRP1_WDT_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_TIMERGRP1_WDT_INT_MAP_S  0

/** INTERRUPT_CORE1_LEDC_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_LEDC_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0xd0)
/** INTERRUPT_CORE1_LEDC_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_LEDC_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_LEDC_INT_MAP_M  (INTERRUPT_CORE1_LEDC_INT_MAP_V << INTERRUPT_CORE1_LEDC_INT_MAP_S)
#define INTERRUPT_CORE1_LEDC_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_LEDC_INT_MAP_S  0

/** INTERRUPT_CORE1_SYSTIMER_TARGET0_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_SYSTIMER_TARGET0_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0xd4)
/** INTERRUPT_CORE1_SYSTIMER_TARGET0_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_SYSTIMER_TARGET0_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_SYSTIMER_TARGET0_INT_MAP_M  (INTERRUPT_CORE1_SYSTIMER_TARGET0_INT_MAP_V << INTERRUPT_CORE1_SYSTIMER_TARGET0_INT_MAP_S)
#define INTERRUPT_CORE1_SYSTIMER_TARGET0_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_SYSTIMER_TARGET0_INT_MAP_S  0

/** INTERRUPT_CORE1_SYSTIMER_TARGET1_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_SYSTIMER_TARGET1_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0xd8)
/** INTERRUPT_CORE1_SYSTIMER_TARGET1_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_SYSTIMER_TARGET1_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_SYSTIMER_TARGET1_INT_MAP_M  (INTERRUPT_CORE1_SYSTIMER_TARGET1_INT_MAP_V << INTERRUPT_CORE1_SYSTIMER_TARGET1_INT_MAP_S)
#define INTERRUPT_CORE1_SYSTIMER_TARGET1_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_SYSTIMER_TARGET1_INT_MAP_S  0

/** INTERRUPT_CORE1_SYSTIMER_TARGET2_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_SYSTIMER_TARGET2_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0xdc)
/** INTERRUPT_CORE1_SYSTIMER_TARGET2_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_SYSTIMER_TARGET2_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_SYSTIMER_TARGET2_INT_MAP_M  (INTERRUPT_CORE1_SYSTIMER_TARGET2_INT_MAP_V << INTERRUPT_CORE1_SYSTIMER_TARGET2_INT_MAP_S)
#define INTERRUPT_CORE1_SYSTIMER_TARGET2_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_SYSTIMER_TARGET2_INT_MAP_S  0

/** INTERRUPT_CORE1_AHB_PDMA_IN_CH0_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_AHB_PDMA_IN_CH0_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0xe0)
/** INTERRUPT_CORE1_AHB_PDMA_IN_CH0_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_AHB_PDMA_IN_CH0_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_AHB_PDMA_IN_CH0_INT_MAP_M  (INTERRUPT_CORE1_AHB_PDMA_IN_CH0_INT_MAP_V << INTERRUPT_CORE1_AHB_PDMA_IN_CH0_INT_MAP_S)
#define INTERRUPT_CORE1_AHB_PDMA_IN_CH0_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_AHB_PDMA_IN_CH0_INT_MAP_S  0

/** INTERRUPT_CORE1_AHB_PDMA_IN_CH1_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_AHB_PDMA_IN_CH1_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0xe4)
/** INTERRUPT_CORE1_AHB_PDMA_IN_CH1_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_AHB_PDMA_IN_CH1_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_AHB_PDMA_IN_CH1_INT_MAP_M  (INTERRUPT_CORE1_AHB_PDMA_IN_CH1_INT_MAP_V << INTERRUPT_CORE1_AHB_PDMA_IN_CH1_INT_MAP_S)
#define INTERRUPT_CORE1_AHB_PDMA_IN_CH1_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_AHB_PDMA_IN_CH1_INT_MAP_S  0

/** INTERRUPT_CORE1_AHB_PDMA_IN_CH2_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_AHB_PDMA_IN_CH2_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0xe8)
/** INTERRUPT_CORE1_AHB_PDMA_IN_CH2_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_AHB_PDMA_IN_CH2_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_AHB_PDMA_IN_CH2_INT_MAP_M  (INTERRUPT_CORE1_AHB_PDMA_IN_CH2_INT_MAP_V << INTERRUPT_CORE1_AHB_PDMA_IN_CH2_INT_MAP_S)
#define INTERRUPT_CORE1_AHB_PDMA_IN_CH2_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_AHB_PDMA_IN_CH2_INT_MAP_S  0

/** INTERRUPT_CORE1_AHB_PDMA_OUT_CH0_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_AHB_PDMA_OUT_CH0_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0xec)
/** INTERRUPT_CORE1_AHB_PDMA_OUT_CH0_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_AHB_PDMA_OUT_CH0_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_AHB_PDMA_OUT_CH0_INT_MAP_M  (INTERRUPT_CORE1_AHB_PDMA_OUT_CH0_INT_MAP_V << INTERRUPT_CORE1_AHB_PDMA_OUT_CH0_INT_MAP_S)
#define INTERRUPT_CORE1_AHB_PDMA_OUT_CH0_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_AHB_PDMA_OUT_CH0_INT_MAP_S  0

/** INTERRUPT_CORE1_AHB_PDMA_OUT_CH1_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_AHB_PDMA_OUT_CH1_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0xf0)
/** INTERRUPT_CORE1_AHB_PDMA_OUT_CH1_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_AHB_PDMA_OUT_CH1_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_AHB_PDMA_OUT_CH1_INT_MAP_M  (INTERRUPT_CORE1_AHB_PDMA_OUT_CH1_INT_MAP_V << INTERRUPT_CORE1_AHB_PDMA_OUT_CH1_INT_MAP_S)
#define INTERRUPT_CORE1_AHB_PDMA_OUT_CH1_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_AHB_PDMA_OUT_CH1_INT_MAP_S  0

/** INTERRUPT_CORE1_AHB_PDMA_OUT_CH2_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_AHB_PDMA_OUT_CH2_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0xf4)
/** INTERRUPT_CORE1_AHB_PDMA_OUT_CH2_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_AHB_PDMA_OUT_CH2_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_AHB_PDMA_OUT_CH2_INT_MAP_M  (INTERRUPT_CORE1_AHB_PDMA_OUT_CH2_INT_MAP_V << INTERRUPT_CORE1_AHB_PDMA_OUT_CH2_INT_MAP_S)
#define INTERRUPT_CORE1_AHB_PDMA_OUT_CH2_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_AHB_PDMA_OUT_CH2_INT_MAP_S  0

/** INTERRUPT_CORE1_AXI_PDMA_IN_CH0_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_AXI_PDMA_IN_CH0_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0xf8)
/** INTERRUPT_CORE1_AXI_PDMA_IN_CH0_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_AXI_PDMA_IN_CH0_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_AXI_PDMA_IN_CH0_INT_MAP_M  (INTERRUPT_CORE1_AXI_PDMA_IN_CH0_INT_MAP_V << INTERRUPT_CORE1_AXI_PDMA_IN_CH0_INT_MAP_S)
#define INTERRUPT_CORE1_AXI_PDMA_IN_CH0_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_AXI_PDMA_IN_CH0_INT_MAP_S  0

/** INTERRUPT_CORE1_AXI_PDMA_IN_CH1_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_AXI_PDMA_IN_CH1_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0xfc)
/** INTERRUPT_CORE1_AXI_PDMA_IN_CH1_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_AXI_PDMA_IN_CH1_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_AXI_PDMA_IN_CH1_INT_MAP_M  (INTERRUPT_CORE1_AXI_PDMA_IN_CH1_INT_MAP_V << INTERRUPT_CORE1_AXI_PDMA_IN_CH1_INT_MAP_S)
#define INTERRUPT_CORE1_AXI_PDMA_IN_CH1_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_AXI_PDMA_IN_CH1_INT_MAP_S  0

/** INTERRUPT_CORE1_AXI_PDMA_IN_CH2_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_AXI_PDMA_IN_CH2_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0x100)
/** INTERRUPT_CORE1_AXI_PDMA_IN_CH2_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_AXI_PDMA_IN_CH2_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_AXI_PDMA_IN_CH2_INT_MAP_M  (INTERRUPT_CORE1_AXI_PDMA_IN_CH2_INT_MAP_V << INTERRUPT_CORE1_AXI_PDMA_IN_CH2_INT_MAP_S)
#define INTERRUPT_CORE1_AXI_PDMA_IN_CH2_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_AXI_PDMA_IN_CH2_INT_MAP_S  0

/** INTERRUPT_CORE1_AXI_PDMA_OUT_CH0_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_AXI_PDMA_OUT_CH0_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0x104)
/** INTERRUPT_CORE1_AXI_PDMA_OUT_CH0_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_AXI_PDMA_OUT_CH0_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_AXI_PDMA_OUT_CH0_INT_MAP_M  (INTERRUPT_CORE1_AXI_PDMA_OUT_CH0_INT_MAP_V << INTERRUPT_CORE1_AXI_PDMA_OUT_CH0_INT_MAP_S)
#define INTERRUPT_CORE1_AXI_PDMA_OUT_CH0_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_AXI_PDMA_OUT_CH0_INT_MAP_S  0

/** INTERRUPT_CORE1_AXI_PDMA_OUT_CH1_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_AXI_PDMA_OUT_CH1_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0x108)
/** INTERRUPT_CORE1_AXI_PDMA_OUT_CH1_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_AXI_PDMA_OUT_CH1_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_AXI_PDMA_OUT_CH1_INT_MAP_M  (INTERRUPT_CORE1_AXI_PDMA_OUT_CH1_INT_MAP_V << INTERRUPT_CORE1_AXI_PDMA_OUT_CH1_INT_MAP_S)
#define INTERRUPT_CORE1_AXI_PDMA_OUT_CH1_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_AXI_PDMA_OUT_CH1_INT_MAP_S  0

/** INTERRUPT_CORE1_AXI_PDMA_OUT_CH2_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_AXI_PDMA_OUT_CH2_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0x10c)
/** INTERRUPT_CORE1_AXI_PDMA_OUT_CH2_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_AXI_PDMA_OUT_CH2_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_AXI_PDMA_OUT_CH2_INT_MAP_M  (INTERRUPT_CORE1_AXI_PDMA_OUT_CH2_INT_MAP_V << INTERRUPT_CORE1_AXI_PDMA_OUT_CH2_INT_MAP_S)
#define INTERRUPT_CORE1_AXI_PDMA_OUT_CH2_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_AXI_PDMA_OUT_CH2_INT_MAP_S  0

/** INTERRUPT_CORE1_RSA_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_RSA_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0x110)
/** INTERRUPT_CORE1_RSA_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_RSA_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_RSA_INT_MAP_M  (INTERRUPT_CORE1_RSA_INT_MAP_V << INTERRUPT_CORE1_RSA_INT_MAP_S)
#define INTERRUPT_CORE1_RSA_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_RSA_INT_MAP_S  0

/** INTERRUPT_CORE1_AES_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_AES_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0x114)
/** INTERRUPT_CORE1_AES_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_AES_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_AES_INT_MAP_M  (INTERRUPT_CORE1_AES_INT_MAP_V << INTERRUPT_CORE1_AES_INT_MAP_S)
#define INTERRUPT_CORE1_AES_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_AES_INT_MAP_S  0

/** INTERRUPT_CORE1_SHA_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_SHA_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0x118)
/** INTERRUPT_CORE1_SHA_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_SHA_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_SHA_INT_MAP_M  (INTERRUPT_CORE1_SHA_INT_MAP_V << INTERRUPT_CORE1_SHA_INT_MAP_S)
#define INTERRUPT_CORE1_SHA_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_SHA_INT_MAP_S  0

/** INTERRUPT_CORE1_ECC_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_ECC_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0x11c)
/** INTERRUPT_CORE1_ECC_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_ECC_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_ECC_INT_MAP_M  (INTERRUPT_CORE1_ECC_INT_MAP_V << INTERRUPT_CORE1_ECC_INT_MAP_S)
#define INTERRUPT_CORE1_ECC_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_ECC_INT_MAP_S  0

/** INTERRUPT_CORE1_ECDSA_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_ECDSA_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0x120)
/** INTERRUPT_CORE1_ECDSA_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_ECDSA_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_ECDSA_INT_MAP_M  (INTERRUPT_CORE1_ECDSA_INT_MAP_V << INTERRUPT_CORE1_ECDSA_INT_MAP_S)
#define INTERRUPT_CORE1_ECDSA_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_ECDSA_INT_MAP_S  0

/** INTERRUPT_CORE1_KM_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_KM_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0x124)
/** INTERRUPT_CORE1_KM_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_KM_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_KM_INT_MAP_M  (INTERRUPT_CORE1_KM_INT_MAP_V << INTERRUPT_CORE1_KM_INT_MAP_S)
#define INTERRUPT_CORE1_KM_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_KM_INT_MAP_S  0

/** INTERRUPT_CORE1_GPIO_INT0_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_GPIO_INT0_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0x128)
/** INTERRUPT_CORE1_GPIO_INT0_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_GPIO_INT0_MAP    0x0000003FU
#define INTERRUPT_CORE1_GPIO_INT0_MAP_M  (INTERRUPT_CORE1_GPIO_INT0_MAP_V << INTERRUPT_CORE1_GPIO_INT0_MAP_S)
#define INTERRUPT_CORE1_GPIO_INT0_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_GPIO_INT0_MAP_S  0

/** INTERRUPT_CORE1_GPIO_INT1_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_GPIO_INT1_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0x12c)
/** INTERRUPT_CORE1_GPIO_INT1_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_GPIO_INT1_MAP    0x0000003FU
#define INTERRUPT_CORE1_GPIO_INT1_MAP_M  (INTERRUPT_CORE1_GPIO_INT1_MAP_V << INTERRUPT_CORE1_GPIO_INT1_MAP_S)
#define INTERRUPT_CORE1_GPIO_INT1_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_GPIO_INT1_MAP_S  0

/** INTERRUPT_CORE1_GPIO_INT2_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_GPIO_INT2_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0x130)
/** INTERRUPT_CORE1_GPIO_INT2_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_GPIO_INT2_MAP    0x0000003FU
#define INTERRUPT_CORE1_GPIO_INT2_MAP_M  (INTERRUPT_CORE1_GPIO_INT2_MAP_V << INTERRUPT_CORE1_GPIO_INT2_MAP_S)
#define INTERRUPT_CORE1_GPIO_INT2_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_GPIO_INT2_MAP_S  0

/** INTERRUPT_CORE1_GPIO_INT3_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_GPIO_INT3_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0x134)
/** INTERRUPT_CORE1_GPIO_INT3_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_GPIO_INT3_MAP    0x0000003FU
#define INTERRUPT_CORE1_GPIO_INT3_MAP_M  (INTERRUPT_CORE1_GPIO_INT3_MAP_V << INTERRUPT_CORE1_GPIO_INT3_MAP_S)
#define INTERRUPT_CORE1_GPIO_INT3_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_GPIO_INT3_MAP_S  0

/** INTERRUPT_CORE1_GPIO_PAD_COMP_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_GPIO_PAD_COMP_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0x138)
/** INTERRUPT_CORE1_GPIO_PAD_COMP_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_GPIO_PAD_COMP_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_GPIO_PAD_COMP_INT_MAP_M  (INTERRUPT_CORE1_GPIO_PAD_COMP_INT_MAP_V << INTERRUPT_CORE1_GPIO_PAD_COMP_INT_MAP_S)
#define INTERRUPT_CORE1_GPIO_PAD_COMP_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_GPIO_PAD_COMP_INT_MAP_S  0

/** INTERRUPT_CORE1_CPU_INT_FROM_CPU_0_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_CPU_INT_FROM_CPU_0_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0x13c)
/** INTERRUPT_CORE1_CPU_INT_FROM_CPU_0_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_CPU_INT_FROM_CPU_0_MAP    0x0000003FU
#define INTERRUPT_CORE1_CPU_INT_FROM_CPU_0_MAP_M  (INTERRUPT_CORE1_CPU_INT_FROM_CPU_0_MAP_V << INTERRUPT_CORE1_CPU_INT_FROM_CPU_0_MAP_S)
#define INTERRUPT_CORE1_CPU_INT_FROM_CPU_0_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_CPU_INT_FROM_CPU_0_MAP_S  0

/** INTERRUPT_CORE1_CPU_INT_FROM_CPU_1_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_CPU_INT_FROM_CPU_1_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0x140)
/** INTERRUPT_CORE1_CPU_INT_FROM_CPU_1_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_CPU_INT_FROM_CPU_1_MAP    0x0000003FU
#define INTERRUPT_CORE1_CPU_INT_FROM_CPU_1_MAP_M  (INTERRUPT_CORE1_CPU_INT_FROM_CPU_1_MAP_V << INTERRUPT_CORE1_CPU_INT_FROM_CPU_1_MAP_S)
#define INTERRUPT_CORE1_CPU_INT_FROM_CPU_1_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_CPU_INT_FROM_CPU_1_MAP_S  0

/** INTERRUPT_CORE1_CPU_INT_FROM_CPU_2_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_CPU_INT_FROM_CPU_2_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0x144)
/** INTERRUPT_CORE1_CPU_INT_FROM_CPU_2_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_CPU_INT_FROM_CPU_2_MAP    0x0000003FU
#define INTERRUPT_CORE1_CPU_INT_FROM_CPU_2_MAP_M  (INTERRUPT_CORE1_CPU_INT_FROM_CPU_2_MAP_V << INTERRUPT_CORE1_CPU_INT_FROM_CPU_2_MAP_S)
#define INTERRUPT_CORE1_CPU_INT_FROM_CPU_2_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_CPU_INT_FROM_CPU_2_MAP_S  0

/** INTERRUPT_CORE1_CPU_INT_FROM_CPU_3_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_CPU_INT_FROM_CPU_3_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0x148)
/** INTERRUPT_CORE1_CPU_INT_FROM_CPU_3_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_CPU_INT_FROM_CPU_3_MAP    0x0000003FU
#define INTERRUPT_CORE1_CPU_INT_FROM_CPU_3_MAP_M  (INTERRUPT_CORE1_CPU_INT_FROM_CPU_3_MAP_V << INTERRUPT_CORE1_CPU_INT_FROM_CPU_3_MAP_S)
#define INTERRUPT_CORE1_CPU_INT_FROM_CPU_3_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_CPU_INT_FROM_CPU_3_MAP_S  0

/** INTERRUPT_CORE1_CACHE_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_CACHE_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0x14c)
/** INTERRUPT_CORE1_CACHE_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_CACHE_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_CACHE_INT_MAP_M  (INTERRUPT_CORE1_CACHE_INT_MAP_V << INTERRUPT_CORE1_CACHE_INT_MAP_S)
#define INTERRUPT_CORE1_CACHE_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_CACHE_INT_MAP_S  0

/** INTERRUPT_CORE1_FLASH_MSPI_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_FLASH_MSPI_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0x150)
/** INTERRUPT_CORE1_FLASH_MSPI_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_FLASH_MSPI_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_FLASH_MSPI_INT_MAP_M  (INTERRUPT_CORE1_FLASH_MSPI_INT_MAP_V << INTERRUPT_CORE1_FLASH_MSPI_INT_MAP_S)
#define INTERRUPT_CORE1_FLASH_MSPI_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_FLASH_MSPI_INT_MAP_S  0

/** INTERRUPT_CORE1_CSI_BRIDGE_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_CSI_BRIDGE_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0x154)
/** INTERRUPT_CORE1_CSI_BRIDGE_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_CSI_BRIDGE_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_CSI_BRIDGE_INT_MAP_M  (INTERRUPT_CORE1_CSI_BRIDGE_INT_MAP_V << INTERRUPT_CORE1_CSI_BRIDGE_INT_MAP_S)
#define INTERRUPT_CORE1_CSI_BRIDGE_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_CSI_BRIDGE_INT_MAP_S  0

/** INTERRUPT_CORE1_DSI_BRIDGE_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_DSI_BRIDGE_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0x158)
/** INTERRUPT_CORE1_DSI_BRIDGE_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_DSI_BRIDGE_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_DSI_BRIDGE_INT_MAP_M  (INTERRUPT_CORE1_DSI_BRIDGE_INT_MAP_V << INTERRUPT_CORE1_DSI_BRIDGE_INT_MAP_S)
#define INTERRUPT_CORE1_DSI_BRIDGE_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_DSI_BRIDGE_INT_MAP_S  0

/** INTERRUPT_CORE1_CSI_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_CSI_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0x15c)
/** INTERRUPT_CORE1_CSI_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_CSI_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_CSI_INT_MAP_M  (INTERRUPT_CORE1_CSI_INT_MAP_V << INTERRUPT_CORE1_CSI_INT_MAP_S)
#define INTERRUPT_CORE1_CSI_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_CSI_INT_MAP_S  0

/** INTERRUPT_CORE1_DSI_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_DSI_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0x160)
/** INTERRUPT_CORE1_DSI_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_DSI_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_DSI_INT_MAP_M  (INTERRUPT_CORE1_DSI_INT_MAP_V << INTERRUPT_CORE1_DSI_INT_MAP_S)
#define INTERRUPT_CORE1_DSI_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_DSI_INT_MAP_S  0

/** INTERRUPT_CORE1_GMII_PHY_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_GMII_PHY_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0x164)
/** INTERRUPT_CORE1_GMII_PHY_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_GMII_PHY_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_GMII_PHY_INT_MAP_M  (INTERRUPT_CORE1_GMII_PHY_INT_MAP_V << INTERRUPT_CORE1_GMII_PHY_INT_MAP_S)
#define INTERRUPT_CORE1_GMII_PHY_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_GMII_PHY_INT_MAP_S  0

/** INTERRUPT_CORE1_LPI_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_LPI_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0x168)
/** INTERRUPT_CORE1_LPI_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_LPI_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_LPI_INT_MAP_M  (INTERRUPT_CORE1_LPI_INT_MAP_V << INTERRUPT_CORE1_LPI_INT_MAP_S)
#define INTERRUPT_CORE1_LPI_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_LPI_INT_MAP_S  0

/** INTERRUPT_CORE1_PMT_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_PMT_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0x16c)
/** INTERRUPT_CORE1_PMT_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_PMT_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_PMT_INT_MAP_M  (INTERRUPT_CORE1_PMT_INT_MAP_V << INTERRUPT_CORE1_PMT_INT_MAP_S)
#define INTERRUPT_CORE1_PMT_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_PMT_INT_MAP_S  0

/** INTERRUPT_CORE1_SBD_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_SBD_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0x170)
/** INTERRUPT_CORE1_SBD_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_SBD_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_SBD_INT_MAP_M  (INTERRUPT_CORE1_SBD_INT_MAP_V << INTERRUPT_CORE1_SBD_INT_MAP_S)
#define INTERRUPT_CORE1_SBD_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_SBD_INT_MAP_S  0

/** INTERRUPT_CORE1_USB_OTG_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_USB_OTG_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0x174)
/** INTERRUPT_CORE1_USB_OTG_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_USB_OTG_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_USB_OTG_INT_MAP_M  (INTERRUPT_CORE1_USB_OTG_INT_MAP_V << INTERRUPT_CORE1_USB_OTG_INT_MAP_S)
#define INTERRUPT_CORE1_USB_OTG_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_USB_OTG_INT_MAP_S  0

/** INTERRUPT_CORE1_USB_OTG_ENDP_MULTI_PROC_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_USB_OTG_ENDP_MULTI_PROC_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0x178)
/** INTERRUPT_CORE1_USB_OTG_ENDP_MULTI_PROC_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_USB_OTG_ENDP_MULTI_PROC_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_USB_OTG_ENDP_MULTI_PROC_INT_MAP_M  (INTERRUPT_CORE1_USB_OTG_ENDP_MULTI_PROC_INT_MAP_V << INTERRUPT_CORE1_USB_OTG_ENDP_MULTI_PROC_INT_MAP_S)
#define INTERRUPT_CORE1_USB_OTG_ENDP_MULTI_PROC_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_USB_OTG_ENDP_MULTI_PROC_INT_MAP_S  0

/** INTERRUPT_CORE1_JPEG_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_JPEG_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0x17c)
/** INTERRUPT_CORE1_JPEG_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_JPEG_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_JPEG_INT_MAP_M  (INTERRUPT_CORE1_JPEG_INT_MAP_V << INTERRUPT_CORE1_JPEG_INT_MAP_S)
#define INTERRUPT_CORE1_JPEG_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_JPEG_INT_MAP_S  0

/** INTERRUPT_CORE1_PPA_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_PPA_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0x180)
/** INTERRUPT_CORE1_PPA_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_PPA_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_PPA_INT_MAP_M  (INTERRUPT_CORE1_PPA_INT_MAP_V << INTERRUPT_CORE1_PPA_INT_MAP_S)
#define INTERRUPT_CORE1_PPA_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_PPA_INT_MAP_S  0

/** INTERRUPT_CORE1_CORE0_TRACE_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_CORE0_TRACE_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0x184)
/** INTERRUPT_CORE1_CORE0_TRACE_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_CORE0_TRACE_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_CORE0_TRACE_INT_MAP_M  (INTERRUPT_CORE1_CORE0_TRACE_INT_MAP_V << INTERRUPT_CORE1_CORE0_TRACE_INT_MAP_S)
#define INTERRUPT_CORE1_CORE0_TRACE_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_CORE0_TRACE_INT_MAP_S  0

/** INTERRUPT_CORE1_TRACE_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_TRACE_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0x188)
/** INTERRUPT_CORE1_CORE1_TRACE_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_CORE1_TRACE_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_CORE1_TRACE_INT_MAP_M  (INTERRUPT_CORE1_CORE1_TRACE_INT_MAP_V << INTERRUPT_CORE1_CORE1_TRACE_INT_MAP_S)
#define INTERRUPT_CORE1_CORE1_TRACE_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_CORE1_TRACE_INT_MAP_S  0

/** INTERRUPT_CORE1_HP_CORE_CTRL_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_HP_CORE_CTRL_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0x18c)
/** INTERRUPT_CORE1_HP_CORE_CTRL_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_HP_CORE_CTRL_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_HP_CORE_CTRL_INT_MAP_M  (INTERRUPT_CORE1_HP_CORE_CTRL_INT_MAP_V << INTERRUPT_CORE1_HP_CORE_CTRL_INT_MAP_S)
#define INTERRUPT_CORE1_HP_CORE_CTRL_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_HP_CORE_CTRL_INT_MAP_S  0

/** INTERRUPT_CORE1_ISP_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_ISP_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0x190)
/** INTERRUPT_CORE1_ISP_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_ISP_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_ISP_INT_MAP_M  (INTERRUPT_CORE1_ISP_INT_MAP_V << INTERRUPT_CORE1_ISP_INT_MAP_S)
#define INTERRUPT_CORE1_ISP_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_ISP_INT_MAP_S  0

/** INTERRUPT_CORE1_I3C_MST_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_I3C_MST_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0x194)
/** INTERRUPT_CORE1_I3C_MST_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_I3C_MST_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_I3C_MST_INT_MAP_M  (INTERRUPT_CORE1_I3C_MST_INT_MAP_V << INTERRUPT_CORE1_I3C_MST_INT_MAP_S)
#define INTERRUPT_CORE1_I3C_MST_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_I3C_MST_INT_MAP_S  0

/** INTERRUPT_CORE1_I3C_SLV_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_I3C_SLV_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0x198)
/** INTERRUPT_CORE1_I3C_SLV_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_I3C_SLV_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_I3C_SLV_INT_MAP_M  (INTERRUPT_CORE1_I3C_SLV_INT_MAP_V << INTERRUPT_CORE1_I3C_SLV_INT_MAP_S)
#define INTERRUPT_CORE1_I3C_SLV_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_I3C_SLV_INT_MAP_S  0

/** INTERRUPT_CORE1_USB_OTG11_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_USB_OTG11_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0x19c)
/** INTERRUPT_CORE1_USB_OTG11_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_USB_OTG11_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_USB_OTG11_INT_MAP_M  (INTERRUPT_CORE1_USB_OTG11_INT_MAP_V << INTERRUPT_CORE1_USB_OTG11_INT_MAP_S)
#define INTERRUPT_CORE1_USB_OTG11_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_USB_OTG11_INT_MAP_S  0

/** INTERRUPT_CORE1_DMA2D_IN_CH0_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_DMA2D_IN_CH0_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0x1a0)
/** INTERRUPT_CORE1_DMA2D_IN_CH0_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_DMA2D_IN_CH0_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_DMA2D_IN_CH0_INT_MAP_M  (INTERRUPT_CORE1_DMA2D_IN_CH0_INT_MAP_V << INTERRUPT_CORE1_DMA2D_IN_CH0_INT_MAP_S)
#define INTERRUPT_CORE1_DMA2D_IN_CH0_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_DMA2D_IN_CH0_INT_MAP_S  0

/** INTERRUPT_CORE1_DMA2D_IN_CH1_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_DMA2D_IN_CH1_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0x1a4)
/** INTERRUPT_CORE1_DMA2D_IN_CH1_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_DMA2D_IN_CH1_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_DMA2D_IN_CH1_INT_MAP_M  (INTERRUPT_CORE1_DMA2D_IN_CH1_INT_MAP_V << INTERRUPT_CORE1_DMA2D_IN_CH1_INT_MAP_S)
#define INTERRUPT_CORE1_DMA2D_IN_CH1_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_DMA2D_IN_CH1_INT_MAP_S  0

/** INTERRUPT_CORE1_DMA2D_OUT_CH0_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_DMA2D_OUT_CH0_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0x1a8)
/** INTERRUPT_CORE1_DMA2D_OUT_CH0_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_DMA2D_OUT_CH0_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_DMA2D_OUT_CH0_INT_MAP_M  (INTERRUPT_CORE1_DMA2D_OUT_CH0_INT_MAP_V << INTERRUPT_CORE1_DMA2D_OUT_CH0_INT_MAP_S)
#define INTERRUPT_CORE1_DMA2D_OUT_CH0_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_DMA2D_OUT_CH0_INT_MAP_S  0

/** INTERRUPT_CORE1_DMA2D_OUT_CH1_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_DMA2D_OUT_CH1_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0x1ac)
/** INTERRUPT_CORE1_DMA2D_OUT_CH1_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_DMA2D_OUT_CH1_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_DMA2D_OUT_CH1_INT_MAP_M  (INTERRUPT_CORE1_DMA2D_OUT_CH1_INT_MAP_V << INTERRUPT_CORE1_DMA2D_OUT_CH1_INT_MAP_S)
#define INTERRUPT_CORE1_DMA2D_OUT_CH1_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_DMA2D_OUT_CH1_INT_MAP_S  0

/** INTERRUPT_CORE1_DMA2D_OUT_CH2_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_DMA2D_OUT_CH2_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0x1b0)
/** INTERRUPT_CORE1_DMA2D_OUT_CH2_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_DMA2D_OUT_CH2_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_DMA2D_OUT_CH2_INT_MAP_M  (INTERRUPT_CORE1_DMA2D_OUT_CH2_INT_MAP_V << INTERRUPT_CORE1_DMA2D_OUT_CH2_INT_MAP_S)
#define INTERRUPT_CORE1_DMA2D_OUT_CH2_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_DMA2D_OUT_CH2_INT_MAP_S  0

/** INTERRUPT_CORE1_PSRAM_MSPI_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_PSRAM_MSPI_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0x1b4)
/** INTERRUPT_CORE1_PSRAM_MSPI_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_PSRAM_MSPI_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_PSRAM_MSPI_INT_MAP_M  (INTERRUPT_CORE1_PSRAM_MSPI_INT_MAP_V << INTERRUPT_CORE1_PSRAM_MSPI_INT_MAP_S)
#define INTERRUPT_CORE1_PSRAM_MSPI_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_PSRAM_MSPI_INT_MAP_S  0

/** INTERRUPT_CORE1_HP_SYSREG_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_HP_SYSREG_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0x1b8)
/** INTERRUPT_CORE1_HP_SYSREG_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_HP_SYSREG_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_HP_SYSREG_INT_MAP_M  (INTERRUPT_CORE1_HP_SYSREG_INT_MAP_V << INTERRUPT_CORE1_HP_SYSREG_INT_MAP_S)
#define INTERRUPT_CORE1_HP_SYSREG_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_HP_SYSREG_INT_MAP_S  0

/** INTERRUPT_CORE1_PCNT_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_PCNT_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0x1bc)
/** INTERRUPT_CORE1_PCNT_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_PCNT_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_PCNT_INT_MAP_M  (INTERRUPT_CORE1_PCNT_INT_MAP_V << INTERRUPT_CORE1_PCNT_INT_MAP_S)
#define INTERRUPT_CORE1_PCNT_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_PCNT_INT_MAP_S  0

/** INTERRUPT_CORE1_HP_PAU_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_HP_PAU_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0x1c0)
/** INTERRUPT_CORE1_HP_PAU_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_HP_PAU_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_HP_PAU_INT_MAP_M  (INTERRUPT_CORE1_HP_PAU_INT_MAP_V << INTERRUPT_CORE1_HP_PAU_INT_MAP_S)
#define INTERRUPT_CORE1_HP_PAU_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_HP_PAU_INT_MAP_S  0

/** INTERRUPT_CORE1_HP_PARLIO_RX_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_HP_PARLIO_RX_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0x1c4)
/** INTERRUPT_CORE1_HP_PARLIO_RX_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_HP_PARLIO_RX_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_HP_PARLIO_RX_INT_MAP_M  (INTERRUPT_CORE1_HP_PARLIO_RX_INT_MAP_V << INTERRUPT_CORE1_HP_PARLIO_RX_INT_MAP_S)
#define INTERRUPT_CORE1_HP_PARLIO_RX_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_HP_PARLIO_RX_INT_MAP_S  0

/** INTERRUPT_CORE1_HP_PARLIO_TX_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_HP_PARLIO_TX_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0x1c8)
/** INTERRUPT_CORE1_HP_PARLIO_TX_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_HP_PARLIO_TX_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_HP_PARLIO_TX_INT_MAP_M  (INTERRUPT_CORE1_HP_PARLIO_TX_INT_MAP_V << INTERRUPT_CORE1_HP_PARLIO_TX_INT_MAP_S)
#define INTERRUPT_CORE1_HP_PARLIO_TX_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_HP_PARLIO_TX_INT_MAP_S  0

/** INTERRUPT_CORE1_H264_DMA2D_OUT_CH0_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_H264_DMA2D_OUT_CH0_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0x1cc)
/** INTERRUPT_CORE1_H264_DMA2D_OUT_CH0_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_H264_DMA2D_OUT_CH0_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_H264_DMA2D_OUT_CH0_INT_MAP_M  (INTERRUPT_CORE1_H264_DMA2D_OUT_CH0_INT_MAP_V << INTERRUPT_CORE1_H264_DMA2D_OUT_CH0_INT_MAP_S)
#define INTERRUPT_CORE1_H264_DMA2D_OUT_CH0_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_H264_DMA2D_OUT_CH0_INT_MAP_S  0

/** INTERRUPT_CORE1_H264_DMA2D_OUT_CH1_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_H264_DMA2D_OUT_CH1_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0x1d0)
/** INTERRUPT_CORE1_H264_DMA2D_OUT_CH1_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_H264_DMA2D_OUT_CH1_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_H264_DMA2D_OUT_CH1_INT_MAP_M  (INTERRUPT_CORE1_H264_DMA2D_OUT_CH1_INT_MAP_V << INTERRUPT_CORE1_H264_DMA2D_OUT_CH1_INT_MAP_S)
#define INTERRUPT_CORE1_H264_DMA2D_OUT_CH1_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_H264_DMA2D_OUT_CH1_INT_MAP_S  0

/** INTERRUPT_CORE1_H264_DMA2D_OUT_CH2_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_H264_DMA2D_OUT_CH2_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0x1d4)
/** INTERRUPT_CORE1_H264_DMA2D_OUT_CH2_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_H264_DMA2D_OUT_CH2_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_H264_DMA2D_OUT_CH2_INT_MAP_M  (INTERRUPT_CORE1_H264_DMA2D_OUT_CH2_INT_MAP_V << INTERRUPT_CORE1_H264_DMA2D_OUT_CH2_INT_MAP_S)
#define INTERRUPT_CORE1_H264_DMA2D_OUT_CH2_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_H264_DMA2D_OUT_CH2_INT_MAP_S  0

/** INTERRUPT_CORE1_H264_DMA2D_OUT_CH3_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_H264_DMA2D_OUT_CH3_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0x1d8)
/** INTERRUPT_CORE1_H264_DMA2D_OUT_CH3_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_H264_DMA2D_OUT_CH3_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_H264_DMA2D_OUT_CH3_INT_MAP_M  (INTERRUPT_CORE1_H264_DMA2D_OUT_CH3_INT_MAP_V << INTERRUPT_CORE1_H264_DMA2D_OUT_CH3_INT_MAP_S)
#define INTERRUPT_CORE1_H264_DMA2D_OUT_CH3_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_H264_DMA2D_OUT_CH3_INT_MAP_S  0

/** INTERRUPT_CORE1_H264_DMA2D_OUT_CH4_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_H264_DMA2D_OUT_CH4_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0x1dc)
/** INTERRUPT_CORE1_H264_DMA2D_OUT_CH4_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_H264_DMA2D_OUT_CH4_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_H264_DMA2D_OUT_CH4_INT_MAP_M  (INTERRUPT_CORE1_H264_DMA2D_OUT_CH4_INT_MAP_V << INTERRUPT_CORE1_H264_DMA2D_OUT_CH4_INT_MAP_S)
#define INTERRUPT_CORE1_H264_DMA2D_OUT_CH4_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_H264_DMA2D_OUT_CH4_INT_MAP_S  0

/** INTERRUPT_CORE1_H264_DMA2D_IN_CH0_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_H264_DMA2D_IN_CH0_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0x1e0)
/** INTERRUPT_CORE1_H264_DMA2D_IN_CH0_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_H264_DMA2D_IN_CH0_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_H264_DMA2D_IN_CH0_INT_MAP_M  (INTERRUPT_CORE1_H264_DMA2D_IN_CH0_INT_MAP_V << INTERRUPT_CORE1_H264_DMA2D_IN_CH0_INT_MAP_S)
#define INTERRUPT_CORE1_H264_DMA2D_IN_CH0_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_H264_DMA2D_IN_CH0_INT_MAP_S  0

/** INTERRUPT_CORE1_H264_DMA2D_IN_CH1_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_H264_DMA2D_IN_CH1_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0x1e4)
/** INTERRUPT_CORE1_H264_DMA2D_IN_CH1_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_H264_DMA2D_IN_CH1_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_H264_DMA2D_IN_CH1_INT_MAP_M  (INTERRUPT_CORE1_H264_DMA2D_IN_CH1_INT_MAP_V << INTERRUPT_CORE1_H264_DMA2D_IN_CH1_INT_MAP_S)
#define INTERRUPT_CORE1_H264_DMA2D_IN_CH1_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_H264_DMA2D_IN_CH1_INT_MAP_S  0

/** INTERRUPT_CORE1_H264_DMA2D_IN_CH2_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_H264_DMA2D_IN_CH2_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0x1e8)
/** INTERRUPT_CORE1_H264_DMA2D_IN_CH2_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_H264_DMA2D_IN_CH2_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_H264_DMA2D_IN_CH2_INT_MAP_M  (INTERRUPT_CORE1_H264_DMA2D_IN_CH2_INT_MAP_V << INTERRUPT_CORE1_H264_DMA2D_IN_CH2_INT_MAP_S)
#define INTERRUPT_CORE1_H264_DMA2D_IN_CH2_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_H264_DMA2D_IN_CH2_INT_MAP_S  0

/** INTERRUPT_CORE1_H264_DMA2D_IN_CH3_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_H264_DMA2D_IN_CH3_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0x1ec)
/** INTERRUPT_CORE1_H264_DMA2D_IN_CH3_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_H264_DMA2D_IN_CH3_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_H264_DMA2D_IN_CH3_INT_MAP_M  (INTERRUPT_CORE1_H264_DMA2D_IN_CH3_INT_MAP_V << INTERRUPT_CORE1_H264_DMA2D_IN_CH3_INT_MAP_S)
#define INTERRUPT_CORE1_H264_DMA2D_IN_CH3_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_H264_DMA2D_IN_CH3_INT_MAP_S  0

/** INTERRUPT_CORE1_H264_DMA2D_IN_CH4_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_H264_DMA2D_IN_CH4_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0x1f0)
/** INTERRUPT_CORE1_H264_DMA2D_IN_CH4_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_H264_DMA2D_IN_CH4_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_H264_DMA2D_IN_CH4_INT_MAP_M  (INTERRUPT_CORE1_H264_DMA2D_IN_CH4_INT_MAP_V << INTERRUPT_CORE1_H264_DMA2D_IN_CH4_INT_MAP_S)
#define INTERRUPT_CORE1_H264_DMA2D_IN_CH4_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_H264_DMA2D_IN_CH4_INT_MAP_S  0

/** INTERRUPT_CORE1_H264_DMA2D_IN_CH5_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_H264_DMA2D_IN_CH5_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0x1f4)
/** INTERRUPT_CORE1_H264_DMA2D_IN_CH5_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_H264_DMA2D_IN_CH5_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_H264_DMA2D_IN_CH5_INT_MAP_M  (INTERRUPT_CORE1_H264_DMA2D_IN_CH5_INT_MAP_V << INTERRUPT_CORE1_H264_DMA2D_IN_CH5_INT_MAP_S)
#define INTERRUPT_CORE1_H264_DMA2D_IN_CH5_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_H264_DMA2D_IN_CH5_INT_MAP_S  0

/** INTERRUPT_CORE1_H264_REG_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_H264_REG_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0x1f8)
/** INTERRUPT_CORE1_H264_REG_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_H264_REG_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_H264_REG_INT_MAP_M  (INTERRUPT_CORE1_H264_REG_INT_MAP_V << INTERRUPT_CORE1_H264_REG_INT_MAP_S)
#define INTERRUPT_CORE1_H264_REG_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_H264_REG_INT_MAP_S  0

/** INTERRUPT_CORE1_ASSIST_DEBUG_INT_MAP_REG register
 *  NA
 */
#define INTERRUPT_CORE1_ASSIST_DEBUG_INT_MAP_REG (DR_REG_INTERRUPT_CORE1_BASE + 0x1fc)
/** INTERRUPT_CORE1_ASSIST_DEBUG_INT_MAP : R/W; bitpos: [5:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_ASSIST_DEBUG_INT_MAP    0x0000003FU
#define INTERRUPT_CORE1_ASSIST_DEBUG_INT_MAP_M  (INTERRUPT_CORE1_ASSIST_DEBUG_INT_MAP_V << INTERRUPT_CORE1_ASSIST_DEBUG_INT_MAP_S)
#define INTERRUPT_CORE1_ASSIST_DEBUG_INT_MAP_V  0x0000003FU
#define INTERRUPT_CORE1_ASSIST_DEBUG_INT_MAP_S  0

/** INTERRUPT_CORE1_INTR_STATUS_REG_0_REG register
 *  NA
 */
#define INTERRUPT_CORE1_INTR_STATUS_REG_0_REG (DR_REG_INTERRUPT_CORE1_BASE + 0x200)
/** INTERRUPT_CORE1_INTR_STATUS_0 : RO; bitpos: [31:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_INTR_STATUS_0    0xFFFFFFFFU
#define INTERRUPT_CORE1_INTR_STATUS_0_M  (INTERRUPT_CORE1_INTR_STATUS_0_V << INTERRUPT_CORE1_INTR_STATUS_0_S)
#define INTERRUPT_CORE1_INTR_STATUS_0_V  0xFFFFFFFFU
#define INTERRUPT_CORE1_INTR_STATUS_0_S  0

/** INTERRUPT_CORE1_INTR_STATUS_REG_1_REG register
 *  NA
 */
#define INTERRUPT_CORE1_INTR_STATUS_REG_1_REG (DR_REG_INTERRUPT_CORE1_BASE + 0x204)
/** INTERRUPT_CORE1_INTR_STATUS_1 : RO; bitpos: [31:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_INTR_STATUS_1    0xFFFFFFFFU
#define INTERRUPT_CORE1_INTR_STATUS_1_M  (INTERRUPT_CORE1_INTR_STATUS_1_V << INTERRUPT_CORE1_INTR_STATUS_1_S)
#define INTERRUPT_CORE1_INTR_STATUS_1_V  0xFFFFFFFFU
#define INTERRUPT_CORE1_INTR_STATUS_1_S  0

/** INTERRUPT_CORE1_INTR_STATUS_REG_2_REG register
 *  NA
 */
#define INTERRUPT_CORE1_INTR_STATUS_REG_2_REG (DR_REG_INTERRUPT_CORE1_BASE + 0x208)
/** INTERRUPT_CORE1_INTR_STATUS_2 : RO; bitpos: [31:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_INTR_STATUS_2    0xFFFFFFFFU
#define INTERRUPT_CORE1_INTR_STATUS_2_M  (INTERRUPT_CORE1_INTR_STATUS_2_V << INTERRUPT_CORE1_INTR_STATUS_2_S)
#define INTERRUPT_CORE1_INTR_STATUS_2_V  0xFFFFFFFFU
#define INTERRUPT_CORE1_INTR_STATUS_2_S  0

/** INTERRUPT_CORE1_INTR_STATUS_REG_3_REG register
 *  NA
 */
#define INTERRUPT_CORE1_INTR_STATUS_REG_3_REG (DR_REG_INTERRUPT_CORE1_BASE + 0x20c)
/** INTERRUPT_CORE1_INTR_STATUS_3 : RO; bitpos: [31:0]; default: 0;
 *  NA
 */
#define INTERRUPT_CORE1_INTR_STATUS_3    0xFFFFFFFFU
#define INTERRUPT_CORE1_INTR_STATUS_3_M  (INTERRUPT_CORE1_INTR_STATUS_3_V << INTERRUPT_CORE1_INTR_STATUS_3_S)
#define INTERRUPT_CORE1_INTR_STATUS_3_V  0xFFFFFFFFU
#define INTERRUPT_CORE1_INTR_STATUS_3_S  0

/** INTERRUPT_CORE1_CLOCK_GATE_REG register
 *  NA
 */
#define INTERRUPT_CORE1_CLOCK_GATE_REG (DR_REG_INTERRUPT_CORE1_BASE + 0x210)
/** INTERRUPT_CORE1_REG_CLK_EN : R/W; bitpos: [0]; default: 1;
 *  NA
 */
#define INTERRUPT_CORE1_REG_CLK_EN    (BIT(0))
#define INTERRUPT_CORE1_REG_CLK_EN_M  (INTERRUPT_CORE1_REG_CLK_EN_V << INTERRUPT_CORE1_REG_CLK_EN_S)
#define INTERRUPT_CORE1_REG_CLK_EN_V  0x00000001U
#define INTERRUPT_CORE1_REG_CLK_EN_S  0

/** INTERRUPT_CORE1_INTERRUPT_REG_DATE_REG register
 *  NA
 */
#define INTERRUPT_CORE1_INTERRUPT_REG_DATE_REG (DR_REG_INTERRUPT_CORE1_BASE + 0x3fc)
/** INTERRUPT_CORE1_INTERRUPT_REG_DATE : R/W; bitpos: [27:0]; default: 33566752;
 *  NA
 */
#define INTERRUPT_CORE1_INTERRUPT_REG_DATE    0x0FFFFFFFU
#define INTERRUPT_CORE1_INTERRUPT_REG_DATE_M  (INTERRUPT_CORE1_INTERRUPT_REG_DATE_V << INTERRUPT_CORE1_INTERRUPT_REG_DATE_S)
#define INTERRUPT_CORE1_INTERRUPT_REG_DATE_V  0x0FFFFFFFU
#define INTERRUPT_CORE1_INTERRUPT_REG_DATE_S  0

#ifdef __cplusplus
}
#endif
