/**
 * SPDX-FileCopyrightText: 2023 Espressif Systems (Shanghai) CO LTD
 *
 *  SPDX-License-Identifier: Apache-2.0
 */
#pragma once

#include <stdint.h>
#include "soc/soc.h"
#ifdef __cplusplus
extern "C" {
#endif

/** PMU_HP_ACTIVE_DIG_POWER_REG register
 *  need_des
 */
#define PMU_HP_ACTIVE_DIG_POWER_REG (DR_REG_PMU_BASE + 0x0)
/** PMU_HP_ACTIVE_DCDC_SWITCH_PD_EN : R/W; bitpos: [21]; default: 0;
 *  need_des
 */
#define PMU_HP_ACTIVE_DCDC_SWITCH_PD_EN    (BIT(21))
#define PMU_HP_ACTIVE_DCDC_SWITCH_PD_EN_M  (PMU_HP_ACTIVE_DCDC_SWITCH_PD_EN_V << PMU_HP_ACTIVE_DCDC_SWITCH_PD_EN_S)
#define PMU_HP_ACTIVE_DCDC_SWITCH_PD_EN_V  0x00000001U
#define PMU_HP_ACTIVE_DCDC_SWITCH_PD_EN_S  21
/** PMU_HP_ACTIVE_HP_MEM_DSLP : R/W; bitpos: [22]; default: 0;
 *  need_des
 */
#define PMU_HP_ACTIVE_HP_MEM_DSLP    (BIT(22))
#define PMU_HP_ACTIVE_HP_MEM_DSLP_M  (PMU_HP_ACTIVE_HP_MEM_DSLP_V << PMU_HP_ACTIVE_HP_MEM_DSLP_S)
#define PMU_HP_ACTIVE_HP_MEM_DSLP_V  0x00000001U
#define PMU_HP_ACTIVE_HP_MEM_DSLP_S  22
/** PMU_HP_ACTIVE_PD_HP_MEM_PD_EN : R/W; bitpos: [23]; default: 0;
 *  need_des
 */
#define PMU_HP_ACTIVE_PD_HP_MEM_PD_EN    (BIT(23))
#define PMU_HP_ACTIVE_PD_HP_MEM_PD_EN_M  (PMU_HP_ACTIVE_PD_HP_MEM_PD_EN_V << PMU_HP_ACTIVE_PD_HP_MEM_PD_EN_S)
#define PMU_HP_ACTIVE_PD_HP_MEM_PD_EN_V  0x00000001U
#define PMU_HP_ACTIVE_PD_HP_MEM_PD_EN_S  23
/** PMU_HP_ACTIVE_PD_CNNT_PD_EN : R/W; bitpos: [30]; default: 0;
 *  need_des
 */
#define PMU_HP_ACTIVE_PD_CNNT_PD_EN    (BIT(30))
#define PMU_HP_ACTIVE_PD_CNNT_PD_EN_M  (PMU_HP_ACTIVE_PD_CNNT_PD_EN_V << PMU_HP_ACTIVE_PD_CNNT_PD_EN_S)
#define PMU_HP_ACTIVE_PD_CNNT_PD_EN_V  0x00000001U
#define PMU_HP_ACTIVE_PD_CNNT_PD_EN_S  30
/** PMU_HP_ACTIVE_PD_TOP_PD_EN : R/W; bitpos: [31]; default: 0;
 *  need_des
 */
#define PMU_HP_ACTIVE_PD_TOP_PD_EN    (BIT(31))
#define PMU_HP_ACTIVE_PD_TOP_PD_EN_M  (PMU_HP_ACTIVE_PD_TOP_PD_EN_V << PMU_HP_ACTIVE_PD_TOP_PD_EN_S)
#define PMU_HP_ACTIVE_PD_TOP_PD_EN_V  0x00000001U
#define PMU_HP_ACTIVE_PD_TOP_PD_EN_S  31

/** PMU_HP_ACTIVE_ICG_HP_FUNC_REG register
 *  need_des
 */
#define PMU_HP_ACTIVE_ICG_HP_FUNC_REG (DR_REG_PMU_BASE + 0x4)
/** PMU_HP_ACTIVE_DIG_ICG_FUNC_EN : R/W; bitpos: [31:0]; default: 4294967295;
 *  need_des
 */
#define PMU_HP_ACTIVE_DIG_ICG_FUNC_EN    0xFFFFFFFFU
#define PMU_HP_ACTIVE_DIG_ICG_FUNC_EN_M  (PMU_HP_ACTIVE_DIG_ICG_FUNC_EN_V << PMU_HP_ACTIVE_DIG_ICG_FUNC_EN_S)
#define PMU_HP_ACTIVE_DIG_ICG_FUNC_EN_V  0xFFFFFFFFU
#define PMU_HP_ACTIVE_DIG_ICG_FUNC_EN_S  0

/** PMU_HP_ACTIVE_ICG_HP_APB_REG register
 *  need_des
 */
#define PMU_HP_ACTIVE_ICG_HP_APB_REG (DR_REG_PMU_BASE + 0x8)
/** PMU_HP_ACTIVE_DIG_ICG_APB_EN : R/W; bitpos: [31:0]; default: 4294967295;
 *  need_des
 */
#define PMU_HP_ACTIVE_DIG_ICG_APB_EN    0xFFFFFFFFU
#define PMU_HP_ACTIVE_DIG_ICG_APB_EN_M  (PMU_HP_ACTIVE_DIG_ICG_APB_EN_V << PMU_HP_ACTIVE_DIG_ICG_APB_EN_S)
#define PMU_HP_ACTIVE_DIG_ICG_APB_EN_V  0xFFFFFFFFU
#define PMU_HP_ACTIVE_DIG_ICG_APB_EN_S  0

/** PMU_HP_ACTIVE_ICG_MODEM_REG register
 *  need_des
 */
#define PMU_HP_ACTIVE_ICG_MODEM_REG (DR_REG_PMU_BASE + 0xc)
/** PMU_HP_ACTIVE_DIG_ICG_MODEM_CODE : R/W; bitpos: [31:30]; default: 0;
 *  need_des
 */
#define PMU_HP_ACTIVE_DIG_ICG_MODEM_CODE    0x00000003U
#define PMU_HP_ACTIVE_DIG_ICG_MODEM_CODE_M  (PMU_HP_ACTIVE_DIG_ICG_MODEM_CODE_V << PMU_HP_ACTIVE_DIG_ICG_MODEM_CODE_S)
#define PMU_HP_ACTIVE_DIG_ICG_MODEM_CODE_V  0x00000003U
#define PMU_HP_ACTIVE_DIG_ICG_MODEM_CODE_S  30

/** PMU_HP_ACTIVE_HP_SYS_CNTL_REG register
 *  need_des
 */
#define PMU_HP_ACTIVE_HP_SYS_CNTL_REG (DR_REG_PMU_BASE + 0x10)
/** PMU_HP_ACTIVE_HP_POWER_DET_BYPASS : R/W; bitpos: [23]; default: 0;
 *  need_des
 */
#define PMU_HP_ACTIVE_HP_POWER_DET_BYPASS    (BIT(23))
#define PMU_HP_ACTIVE_HP_POWER_DET_BYPASS_M  (PMU_HP_ACTIVE_HP_POWER_DET_BYPASS_V << PMU_HP_ACTIVE_HP_POWER_DET_BYPASS_S)
#define PMU_HP_ACTIVE_HP_POWER_DET_BYPASS_V  0x00000001U
#define PMU_HP_ACTIVE_HP_POWER_DET_BYPASS_S  23
/** PMU_HP_ACTIVE_UART_WAKEUP_EN : R/W; bitpos: [24]; default: 0;
 *  need_des
 */
#define PMU_HP_ACTIVE_UART_WAKEUP_EN    (BIT(24))
#define PMU_HP_ACTIVE_UART_WAKEUP_EN_M  (PMU_HP_ACTIVE_UART_WAKEUP_EN_V << PMU_HP_ACTIVE_UART_WAKEUP_EN_S)
#define PMU_HP_ACTIVE_UART_WAKEUP_EN_V  0x00000001U
#define PMU_HP_ACTIVE_UART_WAKEUP_EN_S  24
/** PMU_HP_ACTIVE_LP_PAD_HOLD_ALL : R/W; bitpos: [25]; default: 0;
 *  need_des
 */
#define PMU_HP_ACTIVE_LP_PAD_HOLD_ALL    (BIT(25))
#define PMU_HP_ACTIVE_LP_PAD_HOLD_ALL_M  (PMU_HP_ACTIVE_LP_PAD_HOLD_ALL_V << PMU_HP_ACTIVE_LP_PAD_HOLD_ALL_S)
#define PMU_HP_ACTIVE_LP_PAD_HOLD_ALL_V  0x00000001U
#define PMU_HP_ACTIVE_LP_PAD_HOLD_ALL_S  25
/** PMU_HP_ACTIVE_HP_PAD_HOLD_ALL : R/W; bitpos: [26]; default: 0;
 *  need_des
 */
#define PMU_HP_ACTIVE_HP_PAD_HOLD_ALL    (BIT(26))
#define PMU_HP_ACTIVE_HP_PAD_HOLD_ALL_M  (PMU_HP_ACTIVE_HP_PAD_HOLD_ALL_V << PMU_HP_ACTIVE_HP_PAD_HOLD_ALL_S)
#define PMU_HP_ACTIVE_HP_PAD_HOLD_ALL_V  0x00000001U
#define PMU_HP_ACTIVE_HP_PAD_HOLD_ALL_S  26
/** PMU_HP_ACTIVE_DIG_PAD_SLP_SEL : R/W; bitpos: [27]; default: 0;
 *  need_des
 */
#define PMU_HP_ACTIVE_DIG_PAD_SLP_SEL    (BIT(27))
#define PMU_HP_ACTIVE_DIG_PAD_SLP_SEL_M  (PMU_HP_ACTIVE_DIG_PAD_SLP_SEL_V << PMU_HP_ACTIVE_DIG_PAD_SLP_SEL_S)
#define PMU_HP_ACTIVE_DIG_PAD_SLP_SEL_V  0x00000001U
#define PMU_HP_ACTIVE_DIG_PAD_SLP_SEL_S  27
/** PMU_HP_ACTIVE_DIG_PAUSE_WDT : R/W; bitpos: [28]; default: 0;
 *  need_des
 */
#define PMU_HP_ACTIVE_DIG_PAUSE_WDT    (BIT(28))
#define PMU_HP_ACTIVE_DIG_PAUSE_WDT_M  (PMU_HP_ACTIVE_DIG_PAUSE_WDT_V << PMU_HP_ACTIVE_DIG_PAUSE_WDT_S)
#define PMU_HP_ACTIVE_DIG_PAUSE_WDT_V  0x00000001U
#define PMU_HP_ACTIVE_DIG_PAUSE_WDT_S  28
/** PMU_HP_ACTIVE_DIG_CPU_STALL : R/W; bitpos: [29]; default: 0;
 *  need_des
 */
#define PMU_HP_ACTIVE_DIG_CPU_STALL    (BIT(29))
#define PMU_HP_ACTIVE_DIG_CPU_STALL_M  (PMU_HP_ACTIVE_DIG_CPU_STALL_V << PMU_HP_ACTIVE_DIG_CPU_STALL_S)
#define PMU_HP_ACTIVE_DIG_CPU_STALL_V  0x00000001U
#define PMU_HP_ACTIVE_DIG_CPU_STALL_S  29

/** PMU_HP_ACTIVE_HP_CK_POWER_REG register
 *  need_des
 */
#define PMU_HP_ACTIVE_HP_CK_POWER_REG (DR_REG_PMU_BASE + 0x14)
/** PMU_HP_ACTIVE_I2C_ISO_EN : R/W; bitpos: [21]; default: 0;
 *  need_des
 */
#define PMU_HP_ACTIVE_I2C_ISO_EN    (BIT(21))
#define PMU_HP_ACTIVE_I2C_ISO_EN_M  (PMU_HP_ACTIVE_I2C_ISO_EN_V << PMU_HP_ACTIVE_I2C_ISO_EN_S)
#define PMU_HP_ACTIVE_I2C_ISO_EN_V  0x00000001U
#define PMU_HP_ACTIVE_I2C_ISO_EN_S  21
/** PMU_HP_ACTIVE_I2C_RETENTION : R/W; bitpos: [22]; default: 0;
 *  need_des
 */
#define PMU_HP_ACTIVE_I2C_RETENTION    (BIT(22))
#define PMU_HP_ACTIVE_I2C_RETENTION_M  (PMU_HP_ACTIVE_I2C_RETENTION_V << PMU_HP_ACTIVE_I2C_RETENTION_S)
#define PMU_HP_ACTIVE_I2C_RETENTION_V  0x00000001U
#define PMU_HP_ACTIVE_I2C_RETENTION_S  22
/** PMU_HP_ACTIVE_XPD_PLL_I2C : R/W; bitpos: [26:23]; default: 0;
 *  need_des
 */
#define PMU_HP_ACTIVE_XPD_PLL_I2C    0x0000000FU
#define PMU_HP_ACTIVE_XPD_PLL_I2C_M  (PMU_HP_ACTIVE_XPD_PLL_I2C_V << PMU_HP_ACTIVE_XPD_PLL_I2C_S)
#define PMU_HP_ACTIVE_XPD_PLL_I2C_V  0x0000000FU
#define PMU_HP_ACTIVE_XPD_PLL_I2C_S  23
/** PMU_HP_ACTIVE_XPD_PLL : R/W; bitpos: [30:27]; default: 0;
 *  need_des
 */
#define PMU_HP_ACTIVE_XPD_PLL    0x0000000FU
#define PMU_HP_ACTIVE_XPD_PLL_M  (PMU_HP_ACTIVE_XPD_PLL_V << PMU_HP_ACTIVE_XPD_PLL_S)
#define PMU_HP_ACTIVE_XPD_PLL_V  0x0000000FU
#define PMU_HP_ACTIVE_XPD_PLL_S  27

/** PMU_HP_ACTIVE_BIAS_REG register
 *  need_des
 */
#define PMU_HP_ACTIVE_BIAS_REG (DR_REG_PMU_BASE + 0x18)
/** PMU_HP_ACTIVE_DCM_VSET : R/W; bitpos: [22:18]; default: 20;
 *  need_des
 */
#define PMU_HP_ACTIVE_DCM_VSET    0x0000001FU
#define PMU_HP_ACTIVE_DCM_VSET_M  (PMU_HP_ACTIVE_DCM_VSET_V << PMU_HP_ACTIVE_DCM_VSET_S)
#define PMU_HP_ACTIVE_DCM_VSET_V  0x0000001FU
#define PMU_HP_ACTIVE_DCM_VSET_S  18
/** PMU_HP_ACTIVE_DCM_MODE : R/W; bitpos: [24:23]; default: 0;
 *  need_des
 */
#define PMU_HP_ACTIVE_DCM_MODE    0x00000003U
#define PMU_HP_ACTIVE_DCM_MODE_M  (PMU_HP_ACTIVE_DCM_MODE_V << PMU_HP_ACTIVE_DCM_MODE_S)
#define PMU_HP_ACTIVE_DCM_MODE_V  0x00000003U
#define PMU_HP_ACTIVE_DCM_MODE_S  23
/** PMU_HP_ACTIVE_XPD_BIAS : R/W; bitpos: [25]; default: 0;
 *  need_des
 */
#define PMU_HP_ACTIVE_XPD_BIAS    (BIT(25))
#define PMU_HP_ACTIVE_XPD_BIAS_M  (PMU_HP_ACTIVE_XPD_BIAS_V << PMU_HP_ACTIVE_XPD_BIAS_S)
#define PMU_HP_ACTIVE_XPD_BIAS_V  0x00000001U
#define PMU_HP_ACTIVE_XPD_BIAS_S  25
/** PMU_HP_ACTIVE_DBG_ATTEN : R/W; bitpos: [29:26]; default: 0;
 *  need_des
 */
#define PMU_HP_ACTIVE_DBG_ATTEN    0x0000000FU
#define PMU_HP_ACTIVE_DBG_ATTEN_M  (PMU_HP_ACTIVE_DBG_ATTEN_V << PMU_HP_ACTIVE_DBG_ATTEN_S)
#define PMU_HP_ACTIVE_DBG_ATTEN_V  0x0000000FU
#define PMU_HP_ACTIVE_DBG_ATTEN_S  26
/** PMU_HP_ACTIVE_PD_CUR : R/W; bitpos: [30]; default: 0;
 *  need_des
 */
#define PMU_HP_ACTIVE_PD_CUR    (BIT(30))
#define PMU_HP_ACTIVE_PD_CUR_M  (PMU_HP_ACTIVE_PD_CUR_V << PMU_HP_ACTIVE_PD_CUR_S)
#define PMU_HP_ACTIVE_PD_CUR_V  0x00000001U
#define PMU_HP_ACTIVE_PD_CUR_S  30
/** PMU_HP_ACTIVE_BIAS_SLEEP : R/W; bitpos: [31]; default: 0;
 *  need_des
 */
#define PMU_HP_ACTIVE_BIAS_SLEEP    (BIT(31))
#define PMU_HP_ACTIVE_BIAS_SLEEP_M  (PMU_HP_ACTIVE_BIAS_SLEEP_V << PMU_HP_ACTIVE_BIAS_SLEEP_S)
#define PMU_HP_ACTIVE_BIAS_SLEEP_V  0x00000001U
#define PMU_HP_ACTIVE_BIAS_SLEEP_S  31

/** PMU_HP_ACTIVE_BACKUP_REG register
 *  need_des
 */
#define PMU_HP_ACTIVE_BACKUP_REG (DR_REG_PMU_BASE + 0x1c)
/** PMU_HP_SLEEP2ACTIVE_BACKUP_MODEM_CLK_CODE : R/W; bitpos: [5:4]; default: 0;
 *  need_des
 */
#define PMU_HP_SLEEP2ACTIVE_BACKUP_MODEM_CLK_CODE    0x00000003U
#define PMU_HP_SLEEP2ACTIVE_BACKUP_MODEM_CLK_CODE_M  (PMU_HP_SLEEP2ACTIVE_BACKUP_MODEM_CLK_CODE_V << PMU_HP_SLEEP2ACTIVE_BACKUP_MODEM_CLK_CODE_S)
#define PMU_HP_SLEEP2ACTIVE_BACKUP_MODEM_CLK_CODE_V  0x00000003U
#define PMU_HP_SLEEP2ACTIVE_BACKUP_MODEM_CLK_CODE_S  4
/** PMU_HP_MODEM2ACTIVE_BACKUP_MODEM_CLK_CODE : R/W; bitpos: [7:6]; default: 0;
 *  need_des
 */
#define PMU_HP_MODEM2ACTIVE_BACKUP_MODEM_CLK_CODE    0x00000003U
#define PMU_HP_MODEM2ACTIVE_BACKUP_MODEM_CLK_CODE_M  (PMU_HP_MODEM2ACTIVE_BACKUP_MODEM_CLK_CODE_V << PMU_HP_MODEM2ACTIVE_BACKUP_MODEM_CLK_CODE_S)
#define PMU_HP_MODEM2ACTIVE_BACKUP_MODEM_CLK_CODE_V  0x00000003U
#define PMU_HP_MODEM2ACTIVE_BACKUP_MODEM_CLK_CODE_S  6
/** PMU_HP_ACTIVE_RETENTION_MODE : R/W; bitpos: [10]; default: 0;
 *  need_des
 */
#define PMU_HP_ACTIVE_RETENTION_MODE    (BIT(10))
#define PMU_HP_ACTIVE_RETENTION_MODE_M  (PMU_HP_ACTIVE_RETENTION_MODE_V << PMU_HP_ACTIVE_RETENTION_MODE_S)
#define PMU_HP_ACTIVE_RETENTION_MODE_V  0x00000001U
#define PMU_HP_ACTIVE_RETENTION_MODE_S  10
/** PMU_HP_SLEEP2ACTIVE_RETENTION_EN : R/W; bitpos: [11]; default: 0;
 *  need_des
 */
#define PMU_HP_SLEEP2ACTIVE_RETENTION_EN    (BIT(11))
#define PMU_HP_SLEEP2ACTIVE_RETENTION_EN_M  (PMU_HP_SLEEP2ACTIVE_RETENTION_EN_V << PMU_HP_SLEEP2ACTIVE_RETENTION_EN_S)
#define PMU_HP_SLEEP2ACTIVE_RETENTION_EN_V  0x00000001U
#define PMU_HP_SLEEP2ACTIVE_RETENTION_EN_S  11
/** PMU_HP_MODEM2ACTIVE_RETENTION_EN : R/W; bitpos: [12]; default: 0;
 *  need_des
 */
#define PMU_HP_MODEM2ACTIVE_RETENTION_EN    (BIT(12))
#define PMU_HP_MODEM2ACTIVE_RETENTION_EN_M  (PMU_HP_MODEM2ACTIVE_RETENTION_EN_V << PMU_HP_MODEM2ACTIVE_RETENTION_EN_S)
#define PMU_HP_MODEM2ACTIVE_RETENTION_EN_V  0x00000001U
#define PMU_HP_MODEM2ACTIVE_RETENTION_EN_S  12
/** PMU_HP_SLEEP2ACTIVE_BACKUP_CLK_SEL : R/W; bitpos: [15:14]; default: 0;
 *  need_des
 */
#define PMU_HP_SLEEP2ACTIVE_BACKUP_CLK_SEL    0x00000003U
#define PMU_HP_SLEEP2ACTIVE_BACKUP_CLK_SEL_M  (PMU_HP_SLEEP2ACTIVE_BACKUP_CLK_SEL_V << PMU_HP_SLEEP2ACTIVE_BACKUP_CLK_SEL_S)
#define PMU_HP_SLEEP2ACTIVE_BACKUP_CLK_SEL_V  0x00000003U
#define PMU_HP_SLEEP2ACTIVE_BACKUP_CLK_SEL_S  14
/** PMU_HP_MODEM2ACTIVE_BACKUP_CLK_SEL : R/W; bitpos: [17:16]; default: 0;
 *  need_des
 */
#define PMU_HP_MODEM2ACTIVE_BACKUP_CLK_SEL    0x00000003U
#define PMU_HP_MODEM2ACTIVE_BACKUP_CLK_SEL_M  (PMU_HP_MODEM2ACTIVE_BACKUP_CLK_SEL_V << PMU_HP_MODEM2ACTIVE_BACKUP_CLK_SEL_S)
#define PMU_HP_MODEM2ACTIVE_BACKUP_CLK_SEL_V  0x00000003U
#define PMU_HP_MODEM2ACTIVE_BACKUP_CLK_SEL_S  16
/** PMU_HP_SLEEP2ACTIVE_BACKUP_MODE : R/W; bitpos: [22:20]; default: 0;
 *  need_des
 */
#define PMU_HP_SLEEP2ACTIVE_BACKUP_MODE    0x00000007U
#define PMU_HP_SLEEP2ACTIVE_BACKUP_MODE_M  (PMU_HP_SLEEP2ACTIVE_BACKUP_MODE_V << PMU_HP_SLEEP2ACTIVE_BACKUP_MODE_S)
#define PMU_HP_SLEEP2ACTIVE_BACKUP_MODE_V  0x00000007U
#define PMU_HP_SLEEP2ACTIVE_BACKUP_MODE_S  20
/** PMU_HP_MODEM2ACTIVE_BACKUP_MODE : R/W; bitpos: [25:23]; default: 0;
 *  need_des
 */
#define PMU_HP_MODEM2ACTIVE_BACKUP_MODE    0x00000007U
#define PMU_HP_MODEM2ACTIVE_BACKUP_MODE_M  (PMU_HP_MODEM2ACTIVE_BACKUP_MODE_V << PMU_HP_MODEM2ACTIVE_BACKUP_MODE_S)
#define PMU_HP_MODEM2ACTIVE_BACKUP_MODE_V  0x00000007U
#define PMU_HP_MODEM2ACTIVE_BACKUP_MODE_S  23
/** PMU_HP_SLEEP2ACTIVE_BACKUP_EN : R/W; bitpos: [29]; default: 0;
 *  need_des
 */
#define PMU_HP_SLEEP2ACTIVE_BACKUP_EN    (BIT(29))
#define PMU_HP_SLEEP2ACTIVE_BACKUP_EN_M  (PMU_HP_SLEEP2ACTIVE_BACKUP_EN_V << PMU_HP_SLEEP2ACTIVE_BACKUP_EN_S)
#define PMU_HP_SLEEP2ACTIVE_BACKUP_EN_V  0x00000001U
#define PMU_HP_SLEEP2ACTIVE_BACKUP_EN_S  29
/** PMU_HP_MODEM2ACTIVE_BACKUP_EN : R/W; bitpos: [30]; default: 0;
 *  need_des
 */
#define PMU_HP_MODEM2ACTIVE_BACKUP_EN    (BIT(30))
#define PMU_HP_MODEM2ACTIVE_BACKUP_EN_M  (PMU_HP_MODEM2ACTIVE_BACKUP_EN_V << PMU_HP_MODEM2ACTIVE_BACKUP_EN_S)
#define PMU_HP_MODEM2ACTIVE_BACKUP_EN_V  0x00000001U
#define PMU_HP_MODEM2ACTIVE_BACKUP_EN_S  30

/** PMU_HP_ACTIVE_BACKUP_CLK_REG register
 *  need_des
 */
#define PMU_HP_ACTIVE_BACKUP_CLK_REG (DR_REG_PMU_BASE + 0x20)
/** PMU_HP_ACTIVE_BACKUP_ICG_FUNC_EN : R/W; bitpos: [31:0]; default: 0;
 *  need_des
 */
#define PMU_HP_ACTIVE_BACKUP_ICG_FUNC_EN    0xFFFFFFFFU
#define PMU_HP_ACTIVE_BACKUP_ICG_FUNC_EN_M  (PMU_HP_ACTIVE_BACKUP_ICG_FUNC_EN_V << PMU_HP_ACTIVE_BACKUP_ICG_FUNC_EN_S)
#define PMU_HP_ACTIVE_BACKUP_ICG_FUNC_EN_V  0xFFFFFFFFU
#define PMU_HP_ACTIVE_BACKUP_ICG_FUNC_EN_S  0

/** PMU_HP_ACTIVE_SYSCLK_REG register
 *  need_des
 */
#define PMU_HP_ACTIVE_SYSCLK_REG (DR_REG_PMU_BASE + 0x24)
/** PMU_HP_ACTIVE_DIG_SYS_CLK_NO_DIV : R/W; bitpos: [26]; default: 0;
 *  need_des
 */
#define PMU_HP_ACTIVE_DIG_SYS_CLK_NO_DIV    (BIT(26))
#define PMU_HP_ACTIVE_DIG_SYS_CLK_NO_DIV_M  (PMU_HP_ACTIVE_DIG_SYS_CLK_NO_DIV_V << PMU_HP_ACTIVE_DIG_SYS_CLK_NO_DIV_S)
#define PMU_HP_ACTIVE_DIG_SYS_CLK_NO_DIV_V  0x00000001U
#define PMU_HP_ACTIVE_DIG_SYS_CLK_NO_DIV_S  26
/** PMU_HP_ACTIVE_ICG_SYS_CLOCK_EN : R/W; bitpos: [27]; default: 0;
 *  need_des
 */
#define PMU_HP_ACTIVE_ICG_SYS_CLOCK_EN    (BIT(27))
#define PMU_HP_ACTIVE_ICG_SYS_CLOCK_EN_M  (PMU_HP_ACTIVE_ICG_SYS_CLOCK_EN_V << PMU_HP_ACTIVE_ICG_SYS_CLOCK_EN_S)
#define PMU_HP_ACTIVE_ICG_SYS_CLOCK_EN_V  0x00000001U
#define PMU_HP_ACTIVE_ICG_SYS_CLOCK_EN_S  27
/** PMU_HP_ACTIVE_SYS_CLK_SLP_SEL : R/W; bitpos: [28]; default: 0;
 *  need_des
 */
#define PMU_HP_ACTIVE_SYS_CLK_SLP_SEL    (BIT(28))
#define PMU_HP_ACTIVE_SYS_CLK_SLP_SEL_M  (PMU_HP_ACTIVE_SYS_CLK_SLP_SEL_V << PMU_HP_ACTIVE_SYS_CLK_SLP_SEL_S)
#define PMU_HP_ACTIVE_SYS_CLK_SLP_SEL_V  0x00000001U
#define PMU_HP_ACTIVE_SYS_CLK_SLP_SEL_S  28
/** PMU_HP_ACTIVE_ICG_SLP_SEL : R/W; bitpos: [29]; default: 0;
 *  need_des
 */
#define PMU_HP_ACTIVE_ICG_SLP_SEL    (BIT(29))
#define PMU_HP_ACTIVE_ICG_SLP_SEL_M  (PMU_HP_ACTIVE_ICG_SLP_SEL_V << PMU_HP_ACTIVE_ICG_SLP_SEL_S)
#define PMU_HP_ACTIVE_ICG_SLP_SEL_V  0x00000001U
#define PMU_HP_ACTIVE_ICG_SLP_SEL_S  29
/** PMU_HP_ACTIVE_DIG_SYS_CLK_SEL : R/W; bitpos: [31:30]; default: 0;
 *  need_des
 */
#define PMU_HP_ACTIVE_DIG_SYS_CLK_SEL    0x00000003U
#define PMU_HP_ACTIVE_DIG_SYS_CLK_SEL_M  (PMU_HP_ACTIVE_DIG_SYS_CLK_SEL_V << PMU_HP_ACTIVE_DIG_SYS_CLK_SEL_S)
#define PMU_HP_ACTIVE_DIG_SYS_CLK_SEL_V  0x00000003U
#define PMU_HP_ACTIVE_DIG_SYS_CLK_SEL_S  30

/** PMU_HP_ACTIVE_HP_REGULATOR0_REG register
 *  need_des
 */
#define PMU_HP_ACTIVE_HP_REGULATOR0_REG (DR_REG_PMU_BASE + 0x28)
/** PMU_LP_DBIAS_VOL : RO; bitpos: [8:4]; default: 24;
 *  need_des
 */
#define PMU_LP_DBIAS_VOL    0x0000001FU
#define PMU_LP_DBIAS_VOL_M  (PMU_LP_DBIAS_VOL_V << PMU_LP_DBIAS_VOL_S)
#define PMU_LP_DBIAS_VOL_V  0x0000001FU
#define PMU_LP_DBIAS_VOL_S  4
/** PMU_HP_DBIAS_VOL : RO; bitpos: [13:9]; default: 24;
 *  need_des
 */
#define PMU_HP_DBIAS_VOL    0x0000001FU
#define PMU_HP_DBIAS_VOL_M  (PMU_HP_DBIAS_VOL_V << PMU_HP_DBIAS_VOL_S)
#define PMU_HP_DBIAS_VOL_V  0x0000001FU
#define PMU_HP_DBIAS_VOL_S  9
/** PMU_DIG_REGULATOR0_DBIAS_SEL : R/W; bitpos: [14]; default: 1;
 *  need_des
 */
#define PMU_DIG_REGULATOR0_DBIAS_SEL    (BIT(14))
#define PMU_DIG_REGULATOR0_DBIAS_SEL_M  (PMU_DIG_REGULATOR0_DBIAS_SEL_V << PMU_DIG_REGULATOR0_DBIAS_SEL_S)
#define PMU_DIG_REGULATOR0_DBIAS_SEL_V  0x00000001U
#define PMU_DIG_REGULATOR0_DBIAS_SEL_S  14
/** PMU_DIG_DBIAS_INIT : WT; bitpos: [15]; default: 0;
 *  need_des
 */
#define PMU_DIG_DBIAS_INIT    (BIT(15))
#define PMU_DIG_DBIAS_INIT_M  (PMU_DIG_DBIAS_INIT_V << PMU_DIG_DBIAS_INIT_S)
#define PMU_DIG_DBIAS_INIT_V  0x00000001U
#define PMU_DIG_DBIAS_INIT_S  15
/** PMU_HP_ACTIVE_HP_REGULATOR_SLP_MEM_XPD : R/W; bitpos: [16]; default: 1;
 *  need_des
 */
#define PMU_HP_ACTIVE_HP_REGULATOR_SLP_MEM_XPD    (BIT(16))
#define PMU_HP_ACTIVE_HP_REGULATOR_SLP_MEM_XPD_M  (PMU_HP_ACTIVE_HP_REGULATOR_SLP_MEM_XPD_V << PMU_HP_ACTIVE_HP_REGULATOR_SLP_MEM_XPD_S)
#define PMU_HP_ACTIVE_HP_REGULATOR_SLP_MEM_XPD_V  0x00000001U
#define PMU_HP_ACTIVE_HP_REGULATOR_SLP_MEM_XPD_S  16
/** PMU_HP_ACTIVE_HP_REGULATOR_SLP_LOGIC_XPD : R/W; bitpos: [17]; default: 1;
 *  need_des
 */
#define PMU_HP_ACTIVE_HP_REGULATOR_SLP_LOGIC_XPD    (BIT(17))
#define PMU_HP_ACTIVE_HP_REGULATOR_SLP_LOGIC_XPD_M  (PMU_HP_ACTIVE_HP_REGULATOR_SLP_LOGIC_XPD_V << PMU_HP_ACTIVE_HP_REGULATOR_SLP_LOGIC_XPD_S)
#define PMU_HP_ACTIVE_HP_REGULATOR_SLP_LOGIC_XPD_V  0x00000001U
#define PMU_HP_ACTIVE_HP_REGULATOR_SLP_LOGIC_XPD_S  17
/** PMU_HP_ACTIVE_HP_REGULATOR_XPD : R/W; bitpos: [18]; default: 1;
 *  need_des
 */
#define PMU_HP_ACTIVE_HP_REGULATOR_XPD    (BIT(18))
#define PMU_HP_ACTIVE_HP_REGULATOR_XPD_M  (PMU_HP_ACTIVE_HP_REGULATOR_XPD_V << PMU_HP_ACTIVE_HP_REGULATOR_XPD_S)
#define PMU_HP_ACTIVE_HP_REGULATOR_XPD_V  0x00000001U
#define PMU_HP_ACTIVE_HP_REGULATOR_XPD_S  18
/** PMU_HP_ACTIVE_HP_REGULATOR_SLP_MEM_DBIAS : R/W; bitpos: [22:19]; default: 12;
 *  need_des
 */
#define PMU_HP_ACTIVE_HP_REGULATOR_SLP_MEM_DBIAS    0x0000000FU
#define PMU_HP_ACTIVE_HP_REGULATOR_SLP_MEM_DBIAS_M  (PMU_HP_ACTIVE_HP_REGULATOR_SLP_MEM_DBIAS_V << PMU_HP_ACTIVE_HP_REGULATOR_SLP_MEM_DBIAS_S)
#define PMU_HP_ACTIVE_HP_REGULATOR_SLP_MEM_DBIAS_V  0x0000000FU
#define PMU_HP_ACTIVE_HP_REGULATOR_SLP_MEM_DBIAS_S  19
/** PMU_HP_ACTIVE_HP_REGULATOR_SLP_LOGIC_DBIAS : R/W; bitpos: [26:23]; default: 12;
 *  need_des
 */
#define PMU_HP_ACTIVE_HP_REGULATOR_SLP_LOGIC_DBIAS    0x0000000FU
#define PMU_HP_ACTIVE_HP_REGULATOR_SLP_LOGIC_DBIAS_M  (PMU_HP_ACTIVE_HP_REGULATOR_SLP_LOGIC_DBIAS_V << PMU_HP_ACTIVE_HP_REGULATOR_SLP_LOGIC_DBIAS_S)
#define PMU_HP_ACTIVE_HP_REGULATOR_SLP_LOGIC_DBIAS_V  0x0000000FU
#define PMU_HP_ACTIVE_HP_REGULATOR_SLP_LOGIC_DBIAS_S  23
/** PMU_HP_ACTIVE_HP_REGULATOR_DBIAS : R/W; bitpos: [31:27]; default: 24;
 *  need_des
 */
#define PMU_HP_ACTIVE_HP_REGULATOR_DBIAS    0x0000001FU
#define PMU_HP_ACTIVE_HP_REGULATOR_DBIAS_M  (PMU_HP_ACTIVE_HP_REGULATOR_DBIAS_V << PMU_HP_ACTIVE_HP_REGULATOR_DBIAS_S)
#define PMU_HP_ACTIVE_HP_REGULATOR_DBIAS_V  0x0000001FU
#define PMU_HP_ACTIVE_HP_REGULATOR_DBIAS_S  27

/** PMU_HP_ACTIVE_HP_REGULATOR1_REG register
 *  need_des
 */
#define PMU_HP_ACTIVE_HP_REGULATOR1_REG (DR_REG_PMU_BASE + 0x2c)
/** PMU_HP_ACTIVE_HP_REGULATOR_DRV_B : R/W; bitpos: [31:26]; default: 0;
 *  need_des
 */
#define PMU_HP_ACTIVE_HP_REGULATOR_DRV_B    0x0000003FU
#define PMU_HP_ACTIVE_HP_REGULATOR_DRV_B_M  (PMU_HP_ACTIVE_HP_REGULATOR_DRV_B_V << PMU_HP_ACTIVE_HP_REGULATOR_DRV_B_S)
#define PMU_HP_ACTIVE_HP_REGULATOR_DRV_B_V  0x0000003FU
#define PMU_HP_ACTIVE_HP_REGULATOR_DRV_B_S  26

/** PMU_HP_ACTIVE_XTAL_REG register
 *  need_des
 */
#define PMU_HP_ACTIVE_XTAL_REG (DR_REG_PMU_BASE + 0x30)
/** PMU_HP_ACTIVE_XPD_XTAL : R/W; bitpos: [31]; default: 1;
 *  need_des
 */
#define PMU_HP_ACTIVE_XPD_XTAL    (BIT(31))
#define PMU_HP_ACTIVE_XPD_XTAL_M  (PMU_HP_ACTIVE_XPD_XTAL_V << PMU_HP_ACTIVE_XPD_XTAL_S)
#define PMU_HP_ACTIVE_XPD_XTAL_V  0x00000001U
#define PMU_HP_ACTIVE_XPD_XTAL_S  31

/** PMU_HP_MODEM_DIG_POWER_REG register
 *  need_des
 */
#define PMU_HP_MODEM_DIG_POWER_REG (DR_REG_PMU_BASE + 0x34)
/** PMU_HP_MODEM_DCDC_SWITCH_PD_EN : R/W; bitpos: [21]; default: 0;
 *  need_des
 */
#define PMU_HP_MODEM_DCDC_SWITCH_PD_EN    (BIT(21))
#define PMU_HP_MODEM_DCDC_SWITCH_PD_EN_M  (PMU_HP_MODEM_DCDC_SWITCH_PD_EN_V << PMU_HP_MODEM_DCDC_SWITCH_PD_EN_S)
#define PMU_HP_MODEM_DCDC_SWITCH_PD_EN_V  0x00000001U
#define PMU_HP_MODEM_DCDC_SWITCH_PD_EN_S  21
/** PMU_HP_MODEM_HP_MEM_DSLP : WT; bitpos: [22]; default: 0;
 *  need_des
 */
#define PMU_HP_MODEM_HP_MEM_DSLP    (BIT(22))
#define PMU_HP_MODEM_HP_MEM_DSLP_M  (PMU_HP_MODEM_HP_MEM_DSLP_V << PMU_HP_MODEM_HP_MEM_DSLP_S)
#define PMU_HP_MODEM_HP_MEM_DSLP_V  0x00000001U
#define PMU_HP_MODEM_HP_MEM_DSLP_S  22
/** PMU_HP_MODEM_PD_HP_MEM_PD_EN : WT; bitpos: [26:23]; default: 0;
 *  need_des
 */
#define PMU_HP_MODEM_PD_HP_MEM_PD_EN    0x0000000FU
#define PMU_HP_MODEM_PD_HP_MEM_PD_EN_M  (PMU_HP_MODEM_PD_HP_MEM_PD_EN_V << PMU_HP_MODEM_PD_HP_MEM_PD_EN_S)
#define PMU_HP_MODEM_PD_HP_MEM_PD_EN_V  0x0000000FU
#define PMU_HP_MODEM_PD_HP_MEM_PD_EN_S  23
/** PMU_HP_MODEM_PD_HP_WIFI_PD_EN : WT; bitpos: [27]; default: 0;
 *  need_des
 */
#define PMU_HP_MODEM_PD_HP_WIFI_PD_EN    (BIT(27))
#define PMU_HP_MODEM_PD_HP_WIFI_PD_EN_M  (PMU_HP_MODEM_PD_HP_WIFI_PD_EN_V << PMU_HP_MODEM_PD_HP_WIFI_PD_EN_S)
#define PMU_HP_MODEM_PD_HP_WIFI_PD_EN_V  0x00000001U
#define PMU_HP_MODEM_PD_HP_WIFI_PD_EN_S  27
/** PMU_HP_MODEM_PD_HP_CPU_PD_EN : WT; bitpos: [29]; default: 0;
 *  need_des
 */
#define PMU_HP_MODEM_PD_HP_CPU_PD_EN    (BIT(29))
#define PMU_HP_MODEM_PD_HP_CPU_PD_EN_M  (PMU_HP_MODEM_PD_HP_CPU_PD_EN_V << PMU_HP_MODEM_PD_HP_CPU_PD_EN_S)
#define PMU_HP_MODEM_PD_HP_CPU_PD_EN_V  0x00000001U
#define PMU_HP_MODEM_PD_HP_CPU_PD_EN_S  29
/** PMU_HP_MODEM_PD_CNNT_PD_EN : WT; bitpos: [30]; default: 0;
 *  need_des
 */
#define PMU_HP_MODEM_PD_CNNT_PD_EN    (BIT(30))
#define PMU_HP_MODEM_PD_CNNT_PD_EN_M  (PMU_HP_MODEM_PD_CNNT_PD_EN_V << PMU_HP_MODEM_PD_CNNT_PD_EN_S)
#define PMU_HP_MODEM_PD_CNNT_PD_EN_V  0x00000001U
#define PMU_HP_MODEM_PD_CNNT_PD_EN_S  30
/** PMU_HP_MODEM_PD_TOP_PD_EN : WT; bitpos: [31]; default: 0;
 *  need_des
 */
#define PMU_HP_MODEM_PD_TOP_PD_EN    (BIT(31))
#define PMU_HP_MODEM_PD_TOP_PD_EN_M  (PMU_HP_MODEM_PD_TOP_PD_EN_V << PMU_HP_MODEM_PD_TOP_PD_EN_S)
#define PMU_HP_MODEM_PD_TOP_PD_EN_V  0x00000001U
#define PMU_HP_MODEM_PD_TOP_PD_EN_S  31

/** PMU_HP_MODEM_ICG_HP_FUNC_REG register
 *  need_des
 */
#define PMU_HP_MODEM_ICG_HP_FUNC_REG (DR_REG_PMU_BASE + 0x38)
/** PMU_HP_MODEM_DIG_ICG_FUNC_EN : WT; bitpos: [31:0]; default: 4294967295;
 *  need_des
 */
#define PMU_HP_MODEM_DIG_ICG_FUNC_EN    0xFFFFFFFFU
#define PMU_HP_MODEM_DIG_ICG_FUNC_EN_M  (PMU_HP_MODEM_DIG_ICG_FUNC_EN_V << PMU_HP_MODEM_DIG_ICG_FUNC_EN_S)
#define PMU_HP_MODEM_DIG_ICG_FUNC_EN_V  0xFFFFFFFFU
#define PMU_HP_MODEM_DIG_ICG_FUNC_EN_S  0

/** PMU_HP_MODEM_ICG_HP_APB_REG register
 *  need_des
 */
#define PMU_HP_MODEM_ICG_HP_APB_REG (DR_REG_PMU_BASE + 0x3c)
/** PMU_HP_MODEM_DIG_ICG_APB_EN : WT; bitpos: [31:0]; default: 4294967295;
 *  need_des
 */
#define PMU_HP_MODEM_DIG_ICG_APB_EN    0xFFFFFFFFU
#define PMU_HP_MODEM_DIG_ICG_APB_EN_M  (PMU_HP_MODEM_DIG_ICG_APB_EN_V << PMU_HP_MODEM_DIG_ICG_APB_EN_S)
#define PMU_HP_MODEM_DIG_ICG_APB_EN_V  0xFFFFFFFFU
#define PMU_HP_MODEM_DIG_ICG_APB_EN_S  0

/** PMU_HP_MODEM_ICG_MODEM_REG register
 *  need_des
 */
#define PMU_HP_MODEM_ICG_MODEM_REG (DR_REG_PMU_BASE + 0x40)
/** PMU_HP_MODEM_DIG_ICG_MODEM_CODE : WT; bitpos: [31:30]; default: 0;
 *  need_des
 */
#define PMU_HP_MODEM_DIG_ICG_MODEM_CODE    0x00000003U
#define PMU_HP_MODEM_DIG_ICG_MODEM_CODE_M  (PMU_HP_MODEM_DIG_ICG_MODEM_CODE_V << PMU_HP_MODEM_DIG_ICG_MODEM_CODE_S)
#define PMU_HP_MODEM_DIG_ICG_MODEM_CODE_V  0x00000003U
#define PMU_HP_MODEM_DIG_ICG_MODEM_CODE_S  30

/** PMU_HP_MODEM_HP_SYS_CNTL_REG register
 *  need_des
 */
#define PMU_HP_MODEM_HP_SYS_CNTL_REG (DR_REG_PMU_BASE + 0x44)
/** PMU_HP_MODEM_HP_POWER_DET_BYPASS : WT; bitpos: [23]; default: 0;
 *  need_des
 */
#define PMU_HP_MODEM_HP_POWER_DET_BYPASS    (BIT(23))
#define PMU_HP_MODEM_HP_POWER_DET_BYPASS_M  (PMU_HP_MODEM_HP_POWER_DET_BYPASS_V << PMU_HP_MODEM_HP_POWER_DET_BYPASS_S)
#define PMU_HP_MODEM_HP_POWER_DET_BYPASS_V  0x00000001U
#define PMU_HP_MODEM_HP_POWER_DET_BYPASS_S  23
/** PMU_HP_MODEM_UART_WAKEUP_EN : WT; bitpos: [24]; default: 0;
 *  need_des
 */
#define PMU_HP_MODEM_UART_WAKEUP_EN    (BIT(24))
#define PMU_HP_MODEM_UART_WAKEUP_EN_M  (PMU_HP_MODEM_UART_WAKEUP_EN_V << PMU_HP_MODEM_UART_WAKEUP_EN_S)
#define PMU_HP_MODEM_UART_WAKEUP_EN_V  0x00000001U
#define PMU_HP_MODEM_UART_WAKEUP_EN_S  24
/** PMU_HP_MODEM_LP_PAD_HOLD_ALL : WT; bitpos: [25]; default: 0;
 *  need_des
 */
#define PMU_HP_MODEM_LP_PAD_HOLD_ALL    (BIT(25))
#define PMU_HP_MODEM_LP_PAD_HOLD_ALL_M  (PMU_HP_MODEM_LP_PAD_HOLD_ALL_V << PMU_HP_MODEM_LP_PAD_HOLD_ALL_S)
#define PMU_HP_MODEM_LP_PAD_HOLD_ALL_V  0x00000001U
#define PMU_HP_MODEM_LP_PAD_HOLD_ALL_S  25
/** PMU_HP_MODEM_HP_PAD_HOLD_ALL : WT; bitpos: [26]; default: 0;
 *  need_des
 */
#define PMU_HP_MODEM_HP_PAD_HOLD_ALL    (BIT(26))
#define PMU_HP_MODEM_HP_PAD_HOLD_ALL_M  (PMU_HP_MODEM_HP_PAD_HOLD_ALL_V << PMU_HP_MODEM_HP_PAD_HOLD_ALL_S)
#define PMU_HP_MODEM_HP_PAD_HOLD_ALL_V  0x00000001U
#define PMU_HP_MODEM_HP_PAD_HOLD_ALL_S  26
/** PMU_HP_MODEM_DIG_PAD_SLP_SEL : WT; bitpos: [27]; default: 0;
 *  need_des
 */
#define PMU_HP_MODEM_DIG_PAD_SLP_SEL    (BIT(27))
#define PMU_HP_MODEM_DIG_PAD_SLP_SEL_M  (PMU_HP_MODEM_DIG_PAD_SLP_SEL_V << PMU_HP_MODEM_DIG_PAD_SLP_SEL_S)
#define PMU_HP_MODEM_DIG_PAD_SLP_SEL_V  0x00000001U
#define PMU_HP_MODEM_DIG_PAD_SLP_SEL_S  27
/** PMU_HP_MODEM_DIG_PAUSE_WDT : WT; bitpos: [28]; default: 0;
 *  need_des
 */
#define PMU_HP_MODEM_DIG_PAUSE_WDT    (BIT(28))
#define PMU_HP_MODEM_DIG_PAUSE_WDT_M  (PMU_HP_MODEM_DIG_PAUSE_WDT_V << PMU_HP_MODEM_DIG_PAUSE_WDT_S)
#define PMU_HP_MODEM_DIG_PAUSE_WDT_V  0x00000001U
#define PMU_HP_MODEM_DIG_PAUSE_WDT_S  28
/** PMU_HP_MODEM_DIG_CPU_STALL : WT; bitpos: [29]; default: 0;
 *  need_des
 */
#define PMU_HP_MODEM_DIG_CPU_STALL    (BIT(29))
#define PMU_HP_MODEM_DIG_CPU_STALL_M  (PMU_HP_MODEM_DIG_CPU_STALL_V << PMU_HP_MODEM_DIG_CPU_STALL_S)
#define PMU_HP_MODEM_DIG_CPU_STALL_V  0x00000001U
#define PMU_HP_MODEM_DIG_CPU_STALL_S  29

/** PMU_HP_MODEM_HP_CK_POWER_REG register
 *  need_des
 */
#define PMU_HP_MODEM_HP_CK_POWER_REG (DR_REG_PMU_BASE + 0x48)
/** PMU_HP_MODEM_I2C_ISO_EN : WT; bitpos: [21]; default: 0;
 *  need_des
 */
#define PMU_HP_MODEM_I2C_ISO_EN    (BIT(21))
#define PMU_HP_MODEM_I2C_ISO_EN_M  (PMU_HP_MODEM_I2C_ISO_EN_V << PMU_HP_MODEM_I2C_ISO_EN_S)
#define PMU_HP_MODEM_I2C_ISO_EN_V  0x00000001U
#define PMU_HP_MODEM_I2C_ISO_EN_S  21
/** PMU_HP_MODEM_I2C_RETENTION : WT; bitpos: [22]; default: 0;
 *  need_des
 */
#define PMU_HP_MODEM_I2C_RETENTION    (BIT(22))
#define PMU_HP_MODEM_I2C_RETENTION_M  (PMU_HP_MODEM_I2C_RETENTION_V << PMU_HP_MODEM_I2C_RETENTION_S)
#define PMU_HP_MODEM_I2C_RETENTION_V  0x00000001U
#define PMU_HP_MODEM_I2C_RETENTION_S  22
/** PMU_HP_MODEM_XPD_PLL_I2C : WT; bitpos: [26:23]; default: 0;
 *  need_des
 */
#define PMU_HP_MODEM_XPD_PLL_I2C    0x0000000FU
#define PMU_HP_MODEM_XPD_PLL_I2C_M  (PMU_HP_MODEM_XPD_PLL_I2C_V << PMU_HP_MODEM_XPD_PLL_I2C_S)
#define PMU_HP_MODEM_XPD_PLL_I2C_V  0x0000000FU
#define PMU_HP_MODEM_XPD_PLL_I2C_S  23
/** PMU_HP_MODEM_XPD_PLL : WT; bitpos: [30:27]; default: 0;
 *  need_des
 */
#define PMU_HP_MODEM_XPD_PLL    0x0000000FU
#define PMU_HP_MODEM_XPD_PLL_M  (PMU_HP_MODEM_XPD_PLL_V << PMU_HP_MODEM_XPD_PLL_S)
#define PMU_HP_MODEM_XPD_PLL_V  0x0000000FU
#define PMU_HP_MODEM_XPD_PLL_S  27

/** PMU_HP_MODEM_BIAS_REG register
 *  need_des
 */
#define PMU_HP_MODEM_BIAS_REG (DR_REG_PMU_BASE + 0x4c)
/** PMU_HP_MODEM_DCM_VSET : WT; bitpos: [22:18]; default: 20;
 *  need_des
 */
#define PMU_HP_MODEM_DCM_VSET    0x0000001FU
#define PMU_HP_MODEM_DCM_VSET_M  (PMU_HP_MODEM_DCM_VSET_V << PMU_HP_MODEM_DCM_VSET_S)
#define PMU_HP_MODEM_DCM_VSET_V  0x0000001FU
#define PMU_HP_MODEM_DCM_VSET_S  18
/** PMU_HP_MODEM_DCM_MODE : WT; bitpos: [24:23]; default: 0;
 *  need_des
 */
#define PMU_HP_MODEM_DCM_MODE    0x00000003U
#define PMU_HP_MODEM_DCM_MODE_M  (PMU_HP_MODEM_DCM_MODE_V << PMU_HP_MODEM_DCM_MODE_S)
#define PMU_HP_MODEM_DCM_MODE_V  0x00000003U
#define PMU_HP_MODEM_DCM_MODE_S  23
/** PMU_HP_MODEM_XPD_BIAS : WT; bitpos: [25]; default: 0;
 *  need_des
 */
#define PMU_HP_MODEM_XPD_BIAS    (BIT(25))
#define PMU_HP_MODEM_XPD_BIAS_M  (PMU_HP_MODEM_XPD_BIAS_V << PMU_HP_MODEM_XPD_BIAS_S)
#define PMU_HP_MODEM_XPD_BIAS_V  0x00000001U
#define PMU_HP_MODEM_XPD_BIAS_S  25
/** PMU_HP_MODEM_DBG_ATTEN : WT; bitpos: [29:26]; default: 0;
 *  need_des
 */
#define PMU_HP_MODEM_DBG_ATTEN    0x0000000FU
#define PMU_HP_MODEM_DBG_ATTEN_M  (PMU_HP_MODEM_DBG_ATTEN_V << PMU_HP_MODEM_DBG_ATTEN_S)
#define PMU_HP_MODEM_DBG_ATTEN_V  0x0000000FU
#define PMU_HP_MODEM_DBG_ATTEN_S  26
/** PMU_HP_MODEM_PD_CUR : WT; bitpos: [30]; default: 0;
 *  need_des
 */
#define PMU_HP_MODEM_PD_CUR    (BIT(30))
#define PMU_HP_MODEM_PD_CUR_M  (PMU_HP_MODEM_PD_CUR_V << PMU_HP_MODEM_PD_CUR_S)
#define PMU_HP_MODEM_PD_CUR_V  0x00000001U
#define PMU_HP_MODEM_PD_CUR_S  30
/** PMU_HP_MODEM_BIAS_SLEEP : WT; bitpos: [31]; default: 0;
 *  need_des
 */
#define PMU_HP_MODEM_BIAS_SLEEP    (BIT(31))
#define PMU_HP_MODEM_BIAS_SLEEP_M  (PMU_HP_MODEM_BIAS_SLEEP_V << PMU_HP_MODEM_BIAS_SLEEP_S)
#define PMU_HP_MODEM_BIAS_SLEEP_V  0x00000001U
#define PMU_HP_MODEM_BIAS_SLEEP_S  31

/** PMU_HP_MODEM_BACKUP_REG register
 *  need_des
 */
#define PMU_HP_MODEM_BACKUP_REG (DR_REG_PMU_BASE + 0x50)
/** PMU_HP_SLEEP2MODEM_BACKUP_MODEM_CLK_CODE : WT; bitpos: [5:4]; default: 0;
 *  need_des
 */
#define PMU_HP_SLEEP2MODEM_BACKUP_MODEM_CLK_CODE    0x00000003U
#define PMU_HP_SLEEP2MODEM_BACKUP_MODEM_CLK_CODE_M  (PMU_HP_SLEEP2MODEM_BACKUP_MODEM_CLK_CODE_V << PMU_HP_SLEEP2MODEM_BACKUP_MODEM_CLK_CODE_S)
#define PMU_HP_SLEEP2MODEM_BACKUP_MODEM_CLK_CODE_V  0x00000003U
#define PMU_HP_SLEEP2MODEM_BACKUP_MODEM_CLK_CODE_S  4
/** PMU_HP_MODEM_RETENTION_MODE : WT; bitpos: [10]; default: 0;
 *  need_des
 */
#define PMU_HP_MODEM_RETENTION_MODE    (BIT(10))
#define PMU_HP_MODEM_RETENTION_MODE_M  (PMU_HP_MODEM_RETENTION_MODE_V << PMU_HP_MODEM_RETENTION_MODE_S)
#define PMU_HP_MODEM_RETENTION_MODE_V  0x00000001U
#define PMU_HP_MODEM_RETENTION_MODE_S  10
/** PMU_HP_SLEEP2MODEM_RETENTION_EN : WT; bitpos: [11]; default: 0;
 *  need_des
 */
#define PMU_HP_SLEEP2MODEM_RETENTION_EN    (BIT(11))
#define PMU_HP_SLEEP2MODEM_RETENTION_EN_M  (PMU_HP_SLEEP2MODEM_RETENTION_EN_V << PMU_HP_SLEEP2MODEM_RETENTION_EN_S)
#define PMU_HP_SLEEP2MODEM_RETENTION_EN_V  0x00000001U
#define PMU_HP_SLEEP2MODEM_RETENTION_EN_S  11
/** PMU_HP_SLEEP2MODEM_BACKUP_CLK_SEL : WT; bitpos: [15:14]; default: 0;
 *  need_des
 */
#define PMU_HP_SLEEP2MODEM_BACKUP_CLK_SEL    0x00000003U
#define PMU_HP_SLEEP2MODEM_BACKUP_CLK_SEL_M  (PMU_HP_SLEEP2MODEM_BACKUP_CLK_SEL_V << PMU_HP_SLEEP2MODEM_BACKUP_CLK_SEL_S)
#define PMU_HP_SLEEP2MODEM_BACKUP_CLK_SEL_V  0x00000003U
#define PMU_HP_SLEEP2MODEM_BACKUP_CLK_SEL_S  14
/** PMU_HP_SLEEP2MODEM_BACKUP_MODE : WT; bitpos: [22:20]; default: 0;
 *  need_des
 */
#define PMU_HP_SLEEP2MODEM_BACKUP_MODE    0x00000007U
#define PMU_HP_SLEEP2MODEM_BACKUP_MODE_M  (PMU_HP_SLEEP2MODEM_BACKUP_MODE_V << PMU_HP_SLEEP2MODEM_BACKUP_MODE_S)
#define PMU_HP_SLEEP2MODEM_BACKUP_MODE_V  0x00000007U
#define PMU_HP_SLEEP2MODEM_BACKUP_MODE_S  20
/** PMU_HP_SLEEP2MODEM_BACKUP_EN : WT; bitpos: [29]; default: 0;
 *  need_des
 */
#define PMU_HP_SLEEP2MODEM_BACKUP_EN    (BIT(29))
#define PMU_HP_SLEEP2MODEM_BACKUP_EN_M  (PMU_HP_SLEEP2MODEM_BACKUP_EN_V << PMU_HP_SLEEP2MODEM_BACKUP_EN_S)
#define PMU_HP_SLEEP2MODEM_BACKUP_EN_V  0x00000001U
#define PMU_HP_SLEEP2MODEM_BACKUP_EN_S  29

/** PMU_HP_MODEM_BACKUP_CLK_REG register
 *  need_des
 */
#define PMU_HP_MODEM_BACKUP_CLK_REG (DR_REG_PMU_BASE + 0x54)
/** PMU_HP_MODEM_BACKUP_ICG_FUNC_EN : WT; bitpos: [31:0]; default: 0;
 *  need_des
 */
#define PMU_HP_MODEM_BACKUP_ICG_FUNC_EN    0xFFFFFFFFU
#define PMU_HP_MODEM_BACKUP_ICG_FUNC_EN_M  (PMU_HP_MODEM_BACKUP_ICG_FUNC_EN_V << PMU_HP_MODEM_BACKUP_ICG_FUNC_EN_S)
#define PMU_HP_MODEM_BACKUP_ICG_FUNC_EN_V  0xFFFFFFFFU
#define PMU_HP_MODEM_BACKUP_ICG_FUNC_EN_S  0

/** PMU_HP_MODEM_SYSCLK_REG register
 *  need_des
 */
#define PMU_HP_MODEM_SYSCLK_REG (DR_REG_PMU_BASE + 0x58)
/** PMU_HP_MODEM_DIG_SYS_CLK_NO_DIV : WT; bitpos: [26]; default: 0;
 *  need_des
 */
#define PMU_HP_MODEM_DIG_SYS_CLK_NO_DIV    (BIT(26))
#define PMU_HP_MODEM_DIG_SYS_CLK_NO_DIV_M  (PMU_HP_MODEM_DIG_SYS_CLK_NO_DIV_V << PMU_HP_MODEM_DIG_SYS_CLK_NO_DIV_S)
#define PMU_HP_MODEM_DIG_SYS_CLK_NO_DIV_V  0x00000001U
#define PMU_HP_MODEM_DIG_SYS_CLK_NO_DIV_S  26
/** PMU_HP_MODEM_ICG_SYS_CLOCK_EN : WT; bitpos: [27]; default: 0;
 *  need_des
 */
#define PMU_HP_MODEM_ICG_SYS_CLOCK_EN    (BIT(27))
#define PMU_HP_MODEM_ICG_SYS_CLOCK_EN_M  (PMU_HP_MODEM_ICG_SYS_CLOCK_EN_V << PMU_HP_MODEM_ICG_SYS_CLOCK_EN_S)
#define PMU_HP_MODEM_ICG_SYS_CLOCK_EN_V  0x00000001U
#define PMU_HP_MODEM_ICG_SYS_CLOCK_EN_S  27
/** PMU_HP_MODEM_SYS_CLK_SLP_SEL : WT; bitpos: [28]; default: 0;
 *  need_des
 */
#define PMU_HP_MODEM_SYS_CLK_SLP_SEL    (BIT(28))
#define PMU_HP_MODEM_SYS_CLK_SLP_SEL_M  (PMU_HP_MODEM_SYS_CLK_SLP_SEL_V << PMU_HP_MODEM_SYS_CLK_SLP_SEL_S)
#define PMU_HP_MODEM_SYS_CLK_SLP_SEL_V  0x00000001U
#define PMU_HP_MODEM_SYS_CLK_SLP_SEL_S  28
/** PMU_HP_MODEM_ICG_SLP_SEL : WT; bitpos: [29]; default: 0;
 *  need_des
 */
#define PMU_HP_MODEM_ICG_SLP_SEL    (BIT(29))
#define PMU_HP_MODEM_ICG_SLP_SEL_M  (PMU_HP_MODEM_ICG_SLP_SEL_V << PMU_HP_MODEM_ICG_SLP_SEL_S)
#define PMU_HP_MODEM_ICG_SLP_SEL_V  0x00000001U
#define PMU_HP_MODEM_ICG_SLP_SEL_S  29
/** PMU_HP_MODEM_DIG_SYS_CLK_SEL : WT; bitpos: [31:30]; default: 0;
 *  need_des
 */
#define PMU_HP_MODEM_DIG_SYS_CLK_SEL    0x00000003U
#define PMU_HP_MODEM_DIG_SYS_CLK_SEL_M  (PMU_HP_MODEM_DIG_SYS_CLK_SEL_V << PMU_HP_MODEM_DIG_SYS_CLK_SEL_S)
#define PMU_HP_MODEM_DIG_SYS_CLK_SEL_V  0x00000003U
#define PMU_HP_MODEM_DIG_SYS_CLK_SEL_S  30

/** PMU_HP_MODEM_HP_REGULATOR0_REG register
 *  need_des
 */
#define PMU_HP_MODEM_HP_REGULATOR0_REG (DR_REG_PMU_BASE + 0x5c)
/** PMU_HP_MODEM_HP_REGULATOR_SLP_MEM_XPD : WT; bitpos: [16]; default: 1;
 *  need_des
 */
#define PMU_HP_MODEM_HP_REGULATOR_SLP_MEM_XPD    (BIT(16))
#define PMU_HP_MODEM_HP_REGULATOR_SLP_MEM_XPD_M  (PMU_HP_MODEM_HP_REGULATOR_SLP_MEM_XPD_V << PMU_HP_MODEM_HP_REGULATOR_SLP_MEM_XPD_S)
#define PMU_HP_MODEM_HP_REGULATOR_SLP_MEM_XPD_V  0x00000001U
#define PMU_HP_MODEM_HP_REGULATOR_SLP_MEM_XPD_S  16
/** PMU_HP_MODEM_HP_REGULATOR_SLP_LOGIC_XPD : WT; bitpos: [17]; default: 1;
 *  need_des
 */
#define PMU_HP_MODEM_HP_REGULATOR_SLP_LOGIC_XPD    (BIT(17))
#define PMU_HP_MODEM_HP_REGULATOR_SLP_LOGIC_XPD_M  (PMU_HP_MODEM_HP_REGULATOR_SLP_LOGIC_XPD_V << PMU_HP_MODEM_HP_REGULATOR_SLP_LOGIC_XPD_S)
#define PMU_HP_MODEM_HP_REGULATOR_SLP_LOGIC_XPD_V  0x00000001U
#define PMU_HP_MODEM_HP_REGULATOR_SLP_LOGIC_XPD_S  17
/** PMU_HP_MODEM_HP_REGULATOR_XPD : WT; bitpos: [18]; default: 1;
 *  need_des
 */
#define PMU_HP_MODEM_HP_REGULATOR_XPD    (BIT(18))
#define PMU_HP_MODEM_HP_REGULATOR_XPD_M  (PMU_HP_MODEM_HP_REGULATOR_XPD_V << PMU_HP_MODEM_HP_REGULATOR_XPD_S)
#define PMU_HP_MODEM_HP_REGULATOR_XPD_V  0x00000001U
#define PMU_HP_MODEM_HP_REGULATOR_XPD_S  18
/** PMU_HP_MODEM_HP_REGULATOR_SLP_MEM_DBIAS : WT; bitpos: [22:19]; default: 12;
 *  need_des
 */
#define PMU_HP_MODEM_HP_REGULATOR_SLP_MEM_DBIAS    0x0000000FU
#define PMU_HP_MODEM_HP_REGULATOR_SLP_MEM_DBIAS_M  (PMU_HP_MODEM_HP_REGULATOR_SLP_MEM_DBIAS_V << PMU_HP_MODEM_HP_REGULATOR_SLP_MEM_DBIAS_S)
#define PMU_HP_MODEM_HP_REGULATOR_SLP_MEM_DBIAS_V  0x0000000FU
#define PMU_HP_MODEM_HP_REGULATOR_SLP_MEM_DBIAS_S  19
/** PMU_HP_MODEM_HP_REGULATOR_SLP_LOGIC_DBIAS : WT; bitpos: [26:23]; default: 12;
 *  need_des
 */
#define PMU_HP_MODEM_HP_REGULATOR_SLP_LOGIC_DBIAS    0x0000000FU
#define PMU_HP_MODEM_HP_REGULATOR_SLP_LOGIC_DBIAS_M  (PMU_HP_MODEM_HP_REGULATOR_SLP_LOGIC_DBIAS_V << PMU_HP_MODEM_HP_REGULATOR_SLP_LOGIC_DBIAS_S)
#define PMU_HP_MODEM_HP_REGULATOR_SLP_LOGIC_DBIAS_V  0x0000000FU
#define PMU_HP_MODEM_HP_REGULATOR_SLP_LOGIC_DBIAS_S  23
/** PMU_HP_MODEM_HP_REGULATOR_DBIAS : WT; bitpos: [31:27]; default: 24;
 *  need_des
 */
#define PMU_HP_MODEM_HP_REGULATOR_DBIAS    0x0000001FU
#define PMU_HP_MODEM_HP_REGULATOR_DBIAS_M  (PMU_HP_MODEM_HP_REGULATOR_DBIAS_V << PMU_HP_MODEM_HP_REGULATOR_DBIAS_S)
#define PMU_HP_MODEM_HP_REGULATOR_DBIAS_V  0x0000001FU
#define PMU_HP_MODEM_HP_REGULATOR_DBIAS_S  27

/** PMU_HP_MODEM_HP_REGULATOR1_REG register
 *  need_des
 */
#define PMU_HP_MODEM_HP_REGULATOR1_REG (DR_REG_PMU_BASE + 0x60)
/** PMU_HP_MODEM_HP_REGULATOR_DRV_B : WT; bitpos: [31:8]; default: 0;
 *  need_des
 */
#define PMU_HP_MODEM_HP_REGULATOR_DRV_B    0x00FFFFFFU
#define PMU_HP_MODEM_HP_REGULATOR_DRV_B_M  (PMU_HP_MODEM_HP_REGULATOR_DRV_B_V << PMU_HP_MODEM_HP_REGULATOR_DRV_B_S)
#define PMU_HP_MODEM_HP_REGULATOR_DRV_B_V  0x00FFFFFFU
#define PMU_HP_MODEM_HP_REGULATOR_DRV_B_S  8

/** PMU_HP_MODEM_XTAL_REG register
 *  need_des
 */
#define PMU_HP_MODEM_XTAL_REG (DR_REG_PMU_BASE + 0x64)
/** PMU_HP_MODEM_XPD_XTAL : WT; bitpos: [31]; default: 1;
 *  need_des
 */
#define PMU_HP_MODEM_XPD_XTAL    (BIT(31))
#define PMU_HP_MODEM_XPD_XTAL_M  (PMU_HP_MODEM_XPD_XTAL_V << PMU_HP_MODEM_XPD_XTAL_S)
#define PMU_HP_MODEM_XPD_XTAL_V  0x00000001U
#define PMU_HP_MODEM_XPD_XTAL_S  31

/** PMU_HP_SLEEP_DIG_POWER_REG register
 *  need_des
 */
#define PMU_HP_SLEEP_DIG_POWER_REG (DR_REG_PMU_BASE + 0x68)
/** PMU_HP_SLEEP_DCDC_SWITCH_PD_EN : R/W; bitpos: [21]; default: 0;
 *  need_des
 */
#define PMU_HP_SLEEP_DCDC_SWITCH_PD_EN    (BIT(21))
#define PMU_HP_SLEEP_DCDC_SWITCH_PD_EN_M  (PMU_HP_SLEEP_DCDC_SWITCH_PD_EN_V << PMU_HP_SLEEP_DCDC_SWITCH_PD_EN_S)
#define PMU_HP_SLEEP_DCDC_SWITCH_PD_EN_V  0x00000001U
#define PMU_HP_SLEEP_DCDC_SWITCH_PD_EN_S  21
/** PMU_HP_SLEEP_HP_MEM_DSLP : R/W; bitpos: [22]; default: 0;
 *  need_des
 */
#define PMU_HP_SLEEP_HP_MEM_DSLP    (BIT(22))
#define PMU_HP_SLEEP_HP_MEM_DSLP_M  (PMU_HP_SLEEP_HP_MEM_DSLP_V << PMU_HP_SLEEP_HP_MEM_DSLP_S)
#define PMU_HP_SLEEP_HP_MEM_DSLP_V  0x00000001U
#define PMU_HP_SLEEP_HP_MEM_DSLP_S  22
/** PMU_HP_SLEEP_PD_HP_MEM_PD_EN : R/W; bitpos: [23]; default: 0;
 *  need_des
 */
#define PMU_HP_SLEEP_PD_HP_MEM_PD_EN    (BIT(23))
#define PMU_HP_SLEEP_PD_HP_MEM_PD_EN_M  (PMU_HP_SLEEP_PD_HP_MEM_PD_EN_V << PMU_HP_SLEEP_PD_HP_MEM_PD_EN_S)
#define PMU_HP_SLEEP_PD_HP_MEM_PD_EN_V  0x00000001U
#define PMU_HP_SLEEP_PD_HP_MEM_PD_EN_S  23
/** PMU_HP_SLEEP_PD_CNNT_PD_EN : R/W; bitpos: [30]; default: 0;
 *  need_des
 */
#define PMU_HP_SLEEP_PD_CNNT_PD_EN    (BIT(30))
#define PMU_HP_SLEEP_PD_CNNT_PD_EN_M  (PMU_HP_SLEEP_PD_CNNT_PD_EN_V << PMU_HP_SLEEP_PD_CNNT_PD_EN_S)
#define PMU_HP_SLEEP_PD_CNNT_PD_EN_V  0x00000001U
#define PMU_HP_SLEEP_PD_CNNT_PD_EN_S  30
/** PMU_HP_SLEEP_PD_TOP_PD_EN : R/W; bitpos: [31]; default: 0;
 *  need_des
 */
#define PMU_HP_SLEEP_PD_TOP_PD_EN    (BIT(31))
#define PMU_HP_SLEEP_PD_TOP_PD_EN_M  (PMU_HP_SLEEP_PD_TOP_PD_EN_V << PMU_HP_SLEEP_PD_TOP_PD_EN_S)
#define PMU_HP_SLEEP_PD_TOP_PD_EN_V  0x00000001U
#define PMU_HP_SLEEP_PD_TOP_PD_EN_S  31

/** PMU_HP_SLEEP_ICG_HP_FUNC_REG register
 *  need_des
 */
#define PMU_HP_SLEEP_ICG_HP_FUNC_REG (DR_REG_PMU_BASE + 0x6c)
/** PMU_HP_SLEEP_DIG_ICG_FUNC_EN : R/W; bitpos: [31:0]; default: 4294967295;
 *  need_des
 */
#define PMU_HP_SLEEP_DIG_ICG_FUNC_EN    0xFFFFFFFFU
#define PMU_HP_SLEEP_DIG_ICG_FUNC_EN_M  (PMU_HP_SLEEP_DIG_ICG_FUNC_EN_V << PMU_HP_SLEEP_DIG_ICG_FUNC_EN_S)
#define PMU_HP_SLEEP_DIG_ICG_FUNC_EN_V  0xFFFFFFFFU
#define PMU_HP_SLEEP_DIG_ICG_FUNC_EN_S  0

/** PMU_HP_SLEEP_ICG_HP_APB_REG register
 *  need_des
 */
#define PMU_HP_SLEEP_ICG_HP_APB_REG (DR_REG_PMU_BASE + 0x70)
/** PMU_HP_SLEEP_DIG_ICG_APB_EN : R/W; bitpos: [31:0]; default: 4294967295;
 *  need_des
 */
#define PMU_HP_SLEEP_DIG_ICG_APB_EN    0xFFFFFFFFU
#define PMU_HP_SLEEP_DIG_ICG_APB_EN_M  (PMU_HP_SLEEP_DIG_ICG_APB_EN_V << PMU_HP_SLEEP_DIG_ICG_APB_EN_S)
#define PMU_HP_SLEEP_DIG_ICG_APB_EN_V  0xFFFFFFFFU
#define PMU_HP_SLEEP_DIG_ICG_APB_EN_S  0

/** PMU_HP_SLEEP_ICG_MODEM_REG register
 *  need_des
 */
#define PMU_HP_SLEEP_ICG_MODEM_REG (DR_REG_PMU_BASE + 0x74)
/** PMU_HP_SLEEP_DIG_ICG_MODEM_CODE : R/W; bitpos: [31:30]; default: 0;
 *  need_des
 */
#define PMU_HP_SLEEP_DIG_ICG_MODEM_CODE    0x00000003U
#define PMU_HP_SLEEP_DIG_ICG_MODEM_CODE_M  (PMU_HP_SLEEP_DIG_ICG_MODEM_CODE_V << PMU_HP_SLEEP_DIG_ICG_MODEM_CODE_S)
#define PMU_HP_SLEEP_DIG_ICG_MODEM_CODE_V  0x00000003U
#define PMU_HP_SLEEP_DIG_ICG_MODEM_CODE_S  30

/** PMU_HP_SLEEP_HP_SYS_CNTL_REG register
 *  need_des
 */
#define PMU_HP_SLEEP_HP_SYS_CNTL_REG (DR_REG_PMU_BASE + 0x78)
/** PMU_HP_SLEEP_HP_POWER_DET_BYPASS : R/W; bitpos: [23]; default: 0;
 *  need_des
 */
#define PMU_HP_SLEEP_HP_POWER_DET_BYPASS    (BIT(23))
#define PMU_HP_SLEEP_HP_POWER_DET_BYPASS_M  (PMU_HP_SLEEP_HP_POWER_DET_BYPASS_V << PMU_HP_SLEEP_HP_POWER_DET_BYPASS_S)
#define PMU_HP_SLEEP_HP_POWER_DET_BYPASS_V  0x00000001U
#define PMU_HP_SLEEP_HP_POWER_DET_BYPASS_S  23
/** PMU_HP_SLEEP_UART_WAKEUP_EN : R/W; bitpos: [24]; default: 0;
 *  need_des
 */
#define PMU_HP_SLEEP_UART_WAKEUP_EN    (BIT(24))
#define PMU_HP_SLEEP_UART_WAKEUP_EN_M  (PMU_HP_SLEEP_UART_WAKEUP_EN_V << PMU_HP_SLEEP_UART_WAKEUP_EN_S)
#define PMU_HP_SLEEP_UART_WAKEUP_EN_V  0x00000001U
#define PMU_HP_SLEEP_UART_WAKEUP_EN_S  24
/** PMU_HP_SLEEP_LP_PAD_HOLD_ALL : R/W; bitpos: [25]; default: 0;
 *  need_des
 */
#define PMU_HP_SLEEP_LP_PAD_HOLD_ALL    (BIT(25))
#define PMU_HP_SLEEP_LP_PAD_HOLD_ALL_M  (PMU_HP_SLEEP_LP_PAD_HOLD_ALL_V << PMU_HP_SLEEP_LP_PAD_HOLD_ALL_S)
#define PMU_HP_SLEEP_LP_PAD_HOLD_ALL_V  0x00000001U
#define PMU_HP_SLEEP_LP_PAD_HOLD_ALL_S  25
/** PMU_HP_SLEEP_HP_PAD_HOLD_ALL : R/W; bitpos: [26]; default: 0;
 *  need_des
 */
#define PMU_HP_SLEEP_HP_PAD_HOLD_ALL    (BIT(26))
#define PMU_HP_SLEEP_HP_PAD_HOLD_ALL_M  (PMU_HP_SLEEP_HP_PAD_HOLD_ALL_V << PMU_HP_SLEEP_HP_PAD_HOLD_ALL_S)
#define PMU_HP_SLEEP_HP_PAD_HOLD_ALL_V  0x00000001U
#define PMU_HP_SLEEP_HP_PAD_HOLD_ALL_S  26
/** PMU_HP_SLEEP_DIG_PAD_SLP_SEL : R/W; bitpos: [27]; default: 0;
 *  need_des
 */
#define PMU_HP_SLEEP_DIG_PAD_SLP_SEL    (BIT(27))
#define PMU_HP_SLEEP_DIG_PAD_SLP_SEL_M  (PMU_HP_SLEEP_DIG_PAD_SLP_SEL_V << PMU_HP_SLEEP_DIG_PAD_SLP_SEL_S)
#define PMU_HP_SLEEP_DIG_PAD_SLP_SEL_V  0x00000001U
#define PMU_HP_SLEEP_DIG_PAD_SLP_SEL_S  27
/** PMU_HP_SLEEP_DIG_PAUSE_WDT : R/W; bitpos: [28]; default: 0;
 *  need_des
 */
#define PMU_HP_SLEEP_DIG_PAUSE_WDT    (BIT(28))
#define PMU_HP_SLEEP_DIG_PAUSE_WDT_M  (PMU_HP_SLEEP_DIG_PAUSE_WDT_V << PMU_HP_SLEEP_DIG_PAUSE_WDT_S)
#define PMU_HP_SLEEP_DIG_PAUSE_WDT_V  0x00000001U
#define PMU_HP_SLEEP_DIG_PAUSE_WDT_S  28
/** PMU_HP_SLEEP_DIG_CPU_STALL : R/W; bitpos: [29]; default: 0;
 *  need_des
 */
#define PMU_HP_SLEEP_DIG_CPU_STALL    (BIT(29))
#define PMU_HP_SLEEP_DIG_CPU_STALL_M  (PMU_HP_SLEEP_DIG_CPU_STALL_V << PMU_HP_SLEEP_DIG_CPU_STALL_S)
#define PMU_HP_SLEEP_DIG_CPU_STALL_V  0x00000001U
#define PMU_HP_SLEEP_DIG_CPU_STALL_S  29

/** PMU_HP_SLEEP_HP_CK_POWER_REG register
 *  need_des
 */
#define PMU_HP_SLEEP_HP_CK_POWER_REG (DR_REG_PMU_BASE + 0x7c)
/** PMU_HP_SLEEP_I2C_ISO_EN : R/W; bitpos: [21]; default: 0;
 *  need_des
 */
#define PMU_HP_SLEEP_I2C_ISO_EN    (BIT(21))
#define PMU_HP_SLEEP_I2C_ISO_EN_M  (PMU_HP_SLEEP_I2C_ISO_EN_V << PMU_HP_SLEEP_I2C_ISO_EN_S)
#define PMU_HP_SLEEP_I2C_ISO_EN_V  0x00000001U
#define PMU_HP_SLEEP_I2C_ISO_EN_S  21
/** PMU_HP_SLEEP_I2C_RETENTION : R/W; bitpos: [22]; default: 0;
 *  need_des
 */
#define PMU_HP_SLEEP_I2C_RETENTION    (BIT(22))
#define PMU_HP_SLEEP_I2C_RETENTION_M  (PMU_HP_SLEEP_I2C_RETENTION_V << PMU_HP_SLEEP_I2C_RETENTION_S)
#define PMU_HP_SLEEP_I2C_RETENTION_V  0x00000001U
#define PMU_HP_SLEEP_I2C_RETENTION_S  22
/** PMU_HP_SLEEP_XPD_PLL_I2C : R/W; bitpos: [26:23]; default: 0;
 *  need_des
 */
#define PMU_HP_SLEEP_XPD_PLL_I2C    0x0000000FU
#define PMU_HP_SLEEP_XPD_PLL_I2C_M  (PMU_HP_SLEEP_XPD_PLL_I2C_V << PMU_HP_SLEEP_XPD_PLL_I2C_S)
#define PMU_HP_SLEEP_XPD_PLL_I2C_V  0x0000000FU
#define PMU_HP_SLEEP_XPD_PLL_I2C_S  23
/** PMU_HP_SLEEP_XPD_PLL : R/W; bitpos: [30:27]; default: 0;
 *  need_des
 */
#define PMU_HP_SLEEP_XPD_PLL    0x0000000FU
#define PMU_HP_SLEEP_XPD_PLL_M  (PMU_HP_SLEEP_XPD_PLL_V << PMU_HP_SLEEP_XPD_PLL_S)
#define PMU_HP_SLEEP_XPD_PLL_V  0x0000000FU
#define PMU_HP_SLEEP_XPD_PLL_S  27

/** PMU_HP_SLEEP_BIAS_REG register
 *  need_des
 */
#define PMU_HP_SLEEP_BIAS_REG (DR_REG_PMU_BASE + 0x80)
/** PMU_HP_SLEEP_DCM_VSET : R/W; bitpos: [22:18]; default: 20;
 *  need_des
 */
#define PMU_HP_SLEEP_DCM_VSET    0x0000001FU
#define PMU_HP_SLEEP_DCM_VSET_M  (PMU_HP_SLEEP_DCM_VSET_V << PMU_HP_SLEEP_DCM_VSET_S)
#define PMU_HP_SLEEP_DCM_VSET_V  0x0000001FU
#define PMU_HP_SLEEP_DCM_VSET_S  18
/** PMU_HP_SLEEP_DCM_MODE : R/W; bitpos: [24:23]; default: 0;
 *  need_des
 */
#define PMU_HP_SLEEP_DCM_MODE    0x00000003U
#define PMU_HP_SLEEP_DCM_MODE_M  (PMU_HP_SLEEP_DCM_MODE_V << PMU_HP_SLEEP_DCM_MODE_S)
#define PMU_HP_SLEEP_DCM_MODE_V  0x00000003U
#define PMU_HP_SLEEP_DCM_MODE_S  23
/** PMU_HP_SLEEP_XPD_BIAS : R/W; bitpos: [25]; default: 0;
 *  need_des
 */
#define PMU_HP_SLEEP_XPD_BIAS    (BIT(25))
#define PMU_HP_SLEEP_XPD_BIAS_M  (PMU_HP_SLEEP_XPD_BIAS_V << PMU_HP_SLEEP_XPD_BIAS_S)
#define PMU_HP_SLEEP_XPD_BIAS_V  0x00000001U
#define PMU_HP_SLEEP_XPD_BIAS_S  25
/** PMU_HP_SLEEP_DBG_ATTEN : R/W; bitpos: [29:26]; default: 0;
 *  need_des
 */
#define PMU_HP_SLEEP_DBG_ATTEN    0x0000000FU
#define PMU_HP_SLEEP_DBG_ATTEN_M  (PMU_HP_SLEEP_DBG_ATTEN_V << PMU_HP_SLEEP_DBG_ATTEN_S)
#define PMU_HP_SLEEP_DBG_ATTEN_V  0x0000000FU
#define PMU_HP_SLEEP_DBG_ATTEN_S  26
/** PMU_HP_SLEEP_PD_CUR : R/W; bitpos: [30]; default: 0;
 *  need_des
 */
#define PMU_HP_SLEEP_PD_CUR    (BIT(30))
#define PMU_HP_SLEEP_PD_CUR_M  (PMU_HP_SLEEP_PD_CUR_V << PMU_HP_SLEEP_PD_CUR_S)
#define PMU_HP_SLEEP_PD_CUR_V  0x00000001U
#define PMU_HP_SLEEP_PD_CUR_S  30
/** PMU_HP_SLEEP_BIAS_SLEEP : R/W; bitpos: [31]; default: 0;
 *  need_des
 */
#define PMU_HP_SLEEP_BIAS_SLEEP    (BIT(31))
#define PMU_HP_SLEEP_BIAS_SLEEP_M  (PMU_HP_SLEEP_BIAS_SLEEP_V << PMU_HP_SLEEP_BIAS_SLEEP_S)
#define PMU_HP_SLEEP_BIAS_SLEEP_V  0x00000001U
#define PMU_HP_SLEEP_BIAS_SLEEP_S  31

/** PMU_HP_SLEEP_BACKUP_REG register
 *  need_des
 */
#define PMU_HP_SLEEP_BACKUP_REG (DR_REG_PMU_BASE + 0x84)
/** PMU_HP_MODEM2SLEEP_BACKUP_MODEM_CLK_CODE : R/W; bitpos: [7:6]; default: 0;
 *  need_des
 */
#define PMU_HP_MODEM2SLEEP_BACKUP_MODEM_CLK_CODE    0x00000003U
#define PMU_HP_MODEM2SLEEP_BACKUP_MODEM_CLK_CODE_M  (PMU_HP_MODEM2SLEEP_BACKUP_MODEM_CLK_CODE_V << PMU_HP_MODEM2SLEEP_BACKUP_MODEM_CLK_CODE_S)
#define PMU_HP_MODEM2SLEEP_BACKUP_MODEM_CLK_CODE_V  0x00000003U
#define PMU_HP_MODEM2SLEEP_BACKUP_MODEM_CLK_CODE_S  6
/** PMU_HP_ACTIVE2SLEEP_BACKUP_MODEM_CLK_CODE : R/W; bitpos: [9:8]; default: 0;
 *  need_des
 */
#define PMU_HP_ACTIVE2SLEEP_BACKUP_MODEM_CLK_CODE    0x00000003U
#define PMU_HP_ACTIVE2SLEEP_BACKUP_MODEM_CLK_CODE_M  (PMU_HP_ACTIVE2SLEEP_BACKUP_MODEM_CLK_CODE_V << PMU_HP_ACTIVE2SLEEP_BACKUP_MODEM_CLK_CODE_S)
#define PMU_HP_ACTIVE2SLEEP_BACKUP_MODEM_CLK_CODE_V  0x00000003U
#define PMU_HP_ACTIVE2SLEEP_BACKUP_MODEM_CLK_CODE_S  8
/** PMU_HP_SLEEP_RETENTION_MODE : R/W; bitpos: [10]; default: 0;
 *  need_des
 */
#define PMU_HP_SLEEP_RETENTION_MODE    (BIT(10))
#define PMU_HP_SLEEP_RETENTION_MODE_M  (PMU_HP_SLEEP_RETENTION_MODE_V << PMU_HP_SLEEP_RETENTION_MODE_S)
#define PMU_HP_SLEEP_RETENTION_MODE_V  0x00000001U
#define PMU_HP_SLEEP_RETENTION_MODE_S  10
/** PMU_HP_MODEM2SLEEP_RETENTION_EN : R/W; bitpos: [12]; default: 0;
 *  need_des
 */
#define PMU_HP_MODEM2SLEEP_RETENTION_EN    (BIT(12))
#define PMU_HP_MODEM2SLEEP_RETENTION_EN_M  (PMU_HP_MODEM2SLEEP_RETENTION_EN_V << PMU_HP_MODEM2SLEEP_RETENTION_EN_S)
#define PMU_HP_MODEM2SLEEP_RETENTION_EN_V  0x00000001U
#define PMU_HP_MODEM2SLEEP_RETENTION_EN_S  12
/** PMU_HP_ACTIVE2SLEEP_RETENTION_EN : R/W; bitpos: [13]; default: 0;
 *  need_des
 */
#define PMU_HP_ACTIVE2SLEEP_RETENTION_EN    (BIT(13))
#define PMU_HP_ACTIVE2SLEEP_RETENTION_EN_M  (PMU_HP_ACTIVE2SLEEP_RETENTION_EN_V << PMU_HP_ACTIVE2SLEEP_RETENTION_EN_S)
#define PMU_HP_ACTIVE2SLEEP_RETENTION_EN_V  0x00000001U
#define PMU_HP_ACTIVE2SLEEP_RETENTION_EN_S  13
/** PMU_HP_MODEM2SLEEP_BACKUP_CLK_SEL : R/W; bitpos: [17:16]; default: 0;
 *  need_des
 */
#define PMU_HP_MODEM2SLEEP_BACKUP_CLK_SEL    0x00000003U
#define PMU_HP_MODEM2SLEEP_BACKUP_CLK_SEL_M  (PMU_HP_MODEM2SLEEP_BACKUP_CLK_SEL_V << PMU_HP_MODEM2SLEEP_BACKUP_CLK_SEL_S)
#define PMU_HP_MODEM2SLEEP_BACKUP_CLK_SEL_V  0x00000003U
#define PMU_HP_MODEM2SLEEP_BACKUP_CLK_SEL_S  16
/** PMU_HP_ACTIVE2SLEEP_BACKUP_CLK_SEL : R/W; bitpos: [19:18]; default: 0;
 *  need_des
 */
#define PMU_HP_ACTIVE2SLEEP_BACKUP_CLK_SEL    0x00000003U
#define PMU_HP_ACTIVE2SLEEP_BACKUP_CLK_SEL_M  (PMU_HP_ACTIVE2SLEEP_BACKUP_CLK_SEL_V << PMU_HP_ACTIVE2SLEEP_BACKUP_CLK_SEL_S)
#define PMU_HP_ACTIVE2SLEEP_BACKUP_CLK_SEL_V  0x00000003U
#define PMU_HP_ACTIVE2SLEEP_BACKUP_CLK_SEL_S  18
/** PMU_HP_MODEM2SLEEP_BACKUP_MODE : R/W; bitpos: [25:23]; default: 0;
 *  need_des
 */
#define PMU_HP_MODEM2SLEEP_BACKUP_MODE    0x00000007U
#define PMU_HP_MODEM2SLEEP_BACKUP_MODE_M  (PMU_HP_MODEM2SLEEP_BACKUP_MODE_V << PMU_HP_MODEM2SLEEP_BACKUP_MODE_S)
#define PMU_HP_MODEM2SLEEP_BACKUP_MODE_V  0x00000007U
#define PMU_HP_MODEM2SLEEP_BACKUP_MODE_S  23
/** PMU_HP_ACTIVE2SLEEP_BACKUP_MODE : R/W; bitpos: [28:26]; default: 0;
 *  need_des
 */
#define PMU_HP_ACTIVE2SLEEP_BACKUP_MODE    0x00000007U
#define PMU_HP_ACTIVE2SLEEP_BACKUP_MODE_M  (PMU_HP_ACTIVE2SLEEP_BACKUP_MODE_V << PMU_HP_ACTIVE2SLEEP_BACKUP_MODE_S)
#define PMU_HP_ACTIVE2SLEEP_BACKUP_MODE_V  0x00000007U
#define PMU_HP_ACTIVE2SLEEP_BACKUP_MODE_S  26
/** PMU_HP_MODEM2SLEEP_BACKUP_EN : R/W; bitpos: [30]; default: 0;
 *  need_des
 */
#define PMU_HP_MODEM2SLEEP_BACKUP_EN    (BIT(30))
#define PMU_HP_MODEM2SLEEP_BACKUP_EN_M  (PMU_HP_MODEM2SLEEP_BACKUP_EN_V << PMU_HP_MODEM2SLEEP_BACKUP_EN_S)
#define PMU_HP_MODEM2SLEEP_BACKUP_EN_V  0x00000001U
#define PMU_HP_MODEM2SLEEP_BACKUP_EN_S  30
/** PMU_HP_ACTIVE2SLEEP_BACKUP_EN : R/W; bitpos: [31]; default: 0;
 *  need_des
 */
#define PMU_HP_ACTIVE2SLEEP_BACKUP_EN    (BIT(31))
#define PMU_HP_ACTIVE2SLEEP_BACKUP_EN_M  (PMU_HP_ACTIVE2SLEEP_BACKUP_EN_V << PMU_HP_ACTIVE2SLEEP_BACKUP_EN_S)
#define PMU_HP_ACTIVE2SLEEP_BACKUP_EN_V  0x00000001U
#define PMU_HP_ACTIVE2SLEEP_BACKUP_EN_S  31

/** PMU_HP_SLEEP_BACKUP_CLK_REG register
 *  need_des
 */
#define PMU_HP_SLEEP_BACKUP_CLK_REG (DR_REG_PMU_BASE + 0x88)
/** PMU_HP_SLEEP_BACKUP_ICG_FUNC_EN : R/W; bitpos: [31:0]; default: 0;
 *  need_des
 */
#define PMU_HP_SLEEP_BACKUP_ICG_FUNC_EN    0xFFFFFFFFU
#define PMU_HP_SLEEP_BACKUP_ICG_FUNC_EN_M  (PMU_HP_SLEEP_BACKUP_ICG_FUNC_EN_V << PMU_HP_SLEEP_BACKUP_ICG_FUNC_EN_S)
#define PMU_HP_SLEEP_BACKUP_ICG_FUNC_EN_V  0xFFFFFFFFU
#define PMU_HP_SLEEP_BACKUP_ICG_FUNC_EN_S  0

/** PMU_HP_SLEEP_SYSCLK_REG register
 *  need_des
 */
#define PMU_HP_SLEEP_SYSCLK_REG (DR_REG_PMU_BASE + 0x8c)
/** PMU_HP_SLEEP_DIG_SYS_CLK_NO_DIV : R/W; bitpos: [26]; default: 0;
 *  need_des
 */
#define PMU_HP_SLEEP_DIG_SYS_CLK_NO_DIV    (BIT(26))
#define PMU_HP_SLEEP_DIG_SYS_CLK_NO_DIV_M  (PMU_HP_SLEEP_DIG_SYS_CLK_NO_DIV_V << PMU_HP_SLEEP_DIG_SYS_CLK_NO_DIV_S)
#define PMU_HP_SLEEP_DIG_SYS_CLK_NO_DIV_V  0x00000001U
#define PMU_HP_SLEEP_DIG_SYS_CLK_NO_DIV_S  26
/** PMU_HP_SLEEP_ICG_SYS_CLOCK_EN : R/W; bitpos: [27]; default: 0;
 *  need_des
 */
#define PMU_HP_SLEEP_ICG_SYS_CLOCK_EN    (BIT(27))
#define PMU_HP_SLEEP_ICG_SYS_CLOCK_EN_M  (PMU_HP_SLEEP_ICG_SYS_CLOCK_EN_V << PMU_HP_SLEEP_ICG_SYS_CLOCK_EN_S)
#define PMU_HP_SLEEP_ICG_SYS_CLOCK_EN_V  0x00000001U
#define PMU_HP_SLEEP_ICG_SYS_CLOCK_EN_S  27
/** PMU_HP_SLEEP_SYS_CLK_SLP_SEL : R/W; bitpos: [28]; default: 0;
 *  need_des
 */
#define PMU_HP_SLEEP_SYS_CLK_SLP_SEL    (BIT(28))
#define PMU_HP_SLEEP_SYS_CLK_SLP_SEL_M  (PMU_HP_SLEEP_SYS_CLK_SLP_SEL_V << PMU_HP_SLEEP_SYS_CLK_SLP_SEL_S)
#define PMU_HP_SLEEP_SYS_CLK_SLP_SEL_V  0x00000001U
#define PMU_HP_SLEEP_SYS_CLK_SLP_SEL_S  28
/** PMU_HP_SLEEP_ICG_SLP_SEL : R/W; bitpos: [29]; default: 0;
 *  need_des
 */
#define PMU_HP_SLEEP_ICG_SLP_SEL    (BIT(29))
#define PMU_HP_SLEEP_ICG_SLP_SEL_M  (PMU_HP_SLEEP_ICG_SLP_SEL_V << PMU_HP_SLEEP_ICG_SLP_SEL_S)
#define PMU_HP_SLEEP_ICG_SLP_SEL_V  0x00000001U
#define PMU_HP_SLEEP_ICG_SLP_SEL_S  29
/** PMU_HP_SLEEP_DIG_SYS_CLK_SEL : R/W; bitpos: [31:30]; default: 0;
 *  need_des
 */
#define PMU_HP_SLEEP_DIG_SYS_CLK_SEL    0x00000003U
#define PMU_HP_SLEEP_DIG_SYS_CLK_SEL_M  (PMU_HP_SLEEP_DIG_SYS_CLK_SEL_V << PMU_HP_SLEEP_DIG_SYS_CLK_SEL_S)
#define PMU_HP_SLEEP_DIG_SYS_CLK_SEL_V  0x00000003U
#define PMU_HP_SLEEP_DIG_SYS_CLK_SEL_S  30

/** PMU_HP_SLEEP_HP_REGULATOR0_REG register
 *  need_des
 */
#define PMU_HP_SLEEP_HP_REGULATOR0_REG (DR_REG_PMU_BASE + 0x90)
/** PMU_HP_SLEEP_HP_REGULATOR_SLP_MEM_XPD : R/W; bitpos: [16]; default: 1;
 *  need_des
 */
#define PMU_HP_SLEEP_HP_REGULATOR_SLP_MEM_XPD    (BIT(16))
#define PMU_HP_SLEEP_HP_REGULATOR_SLP_MEM_XPD_M  (PMU_HP_SLEEP_HP_REGULATOR_SLP_MEM_XPD_V << PMU_HP_SLEEP_HP_REGULATOR_SLP_MEM_XPD_S)
#define PMU_HP_SLEEP_HP_REGULATOR_SLP_MEM_XPD_V  0x00000001U
#define PMU_HP_SLEEP_HP_REGULATOR_SLP_MEM_XPD_S  16
/** PMU_HP_SLEEP_HP_REGULATOR_SLP_LOGIC_XPD : R/W; bitpos: [17]; default: 1;
 *  need_des
 */
#define PMU_HP_SLEEP_HP_REGULATOR_SLP_LOGIC_XPD    (BIT(17))
#define PMU_HP_SLEEP_HP_REGULATOR_SLP_LOGIC_XPD_M  (PMU_HP_SLEEP_HP_REGULATOR_SLP_LOGIC_XPD_V << PMU_HP_SLEEP_HP_REGULATOR_SLP_LOGIC_XPD_S)
#define PMU_HP_SLEEP_HP_REGULATOR_SLP_LOGIC_XPD_V  0x00000001U
#define PMU_HP_SLEEP_HP_REGULATOR_SLP_LOGIC_XPD_S  17
/** PMU_HP_SLEEP_HP_REGULATOR_XPD : R/W; bitpos: [18]; default: 1;
 *  need_des
 */
#define PMU_HP_SLEEP_HP_REGULATOR_XPD    (BIT(18))
#define PMU_HP_SLEEP_HP_REGULATOR_XPD_M  (PMU_HP_SLEEP_HP_REGULATOR_XPD_V << PMU_HP_SLEEP_HP_REGULATOR_XPD_S)
#define PMU_HP_SLEEP_HP_REGULATOR_XPD_V  0x00000001U
#define PMU_HP_SLEEP_HP_REGULATOR_XPD_S  18
/** PMU_HP_SLEEP_HP_REGULATOR_SLP_MEM_DBIAS : R/W; bitpos: [22:19]; default: 12;
 *  need_des
 */
#define PMU_HP_SLEEP_HP_REGULATOR_SLP_MEM_DBIAS    0x0000000FU
#define PMU_HP_SLEEP_HP_REGULATOR_SLP_MEM_DBIAS_M  (PMU_HP_SLEEP_HP_REGULATOR_SLP_MEM_DBIAS_V << PMU_HP_SLEEP_HP_REGULATOR_SLP_MEM_DBIAS_S)
#define PMU_HP_SLEEP_HP_REGULATOR_SLP_MEM_DBIAS_V  0x0000000FU
#define PMU_HP_SLEEP_HP_REGULATOR_SLP_MEM_DBIAS_S  19
/** PMU_HP_SLEEP_HP_REGULATOR_SLP_LOGIC_DBIAS : R/W; bitpos: [26:23]; default: 12;
 *  need_des
 */
#define PMU_HP_SLEEP_HP_REGULATOR_SLP_LOGIC_DBIAS    0x0000000FU
#define PMU_HP_SLEEP_HP_REGULATOR_SLP_LOGIC_DBIAS_M  (PMU_HP_SLEEP_HP_REGULATOR_SLP_LOGIC_DBIAS_V << PMU_HP_SLEEP_HP_REGULATOR_SLP_LOGIC_DBIAS_S)
#define PMU_HP_SLEEP_HP_REGULATOR_SLP_LOGIC_DBIAS_V  0x0000000FU
#define PMU_HP_SLEEP_HP_REGULATOR_SLP_LOGIC_DBIAS_S  23
/** PMU_HP_SLEEP_HP_REGULATOR_DBIAS : R/W; bitpos: [31:27]; default: 24;
 *  need_des
 */
#define PMU_HP_SLEEP_HP_REGULATOR_DBIAS    0x0000001FU
#define PMU_HP_SLEEP_HP_REGULATOR_DBIAS_M  (PMU_HP_SLEEP_HP_REGULATOR_DBIAS_V << PMU_HP_SLEEP_HP_REGULATOR_DBIAS_S)
#define PMU_HP_SLEEP_HP_REGULATOR_DBIAS_V  0x0000001FU
#define PMU_HP_SLEEP_HP_REGULATOR_DBIAS_S  27

/** PMU_HP_SLEEP_HP_REGULATOR1_REG register
 *  need_des
 */
#define PMU_HP_SLEEP_HP_REGULATOR1_REG (DR_REG_PMU_BASE + 0x94)
/** PMU_HP_SLEEP_HP_REGULATOR_DRV_B : R/W; bitpos: [31:26]; default: 0;
 *  need_des
 */
#define PMU_HP_SLEEP_HP_REGULATOR_DRV_B    0x0000003FU
#define PMU_HP_SLEEP_HP_REGULATOR_DRV_B_M  (PMU_HP_SLEEP_HP_REGULATOR_DRV_B_V << PMU_HP_SLEEP_HP_REGULATOR_DRV_B_S)
#define PMU_HP_SLEEP_HP_REGULATOR_DRV_B_V  0x0000003FU
#define PMU_HP_SLEEP_HP_REGULATOR_DRV_B_S  26

/** PMU_HP_SLEEP_XTAL_REG register
 *  need_des
 */
#define PMU_HP_SLEEP_XTAL_REG (DR_REG_PMU_BASE + 0x98)
/** PMU_HP_SLEEP_XPD_XTAL : R/W; bitpos: [31]; default: 1;
 *  need_des
 */
#define PMU_HP_SLEEP_XPD_XTAL    (BIT(31))
#define PMU_HP_SLEEP_XPD_XTAL_M  (PMU_HP_SLEEP_XPD_XTAL_V << PMU_HP_SLEEP_XPD_XTAL_S)
#define PMU_HP_SLEEP_XPD_XTAL_V  0x00000001U
#define PMU_HP_SLEEP_XPD_XTAL_S  31

/** PMU_HP_SLEEP_LP_REGULATOR0_REG register
 *  need_des
 */
#define PMU_HP_SLEEP_LP_REGULATOR0_REG (DR_REG_PMU_BASE + 0x9c)
/** PMU_HP_SLEEP_LP_REGULATOR_SLP_XPD : R/W; bitpos: [21]; default: 1;
 *  need_des
 */
#define PMU_HP_SLEEP_LP_REGULATOR_SLP_XPD    (BIT(21))
#define PMU_HP_SLEEP_LP_REGULATOR_SLP_XPD_M  (PMU_HP_SLEEP_LP_REGULATOR_SLP_XPD_V << PMU_HP_SLEEP_LP_REGULATOR_SLP_XPD_S)
#define PMU_HP_SLEEP_LP_REGULATOR_SLP_XPD_V  0x00000001U
#define PMU_HP_SLEEP_LP_REGULATOR_SLP_XPD_S  21
/** PMU_HP_SLEEP_LP_REGULATOR_XPD : R/W; bitpos: [22]; default: 1;
 *  need_des
 */
#define PMU_HP_SLEEP_LP_REGULATOR_XPD    (BIT(22))
#define PMU_HP_SLEEP_LP_REGULATOR_XPD_M  (PMU_HP_SLEEP_LP_REGULATOR_XPD_V << PMU_HP_SLEEP_LP_REGULATOR_XPD_S)
#define PMU_HP_SLEEP_LP_REGULATOR_XPD_V  0x00000001U
#define PMU_HP_SLEEP_LP_REGULATOR_XPD_S  22
/** PMU_HP_SLEEP_LP_REGULATOR_SLP_DBIAS : R/W; bitpos: [26:23]; default: 12;
 *  need_des
 */
#define PMU_HP_SLEEP_LP_REGULATOR_SLP_DBIAS    0x0000000FU
#define PMU_HP_SLEEP_LP_REGULATOR_SLP_DBIAS_M  (PMU_HP_SLEEP_LP_REGULATOR_SLP_DBIAS_V << PMU_HP_SLEEP_LP_REGULATOR_SLP_DBIAS_S)
#define PMU_HP_SLEEP_LP_REGULATOR_SLP_DBIAS_V  0x0000000FU
#define PMU_HP_SLEEP_LP_REGULATOR_SLP_DBIAS_S  23
/** PMU_HP_SLEEP_LP_REGULATOR_DBIAS : R/W; bitpos: [31:27]; default: 24;
 *  need_des
 */
#define PMU_HP_SLEEP_LP_REGULATOR_DBIAS    0x0000001FU
#define PMU_HP_SLEEP_LP_REGULATOR_DBIAS_M  (PMU_HP_SLEEP_LP_REGULATOR_DBIAS_V << PMU_HP_SLEEP_LP_REGULATOR_DBIAS_S)
#define PMU_HP_SLEEP_LP_REGULATOR_DBIAS_V  0x0000001FU
#define PMU_HP_SLEEP_LP_REGULATOR_DBIAS_S  27

/** PMU_HP_SLEEP_LP_REGULATOR1_REG register
 *  need_des
 */
#define PMU_HP_SLEEP_LP_REGULATOR1_REG (DR_REG_PMU_BASE + 0xa0)
/** PMU_HP_SLEEP_LP_REGULATOR_DRV_B : R/W; bitpos: [31:26]; default: 0;
 *  need_des
 */
#define PMU_HP_SLEEP_LP_REGULATOR_DRV_B    0x0000003FU
#define PMU_HP_SLEEP_LP_REGULATOR_DRV_B_M  (PMU_HP_SLEEP_LP_REGULATOR_DRV_B_V << PMU_HP_SLEEP_LP_REGULATOR_DRV_B_S)
#define PMU_HP_SLEEP_LP_REGULATOR_DRV_B_V  0x0000003FU
#define PMU_HP_SLEEP_LP_REGULATOR_DRV_B_S  26

/** PMU_HP_SLEEP_LP_DIG_POWER_REG register
 *  need_des
 */
#define PMU_HP_SLEEP_LP_DIG_POWER_REG (DR_REG_PMU_BASE + 0xa8)
/** PMU_HP_SLEEP_LP_PAD_SLP_SEL : R/W; bitpos: [26]; default: 0;
 *  need_des
 */
#define PMU_HP_SLEEP_LP_PAD_SLP_SEL    (BIT(26))
#define PMU_HP_SLEEP_LP_PAD_SLP_SEL_M  (PMU_HP_SLEEP_LP_PAD_SLP_SEL_V << PMU_HP_SLEEP_LP_PAD_SLP_SEL_S)
#define PMU_HP_SLEEP_LP_PAD_SLP_SEL_V  0x00000001U
#define PMU_HP_SLEEP_LP_PAD_SLP_SEL_S  26
/** PMU_HP_SLEEP_BOD_SOURCE_SEL : R/W; bitpos: [27]; default: 0;
 *  need_des
 */
#define PMU_HP_SLEEP_BOD_SOURCE_SEL    (BIT(27))
#define PMU_HP_SLEEP_BOD_SOURCE_SEL_M  (PMU_HP_SLEEP_BOD_SOURCE_SEL_V << PMU_HP_SLEEP_BOD_SOURCE_SEL_S)
#define PMU_HP_SLEEP_BOD_SOURCE_SEL_V  0x00000001U
#define PMU_HP_SLEEP_BOD_SOURCE_SEL_S  27
/** PMU_HP_SLEEP_VDDBAT_MODE : R/W; bitpos: [29:28]; default: 0;
 *  need_des
 */
#define PMU_HP_SLEEP_VDDBAT_MODE    0x00000003U
#define PMU_HP_SLEEP_VDDBAT_MODE_M  (PMU_HP_SLEEP_VDDBAT_MODE_V << PMU_HP_SLEEP_VDDBAT_MODE_S)
#define PMU_HP_SLEEP_VDDBAT_MODE_V  0x00000003U
#define PMU_HP_SLEEP_VDDBAT_MODE_S  28
/** PMU_HP_SLEEP_LP_MEM_DSLP : R/W; bitpos: [30]; default: 0;
 *  need_des
 */
#define PMU_HP_SLEEP_LP_MEM_DSLP    (BIT(30))
#define PMU_HP_SLEEP_LP_MEM_DSLP_M  (PMU_HP_SLEEP_LP_MEM_DSLP_V << PMU_HP_SLEEP_LP_MEM_DSLP_S)
#define PMU_HP_SLEEP_LP_MEM_DSLP_V  0x00000001U
#define PMU_HP_SLEEP_LP_MEM_DSLP_S  30
/** PMU_HP_SLEEP_PD_LP_PERI_PD_EN : R/W; bitpos: [31]; default: 0;
 *  need_des
 */
#define PMU_HP_SLEEP_PD_LP_PERI_PD_EN    (BIT(31))
#define PMU_HP_SLEEP_PD_LP_PERI_PD_EN_M  (PMU_HP_SLEEP_PD_LP_PERI_PD_EN_V << PMU_HP_SLEEP_PD_LP_PERI_PD_EN_S)
#define PMU_HP_SLEEP_PD_LP_PERI_PD_EN_V  0x00000001U
#define PMU_HP_SLEEP_PD_LP_PERI_PD_EN_S  31

/** PMU_HP_SLEEP_LP_CK_POWER_REG register
 *  need_des
 */
#define PMU_HP_SLEEP_LP_CK_POWER_REG (DR_REG_PMU_BASE + 0xac)
/** PMU_HP_SLEEP_XPD_LPPLL : R/W; bitpos: [27]; default: 0;
 *  need_des
 */
#define PMU_HP_SLEEP_XPD_LPPLL    (BIT(27))
#define PMU_HP_SLEEP_XPD_LPPLL_M  (PMU_HP_SLEEP_XPD_LPPLL_V << PMU_HP_SLEEP_XPD_LPPLL_S)
#define PMU_HP_SLEEP_XPD_LPPLL_V  0x00000001U
#define PMU_HP_SLEEP_XPD_LPPLL_S  27
/** PMU_HP_SLEEP_XPD_XTAL32K : R/W; bitpos: [28]; default: 0;
 *  need_des
 */
#define PMU_HP_SLEEP_XPD_XTAL32K    (BIT(28))
#define PMU_HP_SLEEP_XPD_XTAL32K_M  (PMU_HP_SLEEP_XPD_XTAL32K_V << PMU_HP_SLEEP_XPD_XTAL32K_S)
#define PMU_HP_SLEEP_XPD_XTAL32K_V  0x00000001U
#define PMU_HP_SLEEP_XPD_XTAL32K_S  28
/** PMU_HP_SLEEP_XPD_RC32K : R/W; bitpos: [29]; default: 0;
 *  need_des
 */
#define PMU_HP_SLEEP_XPD_RC32K    (BIT(29))
#define PMU_HP_SLEEP_XPD_RC32K_M  (PMU_HP_SLEEP_XPD_RC32K_V << PMU_HP_SLEEP_XPD_RC32K_S)
#define PMU_HP_SLEEP_XPD_RC32K_V  0x00000001U
#define PMU_HP_SLEEP_XPD_RC32K_S  29
/** PMU_HP_SLEEP_XPD_FOSC_CLK : R/W; bitpos: [30]; default: 1;
 *  need_des
 */
#define PMU_HP_SLEEP_XPD_FOSC_CLK    (BIT(30))
#define PMU_HP_SLEEP_XPD_FOSC_CLK_M  (PMU_HP_SLEEP_XPD_FOSC_CLK_V << PMU_HP_SLEEP_XPD_FOSC_CLK_S)
#define PMU_HP_SLEEP_XPD_FOSC_CLK_V  0x00000001U
#define PMU_HP_SLEEP_XPD_FOSC_CLK_S  30
/** PMU_HP_SLEEP_PD_OSC_CLK : R/W; bitpos: [31]; default: 0;
 *  need_des
 */
#define PMU_HP_SLEEP_PD_OSC_CLK    (BIT(31))
#define PMU_HP_SLEEP_PD_OSC_CLK_M  (PMU_HP_SLEEP_PD_OSC_CLK_V << PMU_HP_SLEEP_PD_OSC_CLK_S)
#define PMU_HP_SLEEP_PD_OSC_CLK_V  0x00000001U
#define PMU_HP_SLEEP_PD_OSC_CLK_S  31

/** PMU_LP_SLEEP_LP_REGULATOR0_REG register
 *  need_des
 */
#define PMU_LP_SLEEP_LP_REGULATOR0_REG (DR_REG_PMU_BASE + 0xb4)
/** PMU_LP_SLEEP_LP_REGULATOR_SLP_XPD : R/W; bitpos: [21]; default: 1;
 *  need_des
 */
#define PMU_LP_SLEEP_LP_REGULATOR_SLP_XPD    (BIT(21))
#define PMU_LP_SLEEP_LP_REGULATOR_SLP_XPD_M  (PMU_LP_SLEEP_LP_REGULATOR_SLP_XPD_V << PMU_LP_SLEEP_LP_REGULATOR_SLP_XPD_S)
#define PMU_LP_SLEEP_LP_REGULATOR_SLP_XPD_V  0x00000001U
#define PMU_LP_SLEEP_LP_REGULATOR_SLP_XPD_S  21
/** PMU_LP_SLEEP_LP_REGULATOR_XPD : R/W; bitpos: [22]; default: 1;
 *  need_des
 */
#define PMU_LP_SLEEP_LP_REGULATOR_XPD    (BIT(22))
#define PMU_LP_SLEEP_LP_REGULATOR_XPD_M  (PMU_LP_SLEEP_LP_REGULATOR_XPD_V << PMU_LP_SLEEP_LP_REGULATOR_XPD_S)
#define PMU_LP_SLEEP_LP_REGULATOR_XPD_V  0x00000001U
#define PMU_LP_SLEEP_LP_REGULATOR_XPD_S  22
/** PMU_LP_SLEEP_LP_REGULATOR_SLP_DBIAS : R/W; bitpos: [26:23]; default: 12;
 *  need_des
 */
#define PMU_LP_SLEEP_LP_REGULATOR_SLP_DBIAS    0x0000000FU
#define PMU_LP_SLEEP_LP_REGULATOR_SLP_DBIAS_M  (PMU_LP_SLEEP_LP_REGULATOR_SLP_DBIAS_V << PMU_LP_SLEEP_LP_REGULATOR_SLP_DBIAS_S)
#define PMU_LP_SLEEP_LP_REGULATOR_SLP_DBIAS_V  0x0000000FU
#define PMU_LP_SLEEP_LP_REGULATOR_SLP_DBIAS_S  23
/** PMU_LP_SLEEP_LP_REGULATOR_DBIAS : R/W; bitpos: [31:27]; default: 24;
 *  need_des
 */
#define PMU_LP_SLEEP_LP_REGULATOR_DBIAS    0x0000001FU
#define PMU_LP_SLEEP_LP_REGULATOR_DBIAS_M  (PMU_LP_SLEEP_LP_REGULATOR_DBIAS_V << PMU_LP_SLEEP_LP_REGULATOR_DBIAS_S)
#define PMU_LP_SLEEP_LP_REGULATOR_DBIAS_V  0x0000001FU
#define PMU_LP_SLEEP_LP_REGULATOR_DBIAS_S  27

/** PMU_LP_SLEEP_LP_REGULATOR1_REG register
 *  need_des
 */
#define PMU_LP_SLEEP_LP_REGULATOR1_REG (DR_REG_PMU_BASE + 0xb8)
/** PMU_LP_SLEEP_LP_REGULATOR_DRV_B : R/W; bitpos: [31:26]; default: 0;
 *  need_des
 */
#define PMU_LP_SLEEP_LP_REGULATOR_DRV_B    0x0000003FU
#define PMU_LP_SLEEP_LP_REGULATOR_DRV_B_M  (PMU_LP_SLEEP_LP_REGULATOR_DRV_B_V << PMU_LP_SLEEP_LP_REGULATOR_DRV_B_S)
#define PMU_LP_SLEEP_LP_REGULATOR_DRV_B_V  0x0000003FU
#define PMU_LP_SLEEP_LP_REGULATOR_DRV_B_S  26

/** PMU_LP_SLEEP_XTAL_REG register
 *  need_des
 */
#define PMU_LP_SLEEP_XTAL_REG (DR_REG_PMU_BASE + 0xbc)
/** PMU_LP_SLEEP_XPD_XTAL : R/W; bitpos: [31]; default: 1;
 *  need_des
 */
#define PMU_LP_SLEEP_XPD_XTAL    (BIT(31))
#define PMU_LP_SLEEP_XPD_XTAL_M  (PMU_LP_SLEEP_XPD_XTAL_V << PMU_LP_SLEEP_XPD_XTAL_S)
#define PMU_LP_SLEEP_XPD_XTAL_V  0x00000001U
#define PMU_LP_SLEEP_XPD_XTAL_S  31

/** PMU_LP_SLEEP_LP_DIG_POWER_REG register
 *  need_des
 */
#define PMU_LP_SLEEP_LP_DIG_POWER_REG (DR_REG_PMU_BASE + 0xc0)
/** PMU_LP_SLEEP_LP_PAD_SLP_SEL : R/W; bitpos: [26]; default: 0;
 *  need_des
 */
#define PMU_LP_SLEEP_LP_PAD_SLP_SEL    (BIT(26))
#define PMU_LP_SLEEP_LP_PAD_SLP_SEL_M  (PMU_LP_SLEEP_LP_PAD_SLP_SEL_V << PMU_LP_SLEEP_LP_PAD_SLP_SEL_S)
#define PMU_LP_SLEEP_LP_PAD_SLP_SEL_V  0x00000001U
#define PMU_LP_SLEEP_LP_PAD_SLP_SEL_S  26
/** PMU_LP_SLEEP_BOD_SOURCE_SEL : R/W; bitpos: [27]; default: 0;
 *  need_des
 */
#define PMU_LP_SLEEP_BOD_SOURCE_SEL    (BIT(27))
#define PMU_LP_SLEEP_BOD_SOURCE_SEL_M  (PMU_LP_SLEEP_BOD_SOURCE_SEL_V << PMU_LP_SLEEP_BOD_SOURCE_SEL_S)
#define PMU_LP_SLEEP_BOD_SOURCE_SEL_V  0x00000001U
#define PMU_LP_SLEEP_BOD_SOURCE_SEL_S  27
/** PMU_LP_SLEEP_VDDBAT_MODE : R/W; bitpos: [29:28]; default: 0;
 *  need_des
 */
#define PMU_LP_SLEEP_VDDBAT_MODE    0x00000003U
#define PMU_LP_SLEEP_VDDBAT_MODE_M  (PMU_LP_SLEEP_VDDBAT_MODE_V << PMU_LP_SLEEP_VDDBAT_MODE_S)
#define PMU_LP_SLEEP_VDDBAT_MODE_V  0x00000003U
#define PMU_LP_SLEEP_VDDBAT_MODE_S  28
/** PMU_LP_SLEEP_LP_MEM_DSLP : R/W; bitpos: [30]; default: 0;
 *  need_des
 */
#define PMU_LP_SLEEP_LP_MEM_DSLP    (BIT(30))
#define PMU_LP_SLEEP_LP_MEM_DSLP_M  (PMU_LP_SLEEP_LP_MEM_DSLP_V << PMU_LP_SLEEP_LP_MEM_DSLP_S)
#define PMU_LP_SLEEP_LP_MEM_DSLP_V  0x00000001U
#define PMU_LP_SLEEP_LP_MEM_DSLP_S  30
/** PMU_LP_SLEEP_PD_LP_PERI_PD_EN : R/W; bitpos: [31]; default: 0;
 *  need_des
 */
#define PMU_LP_SLEEP_PD_LP_PERI_PD_EN    (BIT(31))
#define PMU_LP_SLEEP_PD_LP_PERI_PD_EN_M  (PMU_LP_SLEEP_PD_LP_PERI_PD_EN_V << PMU_LP_SLEEP_PD_LP_PERI_PD_EN_S)
#define PMU_LP_SLEEP_PD_LP_PERI_PD_EN_V  0x00000001U
#define PMU_LP_SLEEP_PD_LP_PERI_PD_EN_S  31

/** PMU_LP_SLEEP_LP_CK_POWER_REG register
 *  need_des
 */
#define PMU_LP_SLEEP_LP_CK_POWER_REG (DR_REG_PMU_BASE + 0xc4)
/** PMU_LP_SLEEP_XPD_LPPLL : R/W; bitpos: [27]; default: 0;
 *  need_des
 */
#define PMU_LP_SLEEP_XPD_LPPLL    (BIT(27))
#define PMU_LP_SLEEP_XPD_LPPLL_M  (PMU_LP_SLEEP_XPD_LPPLL_V << PMU_LP_SLEEP_XPD_LPPLL_S)
#define PMU_LP_SLEEP_XPD_LPPLL_V  0x00000001U
#define PMU_LP_SLEEP_XPD_LPPLL_S  27
/** PMU_LP_SLEEP_XPD_XTAL32K : R/W; bitpos: [28]; default: 0;
 *  need_des
 */
#define PMU_LP_SLEEP_XPD_XTAL32K    (BIT(28))
#define PMU_LP_SLEEP_XPD_XTAL32K_M  (PMU_LP_SLEEP_XPD_XTAL32K_V << PMU_LP_SLEEP_XPD_XTAL32K_S)
#define PMU_LP_SLEEP_XPD_XTAL32K_V  0x00000001U
#define PMU_LP_SLEEP_XPD_XTAL32K_S  28
/** PMU_LP_SLEEP_XPD_RC32K : R/W; bitpos: [29]; default: 0;
 *  need_des
 */
#define PMU_LP_SLEEP_XPD_RC32K    (BIT(29))
#define PMU_LP_SLEEP_XPD_RC32K_M  (PMU_LP_SLEEP_XPD_RC32K_V << PMU_LP_SLEEP_XPD_RC32K_S)
#define PMU_LP_SLEEP_XPD_RC32K_V  0x00000001U
#define PMU_LP_SLEEP_XPD_RC32K_S  29
/** PMU_LP_SLEEP_XPD_FOSC_CLK : R/W; bitpos: [30]; default: 1;
 *  need_des
 */
#define PMU_LP_SLEEP_XPD_FOSC_CLK    (BIT(30))
#define PMU_LP_SLEEP_XPD_FOSC_CLK_M  (PMU_LP_SLEEP_XPD_FOSC_CLK_V << PMU_LP_SLEEP_XPD_FOSC_CLK_S)
#define PMU_LP_SLEEP_XPD_FOSC_CLK_V  0x00000001U
#define PMU_LP_SLEEP_XPD_FOSC_CLK_S  30
/** PMU_LP_SLEEP_PD_OSC_CLK : R/W; bitpos: [31]; default: 0;
 *  need_des
 */
#define PMU_LP_SLEEP_PD_OSC_CLK    (BIT(31))
#define PMU_LP_SLEEP_PD_OSC_CLK_M  (PMU_LP_SLEEP_PD_OSC_CLK_V << PMU_LP_SLEEP_PD_OSC_CLK_S)
#define PMU_LP_SLEEP_PD_OSC_CLK_V  0x00000001U
#define PMU_LP_SLEEP_PD_OSC_CLK_S  31

/** PMU_LP_SLEEP_BIAS_REG register
 *  need_des
 */
#define PMU_LP_SLEEP_BIAS_REG (DR_REG_PMU_BASE + 0xc8)
/** PMU_LP_SLEEP_XPD_BIAS : R/W; bitpos: [25]; default: 0;
 *  need_des
 */
#define PMU_LP_SLEEP_XPD_BIAS    (BIT(25))
#define PMU_LP_SLEEP_XPD_BIAS_M  (PMU_LP_SLEEP_XPD_BIAS_V << PMU_LP_SLEEP_XPD_BIAS_S)
#define PMU_LP_SLEEP_XPD_BIAS_V  0x00000001U
#define PMU_LP_SLEEP_XPD_BIAS_S  25
/** PMU_LP_SLEEP_DBG_ATTEN : R/W; bitpos: [29:26]; default: 0;
 *  need_des
 */
#define PMU_LP_SLEEP_DBG_ATTEN    0x0000000FU
#define PMU_LP_SLEEP_DBG_ATTEN_M  (PMU_LP_SLEEP_DBG_ATTEN_V << PMU_LP_SLEEP_DBG_ATTEN_S)
#define PMU_LP_SLEEP_DBG_ATTEN_V  0x0000000FU
#define PMU_LP_SLEEP_DBG_ATTEN_S  26
/** PMU_LP_SLEEP_PD_CUR : R/W; bitpos: [30]; default: 0;
 *  need_des
 */
#define PMU_LP_SLEEP_PD_CUR    (BIT(30))
#define PMU_LP_SLEEP_PD_CUR_M  (PMU_LP_SLEEP_PD_CUR_V << PMU_LP_SLEEP_PD_CUR_S)
#define PMU_LP_SLEEP_PD_CUR_V  0x00000001U
#define PMU_LP_SLEEP_PD_CUR_S  30
/** PMU_LP_SLEEP_BIAS_SLEEP : R/W; bitpos: [31]; default: 0;
 *  need_des
 */
#define PMU_LP_SLEEP_BIAS_SLEEP    (BIT(31))
#define PMU_LP_SLEEP_BIAS_SLEEP_M  (PMU_LP_SLEEP_BIAS_SLEEP_V << PMU_LP_SLEEP_BIAS_SLEEP_S)
#define PMU_LP_SLEEP_BIAS_SLEEP_V  0x00000001U
#define PMU_LP_SLEEP_BIAS_SLEEP_S  31

/** PMU_IMM_HP_CK_POWER_REG register
 *  need_des
 */
#define PMU_IMM_HP_CK_POWER_REG (DR_REG_PMU_BASE + 0xcc)
/** PMU_TIE_LOW_CALI_XTAL_ICG : R/W; bitpos: [0]; default: 0;
 *  need_des
 */
#define PMU_TIE_LOW_CALI_XTAL_ICG    (BIT(0))
#define PMU_TIE_LOW_CALI_XTAL_ICG_M  (PMU_TIE_LOW_CALI_XTAL_ICG_V << PMU_TIE_LOW_CALI_XTAL_ICG_S)
#define PMU_TIE_LOW_CALI_XTAL_ICG_V  0x00000001U
#define PMU_TIE_LOW_CALI_XTAL_ICG_S  0
/** PMU_TIE_LOW_GLOBAL_CPLL_ICG : WT; bitpos: [1]; default: 0;
 *  need_des
 */
#define PMU_TIE_LOW_GLOBAL_CPLL_ICG    (BIT(1))
#define PMU_TIE_LOW_GLOBAL_CPLL_ICG_M  (PMU_TIE_LOW_GLOBAL_CPLL_ICG_V << PMU_TIE_LOW_GLOBAL_CPLL_ICG_S)
#define PMU_TIE_LOW_GLOBAL_CPLL_ICG_V  0x00000001U
#define PMU_TIE_LOW_GLOBAL_CPLL_ICG_S  1
/** PMU_TIE_LOW_GLOBAL_SPLL_ICG : WT; bitpos: [2]; default: 0;
 *  need_des
 */
#define PMU_TIE_LOW_GLOBAL_SPLL_ICG    (BIT(2))
#define PMU_TIE_LOW_GLOBAL_SPLL_ICG_M  (PMU_TIE_LOW_GLOBAL_SPLL_ICG_V << PMU_TIE_LOW_GLOBAL_SPLL_ICG_S)
#define PMU_TIE_LOW_GLOBAL_SPLL_ICG_V  0x00000001U
#define PMU_TIE_LOW_GLOBAL_SPLL_ICG_S  2
/** PMU_TIE_LOW_GLOBAL_APLL_ICG : WT; bitpos: [3]; default: 0;
 *  need_des
 */
#define PMU_TIE_LOW_GLOBAL_APLL_ICG    (BIT(3))
#define PMU_TIE_LOW_GLOBAL_APLL_ICG_M  (PMU_TIE_LOW_GLOBAL_APLL_ICG_V << PMU_TIE_LOW_GLOBAL_APLL_ICG_S)
#define PMU_TIE_LOW_GLOBAL_APLL_ICG_V  0x00000001U
#define PMU_TIE_LOW_GLOBAL_APLL_ICG_S  3
/** PMU_TIE_LOW_GLOBAL_SDIOPLL_ICG : WT; bitpos: [4]; default: 0;
 *  need_des
 */
#define PMU_TIE_LOW_GLOBAL_SDIOPLL_ICG    (BIT(4))
#define PMU_TIE_LOW_GLOBAL_SDIOPLL_ICG_M  (PMU_TIE_LOW_GLOBAL_SDIOPLL_ICG_V << PMU_TIE_LOW_GLOBAL_SDIOPLL_ICG_S)
#define PMU_TIE_LOW_GLOBAL_SDIOPLL_ICG_V  0x00000001U
#define PMU_TIE_LOW_GLOBAL_SDIOPLL_ICG_S  4
/** PMU_TIE_LOW_GLOBAL_XTAL_ICG : WT; bitpos: [5]; default: 0;
 *  need_des
 */
#define PMU_TIE_LOW_GLOBAL_XTAL_ICG    (BIT(5))
#define PMU_TIE_LOW_GLOBAL_XTAL_ICG_M  (PMU_TIE_LOW_GLOBAL_XTAL_ICG_V << PMU_TIE_LOW_GLOBAL_XTAL_ICG_S)
#define PMU_TIE_LOW_GLOBAL_XTAL_ICG_V  0x00000001U
#define PMU_TIE_LOW_GLOBAL_XTAL_ICG_S  5
/** PMU_TIE_LOW_I2C_RETENTION : WT; bitpos: [6]; default: 0;
 *  need_des
 */
#define PMU_TIE_LOW_I2C_RETENTION    (BIT(6))
#define PMU_TIE_LOW_I2C_RETENTION_M  (PMU_TIE_LOW_I2C_RETENTION_V << PMU_TIE_LOW_I2C_RETENTION_S)
#define PMU_TIE_LOW_I2C_RETENTION_V  0x00000001U
#define PMU_TIE_LOW_I2C_RETENTION_S  6
/** PMU_TIE_LOW_XPD_CPLL_I2C : WT; bitpos: [7]; default: 0;
 *  need_des
 */
#define PMU_TIE_LOW_XPD_CPLL_I2C    (BIT(7))
#define PMU_TIE_LOW_XPD_CPLL_I2C_M  (PMU_TIE_LOW_XPD_CPLL_I2C_V << PMU_TIE_LOW_XPD_CPLL_I2C_S)
#define PMU_TIE_LOW_XPD_CPLL_I2C_V  0x00000001U
#define PMU_TIE_LOW_XPD_CPLL_I2C_S  7
/** PMU_TIE_LOW_XPD_SPLL_I2C : WT; bitpos: [8]; default: 0;
 *  need_des
 */
#define PMU_TIE_LOW_XPD_SPLL_I2C    (BIT(8))
#define PMU_TIE_LOW_XPD_SPLL_I2C_M  (PMU_TIE_LOW_XPD_SPLL_I2C_V << PMU_TIE_LOW_XPD_SPLL_I2C_S)
#define PMU_TIE_LOW_XPD_SPLL_I2C_V  0x00000001U
#define PMU_TIE_LOW_XPD_SPLL_I2C_S  8
/** PMU_TIE_LOW_XPD_APLL_I2C : WT; bitpos: [9]; default: 0;
 *  need_des
 */
#define PMU_TIE_LOW_XPD_APLL_I2C    (BIT(9))
#define PMU_TIE_LOW_XPD_APLL_I2C_M  (PMU_TIE_LOW_XPD_APLL_I2C_V << PMU_TIE_LOW_XPD_APLL_I2C_S)
#define PMU_TIE_LOW_XPD_APLL_I2C_V  0x00000001U
#define PMU_TIE_LOW_XPD_APLL_I2C_S  9
/** PMU_TIE_LOW_XPD_SDIOPLL_I2C : WT; bitpos: [10]; default: 0;
 *  need_des
 */
#define PMU_TIE_LOW_XPD_SDIOPLL_I2C    (BIT(10))
#define PMU_TIE_LOW_XPD_SDIOPLL_I2C_M  (PMU_TIE_LOW_XPD_SDIOPLL_I2C_V << PMU_TIE_LOW_XPD_SDIOPLL_I2C_S)
#define PMU_TIE_LOW_XPD_SDIOPLL_I2C_V  0x00000001U
#define PMU_TIE_LOW_XPD_SDIOPLL_I2C_S  10
/** PMU_TIE_LOW_XPD_CPLL : WT; bitpos: [11]; default: 0;
 *  need_des
 */
#define PMU_TIE_LOW_XPD_CPLL    (BIT(11))
#define PMU_TIE_LOW_XPD_CPLL_M  (PMU_TIE_LOW_XPD_CPLL_V << PMU_TIE_LOW_XPD_CPLL_S)
#define PMU_TIE_LOW_XPD_CPLL_V  0x00000001U
#define PMU_TIE_LOW_XPD_CPLL_S  11
/** PMU_TIE_LOW_XPD_SPLL : WT; bitpos: [12]; default: 0;
 *  need_des
 */
#define PMU_TIE_LOW_XPD_SPLL    (BIT(12))
#define PMU_TIE_LOW_XPD_SPLL_M  (PMU_TIE_LOW_XPD_SPLL_V << PMU_TIE_LOW_XPD_SPLL_S)
#define PMU_TIE_LOW_XPD_SPLL_V  0x00000001U
#define PMU_TIE_LOW_XPD_SPLL_S  12
/** PMU_TIE_LOW_XPD_APLL : WT; bitpos: [13]; default: 0;
 *  need_des
 */
#define PMU_TIE_LOW_XPD_APLL    (BIT(13))
#define PMU_TIE_LOW_XPD_APLL_M  (PMU_TIE_LOW_XPD_APLL_V << PMU_TIE_LOW_XPD_APLL_S)
#define PMU_TIE_LOW_XPD_APLL_V  0x00000001U
#define PMU_TIE_LOW_XPD_APLL_S  13
/** PMU_TIE_LOW_XPD_SDIOPLL : WT; bitpos: [14]; default: 0;
 *  need_des
 */
#define PMU_TIE_LOW_XPD_SDIOPLL    (BIT(14))
#define PMU_TIE_LOW_XPD_SDIOPLL_M  (PMU_TIE_LOW_XPD_SDIOPLL_V << PMU_TIE_LOW_XPD_SDIOPLL_S)
#define PMU_TIE_LOW_XPD_SDIOPLL_V  0x00000001U
#define PMU_TIE_LOW_XPD_SDIOPLL_S  14
/** PMU_TIE_LOW_XPD_XTAL : WT; bitpos: [15]; default: 0;
 *  need_des
 */
#define PMU_TIE_LOW_XPD_XTAL    (BIT(15))
#define PMU_TIE_LOW_XPD_XTAL_M  (PMU_TIE_LOW_XPD_XTAL_V << PMU_TIE_LOW_XPD_XTAL_S)
#define PMU_TIE_LOW_XPD_XTAL_V  0x00000001U
#define PMU_TIE_LOW_XPD_XTAL_S  15
/** PMU_TIE_HIGH_CALI_XTAL_ICG : R/W; bitpos: [16]; default: 0;
 *  need_des
 */
#define PMU_TIE_HIGH_CALI_XTAL_ICG    (BIT(16))
#define PMU_TIE_HIGH_CALI_XTAL_ICG_M  (PMU_TIE_HIGH_CALI_XTAL_ICG_V << PMU_TIE_HIGH_CALI_XTAL_ICG_S)
#define PMU_TIE_HIGH_CALI_XTAL_ICG_V  0x00000001U
#define PMU_TIE_HIGH_CALI_XTAL_ICG_S  16
/** PMU_TIE_HIGH_GLOBAL_CPLL_ICG : WT; bitpos: [17]; default: 0;
 *  need_des
 */
#define PMU_TIE_HIGH_GLOBAL_CPLL_ICG    (BIT(17))
#define PMU_TIE_HIGH_GLOBAL_CPLL_ICG_M  (PMU_TIE_HIGH_GLOBAL_CPLL_ICG_V << PMU_TIE_HIGH_GLOBAL_CPLL_ICG_S)
#define PMU_TIE_HIGH_GLOBAL_CPLL_ICG_V  0x00000001U
#define PMU_TIE_HIGH_GLOBAL_CPLL_ICG_S  17
/** PMU_TIE_HIGH_GLOBAL_SPLL_ICG : WT; bitpos: [18]; default: 0;
 *  need_des
 */
#define PMU_TIE_HIGH_GLOBAL_SPLL_ICG    (BIT(18))
#define PMU_TIE_HIGH_GLOBAL_SPLL_ICG_M  (PMU_TIE_HIGH_GLOBAL_SPLL_ICG_V << PMU_TIE_HIGH_GLOBAL_SPLL_ICG_S)
#define PMU_TIE_HIGH_GLOBAL_SPLL_ICG_V  0x00000001U
#define PMU_TIE_HIGH_GLOBAL_SPLL_ICG_S  18
/** PMU_TIE_HIGH_GLOBAL_APLL_ICG : WT; bitpos: [19]; default: 0;
 *  need_des
 */
#define PMU_TIE_HIGH_GLOBAL_APLL_ICG    (BIT(19))
#define PMU_TIE_HIGH_GLOBAL_APLL_ICG_M  (PMU_TIE_HIGH_GLOBAL_APLL_ICG_V << PMU_TIE_HIGH_GLOBAL_APLL_ICG_S)
#define PMU_TIE_HIGH_GLOBAL_APLL_ICG_V  0x00000001U
#define PMU_TIE_HIGH_GLOBAL_APLL_ICG_S  19
/** PMU_TIE_HIGH_GLOBAL_SDIOPLL_ICG : WT; bitpos: [20]; default: 0;
 *  need_des
 */
#define PMU_TIE_HIGH_GLOBAL_SDIOPLL_ICG    (BIT(20))
#define PMU_TIE_HIGH_GLOBAL_SDIOPLL_ICG_M  (PMU_TIE_HIGH_GLOBAL_SDIOPLL_ICG_V << PMU_TIE_HIGH_GLOBAL_SDIOPLL_ICG_S)
#define PMU_TIE_HIGH_GLOBAL_SDIOPLL_ICG_V  0x00000001U
#define PMU_TIE_HIGH_GLOBAL_SDIOPLL_ICG_S  20
/** PMU_TIE_HIGH_GLOBAL_XTAL_ICG : WT; bitpos: [21]; default: 0;
 *  need_des
 */
#define PMU_TIE_HIGH_GLOBAL_XTAL_ICG    (BIT(21))
#define PMU_TIE_HIGH_GLOBAL_XTAL_ICG_M  (PMU_TIE_HIGH_GLOBAL_XTAL_ICG_V << PMU_TIE_HIGH_GLOBAL_XTAL_ICG_S)
#define PMU_TIE_HIGH_GLOBAL_XTAL_ICG_V  0x00000001U
#define PMU_TIE_HIGH_GLOBAL_XTAL_ICG_S  21
/** PMU_TIE_HIGH_I2C_RETENTION : WT; bitpos: [22]; default: 0;
 *  need_des
 */
#define PMU_TIE_HIGH_I2C_RETENTION    (BIT(22))
#define PMU_TIE_HIGH_I2C_RETENTION_M  (PMU_TIE_HIGH_I2C_RETENTION_V << PMU_TIE_HIGH_I2C_RETENTION_S)
#define PMU_TIE_HIGH_I2C_RETENTION_V  0x00000001U
#define PMU_TIE_HIGH_I2C_RETENTION_S  22
/** PMU_TIE_HIGH_XPD_CPLL_I2C : WT; bitpos: [23]; default: 0;
 *  need_des
 */
#define PMU_TIE_HIGH_XPD_CPLL_I2C    (BIT(23))
#define PMU_TIE_HIGH_XPD_CPLL_I2C_M  (PMU_TIE_HIGH_XPD_CPLL_I2C_V << PMU_TIE_HIGH_XPD_CPLL_I2C_S)
#define PMU_TIE_HIGH_XPD_CPLL_I2C_V  0x00000001U
#define PMU_TIE_HIGH_XPD_CPLL_I2C_S  23
/** PMU_TIE_HIGH_XPD_SPLL_I2C : WT; bitpos: [24]; default: 0;
 *  need_des
 */
#define PMU_TIE_HIGH_XPD_SPLL_I2C    (BIT(24))
#define PMU_TIE_HIGH_XPD_SPLL_I2C_M  (PMU_TIE_HIGH_XPD_SPLL_I2C_V << PMU_TIE_HIGH_XPD_SPLL_I2C_S)
#define PMU_TIE_HIGH_XPD_SPLL_I2C_V  0x00000001U
#define PMU_TIE_HIGH_XPD_SPLL_I2C_S  24
/** PMU_TIE_HIGH_XPD_APLL_I2C : WT; bitpos: [25]; default: 0;
 *  need_des
 */
#define PMU_TIE_HIGH_XPD_APLL_I2C    (BIT(25))
#define PMU_TIE_HIGH_XPD_APLL_I2C_M  (PMU_TIE_HIGH_XPD_APLL_I2C_V << PMU_TIE_HIGH_XPD_APLL_I2C_S)
#define PMU_TIE_HIGH_XPD_APLL_I2C_V  0x00000001U
#define PMU_TIE_HIGH_XPD_APLL_I2C_S  25
/** PMU_TIE_HIGH_XPD_SDIOPLL_I2C : WT; bitpos: [26]; default: 0;
 *  need_des
 */
#define PMU_TIE_HIGH_XPD_SDIOPLL_I2C    (BIT(26))
#define PMU_TIE_HIGH_XPD_SDIOPLL_I2C_M  (PMU_TIE_HIGH_XPD_SDIOPLL_I2C_V << PMU_TIE_HIGH_XPD_SDIOPLL_I2C_S)
#define PMU_TIE_HIGH_XPD_SDIOPLL_I2C_V  0x00000001U
#define PMU_TIE_HIGH_XPD_SDIOPLL_I2C_S  26
/** PMU_TIE_HIGH_XPD_CPLL : WT; bitpos: [27]; default: 0;
 *  need_des
 */
#define PMU_TIE_HIGH_XPD_CPLL    (BIT(27))
#define PMU_TIE_HIGH_XPD_CPLL_M  (PMU_TIE_HIGH_XPD_CPLL_V << PMU_TIE_HIGH_XPD_CPLL_S)
#define PMU_TIE_HIGH_XPD_CPLL_V  0x00000001U
#define PMU_TIE_HIGH_XPD_CPLL_S  27
/** PMU_TIE_HIGH_XPD_SPLL : WT; bitpos: [28]; default: 0;
 *  need_des
 */
#define PMU_TIE_HIGH_XPD_SPLL    (BIT(28))
#define PMU_TIE_HIGH_XPD_SPLL_M  (PMU_TIE_HIGH_XPD_SPLL_V << PMU_TIE_HIGH_XPD_SPLL_S)
#define PMU_TIE_HIGH_XPD_SPLL_V  0x00000001U
#define PMU_TIE_HIGH_XPD_SPLL_S  28
/** PMU_TIE_HIGH_XPD_APLL : WT; bitpos: [29]; default: 0;
 *  need_des
 */
#define PMU_TIE_HIGH_XPD_APLL    (BIT(29))
#define PMU_TIE_HIGH_XPD_APLL_M  (PMU_TIE_HIGH_XPD_APLL_V << PMU_TIE_HIGH_XPD_APLL_S)
#define PMU_TIE_HIGH_XPD_APLL_V  0x00000001U
#define PMU_TIE_HIGH_XPD_APLL_S  29
/** PMU_TIE_HIGH_XPD_SDIOPLL : WT; bitpos: [30]; default: 0;
 *  need_des
 */
#define PMU_TIE_HIGH_XPD_SDIOPLL    (BIT(30))
#define PMU_TIE_HIGH_XPD_SDIOPLL_M  (PMU_TIE_HIGH_XPD_SDIOPLL_V << PMU_TIE_HIGH_XPD_SDIOPLL_S)
#define PMU_TIE_HIGH_XPD_SDIOPLL_V  0x00000001U
#define PMU_TIE_HIGH_XPD_SDIOPLL_S  30
/** PMU_TIE_HIGH_XPD_XTAL : WT; bitpos: [31]; default: 0;
 *  need_des
 */
#define PMU_TIE_HIGH_XPD_XTAL    (BIT(31))
#define PMU_TIE_HIGH_XPD_XTAL_M  (PMU_TIE_HIGH_XPD_XTAL_V << PMU_TIE_HIGH_XPD_XTAL_S)
#define PMU_TIE_HIGH_XPD_XTAL_V  0x00000001U
#define PMU_TIE_HIGH_XPD_XTAL_S  31

/** PMU_IMM_SLEEP_SYSCLK_REG register
 *  need_des
 */
#define PMU_IMM_SLEEP_SYSCLK_REG (DR_REG_PMU_BASE + 0xd0)
/** PMU_UPDATE_DIG_ICG_SWITCH : WT; bitpos: [28]; default: 0;
 *  need_des
 */
#define PMU_UPDATE_DIG_ICG_SWITCH    (BIT(28))
#define PMU_UPDATE_DIG_ICG_SWITCH_M  (PMU_UPDATE_DIG_ICG_SWITCH_V << PMU_UPDATE_DIG_ICG_SWITCH_S)
#define PMU_UPDATE_DIG_ICG_SWITCH_V  0x00000001U
#define PMU_UPDATE_DIG_ICG_SWITCH_S  28
/** PMU_TIE_LOW_ICG_SLP_SEL : WT; bitpos: [29]; default: 0;
 *  need_des
 */
#define PMU_TIE_LOW_ICG_SLP_SEL    (BIT(29))
#define PMU_TIE_LOW_ICG_SLP_SEL_M  (PMU_TIE_LOW_ICG_SLP_SEL_V << PMU_TIE_LOW_ICG_SLP_SEL_S)
#define PMU_TIE_LOW_ICG_SLP_SEL_V  0x00000001U
#define PMU_TIE_LOW_ICG_SLP_SEL_S  29
/** PMU_TIE_HIGH_ICG_SLP_SEL : WT; bitpos: [30]; default: 0;
 *  need_des
 */
#define PMU_TIE_HIGH_ICG_SLP_SEL    (BIT(30))
#define PMU_TIE_HIGH_ICG_SLP_SEL_M  (PMU_TIE_HIGH_ICG_SLP_SEL_V << PMU_TIE_HIGH_ICG_SLP_SEL_S)
#define PMU_TIE_HIGH_ICG_SLP_SEL_V  0x00000001U
#define PMU_TIE_HIGH_ICG_SLP_SEL_S  30
/** PMU_UPDATE_DIG_SYS_CLK_SEL : WT; bitpos: [31]; default: 0;
 *  need_des
 */
#define PMU_UPDATE_DIG_SYS_CLK_SEL    (BIT(31))
#define PMU_UPDATE_DIG_SYS_CLK_SEL_M  (PMU_UPDATE_DIG_SYS_CLK_SEL_V << PMU_UPDATE_DIG_SYS_CLK_SEL_S)
#define PMU_UPDATE_DIG_SYS_CLK_SEL_V  0x00000001U
#define PMU_UPDATE_DIG_SYS_CLK_SEL_S  31

/** PMU_IMM_HP_FUNC_ICG_REG register
 *  need_des
 */
#define PMU_IMM_HP_FUNC_ICG_REG (DR_REG_PMU_BASE + 0xd4)
/** PMU_UPDATE_DIG_ICG_FUNC_EN : WT; bitpos: [31]; default: 0;
 *  need_des
 */
#define PMU_UPDATE_DIG_ICG_FUNC_EN    (BIT(31))
#define PMU_UPDATE_DIG_ICG_FUNC_EN_M  (PMU_UPDATE_DIG_ICG_FUNC_EN_V << PMU_UPDATE_DIG_ICG_FUNC_EN_S)
#define PMU_UPDATE_DIG_ICG_FUNC_EN_V  0x00000001U
#define PMU_UPDATE_DIG_ICG_FUNC_EN_S  31

/** PMU_IMM_HP_APB_ICG_REG register
 *  need_des
 */
#define PMU_IMM_HP_APB_ICG_REG (DR_REG_PMU_BASE + 0xd8)
/** PMU_UPDATE_DIG_ICG_APB_EN : WT; bitpos: [31]; default: 0;
 *  need_des
 */
#define PMU_UPDATE_DIG_ICG_APB_EN    (BIT(31))
#define PMU_UPDATE_DIG_ICG_APB_EN_M  (PMU_UPDATE_DIG_ICG_APB_EN_V << PMU_UPDATE_DIG_ICG_APB_EN_S)
#define PMU_UPDATE_DIG_ICG_APB_EN_V  0x00000001U
#define PMU_UPDATE_DIG_ICG_APB_EN_S  31

/** PMU_IMM_MODEM_ICG_REG register
 *  need_des
 */
#define PMU_IMM_MODEM_ICG_REG (DR_REG_PMU_BASE + 0xdc)
/** PMU_UPDATE_DIG_ICG_MODEM_EN : WT; bitpos: [31]; default: 0;
 *  need_des
 */
#define PMU_UPDATE_DIG_ICG_MODEM_EN    (BIT(31))
#define PMU_UPDATE_DIG_ICG_MODEM_EN_M  (PMU_UPDATE_DIG_ICG_MODEM_EN_V << PMU_UPDATE_DIG_ICG_MODEM_EN_S)
#define PMU_UPDATE_DIG_ICG_MODEM_EN_V  0x00000001U
#define PMU_UPDATE_DIG_ICG_MODEM_EN_S  31

/** PMU_IMM_LP_ICG_REG register
 *  need_des
 */
#define PMU_IMM_LP_ICG_REG (DR_REG_PMU_BASE + 0xe0)
/** PMU_TIE_LOW_LP_ROOTCLK_SEL : WT; bitpos: [30]; default: 0;
 *  need_des
 */
#define PMU_TIE_LOW_LP_ROOTCLK_SEL    (BIT(30))
#define PMU_TIE_LOW_LP_ROOTCLK_SEL_M  (PMU_TIE_LOW_LP_ROOTCLK_SEL_V << PMU_TIE_LOW_LP_ROOTCLK_SEL_S)
#define PMU_TIE_LOW_LP_ROOTCLK_SEL_V  0x00000001U
#define PMU_TIE_LOW_LP_ROOTCLK_SEL_S  30
/** PMU_TIE_HIGH_LP_ROOTCLK_SEL : WT; bitpos: [31]; default: 0;
 *  need_des
 */
#define PMU_TIE_HIGH_LP_ROOTCLK_SEL    (BIT(31))
#define PMU_TIE_HIGH_LP_ROOTCLK_SEL_M  (PMU_TIE_HIGH_LP_ROOTCLK_SEL_V << PMU_TIE_HIGH_LP_ROOTCLK_SEL_S)
#define PMU_TIE_HIGH_LP_ROOTCLK_SEL_V  0x00000001U
#define PMU_TIE_HIGH_LP_ROOTCLK_SEL_S  31

/** PMU_IMM_PAD_HOLD_ALL_REG register
 *  need_des
 */
#define PMU_IMM_PAD_HOLD_ALL_REG (DR_REG_PMU_BASE + 0xe4)
/** PMU_PAD_SLP_SEL : RO; bitpos: [0]; default: 0;
 *  need_des
 */
#define PMU_PAD_SLP_SEL    (BIT(0))
#define PMU_PAD_SLP_SEL_M  (PMU_PAD_SLP_SEL_V << PMU_PAD_SLP_SEL_S)
#define PMU_PAD_SLP_SEL_V  0x00000001U
#define PMU_PAD_SLP_SEL_S  0
/** PMU_LP_PAD_HOLD_ALL : RO; bitpos: [1]; default: 0;
 *  need_des
 */
#define PMU_LP_PAD_HOLD_ALL    (BIT(1))
#define PMU_LP_PAD_HOLD_ALL_M  (PMU_LP_PAD_HOLD_ALL_V << PMU_LP_PAD_HOLD_ALL_S)
#define PMU_LP_PAD_HOLD_ALL_V  0x00000001U
#define PMU_LP_PAD_HOLD_ALL_S  1
/** PMU_HP_PAD_HOLD_ALL : RO; bitpos: [2]; default: 0;
 *  need_des
 */
#define PMU_HP_PAD_HOLD_ALL    (BIT(2))
#define PMU_HP_PAD_HOLD_ALL_M  (PMU_HP_PAD_HOLD_ALL_V << PMU_HP_PAD_HOLD_ALL_S)
#define PMU_HP_PAD_HOLD_ALL_V  0x00000001U
#define PMU_HP_PAD_HOLD_ALL_S  2
/** PMU_TIE_HIGH_PAD_SLP_SEL : WT; bitpos: [26]; default: 0;
 *  need_des
 */
#define PMU_TIE_HIGH_PAD_SLP_SEL    (BIT(26))
#define PMU_TIE_HIGH_PAD_SLP_SEL_M  (PMU_TIE_HIGH_PAD_SLP_SEL_V << PMU_TIE_HIGH_PAD_SLP_SEL_S)
#define PMU_TIE_HIGH_PAD_SLP_SEL_V  0x00000001U
#define PMU_TIE_HIGH_PAD_SLP_SEL_S  26
/** PMU_TIE_LOW_PAD_SLP_SEL : WT; bitpos: [27]; default: 0;
 *  need_des
 */
#define PMU_TIE_LOW_PAD_SLP_SEL    (BIT(27))
#define PMU_TIE_LOW_PAD_SLP_SEL_M  (PMU_TIE_LOW_PAD_SLP_SEL_V << PMU_TIE_LOW_PAD_SLP_SEL_S)
#define PMU_TIE_LOW_PAD_SLP_SEL_V  0x00000001U
#define PMU_TIE_LOW_PAD_SLP_SEL_S  27
/** PMU_TIE_HIGH_LP_PAD_HOLD_ALL : WT; bitpos: [28]; default: 0;
 *  need_des
 */
#define PMU_TIE_HIGH_LP_PAD_HOLD_ALL    (BIT(28))
#define PMU_TIE_HIGH_LP_PAD_HOLD_ALL_M  (PMU_TIE_HIGH_LP_PAD_HOLD_ALL_V << PMU_TIE_HIGH_LP_PAD_HOLD_ALL_S)
#define PMU_TIE_HIGH_LP_PAD_HOLD_ALL_V  0x00000001U
#define PMU_TIE_HIGH_LP_PAD_HOLD_ALL_S  28
/** PMU_TIE_LOW_LP_PAD_HOLD_ALL : WT; bitpos: [29]; default: 0;
 *  need_des
 */
#define PMU_TIE_LOW_LP_PAD_HOLD_ALL    (BIT(29))
#define PMU_TIE_LOW_LP_PAD_HOLD_ALL_M  (PMU_TIE_LOW_LP_PAD_HOLD_ALL_V << PMU_TIE_LOW_LP_PAD_HOLD_ALL_S)
#define PMU_TIE_LOW_LP_PAD_HOLD_ALL_V  0x00000001U
#define PMU_TIE_LOW_LP_PAD_HOLD_ALL_S  29
/** PMU_TIE_HIGH_HP_PAD_HOLD_ALL : WT; bitpos: [30]; default: 0;
 *  need_des
 */
#define PMU_TIE_HIGH_HP_PAD_HOLD_ALL    (BIT(30))
#define PMU_TIE_HIGH_HP_PAD_HOLD_ALL_M  (PMU_TIE_HIGH_HP_PAD_HOLD_ALL_V << PMU_TIE_HIGH_HP_PAD_HOLD_ALL_S)
#define PMU_TIE_HIGH_HP_PAD_HOLD_ALL_V  0x00000001U
#define PMU_TIE_HIGH_HP_PAD_HOLD_ALL_S  30
/** PMU_TIE_LOW_HP_PAD_HOLD_ALL : WT; bitpos: [31]; default: 0;
 *  need_des
 */
#define PMU_TIE_LOW_HP_PAD_HOLD_ALL    (BIT(31))
#define PMU_TIE_LOW_HP_PAD_HOLD_ALL_M  (PMU_TIE_LOW_HP_PAD_HOLD_ALL_V << PMU_TIE_LOW_HP_PAD_HOLD_ALL_S)
#define PMU_TIE_LOW_HP_PAD_HOLD_ALL_V  0x00000001U
#define PMU_TIE_LOW_HP_PAD_HOLD_ALL_S  31

/** PMU_IMM_I2C_ISO_REG register
 *  need_des
 */
#define PMU_IMM_I2C_ISO_REG (DR_REG_PMU_BASE + 0xe8)
/** PMU_TIE_HIGH_I2C_ISO_EN : WT; bitpos: [30]; default: 0;
 *  need_des
 */
#define PMU_TIE_HIGH_I2C_ISO_EN    (BIT(30))
#define PMU_TIE_HIGH_I2C_ISO_EN_M  (PMU_TIE_HIGH_I2C_ISO_EN_V << PMU_TIE_HIGH_I2C_ISO_EN_S)
#define PMU_TIE_HIGH_I2C_ISO_EN_V  0x00000001U
#define PMU_TIE_HIGH_I2C_ISO_EN_S  30
/** PMU_TIE_LOW_I2C_ISO_EN : WT; bitpos: [31]; default: 0;
 *  need_des
 */
#define PMU_TIE_LOW_I2C_ISO_EN    (BIT(31))
#define PMU_TIE_LOW_I2C_ISO_EN_M  (PMU_TIE_LOW_I2C_ISO_EN_V << PMU_TIE_LOW_I2C_ISO_EN_S)
#define PMU_TIE_LOW_I2C_ISO_EN_V  0x00000001U
#define PMU_TIE_LOW_I2C_ISO_EN_S  31

/** PMU_POWER_WAIT_TIMER0_REG register
 *  need_des
 */
#define PMU_POWER_WAIT_TIMER0_REG (DR_REG_PMU_BASE + 0xec)
/** PMU_DG_HP_POWERDOWN_TIMER : R/W; bitpos: [13:5]; default: 255;
 *  need_des
 */
#define PMU_DG_HP_POWERDOWN_TIMER    0x000001FFU
#define PMU_DG_HP_POWERDOWN_TIMER_M  (PMU_DG_HP_POWERDOWN_TIMER_V << PMU_DG_HP_POWERDOWN_TIMER_S)
#define PMU_DG_HP_POWERDOWN_TIMER_V  0x000001FFU
#define PMU_DG_HP_POWERDOWN_TIMER_S  5
/** PMU_DG_HP_POWERUP_TIMER : R/W; bitpos: [22:14]; default: 255;
 *  need_des
 */
#define PMU_DG_HP_POWERUP_TIMER    0x000001FFU
#define PMU_DG_HP_POWERUP_TIMER_M  (PMU_DG_HP_POWERUP_TIMER_V << PMU_DG_HP_POWERUP_TIMER_S)
#define PMU_DG_HP_POWERUP_TIMER_V  0x000001FFU
#define PMU_DG_HP_POWERUP_TIMER_S  14
/** PMU_DG_HP_WAIT_TIMER : R/W; bitpos: [31:23]; default: 255;
 *  need_des
 */
#define PMU_DG_HP_WAIT_TIMER    0x000001FFU
#define PMU_DG_HP_WAIT_TIMER_M  (PMU_DG_HP_WAIT_TIMER_V << PMU_DG_HP_WAIT_TIMER_S)
#define PMU_DG_HP_WAIT_TIMER_V  0x000001FFU
#define PMU_DG_HP_WAIT_TIMER_S  23

/** PMU_POWER_WAIT_TIMER1_REG register
 *  need_des
 */
#define PMU_POWER_WAIT_TIMER1_REG (DR_REG_PMU_BASE + 0xf0)
/** PMU_DG_LP_POWERDOWN_TIMER : R/W; bitpos: [13:5]; default: 255;
 *  need_des
 */
#define PMU_DG_LP_POWERDOWN_TIMER    0x000001FFU
#define PMU_DG_LP_POWERDOWN_TIMER_M  (PMU_DG_LP_POWERDOWN_TIMER_V << PMU_DG_LP_POWERDOWN_TIMER_S)
#define PMU_DG_LP_POWERDOWN_TIMER_V  0x000001FFU
#define PMU_DG_LP_POWERDOWN_TIMER_S  5
/** PMU_DG_LP_POWERUP_TIMER : R/W; bitpos: [22:14]; default: 255;
 *  need_des
 */
#define PMU_DG_LP_POWERUP_TIMER    0x000001FFU
#define PMU_DG_LP_POWERUP_TIMER_M  (PMU_DG_LP_POWERUP_TIMER_V << PMU_DG_LP_POWERUP_TIMER_S)
#define PMU_DG_LP_POWERUP_TIMER_V  0x000001FFU
#define PMU_DG_LP_POWERUP_TIMER_S  14
/** PMU_DG_LP_WAIT_TIMER : R/W; bitpos: [31:23]; default: 255;
 *  need_des
 */
#define PMU_DG_LP_WAIT_TIMER    0x000001FFU
#define PMU_DG_LP_WAIT_TIMER_M  (PMU_DG_LP_WAIT_TIMER_V << PMU_DG_LP_WAIT_TIMER_S)
#define PMU_DG_LP_WAIT_TIMER_V  0x000001FFU
#define PMU_DG_LP_WAIT_TIMER_S  23

/** PMU_POWER_PD_TOP_CNTL_REG register
 *  need_des
 */
#define PMU_POWER_PD_TOP_CNTL_REG (DR_REG_PMU_BASE + 0xf4)
/** PMU_FORCE_TOP_RESET : R/W; bitpos: [0]; default: 0;
 *  need_des
 */
#define PMU_FORCE_TOP_RESET    (BIT(0))
#define PMU_FORCE_TOP_RESET_M  (PMU_FORCE_TOP_RESET_V << PMU_FORCE_TOP_RESET_S)
#define PMU_FORCE_TOP_RESET_V  0x00000001U
#define PMU_FORCE_TOP_RESET_S  0
/** PMU_FORCE_TOP_ISO : R/W; bitpos: [1]; default: 0;
 *  need_des
 */
#define PMU_FORCE_TOP_ISO    (BIT(1))
#define PMU_FORCE_TOP_ISO_M  (PMU_FORCE_TOP_ISO_V << PMU_FORCE_TOP_ISO_S)
#define PMU_FORCE_TOP_ISO_V  0x00000001U
#define PMU_FORCE_TOP_ISO_S  1
/** PMU_FORCE_TOP_PU : R/W; bitpos: [2]; default: 1;
 *  need_des
 */
#define PMU_FORCE_TOP_PU    (BIT(2))
#define PMU_FORCE_TOP_PU_M  (PMU_FORCE_TOP_PU_V << PMU_FORCE_TOP_PU_S)
#define PMU_FORCE_TOP_PU_V  0x00000001U
#define PMU_FORCE_TOP_PU_S  2
/** PMU_FORCE_TOP_NO_RESET : R/W; bitpos: [3]; default: 1;
 *  need_des
 */
#define PMU_FORCE_TOP_NO_RESET    (BIT(3))
#define PMU_FORCE_TOP_NO_RESET_M  (PMU_FORCE_TOP_NO_RESET_V << PMU_FORCE_TOP_NO_RESET_S)
#define PMU_FORCE_TOP_NO_RESET_V  0x00000001U
#define PMU_FORCE_TOP_NO_RESET_S  3
/** PMU_FORCE_TOP_NO_ISO : R/W; bitpos: [4]; default: 1;
 *  need_des
 */
#define PMU_FORCE_TOP_NO_ISO    (BIT(4))
#define PMU_FORCE_TOP_NO_ISO_M  (PMU_FORCE_TOP_NO_ISO_V << PMU_FORCE_TOP_NO_ISO_S)
#define PMU_FORCE_TOP_NO_ISO_V  0x00000001U
#define PMU_FORCE_TOP_NO_ISO_S  4
/** PMU_FORCE_TOP_PD : R/W; bitpos: [5]; default: 0;
 *  need_des
 */
#define PMU_FORCE_TOP_PD    (BIT(5))
#define PMU_FORCE_TOP_PD_M  (PMU_FORCE_TOP_PD_V << PMU_FORCE_TOP_PD_S)
#define PMU_FORCE_TOP_PD_V  0x00000001U
#define PMU_FORCE_TOP_PD_S  5

/** PMU_POWER_PD_CNNT_CNTL_REG register
 *  need_des
 */
#define PMU_POWER_PD_CNNT_CNTL_REG (DR_REG_PMU_BASE + 0xf8)
/** PMU_FORCE_CNNT_RESET : R/W; bitpos: [0]; default: 0;
 *  need_des
 */
#define PMU_FORCE_CNNT_RESET    (BIT(0))
#define PMU_FORCE_CNNT_RESET_M  (PMU_FORCE_CNNT_RESET_V << PMU_FORCE_CNNT_RESET_S)
#define PMU_FORCE_CNNT_RESET_V  0x00000001U
#define PMU_FORCE_CNNT_RESET_S  0
/** PMU_FORCE_CNNT_ISO : R/W; bitpos: [1]; default: 0;
 *  need_des
 */
#define PMU_FORCE_CNNT_ISO    (BIT(1))
#define PMU_FORCE_CNNT_ISO_M  (PMU_FORCE_CNNT_ISO_V << PMU_FORCE_CNNT_ISO_S)
#define PMU_FORCE_CNNT_ISO_V  0x00000001U
#define PMU_FORCE_CNNT_ISO_S  1
/** PMU_FORCE_CNNT_PU : R/W; bitpos: [2]; default: 1;
 *  need_des
 */
#define PMU_FORCE_CNNT_PU    (BIT(2))
#define PMU_FORCE_CNNT_PU_M  (PMU_FORCE_CNNT_PU_V << PMU_FORCE_CNNT_PU_S)
#define PMU_FORCE_CNNT_PU_V  0x00000001U
#define PMU_FORCE_CNNT_PU_S  2
/** PMU_FORCE_CNNT_NO_RESET : R/W; bitpos: [3]; default: 1;
 *  need_des
 */
#define PMU_FORCE_CNNT_NO_RESET    (BIT(3))
#define PMU_FORCE_CNNT_NO_RESET_M  (PMU_FORCE_CNNT_NO_RESET_V << PMU_FORCE_CNNT_NO_RESET_S)
#define PMU_FORCE_CNNT_NO_RESET_V  0x00000001U
#define PMU_FORCE_CNNT_NO_RESET_S  3
/** PMU_FORCE_CNNT_NO_ISO : R/W; bitpos: [4]; default: 1;
 *  need_des
 */
#define PMU_FORCE_CNNT_NO_ISO    (BIT(4))
#define PMU_FORCE_CNNT_NO_ISO_M  (PMU_FORCE_CNNT_NO_ISO_V << PMU_FORCE_CNNT_NO_ISO_S)
#define PMU_FORCE_CNNT_NO_ISO_V  0x00000001U
#define PMU_FORCE_CNNT_NO_ISO_S  4
/** PMU_FORCE_CNNT_PD : R/W; bitpos: [5]; default: 0;
 *  need_des
 */
#define PMU_FORCE_CNNT_PD    (BIT(5))
#define PMU_FORCE_CNNT_PD_M  (PMU_FORCE_CNNT_PD_V << PMU_FORCE_CNNT_PD_S)
#define PMU_FORCE_CNNT_PD_V  0x00000001U
#define PMU_FORCE_CNNT_PD_S  5

/** PMU_POWER_PD_HPMEM_CNTL_REG register
 *  need_des
 */
#define PMU_POWER_PD_HPMEM_CNTL_REG (DR_REG_PMU_BASE + 0xfc)
/** PMU_FORCE_HP_MEM_RESET : R/W; bitpos: [0]; default: 0;
 *  need_des
 */
#define PMU_FORCE_HP_MEM_RESET    (BIT(0))
#define PMU_FORCE_HP_MEM_RESET_M  (PMU_FORCE_HP_MEM_RESET_V << PMU_FORCE_HP_MEM_RESET_S)
#define PMU_FORCE_HP_MEM_RESET_V  0x00000001U
#define PMU_FORCE_HP_MEM_RESET_S  0
/** PMU_FORCE_HP_MEM_ISO : R/W; bitpos: [1]; default: 0;
 *  need_des
 */
#define PMU_FORCE_HP_MEM_ISO    (BIT(1))
#define PMU_FORCE_HP_MEM_ISO_M  (PMU_FORCE_HP_MEM_ISO_V << PMU_FORCE_HP_MEM_ISO_S)
#define PMU_FORCE_HP_MEM_ISO_V  0x00000001U
#define PMU_FORCE_HP_MEM_ISO_S  1
/** PMU_FORCE_HP_MEM_PU : R/W; bitpos: [2]; default: 1;
 *  need_des
 */
#define PMU_FORCE_HP_MEM_PU    (BIT(2))
#define PMU_FORCE_HP_MEM_PU_M  (PMU_FORCE_HP_MEM_PU_V << PMU_FORCE_HP_MEM_PU_S)
#define PMU_FORCE_HP_MEM_PU_V  0x00000001U
#define PMU_FORCE_HP_MEM_PU_S  2
/** PMU_FORCE_HP_MEM_NO_RESET : R/W; bitpos: [3]; default: 1;
 *  need_des
 */
#define PMU_FORCE_HP_MEM_NO_RESET    (BIT(3))
#define PMU_FORCE_HP_MEM_NO_RESET_M  (PMU_FORCE_HP_MEM_NO_RESET_V << PMU_FORCE_HP_MEM_NO_RESET_S)
#define PMU_FORCE_HP_MEM_NO_RESET_V  0x00000001U
#define PMU_FORCE_HP_MEM_NO_RESET_S  3
/** PMU_FORCE_HP_MEM_NO_ISO : R/W; bitpos: [4]; default: 1;
 *  need_des
 */
#define PMU_FORCE_HP_MEM_NO_ISO    (BIT(4))
#define PMU_FORCE_HP_MEM_NO_ISO_M  (PMU_FORCE_HP_MEM_NO_ISO_V << PMU_FORCE_HP_MEM_NO_ISO_S)
#define PMU_FORCE_HP_MEM_NO_ISO_V  0x00000001U
#define PMU_FORCE_HP_MEM_NO_ISO_S  4
/** PMU_FORCE_HP_MEM_PD : R/W; bitpos: [5]; default: 0;
 *  need_des
 */
#define PMU_FORCE_HP_MEM_PD    (BIT(5))
#define PMU_FORCE_HP_MEM_PD_M  (PMU_FORCE_HP_MEM_PD_V << PMU_FORCE_HP_MEM_PD_S)
#define PMU_FORCE_HP_MEM_PD_V  0x00000001U
#define PMU_FORCE_HP_MEM_PD_S  5

/** PMU_POWER_PD_TOP_MASK_REG register
 *  need_des
 */
#define PMU_POWER_PD_TOP_MASK_REG (DR_REG_PMU_BASE + 0x100)
/** PMU_XPD_TOP_MASK : R/W; bitpos: [4:0]; default: 0;
 *  need_des
 */
#define PMU_XPD_TOP_MASK    0x0000001FU
#define PMU_XPD_TOP_MASK_M  (PMU_XPD_TOP_MASK_V << PMU_XPD_TOP_MASK_S)
#define PMU_XPD_TOP_MASK_V  0x0000001FU
#define PMU_XPD_TOP_MASK_S  0
/** PMU_PD_TOP_MASK : R/W; bitpos: [31:27]; default: 0;
 *  need_des
 */
#define PMU_PD_TOP_MASK    0x0000001FU
#define PMU_PD_TOP_MASK_M  (PMU_PD_TOP_MASK_V << PMU_PD_TOP_MASK_S)
#define PMU_PD_TOP_MASK_V  0x0000001FU
#define PMU_PD_TOP_MASK_S  27

/** PMU_POWER_PD_CNNT_MASK_REG register
 *  need_des
 */
#define PMU_POWER_PD_CNNT_MASK_REG (DR_REG_PMU_BASE + 0x104)
/** PMU_XPD_CNNT_MASK : R/W; bitpos: [4:0]; default: 0;
 *  need_des
 */
#define PMU_XPD_CNNT_MASK    0x0000001FU
#define PMU_XPD_CNNT_MASK_M  (PMU_XPD_CNNT_MASK_V << PMU_XPD_CNNT_MASK_S)
#define PMU_XPD_CNNT_MASK_V  0x0000001FU
#define PMU_XPD_CNNT_MASK_S  0
/** PMU_PD_CNNT_MASK : R/W; bitpos: [31:27]; default: 0;
 *  need_des
 */
#define PMU_PD_CNNT_MASK    0x0000001FU
#define PMU_PD_CNNT_MASK_M  (PMU_PD_CNNT_MASK_V << PMU_PD_CNNT_MASK_S)
#define PMU_PD_CNNT_MASK_V  0x0000001FU
#define PMU_PD_CNNT_MASK_S  27

/** PMU_POWER_PD_HPMEM_MASK_REG register
 *  need_des
 */
#define PMU_POWER_PD_HPMEM_MASK_REG (DR_REG_PMU_BASE + 0x108)
/** PMU_XPD_HP_MEM_MASK : R/W; bitpos: [5:0]; default: 0;
 *  need_des
 */
#define PMU_XPD_HP_MEM_MASK    0x0000003FU
#define PMU_XPD_HP_MEM_MASK_M  (PMU_XPD_HP_MEM_MASK_V << PMU_XPD_HP_MEM_MASK_S)
#define PMU_XPD_HP_MEM_MASK_V  0x0000003FU
#define PMU_XPD_HP_MEM_MASK_S  0
/** PMU_PD_HP_MEM_MASK : R/W; bitpos: [31:26]; default: 0;
 *  need_des
 */
#define PMU_PD_HP_MEM_MASK    0x0000003FU
#define PMU_PD_HP_MEM_MASK_M  (PMU_PD_HP_MEM_MASK_V << PMU_PD_HP_MEM_MASK_S)
#define PMU_PD_HP_MEM_MASK_V  0x0000003FU
#define PMU_PD_HP_MEM_MASK_S  26

/** PMU_POWER_DCDC_SWITCH_REG register
 *  need_des
 */
#define PMU_POWER_DCDC_SWITCH_REG (DR_REG_PMU_BASE + 0x10c)
/** PMU_FORCE_DCDC_SWITCH_PU : R/W; bitpos: [0]; default: 1;
 *  need_des
 */
#define PMU_FORCE_DCDC_SWITCH_PU    (BIT(0))
#define PMU_FORCE_DCDC_SWITCH_PU_M  (PMU_FORCE_DCDC_SWITCH_PU_V << PMU_FORCE_DCDC_SWITCH_PU_S)
#define PMU_FORCE_DCDC_SWITCH_PU_V  0x00000001U
#define PMU_FORCE_DCDC_SWITCH_PU_S  0
/** PMU_FORCE_DCDC_SWITCH_PD : R/W; bitpos: [1]; default: 0;
 *  need_des
 */
#define PMU_FORCE_DCDC_SWITCH_PD    (BIT(1))
#define PMU_FORCE_DCDC_SWITCH_PD_M  (PMU_FORCE_DCDC_SWITCH_PD_V << PMU_FORCE_DCDC_SWITCH_PD_S)
#define PMU_FORCE_DCDC_SWITCH_PD_V  0x00000001U
#define PMU_FORCE_DCDC_SWITCH_PD_S  1

/** PMU_POWER_PD_LPPERI_CNTL_REG register
 *  need_des
 */
#define PMU_POWER_PD_LPPERI_CNTL_REG (DR_REG_PMU_BASE + 0x110)
/** PMU_FORCE_LP_PERI_RESET : R/W; bitpos: [0]; default: 0;
 *  need_des
 */
#define PMU_FORCE_LP_PERI_RESET    (BIT(0))
#define PMU_FORCE_LP_PERI_RESET_M  (PMU_FORCE_LP_PERI_RESET_V << PMU_FORCE_LP_PERI_RESET_S)
#define PMU_FORCE_LP_PERI_RESET_V  0x00000001U
#define PMU_FORCE_LP_PERI_RESET_S  0
/** PMU_FORCE_LP_PERI_ISO : R/W; bitpos: [1]; default: 0;
 *  need_des
 */
#define PMU_FORCE_LP_PERI_ISO    (BIT(1))
#define PMU_FORCE_LP_PERI_ISO_M  (PMU_FORCE_LP_PERI_ISO_V << PMU_FORCE_LP_PERI_ISO_S)
#define PMU_FORCE_LP_PERI_ISO_V  0x00000001U
#define PMU_FORCE_LP_PERI_ISO_S  1
/** PMU_FORCE_LP_PERI_PU : R/W; bitpos: [2]; default: 1;
 *  need_des
 */
#define PMU_FORCE_LP_PERI_PU    (BIT(2))
#define PMU_FORCE_LP_PERI_PU_M  (PMU_FORCE_LP_PERI_PU_V << PMU_FORCE_LP_PERI_PU_S)
#define PMU_FORCE_LP_PERI_PU_V  0x00000001U
#define PMU_FORCE_LP_PERI_PU_S  2
/** PMU_FORCE_LP_PERI_NO_RESET : R/W; bitpos: [3]; default: 1;
 *  need_des
 */
#define PMU_FORCE_LP_PERI_NO_RESET    (BIT(3))
#define PMU_FORCE_LP_PERI_NO_RESET_M  (PMU_FORCE_LP_PERI_NO_RESET_V << PMU_FORCE_LP_PERI_NO_RESET_S)
#define PMU_FORCE_LP_PERI_NO_RESET_V  0x00000001U
#define PMU_FORCE_LP_PERI_NO_RESET_S  3
/** PMU_FORCE_LP_PERI_NO_ISO : R/W; bitpos: [4]; default: 1;
 *  need_des
 */
#define PMU_FORCE_LP_PERI_NO_ISO    (BIT(4))
#define PMU_FORCE_LP_PERI_NO_ISO_M  (PMU_FORCE_LP_PERI_NO_ISO_V << PMU_FORCE_LP_PERI_NO_ISO_S)
#define PMU_FORCE_LP_PERI_NO_ISO_V  0x00000001U
#define PMU_FORCE_LP_PERI_NO_ISO_S  4
/** PMU_FORCE_LP_PERI_PD : R/W; bitpos: [5]; default: 0;
 *  need_des
 */
#define PMU_FORCE_LP_PERI_PD    (BIT(5))
#define PMU_FORCE_LP_PERI_PD_M  (PMU_FORCE_LP_PERI_PD_V << PMU_FORCE_LP_PERI_PD_S)
#define PMU_FORCE_LP_PERI_PD_V  0x00000001U
#define PMU_FORCE_LP_PERI_PD_S  5

/** PMU_POWER_PD_LPPERI_MASK_REG register
 *  need_des
 */
#define PMU_POWER_PD_LPPERI_MASK_REG (DR_REG_PMU_BASE + 0x114)
/** PMU_XPD_LP_PERI_MASK : R/W; bitpos: [4:0]; default: 0;
 *  need_des
 */
#define PMU_XPD_LP_PERI_MASK    0x0000001FU
#define PMU_XPD_LP_PERI_MASK_M  (PMU_XPD_LP_PERI_MASK_V << PMU_XPD_LP_PERI_MASK_S)
#define PMU_XPD_LP_PERI_MASK_V  0x0000001FU
#define PMU_XPD_LP_PERI_MASK_S  0
/** PMU_PD_LP_PERI_MASK : R/W; bitpos: [31:27]; default: 0;
 *  need_des
 */
#define PMU_PD_LP_PERI_MASK    0x0000001FU
#define PMU_PD_LP_PERI_MASK_M  (PMU_PD_LP_PERI_MASK_V << PMU_PD_LP_PERI_MASK_S)
#define PMU_PD_LP_PERI_MASK_V  0x0000001FU
#define PMU_PD_LP_PERI_MASK_S  27

/** PMU_POWER_HP_PAD_REG register
 *  need_des
 */
#define PMU_POWER_HP_PAD_REG (DR_REG_PMU_BASE + 0x118)
/** PMU_FORCE_HP_PAD_NO_ISO_ALL : R/W; bitpos: [0]; default: 0;
 *  need_des
 */
#define PMU_FORCE_HP_PAD_NO_ISO_ALL    (BIT(0))
#define PMU_FORCE_HP_PAD_NO_ISO_ALL_M  (PMU_FORCE_HP_PAD_NO_ISO_ALL_V << PMU_FORCE_HP_PAD_NO_ISO_ALL_S)
#define PMU_FORCE_HP_PAD_NO_ISO_ALL_V  0x00000001U
#define PMU_FORCE_HP_PAD_NO_ISO_ALL_S  0
/** PMU_FORCE_HP_PAD_ISO_ALL : R/W; bitpos: [1]; default: 0;
 *  need_des
 */
#define PMU_FORCE_HP_PAD_ISO_ALL    (BIT(1))
#define PMU_FORCE_HP_PAD_ISO_ALL_M  (PMU_FORCE_HP_PAD_ISO_ALL_V << PMU_FORCE_HP_PAD_ISO_ALL_S)
#define PMU_FORCE_HP_PAD_ISO_ALL_V  0x00000001U
#define PMU_FORCE_HP_PAD_ISO_ALL_S  1

/** PMU_POWER_CK_WAIT_CNTL_REG register
 *  need_des
 */
#define PMU_POWER_CK_WAIT_CNTL_REG (DR_REG_PMU_BASE + 0x11c)
/** PMU_PMU_WAIT_XTL_STABLE : R/W; bitpos: [15:0]; default: 256;
 *  need_des
 */
#define PMU_PMU_WAIT_XTL_STABLE    0x0000FFFFU
#define PMU_PMU_WAIT_XTL_STABLE_M  (PMU_PMU_WAIT_XTL_STABLE_V << PMU_PMU_WAIT_XTL_STABLE_S)
#define PMU_PMU_WAIT_XTL_STABLE_V  0x0000FFFFU
#define PMU_PMU_WAIT_XTL_STABLE_S  0
/** PMU_PMU_WAIT_PLL_STABLE : R/W; bitpos: [31:16]; default: 256;
 *  need_des
 */
#define PMU_PMU_WAIT_PLL_STABLE    0x0000FFFFU
#define PMU_PMU_WAIT_PLL_STABLE_M  (PMU_PMU_WAIT_PLL_STABLE_V << PMU_PMU_WAIT_PLL_STABLE_S)
#define PMU_PMU_WAIT_PLL_STABLE_V  0x0000FFFFU
#define PMU_PMU_WAIT_PLL_STABLE_S  16

/** PMU_SLP_WAKEUP_CNTL0_REG register
 *  need_des
 */
#define PMU_SLP_WAKEUP_CNTL0_REG (DR_REG_PMU_BASE + 0x120)
/** PMU_SLEEP_REQ : WT; bitpos: [31]; default: 0;
 *  need_des
 */
#define PMU_SLEEP_REQ    (BIT(31))
#define PMU_SLEEP_REQ_M  (PMU_SLEEP_REQ_V << PMU_SLEEP_REQ_S)
#define PMU_SLEEP_REQ_V  0x00000001U
#define PMU_SLEEP_REQ_S  31

/** PMU_SLP_WAKEUP_CNTL1_REG register
 *  need_des
 */
#define PMU_SLP_WAKEUP_CNTL1_REG (DR_REG_PMU_BASE + 0x124)
/** PMU_SLEEP_REJECT_ENA : R/W; bitpos: [30:0]; default: 0;
 *  need_des
 */
#define PMU_SLEEP_REJECT_ENA    0x7FFFFFFFU
#define PMU_SLEEP_REJECT_ENA_M  (PMU_SLEEP_REJECT_ENA_V << PMU_SLEEP_REJECT_ENA_S)
#define PMU_SLEEP_REJECT_ENA_V  0x7FFFFFFFU
#define PMU_SLEEP_REJECT_ENA_S  0
/** PMU_SLP_REJECT_EN : R/W; bitpos: [31]; default: 0;
 *  need_des
 */
#define PMU_SLP_REJECT_EN    (BIT(31))
#define PMU_SLP_REJECT_EN_M  (PMU_SLP_REJECT_EN_V << PMU_SLP_REJECT_EN_S)
#define PMU_SLP_REJECT_EN_V  0x00000001U
#define PMU_SLP_REJECT_EN_S  31

/** PMU_SLP_WAKEUP_CNTL2_REG register
 *  need_des
 */
#define PMU_SLP_WAKEUP_CNTL2_REG (DR_REG_PMU_BASE + 0x128)
/** PMU_WAKEUP_ENA : R/W; bitpos: [30:0]; default: 0;
 *  need_des
 */
#define PMU_WAKEUP_ENA    0x7FFFFFFFU
#define PMU_WAKEUP_ENA_M  (PMU_WAKEUP_ENA_V << PMU_WAKEUP_ENA_S)
#define PMU_WAKEUP_ENA_V  0x7FFFFFFFU
#define PMU_WAKEUP_ENA_S  0

/** PMU_SLP_WAKEUP_CNTL3_REG register
 *  need_des
 */
#define PMU_SLP_WAKEUP_CNTL3_REG (DR_REG_PMU_BASE + 0x12c)
/** PMU_LP_MIN_SLP_VAL : R/W; bitpos: [7:0]; default: 0;
 *  need_des
 */
#define PMU_LP_MIN_SLP_VAL    0x000000FFU
#define PMU_LP_MIN_SLP_VAL_M  (PMU_LP_MIN_SLP_VAL_V << PMU_LP_MIN_SLP_VAL_S)
#define PMU_LP_MIN_SLP_VAL_V  0x000000FFU
#define PMU_LP_MIN_SLP_VAL_S  0
/** PMU_HP_MIN_SLP_VAL : R/W; bitpos: [15:8]; default: 0;
 *  need_des
 */
#define PMU_HP_MIN_SLP_VAL    0x000000FFU
#define PMU_HP_MIN_SLP_VAL_M  (PMU_HP_MIN_SLP_VAL_V << PMU_HP_MIN_SLP_VAL_S)
#define PMU_HP_MIN_SLP_VAL_V  0x000000FFU
#define PMU_HP_MIN_SLP_VAL_S  8
/** PMU_SLEEP_PRT_SEL : R/W; bitpos: [17:16]; default: 0;
 *  need_des
 */
#define PMU_SLEEP_PRT_SEL    0x00000003U
#define PMU_SLEEP_PRT_SEL_M  (PMU_SLEEP_PRT_SEL_V << PMU_SLEEP_PRT_SEL_S)
#define PMU_SLEEP_PRT_SEL_V  0x00000003U
#define PMU_SLEEP_PRT_SEL_S  16

/** PMU_SLP_WAKEUP_CNTL4_REG register
 *  need_des
 */
#define PMU_SLP_WAKEUP_CNTL4_REG (DR_REG_PMU_BASE + 0x130)
/** PMU_SLP_REJECT_CAUSE_CLR : WT; bitpos: [31]; default: 0;
 *  need_des
 */
#define PMU_SLP_REJECT_CAUSE_CLR    (BIT(31))
#define PMU_SLP_REJECT_CAUSE_CLR_M  (PMU_SLP_REJECT_CAUSE_CLR_V << PMU_SLP_REJECT_CAUSE_CLR_S)
#define PMU_SLP_REJECT_CAUSE_CLR_V  0x00000001U
#define PMU_SLP_REJECT_CAUSE_CLR_S  31

/** PMU_SLP_WAKEUP_CNTL5_REG register
 *  need_des
 */
#define PMU_SLP_WAKEUP_CNTL5_REG (DR_REG_PMU_BASE + 0x134)
/** PMU_MODEM_WAIT_TARGET : R/W; bitpos: [19:0]; default: 128;
 *  need_des
 */
#define PMU_MODEM_WAIT_TARGET    0x000FFFFFU
#define PMU_MODEM_WAIT_TARGET_M  (PMU_MODEM_WAIT_TARGET_V << PMU_MODEM_WAIT_TARGET_S)
#define PMU_MODEM_WAIT_TARGET_V  0x000FFFFFU
#define PMU_MODEM_WAIT_TARGET_S  0
/** PMU_LP_ANA_WAIT_TARGET : R/W; bitpos: [31:24]; default: 1;
 *  need_des
 */
#define PMU_LP_ANA_WAIT_TARGET    0x000000FFU
#define PMU_LP_ANA_WAIT_TARGET_M  (PMU_LP_ANA_WAIT_TARGET_V << PMU_LP_ANA_WAIT_TARGET_S)
#define PMU_LP_ANA_WAIT_TARGET_V  0x000000FFU
#define PMU_LP_ANA_WAIT_TARGET_S  24

/** PMU_SLP_WAKEUP_CNTL6_REG register
 *  need_des
 */
#define PMU_SLP_WAKEUP_CNTL6_REG (DR_REG_PMU_BASE + 0x138)
/** PMU_SOC_WAKEUP_WAIT : R/W; bitpos: [19:0]; default: 128;
 *  need_des
 */
#define PMU_SOC_WAKEUP_WAIT    0x000FFFFFU
#define PMU_SOC_WAKEUP_WAIT_M  (PMU_SOC_WAKEUP_WAIT_V << PMU_SOC_WAKEUP_WAIT_S)
#define PMU_SOC_WAKEUP_WAIT_V  0x000FFFFFU
#define PMU_SOC_WAKEUP_WAIT_S  0
/** PMU_SOC_WAKEUP_WAIT_CFG : R/W; bitpos: [31:30]; default: 0;
 *  need_des
 */
#define PMU_SOC_WAKEUP_WAIT_CFG    0x00000003U
#define PMU_SOC_WAKEUP_WAIT_CFG_M  (PMU_SOC_WAKEUP_WAIT_CFG_V << PMU_SOC_WAKEUP_WAIT_CFG_S)
#define PMU_SOC_WAKEUP_WAIT_CFG_V  0x00000003U
#define PMU_SOC_WAKEUP_WAIT_CFG_S  30

/** PMU_SLP_WAKEUP_CNTL7_REG register
 *  need_des
 */
#define PMU_SLP_WAKEUP_CNTL7_REG (DR_REG_PMU_BASE + 0x13c)
/** PMU_ANA_WAIT_TARGET : R/W; bitpos: [31:16]; default: 1;
 *  need_des
 */
#define PMU_ANA_WAIT_TARGET    0x0000FFFFU
#define PMU_ANA_WAIT_TARGET_M  (PMU_ANA_WAIT_TARGET_V << PMU_ANA_WAIT_TARGET_S)
#define PMU_ANA_WAIT_TARGET_V  0x0000FFFFU
#define PMU_ANA_WAIT_TARGET_S  16

/** PMU_SLP_WAKEUP_CNTL8_REG register
 *  need_des
 */
#define PMU_SLP_WAKEUP_CNTL8_REG (DR_REG_PMU_BASE + 0x140)
/** PMU_LP_LITE_WAKEUP_ENA : R/W; bitpos: [31]; default: 0;
 *  need_des
 */
#define PMU_LP_LITE_WAKEUP_ENA    (BIT(31))
#define PMU_LP_LITE_WAKEUP_ENA_M  (PMU_LP_LITE_WAKEUP_ENA_V << PMU_LP_LITE_WAKEUP_ENA_S)
#define PMU_LP_LITE_WAKEUP_ENA_V  0x00000001U
#define PMU_LP_LITE_WAKEUP_ENA_S  31

/** PMU_SLP_WAKEUP_STATUS0_REG register
 *  need_des
 */
#define PMU_SLP_WAKEUP_STATUS0_REG (DR_REG_PMU_BASE + 0x144)
/** PMU_WAKEUP_CAUSE : RO; bitpos: [30:0]; default: 0;
 *  need_des
 */
#define PMU_WAKEUP_CAUSE    0x7FFFFFFFU
#define PMU_WAKEUP_CAUSE_M  (PMU_WAKEUP_CAUSE_V << PMU_WAKEUP_CAUSE_S)
#define PMU_WAKEUP_CAUSE_V  0x7FFFFFFFU
#define PMU_WAKEUP_CAUSE_S  0

/** PMU_SLP_WAKEUP_STATUS1_REG register
 *  need_des
 */
#define PMU_SLP_WAKEUP_STATUS1_REG (DR_REG_PMU_BASE + 0x148)
/** PMU_REJECT_CAUSE : RO; bitpos: [30:0]; default: 0;
 *  need_des
 */
#define PMU_REJECT_CAUSE    0x7FFFFFFFU
#define PMU_REJECT_CAUSE_M  (PMU_REJECT_CAUSE_V << PMU_REJECT_CAUSE_S)
#define PMU_REJECT_CAUSE_V  0x7FFFFFFFU
#define PMU_REJECT_CAUSE_S  0

/** PMU_SLP_WAKEUP_STATUS2_REG register
 *  need_des
 */
#define PMU_SLP_WAKEUP_STATUS2_REG (DR_REG_PMU_BASE + 0x14c)
/** PMU_LP_LITE_WAKEUP_CAUSE : RO; bitpos: [31]; default: 0;
 *  need_des
 */
#define PMU_LP_LITE_WAKEUP_CAUSE    (BIT(31))
#define PMU_LP_LITE_WAKEUP_CAUSE_M  (PMU_LP_LITE_WAKEUP_CAUSE_V << PMU_LP_LITE_WAKEUP_CAUSE_S)
#define PMU_LP_LITE_WAKEUP_CAUSE_V  0x00000001U
#define PMU_LP_LITE_WAKEUP_CAUSE_S  31

/** PMU_HP_CK_POWERON_REG register
 *  need_des
 */
#define PMU_HP_CK_POWERON_REG (DR_REG_PMU_BASE + 0x150)
/** PMU_I2C_POR_WAIT_TARGET : R/W; bitpos: [7:0]; default: 50;
 *  need_des
 */
#define PMU_I2C_POR_WAIT_TARGET    0x000000FFU
#define PMU_I2C_POR_WAIT_TARGET_M  (PMU_I2C_POR_WAIT_TARGET_V << PMU_I2C_POR_WAIT_TARGET_S)
#define PMU_I2C_POR_WAIT_TARGET_V  0x000000FFU
#define PMU_I2C_POR_WAIT_TARGET_S  0

/** PMU_HP_CK_CNTL_REG register
 *  need_des
 */
#define PMU_HP_CK_CNTL_REG (DR_REG_PMU_BASE + 0x154)
/** PMU_MODIFY_ICG_CNTL_WAIT : R/W; bitpos: [7:0]; default: 10;
 *  need_des
 */
#define PMU_MODIFY_ICG_CNTL_WAIT    0x000000FFU
#define PMU_MODIFY_ICG_CNTL_WAIT_M  (PMU_MODIFY_ICG_CNTL_WAIT_V << PMU_MODIFY_ICG_CNTL_WAIT_S)
#define PMU_MODIFY_ICG_CNTL_WAIT_V  0x000000FFU
#define PMU_MODIFY_ICG_CNTL_WAIT_S  0
/** PMU_SWITCH_ICG_CNTL_WAIT : R/W; bitpos: [15:8]; default: 10;
 *  need_des
 */
#define PMU_SWITCH_ICG_CNTL_WAIT    0x000000FFU
#define PMU_SWITCH_ICG_CNTL_WAIT_M  (PMU_SWITCH_ICG_CNTL_WAIT_V << PMU_SWITCH_ICG_CNTL_WAIT_S)
#define PMU_SWITCH_ICG_CNTL_WAIT_V  0x000000FFU
#define PMU_SWITCH_ICG_CNTL_WAIT_S  8

/** PMU_POR_STATUS_REG register
 *  need_des
 */
#define PMU_POR_STATUS_REG (DR_REG_PMU_BASE + 0x158)
/** PMU_POR_DONE : RO; bitpos: [31]; default: 1;
 *  need_des
 */
#define PMU_POR_DONE    (BIT(31))
#define PMU_POR_DONE_M  (PMU_POR_DONE_V << PMU_POR_DONE_S)
#define PMU_POR_DONE_V  0x00000001U
#define PMU_POR_DONE_S  31

/** PMU_RF_PWC_REG register
 *  need_des
 */
#define PMU_RF_PWC_REG (DR_REG_PMU_BASE + 0x15c)
/** PMU_MSPI_PHY_XPD : R/W; bitpos: [24]; default: 0;
 *  need_des
 */
#define PMU_MSPI_PHY_XPD    (BIT(24))
#define PMU_MSPI_PHY_XPD_M  (PMU_MSPI_PHY_XPD_V << PMU_MSPI_PHY_XPD_S)
#define PMU_MSPI_PHY_XPD_V  0x00000001U
#define PMU_MSPI_PHY_XPD_S  24
/** PMU_SDIO_PLL_XPD : R/W; bitpos: [25]; default: 0;
 *  need_des
 */
#define PMU_SDIO_PLL_XPD    (BIT(25))
#define PMU_SDIO_PLL_XPD_M  (PMU_SDIO_PLL_XPD_V << PMU_SDIO_PLL_XPD_S)
#define PMU_SDIO_PLL_XPD_V  0x00000001U
#define PMU_SDIO_PLL_XPD_S  25
/** PMU_PERIF_I2C_RSTB : R/W; bitpos: [26]; default: 0;
 *  need_des
 */
#define PMU_PERIF_I2C_RSTB    (BIT(26))
#define PMU_PERIF_I2C_RSTB_M  (PMU_PERIF_I2C_RSTB_V << PMU_PERIF_I2C_RSTB_S)
#define PMU_PERIF_I2C_RSTB_V  0x00000001U
#define PMU_PERIF_I2C_RSTB_S  26
/** PMU_XPD_PERIF_I2C : R/W; bitpos: [27]; default: 1;
 *  need_des
 */
#define PMU_XPD_PERIF_I2C    (BIT(27))
#define PMU_XPD_PERIF_I2C_M  (PMU_XPD_PERIF_I2C_V << PMU_XPD_PERIF_I2C_S)
#define PMU_XPD_PERIF_I2C_V  0x00000001U
#define PMU_XPD_PERIF_I2C_S  27
/** PMU_XPD_TXRF_I2C : R/W; bitpos: [28]; default: 0;
 *  need_des
 */
#define PMU_XPD_TXRF_I2C    (BIT(28))
#define PMU_XPD_TXRF_I2C_M  (PMU_XPD_TXRF_I2C_V << PMU_XPD_TXRF_I2C_S)
#define PMU_XPD_TXRF_I2C_V  0x00000001U
#define PMU_XPD_TXRF_I2C_S  28
/** PMU_XPD_RFRX_PBUS : R/W; bitpos: [29]; default: 0;
 *  need_des
 */
#define PMU_XPD_RFRX_PBUS    (BIT(29))
#define PMU_XPD_RFRX_PBUS_M  (PMU_XPD_RFRX_PBUS_V << PMU_XPD_RFRX_PBUS_S)
#define PMU_XPD_RFRX_PBUS_V  0x00000001U
#define PMU_XPD_RFRX_PBUS_S  29
/** PMU_XPD_CKGEN_I2C : R/W; bitpos: [30]; default: 0;
 *  need_des
 */
#define PMU_XPD_CKGEN_I2C    (BIT(30))
#define PMU_XPD_CKGEN_I2C_M  (PMU_XPD_CKGEN_I2C_V << PMU_XPD_CKGEN_I2C_S)
#define PMU_XPD_CKGEN_I2C_V  0x00000001U
#define PMU_XPD_CKGEN_I2C_S  30

/** PMU_BACKUP_CFG_REG register
 *  need_des
 */
#define PMU_BACKUP_CFG_REG (DR_REG_PMU_BASE + 0x160)
/** PMU_BACKUP_SYS_CLK_NO_DIV : R/W; bitpos: [31]; default: 1;
 *  need_des
 */
#define PMU_BACKUP_SYS_CLK_NO_DIV    (BIT(31))
#define PMU_BACKUP_SYS_CLK_NO_DIV_M  (PMU_BACKUP_SYS_CLK_NO_DIV_V << PMU_BACKUP_SYS_CLK_NO_DIV_S)
#define PMU_BACKUP_SYS_CLK_NO_DIV_V  0x00000001U
#define PMU_BACKUP_SYS_CLK_NO_DIV_S  31

/** PMU_INT_RAW_REG register
 *  need_des
 */
#define PMU_INT_RAW_REG (DR_REG_PMU_BASE + 0x164)
/** PMU_0P1A_CNT_TARGET0_REACH_0_HP_INT_RAW : R/WTC/SS; bitpos: [14]; default: 0;
 *  reg_0p1a_0_counter after xpd reach target0
 */
#define PMU_0P1A_CNT_TARGET0_REACH_0_HP_INT_RAW    (BIT(14))
#define PMU_0P1A_CNT_TARGET0_REACH_0_HP_INT_RAW_M  (PMU_0P1A_CNT_TARGET0_REACH_0_HP_INT_RAW_V << PMU_0P1A_CNT_TARGET0_REACH_0_HP_INT_RAW_S)
#define PMU_0P1A_CNT_TARGET0_REACH_0_HP_INT_RAW_V  0x00000001U
#define PMU_0P1A_CNT_TARGET0_REACH_0_HP_INT_RAW_S  14
/** PMU_0P1A_CNT_TARGET1_REACH_0_HP_INT_RAW : R/WTC/SS; bitpos: [15]; default: 0;
 *  reg_0p1a_1_counter after xpd reach target1
 */
#define PMU_0P1A_CNT_TARGET1_REACH_0_HP_INT_RAW    (BIT(15))
#define PMU_0P1A_CNT_TARGET1_REACH_0_HP_INT_RAW_M  (PMU_0P1A_CNT_TARGET1_REACH_0_HP_INT_RAW_V << PMU_0P1A_CNT_TARGET1_REACH_0_HP_INT_RAW_S)
#define PMU_0P1A_CNT_TARGET1_REACH_0_HP_INT_RAW_V  0x00000001U
#define PMU_0P1A_CNT_TARGET1_REACH_0_HP_INT_RAW_S  15
/** PMU_0P1A_CNT_TARGET0_REACH_1_HP_INT_RAW : R/WTC/SS; bitpos: [16]; default: 0;
 *  reg_0p1a_0 counter after xpd reach target0
 */
#define PMU_0P1A_CNT_TARGET0_REACH_1_HP_INT_RAW    (BIT(16))
#define PMU_0P1A_CNT_TARGET0_REACH_1_HP_INT_RAW_M  (PMU_0P1A_CNT_TARGET0_REACH_1_HP_INT_RAW_V << PMU_0P1A_CNT_TARGET0_REACH_1_HP_INT_RAW_S)
#define PMU_0P1A_CNT_TARGET0_REACH_1_HP_INT_RAW_V  0x00000001U
#define PMU_0P1A_CNT_TARGET0_REACH_1_HP_INT_RAW_S  16
/** PMU_0P1A_CNT_TARGET1_REACH_1_HP_INT_RAW : R/WTC/SS; bitpos: [17]; default: 0;
 *  reg_0p1a_1_counter after xpd reach target1
 */
#define PMU_0P1A_CNT_TARGET1_REACH_1_HP_INT_RAW    (BIT(17))
#define PMU_0P1A_CNT_TARGET1_REACH_1_HP_INT_RAW_M  (PMU_0P1A_CNT_TARGET1_REACH_1_HP_INT_RAW_V << PMU_0P1A_CNT_TARGET1_REACH_1_HP_INT_RAW_S)
#define PMU_0P1A_CNT_TARGET1_REACH_1_HP_INT_RAW_V  0x00000001U
#define PMU_0P1A_CNT_TARGET1_REACH_1_HP_INT_RAW_S  17
/** PMU_0P2A_CNT_TARGET0_REACH_0_HP_INT_RAW : R/WTC/SS; bitpos: [18]; default: 0;
 *  reg_0p2a_0 counter after xpd reach target0
 */
#define PMU_0P2A_CNT_TARGET0_REACH_0_HP_INT_RAW    (BIT(18))
#define PMU_0P2A_CNT_TARGET0_REACH_0_HP_INT_RAW_M  (PMU_0P2A_CNT_TARGET0_REACH_0_HP_INT_RAW_V << PMU_0P2A_CNT_TARGET0_REACH_0_HP_INT_RAW_S)
#define PMU_0P2A_CNT_TARGET0_REACH_0_HP_INT_RAW_V  0x00000001U
#define PMU_0P2A_CNT_TARGET0_REACH_0_HP_INT_RAW_S  18
/** PMU_0P2A_CNT_TARGET1_REACH_0_HP_INT_RAW : R/WTC/SS; bitpos: [19]; default: 0;
 *  reg_0p2a_1_counter after xpd reach target1
 */
#define PMU_0P2A_CNT_TARGET1_REACH_0_HP_INT_RAW    (BIT(19))
#define PMU_0P2A_CNT_TARGET1_REACH_0_HP_INT_RAW_M  (PMU_0P2A_CNT_TARGET1_REACH_0_HP_INT_RAW_V << PMU_0P2A_CNT_TARGET1_REACH_0_HP_INT_RAW_S)
#define PMU_0P2A_CNT_TARGET1_REACH_0_HP_INT_RAW_V  0x00000001U
#define PMU_0P2A_CNT_TARGET1_REACH_0_HP_INT_RAW_S  19
/** PMU_0P2A_CNT_TARGET0_REACH_1_HP_INT_RAW : R/WTC/SS; bitpos: [20]; default: 0;
 *  reg_0p2a_0 counter after xpd reach target0
 */
#define PMU_0P2A_CNT_TARGET0_REACH_1_HP_INT_RAW    (BIT(20))
#define PMU_0P2A_CNT_TARGET0_REACH_1_HP_INT_RAW_M  (PMU_0P2A_CNT_TARGET0_REACH_1_HP_INT_RAW_V << PMU_0P2A_CNT_TARGET0_REACH_1_HP_INT_RAW_S)
#define PMU_0P2A_CNT_TARGET0_REACH_1_HP_INT_RAW_V  0x00000001U
#define PMU_0P2A_CNT_TARGET0_REACH_1_HP_INT_RAW_S  20
/** PMU_0P2A_CNT_TARGET1_REACH_1_HP_INT_RAW : R/WTC/SS; bitpos: [21]; default: 0;
 *  reg_0p2a_1_counter after xpd reach target1
 */
#define PMU_0P2A_CNT_TARGET1_REACH_1_HP_INT_RAW    (BIT(21))
#define PMU_0P2A_CNT_TARGET1_REACH_1_HP_INT_RAW_M  (PMU_0P2A_CNT_TARGET1_REACH_1_HP_INT_RAW_V << PMU_0P2A_CNT_TARGET1_REACH_1_HP_INT_RAW_S)
#define PMU_0P2A_CNT_TARGET1_REACH_1_HP_INT_RAW_V  0x00000001U
#define PMU_0P2A_CNT_TARGET1_REACH_1_HP_INT_RAW_S  21
/** PMU_0P3A_CNT_TARGET0_REACH_0_HP_INT_RAW : R/WTC/SS; bitpos: [22]; default: 0;
 *  reg_0p3a_0 counter after xpd reach target0
 */
#define PMU_0P3A_CNT_TARGET0_REACH_0_HP_INT_RAW    (BIT(22))
#define PMU_0P3A_CNT_TARGET0_REACH_0_HP_INT_RAW_M  (PMU_0P3A_CNT_TARGET0_REACH_0_HP_INT_RAW_V << PMU_0P3A_CNT_TARGET0_REACH_0_HP_INT_RAW_S)
#define PMU_0P3A_CNT_TARGET0_REACH_0_HP_INT_RAW_V  0x00000001U
#define PMU_0P3A_CNT_TARGET0_REACH_0_HP_INT_RAW_S  22
/** PMU_0P3A_CNT_TARGET1_REACH_0_HP_INT_RAW : R/WTC/SS; bitpos: [23]; default: 0;
 *  reg_0p3a_1_counter after xpd reach target1
 */
#define PMU_0P3A_CNT_TARGET1_REACH_0_HP_INT_RAW    (BIT(23))
#define PMU_0P3A_CNT_TARGET1_REACH_0_HP_INT_RAW_M  (PMU_0P3A_CNT_TARGET1_REACH_0_HP_INT_RAW_V << PMU_0P3A_CNT_TARGET1_REACH_0_HP_INT_RAW_S)
#define PMU_0P3A_CNT_TARGET1_REACH_0_HP_INT_RAW_V  0x00000001U
#define PMU_0P3A_CNT_TARGET1_REACH_0_HP_INT_RAW_S  23
/** PMU_0P3A_CNT_TARGET0_REACH_1_HP_INT_RAW : R/WTC/SS; bitpos: [24]; default: 0;
 *  reg_0p3a_0_counter after xpd reach target0
 */
#define PMU_0P3A_CNT_TARGET0_REACH_1_HP_INT_RAW    (BIT(24))
#define PMU_0P3A_CNT_TARGET0_REACH_1_HP_INT_RAW_M  (PMU_0P3A_CNT_TARGET0_REACH_1_HP_INT_RAW_V << PMU_0P3A_CNT_TARGET0_REACH_1_HP_INT_RAW_S)
#define PMU_0P3A_CNT_TARGET0_REACH_1_HP_INT_RAW_V  0x00000001U
#define PMU_0P3A_CNT_TARGET0_REACH_1_HP_INT_RAW_S  24
/** PMU_0P3A_CNT_TARGET1_REACH_1_HP_INT_RAW : R/WTC/SS; bitpos: [25]; default: 0;
 *  reg_0p3a_1_counter after xpd reach target1
 */
#define PMU_0P3A_CNT_TARGET1_REACH_1_HP_INT_RAW    (BIT(25))
#define PMU_0P3A_CNT_TARGET1_REACH_1_HP_INT_RAW_M  (PMU_0P3A_CNT_TARGET1_REACH_1_HP_INT_RAW_V << PMU_0P3A_CNT_TARGET1_REACH_1_HP_INT_RAW_S)
#define PMU_0P3A_CNT_TARGET1_REACH_1_HP_INT_RAW_V  0x00000001U
#define PMU_0P3A_CNT_TARGET1_REACH_1_HP_INT_RAW_S  25
/** PMU_LP_CPU_EXC_INT_RAW : R/WTC/SS; bitpos: [27]; default: 0;
 *  need_des
 */
#define PMU_LP_CPU_EXC_INT_RAW    (BIT(27))
#define PMU_LP_CPU_EXC_INT_RAW_M  (PMU_LP_CPU_EXC_INT_RAW_V << PMU_LP_CPU_EXC_INT_RAW_S)
#define PMU_LP_CPU_EXC_INT_RAW_V  0x00000001U
#define PMU_LP_CPU_EXC_INT_RAW_S  27
/** PMU_SDIO_IDLE_INT_RAW : R/WTC/SS; bitpos: [28]; default: 0;
 *  need_des
 */
#define PMU_SDIO_IDLE_INT_RAW    (BIT(28))
#define PMU_SDIO_IDLE_INT_RAW_M  (PMU_SDIO_IDLE_INT_RAW_V << PMU_SDIO_IDLE_INT_RAW_S)
#define PMU_SDIO_IDLE_INT_RAW_V  0x00000001U
#define PMU_SDIO_IDLE_INT_RAW_S  28
/** PMU_SW_INT_RAW : R/WTC/SS; bitpos: [29]; default: 0;
 *  need_des
 */
#define PMU_SW_INT_RAW    (BIT(29))
#define PMU_SW_INT_RAW_M  (PMU_SW_INT_RAW_V << PMU_SW_INT_RAW_S)
#define PMU_SW_INT_RAW_V  0x00000001U
#define PMU_SW_INT_RAW_S  29
/** PMU_SOC_SLEEP_REJECT_INT_RAW : R/WTC/SS; bitpos: [30]; default: 0;
 *  need_des
 */
#define PMU_SOC_SLEEP_REJECT_INT_RAW    (BIT(30))
#define PMU_SOC_SLEEP_REJECT_INT_RAW_M  (PMU_SOC_SLEEP_REJECT_INT_RAW_V << PMU_SOC_SLEEP_REJECT_INT_RAW_S)
#define PMU_SOC_SLEEP_REJECT_INT_RAW_V  0x00000001U
#define PMU_SOC_SLEEP_REJECT_INT_RAW_S  30
/** PMU_SOC_WAKEUP_INT_RAW : R/WTC/SS; bitpos: [31]; default: 0;
 *  need_des
 */
#define PMU_SOC_WAKEUP_INT_RAW    (BIT(31))
#define PMU_SOC_WAKEUP_INT_RAW_M  (PMU_SOC_WAKEUP_INT_RAW_V << PMU_SOC_WAKEUP_INT_RAW_S)
#define PMU_SOC_WAKEUP_INT_RAW_V  0x00000001U
#define PMU_SOC_WAKEUP_INT_RAW_S  31

/** PMU_HP_INT_ST_REG register
 *  need_des
 */
#define PMU_HP_INT_ST_REG (DR_REG_PMU_BASE + 0x168)
/** PMU_0P1A_CNT_TARGET0_REACH_0_HP_INT_ST : RO; bitpos: [14]; default: 0;
 *  reg_0p1a_0_counter after xpd reach target0
 */
#define PMU_0P1A_CNT_TARGET0_REACH_0_HP_INT_ST    (BIT(14))
#define PMU_0P1A_CNT_TARGET0_REACH_0_HP_INT_ST_M  (PMU_0P1A_CNT_TARGET0_REACH_0_HP_INT_ST_V << PMU_0P1A_CNT_TARGET0_REACH_0_HP_INT_ST_S)
#define PMU_0P1A_CNT_TARGET0_REACH_0_HP_INT_ST_V  0x00000001U
#define PMU_0P1A_CNT_TARGET0_REACH_0_HP_INT_ST_S  14
/** PMU_0P1A_CNT_TARGET1_REACH_0_HP_INT_ST : RO; bitpos: [15]; default: 0;
 *  reg_0p1a_1_counter after xpd reach target1
 */
#define PMU_0P1A_CNT_TARGET1_REACH_0_HP_INT_ST    (BIT(15))
#define PMU_0P1A_CNT_TARGET1_REACH_0_HP_INT_ST_M  (PMU_0P1A_CNT_TARGET1_REACH_0_HP_INT_ST_V << PMU_0P1A_CNT_TARGET1_REACH_0_HP_INT_ST_S)
#define PMU_0P1A_CNT_TARGET1_REACH_0_HP_INT_ST_V  0x00000001U
#define PMU_0P1A_CNT_TARGET1_REACH_0_HP_INT_ST_S  15
/** PMU_0P1A_CNT_TARGET0_REACH_1_HP_INT_ST : RO; bitpos: [16]; default: 0;
 *  reg_0p1a_0 counter after xpd reach target0
 */
#define PMU_0P1A_CNT_TARGET0_REACH_1_HP_INT_ST    (BIT(16))
#define PMU_0P1A_CNT_TARGET0_REACH_1_HP_INT_ST_M  (PMU_0P1A_CNT_TARGET0_REACH_1_HP_INT_ST_V << PMU_0P1A_CNT_TARGET0_REACH_1_HP_INT_ST_S)
#define PMU_0P1A_CNT_TARGET0_REACH_1_HP_INT_ST_V  0x00000001U
#define PMU_0P1A_CNT_TARGET0_REACH_1_HP_INT_ST_S  16
/** PMU_0P1A_CNT_TARGET1_REACH_1_HP_INT_ST : RO; bitpos: [17]; default: 0;
 *  reg_0p1a_1_counter after xpd reach target1
 */
#define PMU_0P1A_CNT_TARGET1_REACH_1_HP_INT_ST    (BIT(17))
#define PMU_0P1A_CNT_TARGET1_REACH_1_HP_INT_ST_M  (PMU_0P1A_CNT_TARGET1_REACH_1_HP_INT_ST_V << PMU_0P1A_CNT_TARGET1_REACH_1_HP_INT_ST_S)
#define PMU_0P1A_CNT_TARGET1_REACH_1_HP_INT_ST_V  0x00000001U
#define PMU_0P1A_CNT_TARGET1_REACH_1_HP_INT_ST_S  17
/** PMU_0P2A_CNT_TARGET0_REACH_0_HP_INT_ST : RO; bitpos: [18]; default: 0;
 *  reg_0p2a_0 counter after xpd reach target0
 */
#define PMU_0P2A_CNT_TARGET0_REACH_0_HP_INT_ST    (BIT(18))
#define PMU_0P2A_CNT_TARGET0_REACH_0_HP_INT_ST_M  (PMU_0P2A_CNT_TARGET0_REACH_0_HP_INT_ST_V << PMU_0P2A_CNT_TARGET0_REACH_0_HP_INT_ST_S)
#define PMU_0P2A_CNT_TARGET0_REACH_0_HP_INT_ST_V  0x00000001U
#define PMU_0P2A_CNT_TARGET0_REACH_0_HP_INT_ST_S  18
/** PMU_0P2A_CNT_TARGET1_REACH_0_HP_INT_ST : RO; bitpos: [19]; default: 0;
 *  reg_0p2a_1_counter after xpd reach target1
 */
#define PMU_0P2A_CNT_TARGET1_REACH_0_HP_INT_ST    (BIT(19))
#define PMU_0P2A_CNT_TARGET1_REACH_0_HP_INT_ST_M  (PMU_0P2A_CNT_TARGET1_REACH_0_HP_INT_ST_V << PMU_0P2A_CNT_TARGET1_REACH_0_HP_INT_ST_S)
#define PMU_0P2A_CNT_TARGET1_REACH_0_HP_INT_ST_V  0x00000001U
#define PMU_0P2A_CNT_TARGET1_REACH_0_HP_INT_ST_S  19
/** PMU_0P2A_CNT_TARGET0_REACH_1_HP_INT_ST : RO; bitpos: [20]; default: 0;
 *  reg_0p2a_0 counter after xpd reach target0
 */
#define PMU_0P2A_CNT_TARGET0_REACH_1_HP_INT_ST    (BIT(20))
#define PMU_0P2A_CNT_TARGET0_REACH_1_HP_INT_ST_M  (PMU_0P2A_CNT_TARGET0_REACH_1_HP_INT_ST_V << PMU_0P2A_CNT_TARGET0_REACH_1_HP_INT_ST_S)
#define PMU_0P2A_CNT_TARGET0_REACH_1_HP_INT_ST_V  0x00000001U
#define PMU_0P2A_CNT_TARGET0_REACH_1_HP_INT_ST_S  20
/** PMU_0P2A_CNT_TARGET1_REACH_1_HP_INT_ST : RO; bitpos: [21]; default: 0;
 *  reg_0p2a_1_counter after xpd reach target1
 */
#define PMU_0P2A_CNT_TARGET1_REACH_1_HP_INT_ST    (BIT(21))
#define PMU_0P2A_CNT_TARGET1_REACH_1_HP_INT_ST_M  (PMU_0P2A_CNT_TARGET1_REACH_1_HP_INT_ST_V << PMU_0P2A_CNT_TARGET1_REACH_1_HP_INT_ST_S)
#define PMU_0P2A_CNT_TARGET1_REACH_1_HP_INT_ST_V  0x00000001U
#define PMU_0P2A_CNT_TARGET1_REACH_1_HP_INT_ST_S  21
/** PMU_0P3A_CNT_TARGET0_REACH_0_HP_INT_ST : RO; bitpos: [22]; default: 0;
 *  reg_0p3a_0 counter after xpd reach target0
 */
#define PMU_0P3A_CNT_TARGET0_REACH_0_HP_INT_ST    (BIT(22))
#define PMU_0P3A_CNT_TARGET0_REACH_0_HP_INT_ST_M  (PMU_0P3A_CNT_TARGET0_REACH_0_HP_INT_ST_V << PMU_0P3A_CNT_TARGET0_REACH_0_HP_INT_ST_S)
#define PMU_0P3A_CNT_TARGET0_REACH_0_HP_INT_ST_V  0x00000001U
#define PMU_0P3A_CNT_TARGET0_REACH_0_HP_INT_ST_S  22
/** PMU_0P3A_CNT_TARGET1_REACH_0_HP_INT_ST : RO; bitpos: [23]; default: 0;
 *  reg_0p3a_1_counter after xpd reach target1
 */
#define PMU_0P3A_CNT_TARGET1_REACH_0_HP_INT_ST    (BIT(23))
#define PMU_0P3A_CNT_TARGET1_REACH_0_HP_INT_ST_M  (PMU_0P3A_CNT_TARGET1_REACH_0_HP_INT_ST_V << PMU_0P3A_CNT_TARGET1_REACH_0_HP_INT_ST_S)
#define PMU_0P3A_CNT_TARGET1_REACH_0_HP_INT_ST_V  0x00000001U
#define PMU_0P3A_CNT_TARGET1_REACH_0_HP_INT_ST_S  23
/** PMU_0P3A_CNT_TARGET0_REACH_1_HP_INT_ST : RO; bitpos: [24]; default: 0;
 *  reg_0p3a_0_counter after xpd reach target0
 */
#define PMU_0P3A_CNT_TARGET0_REACH_1_HP_INT_ST    (BIT(24))
#define PMU_0P3A_CNT_TARGET0_REACH_1_HP_INT_ST_M  (PMU_0P3A_CNT_TARGET0_REACH_1_HP_INT_ST_V << PMU_0P3A_CNT_TARGET0_REACH_1_HP_INT_ST_S)
#define PMU_0P3A_CNT_TARGET0_REACH_1_HP_INT_ST_V  0x00000001U
#define PMU_0P3A_CNT_TARGET0_REACH_1_HP_INT_ST_S  24
/** PMU_0P3A_CNT_TARGET1_REACH_1_HP_INT_ST : RO; bitpos: [25]; default: 0;
 *  reg_0p3a_1_counter after xpd reach target1
 */
#define PMU_0P3A_CNT_TARGET1_REACH_1_HP_INT_ST    (BIT(25))
#define PMU_0P3A_CNT_TARGET1_REACH_1_HP_INT_ST_M  (PMU_0P3A_CNT_TARGET1_REACH_1_HP_INT_ST_V << PMU_0P3A_CNT_TARGET1_REACH_1_HP_INT_ST_S)
#define PMU_0P3A_CNT_TARGET1_REACH_1_HP_INT_ST_V  0x00000001U
#define PMU_0P3A_CNT_TARGET1_REACH_1_HP_INT_ST_S  25
/** PMU_LP_CPU_EXC_INT_ST : RO; bitpos: [27]; default: 0;
 *  need_des
 */
#define PMU_LP_CPU_EXC_INT_ST    (BIT(27))
#define PMU_LP_CPU_EXC_INT_ST_M  (PMU_LP_CPU_EXC_INT_ST_V << PMU_LP_CPU_EXC_INT_ST_S)
#define PMU_LP_CPU_EXC_INT_ST_V  0x00000001U
#define PMU_LP_CPU_EXC_INT_ST_S  27
/** PMU_SDIO_IDLE_INT_ST : RO; bitpos: [28]; default: 0;
 *  need_des
 */
#define PMU_SDIO_IDLE_INT_ST    (BIT(28))
#define PMU_SDIO_IDLE_INT_ST_M  (PMU_SDIO_IDLE_INT_ST_V << PMU_SDIO_IDLE_INT_ST_S)
#define PMU_SDIO_IDLE_INT_ST_V  0x00000001U
#define PMU_SDIO_IDLE_INT_ST_S  28
/** PMU_SW_INT_ST : RO; bitpos: [29]; default: 0;
 *  need_des
 */
#define PMU_SW_INT_ST    (BIT(29))
#define PMU_SW_INT_ST_M  (PMU_SW_INT_ST_V << PMU_SW_INT_ST_S)
#define PMU_SW_INT_ST_V  0x00000001U
#define PMU_SW_INT_ST_S  29
/** PMU_SOC_SLEEP_REJECT_INT_ST : RO; bitpos: [30]; default: 0;
 *  need_des
 */
#define PMU_SOC_SLEEP_REJECT_INT_ST    (BIT(30))
#define PMU_SOC_SLEEP_REJECT_INT_ST_M  (PMU_SOC_SLEEP_REJECT_INT_ST_V << PMU_SOC_SLEEP_REJECT_INT_ST_S)
#define PMU_SOC_SLEEP_REJECT_INT_ST_V  0x00000001U
#define PMU_SOC_SLEEP_REJECT_INT_ST_S  30
/** PMU_SOC_WAKEUP_INT_ST : RO; bitpos: [31]; default: 0;
 *  need_des
 */
#define PMU_SOC_WAKEUP_INT_ST    (BIT(31))
#define PMU_SOC_WAKEUP_INT_ST_M  (PMU_SOC_WAKEUP_INT_ST_V << PMU_SOC_WAKEUP_INT_ST_S)
#define PMU_SOC_WAKEUP_INT_ST_V  0x00000001U
#define PMU_SOC_WAKEUP_INT_ST_S  31

/** PMU_HP_INT_ENA_REG register
 *  need_des
 */
#define PMU_HP_INT_ENA_REG (DR_REG_PMU_BASE + 0x16c)
/** PMU_0P1A_CNT_TARGET0_REACH_0_HP_INT_ENA : R/W; bitpos: [14]; default: 0;
 *  reg_0p1a_0_counter after xpd reach target0
 */
#define PMU_0P1A_CNT_TARGET0_REACH_0_HP_INT_ENA    (BIT(14))
#define PMU_0P1A_CNT_TARGET0_REACH_0_HP_INT_ENA_M  (PMU_0P1A_CNT_TARGET0_REACH_0_HP_INT_ENA_V << PMU_0P1A_CNT_TARGET0_REACH_0_HP_INT_ENA_S)
#define PMU_0P1A_CNT_TARGET0_REACH_0_HP_INT_ENA_V  0x00000001U
#define PMU_0P1A_CNT_TARGET0_REACH_0_HP_INT_ENA_S  14
/** PMU_0P1A_CNT_TARGET1_REACH_0_HP_INT_ENA : R/W; bitpos: [15]; default: 0;
 *  reg_0p1a_1_counter after xpd reach target1
 */
#define PMU_0P1A_CNT_TARGET1_REACH_0_HP_INT_ENA    (BIT(15))
#define PMU_0P1A_CNT_TARGET1_REACH_0_HP_INT_ENA_M  (PMU_0P1A_CNT_TARGET1_REACH_0_HP_INT_ENA_V << PMU_0P1A_CNT_TARGET1_REACH_0_HP_INT_ENA_S)
#define PMU_0P1A_CNT_TARGET1_REACH_0_HP_INT_ENA_V  0x00000001U
#define PMU_0P1A_CNT_TARGET1_REACH_0_HP_INT_ENA_S  15
/** PMU_0P1A_CNT_TARGET0_REACH_1_HP_INT_ENA : R/W; bitpos: [16]; default: 0;
 *  reg_0p1a_0 counter after xpd reach target0
 */
#define PMU_0P1A_CNT_TARGET0_REACH_1_HP_INT_ENA    (BIT(16))
#define PMU_0P1A_CNT_TARGET0_REACH_1_HP_INT_ENA_M  (PMU_0P1A_CNT_TARGET0_REACH_1_HP_INT_ENA_V << PMU_0P1A_CNT_TARGET0_REACH_1_HP_INT_ENA_S)
#define PMU_0P1A_CNT_TARGET0_REACH_1_HP_INT_ENA_V  0x00000001U
#define PMU_0P1A_CNT_TARGET0_REACH_1_HP_INT_ENA_S  16
/** PMU_0P1A_CNT_TARGET1_REACH_1_HP_INT_ENA : R/W; bitpos: [17]; default: 0;
 *  reg_0p1a_1_counter after xpd reach target1
 */
#define PMU_0P1A_CNT_TARGET1_REACH_1_HP_INT_ENA    (BIT(17))
#define PMU_0P1A_CNT_TARGET1_REACH_1_HP_INT_ENA_M  (PMU_0P1A_CNT_TARGET1_REACH_1_HP_INT_ENA_V << PMU_0P1A_CNT_TARGET1_REACH_1_HP_INT_ENA_S)
#define PMU_0P1A_CNT_TARGET1_REACH_1_HP_INT_ENA_V  0x00000001U
#define PMU_0P1A_CNT_TARGET1_REACH_1_HP_INT_ENA_S  17
/** PMU_0P2A_CNT_TARGET0_REACH_0_HP_INT_ENA : R/W; bitpos: [18]; default: 0;
 *  reg_0p2a_0 counter after xpd reach target0
 */
#define PMU_0P2A_CNT_TARGET0_REACH_0_HP_INT_ENA    (BIT(18))
#define PMU_0P2A_CNT_TARGET0_REACH_0_HP_INT_ENA_M  (PMU_0P2A_CNT_TARGET0_REACH_0_HP_INT_ENA_V << PMU_0P2A_CNT_TARGET0_REACH_0_HP_INT_ENA_S)
#define PMU_0P2A_CNT_TARGET0_REACH_0_HP_INT_ENA_V  0x00000001U
#define PMU_0P2A_CNT_TARGET0_REACH_0_HP_INT_ENA_S  18
/** PMU_0P2A_CNT_TARGET1_REACH_0_HP_INT_ENA : R/W; bitpos: [19]; default: 0;
 *  reg_0p2a_1_counter after xpd reach target1
 */
#define PMU_0P2A_CNT_TARGET1_REACH_0_HP_INT_ENA    (BIT(19))
#define PMU_0P2A_CNT_TARGET1_REACH_0_HP_INT_ENA_M  (PMU_0P2A_CNT_TARGET1_REACH_0_HP_INT_ENA_V << PMU_0P2A_CNT_TARGET1_REACH_0_HP_INT_ENA_S)
#define PMU_0P2A_CNT_TARGET1_REACH_0_HP_INT_ENA_V  0x00000001U
#define PMU_0P2A_CNT_TARGET1_REACH_0_HP_INT_ENA_S  19
/** PMU_0P2A_CNT_TARGET0_REACH_1_HP_INT_ENA : R/W; bitpos: [20]; default: 0;
 *  reg_0p2a_0 counter after xpd reach target0
 */
#define PMU_0P2A_CNT_TARGET0_REACH_1_HP_INT_ENA    (BIT(20))
#define PMU_0P2A_CNT_TARGET0_REACH_1_HP_INT_ENA_M  (PMU_0P2A_CNT_TARGET0_REACH_1_HP_INT_ENA_V << PMU_0P2A_CNT_TARGET0_REACH_1_HP_INT_ENA_S)
#define PMU_0P2A_CNT_TARGET0_REACH_1_HP_INT_ENA_V  0x00000001U
#define PMU_0P2A_CNT_TARGET0_REACH_1_HP_INT_ENA_S  20
/** PMU_0P2A_CNT_TARGET1_REACH_1_HP_INT_ENA : R/W; bitpos: [21]; default: 0;
 *  reg_0p2a_1_counter after xpd reach target1
 */
#define PMU_0P2A_CNT_TARGET1_REACH_1_HP_INT_ENA    (BIT(21))
#define PMU_0P2A_CNT_TARGET1_REACH_1_HP_INT_ENA_M  (PMU_0P2A_CNT_TARGET1_REACH_1_HP_INT_ENA_V << PMU_0P2A_CNT_TARGET1_REACH_1_HP_INT_ENA_S)
#define PMU_0P2A_CNT_TARGET1_REACH_1_HP_INT_ENA_V  0x00000001U
#define PMU_0P2A_CNT_TARGET1_REACH_1_HP_INT_ENA_S  21
/** PMU_0P3A_CNT_TARGET0_REACH_0_HP_INT_ENA : R/W; bitpos: [22]; default: 0;
 *  reg_0p3a_0 counter after xpd reach target0
 */
#define PMU_0P3A_CNT_TARGET0_REACH_0_HP_INT_ENA    (BIT(22))
#define PMU_0P3A_CNT_TARGET0_REACH_0_HP_INT_ENA_M  (PMU_0P3A_CNT_TARGET0_REACH_0_HP_INT_ENA_V << PMU_0P3A_CNT_TARGET0_REACH_0_HP_INT_ENA_S)
#define PMU_0P3A_CNT_TARGET0_REACH_0_HP_INT_ENA_V  0x00000001U
#define PMU_0P3A_CNT_TARGET0_REACH_0_HP_INT_ENA_S  22
/** PMU_0P3A_CNT_TARGET1_REACH_0_HP_INT_ENA : R/W; bitpos: [23]; default: 0;
 *  reg_0p3a_1_counter after xpd reach target1
 */
#define PMU_0P3A_CNT_TARGET1_REACH_0_HP_INT_ENA    (BIT(23))
#define PMU_0P3A_CNT_TARGET1_REACH_0_HP_INT_ENA_M  (PMU_0P3A_CNT_TARGET1_REACH_0_HP_INT_ENA_V << PMU_0P3A_CNT_TARGET1_REACH_0_HP_INT_ENA_S)
#define PMU_0P3A_CNT_TARGET1_REACH_0_HP_INT_ENA_V  0x00000001U
#define PMU_0P3A_CNT_TARGET1_REACH_0_HP_INT_ENA_S  23
/** PMU_0P3A_CNT_TARGET0_REACH_1_HP_INT_ENA : R/W; bitpos: [24]; default: 0;
 *  reg_0p3a_0_counter after xpd reach target0
 */
#define PMU_0P3A_CNT_TARGET0_REACH_1_HP_INT_ENA    (BIT(24))
#define PMU_0P3A_CNT_TARGET0_REACH_1_HP_INT_ENA_M  (PMU_0P3A_CNT_TARGET0_REACH_1_HP_INT_ENA_V << PMU_0P3A_CNT_TARGET0_REACH_1_HP_INT_ENA_S)
#define PMU_0P3A_CNT_TARGET0_REACH_1_HP_INT_ENA_V  0x00000001U
#define PMU_0P3A_CNT_TARGET0_REACH_1_HP_INT_ENA_S  24
/** PMU_0P3A_CNT_TARGET1_REACH_1_HP_INT_ENA : R/W; bitpos: [25]; default: 0;
 *  reg_0p3a_1_counter after xpd reach target1
 */
#define PMU_0P3A_CNT_TARGET1_REACH_1_HP_INT_ENA    (BIT(25))
#define PMU_0P3A_CNT_TARGET1_REACH_1_HP_INT_ENA_M  (PMU_0P3A_CNT_TARGET1_REACH_1_HP_INT_ENA_V << PMU_0P3A_CNT_TARGET1_REACH_1_HP_INT_ENA_S)
#define PMU_0P3A_CNT_TARGET1_REACH_1_HP_INT_ENA_V  0x00000001U
#define PMU_0P3A_CNT_TARGET1_REACH_1_HP_INT_ENA_S  25
/** PMU_LP_CPU_EXC_INT_ENA : R/W; bitpos: [27]; default: 0;
 *  need_des
 */
#define PMU_LP_CPU_EXC_INT_ENA    (BIT(27))
#define PMU_LP_CPU_EXC_INT_ENA_M  (PMU_LP_CPU_EXC_INT_ENA_V << PMU_LP_CPU_EXC_INT_ENA_S)
#define PMU_LP_CPU_EXC_INT_ENA_V  0x00000001U
#define PMU_LP_CPU_EXC_INT_ENA_S  27
/** PMU_SDIO_IDLE_INT_ENA : R/W; bitpos: [28]; default: 0;
 *  need_des
 */
#define PMU_SDIO_IDLE_INT_ENA    (BIT(28))
#define PMU_SDIO_IDLE_INT_ENA_M  (PMU_SDIO_IDLE_INT_ENA_V << PMU_SDIO_IDLE_INT_ENA_S)
#define PMU_SDIO_IDLE_INT_ENA_V  0x00000001U
#define PMU_SDIO_IDLE_INT_ENA_S  28
/** PMU_SW_INT_ENA : R/W; bitpos: [29]; default: 0;
 *  need_des
 */
#define PMU_SW_INT_ENA    (BIT(29))
#define PMU_SW_INT_ENA_M  (PMU_SW_INT_ENA_V << PMU_SW_INT_ENA_S)
#define PMU_SW_INT_ENA_V  0x00000001U
#define PMU_SW_INT_ENA_S  29
/** PMU_SOC_SLEEP_REJECT_INT_ENA : R/W; bitpos: [30]; default: 0;
 *  need_des
 */
#define PMU_SOC_SLEEP_REJECT_INT_ENA    (BIT(30))
#define PMU_SOC_SLEEP_REJECT_INT_ENA_M  (PMU_SOC_SLEEP_REJECT_INT_ENA_V << PMU_SOC_SLEEP_REJECT_INT_ENA_S)
#define PMU_SOC_SLEEP_REJECT_INT_ENA_V  0x00000001U
#define PMU_SOC_SLEEP_REJECT_INT_ENA_S  30
/** PMU_SOC_WAKEUP_INT_ENA : R/W; bitpos: [31]; default: 0;
 *  need_des
 */
#define PMU_SOC_WAKEUP_INT_ENA    (BIT(31))
#define PMU_SOC_WAKEUP_INT_ENA_M  (PMU_SOC_WAKEUP_INT_ENA_V << PMU_SOC_WAKEUP_INT_ENA_S)
#define PMU_SOC_WAKEUP_INT_ENA_V  0x00000001U
#define PMU_SOC_WAKEUP_INT_ENA_S  31

/** PMU_HP_INT_CLR_REG register
 *  need_des
 */
#define PMU_HP_INT_CLR_REG (DR_REG_PMU_BASE + 0x170)
/** PMU_0P1A_CNT_TARGET0_REACH_0_HP_INT_CLR : WT; bitpos: [14]; default: 0;
 *  reg_0p1a_0_counter after xpd reach target0
 */
#define PMU_0P1A_CNT_TARGET0_REACH_0_HP_INT_CLR    (BIT(14))
#define PMU_0P1A_CNT_TARGET0_REACH_0_HP_INT_CLR_M  (PMU_0P1A_CNT_TARGET0_REACH_0_HP_INT_CLR_V << PMU_0P1A_CNT_TARGET0_REACH_0_HP_INT_CLR_S)
#define PMU_0P1A_CNT_TARGET0_REACH_0_HP_INT_CLR_V  0x00000001U
#define PMU_0P1A_CNT_TARGET0_REACH_0_HP_INT_CLR_S  14
/** PMU_0P1A_CNT_TARGET1_REACH_0_HP_INT_CLR : WT; bitpos: [15]; default: 0;
 *  reg_0p1a_1_counter after xpd reach target1
 */
#define PMU_0P1A_CNT_TARGET1_REACH_0_HP_INT_CLR    (BIT(15))
#define PMU_0P1A_CNT_TARGET1_REACH_0_HP_INT_CLR_M  (PMU_0P1A_CNT_TARGET1_REACH_0_HP_INT_CLR_V << PMU_0P1A_CNT_TARGET1_REACH_0_HP_INT_CLR_S)
#define PMU_0P1A_CNT_TARGET1_REACH_0_HP_INT_CLR_V  0x00000001U
#define PMU_0P1A_CNT_TARGET1_REACH_0_HP_INT_CLR_S  15
/** PMU_0P1A_CNT_TARGET0_REACH_1_HP_INT_CLR : WT; bitpos: [16]; default: 0;
 *  reg_0p1a_0 counter after xpd reach target0
 */
#define PMU_0P1A_CNT_TARGET0_REACH_1_HP_INT_CLR    (BIT(16))
#define PMU_0P1A_CNT_TARGET0_REACH_1_HP_INT_CLR_M  (PMU_0P1A_CNT_TARGET0_REACH_1_HP_INT_CLR_V << PMU_0P1A_CNT_TARGET0_REACH_1_HP_INT_CLR_S)
#define PMU_0P1A_CNT_TARGET0_REACH_1_HP_INT_CLR_V  0x00000001U
#define PMU_0P1A_CNT_TARGET0_REACH_1_HP_INT_CLR_S  16
/** PMU_0P1A_CNT_TARGET1_REACH_1_HP_INT_CLR : WT; bitpos: [17]; default: 0;
 *  reg_0p1a_1_counter after xpd reach target1
 */
#define PMU_0P1A_CNT_TARGET1_REACH_1_HP_INT_CLR    (BIT(17))
#define PMU_0P1A_CNT_TARGET1_REACH_1_HP_INT_CLR_M  (PMU_0P1A_CNT_TARGET1_REACH_1_HP_INT_CLR_V << PMU_0P1A_CNT_TARGET1_REACH_1_HP_INT_CLR_S)
#define PMU_0P1A_CNT_TARGET1_REACH_1_HP_INT_CLR_V  0x00000001U
#define PMU_0P1A_CNT_TARGET1_REACH_1_HP_INT_CLR_S  17
/** PMU_0P2A_CNT_TARGET0_REACH_0_HP_INT_CLR : WT; bitpos: [18]; default: 0;
 *  reg_0p2a_0 counter after xpd reach target0
 */
#define PMU_0P2A_CNT_TARGET0_REACH_0_HP_INT_CLR    (BIT(18))
#define PMU_0P2A_CNT_TARGET0_REACH_0_HP_INT_CLR_M  (PMU_0P2A_CNT_TARGET0_REACH_0_HP_INT_CLR_V << PMU_0P2A_CNT_TARGET0_REACH_0_HP_INT_CLR_S)
#define PMU_0P2A_CNT_TARGET0_REACH_0_HP_INT_CLR_V  0x00000001U
#define PMU_0P2A_CNT_TARGET0_REACH_0_HP_INT_CLR_S  18
/** PMU_0P2A_CNT_TARGET1_REACH_0_HP_INT_CLR : WT; bitpos: [19]; default: 0;
 *  reg_0p2a_1_counter after xpd reach target1
 */
#define PMU_0P2A_CNT_TARGET1_REACH_0_HP_INT_CLR    (BIT(19))
#define PMU_0P2A_CNT_TARGET1_REACH_0_HP_INT_CLR_M  (PMU_0P2A_CNT_TARGET1_REACH_0_HP_INT_CLR_V << PMU_0P2A_CNT_TARGET1_REACH_0_HP_INT_CLR_S)
#define PMU_0P2A_CNT_TARGET1_REACH_0_HP_INT_CLR_V  0x00000001U
#define PMU_0P2A_CNT_TARGET1_REACH_0_HP_INT_CLR_S  19
/** PMU_0P2A_CNT_TARGET0_REACH_1_HP_INT_CLR : WT; bitpos: [20]; default: 0;
 *  reg_0p2a_0 counter after xpd reach target0
 */
#define PMU_0P2A_CNT_TARGET0_REACH_1_HP_INT_CLR    (BIT(20))
#define PMU_0P2A_CNT_TARGET0_REACH_1_HP_INT_CLR_M  (PMU_0P2A_CNT_TARGET0_REACH_1_HP_INT_CLR_V << PMU_0P2A_CNT_TARGET0_REACH_1_HP_INT_CLR_S)
#define PMU_0P2A_CNT_TARGET0_REACH_1_HP_INT_CLR_V  0x00000001U
#define PMU_0P2A_CNT_TARGET0_REACH_1_HP_INT_CLR_S  20
/** PMU_0P2A_CNT_TARGET1_REACH_1_HP_INT_CLR : WT; bitpos: [21]; default: 0;
 *  reg_0p2a_1_counter after xpd reach target1
 */
#define PMU_0P2A_CNT_TARGET1_REACH_1_HP_INT_CLR    (BIT(21))
#define PMU_0P2A_CNT_TARGET1_REACH_1_HP_INT_CLR_M  (PMU_0P2A_CNT_TARGET1_REACH_1_HP_INT_CLR_V << PMU_0P2A_CNT_TARGET1_REACH_1_HP_INT_CLR_S)
#define PMU_0P2A_CNT_TARGET1_REACH_1_HP_INT_CLR_V  0x00000001U
#define PMU_0P2A_CNT_TARGET1_REACH_1_HP_INT_CLR_S  21
/** PMU_0P3A_CNT_TARGET0_REACH_0_HP_INT_CLR : WT; bitpos: [22]; default: 0;
 *  reg_0p3a_0 counter after xpd reach target0
 */
#define PMU_0P3A_CNT_TARGET0_REACH_0_HP_INT_CLR    (BIT(22))
#define PMU_0P3A_CNT_TARGET0_REACH_0_HP_INT_CLR_M  (PMU_0P3A_CNT_TARGET0_REACH_0_HP_INT_CLR_V << PMU_0P3A_CNT_TARGET0_REACH_0_HP_INT_CLR_S)
#define PMU_0P3A_CNT_TARGET0_REACH_0_HP_INT_CLR_V  0x00000001U
#define PMU_0P3A_CNT_TARGET0_REACH_0_HP_INT_CLR_S  22
/** PMU_0P3A_CNT_TARGET1_REACH_0_HP_INT_CLR : WT; bitpos: [23]; default: 0;
 *  reg_0p3a_1_counter after xpd reach target1
 */
#define PMU_0P3A_CNT_TARGET1_REACH_0_HP_INT_CLR    (BIT(23))
#define PMU_0P3A_CNT_TARGET1_REACH_0_HP_INT_CLR_M  (PMU_0P3A_CNT_TARGET1_REACH_0_HP_INT_CLR_V << PMU_0P3A_CNT_TARGET1_REACH_0_HP_INT_CLR_S)
#define PMU_0P3A_CNT_TARGET1_REACH_0_HP_INT_CLR_V  0x00000001U
#define PMU_0P3A_CNT_TARGET1_REACH_0_HP_INT_CLR_S  23
/** PMU_0P3A_CNT_TARGET0_REACH_1_HP_INT_CLR : WT; bitpos: [24]; default: 0;
 *  reg_0p3a_0_counter after xpd reach target0
 */
#define PMU_0P3A_CNT_TARGET0_REACH_1_HP_INT_CLR    (BIT(24))
#define PMU_0P3A_CNT_TARGET0_REACH_1_HP_INT_CLR_M  (PMU_0P3A_CNT_TARGET0_REACH_1_HP_INT_CLR_V << PMU_0P3A_CNT_TARGET0_REACH_1_HP_INT_CLR_S)
#define PMU_0P3A_CNT_TARGET0_REACH_1_HP_INT_CLR_V  0x00000001U
#define PMU_0P3A_CNT_TARGET0_REACH_1_HP_INT_CLR_S  24
/** PMU_0P3A_CNT_TARGET1_REACH_1_HP_INT_CLR : WT; bitpos: [25]; default: 0;
 *  reg_0p3a_1_counter after xpd reach target1
 */
#define PMU_0P3A_CNT_TARGET1_REACH_1_HP_INT_CLR    (BIT(25))
#define PMU_0P3A_CNT_TARGET1_REACH_1_HP_INT_CLR_M  (PMU_0P3A_CNT_TARGET1_REACH_1_HP_INT_CLR_V << PMU_0P3A_CNT_TARGET1_REACH_1_HP_INT_CLR_S)
#define PMU_0P3A_CNT_TARGET1_REACH_1_HP_INT_CLR_V  0x00000001U
#define PMU_0P3A_CNT_TARGET1_REACH_1_HP_INT_CLR_S  25
/** PMU_LP_CPU_EXC_INT_CLR : WT; bitpos: [27]; default: 0;
 *  need_des
 */
#define PMU_LP_CPU_EXC_INT_CLR    (BIT(27))
#define PMU_LP_CPU_EXC_INT_CLR_M  (PMU_LP_CPU_EXC_INT_CLR_V << PMU_LP_CPU_EXC_INT_CLR_S)
#define PMU_LP_CPU_EXC_INT_CLR_V  0x00000001U
#define PMU_LP_CPU_EXC_INT_CLR_S  27
/** PMU_SDIO_IDLE_INT_CLR : WT; bitpos: [28]; default: 0;
 *  need_des
 */
#define PMU_SDIO_IDLE_INT_CLR    (BIT(28))
#define PMU_SDIO_IDLE_INT_CLR_M  (PMU_SDIO_IDLE_INT_CLR_V << PMU_SDIO_IDLE_INT_CLR_S)
#define PMU_SDIO_IDLE_INT_CLR_V  0x00000001U
#define PMU_SDIO_IDLE_INT_CLR_S  28
/** PMU_SW_INT_CLR : WT; bitpos: [29]; default: 0;
 *  need_des
 */
#define PMU_SW_INT_CLR    (BIT(29))
#define PMU_SW_INT_CLR_M  (PMU_SW_INT_CLR_V << PMU_SW_INT_CLR_S)
#define PMU_SW_INT_CLR_V  0x00000001U
#define PMU_SW_INT_CLR_S  29
/** PMU_SOC_SLEEP_REJECT_INT_CLR : WT; bitpos: [30]; default: 0;
 *  need_des
 */
#define PMU_SOC_SLEEP_REJECT_INT_CLR    (BIT(30))
#define PMU_SOC_SLEEP_REJECT_INT_CLR_M  (PMU_SOC_SLEEP_REJECT_INT_CLR_V << PMU_SOC_SLEEP_REJECT_INT_CLR_S)
#define PMU_SOC_SLEEP_REJECT_INT_CLR_V  0x00000001U
#define PMU_SOC_SLEEP_REJECT_INT_CLR_S  30
/** PMU_SOC_WAKEUP_INT_CLR : WT; bitpos: [31]; default: 0;
 *  need_des
 */
#define PMU_SOC_WAKEUP_INT_CLR    (BIT(31))
#define PMU_SOC_WAKEUP_INT_CLR_M  (PMU_SOC_WAKEUP_INT_CLR_V << PMU_SOC_WAKEUP_INT_CLR_S)
#define PMU_SOC_WAKEUP_INT_CLR_V  0x00000001U
#define PMU_SOC_WAKEUP_INT_CLR_S  31

/** PMU_LP_INT_RAW_REG register
 *  need_des
 */
#define PMU_LP_INT_RAW_REG (DR_REG_PMU_BASE + 0x174)
/** PMU_LP_CPU_SLEEP_REJECT_INT_RAW : R/WTC/SS; bitpos: [13]; default: 0;
 *  need_des
 */
#define PMU_LP_CPU_SLEEP_REJECT_INT_RAW    (BIT(13))
#define PMU_LP_CPU_SLEEP_REJECT_INT_RAW_M  (PMU_LP_CPU_SLEEP_REJECT_INT_RAW_V << PMU_LP_CPU_SLEEP_REJECT_INT_RAW_S)
#define PMU_LP_CPU_SLEEP_REJECT_INT_RAW_V  0x00000001U
#define PMU_LP_CPU_SLEEP_REJECT_INT_RAW_S  13
/** PMU_0P1A_CNT_TARGET0_REACH_0_LP_INT_RAW : R/WTC/SS; bitpos: [14]; default: 0;
 *  reg_0p1a_0_counter after xpd reach target0
 */
#define PMU_0P1A_CNT_TARGET0_REACH_0_LP_INT_RAW    (BIT(14))
#define PMU_0P1A_CNT_TARGET0_REACH_0_LP_INT_RAW_M  (PMU_0P1A_CNT_TARGET0_REACH_0_LP_INT_RAW_V << PMU_0P1A_CNT_TARGET0_REACH_0_LP_INT_RAW_S)
#define PMU_0P1A_CNT_TARGET0_REACH_0_LP_INT_RAW_V  0x00000001U
#define PMU_0P1A_CNT_TARGET0_REACH_0_LP_INT_RAW_S  14
/** PMU_0P1A_CNT_TARGET1_REACH_0_LP_INT_RAW : R/WTC/SS; bitpos: [15]; default: 0;
 *  reg_0p1a_1_counter after xpd reach target1
 */
#define PMU_0P1A_CNT_TARGET1_REACH_0_LP_INT_RAW    (BIT(15))
#define PMU_0P1A_CNT_TARGET1_REACH_0_LP_INT_RAW_M  (PMU_0P1A_CNT_TARGET1_REACH_0_LP_INT_RAW_V << PMU_0P1A_CNT_TARGET1_REACH_0_LP_INT_RAW_S)
#define PMU_0P1A_CNT_TARGET1_REACH_0_LP_INT_RAW_V  0x00000001U
#define PMU_0P1A_CNT_TARGET1_REACH_0_LP_INT_RAW_S  15
/** PMU_0P1A_CNT_TARGET0_REACH_1_LP_INT_RAW : R/WTC/SS; bitpos: [16]; default: 0;
 *  reg_0p1a_0 counter after xpd reach target0
 */
#define PMU_0P1A_CNT_TARGET0_REACH_1_LP_INT_RAW    (BIT(16))
#define PMU_0P1A_CNT_TARGET0_REACH_1_LP_INT_RAW_M  (PMU_0P1A_CNT_TARGET0_REACH_1_LP_INT_RAW_V << PMU_0P1A_CNT_TARGET0_REACH_1_LP_INT_RAW_S)
#define PMU_0P1A_CNT_TARGET0_REACH_1_LP_INT_RAW_V  0x00000001U
#define PMU_0P1A_CNT_TARGET0_REACH_1_LP_INT_RAW_S  16
/** PMU_0P1A_CNT_TARGET1_REACH_1_LP_INT_RAW : R/WTC/SS; bitpos: [17]; default: 0;
 *  reg_0p1a_1_counter after xpd reach target1
 */
#define PMU_0P1A_CNT_TARGET1_REACH_1_LP_INT_RAW    (BIT(17))
#define PMU_0P1A_CNT_TARGET1_REACH_1_LP_INT_RAW_M  (PMU_0P1A_CNT_TARGET1_REACH_1_LP_INT_RAW_V << PMU_0P1A_CNT_TARGET1_REACH_1_LP_INT_RAW_S)
#define PMU_0P1A_CNT_TARGET1_REACH_1_LP_INT_RAW_V  0x00000001U
#define PMU_0P1A_CNT_TARGET1_REACH_1_LP_INT_RAW_S  17
/** PMU_0P2A_CNT_TARGET0_REACH_0_LP_INT_RAW : R/WTC/SS; bitpos: [18]; default: 0;
 *  reg_0p2a_0 counter after xpd reach target0
 */
#define PMU_0P2A_CNT_TARGET0_REACH_0_LP_INT_RAW    (BIT(18))
#define PMU_0P2A_CNT_TARGET0_REACH_0_LP_INT_RAW_M  (PMU_0P2A_CNT_TARGET0_REACH_0_LP_INT_RAW_V << PMU_0P2A_CNT_TARGET0_REACH_0_LP_INT_RAW_S)
#define PMU_0P2A_CNT_TARGET0_REACH_0_LP_INT_RAW_V  0x00000001U
#define PMU_0P2A_CNT_TARGET0_REACH_0_LP_INT_RAW_S  18
/** PMU_0P2A_CNT_TARGET1_REACH_0_LP_INT_RAW : R/WTC/SS; bitpos: [19]; default: 0;
 *  reg_0p2a_1_counter after xpd reach target1
 */
#define PMU_0P2A_CNT_TARGET1_REACH_0_LP_INT_RAW    (BIT(19))
#define PMU_0P2A_CNT_TARGET1_REACH_0_LP_INT_RAW_M  (PMU_0P2A_CNT_TARGET1_REACH_0_LP_INT_RAW_V << PMU_0P2A_CNT_TARGET1_REACH_0_LP_INT_RAW_S)
#define PMU_0P2A_CNT_TARGET1_REACH_0_LP_INT_RAW_V  0x00000001U
#define PMU_0P2A_CNT_TARGET1_REACH_0_LP_INT_RAW_S  19
/** PMU_0P2A_CNT_TARGET0_REACH_1_LP_INT_RAW : R/WTC/SS; bitpos: [20]; default: 0;
 *  reg_0p2a_0 counter after xpd reach target0
 */
#define PMU_0P2A_CNT_TARGET0_REACH_1_LP_INT_RAW    (BIT(20))
#define PMU_0P2A_CNT_TARGET0_REACH_1_LP_INT_RAW_M  (PMU_0P2A_CNT_TARGET0_REACH_1_LP_INT_RAW_V << PMU_0P2A_CNT_TARGET0_REACH_1_LP_INT_RAW_S)
#define PMU_0P2A_CNT_TARGET0_REACH_1_LP_INT_RAW_V  0x00000001U
#define PMU_0P2A_CNT_TARGET0_REACH_1_LP_INT_RAW_S  20
/** PMU_0P2A_CNT_TARGET1_REACH_1_LP_INT_RAW : R/WTC/SS; bitpos: [21]; default: 0;
 *  reg_0p2a_1_counter after xpd reach target1
 */
#define PMU_0P2A_CNT_TARGET1_REACH_1_LP_INT_RAW    (BIT(21))
#define PMU_0P2A_CNT_TARGET1_REACH_1_LP_INT_RAW_M  (PMU_0P2A_CNT_TARGET1_REACH_1_LP_INT_RAW_V << PMU_0P2A_CNT_TARGET1_REACH_1_LP_INT_RAW_S)
#define PMU_0P2A_CNT_TARGET1_REACH_1_LP_INT_RAW_V  0x00000001U
#define PMU_0P2A_CNT_TARGET1_REACH_1_LP_INT_RAW_S  21
/** PMU_0P3A_CNT_TARGET0_REACH_0_LP_INT_RAW : R/WTC/SS; bitpos: [22]; default: 0;
 *  reg_0p3a_0 counter after xpd reach target0
 */
#define PMU_0P3A_CNT_TARGET0_REACH_0_LP_INT_RAW    (BIT(22))
#define PMU_0P3A_CNT_TARGET0_REACH_0_LP_INT_RAW_M  (PMU_0P3A_CNT_TARGET0_REACH_0_LP_INT_RAW_V << PMU_0P3A_CNT_TARGET0_REACH_0_LP_INT_RAW_S)
#define PMU_0P3A_CNT_TARGET0_REACH_0_LP_INT_RAW_V  0x00000001U
#define PMU_0P3A_CNT_TARGET0_REACH_0_LP_INT_RAW_S  22
/** PMU_0P3A_CNT_TARGET1_REACH_0_LP_INT_RAW : R/WTC/SS; bitpos: [23]; default: 0;
 *  reg_0p3a_1_counter after xpd reach target1
 */
#define PMU_0P3A_CNT_TARGET1_REACH_0_LP_INT_RAW    (BIT(23))
#define PMU_0P3A_CNT_TARGET1_REACH_0_LP_INT_RAW_M  (PMU_0P3A_CNT_TARGET1_REACH_0_LP_INT_RAW_V << PMU_0P3A_CNT_TARGET1_REACH_0_LP_INT_RAW_S)
#define PMU_0P3A_CNT_TARGET1_REACH_0_LP_INT_RAW_V  0x00000001U
#define PMU_0P3A_CNT_TARGET1_REACH_0_LP_INT_RAW_S  23
/** PMU_0P3A_CNT_TARGET0_REACH_1_LP_INT_RAW : R/WTC/SS; bitpos: [24]; default: 0;
 *  reg_0p3a_0_counter after xpd reach target0
 */
#define PMU_0P3A_CNT_TARGET0_REACH_1_LP_INT_RAW    (BIT(24))
#define PMU_0P3A_CNT_TARGET0_REACH_1_LP_INT_RAW_M  (PMU_0P3A_CNT_TARGET0_REACH_1_LP_INT_RAW_V << PMU_0P3A_CNT_TARGET0_REACH_1_LP_INT_RAW_S)
#define PMU_0P3A_CNT_TARGET0_REACH_1_LP_INT_RAW_V  0x00000001U
#define PMU_0P3A_CNT_TARGET0_REACH_1_LP_INT_RAW_S  24
/** PMU_0P3A_CNT_TARGET1_REACH_1_LP_INT_RAW : R/WTC/SS; bitpos: [25]; default: 0;
 *  reg_0p3a_1_counter after xpd reach target1
 */
#define PMU_0P3A_CNT_TARGET1_REACH_1_LP_INT_RAW    (BIT(25))
#define PMU_0P3A_CNT_TARGET1_REACH_1_LP_INT_RAW_M  (PMU_0P3A_CNT_TARGET1_REACH_1_LP_INT_RAW_V << PMU_0P3A_CNT_TARGET1_REACH_1_LP_INT_RAW_S)
#define PMU_0P3A_CNT_TARGET1_REACH_1_LP_INT_RAW_V  0x00000001U
#define PMU_0P3A_CNT_TARGET1_REACH_1_LP_INT_RAW_S  25
/** PMU_LP_CPU_WAKEUP_INT_RAW : R/WTC/SS; bitpos: [26]; default: 0;
 *  need_des
 */
#define PMU_LP_CPU_WAKEUP_INT_RAW    (BIT(26))
#define PMU_LP_CPU_WAKEUP_INT_RAW_M  (PMU_LP_CPU_WAKEUP_INT_RAW_V << PMU_LP_CPU_WAKEUP_INT_RAW_S)
#define PMU_LP_CPU_WAKEUP_INT_RAW_V  0x00000001U
#define PMU_LP_CPU_WAKEUP_INT_RAW_S  26
/** PMU_SLEEP_SWITCH_ACTIVE_END_INT_RAW : R/WTC/SS; bitpos: [27]; default: 0;
 *  need_des
 */
#define PMU_SLEEP_SWITCH_ACTIVE_END_INT_RAW    (BIT(27))
#define PMU_SLEEP_SWITCH_ACTIVE_END_INT_RAW_M  (PMU_SLEEP_SWITCH_ACTIVE_END_INT_RAW_V << PMU_SLEEP_SWITCH_ACTIVE_END_INT_RAW_S)
#define PMU_SLEEP_SWITCH_ACTIVE_END_INT_RAW_V  0x00000001U
#define PMU_SLEEP_SWITCH_ACTIVE_END_INT_RAW_S  27
/** PMU_ACTIVE_SWITCH_SLEEP_END_INT_RAW : R/WTC/SS; bitpos: [28]; default: 0;
 *  need_des
 */
#define PMU_ACTIVE_SWITCH_SLEEP_END_INT_RAW    (BIT(28))
#define PMU_ACTIVE_SWITCH_SLEEP_END_INT_RAW_M  (PMU_ACTIVE_SWITCH_SLEEP_END_INT_RAW_V << PMU_ACTIVE_SWITCH_SLEEP_END_INT_RAW_S)
#define PMU_ACTIVE_SWITCH_SLEEP_END_INT_RAW_V  0x00000001U
#define PMU_ACTIVE_SWITCH_SLEEP_END_INT_RAW_S  28
/** PMU_SLEEP_SWITCH_ACTIVE_START_INT_RAW : R/WTC/SS; bitpos: [29]; default: 0;
 *  need_des
 */
#define PMU_SLEEP_SWITCH_ACTIVE_START_INT_RAW    (BIT(29))
#define PMU_SLEEP_SWITCH_ACTIVE_START_INT_RAW_M  (PMU_SLEEP_SWITCH_ACTIVE_START_INT_RAW_V << PMU_SLEEP_SWITCH_ACTIVE_START_INT_RAW_S)
#define PMU_SLEEP_SWITCH_ACTIVE_START_INT_RAW_V  0x00000001U
#define PMU_SLEEP_SWITCH_ACTIVE_START_INT_RAW_S  29
/** PMU_ACTIVE_SWITCH_SLEEP_START_INT_RAW : R/WTC/SS; bitpos: [30]; default: 0;
 *  need_des
 */
#define PMU_ACTIVE_SWITCH_SLEEP_START_INT_RAW    (BIT(30))
#define PMU_ACTIVE_SWITCH_SLEEP_START_INT_RAW_M  (PMU_ACTIVE_SWITCH_SLEEP_START_INT_RAW_V << PMU_ACTIVE_SWITCH_SLEEP_START_INT_RAW_S)
#define PMU_ACTIVE_SWITCH_SLEEP_START_INT_RAW_V  0x00000001U
#define PMU_ACTIVE_SWITCH_SLEEP_START_INT_RAW_S  30
/** PMU_HP_SW_TRIGGER_INT_RAW : R/WTC/SS; bitpos: [31]; default: 0;
 *  need_des
 */
#define PMU_HP_SW_TRIGGER_INT_RAW    (BIT(31))
#define PMU_HP_SW_TRIGGER_INT_RAW_M  (PMU_HP_SW_TRIGGER_INT_RAW_V << PMU_HP_SW_TRIGGER_INT_RAW_S)
#define PMU_HP_SW_TRIGGER_INT_RAW_V  0x00000001U
#define PMU_HP_SW_TRIGGER_INT_RAW_S  31

/** PMU_LP_INT_ST_REG register
 *  need_des
 */
#define PMU_LP_INT_ST_REG (DR_REG_PMU_BASE + 0x178)
/** PMU_LP_CPU_SLEEP_REJECT_INT_ST : RO; bitpos: [13]; default: 0;
 *  need_des
 */
#define PMU_LP_CPU_SLEEP_REJECT_INT_ST    (BIT(13))
#define PMU_LP_CPU_SLEEP_REJECT_INT_ST_M  (PMU_LP_CPU_SLEEP_REJECT_INT_ST_V << PMU_LP_CPU_SLEEP_REJECT_INT_ST_S)
#define PMU_LP_CPU_SLEEP_REJECT_INT_ST_V  0x00000001U
#define PMU_LP_CPU_SLEEP_REJECT_INT_ST_S  13
/** PMU_0P1A_CNT_TARGET0_REACH_0_LP_INT_ST : RO; bitpos: [14]; default: 0;
 *  reg_0p1a_0_counter after xpd reach target0
 */
#define PMU_0P1A_CNT_TARGET0_REACH_0_LP_INT_ST    (BIT(14))
#define PMU_0P1A_CNT_TARGET0_REACH_0_LP_INT_ST_M  (PMU_0P1A_CNT_TARGET0_REACH_0_LP_INT_ST_V << PMU_0P1A_CNT_TARGET0_REACH_0_LP_INT_ST_S)
#define PMU_0P1A_CNT_TARGET0_REACH_0_LP_INT_ST_V  0x00000001U
#define PMU_0P1A_CNT_TARGET0_REACH_0_LP_INT_ST_S  14
/** PMU_0P1A_CNT_TARGET1_REACH_0_LP_INT_ST : RO; bitpos: [15]; default: 0;
 *  reg_0p1a_1_counter after xpd reach target1
 */
#define PMU_0P1A_CNT_TARGET1_REACH_0_LP_INT_ST    (BIT(15))
#define PMU_0P1A_CNT_TARGET1_REACH_0_LP_INT_ST_M  (PMU_0P1A_CNT_TARGET1_REACH_0_LP_INT_ST_V << PMU_0P1A_CNT_TARGET1_REACH_0_LP_INT_ST_S)
#define PMU_0P1A_CNT_TARGET1_REACH_0_LP_INT_ST_V  0x00000001U
#define PMU_0P1A_CNT_TARGET1_REACH_0_LP_INT_ST_S  15
/** PMU_0P1A_CNT_TARGET0_REACH_1_LP_INT_ST : RO; bitpos: [16]; default: 0;
 *  reg_0p1a_0 counter after xpd reach target0
 */
#define PMU_0P1A_CNT_TARGET0_REACH_1_LP_INT_ST    (BIT(16))
#define PMU_0P1A_CNT_TARGET0_REACH_1_LP_INT_ST_M  (PMU_0P1A_CNT_TARGET0_REACH_1_LP_INT_ST_V << PMU_0P1A_CNT_TARGET0_REACH_1_LP_INT_ST_S)
#define PMU_0P1A_CNT_TARGET0_REACH_1_LP_INT_ST_V  0x00000001U
#define PMU_0P1A_CNT_TARGET0_REACH_1_LP_INT_ST_S  16
/** PMU_0P1A_CNT_TARGET1_REACH_1_LP_INT_ST : RO; bitpos: [17]; default: 0;
 *  reg_0p1a_1_counter after xpd reach target1
 */
#define PMU_0P1A_CNT_TARGET1_REACH_1_LP_INT_ST    (BIT(17))
#define PMU_0P1A_CNT_TARGET1_REACH_1_LP_INT_ST_M  (PMU_0P1A_CNT_TARGET1_REACH_1_LP_INT_ST_V << PMU_0P1A_CNT_TARGET1_REACH_1_LP_INT_ST_S)
#define PMU_0P1A_CNT_TARGET1_REACH_1_LP_INT_ST_V  0x00000001U
#define PMU_0P1A_CNT_TARGET1_REACH_1_LP_INT_ST_S  17
/** PMU_0P2A_CNT_TARGET0_REACH_0_LP_INT_ST : RO; bitpos: [18]; default: 0;
 *  reg_0p2a_0 counter after xpd reach target0
 */
#define PMU_0P2A_CNT_TARGET0_REACH_0_LP_INT_ST    (BIT(18))
#define PMU_0P2A_CNT_TARGET0_REACH_0_LP_INT_ST_M  (PMU_0P2A_CNT_TARGET0_REACH_0_LP_INT_ST_V << PMU_0P2A_CNT_TARGET0_REACH_0_LP_INT_ST_S)
#define PMU_0P2A_CNT_TARGET0_REACH_0_LP_INT_ST_V  0x00000001U
#define PMU_0P2A_CNT_TARGET0_REACH_0_LP_INT_ST_S  18
/** PMU_0P2A_CNT_TARGET1_REACH_0_LP_INT_ST : RO; bitpos: [19]; default: 0;
 *  reg_0p2a_1_counter after xpd reach target1
 */
#define PMU_0P2A_CNT_TARGET1_REACH_0_LP_INT_ST    (BIT(19))
#define PMU_0P2A_CNT_TARGET1_REACH_0_LP_INT_ST_M  (PMU_0P2A_CNT_TARGET1_REACH_0_LP_INT_ST_V << PMU_0P2A_CNT_TARGET1_REACH_0_LP_INT_ST_S)
#define PMU_0P2A_CNT_TARGET1_REACH_0_LP_INT_ST_V  0x00000001U
#define PMU_0P2A_CNT_TARGET1_REACH_0_LP_INT_ST_S  19
/** PMU_0P2A_CNT_TARGET0_REACH_1_LP_INT_ST : RO; bitpos: [20]; default: 0;
 *  reg_0p2a_0 counter after xpd reach target0
 */
#define PMU_0P2A_CNT_TARGET0_REACH_1_LP_INT_ST    (BIT(20))
#define PMU_0P2A_CNT_TARGET0_REACH_1_LP_INT_ST_M  (PMU_0P2A_CNT_TARGET0_REACH_1_LP_INT_ST_V << PMU_0P2A_CNT_TARGET0_REACH_1_LP_INT_ST_S)
#define PMU_0P2A_CNT_TARGET0_REACH_1_LP_INT_ST_V  0x00000001U
#define PMU_0P2A_CNT_TARGET0_REACH_1_LP_INT_ST_S  20
/** PMU_0P2A_CNT_TARGET1_REACH_1_LP_INT_ST : RO; bitpos: [21]; default: 0;
 *  reg_0p2a_1_counter after xpd reach target1
 */
#define PMU_0P2A_CNT_TARGET1_REACH_1_LP_INT_ST    (BIT(21))
#define PMU_0P2A_CNT_TARGET1_REACH_1_LP_INT_ST_M  (PMU_0P2A_CNT_TARGET1_REACH_1_LP_INT_ST_V << PMU_0P2A_CNT_TARGET1_REACH_1_LP_INT_ST_S)
#define PMU_0P2A_CNT_TARGET1_REACH_1_LP_INT_ST_V  0x00000001U
#define PMU_0P2A_CNT_TARGET1_REACH_1_LP_INT_ST_S  21
/** PMU_0P3A_CNT_TARGET0_REACH_0_LP_INT_ST : RO; bitpos: [22]; default: 0;
 *  reg_0p3a_0 counter after xpd reach target0
 */
#define PMU_0P3A_CNT_TARGET0_REACH_0_LP_INT_ST    (BIT(22))
#define PMU_0P3A_CNT_TARGET0_REACH_0_LP_INT_ST_M  (PMU_0P3A_CNT_TARGET0_REACH_0_LP_INT_ST_V << PMU_0P3A_CNT_TARGET0_REACH_0_LP_INT_ST_S)
#define PMU_0P3A_CNT_TARGET0_REACH_0_LP_INT_ST_V  0x00000001U
#define PMU_0P3A_CNT_TARGET0_REACH_0_LP_INT_ST_S  22
/** PMU_0P3A_CNT_TARGET1_REACH_0_LP_INT_ST : RO; bitpos: [23]; default: 0;
 *  reg_0p3a_1_counter after xpd reach target1
 */
#define PMU_0P3A_CNT_TARGET1_REACH_0_LP_INT_ST    (BIT(23))
#define PMU_0P3A_CNT_TARGET1_REACH_0_LP_INT_ST_M  (PMU_0P3A_CNT_TARGET1_REACH_0_LP_INT_ST_V << PMU_0P3A_CNT_TARGET1_REACH_0_LP_INT_ST_S)
#define PMU_0P3A_CNT_TARGET1_REACH_0_LP_INT_ST_V  0x00000001U
#define PMU_0P3A_CNT_TARGET1_REACH_0_LP_INT_ST_S  23
/** PMU_0P3A_CNT_TARGET0_REACH_1_LP_INT_ST : RO; bitpos: [24]; default: 0;
 *  reg_0p3a_0_counter after xpd reach target0
 */
#define PMU_0P3A_CNT_TARGET0_REACH_1_LP_INT_ST    (BIT(24))
#define PMU_0P3A_CNT_TARGET0_REACH_1_LP_INT_ST_M  (PMU_0P3A_CNT_TARGET0_REACH_1_LP_INT_ST_V << PMU_0P3A_CNT_TARGET0_REACH_1_LP_INT_ST_S)
#define PMU_0P3A_CNT_TARGET0_REACH_1_LP_INT_ST_V  0x00000001U
#define PMU_0P3A_CNT_TARGET0_REACH_1_LP_INT_ST_S  24
/** PMU_0P3A_CNT_TARGET1_REACH_1_LP_INT_ST : RO; bitpos: [25]; default: 0;
 *  reg_0p3a_1_counter after xpd reach target1
 */
#define PMU_0P3A_CNT_TARGET1_REACH_1_LP_INT_ST    (BIT(25))
#define PMU_0P3A_CNT_TARGET1_REACH_1_LP_INT_ST_M  (PMU_0P3A_CNT_TARGET1_REACH_1_LP_INT_ST_V << PMU_0P3A_CNT_TARGET1_REACH_1_LP_INT_ST_S)
#define PMU_0P3A_CNT_TARGET1_REACH_1_LP_INT_ST_V  0x00000001U
#define PMU_0P3A_CNT_TARGET1_REACH_1_LP_INT_ST_S  25
/** PMU_LP_CPU_WAKEUP_INT_ST : RO; bitpos: [26]; default: 0;
 *  need_des
 */
#define PMU_LP_CPU_WAKEUP_INT_ST    (BIT(26))
#define PMU_LP_CPU_WAKEUP_INT_ST_M  (PMU_LP_CPU_WAKEUP_INT_ST_V << PMU_LP_CPU_WAKEUP_INT_ST_S)
#define PMU_LP_CPU_WAKEUP_INT_ST_V  0x00000001U
#define PMU_LP_CPU_WAKEUP_INT_ST_S  26
/** PMU_SLEEP_SWITCH_ACTIVE_END_INT_ST : RO; bitpos: [27]; default: 0;
 *  need_des
 */
#define PMU_SLEEP_SWITCH_ACTIVE_END_INT_ST    (BIT(27))
#define PMU_SLEEP_SWITCH_ACTIVE_END_INT_ST_M  (PMU_SLEEP_SWITCH_ACTIVE_END_INT_ST_V << PMU_SLEEP_SWITCH_ACTIVE_END_INT_ST_S)
#define PMU_SLEEP_SWITCH_ACTIVE_END_INT_ST_V  0x00000001U
#define PMU_SLEEP_SWITCH_ACTIVE_END_INT_ST_S  27
/** PMU_ACTIVE_SWITCH_SLEEP_END_INT_ST : RO; bitpos: [28]; default: 0;
 *  need_des
 */
#define PMU_ACTIVE_SWITCH_SLEEP_END_INT_ST    (BIT(28))
#define PMU_ACTIVE_SWITCH_SLEEP_END_INT_ST_M  (PMU_ACTIVE_SWITCH_SLEEP_END_INT_ST_V << PMU_ACTIVE_SWITCH_SLEEP_END_INT_ST_S)
#define PMU_ACTIVE_SWITCH_SLEEP_END_INT_ST_V  0x00000001U
#define PMU_ACTIVE_SWITCH_SLEEP_END_INT_ST_S  28
/** PMU_SLEEP_SWITCH_ACTIVE_START_INT_ST : RO; bitpos: [29]; default: 0;
 *  need_des
 */
#define PMU_SLEEP_SWITCH_ACTIVE_START_INT_ST    (BIT(29))
#define PMU_SLEEP_SWITCH_ACTIVE_START_INT_ST_M  (PMU_SLEEP_SWITCH_ACTIVE_START_INT_ST_V << PMU_SLEEP_SWITCH_ACTIVE_START_INT_ST_S)
#define PMU_SLEEP_SWITCH_ACTIVE_START_INT_ST_V  0x00000001U
#define PMU_SLEEP_SWITCH_ACTIVE_START_INT_ST_S  29
/** PMU_ACTIVE_SWITCH_SLEEP_START_INT_ST : RO; bitpos: [30]; default: 0;
 *  need_des
 */
#define PMU_ACTIVE_SWITCH_SLEEP_START_INT_ST    (BIT(30))
#define PMU_ACTIVE_SWITCH_SLEEP_START_INT_ST_M  (PMU_ACTIVE_SWITCH_SLEEP_START_INT_ST_V << PMU_ACTIVE_SWITCH_SLEEP_START_INT_ST_S)
#define PMU_ACTIVE_SWITCH_SLEEP_START_INT_ST_V  0x00000001U
#define PMU_ACTIVE_SWITCH_SLEEP_START_INT_ST_S  30
/** PMU_HP_SW_TRIGGER_INT_ST : RO; bitpos: [31]; default: 0;
 *  need_des
 */
#define PMU_HP_SW_TRIGGER_INT_ST    (BIT(31))
#define PMU_HP_SW_TRIGGER_INT_ST_M  (PMU_HP_SW_TRIGGER_INT_ST_V << PMU_HP_SW_TRIGGER_INT_ST_S)
#define PMU_HP_SW_TRIGGER_INT_ST_V  0x00000001U
#define PMU_HP_SW_TRIGGER_INT_ST_S  31

/** PMU_LP_INT_ENA_REG register
 *  need_des
 */
#define PMU_LP_INT_ENA_REG (DR_REG_PMU_BASE + 0x17c)
/** PMU_LP_CPU_SLEEP_REJECT_INT_ENA : R/W; bitpos: [13]; default: 0;
 *  need_des
 */
#define PMU_LP_CPU_SLEEP_REJECT_INT_ENA    (BIT(13))
#define PMU_LP_CPU_SLEEP_REJECT_INT_ENA_M  (PMU_LP_CPU_SLEEP_REJECT_INT_ENA_V << PMU_LP_CPU_SLEEP_REJECT_INT_ENA_S)
#define PMU_LP_CPU_SLEEP_REJECT_INT_ENA_V  0x00000001U
#define PMU_LP_CPU_SLEEP_REJECT_INT_ENA_S  13
/** PMU_0P1A_CNT_TARGET0_REACH_0_LP_INT_ENA : R/W; bitpos: [14]; default: 0;
 *  reg_0p1a_0_counter after xpd reach target0
 */
#define PMU_0P1A_CNT_TARGET0_REACH_0_LP_INT_ENA    (BIT(14))
#define PMU_0P1A_CNT_TARGET0_REACH_0_LP_INT_ENA_M  (PMU_0P1A_CNT_TARGET0_REACH_0_LP_INT_ENA_V << PMU_0P1A_CNT_TARGET0_REACH_0_LP_INT_ENA_S)
#define PMU_0P1A_CNT_TARGET0_REACH_0_LP_INT_ENA_V  0x00000001U
#define PMU_0P1A_CNT_TARGET0_REACH_0_LP_INT_ENA_S  14
/** PMU_0P1A_CNT_TARGET1_REACH_0_LP_INT_ENA : R/W; bitpos: [15]; default: 0;
 *  reg_0p1a_1_counter after xpd reach target1
 */
#define PMU_0P1A_CNT_TARGET1_REACH_0_LP_INT_ENA    (BIT(15))
#define PMU_0P1A_CNT_TARGET1_REACH_0_LP_INT_ENA_M  (PMU_0P1A_CNT_TARGET1_REACH_0_LP_INT_ENA_V << PMU_0P1A_CNT_TARGET1_REACH_0_LP_INT_ENA_S)
#define PMU_0P1A_CNT_TARGET1_REACH_0_LP_INT_ENA_V  0x00000001U
#define PMU_0P1A_CNT_TARGET1_REACH_0_LP_INT_ENA_S  15
/** PMU_0P1A_CNT_TARGET0_REACH_1_LP_INT_ENA : R/W; bitpos: [16]; default: 0;
 *  reg_0p1a_0 counter after xpd reach target0
 */
#define PMU_0P1A_CNT_TARGET0_REACH_1_LP_INT_ENA    (BIT(16))
#define PMU_0P1A_CNT_TARGET0_REACH_1_LP_INT_ENA_M  (PMU_0P1A_CNT_TARGET0_REACH_1_LP_INT_ENA_V << PMU_0P1A_CNT_TARGET0_REACH_1_LP_INT_ENA_S)
#define PMU_0P1A_CNT_TARGET0_REACH_1_LP_INT_ENA_V  0x00000001U
#define PMU_0P1A_CNT_TARGET0_REACH_1_LP_INT_ENA_S  16
/** PMU_0P1A_CNT_TARGET1_REACH_1_LP_INT_ENA : R/W; bitpos: [17]; default: 0;
 *  reg_0p1a_1_counter after xpd reach target1
 */
#define PMU_0P1A_CNT_TARGET1_REACH_1_LP_INT_ENA    (BIT(17))
#define PMU_0P1A_CNT_TARGET1_REACH_1_LP_INT_ENA_M  (PMU_0P1A_CNT_TARGET1_REACH_1_LP_INT_ENA_V << PMU_0P1A_CNT_TARGET1_REACH_1_LP_INT_ENA_S)
#define PMU_0P1A_CNT_TARGET1_REACH_1_LP_INT_ENA_V  0x00000001U
#define PMU_0P1A_CNT_TARGET1_REACH_1_LP_INT_ENA_S  17
/** PMU_0P2A_CNT_TARGET0_REACH_0_LP_INT_ENA : R/W; bitpos: [18]; default: 0;
 *  reg_0p2a_0 counter after xpd reach target0
 */
#define PMU_0P2A_CNT_TARGET0_REACH_0_LP_INT_ENA    (BIT(18))
#define PMU_0P2A_CNT_TARGET0_REACH_0_LP_INT_ENA_M  (PMU_0P2A_CNT_TARGET0_REACH_0_LP_INT_ENA_V << PMU_0P2A_CNT_TARGET0_REACH_0_LP_INT_ENA_S)
#define PMU_0P2A_CNT_TARGET0_REACH_0_LP_INT_ENA_V  0x00000001U
#define PMU_0P2A_CNT_TARGET0_REACH_0_LP_INT_ENA_S  18
/** PMU_0P2A_CNT_TARGET1_REACH_0_LP_INT_ENA : R/W; bitpos: [19]; default: 0;
 *  reg_0p2a_1_counter after xpd reach target1
 */
#define PMU_0P2A_CNT_TARGET1_REACH_0_LP_INT_ENA    (BIT(19))
#define PMU_0P2A_CNT_TARGET1_REACH_0_LP_INT_ENA_M  (PMU_0P2A_CNT_TARGET1_REACH_0_LP_INT_ENA_V << PMU_0P2A_CNT_TARGET1_REACH_0_LP_INT_ENA_S)
#define PMU_0P2A_CNT_TARGET1_REACH_0_LP_INT_ENA_V  0x00000001U
#define PMU_0P2A_CNT_TARGET1_REACH_0_LP_INT_ENA_S  19
/** PMU_0P2A_CNT_TARGET0_REACH_1_LP_INT_ENA : R/W; bitpos: [20]; default: 0;
 *  reg_0p2a_0 counter after xpd reach target0
 */
#define PMU_0P2A_CNT_TARGET0_REACH_1_LP_INT_ENA    (BIT(20))
#define PMU_0P2A_CNT_TARGET0_REACH_1_LP_INT_ENA_M  (PMU_0P2A_CNT_TARGET0_REACH_1_LP_INT_ENA_V << PMU_0P2A_CNT_TARGET0_REACH_1_LP_INT_ENA_S)
#define PMU_0P2A_CNT_TARGET0_REACH_1_LP_INT_ENA_V  0x00000001U
#define PMU_0P2A_CNT_TARGET0_REACH_1_LP_INT_ENA_S  20
/** PMU_0P2A_CNT_TARGET1_REACH_1_LP_INT_ENA : R/W; bitpos: [21]; default: 0;
 *  reg_0p2a_1_counter after xpd reach target1
 */
#define PMU_0P2A_CNT_TARGET1_REACH_1_LP_INT_ENA    (BIT(21))
#define PMU_0P2A_CNT_TARGET1_REACH_1_LP_INT_ENA_M  (PMU_0P2A_CNT_TARGET1_REACH_1_LP_INT_ENA_V << PMU_0P2A_CNT_TARGET1_REACH_1_LP_INT_ENA_S)
#define PMU_0P2A_CNT_TARGET1_REACH_1_LP_INT_ENA_V  0x00000001U
#define PMU_0P2A_CNT_TARGET1_REACH_1_LP_INT_ENA_S  21
/** PMU_0P3A_CNT_TARGET0_REACH_0_LP_INT_ENA : R/W; bitpos: [22]; default: 0;
 *  reg_0p3a_0 counter after xpd reach target0
 */
#define PMU_0P3A_CNT_TARGET0_REACH_0_LP_INT_ENA    (BIT(22))
#define PMU_0P3A_CNT_TARGET0_REACH_0_LP_INT_ENA_M  (PMU_0P3A_CNT_TARGET0_REACH_0_LP_INT_ENA_V << PMU_0P3A_CNT_TARGET0_REACH_0_LP_INT_ENA_S)
#define PMU_0P3A_CNT_TARGET0_REACH_0_LP_INT_ENA_V  0x00000001U
#define PMU_0P3A_CNT_TARGET0_REACH_0_LP_INT_ENA_S  22
/** PMU_0P3A_CNT_TARGET1_REACH_0_LP_INT_ENA : R/W; bitpos: [23]; default: 0;
 *  reg_0p3a_1_counter after xpd reach target1
 */
#define PMU_0P3A_CNT_TARGET1_REACH_0_LP_INT_ENA    (BIT(23))
#define PMU_0P3A_CNT_TARGET1_REACH_0_LP_INT_ENA_M  (PMU_0P3A_CNT_TARGET1_REACH_0_LP_INT_ENA_V << PMU_0P3A_CNT_TARGET1_REACH_0_LP_INT_ENA_S)
#define PMU_0P3A_CNT_TARGET1_REACH_0_LP_INT_ENA_V  0x00000001U
#define PMU_0P3A_CNT_TARGET1_REACH_0_LP_INT_ENA_S  23
/** PMU_0P3A_CNT_TARGET0_REACH_1_LP_INT_ENA : R/W; bitpos: [24]; default: 0;
 *  reg_0p3a_0_counter after xpd reach target0
 */
#define PMU_0P3A_CNT_TARGET0_REACH_1_LP_INT_ENA    (BIT(24))
#define PMU_0P3A_CNT_TARGET0_REACH_1_LP_INT_ENA_M  (PMU_0P3A_CNT_TARGET0_REACH_1_LP_INT_ENA_V << PMU_0P3A_CNT_TARGET0_REACH_1_LP_INT_ENA_S)
#define PMU_0P3A_CNT_TARGET0_REACH_1_LP_INT_ENA_V  0x00000001U
#define PMU_0P3A_CNT_TARGET0_REACH_1_LP_INT_ENA_S  24
/** PMU_0P3A_CNT_TARGET1_REACH_1_LP_INT_ENA : R/W; bitpos: [25]; default: 0;
 *  reg_0p3a_1_counter after xpd reach target1
 */
#define PMU_0P3A_CNT_TARGET1_REACH_1_LP_INT_ENA    (BIT(25))
#define PMU_0P3A_CNT_TARGET1_REACH_1_LP_INT_ENA_M  (PMU_0P3A_CNT_TARGET1_REACH_1_LP_INT_ENA_V << PMU_0P3A_CNT_TARGET1_REACH_1_LP_INT_ENA_S)
#define PMU_0P3A_CNT_TARGET1_REACH_1_LP_INT_ENA_V  0x00000001U
#define PMU_0P3A_CNT_TARGET1_REACH_1_LP_INT_ENA_S  25
/** PMU_LP_CPU_WAKEUP_INT_ENA : R/W; bitpos: [26]; default: 0;
 *  need_des
 */
#define PMU_LP_CPU_WAKEUP_INT_ENA    (BIT(26))
#define PMU_LP_CPU_WAKEUP_INT_ENA_M  (PMU_LP_CPU_WAKEUP_INT_ENA_V << PMU_LP_CPU_WAKEUP_INT_ENA_S)
#define PMU_LP_CPU_WAKEUP_INT_ENA_V  0x00000001U
#define PMU_LP_CPU_WAKEUP_INT_ENA_S  26
/** PMU_SLEEP_SWITCH_ACTIVE_END_INT_ENA : R/W; bitpos: [27]; default: 0;
 *  need_des
 */
#define PMU_SLEEP_SWITCH_ACTIVE_END_INT_ENA    (BIT(27))
#define PMU_SLEEP_SWITCH_ACTIVE_END_INT_ENA_M  (PMU_SLEEP_SWITCH_ACTIVE_END_INT_ENA_V << PMU_SLEEP_SWITCH_ACTIVE_END_INT_ENA_S)
#define PMU_SLEEP_SWITCH_ACTIVE_END_INT_ENA_V  0x00000001U
#define PMU_SLEEP_SWITCH_ACTIVE_END_INT_ENA_S  27
/** PMU_ACTIVE_SWITCH_SLEEP_END_INT_ENA : R/W; bitpos: [28]; default: 0;
 *  need_des
 */
#define PMU_ACTIVE_SWITCH_SLEEP_END_INT_ENA    (BIT(28))
#define PMU_ACTIVE_SWITCH_SLEEP_END_INT_ENA_M  (PMU_ACTIVE_SWITCH_SLEEP_END_INT_ENA_V << PMU_ACTIVE_SWITCH_SLEEP_END_INT_ENA_S)
#define PMU_ACTIVE_SWITCH_SLEEP_END_INT_ENA_V  0x00000001U
#define PMU_ACTIVE_SWITCH_SLEEP_END_INT_ENA_S  28
/** PMU_SLEEP_SWITCH_ACTIVE_START_INT_ENA : R/W; bitpos: [29]; default: 0;
 *  need_des
 */
#define PMU_SLEEP_SWITCH_ACTIVE_START_INT_ENA    (BIT(29))
#define PMU_SLEEP_SWITCH_ACTIVE_START_INT_ENA_M  (PMU_SLEEP_SWITCH_ACTIVE_START_INT_ENA_V << PMU_SLEEP_SWITCH_ACTIVE_START_INT_ENA_S)
#define PMU_SLEEP_SWITCH_ACTIVE_START_INT_ENA_V  0x00000001U
#define PMU_SLEEP_SWITCH_ACTIVE_START_INT_ENA_S  29
/** PMU_ACTIVE_SWITCH_SLEEP_START_INT_ENA : R/W; bitpos: [30]; default: 0;
 *  need_des
 */
#define PMU_ACTIVE_SWITCH_SLEEP_START_INT_ENA    (BIT(30))
#define PMU_ACTIVE_SWITCH_SLEEP_START_INT_ENA_M  (PMU_ACTIVE_SWITCH_SLEEP_START_INT_ENA_V << PMU_ACTIVE_SWITCH_SLEEP_START_INT_ENA_S)
#define PMU_ACTIVE_SWITCH_SLEEP_START_INT_ENA_V  0x00000001U
#define PMU_ACTIVE_SWITCH_SLEEP_START_INT_ENA_S  30
/** PMU_HP_SW_TRIGGER_INT_ENA : R/W; bitpos: [31]; default: 0;
 *  need_des
 */
#define PMU_HP_SW_TRIGGER_INT_ENA    (BIT(31))
#define PMU_HP_SW_TRIGGER_INT_ENA_M  (PMU_HP_SW_TRIGGER_INT_ENA_V << PMU_HP_SW_TRIGGER_INT_ENA_S)
#define PMU_HP_SW_TRIGGER_INT_ENA_V  0x00000001U
#define PMU_HP_SW_TRIGGER_INT_ENA_S  31

/** PMU_LP_INT_CLR_REG register
 *  need_des
 */
#define PMU_LP_INT_CLR_REG (DR_REG_PMU_BASE + 0x180)
/** PMU_LP_CPU_SLEEP_REJECT_LP_INT_CLR : WT; bitpos: [13]; default: 0;
 *  need_des
 */
#define PMU_LP_CPU_SLEEP_REJECT_LP_INT_CLR    (BIT(13))
#define PMU_LP_CPU_SLEEP_REJECT_LP_INT_CLR_M  (PMU_LP_CPU_SLEEP_REJECT_LP_INT_CLR_V << PMU_LP_CPU_SLEEP_REJECT_LP_INT_CLR_S)
#define PMU_LP_CPU_SLEEP_REJECT_LP_INT_CLR_V  0x00000001U
#define PMU_LP_CPU_SLEEP_REJECT_LP_INT_CLR_S  13
/** PMU_0P1A_CNT_TARGET0_REACH_0_LP_INT_CLR : WT; bitpos: [14]; default: 0;
 *  reg_0p1a_0_counter after xpd reach target0
 */
#define PMU_0P1A_CNT_TARGET0_REACH_0_LP_INT_CLR    (BIT(14))
#define PMU_0P1A_CNT_TARGET0_REACH_0_LP_INT_CLR_M  (PMU_0P1A_CNT_TARGET0_REACH_0_LP_INT_CLR_V << PMU_0P1A_CNT_TARGET0_REACH_0_LP_INT_CLR_S)
#define PMU_0P1A_CNT_TARGET0_REACH_0_LP_INT_CLR_V  0x00000001U
#define PMU_0P1A_CNT_TARGET0_REACH_0_LP_INT_CLR_S  14
/** PMU_0P1A_CNT_TARGET1_REACH_0_LP_INT_CLR : WT; bitpos: [15]; default: 0;
 *  reg_0p1a_1_counter after xpd reach target1
 */
#define PMU_0P1A_CNT_TARGET1_REACH_0_LP_INT_CLR    (BIT(15))
#define PMU_0P1A_CNT_TARGET1_REACH_0_LP_INT_CLR_M  (PMU_0P1A_CNT_TARGET1_REACH_0_LP_INT_CLR_V << PMU_0P1A_CNT_TARGET1_REACH_0_LP_INT_CLR_S)
#define PMU_0P1A_CNT_TARGET1_REACH_0_LP_INT_CLR_V  0x00000001U
#define PMU_0P1A_CNT_TARGET1_REACH_0_LP_INT_CLR_S  15
/** PMU_0P1A_CNT_TARGET0_REACH_1_LP_INT_CLR : WT; bitpos: [16]; default: 0;
 *  reg_0p1a_0 counter after xpd reach target0
 */
#define PMU_0P1A_CNT_TARGET0_REACH_1_LP_INT_CLR    (BIT(16))
#define PMU_0P1A_CNT_TARGET0_REACH_1_LP_INT_CLR_M  (PMU_0P1A_CNT_TARGET0_REACH_1_LP_INT_CLR_V << PMU_0P1A_CNT_TARGET0_REACH_1_LP_INT_CLR_S)
#define PMU_0P1A_CNT_TARGET0_REACH_1_LP_INT_CLR_V  0x00000001U
#define PMU_0P1A_CNT_TARGET0_REACH_1_LP_INT_CLR_S  16
/** PMU_0P1A_CNT_TARGET1_REACH_1_LP_INT_CLR : WT; bitpos: [17]; default: 0;
 *  reg_0p1a_1_counter after xpd reach target1
 */
#define PMU_0P1A_CNT_TARGET1_REACH_1_LP_INT_CLR    (BIT(17))
#define PMU_0P1A_CNT_TARGET1_REACH_1_LP_INT_CLR_M  (PMU_0P1A_CNT_TARGET1_REACH_1_LP_INT_CLR_V << PMU_0P1A_CNT_TARGET1_REACH_1_LP_INT_CLR_S)
#define PMU_0P1A_CNT_TARGET1_REACH_1_LP_INT_CLR_V  0x00000001U
#define PMU_0P1A_CNT_TARGET1_REACH_1_LP_INT_CLR_S  17
/** PMU_0P2A_CNT_TARGET0_REACH_0_LP_INT_CLR : WT; bitpos: [18]; default: 0;
 *  reg_0p2a_0 counter after xpd reach target0
 */
#define PMU_0P2A_CNT_TARGET0_REACH_0_LP_INT_CLR    (BIT(18))
#define PMU_0P2A_CNT_TARGET0_REACH_0_LP_INT_CLR_M  (PMU_0P2A_CNT_TARGET0_REACH_0_LP_INT_CLR_V << PMU_0P2A_CNT_TARGET0_REACH_0_LP_INT_CLR_S)
#define PMU_0P2A_CNT_TARGET0_REACH_0_LP_INT_CLR_V  0x00000001U
#define PMU_0P2A_CNT_TARGET0_REACH_0_LP_INT_CLR_S  18
/** PMU_0P2A_CNT_TARGET1_REACH_0_LP_INT_CLR : WT; bitpos: [19]; default: 0;
 *  reg_0p2a_1_counter after xpd reach target1
 */
#define PMU_0P2A_CNT_TARGET1_REACH_0_LP_INT_CLR    (BIT(19))
#define PMU_0P2A_CNT_TARGET1_REACH_0_LP_INT_CLR_M  (PMU_0P2A_CNT_TARGET1_REACH_0_LP_INT_CLR_V << PMU_0P2A_CNT_TARGET1_REACH_0_LP_INT_CLR_S)
#define PMU_0P2A_CNT_TARGET1_REACH_0_LP_INT_CLR_V  0x00000001U
#define PMU_0P2A_CNT_TARGET1_REACH_0_LP_INT_CLR_S  19
/** PMU_0P2A_CNT_TARGET0_REACH_1_LP_INT_CLR : WT; bitpos: [20]; default: 0;
 *  reg_0p2a_0 counter after xpd reach target0
 */
#define PMU_0P2A_CNT_TARGET0_REACH_1_LP_INT_CLR    (BIT(20))
#define PMU_0P2A_CNT_TARGET0_REACH_1_LP_INT_CLR_M  (PMU_0P2A_CNT_TARGET0_REACH_1_LP_INT_CLR_V << PMU_0P2A_CNT_TARGET0_REACH_1_LP_INT_CLR_S)
#define PMU_0P2A_CNT_TARGET0_REACH_1_LP_INT_CLR_V  0x00000001U
#define PMU_0P2A_CNT_TARGET0_REACH_1_LP_INT_CLR_S  20
/** PMU_0P2A_CNT_TARGET1_REACH_1_LP_INT_CLR : WT; bitpos: [21]; default: 0;
 *  reg_0p2a_1_counter after xpd reach target1
 */
#define PMU_0P2A_CNT_TARGET1_REACH_1_LP_INT_CLR    (BIT(21))
#define PMU_0P2A_CNT_TARGET1_REACH_1_LP_INT_CLR_M  (PMU_0P2A_CNT_TARGET1_REACH_1_LP_INT_CLR_V << PMU_0P2A_CNT_TARGET1_REACH_1_LP_INT_CLR_S)
#define PMU_0P2A_CNT_TARGET1_REACH_1_LP_INT_CLR_V  0x00000001U
#define PMU_0P2A_CNT_TARGET1_REACH_1_LP_INT_CLR_S  21
/** PMU_0P3A_CNT_TARGET0_REACH_0_LP_INT_CLR : WT; bitpos: [22]; default: 0;
 *  reg_0p3a_0 counter after xpd reach target0
 */
#define PMU_0P3A_CNT_TARGET0_REACH_0_LP_INT_CLR    (BIT(22))
#define PMU_0P3A_CNT_TARGET0_REACH_0_LP_INT_CLR_M  (PMU_0P3A_CNT_TARGET0_REACH_0_LP_INT_CLR_V << PMU_0P3A_CNT_TARGET0_REACH_0_LP_INT_CLR_S)
#define PMU_0P3A_CNT_TARGET0_REACH_0_LP_INT_CLR_V  0x00000001U
#define PMU_0P3A_CNT_TARGET0_REACH_0_LP_INT_CLR_S  22
/** PMU_0P3A_CNT_TARGET1_REACH_0_LP_INT_CLR : WT; bitpos: [23]; default: 0;
 *  reg_0p3a_1_counter after xpd reach target1
 */
#define PMU_0P3A_CNT_TARGET1_REACH_0_LP_INT_CLR    (BIT(23))
#define PMU_0P3A_CNT_TARGET1_REACH_0_LP_INT_CLR_M  (PMU_0P3A_CNT_TARGET1_REACH_0_LP_INT_CLR_V << PMU_0P3A_CNT_TARGET1_REACH_0_LP_INT_CLR_S)
#define PMU_0P3A_CNT_TARGET1_REACH_0_LP_INT_CLR_V  0x00000001U
#define PMU_0P3A_CNT_TARGET1_REACH_0_LP_INT_CLR_S  23
/** PMU_0P3A_CNT_TARGET0_REACH_1_LP_INT_CLR : WT; bitpos: [24]; default: 0;
 *  reg_0p3a_0_counter after xpd reach target0
 */
#define PMU_0P3A_CNT_TARGET0_REACH_1_LP_INT_CLR    (BIT(24))
#define PMU_0P3A_CNT_TARGET0_REACH_1_LP_INT_CLR_M  (PMU_0P3A_CNT_TARGET0_REACH_1_LP_INT_CLR_V << PMU_0P3A_CNT_TARGET0_REACH_1_LP_INT_CLR_S)
#define PMU_0P3A_CNT_TARGET0_REACH_1_LP_INT_CLR_V  0x00000001U
#define PMU_0P3A_CNT_TARGET0_REACH_1_LP_INT_CLR_S  24
/** PMU_0P3A_CNT_TARGET1_REACH_1_LP_INT_CLR : WT; bitpos: [25]; default: 0;
 *  reg_0p3a_1_counter after xpd reach target1
 */
#define PMU_0P3A_CNT_TARGET1_REACH_1_LP_INT_CLR    (BIT(25))
#define PMU_0P3A_CNT_TARGET1_REACH_1_LP_INT_CLR_M  (PMU_0P3A_CNT_TARGET1_REACH_1_LP_INT_CLR_V << PMU_0P3A_CNT_TARGET1_REACH_1_LP_INT_CLR_S)
#define PMU_0P3A_CNT_TARGET1_REACH_1_LP_INT_CLR_V  0x00000001U
#define PMU_0P3A_CNT_TARGET1_REACH_1_LP_INT_CLR_S  25
/** PMU_LP_CPU_WAKEUP_INT_CLR : WT; bitpos: [26]; default: 0;
 *  need_des
 */
#define PMU_LP_CPU_WAKEUP_INT_CLR    (BIT(26))
#define PMU_LP_CPU_WAKEUP_INT_CLR_M  (PMU_LP_CPU_WAKEUP_INT_CLR_V << PMU_LP_CPU_WAKEUP_INT_CLR_S)
#define PMU_LP_CPU_WAKEUP_INT_CLR_V  0x00000001U
#define PMU_LP_CPU_WAKEUP_INT_CLR_S  26
/** PMU_SLEEP_SWITCH_ACTIVE_END_INT_CLR : WT; bitpos: [27]; default: 0;
 *  need_des
 */
#define PMU_SLEEP_SWITCH_ACTIVE_END_INT_CLR    (BIT(27))
#define PMU_SLEEP_SWITCH_ACTIVE_END_INT_CLR_M  (PMU_SLEEP_SWITCH_ACTIVE_END_INT_CLR_V << PMU_SLEEP_SWITCH_ACTIVE_END_INT_CLR_S)
#define PMU_SLEEP_SWITCH_ACTIVE_END_INT_CLR_V  0x00000001U
#define PMU_SLEEP_SWITCH_ACTIVE_END_INT_CLR_S  27
/** PMU_ACTIVE_SWITCH_SLEEP_END_INT_CLR : WT; bitpos: [28]; default: 0;
 *  need_des
 */
#define PMU_ACTIVE_SWITCH_SLEEP_END_INT_CLR    (BIT(28))
#define PMU_ACTIVE_SWITCH_SLEEP_END_INT_CLR_M  (PMU_ACTIVE_SWITCH_SLEEP_END_INT_CLR_V << PMU_ACTIVE_SWITCH_SLEEP_END_INT_CLR_S)
#define PMU_ACTIVE_SWITCH_SLEEP_END_INT_CLR_V  0x00000001U
#define PMU_ACTIVE_SWITCH_SLEEP_END_INT_CLR_S  28
/** PMU_SLEEP_SWITCH_ACTIVE_START_INT_CLR : WT; bitpos: [29]; default: 0;
 *  need_des
 */
#define PMU_SLEEP_SWITCH_ACTIVE_START_INT_CLR    (BIT(29))
#define PMU_SLEEP_SWITCH_ACTIVE_START_INT_CLR_M  (PMU_SLEEP_SWITCH_ACTIVE_START_INT_CLR_V << PMU_SLEEP_SWITCH_ACTIVE_START_INT_CLR_S)
#define PMU_SLEEP_SWITCH_ACTIVE_START_INT_CLR_V  0x00000001U
#define PMU_SLEEP_SWITCH_ACTIVE_START_INT_CLR_S  29
/** PMU_ACTIVE_SWITCH_SLEEP_START_INT_CLR : WT; bitpos: [30]; default: 0;
 *  need_des
 */
#define PMU_ACTIVE_SWITCH_SLEEP_START_INT_CLR    (BIT(30))
#define PMU_ACTIVE_SWITCH_SLEEP_START_INT_CLR_M  (PMU_ACTIVE_SWITCH_SLEEP_START_INT_CLR_V << PMU_ACTIVE_SWITCH_SLEEP_START_INT_CLR_S)
#define PMU_ACTIVE_SWITCH_SLEEP_START_INT_CLR_V  0x00000001U
#define PMU_ACTIVE_SWITCH_SLEEP_START_INT_CLR_S  30
/** PMU_HP_SW_TRIGGER_INT_CLR : WT; bitpos: [31]; default: 0;
 *  need_des
 */
#define PMU_HP_SW_TRIGGER_INT_CLR    (BIT(31))
#define PMU_HP_SW_TRIGGER_INT_CLR_M  (PMU_HP_SW_TRIGGER_INT_CLR_V << PMU_HP_SW_TRIGGER_INT_CLR_S)
#define PMU_HP_SW_TRIGGER_INT_CLR_V  0x00000001U
#define PMU_HP_SW_TRIGGER_INT_CLR_S  31

/** PMU_LP_CPU_PWR0_REG register
 *  need_des
 */
#define PMU_LP_CPU_PWR0_REG (DR_REG_PMU_BASE + 0x184)
/** PMU_LP_CPU_WAITI_RDY : RO; bitpos: [0]; default: 0;
 *  need_des
 */
#define PMU_LP_CPU_WAITI_RDY    (BIT(0))
#define PMU_LP_CPU_WAITI_RDY_M  (PMU_LP_CPU_WAITI_RDY_V << PMU_LP_CPU_WAITI_RDY_S)
#define PMU_LP_CPU_WAITI_RDY_V  0x00000001U
#define PMU_LP_CPU_WAITI_RDY_S  0
/** PMU_LP_CPU_STALL_RDY : RO; bitpos: [1]; default: 0;
 *  need_des
 */
#define PMU_LP_CPU_STALL_RDY    (BIT(1))
#define PMU_LP_CPU_STALL_RDY_M  (PMU_LP_CPU_STALL_RDY_V << PMU_LP_CPU_STALL_RDY_S)
#define PMU_LP_CPU_STALL_RDY_V  0x00000001U
#define PMU_LP_CPU_STALL_RDY_S  1
/** PMU_LP_CPU_FORCE_STALL : R/W; bitpos: [18]; default: 0;
 *  need_des
 */
#define PMU_LP_CPU_FORCE_STALL    (BIT(18))
#define PMU_LP_CPU_FORCE_STALL_M  (PMU_LP_CPU_FORCE_STALL_V << PMU_LP_CPU_FORCE_STALL_S)
#define PMU_LP_CPU_FORCE_STALL_V  0x00000001U
#define PMU_LP_CPU_FORCE_STALL_S  18
/** PMU_LP_CPU_SLP_WAITI_FLAG_EN : R/W; bitpos: [19]; default: 0;
 *  need_des
 */
#define PMU_LP_CPU_SLP_WAITI_FLAG_EN    (BIT(19))
#define PMU_LP_CPU_SLP_WAITI_FLAG_EN_M  (PMU_LP_CPU_SLP_WAITI_FLAG_EN_V << PMU_LP_CPU_SLP_WAITI_FLAG_EN_S)
#define PMU_LP_CPU_SLP_WAITI_FLAG_EN_V  0x00000001U
#define PMU_LP_CPU_SLP_WAITI_FLAG_EN_S  19
/** PMU_LP_CPU_SLP_STALL_FLAG_EN : R/W; bitpos: [20]; default: 1;
 *  need_des
 */
#define PMU_LP_CPU_SLP_STALL_FLAG_EN    (BIT(20))
#define PMU_LP_CPU_SLP_STALL_FLAG_EN_M  (PMU_LP_CPU_SLP_STALL_FLAG_EN_V << PMU_LP_CPU_SLP_STALL_FLAG_EN_S)
#define PMU_LP_CPU_SLP_STALL_FLAG_EN_V  0x00000001U
#define PMU_LP_CPU_SLP_STALL_FLAG_EN_S  20
/** PMU_LP_CPU_SLP_STALL_WAIT : R/W; bitpos: [28:21]; default: 255;
 *  need_des
 */
#define PMU_LP_CPU_SLP_STALL_WAIT    0x000000FFU
#define PMU_LP_CPU_SLP_STALL_WAIT_M  (PMU_LP_CPU_SLP_STALL_WAIT_V << PMU_LP_CPU_SLP_STALL_WAIT_S)
#define PMU_LP_CPU_SLP_STALL_WAIT_V  0x000000FFU
#define PMU_LP_CPU_SLP_STALL_WAIT_S  21
/** PMU_LP_CPU_SLP_STALL_EN : R/W; bitpos: [29]; default: 0;
 *  need_des
 */
#define PMU_LP_CPU_SLP_STALL_EN    (BIT(29))
#define PMU_LP_CPU_SLP_STALL_EN_M  (PMU_LP_CPU_SLP_STALL_EN_V << PMU_LP_CPU_SLP_STALL_EN_S)
#define PMU_LP_CPU_SLP_STALL_EN_V  0x00000001U
#define PMU_LP_CPU_SLP_STALL_EN_S  29
/** PMU_LP_CPU_SLP_RESET_EN : R/W; bitpos: [30]; default: 0;
 *  need_des
 */
#define PMU_LP_CPU_SLP_RESET_EN    (BIT(30))
#define PMU_LP_CPU_SLP_RESET_EN_M  (PMU_LP_CPU_SLP_RESET_EN_V << PMU_LP_CPU_SLP_RESET_EN_S)
#define PMU_LP_CPU_SLP_RESET_EN_V  0x00000001U
#define PMU_LP_CPU_SLP_RESET_EN_S  30
/** PMU_LP_CPU_SLP_BYPASS_INTR_EN : R/W; bitpos: [31]; default: 0;
 *  need_des
 */
#define PMU_LP_CPU_SLP_BYPASS_INTR_EN    (BIT(31))
#define PMU_LP_CPU_SLP_BYPASS_INTR_EN_M  (PMU_LP_CPU_SLP_BYPASS_INTR_EN_V << PMU_LP_CPU_SLP_BYPASS_INTR_EN_S)
#define PMU_LP_CPU_SLP_BYPASS_INTR_EN_V  0x00000001U
#define PMU_LP_CPU_SLP_BYPASS_INTR_EN_S  31

/** PMU_LP_CPU_PWR1_REG register
 *  need_des
 */
#define PMU_LP_CPU_PWR1_REG (DR_REG_PMU_BASE + 0x188)
/** PMU_LP_CPU_SLEEP_REQ : WT; bitpos: [31]; default: 0;
 *  need_des
 */
#define PMU_LP_CPU_SLEEP_REQ    (BIT(31))
#define PMU_LP_CPU_SLEEP_REQ_M  (PMU_LP_CPU_SLEEP_REQ_V << PMU_LP_CPU_SLEEP_REQ_S)
#define PMU_LP_CPU_SLEEP_REQ_V  0x00000001U
#define PMU_LP_CPU_SLEEP_REQ_S  31

/** PMU_LP_CPU_PWR2_REG register
 *  need_des
 */
#define PMU_LP_CPU_PWR2_REG (DR_REG_PMU_BASE + 0x18c)
/** PMU_LP_CPU_WAKEUP_EN : R/W; bitpos: [30:0]; default: 0;
 *  need_des
 */
#define PMU_LP_CPU_WAKEUP_EN    0x7FFFFFFFU
#define PMU_LP_CPU_WAKEUP_EN_M  (PMU_LP_CPU_WAKEUP_EN_V << PMU_LP_CPU_WAKEUP_EN_S)
#define PMU_LP_CPU_WAKEUP_EN_V  0x7FFFFFFFU
#define PMU_LP_CPU_WAKEUP_EN_S  0

/** PMU_LP_CPU_PWR3_REG register
 *  need_des
 */
#define PMU_LP_CPU_PWR3_REG (DR_REG_PMU_BASE + 0x190)
/** PMU_LP_CPU_WAKEUP_CAUSE : RO; bitpos: [30:0]; default: 0;
 *  need_des
 */
#define PMU_LP_CPU_WAKEUP_CAUSE    0x7FFFFFFFU
#define PMU_LP_CPU_WAKEUP_CAUSE_M  (PMU_LP_CPU_WAKEUP_CAUSE_V << PMU_LP_CPU_WAKEUP_CAUSE_S)
#define PMU_LP_CPU_WAKEUP_CAUSE_V  0x7FFFFFFFU
#define PMU_LP_CPU_WAKEUP_CAUSE_S  0

/** PMU_LP_CPU_PWR4_REG register
 *  need_des
 */
#define PMU_LP_CPU_PWR4_REG (DR_REG_PMU_BASE + 0x194)
/** PMU_LP_CPU_REJECT_EN : R/W; bitpos: [30:0]; default: 0;
 *  need_des
 */
#define PMU_LP_CPU_REJECT_EN    0x7FFFFFFFU
#define PMU_LP_CPU_REJECT_EN_M  (PMU_LP_CPU_REJECT_EN_V << PMU_LP_CPU_REJECT_EN_S)
#define PMU_LP_CPU_REJECT_EN_V  0x7FFFFFFFU
#define PMU_LP_CPU_REJECT_EN_S  0

/** PMU_LP_CPU_PWR5_REG register
 *  need_des
 */
#define PMU_LP_CPU_PWR5_REG (DR_REG_PMU_BASE + 0x198)
/** PMU_LP_CPU_REJECT_CAUSE : RO; bitpos: [30:0]; default: 0;
 *  need_des
 */
#define PMU_LP_CPU_REJECT_CAUSE    0x7FFFFFFFU
#define PMU_LP_CPU_REJECT_CAUSE_M  (PMU_LP_CPU_REJECT_CAUSE_V << PMU_LP_CPU_REJECT_CAUSE_S)
#define PMU_LP_CPU_REJECT_CAUSE_V  0x7FFFFFFFU
#define PMU_LP_CPU_REJECT_CAUSE_S  0

/** PMU_HP_LP_CPU_COMM_REG register
 *  need_des
 */
#define PMU_HP_LP_CPU_COMM_REG (DR_REG_PMU_BASE + 0x19c)
/** PMU_LP_TRIGGER_HP : WT; bitpos: [30]; default: 0;
 *  need_des
 */
#define PMU_LP_TRIGGER_HP    (BIT(30))
#define PMU_LP_TRIGGER_HP_M  (PMU_LP_TRIGGER_HP_V << PMU_LP_TRIGGER_HP_S)
#define PMU_LP_TRIGGER_HP_V  0x00000001U
#define PMU_LP_TRIGGER_HP_S  30
/** PMU_HP_TRIGGER_LP : WT; bitpos: [31]; default: 0;
 *  need_des
 */
#define PMU_HP_TRIGGER_LP    (BIT(31))
#define PMU_HP_TRIGGER_LP_M  (PMU_HP_TRIGGER_LP_V << PMU_HP_TRIGGER_LP_S)
#define PMU_HP_TRIGGER_LP_V  0x00000001U
#define PMU_HP_TRIGGER_LP_S  31

/** PMU_HP_REGULATOR_CFG_REG register
 *  need_des
 */
#define PMU_HP_REGULATOR_CFG_REG (DR_REG_PMU_BASE + 0x1a0)
/** PMU_DIG_REGULATOR_EN_CAL : R/W; bitpos: [31]; default: 0;
 *  need_des
 */
#define PMU_DIG_REGULATOR_EN_CAL    (BIT(31))
#define PMU_DIG_REGULATOR_EN_CAL_M  (PMU_DIG_REGULATOR_EN_CAL_V << PMU_DIG_REGULATOR_EN_CAL_S)
#define PMU_DIG_REGULATOR_EN_CAL_V  0x00000001U
#define PMU_DIG_REGULATOR_EN_CAL_S  31

/** PMU_MAIN_STATE_REG register
 *  need_des
 */
#define PMU_MAIN_STATE_REG (DR_REG_PMU_BASE + 0x1a4)
/** PMU_ENABLE_CALI_PMU_CNTL : R/W; bitpos: [0]; default: 1;
 *  need_des
 */
#define PMU_ENABLE_CALI_PMU_CNTL    (BIT(0))
#define PMU_ENABLE_CALI_PMU_CNTL_M  (PMU_ENABLE_CALI_PMU_CNTL_V << PMU_ENABLE_CALI_PMU_CNTL_S)
#define PMU_ENABLE_CALI_PMU_CNTL_V  0x00000001U
#define PMU_ENABLE_CALI_PMU_CNTL_S  0
/** PMU_PMU_MAIN_LAST_ST_STATE : RO; bitpos: [17:11]; default: 1;
 *  need_des
 */
#define PMU_PMU_MAIN_LAST_ST_STATE    0x0000007FU
#define PMU_PMU_MAIN_LAST_ST_STATE_M  (PMU_PMU_MAIN_LAST_ST_STATE_V << PMU_PMU_MAIN_LAST_ST_STATE_S)
#define PMU_PMU_MAIN_LAST_ST_STATE_V  0x0000007FU
#define PMU_PMU_MAIN_LAST_ST_STATE_S  11
/** PMU_PMU_MAIN_TAR_ST_STATE : RO; bitpos: [24:18]; default: 4;
 *  need_des
 */
#define PMU_PMU_MAIN_TAR_ST_STATE    0x0000007FU
#define PMU_PMU_MAIN_TAR_ST_STATE_M  (PMU_PMU_MAIN_TAR_ST_STATE_V << PMU_PMU_MAIN_TAR_ST_STATE_S)
#define PMU_PMU_MAIN_TAR_ST_STATE_V  0x0000007FU
#define PMU_PMU_MAIN_TAR_ST_STATE_S  18
/** PMU_PMU_MAIN_CUR_ST_STATE : RO; bitpos: [31:25]; default: 4;
 *  need_des
 */
#define PMU_PMU_MAIN_CUR_ST_STATE    0x0000007FU
#define PMU_PMU_MAIN_CUR_ST_STATE_M  (PMU_PMU_MAIN_CUR_ST_STATE_V << PMU_PMU_MAIN_CUR_ST_STATE_S)
#define PMU_PMU_MAIN_CUR_ST_STATE_V  0x0000007FU
#define PMU_PMU_MAIN_CUR_ST_STATE_S  25

/** PMU_PWR_STATE_REG register
 *  need_des
 */
#define PMU_PWR_STATE_REG (DR_REG_PMU_BASE + 0x1a8)
/** PMU_PMU_BACKUP_ST_STATE : RO; bitpos: [17:13]; default: 1;
 *  need_des
 */
#define PMU_PMU_BACKUP_ST_STATE    0x0000001FU
#define PMU_PMU_BACKUP_ST_STATE_M  (PMU_PMU_BACKUP_ST_STATE_V << PMU_PMU_BACKUP_ST_STATE_S)
#define PMU_PMU_BACKUP_ST_STATE_V  0x0000001FU
#define PMU_PMU_BACKUP_ST_STATE_S  13
/** PMU_PMU_LP_PWR_ST_STATE : RO; bitpos: [22:18]; default: 0;
 *  need_des
 */
#define PMU_PMU_LP_PWR_ST_STATE    0x0000001FU
#define PMU_PMU_LP_PWR_ST_STATE_M  (PMU_PMU_LP_PWR_ST_STATE_V << PMU_PMU_LP_PWR_ST_STATE_S)
#define PMU_PMU_LP_PWR_ST_STATE_V  0x0000001FU
#define PMU_PMU_LP_PWR_ST_STATE_S  18
/** PMU_PMU_HP_PWR_ST_STATE : RO; bitpos: [31:23]; default: 1;
 *  need_des
 */
#define PMU_PMU_HP_PWR_ST_STATE    0x000001FFU
#define PMU_PMU_HP_PWR_ST_STATE_M  (PMU_PMU_HP_PWR_ST_STATE_V << PMU_PMU_HP_PWR_ST_STATE_S)
#define PMU_PMU_HP_PWR_ST_STATE_V  0x000001FFU
#define PMU_PMU_HP_PWR_ST_STATE_S  23

/** PMU_CLK_STATE0_REG register
 *  need_des
 */
#define PMU_CLK_STATE0_REG (DR_REG_PMU_BASE + 0x1ac)
/** PMU_STABLE_XPD_PLL_STATE : RO; bitpos: [2:0]; default: 7;
 *  need_des
 */
#define PMU_STABLE_XPD_PLL_STATE    0x00000007U
#define PMU_STABLE_XPD_PLL_STATE_M  (PMU_STABLE_XPD_PLL_STATE_V << PMU_STABLE_XPD_PLL_STATE_S)
#define PMU_STABLE_XPD_PLL_STATE_V  0x00000007U
#define PMU_STABLE_XPD_PLL_STATE_S  0
/** PMU_STABLE_XPD_XTAL_STATE : RO; bitpos: [3]; default: 1;
 *  need_des
 */
#define PMU_STABLE_XPD_XTAL_STATE    (BIT(3))
#define PMU_STABLE_XPD_XTAL_STATE_M  (PMU_STABLE_XPD_XTAL_STATE_V << PMU_STABLE_XPD_XTAL_STATE_S)
#define PMU_STABLE_XPD_XTAL_STATE_V  0x00000001U
#define PMU_STABLE_XPD_XTAL_STATE_S  3
/** PMU_PMU_ANA_XPD_PLL_I2C_STATE : RO; bitpos: [6:4]; default: 0;
 *  need_des
 */
#define PMU_PMU_ANA_XPD_PLL_I2C_STATE    0x00000007U
#define PMU_PMU_ANA_XPD_PLL_I2C_STATE_M  (PMU_PMU_ANA_XPD_PLL_I2C_STATE_V << PMU_PMU_ANA_XPD_PLL_I2C_STATE_S)
#define PMU_PMU_ANA_XPD_PLL_I2C_STATE_V  0x00000007U
#define PMU_PMU_ANA_XPD_PLL_I2C_STATE_S  4
/** PMU_PMU_SYS_CLK_SLP_SEL_STATE : RO; bitpos: [10]; default: 0;
 *  need_des
 */
#define PMU_PMU_SYS_CLK_SLP_SEL_STATE    (BIT(10))
#define PMU_PMU_SYS_CLK_SLP_SEL_STATE_M  (PMU_PMU_SYS_CLK_SLP_SEL_STATE_V << PMU_PMU_SYS_CLK_SLP_SEL_STATE_S)
#define PMU_PMU_SYS_CLK_SLP_SEL_STATE_V  0x00000001U
#define PMU_PMU_SYS_CLK_SLP_SEL_STATE_S  10
/** PMU_PMU_SYS_CLK_SEL_STATE : RO; bitpos: [12:11]; default: 0;
 *  need_des
 */
#define PMU_PMU_SYS_CLK_SEL_STATE    0x00000003U
#define PMU_PMU_SYS_CLK_SEL_STATE_M  (PMU_PMU_SYS_CLK_SEL_STATE_V << PMU_PMU_SYS_CLK_SEL_STATE_S)
#define PMU_PMU_SYS_CLK_SEL_STATE_V  0x00000003U
#define PMU_PMU_SYS_CLK_SEL_STATE_S  11
/** PMU_PMU_SYS_CLK_NO_DIV_STATE : RO; bitpos: [13]; default: 0;
 *  need_des
 */
#define PMU_PMU_SYS_CLK_NO_DIV_STATE    (BIT(13))
#define PMU_PMU_SYS_CLK_NO_DIV_STATE_M  (PMU_PMU_SYS_CLK_NO_DIV_STATE_V << PMU_PMU_SYS_CLK_NO_DIV_STATE_S)
#define PMU_PMU_SYS_CLK_NO_DIV_STATE_V  0x00000001U
#define PMU_PMU_SYS_CLK_NO_DIV_STATE_S  13
/** PMU_PMU_ICG_SYS_CLK_EN_STATE : RO; bitpos: [14]; default: 0;
 *  need_des
 */
#define PMU_PMU_ICG_SYS_CLK_EN_STATE    (BIT(14))
#define PMU_PMU_ICG_SYS_CLK_EN_STATE_M  (PMU_PMU_ICG_SYS_CLK_EN_STATE_V << PMU_PMU_ICG_SYS_CLK_EN_STATE_S)
#define PMU_PMU_ICG_SYS_CLK_EN_STATE_V  0x00000001U
#define PMU_PMU_ICG_SYS_CLK_EN_STATE_S  14
/** PMU_PMU_ICG_MODEM_SWITCH_STATE : RO; bitpos: [15]; default: 0;
 *  need_des
 */
#define PMU_PMU_ICG_MODEM_SWITCH_STATE    (BIT(15))
#define PMU_PMU_ICG_MODEM_SWITCH_STATE_M  (PMU_PMU_ICG_MODEM_SWITCH_STATE_V << PMU_PMU_ICG_MODEM_SWITCH_STATE_S)
#define PMU_PMU_ICG_MODEM_SWITCH_STATE_V  0x00000001U
#define PMU_PMU_ICG_MODEM_SWITCH_STATE_S  15
/** PMU_PMU_ICG_MODEM_CODE_STATE : RO; bitpos: [17:16]; default: 0;
 *  need_des
 */
#define PMU_PMU_ICG_MODEM_CODE_STATE    0x00000003U
#define PMU_PMU_ICG_MODEM_CODE_STATE_M  (PMU_PMU_ICG_MODEM_CODE_STATE_V << PMU_PMU_ICG_MODEM_CODE_STATE_S)
#define PMU_PMU_ICG_MODEM_CODE_STATE_V  0x00000003U
#define PMU_PMU_ICG_MODEM_CODE_STATE_S  16
/** PMU_PMU_ICG_SLP_SEL_STATE : RO; bitpos: [18]; default: 0;
 *  need_des
 */
#define PMU_PMU_ICG_SLP_SEL_STATE    (BIT(18))
#define PMU_PMU_ICG_SLP_SEL_STATE_M  (PMU_PMU_ICG_SLP_SEL_STATE_V << PMU_PMU_ICG_SLP_SEL_STATE_S)
#define PMU_PMU_ICG_SLP_SEL_STATE_V  0x00000001U
#define PMU_PMU_ICG_SLP_SEL_STATE_S  18
/** PMU_PMU_ICG_GLOBAL_XTAL_STATE : RO; bitpos: [19]; default: 0;
 *  need_des
 */
#define PMU_PMU_ICG_GLOBAL_XTAL_STATE    (BIT(19))
#define PMU_PMU_ICG_GLOBAL_XTAL_STATE_M  (PMU_PMU_ICG_GLOBAL_XTAL_STATE_V << PMU_PMU_ICG_GLOBAL_XTAL_STATE_S)
#define PMU_PMU_ICG_GLOBAL_XTAL_STATE_V  0x00000001U
#define PMU_PMU_ICG_GLOBAL_XTAL_STATE_S  19
/** PMU_PMU_ICG_GLOBAL_PLL_STATE : RO; bitpos: [23:20]; default: 0;
 *  need_des
 */
#define PMU_PMU_ICG_GLOBAL_PLL_STATE    0x0000000FU
#define PMU_PMU_ICG_GLOBAL_PLL_STATE_M  (PMU_PMU_ICG_GLOBAL_PLL_STATE_V << PMU_PMU_ICG_GLOBAL_PLL_STATE_S)
#define PMU_PMU_ICG_GLOBAL_PLL_STATE_V  0x0000000FU
#define PMU_PMU_ICG_GLOBAL_PLL_STATE_S  20
/** PMU_PMU_ANA_I2C_ISO_EN_STATE : RO; bitpos: [24]; default: 0;
 *  need_des
 */
#define PMU_PMU_ANA_I2C_ISO_EN_STATE    (BIT(24))
#define PMU_PMU_ANA_I2C_ISO_EN_STATE_M  (PMU_PMU_ANA_I2C_ISO_EN_STATE_V << PMU_PMU_ANA_I2C_ISO_EN_STATE_S)
#define PMU_PMU_ANA_I2C_ISO_EN_STATE_V  0x00000001U
#define PMU_PMU_ANA_I2C_ISO_EN_STATE_S  24
/** PMU_PMU_ANA_I2C_RETENTION_STATE : RO; bitpos: [25]; default: 0;
 *  need_des
 */
#define PMU_PMU_ANA_I2C_RETENTION_STATE    (BIT(25))
#define PMU_PMU_ANA_I2C_RETENTION_STATE_M  (PMU_PMU_ANA_I2C_RETENTION_STATE_V << PMU_PMU_ANA_I2C_RETENTION_STATE_S)
#define PMU_PMU_ANA_I2C_RETENTION_STATE_V  0x00000001U
#define PMU_PMU_ANA_I2C_RETENTION_STATE_S  25
/** PMU_PMU_ANA_XPD_PLL_STATE : RO; bitpos: [30:27]; default: 0;
 *  need_des
 */
#define PMU_PMU_ANA_XPD_PLL_STATE    0x0000000FU
#define PMU_PMU_ANA_XPD_PLL_STATE_M  (PMU_PMU_ANA_XPD_PLL_STATE_V << PMU_PMU_ANA_XPD_PLL_STATE_S)
#define PMU_PMU_ANA_XPD_PLL_STATE_V  0x0000000FU
#define PMU_PMU_ANA_XPD_PLL_STATE_S  27
/** PMU_PMU_ANA_XPD_XTAL_STATE : RO; bitpos: [31]; default: 0;
 *  need_des
 */
#define PMU_PMU_ANA_XPD_XTAL_STATE    (BIT(31))
#define PMU_PMU_ANA_XPD_XTAL_STATE_M  (PMU_PMU_ANA_XPD_XTAL_STATE_V << PMU_PMU_ANA_XPD_XTAL_STATE_S)
#define PMU_PMU_ANA_XPD_XTAL_STATE_V  0x00000001U
#define PMU_PMU_ANA_XPD_XTAL_STATE_S  31

/** PMU_CLK_STATE1_REG register
 *  need_des
 */
#define PMU_CLK_STATE1_REG (DR_REG_PMU_BASE + 0x1b0)
/** PMU_PMU_ICG_FUNC_EN_STATE : RO; bitpos: [31:0]; default: 4294967295;
 *  need_des
 */
#define PMU_PMU_ICG_FUNC_EN_STATE    0xFFFFFFFFU
#define PMU_PMU_ICG_FUNC_EN_STATE_M  (PMU_PMU_ICG_FUNC_EN_STATE_V << PMU_PMU_ICG_FUNC_EN_STATE_S)
#define PMU_PMU_ICG_FUNC_EN_STATE_V  0xFFFFFFFFU
#define PMU_PMU_ICG_FUNC_EN_STATE_S  0

/** PMU_CLK_STATE2_REG register
 *  need_des
 */
#define PMU_CLK_STATE2_REG (DR_REG_PMU_BASE + 0x1b4)
/** PMU_PMU_ICG_APB_EN_STATE : RO; bitpos: [31:0]; default: 4294967295;
 *  need_des
 */
#define PMU_PMU_ICG_APB_EN_STATE    0xFFFFFFFFU
#define PMU_PMU_ICG_APB_EN_STATE_M  (PMU_PMU_ICG_APB_EN_STATE_V << PMU_PMU_ICG_APB_EN_STATE_S)
#define PMU_PMU_ICG_APB_EN_STATE_V  0xFFFFFFFFU
#define PMU_PMU_ICG_APB_EN_STATE_S  0

/** PMU_EXT_LDO_P0_0P1A_REG register
 *  need_des
 */
#define PMU_EXT_LDO_P0_0P1A_REG (DR_REG_PMU_BASE + 0x1b8)
/** PMU_0P1A_FORCE_TIEH_SEL_0 : R/W; bitpos: [7]; default: 0;
 *  need_des
 */
#define PMU_0P1A_FORCE_TIEH_SEL_0    (BIT(7))
#define PMU_0P1A_FORCE_TIEH_SEL_0_M  (PMU_0P1A_FORCE_TIEH_SEL_0_V << PMU_0P1A_FORCE_TIEH_SEL_0_S)
#define PMU_0P1A_FORCE_TIEH_SEL_0_V  0x00000001U
#define PMU_0P1A_FORCE_TIEH_SEL_0_S  7
/** PMU_0P1A_XPD_0 : R/W; bitpos: [8]; default: 1;
 *  need_des
 */
#define PMU_0P1A_XPD_0    (BIT(8))
#define PMU_0P1A_XPD_0_M  (PMU_0P1A_XPD_0_V << PMU_0P1A_XPD_0_S)
#define PMU_0P1A_XPD_0_V  0x00000001U
#define PMU_0P1A_XPD_0_S  8
/** PMU_0P1A_TIEH_SEL_0 : R/W; bitpos: [11:9]; default: 0;
 *  need_des
 */
#define PMU_0P1A_TIEH_SEL_0    0x00000007U
#define PMU_0P1A_TIEH_SEL_0_M  (PMU_0P1A_TIEH_SEL_0_V << PMU_0P1A_TIEH_SEL_0_S)
#define PMU_0P1A_TIEH_SEL_0_V  0x00000007U
#define PMU_0P1A_TIEH_SEL_0_S  9
/** PMU_0P1A_TIEH_POS_EN_0 : R/W; bitpos: [12]; default: 0;
 *  need_des
 */
#define PMU_0P1A_TIEH_POS_EN_0    (BIT(12))
#define PMU_0P1A_TIEH_POS_EN_0_M  (PMU_0P1A_TIEH_POS_EN_0_V << PMU_0P1A_TIEH_POS_EN_0_S)
#define PMU_0P1A_TIEH_POS_EN_0_V  0x00000001U
#define PMU_0P1A_TIEH_POS_EN_0_S  12
/** PMU_0P1A_TIEH_NEG_EN_0 : R/W; bitpos: [13]; default: 0;
 *  need_des
 */
#define PMU_0P1A_TIEH_NEG_EN_0    (BIT(13))
#define PMU_0P1A_TIEH_NEG_EN_0_M  (PMU_0P1A_TIEH_NEG_EN_0_V << PMU_0P1A_TIEH_NEG_EN_0_S)
#define PMU_0P1A_TIEH_NEG_EN_0_V  0x00000001U
#define PMU_0P1A_TIEH_NEG_EN_0_S  13
/** PMU_0P1A_TIEH_0 : R/W; bitpos: [14]; default: 0;
 *  need_des
 */
#define PMU_0P1A_TIEH_0    (BIT(14))
#define PMU_0P1A_TIEH_0_M  (PMU_0P1A_TIEH_0_V << PMU_0P1A_TIEH_0_S)
#define PMU_0P1A_TIEH_0_V  0x00000001U
#define PMU_0P1A_TIEH_0_S  14
/** PMU_0P1A_TARGET1_0 : R/W; bitpos: [22:15]; default: 64;
 *  need_des
 */
#define PMU_0P1A_TARGET1_0    0x000000FFU
#define PMU_0P1A_TARGET1_0_M  (PMU_0P1A_TARGET1_0_V << PMU_0P1A_TARGET1_0_S)
#define PMU_0P1A_TARGET1_0_V  0x000000FFU
#define PMU_0P1A_TARGET1_0_S  15
/** PMU_0P1A_TARGET0_0 : R/W; bitpos: [30:23]; default: 128;
 *  need_des
 */
#define PMU_0P1A_TARGET0_0    0x000000FFU
#define PMU_0P1A_TARGET0_0_M  (PMU_0P1A_TARGET0_0_V << PMU_0P1A_TARGET0_0_S)
#define PMU_0P1A_TARGET0_0_V  0x000000FFU
#define PMU_0P1A_TARGET0_0_S  23
/** PMU_0P1A_LDO_CNT_PRESCALER_SEL_0 : R/W; bitpos: [31]; default: 0;
 *  need_des
 */
#define PMU_0P1A_LDO_CNT_PRESCALER_SEL_0    (BIT(31))
#define PMU_0P1A_LDO_CNT_PRESCALER_SEL_0_M  (PMU_0P1A_LDO_CNT_PRESCALER_SEL_0_V << PMU_0P1A_LDO_CNT_PRESCALER_SEL_0_S)
#define PMU_0P1A_LDO_CNT_PRESCALER_SEL_0_V  0x00000001U
#define PMU_0P1A_LDO_CNT_PRESCALER_SEL_0_S  31

/** PMU_EXT_LDO_P0_0P1A_ANA_REG register
 *  need_des
 */
#define PMU_EXT_LDO_P0_0P1A_ANA_REG (DR_REG_PMU_BASE + 0x1bc)
/** PMU_ANA_0P1A_MUL_0 : R/W; bitpos: [25:23]; default: 2;
 *  need_des
 */
#define PMU_ANA_0P1A_MUL_0    0x00000007U
#define PMU_ANA_0P1A_MUL_0_M  (PMU_ANA_0P1A_MUL_0_V << PMU_ANA_0P1A_MUL_0_S)
#define PMU_ANA_0P1A_MUL_0_V  0x00000007U
#define PMU_ANA_0P1A_MUL_0_S  23
/** PMU_ANA_0P1A_EN_VDET_0 : R/W; bitpos: [26]; default: 0;
 *  need_des
 */
#define PMU_ANA_0P1A_EN_VDET_0    (BIT(26))
#define PMU_ANA_0P1A_EN_VDET_0_M  (PMU_ANA_0P1A_EN_VDET_0_V << PMU_ANA_0P1A_EN_VDET_0_S)
#define PMU_ANA_0P1A_EN_VDET_0_V  0x00000001U
#define PMU_ANA_0P1A_EN_VDET_0_S  26
/** PMU_ANA_0P1A_EN_CUR_LIM_0 : R/W; bitpos: [27]; default: 0;
 *  need_des
 */
#define PMU_ANA_0P1A_EN_CUR_LIM_0    (BIT(27))
#define PMU_ANA_0P1A_EN_CUR_LIM_0_M  (PMU_ANA_0P1A_EN_CUR_LIM_0_V << PMU_ANA_0P1A_EN_CUR_LIM_0_S)
#define PMU_ANA_0P1A_EN_CUR_LIM_0_V  0x00000001U
#define PMU_ANA_0P1A_EN_CUR_LIM_0_S  27
/** PMU_ANA_0P1A_DREF_0 : R/W; bitpos: [31:28]; default: 11;
 *  need_des
 */
#define PMU_ANA_0P1A_DREF_0    0x0000000FU
#define PMU_ANA_0P1A_DREF_0_M  (PMU_ANA_0P1A_DREF_0_V << PMU_ANA_0P1A_DREF_0_S)
#define PMU_ANA_0P1A_DREF_0_V  0x0000000FU
#define PMU_ANA_0P1A_DREF_0_S  28

/** PMU_EXT_LDO_P0_0P2A_REG register
 *  need_des
 */
#define PMU_EXT_LDO_P0_0P2A_REG (DR_REG_PMU_BASE + 0x1c0)
/** PMU_0P2A_FORCE_TIEH_SEL_0 : R/W; bitpos: [7]; default: 0;
 *  need_des
 */
#define PMU_0P2A_FORCE_TIEH_SEL_0    (BIT(7))
#define PMU_0P2A_FORCE_TIEH_SEL_0_M  (PMU_0P2A_FORCE_TIEH_SEL_0_V << PMU_0P2A_FORCE_TIEH_SEL_0_S)
#define PMU_0P2A_FORCE_TIEH_SEL_0_V  0x00000001U
#define PMU_0P2A_FORCE_TIEH_SEL_0_S  7
/** PMU_0P2A_XPD_0 : R/W; bitpos: [8]; default: 0;
 *  need_des
 */
#define PMU_0P2A_XPD_0    (BIT(8))
#define PMU_0P2A_XPD_0_M  (PMU_0P2A_XPD_0_V << PMU_0P2A_XPD_0_S)
#define PMU_0P2A_XPD_0_V  0x00000001U
#define PMU_0P2A_XPD_0_S  8
/** PMU_0P2A_TIEH_SEL_0 : R/W; bitpos: [11:9]; default: 0;
 *  need_des
 */
#define PMU_0P2A_TIEH_SEL_0    0x00000007U
#define PMU_0P2A_TIEH_SEL_0_M  (PMU_0P2A_TIEH_SEL_0_V << PMU_0P2A_TIEH_SEL_0_S)
#define PMU_0P2A_TIEH_SEL_0_V  0x00000007U
#define PMU_0P2A_TIEH_SEL_0_S  9
/** PMU_0P2A_TIEH_POS_EN_0 : R/W; bitpos: [12]; default: 0;
 *  need_des
 */
#define PMU_0P2A_TIEH_POS_EN_0    (BIT(12))
#define PMU_0P2A_TIEH_POS_EN_0_M  (PMU_0P2A_TIEH_POS_EN_0_V << PMU_0P2A_TIEH_POS_EN_0_S)
#define PMU_0P2A_TIEH_POS_EN_0_V  0x00000001U
#define PMU_0P2A_TIEH_POS_EN_0_S  12
/** PMU_0P2A_TIEH_NEG_EN_0 : R/W; bitpos: [13]; default: 0;
 *  need_des
 */
#define PMU_0P2A_TIEH_NEG_EN_0    (BIT(13))
#define PMU_0P2A_TIEH_NEG_EN_0_M  (PMU_0P2A_TIEH_NEG_EN_0_V << PMU_0P2A_TIEH_NEG_EN_0_S)
#define PMU_0P2A_TIEH_NEG_EN_0_V  0x00000001U
#define PMU_0P2A_TIEH_NEG_EN_0_S  13
/** PMU_0P2A_TIEH_0 : R/W; bitpos: [14]; default: 0;
 *  need_des
 */
#define PMU_0P2A_TIEH_0    (BIT(14))
#define PMU_0P2A_TIEH_0_M  (PMU_0P2A_TIEH_0_V << PMU_0P2A_TIEH_0_S)
#define PMU_0P2A_TIEH_0_V  0x00000001U
#define PMU_0P2A_TIEH_0_S  14
/** PMU_0P2A_TARGET1_0 : R/W; bitpos: [22:15]; default: 64;
 *  need_des
 */
#define PMU_0P2A_TARGET1_0    0x000000FFU
#define PMU_0P2A_TARGET1_0_M  (PMU_0P2A_TARGET1_0_V << PMU_0P2A_TARGET1_0_S)
#define PMU_0P2A_TARGET1_0_V  0x000000FFU
#define PMU_0P2A_TARGET1_0_S  15
/** PMU_0P2A_TARGET0_0 : R/W; bitpos: [30:23]; default: 128;
 *  need_des
 */
#define PMU_0P2A_TARGET0_0    0x000000FFU
#define PMU_0P2A_TARGET0_0_M  (PMU_0P2A_TARGET0_0_V << PMU_0P2A_TARGET0_0_S)
#define PMU_0P2A_TARGET0_0_V  0x000000FFU
#define PMU_0P2A_TARGET0_0_S  23
/** PMU_0P2A_LDO_CNT_PRESCALER_SEL_0 : R/W; bitpos: [31]; default: 0;
 *  need_des
 */
#define PMU_0P2A_LDO_CNT_PRESCALER_SEL_0    (BIT(31))
#define PMU_0P2A_LDO_CNT_PRESCALER_SEL_0_M  (PMU_0P2A_LDO_CNT_PRESCALER_SEL_0_V << PMU_0P2A_LDO_CNT_PRESCALER_SEL_0_S)
#define PMU_0P2A_LDO_CNT_PRESCALER_SEL_0_V  0x00000001U
#define PMU_0P2A_LDO_CNT_PRESCALER_SEL_0_S  31

/** PMU_EXT_LDO_P0_0P2A_ANA_REG register
 *  need_des
 */
#define PMU_EXT_LDO_P0_0P2A_ANA_REG (DR_REG_PMU_BASE + 0x1c4)
/** PMU_ANA_0P2A_MUL_0 : R/W; bitpos: [25:23]; default: 0;
 *  need_des
 */
#define PMU_ANA_0P2A_MUL_0    0x00000007U
#define PMU_ANA_0P2A_MUL_0_M  (PMU_ANA_0P2A_MUL_0_V << PMU_ANA_0P2A_MUL_0_S)
#define PMU_ANA_0P2A_MUL_0_V  0x00000007U
#define PMU_ANA_0P2A_MUL_0_S  23
/** PMU_ANA_0P2A_EN_VDET_0 : R/W; bitpos: [26]; default: 0;
 *  need_des
 */
#define PMU_ANA_0P2A_EN_VDET_0    (BIT(26))
#define PMU_ANA_0P2A_EN_VDET_0_M  (PMU_ANA_0P2A_EN_VDET_0_V << PMU_ANA_0P2A_EN_VDET_0_S)
#define PMU_ANA_0P2A_EN_VDET_0_V  0x00000001U
#define PMU_ANA_0P2A_EN_VDET_0_S  26
/** PMU_ANA_0P2A_EN_CUR_LIM_0 : R/W; bitpos: [27]; default: 0;
 *  need_des
 */
#define PMU_ANA_0P2A_EN_CUR_LIM_0    (BIT(27))
#define PMU_ANA_0P2A_EN_CUR_LIM_0_M  (PMU_ANA_0P2A_EN_CUR_LIM_0_V << PMU_ANA_0P2A_EN_CUR_LIM_0_S)
#define PMU_ANA_0P2A_EN_CUR_LIM_0_V  0x00000001U
#define PMU_ANA_0P2A_EN_CUR_LIM_0_S  27
/** PMU_ANA_0P2A_DREF_0 : R/W; bitpos: [31:28]; default: 10;
 *  need_des
 */
#define PMU_ANA_0P2A_DREF_0    0x0000000FU
#define PMU_ANA_0P2A_DREF_0_M  (PMU_ANA_0P2A_DREF_0_V << PMU_ANA_0P2A_DREF_0_S)
#define PMU_ANA_0P2A_DREF_0_V  0x0000000FU
#define PMU_ANA_0P2A_DREF_0_S  28

/** PMU_EXT_LDO_P0_0P3A_REG register
 *  need_des
 */
#define PMU_EXT_LDO_P0_0P3A_REG (DR_REG_PMU_BASE + 0x1c8)
/** PMU_0P3A_FORCE_TIEH_SEL_0 : R/W; bitpos: [7]; default: 0;
 *  need_des
 */
#define PMU_0P3A_FORCE_TIEH_SEL_0    (BIT(7))
#define PMU_0P3A_FORCE_TIEH_SEL_0_M  (PMU_0P3A_FORCE_TIEH_SEL_0_V << PMU_0P3A_FORCE_TIEH_SEL_0_S)
#define PMU_0P3A_FORCE_TIEH_SEL_0_V  0x00000001U
#define PMU_0P3A_FORCE_TIEH_SEL_0_S  7
/** PMU_0P3A_XPD_0 : R/W; bitpos: [8]; default: 0;
 *  need_des
 */
#define PMU_0P3A_XPD_0    (BIT(8))
#define PMU_0P3A_XPD_0_M  (PMU_0P3A_XPD_0_V << PMU_0P3A_XPD_0_S)
#define PMU_0P3A_XPD_0_V  0x00000001U
#define PMU_0P3A_XPD_0_S  8
/** PMU_0P3A_TIEH_SEL_0 : R/W; bitpos: [11:9]; default: 0;
 *  need_des
 */
#define PMU_0P3A_TIEH_SEL_0    0x00000007U
#define PMU_0P3A_TIEH_SEL_0_M  (PMU_0P3A_TIEH_SEL_0_V << PMU_0P3A_TIEH_SEL_0_S)
#define PMU_0P3A_TIEH_SEL_0_V  0x00000007U
#define PMU_0P3A_TIEH_SEL_0_S  9
/** PMU_0P3A_TIEH_POS_EN_0 : R/W; bitpos: [12]; default: 0;
 *  need_des
 */
#define PMU_0P3A_TIEH_POS_EN_0    (BIT(12))
#define PMU_0P3A_TIEH_POS_EN_0_M  (PMU_0P3A_TIEH_POS_EN_0_V << PMU_0P3A_TIEH_POS_EN_0_S)
#define PMU_0P3A_TIEH_POS_EN_0_V  0x00000001U
#define PMU_0P3A_TIEH_POS_EN_0_S  12
/** PMU_0P3A_TIEH_NEG_EN_0 : R/W; bitpos: [13]; default: 0;
 *  need_des
 */
#define PMU_0P3A_TIEH_NEG_EN_0    (BIT(13))
#define PMU_0P3A_TIEH_NEG_EN_0_M  (PMU_0P3A_TIEH_NEG_EN_0_V << PMU_0P3A_TIEH_NEG_EN_0_S)
#define PMU_0P3A_TIEH_NEG_EN_0_V  0x00000001U
#define PMU_0P3A_TIEH_NEG_EN_0_S  13
/** PMU_0P3A_TIEH_0 : R/W; bitpos: [14]; default: 0;
 *  need_des
 */
#define PMU_0P3A_TIEH_0    (BIT(14))
#define PMU_0P3A_TIEH_0_M  (PMU_0P3A_TIEH_0_V << PMU_0P3A_TIEH_0_S)
#define PMU_0P3A_TIEH_0_V  0x00000001U
#define PMU_0P3A_TIEH_0_S  14
/** PMU_0P3A_TARGET1_0 : R/W; bitpos: [22:15]; default: 64;
 *  need_des
 */
#define PMU_0P3A_TARGET1_0    0x000000FFU
#define PMU_0P3A_TARGET1_0_M  (PMU_0P3A_TARGET1_0_V << PMU_0P3A_TARGET1_0_S)
#define PMU_0P3A_TARGET1_0_V  0x000000FFU
#define PMU_0P3A_TARGET1_0_S  15
/** PMU_0P3A_TARGET0_0 : R/W; bitpos: [30:23]; default: 128;
 *  need_des
 */
#define PMU_0P3A_TARGET0_0    0x000000FFU
#define PMU_0P3A_TARGET0_0_M  (PMU_0P3A_TARGET0_0_V << PMU_0P3A_TARGET0_0_S)
#define PMU_0P3A_TARGET0_0_V  0x000000FFU
#define PMU_0P3A_TARGET0_0_S  23
/** PMU_0P3A_LDO_CNT_PRESCALER_SEL_0 : R/W; bitpos: [31]; default: 0;
 *  need_des
 */
#define PMU_0P3A_LDO_CNT_PRESCALER_SEL_0    (BIT(31))
#define PMU_0P3A_LDO_CNT_PRESCALER_SEL_0_M  (PMU_0P3A_LDO_CNT_PRESCALER_SEL_0_V << PMU_0P3A_LDO_CNT_PRESCALER_SEL_0_S)
#define PMU_0P3A_LDO_CNT_PRESCALER_SEL_0_V  0x00000001U
#define PMU_0P3A_LDO_CNT_PRESCALER_SEL_0_S  31

/** PMU_EXT_LDO_P0_0P3A_ANA_REG register
 *  need_des
 */
#define PMU_EXT_LDO_P0_0P3A_ANA_REG (DR_REG_PMU_BASE + 0x1cc)
/** PMU_ANA_0P3A_MUL_0 : R/W; bitpos: [25:23]; default: 0;
 *  need_des
 */
#define PMU_ANA_0P3A_MUL_0    0x00000007U
#define PMU_ANA_0P3A_MUL_0_M  (PMU_ANA_0P3A_MUL_0_V << PMU_ANA_0P3A_MUL_0_S)
#define PMU_ANA_0P3A_MUL_0_V  0x00000007U
#define PMU_ANA_0P3A_MUL_0_S  23
/** PMU_ANA_0P3A_EN_VDET_0 : R/W; bitpos: [26]; default: 0;
 *  need_des
 */
#define PMU_ANA_0P3A_EN_VDET_0    (BIT(26))
#define PMU_ANA_0P3A_EN_VDET_0_M  (PMU_ANA_0P3A_EN_VDET_0_V << PMU_ANA_0P3A_EN_VDET_0_S)
#define PMU_ANA_0P3A_EN_VDET_0_V  0x00000001U
#define PMU_ANA_0P3A_EN_VDET_0_S  26
/** PMU_ANA_0P3A_EN_CUR_LIM_0 : R/W; bitpos: [27]; default: 0;
 *  need_des
 */
#define PMU_ANA_0P3A_EN_CUR_LIM_0    (BIT(27))
#define PMU_ANA_0P3A_EN_CUR_LIM_0_M  (PMU_ANA_0P3A_EN_CUR_LIM_0_V << PMU_ANA_0P3A_EN_CUR_LIM_0_S)
#define PMU_ANA_0P3A_EN_CUR_LIM_0_V  0x00000001U
#define PMU_ANA_0P3A_EN_CUR_LIM_0_S  27
/** PMU_ANA_0P3A_DREF_0 : R/W; bitpos: [31:28]; default: 10;
 *  need_des
 */
#define PMU_ANA_0P3A_DREF_0    0x0000000FU
#define PMU_ANA_0P3A_DREF_0_M  (PMU_ANA_0P3A_DREF_0_V << PMU_ANA_0P3A_DREF_0_S)
#define PMU_ANA_0P3A_DREF_0_V  0x0000000FU
#define PMU_ANA_0P3A_DREF_0_S  28

/** PMU_EXT_LDO_P1_0P1A_REG register
 *  need_des
 */
#define PMU_EXT_LDO_P1_0P1A_REG (DR_REG_PMU_BASE + 0x1d0)
/** PMU_0P1A_FORCE_TIEH_SEL_1 : R/W; bitpos: [7]; default: 0;
 *  need_des
 */
#define PMU_0P1A_FORCE_TIEH_SEL_1    (BIT(7))
#define PMU_0P1A_FORCE_TIEH_SEL_1_M  (PMU_0P1A_FORCE_TIEH_SEL_1_V << PMU_0P1A_FORCE_TIEH_SEL_1_S)
#define PMU_0P1A_FORCE_TIEH_SEL_1_V  0x00000001U
#define PMU_0P1A_FORCE_TIEH_SEL_1_S  7
/** PMU_0P1A_XPD_1 : R/W; bitpos: [8]; default: 0;
 *  need_des
 */
#define PMU_0P1A_XPD_1    (BIT(8))
#define PMU_0P1A_XPD_1_M  (PMU_0P1A_XPD_1_V << PMU_0P1A_XPD_1_S)
#define PMU_0P1A_XPD_1_V  0x00000001U
#define PMU_0P1A_XPD_1_S  8
/** PMU_0P1A_TIEH_SEL_1 : R/W; bitpos: [11:9]; default: 0;
 *  need_des
 */
#define PMU_0P1A_TIEH_SEL_1    0x00000007U
#define PMU_0P1A_TIEH_SEL_1_M  (PMU_0P1A_TIEH_SEL_1_V << PMU_0P1A_TIEH_SEL_1_S)
#define PMU_0P1A_TIEH_SEL_1_V  0x00000007U
#define PMU_0P1A_TIEH_SEL_1_S  9
/** PMU_0P1A_TIEH_POS_EN_1 : R/W; bitpos: [12]; default: 0;
 *  need_des
 */
#define PMU_0P1A_TIEH_POS_EN_1    (BIT(12))
#define PMU_0P1A_TIEH_POS_EN_1_M  (PMU_0P1A_TIEH_POS_EN_1_V << PMU_0P1A_TIEH_POS_EN_1_S)
#define PMU_0P1A_TIEH_POS_EN_1_V  0x00000001U
#define PMU_0P1A_TIEH_POS_EN_1_S  12
/** PMU_0P1A_TIEH_NEG_EN_1 : R/W; bitpos: [13]; default: 0;
 *  need_des
 */
#define PMU_0P1A_TIEH_NEG_EN_1    (BIT(13))
#define PMU_0P1A_TIEH_NEG_EN_1_M  (PMU_0P1A_TIEH_NEG_EN_1_V << PMU_0P1A_TIEH_NEG_EN_1_S)
#define PMU_0P1A_TIEH_NEG_EN_1_V  0x00000001U
#define PMU_0P1A_TIEH_NEG_EN_1_S  13
/** PMU_0P1A_TIEH_1 : R/W; bitpos: [14]; default: 0;
 *  need_des
 */
#define PMU_0P1A_TIEH_1    (BIT(14))
#define PMU_0P1A_TIEH_1_M  (PMU_0P1A_TIEH_1_V << PMU_0P1A_TIEH_1_S)
#define PMU_0P1A_TIEH_1_V  0x00000001U
#define PMU_0P1A_TIEH_1_S  14
/** PMU_0P1A_TARGET1_1 : R/W; bitpos: [22:15]; default: 64;
 *  need_des
 */
#define PMU_0P1A_TARGET1_1    0x000000FFU
#define PMU_0P1A_TARGET1_1_M  (PMU_0P1A_TARGET1_1_V << PMU_0P1A_TARGET1_1_S)
#define PMU_0P1A_TARGET1_1_V  0x000000FFU
#define PMU_0P1A_TARGET1_1_S  15
/** PMU_0P1A_TARGET0_1 : R/W; bitpos: [30:23]; default: 128;
 *  need_des
 */
#define PMU_0P1A_TARGET0_1    0x000000FFU
#define PMU_0P1A_TARGET0_1_M  (PMU_0P1A_TARGET0_1_V << PMU_0P1A_TARGET0_1_S)
#define PMU_0P1A_TARGET0_1_V  0x000000FFU
#define PMU_0P1A_TARGET0_1_S  23
/** PMU_0P1A_LDO_CNT_PRESCALER_SEL_1 : R/W; bitpos: [31]; default: 0;
 *  need_des
 */
#define PMU_0P1A_LDO_CNT_PRESCALER_SEL_1    (BIT(31))
#define PMU_0P1A_LDO_CNT_PRESCALER_SEL_1_M  (PMU_0P1A_LDO_CNT_PRESCALER_SEL_1_V << PMU_0P1A_LDO_CNT_PRESCALER_SEL_1_S)
#define PMU_0P1A_LDO_CNT_PRESCALER_SEL_1_V  0x00000001U
#define PMU_0P1A_LDO_CNT_PRESCALER_SEL_1_S  31

/** PMU_EXT_LDO_P1_0P1A_ANA_REG register
 *  need_des
 */
#define PMU_EXT_LDO_P1_0P1A_ANA_REG (DR_REG_PMU_BASE + 0x1d4)
/** PMU_ANA_0P1A_MUL_1 : R/W; bitpos: [25:23]; default: 0;
 *  need_des
 */
#define PMU_ANA_0P1A_MUL_1    0x00000007U
#define PMU_ANA_0P1A_MUL_1_M  (PMU_ANA_0P1A_MUL_1_V << PMU_ANA_0P1A_MUL_1_S)
#define PMU_ANA_0P1A_MUL_1_V  0x00000007U
#define PMU_ANA_0P1A_MUL_1_S  23
/** PMU_ANA_0P1A_EN_VDET_1 : R/W; bitpos: [26]; default: 0;
 *  need_des
 */
#define PMU_ANA_0P1A_EN_VDET_1    (BIT(26))
#define PMU_ANA_0P1A_EN_VDET_1_M  (PMU_ANA_0P1A_EN_VDET_1_V << PMU_ANA_0P1A_EN_VDET_1_S)
#define PMU_ANA_0P1A_EN_VDET_1_V  0x00000001U
#define PMU_ANA_0P1A_EN_VDET_1_S  26
/** PMU_ANA_0P1A_EN_CUR_LIM_1 : R/W; bitpos: [27]; default: 0;
 *  need_des
 */
#define PMU_ANA_0P1A_EN_CUR_LIM_1    (BIT(27))
#define PMU_ANA_0P1A_EN_CUR_LIM_1_M  (PMU_ANA_0P1A_EN_CUR_LIM_1_V << PMU_ANA_0P1A_EN_CUR_LIM_1_S)
#define PMU_ANA_0P1A_EN_CUR_LIM_1_V  0x00000001U
#define PMU_ANA_0P1A_EN_CUR_LIM_1_S  27
/** PMU_ANA_0P1A_DREF_1 : R/W; bitpos: [31:28]; default: 10;
 *  need_des
 */
#define PMU_ANA_0P1A_DREF_1    0x0000000FU
#define PMU_ANA_0P1A_DREF_1_M  (PMU_ANA_0P1A_DREF_1_V << PMU_ANA_0P1A_DREF_1_S)
#define PMU_ANA_0P1A_DREF_1_V  0x0000000FU
#define PMU_ANA_0P1A_DREF_1_S  28

/** PMU_EXT_LDO_P1_0P2A_REG register
 *  need_des
 */
#define PMU_EXT_LDO_P1_0P2A_REG (DR_REG_PMU_BASE + 0x1d8)
/** PMU_0P2A_FORCE_TIEH_SEL_1 : R/W; bitpos: [7]; default: 0;
 *  need_des
 */
#define PMU_0P2A_FORCE_TIEH_SEL_1    (BIT(7))
#define PMU_0P2A_FORCE_TIEH_SEL_1_M  (PMU_0P2A_FORCE_TIEH_SEL_1_V << PMU_0P2A_FORCE_TIEH_SEL_1_S)
#define PMU_0P2A_FORCE_TIEH_SEL_1_V  0x00000001U
#define PMU_0P2A_FORCE_TIEH_SEL_1_S  7
/** PMU_0P2A_XPD_1 : R/W; bitpos: [8]; default: 0;
 *  need_des
 */
#define PMU_0P2A_XPD_1    (BIT(8))
#define PMU_0P2A_XPD_1_M  (PMU_0P2A_XPD_1_V << PMU_0P2A_XPD_1_S)
#define PMU_0P2A_XPD_1_V  0x00000001U
#define PMU_0P2A_XPD_1_S  8
/** PMU_0P2A_TIEH_SEL_1 : R/W; bitpos: [11:9]; default: 0;
 *  need_des
 */
#define PMU_0P2A_TIEH_SEL_1    0x00000007U
#define PMU_0P2A_TIEH_SEL_1_M  (PMU_0P2A_TIEH_SEL_1_V << PMU_0P2A_TIEH_SEL_1_S)
#define PMU_0P2A_TIEH_SEL_1_V  0x00000007U
#define PMU_0P2A_TIEH_SEL_1_S  9
/** PMU_0P2A_TIEH_POS_EN_1 : R/W; bitpos: [12]; default: 0;
 *  need_des
 */
#define PMU_0P2A_TIEH_POS_EN_1    (BIT(12))
#define PMU_0P2A_TIEH_POS_EN_1_M  (PMU_0P2A_TIEH_POS_EN_1_V << PMU_0P2A_TIEH_POS_EN_1_S)
#define PMU_0P2A_TIEH_POS_EN_1_V  0x00000001U
#define PMU_0P2A_TIEH_POS_EN_1_S  12
/** PMU_0P2A_TIEH_NEG_EN_1 : R/W; bitpos: [13]; default: 0;
 *  need_des
 */
#define PMU_0P2A_TIEH_NEG_EN_1    (BIT(13))
#define PMU_0P2A_TIEH_NEG_EN_1_M  (PMU_0P2A_TIEH_NEG_EN_1_V << PMU_0P2A_TIEH_NEG_EN_1_S)
#define PMU_0P2A_TIEH_NEG_EN_1_V  0x00000001U
#define PMU_0P2A_TIEH_NEG_EN_1_S  13
/** PMU_0P2A_TIEH_1 : R/W; bitpos: [14]; default: 0;
 *  need_des
 */
#define PMU_0P2A_TIEH_1    (BIT(14))
#define PMU_0P2A_TIEH_1_M  (PMU_0P2A_TIEH_1_V << PMU_0P2A_TIEH_1_S)
#define PMU_0P2A_TIEH_1_V  0x00000001U
#define PMU_0P2A_TIEH_1_S  14
/** PMU_0P2A_TARGET1_1 : R/W; bitpos: [22:15]; default: 64;
 *  need_des
 */
#define PMU_0P2A_TARGET1_1    0x000000FFU
#define PMU_0P2A_TARGET1_1_M  (PMU_0P2A_TARGET1_1_V << PMU_0P2A_TARGET1_1_S)
#define PMU_0P2A_TARGET1_1_V  0x000000FFU
#define PMU_0P2A_TARGET1_1_S  15
/** PMU_0P2A_TARGET0_1 : R/W; bitpos: [30:23]; default: 128;
 *  need_des
 */
#define PMU_0P2A_TARGET0_1    0x000000FFU
#define PMU_0P2A_TARGET0_1_M  (PMU_0P2A_TARGET0_1_V << PMU_0P2A_TARGET0_1_S)
#define PMU_0P2A_TARGET0_1_V  0x000000FFU
#define PMU_0P2A_TARGET0_1_S  23
/** PMU_0P2A_LDO_CNT_PRESCALER_SEL_1 : R/W; bitpos: [31]; default: 0;
 *  need_des
 */
#define PMU_0P2A_LDO_CNT_PRESCALER_SEL_1    (BIT(31))
#define PMU_0P2A_LDO_CNT_PRESCALER_SEL_1_M  (PMU_0P2A_LDO_CNT_PRESCALER_SEL_1_V << PMU_0P2A_LDO_CNT_PRESCALER_SEL_1_S)
#define PMU_0P2A_LDO_CNT_PRESCALER_SEL_1_V  0x00000001U
#define PMU_0P2A_LDO_CNT_PRESCALER_SEL_1_S  31

/** PMU_EXT_LDO_P1_0P2A_ANA_REG register
 *  need_des
 */
#define PMU_EXT_LDO_P1_0P2A_ANA_REG (DR_REG_PMU_BASE + 0x1dc)
/** PMU_ANA_0P2A_MUL_1 : R/W; bitpos: [25:23]; default: 0;
 *  need_des
 */
#define PMU_ANA_0P2A_MUL_1    0x00000007U
#define PMU_ANA_0P2A_MUL_1_M  (PMU_ANA_0P2A_MUL_1_V << PMU_ANA_0P2A_MUL_1_S)
#define PMU_ANA_0P2A_MUL_1_V  0x00000007U
#define PMU_ANA_0P2A_MUL_1_S  23
/** PMU_ANA_0P2A_EN_VDET_1 : R/W; bitpos: [26]; default: 0;
 *  need_des
 */
#define PMU_ANA_0P2A_EN_VDET_1    (BIT(26))
#define PMU_ANA_0P2A_EN_VDET_1_M  (PMU_ANA_0P2A_EN_VDET_1_V << PMU_ANA_0P2A_EN_VDET_1_S)
#define PMU_ANA_0P2A_EN_VDET_1_V  0x00000001U
#define PMU_ANA_0P2A_EN_VDET_1_S  26
/** PMU_ANA_0P2A_EN_CUR_LIM_1 : R/W; bitpos: [27]; default: 0;
 *  need_des
 */
#define PMU_ANA_0P2A_EN_CUR_LIM_1    (BIT(27))
#define PMU_ANA_0P2A_EN_CUR_LIM_1_M  (PMU_ANA_0P2A_EN_CUR_LIM_1_V << PMU_ANA_0P2A_EN_CUR_LIM_1_S)
#define PMU_ANA_0P2A_EN_CUR_LIM_1_V  0x00000001U
#define PMU_ANA_0P2A_EN_CUR_LIM_1_S  27
/** PMU_ANA_0P2A_DREF_1 : R/W; bitpos: [31:28]; default: 10;
 *  need_des
 */
#define PMU_ANA_0P2A_DREF_1    0x0000000FU
#define PMU_ANA_0P2A_DREF_1_M  (PMU_ANA_0P2A_DREF_1_V << PMU_ANA_0P2A_DREF_1_S)
#define PMU_ANA_0P2A_DREF_1_V  0x0000000FU
#define PMU_ANA_0P2A_DREF_1_S  28

/** PMU_EXT_LDO_P1_0P3A_REG register
 *  need_des
 */
#define PMU_EXT_LDO_P1_0P3A_REG (DR_REG_PMU_BASE + 0x1e0)
/** PMU_0P3A_FORCE_TIEH_SEL_1 : R/W; bitpos: [7]; default: 0;
 *  need_des
 */
#define PMU_0P3A_FORCE_TIEH_SEL_1    (BIT(7))
#define PMU_0P3A_FORCE_TIEH_SEL_1_M  (PMU_0P3A_FORCE_TIEH_SEL_1_V << PMU_0P3A_FORCE_TIEH_SEL_1_S)
#define PMU_0P3A_FORCE_TIEH_SEL_1_V  0x00000001U
#define PMU_0P3A_FORCE_TIEH_SEL_1_S  7
/** PMU_0P3A_XPD_1 : R/W; bitpos: [8]; default: 0;
 *  need_des
 */
#define PMU_0P3A_XPD_1    (BIT(8))
#define PMU_0P3A_XPD_1_M  (PMU_0P3A_XPD_1_V << PMU_0P3A_XPD_1_S)
#define PMU_0P3A_XPD_1_V  0x00000001U
#define PMU_0P3A_XPD_1_S  8
/** PMU_0P3A_TIEH_SEL_1 : R/W; bitpos: [11:9]; default: 0;
 *  need_des
 */
#define PMU_0P3A_TIEH_SEL_1    0x00000007U
#define PMU_0P3A_TIEH_SEL_1_M  (PMU_0P3A_TIEH_SEL_1_V << PMU_0P3A_TIEH_SEL_1_S)
#define PMU_0P3A_TIEH_SEL_1_V  0x00000007U
#define PMU_0P3A_TIEH_SEL_1_S  9
/** PMU_0P3A_TIEH_POS_EN_1 : R/W; bitpos: [12]; default: 0;
 *  need_des
 */
#define PMU_0P3A_TIEH_POS_EN_1    (BIT(12))
#define PMU_0P3A_TIEH_POS_EN_1_M  (PMU_0P3A_TIEH_POS_EN_1_V << PMU_0P3A_TIEH_POS_EN_1_S)
#define PMU_0P3A_TIEH_POS_EN_1_V  0x00000001U
#define PMU_0P3A_TIEH_POS_EN_1_S  12
/** PMU_0P3A_TIEH_NEG_EN_1 : R/W; bitpos: [13]; default: 0;
 *  need_des
 */
#define PMU_0P3A_TIEH_NEG_EN_1    (BIT(13))
#define PMU_0P3A_TIEH_NEG_EN_1_M  (PMU_0P3A_TIEH_NEG_EN_1_V << PMU_0P3A_TIEH_NEG_EN_1_S)
#define PMU_0P3A_TIEH_NEG_EN_1_V  0x00000001U
#define PMU_0P3A_TIEH_NEG_EN_1_S  13
/** PMU_0P3A_TIEH_1 : R/W; bitpos: [14]; default: 0;
 *  need_des
 */
#define PMU_0P3A_TIEH_1    (BIT(14))
#define PMU_0P3A_TIEH_1_M  (PMU_0P3A_TIEH_1_V << PMU_0P3A_TIEH_1_S)
#define PMU_0P3A_TIEH_1_V  0x00000001U
#define PMU_0P3A_TIEH_1_S  14
/** PMU_0P3A_TARGET1_1 : R/W; bitpos: [22:15]; default: 64;
 *  need_des
 */
#define PMU_0P3A_TARGET1_1    0x000000FFU
#define PMU_0P3A_TARGET1_1_M  (PMU_0P3A_TARGET1_1_V << PMU_0P3A_TARGET1_1_S)
#define PMU_0P3A_TARGET1_1_V  0x000000FFU
#define PMU_0P3A_TARGET1_1_S  15
/** PMU_0P3A_TARGET0_1 : R/W; bitpos: [30:23]; default: 128;
 *  need_des
 */
#define PMU_0P3A_TARGET0_1    0x000000FFU
#define PMU_0P3A_TARGET0_1_M  (PMU_0P3A_TARGET0_1_V << PMU_0P3A_TARGET0_1_S)
#define PMU_0P3A_TARGET0_1_V  0x000000FFU
#define PMU_0P3A_TARGET0_1_S  23
/** PMU_0P3A_LDO_CNT_PRESCALER_SEL_1 : R/W; bitpos: [31]; default: 0;
 *  need_des
 */
#define PMU_0P3A_LDO_CNT_PRESCALER_SEL_1    (BIT(31))
#define PMU_0P3A_LDO_CNT_PRESCALER_SEL_1_M  (PMU_0P3A_LDO_CNT_PRESCALER_SEL_1_V << PMU_0P3A_LDO_CNT_PRESCALER_SEL_1_S)
#define PMU_0P3A_LDO_CNT_PRESCALER_SEL_1_V  0x00000001U
#define PMU_0P3A_LDO_CNT_PRESCALER_SEL_1_S  31

/** PMU_EXT_LDO_P1_0P3A_ANA_REG register
 *  need_des
 */
#define PMU_EXT_LDO_P1_0P3A_ANA_REG (DR_REG_PMU_BASE + 0x1e4)
/** PMU_ANA_0P3A_MUL_1 : R/W; bitpos: [25:23]; default: 0;
 *  need_des
 */
#define PMU_ANA_0P3A_MUL_1    0x00000007U
#define PMU_ANA_0P3A_MUL_1_M  (PMU_ANA_0P3A_MUL_1_V << PMU_ANA_0P3A_MUL_1_S)
#define PMU_ANA_0P3A_MUL_1_V  0x00000007U
#define PMU_ANA_0P3A_MUL_1_S  23
/** PMU_ANA_0P3A_EN_VDET_1 : R/W; bitpos: [26]; default: 0;
 *  need_des
 */
#define PMU_ANA_0P3A_EN_VDET_1    (BIT(26))
#define PMU_ANA_0P3A_EN_VDET_1_M  (PMU_ANA_0P3A_EN_VDET_1_V << PMU_ANA_0P3A_EN_VDET_1_S)
#define PMU_ANA_0P3A_EN_VDET_1_V  0x00000001U
#define PMU_ANA_0P3A_EN_VDET_1_S  26
/** PMU_ANA_0P3A_EN_CUR_LIM_1 : R/W; bitpos: [27]; default: 0;
 *  need_des
 */
#define PMU_ANA_0P3A_EN_CUR_LIM_1    (BIT(27))
#define PMU_ANA_0P3A_EN_CUR_LIM_1_M  (PMU_ANA_0P3A_EN_CUR_LIM_1_V << PMU_ANA_0P3A_EN_CUR_LIM_1_S)
#define PMU_ANA_0P3A_EN_CUR_LIM_1_V  0x00000001U
#define PMU_ANA_0P3A_EN_CUR_LIM_1_S  27
/** PMU_ANA_0P3A_DREF_1 : R/W; bitpos: [31:28]; default: 10;
 *  need_des
 */
#define PMU_ANA_0P3A_DREF_1    0x0000000FU
#define PMU_ANA_0P3A_DREF_1_M  (PMU_ANA_0P3A_DREF_1_V << PMU_ANA_0P3A_DREF_1_S)
#define PMU_ANA_0P3A_DREF_1_V  0x0000000FU
#define PMU_ANA_0P3A_DREF_1_S  28

/** PMU_EXT_WAKEUP_LV_REG register
 *  need_des
 */
#define PMU_EXT_WAKEUP_LV_REG (DR_REG_PMU_BASE + 0x1e8)
/** PMU_EXT_WAKEUP_LV : R/W; bitpos: [31:0]; default: 0;
 *  need_des
 */
#define PMU_EXT_WAKEUP_LV    0xFFFFFFFFU
#define PMU_EXT_WAKEUP_LV_M  (PMU_EXT_WAKEUP_LV_V << PMU_EXT_WAKEUP_LV_S)
#define PMU_EXT_WAKEUP_LV_V  0xFFFFFFFFU
#define PMU_EXT_WAKEUP_LV_S  0

/** PMU_EXT_WAKEUP_SEL_REG register
 *  need_des
 */
#define PMU_EXT_WAKEUP_SEL_REG (DR_REG_PMU_BASE + 0x1ec)
/** PMU_EXT_WAKEUP_SEL : R/W; bitpos: [31:0]; default: 0;
 *  need_des
 */
#define PMU_EXT_WAKEUP_SEL    0xFFFFFFFFU
#define PMU_EXT_WAKEUP_SEL_M  (PMU_EXT_WAKEUP_SEL_V << PMU_EXT_WAKEUP_SEL_S)
#define PMU_EXT_WAKEUP_SEL_V  0xFFFFFFFFU
#define PMU_EXT_WAKEUP_SEL_S  0

/** PMU_EXT_WAKEUP_ST_REG register
 *  need_des
 */
#define PMU_EXT_WAKEUP_ST_REG (DR_REG_PMU_BASE + 0x1f0)
/** PMU_EXT_WAKEUP_STATUS : RO; bitpos: [31:0]; default: 0;
 *  need_des
 */
#define PMU_EXT_WAKEUP_STATUS    0xFFFFFFFFU
#define PMU_EXT_WAKEUP_STATUS_M  (PMU_EXT_WAKEUP_STATUS_V << PMU_EXT_WAKEUP_STATUS_S)
#define PMU_EXT_WAKEUP_STATUS_V  0xFFFFFFFFU
#define PMU_EXT_WAKEUP_STATUS_S  0

/** PMU_EXT_WAKEUP_CNTL_REG register
 *  need_des
 */
#define PMU_EXT_WAKEUP_CNTL_REG (DR_REG_PMU_BASE + 0x1f4)
/** PMU_EXT_WAKEUP_STATUS_CLR : R/W; bitpos: [30]; default: 0;
 *  need_des
 */
#define PMU_EXT_WAKEUP_STATUS_CLR    (BIT(30))
#define PMU_EXT_WAKEUP_STATUS_CLR_M  (PMU_EXT_WAKEUP_STATUS_CLR_V << PMU_EXT_WAKEUP_STATUS_CLR_S)
#define PMU_EXT_WAKEUP_STATUS_CLR_V  0x00000001U
#define PMU_EXT_WAKEUP_STATUS_CLR_S  30
/** PMU_EXT_WAKEUP_FILTER : R/W; bitpos: [31]; default: 0;
 *  need_des
 */
#define PMU_EXT_WAKEUP_FILTER    (BIT(31))
#define PMU_EXT_WAKEUP_FILTER_M  (PMU_EXT_WAKEUP_FILTER_V << PMU_EXT_WAKEUP_FILTER_S)
#define PMU_EXT_WAKEUP_FILTER_V  0x00000001U
#define PMU_EXT_WAKEUP_FILTER_S  31

/** PMU_SDIO_WAKEUP_CNTL_REG register
 *  need_des
 */
#define PMU_SDIO_WAKEUP_CNTL_REG (DR_REG_PMU_BASE + 0x1f8)
/** PMU_SDIO_ACT_DNUM : R/W; bitpos: [9:0]; default: 1023;
 *  need_des
 */
#define PMU_SDIO_ACT_DNUM    0x000003FFU
#define PMU_SDIO_ACT_DNUM_M  (PMU_SDIO_ACT_DNUM_V << PMU_SDIO_ACT_DNUM_S)
#define PMU_SDIO_ACT_DNUM_V  0x000003FFU
#define PMU_SDIO_ACT_DNUM_S  0

/** PMU_XTAL_SLP_REG register
 *  need_des
 */
#define PMU_XTAL_SLP_REG (DR_REG_PMU_BASE + 0x1fc)
/** PMU_XTAL_SLP_CNT_TARGET : R/W; bitpos: [31:16]; default: 15;
 *  need_des
 */
#define PMU_XTAL_SLP_CNT_TARGET    0x0000FFFFU
#define PMU_XTAL_SLP_CNT_TARGET_M  (PMU_XTAL_SLP_CNT_TARGET_V << PMU_XTAL_SLP_CNT_TARGET_S)
#define PMU_XTAL_SLP_CNT_TARGET_V  0x0000FFFFU
#define PMU_XTAL_SLP_CNT_TARGET_S  16

/** PMU_CPU_SW_STALL_REG register
 *  need_des
 */
#define PMU_CPU_SW_STALL_REG (DR_REG_PMU_BASE + 0x200)
/** PMU_HPCORE1_SW_STALL_CODE : R/W; bitpos: [23:16]; default: 0;
 *  need_des
 */
#define PMU_HPCORE1_SW_STALL_CODE    0x000000FFU
#define PMU_HPCORE1_SW_STALL_CODE_M  (PMU_HPCORE1_SW_STALL_CODE_V << PMU_HPCORE1_SW_STALL_CODE_S)
#define PMU_HPCORE1_SW_STALL_CODE_V  0x000000FFU
#define PMU_HPCORE1_SW_STALL_CODE_S  16
/** PMU_HPCORE0_SW_STALL_CODE : R/W; bitpos: [31:24]; default: 0;
 *  need_des
 */
#define PMU_HPCORE0_SW_STALL_CODE    0x000000FFU
#define PMU_HPCORE0_SW_STALL_CODE_M  (PMU_HPCORE0_SW_STALL_CODE_V << PMU_HPCORE0_SW_STALL_CODE_S)
#define PMU_HPCORE0_SW_STALL_CODE_V  0x000000FFU
#define PMU_HPCORE0_SW_STALL_CODE_S  24

/** PMU_DCM_CTRL_REG register
 *  need_des
 */
#define PMU_DCM_CTRL_REG (DR_REG_PMU_BASE + 0x204)
/** PMU_DCDC_ON_REQ : WT; bitpos: [0]; default: 0;
 *  SW trigger dcdc on
 */
#define PMU_DCDC_ON_REQ    (BIT(0))
#define PMU_DCDC_ON_REQ_M  (PMU_DCDC_ON_REQ_V << PMU_DCDC_ON_REQ_S)
#define PMU_DCDC_ON_REQ_V  0x00000001U
#define PMU_DCDC_ON_REQ_S  0
/** PMU_DCDC_OFF_REQ : WT; bitpos: [1]; default: 0;
 *  SW trigger dcdc off
 */
#define PMU_DCDC_OFF_REQ    (BIT(1))
#define PMU_DCDC_OFF_REQ_M  (PMU_DCDC_OFF_REQ_V << PMU_DCDC_OFF_REQ_S)
#define PMU_DCDC_OFF_REQ_V  0x00000001U
#define PMU_DCDC_OFF_REQ_S  1
/** PMU_DCDC_LIGHTSLP_REQ : WT; bitpos: [2]; default: 0;
 *  SW trigger dcdc enter lightsleep
 */
#define PMU_DCDC_LIGHTSLP_REQ    (BIT(2))
#define PMU_DCDC_LIGHTSLP_REQ_M  (PMU_DCDC_LIGHTSLP_REQ_V << PMU_DCDC_LIGHTSLP_REQ_S)
#define PMU_DCDC_LIGHTSLP_REQ_V  0x00000001U
#define PMU_DCDC_LIGHTSLP_REQ_S  2
/** PMU_DCDC_DEEPSLP_REQ : WT; bitpos: [3]; default: 0;
 *  SW trigger dcdc enter deepsleep
 */
#define PMU_DCDC_DEEPSLP_REQ    (BIT(3))
#define PMU_DCDC_DEEPSLP_REQ_M  (PMU_DCDC_DEEPSLP_REQ_V << PMU_DCDC_DEEPSLP_REQ_S)
#define PMU_DCDC_DEEPSLP_REQ_V  0x00000001U
#define PMU_DCDC_DEEPSLP_REQ_S  3
/** PMU_DCDC_DONE_FORCE : R/W; bitpos: [7]; default: 0;
 *  need_des
 */
#define PMU_DCDC_DONE_FORCE    (BIT(7))
#define PMU_DCDC_DONE_FORCE_M  (PMU_DCDC_DONE_FORCE_V << PMU_DCDC_DONE_FORCE_S)
#define PMU_DCDC_DONE_FORCE_V  0x00000001U
#define PMU_DCDC_DONE_FORCE_S  7
/** PMU_DCDC_ON_FORCE_PU : R/W; bitpos: [8]; default: 0;
 *  need_des
 */
#define PMU_DCDC_ON_FORCE_PU    (BIT(8))
#define PMU_DCDC_ON_FORCE_PU_M  (PMU_DCDC_ON_FORCE_PU_V << PMU_DCDC_ON_FORCE_PU_S)
#define PMU_DCDC_ON_FORCE_PU_V  0x00000001U
#define PMU_DCDC_ON_FORCE_PU_S  8
/** PMU_DCDC_ON_FORCE_PD : R/W; bitpos: [9]; default: 0;
 *  need_des
 */
#define PMU_DCDC_ON_FORCE_PD    (BIT(9))
#define PMU_DCDC_ON_FORCE_PD_M  (PMU_DCDC_ON_FORCE_PD_V << PMU_DCDC_ON_FORCE_PD_S)
#define PMU_DCDC_ON_FORCE_PD_V  0x00000001U
#define PMU_DCDC_ON_FORCE_PD_S  9
/** PMU_DCDC_FB_RES_FORCE_PU : R/W; bitpos: [10]; default: 0;
 *  need_des
 */
#define PMU_DCDC_FB_RES_FORCE_PU    (BIT(10))
#define PMU_DCDC_FB_RES_FORCE_PU_M  (PMU_DCDC_FB_RES_FORCE_PU_V << PMU_DCDC_FB_RES_FORCE_PU_S)
#define PMU_DCDC_FB_RES_FORCE_PU_V  0x00000001U
#define PMU_DCDC_FB_RES_FORCE_PU_S  10
/** PMU_DCDC_FB_RES_FORCE_PD : R/W; bitpos: [11]; default: 0;
 *  need_des
 */
#define PMU_DCDC_FB_RES_FORCE_PD    (BIT(11))
#define PMU_DCDC_FB_RES_FORCE_PD_M  (PMU_DCDC_FB_RES_FORCE_PD_V << PMU_DCDC_FB_RES_FORCE_PD_S)
#define PMU_DCDC_FB_RES_FORCE_PD_V  0x00000001U
#define PMU_DCDC_FB_RES_FORCE_PD_S  11
/** PMU_DCDC_LS_FORCE_PU : R/W; bitpos: [12]; default: 0;
 *  need_des
 */
#define PMU_DCDC_LS_FORCE_PU    (BIT(12))
#define PMU_DCDC_LS_FORCE_PU_M  (PMU_DCDC_LS_FORCE_PU_V << PMU_DCDC_LS_FORCE_PU_S)
#define PMU_DCDC_LS_FORCE_PU_V  0x00000001U
#define PMU_DCDC_LS_FORCE_PU_S  12
/** PMU_DCDC_LS_FORCE_PD : R/W; bitpos: [13]; default: 0;
 *  need_des
 */
#define PMU_DCDC_LS_FORCE_PD    (BIT(13))
#define PMU_DCDC_LS_FORCE_PD_M  (PMU_DCDC_LS_FORCE_PD_V << PMU_DCDC_LS_FORCE_PD_S)
#define PMU_DCDC_LS_FORCE_PD_V  0x00000001U
#define PMU_DCDC_LS_FORCE_PD_S  13
/** PMU_DCDC_DS_FORCE_PU : R/W; bitpos: [14]; default: 0;
 *  need_des
 */
#define PMU_DCDC_DS_FORCE_PU    (BIT(14))
#define PMU_DCDC_DS_FORCE_PU_M  (PMU_DCDC_DS_FORCE_PU_V << PMU_DCDC_DS_FORCE_PU_S)
#define PMU_DCDC_DS_FORCE_PU_V  0x00000001U
#define PMU_DCDC_DS_FORCE_PU_S  14
/** PMU_DCDC_DS_FORCE_PD : R/W; bitpos: [15]; default: 0;
 *  need_des
 */
#define PMU_DCDC_DS_FORCE_PD    (BIT(15))
#define PMU_DCDC_DS_FORCE_PD_M  (PMU_DCDC_DS_FORCE_PD_V << PMU_DCDC_DS_FORCE_PD_S)
#define PMU_DCDC_DS_FORCE_PD_V  0x00000001U
#define PMU_DCDC_DS_FORCE_PD_S  15
/** PMU_DCM_CUR_ST : RO; bitpos: [23:16]; default: 1;
 *  need_des
 */
#define PMU_DCM_CUR_ST    0x000000FFU
#define PMU_DCM_CUR_ST_M  (PMU_DCM_CUR_ST_V << PMU_DCM_CUR_ST_S)
#define PMU_DCM_CUR_ST_V  0x000000FFU
#define PMU_DCM_CUR_ST_S  16
/** PMU_DCDC_EN_AMUX_TEST : R/W; bitpos: [29]; default: 0;
 *  Enable analog mux to pull PAD TEST_DCDC voltage signal
 */
#define PMU_DCDC_EN_AMUX_TEST    (BIT(29))
#define PMU_DCDC_EN_AMUX_TEST_M  (PMU_DCDC_EN_AMUX_TEST_V << PMU_DCDC_EN_AMUX_TEST_S)
#define PMU_DCDC_EN_AMUX_TEST_V  0x00000001U
#define PMU_DCDC_EN_AMUX_TEST_S  29

/** PMU_DCM_WAIT_DELAY_REG register
 *  need_des
 */
#define PMU_DCM_WAIT_DELAY_REG (DR_REG_PMU_BASE + 0x208)
/** PMU_DCDC_PRE_DELAY : R/W; bitpos: [7:0]; default: 5;
 *  DCDC pre-on/post off delay
 */
#define PMU_DCDC_PRE_DELAY    0x000000FFU
#define PMU_DCDC_PRE_DELAY_M  (PMU_DCDC_PRE_DELAY_V << PMU_DCDC_PRE_DELAY_S)
#define PMU_DCDC_PRE_DELAY_V  0x000000FFU
#define PMU_DCDC_PRE_DELAY_S  0
/** PMU_DCDC_RES_OFF_DELAY : R/W; bitpos: [15:8]; default: 2;
 *  DCDC fb res off delay
 */
#define PMU_DCDC_RES_OFF_DELAY    0x000000FFU
#define PMU_DCDC_RES_OFF_DELAY_M  (PMU_DCDC_RES_OFF_DELAY_V << PMU_DCDC_RES_OFF_DELAY_S)
#define PMU_DCDC_RES_OFF_DELAY_V  0x000000FFU
#define PMU_DCDC_RES_OFF_DELAY_S  8
/** PMU_DCDC_STABLE_DELAY : R/W; bitpos: [25:16]; default: 75;
 *  DCDC stable delay
 */
#define PMU_DCDC_STABLE_DELAY    0x000003FFU
#define PMU_DCDC_STABLE_DELAY_M  (PMU_DCDC_STABLE_DELAY_V << PMU_DCDC_STABLE_DELAY_S)
#define PMU_DCDC_STABLE_DELAY_V  0x000003FFU
#define PMU_DCDC_STABLE_DELAY_S  16

/** PMU_VDDBAT_CFG_REG register
 *  need_des
 */
#define PMU_VDDBAT_CFG_REG (DR_REG_PMU_BASE + 0x20c)
/** PMU_ANA_VDDBAT_MODE : RO; bitpos: [1:0]; default: 0;
 *  need_des
 */
#define PMU_ANA_VDDBAT_MODE    0x00000003U
#define PMU_ANA_VDDBAT_MODE_M  (PMU_ANA_VDDBAT_MODE_V << PMU_ANA_VDDBAT_MODE_S)
#define PMU_ANA_VDDBAT_MODE_V  0x00000003U
#define PMU_ANA_VDDBAT_MODE_S  0
/** PMU_VDDBAT_SW_UPDATE : WT; bitpos: [31]; default: 0;
 *  need_des
 */
#define PMU_VDDBAT_SW_UPDATE    (BIT(31))
#define PMU_VDDBAT_SW_UPDATE_M  (PMU_VDDBAT_SW_UPDATE_V << PMU_VDDBAT_SW_UPDATE_S)
#define PMU_VDDBAT_SW_UPDATE_V  0x00000001U
#define PMU_VDDBAT_SW_UPDATE_S  31

/** PMU_TOUCH_PWR_CNTL_REG register
 *  need_des
 */
#define PMU_TOUCH_PWR_CNTL_REG (DR_REG_PMU_BASE + 0x210)
/** PMU_TOUCH_WAIT_CYCLES : R/W; bitpos: [13:5]; default: 10;
 *  need_des
 */
#define PMU_TOUCH_WAIT_CYCLES    0x000001FFU
#define PMU_TOUCH_WAIT_CYCLES_M  (PMU_TOUCH_WAIT_CYCLES_V << PMU_TOUCH_WAIT_CYCLES_S)
#define PMU_TOUCH_WAIT_CYCLES_V  0x000001FFU
#define PMU_TOUCH_WAIT_CYCLES_S  5
/** PMU_TOUCH_SLEEP_CYCLES : R/W; bitpos: [29:14]; default: 100;
 *  need_des
 */
#define PMU_TOUCH_SLEEP_CYCLES    0x0000FFFFU
#define PMU_TOUCH_SLEEP_CYCLES_M  (PMU_TOUCH_SLEEP_CYCLES_V << PMU_TOUCH_SLEEP_CYCLES_S)
#define PMU_TOUCH_SLEEP_CYCLES_V  0x0000FFFFU
#define PMU_TOUCH_SLEEP_CYCLES_S  14
/** PMU_TOUCH_FORCE_DONE : R/W; bitpos: [30]; default: 0;
 *  need_des
 */
#define PMU_TOUCH_FORCE_DONE    (BIT(30))
#define PMU_TOUCH_FORCE_DONE_M  (PMU_TOUCH_FORCE_DONE_V << PMU_TOUCH_FORCE_DONE_S)
#define PMU_TOUCH_FORCE_DONE_V  0x00000001U
#define PMU_TOUCH_FORCE_DONE_S  30
/** PMU_TOUCH_SLEEP_TIMER_EN : R/W; bitpos: [31]; default: 0;
 *  need_des
 */
#define PMU_TOUCH_SLEEP_TIMER_EN    (BIT(31))
#define PMU_TOUCH_SLEEP_TIMER_EN_M  (PMU_TOUCH_SLEEP_TIMER_EN_V << PMU_TOUCH_SLEEP_TIMER_EN_S)
#define PMU_TOUCH_SLEEP_TIMER_EN_V  0x00000001U
#define PMU_TOUCH_SLEEP_TIMER_EN_S  31

/** PMU_RDN_ECO_REG register
 *  need_des
 */
#define PMU_RDN_ECO_REG (DR_REG_PMU_BASE + 0x214)
/** PMU_PMU_RDN_ECO_RESULT : RO; bitpos: [0]; default: 0;
 *  need_des
 */
#define PMU_PMU_RDN_ECO_RESULT    (BIT(0))
#define PMU_PMU_RDN_ECO_RESULT_M  (PMU_PMU_RDN_ECO_RESULT_V << PMU_PMU_RDN_ECO_RESULT_S)
#define PMU_PMU_RDN_ECO_RESULT_V  0x00000001U
#define PMU_PMU_RDN_ECO_RESULT_S  0
/** PMU_PMU_RDN_ECO_EN : R/W; bitpos: [31]; default: 0;
 *  need_des
 */
#define PMU_PMU_RDN_ECO_EN    (BIT(31))
#define PMU_PMU_RDN_ECO_EN_M  (PMU_PMU_RDN_ECO_EN_V << PMU_PMU_RDN_ECO_EN_S)
#define PMU_PMU_RDN_ECO_EN_V  0x00000001U
#define PMU_PMU_RDN_ECO_EN_S  31

/** PMU_DATE_REG register
 *  need_des
 */
#define PMU_DATE_REG (DR_REG_PMU_BASE + 0x3fc)
/** PMU_PMU_DATE : R/W; bitpos: [30:0]; default: 36712768;
 *  need_des
 */
#define PMU_PMU_DATE    0x7FFFFFFFU
#define PMU_PMU_DATE_M  (PMU_PMU_DATE_V << PMU_PMU_DATE_S)
#define PMU_PMU_DATE_V  0x7FFFFFFFU
#define PMU_PMU_DATE_S  0
/** PMU_CLK_EN : R/W; bitpos: [31]; default: 0;
 *  need_des
 */
#define PMU_CLK_EN    (BIT(31))
#define PMU_CLK_EN_M  (PMU_CLK_EN_V << PMU_CLK_EN_S)
#define PMU_CLK_EN_V  0x00000001U
#define PMU_CLK_EN_S  31

#ifdef __cplusplus
}
#endif
