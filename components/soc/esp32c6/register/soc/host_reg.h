/**
 * SPDX-FileCopyrightText: 2022 Espressif Systems (Shanghai) CO LTD
 *
 *  SPDX-License-Identifier: Apache-2.0
 */
#pragma once

#include <stdint.h>
#include "soc/soc.h"
#ifdef __cplusplus
extern "C" {
#endif

/** SLCHOST_FUNC2_0_REG register
 *  *******Description***********
 */
#define SLCHOST_FUNC2_0_REG (DR_REG_SLCHOST_BASE + 0x10)
/** SLCHOST_SLC_FUNC2_INT : R/W; bitpos: [24]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC_FUNC2_INT    (BIT(24))
#define SLCHOST_SLC_FUNC2_INT_M  (SLCHOST_SLC_FUNC2_INT_V << SLCHOST_SLC_FUNC2_INT_S)
#define SLCHOST_SLC_FUNC2_INT_V  0x00000001U
#define SLCHOST_SLC_FUNC2_INT_S  24

/** SLCHOST_FUNC2_1_REG register
 *  *******Description***********
 */
#define SLCHOST_FUNC2_1_REG (DR_REG_SLCHOST_BASE + 0x14)
/** SLCHOST_SLC_FUNC2_INT_EN : R/W; bitpos: [0]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC_FUNC2_INT_EN    (BIT(0))
#define SLCHOST_SLC_FUNC2_INT_EN_M  (SLCHOST_SLC_FUNC2_INT_EN_V << SLCHOST_SLC_FUNC2_INT_EN_S)
#define SLCHOST_SLC_FUNC2_INT_EN_V  0x00000001U
#define SLCHOST_SLC_FUNC2_INT_EN_S  0

/** SLCHOST_FUNC2_2_REG register
 *  *******Description***********
 */
#define SLCHOST_FUNC2_2_REG (DR_REG_SLCHOST_BASE + 0x20)
/** SLCHOST_SLC_FUNC1_MDSTAT : R/W; bitpos: [0]; default: 1;
 *  *******Description***********
 */
#define SLCHOST_SLC_FUNC1_MDSTAT    (BIT(0))
#define SLCHOST_SLC_FUNC1_MDSTAT_M  (SLCHOST_SLC_FUNC1_MDSTAT_V << SLCHOST_SLC_FUNC1_MDSTAT_S)
#define SLCHOST_SLC_FUNC1_MDSTAT_V  0x00000001U
#define SLCHOST_SLC_FUNC1_MDSTAT_S  0

/** SLCHOST_GPIO_STATUS0_REG register
 *  *******Description***********
 */
#define SLCHOST_GPIO_STATUS0_REG (DR_REG_SLCHOST_BASE + 0x34)
/** SLCHOST_GPIO_SDIO_INT0 : RO; bitpos: [31:0]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_GPIO_SDIO_INT0    0xFFFFFFFFU
#define SLCHOST_GPIO_SDIO_INT0_M  (SLCHOST_GPIO_SDIO_INT0_V << SLCHOST_GPIO_SDIO_INT0_S)
#define SLCHOST_GPIO_SDIO_INT0_V  0xFFFFFFFFU
#define SLCHOST_GPIO_SDIO_INT0_S  0

/** SLCHOST_GPIO_STATUS1_REG register
 *  *******Description***********
 */
#define SLCHOST_GPIO_STATUS1_REG (DR_REG_SLCHOST_BASE + 0x38)
/** SLCHOST_GPIO_SDIO_INT1 : RO; bitpos: [31:0]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_GPIO_SDIO_INT1    0xFFFFFFFFU
#define SLCHOST_GPIO_SDIO_INT1_M  (SLCHOST_GPIO_SDIO_INT1_V << SLCHOST_GPIO_SDIO_INT1_S)
#define SLCHOST_GPIO_SDIO_INT1_V  0xFFFFFFFFU
#define SLCHOST_GPIO_SDIO_INT1_S  0

/** SLCHOST_GPIO_IN0_REG register
 *  *******Description***********
 */
#define SLCHOST_GPIO_IN0_REG (DR_REG_SLCHOST_BASE + 0x3c)
/** SLCHOST_GPIO_SDIO_IN0 : RO; bitpos: [31:0]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_GPIO_SDIO_IN0    0xFFFFFFFFU
#define SLCHOST_GPIO_SDIO_IN0_M  (SLCHOST_GPIO_SDIO_IN0_V << SLCHOST_GPIO_SDIO_IN0_S)
#define SLCHOST_GPIO_SDIO_IN0_V  0xFFFFFFFFU
#define SLCHOST_GPIO_SDIO_IN0_S  0

/** SLCHOST_GPIO_IN1_REG register
 *  *******Description***********
 */
#define SLCHOST_GPIO_IN1_REG (DR_REG_SLCHOST_BASE + 0x40)
/** SLCHOST_GPIO_SDIO_IN1 : RO; bitpos: [31:0]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_GPIO_SDIO_IN1    0xFFFFFFFFU
#define SLCHOST_GPIO_SDIO_IN1_M  (SLCHOST_GPIO_SDIO_IN1_V << SLCHOST_GPIO_SDIO_IN1_S)
#define SLCHOST_GPIO_SDIO_IN1_V  0xFFFFFFFFU
#define SLCHOST_GPIO_SDIO_IN1_S  0

/** SLCHOST_SLC0HOST_TOKEN_RDATA_REG register
 *  *******Description***********
 */
#define SLCHOST_SLC0HOST_TOKEN_RDATA_REG (DR_REG_SLCHOST_BASE + 0x44)
/** SLCHOST_SLC0_TOKEN0 : RO; bitpos: [11:0]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0_TOKEN0    0x00000FFFU
#define SLCHOST_SLC0_TOKEN0_M  (SLCHOST_SLC0_TOKEN0_V << SLCHOST_SLC0_TOKEN0_S)
#define SLCHOST_SLC0_TOKEN0_V  0x00000FFFU
#define SLCHOST_SLC0_TOKEN0_S  0
/** SLCHOST_SLC0_RX_PF_VALID : RO; bitpos: [12]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0_RX_PF_VALID    (BIT(12))
#define SLCHOST_SLC0_RX_PF_VALID_M  (SLCHOST_SLC0_RX_PF_VALID_V << SLCHOST_SLC0_RX_PF_VALID_S)
#define SLCHOST_SLC0_RX_PF_VALID_V  0x00000001U
#define SLCHOST_SLC0_RX_PF_VALID_S  12
/** SLCHOST_HOSTSLCHOST_SLC0_TOKEN1 : RO; bitpos: [27:16]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_HOSTSLCHOST_SLC0_TOKEN1    0x00000FFFU
#define SLCHOST_HOSTSLCHOST_SLC0_TOKEN1_M  (SLCHOST_HOSTSLCHOST_SLC0_TOKEN1_V << SLCHOST_HOSTSLCHOST_SLC0_TOKEN1_S)
#define SLCHOST_HOSTSLCHOST_SLC0_TOKEN1_V  0x00000FFFU
#define SLCHOST_HOSTSLCHOST_SLC0_TOKEN1_S  16
/** SLCHOST_SLC0_RX_PF_EOF : RO; bitpos: [31:28]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0_RX_PF_EOF    0x0000000FU
#define SLCHOST_SLC0_RX_PF_EOF_M  (SLCHOST_SLC0_RX_PF_EOF_V << SLCHOST_SLC0_RX_PF_EOF_S)
#define SLCHOST_SLC0_RX_PF_EOF_V  0x0000000FU
#define SLCHOST_SLC0_RX_PF_EOF_S  28

/** SLCHOST_SLC0_HOST_PF_REG register
 *  *******Description***********
 */
#define SLCHOST_SLC0_HOST_PF_REG (DR_REG_SLCHOST_BASE + 0x48)
/** SLCHOST_SLC0_PF_DATA : RO; bitpos: [31:0]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0_PF_DATA    0xFFFFFFFFU
#define SLCHOST_SLC0_PF_DATA_M  (SLCHOST_SLC0_PF_DATA_V << SLCHOST_SLC0_PF_DATA_S)
#define SLCHOST_SLC0_PF_DATA_V  0xFFFFFFFFU
#define SLCHOST_SLC0_PF_DATA_S  0

/** SLCHOST_SLC1_HOST_PF_REG register
 *  *******Description***********
 */
#define SLCHOST_SLC1_HOST_PF_REG (DR_REG_SLCHOST_BASE + 0x4c)
/** SLCHOST_SLC1_PF_DATA : RO; bitpos: [31:0]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1_PF_DATA    0xFFFFFFFFU
#define SLCHOST_SLC1_PF_DATA_M  (SLCHOST_SLC1_PF_DATA_V << SLCHOST_SLC1_PF_DATA_S)
#define SLCHOST_SLC1_PF_DATA_V  0xFFFFFFFFU
#define SLCHOST_SLC1_PF_DATA_S  0

/** SLCHOST_SLC0HOST_INT_RAW_REG register
 *  *******Description***********
 */
#define SLCHOST_SLC0HOST_INT_RAW_REG (DR_REG_SLCHOST_BASE + 0x50)
/** SLCHOST_SLC0_TOHOST_BIT0_INT_RAW : R/WTC/SS; bitpos: [0]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0_TOHOST_BIT0_INT_RAW    (BIT(0))
#define SLCHOST_SLC0_TOHOST_BIT0_INT_RAW_M  (SLCHOST_SLC0_TOHOST_BIT0_INT_RAW_V << SLCHOST_SLC0_TOHOST_BIT0_INT_RAW_S)
#define SLCHOST_SLC0_TOHOST_BIT0_INT_RAW_V  0x00000001U
#define SLCHOST_SLC0_TOHOST_BIT0_INT_RAW_S  0
/** SLCHOST_SLC0_TOHOST_BIT1_INT_RAW : R/WTC/SS; bitpos: [1]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0_TOHOST_BIT1_INT_RAW    (BIT(1))
#define SLCHOST_SLC0_TOHOST_BIT1_INT_RAW_M  (SLCHOST_SLC0_TOHOST_BIT1_INT_RAW_V << SLCHOST_SLC0_TOHOST_BIT1_INT_RAW_S)
#define SLCHOST_SLC0_TOHOST_BIT1_INT_RAW_V  0x00000001U
#define SLCHOST_SLC0_TOHOST_BIT1_INT_RAW_S  1
/** SLCHOST_SLC0_TOHOST_BIT2_INT_RAW : R/WTC/SS; bitpos: [2]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0_TOHOST_BIT2_INT_RAW    (BIT(2))
#define SLCHOST_SLC0_TOHOST_BIT2_INT_RAW_M  (SLCHOST_SLC0_TOHOST_BIT2_INT_RAW_V << SLCHOST_SLC0_TOHOST_BIT2_INT_RAW_S)
#define SLCHOST_SLC0_TOHOST_BIT2_INT_RAW_V  0x00000001U
#define SLCHOST_SLC0_TOHOST_BIT2_INT_RAW_S  2
/** SLCHOST_SLC0_TOHOST_BIT3_INT_RAW : R/WTC/SS; bitpos: [3]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0_TOHOST_BIT3_INT_RAW    (BIT(3))
#define SLCHOST_SLC0_TOHOST_BIT3_INT_RAW_M  (SLCHOST_SLC0_TOHOST_BIT3_INT_RAW_V << SLCHOST_SLC0_TOHOST_BIT3_INT_RAW_S)
#define SLCHOST_SLC0_TOHOST_BIT3_INT_RAW_V  0x00000001U
#define SLCHOST_SLC0_TOHOST_BIT3_INT_RAW_S  3
/** SLCHOST_SLC0_TOHOST_BIT4_INT_RAW : R/WTC/SS; bitpos: [4]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0_TOHOST_BIT4_INT_RAW    (BIT(4))
#define SLCHOST_SLC0_TOHOST_BIT4_INT_RAW_M  (SLCHOST_SLC0_TOHOST_BIT4_INT_RAW_V << SLCHOST_SLC0_TOHOST_BIT4_INT_RAW_S)
#define SLCHOST_SLC0_TOHOST_BIT4_INT_RAW_V  0x00000001U
#define SLCHOST_SLC0_TOHOST_BIT4_INT_RAW_S  4
/** SLCHOST_SLC0_TOHOST_BIT5_INT_RAW : R/WTC/SS; bitpos: [5]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0_TOHOST_BIT5_INT_RAW    (BIT(5))
#define SLCHOST_SLC0_TOHOST_BIT5_INT_RAW_M  (SLCHOST_SLC0_TOHOST_BIT5_INT_RAW_V << SLCHOST_SLC0_TOHOST_BIT5_INT_RAW_S)
#define SLCHOST_SLC0_TOHOST_BIT5_INT_RAW_V  0x00000001U
#define SLCHOST_SLC0_TOHOST_BIT5_INT_RAW_S  5
/** SLCHOST_SLC0_TOHOST_BIT6_INT_RAW : R/WTC/SS/SC; bitpos: [6]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0_TOHOST_BIT6_INT_RAW    (BIT(6))
#define SLCHOST_SLC0_TOHOST_BIT6_INT_RAW_M  (SLCHOST_SLC0_TOHOST_BIT6_INT_RAW_V << SLCHOST_SLC0_TOHOST_BIT6_INT_RAW_S)
#define SLCHOST_SLC0_TOHOST_BIT6_INT_RAW_V  0x00000001U
#define SLCHOST_SLC0_TOHOST_BIT6_INT_RAW_S  6
/** SLCHOST_SLC0_TOHOST_BIT7_INT_RAW : R/WTC/SS/SC; bitpos: [7]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0_TOHOST_BIT7_INT_RAW    (BIT(7))
#define SLCHOST_SLC0_TOHOST_BIT7_INT_RAW_M  (SLCHOST_SLC0_TOHOST_BIT7_INT_RAW_V << SLCHOST_SLC0_TOHOST_BIT7_INT_RAW_S)
#define SLCHOST_SLC0_TOHOST_BIT7_INT_RAW_V  0x00000001U
#define SLCHOST_SLC0_TOHOST_BIT7_INT_RAW_S  7
/** SLCHOST_SLC0_TOKEN0_1TO0_INT_RAW : R/WTC/SS; bitpos: [8]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0_TOKEN0_1TO0_INT_RAW    (BIT(8))
#define SLCHOST_SLC0_TOKEN0_1TO0_INT_RAW_M  (SLCHOST_SLC0_TOKEN0_1TO0_INT_RAW_V << SLCHOST_SLC0_TOKEN0_1TO0_INT_RAW_S)
#define SLCHOST_SLC0_TOKEN0_1TO0_INT_RAW_V  0x00000001U
#define SLCHOST_SLC0_TOKEN0_1TO0_INT_RAW_S  8
/** SLCHOST_SLC0_TOKEN1_1TO0_INT_RAW : R/WTC/SS; bitpos: [9]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0_TOKEN1_1TO0_INT_RAW    (BIT(9))
#define SLCHOST_SLC0_TOKEN1_1TO0_INT_RAW_M  (SLCHOST_SLC0_TOKEN1_1TO0_INT_RAW_V << SLCHOST_SLC0_TOKEN1_1TO0_INT_RAW_S)
#define SLCHOST_SLC0_TOKEN1_1TO0_INT_RAW_V  0x00000001U
#define SLCHOST_SLC0_TOKEN1_1TO0_INT_RAW_S  9
/** SLCHOST_SLC0_TOKEN0_0TO1_INT_RAW : R/WTC/SS; bitpos: [10]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0_TOKEN0_0TO1_INT_RAW    (BIT(10))
#define SLCHOST_SLC0_TOKEN0_0TO1_INT_RAW_M  (SLCHOST_SLC0_TOKEN0_0TO1_INT_RAW_V << SLCHOST_SLC0_TOKEN0_0TO1_INT_RAW_S)
#define SLCHOST_SLC0_TOKEN0_0TO1_INT_RAW_V  0x00000001U
#define SLCHOST_SLC0_TOKEN0_0TO1_INT_RAW_S  10
/** SLCHOST_SLC0_TOKEN1_0TO1_INT_RAW : R/WTC/SS; bitpos: [11]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0_TOKEN1_0TO1_INT_RAW    (BIT(11))
#define SLCHOST_SLC0_TOKEN1_0TO1_INT_RAW_M  (SLCHOST_SLC0_TOKEN1_0TO1_INT_RAW_V << SLCHOST_SLC0_TOKEN1_0TO1_INT_RAW_S)
#define SLCHOST_SLC0_TOKEN1_0TO1_INT_RAW_V  0x00000001U
#define SLCHOST_SLC0_TOKEN1_0TO1_INT_RAW_S  11
/** SLCHOST_SLC0HOST_RX_SOF_INT_RAW : R/WTC/SS; bitpos: [12]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0HOST_RX_SOF_INT_RAW    (BIT(12))
#define SLCHOST_SLC0HOST_RX_SOF_INT_RAW_M  (SLCHOST_SLC0HOST_RX_SOF_INT_RAW_V << SLCHOST_SLC0HOST_RX_SOF_INT_RAW_S)
#define SLCHOST_SLC0HOST_RX_SOF_INT_RAW_V  0x00000001U
#define SLCHOST_SLC0HOST_RX_SOF_INT_RAW_S  12
/** SLCHOST_SLC0HOST_RX_EOF_INT_RAW : R/WTC/SS; bitpos: [13]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0HOST_RX_EOF_INT_RAW    (BIT(13))
#define SLCHOST_SLC0HOST_RX_EOF_INT_RAW_M  (SLCHOST_SLC0HOST_RX_EOF_INT_RAW_V << SLCHOST_SLC0HOST_RX_EOF_INT_RAW_S)
#define SLCHOST_SLC0HOST_RX_EOF_INT_RAW_V  0x00000001U
#define SLCHOST_SLC0HOST_RX_EOF_INT_RAW_S  13
/** SLCHOST_SLC0HOST_RX_START_INT_RAW : R/WTC/SS; bitpos: [14]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0HOST_RX_START_INT_RAW    (BIT(14))
#define SLCHOST_SLC0HOST_RX_START_INT_RAW_M  (SLCHOST_SLC0HOST_RX_START_INT_RAW_V << SLCHOST_SLC0HOST_RX_START_INT_RAW_S)
#define SLCHOST_SLC0HOST_RX_START_INT_RAW_V  0x00000001U
#define SLCHOST_SLC0HOST_RX_START_INT_RAW_S  14
/** SLCHOST_SLC0HOST_TX_START_INT_RAW : R/WTC/SS; bitpos: [15]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0HOST_TX_START_INT_RAW    (BIT(15))
#define SLCHOST_SLC0HOST_TX_START_INT_RAW_M  (SLCHOST_SLC0HOST_TX_START_INT_RAW_V << SLCHOST_SLC0HOST_TX_START_INT_RAW_S)
#define SLCHOST_SLC0HOST_TX_START_INT_RAW_V  0x00000001U
#define SLCHOST_SLC0HOST_TX_START_INT_RAW_S  15
/** SLCHOST_SLC0_RX_UDF_INT_RAW : R/WTC/SS; bitpos: [16]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0_RX_UDF_INT_RAW    (BIT(16))
#define SLCHOST_SLC0_RX_UDF_INT_RAW_M  (SLCHOST_SLC0_RX_UDF_INT_RAW_V << SLCHOST_SLC0_RX_UDF_INT_RAW_S)
#define SLCHOST_SLC0_RX_UDF_INT_RAW_V  0x00000001U
#define SLCHOST_SLC0_RX_UDF_INT_RAW_S  16
/** SLCHOST_SLC0_TX_OVF_INT_RAW : R/WTC/SS; bitpos: [17]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0_TX_OVF_INT_RAW    (BIT(17))
#define SLCHOST_SLC0_TX_OVF_INT_RAW_M  (SLCHOST_SLC0_TX_OVF_INT_RAW_V << SLCHOST_SLC0_TX_OVF_INT_RAW_S)
#define SLCHOST_SLC0_TX_OVF_INT_RAW_V  0x00000001U
#define SLCHOST_SLC0_TX_OVF_INT_RAW_S  17
/** SLCHOST_SLC0_RX_PF_VALID_INT_RAW : R/WTC/SS; bitpos: [18]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0_RX_PF_VALID_INT_RAW    (BIT(18))
#define SLCHOST_SLC0_RX_PF_VALID_INT_RAW_M  (SLCHOST_SLC0_RX_PF_VALID_INT_RAW_V << SLCHOST_SLC0_RX_PF_VALID_INT_RAW_S)
#define SLCHOST_SLC0_RX_PF_VALID_INT_RAW_V  0x00000001U
#define SLCHOST_SLC0_RX_PF_VALID_INT_RAW_S  18
/** SLCHOST_SLC0_EXT_BIT0_INT_RAW : R/WTC/SS; bitpos: [19]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0_EXT_BIT0_INT_RAW    (BIT(19))
#define SLCHOST_SLC0_EXT_BIT0_INT_RAW_M  (SLCHOST_SLC0_EXT_BIT0_INT_RAW_V << SLCHOST_SLC0_EXT_BIT0_INT_RAW_S)
#define SLCHOST_SLC0_EXT_BIT0_INT_RAW_V  0x00000001U
#define SLCHOST_SLC0_EXT_BIT0_INT_RAW_S  19
/** SLCHOST_SLC0_EXT_BIT1_INT_RAW : R/WTC/SS; bitpos: [20]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0_EXT_BIT1_INT_RAW    (BIT(20))
#define SLCHOST_SLC0_EXT_BIT1_INT_RAW_M  (SLCHOST_SLC0_EXT_BIT1_INT_RAW_V << SLCHOST_SLC0_EXT_BIT1_INT_RAW_S)
#define SLCHOST_SLC0_EXT_BIT1_INT_RAW_V  0x00000001U
#define SLCHOST_SLC0_EXT_BIT1_INT_RAW_S  20
/** SLCHOST_SLC0_EXT_BIT2_INT_RAW : R/WTC/SS; bitpos: [21]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0_EXT_BIT2_INT_RAW    (BIT(21))
#define SLCHOST_SLC0_EXT_BIT2_INT_RAW_M  (SLCHOST_SLC0_EXT_BIT2_INT_RAW_V << SLCHOST_SLC0_EXT_BIT2_INT_RAW_S)
#define SLCHOST_SLC0_EXT_BIT2_INT_RAW_V  0x00000001U
#define SLCHOST_SLC0_EXT_BIT2_INT_RAW_S  21
/** SLCHOST_SLC0_EXT_BIT3_INT_RAW : R/WTC/SS; bitpos: [22]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0_EXT_BIT3_INT_RAW    (BIT(22))
#define SLCHOST_SLC0_EXT_BIT3_INT_RAW_M  (SLCHOST_SLC0_EXT_BIT3_INT_RAW_V << SLCHOST_SLC0_EXT_BIT3_INT_RAW_S)
#define SLCHOST_SLC0_EXT_BIT3_INT_RAW_V  0x00000001U
#define SLCHOST_SLC0_EXT_BIT3_INT_RAW_S  22
/** SLCHOST_SLC0_RX_NEW_PACKET_INT_RAW : R/WTC/SS; bitpos: [23]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0_RX_NEW_PACKET_INT_RAW    (BIT(23))
#define SLCHOST_SLC0_RX_NEW_PACKET_INT_RAW_M  (SLCHOST_SLC0_RX_NEW_PACKET_INT_RAW_V << SLCHOST_SLC0_RX_NEW_PACKET_INT_RAW_S)
#define SLCHOST_SLC0_RX_NEW_PACKET_INT_RAW_V  0x00000001U
#define SLCHOST_SLC0_RX_NEW_PACKET_INT_RAW_S  23
/** SLCHOST_SLC0_HOST_RD_RETRY_INT_RAW : R/WTC/SS; bitpos: [24]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0_HOST_RD_RETRY_INT_RAW    (BIT(24))
#define SLCHOST_SLC0_HOST_RD_RETRY_INT_RAW_M  (SLCHOST_SLC0_HOST_RD_RETRY_INT_RAW_V << SLCHOST_SLC0_HOST_RD_RETRY_INT_RAW_S)
#define SLCHOST_SLC0_HOST_RD_RETRY_INT_RAW_V  0x00000001U
#define SLCHOST_SLC0_HOST_RD_RETRY_INT_RAW_S  24
/** SLCHOST_GPIO_SDIO_INT_RAW : R/WTC/SS/SC; bitpos: [25]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_GPIO_SDIO_INT_RAW    (BIT(25))
#define SLCHOST_GPIO_SDIO_INT_RAW_M  (SLCHOST_GPIO_SDIO_INT_RAW_V << SLCHOST_GPIO_SDIO_INT_RAW_S)
#define SLCHOST_GPIO_SDIO_INT_RAW_V  0x00000001U
#define SLCHOST_GPIO_SDIO_INT_RAW_S  25

/** SLCHOST_SLC1HOST_INT_RAW_REG register
 *  *******Description***********
 */
#define SLCHOST_SLC1HOST_INT_RAW_REG (DR_REG_SLCHOST_BASE + 0x54)
/** SLCHOST_SLC1_TOHOST_BIT0_INT_RAW : R/WTC/SS; bitpos: [0]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1_TOHOST_BIT0_INT_RAW    (BIT(0))
#define SLCHOST_SLC1_TOHOST_BIT0_INT_RAW_M  (SLCHOST_SLC1_TOHOST_BIT0_INT_RAW_V << SLCHOST_SLC1_TOHOST_BIT0_INT_RAW_S)
#define SLCHOST_SLC1_TOHOST_BIT0_INT_RAW_V  0x00000001U
#define SLCHOST_SLC1_TOHOST_BIT0_INT_RAW_S  0
/** SLCHOST_SLC1_TOHOST_BIT1_INT_RAW : R/WTC/SS; bitpos: [1]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1_TOHOST_BIT1_INT_RAW    (BIT(1))
#define SLCHOST_SLC1_TOHOST_BIT1_INT_RAW_M  (SLCHOST_SLC1_TOHOST_BIT1_INT_RAW_V << SLCHOST_SLC1_TOHOST_BIT1_INT_RAW_S)
#define SLCHOST_SLC1_TOHOST_BIT1_INT_RAW_V  0x00000001U
#define SLCHOST_SLC1_TOHOST_BIT1_INT_RAW_S  1
/** SLCHOST_SLC1_TOHOST_BIT2_INT_RAW : R/WTC/SS; bitpos: [2]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1_TOHOST_BIT2_INT_RAW    (BIT(2))
#define SLCHOST_SLC1_TOHOST_BIT2_INT_RAW_M  (SLCHOST_SLC1_TOHOST_BIT2_INT_RAW_V << SLCHOST_SLC1_TOHOST_BIT2_INT_RAW_S)
#define SLCHOST_SLC1_TOHOST_BIT2_INT_RAW_V  0x00000001U
#define SLCHOST_SLC1_TOHOST_BIT2_INT_RAW_S  2
/** SLCHOST_SLC1_TOHOST_BIT3_INT_RAW : R/WTC/SS; bitpos: [3]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1_TOHOST_BIT3_INT_RAW    (BIT(3))
#define SLCHOST_SLC1_TOHOST_BIT3_INT_RAW_M  (SLCHOST_SLC1_TOHOST_BIT3_INT_RAW_V << SLCHOST_SLC1_TOHOST_BIT3_INT_RAW_S)
#define SLCHOST_SLC1_TOHOST_BIT3_INT_RAW_V  0x00000001U
#define SLCHOST_SLC1_TOHOST_BIT3_INT_RAW_S  3
/** SLCHOST_SLC1_TOHOST_BIT4_INT_RAW : R/WTC/SS; bitpos: [4]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1_TOHOST_BIT4_INT_RAW    (BIT(4))
#define SLCHOST_SLC1_TOHOST_BIT4_INT_RAW_M  (SLCHOST_SLC1_TOHOST_BIT4_INT_RAW_V << SLCHOST_SLC1_TOHOST_BIT4_INT_RAW_S)
#define SLCHOST_SLC1_TOHOST_BIT4_INT_RAW_V  0x00000001U
#define SLCHOST_SLC1_TOHOST_BIT4_INT_RAW_S  4
/** SLCHOST_SLC1_TOHOST_BIT5_INT_RAW : R/WTC/SS; bitpos: [5]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1_TOHOST_BIT5_INT_RAW    (BIT(5))
#define SLCHOST_SLC1_TOHOST_BIT5_INT_RAW_M  (SLCHOST_SLC1_TOHOST_BIT5_INT_RAW_V << SLCHOST_SLC1_TOHOST_BIT5_INT_RAW_S)
#define SLCHOST_SLC1_TOHOST_BIT5_INT_RAW_V  0x00000001U
#define SLCHOST_SLC1_TOHOST_BIT5_INT_RAW_S  5
/** SLCHOST_SLC1_TOHOST_BIT6_INT_RAW : R/WTC/SS/SC; bitpos: [6]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1_TOHOST_BIT6_INT_RAW    (BIT(6))
#define SLCHOST_SLC1_TOHOST_BIT6_INT_RAW_M  (SLCHOST_SLC1_TOHOST_BIT6_INT_RAW_V << SLCHOST_SLC1_TOHOST_BIT6_INT_RAW_S)
#define SLCHOST_SLC1_TOHOST_BIT6_INT_RAW_V  0x00000001U
#define SLCHOST_SLC1_TOHOST_BIT6_INT_RAW_S  6
/** SLCHOST_SLC1_TOHOST_BIT7_INT_RAW : R/WTC/SS/SC; bitpos: [7]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1_TOHOST_BIT7_INT_RAW    (BIT(7))
#define SLCHOST_SLC1_TOHOST_BIT7_INT_RAW_M  (SLCHOST_SLC1_TOHOST_BIT7_INT_RAW_V << SLCHOST_SLC1_TOHOST_BIT7_INT_RAW_S)
#define SLCHOST_SLC1_TOHOST_BIT7_INT_RAW_V  0x00000001U
#define SLCHOST_SLC1_TOHOST_BIT7_INT_RAW_S  7
/** SLCHOST_SLC1_TOKEN0_1TO0_INT_RAW : R/WTC/SS; bitpos: [8]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1_TOKEN0_1TO0_INT_RAW    (BIT(8))
#define SLCHOST_SLC1_TOKEN0_1TO0_INT_RAW_M  (SLCHOST_SLC1_TOKEN0_1TO0_INT_RAW_V << SLCHOST_SLC1_TOKEN0_1TO0_INT_RAW_S)
#define SLCHOST_SLC1_TOKEN0_1TO0_INT_RAW_V  0x00000001U
#define SLCHOST_SLC1_TOKEN0_1TO0_INT_RAW_S  8
/** SLCHOST_SLC1_TOKEN1_1TO0_INT_RAW : R/WTC/SS; bitpos: [9]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1_TOKEN1_1TO0_INT_RAW    (BIT(9))
#define SLCHOST_SLC1_TOKEN1_1TO0_INT_RAW_M  (SLCHOST_SLC1_TOKEN1_1TO0_INT_RAW_V << SLCHOST_SLC1_TOKEN1_1TO0_INT_RAW_S)
#define SLCHOST_SLC1_TOKEN1_1TO0_INT_RAW_V  0x00000001U
#define SLCHOST_SLC1_TOKEN1_1TO0_INT_RAW_S  9
/** SLCHOST_SLC1_TOKEN0_0TO1_INT_RAW : R/WTC/SS; bitpos: [10]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1_TOKEN0_0TO1_INT_RAW    (BIT(10))
#define SLCHOST_SLC1_TOKEN0_0TO1_INT_RAW_M  (SLCHOST_SLC1_TOKEN0_0TO1_INT_RAW_V << SLCHOST_SLC1_TOKEN0_0TO1_INT_RAW_S)
#define SLCHOST_SLC1_TOKEN0_0TO1_INT_RAW_V  0x00000001U
#define SLCHOST_SLC1_TOKEN0_0TO1_INT_RAW_S  10
/** SLCHOST_SLC1_TOKEN1_0TO1_INT_RAW : R/WTC/SS; bitpos: [11]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1_TOKEN1_0TO1_INT_RAW    (BIT(11))
#define SLCHOST_SLC1_TOKEN1_0TO1_INT_RAW_M  (SLCHOST_SLC1_TOKEN1_0TO1_INT_RAW_V << SLCHOST_SLC1_TOKEN1_0TO1_INT_RAW_S)
#define SLCHOST_SLC1_TOKEN1_0TO1_INT_RAW_V  0x00000001U
#define SLCHOST_SLC1_TOKEN1_0TO1_INT_RAW_S  11
/** SLCHOST_SLC1HOST_RX_SOF_INT_RAW : R/WTC/SS; bitpos: [12]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1HOST_RX_SOF_INT_RAW    (BIT(12))
#define SLCHOST_SLC1HOST_RX_SOF_INT_RAW_M  (SLCHOST_SLC1HOST_RX_SOF_INT_RAW_V << SLCHOST_SLC1HOST_RX_SOF_INT_RAW_S)
#define SLCHOST_SLC1HOST_RX_SOF_INT_RAW_V  0x00000001U
#define SLCHOST_SLC1HOST_RX_SOF_INT_RAW_S  12
/** SLCHOST_SLC1HOST_RX_EOF_INT_RAW : R/WTC/SS; bitpos: [13]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1HOST_RX_EOF_INT_RAW    (BIT(13))
#define SLCHOST_SLC1HOST_RX_EOF_INT_RAW_M  (SLCHOST_SLC1HOST_RX_EOF_INT_RAW_V << SLCHOST_SLC1HOST_RX_EOF_INT_RAW_S)
#define SLCHOST_SLC1HOST_RX_EOF_INT_RAW_V  0x00000001U
#define SLCHOST_SLC1HOST_RX_EOF_INT_RAW_S  13
/** SLCHOST_SLC1HOST_RX_START_INT_RAW : R/WTC/SS; bitpos: [14]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1HOST_RX_START_INT_RAW    (BIT(14))
#define SLCHOST_SLC1HOST_RX_START_INT_RAW_M  (SLCHOST_SLC1HOST_RX_START_INT_RAW_V << SLCHOST_SLC1HOST_RX_START_INT_RAW_S)
#define SLCHOST_SLC1HOST_RX_START_INT_RAW_V  0x00000001U
#define SLCHOST_SLC1HOST_RX_START_INT_RAW_S  14
/** SLCHOST_SLC1HOST_TX_START_INT_RAW : R/WTC/SS; bitpos: [15]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1HOST_TX_START_INT_RAW    (BIT(15))
#define SLCHOST_SLC1HOST_TX_START_INT_RAW_M  (SLCHOST_SLC1HOST_TX_START_INT_RAW_V << SLCHOST_SLC1HOST_TX_START_INT_RAW_S)
#define SLCHOST_SLC1HOST_TX_START_INT_RAW_V  0x00000001U
#define SLCHOST_SLC1HOST_TX_START_INT_RAW_S  15
/** SLCHOST_SLC1_RX_UDF_INT_RAW : R/WTC/SS; bitpos: [16]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1_RX_UDF_INT_RAW    (BIT(16))
#define SLCHOST_SLC1_RX_UDF_INT_RAW_M  (SLCHOST_SLC1_RX_UDF_INT_RAW_V << SLCHOST_SLC1_RX_UDF_INT_RAW_S)
#define SLCHOST_SLC1_RX_UDF_INT_RAW_V  0x00000001U
#define SLCHOST_SLC1_RX_UDF_INT_RAW_S  16
/** SLCHOST_SLC1_TX_OVF_INT_RAW : R/WTC/SS; bitpos: [17]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1_TX_OVF_INT_RAW    (BIT(17))
#define SLCHOST_SLC1_TX_OVF_INT_RAW_M  (SLCHOST_SLC1_TX_OVF_INT_RAW_V << SLCHOST_SLC1_TX_OVF_INT_RAW_S)
#define SLCHOST_SLC1_TX_OVF_INT_RAW_V  0x00000001U
#define SLCHOST_SLC1_TX_OVF_INT_RAW_S  17
/** SLCHOST_SLC1_RX_PF_VALID_INT_RAW : R/WTC/SS; bitpos: [18]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1_RX_PF_VALID_INT_RAW    (BIT(18))
#define SLCHOST_SLC1_RX_PF_VALID_INT_RAW_M  (SLCHOST_SLC1_RX_PF_VALID_INT_RAW_V << SLCHOST_SLC1_RX_PF_VALID_INT_RAW_S)
#define SLCHOST_SLC1_RX_PF_VALID_INT_RAW_V  0x00000001U
#define SLCHOST_SLC1_RX_PF_VALID_INT_RAW_S  18
/** SLCHOST_SLC1_EXT_BIT0_INT_RAW : R/WTC/SS; bitpos: [19]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1_EXT_BIT0_INT_RAW    (BIT(19))
#define SLCHOST_SLC1_EXT_BIT0_INT_RAW_M  (SLCHOST_SLC1_EXT_BIT0_INT_RAW_V << SLCHOST_SLC1_EXT_BIT0_INT_RAW_S)
#define SLCHOST_SLC1_EXT_BIT0_INT_RAW_V  0x00000001U
#define SLCHOST_SLC1_EXT_BIT0_INT_RAW_S  19
/** SLCHOST_SLC1_EXT_BIT1_INT_RAW : R/WTC/SS; bitpos: [20]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1_EXT_BIT1_INT_RAW    (BIT(20))
#define SLCHOST_SLC1_EXT_BIT1_INT_RAW_M  (SLCHOST_SLC1_EXT_BIT1_INT_RAW_V << SLCHOST_SLC1_EXT_BIT1_INT_RAW_S)
#define SLCHOST_SLC1_EXT_BIT1_INT_RAW_V  0x00000001U
#define SLCHOST_SLC1_EXT_BIT1_INT_RAW_S  20
/** SLCHOST_SLC1_EXT_BIT2_INT_RAW : R/WTC/SS; bitpos: [21]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1_EXT_BIT2_INT_RAW    (BIT(21))
#define SLCHOST_SLC1_EXT_BIT2_INT_RAW_M  (SLCHOST_SLC1_EXT_BIT2_INT_RAW_V << SLCHOST_SLC1_EXT_BIT2_INT_RAW_S)
#define SLCHOST_SLC1_EXT_BIT2_INT_RAW_V  0x00000001U
#define SLCHOST_SLC1_EXT_BIT2_INT_RAW_S  21
/** SLCHOST_SLC1_EXT_BIT3_INT_RAW : R/WTC/SS; bitpos: [22]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1_EXT_BIT3_INT_RAW    (BIT(22))
#define SLCHOST_SLC1_EXT_BIT3_INT_RAW_M  (SLCHOST_SLC1_EXT_BIT3_INT_RAW_V << SLCHOST_SLC1_EXT_BIT3_INT_RAW_S)
#define SLCHOST_SLC1_EXT_BIT3_INT_RAW_V  0x00000001U
#define SLCHOST_SLC1_EXT_BIT3_INT_RAW_S  22
/** SLCHOST_SLC1_WIFI_RX_NEW_PACKET_INT_RAW : R/WTC/SS; bitpos: [23]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1_WIFI_RX_NEW_PACKET_INT_RAW    (BIT(23))
#define SLCHOST_SLC1_WIFI_RX_NEW_PACKET_INT_RAW_M  (SLCHOST_SLC1_WIFI_RX_NEW_PACKET_INT_RAW_V << SLCHOST_SLC1_WIFI_RX_NEW_PACKET_INT_RAW_S)
#define SLCHOST_SLC1_WIFI_RX_NEW_PACKET_INT_RAW_V  0x00000001U
#define SLCHOST_SLC1_WIFI_RX_NEW_PACKET_INT_RAW_S  23
/** SLCHOST_SLC1_HOST_RD_RETRY_INT_RAW : R/WTC/SS; bitpos: [24]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1_HOST_RD_RETRY_INT_RAW    (BIT(24))
#define SLCHOST_SLC1_HOST_RD_RETRY_INT_RAW_M  (SLCHOST_SLC1_HOST_RD_RETRY_INT_RAW_V << SLCHOST_SLC1_HOST_RD_RETRY_INT_RAW_S)
#define SLCHOST_SLC1_HOST_RD_RETRY_INT_RAW_V  0x00000001U
#define SLCHOST_SLC1_HOST_RD_RETRY_INT_RAW_S  24
/** SLCHOST_SLC1_BT_RX_NEW_PACKET_INT_RAW : R/WTC/SS; bitpos: [25]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1_BT_RX_NEW_PACKET_INT_RAW    (BIT(25))
#define SLCHOST_SLC1_BT_RX_NEW_PACKET_INT_RAW_M  (SLCHOST_SLC1_BT_RX_NEW_PACKET_INT_RAW_V << SLCHOST_SLC1_BT_RX_NEW_PACKET_INT_RAW_S)
#define SLCHOST_SLC1_BT_RX_NEW_PACKET_INT_RAW_V  0x00000001U
#define SLCHOST_SLC1_BT_RX_NEW_PACKET_INT_RAW_S  25

/** SLCHOST_SLC0HOST_INT_ST_REG register
 *  *******Description***********
 */
#define SLCHOST_SLC0HOST_INT_ST_REG (DR_REG_SLCHOST_BASE + 0x58)
/** SLCHOST_SLC0_TOHOST_BIT0_INT_ST : RO; bitpos: [0]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0_TOHOST_BIT0_INT_ST    (BIT(0))
#define SLCHOST_SLC0_TOHOST_BIT0_INT_ST_M  (SLCHOST_SLC0_TOHOST_BIT0_INT_ST_V << SLCHOST_SLC0_TOHOST_BIT0_INT_ST_S)
#define SLCHOST_SLC0_TOHOST_BIT0_INT_ST_V  0x00000001U
#define SLCHOST_SLC0_TOHOST_BIT0_INT_ST_S  0
/** SLCHOST_SLC0_TOHOST_BIT1_INT_ST : RO; bitpos: [1]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0_TOHOST_BIT1_INT_ST    (BIT(1))
#define SLCHOST_SLC0_TOHOST_BIT1_INT_ST_M  (SLCHOST_SLC0_TOHOST_BIT1_INT_ST_V << SLCHOST_SLC0_TOHOST_BIT1_INT_ST_S)
#define SLCHOST_SLC0_TOHOST_BIT1_INT_ST_V  0x00000001U
#define SLCHOST_SLC0_TOHOST_BIT1_INT_ST_S  1
/** SLCHOST_SLC0_TOHOST_BIT2_INT_ST : RO; bitpos: [2]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0_TOHOST_BIT2_INT_ST    (BIT(2))
#define SLCHOST_SLC0_TOHOST_BIT2_INT_ST_M  (SLCHOST_SLC0_TOHOST_BIT2_INT_ST_V << SLCHOST_SLC0_TOHOST_BIT2_INT_ST_S)
#define SLCHOST_SLC0_TOHOST_BIT2_INT_ST_V  0x00000001U
#define SLCHOST_SLC0_TOHOST_BIT2_INT_ST_S  2
/** SLCHOST_SLC0_TOHOST_BIT3_INT_ST : RO; bitpos: [3]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0_TOHOST_BIT3_INT_ST    (BIT(3))
#define SLCHOST_SLC0_TOHOST_BIT3_INT_ST_M  (SLCHOST_SLC0_TOHOST_BIT3_INT_ST_V << SLCHOST_SLC0_TOHOST_BIT3_INT_ST_S)
#define SLCHOST_SLC0_TOHOST_BIT3_INT_ST_V  0x00000001U
#define SLCHOST_SLC0_TOHOST_BIT3_INT_ST_S  3
/** SLCHOST_SLC0_TOHOST_BIT4_INT_ST : RO; bitpos: [4]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0_TOHOST_BIT4_INT_ST    (BIT(4))
#define SLCHOST_SLC0_TOHOST_BIT4_INT_ST_M  (SLCHOST_SLC0_TOHOST_BIT4_INT_ST_V << SLCHOST_SLC0_TOHOST_BIT4_INT_ST_S)
#define SLCHOST_SLC0_TOHOST_BIT4_INT_ST_V  0x00000001U
#define SLCHOST_SLC0_TOHOST_BIT4_INT_ST_S  4
/** SLCHOST_SLC0_TOHOST_BIT5_INT_ST : RO; bitpos: [5]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0_TOHOST_BIT5_INT_ST    (BIT(5))
#define SLCHOST_SLC0_TOHOST_BIT5_INT_ST_M  (SLCHOST_SLC0_TOHOST_BIT5_INT_ST_V << SLCHOST_SLC0_TOHOST_BIT5_INT_ST_S)
#define SLCHOST_SLC0_TOHOST_BIT5_INT_ST_V  0x00000001U
#define SLCHOST_SLC0_TOHOST_BIT5_INT_ST_S  5
/** SLCHOST_SLC0_TOHOST_BIT6_INT_ST : RO; bitpos: [6]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0_TOHOST_BIT6_INT_ST    (BIT(6))
#define SLCHOST_SLC0_TOHOST_BIT6_INT_ST_M  (SLCHOST_SLC0_TOHOST_BIT6_INT_ST_V << SLCHOST_SLC0_TOHOST_BIT6_INT_ST_S)
#define SLCHOST_SLC0_TOHOST_BIT6_INT_ST_V  0x00000001U
#define SLCHOST_SLC0_TOHOST_BIT6_INT_ST_S  6
/** SLCHOST_SLC0_TOHOST_BIT7_INT_ST : RO; bitpos: [7]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0_TOHOST_BIT7_INT_ST    (BIT(7))
#define SLCHOST_SLC0_TOHOST_BIT7_INT_ST_M  (SLCHOST_SLC0_TOHOST_BIT7_INT_ST_V << SLCHOST_SLC0_TOHOST_BIT7_INT_ST_S)
#define SLCHOST_SLC0_TOHOST_BIT7_INT_ST_V  0x00000001U
#define SLCHOST_SLC0_TOHOST_BIT7_INT_ST_S  7
/** SLCHOST_SLC0_TOKEN0_1TO0_INT_ST : RO; bitpos: [8]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0_TOKEN0_1TO0_INT_ST    (BIT(8))
#define SLCHOST_SLC0_TOKEN0_1TO0_INT_ST_M  (SLCHOST_SLC0_TOKEN0_1TO0_INT_ST_V << SLCHOST_SLC0_TOKEN0_1TO0_INT_ST_S)
#define SLCHOST_SLC0_TOKEN0_1TO0_INT_ST_V  0x00000001U
#define SLCHOST_SLC0_TOKEN0_1TO0_INT_ST_S  8
/** SLCHOST_SLC0_TOKEN1_1TO0_INT_ST : RO; bitpos: [9]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0_TOKEN1_1TO0_INT_ST    (BIT(9))
#define SLCHOST_SLC0_TOKEN1_1TO0_INT_ST_M  (SLCHOST_SLC0_TOKEN1_1TO0_INT_ST_V << SLCHOST_SLC0_TOKEN1_1TO0_INT_ST_S)
#define SLCHOST_SLC0_TOKEN1_1TO0_INT_ST_V  0x00000001U
#define SLCHOST_SLC0_TOKEN1_1TO0_INT_ST_S  9
/** SLCHOST_SLC0_TOKEN0_0TO1_INT_ST : RO; bitpos: [10]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0_TOKEN0_0TO1_INT_ST    (BIT(10))
#define SLCHOST_SLC0_TOKEN0_0TO1_INT_ST_M  (SLCHOST_SLC0_TOKEN0_0TO1_INT_ST_V << SLCHOST_SLC0_TOKEN0_0TO1_INT_ST_S)
#define SLCHOST_SLC0_TOKEN0_0TO1_INT_ST_V  0x00000001U
#define SLCHOST_SLC0_TOKEN0_0TO1_INT_ST_S  10
/** SLCHOST_SLC0_TOKEN1_0TO1_INT_ST : RO; bitpos: [11]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0_TOKEN1_0TO1_INT_ST    (BIT(11))
#define SLCHOST_SLC0_TOKEN1_0TO1_INT_ST_M  (SLCHOST_SLC0_TOKEN1_0TO1_INT_ST_V << SLCHOST_SLC0_TOKEN1_0TO1_INT_ST_S)
#define SLCHOST_SLC0_TOKEN1_0TO1_INT_ST_V  0x00000001U
#define SLCHOST_SLC0_TOKEN1_0TO1_INT_ST_S  11
/** SLCHOST_SLC0HOST_RX_SOF_INT_ST : RO; bitpos: [12]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0HOST_RX_SOF_INT_ST    (BIT(12))
#define SLCHOST_SLC0HOST_RX_SOF_INT_ST_M  (SLCHOST_SLC0HOST_RX_SOF_INT_ST_V << SLCHOST_SLC0HOST_RX_SOF_INT_ST_S)
#define SLCHOST_SLC0HOST_RX_SOF_INT_ST_V  0x00000001U
#define SLCHOST_SLC0HOST_RX_SOF_INT_ST_S  12
/** SLCHOST_SLC0HOST_RX_EOF_INT_ST : RO; bitpos: [13]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0HOST_RX_EOF_INT_ST    (BIT(13))
#define SLCHOST_SLC0HOST_RX_EOF_INT_ST_M  (SLCHOST_SLC0HOST_RX_EOF_INT_ST_V << SLCHOST_SLC0HOST_RX_EOF_INT_ST_S)
#define SLCHOST_SLC0HOST_RX_EOF_INT_ST_V  0x00000001U
#define SLCHOST_SLC0HOST_RX_EOF_INT_ST_S  13
/** SLCHOST_SLC0HOST_RX_START_INT_ST : RO; bitpos: [14]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0HOST_RX_START_INT_ST    (BIT(14))
#define SLCHOST_SLC0HOST_RX_START_INT_ST_M  (SLCHOST_SLC0HOST_RX_START_INT_ST_V << SLCHOST_SLC0HOST_RX_START_INT_ST_S)
#define SLCHOST_SLC0HOST_RX_START_INT_ST_V  0x00000001U
#define SLCHOST_SLC0HOST_RX_START_INT_ST_S  14
/** SLCHOST_SLC0HOST_TX_START_INT_ST : RO; bitpos: [15]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0HOST_TX_START_INT_ST    (BIT(15))
#define SLCHOST_SLC0HOST_TX_START_INT_ST_M  (SLCHOST_SLC0HOST_TX_START_INT_ST_V << SLCHOST_SLC0HOST_TX_START_INT_ST_S)
#define SLCHOST_SLC0HOST_TX_START_INT_ST_V  0x00000001U
#define SLCHOST_SLC0HOST_TX_START_INT_ST_S  15
/** SLCHOST_SLC0_RX_UDF_INT_ST : RO; bitpos: [16]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0_RX_UDF_INT_ST    (BIT(16))
#define SLCHOST_SLC0_RX_UDF_INT_ST_M  (SLCHOST_SLC0_RX_UDF_INT_ST_V << SLCHOST_SLC0_RX_UDF_INT_ST_S)
#define SLCHOST_SLC0_RX_UDF_INT_ST_V  0x00000001U
#define SLCHOST_SLC0_RX_UDF_INT_ST_S  16
/** SLCHOST_SLC0_TX_OVF_INT_ST : RO; bitpos: [17]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0_TX_OVF_INT_ST    (BIT(17))
#define SLCHOST_SLC0_TX_OVF_INT_ST_M  (SLCHOST_SLC0_TX_OVF_INT_ST_V << SLCHOST_SLC0_TX_OVF_INT_ST_S)
#define SLCHOST_SLC0_TX_OVF_INT_ST_V  0x00000001U
#define SLCHOST_SLC0_TX_OVF_INT_ST_S  17
/** SLCHOST_SLC0_RX_PF_VALID_INT_ST : RO; bitpos: [18]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0_RX_PF_VALID_INT_ST    (BIT(18))
#define SLCHOST_SLC0_RX_PF_VALID_INT_ST_M  (SLCHOST_SLC0_RX_PF_VALID_INT_ST_V << SLCHOST_SLC0_RX_PF_VALID_INT_ST_S)
#define SLCHOST_SLC0_RX_PF_VALID_INT_ST_V  0x00000001U
#define SLCHOST_SLC0_RX_PF_VALID_INT_ST_S  18
/** SLCHOST_SLC0_EXT_BIT0_INT_ST : RO; bitpos: [19]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0_EXT_BIT0_INT_ST    (BIT(19))
#define SLCHOST_SLC0_EXT_BIT0_INT_ST_M  (SLCHOST_SLC0_EXT_BIT0_INT_ST_V << SLCHOST_SLC0_EXT_BIT0_INT_ST_S)
#define SLCHOST_SLC0_EXT_BIT0_INT_ST_V  0x00000001U
#define SLCHOST_SLC0_EXT_BIT0_INT_ST_S  19
/** SLCHOST_SLC0_EXT_BIT1_INT_ST : RO; bitpos: [20]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0_EXT_BIT1_INT_ST    (BIT(20))
#define SLCHOST_SLC0_EXT_BIT1_INT_ST_M  (SLCHOST_SLC0_EXT_BIT1_INT_ST_V << SLCHOST_SLC0_EXT_BIT1_INT_ST_S)
#define SLCHOST_SLC0_EXT_BIT1_INT_ST_V  0x00000001U
#define SLCHOST_SLC0_EXT_BIT1_INT_ST_S  20
/** SLCHOST_SLC0_EXT_BIT2_INT_ST : RO; bitpos: [21]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0_EXT_BIT2_INT_ST    (BIT(21))
#define SLCHOST_SLC0_EXT_BIT2_INT_ST_M  (SLCHOST_SLC0_EXT_BIT2_INT_ST_V << SLCHOST_SLC0_EXT_BIT2_INT_ST_S)
#define SLCHOST_SLC0_EXT_BIT2_INT_ST_V  0x00000001U
#define SLCHOST_SLC0_EXT_BIT2_INT_ST_S  21
/** SLCHOST_SLC0_EXT_BIT3_INT_ST : RO; bitpos: [22]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0_EXT_BIT3_INT_ST    (BIT(22))
#define SLCHOST_SLC0_EXT_BIT3_INT_ST_M  (SLCHOST_SLC0_EXT_BIT3_INT_ST_V << SLCHOST_SLC0_EXT_BIT3_INT_ST_S)
#define SLCHOST_SLC0_EXT_BIT3_INT_ST_V  0x00000001U
#define SLCHOST_SLC0_EXT_BIT3_INT_ST_S  22
/** SLCHOST_SLC0_RX_NEW_PACKET_INT_ST : RO; bitpos: [23]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0_RX_NEW_PACKET_INT_ST    (BIT(23))
#define SLCHOST_SLC0_RX_NEW_PACKET_INT_ST_M  (SLCHOST_SLC0_RX_NEW_PACKET_INT_ST_V << SLCHOST_SLC0_RX_NEW_PACKET_INT_ST_S)
#define SLCHOST_SLC0_RX_NEW_PACKET_INT_ST_V  0x00000001U
#define SLCHOST_SLC0_RX_NEW_PACKET_INT_ST_S  23
/** SLCHOST_SLC0_HOST_RD_RETRY_INT_ST : RO; bitpos: [24]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0_HOST_RD_RETRY_INT_ST    (BIT(24))
#define SLCHOST_SLC0_HOST_RD_RETRY_INT_ST_M  (SLCHOST_SLC0_HOST_RD_RETRY_INT_ST_V << SLCHOST_SLC0_HOST_RD_RETRY_INT_ST_S)
#define SLCHOST_SLC0_HOST_RD_RETRY_INT_ST_V  0x00000001U
#define SLCHOST_SLC0_HOST_RD_RETRY_INT_ST_S  24
/** SLCHOST_GPIO_SDIO_INT_ST : RO; bitpos: [25]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_GPIO_SDIO_INT_ST    (BIT(25))
#define SLCHOST_GPIO_SDIO_INT_ST_M  (SLCHOST_GPIO_SDIO_INT_ST_V << SLCHOST_GPIO_SDIO_INT_ST_S)
#define SLCHOST_GPIO_SDIO_INT_ST_V  0x00000001U
#define SLCHOST_GPIO_SDIO_INT_ST_S  25

/** SLCHOST_SLC1HOST_INT_ST_REG register
 *  *******Description***********
 */
#define SLCHOST_SLC1HOST_INT_ST_REG (DR_REG_SLCHOST_BASE + 0x5c)
/** SLCHOST_SLC1_TOHOST_BIT0_INT_ST : RO; bitpos: [0]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1_TOHOST_BIT0_INT_ST    (BIT(0))
#define SLCHOST_SLC1_TOHOST_BIT0_INT_ST_M  (SLCHOST_SLC1_TOHOST_BIT0_INT_ST_V << SLCHOST_SLC1_TOHOST_BIT0_INT_ST_S)
#define SLCHOST_SLC1_TOHOST_BIT0_INT_ST_V  0x00000001U
#define SLCHOST_SLC1_TOHOST_BIT0_INT_ST_S  0
/** SLCHOST_SLC1_TOHOST_BIT1_INT_ST : RO; bitpos: [1]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1_TOHOST_BIT1_INT_ST    (BIT(1))
#define SLCHOST_SLC1_TOHOST_BIT1_INT_ST_M  (SLCHOST_SLC1_TOHOST_BIT1_INT_ST_V << SLCHOST_SLC1_TOHOST_BIT1_INT_ST_S)
#define SLCHOST_SLC1_TOHOST_BIT1_INT_ST_V  0x00000001U
#define SLCHOST_SLC1_TOHOST_BIT1_INT_ST_S  1
/** SLCHOST_SLC1_TOHOST_BIT2_INT_ST : RO; bitpos: [2]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1_TOHOST_BIT2_INT_ST    (BIT(2))
#define SLCHOST_SLC1_TOHOST_BIT2_INT_ST_M  (SLCHOST_SLC1_TOHOST_BIT2_INT_ST_V << SLCHOST_SLC1_TOHOST_BIT2_INT_ST_S)
#define SLCHOST_SLC1_TOHOST_BIT2_INT_ST_V  0x00000001U
#define SLCHOST_SLC1_TOHOST_BIT2_INT_ST_S  2
/** SLCHOST_SLC1_TOHOST_BIT3_INT_ST : RO; bitpos: [3]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1_TOHOST_BIT3_INT_ST    (BIT(3))
#define SLCHOST_SLC1_TOHOST_BIT3_INT_ST_M  (SLCHOST_SLC1_TOHOST_BIT3_INT_ST_V << SLCHOST_SLC1_TOHOST_BIT3_INT_ST_S)
#define SLCHOST_SLC1_TOHOST_BIT3_INT_ST_V  0x00000001U
#define SLCHOST_SLC1_TOHOST_BIT3_INT_ST_S  3
/** SLCHOST_SLC1_TOHOST_BIT4_INT_ST : RO; bitpos: [4]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1_TOHOST_BIT4_INT_ST    (BIT(4))
#define SLCHOST_SLC1_TOHOST_BIT4_INT_ST_M  (SLCHOST_SLC1_TOHOST_BIT4_INT_ST_V << SLCHOST_SLC1_TOHOST_BIT4_INT_ST_S)
#define SLCHOST_SLC1_TOHOST_BIT4_INT_ST_V  0x00000001U
#define SLCHOST_SLC1_TOHOST_BIT4_INT_ST_S  4
/** SLCHOST_SLC1_TOHOST_BIT5_INT_ST : RO; bitpos: [5]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1_TOHOST_BIT5_INT_ST    (BIT(5))
#define SLCHOST_SLC1_TOHOST_BIT5_INT_ST_M  (SLCHOST_SLC1_TOHOST_BIT5_INT_ST_V << SLCHOST_SLC1_TOHOST_BIT5_INT_ST_S)
#define SLCHOST_SLC1_TOHOST_BIT5_INT_ST_V  0x00000001U
#define SLCHOST_SLC1_TOHOST_BIT5_INT_ST_S  5
/** SLCHOST_SLC1_TOHOST_BIT6_INT_ST : RO; bitpos: [6]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1_TOHOST_BIT6_INT_ST    (BIT(6))
#define SLCHOST_SLC1_TOHOST_BIT6_INT_ST_M  (SLCHOST_SLC1_TOHOST_BIT6_INT_ST_V << SLCHOST_SLC1_TOHOST_BIT6_INT_ST_S)
#define SLCHOST_SLC1_TOHOST_BIT6_INT_ST_V  0x00000001U
#define SLCHOST_SLC1_TOHOST_BIT6_INT_ST_S  6
/** SLCHOST_SLC1_TOHOST_BIT7_INT_ST : RO; bitpos: [7]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1_TOHOST_BIT7_INT_ST    (BIT(7))
#define SLCHOST_SLC1_TOHOST_BIT7_INT_ST_M  (SLCHOST_SLC1_TOHOST_BIT7_INT_ST_V << SLCHOST_SLC1_TOHOST_BIT7_INT_ST_S)
#define SLCHOST_SLC1_TOHOST_BIT7_INT_ST_V  0x00000001U
#define SLCHOST_SLC1_TOHOST_BIT7_INT_ST_S  7
/** SLCHOST_SLC1_TOKEN0_1TO0_INT_ST : RO; bitpos: [8]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1_TOKEN0_1TO0_INT_ST    (BIT(8))
#define SLCHOST_SLC1_TOKEN0_1TO0_INT_ST_M  (SLCHOST_SLC1_TOKEN0_1TO0_INT_ST_V << SLCHOST_SLC1_TOKEN0_1TO0_INT_ST_S)
#define SLCHOST_SLC1_TOKEN0_1TO0_INT_ST_V  0x00000001U
#define SLCHOST_SLC1_TOKEN0_1TO0_INT_ST_S  8
/** SLCHOST_SLC1_TOKEN1_1TO0_INT_ST : RO; bitpos: [9]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1_TOKEN1_1TO0_INT_ST    (BIT(9))
#define SLCHOST_SLC1_TOKEN1_1TO0_INT_ST_M  (SLCHOST_SLC1_TOKEN1_1TO0_INT_ST_V << SLCHOST_SLC1_TOKEN1_1TO0_INT_ST_S)
#define SLCHOST_SLC1_TOKEN1_1TO0_INT_ST_V  0x00000001U
#define SLCHOST_SLC1_TOKEN1_1TO0_INT_ST_S  9
/** SLCHOST_SLC1_TOKEN0_0TO1_INT_ST : RO; bitpos: [10]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1_TOKEN0_0TO1_INT_ST    (BIT(10))
#define SLCHOST_SLC1_TOKEN0_0TO1_INT_ST_M  (SLCHOST_SLC1_TOKEN0_0TO1_INT_ST_V << SLCHOST_SLC1_TOKEN0_0TO1_INT_ST_S)
#define SLCHOST_SLC1_TOKEN0_0TO1_INT_ST_V  0x00000001U
#define SLCHOST_SLC1_TOKEN0_0TO1_INT_ST_S  10
/** SLCHOST_SLC1_TOKEN1_0TO1_INT_ST : RO; bitpos: [11]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1_TOKEN1_0TO1_INT_ST    (BIT(11))
#define SLCHOST_SLC1_TOKEN1_0TO1_INT_ST_M  (SLCHOST_SLC1_TOKEN1_0TO1_INT_ST_V << SLCHOST_SLC1_TOKEN1_0TO1_INT_ST_S)
#define SLCHOST_SLC1_TOKEN1_0TO1_INT_ST_V  0x00000001U
#define SLCHOST_SLC1_TOKEN1_0TO1_INT_ST_S  11
/** SLCHOST_SLC1HOST_RX_SOF_INT_ST : RO; bitpos: [12]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1HOST_RX_SOF_INT_ST    (BIT(12))
#define SLCHOST_SLC1HOST_RX_SOF_INT_ST_M  (SLCHOST_SLC1HOST_RX_SOF_INT_ST_V << SLCHOST_SLC1HOST_RX_SOF_INT_ST_S)
#define SLCHOST_SLC1HOST_RX_SOF_INT_ST_V  0x00000001U
#define SLCHOST_SLC1HOST_RX_SOF_INT_ST_S  12
/** SLCHOST_SLC1HOST_RX_EOF_INT_ST : RO; bitpos: [13]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1HOST_RX_EOF_INT_ST    (BIT(13))
#define SLCHOST_SLC1HOST_RX_EOF_INT_ST_M  (SLCHOST_SLC1HOST_RX_EOF_INT_ST_V << SLCHOST_SLC1HOST_RX_EOF_INT_ST_S)
#define SLCHOST_SLC1HOST_RX_EOF_INT_ST_V  0x00000001U
#define SLCHOST_SLC1HOST_RX_EOF_INT_ST_S  13
/** SLCHOST_SLC1HOST_RX_START_INT_ST : RO; bitpos: [14]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1HOST_RX_START_INT_ST    (BIT(14))
#define SLCHOST_SLC1HOST_RX_START_INT_ST_M  (SLCHOST_SLC1HOST_RX_START_INT_ST_V << SLCHOST_SLC1HOST_RX_START_INT_ST_S)
#define SLCHOST_SLC1HOST_RX_START_INT_ST_V  0x00000001U
#define SLCHOST_SLC1HOST_RX_START_INT_ST_S  14
/** SLCHOST_SLC1HOST_TX_START_INT_ST : RO; bitpos: [15]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1HOST_TX_START_INT_ST    (BIT(15))
#define SLCHOST_SLC1HOST_TX_START_INT_ST_M  (SLCHOST_SLC1HOST_TX_START_INT_ST_V << SLCHOST_SLC1HOST_TX_START_INT_ST_S)
#define SLCHOST_SLC1HOST_TX_START_INT_ST_V  0x00000001U
#define SLCHOST_SLC1HOST_TX_START_INT_ST_S  15
/** SLCHOST_SLC1_RX_UDF_INT_ST : RO; bitpos: [16]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1_RX_UDF_INT_ST    (BIT(16))
#define SLCHOST_SLC1_RX_UDF_INT_ST_M  (SLCHOST_SLC1_RX_UDF_INT_ST_V << SLCHOST_SLC1_RX_UDF_INT_ST_S)
#define SLCHOST_SLC1_RX_UDF_INT_ST_V  0x00000001U
#define SLCHOST_SLC1_RX_UDF_INT_ST_S  16
/** SLCHOST_SLC1_TX_OVF_INT_ST : RO; bitpos: [17]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1_TX_OVF_INT_ST    (BIT(17))
#define SLCHOST_SLC1_TX_OVF_INT_ST_M  (SLCHOST_SLC1_TX_OVF_INT_ST_V << SLCHOST_SLC1_TX_OVF_INT_ST_S)
#define SLCHOST_SLC1_TX_OVF_INT_ST_V  0x00000001U
#define SLCHOST_SLC1_TX_OVF_INT_ST_S  17
/** SLCHOST_SLC1_RX_PF_VALID_INT_ST : RO; bitpos: [18]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1_RX_PF_VALID_INT_ST    (BIT(18))
#define SLCHOST_SLC1_RX_PF_VALID_INT_ST_M  (SLCHOST_SLC1_RX_PF_VALID_INT_ST_V << SLCHOST_SLC1_RX_PF_VALID_INT_ST_S)
#define SLCHOST_SLC1_RX_PF_VALID_INT_ST_V  0x00000001U
#define SLCHOST_SLC1_RX_PF_VALID_INT_ST_S  18
/** SLCHOST_SLC1_EXT_BIT0_INT_ST : RO; bitpos: [19]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1_EXT_BIT0_INT_ST    (BIT(19))
#define SLCHOST_SLC1_EXT_BIT0_INT_ST_M  (SLCHOST_SLC1_EXT_BIT0_INT_ST_V << SLCHOST_SLC1_EXT_BIT0_INT_ST_S)
#define SLCHOST_SLC1_EXT_BIT0_INT_ST_V  0x00000001U
#define SLCHOST_SLC1_EXT_BIT0_INT_ST_S  19
/** SLCHOST_SLC1_EXT_BIT1_INT_ST : RO; bitpos: [20]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1_EXT_BIT1_INT_ST    (BIT(20))
#define SLCHOST_SLC1_EXT_BIT1_INT_ST_M  (SLCHOST_SLC1_EXT_BIT1_INT_ST_V << SLCHOST_SLC1_EXT_BIT1_INT_ST_S)
#define SLCHOST_SLC1_EXT_BIT1_INT_ST_V  0x00000001U
#define SLCHOST_SLC1_EXT_BIT1_INT_ST_S  20
/** SLCHOST_SLC1_EXT_BIT2_INT_ST : RO; bitpos: [21]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1_EXT_BIT2_INT_ST    (BIT(21))
#define SLCHOST_SLC1_EXT_BIT2_INT_ST_M  (SLCHOST_SLC1_EXT_BIT2_INT_ST_V << SLCHOST_SLC1_EXT_BIT2_INT_ST_S)
#define SLCHOST_SLC1_EXT_BIT2_INT_ST_V  0x00000001U
#define SLCHOST_SLC1_EXT_BIT2_INT_ST_S  21
/** SLCHOST_SLC1_EXT_BIT3_INT_ST : RO; bitpos: [22]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1_EXT_BIT3_INT_ST    (BIT(22))
#define SLCHOST_SLC1_EXT_BIT3_INT_ST_M  (SLCHOST_SLC1_EXT_BIT3_INT_ST_V << SLCHOST_SLC1_EXT_BIT3_INT_ST_S)
#define SLCHOST_SLC1_EXT_BIT3_INT_ST_V  0x00000001U
#define SLCHOST_SLC1_EXT_BIT3_INT_ST_S  22
/** SLCHOST_SLC1_WIFI_RX_NEW_PACKET_INT_ST : RO; bitpos: [23]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1_WIFI_RX_NEW_PACKET_INT_ST    (BIT(23))
#define SLCHOST_SLC1_WIFI_RX_NEW_PACKET_INT_ST_M  (SLCHOST_SLC1_WIFI_RX_NEW_PACKET_INT_ST_V << SLCHOST_SLC1_WIFI_RX_NEW_PACKET_INT_ST_S)
#define SLCHOST_SLC1_WIFI_RX_NEW_PACKET_INT_ST_V  0x00000001U
#define SLCHOST_SLC1_WIFI_RX_NEW_PACKET_INT_ST_S  23
/** SLCHOST_SLC1_HOST_RD_RETRY_INT_ST : RO; bitpos: [24]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1_HOST_RD_RETRY_INT_ST    (BIT(24))
#define SLCHOST_SLC1_HOST_RD_RETRY_INT_ST_M  (SLCHOST_SLC1_HOST_RD_RETRY_INT_ST_V << SLCHOST_SLC1_HOST_RD_RETRY_INT_ST_S)
#define SLCHOST_SLC1_HOST_RD_RETRY_INT_ST_V  0x00000001U
#define SLCHOST_SLC1_HOST_RD_RETRY_INT_ST_S  24
/** SLCHOST_SLC1_BT_RX_NEW_PACKET_INT_ST : RO; bitpos: [25]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1_BT_RX_NEW_PACKET_INT_ST    (BIT(25))
#define SLCHOST_SLC1_BT_RX_NEW_PACKET_INT_ST_M  (SLCHOST_SLC1_BT_RX_NEW_PACKET_INT_ST_V << SLCHOST_SLC1_BT_RX_NEW_PACKET_INT_ST_S)
#define SLCHOST_SLC1_BT_RX_NEW_PACKET_INT_ST_V  0x00000001U
#define SLCHOST_SLC1_BT_RX_NEW_PACKET_INT_ST_S  25

/** SLCHOST_PKT_LEN_REG register
 *  *******Description***********
 */
#define SLCHOST_PKT_LEN_REG (DR_REG_SLCHOST_BASE + 0x60)
/** SLCHOST_HOSTSLCHOST_SLC0_LEN : RO; bitpos: [19:0]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_HOSTSLCHOST_SLC0_LEN    0x000FFFFFU
#define SLCHOST_HOSTSLCHOST_SLC0_LEN_M  (SLCHOST_HOSTSLCHOST_SLC0_LEN_V << SLCHOST_HOSTSLCHOST_SLC0_LEN_S)
#define SLCHOST_HOSTSLCHOST_SLC0_LEN_V  0x000FFFFFU
#define SLCHOST_HOSTSLCHOST_SLC0_LEN_S  0
/** SLCHOST_HOSTSLCHOST_SLC0_LEN_CHECK : RO; bitpos: [31:20]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_HOSTSLCHOST_SLC0_LEN_CHECK    0x00000FFFU
#define SLCHOST_HOSTSLCHOST_SLC0_LEN_CHECK_M  (SLCHOST_HOSTSLCHOST_SLC0_LEN_CHECK_V << SLCHOST_HOSTSLCHOST_SLC0_LEN_CHECK_S)
#define SLCHOST_HOSTSLCHOST_SLC0_LEN_CHECK_V  0x00000FFFU
#define SLCHOST_HOSTSLCHOST_SLC0_LEN_CHECK_S  20

/** SLCHOST_STATE_W0_REG register
 *  *******Description***********
 */
#define SLCHOST_STATE_W0_REG (DR_REG_SLCHOST_BASE + 0x64)
/** SLCHOST_SLCHOST_STATE0 : RO; bitpos: [7:0]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLCHOST_STATE0    0x000000FFU
#define SLCHOST_SLCHOST_STATE0_M  (SLCHOST_SLCHOST_STATE0_V << SLCHOST_SLCHOST_STATE0_S)
#define SLCHOST_SLCHOST_STATE0_V  0x000000FFU
#define SLCHOST_SLCHOST_STATE0_S  0
/** SLCHOST_SLCHOST_STATE1 : RO; bitpos: [15:8]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLCHOST_STATE1    0x000000FFU
#define SLCHOST_SLCHOST_STATE1_M  (SLCHOST_SLCHOST_STATE1_V << SLCHOST_SLCHOST_STATE1_S)
#define SLCHOST_SLCHOST_STATE1_V  0x000000FFU
#define SLCHOST_SLCHOST_STATE1_S  8
/** SLCHOST_SLCHOST_STATE2 : RO; bitpos: [23:16]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLCHOST_STATE2    0x000000FFU
#define SLCHOST_SLCHOST_STATE2_M  (SLCHOST_SLCHOST_STATE2_V << SLCHOST_SLCHOST_STATE2_S)
#define SLCHOST_SLCHOST_STATE2_V  0x000000FFU
#define SLCHOST_SLCHOST_STATE2_S  16
/** SLCHOST_SLCHOST_STATE3 : RO; bitpos: [31:24]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLCHOST_STATE3    0x000000FFU
#define SLCHOST_SLCHOST_STATE3_M  (SLCHOST_SLCHOST_STATE3_V << SLCHOST_SLCHOST_STATE3_S)
#define SLCHOST_SLCHOST_STATE3_V  0x000000FFU
#define SLCHOST_SLCHOST_STATE3_S  24

/** SLCHOST_STATE_W1_REG register
 *  *******Description***********
 */
#define SLCHOST_STATE_W1_REG (DR_REG_SLCHOST_BASE + 0x68)
/** SLCHOST_SLCHOST_STATE4 : RO; bitpos: [7:0]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLCHOST_STATE4    0x000000FFU
#define SLCHOST_SLCHOST_STATE4_M  (SLCHOST_SLCHOST_STATE4_V << SLCHOST_SLCHOST_STATE4_S)
#define SLCHOST_SLCHOST_STATE4_V  0x000000FFU
#define SLCHOST_SLCHOST_STATE4_S  0
/** SLCHOST_SLCHOST_STATE5 : RO; bitpos: [15:8]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLCHOST_STATE5    0x000000FFU
#define SLCHOST_SLCHOST_STATE5_M  (SLCHOST_SLCHOST_STATE5_V << SLCHOST_SLCHOST_STATE5_S)
#define SLCHOST_SLCHOST_STATE5_V  0x000000FFU
#define SLCHOST_SLCHOST_STATE5_S  8
/** SLCHOST_SLCHOST_STATE6 : RO; bitpos: [23:16]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLCHOST_STATE6    0x000000FFU
#define SLCHOST_SLCHOST_STATE6_M  (SLCHOST_SLCHOST_STATE6_V << SLCHOST_SLCHOST_STATE6_S)
#define SLCHOST_SLCHOST_STATE6_V  0x000000FFU
#define SLCHOST_SLCHOST_STATE6_S  16
/** SLCHOST_SLCHOST_STATE7 : RO; bitpos: [31:24]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLCHOST_STATE7    0x000000FFU
#define SLCHOST_SLCHOST_STATE7_M  (SLCHOST_SLCHOST_STATE7_V << SLCHOST_SLCHOST_STATE7_S)
#define SLCHOST_SLCHOST_STATE7_V  0x000000FFU
#define SLCHOST_SLCHOST_STATE7_S  24

/** SLCHOST_CONF_W0_REG register
 *  *******Description***********
 */
#define SLCHOST_CONF_W0_REG (DR_REG_SLCHOST_BASE + 0x6c)
/** SLCHOST_SLCHOST_CONF0 : R/W; bitpos: [7:0]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLCHOST_CONF0    0x000000FFU
#define SLCHOST_SLCHOST_CONF0_M  (SLCHOST_SLCHOST_CONF0_V << SLCHOST_SLCHOST_CONF0_S)
#define SLCHOST_SLCHOST_CONF0_V  0x000000FFU
#define SLCHOST_SLCHOST_CONF0_S  0
/** SLCHOST_SLCHOST_CONF1 : R/W; bitpos: [15:8]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLCHOST_CONF1    0x000000FFU
#define SLCHOST_SLCHOST_CONF1_M  (SLCHOST_SLCHOST_CONF1_V << SLCHOST_SLCHOST_CONF1_S)
#define SLCHOST_SLCHOST_CONF1_V  0x000000FFU
#define SLCHOST_SLCHOST_CONF1_S  8
/** SLCHOST_SLCHOST_CONF2 : R/W; bitpos: [23:16]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLCHOST_CONF2    0x000000FFU
#define SLCHOST_SLCHOST_CONF2_M  (SLCHOST_SLCHOST_CONF2_V << SLCHOST_SLCHOST_CONF2_S)
#define SLCHOST_SLCHOST_CONF2_V  0x000000FFU
#define SLCHOST_SLCHOST_CONF2_S  16
/** SLCHOST_SLCHOST_CONF3 : R/W; bitpos: [31:24]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLCHOST_CONF3    0x000000FFU
#define SLCHOST_SLCHOST_CONF3_M  (SLCHOST_SLCHOST_CONF3_V << SLCHOST_SLCHOST_CONF3_S)
#define SLCHOST_SLCHOST_CONF3_V  0x000000FFU
#define SLCHOST_SLCHOST_CONF3_S  24

/** SLCHOST_CONF_W1_REG register
 *  *******Description***********
 */
#define SLCHOST_CONF_W1_REG (DR_REG_SLCHOST_BASE + 0x70)
/** SLCHOST_SLCHOST_CONF4 : R/W; bitpos: [7:0]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLCHOST_CONF4    0x000000FFU
#define SLCHOST_SLCHOST_CONF4_M  (SLCHOST_SLCHOST_CONF4_V << SLCHOST_SLCHOST_CONF4_S)
#define SLCHOST_SLCHOST_CONF4_V  0x000000FFU
#define SLCHOST_SLCHOST_CONF4_S  0
/** SLCHOST_SLCHOST_CONF5 : R/W; bitpos: [15:8]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLCHOST_CONF5    0x000000FFU
#define SLCHOST_SLCHOST_CONF5_M  (SLCHOST_SLCHOST_CONF5_V << SLCHOST_SLCHOST_CONF5_S)
#define SLCHOST_SLCHOST_CONF5_V  0x000000FFU
#define SLCHOST_SLCHOST_CONF5_S  8
/** SLCHOST_SLCHOST_CONF6 : R/W; bitpos: [23:16]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLCHOST_CONF6    0x000000FFU
#define SLCHOST_SLCHOST_CONF6_M  (SLCHOST_SLCHOST_CONF6_V << SLCHOST_SLCHOST_CONF6_S)
#define SLCHOST_SLCHOST_CONF6_V  0x000000FFU
#define SLCHOST_SLCHOST_CONF6_S  16
/** SLCHOST_SLCHOST_CONF7 : R/W; bitpos: [31:24]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLCHOST_CONF7    0x000000FFU
#define SLCHOST_SLCHOST_CONF7_M  (SLCHOST_SLCHOST_CONF7_V << SLCHOST_SLCHOST_CONF7_S)
#define SLCHOST_SLCHOST_CONF7_V  0x000000FFU
#define SLCHOST_SLCHOST_CONF7_S  24

/** SLCHOST_CONF_W2_REG register
 *  *******Description***********
 */
#define SLCHOST_CONF_W2_REG (DR_REG_SLCHOST_BASE + 0x74)
/** SLCHOST_SLCHOST_CONF8 : R/W; bitpos: [7:0]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLCHOST_CONF8    0x000000FFU
#define SLCHOST_SLCHOST_CONF8_M  (SLCHOST_SLCHOST_CONF8_V << SLCHOST_SLCHOST_CONF8_S)
#define SLCHOST_SLCHOST_CONF8_V  0x000000FFU
#define SLCHOST_SLCHOST_CONF8_S  0
/** SLCHOST_SLCHOST_CONF9 : R/W; bitpos: [15:8]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLCHOST_CONF9    0x000000FFU
#define SLCHOST_SLCHOST_CONF9_M  (SLCHOST_SLCHOST_CONF9_V << SLCHOST_SLCHOST_CONF9_S)
#define SLCHOST_SLCHOST_CONF9_V  0x000000FFU
#define SLCHOST_SLCHOST_CONF9_S  8
/** SLCHOST_SLCHOST_CONF10 : R/W; bitpos: [23:16]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLCHOST_CONF10    0x000000FFU
#define SLCHOST_SLCHOST_CONF10_M  (SLCHOST_SLCHOST_CONF10_V << SLCHOST_SLCHOST_CONF10_S)
#define SLCHOST_SLCHOST_CONF10_V  0x000000FFU
#define SLCHOST_SLCHOST_CONF10_S  16
/** SLCHOST_SLCHOST_CONF11 : R/W; bitpos: [31:24]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLCHOST_CONF11    0x000000FFU
#define SLCHOST_SLCHOST_CONF11_M  (SLCHOST_SLCHOST_CONF11_V << SLCHOST_SLCHOST_CONF11_S)
#define SLCHOST_SLCHOST_CONF11_V  0x000000FFU
#define SLCHOST_SLCHOST_CONF11_S  24

/** SLCHOST_CONF_W3_REG register
 *  *******Description***********
 */
#define SLCHOST_CONF_W3_REG (DR_REG_SLCHOST_BASE + 0x78)
/** SLCHOST_SLCHOST_CONF12 : R/W; bitpos: [7:0]; default: 192;
 *  *******Description***********
 */
#define SLCHOST_SLCHOST_CONF12    0x000000FFU
#define SLCHOST_SLCHOST_CONF12_M  (SLCHOST_SLCHOST_CONF12_V << SLCHOST_SLCHOST_CONF12_S)
#define SLCHOST_SLCHOST_CONF12_V  0x000000FFU
#define SLCHOST_SLCHOST_CONF12_S  0
/** SLCHOST_SLCHOST_CONF13 : R/W; bitpos: [15:8]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLCHOST_CONF13    0x000000FFU
#define SLCHOST_SLCHOST_CONF13_M  (SLCHOST_SLCHOST_CONF13_V << SLCHOST_SLCHOST_CONF13_S)
#define SLCHOST_SLCHOST_CONF13_V  0x000000FFU
#define SLCHOST_SLCHOST_CONF13_S  8
/** SLCHOST_SLCHOST_CONF14 : R/W; bitpos: [23:16]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLCHOST_CONF14    0x000000FFU
#define SLCHOST_SLCHOST_CONF14_M  (SLCHOST_SLCHOST_CONF14_V << SLCHOST_SLCHOST_CONF14_S)
#define SLCHOST_SLCHOST_CONF14_V  0x000000FFU
#define SLCHOST_SLCHOST_CONF14_S  16
/** SLCHOST_SLCHOST_CONF15 : R/W; bitpos: [31:24]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLCHOST_CONF15    0x000000FFU
#define SLCHOST_SLCHOST_CONF15_M  (SLCHOST_SLCHOST_CONF15_V << SLCHOST_SLCHOST_CONF15_S)
#define SLCHOST_SLCHOST_CONF15_V  0x000000FFU
#define SLCHOST_SLCHOST_CONF15_S  24

/** SLCHOST_CONF_W4_REG register
 *  *******Description***********
 */
#define SLCHOST_CONF_W4_REG (DR_REG_SLCHOST_BASE + 0x7c)
/** SLCHOST_SLCHOST_CONF16 : R/W; bitpos: [7:0]; default: 255;
 *  *******Description***********
 */
#define SLCHOST_SLCHOST_CONF16    0x000000FFU
#define SLCHOST_SLCHOST_CONF16_M  (SLCHOST_SLCHOST_CONF16_V << SLCHOST_SLCHOST_CONF16_S)
#define SLCHOST_SLCHOST_CONF16_V  0x000000FFU
#define SLCHOST_SLCHOST_CONF16_S  0
/** SLCHOST_SLCHOST_CONF17 : R/W; bitpos: [15:8]; default: 1;
 *  *******Description***********
 */
#define SLCHOST_SLCHOST_CONF17    0x000000FFU
#define SLCHOST_SLCHOST_CONF17_M  (SLCHOST_SLCHOST_CONF17_V << SLCHOST_SLCHOST_CONF17_S)
#define SLCHOST_SLCHOST_CONF17_V  0x000000FFU
#define SLCHOST_SLCHOST_CONF17_S  8
/** SLCHOST_SLCHOST_CONF18 : R/W; bitpos: [23:16]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLCHOST_CONF18    0x000000FFU
#define SLCHOST_SLCHOST_CONF18_M  (SLCHOST_SLCHOST_CONF18_V << SLCHOST_SLCHOST_CONF18_S)
#define SLCHOST_SLCHOST_CONF18_V  0x000000FFU
#define SLCHOST_SLCHOST_CONF18_S  16
/** SLCHOST_SLCHOST_CONF19 : R/W; bitpos: [31:24]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLCHOST_CONF19    0x000000FFU
#define SLCHOST_SLCHOST_CONF19_M  (SLCHOST_SLCHOST_CONF19_V << SLCHOST_SLCHOST_CONF19_S)
#define SLCHOST_SLCHOST_CONF19_V  0x000000FFU
#define SLCHOST_SLCHOST_CONF19_S  24

/** SLCHOST_CONF_W5_REG register
 *  *******Description***********
 */
#define SLCHOST_CONF_W5_REG (DR_REG_SLCHOST_BASE + 0x80)
/** SLCHOST_SLCHOST_CONF20 : R/W; bitpos: [7:0]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLCHOST_CONF20    0x000000FFU
#define SLCHOST_SLCHOST_CONF20_M  (SLCHOST_SLCHOST_CONF20_V << SLCHOST_SLCHOST_CONF20_S)
#define SLCHOST_SLCHOST_CONF20_V  0x000000FFU
#define SLCHOST_SLCHOST_CONF20_S  0
/** SLCHOST_SLCHOST_CONF21 : R/W; bitpos: [15:8]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLCHOST_CONF21    0x000000FFU
#define SLCHOST_SLCHOST_CONF21_M  (SLCHOST_SLCHOST_CONF21_V << SLCHOST_SLCHOST_CONF21_S)
#define SLCHOST_SLCHOST_CONF21_V  0x000000FFU
#define SLCHOST_SLCHOST_CONF21_S  8
/** SLCHOST_SLCHOST_CONF22 : R/W; bitpos: [23:16]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLCHOST_CONF22    0x000000FFU
#define SLCHOST_SLCHOST_CONF22_M  (SLCHOST_SLCHOST_CONF22_V << SLCHOST_SLCHOST_CONF22_S)
#define SLCHOST_SLCHOST_CONF22_V  0x000000FFU
#define SLCHOST_SLCHOST_CONF22_S  16
/** SLCHOST_SLCHOST_CONF23 : R/W; bitpos: [31:24]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLCHOST_CONF23    0x000000FFU
#define SLCHOST_SLCHOST_CONF23_M  (SLCHOST_SLCHOST_CONF23_V << SLCHOST_SLCHOST_CONF23_S)
#define SLCHOST_SLCHOST_CONF23_V  0x000000FFU
#define SLCHOST_SLCHOST_CONF23_S  24

/** SLCHOST_WIN_CMD_REG register
 *  *******Description***********
 */
#define SLCHOST_WIN_CMD_REG (DR_REG_SLCHOST_BASE + 0x84)
/** SLCHOST_SLCHOST_WIN_CMD : R/W; bitpos: [15:0]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLCHOST_WIN_CMD    0x0000FFFFU
#define SLCHOST_SLCHOST_WIN_CMD_M  (SLCHOST_SLCHOST_WIN_CMD_V << SLCHOST_SLCHOST_WIN_CMD_S)
#define SLCHOST_SLCHOST_WIN_CMD_V  0x0000FFFFU
#define SLCHOST_SLCHOST_WIN_CMD_S  0

/** SLCHOST_CONF_W6_REG register
 *  *******Description***********
 */
#define SLCHOST_CONF_W6_REG (DR_REG_SLCHOST_BASE + 0x88)
/** SLCHOST_SLCHOST_CONF24 : R/W; bitpos: [7:0]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLCHOST_CONF24    0x000000FFU
#define SLCHOST_SLCHOST_CONF24_M  (SLCHOST_SLCHOST_CONF24_V << SLCHOST_SLCHOST_CONF24_S)
#define SLCHOST_SLCHOST_CONF24_V  0x000000FFU
#define SLCHOST_SLCHOST_CONF24_S  0
/** SLCHOST_SLCHOST_CONF25 : R/W; bitpos: [15:8]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLCHOST_CONF25    0x000000FFU
#define SLCHOST_SLCHOST_CONF25_M  (SLCHOST_SLCHOST_CONF25_V << SLCHOST_SLCHOST_CONF25_S)
#define SLCHOST_SLCHOST_CONF25_V  0x000000FFU
#define SLCHOST_SLCHOST_CONF25_S  8
/** SLCHOST_SLCHOST_CONF26 : R/W; bitpos: [23:16]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLCHOST_CONF26    0x000000FFU
#define SLCHOST_SLCHOST_CONF26_M  (SLCHOST_SLCHOST_CONF26_V << SLCHOST_SLCHOST_CONF26_S)
#define SLCHOST_SLCHOST_CONF26_V  0x000000FFU
#define SLCHOST_SLCHOST_CONF26_S  16
/** SLCHOST_SLCHOST_CONF27 : R/W; bitpos: [31:24]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLCHOST_CONF27    0x000000FFU
#define SLCHOST_SLCHOST_CONF27_M  (SLCHOST_SLCHOST_CONF27_V << SLCHOST_SLCHOST_CONF27_S)
#define SLCHOST_SLCHOST_CONF27_V  0x000000FFU
#define SLCHOST_SLCHOST_CONF27_S  24

/** SLCHOST_CONF_W7_REG register
 *  *******Description***********
 */
#define SLCHOST_CONF_W7_REG (DR_REG_SLCHOST_BASE + 0x8c)
/** SLCHOST_SLCHOST_CONF28 : R/W; bitpos: [7:0]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLCHOST_CONF28    0x000000FFU
#define SLCHOST_SLCHOST_CONF28_M  (SLCHOST_SLCHOST_CONF28_V << SLCHOST_SLCHOST_CONF28_S)
#define SLCHOST_SLCHOST_CONF28_V  0x000000FFU
#define SLCHOST_SLCHOST_CONF28_S  0
/** SLCHOST_SLCHOST_CONF29 : R/W; bitpos: [15:8]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLCHOST_CONF29    0x000000FFU
#define SLCHOST_SLCHOST_CONF29_M  (SLCHOST_SLCHOST_CONF29_V << SLCHOST_SLCHOST_CONF29_S)
#define SLCHOST_SLCHOST_CONF29_V  0x000000FFU
#define SLCHOST_SLCHOST_CONF29_S  8
/** SLCHOST_SLCHOST_CONF30 : R/W; bitpos: [23:16]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLCHOST_CONF30    0x000000FFU
#define SLCHOST_SLCHOST_CONF30_M  (SLCHOST_SLCHOST_CONF30_V << SLCHOST_SLCHOST_CONF30_S)
#define SLCHOST_SLCHOST_CONF30_V  0x000000FFU
#define SLCHOST_SLCHOST_CONF30_S  16
/** SLCHOST_SLCHOST_CONF31 : R/W; bitpos: [31:24]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLCHOST_CONF31    0x000000FFU
#define SLCHOST_SLCHOST_CONF31_M  (SLCHOST_SLCHOST_CONF31_V << SLCHOST_SLCHOST_CONF31_S)
#define SLCHOST_SLCHOST_CONF31_V  0x000000FFU
#define SLCHOST_SLCHOST_CONF31_S  24

/** SLCHOST_PKT_LEN0_REG register
 *  *******Description***********
 */
#define SLCHOST_PKT_LEN0_REG (DR_REG_SLCHOST_BASE + 0x90)
/** SLCHOST_HOSTSLCHOST_SLC0_LEN0 : RO; bitpos: [19:0]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_HOSTSLCHOST_SLC0_LEN0    0x000FFFFFU
#define SLCHOST_HOSTSLCHOST_SLC0_LEN0_M  (SLCHOST_HOSTSLCHOST_SLC0_LEN0_V << SLCHOST_HOSTSLCHOST_SLC0_LEN0_S)
#define SLCHOST_HOSTSLCHOST_SLC0_LEN0_V  0x000FFFFFU
#define SLCHOST_HOSTSLCHOST_SLC0_LEN0_S  0
/** SLCHOST_HOSTSLCHOST_SLC0_LEN0_CHECK : RO; bitpos: [31:20]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_HOSTSLCHOST_SLC0_LEN0_CHECK    0x00000FFFU
#define SLCHOST_HOSTSLCHOST_SLC0_LEN0_CHECK_M  (SLCHOST_HOSTSLCHOST_SLC0_LEN0_CHECK_V << SLCHOST_HOSTSLCHOST_SLC0_LEN0_CHECK_S)
#define SLCHOST_HOSTSLCHOST_SLC0_LEN0_CHECK_V  0x00000FFFU
#define SLCHOST_HOSTSLCHOST_SLC0_LEN0_CHECK_S  20

/** SLCHOST_PKT_LEN1_REG register
 *  *******Description***********
 */
#define SLCHOST_PKT_LEN1_REG (DR_REG_SLCHOST_BASE + 0x94)
/** SLCHOST_HOSTSLCHOST_SLC0_LEN1 : RO; bitpos: [19:0]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_HOSTSLCHOST_SLC0_LEN1    0x000FFFFFU
#define SLCHOST_HOSTSLCHOST_SLC0_LEN1_M  (SLCHOST_HOSTSLCHOST_SLC0_LEN1_V << SLCHOST_HOSTSLCHOST_SLC0_LEN1_S)
#define SLCHOST_HOSTSLCHOST_SLC0_LEN1_V  0x000FFFFFU
#define SLCHOST_HOSTSLCHOST_SLC0_LEN1_S  0
/** SLCHOST_HOSTSLCHOST_SLC0_LEN1_CHECK : RO; bitpos: [31:20]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_HOSTSLCHOST_SLC0_LEN1_CHECK    0x00000FFFU
#define SLCHOST_HOSTSLCHOST_SLC0_LEN1_CHECK_M  (SLCHOST_HOSTSLCHOST_SLC0_LEN1_CHECK_V << SLCHOST_HOSTSLCHOST_SLC0_LEN1_CHECK_S)
#define SLCHOST_HOSTSLCHOST_SLC0_LEN1_CHECK_V  0x00000FFFU
#define SLCHOST_HOSTSLCHOST_SLC0_LEN1_CHECK_S  20

/** SLCHOST_PKT_LEN2_REG register
 *  *******Description***********
 */
#define SLCHOST_PKT_LEN2_REG (DR_REG_SLCHOST_BASE + 0x98)
/** SLCHOST_HOSTSLCHOST_SLC0_LEN2 : RO; bitpos: [19:0]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_HOSTSLCHOST_SLC0_LEN2    0x000FFFFFU
#define SLCHOST_HOSTSLCHOST_SLC0_LEN2_M  (SLCHOST_HOSTSLCHOST_SLC0_LEN2_V << SLCHOST_HOSTSLCHOST_SLC0_LEN2_S)
#define SLCHOST_HOSTSLCHOST_SLC0_LEN2_V  0x000FFFFFU
#define SLCHOST_HOSTSLCHOST_SLC0_LEN2_S  0
/** SLCHOST_HOSTSLCHOST_SLC0_LEN2_CHECK : RO; bitpos: [31:20]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_HOSTSLCHOST_SLC0_LEN2_CHECK    0x00000FFFU
#define SLCHOST_HOSTSLCHOST_SLC0_LEN2_CHECK_M  (SLCHOST_HOSTSLCHOST_SLC0_LEN2_CHECK_V << SLCHOST_HOSTSLCHOST_SLC0_LEN2_CHECK_S)
#define SLCHOST_HOSTSLCHOST_SLC0_LEN2_CHECK_V  0x00000FFFU
#define SLCHOST_HOSTSLCHOST_SLC0_LEN2_CHECK_S  20

/** SLCHOST_CONF_W8_REG register
 *  *******Description***********
 */
#define SLCHOST_CONF_W8_REG (DR_REG_SLCHOST_BASE + 0x9c)
/** SLCHOST_SLCHOST_CONF32 : R/W; bitpos: [7:0]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLCHOST_CONF32    0x000000FFU
#define SLCHOST_SLCHOST_CONF32_M  (SLCHOST_SLCHOST_CONF32_V << SLCHOST_SLCHOST_CONF32_S)
#define SLCHOST_SLCHOST_CONF32_V  0x000000FFU
#define SLCHOST_SLCHOST_CONF32_S  0
/** SLCHOST_SLCHOST_CONF33 : R/W; bitpos: [15:8]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLCHOST_CONF33    0x000000FFU
#define SLCHOST_SLCHOST_CONF33_M  (SLCHOST_SLCHOST_CONF33_V << SLCHOST_SLCHOST_CONF33_S)
#define SLCHOST_SLCHOST_CONF33_V  0x000000FFU
#define SLCHOST_SLCHOST_CONF33_S  8
/** SLCHOST_SLCHOST_CONF34 : R/W; bitpos: [23:16]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLCHOST_CONF34    0x000000FFU
#define SLCHOST_SLCHOST_CONF34_M  (SLCHOST_SLCHOST_CONF34_V << SLCHOST_SLCHOST_CONF34_S)
#define SLCHOST_SLCHOST_CONF34_V  0x000000FFU
#define SLCHOST_SLCHOST_CONF34_S  16
/** SLCHOST_SLCHOST_CONF35 : R/W; bitpos: [31:24]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLCHOST_CONF35    0x000000FFU
#define SLCHOST_SLCHOST_CONF35_M  (SLCHOST_SLCHOST_CONF35_V << SLCHOST_SLCHOST_CONF35_S)
#define SLCHOST_SLCHOST_CONF35_V  0x000000FFU
#define SLCHOST_SLCHOST_CONF35_S  24

/** SLCHOST_CONF_W9_REG register
 *  *******Description***********
 */
#define SLCHOST_CONF_W9_REG (DR_REG_SLCHOST_BASE + 0xa0)
/** SLCHOST_SLCHOST_CONF36 : R/W; bitpos: [7:0]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLCHOST_CONF36    0x000000FFU
#define SLCHOST_SLCHOST_CONF36_M  (SLCHOST_SLCHOST_CONF36_V << SLCHOST_SLCHOST_CONF36_S)
#define SLCHOST_SLCHOST_CONF36_V  0x000000FFU
#define SLCHOST_SLCHOST_CONF36_S  0
/** SLCHOST_SLCHOST_CONF37 : R/W; bitpos: [15:8]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLCHOST_CONF37    0x000000FFU
#define SLCHOST_SLCHOST_CONF37_M  (SLCHOST_SLCHOST_CONF37_V << SLCHOST_SLCHOST_CONF37_S)
#define SLCHOST_SLCHOST_CONF37_V  0x000000FFU
#define SLCHOST_SLCHOST_CONF37_S  8
/** SLCHOST_SLCHOST_CONF38 : R/W; bitpos: [23:16]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLCHOST_CONF38    0x000000FFU
#define SLCHOST_SLCHOST_CONF38_M  (SLCHOST_SLCHOST_CONF38_V << SLCHOST_SLCHOST_CONF38_S)
#define SLCHOST_SLCHOST_CONF38_V  0x000000FFU
#define SLCHOST_SLCHOST_CONF38_S  16
/** SLCHOST_SLCHOST_CONF39 : R/W; bitpos: [31:24]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLCHOST_CONF39    0x000000FFU
#define SLCHOST_SLCHOST_CONF39_M  (SLCHOST_SLCHOST_CONF39_V << SLCHOST_SLCHOST_CONF39_S)
#define SLCHOST_SLCHOST_CONF39_V  0x000000FFU
#define SLCHOST_SLCHOST_CONF39_S  24

/** SLCHOST_CONF_W10_REG register
 *  *******Description***********
 */
#define SLCHOST_CONF_W10_REG (DR_REG_SLCHOST_BASE + 0xa4)
/** SLCHOST_SLCHOST_CONF40 : R/W; bitpos: [7:0]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLCHOST_CONF40    0x000000FFU
#define SLCHOST_SLCHOST_CONF40_M  (SLCHOST_SLCHOST_CONF40_V << SLCHOST_SLCHOST_CONF40_S)
#define SLCHOST_SLCHOST_CONF40_V  0x000000FFU
#define SLCHOST_SLCHOST_CONF40_S  0
/** SLCHOST_SLCHOST_CONF41 : R/W; bitpos: [15:8]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLCHOST_CONF41    0x000000FFU
#define SLCHOST_SLCHOST_CONF41_M  (SLCHOST_SLCHOST_CONF41_V << SLCHOST_SLCHOST_CONF41_S)
#define SLCHOST_SLCHOST_CONF41_V  0x000000FFU
#define SLCHOST_SLCHOST_CONF41_S  8
/** SLCHOST_SLCHOST_CONF42 : R/W; bitpos: [23:16]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLCHOST_CONF42    0x000000FFU
#define SLCHOST_SLCHOST_CONF42_M  (SLCHOST_SLCHOST_CONF42_V << SLCHOST_SLCHOST_CONF42_S)
#define SLCHOST_SLCHOST_CONF42_V  0x000000FFU
#define SLCHOST_SLCHOST_CONF42_S  16
/** SLCHOST_SLCHOST_CONF43 : R/W; bitpos: [31:24]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLCHOST_CONF43    0x000000FFU
#define SLCHOST_SLCHOST_CONF43_M  (SLCHOST_SLCHOST_CONF43_V << SLCHOST_SLCHOST_CONF43_S)
#define SLCHOST_SLCHOST_CONF43_V  0x000000FFU
#define SLCHOST_SLCHOST_CONF43_S  24

/** SLCHOST_CONF_W11_REG register
 *  *******Description***********
 */
#define SLCHOST_CONF_W11_REG (DR_REG_SLCHOST_BASE + 0xa8)
/** SLCHOST_SLCHOST_CONF44 : R/W; bitpos: [7:0]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLCHOST_CONF44    0x000000FFU
#define SLCHOST_SLCHOST_CONF44_M  (SLCHOST_SLCHOST_CONF44_V << SLCHOST_SLCHOST_CONF44_S)
#define SLCHOST_SLCHOST_CONF44_V  0x000000FFU
#define SLCHOST_SLCHOST_CONF44_S  0
/** SLCHOST_SLCHOST_CONF45 : R/W; bitpos: [15:8]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLCHOST_CONF45    0x000000FFU
#define SLCHOST_SLCHOST_CONF45_M  (SLCHOST_SLCHOST_CONF45_V << SLCHOST_SLCHOST_CONF45_S)
#define SLCHOST_SLCHOST_CONF45_V  0x000000FFU
#define SLCHOST_SLCHOST_CONF45_S  8
/** SLCHOST_SLCHOST_CONF46 : R/W; bitpos: [23:16]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLCHOST_CONF46    0x000000FFU
#define SLCHOST_SLCHOST_CONF46_M  (SLCHOST_SLCHOST_CONF46_V << SLCHOST_SLCHOST_CONF46_S)
#define SLCHOST_SLCHOST_CONF46_V  0x000000FFU
#define SLCHOST_SLCHOST_CONF46_S  16
/** SLCHOST_SLCHOST_CONF47 : R/W; bitpos: [31:24]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLCHOST_CONF47    0x000000FFU
#define SLCHOST_SLCHOST_CONF47_M  (SLCHOST_SLCHOST_CONF47_V << SLCHOST_SLCHOST_CONF47_S)
#define SLCHOST_SLCHOST_CONF47_V  0x000000FFU
#define SLCHOST_SLCHOST_CONF47_S  24

/** SLCHOST_CONF_W12_REG register
 *  *******Description***********
 */
#define SLCHOST_CONF_W12_REG (DR_REG_SLCHOST_BASE + 0xac)
/** SLCHOST_SLCHOST_CONF48 : R/W; bitpos: [7:0]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLCHOST_CONF48    0x000000FFU
#define SLCHOST_SLCHOST_CONF48_M  (SLCHOST_SLCHOST_CONF48_V << SLCHOST_SLCHOST_CONF48_S)
#define SLCHOST_SLCHOST_CONF48_V  0x000000FFU
#define SLCHOST_SLCHOST_CONF48_S  0
/** SLCHOST_SLCHOST_CONF49 : R/W; bitpos: [15:8]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLCHOST_CONF49    0x000000FFU
#define SLCHOST_SLCHOST_CONF49_M  (SLCHOST_SLCHOST_CONF49_V << SLCHOST_SLCHOST_CONF49_S)
#define SLCHOST_SLCHOST_CONF49_V  0x000000FFU
#define SLCHOST_SLCHOST_CONF49_S  8
/** SLCHOST_SLCHOST_CONF50 : R/W; bitpos: [23:16]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLCHOST_CONF50    0x000000FFU
#define SLCHOST_SLCHOST_CONF50_M  (SLCHOST_SLCHOST_CONF50_V << SLCHOST_SLCHOST_CONF50_S)
#define SLCHOST_SLCHOST_CONF50_V  0x000000FFU
#define SLCHOST_SLCHOST_CONF50_S  16
/** SLCHOST_SLCHOST_CONF51 : R/W; bitpos: [31:24]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLCHOST_CONF51    0x000000FFU
#define SLCHOST_SLCHOST_CONF51_M  (SLCHOST_SLCHOST_CONF51_V << SLCHOST_SLCHOST_CONF51_S)
#define SLCHOST_SLCHOST_CONF51_V  0x000000FFU
#define SLCHOST_SLCHOST_CONF51_S  24

/** SLCHOST_CONF_W13_REG register
 *  *******Description***********
 */
#define SLCHOST_CONF_W13_REG (DR_REG_SLCHOST_BASE + 0xb0)
/** SLCHOST_SLCHOST_CONF52 : R/W; bitpos: [7:0]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLCHOST_CONF52    0x000000FFU
#define SLCHOST_SLCHOST_CONF52_M  (SLCHOST_SLCHOST_CONF52_V << SLCHOST_SLCHOST_CONF52_S)
#define SLCHOST_SLCHOST_CONF52_V  0x000000FFU
#define SLCHOST_SLCHOST_CONF52_S  0
/** SLCHOST_SLCHOST_CONF53 : R/W; bitpos: [15:8]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLCHOST_CONF53    0x000000FFU
#define SLCHOST_SLCHOST_CONF53_M  (SLCHOST_SLCHOST_CONF53_V << SLCHOST_SLCHOST_CONF53_S)
#define SLCHOST_SLCHOST_CONF53_V  0x000000FFU
#define SLCHOST_SLCHOST_CONF53_S  8
/** SLCHOST_SLCHOST_CONF54 : R/W; bitpos: [23:16]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLCHOST_CONF54    0x000000FFU
#define SLCHOST_SLCHOST_CONF54_M  (SLCHOST_SLCHOST_CONF54_V << SLCHOST_SLCHOST_CONF54_S)
#define SLCHOST_SLCHOST_CONF54_V  0x000000FFU
#define SLCHOST_SLCHOST_CONF54_S  16
/** SLCHOST_SLCHOST_CONF55 : R/W; bitpos: [31:24]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLCHOST_CONF55    0x000000FFU
#define SLCHOST_SLCHOST_CONF55_M  (SLCHOST_SLCHOST_CONF55_V << SLCHOST_SLCHOST_CONF55_S)
#define SLCHOST_SLCHOST_CONF55_V  0x000000FFU
#define SLCHOST_SLCHOST_CONF55_S  24

/** SLCHOST_CONF_W14_REG register
 *  *******Description***********
 */
#define SLCHOST_CONF_W14_REG (DR_REG_SLCHOST_BASE + 0xb4)
/** SLCHOST_SLCHOST_CONF56 : R/W; bitpos: [7:0]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLCHOST_CONF56    0x000000FFU
#define SLCHOST_SLCHOST_CONF56_M  (SLCHOST_SLCHOST_CONF56_V << SLCHOST_SLCHOST_CONF56_S)
#define SLCHOST_SLCHOST_CONF56_V  0x000000FFU
#define SLCHOST_SLCHOST_CONF56_S  0
/** SLCHOST_SLCHOST_CONF57 : R/W; bitpos: [15:8]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLCHOST_CONF57    0x000000FFU
#define SLCHOST_SLCHOST_CONF57_M  (SLCHOST_SLCHOST_CONF57_V << SLCHOST_SLCHOST_CONF57_S)
#define SLCHOST_SLCHOST_CONF57_V  0x000000FFU
#define SLCHOST_SLCHOST_CONF57_S  8
/** SLCHOST_SLCHOST_CONF58 : R/W; bitpos: [23:16]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLCHOST_CONF58    0x000000FFU
#define SLCHOST_SLCHOST_CONF58_M  (SLCHOST_SLCHOST_CONF58_V << SLCHOST_SLCHOST_CONF58_S)
#define SLCHOST_SLCHOST_CONF58_V  0x000000FFU
#define SLCHOST_SLCHOST_CONF58_S  16
/** SLCHOST_SLCHOST_CONF59 : R/W; bitpos: [31:24]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLCHOST_CONF59    0x000000FFU
#define SLCHOST_SLCHOST_CONF59_M  (SLCHOST_SLCHOST_CONF59_V << SLCHOST_SLCHOST_CONF59_S)
#define SLCHOST_SLCHOST_CONF59_V  0x000000FFU
#define SLCHOST_SLCHOST_CONF59_S  24

/** SLCHOST_CONF_W15_REG register
 *  *******Description***********
 */
#define SLCHOST_CONF_W15_REG (DR_REG_SLCHOST_BASE + 0xb8)
/** SLCHOST_SLCHOST_CONF60 : R/W; bitpos: [7:0]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLCHOST_CONF60    0x000000FFU
#define SLCHOST_SLCHOST_CONF60_M  (SLCHOST_SLCHOST_CONF60_V << SLCHOST_SLCHOST_CONF60_S)
#define SLCHOST_SLCHOST_CONF60_V  0x000000FFU
#define SLCHOST_SLCHOST_CONF60_S  0
/** SLCHOST_SLCHOST_CONF61 : R/W; bitpos: [15:8]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLCHOST_CONF61    0x000000FFU
#define SLCHOST_SLCHOST_CONF61_M  (SLCHOST_SLCHOST_CONF61_V << SLCHOST_SLCHOST_CONF61_S)
#define SLCHOST_SLCHOST_CONF61_V  0x000000FFU
#define SLCHOST_SLCHOST_CONF61_S  8
/** SLCHOST_SLCHOST_CONF62 : R/W; bitpos: [23:16]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLCHOST_CONF62    0x000000FFU
#define SLCHOST_SLCHOST_CONF62_M  (SLCHOST_SLCHOST_CONF62_V << SLCHOST_SLCHOST_CONF62_S)
#define SLCHOST_SLCHOST_CONF62_V  0x000000FFU
#define SLCHOST_SLCHOST_CONF62_S  16
/** SLCHOST_SLCHOST_CONF63 : R/W; bitpos: [31:24]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLCHOST_CONF63    0x000000FFU
#define SLCHOST_SLCHOST_CONF63_M  (SLCHOST_SLCHOST_CONF63_V << SLCHOST_SLCHOST_CONF63_S)
#define SLCHOST_SLCHOST_CONF63_V  0x000000FFU
#define SLCHOST_SLCHOST_CONF63_S  24

/** SLCHOST_CHECK_SUM0_REG register
 *  *******Description***********
 */
#define SLCHOST_CHECK_SUM0_REG (DR_REG_SLCHOST_BASE + 0xbc)
/** SLCHOST_SLCHOST_CHECK_SUM0 : RO; bitpos: [31:0]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLCHOST_CHECK_SUM0    0xFFFFFFFFU
#define SLCHOST_SLCHOST_CHECK_SUM0_M  (SLCHOST_SLCHOST_CHECK_SUM0_V << SLCHOST_SLCHOST_CHECK_SUM0_S)
#define SLCHOST_SLCHOST_CHECK_SUM0_V  0xFFFFFFFFU
#define SLCHOST_SLCHOST_CHECK_SUM0_S  0

/** SLCHOST_CHECK_SUM1_REG register
 *  *******Description***********
 */
#define SLCHOST_CHECK_SUM1_REG (DR_REG_SLCHOST_BASE + 0xc0)
/** SLCHOST_SLCHOST_CHECK_SUM1 : RO; bitpos: [31:0]; default: 319;
 *  *******Description***********
 */
#define SLCHOST_SLCHOST_CHECK_SUM1    0xFFFFFFFFU
#define SLCHOST_SLCHOST_CHECK_SUM1_M  (SLCHOST_SLCHOST_CHECK_SUM1_V << SLCHOST_SLCHOST_CHECK_SUM1_S)
#define SLCHOST_SLCHOST_CHECK_SUM1_V  0xFFFFFFFFU
#define SLCHOST_SLCHOST_CHECK_SUM1_S  0

/** SLCHOST_SLC1HOST_TOKEN_RDATA_REG register
 *  *******Description***********
 */
#define SLCHOST_SLC1HOST_TOKEN_RDATA_REG (DR_REG_SLCHOST_BASE + 0xc4)
/** SLCHOST_SLC1_TOKEN0 : RO; bitpos: [11:0]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1_TOKEN0    0x00000FFFU
#define SLCHOST_SLC1_TOKEN0_M  (SLCHOST_SLC1_TOKEN0_V << SLCHOST_SLC1_TOKEN0_S)
#define SLCHOST_SLC1_TOKEN0_V  0x00000FFFU
#define SLCHOST_SLC1_TOKEN0_S  0
/** SLCHOST_SLC1_RX_PF_VALID : RO; bitpos: [12]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1_RX_PF_VALID    (BIT(12))
#define SLCHOST_SLC1_RX_PF_VALID_M  (SLCHOST_SLC1_RX_PF_VALID_V << SLCHOST_SLC1_RX_PF_VALID_S)
#define SLCHOST_SLC1_RX_PF_VALID_V  0x00000001U
#define SLCHOST_SLC1_RX_PF_VALID_S  12
/** SLCHOST_HOSTSLCHOST_SLC1_TOKEN1 : RO; bitpos: [27:16]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_HOSTSLCHOST_SLC1_TOKEN1    0x00000FFFU
#define SLCHOST_HOSTSLCHOST_SLC1_TOKEN1_M  (SLCHOST_HOSTSLCHOST_SLC1_TOKEN1_V << SLCHOST_HOSTSLCHOST_SLC1_TOKEN1_S)
#define SLCHOST_HOSTSLCHOST_SLC1_TOKEN1_V  0x00000FFFU
#define SLCHOST_HOSTSLCHOST_SLC1_TOKEN1_S  16
/** SLCHOST_SLC1_RX_PF_EOF : RO; bitpos: [31:28]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1_RX_PF_EOF    0x0000000FU
#define SLCHOST_SLC1_RX_PF_EOF_M  (SLCHOST_SLC1_RX_PF_EOF_V << SLCHOST_SLC1_RX_PF_EOF_S)
#define SLCHOST_SLC1_RX_PF_EOF_V  0x0000000FU
#define SLCHOST_SLC1_RX_PF_EOF_S  28

/** SLCHOST_SLC0HOST_TOKEN_WDATA_REG register
 *  *******Description***********
 */
#define SLCHOST_SLC0HOST_TOKEN_WDATA_REG (DR_REG_SLCHOST_BASE + 0xc8)
/** SLCHOST_SLC0HOST_TOKEN0_WD : R/W; bitpos: [11:0]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0HOST_TOKEN0_WD    0x00000FFFU
#define SLCHOST_SLC0HOST_TOKEN0_WD_M  (SLCHOST_SLC0HOST_TOKEN0_WD_V << SLCHOST_SLC0HOST_TOKEN0_WD_S)
#define SLCHOST_SLC0HOST_TOKEN0_WD_V  0x00000FFFU
#define SLCHOST_SLC0HOST_TOKEN0_WD_S  0
/** SLCHOST_SLC0HOST_TOKEN1_WD : R/W; bitpos: [27:16]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0HOST_TOKEN1_WD    0x00000FFFU
#define SLCHOST_SLC0HOST_TOKEN1_WD_M  (SLCHOST_SLC0HOST_TOKEN1_WD_V << SLCHOST_SLC0HOST_TOKEN1_WD_S)
#define SLCHOST_SLC0HOST_TOKEN1_WD_V  0x00000FFFU
#define SLCHOST_SLC0HOST_TOKEN1_WD_S  16

/** SLCHOST_SLC1HOST_TOKEN_WDATA_REG register
 *  *******Description***********
 */
#define SLCHOST_SLC1HOST_TOKEN_WDATA_REG (DR_REG_SLCHOST_BASE + 0xcc)
/** SLCHOST_SLC1HOST_TOKEN0_WD : R/W; bitpos: [11:0]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1HOST_TOKEN0_WD    0x00000FFFU
#define SLCHOST_SLC1HOST_TOKEN0_WD_M  (SLCHOST_SLC1HOST_TOKEN0_WD_V << SLCHOST_SLC1HOST_TOKEN0_WD_S)
#define SLCHOST_SLC1HOST_TOKEN0_WD_V  0x00000FFFU
#define SLCHOST_SLC1HOST_TOKEN0_WD_S  0
/** SLCHOST_SLC1HOST_TOKEN1_WD : R/W; bitpos: [27:16]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1HOST_TOKEN1_WD    0x00000FFFU
#define SLCHOST_SLC1HOST_TOKEN1_WD_M  (SLCHOST_SLC1HOST_TOKEN1_WD_V << SLCHOST_SLC1HOST_TOKEN1_WD_S)
#define SLCHOST_SLC1HOST_TOKEN1_WD_V  0x00000FFFU
#define SLCHOST_SLC1HOST_TOKEN1_WD_S  16

/** SLCHOST_TOKEN_CON_REG register
 *  *******Description***********
 */
#define SLCHOST_TOKEN_CON_REG (DR_REG_SLCHOST_BASE + 0xd0)
/** SLCHOST_SLC0HOST_TOKEN0_DEC : WT; bitpos: [0]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0HOST_TOKEN0_DEC    (BIT(0))
#define SLCHOST_SLC0HOST_TOKEN0_DEC_M  (SLCHOST_SLC0HOST_TOKEN0_DEC_V << SLCHOST_SLC0HOST_TOKEN0_DEC_S)
#define SLCHOST_SLC0HOST_TOKEN0_DEC_V  0x00000001U
#define SLCHOST_SLC0HOST_TOKEN0_DEC_S  0
/** SLCHOST_SLC0HOST_TOKEN1_DEC : WT; bitpos: [1]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0HOST_TOKEN1_DEC    (BIT(1))
#define SLCHOST_SLC0HOST_TOKEN1_DEC_M  (SLCHOST_SLC0HOST_TOKEN1_DEC_V << SLCHOST_SLC0HOST_TOKEN1_DEC_S)
#define SLCHOST_SLC0HOST_TOKEN1_DEC_V  0x00000001U
#define SLCHOST_SLC0HOST_TOKEN1_DEC_S  1
/** SLCHOST_SLC0HOST_TOKEN0_WR : WT; bitpos: [2]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0HOST_TOKEN0_WR    (BIT(2))
#define SLCHOST_SLC0HOST_TOKEN0_WR_M  (SLCHOST_SLC0HOST_TOKEN0_WR_V << SLCHOST_SLC0HOST_TOKEN0_WR_S)
#define SLCHOST_SLC0HOST_TOKEN0_WR_V  0x00000001U
#define SLCHOST_SLC0HOST_TOKEN0_WR_S  2
/** SLCHOST_SLC0HOST_TOKEN1_WR : WT; bitpos: [3]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0HOST_TOKEN1_WR    (BIT(3))
#define SLCHOST_SLC0HOST_TOKEN1_WR_M  (SLCHOST_SLC0HOST_TOKEN1_WR_V << SLCHOST_SLC0HOST_TOKEN1_WR_S)
#define SLCHOST_SLC0HOST_TOKEN1_WR_V  0x00000001U
#define SLCHOST_SLC0HOST_TOKEN1_WR_S  3
/** SLCHOST_SLC1HOST_TOKEN0_DEC : WT; bitpos: [4]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1HOST_TOKEN0_DEC    (BIT(4))
#define SLCHOST_SLC1HOST_TOKEN0_DEC_M  (SLCHOST_SLC1HOST_TOKEN0_DEC_V << SLCHOST_SLC1HOST_TOKEN0_DEC_S)
#define SLCHOST_SLC1HOST_TOKEN0_DEC_V  0x00000001U
#define SLCHOST_SLC1HOST_TOKEN0_DEC_S  4
/** SLCHOST_SLC1HOST_TOKEN1_DEC : WT; bitpos: [5]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1HOST_TOKEN1_DEC    (BIT(5))
#define SLCHOST_SLC1HOST_TOKEN1_DEC_M  (SLCHOST_SLC1HOST_TOKEN1_DEC_V << SLCHOST_SLC1HOST_TOKEN1_DEC_S)
#define SLCHOST_SLC1HOST_TOKEN1_DEC_V  0x00000001U
#define SLCHOST_SLC1HOST_TOKEN1_DEC_S  5
/** SLCHOST_SLC1HOST_TOKEN0_WR : WT; bitpos: [6]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1HOST_TOKEN0_WR    (BIT(6))
#define SLCHOST_SLC1HOST_TOKEN0_WR_M  (SLCHOST_SLC1HOST_TOKEN0_WR_V << SLCHOST_SLC1HOST_TOKEN0_WR_S)
#define SLCHOST_SLC1HOST_TOKEN0_WR_V  0x00000001U
#define SLCHOST_SLC1HOST_TOKEN0_WR_S  6
/** SLCHOST_SLC1HOST_TOKEN1_WR : WT; bitpos: [7]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1HOST_TOKEN1_WR    (BIT(7))
#define SLCHOST_SLC1HOST_TOKEN1_WR_M  (SLCHOST_SLC1HOST_TOKEN1_WR_V << SLCHOST_SLC1HOST_TOKEN1_WR_S)
#define SLCHOST_SLC1HOST_TOKEN1_WR_V  0x00000001U
#define SLCHOST_SLC1HOST_TOKEN1_WR_S  7
/** SLCHOST_SLC0HOST_LEN_WR : WT; bitpos: [8]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0HOST_LEN_WR    (BIT(8))
#define SLCHOST_SLC0HOST_LEN_WR_M  (SLCHOST_SLC0HOST_LEN_WR_V << SLCHOST_SLC0HOST_LEN_WR_S)
#define SLCHOST_SLC0HOST_LEN_WR_V  0x00000001U
#define SLCHOST_SLC0HOST_LEN_WR_S  8

/** SLCHOST_SLC0HOST_INT_CLR_REG register
 *  *******Description***********
 */
#define SLCHOST_SLC0HOST_INT_CLR_REG (DR_REG_SLCHOST_BASE + 0xd4)
/** SLCHOST_SLC0_TOHOST_BIT0_INT_CLR : WT; bitpos: [0]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0_TOHOST_BIT0_INT_CLR    (BIT(0))
#define SLCHOST_SLC0_TOHOST_BIT0_INT_CLR_M  (SLCHOST_SLC0_TOHOST_BIT0_INT_CLR_V << SLCHOST_SLC0_TOHOST_BIT0_INT_CLR_S)
#define SLCHOST_SLC0_TOHOST_BIT0_INT_CLR_V  0x00000001U
#define SLCHOST_SLC0_TOHOST_BIT0_INT_CLR_S  0
/** SLCHOST_SLC0_TOHOST_BIT1_INT_CLR : WT; bitpos: [1]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0_TOHOST_BIT1_INT_CLR    (BIT(1))
#define SLCHOST_SLC0_TOHOST_BIT1_INT_CLR_M  (SLCHOST_SLC0_TOHOST_BIT1_INT_CLR_V << SLCHOST_SLC0_TOHOST_BIT1_INT_CLR_S)
#define SLCHOST_SLC0_TOHOST_BIT1_INT_CLR_V  0x00000001U
#define SLCHOST_SLC0_TOHOST_BIT1_INT_CLR_S  1
/** SLCHOST_SLC0_TOHOST_BIT2_INT_CLR : WT; bitpos: [2]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0_TOHOST_BIT2_INT_CLR    (BIT(2))
#define SLCHOST_SLC0_TOHOST_BIT2_INT_CLR_M  (SLCHOST_SLC0_TOHOST_BIT2_INT_CLR_V << SLCHOST_SLC0_TOHOST_BIT2_INT_CLR_S)
#define SLCHOST_SLC0_TOHOST_BIT2_INT_CLR_V  0x00000001U
#define SLCHOST_SLC0_TOHOST_BIT2_INT_CLR_S  2
/** SLCHOST_SLC0_TOHOST_BIT3_INT_CLR : WT; bitpos: [3]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0_TOHOST_BIT3_INT_CLR    (BIT(3))
#define SLCHOST_SLC0_TOHOST_BIT3_INT_CLR_M  (SLCHOST_SLC0_TOHOST_BIT3_INT_CLR_V << SLCHOST_SLC0_TOHOST_BIT3_INT_CLR_S)
#define SLCHOST_SLC0_TOHOST_BIT3_INT_CLR_V  0x00000001U
#define SLCHOST_SLC0_TOHOST_BIT3_INT_CLR_S  3
/** SLCHOST_SLC0_TOHOST_BIT4_INT_CLR : WT; bitpos: [4]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0_TOHOST_BIT4_INT_CLR    (BIT(4))
#define SLCHOST_SLC0_TOHOST_BIT4_INT_CLR_M  (SLCHOST_SLC0_TOHOST_BIT4_INT_CLR_V << SLCHOST_SLC0_TOHOST_BIT4_INT_CLR_S)
#define SLCHOST_SLC0_TOHOST_BIT4_INT_CLR_V  0x00000001U
#define SLCHOST_SLC0_TOHOST_BIT4_INT_CLR_S  4
/** SLCHOST_SLC0_TOHOST_BIT5_INT_CLR : WT; bitpos: [5]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0_TOHOST_BIT5_INT_CLR    (BIT(5))
#define SLCHOST_SLC0_TOHOST_BIT5_INT_CLR_M  (SLCHOST_SLC0_TOHOST_BIT5_INT_CLR_V << SLCHOST_SLC0_TOHOST_BIT5_INT_CLR_S)
#define SLCHOST_SLC0_TOHOST_BIT5_INT_CLR_V  0x00000001U
#define SLCHOST_SLC0_TOHOST_BIT5_INT_CLR_S  5
/** SLCHOST_SLC0_TOHOST_BIT6_INT_CLR : WT; bitpos: [6]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0_TOHOST_BIT6_INT_CLR    (BIT(6))
#define SLCHOST_SLC0_TOHOST_BIT6_INT_CLR_M  (SLCHOST_SLC0_TOHOST_BIT6_INT_CLR_V << SLCHOST_SLC0_TOHOST_BIT6_INT_CLR_S)
#define SLCHOST_SLC0_TOHOST_BIT6_INT_CLR_V  0x00000001U
#define SLCHOST_SLC0_TOHOST_BIT6_INT_CLR_S  6
/** SLCHOST_SLC0_TOHOST_BIT7_INT_CLR : WT; bitpos: [7]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0_TOHOST_BIT7_INT_CLR    (BIT(7))
#define SLCHOST_SLC0_TOHOST_BIT7_INT_CLR_M  (SLCHOST_SLC0_TOHOST_BIT7_INT_CLR_V << SLCHOST_SLC0_TOHOST_BIT7_INT_CLR_S)
#define SLCHOST_SLC0_TOHOST_BIT7_INT_CLR_V  0x00000001U
#define SLCHOST_SLC0_TOHOST_BIT7_INT_CLR_S  7
/** SLCHOST_SLC0_TOKEN0_1TO0_INT_CLR : WT; bitpos: [8]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0_TOKEN0_1TO0_INT_CLR    (BIT(8))
#define SLCHOST_SLC0_TOKEN0_1TO0_INT_CLR_M  (SLCHOST_SLC0_TOKEN0_1TO0_INT_CLR_V << SLCHOST_SLC0_TOKEN0_1TO0_INT_CLR_S)
#define SLCHOST_SLC0_TOKEN0_1TO0_INT_CLR_V  0x00000001U
#define SLCHOST_SLC0_TOKEN0_1TO0_INT_CLR_S  8
/** SLCHOST_SLC0_TOKEN1_1TO0_INT_CLR : WT; bitpos: [9]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0_TOKEN1_1TO0_INT_CLR    (BIT(9))
#define SLCHOST_SLC0_TOKEN1_1TO0_INT_CLR_M  (SLCHOST_SLC0_TOKEN1_1TO0_INT_CLR_V << SLCHOST_SLC0_TOKEN1_1TO0_INT_CLR_S)
#define SLCHOST_SLC0_TOKEN1_1TO0_INT_CLR_V  0x00000001U
#define SLCHOST_SLC0_TOKEN1_1TO0_INT_CLR_S  9
/** SLCHOST_SLC0_TOKEN0_0TO1_INT_CLR : WT; bitpos: [10]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0_TOKEN0_0TO1_INT_CLR    (BIT(10))
#define SLCHOST_SLC0_TOKEN0_0TO1_INT_CLR_M  (SLCHOST_SLC0_TOKEN0_0TO1_INT_CLR_V << SLCHOST_SLC0_TOKEN0_0TO1_INT_CLR_S)
#define SLCHOST_SLC0_TOKEN0_0TO1_INT_CLR_V  0x00000001U
#define SLCHOST_SLC0_TOKEN0_0TO1_INT_CLR_S  10
/** SLCHOST_SLC0_TOKEN1_0TO1_INT_CLR : WT; bitpos: [11]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0_TOKEN1_0TO1_INT_CLR    (BIT(11))
#define SLCHOST_SLC0_TOKEN1_0TO1_INT_CLR_M  (SLCHOST_SLC0_TOKEN1_0TO1_INT_CLR_V << SLCHOST_SLC0_TOKEN1_0TO1_INT_CLR_S)
#define SLCHOST_SLC0_TOKEN1_0TO1_INT_CLR_V  0x00000001U
#define SLCHOST_SLC0_TOKEN1_0TO1_INT_CLR_S  11
/** SLCHOST_SLC0HOST_RX_SOF_INT_CLR : WT; bitpos: [12]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0HOST_RX_SOF_INT_CLR    (BIT(12))
#define SLCHOST_SLC0HOST_RX_SOF_INT_CLR_M  (SLCHOST_SLC0HOST_RX_SOF_INT_CLR_V << SLCHOST_SLC0HOST_RX_SOF_INT_CLR_S)
#define SLCHOST_SLC0HOST_RX_SOF_INT_CLR_V  0x00000001U
#define SLCHOST_SLC0HOST_RX_SOF_INT_CLR_S  12
/** SLCHOST_SLC0HOST_RX_EOF_INT_CLR : WT; bitpos: [13]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0HOST_RX_EOF_INT_CLR    (BIT(13))
#define SLCHOST_SLC0HOST_RX_EOF_INT_CLR_M  (SLCHOST_SLC0HOST_RX_EOF_INT_CLR_V << SLCHOST_SLC0HOST_RX_EOF_INT_CLR_S)
#define SLCHOST_SLC0HOST_RX_EOF_INT_CLR_V  0x00000001U
#define SLCHOST_SLC0HOST_RX_EOF_INT_CLR_S  13
/** SLCHOST_SLC0HOST_RX_START_INT_CLR : WT; bitpos: [14]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0HOST_RX_START_INT_CLR    (BIT(14))
#define SLCHOST_SLC0HOST_RX_START_INT_CLR_M  (SLCHOST_SLC0HOST_RX_START_INT_CLR_V << SLCHOST_SLC0HOST_RX_START_INT_CLR_S)
#define SLCHOST_SLC0HOST_RX_START_INT_CLR_V  0x00000001U
#define SLCHOST_SLC0HOST_RX_START_INT_CLR_S  14
/** SLCHOST_SLC0HOST_TX_START_INT_CLR : WT; bitpos: [15]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0HOST_TX_START_INT_CLR    (BIT(15))
#define SLCHOST_SLC0HOST_TX_START_INT_CLR_M  (SLCHOST_SLC0HOST_TX_START_INT_CLR_V << SLCHOST_SLC0HOST_TX_START_INT_CLR_S)
#define SLCHOST_SLC0HOST_TX_START_INT_CLR_V  0x00000001U
#define SLCHOST_SLC0HOST_TX_START_INT_CLR_S  15
/** SLCHOST_SLC0_RX_UDF_INT_CLR : WT; bitpos: [16]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0_RX_UDF_INT_CLR    (BIT(16))
#define SLCHOST_SLC0_RX_UDF_INT_CLR_M  (SLCHOST_SLC0_RX_UDF_INT_CLR_V << SLCHOST_SLC0_RX_UDF_INT_CLR_S)
#define SLCHOST_SLC0_RX_UDF_INT_CLR_V  0x00000001U
#define SLCHOST_SLC0_RX_UDF_INT_CLR_S  16
/** SLCHOST_SLC0_TX_OVF_INT_CLR : WT; bitpos: [17]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0_TX_OVF_INT_CLR    (BIT(17))
#define SLCHOST_SLC0_TX_OVF_INT_CLR_M  (SLCHOST_SLC0_TX_OVF_INT_CLR_V << SLCHOST_SLC0_TX_OVF_INT_CLR_S)
#define SLCHOST_SLC0_TX_OVF_INT_CLR_V  0x00000001U
#define SLCHOST_SLC0_TX_OVF_INT_CLR_S  17
/** SLCHOST_SLC0_RX_PF_VALID_INT_CLR : WT; bitpos: [18]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0_RX_PF_VALID_INT_CLR    (BIT(18))
#define SLCHOST_SLC0_RX_PF_VALID_INT_CLR_M  (SLCHOST_SLC0_RX_PF_VALID_INT_CLR_V << SLCHOST_SLC0_RX_PF_VALID_INT_CLR_S)
#define SLCHOST_SLC0_RX_PF_VALID_INT_CLR_V  0x00000001U
#define SLCHOST_SLC0_RX_PF_VALID_INT_CLR_S  18
/** SLCHOST_SLC0_EXT_BIT0_INT_CLR : WT; bitpos: [19]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0_EXT_BIT0_INT_CLR    (BIT(19))
#define SLCHOST_SLC0_EXT_BIT0_INT_CLR_M  (SLCHOST_SLC0_EXT_BIT0_INT_CLR_V << SLCHOST_SLC0_EXT_BIT0_INT_CLR_S)
#define SLCHOST_SLC0_EXT_BIT0_INT_CLR_V  0x00000001U
#define SLCHOST_SLC0_EXT_BIT0_INT_CLR_S  19
/** SLCHOST_SLC0_EXT_BIT1_INT_CLR : WT; bitpos: [20]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0_EXT_BIT1_INT_CLR    (BIT(20))
#define SLCHOST_SLC0_EXT_BIT1_INT_CLR_M  (SLCHOST_SLC0_EXT_BIT1_INT_CLR_V << SLCHOST_SLC0_EXT_BIT1_INT_CLR_S)
#define SLCHOST_SLC0_EXT_BIT1_INT_CLR_V  0x00000001U
#define SLCHOST_SLC0_EXT_BIT1_INT_CLR_S  20
/** SLCHOST_SLC0_EXT_BIT2_INT_CLR : WT; bitpos: [21]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0_EXT_BIT2_INT_CLR    (BIT(21))
#define SLCHOST_SLC0_EXT_BIT2_INT_CLR_M  (SLCHOST_SLC0_EXT_BIT2_INT_CLR_V << SLCHOST_SLC0_EXT_BIT2_INT_CLR_S)
#define SLCHOST_SLC0_EXT_BIT2_INT_CLR_V  0x00000001U
#define SLCHOST_SLC0_EXT_BIT2_INT_CLR_S  21
/** SLCHOST_SLC0_EXT_BIT3_INT_CLR : WT; bitpos: [22]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0_EXT_BIT3_INT_CLR    (BIT(22))
#define SLCHOST_SLC0_EXT_BIT3_INT_CLR_M  (SLCHOST_SLC0_EXT_BIT3_INT_CLR_V << SLCHOST_SLC0_EXT_BIT3_INT_CLR_S)
#define SLCHOST_SLC0_EXT_BIT3_INT_CLR_V  0x00000001U
#define SLCHOST_SLC0_EXT_BIT3_INT_CLR_S  22
/** SLCHOST_SLC0_RX_NEW_PACKET_INT_CLR : WT; bitpos: [23]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0_RX_NEW_PACKET_INT_CLR    (BIT(23))
#define SLCHOST_SLC0_RX_NEW_PACKET_INT_CLR_M  (SLCHOST_SLC0_RX_NEW_PACKET_INT_CLR_V << SLCHOST_SLC0_RX_NEW_PACKET_INT_CLR_S)
#define SLCHOST_SLC0_RX_NEW_PACKET_INT_CLR_V  0x00000001U
#define SLCHOST_SLC0_RX_NEW_PACKET_INT_CLR_S  23
/** SLCHOST_SLC0_HOST_RD_RETRY_INT_CLR : WT; bitpos: [24]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0_HOST_RD_RETRY_INT_CLR    (BIT(24))
#define SLCHOST_SLC0_HOST_RD_RETRY_INT_CLR_M  (SLCHOST_SLC0_HOST_RD_RETRY_INT_CLR_V << SLCHOST_SLC0_HOST_RD_RETRY_INT_CLR_S)
#define SLCHOST_SLC0_HOST_RD_RETRY_INT_CLR_V  0x00000001U
#define SLCHOST_SLC0_HOST_RD_RETRY_INT_CLR_S  24
/** SLCHOST_GPIO_SDIO_INT_CLR : WT; bitpos: [25]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_GPIO_SDIO_INT_CLR    (BIT(25))
#define SLCHOST_GPIO_SDIO_INT_CLR_M  (SLCHOST_GPIO_SDIO_INT_CLR_V << SLCHOST_GPIO_SDIO_INT_CLR_S)
#define SLCHOST_GPIO_SDIO_INT_CLR_V  0x00000001U
#define SLCHOST_GPIO_SDIO_INT_CLR_S  25

/** SLCHOST_SLC1HOST_INT_CLR_REG register
 *  *******Description***********
 */
#define SLCHOST_SLC1HOST_INT_CLR_REG (DR_REG_SLCHOST_BASE + 0xd8)
/** SLCHOST_SLC1_TOHOST_BIT0_INT_CLR : WT; bitpos: [0]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1_TOHOST_BIT0_INT_CLR    (BIT(0))
#define SLCHOST_SLC1_TOHOST_BIT0_INT_CLR_M  (SLCHOST_SLC1_TOHOST_BIT0_INT_CLR_V << SLCHOST_SLC1_TOHOST_BIT0_INT_CLR_S)
#define SLCHOST_SLC1_TOHOST_BIT0_INT_CLR_V  0x00000001U
#define SLCHOST_SLC1_TOHOST_BIT0_INT_CLR_S  0
/** SLCHOST_SLC1_TOHOST_BIT1_INT_CLR : WT; bitpos: [1]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1_TOHOST_BIT1_INT_CLR    (BIT(1))
#define SLCHOST_SLC1_TOHOST_BIT1_INT_CLR_M  (SLCHOST_SLC1_TOHOST_BIT1_INT_CLR_V << SLCHOST_SLC1_TOHOST_BIT1_INT_CLR_S)
#define SLCHOST_SLC1_TOHOST_BIT1_INT_CLR_V  0x00000001U
#define SLCHOST_SLC1_TOHOST_BIT1_INT_CLR_S  1
/** SLCHOST_SLC1_TOHOST_BIT2_INT_CLR : WT; bitpos: [2]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1_TOHOST_BIT2_INT_CLR    (BIT(2))
#define SLCHOST_SLC1_TOHOST_BIT2_INT_CLR_M  (SLCHOST_SLC1_TOHOST_BIT2_INT_CLR_V << SLCHOST_SLC1_TOHOST_BIT2_INT_CLR_S)
#define SLCHOST_SLC1_TOHOST_BIT2_INT_CLR_V  0x00000001U
#define SLCHOST_SLC1_TOHOST_BIT2_INT_CLR_S  2
/** SLCHOST_SLC1_TOHOST_BIT3_INT_CLR : WT; bitpos: [3]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1_TOHOST_BIT3_INT_CLR    (BIT(3))
#define SLCHOST_SLC1_TOHOST_BIT3_INT_CLR_M  (SLCHOST_SLC1_TOHOST_BIT3_INT_CLR_V << SLCHOST_SLC1_TOHOST_BIT3_INT_CLR_S)
#define SLCHOST_SLC1_TOHOST_BIT3_INT_CLR_V  0x00000001U
#define SLCHOST_SLC1_TOHOST_BIT3_INT_CLR_S  3
/** SLCHOST_SLC1_TOHOST_BIT4_INT_CLR : WT; bitpos: [4]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1_TOHOST_BIT4_INT_CLR    (BIT(4))
#define SLCHOST_SLC1_TOHOST_BIT4_INT_CLR_M  (SLCHOST_SLC1_TOHOST_BIT4_INT_CLR_V << SLCHOST_SLC1_TOHOST_BIT4_INT_CLR_S)
#define SLCHOST_SLC1_TOHOST_BIT4_INT_CLR_V  0x00000001U
#define SLCHOST_SLC1_TOHOST_BIT4_INT_CLR_S  4
/** SLCHOST_SLC1_TOHOST_BIT5_INT_CLR : WT; bitpos: [5]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1_TOHOST_BIT5_INT_CLR    (BIT(5))
#define SLCHOST_SLC1_TOHOST_BIT5_INT_CLR_M  (SLCHOST_SLC1_TOHOST_BIT5_INT_CLR_V << SLCHOST_SLC1_TOHOST_BIT5_INT_CLR_S)
#define SLCHOST_SLC1_TOHOST_BIT5_INT_CLR_V  0x00000001U
#define SLCHOST_SLC1_TOHOST_BIT5_INT_CLR_S  5
/** SLCHOST_SLC1_TOHOST_BIT6_INT_CLR : WT; bitpos: [6]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1_TOHOST_BIT6_INT_CLR    (BIT(6))
#define SLCHOST_SLC1_TOHOST_BIT6_INT_CLR_M  (SLCHOST_SLC1_TOHOST_BIT6_INT_CLR_V << SLCHOST_SLC1_TOHOST_BIT6_INT_CLR_S)
#define SLCHOST_SLC1_TOHOST_BIT6_INT_CLR_V  0x00000001U
#define SLCHOST_SLC1_TOHOST_BIT6_INT_CLR_S  6
/** SLCHOST_SLC1_TOHOST_BIT7_INT_CLR : WT; bitpos: [7]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1_TOHOST_BIT7_INT_CLR    (BIT(7))
#define SLCHOST_SLC1_TOHOST_BIT7_INT_CLR_M  (SLCHOST_SLC1_TOHOST_BIT7_INT_CLR_V << SLCHOST_SLC1_TOHOST_BIT7_INT_CLR_S)
#define SLCHOST_SLC1_TOHOST_BIT7_INT_CLR_V  0x00000001U
#define SLCHOST_SLC1_TOHOST_BIT7_INT_CLR_S  7
/** SLCHOST_SLC1_TOKEN0_1TO0_INT_CLR : WT; bitpos: [8]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1_TOKEN0_1TO0_INT_CLR    (BIT(8))
#define SLCHOST_SLC1_TOKEN0_1TO0_INT_CLR_M  (SLCHOST_SLC1_TOKEN0_1TO0_INT_CLR_V << SLCHOST_SLC1_TOKEN0_1TO0_INT_CLR_S)
#define SLCHOST_SLC1_TOKEN0_1TO0_INT_CLR_V  0x00000001U
#define SLCHOST_SLC1_TOKEN0_1TO0_INT_CLR_S  8
/** SLCHOST_SLC1_TOKEN1_1TO0_INT_CLR : WT; bitpos: [9]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1_TOKEN1_1TO0_INT_CLR    (BIT(9))
#define SLCHOST_SLC1_TOKEN1_1TO0_INT_CLR_M  (SLCHOST_SLC1_TOKEN1_1TO0_INT_CLR_V << SLCHOST_SLC1_TOKEN1_1TO0_INT_CLR_S)
#define SLCHOST_SLC1_TOKEN1_1TO0_INT_CLR_V  0x00000001U
#define SLCHOST_SLC1_TOKEN1_1TO0_INT_CLR_S  9
/** SLCHOST_SLC1_TOKEN0_0TO1_INT_CLR : WT; bitpos: [10]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1_TOKEN0_0TO1_INT_CLR    (BIT(10))
#define SLCHOST_SLC1_TOKEN0_0TO1_INT_CLR_M  (SLCHOST_SLC1_TOKEN0_0TO1_INT_CLR_V << SLCHOST_SLC1_TOKEN0_0TO1_INT_CLR_S)
#define SLCHOST_SLC1_TOKEN0_0TO1_INT_CLR_V  0x00000001U
#define SLCHOST_SLC1_TOKEN0_0TO1_INT_CLR_S  10
/** SLCHOST_SLC1_TOKEN1_0TO1_INT_CLR : WT; bitpos: [11]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1_TOKEN1_0TO1_INT_CLR    (BIT(11))
#define SLCHOST_SLC1_TOKEN1_0TO1_INT_CLR_M  (SLCHOST_SLC1_TOKEN1_0TO1_INT_CLR_V << SLCHOST_SLC1_TOKEN1_0TO1_INT_CLR_S)
#define SLCHOST_SLC1_TOKEN1_0TO1_INT_CLR_V  0x00000001U
#define SLCHOST_SLC1_TOKEN1_0TO1_INT_CLR_S  11
/** SLCHOST_SLC1HOST_RX_SOF_INT_CLR : WT; bitpos: [12]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1HOST_RX_SOF_INT_CLR    (BIT(12))
#define SLCHOST_SLC1HOST_RX_SOF_INT_CLR_M  (SLCHOST_SLC1HOST_RX_SOF_INT_CLR_V << SLCHOST_SLC1HOST_RX_SOF_INT_CLR_S)
#define SLCHOST_SLC1HOST_RX_SOF_INT_CLR_V  0x00000001U
#define SLCHOST_SLC1HOST_RX_SOF_INT_CLR_S  12
/** SLCHOST_SLC1HOST_RX_EOF_INT_CLR : WT; bitpos: [13]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1HOST_RX_EOF_INT_CLR    (BIT(13))
#define SLCHOST_SLC1HOST_RX_EOF_INT_CLR_M  (SLCHOST_SLC1HOST_RX_EOF_INT_CLR_V << SLCHOST_SLC1HOST_RX_EOF_INT_CLR_S)
#define SLCHOST_SLC1HOST_RX_EOF_INT_CLR_V  0x00000001U
#define SLCHOST_SLC1HOST_RX_EOF_INT_CLR_S  13
/** SLCHOST_SLC1HOST_RX_START_INT_CLR : WT; bitpos: [14]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1HOST_RX_START_INT_CLR    (BIT(14))
#define SLCHOST_SLC1HOST_RX_START_INT_CLR_M  (SLCHOST_SLC1HOST_RX_START_INT_CLR_V << SLCHOST_SLC1HOST_RX_START_INT_CLR_S)
#define SLCHOST_SLC1HOST_RX_START_INT_CLR_V  0x00000001U
#define SLCHOST_SLC1HOST_RX_START_INT_CLR_S  14
/** SLCHOST_SLC1HOST_TX_START_INT_CLR : WT; bitpos: [15]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1HOST_TX_START_INT_CLR    (BIT(15))
#define SLCHOST_SLC1HOST_TX_START_INT_CLR_M  (SLCHOST_SLC1HOST_TX_START_INT_CLR_V << SLCHOST_SLC1HOST_TX_START_INT_CLR_S)
#define SLCHOST_SLC1HOST_TX_START_INT_CLR_V  0x00000001U
#define SLCHOST_SLC1HOST_TX_START_INT_CLR_S  15
/** SLCHOST_SLC1_RX_UDF_INT_CLR : WT; bitpos: [16]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1_RX_UDF_INT_CLR    (BIT(16))
#define SLCHOST_SLC1_RX_UDF_INT_CLR_M  (SLCHOST_SLC1_RX_UDF_INT_CLR_V << SLCHOST_SLC1_RX_UDF_INT_CLR_S)
#define SLCHOST_SLC1_RX_UDF_INT_CLR_V  0x00000001U
#define SLCHOST_SLC1_RX_UDF_INT_CLR_S  16
/** SLCHOST_SLC1_TX_OVF_INT_CLR : WT; bitpos: [17]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1_TX_OVF_INT_CLR    (BIT(17))
#define SLCHOST_SLC1_TX_OVF_INT_CLR_M  (SLCHOST_SLC1_TX_OVF_INT_CLR_V << SLCHOST_SLC1_TX_OVF_INT_CLR_S)
#define SLCHOST_SLC1_TX_OVF_INT_CLR_V  0x00000001U
#define SLCHOST_SLC1_TX_OVF_INT_CLR_S  17
/** SLCHOST_SLC1_RX_PF_VALID_INT_CLR : WT; bitpos: [18]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1_RX_PF_VALID_INT_CLR    (BIT(18))
#define SLCHOST_SLC1_RX_PF_VALID_INT_CLR_M  (SLCHOST_SLC1_RX_PF_VALID_INT_CLR_V << SLCHOST_SLC1_RX_PF_VALID_INT_CLR_S)
#define SLCHOST_SLC1_RX_PF_VALID_INT_CLR_V  0x00000001U
#define SLCHOST_SLC1_RX_PF_VALID_INT_CLR_S  18
/** SLCHOST_SLC1_EXT_BIT0_INT_CLR : WT; bitpos: [19]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1_EXT_BIT0_INT_CLR    (BIT(19))
#define SLCHOST_SLC1_EXT_BIT0_INT_CLR_M  (SLCHOST_SLC1_EXT_BIT0_INT_CLR_V << SLCHOST_SLC1_EXT_BIT0_INT_CLR_S)
#define SLCHOST_SLC1_EXT_BIT0_INT_CLR_V  0x00000001U
#define SLCHOST_SLC1_EXT_BIT0_INT_CLR_S  19
/** SLCHOST_SLC1_EXT_BIT1_INT_CLR : WT; bitpos: [20]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1_EXT_BIT1_INT_CLR    (BIT(20))
#define SLCHOST_SLC1_EXT_BIT1_INT_CLR_M  (SLCHOST_SLC1_EXT_BIT1_INT_CLR_V << SLCHOST_SLC1_EXT_BIT1_INT_CLR_S)
#define SLCHOST_SLC1_EXT_BIT1_INT_CLR_V  0x00000001U
#define SLCHOST_SLC1_EXT_BIT1_INT_CLR_S  20
/** SLCHOST_SLC1_EXT_BIT2_INT_CLR : WT; bitpos: [21]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1_EXT_BIT2_INT_CLR    (BIT(21))
#define SLCHOST_SLC1_EXT_BIT2_INT_CLR_M  (SLCHOST_SLC1_EXT_BIT2_INT_CLR_V << SLCHOST_SLC1_EXT_BIT2_INT_CLR_S)
#define SLCHOST_SLC1_EXT_BIT2_INT_CLR_V  0x00000001U
#define SLCHOST_SLC1_EXT_BIT2_INT_CLR_S  21
/** SLCHOST_SLC1_EXT_BIT3_INT_CLR : WT; bitpos: [22]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1_EXT_BIT3_INT_CLR    (BIT(22))
#define SLCHOST_SLC1_EXT_BIT3_INT_CLR_M  (SLCHOST_SLC1_EXT_BIT3_INT_CLR_V << SLCHOST_SLC1_EXT_BIT3_INT_CLR_S)
#define SLCHOST_SLC1_EXT_BIT3_INT_CLR_V  0x00000001U
#define SLCHOST_SLC1_EXT_BIT3_INT_CLR_S  22
/** SLCHOST_SLC1_WIFI_RX_NEW_PACKET_INT_CLR : WT; bitpos: [23]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1_WIFI_RX_NEW_PACKET_INT_CLR    (BIT(23))
#define SLCHOST_SLC1_WIFI_RX_NEW_PACKET_INT_CLR_M  (SLCHOST_SLC1_WIFI_RX_NEW_PACKET_INT_CLR_V << SLCHOST_SLC1_WIFI_RX_NEW_PACKET_INT_CLR_S)
#define SLCHOST_SLC1_WIFI_RX_NEW_PACKET_INT_CLR_V  0x00000001U
#define SLCHOST_SLC1_WIFI_RX_NEW_PACKET_INT_CLR_S  23
/** SLCHOST_SLC1_HOST_RD_RETRY_INT_CLR : WT; bitpos: [24]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1_HOST_RD_RETRY_INT_CLR    (BIT(24))
#define SLCHOST_SLC1_HOST_RD_RETRY_INT_CLR_M  (SLCHOST_SLC1_HOST_RD_RETRY_INT_CLR_V << SLCHOST_SLC1_HOST_RD_RETRY_INT_CLR_S)
#define SLCHOST_SLC1_HOST_RD_RETRY_INT_CLR_V  0x00000001U
#define SLCHOST_SLC1_HOST_RD_RETRY_INT_CLR_S  24
/** SLCHOST_SLC1_BT_RX_NEW_PACKET_INT_CLR : WT; bitpos: [25]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1_BT_RX_NEW_PACKET_INT_CLR    (BIT(25))
#define SLCHOST_SLC1_BT_RX_NEW_PACKET_INT_CLR_M  (SLCHOST_SLC1_BT_RX_NEW_PACKET_INT_CLR_V << SLCHOST_SLC1_BT_RX_NEW_PACKET_INT_CLR_S)
#define SLCHOST_SLC1_BT_RX_NEW_PACKET_INT_CLR_V  0x00000001U
#define SLCHOST_SLC1_BT_RX_NEW_PACKET_INT_CLR_S  25

/** SLCHOST_SLC0HOST_FUNC1_INT_ENA_REG register
 *  *******Description***********
 */
#define SLCHOST_SLC0HOST_FUNC1_INT_ENA_REG (DR_REG_SLCHOST_BASE + 0xdc)
/** SLCHOST_FN1_SLC0_TOHOST_BIT0_INT_ENA : R/W; bitpos: [0]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_FN1_SLC0_TOHOST_BIT0_INT_ENA    (BIT(0))
#define SLCHOST_FN1_SLC0_TOHOST_BIT0_INT_ENA_M  (SLCHOST_FN1_SLC0_TOHOST_BIT0_INT_ENA_V << SLCHOST_FN1_SLC0_TOHOST_BIT0_INT_ENA_S)
#define SLCHOST_FN1_SLC0_TOHOST_BIT0_INT_ENA_V  0x00000001U
#define SLCHOST_FN1_SLC0_TOHOST_BIT0_INT_ENA_S  0
/** SLCHOST_FN1_SLC0_TOHOST_BIT1_INT_ENA : R/W; bitpos: [1]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_FN1_SLC0_TOHOST_BIT1_INT_ENA    (BIT(1))
#define SLCHOST_FN1_SLC0_TOHOST_BIT1_INT_ENA_M  (SLCHOST_FN1_SLC0_TOHOST_BIT1_INT_ENA_V << SLCHOST_FN1_SLC0_TOHOST_BIT1_INT_ENA_S)
#define SLCHOST_FN1_SLC0_TOHOST_BIT1_INT_ENA_V  0x00000001U
#define SLCHOST_FN1_SLC0_TOHOST_BIT1_INT_ENA_S  1
/** SLCHOST_FN1_SLC0_TOHOST_BIT2_INT_ENA : R/W; bitpos: [2]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_FN1_SLC0_TOHOST_BIT2_INT_ENA    (BIT(2))
#define SLCHOST_FN1_SLC0_TOHOST_BIT2_INT_ENA_M  (SLCHOST_FN1_SLC0_TOHOST_BIT2_INT_ENA_V << SLCHOST_FN1_SLC0_TOHOST_BIT2_INT_ENA_S)
#define SLCHOST_FN1_SLC0_TOHOST_BIT2_INT_ENA_V  0x00000001U
#define SLCHOST_FN1_SLC0_TOHOST_BIT2_INT_ENA_S  2
/** SLCHOST_FN1_SLC0_TOHOST_BIT3_INT_ENA : R/W; bitpos: [3]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_FN1_SLC0_TOHOST_BIT3_INT_ENA    (BIT(3))
#define SLCHOST_FN1_SLC0_TOHOST_BIT3_INT_ENA_M  (SLCHOST_FN1_SLC0_TOHOST_BIT3_INT_ENA_V << SLCHOST_FN1_SLC0_TOHOST_BIT3_INT_ENA_S)
#define SLCHOST_FN1_SLC0_TOHOST_BIT3_INT_ENA_V  0x00000001U
#define SLCHOST_FN1_SLC0_TOHOST_BIT3_INT_ENA_S  3
/** SLCHOST_FN1_SLC0_TOHOST_BIT4_INT_ENA : R/W; bitpos: [4]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_FN1_SLC0_TOHOST_BIT4_INT_ENA    (BIT(4))
#define SLCHOST_FN1_SLC0_TOHOST_BIT4_INT_ENA_M  (SLCHOST_FN1_SLC0_TOHOST_BIT4_INT_ENA_V << SLCHOST_FN1_SLC0_TOHOST_BIT4_INT_ENA_S)
#define SLCHOST_FN1_SLC0_TOHOST_BIT4_INT_ENA_V  0x00000001U
#define SLCHOST_FN1_SLC0_TOHOST_BIT4_INT_ENA_S  4
/** SLCHOST_FN1_SLC0_TOHOST_BIT5_INT_ENA : R/W; bitpos: [5]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_FN1_SLC0_TOHOST_BIT5_INT_ENA    (BIT(5))
#define SLCHOST_FN1_SLC0_TOHOST_BIT5_INT_ENA_M  (SLCHOST_FN1_SLC0_TOHOST_BIT5_INT_ENA_V << SLCHOST_FN1_SLC0_TOHOST_BIT5_INT_ENA_S)
#define SLCHOST_FN1_SLC0_TOHOST_BIT5_INT_ENA_V  0x00000001U
#define SLCHOST_FN1_SLC0_TOHOST_BIT5_INT_ENA_S  5
/** SLCHOST_FN1_SLC0_TOHOST_BIT6_INT_ENA : R/W; bitpos: [6]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_FN1_SLC0_TOHOST_BIT6_INT_ENA    (BIT(6))
#define SLCHOST_FN1_SLC0_TOHOST_BIT6_INT_ENA_M  (SLCHOST_FN1_SLC0_TOHOST_BIT6_INT_ENA_V << SLCHOST_FN1_SLC0_TOHOST_BIT6_INT_ENA_S)
#define SLCHOST_FN1_SLC0_TOHOST_BIT6_INT_ENA_V  0x00000001U
#define SLCHOST_FN1_SLC0_TOHOST_BIT6_INT_ENA_S  6
/** SLCHOST_FN1_SLC0_TOHOST_BIT7_INT_ENA : R/W; bitpos: [7]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_FN1_SLC0_TOHOST_BIT7_INT_ENA    (BIT(7))
#define SLCHOST_FN1_SLC0_TOHOST_BIT7_INT_ENA_M  (SLCHOST_FN1_SLC0_TOHOST_BIT7_INT_ENA_V << SLCHOST_FN1_SLC0_TOHOST_BIT7_INT_ENA_S)
#define SLCHOST_FN1_SLC0_TOHOST_BIT7_INT_ENA_V  0x00000001U
#define SLCHOST_FN1_SLC0_TOHOST_BIT7_INT_ENA_S  7
/** SLCHOST_FN1_SLC0_TOKEN0_1TO0_INT_ENA : R/W; bitpos: [8]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_FN1_SLC0_TOKEN0_1TO0_INT_ENA    (BIT(8))
#define SLCHOST_FN1_SLC0_TOKEN0_1TO0_INT_ENA_M  (SLCHOST_FN1_SLC0_TOKEN0_1TO0_INT_ENA_V << SLCHOST_FN1_SLC0_TOKEN0_1TO0_INT_ENA_S)
#define SLCHOST_FN1_SLC0_TOKEN0_1TO0_INT_ENA_V  0x00000001U
#define SLCHOST_FN1_SLC0_TOKEN0_1TO0_INT_ENA_S  8
/** SLCHOST_FN1_SLC0_TOKEN1_1TO0_INT_ENA : R/W; bitpos: [9]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_FN1_SLC0_TOKEN1_1TO0_INT_ENA    (BIT(9))
#define SLCHOST_FN1_SLC0_TOKEN1_1TO0_INT_ENA_M  (SLCHOST_FN1_SLC0_TOKEN1_1TO0_INT_ENA_V << SLCHOST_FN1_SLC0_TOKEN1_1TO0_INT_ENA_S)
#define SLCHOST_FN1_SLC0_TOKEN1_1TO0_INT_ENA_V  0x00000001U
#define SLCHOST_FN1_SLC0_TOKEN1_1TO0_INT_ENA_S  9
/** SLCHOST_FN1_SLC0_TOKEN0_0TO1_INT_ENA : R/W; bitpos: [10]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_FN1_SLC0_TOKEN0_0TO1_INT_ENA    (BIT(10))
#define SLCHOST_FN1_SLC0_TOKEN0_0TO1_INT_ENA_M  (SLCHOST_FN1_SLC0_TOKEN0_0TO1_INT_ENA_V << SLCHOST_FN1_SLC0_TOKEN0_0TO1_INT_ENA_S)
#define SLCHOST_FN1_SLC0_TOKEN0_0TO1_INT_ENA_V  0x00000001U
#define SLCHOST_FN1_SLC0_TOKEN0_0TO1_INT_ENA_S  10
/** SLCHOST_FN1_SLC0_TOKEN1_0TO1_INT_ENA : R/W; bitpos: [11]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_FN1_SLC0_TOKEN1_0TO1_INT_ENA    (BIT(11))
#define SLCHOST_FN1_SLC0_TOKEN1_0TO1_INT_ENA_M  (SLCHOST_FN1_SLC0_TOKEN1_0TO1_INT_ENA_V << SLCHOST_FN1_SLC0_TOKEN1_0TO1_INT_ENA_S)
#define SLCHOST_FN1_SLC0_TOKEN1_0TO1_INT_ENA_V  0x00000001U
#define SLCHOST_FN1_SLC0_TOKEN1_0TO1_INT_ENA_S  11
/** SLCHOST_FN1_SLC0HOST_RX_SOF_INT_ENA : R/W; bitpos: [12]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_FN1_SLC0HOST_RX_SOF_INT_ENA    (BIT(12))
#define SLCHOST_FN1_SLC0HOST_RX_SOF_INT_ENA_M  (SLCHOST_FN1_SLC0HOST_RX_SOF_INT_ENA_V << SLCHOST_FN1_SLC0HOST_RX_SOF_INT_ENA_S)
#define SLCHOST_FN1_SLC0HOST_RX_SOF_INT_ENA_V  0x00000001U
#define SLCHOST_FN1_SLC0HOST_RX_SOF_INT_ENA_S  12
/** SLCHOST_FN1_SLC0HOST_RX_EOF_INT_ENA : R/W; bitpos: [13]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_FN1_SLC0HOST_RX_EOF_INT_ENA    (BIT(13))
#define SLCHOST_FN1_SLC0HOST_RX_EOF_INT_ENA_M  (SLCHOST_FN1_SLC0HOST_RX_EOF_INT_ENA_V << SLCHOST_FN1_SLC0HOST_RX_EOF_INT_ENA_S)
#define SLCHOST_FN1_SLC0HOST_RX_EOF_INT_ENA_V  0x00000001U
#define SLCHOST_FN1_SLC0HOST_RX_EOF_INT_ENA_S  13
/** SLCHOST_FN1_SLC0HOST_RX_START_INT_ENA : R/W; bitpos: [14]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_FN1_SLC0HOST_RX_START_INT_ENA    (BIT(14))
#define SLCHOST_FN1_SLC0HOST_RX_START_INT_ENA_M  (SLCHOST_FN1_SLC0HOST_RX_START_INT_ENA_V << SLCHOST_FN1_SLC0HOST_RX_START_INT_ENA_S)
#define SLCHOST_FN1_SLC0HOST_RX_START_INT_ENA_V  0x00000001U
#define SLCHOST_FN1_SLC0HOST_RX_START_INT_ENA_S  14
/** SLCHOST_FN1_SLC0HOST_TX_START_INT_ENA : R/W; bitpos: [15]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_FN1_SLC0HOST_TX_START_INT_ENA    (BIT(15))
#define SLCHOST_FN1_SLC0HOST_TX_START_INT_ENA_M  (SLCHOST_FN1_SLC0HOST_TX_START_INT_ENA_V << SLCHOST_FN1_SLC0HOST_TX_START_INT_ENA_S)
#define SLCHOST_FN1_SLC0HOST_TX_START_INT_ENA_V  0x00000001U
#define SLCHOST_FN1_SLC0HOST_TX_START_INT_ENA_S  15
/** SLCHOST_FN1_SLC0_RX_UDF_INT_ENA : R/W; bitpos: [16]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_FN1_SLC0_RX_UDF_INT_ENA    (BIT(16))
#define SLCHOST_FN1_SLC0_RX_UDF_INT_ENA_M  (SLCHOST_FN1_SLC0_RX_UDF_INT_ENA_V << SLCHOST_FN1_SLC0_RX_UDF_INT_ENA_S)
#define SLCHOST_FN1_SLC0_RX_UDF_INT_ENA_V  0x00000001U
#define SLCHOST_FN1_SLC0_RX_UDF_INT_ENA_S  16
/** SLCHOST_FN1_SLC0_TX_OVF_INT_ENA : R/W; bitpos: [17]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_FN1_SLC0_TX_OVF_INT_ENA    (BIT(17))
#define SLCHOST_FN1_SLC0_TX_OVF_INT_ENA_M  (SLCHOST_FN1_SLC0_TX_OVF_INT_ENA_V << SLCHOST_FN1_SLC0_TX_OVF_INT_ENA_S)
#define SLCHOST_FN1_SLC0_TX_OVF_INT_ENA_V  0x00000001U
#define SLCHOST_FN1_SLC0_TX_OVF_INT_ENA_S  17
/** SLCHOST_FN1_SLC0_RX_PF_VALID_INT_ENA : R/W; bitpos: [18]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_FN1_SLC0_RX_PF_VALID_INT_ENA    (BIT(18))
#define SLCHOST_FN1_SLC0_RX_PF_VALID_INT_ENA_M  (SLCHOST_FN1_SLC0_RX_PF_VALID_INT_ENA_V << SLCHOST_FN1_SLC0_RX_PF_VALID_INT_ENA_S)
#define SLCHOST_FN1_SLC0_RX_PF_VALID_INT_ENA_V  0x00000001U
#define SLCHOST_FN1_SLC0_RX_PF_VALID_INT_ENA_S  18
/** SLCHOST_FN1_SLC0_EXT_BIT0_INT_ENA : R/W; bitpos: [19]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_FN1_SLC0_EXT_BIT0_INT_ENA    (BIT(19))
#define SLCHOST_FN1_SLC0_EXT_BIT0_INT_ENA_M  (SLCHOST_FN1_SLC0_EXT_BIT0_INT_ENA_V << SLCHOST_FN1_SLC0_EXT_BIT0_INT_ENA_S)
#define SLCHOST_FN1_SLC0_EXT_BIT0_INT_ENA_V  0x00000001U
#define SLCHOST_FN1_SLC0_EXT_BIT0_INT_ENA_S  19
/** SLCHOST_FN1_SLC0_EXT_BIT1_INT_ENA : R/W; bitpos: [20]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_FN1_SLC0_EXT_BIT1_INT_ENA    (BIT(20))
#define SLCHOST_FN1_SLC0_EXT_BIT1_INT_ENA_M  (SLCHOST_FN1_SLC0_EXT_BIT1_INT_ENA_V << SLCHOST_FN1_SLC0_EXT_BIT1_INT_ENA_S)
#define SLCHOST_FN1_SLC0_EXT_BIT1_INT_ENA_V  0x00000001U
#define SLCHOST_FN1_SLC0_EXT_BIT1_INT_ENA_S  20
/** SLCHOST_FN1_SLC0_EXT_BIT2_INT_ENA : R/W; bitpos: [21]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_FN1_SLC0_EXT_BIT2_INT_ENA    (BIT(21))
#define SLCHOST_FN1_SLC0_EXT_BIT2_INT_ENA_M  (SLCHOST_FN1_SLC0_EXT_BIT2_INT_ENA_V << SLCHOST_FN1_SLC0_EXT_BIT2_INT_ENA_S)
#define SLCHOST_FN1_SLC0_EXT_BIT2_INT_ENA_V  0x00000001U
#define SLCHOST_FN1_SLC0_EXT_BIT2_INT_ENA_S  21
/** SLCHOST_FN1_SLC0_EXT_BIT3_INT_ENA : R/W; bitpos: [22]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_FN1_SLC0_EXT_BIT3_INT_ENA    (BIT(22))
#define SLCHOST_FN1_SLC0_EXT_BIT3_INT_ENA_M  (SLCHOST_FN1_SLC0_EXT_BIT3_INT_ENA_V << SLCHOST_FN1_SLC0_EXT_BIT3_INT_ENA_S)
#define SLCHOST_FN1_SLC0_EXT_BIT3_INT_ENA_V  0x00000001U
#define SLCHOST_FN1_SLC0_EXT_BIT3_INT_ENA_S  22
/** SLCHOST_FN1_SLC0_RX_NEW_PACKET_INT_ENA : R/W; bitpos: [23]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_FN1_SLC0_RX_NEW_PACKET_INT_ENA    (BIT(23))
#define SLCHOST_FN1_SLC0_RX_NEW_PACKET_INT_ENA_M  (SLCHOST_FN1_SLC0_RX_NEW_PACKET_INT_ENA_V << SLCHOST_FN1_SLC0_RX_NEW_PACKET_INT_ENA_S)
#define SLCHOST_FN1_SLC0_RX_NEW_PACKET_INT_ENA_V  0x00000001U
#define SLCHOST_FN1_SLC0_RX_NEW_PACKET_INT_ENA_S  23
/** SLCHOST_FN1_SLC0_HOST_RD_RETRY_INT_ENA : R/W; bitpos: [24]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_FN1_SLC0_HOST_RD_RETRY_INT_ENA    (BIT(24))
#define SLCHOST_FN1_SLC0_HOST_RD_RETRY_INT_ENA_M  (SLCHOST_FN1_SLC0_HOST_RD_RETRY_INT_ENA_V << SLCHOST_FN1_SLC0_HOST_RD_RETRY_INT_ENA_S)
#define SLCHOST_FN1_SLC0_HOST_RD_RETRY_INT_ENA_V  0x00000001U
#define SLCHOST_FN1_SLC0_HOST_RD_RETRY_INT_ENA_S  24
/** SLCHOST_FN1_GPIO_SDIO_INT_ENA : R/W; bitpos: [25]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_FN1_GPIO_SDIO_INT_ENA    (BIT(25))
#define SLCHOST_FN1_GPIO_SDIO_INT_ENA_M  (SLCHOST_FN1_GPIO_SDIO_INT_ENA_V << SLCHOST_FN1_GPIO_SDIO_INT_ENA_S)
#define SLCHOST_FN1_GPIO_SDIO_INT_ENA_V  0x00000001U
#define SLCHOST_FN1_GPIO_SDIO_INT_ENA_S  25

/** SLCHOST_SLC1HOST_FUNC1_INT_ENA_REG register
 *  *******Description***********
 */
#define SLCHOST_SLC1HOST_FUNC1_INT_ENA_REG (DR_REG_SLCHOST_BASE + 0xe0)
/** SLCHOST_FN1_SLC1_TOHOST_BIT0_INT_ENA : R/W; bitpos: [0]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_FN1_SLC1_TOHOST_BIT0_INT_ENA    (BIT(0))
#define SLCHOST_FN1_SLC1_TOHOST_BIT0_INT_ENA_M  (SLCHOST_FN1_SLC1_TOHOST_BIT0_INT_ENA_V << SLCHOST_FN1_SLC1_TOHOST_BIT0_INT_ENA_S)
#define SLCHOST_FN1_SLC1_TOHOST_BIT0_INT_ENA_V  0x00000001U
#define SLCHOST_FN1_SLC1_TOHOST_BIT0_INT_ENA_S  0
/** SLCHOST_FN1_SLC1_TOHOST_BIT1_INT_ENA : R/W; bitpos: [1]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_FN1_SLC1_TOHOST_BIT1_INT_ENA    (BIT(1))
#define SLCHOST_FN1_SLC1_TOHOST_BIT1_INT_ENA_M  (SLCHOST_FN1_SLC1_TOHOST_BIT1_INT_ENA_V << SLCHOST_FN1_SLC1_TOHOST_BIT1_INT_ENA_S)
#define SLCHOST_FN1_SLC1_TOHOST_BIT1_INT_ENA_V  0x00000001U
#define SLCHOST_FN1_SLC1_TOHOST_BIT1_INT_ENA_S  1
/** SLCHOST_FN1_SLC1_TOHOST_BIT2_INT_ENA : R/W; bitpos: [2]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_FN1_SLC1_TOHOST_BIT2_INT_ENA    (BIT(2))
#define SLCHOST_FN1_SLC1_TOHOST_BIT2_INT_ENA_M  (SLCHOST_FN1_SLC1_TOHOST_BIT2_INT_ENA_V << SLCHOST_FN1_SLC1_TOHOST_BIT2_INT_ENA_S)
#define SLCHOST_FN1_SLC1_TOHOST_BIT2_INT_ENA_V  0x00000001U
#define SLCHOST_FN1_SLC1_TOHOST_BIT2_INT_ENA_S  2
/** SLCHOST_FN1_SLC1_TOHOST_BIT3_INT_ENA : R/W; bitpos: [3]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_FN1_SLC1_TOHOST_BIT3_INT_ENA    (BIT(3))
#define SLCHOST_FN1_SLC1_TOHOST_BIT3_INT_ENA_M  (SLCHOST_FN1_SLC1_TOHOST_BIT3_INT_ENA_V << SLCHOST_FN1_SLC1_TOHOST_BIT3_INT_ENA_S)
#define SLCHOST_FN1_SLC1_TOHOST_BIT3_INT_ENA_V  0x00000001U
#define SLCHOST_FN1_SLC1_TOHOST_BIT3_INT_ENA_S  3
/** SLCHOST_FN1_SLC1_TOHOST_BIT4_INT_ENA : R/W; bitpos: [4]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_FN1_SLC1_TOHOST_BIT4_INT_ENA    (BIT(4))
#define SLCHOST_FN1_SLC1_TOHOST_BIT4_INT_ENA_M  (SLCHOST_FN1_SLC1_TOHOST_BIT4_INT_ENA_V << SLCHOST_FN1_SLC1_TOHOST_BIT4_INT_ENA_S)
#define SLCHOST_FN1_SLC1_TOHOST_BIT4_INT_ENA_V  0x00000001U
#define SLCHOST_FN1_SLC1_TOHOST_BIT4_INT_ENA_S  4
/** SLCHOST_FN1_SLC1_TOHOST_BIT5_INT_ENA : R/W; bitpos: [5]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_FN1_SLC1_TOHOST_BIT5_INT_ENA    (BIT(5))
#define SLCHOST_FN1_SLC1_TOHOST_BIT5_INT_ENA_M  (SLCHOST_FN1_SLC1_TOHOST_BIT5_INT_ENA_V << SLCHOST_FN1_SLC1_TOHOST_BIT5_INT_ENA_S)
#define SLCHOST_FN1_SLC1_TOHOST_BIT5_INT_ENA_V  0x00000001U
#define SLCHOST_FN1_SLC1_TOHOST_BIT5_INT_ENA_S  5
/** SLCHOST_FN1_SLC1_TOHOST_BIT6_INT_ENA : R/W; bitpos: [6]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_FN1_SLC1_TOHOST_BIT6_INT_ENA    (BIT(6))
#define SLCHOST_FN1_SLC1_TOHOST_BIT6_INT_ENA_M  (SLCHOST_FN1_SLC1_TOHOST_BIT6_INT_ENA_V << SLCHOST_FN1_SLC1_TOHOST_BIT6_INT_ENA_S)
#define SLCHOST_FN1_SLC1_TOHOST_BIT6_INT_ENA_V  0x00000001U
#define SLCHOST_FN1_SLC1_TOHOST_BIT6_INT_ENA_S  6
/** SLCHOST_FN1_SLC1_TOHOST_BIT7_INT_ENA : R/W; bitpos: [7]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_FN1_SLC1_TOHOST_BIT7_INT_ENA    (BIT(7))
#define SLCHOST_FN1_SLC1_TOHOST_BIT7_INT_ENA_M  (SLCHOST_FN1_SLC1_TOHOST_BIT7_INT_ENA_V << SLCHOST_FN1_SLC1_TOHOST_BIT7_INT_ENA_S)
#define SLCHOST_FN1_SLC1_TOHOST_BIT7_INT_ENA_V  0x00000001U
#define SLCHOST_FN1_SLC1_TOHOST_BIT7_INT_ENA_S  7
/** SLCHOST_FN1_SLC1_TOKEN0_1TO0_INT_ENA : R/W; bitpos: [8]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_FN1_SLC1_TOKEN0_1TO0_INT_ENA    (BIT(8))
#define SLCHOST_FN1_SLC1_TOKEN0_1TO0_INT_ENA_M  (SLCHOST_FN1_SLC1_TOKEN0_1TO0_INT_ENA_V << SLCHOST_FN1_SLC1_TOKEN0_1TO0_INT_ENA_S)
#define SLCHOST_FN1_SLC1_TOKEN0_1TO0_INT_ENA_V  0x00000001U
#define SLCHOST_FN1_SLC1_TOKEN0_1TO0_INT_ENA_S  8
/** SLCHOST_FN1_SLC1_TOKEN1_1TO0_INT_ENA : R/W; bitpos: [9]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_FN1_SLC1_TOKEN1_1TO0_INT_ENA    (BIT(9))
#define SLCHOST_FN1_SLC1_TOKEN1_1TO0_INT_ENA_M  (SLCHOST_FN1_SLC1_TOKEN1_1TO0_INT_ENA_V << SLCHOST_FN1_SLC1_TOKEN1_1TO0_INT_ENA_S)
#define SLCHOST_FN1_SLC1_TOKEN1_1TO0_INT_ENA_V  0x00000001U
#define SLCHOST_FN1_SLC1_TOKEN1_1TO0_INT_ENA_S  9
/** SLCHOST_FN1_SLC1_TOKEN0_0TO1_INT_ENA : R/W; bitpos: [10]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_FN1_SLC1_TOKEN0_0TO1_INT_ENA    (BIT(10))
#define SLCHOST_FN1_SLC1_TOKEN0_0TO1_INT_ENA_M  (SLCHOST_FN1_SLC1_TOKEN0_0TO1_INT_ENA_V << SLCHOST_FN1_SLC1_TOKEN0_0TO1_INT_ENA_S)
#define SLCHOST_FN1_SLC1_TOKEN0_0TO1_INT_ENA_V  0x00000001U
#define SLCHOST_FN1_SLC1_TOKEN0_0TO1_INT_ENA_S  10
/** SLCHOST_FN1_SLC1_TOKEN1_0TO1_INT_ENA : R/W; bitpos: [11]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_FN1_SLC1_TOKEN1_0TO1_INT_ENA    (BIT(11))
#define SLCHOST_FN1_SLC1_TOKEN1_0TO1_INT_ENA_M  (SLCHOST_FN1_SLC1_TOKEN1_0TO1_INT_ENA_V << SLCHOST_FN1_SLC1_TOKEN1_0TO1_INT_ENA_S)
#define SLCHOST_FN1_SLC1_TOKEN1_0TO1_INT_ENA_V  0x00000001U
#define SLCHOST_FN1_SLC1_TOKEN1_0TO1_INT_ENA_S  11
/** SLCHOST_FN1_SLC1HOST_RX_SOF_INT_ENA : R/W; bitpos: [12]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_FN1_SLC1HOST_RX_SOF_INT_ENA    (BIT(12))
#define SLCHOST_FN1_SLC1HOST_RX_SOF_INT_ENA_M  (SLCHOST_FN1_SLC1HOST_RX_SOF_INT_ENA_V << SLCHOST_FN1_SLC1HOST_RX_SOF_INT_ENA_S)
#define SLCHOST_FN1_SLC1HOST_RX_SOF_INT_ENA_V  0x00000001U
#define SLCHOST_FN1_SLC1HOST_RX_SOF_INT_ENA_S  12
/** SLCHOST_FN1_SLC1HOST_RX_EOF_INT_ENA : R/W; bitpos: [13]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_FN1_SLC1HOST_RX_EOF_INT_ENA    (BIT(13))
#define SLCHOST_FN1_SLC1HOST_RX_EOF_INT_ENA_M  (SLCHOST_FN1_SLC1HOST_RX_EOF_INT_ENA_V << SLCHOST_FN1_SLC1HOST_RX_EOF_INT_ENA_S)
#define SLCHOST_FN1_SLC1HOST_RX_EOF_INT_ENA_V  0x00000001U
#define SLCHOST_FN1_SLC1HOST_RX_EOF_INT_ENA_S  13
/** SLCHOST_FN1_SLC1HOST_RX_START_INT_ENA : R/W; bitpos: [14]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_FN1_SLC1HOST_RX_START_INT_ENA    (BIT(14))
#define SLCHOST_FN1_SLC1HOST_RX_START_INT_ENA_M  (SLCHOST_FN1_SLC1HOST_RX_START_INT_ENA_V << SLCHOST_FN1_SLC1HOST_RX_START_INT_ENA_S)
#define SLCHOST_FN1_SLC1HOST_RX_START_INT_ENA_V  0x00000001U
#define SLCHOST_FN1_SLC1HOST_RX_START_INT_ENA_S  14
/** SLCHOST_FN1_SLC1HOST_TX_START_INT_ENA : R/W; bitpos: [15]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_FN1_SLC1HOST_TX_START_INT_ENA    (BIT(15))
#define SLCHOST_FN1_SLC1HOST_TX_START_INT_ENA_M  (SLCHOST_FN1_SLC1HOST_TX_START_INT_ENA_V << SLCHOST_FN1_SLC1HOST_TX_START_INT_ENA_S)
#define SLCHOST_FN1_SLC1HOST_TX_START_INT_ENA_V  0x00000001U
#define SLCHOST_FN1_SLC1HOST_TX_START_INT_ENA_S  15
/** SLCHOST_FN1_SLC1_RX_UDF_INT_ENA : R/W; bitpos: [16]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_FN1_SLC1_RX_UDF_INT_ENA    (BIT(16))
#define SLCHOST_FN1_SLC1_RX_UDF_INT_ENA_M  (SLCHOST_FN1_SLC1_RX_UDF_INT_ENA_V << SLCHOST_FN1_SLC1_RX_UDF_INT_ENA_S)
#define SLCHOST_FN1_SLC1_RX_UDF_INT_ENA_V  0x00000001U
#define SLCHOST_FN1_SLC1_RX_UDF_INT_ENA_S  16
/** SLCHOST_FN1_SLC1_TX_OVF_INT_ENA : R/W; bitpos: [17]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_FN1_SLC1_TX_OVF_INT_ENA    (BIT(17))
#define SLCHOST_FN1_SLC1_TX_OVF_INT_ENA_M  (SLCHOST_FN1_SLC1_TX_OVF_INT_ENA_V << SLCHOST_FN1_SLC1_TX_OVF_INT_ENA_S)
#define SLCHOST_FN1_SLC1_TX_OVF_INT_ENA_V  0x00000001U
#define SLCHOST_FN1_SLC1_TX_OVF_INT_ENA_S  17
/** SLCHOST_FN1_SLC1_RX_PF_VALID_INT_ENA : R/W; bitpos: [18]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_FN1_SLC1_RX_PF_VALID_INT_ENA    (BIT(18))
#define SLCHOST_FN1_SLC1_RX_PF_VALID_INT_ENA_M  (SLCHOST_FN1_SLC1_RX_PF_VALID_INT_ENA_V << SLCHOST_FN1_SLC1_RX_PF_VALID_INT_ENA_S)
#define SLCHOST_FN1_SLC1_RX_PF_VALID_INT_ENA_V  0x00000001U
#define SLCHOST_FN1_SLC1_RX_PF_VALID_INT_ENA_S  18
/** SLCHOST_FN1_SLC1_EXT_BIT0_INT_ENA : R/W; bitpos: [19]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_FN1_SLC1_EXT_BIT0_INT_ENA    (BIT(19))
#define SLCHOST_FN1_SLC1_EXT_BIT0_INT_ENA_M  (SLCHOST_FN1_SLC1_EXT_BIT0_INT_ENA_V << SLCHOST_FN1_SLC1_EXT_BIT0_INT_ENA_S)
#define SLCHOST_FN1_SLC1_EXT_BIT0_INT_ENA_V  0x00000001U
#define SLCHOST_FN1_SLC1_EXT_BIT0_INT_ENA_S  19
/** SLCHOST_FN1_SLC1_EXT_BIT1_INT_ENA : R/W; bitpos: [20]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_FN1_SLC1_EXT_BIT1_INT_ENA    (BIT(20))
#define SLCHOST_FN1_SLC1_EXT_BIT1_INT_ENA_M  (SLCHOST_FN1_SLC1_EXT_BIT1_INT_ENA_V << SLCHOST_FN1_SLC1_EXT_BIT1_INT_ENA_S)
#define SLCHOST_FN1_SLC1_EXT_BIT1_INT_ENA_V  0x00000001U
#define SLCHOST_FN1_SLC1_EXT_BIT1_INT_ENA_S  20
/** SLCHOST_FN1_SLC1_EXT_BIT2_INT_ENA : R/W; bitpos: [21]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_FN1_SLC1_EXT_BIT2_INT_ENA    (BIT(21))
#define SLCHOST_FN1_SLC1_EXT_BIT2_INT_ENA_M  (SLCHOST_FN1_SLC1_EXT_BIT2_INT_ENA_V << SLCHOST_FN1_SLC1_EXT_BIT2_INT_ENA_S)
#define SLCHOST_FN1_SLC1_EXT_BIT2_INT_ENA_V  0x00000001U
#define SLCHOST_FN1_SLC1_EXT_BIT2_INT_ENA_S  21
/** SLCHOST_FN1_SLC1_EXT_BIT3_INT_ENA : R/W; bitpos: [22]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_FN1_SLC1_EXT_BIT3_INT_ENA    (BIT(22))
#define SLCHOST_FN1_SLC1_EXT_BIT3_INT_ENA_M  (SLCHOST_FN1_SLC1_EXT_BIT3_INT_ENA_V << SLCHOST_FN1_SLC1_EXT_BIT3_INT_ENA_S)
#define SLCHOST_FN1_SLC1_EXT_BIT3_INT_ENA_V  0x00000001U
#define SLCHOST_FN1_SLC1_EXT_BIT3_INT_ENA_S  22
/** SLCHOST_FN1_SLC1_WIFI_RX_NEW_PACKET_INT_ENA : R/W; bitpos: [23]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_FN1_SLC1_WIFI_RX_NEW_PACKET_INT_ENA    (BIT(23))
#define SLCHOST_FN1_SLC1_WIFI_RX_NEW_PACKET_INT_ENA_M  (SLCHOST_FN1_SLC1_WIFI_RX_NEW_PACKET_INT_ENA_V << SLCHOST_FN1_SLC1_WIFI_RX_NEW_PACKET_INT_ENA_S)
#define SLCHOST_FN1_SLC1_WIFI_RX_NEW_PACKET_INT_ENA_V  0x00000001U
#define SLCHOST_FN1_SLC1_WIFI_RX_NEW_PACKET_INT_ENA_S  23
/** SLCHOST_FN1_SLC1_HOST_RD_RETRY_INT_ENA : R/W; bitpos: [24]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_FN1_SLC1_HOST_RD_RETRY_INT_ENA    (BIT(24))
#define SLCHOST_FN1_SLC1_HOST_RD_RETRY_INT_ENA_M  (SLCHOST_FN1_SLC1_HOST_RD_RETRY_INT_ENA_V << SLCHOST_FN1_SLC1_HOST_RD_RETRY_INT_ENA_S)
#define SLCHOST_FN1_SLC1_HOST_RD_RETRY_INT_ENA_V  0x00000001U
#define SLCHOST_FN1_SLC1_HOST_RD_RETRY_INT_ENA_S  24
/** SLCHOST_FN1_SLC1_BT_RX_NEW_PACKET_INT_ENA : R/W; bitpos: [25]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_FN1_SLC1_BT_RX_NEW_PACKET_INT_ENA    (BIT(25))
#define SLCHOST_FN1_SLC1_BT_RX_NEW_PACKET_INT_ENA_M  (SLCHOST_FN1_SLC1_BT_RX_NEW_PACKET_INT_ENA_V << SLCHOST_FN1_SLC1_BT_RX_NEW_PACKET_INT_ENA_S)
#define SLCHOST_FN1_SLC1_BT_RX_NEW_PACKET_INT_ENA_V  0x00000001U
#define SLCHOST_FN1_SLC1_BT_RX_NEW_PACKET_INT_ENA_S  25

/** SLCHOST_SLC0HOST_FUNC2_INT_ENA_REG register
 *  *******Description***********
 */
#define SLCHOST_SLC0HOST_FUNC2_INT_ENA_REG (DR_REG_SLCHOST_BASE + 0xe4)
/** SLCHOST_FN2_SLC0_TOHOST_BIT0_INT_ENA : R/W; bitpos: [0]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_FN2_SLC0_TOHOST_BIT0_INT_ENA    (BIT(0))
#define SLCHOST_FN2_SLC0_TOHOST_BIT0_INT_ENA_M  (SLCHOST_FN2_SLC0_TOHOST_BIT0_INT_ENA_V << SLCHOST_FN2_SLC0_TOHOST_BIT0_INT_ENA_S)
#define SLCHOST_FN2_SLC0_TOHOST_BIT0_INT_ENA_V  0x00000001U
#define SLCHOST_FN2_SLC0_TOHOST_BIT0_INT_ENA_S  0
/** SLCHOST_FN2_SLC0_TOHOST_BIT1_INT_ENA : R/W; bitpos: [1]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_FN2_SLC0_TOHOST_BIT1_INT_ENA    (BIT(1))
#define SLCHOST_FN2_SLC0_TOHOST_BIT1_INT_ENA_M  (SLCHOST_FN2_SLC0_TOHOST_BIT1_INT_ENA_V << SLCHOST_FN2_SLC0_TOHOST_BIT1_INT_ENA_S)
#define SLCHOST_FN2_SLC0_TOHOST_BIT1_INT_ENA_V  0x00000001U
#define SLCHOST_FN2_SLC0_TOHOST_BIT1_INT_ENA_S  1
/** SLCHOST_FN2_SLC0_TOHOST_BIT2_INT_ENA : R/W; bitpos: [2]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_FN2_SLC0_TOHOST_BIT2_INT_ENA    (BIT(2))
#define SLCHOST_FN2_SLC0_TOHOST_BIT2_INT_ENA_M  (SLCHOST_FN2_SLC0_TOHOST_BIT2_INT_ENA_V << SLCHOST_FN2_SLC0_TOHOST_BIT2_INT_ENA_S)
#define SLCHOST_FN2_SLC0_TOHOST_BIT2_INT_ENA_V  0x00000001U
#define SLCHOST_FN2_SLC0_TOHOST_BIT2_INT_ENA_S  2
/** SLCHOST_FN2_SLC0_TOHOST_BIT3_INT_ENA : R/W; bitpos: [3]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_FN2_SLC0_TOHOST_BIT3_INT_ENA    (BIT(3))
#define SLCHOST_FN2_SLC0_TOHOST_BIT3_INT_ENA_M  (SLCHOST_FN2_SLC0_TOHOST_BIT3_INT_ENA_V << SLCHOST_FN2_SLC0_TOHOST_BIT3_INT_ENA_S)
#define SLCHOST_FN2_SLC0_TOHOST_BIT3_INT_ENA_V  0x00000001U
#define SLCHOST_FN2_SLC0_TOHOST_BIT3_INT_ENA_S  3
/** SLCHOST_FN2_SLC0_TOHOST_BIT4_INT_ENA : R/W; bitpos: [4]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_FN2_SLC0_TOHOST_BIT4_INT_ENA    (BIT(4))
#define SLCHOST_FN2_SLC0_TOHOST_BIT4_INT_ENA_M  (SLCHOST_FN2_SLC0_TOHOST_BIT4_INT_ENA_V << SLCHOST_FN2_SLC0_TOHOST_BIT4_INT_ENA_S)
#define SLCHOST_FN2_SLC0_TOHOST_BIT4_INT_ENA_V  0x00000001U
#define SLCHOST_FN2_SLC0_TOHOST_BIT4_INT_ENA_S  4
/** SLCHOST_FN2_SLC0_TOHOST_BIT5_INT_ENA : R/W; bitpos: [5]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_FN2_SLC0_TOHOST_BIT5_INT_ENA    (BIT(5))
#define SLCHOST_FN2_SLC0_TOHOST_BIT5_INT_ENA_M  (SLCHOST_FN2_SLC0_TOHOST_BIT5_INT_ENA_V << SLCHOST_FN2_SLC0_TOHOST_BIT5_INT_ENA_S)
#define SLCHOST_FN2_SLC0_TOHOST_BIT5_INT_ENA_V  0x00000001U
#define SLCHOST_FN2_SLC0_TOHOST_BIT5_INT_ENA_S  5
/** SLCHOST_FN2_SLC0_TOHOST_BIT6_INT_ENA : R/W; bitpos: [6]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_FN2_SLC0_TOHOST_BIT6_INT_ENA    (BIT(6))
#define SLCHOST_FN2_SLC0_TOHOST_BIT6_INT_ENA_M  (SLCHOST_FN2_SLC0_TOHOST_BIT6_INT_ENA_V << SLCHOST_FN2_SLC0_TOHOST_BIT6_INT_ENA_S)
#define SLCHOST_FN2_SLC0_TOHOST_BIT6_INT_ENA_V  0x00000001U
#define SLCHOST_FN2_SLC0_TOHOST_BIT6_INT_ENA_S  6
/** SLCHOST_FN2_SLC0_TOHOST_BIT7_INT_ENA : R/W; bitpos: [7]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_FN2_SLC0_TOHOST_BIT7_INT_ENA    (BIT(7))
#define SLCHOST_FN2_SLC0_TOHOST_BIT7_INT_ENA_M  (SLCHOST_FN2_SLC0_TOHOST_BIT7_INT_ENA_V << SLCHOST_FN2_SLC0_TOHOST_BIT7_INT_ENA_S)
#define SLCHOST_FN2_SLC0_TOHOST_BIT7_INT_ENA_V  0x00000001U
#define SLCHOST_FN2_SLC0_TOHOST_BIT7_INT_ENA_S  7
/** SLCHOST_FN2_SLC0_TOKEN0_1TO0_INT_ENA : R/W; bitpos: [8]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_FN2_SLC0_TOKEN0_1TO0_INT_ENA    (BIT(8))
#define SLCHOST_FN2_SLC0_TOKEN0_1TO0_INT_ENA_M  (SLCHOST_FN2_SLC0_TOKEN0_1TO0_INT_ENA_V << SLCHOST_FN2_SLC0_TOKEN0_1TO0_INT_ENA_S)
#define SLCHOST_FN2_SLC0_TOKEN0_1TO0_INT_ENA_V  0x00000001U
#define SLCHOST_FN2_SLC0_TOKEN0_1TO0_INT_ENA_S  8
/** SLCHOST_FN2_SLC0_TOKEN1_1TO0_INT_ENA : R/W; bitpos: [9]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_FN2_SLC0_TOKEN1_1TO0_INT_ENA    (BIT(9))
#define SLCHOST_FN2_SLC0_TOKEN1_1TO0_INT_ENA_M  (SLCHOST_FN2_SLC0_TOKEN1_1TO0_INT_ENA_V << SLCHOST_FN2_SLC0_TOKEN1_1TO0_INT_ENA_S)
#define SLCHOST_FN2_SLC0_TOKEN1_1TO0_INT_ENA_V  0x00000001U
#define SLCHOST_FN2_SLC0_TOKEN1_1TO0_INT_ENA_S  9
/** SLCHOST_FN2_SLC0_TOKEN0_0TO1_INT_ENA : R/W; bitpos: [10]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_FN2_SLC0_TOKEN0_0TO1_INT_ENA    (BIT(10))
#define SLCHOST_FN2_SLC0_TOKEN0_0TO1_INT_ENA_M  (SLCHOST_FN2_SLC0_TOKEN0_0TO1_INT_ENA_V << SLCHOST_FN2_SLC0_TOKEN0_0TO1_INT_ENA_S)
#define SLCHOST_FN2_SLC0_TOKEN0_0TO1_INT_ENA_V  0x00000001U
#define SLCHOST_FN2_SLC0_TOKEN0_0TO1_INT_ENA_S  10
/** SLCHOST_FN2_SLC0_TOKEN1_0TO1_INT_ENA : R/W; bitpos: [11]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_FN2_SLC0_TOKEN1_0TO1_INT_ENA    (BIT(11))
#define SLCHOST_FN2_SLC0_TOKEN1_0TO1_INT_ENA_M  (SLCHOST_FN2_SLC0_TOKEN1_0TO1_INT_ENA_V << SLCHOST_FN2_SLC0_TOKEN1_0TO1_INT_ENA_S)
#define SLCHOST_FN2_SLC0_TOKEN1_0TO1_INT_ENA_V  0x00000001U
#define SLCHOST_FN2_SLC0_TOKEN1_0TO1_INT_ENA_S  11
/** SLCHOST_FN2_SLC0HOST_RX_SOF_INT_ENA : R/W; bitpos: [12]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_FN2_SLC0HOST_RX_SOF_INT_ENA    (BIT(12))
#define SLCHOST_FN2_SLC0HOST_RX_SOF_INT_ENA_M  (SLCHOST_FN2_SLC0HOST_RX_SOF_INT_ENA_V << SLCHOST_FN2_SLC0HOST_RX_SOF_INT_ENA_S)
#define SLCHOST_FN2_SLC0HOST_RX_SOF_INT_ENA_V  0x00000001U
#define SLCHOST_FN2_SLC0HOST_RX_SOF_INT_ENA_S  12
/** SLCHOST_FN2_SLC0HOST_RX_EOF_INT_ENA : R/W; bitpos: [13]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_FN2_SLC0HOST_RX_EOF_INT_ENA    (BIT(13))
#define SLCHOST_FN2_SLC0HOST_RX_EOF_INT_ENA_M  (SLCHOST_FN2_SLC0HOST_RX_EOF_INT_ENA_V << SLCHOST_FN2_SLC0HOST_RX_EOF_INT_ENA_S)
#define SLCHOST_FN2_SLC0HOST_RX_EOF_INT_ENA_V  0x00000001U
#define SLCHOST_FN2_SLC0HOST_RX_EOF_INT_ENA_S  13
/** SLCHOST_FN2_SLC0HOST_RX_START_INT_ENA : R/W; bitpos: [14]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_FN2_SLC0HOST_RX_START_INT_ENA    (BIT(14))
#define SLCHOST_FN2_SLC0HOST_RX_START_INT_ENA_M  (SLCHOST_FN2_SLC0HOST_RX_START_INT_ENA_V << SLCHOST_FN2_SLC0HOST_RX_START_INT_ENA_S)
#define SLCHOST_FN2_SLC0HOST_RX_START_INT_ENA_V  0x00000001U
#define SLCHOST_FN2_SLC0HOST_RX_START_INT_ENA_S  14
/** SLCHOST_FN2_SLC0HOST_TX_START_INT_ENA : R/W; bitpos: [15]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_FN2_SLC0HOST_TX_START_INT_ENA    (BIT(15))
#define SLCHOST_FN2_SLC0HOST_TX_START_INT_ENA_M  (SLCHOST_FN2_SLC0HOST_TX_START_INT_ENA_V << SLCHOST_FN2_SLC0HOST_TX_START_INT_ENA_S)
#define SLCHOST_FN2_SLC0HOST_TX_START_INT_ENA_V  0x00000001U
#define SLCHOST_FN2_SLC0HOST_TX_START_INT_ENA_S  15
/** SLCHOST_FN2_SLC0_RX_UDF_INT_ENA : R/W; bitpos: [16]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_FN2_SLC0_RX_UDF_INT_ENA    (BIT(16))
#define SLCHOST_FN2_SLC0_RX_UDF_INT_ENA_M  (SLCHOST_FN2_SLC0_RX_UDF_INT_ENA_V << SLCHOST_FN2_SLC0_RX_UDF_INT_ENA_S)
#define SLCHOST_FN2_SLC0_RX_UDF_INT_ENA_V  0x00000001U
#define SLCHOST_FN2_SLC0_RX_UDF_INT_ENA_S  16
/** SLCHOST_FN2_SLC0_TX_OVF_INT_ENA : R/W; bitpos: [17]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_FN2_SLC0_TX_OVF_INT_ENA    (BIT(17))
#define SLCHOST_FN2_SLC0_TX_OVF_INT_ENA_M  (SLCHOST_FN2_SLC0_TX_OVF_INT_ENA_V << SLCHOST_FN2_SLC0_TX_OVF_INT_ENA_S)
#define SLCHOST_FN2_SLC0_TX_OVF_INT_ENA_V  0x00000001U
#define SLCHOST_FN2_SLC0_TX_OVF_INT_ENA_S  17
/** SLCHOST_FN2_SLC0_RX_PF_VALID_INT_ENA : R/W; bitpos: [18]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_FN2_SLC0_RX_PF_VALID_INT_ENA    (BIT(18))
#define SLCHOST_FN2_SLC0_RX_PF_VALID_INT_ENA_M  (SLCHOST_FN2_SLC0_RX_PF_VALID_INT_ENA_V << SLCHOST_FN2_SLC0_RX_PF_VALID_INT_ENA_S)
#define SLCHOST_FN2_SLC0_RX_PF_VALID_INT_ENA_V  0x00000001U
#define SLCHOST_FN2_SLC0_RX_PF_VALID_INT_ENA_S  18
/** SLCHOST_FN2_SLC0_EXT_BIT0_INT_ENA : R/W; bitpos: [19]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_FN2_SLC0_EXT_BIT0_INT_ENA    (BIT(19))
#define SLCHOST_FN2_SLC0_EXT_BIT0_INT_ENA_M  (SLCHOST_FN2_SLC0_EXT_BIT0_INT_ENA_V << SLCHOST_FN2_SLC0_EXT_BIT0_INT_ENA_S)
#define SLCHOST_FN2_SLC0_EXT_BIT0_INT_ENA_V  0x00000001U
#define SLCHOST_FN2_SLC0_EXT_BIT0_INT_ENA_S  19
/** SLCHOST_FN2_SLC0_EXT_BIT1_INT_ENA : R/W; bitpos: [20]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_FN2_SLC0_EXT_BIT1_INT_ENA    (BIT(20))
#define SLCHOST_FN2_SLC0_EXT_BIT1_INT_ENA_M  (SLCHOST_FN2_SLC0_EXT_BIT1_INT_ENA_V << SLCHOST_FN2_SLC0_EXT_BIT1_INT_ENA_S)
#define SLCHOST_FN2_SLC0_EXT_BIT1_INT_ENA_V  0x00000001U
#define SLCHOST_FN2_SLC0_EXT_BIT1_INT_ENA_S  20
/** SLCHOST_FN2_SLC0_EXT_BIT2_INT_ENA : R/W; bitpos: [21]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_FN2_SLC0_EXT_BIT2_INT_ENA    (BIT(21))
#define SLCHOST_FN2_SLC0_EXT_BIT2_INT_ENA_M  (SLCHOST_FN2_SLC0_EXT_BIT2_INT_ENA_V << SLCHOST_FN2_SLC0_EXT_BIT2_INT_ENA_S)
#define SLCHOST_FN2_SLC0_EXT_BIT2_INT_ENA_V  0x00000001U
#define SLCHOST_FN2_SLC0_EXT_BIT2_INT_ENA_S  21
/** SLCHOST_FN2_SLC0_EXT_BIT3_INT_ENA : R/W; bitpos: [22]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_FN2_SLC0_EXT_BIT3_INT_ENA    (BIT(22))
#define SLCHOST_FN2_SLC0_EXT_BIT3_INT_ENA_M  (SLCHOST_FN2_SLC0_EXT_BIT3_INT_ENA_V << SLCHOST_FN2_SLC0_EXT_BIT3_INT_ENA_S)
#define SLCHOST_FN2_SLC0_EXT_BIT3_INT_ENA_V  0x00000001U
#define SLCHOST_FN2_SLC0_EXT_BIT3_INT_ENA_S  22
/** SLCHOST_FN2_SLC0_RX_NEW_PACKET_INT_ENA : R/W; bitpos: [23]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_FN2_SLC0_RX_NEW_PACKET_INT_ENA    (BIT(23))
#define SLCHOST_FN2_SLC0_RX_NEW_PACKET_INT_ENA_M  (SLCHOST_FN2_SLC0_RX_NEW_PACKET_INT_ENA_V << SLCHOST_FN2_SLC0_RX_NEW_PACKET_INT_ENA_S)
#define SLCHOST_FN2_SLC0_RX_NEW_PACKET_INT_ENA_V  0x00000001U
#define SLCHOST_FN2_SLC0_RX_NEW_PACKET_INT_ENA_S  23
/** SLCHOST_FN2_SLC0_HOST_RD_RETRY_INT_ENA : R/W; bitpos: [24]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_FN2_SLC0_HOST_RD_RETRY_INT_ENA    (BIT(24))
#define SLCHOST_FN2_SLC0_HOST_RD_RETRY_INT_ENA_M  (SLCHOST_FN2_SLC0_HOST_RD_RETRY_INT_ENA_V << SLCHOST_FN2_SLC0_HOST_RD_RETRY_INT_ENA_S)
#define SLCHOST_FN2_SLC0_HOST_RD_RETRY_INT_ENA_V  0x00000001U
#define SLCHOST_FN2_SLC0_HOST_RD_RETRY_INT_ENA_S  24
/** SLCHOST_FN2_GPIO_SDIO_INT_ENA : R/W; bitpos: [25]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_FN2_GPIO_SDIO_INT_ENA    (BIT(25))
#define SLCHOST_FN2_GPIO_SDIO_INT_ENA_M  (SLCHOST_FN2_GPIO_SDIO_INT_ENA_V << SLCHOST_FN2_GPIO_SDIO_INT_ENA_S)
#define SLCHOST_FN2_GPIO_SDIO_INT_ENA_V  0x00000001U
#define SLCHOST_FN2_GPIO_SDIO_INT_ENA_S  25

/** SLCHOST_SLC1HOST_FUNC2_INT_ENA_REG register
 *  *******Description***********
 */
#define SLCHOST_SLC1HOST_FUNC2_INT_ENA_REG (DR_REG_SLCHOST_BASE + 0xe8)
/** SLCHOST_FN2_SLC1_TOHOST_BIT0_INT_ENA : R/W; bitpos: [0]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_FN2_SLC1_TOHOST_BIT0_INT_ENA    (BIT(0))
#define SLCHOST_FN2_SLC1_TOHOST_BIT0_INT_ENA_M  (SLCHOST_FN2_SLC1_TOHOST_BIT0_INT_ENA_V << SLCHOST_FN2_SLC1_TOHOST_BIT0_INT_ENA_S)
#define SLCHOST_FN2_SLC1_TOHOST_BIT0_INT_ENA_V  0x00000001U
#define SLCHOST_FN2_SLC1_TOHOST_BIT0_INT_ENA_S  0
/** SLCHOST_FN2_SLC1_TOHOST_BIT1_INT_ENA : R/W; bitpos: [1]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_FN2_SLC1_TOHOST_BIT1_INT_ENA    (BIT(1))
#define SLCHOST_FN2_SLC1_TOHOST_BIT1_INT_ENA_M  (SLCHOST_FN2_SLC1_TOHOST_BIT1_INT_ENA_V << SLCHOST_FN2_SLC1_TOHOST_BIT1_INT_ENA_S)
#define SLCHOST_FN2_SLC1_TOHOST_BIT1_INT_ENA_V  0x00000001U
#define SLCHOST_FN2_SLC1_TOHOST_BIT1_INT_ENA_S  1
/** SLCHOST_FN2_SLC1_TOHOST_BIT2_INT_ENA : R/W; bitpos: [2]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_FN2_SLC1_TOHOST_BIT2_INT_ENA    (BIT(2))
#define SLCHOST_FN2_SLC1_TOHOST_BIT2_INT_ENA_M  (SLCHOST_FN2_SLC1_TOHOST_BIT2_INT_ENA_V << SLCHOST_FN2_SLC1_TOHOST_BIT2_INT_ENA_S)
#define SLCHOST_FN2_SLC1_TOHOST_BIT2_INT_ENA_V  0x00000001U
#define SLCHOST_FN2_SLC1_TOHOST_BIT2_INT_ENA_S  2
/** SLCHOST_FN2_SLC1_TOHOST_BIT3_INT_ENA : R/W; bitpos: [3]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_FN2_SLC1_TOHOST_BIT3_INT_ENA    (BIT(3))
#define SLCHOST_FN2_SLC1_TOHOST_BIT3_INT_ENA_M  (SLCHOST_FN2_SLC1_TOHOST_BIT3_INT_ENA_V << SLCHOST_FN2_SLC1_TOHOST_BIT3_INT_ENA_S)
#define SLCHOST_FN2_SLC1_TOHOST_BIT3_INT_ENA_V  0x00000001U
#define SLCHOST_FN2_SLC1_TOHOST_BIT3_INT_ENA_S  3
/** SLCHOST_FN2_SLC1_TOHOST_BIT4_INT_ENA : R/W; bitpos: [4]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_FN2_SLC1_TOHOST_BIT4_INT_ENA    (BIT(4))
#define SLCHOST_FN2_SLC1_TOHOST_BIT4_INT_ENA_M  (SLCHOST_FN2_SLC1_TOHOST_BIT4_INT_ENA_V << SLCHOST_FN2_SLC1_TOHOST_BIT4_INT_ENA_S)
#define SLCHOST_FN2_SLC1_TOHOST_BIT4_INT_ENA_V  0x00000001U
#define SLCHOST_FN2_SLC1_TOHOST_BIT4_INT_ENA_S  4
/** SLCHOST_FN2_SLC1_TOHOST_BIT5_INT_ENA : R/W; bitpos: [5]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_FN2_SLC1_TOHOST_BIT5_INT_ENA    (BIT(5))
#define SLCHOST_FN2_SLC1_TOHOST_BIT5_INT_ENA_M  (SLCHOST_FN2_SLC1_TOHOST_BIT5_INT_ENA_V << SLCHOST_FN2_SLC1_TOHOST_BIT5_INT_ENA_S)
#define SLCHOST_FN2_SLC1_TOHOST_BIT5_INT_ENA_V  0x00000001U
#define SLCHOST_FN2_SLC1_TOHOST_BIT5_INT_ENA_S  5
/** SLCHOST_FN2_SLC1_TOHOST_BIT6_INT_ENA : R/W; bitpos: [6]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_FN2_SLC1_TOHOST_BIT6_INT_ENA    (BIT(6))
#define SLCHOST_FN2_SLC1_TOHOST_BIT6_INT_ENA_M  (SLCHOST_FN2_SLC1_TOHOST_BIT6_INT_ENA_V << SLCHOST_FN2_SLC1_TOHOST_BIT6_INT_ENA_S)
#define SLCHOST_FN2_SLC1_TOHOST_BIT6_INT_ENA_V  0x00000001U
#define SLCHOST_FN2_SLC1_TOHOST_BIT6_INT_ENA_S  6
/** SLCHOST_FN2_SLC1_TOHOST_BIT7_INT_ENA : R/W; bitpos: [7]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_FN2_SLC1_TOHOST_BIT7_INT_ENA    (BIT(7))
#define SLCHOST_FN2_SLC1_TOHOST_BIT7_INT_ENA_M  (SLCHOST_FN2_SLC1_TOHOST_BIT7_INT_ENA_V << SLCHOST_FN2_SLC1_TOHOST_BIT7_INT_ENA_S)
#define SLCHOST_FN2_SLC1_TOHOST_BIT7_INT_ENA_V  0x00000001U
#define SLCHOST_FN2_SLC1_TOHOST_BIT7_INT_ENA_S  7
/** SLCHOST_FN2_SLC1_TOKEN0_1TO0_INT_ENA : R/W; bitpos: [8]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_FN2_SLC1_TOKEN0_1TO0_INT_ENA    (BIT(8))
#define SLCHOST_FN2_SLC1_TOKEN0_1TO0_INT_ENA_M  (SLCHOST_FN2_SLC1_TOKEN0_1TO0_INT_ENA_V << SLCHOST_FN2_SLC1_TOKEN0_1TO0_INT_ENA_S)
#define SLCHOST_FN2_SLC1_TOKEN0_1TO0_INT_ENA_V  0x00000001U
#define SLCHOST_FN2_SLC1_TOKEN0_1TO0_INT_ENA_S  8
/** SLCHOST_FN2_SLC1_TOKEN1_1TO0_INT_ENA : R/W; bitpos: [9]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_FN2_SLC1_TOKEN1_1TO0_INT_ENA    (BIT(9))
#define SLCHOST_FN2_SLC1_TOKEN1_1TO0_INT_ENA_M  (SLCHOST_FN2_SLC1_TOKEN1_1TO0_INT_ENA_V << SLCHOST_FN2_SLC1_TOKEN1_1TO0_INT_ENA_S)
#define SLCHOST_FN2_SLC1_TOKEN1_1TO0_INT_ENA_V  0x00000001U
#define SLCHOST_FN2_SLC1_TOKEN1_1TO0_INT_ENA_S  9
/** SLCHOST_FN2_SLC1_TOKEN0_0TO1_INT_ENA : R/W; bitpos: [10]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_FN2_SLC1_TOKEN0_0TO1_INT_ENA    (BIT(10))
#define SLCHOST_FN2_SLC1_TOKEN0_0TO1_INT_ENA_M  (SLCHOST_FN2_SLC1_TOKEN0_0TO1_INT_ENA_V << SLCHOST_FN2_SLC1_TOKEN0_0TO1_INT_ENA_S)
#define SLCHOST_FN2_SLC1_TOKEN0_0TO1_INT_ENA_V  0x00000001U
#define SLCHOST_FN2_SLC1_TOKEN0_0TO1_INT_ENA_S  10
/** SLCHOST_FN2_SLC1_TOKEN1_0TO1_INT_ENA : R/W; bitpos: [11]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_FN2_SLC1_TOKEN1_0TO1_INT_ENA    (BIT(11))
#define SLCHOST_FN2_SLC1_TOKEN1_0TO1_INT_ENA_M  (SLCHOST_FN2_SLC1_TOKEN1_0TO1_INT_ENA_V << SLCHOST_FN2_SLC1_TOKEN1_0TO1_INT_ENA_S)
#define SLCHOST_FN2_SLC1_TOKEN1_0TO1_INT_ENA_V  0x00000001U
#define SLCHOST_FN2_SLC1_TOKEN1_0TO1_INT_ENA_S  11
/** SLCHOST_FN2_SLC1HOST_RX_SOF_INT_ENA : R/W; bitpos: [12]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_FN2_SLC1HOST_RX_SOF_INT_ENA    (BIT(12))
#define SLCHOST_FN2_SLC1HOST_RX_SOF_INT_ENA_M  (SLCHOST_FN2_SLC1HOST_RX_SOF_INT_ENA_V << SLCHOST_FN2_SLC1HOST_RX_SOF_INT_ENA_S)
#define SLCHOST_FN2_SLC1HOST_RX_SOF_INT_ENA_V  0x00000001U
#define SLCHOST_FN2_SLC1HOST_RX_SOF_INT_ENA_S  12
/** SLCHOST_FN2_SLC1HOST_RX_EOF_INT_ENA : R/W; bitpos: [13]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_FN2_SLC1HOST_RX_EOF_INT_ENA    (BIT(13))
#define SLCHOST_FN2_SLC1HOST_RX_EOF_INT_ENA_M  (SLCHOST_FN2_SLC1HOST_RX_EOF_INT_ENA_V << SLCHOST_FN2_SLC1HOST_RX_EOF_INT_ENA_S)
#define SLCHOST_FN2_SLC1HOST_RX_EOF_INT_ENA_V  0x00000001U
#define SLCHOST_FN2_SLC1HOST_RX_EOF_INT_ENA_S  13
/** SLCHOST_FN2_SLC1HOST_RX_START_INT_ENA : R/W; bitpos: [14]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_FN2_SLC1HOST_RX_START_INT_ENA    (BIT(14))
#define SLCHOST_FN2_SLC1HOST_RX_START_INT_ENA_M  (SLCHOST_FN2_SLC1HOST_RX_START_INT_ENA_V << SLCHOST_FN2_SLC1HOST_RX_START_INT_ENA_S)
#define SLCHOST_FN2_SLC1HOST_RX_START_INT_ENA_V  0x00000001U
#define SLCHOST_FN2_SLC1HOST_RX_START_INT_ENA_S  14
/** SLCHOST_FN2_SLC1HOST_TX_START_INT_ENA : R/W; bitpos: [15]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_FN2_SLC1HOST_TX_START_INT_ENA    (BIT(15))
#define SLCHOST_FN2_SLC1HOST_TX_START_INT_ENA_M  (SLCHOST_FN2_SLC1HOST_TX_START_INT_ENA_V << SLCHOST_FN2_SLC1HOST_TX_START_INT_ENA_S)
#define SLCHOST_FN2_SLC1HOST_TX_START_INT_ENA_V  0x00000001U
#define SLCHOST_FN2_SLC1HOST_TX_START_INT_ENA_S  15
/** SLCHOST_FN2_SLC1_RX_UDF_INT_ENA : R/W; bitpos: [16]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_FN2_SLC1_RX_UDF_INT_ENA    (BIT(16))
#define SLCHOST_FN2_SLC1_RX_UDF_INT_ENA_M  (SLCHOST_FN2_SLC1_RX_UDF_INT_ENA_V << SLCHOST_FN2_SLC1_RX_UDF_INT_ENA_S)
#define SLCHOST_FN2_SLC1_RX_UDF_INT_ENA_V  0x00000001U
#define SLCHOST_FN2_SLC1_RX_UDF_INT_ENA_S  16
/** SLCHOST_FN2_SLC1_TX_OVF_INT_ENA : R/W; bitpos: [17]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_FN2_SLC1_TX_OVF_INT_ENA    (BIT(17))
#define SLCHOST_FN2_SLC1_TX_OVF_INT_ENA_M  (SLCHOST_FN2_SLC1_TX_OVF_INT_ENA_V << SLCHOST_FN2_SLC1_TX_OVF_INT_ENA_S)
#define SLCHOST_FN2_SLC1_TX_OVF_INT_ENA_V  0x00000001U
#define SLCHOST_FN2_SLC1_TX_OVF_INT_ENA_S  17
/** SLCHOST_FN2_SLC1_RX_PF_VALID_INT_ENA : R/W; bitpos: [18]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_FN2_SLC1_RX_PF_VALID_INT_ENA    (BIT(18))
#define SLCHOST_FN2_SLC1_RX_PF_VALID_INT_ENA_M  (SLCHOST_FN2_SLC1_RX_PF_VALID_INT_ENA_V << SLCHOST_FN2_SLC1_RX_PF_VALID_INT_ENA_S)
#define SLCHOST_FN2_SLC1_RX_PF_VALID_INT_ENA_V  0x00000001U
#define SLCHOST_FN2_SLC1_RX_PF_VALID_INT_ENA_S  18
/** SLCHOST_FN2_SLC1_EXT_BIT0_INT_ENA : R/W; bitpos: [19]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_FN2_SLC1_EXT_BIT0_INT_ENA    (BIT(19))
#define SLCHOST_FN2_SLC1_EXT_BIT0_INT_ENA_M  (SLCHOST_FN2_SLC1_EXT_BIT0_INT_ENA_V << SLCHOST_FN2_SLC1_EXT_BIT0_INT_ENA_S)
#define SLCHOST_FN2_SLC1_EXT_BIT0_INT_ENA_V  0x00000001U
#define SLCHOST_FN2_SLC1_EXT_BIT0_INT_ENA_S  19
/** SLCHOST_FN2_SLC1_EXT_BIT1_INT_ENA : R/W; bitpos: [20]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_FN2_SLC1_EXT_BIT1_INT_ENA    (BIT(20))
#define SLCHOST_FN2_SLC1_EXT_BIT1_INT_ENA_M  (SLCHOST_FN2_SLC1_EXT_BIT1_INT_ENA_V << SLCHOST_FN2_SLC1_EXT_BIT1_INT_ENA_S)
#define SLCHOST_FN2_SLC1_EXT_BIT1_INT_ENA_V  0x00000001U
#define SLCHOST_FN2_SLC1_EXT_BIT1_INT_ENA_S  20
/** SLCHOST_FN2_SLC1_EXT_BIT2_INT_ENA : R/W; bitpos: [21]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_FN2_SLC1_EXT_BIT2_INT_ENA    (BIT(21))
#define SLCHOST_FN2_SLC1_EXT_BIT2_INT_ENA_M  (SLCHOST_FN2_SLC1_EXT_BIT2_INT_ENA_V << SLCHOST_FN2_SLC1_EXT_BIT2_INT_ENA_S)
#define SLCHOST_FN2_SLC1_EXT_BIT2_INT_ENA_V  0x00000001U
#define SLCHOST_FN2_SLC1_EXT_BIT2_INT_ENA_S  21
/** SLCHOST_FN2_SLC1_EXT_BIT3_INT_ENA : R/W; bitpos: [22]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_FN2_SLC1_EXT_BIT3_INT_ENA    (BIT(22))
#define SLCHOST_FN2_SLC1_EXT_BIT3_INT_ENA_M  (SLCHOST_FN2_SLC1_EXT_BIT3_INT_ENA_V << SLCHOST_FN2_SLC1_EXT_BIT3_INT_ENA_S)
#define SLCHOST_FN2_SLC1_EXT_BIT3_INT_ENA_V  0x00000001U
#define SLCHOST_FN2_SLC1_EXT_BIT3_INT_ENA_S  22
/** SLCHOST_FN2_SLC1_WIFI_RX_NEW_PACKET_INT_ENA : R/W; bitpos: [23]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_FN2_SLC1_WIFI_RX_NEW_PACKET_INT_ENA    (BIT(23))
#define SLCHOST_FN2_SLC1_WIFI_RX_NEW_PACKET_INT_ENA_M  (SLCHOST_FN2_SLC1_WIFI_RX_NEW_PACKET_INT_ENA_V << SLCHOST_FN2_SLC1_WIFI_RX_NEW_PACKET_INT_ENA_S)
#define SLCHOST_FN2_SLC1_WIFI_RX_NEW_PACKET_INT_ENA_V  0x00000001U
#define SLCHOST_FN2_SLC1_WIFI_RX_NEW_PACKET_INT_ENA_S  23
/** SLCHOST_FN2_SLC1_HOST_RD_RETRY_INT_ENA : R/W; bitpos: [24]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_FN2_SLC1_HOST_RD_RETRY_INT_ENA    (BIT(24))
#define SLCHOST_FN2_SLC1_HOST_RD_RETRY_INT_ENA_M  (SLCHOST_FN2_SLC1_HOST_RD_RETRY_INT_ENA_V << SLCHOST_FN2_SLC1_HOST_RD_RETRY_INT_ENA_S)
#define SLCHOST_FN2_SLC1_HOST_RD_RETRY_INT_ENA_V  0x00000001U
#define SLCHOST_FN2_SLC1_HOST_RD_RETRY_INT_ENA_S  24
/** SLCHOST_FN2_SLC1_BT_RX_NEW_PACKET_INT_ENA : R/W; bitpos: [25]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_FN2_SLC1_BT_RX_NEW_PACKET_INT_ENA    (BIT(25))
#define SLCHOST_FN2_SLC1_BT_RX_NEW_PACKET_INT_ENA_M  (SLCHOST_FN2_SLC1_BT_RX_NEW_PACKET_INT_ENA_V << SLCHOST_FN2_SLC1_BT_RX_NEW_PACKET_INT_ENA_S)
#define SLCHOST_FN2_SLC1_BT_RX_NEW_PACKET_INT_ENA_V  0x00000001U
#define SLCHOST_FN2_SLC1_BT_RX_NEW_PACKET_INT_ENA_S  25

/** SLCHOST_SLC0HOST_INT_ENA_REG register
 *  *******Description***********
 */
#define SLCHOST_SLC0HOST_INT_ENA_REG (DR_REG_SLCHOST_BASE + 0xec)
/** SLCHOST_SLC0_TOHOST_BIT0_INT_ENA : R/W; bitpos: [0]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0_TOHOST_BIT0_INT_ENA    (BIT(0))
#define SLCHOST_SLC0_TOHOST_BIT0_INT_ENA_M  (SLCHOST_SLC0_TOHOST_BIT0_INT_ENA_V << SLCHOST_SLC0_TOHOST_BIT0_INT_ENA_S)
#define SLCHOST_SLC0_TOHOST_BIT0_INT_ENA_V  0x00000001U
#define SLCHOST_SLC0_TOHOST_BIT0_INT_ENA_S  0
/** SLCHOST_SLC0_TOHOST_BIT1_INT_ENA : R/W; bitpos: [1]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0_TOHOST_BIT1_INT_ENA    (BIT(1))
#define SLCHOST_SLC0_TOHOST_BIT1_INT_ENA_M  (SLCHOST_SLC0_TOHOST_BIT1_INT_ENA_V << SLCHOST_SLC0_TOHOST_BIT1_INT_ENA_S)
#define SLCHOST_SLC0_TOHOST_BIT1_INT_ENA_V  0x00000001U
#define SLCHOST_SLC0_TOHOST_BIT1_INT_ENA_S  1
/** SLCHOST_SLC0_TOHOST_BIT2_INT_ENA : R/W; bitpos: [2]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0_TOHOST_BIT2_INT_ENA    (BIT(2))
#define SLCHOST_SLC0_TOHOST_BIT2_INT_ENA_M  (SLCHOST_SLC0_TOHOST_BIT2_INT_ENA_V << SLCHOST_SLC0_TOHOST_BIT2_INT_ENA_S)
#define SLCHOST_SLC0_TOHOST_BIT2_INT_ENA_V  0x00000001U
#define SLCHOST_SLC0_TOHOST_BIT2_INT_ENA_S  2
/** SLCHOST_SLC0_TOHOST_BIT3_INT_ENA : R/W; bitpos: [3]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0_TOHOST_BIT3_INT_ENA    (BIT(3))
#define SLCHOST_SLC0_TOHOST_BIT3_INT_ENA_M  (SLCHOST_SLC0_TOHOST_BIT3_INT_ENA_V << SLCHOST_SLC0_TOHOST_BIT3_INT_ENA_S)
#define SLCHOST_SLC0_TOHOST_BIT3_INT_ENA_V  0x00000001U
#define SLCHOST_SLC0_TOHOST_BIT3_INT_ENA_S  3
/** SLCHOST_SLC0_TOHOST_BIT4_INT_ENA : R/W; bitpos: [4]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0_TOHOST_BIT4_INT_ENA    (BIT(4))
#define SLCHOST_SLC0_TOHOST_BIT4_INT_ENA_M  (SLCHOST_SLC0_TOHOST_BIT4_INT_ENA_V << SLCHOST_SLC0_TOHOST_BIT4_INT_ENA_S)
#define SLCHOST_SLC0_TOHOST_BIT4_INT_ENA_V  0x00000001U
#define SLCHOST_SLC0_TOHOST_BIT4_INT_ENA_S  4
/** SLCHOST_SLC0_TOHOST_BIT5_INT_ENA : R/W; bitpos: [5]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0_TOHOST_BIT5_INT_ENA    (BIT(5))
#define SLCHOST_SLC0_TOHOST_BIT5_INT_ENA_M  (SLCHOST_SLC0_TOHOST_BIT5_INT_ENA_V << SLCHOST_SLC0_TOHOST_BIT5_INT_ENA_S)
#define SLCHOST_SLC0_TOHOST_BIT5_INT_ENA_V  0x00000001U
#define SLCHOST_SLC0_TOHOST_BIT5_INT_ENA_S  5
/** SLCHOST_SLC0_TOHOST_BIT6_INT_ENA : R/W; bitpos: [6]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0_TOHOST_BIT6_INT_ENA    (BIT(6))
#define SLCHOST_SLC0_TOHOST_BIT6_INT_ENA_M  (SLCHOST_SLC0_TOHOST_BIT6_INT_ENA_V << SLCHOST_SLC0_TOHOST_BIT6_INT_ENA_S)
#define SLCHOST_SLC0_TOHOST_BIT6_INT_ENA_V  0x00000001U
#define SLCHOST_SLC0_TOHOST_BIT6_INT_ENA_S  6
/** SLCHOST_SLC0_TOHOST_BIT7_INT_ENA : R/W; bitpos: [7]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0_TOHOST_BIT7_INT_ENA    (BIT(7))
#define SLCHOST_SLC0_TOHOST_BIT7_INT_ENA_M  (SLCHOST_SLC0_TOHOST_BIT7_INT_ENA_V << SLCHOST_SLC0_TOHOST_BIT7_INT_ENA_S)
#define SLCHOST_SLC0_TOHOST_BIT7_INT_ENA_V  0x00000001U
#define SLCHOST_SLC0_TOHOST_BIT7_INT_ENA_S  7
/** SLCHOST_SLC0_TOKEN0_1TO0_INT_ENA : R/W; bitpos: [8]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0_TOKEN0_1TO0_INT_ENA    (BIT(8))
#define SLCHOST_SLC0_TOKEN0_1TO0_INT_ENA_M  (SLCHOST_SLC0_TOKEN0_1TO0_INT_ENA_V << SLCHOST_SLC0_TOKEN0_1TO0_INT_ENA_S)
#define SLCHOST_SLC0_TOKEN0_1TO0_INT_ENA_V  0x00000001U
#define SLCHOST_SLC0_TOKEN0_1TO0_INT_ENA_S  8
/** SLCHOST_SLC0_TOKEN1_1TO0_INT_ENA : R/W; bitpos: [9]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0_TOKEN1_1TO0_INT_ENA    (BIT(9))
#define SLCHOST_SLC0_TOKEN1_1TO0_INT_ENA_M  (SLCHOST_SLC0_TOKEN1_1TO0_INT_ENA_V << SLCHOST_SLC0_TOKEN1_1TO0_INT_ENA_S)
#define SLCHOST_SLC0_TOKEN1_1TO0_INT_ENA_V  0x00000001U
#define SLCHOST_SLC0_TOKEN1_1TO0_INT_ENA_S  9
/** SLCHOST_SLC0_TOKEN0_0TO1_INT_ENA : R/W; bitpos: [10]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0_TOKEN0_0TO1_INT_ENA    (BIT(10))
#define SLCHOST_SLC0_TOKEN0_0TO1_INT_ENA_M  (SLCHOST_SLC0_TOKEN0_0TO1_INT_ENA_V << SLCHOST_SLC0_TOKEN0_0TO1_INT_ENA_S)
#define SLCHOST_SLC0_TOKEN0_0TO1_INT_ENA_V  0x00000001U
#define SLCHOST_SLC0_TOKEN0_0TO1_INT_ENA_S  10
/** SLCHOST_SLC0_TOKEN1_0TO1_INT_ENA : R/W; bitpos: [11]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0_TOKEN1_0TO1_INT_ENA    (BIT(11))
#define SLCHOST_SLC0_TOKEN1_0TO1_INT_ENA_M  (SLCHOST_SLC0_TOKEN1_0TO1_INT_ENA_V << SLCHOST_SLC0_TOKEN1_0TO1_INT_ENA_S)
#define SLCHOST_SLC0_TOKEN1_0TO1_INT_ENA_V  0x00000001U
#define SLCHOST_SLC0_TOKEN1_0TO1_INT_ENA_S  11
/** SLCHOST_SLC0HOST_RX_SOF_INT_ENA : R/W; bitpos: [12]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0HOST_RX_SOF_INT_ENA    (BIT(12))
#define SLCHOST_SLC0HOST_RX_SOF_INT_ENA_M  (SLCHOST_SLC0HOST_RX_SOF_INT_ENA_V << SLCHOST_SLC0HOST_RX_SOF_INT_ENA_S)
#define SLCHOST_SLC0HOST_RX_SOF_INT_ENA_V  0x00000001U
#define SLCHOST_SLC0HOST_RX_SOF_INT_ENA_S  12
/** SLCHOST_SLC0HOST_RX_EOF_INT_ENA : R/W; bitpos: [13]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0HOST_RX_EOF_INT_ENA    (BIT(13))
#define SLCHOST_SLC0HOST_RX_EOF_INT_ENA_M  (SLCHOST_SLC0HOST_RX_EOF_INT_ENA_V << SLCHOST_SLC0HOST_RX_EOF_INT_ENA_S)
#define SLCHOST_SLC0HOST_RX_EOF_INT_ENA_V  0x00000001U
#define SLCHOST_SLC0HOST_RX_EOF_INT_ENA_S  13
/** SLCHOST_SLC0HOST_RX_START_INT_ENA : R/W; bitpos: [14]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0HOST_RX_START_INT_ENA    (BIT(14))
#define SLCHOST_SLC0HOST_RX_START_INT_ENA_M  (SLCHOST_SLC0HOST_RX_START_INT_ENA_V << SLCHOST_SLC0HOST_RX_START_INT_ENA_S)
#define SLCHOST_SLC0HOST_RX_START_INT_ENA_V  0x00000001U
#define SLCHOST_SLC0HOST_RX_START_INT_ENA_S  14
/** SLCHOST_SLC0HOST_TX_START_INT_ENA : R/W; bitpos: [15]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0HOST_TX_START_INT_ENA    (BIT(15))
#define SLCHOST_SLC0HOST_TX_START_INT_ENA_M  (SLCHOST_SLC0HOST_TX_START_INT_ENA_V << SLCHOST_SLC0HOST_TX_START_INT_ENA_S)
#define SLCHOST_SLC0HOST_TX_START_INT_ENA_V  0x00000001U
#define SLCHOST_SLC0HOST_TX_START_INT_ENA_S  15
/** SLCHOST_SLC0_RX_UDF_INT_ENA : R/W; bitpos: [16]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0_RX_UDF_INT_ENA    (BIT(16))
#define SLCHOST_SLC0_RX_UDF_INT_ENA_M  (SLCHOST_SLC0_RX_UDF_INT_ENA_V << SLCHOST_SLC0_RX_UDF_INT_ENA_S)
#define SLCHOST_SLC0_RX_UDF_INT_ENA_V  0x00000001U
#define SLCHOST_SLC0_RX_UDF_INT_ENA_S  16
/** SLCHOST_SLC0_TX_OVF_INT_ENA : R/W; bitpos: [17]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0_TX_OVF_INT_ENA    (BIT(17))
#define SLCHOST_SLC0_TX_OVF_INT_ENA_M  (SLCHOST_SLC0_TX_OVF_INT_ENA_V << SLCHOST_SLC0_TX_OVF_INT_ENA_S)
#define SLCHOST_SLC0_TX_OVF_INT_ENA_V  0x00000001U
#define SLCHOST_SLC0_TX_OVF_INT_ENA_S  17
/** SLCHOST_SLC0_RX_PF_VALID_INT_ENA : R/W; bitpos: [18]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0_RX_PF_VALID_INT_ENA    (BIT(18))
#define SLCHOST_SLC0_RX_PF_VALID_INT_ENA_M  (SLCHOST_SLC0_RX_PF_VALID_INT_ENA_V << SLCHOST_SLC0_RX_PF_VALID_INT_ENA_S)
#define SLCHOST_SLC0_RX_PF_VALID_INT_ENA_V  0x00000001U
#define SLCHOST_SLC0_RX_PF_VALID_INT_ENA_S  18
/** SLCHOST_SLC0_EXT_BIT0_INT_ENA : R/W; bitpos: [19]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0_EXT_BIT0_INT_ENA    (BIT(19))
#define SLCHOST_SLC0_EXT_BIT0_INT_ENA_M  (SLCHOST_SLC0_EXT_BIT0_INT_ENA_V << SLCHOST_SLC0_EXT_BIT0_INT_ENA_S)
#define SLCHOST_SLC0_EXT_BIT0_INT_ENA_V  0x00000001U
#define SLCHOST_SLC0_EXT_BIT0_INT_ENA_S  19
/** SLCHOST_SLC0_EXT_BIT1_INT_ENA : R/W; bitpos: [20]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0_EXT_BIT1_INT_ENA    (BIT(20))
#define SLCHOST_SLC0_EXT_BIT1_INT_ENA_M  (SLCHOST_SLC0_EXT_BIT1_INT_ENA_V << SLCHOST_SLC0_EXT_BIT1_INT_ENA_S)
#define SLCHOST_SLC0_EXT_BIT1_INT_ENA_V  0x00000001U
#define SLCHOST_SLC0_EXT_BIT1_INT_ENA_S  20
/** SLCHOST_SLC0_EXT_BIT2_INT_ENA : R/W; bitpos: [21]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0_EXT_BIT2_INT_ENA    (BIT(21))
#define SLCHOST_SLC0_EXT_BIT2_INT_ENA_M  (SLCHOST_SLC0_EXT_BIT2_INT_ENA_V << SLCHOST_SLC0_EXT_BIT2_INT_ENA_S)
#define SLCHOST_SLC0_EXT_BIT2_INT_ENA_V  0x00000001U
#define SLCHOST_SLC0_EXT_BIT2_INT_ENA_S  21
/** SLCHOST_SLC0_EXT_BIT3_INT_ENA : R/W; bitpos: [22]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0_EXT_BIT3_INT_ENA    (BIT(22))
#define SLCHOST_SLC0_EXT_BIT3_INT_ENA_M  (SLCHOST_SLC0_EXT_BIT3_INT_ENA_V << SLCHOST_SLC0_EXT_BIT3_INT_ENA_S)
#define SLCHOST_SLC0_EXT_BIT3_INT_ENA_V  0x00000001U
#define SLCHOST_SLC0_EXT_BIT3_INT_ENA_S  22
/** SLCHOST_SLC0_RX_NEW_PACKET_INT_ENA : R/W; bitpos: [23]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0_RX_NEW_PACKET_INT_ENA    (BIT(23))
#define SLCHOST_SLC0_RX_NEW_PACKET_INT_ENA_M  (SLCHOST_SLC0_RX_NEW_PACKET_INT_ENA_V << SLCHOST_SLC0_RX_NEW_PACKET_INT_ENA_S)
#define SLCHOST_SLC0_RX_NEW_PACKET_INT_ENA_V  0x00000001U
#define SLCHOST_SLC0_RX_NEW_PACKET_INT_ENA_S  23
/** SLCHOST_SLC0_HOST_RD_RETRY_INT_ENA : R/W; bitpos: [24]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0_HOST_RD_RETRY_INT_ENA    (BIT(24))
#define SLCHOST_SLC0_HOST_RD_RETRY_INT_ENA_M  (SLCHOST_SLC0_HOST_RD_RETRY_INT_ENA_V << SLCHOST_SLC0_HOST_RD_RETRY_INT_ENA_S)
#define SLCHOST_SLC0_HOST_RD_RETRY_INT_ENA_V  0x00000001U
#define SLCHOST_SLC0_HOST_RD_RETRY_INT_ENA_S  24
/** SLCHOST_GPIO_SDIO_INT_ENA : R/W; bitpos: [25]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_GPIO_SDIO_INT_ENA    (BIT(25))
#define SLCHOST_GPIO_SDIO_INT_ENA_M  (SLCHOST_GPIO_SDIO_INT_ENA_V << SLCHOST_GPIO_SDIO_INT_ENA_S)
#define SLCHOST_GPIO_SDIO_INT_ENA_V  0x00000001U
#define SLCHOST_GPIO_SDIO_INT_ENA_S  25

/** SLCHOST_SLC1HOST_INT_ENA_REG register
 *  *******Description***********
 */
#define SLCHOST_SLC1HOST_INT_ENA_REG (DR_REG_SLCHOST_BASE + 0xf0)
/** SLCHOST_SLC1_TOHOST_BIT0_INT_ENA : R/W; bitpos: [0]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1_TOHOST_BIT0_INT_ENA    (BIT(0))
#define SLCHOST_SLC1_TOHOST_BIT0_INT_ENA_M  (SLCHOST_SLC1_TOHOST_BIT0_INT_ENA_V << SLCHOST_SLC1_TOHOST_BIT0_INT_ENA_S)
#define SLCHOST_SLC1_TOHOST_BIT0_INT_ENA_V  0x00000001U
#define SLCHOST_SLC1_TOHOST_BIT0_INT_ENA_S  0
/** SLCHOST_SLC1_TOHOST_BIT1_INT_ENA : R/W; bitpos: [1]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1_TOHOST_BIT1_INT_ENA    (BIT(1))
#define SLCHOST_SLC1_TOHOST_BIT1_INT_ENA_M  (SLCHOST_SLC1_TOHOST_BIT1_INT_ENA_V << SLCHOST_SLC1_TOHOST_BIT1_INT_ENA_S)
#define SLCHOST_SLC1_TOHOST_BIT1_INT_ENA_V  0x00000001U
#define SLCHOST_SLC1_TOHOST_BIT1_INT_ENA_S  1
/** SLCHOST_SLC1_TOHOST_BIT2_INT_ENA : R/W; bitpos: [2]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1_TOHOST_BIT2_INT_ENA    (BIT(2))
#define SLCHOST_SLC1_TOHOST_BIT2_INT_ENA_M  (SLCHOST_SLC1_TOHOST_BIT2_INT_ENA_V << SLCHOST_SLC1_TOHOST_BIT2_INT_ENA_S)
#define SLCHOST_SLC1_TOHOST_BIT2_INT_ENA_V  0x00000001U
#define SLCHOST_SLC1_TOHOST_BIT2_INT_ENA_S  2
/** SLCHOST_SLC1_TOHOST_BIT3_INT_ENA : R/W; bitpos: [3]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1_TOHOST_BIT3_INT_ENA    (BIT(3))
#define SLCHOST_SLC1_TOHOST_BIT3_INT_ENA_M  (SLCHOST_SLC1_TOHOST_BIT3_INT_ENA_V << SLCHOST_SLC1_TOHOST_BIT3_INT_ENA_S)
#define SLCHOST_SLC1_TOHOST_BIT3_INT_ENA_V  0x00000001U
#define SLCHOST_SLC1_TOHOST_BIT3_INT_ENA_S  3
/** SLCHOST_SLC1_TOHOST_BIT4_INT_ENA : R/W; bitpos: [4]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1_TOHOST_BIT4_INT_ENA    (BIT(4))
#define SLCHOST_SLC1_TOHOST_BIT4_INT_ENA_M  (SLCHOST_SLC1_TOHOST_BIT4_INT_ENA_V << SLCHOST_SLC1_TOHOST_BIT4_INT_ENA_S)
#define SLCHOST_SLC1_TOHOST_BIT4_INT_ENA_V  0x00000001U
#define SLCHOST_SLC1_TOHOST_BIT4_INT_ENA_S  4
/** SLCHOST_SLC1_TOHOST_BIT5_INT_ENA : R/W; bitpos: [5]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1_TOHOST_BIT5_INT_ENA    (BIT(5))
#define SLCHOST_SLC1_TOHOST_BIT5_INT_ENA_M  (SLCHOST_SLC1_TOHOST_BIT5_INT_ENA_V << SLCHOST_SLC1_TOHOST_BIT5_INT_ENA_S)
#define SLCHOST_SLC1_TOHOST_BIT5_INT_ENA_V  0x00000001U
#define SLCHOST_SLC1_TOHOST_BIT5_INT_ENA_S  5
/** SLCHOST_SLC1_TOHOST_BIT6_INT_ENA : R/W; bitpos: [6]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1_TOHOST_BIT6_INT_ENA    (BIT(6))
#define SLCHOST_SLC1_TOHOST_BIT6_INT_ENA_M  (SLCHOST_SLC1_TOHOST_BIT6_INT_ENA_V << SLCHOST_SLC1_TOHOST_BIT6_INT_ENA_S)
#define SLCHOST_SLC1_TOHOST_BIT6_INT_ENA_V  0x00000001U
#define SLCHOST_SLC1_TOHOST_BIT6_INT_ENA_S  6
/** SLCHOST_SLC1_TOHOST_BIT7_INT_ENA : R/W; bitpos: [7]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1_TOHOST_BIT7_INT_ENA    (BIT(7))
#define SLCHOST_SLC1_TOHOST_BIT7_INT_ENA_M  (SLCHOST_SLC1_TOHOST_BIT7_INT_ENA_V << SLCHOST_SLC1_TOHOST_BIT7_INT_ENA_S)
#define SLCHOST_SLC1_TOHOST_BIT7_INT_ENA_V  0x00000001U
#define SLCHOST_SLC1_TOHOST_BIT7_INT_ENA_S  7
/** SLCHOST_SLC1_TOKEN0_1TO0_INT_ENA : R/W; bitpos: [8]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1_TOKEN0_1TO0_INT_ENA    (BIT(8))
#define SLCHOST_SLC1_TOKEN0_1TO0_INT_ENA_M  (SLCHOST_SLC1_TOKEN0_1TO0_INT_ENA_V << SLCHOST_SLC1_TOKEN0_1TO0_INT_ENA_S)
#define SLCHOST_SLC1_TOKEN0_1TO0_INT_ENA_V  0x00000001U
#define SLCHOST_SLC1_TOKEN0_1TO0_INT_ENA_S  8
/** SLCHOST_SLC1_TOKEN1_1TO0_INT_ENA : R/W; bitpos: [9]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1_TOKEN1_1TO0_INT_ENA    (BIT(9))
#define SLCHOST_SLC1_TOKEN1_1TO0_INT_ENA_M  (SLCHOST_SLC1_TOKEN1_1TO0_INT_ENA_V << SLCHOST_SLC1_TOKEN1_1TO0_INT_ENA_S)
#define SLCHOST_SLC1_TOKEN1_1TO0_INT_ENA_V  0x00000001U
#define SLCHOST_SLC1_TOKEN1_1TO0_INT_ENA_S  9
/** SLCHOST_SLC1_TOKEN0_0TO1_INT_ENA : R/W; bitpos: [10]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1_TOKEN0_0TO1_INT_ENA    (BIT(10))
#define SLCHOST_SLC1_TOKEN0_0TO1_INT_ENA_M  (SLCHOST_SLC1_TOKEN0_0TO1_INT_ENA_V << SLCHOST_SLC1_TOKEN0_0TO1_INT_ENA_S)
#define SLCHOST_SLC1_TOKEN0_0TO1_INT_ENA_V  0x00000001U
#define SLCHOST_SLC1_TOKEN0_0TO1_INT_ENA_S  10
/** SLCHOST_SLC1_TOKEN1_0TO1_INT_ENA : R/W; bitpos: [11]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1_TOKEN1_0TO1_INT_ENA    (BIT(11))
#define SLCHOST_SLC1_TOKEN1_0TO1_INT_ENA_M  (SLCHOST_SLC1_TOKEN1_0TO1_INT_ENA_V << SLCHOST_SLC1_TOKEN1_0TO1_INT_ENA_S)
#define SLCHOST_SLC1_TOKEN1_0TO1_INT_ENA_V  0x00000001U
#define SLCHOST_SLC1_TOKEN1_0TO1_INT_ENA_S  11
/** SLCHOST_SLC1HOST_RX_SOF_INT_ENA : R/W; bitpos: [12]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1HOST_RX_SOF_INT_ENA    (BIT(12))
#define SLCHOST_SLC1HOST_RX_SOF_INT_ENA_M  (SLCHOST_SLC1HOST_RX_SOF_INT_ENA_V << SLCHOST_SLC1HOST_RX_SOF_INT_ENA_S)
#define SLCHOST_SLC1HOST_RX_SOF_INT_ENA_V  0x00000001U
#define SLCHOST_SLC1HOST_RX_SOF_INT_ENA_S  12
/** SLCHOST_SLC1HOST_RX_EOF_INT_ENA : R/W; bitpos: [13]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1HOST_RX_EOF_INT_ENA    (BIT(13))
#define SLCHOST_SLC1HOST_RX_EOF_INT_ENA_M  (SLCHOST_SLC1HOST_RX_EOF_INT_ENA_V << SLCHOST_SLC1HOST_RX_EOF_INT_ENA_S)
#define SLCHOST_SLC1HOST_RX_EOF_INT_ENA_V  0x00000001U
#define SLCHOST_SLC1HOST_RX_EOF_INT_ENA_S  13
/** SLCHOST_SLC1HOST_RX_START_INT_ENA : R/W; bitpos: [14]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1HOST_RX_START_INT_ENA    (BIT(14))
#define SLCHOST_SLC1HOST_RX_START_INT_ENA_M  (SLCHOST_SLC1HOST_RX_START_INT_ENA_V << SLCHOST_SLC1HOST_RX_START_INT_ENA_S)
#define SLCHOST_SLC1HOST_RX_START_INT_ENA_V  0x00000001U
#define SLCHOST_SLC1HOST_RX_START_INT_ENA_S  14
/** SLCHOST_SLC1HOST_TX_START_INT_ENA : R/W; bitpos: [15]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1HOST_TX_START_INT_ENA    (BIT(15))
#define SLCHOST_SLC1HOST_TX_START_INT_ENA_M  (SLCHOST_SLC1HOST_TX_START_INT_ENA_V << SLCHOST_SLC1HOST_TX_START_INT_ENA_S)
#define SLCHOST_SLC1HOST_TX_START_INT_ENA_V  0x00000001U
#define SLCHOST_SLC1HOST_TX_START_INT_ENA_S  15
/** SLCHOST_SLC1_RX_UDF_INT_ENA : R/W; bitpos: [16]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1_RX_UDF_INT_ENA    (BIT(16))
#define SLCHOST_SLC1_RX_UDF_INT_ENA_M  (SLCHOST_SLC1_RX_UDF_INT_ENA_V << SLCHOST_SLC1_RX_UDF_INT_ENA_S)
#define SLCHOST_SLC1_RX_UDF_INT_ENA_V  0x00000001U
#define SLCHOST_SLC1_RX_UDF_INT_ENA_S  16
/** SLCHOST_SLC1_TX_OVF_INT_ENA : R/W; bitpos: [17]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1_TX_OVF_INT_ENA    (BIT(17))
#define SLCHOST_SLC1_TX_OVF_INT_ENA_M  (SLCHOST_SLC1_TX_OVF_INT_ENA_V << SLCHOST_SLC1_TX_OVF_INT_ENA_S)
#define SLCHOST_SLC1_TX_OVF_INT_ENA_V  0x00000001U
#define SLCHOST_SLC1_TX_OVF_INT_ENA_S  17
/** SLCHOST_SLC1_RX_PF_VALID_INT_ENA : R/W; bitpos: [18]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1_RX_PF_VALID_INT_ENA    (BIT(18))
#define SLCHOST_SLC1_RX_PF_VALID_INT_ENA_M  (SLCHOST_SLC1_RX_PF_VALID_INT_ENA_V << SLCHOST_SLC1_RX_PF_VALID_INT_ENA_S)
#define SLCHOST_SLC1_RX_PF_VALID_INT_ENA_V  0x00000001U
#define SLCHOST_SLC1_RX_PF_VALID_INT_ENA_S  18
/** SLCHOST_SLC1_EXT_BIT0_INT_ENA : R/W; bitpos: [19]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1_EXT_BIT0_INT_ENA    (BIT(19))
#define SLCHOST_SLC1_EXT_BIT0_INT_ENA_M  (SLCHOST_SLC1_EXT_BIT0_INT_ENA_V << SLCHOST_SLC1_EXT_BIT0_INT_ENA_S)
#define SLCHOST_SLC1_EXT_BIT0_INT_ENA_V  0x00000001U
#define SLCHOST_SLC1_EXT_BIT0_INT_ENA_S  19
/** SLCHOST_SLC1_EXT_BIT1_INT_ENA : R/W; bitpos: [20]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1_EXT_BIT1_INT_ENA    (BIT(20))
#define SLCHOST_SLC1_EXT_BIT1_INT_ENA_M  (SLCHOST_SLC1_EXT_BIT1_INT_ENA_V << SLCHOST_SLC1_EXT_BIT1_INT_ENA_S)
#define SLCHOST_SLC1_EXT_BIT1_INT_ENA_V  0x00000001U
#define SLCHOST_SLC1_EXT_BIT1_INT_ENA_S  20
/** SLCHOST_SLC1_EXT_BIT2_INT_ENA : R/W; bitpos: [21]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1_EXT_BIT2_INT_ENA    (BIT(21))
#define SLCHOST_SLC1_EXT_BIT2_INT_ENA_M  (SLCHOST_SLC1_EXT_BIT2_INT_ENA_V << SLCHOST_SLC1_EXT_BIT2_INT_ENA_S)
#define SLCHOST_SLC1_EXT_BIT2_INT_ENA_V  0x00000001U
#define SLCHOST_SLC1_EXT_BIT2_INT_ENA_S  21
/** SLCHOST_SLC1_EXT_BIT3_INT_ENA : R/W; bitpos: [22]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1_EXT_BIT3_INT_ENA    (BIT(22))
#define SLCHOST_SLC1_EXT_BIT3_INT_ENA_M  (SLCHOST_SLC1_EXT_BIT3_INT_ENA_V << SLCHOST_SLC1_EXT_BIT3_INT_ENA_S)
#define SLCHOST_SLC1_EXT_BIT3_INT_ENA_V  0x00000001U
#define SLCHOST_SLC1_EXT_BIT3_INT_ENA_S  22
/** SLCHOST_SLC1_WIFI_RX_NEW_PACKET_INT_ENA : R/W; bitpos: [23]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1_WIFI_RX_NEW_PACKET_INT_ENA    (BIT(23))
#define SLCHOST_SLC1_WIFI_RX_NEW_PACKET_INT_ENA_M  (SLCHOST_SLC1_WIFI_RX_NEW_PACKET_INT_ENA_V << SLCHOST_SLC1_WIFI_RX_NEW_PACKET_INT_ENA_S)
#define SLCHOST_SLC1_WIFI_RX_NEW_PACKET_INT_ENA_V  0x00000001U
#define SLCHOST_SLC1_WIFI_RX_NEW_PACKET_INT_ENA_S  23
/** SLCHOST_SLC1_HOST_RD_RETRY_INT_ENA : R/W; bitpos: [24]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1_HOST_RD_RETRY_INT_ENA    (BIT(24))
#define SLCHOST_SLC1_HOST_RD_RETRY_INT_ENA_M  (SLCHOST_SLC1_HOST_RD_RETRY_INT_ENA_V << SLCHOST_SLC1_HOST_RD_RETRY_INT_ENA_S)
#define SLCHOST_SLC1_HOST_RD_RETRY_INT_ENA_V  0x00000001U
#define SLCHOST_SLC1_HOST_RD_RETRY_INT_ENA_S  24
/** SLCHOST_SLC1_BT_RX_NEW_PACKET_INT_ENA : R/W; bitpos: [25]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1_BT_RX_NEW_PACKET_INT_ENA    (BIT(25))
#define SLCHOST_SLC1_BT_RX_NEW_PACKET_INT_ENA_M  (SLCHOST_SLC1_BT_RX_NEW_PACKET_INT_ENA_V << SLCHOST_SLC1_BT_RX_NEW_PACKET_INT_ENA_S)
#define SLCHOST_SLC1_BT_RX_NEW_PACKET_INT_ENA_V  0x00000001U
#define SLCHOST_SLC1_BT_RX_NEW_PACKET_INT_ENA_S  25

/** SLCHOST_SLC0HOST_RX_INFOR_REG register
 *  *******Description***********
 */
#define SLCHOST_SLC0HOST_RX_INFOR_REG (DR_REG_SLCHOST_BASE + 0xf4)
/** SLCHOST_SLC0HOST_RX_INFOR : R/W; bitpos: [19:0]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0HOST_RX_INFOR    0x000FFFFFU
#define SLCHOST_SLC0HOST_RX_INFOR_M  (SLCHOST_SLC0HOST_RX_INFOR_V << SLCHOST_SLC0HOST_RX_INFOR_S)
#define SLCHOST_SLC0HOST_RX_INFOR_V  0x000FFFFFU
#define SLCHOST_SLC0HOST_RX_INFOR_S  0

/** SLCHOST_SLC1HOST_RX_INFOR_REG register
 *  *******Description***********
 */
#define SLCHOST_SLC1HOST_RX_INFOR_REG (DR_REG_SLCHOST_BASE + 0xf8)
/** SLCHOST_SLC1HOST_RX_INFOR : R/W; bitpos: [19:0]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1HOST_RX_INFOR    0x000FFFFFU
#define SLCHOST_SLC1HOST_RX_INFOR_M  (SLCHOST_SLC1HOST_RX_INFOR_V << SLCHOST_SLC1HOST_RX_INFOR_S)
#define SLCHOST_SLC1HOST_RX_INFOR_V  0x000FFFFFU
#define SLCHOST_SLC1HOST_RX_INFOR_S  0

/** SLCHOST_SLC0HOST_LEN_WD_REG register
 *  *******Description***********
 */
#define SLCHOST_SLC0HOST_LEN_WD_REG (DR_REG_SLCHOST_BASE + 0xfc)
/** SLCHOST_SLC0HOST_LEN_WD : R/W; bitpos: [31:0]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0HOST_LEN_WD    0xFFFFFFFFU
#define SLCHOST_SLC0HOST_LEN_WD_M  (SLCHOST_SLC0HOST_LEN_WD_V << SLCHOST_SLC0HOST_LEN_WD_S)
#define SLCHOST_SLC0HOST_LEN_WD_V  0xFFFFFFFFU
#define SLCHOST_SLC0HOST_LEN_WD_S  0

/** SLCHOST_SLC_APBWIN_WDATA_REG register
 *  *******Description***********
 */
#define SLCHOST_SLC_APBWIN_WDATA_REG (DR_REG_SLCHOST_BASE + 0x100)
/** SLCHOST_SLC_APBWIN_WDATA : R/W; bitpos: [31:0]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC_APBWIN_WDATA    0xFFFFFFFFU
#define SLCHOST_SLC_APBWIN_WDATA_M  (SLCHOST_SLC_APBWIN_WDATA_V << SLCHOST_SLC_APBWIN_WDATA_S)
#define SLCHOST_SLC_APBWIN_WDATA_V  0xFFFFFFFFU
#define SLCHOST_SLC_APBWIN_WDATA_S  0

/** SLCHOST_SLC_APBWIN_CONF_REG register
 *  *******Description***********
 */
#define SLCHOST_SLC_APBWIN_CONF_REG (DR_REG_SLCHOST_BASE + 0x104)
/** SLCHOST_SLC_APBWIN_ADDR : R/W; bitpos: [27:0]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC_APBWIN_ADDR    0x0FFFFFFFU
#define SLCHOST_SLC_APBWIN_ADDR_M  (SLCHOST_SLC_APBWIN_ADDR_V << SLCHOST_SLC_APBWIN_ADDR_S)
#define SLCHOST_SLC_APBWIN_ADDR_V  0x0FFFFFFFU
#define SLCHOST_SLC_APBWIN_ADDR_S  0
/** SLCHOST_SLC_APBWIN_WR : R/W; bitpos: [28]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC_APBWIN_WR    (BIT(28))
#define SLCHOST_SLC_APBWIN_WR_M  (SLCHOST_SLC_APBWIN_WR_V << SLCHOST_SLC_APBWIN_WR_S)
#define SLCHOST_SLC_APBWIN_WR_V  0x00000001U
#define SLCHOST_SLC_APBWIN_WR_S  28
/** SLCHOST_SLC_APBWIN_START : R/W/SC; bitpos: [29]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC_APBWIN_START    (BIT(29))
#define SLCHOST_SLC_APBWIN_START_M  (SLCHOST_SLC_APBWIN_START_V << SLCHOST_SLC_APBWIN_START_S)
#define SLCHOST_SLC_APBWIN_START_V  0x00000001U
#define SLCHOST_SLC_APBWIN_START_S  29

/** SLCHOST_SLC_APBWIN_RDATA_REG register
 *  *******Description***********
 */
#define SLCHOST_SLC_APBWIN_RDATA_REG (DR_REG_SLCHOST_BASE + 0x108)
/** SLCHOST_SLC_APBWIN_RDATA : RO; bitpos: [31:0]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC_APBWIN_RDATA    0xFFFFFFFFU
#define SLCHOST_SLC_APBWIN_RDATA_M  (SLCHOST_SLC_APBWIN_RDATA_V << SLCHOST_SLC_APBWIN_RDATA_S)
#define SLCHOST_SLC_APBWIN_RDATA_V  0xFFFFFFFFU
#define SLCHOST_SLC_APBWIN_RDATA_S  0

/** SLCHOST_RDCLR0_REG register
 *  *******Description***********
 */
#define SLCHOST_RDCLR0_REG (DR_REG_SLCHOST_BASE + 0x10c)
/** SLCHOST_SLCHOST_SLC0_BIT7_CLRADDR : R/W; bitpos: [8:0]; default: 68;
 *  *******Description***********
 */
#define SLCHOST_SLCHOST_SLC0_BIT7_CLRADDR    0x000001FFU
#define SLCHOST_SLCHOST_SLC0_BIT7_CLRADDR_M  (SLCHOST_SLCHOST_SLC0_BIT7_CLRADDR_V << SLCHOST_SLCHOST_SLC0_BIT7_CLRADDR_S)
#define SLCHOST_SLCHOST_SLC0_BIT7_CLRADDR_V  0x000001FFU
#define SLCHOST_SLCHOST_SLC0_BIT7_CLRADDR_S  0
/** SLCHOST_SLCHOST_SLC0_BIT6_CLRADDR : R/W; bitpos: [17:9]; default: 480;
 *  *******Description***********
 */
#define SLCHOST_SLCHOST_SLC0_BIT6_CLRADDR    0x000001FFU
#define SLCHOST_SLCHOST_SLC0_BIT6_CLRADDR_M  (SLCHOST_SLCHOST_SLC0_BIT6_CLRADDR_V << SLCHOST_SLCHOST_SLC0_BIT6_CLRADDR_S)
#define SLCHOST_SLCHOST_SLC0_BIT6_CLRADDR_V  0x000001FFU
#define SLCHOST_SLCHOST_SLC0_BIT6_CLRADDR_S  9

/** SLCHOST_RDCLR1_REG register
 *  *******Description***********
 */
#define SLCHOST_RDCLR1_REG (DR_REG_SLCHOST_BASE + 0x110)
/** SLCHOST_SLCHOST_SLC1_BIT7_CLRADDR : R/W; bitpos: [8:0]; default: 480;
 *  *******Description***********
 */
#define SLCHOST_SLCHOST_SLC1_BIT7_CLRADDR    0x000001FFU
#define SLCHOST_SLCHOST_SLC1_BIT7_CLRADDR_M  (SLCHOST_SLCHOST_SLC1_BIT7_CLRADDR_V << SLCHOST_SLCHOST_SLC1_BIT7_CLRADDR_S)
#define SLCHOST_SLCHOST_SLC1_BIT7_CLRADDR_V  0x000001FFU
#define SLCHOST_SLCHOST_SLC1_BIT7_CLRADDR_S  0
/** SLCHOST_SLCHOST_SLC1_BIT6_CLRADDR : R/W; bitpos: [17:9]; default: 480;
 *  *******Description***********
 */
#define SLCHOST_SLCHOST_SLC1_BIT6_CLRADDR    0x000001FFU
#define SLCHOST_SLCHOST_SLC1_BIT6_CLRADDR_M  (SLCHOST_SLCHOST_SLC1_BIT6_CLRADDR_V << SLCHOST_SLCHOST_SLC1_BIT6_CLRADDR_S)
#define SLCHOST_SLCHOST_SLC1_BIT6_CLRADDR_V  0x000001FFU
#define SLCHOST_SLCHOST_SLC1_BIT6_CLRADDR_S  9

/** SLCHOST_SLC0HOST_INT_ENA1_REG register
 *  *******Description***********
 */
#define SLCHOST_SLC0HOST_INT_ENA1_REG (DR_REG_SLCHOST_BASE + 0x114)
/** SLCHOST_SLC0_TOHOST_BIT0_INT_ENA1 : R/W; bitpos: [0]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0_TOHOST_BIT0_INT_ENA1    (BIT(0))
#define SLCHOST_SLC0_TOHOST_BIT0_INT_ENA1_M  (SLCHOST_SLC0_TOHOST_BIT0_INT_ENA1_V << SLCHOST_SLC0_TOHOST_BIT0_INT_ENA1_S)
#define SLCHOST_SLC0_TOHOST_BIT0_INT_ENA1_V  0x00000001U
#define SLCHOST_SLC0_TOHOST_BIT0_INT_ENA1_S  0
/** SLCHOST_SLC0_TOHOST_BIT1_INT_ENA1 : R/W; bitpos: [1]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0_TOHOST_BIT1_INT_ENA1    (BIT(1))
#define SLCHOST_SLC0_TOHOST_BIT1_INT_ENA1_M  (SLCHOST_SLC0_TOHOST_BIT1_INT_ENA1_V << SLCHOST_SLC0_TOHOST_BIT1_INT_ENA1_S)
#define SLCHOST_SLC0_TOHOST_BIT1_INT_ENA1_V  0x00000001U
#define SLCHOST_SLC0_TOHOST_BIT1_INT_ENA1_S  1
/** SLCHOST_SLC0_TOHOST_BIT2_INT_ENA1 : R/W; bitpos: [2]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0_TOHOST_BIT2_INT_ENA1    (BIT(2))
#define SLCHOST_SLC0_TOHOST_BIT2_INT_ENA1_M  (SLCHOST_SLC0_TOHOST_BIT2_INT_ENA1_V << SLCHOST_SLC0_TOHOST_BIT2_INT_ENA1_S)
#define SLCHOST_SLC0_TOHOST_BIT2_INT_ENA1_V  0x00000001U
#define SLCHOST_SLC0_TOHOST_BIT2_INT_ENA1_S  2
/** SLCHOST_SLC0_TOHOST_BIT3_INT_ENA1 : R/W; bitpos: [3]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0_TOHOST_BIT3_INT_ENA1    (BIT(3))
#define SLCHOST_SLC0_TOHOST_BIT3_INT_ENA1_M  (SLCHOST_SLC0_TOHOST_BIT3_INT_ENA1_V << SLCHOST_SLC0_TOHOST_BIT3_INT_ENA1_S)
#define SLCHOST_SLC0_TOHOST_BIT3_INT_ENA1_V  0x00000001U
#define SLCHOST_SLC0_TOHOST_BIT3_INT_ENA1_S  3
/** SLCHOST_SLC0_TOHOST_BIT4_INT_ENA1 : R/W; bitpos: [4]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0_TOHOST_BIT4_INT_ENA1    (BIT(4))
#define SLCHOST_SLC0_TOHOST_BIT4_INT_ENA1_M  (SLCHOST_SLC0_TOHOST_BIT4_INT_ENA1_V << SLCHOST_SLC0_TOHOST_BIT4_INT_ENA1_S)
#define SLCHOST_SLC0_TOHOST_BIT4_INT_ENA1_V  0x00000001U
#define SLCHOST_SLC0_TOHOST_BIT4_INT_ENA1_S  4
/** SLCHOST_SLC0_TOHOST_BIT5_INT_ENA1 : R/W; bitpos: [5]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0_TOHOST_BIT5_INT_ENA1    (BIT(5))
#define SLCHOST_SLC0_TOHOST_BIT5_INT_ENA1_M  (SLCHOST_SLC0_TOHOST_BIT5_INT_ENA1_V << SLCHOST_SLC0_TOHOST_BIT5_INT_ENA1_S)
#define SLCHOST_SLC0_TOHOST_BIT5_INT_ENA1_V  0x00000001U
#define SLCHOST_SLC0_TOHOST_BIT5_INT_ENA1_S  5
/** SLCHOST_SLC0_TOHOST_BIT6_INT_ENA1 : R/W; bitpos: [6]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0_TOHOST_BIT6_INT_ENA1    (BIT(6))
#define SLCHOST_SLC0_TOHOST_BIT6_INT_ENA1_M  (SLCHOST_SLC0_TOHOST_BIT6_INT_ENA1_V << SLCHOST_SLC0_TOHOST_BIT6_INT_ENA1_S)
#define SLCHOST_SLC0_TOHOST_BIT6_INT_ENA1_V  0x00000001U
#define SLCHOST_SLC0_TOHOST_BIT6_INT_ENA1_S  6
/** SLCHOST_SLC0_TOHOST_BIT7_INT_ENA1 : R/W; bitpos: [7]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0_TOHOST_BIT7_INT_ENA1    (BIT(7))
#define SLCHOST_SLC0_TOHOST_BIT7_INT_ENA1_M  (SLCHOST_SLC0_TOHOST_BIT7_INT_ENA1_V << SLCHOST_SLC0_TOHOST_BIT7_INT_ENA1_S)
#define SLCHOST_SLC0_TOHOST_BIT7_INT_ENA1_V  0x00000001U
#define SLCHOST_SLC0_TOHOST_BIT7_INT_ENA1_S  7
/** SLCHOST_SLC0_TOKEN0_1TO0_INT_ENA1 : R/W; bitpos: [8]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0_TOKEN0_1TO0_INT_ENA1    (BIT(8))
#define SLCHOST_SLC0_TOKEN0_1TO0_INT_ENA1_M  (SLCHOST_SLC0_TOKEN0_1TO0_INT_ENA1_V << SLCHOST_SLC0_TOKEN0_1TO0_INT_ENA1_S)
#define SLCHOST_SLC0_TOKEN0_1TO0_INT_ENA1_V  0x00000001U
#define SLCHOST_SLC0_TOKEN0_1TO0_INT_ENA1_S  8
/** SLCHOST_SLC0_TOKEN1_1TO0_INT_ENA1 : R/W; bitpos: [9]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0_TOKEN1_1TO0_INT_ENA1    (BIT(9))
#define SLCHOST_SLC0_TOKEN1_1TO0_INT_ENA1_M  (SLCHOST_SLC0_TOKEN1_1TO0_INT_ENA1_V << SLCHOST_SLC0_TOKEN1_1TO0_INT_ENA1_S)
#define SLCHOST_SLC0_TOKEN1_1TO0_INT_ENA1_V  0x00000001U
#define SLCHOST_SLC0_TOKEN1_1TO0_INT_ENA1_S  9
/** SLCHOST_SLC0_TOKEN0_0TO1_INT_ENA1 : R/W; bitpos: [10]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0_TOKEN0_0TO1_INT_ENA1    (BIT(10))
#define SLCHOST_SLC0_TOKEN0_0TO1_INT_ENA1_M  (SLCHOST_SLC0_TOKEN0_0TO1_INT_ENA1_V << SLCHOST_SLC0_TOKEN0_0TO1_INT_ENA1_S)
#define SLCHOST_SLC0_TOKEN0_0TO1_INT_ENA1_V  0x00000001U
#define SLCHOST_SLC0_TOKEN0_0TO1_INT_ENA1_S  10
/** SLCHOST_SLC0_TOKEN1_0TO1_INT_ENA1 : R/W; bitpos: [11]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0_TOKEN1_0TO1_INT_ENA1    (BIT(11))
#define SLCHOST_SLC0_TOKEN1_0TO1_INT_ENA1_M  (SLCHOST_SLC0_TOKEN1_0TO1_INT_ENA1_V << SLCHOST_SLC0_TOKEN1_0TO1_INT_ENA1_S)
#define SLCHOST_SLC0_TOKEN1_0TO1_INT_ENA1_V  0x00000001U
#define SLCHOST_SLC0_TOKEN1_0TO1_INT_ENA1_S  11
/** SLCHOST_SLC0HOST_RX_SOF_INT_ENA1 : R/W; bitpos: [12]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0HOST_RX_SOF_INT_ENA1    (BIT(12))
#define SLCHOST_SLC0HOST_RX_SOF_INT_ENA1_M  (SLCHOST_SLC0HOST_RX_SOF_INT_ENA1_V << SLCHOST_SLC0HOST_RX_SOF_INT_ENA1_S)
#define SLCHOST_SLC0HOST_RX_SOF_INT_ENA1_V  0x00000001U
#define SLCHOST_SLC0HOST_RX_SOF_INT_ENA1_S  12
/** SLCHOST_SLC0HOST_RX_EOF_INT_ENA1 : R/W; bitpos: [13]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0HOST_RX_EOF_INT_ENA1    (BIT(13))
#define SLCHOST_SLC0HOST_RX_EOF_INT_ENA1_M  (SLCHOST_SLC0HOST_RX_EOF_INT_ENA1_V << SLCHOST_SLC0HOST_RX_EOF_INT_ENA1_S)
#define SLCHOST_SLC0HOST_RX_EOF_INT_ENA1_V  0x00000001U
#define SLCHOST_SLC0HOST_RX_EOF_INT_ENA1_S  13
/** SLCHOST_SLC0HOST_RX_START_INT_ENA1 : R/W; bitpos: [14]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0HOST_RX_START_INT_ENA1    (BIT(14))
#define SLCHOST_SLC0HOST_RX_START_INT_ENA1_M  (SLCHOST_SLC0HOST_RX_START_INT_ENA1_V << SLCHOST_SLC0HOST_RX_START_INT_ENA1_S)
#define SLCHOST_SLC0HOST_RX_START_INT_ENA1_V  0x00000001U
#define SLCHOST_SLC0HOST_RX_START_INT_ENA1_S  14
/** SLCHOST_SLC0HOST_TX_START_INT_ENA1 : R/W; bitpos: [15]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0HOST_TX_START_INT_ENA1    (BIT(15))
#define SLCHOST_SLC0HOST_TX_START_INT_ENA1_M  (SLCHOST_SLC0HOST_TX_START_INT_ENA1_V << SLCHOST_SLC0HOST_TX_START_INT_ENA1_S)
#define SLCHOST_SLC0HOST_TX_START_INT_ENA1_V  0x00000001U
#define SLCHOST_SLC0HOST_TX_START_INT_ENA1_S  15
/** SLCHOST_SLC0_RX_UDF_INT_ENA1 : R/W; bitpos: [16]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0_RX_UDF_INT_ENA1    (BIT(16))
#define SLCHOST_SLC0_RX_UDF_INT_ENA1_M  (SLCHOST_SLC0_RX_UDF_INT_ENA1_V << SLCHOST_SLC0_RX_UDF_INT_ENA1_S)
#define SLCHOST_SLC0_RX_UDF_INT_ENA1_V  0x00000001U
#define SLCHOST_SLC0_RX_UDF_INT_ENA1_S  16
/** SLCHOST_SLC0_TX_OVF_INT_ENA1 : R/W; bitpos: [17]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0_TX_OVF_INT_ENA1    (BIT(17))
#define SLCHOST_SLC0_TX_OVF_INT_ENA1_M  (SLCHOST_SLC0_TX_OVF_INT_ENA1_V << SLCHOST_SLC0_TX_OVF_INT_ENA1_S)
#define SLCHOST_SLC0_TX_OVF_INT_ENA1_V  0x00000001U
#define SLCHOST_SLC0_TX_OVF_INT_ENA1_S  17
/** SLCHOST_SLC0_RX_PF_VALID_INT_ENA1 : R/W; bitpos: [18]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0_RX_PF_VALID_INT_ENA1    (BIT(18))
#define SLCHOST_SLC0_RX_PF_VALID_INT_ENA1_M  (SLCHOST_SLC0_RX_PF_VALID_INT_ENA1_V << SLCHOST_SLC0_RX_PF_VALID_INT_ENA1_S)
#define SLCHOST_SLC0_RX_PF_VALID_INT_ENA1_V  0x00000001U
#define SLCHOST_SLC0_RX_PF_VALID_INT_ENA1_S  18
/** SLCHOST_SLC0_EXT_BIT0_INT_ENA1 : R/W; bitpos: [19]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0_EXT_BIT0_INT_ENA1    (BIT(19))
#define SLCHOST_SLC0_EXT_BIT0_INT_ENA1_M  (SLCHOST_SLC0_EXT_BIT0_INT_ENA1_V << SLCHOST_SLC0_EXT_BIT0_INT_ENA1_S)
#define SLCHOST_SLC0_EXT_BIT0_INT_ENA1_V  0x00000001U
#define SLCHOST_SLC0_EXT_BIT0_INT_ENA1_S  19
/** SLCHOST_SLC0_EXT_BIT1_INT_ENA1 : R/W; bitpos: [20]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0_EXT_BIT1_INT_ENA1    (BIT(20))
#define SLCHOST_SLC0_EXT_BIT1_INT_ENA1_M  (SLCHOST_SLC0_EXT_BIT1_INT_ENA1_V << SLCHOST_SLC0_EXT_BIT1_INT_ENA1_S)
#define SLCHOST_SLC0_EXT_BIT1_INT_ENA1_V  0x00000001U
#define SLCHOST_SLC0_EXT_BIT1_INT_ENA1_S  20
/** SLCHOST_SLC0_EXT_BIT2_INT_ENA1 : R/W; bitpos: [21]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0_EXT_BIT2_INT_ENA1    (BIT(21))
#define SLCHOST_SLC0_EXT_BIT2_INT_ENA1_M  (SLCHOST_SLC0_EXT_BIT2_INT_ENA1_V << SLCHOST_SLC0_EXT_BIT2_INT_ENA1_S)
#define SLCHOST_SLC0_EXT_BIT2_INT_ENA1_V  0x00000001U
#define SLCHOST_SLC0_EXT_BIT2_INT_ENA1_S  21
/** SLCHOST_SLC0_EXT_BIT3_INT_ENA1 : R/W; bitpos: [22]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0_EXT_BIT3_INT_ENA1    (BIT(22))
#define SLCHOST_SLC0_EXT_BIT3_INT_ENA1_M  (SLCHOST_SLC0_EXT_BIT3_INT_ENA1_V << SLCHOST_SLC0_EXT_BIT3_INT_ENA1_S)
#define SLCHOST_SLC0_EXT_BIT3_INT_ENA1_V  0x00000001U
#define SLCHOST_SLC0_EXT_BIT3_INT_ENA1_S  22
/** SLCHOST_SLC0_RX_NEW_PACKET_INT_ENA1 : R/W; bitpos: [23]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0_RX_NEW_PACKET_INT_ENA1    (BIT(23))
#define SLCHOST_SLC0_RX_NEW_PACKET_INT_ENA1_M  (SLCHOST_SLC0_RX_NEW_PACKET_INT_ENA1_V << SLCHOST_SLC0_RX_NEW_PACKET_INT_ENA1_S)
#define SLCHOST_SLC0_RX_NEW_PACKET_INT_ENA1_V  0x00000001U
#define SLCHOST_SLC0_RX_NEW_PACKET_INT_ENA1_S  23
/** SLCHOST_SLC0_HOST_RD_RETRY_INT_ENA1 : R/W; bitpos: [24]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC0_HOST_RD_RETRY_INT_ENA1    (BIT(24))
#define SLCHOST_SLC0_HOST_RD_RETRY_INT_ENA1_M  (SLCHOST_SLC0_HOST_RD_RETRY_INT_ENA1_V << SLCHOST_SLC0_HOST_RD_RETRY_INT_ENA1_S)
#define SLCHOST_SLC0_HOST_RD_RETRY_INT_ENA1_V  0x00000001U
#define SLCHOST_SLC0_HOST_RD_RETRY_INT_ENA1_S  24
/** SLCHOST_GPIO_SDIO_INT_ENA1 : R/W; bitpos: [25]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_GPIO_SDIO_INT_ENA1    (BIT(25))
#define SLCHOST_GPIO_SDIO_INT_ENA1_M  (SLCHOST_GPIO_SDIO_INT_ENA1_V << SLCHOST_GPIO_SDIO_INT_ENA1_S)
#define SLCHOST_GPIO_SDIO_INT_ENA1_V  0x00000001U
#define SLCHOST_GPIO_SDIO_INT_ENA1_S  25

/** SLCHOST_SLC1HOST_INT_ENA1_REG register
 *  *******Description***********
 */
#define SLCHOST_SLC1HOST_INT_ENA1_REG (DR_REG_SLCHOST_BASE + 0x118)
/** SLCHOST_SLC1_TOHOST_BIT0_INT_ENA1 : R/W; bitpos: [0]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1_TOHOST_BIT0_INT_ENA1    (BIT(0))
#define SLCHOST_SLC1_TOHOST_BIT0_INT_ENA1_M  (SLCHOST_SLC1_TOHOST_BIT0_INT_ENA1_V << SLCHOST_SLC1_TOHOST_BIT0_INT_ENA1_S)
#define SLCHOST_SLC1_TOHOST_BIT0_INT_ENA1_V  0x00000001U
#define SLCHOST_SLC1_TOHOST_BIT0_INT_ENA1_S  0
/** SLCHOST_SLC1_TOHOST_BIT1_INT_ENA1 : R/W; bitpos: [1]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1_TOHOST_BIT1_INT_ENA1    (BIT(1))
#define SLCHOST_SLC1_TOHOST_BIT1_INT_ENA1_M  (SLCHOST_SLC1_TOHOST_BIT1_INT_ENA1_V << SLCHOST_SLC1_TOHOST_BIT1_INT_ENA1_S)
#define SLCHOST_SLC1_TOHOST_BIT1_INT_ENA1_V  0x00000001U
#define SLCHOST_SLC1_TOHOST_BIT1_INT_ENA1_S  1
/** SLCHOST_SLC1_TOHOST_BIT2_INT_ENA1 : R/W; bitpos: [2]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1_TOHOST_BIT2_INT_ENA1    (BIT(2))
#define SLCHOST_SLC1_TOHOST_BIT2_INT_ENA1_M  (SLCHOST_SLC1_TOHOST_BIT2_INT_ENA1_V << SLCHOST_SLC1_TOHOST_BIT2_INT_ENA1_S)
#define SLCHOST_SLC1_TOHOST_BIT2_INT_ENA1_V  0x00000001U
#define SLCHOST_SLC1_TOHOST_BIT2_INT_ENA1_S  2
/** SLCHOST_SLC1_TOHOST_BIT3_INT_ENA1 : R/W; bitpos: [3]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1_TOHOST_BIT3_INT_ENA1    (BIT(3))
#define SLCHOST_SLC1_TOHOST_BIT3_INT_ENA1_M  (SLCHOST_SLC1_TOHOST_BIT3_INT_ENA1_V << SLCHOST_SLC1_TOHOST_BIT3_INT_ENA1_S)
#define SLCHOST_SLC1_TOHOST_BIT3_INT_ENA1_V  0x00000001U
#define SLCHOST_SLC1_TOHOST_BIT3_INT_ENA1_S  3
/** SLCHOST_SLC1_TOHOST_BIT4_INT_ENA1 : R/W; bitpos: [4]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1_TOHOST_BIT4_INT_ENA1    (BIT(4))
#define SLCHOST_SLC1_TOHOST_BIT4_INT_ENA1_M  (SLCHOST_SLC1_TOHOST_BIT4_INT_ENA1_V << SLCHOST_SLC1_TOHOST_BIT4_INT_ENA1_S)
#define SLCHOST_SLC1_TOHOST_BIT4_INT_ENA1_V  0x00000001U
#define SLCHOST_SLC1_TOHOST_BIT4_INT_ENA1_S  4
/** SLCHOST_SLC1_TOHOST_BIT5_INT_ENA1 : R/W; bitpos: [5]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1_TOHOST_BIT5_INT_ENA1    (BIT(5))
#define SLCHOST_SLC1_TOHOST_BIT5_INT_ENA1_M  (SLCHOST_SLC1_TOHOST_BIT5_INT_ENA1_V << SLCHOST_SLC1_TOHOST_BIT5_INT_ENA1_S)
#define SLCHOST_SLC1_TOHOST_BIT5_INT_ENA1_V  0x00000001U
#define SLCHOST_SLC1_TOHOST_BIT5_INT_ENA1_S  5
/** SLCHOST_SLC1_TOHOST_BIT6_INT_ENA1 : R/W; bitpos: [6]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1_TOHOST_BIT6_INT_ENA1    (BIT(6))
#define SLCHOST_SLC1_TOHOST_BIT6_INT_ENA1_M  (SLCHOST_SLC1_TOHOST_BIT6_INT_ENA1_V << SLCHOST_SLC1_TOHOST_BIT6_INT_ENA1_S)
#define SLCHOST_SLC1_TOHOST_BIT6_INT_ENA1_V  0x00000001U
#define SLCHOST_SLC1_TOHOST_BIT6_INT_ENA1_S  6
/** SLCHOST_SLC1_TOHOST_BIT7_INT_ENA1 : R/W; bitpos: [7]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1_TOHOST_BIT7_INT_ENA1    (BIT(7))
#define SLCHOST_SLC1_TOHOST_BIT7_INT_ENA1_M  (SLCHOST_SLC1_TOHOST_BIT7_INT_ENA1_V << SLCHOST_SLC1_TOHOST_BIT7_INT_ENA1_S)
#define SLCHOST_SLC1_TOHOST_BIT7_INT_ENA1_V  0x00000001U
#define SLCHOST_SLC1_TOHOST_BIT7_INT_ENA1_S  7
/** SLCHOST_SLC1_TOKEN0_1TO0_INT_ENA1 : R/W; bitpos: [8]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1_TOKEN0_1TO0_INT_ENA1    (BIT(8))
#define SLCHOST_SLC1_TOKEN0_1TO0_INT_ENA1_M  (SLCHOST_SLC1_TOKEN0_1TO0_INT_ENA1_V << SLCHOST_SLC1_TOKEN0_1TO0_INT_ENA1_S)
#define SLCHOST_SLC1_TOKEN0_1TO0_INT_ENA1_V  0x00000001U
#define SLCHOST_SLC1_TOKEN0_1TO0_INT_ENA1_S  8
/** SLCHOST_SLC1_TOKEN1_1TO0_INT_ENA1 : R/W; bitpos: [9]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1_TOKEN1_1TO0_INT_ENA1    (BIT(9))
#define SLCHOST_SLC1_TOKEN1_1TO0_INT_ENA1_M  (SLCHOST_SLC1_TOKEN1_1TO0_INT_ENA1_V << SLCHOST_SLC1_TOKEN1_1TO0_INT_ENA1_S)
#define SLCHOST_SLC1_TOKEN1_1TO0_INT_ENA1_V  0x00000001U
#define SLCHOST_SLC1_TOKEN1_1TO0_INT_ENA1_S  9
/** SLCHOST_SLC1_TOKEN0_0TO1_INT_ENA1 : R/W; bitpos: [10]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1_TOKEN0_0TO1_INT_ENA1    (BIT(10))
#define SLCHOST_SLC1_TOKEN0_0TO1_INT_ENA1_M  (SLCHOST_SLC1_TOKEN0_0TO1_INT_ENA1_V << SLCHOST_SLC1_TOKEN0_0TO1_INT_ENA1_S)
#define SLCHOST_SLC1_TOKEN0_0TO1_INT_ENA1_V  0x00000001U
#define SLCHOST_SLC1_TOKEN0_0TO1_INT_ENA1_S  10
/** SLCHOST_SLC1_TOKEN1_0TO1_INT_ENA1 : R/W; bitpos: [11]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1_TOKEN1_0TO1_INT_ENA1    (BIT(11))
#define SLCHOST_SLC1_TOKEN1_0TO1_INT_ENA1_M  (SLCHOST_SLC1_TOKEN1_0TO1_INT_ENA1_V << SLCHOST_SLC1_TOKEN1_0TO1_INT_ENA1_S)
#define SLCHOST_SLC1_TOKEN1_0TO1_INT_ENA1_V  0x00000001U
#define SLCHOST_SLC1_TOKEN1_0TO1_INT_ENA1_S  11
/** SLCHOST_SLC1HOST_RX_SOF_INT_ENA1 : R/W; bitpos: [12]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1HOST_RX_SOF_INT_ENA1    (BIT(12))
#define SLCHOST_SLC1HOST_RX_SOF_INT_ENA1_M  (SLCHOST_SLC1HOST_RX_SOF_INT_ENA1_V << SLCHOST_SLC1HOST_RX_SOF_INT_ENA1_S)
#define SLCHOST_SLC1HOST_RX_SOF_INT_ENA1_V  0x00000001U
#define SLCHOST_SLC1HOST_RX_SOF_INT_ENA1_S  12
/** SLCHOST_SLC1HOST_RX_EOF_INT_ENA1 : R/W; bitpos: [13]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1HOST_RX_EOF_INT_ENA1    (BIT(13))
#define SLCHOST_SLC1HOST_RX_EOF_INT_ENA1_M  (SLCHOST_SLC1HOST_RX_EOF_INT_ENA1_V << SLCHOST_SLC1HOST_RX_EOF_INT_ENA1_S)
#define SLCHOST_SLC1HOST_RX_EOF_INT_ENA1_V  0x00000001U
#define SLCHOST_SLC1HOST_RX_EOF_INT_ENA1_S  13
/** SLCHOST_SLC1HOST_RX_START_INT_ENA1 : R/W; bitpos: [14]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1HOST_RX_START_INT_ENA1    (BIT(14))
#define SLCHOST_SLC1HOST_RX_START_INT_ENA1_M  (SLCHOST_SLC1HOST_RX_START_INT_ENA1_V << SLCHOST_SLC1HOST_RX_START_INT_ENA1_S)
#define SLCHOST_SLC1HOST_RX_START_INT_ENA1_V  0x00000001U
#define SLCHOST_SLC1HOST_RX_START_INT_ENA1_S  14
/** SLCHOST_SLC1HOST_TX_START_INT_ENA1 : R/W; bitpos: [15]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1HOST_TX_START_INT_ENA1    (BIT(15))
#define SLCHOST_SLC1HOST_TX_START_INT_ENA1_M  (SLCHOST_SLC1HOST_TX_START_INT_ENA1_V << SLCHOST_SLC1HOST_TX_START_INT_ENA1_S)
#define SLCHOST_SLC1HOST_TX_START_INT_ENA1_V  0x00000001U
#define SLCHOST_SLC1HOST_TX_START_INT_ENA1_S  15
/** SLCHOST_SLC1_RX_UDF_INT_ENA1 : R/W; bitpos: [16]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1_RX_UDF_INT_ENA1    (BIT(16))
#define SLCHOST_SLC1_RX_UDF_INT_ENA1_M  (SLCHOST_SLC1_RX_UDF_INT_ENA1_V << SLCHOST_SLC1_RX_UDF_INT_ENA1_S)
#define SLCHOST_SLC1_RX_UDF_INT_ENA1_V  0x00000001U
#define SLCHOST_SLC1_RX_UDF_INT_ENA1_S  16
/** SLCHOST_SLC1_TX_OVF_INT_ENA1 : R/W; bitpos: [17]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1_TX_OVF_INT_ENA1    (BIT(17))
#define SLCHOST_SLC1_TX_OVF_INT_ENA1_M  (SLCHOST_SLC1_TX_OVF_INT_ENA1_V << SLCHOST_SLC1_TX_OVF_INT_ENA1_S)
#define SLCHOST_SLC1_TX_OVF_INT_ENA1_V  0x00000001U
#define SLCHOST_SLC1_TX_OVF_INT_ENA1_S  17
/** SLCHOST_SLC1_RX_PF_VALID_INT_ENA1 : R/W; bitpos: [18]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1_RX_PF_VALID_INT_ENA1    (BIT(18))
#define SLCHOST_SLC1_RX_PF_VALID_INT_ENA1_M  (SLCHOST_SLC1_RX_PF_VALID_INT_ENA1_V << SLCHOST_SLC1_RX_PF_VALID_INT_ENA1_S)
#define SLCHOST_SLC1_RX_PF_VALID_INT_ENA1_V  0x00000001U
#define SLCHOST_SLC1_RX_PF_VALID_INT_ENA1_S  18
/** SLCHOST_SLC1_EXT_BIT0_INT_ENA1 : R/W; bitpos: [19]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1_EXT_BIT0_INT_ENA1    (BIT(19))
#define SLCHOST_SLC1_EXT_BIT0_INT_ENA1_M  (SLCHOST_SLC1_EXT_BIT0_INT_ENA1_V << SLCHOST_SLC1_EXT_BIT0_INT_ENA1_S)
#define SLCHOST_SLC1_EXT_BIT0_INT_ENA1_V  0x00000001U
#define SLCHOST_SLC1_EXT_BIT0_INT_ENA1_S  19
/** SLCHOST_SLC1_EXT_BIT1_INT_ENA1 : R/W; bitpos: [20]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1_EXT_BIT1_INT_ENA1    (BIT(20))
#define SLCHOST_SLC1_EXT_BIT1_INT_ENA1_M  (SLCHOST_SLC1_EXT_BIT1_INT_ENA1_V << SLCHOST_SLC1_EXT_BIT1_INT_ENA1_S)
#define SLCHOST_SLC1_EXT_BIT1_INT_ENA1_V  0x00000001U
#define SLCHOST_SLC1_EXT_BIT1_INT_ENA1_S  20
/** SLCHOST_SLC1_EXT_BIT2_INT_ENA1 : R/W; bitpos: [21]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1_EXT_BIT2_INT_ENA1    (BIT(21))
#define SLCHOST_SLC1_EXT_BIT2_INT_ENA1_M  (SLCHOST_SLC1_EXT_BIT2_INT_ENA1_V << SLCHOST_SLC1_EXT_BIT2_INT_ENA1_S)
#define SLCHOST_SLC1_EXT_BIT2_INT_ENA1_V  0x00000001U
#define SLCHOST_SLC1_EXT_BIT2_INT_ENA1_S  21
/** SLCHOST_SLC1_EXT_BIT3_INT_ENA1 : R/W; bitpos: [22]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1_EXT_BIT3_INT_ENA1    (BIT(22))
#define SLCHOST_SLC1_EXT_BIT3_INT_ENA1_M  (SLCHOST_SLC1_EXT_BIT3_INT_ENA1_V << SLCHOST_SLC1_EXT_BIT3_INT_ENA1_S)
#define SLCHOST_SLC1_EXT_BIT3_INT_ENA1_V  0x00000001U
#define SLCHOST_SLC1_EXT_BIT3_INT_ENA1_S  22
/** SLCHOST_SLC1_WIFI_RX_NEW_PACKET_INT_ENA1 : R/W; bitpos: [23]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1_WIFI_RX_NEW_PACKET_INT_ENA1    (BIT(23))
#define SLCHOST_SLC1_WIFI_RX_NEW_PACKET_INT_ENA1_M  (SLCHOST_SLC1_WIFI_RX_NEW_PACKET_INT_ENA1_V << SLCHOST_SLC1_WIFI_RX_NEW_PACKET_INT_ENA1_S)
#define SLCHOST_SLC1_WIFI_RX_NEW_PACKET_INT_ENA1_V  0x00000001U
#define SLCHOST_SLC1_WIFI_RX_NEW_PACKET_INT_ENA1_S  23
/** SLCHOST_SLC1_HOST_RD_RETRY_INT_ENA1 : R/W; bitpos: [24]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1_HOST_RD_RETRY_INT_ENA1    (BIT(24))
#define SLCHOST_SLC1_HOST_RD_RETRY_INT_ENA1_M  (SLCHOST_SLC1_HOST_RD_RETRY_INT_ENA1_V << SLCHOST_SLC1_HOST_RD_RETRY_INT_ENA1_S)
#define SLCHOST_SLC1_HOST_RD_RETRY_INT_ENA1_V  0x00000001U
#define SLCHOST_SLC1_HOST_RD_RETRY_INT_ENA1_S  24
/** SLCHOST_SLC1_BT_RX_NEW_PACKET_INT_ENA1 : R/W; bitpos: [25]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SLC1_BT_RX_NEW_PACKET_INT_ENA1    (BIT(25))
#define SLCHOST_SLC1_BT_RX_NEW_PACKET_INT_ENA1_M  (SLCHOST_SLC1_BT_RX_NEW_PACKET_INT_ENA1_V << SLCHOST_SLC1_BT_RX_NEW_PACKET_INT_ENA1_S)
#define SLCHOST_SLC1_BT_RX_NEW_PACKET_INT_ENA1_V  0x00000001U
#define SLCHOST_SLC1_BT_RX_NEW_PACKET_INT_ENA1_S  25

/** SLCHOST_SLCHOSTDATE_REG register
 *  *******Description***********
 */
#define SLCHOST_SLCHOSTDATE_REG (DR_REG_SLCHOST_BASE + 0x178)
/** SLCHOST_SLCHOST_DATE : R/W; bitpos: [31:0]; default: 554043136;
 *  *******Description***********
 */
#define SLCHOST_SLCHOST_DATE    0xFFFFFFFFU
#define SLCHOST_SLCHOST_DATE_M  (SLCHOST_SLCHOST_DATE_V << SLCHOST_SLCHOST_DATE_S)
#define SLCHOST_SLCHOST_DATE_V  0xFFFFFFFFU
#define SLCHOST_SLCHOST_DATE_S  0

/** SLCHOST_SLCHOSTID_REG register
 *  *******Description***********
 */
#define SLCHOST_SLCHOSTID_REG (DR_REG_SLCHOST_BASE + 0x17c)
/** SLCHOST_SLCHOST_ID : R/W; bitpos: [31:0]; default: 1536;
 *  *******Description***********
 */
#define SLCHOST_SLCHOST_ID    0xFFFFFFFFU
#define SLCHOST_SLCHOST_ID_M  (SLCHOST_SLCHOST_ID_V << SLCHOST_SLCHOST_ID_S)
#define SLCHOST_SLCHOST_ID_V  0xFFFFFFFFU
#define SLCHOST_SLCHOST_ID_S  0

/** SLCHOST_CONF_REG register
 *  *******Description***********
 */
#define SLCHOST_CONF_REG (DR_REG_SLCHOST_BASE + 0x1f0)
/** SLCHOST_FRC_SDIO11 : R/W; bitpos: [4:0]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_FRC_SDIO11    0x0000001FU
#define SLCHOST_FRC_SDIO11_M  (SLCHOST_FRC_SDIO11_V << SLCHOST_FRC_SDIO11_S)
#define SLCHOST_FRC_SDIO11_V  0x0000001FU
#define SLCHOST_FRC_SDIO11_S  0
/** SLCHOST_FRC_SDIO20 : R/W; bitpos: [9:5]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_FRC_SDIO20    0x0000001FU
#define SLCHOST_FRC_SDIO20_M  (SLCHOST_FRC_SDIO20_V << SLCHOST_FRC_SDIO20_S)
#define SLCHOST_FRC_SDIO20_V  0x0000001FU
#define SLCHOST_FRC_SDIO20_S  5
/** SLCHOST_FRC_NEG_SAMP : R/W; bitpos: [14:10]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_FRC_NEG_SAMP    0x0000001FU
#define SLCHOST_FRC_NEG_SAMP_M  (SLCHOST_FRC_NEG_SAMP_V << SLCHOST_FRC_NEG_SAMP_S)
#define SLCHOST_FRC_NEG_SAMP_V  0x0000001FU
#define SLCHOST_FRC_NEG_SAMP_S  10
/** SLCHOST_FRC_POS_SAMP : R/W; bitpos: [19:15]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_FRC_POS_SAMP    0x0000001FU
#define SLCHOST_FRC_POS_SAMP_M  (SLCHOST_FRC_POS_SAMP_V << SLCHOST_FRC_POS_SAMP_S)
#define SLCHOST_FRC_POS_SAMP_V  0x0000001FU
#define SLCHOST_FRC_POS_SAMP_S  15
/** SLCHOST_FRC_QUICK_IN : R/W; bitpos: [24:20]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_FRC_QUICK_IN    0x0000001FU
#define SLCHOST_FRC_QUICK_IN_M  (SLCHOST_FRC_QUICK_IN_V << SLCHOST_FRC_QUICK_IN_S)
#define SLCHOST_FRC_QUICK_IN_V  0x0000001FU
#define SLCHOST_FRC_QUICK_IN_S  20
/** SLCHOST_SDIO20_INT_DELAY : R/W; bitpos: [25]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SDIO20_INT_DELAY    (BIT(25))
#define SLCHOST_SDIO20_INT_DELAY_M  (SLCHOST_SDIO20_INT_DELAY_V << SLCHOST_SDIO20_INT_DELAY_S)
#define SLCHOST_SDIO20_INT_DELAY_V  0x00000001U
#define SLCHOST_SDIO20_INT_DELAY_S  25
/** SLCHOST_SDIO_PAD_PULLUP : R/W; bitpos: [26]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SDIO_PAD_PULLUP    (BIT(26))
#define SLCHOST_SDIO_PAD_PULLUP_M  (SLCHOST_SDIO_PAD_PULLUP_V << SLCHOST_SDIO_PAD_PULLUP_S)
#define SLCHOST_SDIO_PAD_PULLUP_V  0x00000001U
#define SLCHOST_SDIO_PAD_PULLUP_S  26
/** SLCHOST_HSPEED_CON_EN : R/W; bitpos: [27]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_HSPEED_CON_EN    (BIT(27))
#define SLCHOST_HSPEED_CON_EN_M  (SLCHOST_HSPEED_CON_EN_V << SLCHOST_HSPEED_CON_EN_S)
#define SLCHOST_HSPEED_CON_EN_V  0x00000001U
#define SLCHOST_HSPEED_CON_EN_S  27

/** SLCHOST_INF_ST_REG register
 *  *******Description***********
 */
#define SLCHOST_INF_ST_REG (DR_REG_SLCHOST_BASE + 0x1f4)
/** SLCHOST_SDIO20_MODE : RO; bitpos: [4:0]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SDIO20_MODE    0x0000001FU
#define SLCHOST_SDIO20_MODE_M  (SLCHOST_SDIO20_MODE_V << SLCHOST_SDIO20_MODE_S)
#define SLCHOST_SDIO20_MODE_V  0x0000001FU
#define SLCHOST_SDIO20_MODE_S  0
/** SLCHOST_SDIO_NEG_SAMP : RO; bitpos: [9:5]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SDIO_NEG_SAMP    0x0000001FU
#define SLCHOST_SDIO_NEG_SAMP_M  (SLCHOST_SDIO_NEG_SAMP_V << SLCHOST_SDIO_NEG_SAMP_S)
#define SLCHOST_SDIO_NEG_SAMP_V  0x0000001FU
#define SLCHOST_SDIO_NEG_SAMP_S  5
/** SLCHOST_SDIO_QUICK_IN : RO; bitpos: [14:10]; default: 0;
 *  *******Description***********
 */
#define SLCHOST_SDIO_QUICK_IN    0x0000001FU
#define SLCHOST_SDIO_QUICK_IN_M  (SLCHOST_SDIO_QUICK_IN_V << SLCHOST_SDIO_QUICK_IN_S)
#define SLCHOST_SDIO_QUICK_IN_V  0x0000001FU
#define SLCHOST_SDIO_QUICK_IN_S  10
/** SLCHOST_DLL_ON_SW : R/W; bitpos: [15]; default: 0;
 *  dll is controlled by software
 */
#define SLCHOST_DLL_ON_SW    (BIT(15))
#define SLCHOST_DLL_ON_SW_M  (SLCHOST_DLL_ON_SW_V << SLCHOST_DLL_ON_SW_S)
#define SLCHOST_DLL_ON_SW_V  0x00000001U
#define SLCHOST_DLL_ON_SW_S  15
/** SLCHOST_DLL_ON : R/W; bitpos: [16]; default: 0;
 *  Software dll on
 */
#define SLCHOST_DLL_ON    (BIT(16))
#define SLCHOST_DLL_ON_M  (SLCHOST_DLL_ON_V << SLCHOST_DLL_ON_S)
#define SLCHOST_DLL_ON_V  0x00000001U
#define SLCHOST_DLL_ON_S  16
/** SLCHOST_CLK_MODE_SW : R/W; bitpos: [17]; default: 0;
 *  dll clock mode is controlled by software
 */
#define SLCHOST_CLK_MODE_SW    (BIT(17))
#define SLCHOST_CLK_MODE_SW_M  (SLCHOST_CLK_MODE_SW_V << SLCHOST_CLK_MODE_SW_S)
#define SLCHOST_CLK_MODE_SW_V  0x00000001U
#define SLCHOST_CLK_MODE_SW_S  17
/** SLCHOST_CLK_MODE : R/W; bitpos: [19:18]; default: 0;
 *  Software set clock mode
 */
#define SLCHOST_CLK_MODE    0x00000003U
#define SLCHOST_CLK_MODE_M  (SLCHOST_CLK_MODE_V << SLCHOST_CLK_MODE_S)
#define SLCHOST_CLK_MODE_V  0x00000003U
#define SLCHOST_CLK_MODE_S  18

#ifdef __cplusplus
}
#endif
