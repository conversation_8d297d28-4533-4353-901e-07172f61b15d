#####################################################
# This file is auto-generated from SoC caps
# using gen_soc_caps_kconfig.py, do not edit manually
#####################################################

config SOC_ADC_SUPPORTED
    bool
    default y

config SOC_DEDICATED_GPIO_SUPPORTED
    bool
    default y

config SOC_UART_SUPPORTED
    bool
    default y

config SOC_GDMA_SUPPORTED
    bool
    default y

config SOC_AHB_GDMA_SUPPORTED
    bool
    default y

config SOC_GPTIMER_SUPPORTED
    bool
    default y

config SOC_PCNT_SUPPORTED
    bool
    default y

config SOC_MCPWM_SUPPORTED
    bool
    default y

config SOC_ETM_SUPPORTED
    bool
    default y

config SOC_PARLIO_SUPPORTED
    bool
    default y

config SOC_ASYNC_MEMCPY_SUPPORTED
    bool
    default y

config SOC_USB_SERIAL_JTAG_SUPPORTED
    bool
    default y

config SOC_TEMP_SENSOR_SUPPORTED
    bool
    default y

config SOC_WIFI_SUPPORTED
    bool
    default y

config SOC_SUPPORTS_SECURE_DL_MODE
    bool
    default y

config SOC_LP_CORE_SUPPORTED
    bool
    default y

config SOC_ULP_SUPPORTED
    bool
    default y

config SOC_EFUSE_KEY_PURPOSE_FIELD
    bool
    default y

config SOC_EFUSE_SUPPORTED
    bool
    default y

config SOC_RTC_FAST_MEM_SUPPORTED
    bool
    default y

config SOC_RTC_MEM_SUPPORTED
    bool
    default y

config SOC_I2S_SUPPORTED
    bool
    default y

config SOC_RMT_SUPPORTED
    bool
    default y

config SOC_SDM_SUPPORTED
    bool
    default y

config SOC_GPSPI_SUPPORTED
    bool
    default y

config SOC_LEDC_SUPPORTED
    bool
    default y

config SOC_I2C_SUPPORTED
    bool
    default y

config SOC_SYSTIMER_SUPPORTED
    bool
    default y

config SOC_AES_SUPPORTED
    bool
    default y

config SOC_MPI_SUPPORTED
    bool
    default y

config SOC_SHA_SUPPORTED
    bool
    default y

config SOC_RSA_SUPPORTED
    bool
    default y

config SOC_HMAC_SUPPORTED
    bool
    default y

config SOC_DIG_SIGN_SUPPORTED
    bool
    default y

config SOC_ECC_SUPPORTED
    bool
    default y

config SOC_ECC_EXTENDED_MODES_SUPPORTED
    bool
    default y

config SOC_FLASH_ENC_SUPPORTED
    bool
    default y

config SOC_SECURE_BOOT_SUPPORTED
    bool
    default y

config SOC_IEEE802154_SUPPORTED
    bool
    default y

config SOC_BOD_SUPPORTED
    bool
    default y

config SOC_APM_SUPPORTED
    bool
    default y

config SOC_PMU_SUPPORTED
    bool
    default y

config SOC_PAU_SUPPORTED
    bool
    default y

config SOC_LP_TIMER_SUPPORTED
    bool
    default y

config SOC_LP_AON_SUPPORTED
    bool
    default y

config SOC_LP_PERIPHERALS_SUPPORTED
    bool
    default y

config SOC_LP_I2C_SUPPORTED
    bool
    default y

config SOC_ULP_LP_UART_SUPPORTED
    bool
    default y

config SOC_CLK_TREE_SUPPORTED
    bool
    default y

config SOC_WDT_SUPPORTED
    bool
    default y

config SOC_SPI_FLASH_SUPPORTED
    bool
    default y

config SOC_ECDSA_SUPPORTED
    bool
    default y

config SOC_RNG_SUPPORTED
    bool
    default y

config SOC_MODEM_CLOCK_SUPPORTED
    bool
    default y

config SOC_LIGHT_SLEEP_SUPPORTED
    bool
    default y

config SOC_DEEP_SLEEP_SUPPORTED
    bool
    default y

config SOC_PM_SUPPORTED
    bool
    default y

config SOC_SPIRAM_SUPPORTED
    bool
    default y

config SOC_BT_SUPPORTED
    bool
    default y

config SOC_PHY_SUPPORTED
    bool
    default y

config SOC_XTAL_SUPPORT_40M
    bool
    default y

config SOC_XTAL_SUPPORT_48M
    bool
    default y

config SOC_AES_SUPPORT_DMA
    bool
    default y

config SOC_AES_GDMA
    bool
    default y

config SOC_AES_SUPPORT_AES_128
    bool
    default y

config SOC_AES_SUPPORT_AES_256
    bool
    default y

config SOC_ADC_DIG_CTRL_SUPPORTED
    bool
    default y

config SOC_ADC_DIG_IIR_FILTER_SUPPORTED
    bool
    default y

config SOC_ADC_MONITOR_SUPPORTED
    bool
    default y

config SOC_ADC_DMA_SUPPORTED
    bool
    default y

config SOC_ADC_PERIPH_NUM
    int
    default 1

config SOC_ADC_MAX_CHANNEL_NUM
    int
    default 6

config SOC_ADC_ATTEN_NUM
    int
    default 4

config SOC_ADC_DIGI_CONTROLLER_NUM
    int
    default 1

config SOC_ADC_PATT_LEN_MAX
    int
    default 8

config SOC_ADC_DIGI_MAX_BITWIDTH
    int
    default 12

config SOC_ADC_DIGI_MIN_BITWIDTH
    int
    default 12

config SOC_ADC_DIGI_IIR_FILTER_NUM
    int
    default 2

config SOC_ADC_DIGI_MONITOR_NUM
    int
    default 2

config SOC_ADC_DIGI_RESULT_BYTES
    int
    default 4

config SOC_ADC_DIGI_DATA_BYTES_PER_CONV
    int
    default 4

config SOC_ADC_SAMPLE_FREQ_THRES_HIGH
    int
    default 83333

config SOC_ADC_SAMPLE_FREQ_THRES_LOW
    int
    default 611

config SOC_ADC_RTC_MIN_BITWIDTH
    int
    default 12

config SOC_ADC_RTC_MAX_BITWIDTH
    int
    default 12

config SOC_ADC_TEMPERATURE_SHARE_INTR
    bool
    default y

config SOC_ADC_SHARED_POWER
    bool
    default y

config SOC_BROWNOUT_RESET_SUPPORTED
    bool
    default y

config SOC_SHARED_IDCACHE_SUPPORTED
    bool
    default y

config SOC_CACHE_WRITEBACK_SUPPORTED
    bool
    default y

config SOC_CACHE_FREEZE_SUPPORTED
    bool
    default y

config SOC_CPU_CORES_NUM
    int
    default 1

config SOC_CPU_INTR_NUM
    int
    default 32

config SOC_CPU_HAS_FLEXIBLE_INTC
    bool
    default y

config SOC_CPU_SUPPORT_WFE
    bool
    default y

config SOC_INT_CLIC_SUPPORTED
    bool
    default y

config SOC_INT_HW_NESTED_SUPPORTED
    bool
    default y

config SOC_BRANCH_PREDICTOR_SUPPORTED
    bool
    default y

config SOC_CPU_BREAKPOINTS_NUM
    int
    default 4

config SOC_CPU_WATCHPOINTS_NUM
    int
    default 4

config SOC_CPU_WATCHPOINT_MAX_REGION_SIZE
    hex
    default 0x100

config SOC_CPU_HAS_PMA
    bool
    default y

config SOC_CPU_IDRAM_SPLIT_USING_PMP
    bool
    default y

config SOC_CPU_PMP_REGION_GRANULARITY
    int
    default 128

config SOC_CPU_HAS_LOCKUP_RESET
    bool
    default y

config SOC_DS_SIGNATURE_MAX_BIT_LEN
    int
    default 3072

config SOC_DS_KEY_PARAM_MD_IV_LENGTH
    int
    default 16

config SOC_DS_KEY_CHECK_MAX_WAIT_US
    int
    default 1100

config SOC_DMA_CAN_ACCESS_FLASH
    bool
    default y

config SOC_AHB_GDMA_VERSION
    int
    default 2

config SOC_GDMA_NUM_GROUPS_MAX
    int
    default 1

config SOC_GDMA_PAIRS_PER_GROUP_MAX
    int
    default 3

config SOC_GDMA_SUPPORT_ETM
    bool
    default y

config SOC_GDMA_SUPPORT_SLEEP_RETENTION
    bool
    default y

config SOC_AHB_GDMA_SUPPORT_PSRAM
    bool
    default y

config SOC_ETM_GROUPS
    int
    default 1

config SOC_ETM_CHANNELS_PER_GROUP
    int
    default 50

config SOC_ETM_SUPPORT_SLEEP_RETENTION
    bool
    default y

config SOC_GPIO_PORT
    int
    default 1

config SOC_GPIO_PIN_COUNT
    int
    default 29

config SOC_GPIO_SUPPORT_PIN_GLITCH_FILTER
    bool
    default y

config SOC_GPIO_FLEX_GLITCH_FILTER_NUM
    int
    default 8

config SOC_GPIO_SUPPORT_PIN_HYS_FILTER
    bool
    default y

config SOC_GPIO_SUPPORT_ETM
    bool
    default y

config SOC_GPIO_SUPPORT_RTC_INDEPENDENT
    bool
    default y

config SOC_GPIO_SUPPORT_DEEPSLEEP_WAKEUP
    bool
    default y

config SOC_LP_IO_CLOCK_IS_INDEPENDENT
    bool
    default y

config SOC_GPIO_IN_RANGE_MAX
    int
    default 28

config SOC_GPIO_OUT_RANGE_MAX
    int
    default 28

config SOC_GPIO_DEEP_SLEEP_WAKE_VALID_GPIO_MASK
    int
    default 0

config SOC_GPIO_DEEP_SLEEP_WAKE_SUPPORTED_PIN_CNT
    int
    default 8

config SOC_GPIO_VALID_DIGITAL_IO_PAD_MASK
    hex
    default 0x0000000001FFFF00

config SOC_GPIO_SUPPORT_FORCE_HOLD
    bool
    default y

config SOC_GPIO_SUPPORT_HOLD_IO_IN_DSLP
    bool
    default y

config SOC_GPIO_SUPPORT_HOLD_SINGLE_IO_IN_DSLP
    bool
    default y

config SOC_GPIO_CLOCKOUT_CHANNEL_NUM
    int
    default 3

config SOC_RTCIO_PIN_COUNT
    int
    default 8

config SOC_RTCIO_INPUT_OUTPUT_SUPPORTED
    bool
    default y

config SOC_RTCIO_HOLD_SUPPORTED
    bool
    default y

config SOC_RTCIO_WAKE_SUPPORTED
    bool
    default y

config SOC_DEDIC_GPIO_OUT_CHANNELS_NUM
    int
    default 8

config SOC_DEDIC_GPIO_IN_CHANNELS_NUM
    int
    default 8

config SOC_DEDIC_PERIPH_ALWAYS_ENABLE
    bool
    default y

config SOC_I2C_NUM
    int
    default 2

config SOC_HP_I2C_NUM
    int
    default 1

config SOC_I2C_FIFO_LEN
    int
    default 32

config SOC_I2C_CMD_REG_NUM
    int
    default 8

config SOC_I2C_SUPPORT_SLAVE
    bool
    default y

config SOC_I2C_SUPPORT_HW_FSM_RST
    bool
    default y

config SOC_I2C_SUPPORT_XTAL
    bool
    default y

config SOC_I2C_SUPPORT_RTC
    bool
    default y

config SOC_I2C_SUPPORT_10BIT_ADDR
    bool
    default y

config SOC_I2C_SLAVE_SUPPORT_BROADCAST
    bool
    default y

config SOC_I2C_SLAVE_CAN_GET_STRETCH_CAUSE
    bool
    default y

config SOC_I2C_SLAVE_SUPPORT_I2CRAM_ACCESS
    bool
    default y

config SOC_I2C_SLAVE_SUPPORT_SLAVE_UNMATCH
    bool
    default y

config SOC_I2C_SUPPORT_SLEEP_RETENTION
    bool
    default y

config SOC_LP_I2C_NUM
    int
    default 1

config SOC_LP_I2C_FIFO_LEN
    int
    default 16

config SOC_I2S_NUM
    int
    default 1

config SOC_I2S_HW_VERSION_2
    bool
    default y

config SOC_I2S_SUPPORTS_ETM
    bool
    default y

config SOC_I2S_SUPPORTS_TX_SYNC_CNT
    bool
    default y

config SOC_I2S_SUPPORTS_XTAL
    bool
    default y

config SOC_I2S_SUPPORTS_PLL_F160M
    bool
    default y

config SOC_I2S_SUPPORTS_PLL_F240M
    bool
    default y

config SOC_I2S_SUPPORTS_PCM
    bool
    default y

config SOC_I2S_SUPPORTS_PDM
    bool
    default y

config SOC_I2S_SUPPORTS_PDM_TX
    bool
    default y

config SOC_I2S_PDM_MAX_TX_LINES
    int
    default 2

config SOC_I2S_SUPPORTS_TDM
    bool
    default y

config SOC_I2S_TDM_FULL_DATA_WIDTH
    bool
    default y

config SOC_I2S_SUPPORT_SLEEP_RETENTION
    bool
    default y

config SOC_LEDC_SUPPORT_PLL_DIV_CLOCK
    bool
    default y

config SOC_LEDC_SUPPORT_XTAL_CLOCK
    bool
    default y

config SOC_LEDC_TIMER_NUM
    int
    default 4

config SOC_LEDC_CHANNEL_NUM
    int
    default 6

config SOC_LEDC_TIMER_BIT_WIDTH
    int
    default 20

config SOC_LEDC_SUPPORT_FADE_STOP
    bool
    default y

config SOC_LEDC_GAMMA_CURVE_FADE_SUPPORTED
    bool
    default y

config SOC_LEDC_GAMMA_CURVE_FADE_RANGE_MAX
    int
    default 16

config SOC_LEDC_FADE_PARAMS_BIT_WIDTH
    int
    default 10

config SOC_LEDC_SUPPORT_SLEEP_RETENTION
    bool
    default y

config SOC_MMU_PERIPH_NUM
    int
    default 1

config SOC_MMU_LINEAR_ADDRESS_REGION_NUM
    int
    default 1

config SOC_MMU_DI_VADDR_SHARED
    bool
    default y

config SOC_PCNT_GROUPS
    int
    default 1

config SOC_PCNT_UNITS_PER_GROUP
    int
    default 4

config SOC_PCNT_CHANNELS_PER_UNIT
    int
    default 2

config SOC_PCNT_THRES_POINT_PER_UNIT
    int
    default 2

config SOC_PCNT_SUPPORT_RUNTIME_THRES_UPDATE
    bool
    default y

config SOC_PCNT_SUPPORT_CLEAR_SIGNAL
    bool
    default y

config SOC_PCNT_SUPPORT_STEP_NOTIFY
    bool
    default y

config SOC_PCNT_SUPPORT_SLEEP_RETENTION
    bool
    default y

config SOC_RMT_GROUPS
    int
    default 1

config SOC_RMT_TX_CANDIDATES_PER_GROUP
    int
    default 2

config SOC_RMT_RX_CANDIDATES_PER_GROUP
    int
    default 2

config SOC_RMT_CHANNELS_PER_GROUP
    int
    default 4

config SOC_RMT_MEM_WORDS_PER_CHANNEL
    int
    default 48

config SOC_RMT_SUPPORT_RX_PINGPONG
    bool
    default y

config SOC_RMT_SUPPORT_RX_DEMODULATION
    bool
    default y

config SOC_RMT_SUPPORT_TX_ASYNC_STOP
    bool
    default y

config SOC_RMT_SUPPORT_TX_LOOP_COUNT
    bool
    default y

config SOC_RMT_SUPPORT_TX_LOOP_AUTO_STOP
    bool
    default y

config SOC_RMT_SUPPORT_TX_SYNCHRO
    bool
    default y

config SOC_RMT_SUPPORT_TX_CARRIER_DATA_ONLY
    bool
    default y

config SOC_RMT_SUPPORT_XTAL
    bool
    default y

config SOC_RMT_SUPPORT_SLEEP_RETENTION
    bool
    default y

config SOC_MCPWM_GROUPS
    int
    default 1

config SOC_MCPWM_TIMERS_PER_GROUP
    int
    default 3

config SOC_MCPWM_OPERATORS_PER_GROUP
    int
    default 3

config SOC_MCPWM_COMPARATORS_PER_OPERATOR
    int
    default 2

config SOC_MCPWM_GENERATORS_PER_OPERATOR
    int
    default 2

config SOC_MCPWM_EVENT_COMPARATORS_PER_OPERATOR
    int
    default 2

config SOC_MCPWM_TRIGGERS_PER_OPERATOR
    int
    default 2

config SOC_MCPWM_GPIO_FAULTS_PER_GROUP
    int
    default 3

config SOC_MCPWM_CAPTURE_TIMERS_PER_GROUP
    bool
    default y

config SOC_MCPWM_CAPTURE_CHANNELS_PER_TIMER
    int
    default 3

config SOC_MCPWM_GPIO_SYNCHROS_PER_GROUP
    int
    default 3

config SOC_MCPWM_SWSYNC_CAN_PROPAGATE
    bool
    default y

config SOC_MCPWM_SUPPORT_ETM
    bool
    default y

config SOC_MCPWM_SUPPORT_EVENT_COMPARATOR
    bool
    default y

config SOC_MCPWM_CAPTURE_CLK_FROM_GROUP
    bool
    default y

config SOC_MCPWM_SUPPORT_SLEEP_RETENTION
    bool
    default y

config SOC_PARLIO_GROUPS
    int
    default 1

config SOC_PARLIO_TX_UNITS_PER_GROUP
    int
    default 1

config SOC_PARLIO_RX_UNITS_PER_GROUP
    int
    default 1

config SOC_PARLIO_TX_UNIT_MAX_DATA_WIDTH
    int
    default 8

config SOC_PARLIO_RX_UNIT_MAX_DATA_WIDTH
    int
    default 8

config SOC_PARLIO_TX_CLK_SUPPORT_GATING
    bool
    default y

config SOC_PARLIO_RX_CLK_SUPPORT_GATING
    bool
    default y

config SOC_PARLIO_RX_CLK_SUPPORT_OUTPUT
    bool
    default y

config SOC_PARLIO_TRANS_BIT_ALIGN
    bool
    default y

config SOC_PARLIO_TX_SIZE_BY_DMA
    bool
    default y

config SOC_PARLIO_SUPPORT_SLEEP_RETENTION
    bool
    default y

config SOC_MPI_MEM_BLOCKS_NUM
    int
    default 4

config SOC_MPI_OPERATIONS_NUM
    int
    default 3

config SOC_RSA_MAX_BIT_LEN
    int
    default 3072

config SOC_SHA_DMA_MAX_BUFFER_SIZE
    int
    default 3968

config SOC_SHA_SUPPORT_DMA
    bool
    default y

config SOC_SHA_SUPPORT_RESUME
    bool
    default y

config SOC_SHA_GDMA
    bool
    default y

config SOC_SHA_SUPPORT_SHA1
    bool
    default y

config SOC_SHA_SUPPORT_SHA224
    bool
    default y

config SOC_SHA_SUPPORT_SHA256
    bool
    default y

config SOC_ECC_CONSTANT_TIME_POINT_MUL
    bool
    default y

config SOC_ECDSA_SUPPORT_EXPORT_PUBKEY
    bool
    default y

config SOC_SDM_GROUPS
    int
    default 1

config SOC_SDM_CHANNELS_PER_GROUP
    int
    default 4

config SOC_SDM_CLK_SUPPORT_PLL_F80M
    bool
    default y

config SOC_SDM_CLK_SUPPORT_XTAL
    bool
    default y

config SOC_SPI_PERIPH_NUM
    int
    default 2

config SOC_SPI_MAX_CS_NUM
    int
    default 6

config SOC_SPI_MAXIMUM_BUFFER_SIZE
    int
    default 64

config SOC_SPI_SUPPORT_DDRCLK
    bool
    default y

config SOC_SPI_SLAVE_SUPPORT_SEG_TRANS
    bool
    default y

config SOC_SPI_SUPPORT_CD_SIG
    bool
    default y

config SOC_SPI_SUPPORT_CONTINUOUS_TRANS
    bool
    default y

config SOC_SPI_SUPPORT_SLAVE_HD_VER2
    bool
    default y

config SOC_SPI_SUPPORT_SLEEP_RETENTION
    bool
    default y

config SOC_SPI_SUPPORT_CLK_XTAL
    bool
    default y

config SOC_SPI_SUPPORT_CLK_PLL_F160M
    bool
    default y

config SOC_SPI_SUPPORT_CLK_RC_FAST
    bool
    default y

config SOC_MEMSPI_IS_INDEPENDENT
    bool
    default y

config SOC_SPI_MAX_PRE_DIVIDER
    int
    default 16

config SOC_SPIRAM_XIP_SUPPORTED
    bool
    default y

config SOC_SPI_MEM_SUPPORT_AUTO_WAIT_IDLE
    bool
    default y

config SOC_SPI_MEM_SUPPORT_AUTO_SUSPEND
    bool
    default y

config SOC_SPI_MEM_SUPPORT_AUTO_RESUME
    bool
    default y

config SOC_SPI_MEM_SUPPORT_IDLE_INTR
    bool
    default y

config SOC_SPI_MEM_SUPPORT_SW_SUSPEND
    bool
    default y

config SOC_SPI_MEM_SUPPORT_CHECK_SUS
    bool
    default y

config SOC_SPI_MEM_SUPPORT_WRAP
    bool
    default y

config SOC_MEMSPI_SRC_FREQ_80M_SUPPORTED
    bool
    default y

config SOC_MEMSPI_SRC_FREQ_40M_SUPPORTED
    bool
    default y

config SOC_MEMSPI_SRC_FREQ_20M_SUPPORTED
    bool
    default y

config SOC_MEMSPI_FLASH_CLK_SRC_IS_INDEPENDENT
    bool
    default y

config SOC_SYSTIMER_COUNTER_NUM
    int
    default 2

config SOC_SYSTIMER_ALARM_NUM
    int
    default 3

config SOC_SYSTIMER_BIT_WIDTH_LO
    int
    default 32

config SOC_SYSTIMER_BIT_WIDTH_HI
    int
    default 20

config SOC_SYSTIMER_FIXED_DIVIDER
    bool
    default y

config SOC_SYSTIMER_SUPPORT_RC_FAST
    bool
    default y

config SOC_SYSTIMER_INT_LEVEL
    bool
    default y

config SOC_SYSTIMER_ALARM_MISS_COMPENSATE
    bool
    default y

config SOC_SYSTIMER_SUPPORT_ETM
    bool
    default y

config SOC_LP_TIMER_BIT_WIDTH_LO
    int
    default 32

config SOC_LP_TIMER_BIT_WIDTH_HI
    int
    default 16

config SOC_TIMER_GROUPS
    int
    default 2

config SOC_TIMER_GROUP_TIMERS_PER_GROUP
    int
    default 1

config SOC_TIMER_GROUP_COUNTER_BIT_WIDTH
    int
    default 54

config SOC_TIMER_GROUP_SUPPORT_XTAL
    bool
    default y

config SOC_TIMER_GROUP_SUPPORT_RC_FAST
    bool
    default y

config SOC_TIMER_GROUP_TOTAL_TIMERS
    int
    default 2

config SOC_TIMER_SUPPORT_ETM
    bool
    default y

config SOC_TIMER_SUPPORT_SLEEP_RETENTION
    bool
    default y

config SOC_MWDT_SUPPORT_SLEEP_RETENTION
    bool
    default y

config SOC_EFUSE_ECDSA_KEY
    bool
    default y

config SOC_KEY_MANAGER_ECDSA_KEY_DEPLOY
    bool
    default y

config SOC_KEY_MANAGER_FE_KEY_DEPLOY
    bool
    default y

config SOC_SECURE_BOOT_V2_ECC
    bool
    default y

config SOC_EFUSE_SECURE_BOOT_KEY_DIGESTS
    int
    default 3

config SOC_EFUSE_REVOKE_BOOT_KEY_DIGESTS
    bool
    default y

config SOC_SUPPORT_SECURE_BOOT_REVOKE_KEY
    bool
    default y

config SOC_FLASH_ENCRYPTED_XTS_AES_BLOCK_MAX
    int
    default 64

config SOC_FLASH_ENCRYPTION_XTS_AES
    bool
    default y

config SOC_FLASH_ENCRYPTION_XTS_AES_128
    bool
    default y

config SOC_APM_CTRL_FILTER_SUPPORTED
    bool
    default y

config SOC_APM_LP_APM0_SUPPORTED
    bool
    default y

config SOC_CRYPTO_DPA_PROTECTION_SUPPORTED
    bool
    default y

config SOC_UART_NUM
    int
    default 3

config SOC_UART_HP_NUM
    int
    default 2

config SOC_UART_LP_NUM
    int
    default 1

config SOC_UART_FIFO_LEN
    int
    default 128

config SOC_LP_UART_FIFO_LEN
    int
    default 16

config SOC_UART_BITRATE_MAX
    int
    default 5000000

config SOC_UART_SUPPORT_PLL_F80M_CLK
    bool
    default y

config SOC_UART_SUPPORT_RTC_CLK
    bool
    default y

config SOC_UART_SUPPORT_XTAL_CLK
    bool
    default y

config SOC_UART_SUPPORT_WAKEUP_INT
    bool
    default y

config SOC_UART_HAS_LP_UART
    bool
    default y

config SOC_UART_SUPPORT_SLEEP_RETENTION
    bool
    default y

config SOC_UART_SUPPORT_FSM_TX_WAIT_SEND
    bool
    default y

config SOC_COEX_HW_PTI
    bool
    default y

config SOC_EXTERNAL_COEX_ADVANCE
    bool
    default y

config SOC_EXTERNAL_COEX_LEADER_TX_LINE
    bool
    default n

config SOC_WIFI_LIGHT_SLEEP_CLK_WIDTH
    int
    default 12

config SOC_PM_SUPPORT_WIFI_WAKEUP
    bool
    default y

config SOC_PM_SUPPORT_BEACON_WAKEUP
    bool
    default y

config SOC_PM_SUPPORT_BT_WAKEUP
    bool
    default y

config SOC_PM_SUPPORT_EXT1_WAKEUP
    bool
    default y

config SOC_PM_SUPPORT_EXT1_WAKEUP_MODE_PER_PIN
    bool
    default y

config SOC_PM_SUPPORT_CPU_PD
    bool
    default y

config SOC_PM_SUPPORT_MODEM_PD
    bool
    default y

config SOC_PM_SUPPORT_XTAL32K_PD
    bool
    default y

config SOC_PM_SUPPORT_RC32K_PD
    bool
    default y

config SOC_PM_SUPPORT_RC_FAST_PD
    bool
    default y

config SOC_PM_SUPPORT_VDDSDIO_PD
    bool
    default y

config SOC_PM_SUPPORT_TOP_PD
    bool
    default y

config SOC_PM_SUPPORT_HP_AON_PD
    bool
    default y

config SOC_PM_SUPPORT_MAC_BB_PD
    bool
    default y

config SOC_PM_SUPPORT_RTC_PERIPH_PD
    bool
    default y

config SOC_PM_SUPPORT_PMU_MODEM_STATE
    bool
    default y

config SOC_PM_SUPPORT_DEEPSLEEP_CHECK_STUB_ONLY
    bool
    default y

config SOC_PM_CPU_RETENTION_BY_SW
    bool
    default y

config SOC_PM_MODEM_RETENTION_BY_REGDMA
    bool
    default y

config SOC_EXT_MEM_CACHE_TAG_IN_CPU_DOMAIN
    bool
    default y

config SOC_PM_PAU_LINK_NUM
    int
    default 5

config SOC_PM_PAU_REGDMA_LINK_CONFIGURABLE
    bool
    default y

config SOC_PM_PAU_REGDMA_LINK_IDX_WIFIMAC
    int
    default 4

config SOC_PM_PAU_REGDMA_UPDATE_CACHE_BEFORE_WAIT_COMPARE
    bool
    default y

config SOC_PM_RETENTION_MODULE_NUM
    int
    default 32

config SOC_CLK_RC_FAST_SUPPORT_CALIBRATION
    bool
    default y

config SOC_MODEM_CLOCK_IS_INDEPENDENT
    bool
    default y

config SOC_CLK_XTAL32K_SUPPORTED
    bool
    default y

config SOC_CLK_OSC_SLOW_SUPPORTED
    bool
    default y

config SOC_CLK_LP_FAST_SUPPORT_XTAL
    bool
    default y

config SOC_RCC_IS_INDEPENDENT
    bool
    default y

config SOC_TEMPERATURE_SENSOR_SUPPORT_FAST_RC
    bool
    default y

config SOC_TEMPERATURE_SENSOR_SUPPORT_XTAL
    bool
    default y

config SOC_TEMPERATURE_SENSOR_INTR_SUPPORT
    bool
    default y

config SOC_TEMPERATURE_SENSOR_SUPPORT_SLEEP_RETENTION
    bool
    default y

config SOC_TEMPERATURE_SENSOR_UNDER_PD_TOP_DOMAIN
    bool
    default y

config SOC_WIFI_HW_TSF
    bool
    default y

config SOC_WIFI_FTM_SUPPORT
    bool
    default n

config SOC_WIFI_GCMP_SUPPORT
    bool
    default y

config SOC_WIFI_WAPI_SUPPORT
    bool
    default y

config SOC_WIFI_CSI_SUPPORT
    bool
    default y

config SOC_WIFI_MESH_SUPPORT
    bool
    default y

config SOC_WIFI_HE_SUPPORT
    bool
    default y

config SOC_WIFI_SUPPORT_5G
    bool
    default y

config SOC_WIFI_MAC_VERSION_NUM
    int
    default 3

config SOC_WIFI_NAN_SUPPORT
    bool
    default y

config SOC_BLE_SUPPORTED
    bool
    default y

config SOC_BLE_MESH_SUPPORTED
    bool
    default y

config SOC_ESP_NIMBLE_CONTROLLER
    bool
    default y

config SOC_BLE_50_SUPPORTED
    bool
    default y

config SOC_BLE_DEVICE_PRIVACY_SUPPORTED
    bool
    default y

config SOC_BLE_POWER_CONTROL_SUPPORTED
    bool
    default y

config SOC_BLE_MULTI_CONN_OPTIMIZATION
    bool
    default y

config SOC_BLE_PERIODIC_ADV_ENH_SUPPORTED
    bool
    default y

config SOC_LP_CORE_SINGLE_INTERRUPT_VECTOR
    bool
    default y

config SOC_LP_CORE_SUPPORT_ETM
    bool
    default y
