/*
 * SPDX-FileCopyrightText: 2024 Espressif Systems (Shanghai) CO LTD
 *
 * SPDX-License-Identifier: Apache-2.0
 */

#pragma once

#define RTCIO_GPIO0_CHANNEL         0   //RTCIO_CHANNEL_0
#define RTCIO_CHANNEL_0_GPIO_NUM    0

#define RTCIO_GPIO1_CHANNEL         1   //RTCIO_CHANNEL_1
#define RTCIO_CHANNEL_1_GPIO_NUM    1

#define RTCIO_GPIO2_CHANNEL         2   //RTCIO_CHANNEL_2
#define RTCIO_CHANNEL_2_GPIO_NUM    2

#define RTCIO_GPIO3_CHANNEL         3   //RTCIO_CHANNEL_3
#define RTCIO_CHANNEL_3_GPIO_NUM    3

#define RTCIO_GPIO4_CHANNEL         4   //RTCIO_CHANNEL_4
#define RTCIO_CHANNEL_4_GPIO_NUM    4

#define RTCIO_GPIO5_CHANNEL         5   //RTCIO_CHANNEL_5
#define RTCIO_CHANNEL_5_GPIO_NUM    5

#define RTCIO_GPIO6_CHANNEL         6   //RTCIO_CHANNEL_6
#define RTCIO_CHANNEL_6_GPIO_NUM    6

#define RTCIO_GPIO7_CHANNEL         7   //RTCIO_CHANNEL_7
#define RTCIO_CHANNEL_7_GPIO_NUM    7
