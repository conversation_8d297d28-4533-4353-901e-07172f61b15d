/*
 * SPDX-FileCopyrightText: 2024 Espressif Systems (Shanghai) CO LTD
 *
 * SPDX-License-Identifier: Apache-2.0
 */

#pragma once

#define PMU_ICG_APB_ENA_CAN0                18
#define PMU_ICG_APB_ENA_CAN1                19
#define PMU_ICG_APB_ENA_GDMA                1
#define PMU_ICG_APB_ENA_I2C                 13
#define PMU_ICG_APB_ENA_I2S                 4
#define PMU_ICG_APB_ENA_INTMTX              3
#define PMU_ICG_APB_ENA_IOMUX               26
#define PMU_ICG_APB_ENA_LEDC                14
#define PMU_ICG_APB_ENA_MEM_MONITOR         25
#define PMU_ICG_APB_ENA_MSPI                5
#define PMU_ICG_APB_ENA_PARL                23
#define PMU_ICG_APB_ENA_PCNT                20
#define PMU_ICG_APB_ENA_PVT_MONITOR         27
#define PMU_ICG_APB_ENA_PWM                 21
#define PMU_ICG_APB_ENA_REGDMA              24
#define PMU_ICG_APB_ENA_RMT                 15
#define PMU_ICG_APB_ENA_SARADC              9
#define PMU_ICG_APB_ENA_SEC                 0
#define PMU_ICG_APB_ENA_SOC_ETM             22
#define PMU_ICG_APB_ENA_SPI2                2
#define PMU_ICG_APB_ENA_SYSTIMER            16
#define PMU_ICG_APB_ENA_TG0                 11
#define PMU_ICG_APB_ENA_TG1                 12
#define PMU_ICG_APB_ENA_UART0               6
#define PMU_ICG_APB_ENA_UART1               7
#define PMU_ICG_APB_ENA_UHCI                8
#define PMU_ICG_APB_ENA_USB_DEVICE          17
#define PMU_ICG_FUNC_ENA_CAN0               31
#define PMU_ICG_FUNC_ENA_CAN1               30
#define PMU_ICG_FUNC_ENA_I2C                29
#define PMU_ICG_FUNC_ENA_I2S_RX             2
#define PMU_ICG_FUNC_ENA_I2S_TX             7
#define PMU_ICG_FUNC_ENA_IOMUX              28
#define PMU_ICG_FUNC_ENA_LEDC               27
#define PMU_ICG_FUNC_ENA_MEM_MONITOR        10
#define PMU_ICG_FUNC_ENA_MSPI               26
#define PMU_ICG_FUNC_ENA_PARL_RX            25
#define PMU_ICG_FUNC_ENA_PARL_TX            24
#define PMU_ICG_FUNC_ENA_PVT_MONITOR        23
#define PMU_ICG_FUNC_ENA_PWM                22
#define PMU_ICG_FUNC_ENA_RMT                21
#define PMU_ICG_FUNC_ENA_SARADC             20
#define PMU_ICG_FUNC_ENA_SEC                19
#define PMU_ICG_FUNC_ENA_SPI2               1
#define PMU_ICG_FUNC_ENA_SYSTIMER           18
#define PMU_ICG_FUNC_ENA_TG0                14
#define PMU_ICG_FUNC_ENA_TG1                13
#define PMU_ICG_FUNC_ENA_TSENS              12
#define PMU_ICG_FUNC_ENA_UART0              3
#define PMU_ICG_FUNC_ENA_UART1              4
#define PMU_ICG_FUNC_ENA_USB_DEVICE         6
#define PMU_ICG_FUNC_ENA_GDMA               0
#define PMU_ICG_FUNC_ENA_SOC_ETM            16
#define PMU_ICG_FUNC_ENA_REGDMA             8
#define PMU_ICG_FUNC_ENA_RETENTION          9
#define PMU_ICG_FUNC_ENA_SDIO_SLAVE         11
#define PMU_ICG_FUNC_ENA_UHCI               5
#define PMU_ICG_FUNC_ENA_HPCORE             17
#define PMU_ICG_FUNC_ENA_HPBUS              15
