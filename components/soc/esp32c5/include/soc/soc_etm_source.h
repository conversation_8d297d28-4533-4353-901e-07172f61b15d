/*
 * SPDX-FileCopyrightText: 2024 Espressif Systems (Shanghai) CO LTD
 *
 * SPDX-License-Identifier: Apache-2.0
 */

#pragma once

#define GPIO_EVT_CH0_RISE_EDGE                  1
#define GPIO_EVT_CH1_RISE_EDGE                  2
#define GPIO_EVT_CH2_RISE_EDGE                  3
#define GPIO_EVT_CH3_RISE_EDGE                  4
#define GPIO_EVT_CH4_RISE_EDGE                  5
#define GPIO_EVT_CH5_RISE_EDGE                  6
#define GPIO_EVT_CH6_RISE_EDGE                  7
#define GPIO_EVT_CH7_RISE_EDGE                  8
#define GPIO_EVT_CH0_FALL_EDGE                  9
#define GPIO_EVT_CH1_FALL_EDGE                  10
#define GPIO_EVT_CH2_FALL_EDGE                  11
#define GPIO_EVT_CH3_FALL_EDGE                  12
#define GPIO_EVT_CH4_FALL_EDGE                  13
#define GPIO_EVT_CH5_FALL_EDGE                  14
#define GPIO_EVT_CH6_FALL_EDGE                  15
#define GPIO_EVT_CH7_FALL_EDGE                  16
#define GPIO_EVT_CH0_ANY_EDGE                   17
#define GPIO_EVT_CH1_ANY_EDGE                   18
#define GPIO_EVT_CH2_ANY_EDGE                   19
#define GPIO_EVT_CH3_ANY_EDGE                   20
#define GPIO_EVT_CH4_ANY_EDGE                   21
#define GPIO_EVT_CH5_ANY_EDGE                   22
#define GPIO_EVT_CH6_ANY_EDGE                   23
#define GPIO_EVT_CH7_ANY_EDGE                   24
#define GPIO_EVT_ZERO_DET_POS0                  25
#define GPIO_EVT_ZERO_DET_NEG0                  26
#define LEDC_EVT_DUTY_CHNG_END_CH0              27
#define LEDC_EVT_DUTY_CHNG_END_CH1              28
#define LEDC_EVT_DUTY_CHNG_END_CH2              29
#define LEDC_EVT_DUTY_CHNG_END_CH3              30
#define LEDC_EVT_DUTY_CHNG_END_CH4              31
#define LEDC_EVT_DUTY_CHNG_END_CH5              32
#define LEDC_EVT_OVF_CNT_PLS_CH0                33
#define LEDC_EVT_OVF_CNT_PLS_CH1                34
#define LEDC_EVT_OVF_CNT_PLS_CH2                35
#define LEDC_EVT_OVF_CNT_PLS_CH3                36
#define LEDC_EVT_OVF_CNT_PLS_CH4                37
#define LEDC_EVT_OVF_CNT_PLS_CH5                38
#define LEDC_EVT_TIME_OVF_TIMER0                39
#define LEDC_EVT_TIME_OVF_TIMER1                40
#define LEDC_EVT_TIME_OVF_TIMER2                41
#define LEDC_EVT_TIME_OVF_TIMER3                42
#define LEDC_EVT_TIMER0_CMP                     43
#define LEDC_EVT_TIMER1_CMP                     44
#define LEDC_EVT_TIMER2_CMP                     45
#define LEDC_EVT_TIMER3_CMP                     46
#define TG0_EVT_CNT_CMP_TIMER0                  47
#define TG0_EVT_CNT_CMP_TIMER1                  48
#define TG1_EVT_CNT_CMP_TIMER0                  49
#define TG1_EVT_CNT_CMP_TIMER1                  50
#define SYSTIMER_EVT_CNT_CMP0                   51
#define SYSTIMER_EVT_CNT_CMP1                   52
#define SYSTIMER_EVT_CNT_CMP2                   53
#define MCPWM0_EVT_TIMER0_STOP                  54
#define MCPWM0_EVT_TIMER1_STOP                  55
#define MCPWM0_EVT_TIMER2_STOP                  56
#define MCPWM0_EVT_TIMER0_TEZ                   57
#define MCPWM0_EVT_TIMER1_TEZ                   58
#define MCPWM0_EVT_TIMER2_TEZ                   59
#define MCPWM0_EVT_TIMER0_TEP                   60
#define MCPWM0_EVT_TIMER1_TEP                   61
#define MCPWM0_EVT_TIMER2_TEP                   62
#define MCPWM0_EVT_OP0_TEA                      63
#define MCPWM0_EVT_OP1_TEA                      64
#define MCPWM0_EVT_OP2_TEA                      65
#define MCPWM0_EVT_OP0_TEB                      66
#define MCPWM0_EVT_OP1_TEB                      67
#define MCPWM0_EVT_OP2_TEB                      68
#define MCPWM0_EVT_F0                           69
#define MCPWM0_EVT_F1                           70
#define MCPWM0_EVT_F2                           71
#define MCPWM0_EVT_F0_CLR                       72
#define MCPWM0_EVT_F1_CLR                       73
#define MCPWM0_EVT_F2_CLR                       74
#define MCPWM0_EVT_TZ0_CBC                      75
#define MCPWM0_EVT_TZ1_CBC                      76
#define MCPWM0_EVT_TZ2_CBC                      77
#define MCPWM0_EVT_TZ0_OST                      78
#define MCPWM0_EVT_TZ1_OST                      79
#define MCPWM0_EVT_TZ2_OST                      80
#define MCPWM0_EVT_CAP0                         81
#define MCPWM0_EVT_CAP1                         82
#define MCPWM0_EVT_CAP2                         83
#define MCPWM0_EVT_OP0_TEE1                     84
#define MCPWM0_EVT_OP1_TEE1                     85
#define MCPWM0_EVT_OP2_TEE1                     86
#define MCPWM0_EVT_OP0_TEE2                     87
#define MCPWM0_EVT_OP1_TEE2                     88
#define MCPWM0_EVT_OP2_TEE2                     89
#define ADC_EVT_CONV_CMPLT0                     90
#define ADC_EVT_EQ_ABOVE_THRESH0                91
#define ADC_EVT_EQ_ABOVE_THRESH1                92
#define ADC_EVT_EQ_BELOW_THRESH0                93
#define ADC_EVT_EQ_BELOW_THRESH1                94
#define ADC_EVT_RESULT_DONE0                    95
#define ADC_EVT_STOPPED0                        96
#define ADC_EVT_STARTED0                        97
#define REGDMA_EVT_DONE0                        98
#define REGDMA_EVT_DONE1                        99
#define REGDMA_EVT_DONE2                        100
#define REGDMA_EVT_DONE3                        101
#define REGDMA_EVT_ERR0                         102
#define REGDMA_EVT_ERR1                         103
#define REGDMA_EVT_ERR2                         104
#define REGDMA_EVT_ERR3                         105
#define TMPSNSR_EVT_OVER_LIMIT                  106
#define I2S0_EVT_RX_DONE                        107
#define I2S0_EVT_TX_DONE                        108
#define I2S0_EVT_X_WORDS_RECEIVED               109
#define I2S0_EVT_X_WORDS_SENT                   110
#define ULP_EVT_ERR_INTR                        111
#define ULP_EVT_HALT                            112
#define ULP_EVT_START_INTR                      113
#define RTC_EVT_TICK                            114
#define RTC_EVT_OVF                             115
#define RTC_EVT_CMP                             116
#define GDMA_EVT_IN_DONE_CH0                    117
#define GDMA_EVT_IN_DONE_CH1                    118
#define GDMA_EVT_IN_DONE_CH2                    119
#define GDMA_EVT_IN_SUC_EOF_CH0                 120
#define GDMA_EVT_IN_SUC_EOF_CH1                 121
#define GDMA_EVT_IN_SUC_EOF_CH2                 122
#define GDMA_EVT_IN_FIFO_EMPTY_CH0              123
#define GDMA_EVT_IN_FIFO_EMPTY_CH1              124
#define GDMA_EVT_IN_FIFO_EMPTY_CH2              125
#define GDMA_EVT_IN_FIFO_FULL_CH0               126
#define GDMA_EVT_IN_FIFO_FULL_CH1               127
#define GDMA_EVT_IN_FIFO_FULL_CH2               128
#define GDMA_EVT_OUT_DONE_CH0                   129
#define GDMA_EVT_OUT_DONE_CH1                   130
#define GDMA_EVT_OUT_DONE_CH2                   131
#define GDMA_EVT_OUT_EOF_CH0                    132
#define GDMA_EVT_OUT_EOF_CH1                    133
#define GDMA_EVT_OUT_EOF_CH2                    134
#define GDMA_EVT_OUT_TOTAL_EOF_CH0              135
#define GDMA_EVT_OUT_TOTAL_EOF_CH1              136
#define GDMA_EVT_OUT_TOTAL_EOF_CH2              137
#define GDMA_EVT_OUT_FIFO_EMPTY_CH0             138
#define GDMA_EVT_OUT_FIFO_EMPTY_CH1             139
#define GDMA_EVT_OUT_FIFO_EMPTY_CH2             140
#define GDMA_EVT_OUT_FIFO_FULL_CH0              141
#define GDMA_EVT_OUT_FIFO_FULL_CH1              142
#define GDMA_EVT_OUT_FIFO_FULL_CH2              143
#define PMU_EVT_SLEEP_WEEKUP                    144
#define GPIO_TASK_CH0_SET                       1
#define GPIO_TASK_CH1_SET                       2
#define GPIO_TASK_CH2_SET                       3
#define GPIO_TASK_CH3_SET                       4
#define GPIO_TASK_CH4_SET                       5
#define GPIO_TASK_CH5_SET                       6
#define GPIO_TASK_CH6_SET                       7
#define GPIO_TASK_CH7_SET                       8
#define GPIO_TASK_CH0_CLEAR                     9
#define GPIO_TASK_CH1_CLEAR                     10
#define GPIO_TASK_CH2_CLEAR                     11
#define GPIO_TASK_CH3_CLEAR                     12
#define GPIO_TASK_CH4_CLEAR                     13
#define GPIO_TASK_CH5_CLEAR                     14
#define GPIO_TASK_CH6_CLEAR                     15
#define GPIO_TASK_CH7_CLEAR                     16
#define GPIO_TASK_CH0_TOGGLE                    17
#define GPIO_TASK_CH1_TOGGLE                    18
#define GPIO_TASK_CH2_TOGGLE                    19
#define GPIO_TASK_CH3_TOGGLE                    20
#define GPIO_TASK_CH4_TOGGLE                    21
#define GPIO_TASK_CH5_TOGGLE                    22
#define GPIO_TASK_CH6_TOGGLE                    23
#define GPIO_TASK_CH7_TOGGLE                    24
#define LEDC_TASK_TIMER0_RES_UPDATE             25
#define LEDC_TASK_TIMER1_RES_UPDATE             26
#define LEDC_TASK_TIMER2_RES_UPDATE             27
#define LEDC_TASK_TIMER3_RES_UPDATE             28
#define LEDC_TASK_DUTY_SCALE_UPDATE_CH0         29
#define LEDC_TASK_DUTY_SCALE_UPDATE_CH1         30
#define LEDC_TASK_DUTY_SCALE_UPDATE_CH2         31
#define LEDC_TASK_DUTY_SCALE_UPDATE_CH3         32
#define LEDC_TASK_DUTY_SCALE_UPDATE_CH4         33
#define LEDC_TASK_DUTY_SCALE_UPDATE_CH5         34
#define LEDC_TASK_TIMER0_CAP                    35
#define LEDC_TASK_TIMER1_CAP                    36
#define LEDC_TASK_TIMER2_CAP                    37
#define LEDC_TASK_TIMER3_CAP                    38
#define LEDC_TASK_SIG_OUT_DIS_CH0               39
#define LEDC_TASK_SIG_OUT_DIS_CH1               40
#define LEDC_TASK_SIG_OUT_DIS_CH2               41
#define LEDC_TASK_SIG_OUT_DIS_CH3               42
#define LEDC_TASK_SIG_OUT_DIS_CH4               43
#define LEDC_TASK_SIG_OUT_DIS_CH5               44
#define LEDC_TASK_OVF_CNT_RST_CH0               45
#define LEDC_TASK_OVF_CNT_RST_CH1               46
#define LEDC_TASK_OVF_CNT_RST_CH2               47
#define LEDC_TASK_OVF_CNT_RST_CH3               48
#define LEDC_TASK_OVF_CNT_RST_CH4               49
#define LEDC_TASK_OVF_CNT_RST_CH5               50
#define LEDC_TASK_TIMER0_RST                    51
#define LEDC_TASK_TIMER1_RST                    52
#define LEDC_TASK_TIMER2_RST                    53
#define LEDC_TASK_TIMER3_RST                    54
#define LEDC_TASK_TIMER0_RESUME                 55
#define LEDC_TASK_TIMER1_RESUME                 56
#define LEDC_TASK_TIMER2_RESUME                 57
#define LEDC_TASK_TIMER3_RESUME                 58
#define LEDC_TASK_TIMER0_PAUSE                  59
#define LEDC_TASK_TIMER1_PAUSE                  60
#define LEDC_TASK_TIMER2_PAUSE                  61
#define LEDC_TASK_TIMER3_PAUSE                  62
#define LEDC_TASK_GAMMA_RESTART_CH0             63
#define LEDC_TASK_GAMMA_RESTART_CH1             64
#define LEDC_TASK_GAMMA_RESTART_CH2             65
#define LEDC_TASK_GAMMA_RESTART_CH3             66
#define LEDC_TASK_GAMMA_RESTART_CH4             67
#define LEDC_TASK_GAMMA_RESTART_CH5             68
#define LEDC_TASK_GAMMA_PAUSE_CH0               69
#define LEDC_TASK_GAMMA_PAUSE_CH1               70
#define LEDC_TASK_GAMMA_PAUSE_CH2               71
#define LEDC_TASK_GAMMA_PAUSE_CH3               72
#define LEDC_TASK_GAMMA_PAUSE_CH4               73
#define LEDC_TASK_GAMMA_PAUSE_CH5               74
#define LEDC_TASK_GAMMA_RESUME_CH0              75
#define LEDC_TASK_GAMMA_RESUME_CH1              76
#define LEDC_TASK_GAMMA_RESUME_CH2              77
#define LEDC_TASK_GAMMA_RESUME_CH3              78
#define LEDC_TASK_GAMMA_RESUME_CH4              79
#define LEDC_TASK_GAMMA_RESUME_CH5              80
#define TG0_TASK_CNT_START_TIMER0               81
#define TG0_TASK_ALARM_START_TIMER0             82
#define TG0_TASK_CNT_STOP_TIMER0                83
#define TG0_TASK_CNT_RELOAD_TIMER0              84
#define TG0_TASK_CNT_CAP_TIMER0                 85
#define TG0_TASK_CNT_START_TIMER1               86
#define TG0_TASK_ALARM_START_TIMER1             87
#define TG0_TASK_CNT_STOP_TIMER1                88
#define TG0_TASK_CNT_RELOAD_TIMER1              89
#define TG0_TASK_CNT_CAP_TIMER1                 90
#define TG1_TASK_CNT_START_TIMER0               91
#define TG1_TASK_ALARM_START_TIMER0             92
#define TG1_TASK_CNT_STOP_TIMER0                93
#define TG1_TASK_CNT_RELOAD_TIMER0              94
#define TG1_TASK_CNT_CAP_TIMER0                 95
#define TG1_TASK_CNT_START_TIMER1               96
#define TG1_TASK_ALARM_START_TIMER1             97
#define TG1_TASK_CNT_STOP_TIMER1                98
#define TG1_TASK_CNT_RELOAD_TIMER1              99
#define TG1_TASK_CNT_CAP_TIMER1                 100
#define MCPWM0_TASK_CMPR0_A_UP                  101
#define MCPWM0_TASK_CMPR1_A_UP                  102
#define MCPWM0_TASK_CMPR2_A_UP                  103
#define MCPWM0_TASK_CMPR0_B_UP                  104
#define MCPWM0_TASK_CMPR1_B_UP                  105
#define MCPWM0_TASK_CMPR2_B_UP                  106
#define MCPWM0_TASK_GEN_STOP                    107
#define MCPWM0_TASK_TIMER0_SYN                  108
#define MCPWM0_TASK_TIMER1_SYN                  109
#define MCPWM0_TASK_TIMER2_SYN                  110
#define MCPWM0_TASK_TIMER0_PERIOD_UP            111
#define MCPWM0_TASK_TIMER1_PERIOD_UP            112
#define MCPWM0_TASK_TIMER2_PERIOD_UP            113
#define MCPWM0_TASK_TZ0_OST                     114
#define MCPWM0_TASK_TZ1_OST                     115
#define MCPWM0_TASK_TZ2_OST                     116
#define MCPWM0_TASK_CLR0_OST                    117
#define MCPWM0_TASK_CLR1_OST                    118
#define MCPWM0_TASK_CLR2_OST                    119
#define MCPWM0_TASK_CAP0                        120
#define MCPWM0_TASK_CAP1                        121
#define MCPWM0_TASK_CAP2                        122
#define ADC_TASK_SAMPLE0                        123
#define ADC_TASK_SAMPLE1                        124
#define ADC_TASK_START0                         125
#define ADC_TASK_STOP0                          126
#define REGDMA_TASK_START0                      127
#define REGDMA_TASK_START1                      128
#define REGDMA_TASK_START2                      129
#define REGDMA_TASK_START3                      130
#define TMPSNSR_TASK_START_SAMPLE               131
#define TMPSNSR_TASK_STOP_SAMPLE                132
#define I2S0_TASK_START_RX                      133
#define I2S0_TASK_START_TX                      134
#define I2S0_TASK_STOP_RX                       135
#define I2S0_TASK_STOP_TX                       136
#define ULP_TASK_WAKEUP_CPU                     137
#define ULP_TASK_INT_CPU                        138
#define RTC_TASK_START                          139
#define RTC_TASK_STOP                           140
#define RTC_TASK_CLR                            141
#define RTC_TASK_TRIGGERFLW                     142
#define GDMA_TASK_IN_START_CH0                  143
#define GDMA_TASK_IN_START_CH1                  144
#define GDMA_TASK_IN_START_CH2                  145
#define GDMA_TASK_OUT_START_CH0                 146
#define GDMA_TASK_OUT_START_CH1                 147
#define GDMA_TASK_OUT_START_CH2                 148
#define PMU_TASK_SLEEP_REQ                      149
