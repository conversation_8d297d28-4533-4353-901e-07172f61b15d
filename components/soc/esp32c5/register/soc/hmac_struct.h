/**
 * SPDX-FileCopyrightText: 2024 Espressif Systems (Shanghai) CO LTD
 *
 *  SPDX-License-Identifier: Apache-2.0
 */
#pragma once

#include <stdint.h>
#ifdef __cplusplus
extern "C" {
#endif

/** Group: Control/Status Registers */
/** Type of set_start register
 *  HMAC start control register
 */
typedef union {
    struct {
        /** set_start : WS; bitpos: [0]; default: 0;
         *  Configures whether or not to enable HMAC.
         *  \\0: Disable HMAC
         *  \\1: Enable HMAC
         */
        uint32_t set_start:1;
        uint32_t reserved_1:31;
    };
    uint32_t val;
} hmac_set_start_reg_t;

/** Type of set_para_finish register
 *  HMAC configuration completion register
 */
typedef union {
    struct {
        /** set_para_end : WS; bitpos: [0]; default: 0;
         *  Configures whether to finish HMAC configuration.
         *  \\0: No effect
         *  \\1: Finish configuration
         */
        uint32_t set_para_end:1;
        uint32_t reserved_1:31;
    };
    uint32_t val;
} hmac_set_para_finish_reg_t;

/** Type of set_message_one register
 *  HMAC message control register
 */
typedef union {
    struct {
        /** set_text_one : WS; bitpos: [0]; default: 0;
         *  Calls SHA to calculate one message block.
         */
        uint32_t set_text_one:1;
        uint32_t reserved_1:31;
    };
    uint32_t val;
} hmac_set_message_one_reg_t;

/** Type of set_message_ing register
 *  HMAC message continue register
 */
typedef union {
    struct {
        /** set_text_ing : WS; bitpos: [0]; default: 0;
         *  Configures whether or not there are unprocessed message blocks.
         *  \\0: No unprocessed message block
         *  \\1: There are still some message blocks to be processed.
         */
        uint32_t set_text_ing:1;
        uint32_t reserved_1:31;
    };
    uint32_t val;
} hmac_set_message_ing_reg_t;

/** Type of set_message_end register
 *  HMAC message end register
 */
typedef union {
    struct {
        /** set_text_end : WS; bitpos: [0]; default: 0;
         *  Configures whether to start hardware padding.
         *  \\0: No effect
         *  \\1: Start hardware padding
         */
        uint32_t set_text_end:1;
        uint32_t reserved_1:31;
    };
    uint32_t val;
} hmac_set_message_end_reg_t;

/** Type of set_result_finish register
 *  HMAC result reading finish register
 */
typedef union {
    struct {
        /** set_result_end : WS; bitpos: [0]; default: 0;
         *  Configures whether to exit upstream mode and clear calculation results.
         *  \\0: Not exit
         *  \\1: Exit upstream mode and clear calculation results.
         */
        uint32_t set_result_end:1;
        uint32_t reserved_1:31;
    };
    uint32_t val;
} hmac_set_result_finish_reg_t;

/** Type of set_invalidate_jtag register
 *  Invalidate JTAG result register
 */
typedef union {
    struct {
        /** set_invalidate_jtag : WS; bitpos: [0]; default: 0;
         *  Configures whether or not to clear calculation results when re-enabling JTAG in
         *  downstream mode.
         *  \\0: Not clear
         *  \\1: Clear calculation results
         */
        uint32_t set_invalidate_jtag:1;
        uint32_t reserved_1:31;
    };
    uint32_t val;
} hmac_set_invalidate_jtag_reg_t;

/** Type of set_invalidate_ds register
 *  Invalidate digital signature result register
 */
typedef union {
    struct {
        /** set_invalidate_ds : WS; bitpos: [0]; default: 0;
         *  Configures whether or not to clear calculation results of the DS module in
         *  downstream mode.
         *  \\0: Not clear
         *  \\1: Clear calculation results
         */
        uint32_t set_invalidate_ds:1;
        uint32_t reserved_1:31;
    };
    uint32_t val;
} hmac_set_invalidate_ds_reg_t;

/** Type of query_error register
 *  Stores matching results between keys generated by users and corresponding purposes
 */
typedef union {
    struct {
        /** qurey_check : RO; bitpos: [0]; default: 0;
         *  Represents whether or not an HMAC key matches the purpose.
         *  \\0: Match
         *  \\1: Error
         */
        uint32_t qurey_check:1;
        uint32_t reserved_1:31;
    };
    uint32_t val;
} hmac_query_error_reg_t;

/** Type of query_busy register
 *  Busy state of HMAC module
 */
typedef union {
    struct {
        /** busy_state : RO; bitpos: [0]; default: 0;
         *  Represents whether or not HMAC is in a busy state. Before configuring HMAC, please
         *  make sure HMAC is in an IDLE state.
         *  \\0: Idle
         *  \\1: HMAC is still working on the calculation
         */
        uint32_t busy_state:1;
        uint32_t reserved_1:31;
    };
    uint32_t val;
} hmac_query_busy_reg_t;

/** Type of set_message_pad register
 *  Software padding register
 */
typedef union {
    struct {
        /** set_text_pad : WO; bitpos: [0]; default: 0;
         *  Configures whether or not the padding is applied by software.
         *  \\0: Not applied by software
         *  \\1: Applied by software
         */
        uint32_t set_text_pad:1;
        uint32_t reserved_1:31;
    };
    uint32_t val;
} hmac_set_message_pad_reg_t;

/** Type of one_block register
 *  One block message register
 */
typedef union {
    struct {
        /** set_one_block : WS; bitpos: [0]; default: 0;
         *  Write 1 to indicate there is only one block which already contains padding bits and
         *  there is no need for padding.
         */
        uint32_t set_one_block:1;
        uint32_t reserved_1:31;
    };
    uint32_t val;
} hmac_one_block_reg_t;


/** Group: Configuration Registers */
/** Type of set_para_purpose register
 *  HMAC parameter configuration register
 */
typedef union {
    struct {
        /** purpose_set : WO; bitpos: [3:0]; default: 0;
         *  Configures the HMAC purpose, refer to the Table <a
         *  href=tab:hmac-key-purpose">link</a>. "
         */
        uint32_t purpose_set:4;
        uint32_t reserved_4:28;
    };
    uint32_t val;
} hmac_set_para_purpose_reg_t;

/** Type of set_para_key register
 *  HMAC parameters configuration register
 */
typedef union {
    struct {
        /** key_set : WO; bitpos: [2:0]; default: 0;
         *  Configures HMAC key. There are six keys with index 0~5. Write the index of the
         *  selected key to this field.
         */
        uint32_t key_set:3;
        uint32_t reserved_3:29;
    };
    uint32_t val;
} hmac_set_para_key_reg_t;

/** Type of wr_jtag register
 *  Re-enable JTAG register 1
 */
typedef union {
    struct {
        /** wr_jtag : WO; bitpos: [31:0]; default: 0;
         *  Writes the comparing input used for re-enabling JTAG.
         */
        uint32_t wr_jtag:32;
    };
    uint32_t val;
} hmac_wr_jtag_reg_t;


/** Group: Memory Type */

/** Group: Configuration Register */
/** Type of soft_jtag_ctrl register
 *  Jtag register 0.
 */
typedef union {
    struct {
        /** soft_jtag_ctrl : WS; bitpos: [0]; default: 0;
         *  Configures whether or not to enable JTAG authentication mode.
         *  \\0: Disable
         *  \\1: Enable
         *  \\
         */
        uint32_t soft_jtag_ctrl:1;
        uint32_t reserved_1:31;
    };
    uint32_t val;
} hmac_soft_jtag_ctrl_reg_t;


/** Group: Version Register */
/** Type of date register
 *  Version control register
 */
typedef union {
    struct {
        /** date : R/W; bitpos: [29:0]; default: 539166977;
         *  Hmac date information/ hmac version information.
         */
        uint32_t date:30;
        uint32_t reserved_30:2;
    };
    uint32_t val;
} hmac_date_reg_t;


typedef struct {
    uint32_t reserved_000[16];
    volatile hmac_set_start_reg_t set_start;
    volatile hmac_set_para_purpose_reg_t set_para_purpose;
    volatile hmac_set_para_key_reg_t set_para_key;
    volatile hmac_set_para_finish_reg_t set_para_finish;
    volatile hmac_set_message_one_reg_t set_message_one;
    volatile hmac_set_message_ing_reg_t set_message_ing;
    volatile hmac_set_message_end_reg_t set_message_end;
    volatile hmac_set_result_finish_reg_t set_result_finish;
    volatile hmac_set_invalidate_jtag_reg_t set_invalidate_jtag;
    volatile hmac_set_invalidate_ds_reg_t set_invalidate_ds;
    volatile hmac_query_error_reg_t query_error;
    volatile hmac_query_busy_reg_t query_busy;
    uint32_t reserved_070[4];
    volatile uint32_t wr_message[16];
    volatile uint32_t rd_result[8];
    uint32_t reserved_0e0[4];
    volatile hmac_set_message_pad_reg_t set_message_pad;
    volatile hmac_one_block_reg_t one_block;
    volatile hmac_soft_jtag_ctrl_reg_t soft_jtag_ctrl;
    volatile hmac_wr_jtag_reg_t wr_jtag;
    uint32_t reserved_100[63];
    volatile hmac_date_reg_t date;
} hmac_dev_t;

extern hmac_dev_t HMAC;

#ifndef __cplusplus
_Static_assert(sizeof(hmac_dev_t) == 0x200, "Invalid size of hmac_dev_t structure");
#endif

#ifdef __cplusplus
}
#endif
