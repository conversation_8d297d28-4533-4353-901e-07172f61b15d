/**
 * SPDX-FileCopyrightText: 2021-2022 Espressif Systems (Shanghai) CO LTD
 *
 *  SPDX-License-Identifier: Apache-2.0
 */
#pragma once

#include <stdint.h>
#include "soc/soc.h"
#ifdef __cplusplus
extern "C" {
#endif

/** SENSITIVE_ROM_TABLE_LOCK_REG register
 *  register description
 */
#define SENSITIVE_ROM_TABLE_LOCK_REG (DR_REG_SENSITIVE_BASE + 0x0)
/** SENSITIVE_ROM_TABLE_LOCK : R/W; bitpos: [0]; default: 0;
 *  Need add description
 */
#define SENSITIVE_ROM_TABLE_LOCK    (BIT(0))
#define SENSITIVE_ROM_TABLE_LOCK_M  (SENSITIVE_ROM_TABLE_LOCK_V << SENSITIVE_ROM_TABLE_LOCK_S)
#define SENSITIVE_ROM_TABLE_LOCK_V  0x00000001U
#define SENSITIVE_ROM_TABLE_LOCK_S  0

/** SENSITIVE_ROM_TABLE_REG register
 *  register description
 */
#define SENSITIVE_ROM_TABLE_REG (DR_REG_SENSITIVE_BASE + 0x4)
/** SENSITIVE_ROM_TABLE : R/W; bitpos: [31:0]; default: 0;
 *  Need add description
 */
#define SENSITIVE_ROM_TABLE    0xFFFFFFFFU
#define SENSITIVE_ROM_TABLE_M  (SENSITIVE_ROM_TABLE_V << SENSITIVE_ROM_TABLE_S)
#define SENSITIVE_ROM_TABLE_V  0xFFFFFFFFU
#define SENSITIVE_ROM_TABLE_S  0

/** SENSITIVE_APB_PERIPHERAL_ACCESS_0_REG register
 *  register description
 */
#define SENSITIVE_APB_PERIPHERAL_ACCESS_0_REG (DR_REG_SENSITIVE_BASE + 0x8)
/** SENSITIVE_APB_PERIPHERAL_ACCESS_LOCK : R/W; bitpos: [0]; default: 0;
 *  Need add description
 */
#define SENSITIVE_APB_PERIPHERAL_ACCESS_LOCK    (BIT(0))
#define SENSITIVE_APB_PERIPHERAL_ACCESS_LOCK_M  (SENSITIVE_APB_PERIPHERAL_ACCESS_LOCK_V << SENSITIVE_APB_PERIPHERAL_ACCESS_LOCK_S)
#define SENSITIVE_APB_PERIPHERAL_ACCESS_LOCK_V  0x00000001U
#define SENSITIVE_APB_PERIPHERAL_ACCESS_LOCK_S  0

/** SENSITIVE_APB_PERIPHERAL_ACCESS_1_REG register
 *  register description
 */
#define SENSITIVE_APB_PERIPHERAL_ACCESS_1_REG (DR_REG_SENSITIVE_BASE + 0xc)
/** SENSITIVE_APB_PERIPHERAL_ACCESS_SPLIT_BURST : R/W; bitpos: [0]; default: 1;
 *  Need add description
 */
#define SENSITIVE_APB_PERIPHERAL_ACCESS_SPLIT_BURST    (BIT(0))
#define SENSITIVE_APB_PERIPHERAL_ACCESS_SPLIT_BURST_M  (SENSITIVE_APB_PERIPHERAL_ACCESS_SPLIT_BURST_V << SENSITIVE_APB_PERIPHERAL_ACCESS_SPLIT_BURST_S)
#define SENSITIVE_APB_PERIPHERAL_ACCESS_SPLIT_BURST_V  0x00000001U
#define SENSITIVE_APB_PERIPHERAL_ACCESS_SPLIT_BURST_S  0

/** SENSITIVE_INTERNAL_SRAM_USAGE_0_REG register
 *  register description
 */
#define SENSITIVE_INTERNAL_SRAM_USAGE_0_REG (DR_REG_SENSITIVE_BASE + 0x10)
/** SENSITIVE_INTERNAL_SRAM_USAGE_LOCK : R/W; bitpos: [0]; default: 0;
 *  Need add description
 */
#define SENSITIVE_INTERNAL_SRAM_USAGE_LOCK    (BIT(0))
#define SENSITIVE_INTERNAL_SRAM_USAGE_LOCK_M  (SENSITIVE_INTERNAL_SRAM_USAGE_LOCK_V << SENSITIVE_INTERNAL_SRAM_USAGE_LOCK_S)
#define SENSITIVE_INTERNAL_SRAM_USAGE_LOCK_V  0x00000001U
#define SENSITIVE_INTERNAL_SRAM_USAGE_LOCK_S  0

/** SENSITIVE_INTERNAL_SRAM_USAGE_1_REG register
 *  register description
 */
#define SENSITIVE_INTERNAL_SRAM_USAGE_1_REG (DR_REG_SENSITIVE_BASE + 0x14)
/** SENSITIVE_INTERNAL_SRAM_USAGE_CPU_CACHE : R/W; bitpos: [0]; default: 1;
 *  Need add description
 */
#define SENSITIVE_INTERNAL_SRAM_USAGE_CPU_CACHE    (BIT(0))
#define SENSITIVE_INTERNAL_SRAM_USAGE_CPU_CACHE_M  (SENSITIVE_INTERNAL_SRAM_USAGE_CPU_CACHE_V << SENSITIVE_INTERNAL_SRAM_USAGE_CPU_CACHE_S)
#define SENSITIVE_INTERNAL_SRAM_USAGE_CPU_CACHE_V  0x00000001U
#define SENSITIVE_INTERNAL_SRAM_USAGE_CPU_CACHE_S  0
/** SENSITIVE_INTERNAL_SRAM_USAGE_CPU_SRAM : R/W; bitpos: [3:1]; default: 7;
 *  Need add description
 */
#define SENSITIVE_INTERNAL_SRAM_USAGE_CPU_SRAM    0x00000007U
#define SENSITIVE_INTERNAL_SRAM_USAGE_CPU_SRAM_M  (SENSITIVE_INTERNAL_SRAM_USAGE_CPU_SRAM_V << SENSITIVE_INTERNAL_SRAM_USAGE_CPU_SRAM_S)
#define SENSITIVE_INTERNAL_SRAM_USAGE_CPU_SRAM_V  0x00000007U
#define SENSITIVE_INTERNAL_SRAM_USAGE_CPU_SRAM_S  1

/** SENSITIVE_INTERNAL_SRAM_USAGE_3_REG register
 *  register description
 */
#define SENSITIVE_INTERNAL_SRAM_USAGE_3_REG (DR_REG_SENSITIVE_BASE + 0x18)
/** SENSITIVE_INTERNAL_SRAM_USAGE_MAC_DUMP_SRAM : R/W; bitpos: [2:0]; default: 0;
 *  Need add description
 */
#define SENSITIVE_INTERNAL_SRAM_USAGE_MAC_DUMP_SRAM    0x00000007U
#define SENSITIVE_INTERNAL_SRAM_USAGE_MAC_DUMP_SRAM_M  (SENSITIVE_INTERNAL_SRAM_USAGE_MAC_DUMP_SRAM_V << SENSITIVE_INTERNAL_SRAM_USAGE_MAC_DUMP_SRAM_S)
#define SENSITIVE_INTERNAL_SRAM_USAGE_MAC_DUMP_SRAM_V  0x00000007U
#define SENSITIVE_INTERNAL_SRAM_USAGE_MAC_DUMP_SRAM_S  0
/** SENSITIVE_INTERNAL_SRAM_ALLOC_MAC_DUMP : R/W; bitpos: [3]; default: 0;
 *  Need add description
 */
#define SENSITIVE_INTERNAL_SRAM_ALLOC_MAC_DUMP    (BIT(3))
#define SENSITIVE_INTERNAL_SRAM_ALLOC_MAC_DUMP_M  (SENSITIVE_INTERNAL_SRAM_ALLOC_MAC_DUMP_V << SENSITIVE_INTERNAL_SRAM_ALLOC_MAC_DUMP_S)
#define SENSITIVE_INTERNAL_SRAM_ALLOC_MAC_DUMP_V  0x00000001U
#define SENSITIVE_INTERNAL_SRAM_ALLOC_MAC_DUMP_S  3

/** SENSITIVE_CACHE_TAG_ACCESS_0_REG register
 *  register description
 */
#define SENSITIVE_CACHE_TAG_ACCESS_0_REG (DR_REG_SENSITIVE_BASE + 0x1c)
/** SENSITIVE_CACHE_TAG_ACCESS_LOCK : R/W; bitpos: [0]; default: 0;
 *  Need add description
 */
#define SENSITIVE_CACHE_TAG_ACCESS_LOCK    (BIT(0))
#define SENSITIVE_CACHE_TAG_ACCESS_LOCK_M  (SENSITIVE_CACHE_TAG_ACCESS_LOCK_V << SENSITIVE_CACHE_TAG_ACCESS_LOCK_S)
#define SENSITIVE_CACHE_TAG_ACCESS_LOCK_V  0x00000001U
#define SENSITIVE_CACHE_TAG_ACCESS_LOCK_S  0

/** SENSITIVE_CACHE_TAG_ACCESS_1_REG register
 *  register description
 */
#define SENSITIVE_CACHE_TAG_ACCESS_1_REG (DR_REG_SENSITIVE_BASE + 0x20)
/** SENSITIVE_PRO_I_TAG_RD_ACS : R/W; bitpos: [0]; default: 1;
 *  Need add description
 */
#define SENSITIVE_PRO_I_TAG_RD_ACS    (BIT(0))
#define SENSITIVE_PRO_I_TAG_RD_ACS_M  (SENSITIVE_PRO_I_TAG_RD_ACS_V << SENSITIVE_PRO_I_TAG_RD_ACS_S)
#define SENSITIVE_PRO_I_TAG_RD_ACS_V  0x00000001U
#define SENSITIVE_PRO_I_TAG_RD_ACS_S  0
/** SENSITIVE_PRO_I_TAG_WR_ACS : R/W; bitpos: [1]; default: 1;
 *  Need add description
 */
#define SENSITIVE_PRO_I_TAG_WR_ACS    (BIT(1))
#define SENSITIVE_PRO_I_TAG_WR_ACS_M  (SENSITIVE_PRO_I_TAG_WR_ACS_V << SENSITIVE_PRO_I_TAG_WR_ACS_S)
#define SENSITIVE_PRO_I_TAG_WR_ACS_V  0x00000001U
#define SENSITIVE_PRO_I_TAG_WR_ACS_S  1
/** SENSITIVE_PRO_D_TAG_RD_ACS : R/W; bitpos: [2]; default: 1;
 *  Need add description
 */
#define SENSITIVE_PRO_D_TAG_RD_ACS    (BIT(2))
#define SENSITIVE_PRO_D_TAG_RD_ACS_M  (SENSITIVE_PRO_D_TAG_RD_ACS_V << SENSITIVE_PRO_D_TAG_RD_ACS_S)
#define SENSITIVE_PRO_D_TAG_RD_ACS_V  0x00000001U
#define SENSITIVE_PRO_D_TAG_RD_ACS_S  2
/** SENSITIVE_PRO_D_TAG_WR_ACS : R/W; bitpos: [3]; default: 1;
 *  Need add description
 */
#define SENSITIVE_PRO_D_TAG_WR_ACS    (BIT(3))
#define SENSITIVE_PRO_D_TAG_WR_ACS_M  (SENSITIVE_PRO_D_TAG_WR_ACS_V << SENSITIVE_PRO_D_TAG_WR_ACS_S)
#define SENSITIVE_PRO_D_TAG_WR_ACS_V  0x00000001U
#define SENSITIVE_PRO_D_TAG_WR_ACS_S  3

/** SENSITIVE_CACHE_MMU_ACCESS_0_REG register
 *  register description
 */
#define SENSITIVE_CACHE_MMU_ACCESS_0_REG (DR_REG_SENSITIVE_BASE + 0x24)
/** SENSITIVE_CACHE_MMU_ACCESS_LOCK : R/W; bitpos: [0]; default: 0;
 *  Need add description
 */
#define SENSITIVE_CACHE_MMU_ACCESS_LOCK    (BIT(0))
#define SENSITIVE_CACHE_MMU_ACCESS_LOCK_M  (SENSITIVE_CACHE_MMU_ACCESS_LOCK_V << SENSITIVE_CACHE_MMU_ACCESS_LOCK_S)
#define SENSITIVE_CACHE_MMU_ACCESS_LOCK_V  0x00000001U
#define SENSITIVE_CACHE_MMU_ACCESS_LOCK_S  0

/** SENSITIVE_CACHE_MMU_ACCESS_1_REG register
 *  register description
 */
#define SENSITIVE_CACHE_MMU_ACCESS_1_REG (DR_REG_SENSITIVE_BASE + 0x28)
/** SENSITIVE_PRO_MMU_RD_ACS : R/W; bitpos: [0]; default: 1;
 *  Need add description
 */
#define SENSITIVE_PRO_MMU_RD_ACS    (BIT(0))
#define SENSITIVE_PRO_MMU_RD_ACS_M  (SENSITIVE_PRO_MMU_RD_ACS_V << SENSITIVE_PRO_MMU_RD_ACS_S)
#define SENSITIVE_PRO_MMU_RD_ACS_V  0x00000001U
#define SENSITIVE_PRO_MMU_RD_ACS_S  0
/** SENSITIVE_PRO_MMU_WR_ACS : R/W; bitpos: [1]; default: 1;
 *  Need add description
 */
#define SENSITIVE_PRO_MMU_WR_ACS    (BIT(1))
#define SENSITIVE_PRO_MMU_WR_ACS_M  (SENSITIVE_PRO_MMU_WR_ACS_V << SENSITIVE_PRO_MMU_WR_ACS_S)
#define SENSITIVE_PRO_MMU_WR_ACS_V  0x00000001U
#define SENSITIVE_PRO_MMU_WR_ACS_S  1

/** SENSITIVE_PIF_ACCESS_MONITOR_0_REG register
 *  register description
 */
#define SENSITIVE_PIF_ACCESS_MONITOR_0_REG (DR_REG_SENSITIVE_BASE + 0x2c)
/** SENSITIVE_PIF_ACCESS_MONITOR_LOCK : R/W; bitpos: [0]; default: 0;
 *  Need add description
 */
#define SENSITIVE_PIF_ACCESS_MONITOR_LOCK    (BIT(0))
#define SENSITIVE_PIF_ACCESS_MONITOR_LOCK_M  (SENSITIVE_PIF_ACCESS_MONITOR_LOCK_V << SENSITIVE_PIF_ACCESS_MONITOR_LOCK_S)
#define SENSITIVE_PIF_ACCESS_MONITOR_LOCK_V  0x00000001U
#define SENSITIVE_PIF_ACCESS_MONITOR_LOCK_S  0

/** SENSITIVE_PIF_ACCESS_MONITOR_1_REG register
 *  register description
 */
#define SENSITIVE_PIF_ACCESS_MONITOR_1_REG (DR_REG_SENSITIVE_BASE + 0x30)
/** SENSITIVE_PIF_ACCESS_MONITOR_NONWORD_VIOLATE_CLR : R/W; bitpos: [0]; default: 1;
 *  Need add description
 */
#define SENSITIVE_PIF_ACCESS_MONITOR_NONWORD_VIOLATE_CLR    (BIT(0))
#define SENSITIVE_PIF_ACCESS_MONITOR_NONWORD_VIOLATE_CLR_M  (SENSITIVE_PIF_ACCESS_MONITOR_NONWORD_VIOLATE_CLR_V << SENSITIVE_PIF_ACCESS_MONITOR_NONWORD_VIOLATE_CLR_S)
#define SENSITIVE_PIF_ACCESS_MONITOR_NONWORD_VIOLATE_CLR_V  0x00000001U
#define SENSITIVE_PIF_ACCESS_MONITOR_NONWORD_VIOLATE_CLR_S  0
/** SENSITIVE_PIF_ACCESS_MONITOR_NONWORD_VIOLATE_EN : R/W; bitpos: [1]; default: 1;
 *  Need add description
 */
#define SENSITIVE_PIF_ACCESS_MONITOR_NONWORD_VIOLATE_EN    (BIT(1))
#define SENSITIVE_PIF_ACCESS_MONITOR_NONWORD_VIOLATE_EN_M  (SENSITIVE_PIF_ACCESS_MONITOR_NONWORD_VIOLATE_EN_V << SENSITIVE_PIF_ACCESS_MONITOR_NONWORD_VIOLATE_EN_S)
#define SENSITIVE_PIF_ACCESS_MONITOR_NONWORD_VIOLATE_EN_V  0x00000001U
#define SENSITIVE_PIF_ACCESS_MONITOR_NONWORD_VIOLATE_EN_S  1

/** SENSITIVE_PIF_ACCESS_MONITOR_2_REG register
 *  register description
 */
#define SENSITIVE_PIF_ACCESS_MONITOR_2_REG (DR_REG_SENSITIVE_BASE + 0x34)
/** SENSITIVE_PIF_ACCESS_MONITOR_NONWORD_VIOLATE_INTR : RO; bitpos: [0]; default: 0;
 *  Need add description
 */
#define SENSITIVE_PIF_ACCESS_MONITOR_NONWORD_VIOLATE_INTR    (BIT(0))
#define SENSITIVE_PIF_ACCESS_MONITOR_NONWORD_VIOLATE_INTR_M  (SENSITIVE_PIF_ACCESS_MONITOR_NONWORD_VIOLATE_INTR_V << SENSITIVE_PIF_ACCESS_MONITOR_NONWORD_VIOLATE_INTR_S)
#define SENSITIVE_PIF_ACCESS_MONITOR_NONWORD_VIOLATE_INTR_V  0x00000001U
#define SENSITIVE_PIF_ACCESS_MONITOR_NONWORD_VIOLATE_INTR_S  0
/** SENSITIVE_PIF_ACCESS_MONITOR_NONWORD_VIOLATE_STATUS_HSIZE : RO; bitpos: [2:1];
 *  default: 0;
 *  Need add description
 */
#define SENSITIVE_PIF_ACCESS_MONITOR_NONWORD_VIOLATE_STATUS_HSIZE    0x00000003U
#define SENSITIVE_PIF_ACCESS_MONITOR_NONWORD_VIOLATE_STATUS_HSIZE_M  (SENSITIVE_PIF_ACCESS_MONITOR_NONWORD_VIOLATE_STATUS_HSIZE_V << SENSITIVE_PIF_ACCESS_MONITOR_NONWORD_VIOLATE_STATUS_HSIZE_S)
#define SENSITIVE_PIF_ACCESS_MONITOR_NONWORD_VIOLATE_STATUS_HSIZE_V  0x00000003U
#define SENSITIVE_PIF_ACCESS_MONITOR_NONWORD_VIOLATE_STATUS_HSIZE_S  1

/** SENSITIVE_PIF_ACCESS_MONITOR_3_REG register
 *  register description
 */
#define SENSITIVE_PIF_ACCESS_MONITOR_3_REG (DR_REG_SENSITIVE_BASE + 0x38)
/** SENSITIVE_PIF_ACCESS_MONITOR_NONWORD_VIOLATE_STATUS_HADDR : RO; bitpos: [31:0];
 *  default: 0;
 *  Need add description
 */
#define SENSITIVE_PIF_ACCESS_MONITOR_NONWORD_VIOLATE_STATUS_HADDR    0xFFFFFFFFU
#define SENSITIVE_PIF_ACCESS_MONITOR_NONWORD_VIOLATE_STATUS_HADDR_M  (SENSITIVE_PIF_ACCESS_MONITOR_NONWORD_VIOLATE_STATUS_HADDR_V << SENSITIVE_PIF_ACCESS_MONITOR_NONWORD_VIOLATE_STATUS_HADDR_S)
#define SENSITIVE_PIF_ACCESS_MONITOR_NONWORD_VIOLATE_STATUS_HADDR_V  0xFFFFFFFFU
#define SENSITIVE_PIF_ACCESS_MONITOR_NONWORD_VIOLATE_STATUS_HADDR_S  0

/** SENSITIVE_XTS_AES_KEY_UPDATE_REG register
 *  register description
 */
#define SENSITIVE_XTS_AES_KEY_UPDATE_REG (DR_REG_SENSITIVE_BASE + 0x3c)
/** SENSITIVE_XTS_AES_KEY_UPDATE : R/W; bitpos: [0]; default: 0;
 *  Set this bit to update xts_aes key
 */
#define SENSITIVE_XTS_AES_KEY_UPDATE    (BIT(0))
#define SENSITIVE_XTS_AES_KEY_UPDATE_M  (SENSITIVE_XTS_AES_KEY_UPDATE_V << SENSITIVE_XTS_AES_KEY_UPDATE_S)
#define SENSITIVE_XTS_AES_KEY_UPDATE_V  0x00000001U
#define SENSITIVE_XTS_AES_KEY_UPDATE_S  0

/** SENSITIVE_CLOCK_GATE_REG register
 *  register description
 */
#define SENSITIVE_CLOCK_GATE_REG (DR_REG_SENSITIVE_BASE + 0x40)
/** SENSITIVE_CLK_EN : R/W; bitpos: [0]; default: 1;
 *  Need add description
 */
#define SENSITIVE_CLK_EN    (BIT(0))
#define SENSITIVE_CLK_EN_M  (SENSITIVE_CLK_EN_V << SENSITIVE_CLK_EN_S)
#define SENSITIVE_CLK_EN_V  0x00000001U
#define SENSITIVE_CLK_EN_S  0

/** SENSITIVE_SENSITIVE_REG_DATE_REG register
 *  register description
 */
#define SENSITIVE_SENSITIVE_REG_DATE_REG (DR_REG_SENSITIVE_BASE + 0xffc)
/** SENSITIVE_SENSITIVE_REG_DATE : R/W; bitpos: [27:0]; default: 34628353;
 *  Need add description
 */
#define SENSITIVE_SENSITIVE_REG_DATE    0x0FFFFFFFU
#define SENSITIVE_SENSITIVE_REG_DATE_M  (SENSITIVE_SENSITIVE_REG_DATE_V << SENSITIVE_SENSITIVE_REG_DATE_S)
#define SENSITIVE_SENSITIVE_REG_DATE_V  0x0FFFFFFFU
#define SENSITIVE_SENSITIVE_REG_DATE_S  0

#ifdef __cplusplus
}
#endif
