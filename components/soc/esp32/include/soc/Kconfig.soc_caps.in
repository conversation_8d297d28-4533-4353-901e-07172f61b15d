#####################################################
# This file is auto-generated from SoC caps
# using gen_soc_caps_kconfig.py, do not edit manually
#####################################################

config SOC_BROWNOUT_RESET_SUPPORTED
    string
    default "Not determined"

config SOC_TWAI_BRP_DIV_SUPPORTED
    string
    default "Not determined"

config SOC_DPORT_WORKAROUND
    string
    default "Not determined"

config SOC_CAPS_ECO_VER_MAX
    int
    default 301

config SOC_ADC_SUPPORTED
    bool
    default y

config SOC_DAC_SUPPORTED
    bool
    default y

config SOC_UART_SUPPORTED
    bool
    default y

config SOC_MCPWM_SUPPORTED
    bool
    default y

config SOC_GPTIMER_SUPPORTED
    bool
    default y

config SOC_SDMMC_HOST_SUPPORTED
    bool
    default y

config SOC_BT_SUPPORTED
    bool
    default y

config SOC_PCNT_SUPPORTED
    bool
    default y

config SOC_PHY_SUPPORTED
    bool
    default y

config SOC_WIFI_SUPPORTED
    bool
    default y

config SOC_SDIO_SLAVE_SUPPORTED
    bool
    default y

config SOC_TWAI_SUPPORTED
    bool
    default y

config SOC_EFUSE_SUPPORTED
    bool
    default y

config SOC_EMAC_SUPPORTED
    bool
    default y

config SOC_ULP_SUPPORTED
    bool
    default y

config SOC_CCOMP_TIMER_SUPPORTED
    bool
    default y

config SOC_RTC_FAST_MEM_SUPPORTED
    bool
    default y

config SOC_RTC_SLOW_MEM_SUPPORTED
    bool
    default y

config SOC_RTC_MEM_SUPPORTED
    bool
    default y

config SOC_I2S_SUPPORTED
    bool
    default y

config SOC_RMT_SUPPORTED
    bool
    default y

config SOC_SDM_SUPPORTED
    bool
    default y

config SOC_GPSPI_SUPPORTED
    bool
    default y

config SOC_LEDC_SUPPORTED
    bool
    default y

config SOC_I2C_SUPPORTED
    bool
    default y

config SOC_SUPPORT_COEXISTENCE
    bool
    default y

config SOC_AES_SUPPORTED
    bool
    default y

config SOC_MPI_SUPPORTED
    bool
    default y

config SOC_SHA_SUPPORTED
    bool
    default y

config SOC_FLASH_ENC_SUPPORTED
    bool
    default y

config SOC_SECURE_BOOT_SUPPORTED
    bool
    default y

config SOC_TOUCH_SENSOR_SUPPORTED
    bool
    default y

config SOC_BOD_SUPPORTED
    bool
    default y

config SOC_ULP_FSM_SUPPORTED
    bool
    default y

config SOC_CLK_TREE_SUPPORTED
    bool
    default y

config SOC_MPU_SUPPORTED
    bool
    default y

config SOC_WDT_SUPPORTED
    bool
    default y

config SOC_SPI_FLASH_SUPPORTED
    bool
    default y

config SOC_RNG_SUPPORTED
    bool
    default y

config SOC_LIGHT_SLEEP_SUPPORTED
    bool
    default y

config SOC_DEEP_SLEEP_SUPPORTED
    bool
    default y

config SOC_LP_PERIPH_SHARE_INTERRUPT
    bool
    default y

config SOC_PM_SUPPORTED
    bool
    default y

config SOC_DPORT_WORKAROUND_DIS_INTERRUPT_LVL
    int
    default 5

config SOC_XTAL_SUPPORT_26M
    bool
    default y

config SOC_XTAL_SUPPORT_40M
    bool
    default y

config SOC_XTAL_SUPPORT_AUTO_DETECT
    bool
    default y

config SOC_ADC_RTC_CTRL_SUPPORTED
    bool
    default y

config SOC_ADC_DIG_CTRL_SUPPORTED
    bool
    default y

config SOC_ADC_DMA_SUPPORTED
    bool
    default y

config SOC_ADC_PERIPH_NUM
    int
    default 2

config SOC_ADC_MAX_CHANNEL_NUM
    int
    default 10

config SOC_ADC_ATTEN_NUM
    int
    default 4

config SOC_ADC_DIGI_CONTROLLER_NUM
    int
    default 2

config SOC_ADC_PATT_LEN_MAX
    int
    default 16

config SOC_ADC_DIGI_MIN_BITWIDTH
    int
    default 9

config SOC_ADC_DIGI_MAX_BITWIDTH
    int
    default 12

config SOC_ADC_DIGI_RESULT_BYTES
    int
    default 2

config SOC_ADC_DIGI_DATA_BYTES_PER_CONV
    int
    default 4

config SOC_ADC_DIGI_MONITOR_NUM
    int
    default 0

config SOC_ADC_SAMPLE_FREQ_THRES_HIGH
    int
    default 2

config SOC_ADC_SAMPLE_FREQ_THRES_LOW
    int
    default 20

config SOC_ADC_RTC_MIN_BITWIDTH
    int
    default 9

config SOC_ADC_RTC_MAX_BITWIDTH
    int
    default 12

config SOC_ADC_SHARED_POWER
    bool
    default y

config SOC_SHARED_IDCACHE_SUPPORTED
    bool
    default y

config SOC_IDCACHE_PER_CORE
    bool
    default y

config SOC_CPU_CORES_NUM
    int
    default 2

config SOC_CPU_INTR_NUM
    int
    default 32

config SOC_CPU_HAS_FPU
    bool
    default y

config SOC_HP_CPU_HAS_MULTIPLE_CORES
    bool
    default y

config SOC_CPU_BREAKPOINTS_NUM
    int
    default 2

config SOC_CPU_WATCHPOINTS_NUM
    int
    default 2

config SOC_CPU_WATCHPOINT_MAX_REGION_SIZE
    int
    default 64

config SOC_DAC_CHAN_NUM
    int
    default 2

config SOC_DAC_RESOLUTION
    int
    default 8

config SOC_DAC_DMA_16BIT_ALIGN
    bool
    default y

config SOC_GPIO_PORT
    int
    default 1

config SOC_GPIO_PIN_COUNT
    int
    default 40

config SOC_GPIO_VALID_GPIO_MASK
    hex
    default 0xFFFFFFFFFF

config SOC_GPIO_IN_RANGE_MAX
    int
    default 39

config SOC_GPIO_OUT_RANGE_MAX
    int
    default 33

config SOC_GPIO_VALID_DIGITAL_IO_PAD_MASK
    hex
    default 0xEF0FEA

config SOC_GPIO_CLOCKOUT_BY_IO_MUX
    bool
    default y

config SOC_GPIO_CLOCKOUT_CHANNEL_NUM
    int
    default 3

config SOC_GPIO_SUPPORT_HOLD_IO_IN_DSLP
    bool
    default y

config SOC_I2C_NUM
    int
    default 2

config SOC_HP_I2C_NUM
    int
    default 2

config SOC_I2C_FIFO_LEN
    int
    default 32

config SOC_I2C_CMD_REG_NUM
    int
    default 16

config SOC_I2C_SUPPORT_SLAVE
    bool
    default y

config SOC_I2C_SUPPORT_APB
    bool
    default y

config SOC_I2C_SUPPORT_10BIT_ADDR
    bool
    default y

config SOC_I2C_STOP_INDEPENDENT
    bool
    default y

config SOC_I2S_NUM
    int
    default 2

config SOC_I2S_HW_VERSION_1
    bool
    default y

config SOC_I2S_SUPPORTS_APLL
    bool
    default y

config SOC_I2S_SUPPORTS_PLL_F160M
    bool
    default y

config SOC_I2S_SUPPORTS_PDM
    bool
    default y

config SOC_I2S_SUPPORTS_PDM_TX
    bool
    default y

config SOC_I2S_PDM_MAX_TX_LINES
    int
    default 1

config SOC_I2S_SUPPORTS_PDM_RX
    bool
    default y

config SOC_I2S_PDM_MAX_RX_LINES
    int
    default 1

config SOC_I2S_SUPPORTS_ADC_DAC
    bool
    default y

config SOC_I2S_SUPPORTS_ADC
    bool
    default y

config SOC_I2S_SUPPORTS_DAC
    bool
    default y

config SOC_I2S_SUPPORTS_LCD_CAMERA
    bool
    default y

config SOC_I2S_MAX_DATA_WIDTH
    int
    default 24

config SOC_I2S_TRANS_SIZE_ALIGN_WORD
    bool
    default y

config SOC_I2S_LCD_I80_VARIANT
    bool
    default y

config SOC_LCD_I80_SUPPORTED
    bool
    default y

config SOC_LCD_I80_BUSES
    int
    default 2

config SOC_LCD_I80_BUS_WIDTH
    int
    default 24

config SOC_LEDC_HAS_TIMER_SPECIFIC_MUX
    bool
    default y

config SOC_LEDC_SUPPORT_APB_CLOCK
    bool
    default y

config SOC_LEDC_SUPPORT_REF_TICK
    bool
    default y

config SOC_LEDC_SUPPORT_HS_MODE
    bool
    default y

config SOC_LEDC_TIMER_NUM
    int
    default 4

config SOC_LEDC_CHANNEL_NUM
    int
    default 8

config SOC_LEDC_TIMER_BIT_WIDTH
    int
    default 20

config SOC_MCPWM_GROUPS
    int
    default 2

config SOC_MCPWM_TIMERS_PER_GROUP
    int
    default 3

config SOC_MCPWM_OPERATORS_PER_GROUP
    int
    default 3

config SOC_MCPWM_COMPARATORS_PER_OPERATOR
    int
    default 2

config SOC_MCPWM_GENERATORS_PER_OPERATOR
    int
    default 2

config SOC_MCPWM_TRIGGERS_PER_OPERATOR
    int
    default 2

config SOC_MCPWM_GPIO_FAULTS_PER_GROUP
    int
    default 3

config SOC_MCPWM_CAPTURE_TIMERS_PER_GROUP
    bool
    default y

config SOC_MCPWM_CAPTURE_CHANNELS_PER_TIMER
    int
    default 3

config SOC_MCPWM_GPIO_SYNCHROS_PER_GROUP
    int
    default 3

config SOC_MMU_PERIPH_NUM
    int
    default 2

config SOC_MMU_LINEAR_ADDRESS_REGION_NUM
    int
    default 3

config SOC_MPU_CONFIGURABLE_REGIONS_SUPPORTED
    bool
    default n

config SOC_MPU_MIN_REGION_SIZE
    hex
    default 0x20000000

config SOC_MPU_REGIONS_MAX_NUM
    int
    default 8

config SOC_MPU_REGION_RO_SUPPORTED
    bool
    default n

config SOC_MPU_REGION_WO_SUPPORTED
    bool
    default n

config SOC_PCNT_GROUPS
    int
    default 1

config SOC_PCNT_UNITS_PER_GROUP
    int
    default 8

config SOC_PCNT_CHANNELS_PER_UNIT
    int
    default 2

config SOC_PCNT_THRES_POINT_PER_UNIT
    int
    default 2

config SOC_RMT_GROUPS
    int
    default 1

config SOC_RMT_TX_CANDIDATES_PER_GROUP
    int
    default 8

config SOC_RMT_RX_CANDIDATES_PER_GROUP
    int
    default 8

config SOC_RMT_CHANNELS_PER_GROUP
    int
    default 8

config SOC_RMT_MEM_WORDS_PER_CHANNEL
    int
    default 64

config SOC_RMT_SUPPORT_REF_TICK
    bool
    default y

config SOC_RMT_SUPPORT_APB
    bool
    default y

config SOC_RMT_CHANNEL_CLK_INDEPENDENT
    bool
    default y

config SOC_RTCIO_PIN_COUNT
    int
    default 18

config SOC_RTCIO_INPUT_OUTPUT_SUPPORTED
    bool
    default y

config SOC_RTCIO_HOLD_SUPPORTED
    bool
    default y

config SOC_RTCIO_WAKE_SUPPORTED
    bool
    default y

config SOC_SDM_GROUPS
    int
    default 1

config SOC_SDM_CHANNELS_PER_GROUP
    int
    default 8

config SOC_SDM_CLK_SUPPORT_APB
    bool
    default y

config SOC_SPI_HD_BOTH_INOUT_SUPPORTED
    bool
    default y

config SOC_SPI_AS_CS_SUPPORTED
    bool
    default y

config SOC_SPI_PERIPH_NUM
    int
    default 3

config SOC_SPI_DMA_CHAN_NUM
    int
    default 2

config SOC_SPI_MAX_CS_NUM
    int
    default 3

config SOC_SPI_SUPPORT_CLK_APB
    bool
    default y

config SOC_SPI_MAXIMUM_BUFFER_SIZE
    int
    default 64

config SOC_SPI_MAX_PRE_DIVIDER
    int
    default 8192

config SOC_MEMSPI_SRC_FREQ_80M_SUPPORTED
    bool
    default y

config SOC_MEMSPI_SRC_FREQ_40M_SUPPORTED
    bool
    default y

config SOC_MEMSPI_SRC_FREQ_26M_SUPPORTED
    bool
    default y

config SOC_MEMSPI_SRC_FREQ_20M_SUPPORTED
    bool
    default y

config SOC_TIMER_GROUPS
    int
    default 2

config SOC_TIMER_GROUP_TIMERS_PER_GROUP
    int
    default 2

config SOC_TIMER_GROUP_COUNTER_BIT_WIDTH
    int
    default 64

config SOC_TIMER_GROUP_TOTAL_TIMERS
    int
    default 4

config SOC_TIMER_GROUP_SUPPORT_APB
    bool
    default y

config SOC_LP_TIMER_BIT_WIDTH_LO
    int
    default 32

config SOC_LP_TIMER_BIT_WIDTH_HI
    int
    default 16

config SOC_TOUCH_SENSOR_VERSION
    int
    default 1

config SOC_TOUCH_SENSOR_NUM
    int
    default 10

config SOC_TOUCH_SAMPLE_CFG_NUM
    int
    default 1

config SOC_TWAI_CONTROLLER_NUM
    int
    default 1

config SOC_TWAI_BRP_MIN
    int
    default 2

config SOC_TWAI_CLK_SUPPORT_APB
    bool
    default y

config SOC_TWAI_SUPPORT_MULTI_ADDRESS_LAYOUT
    bool
    default y

config SOC_UART_NUM
    int
    default 3

config SOC_UART_HP_NUM
    int
    default 3

config SOC_UART_SUPPORT_APB_CLK
    bool
    default y

config SOC_UART_SUPPORT_REF_TICK
    bool
    default y

config SOC_UART_FIFO_LEN
    int
    default 128

config SOC_UART_BITRATE_MAX
    int
    default 5000000

config SOC_SPIRAM_SUPPORTED
    bool
    default y

config SOC_SPI_MEM_SUPPORT_CONFIG_GPIO_BY_EFUSE
    bool
    default y

config SOC_SHA_SUPPORT_PARALLEL_ENG
    bool
    default y

config SOC_SHA_ENDIANNESS_BE
    bool
    default y

config SOC_SHA_SUPPORT_SHA1
    bool
    default y

config SOC_SHA_SUPPORT_SHA256
    bool
    default y

config SOC_SHA_SUPPORT_SHA384
    bool
    default y

config SOC_SHA_SUPPORT_SHA512
    bool
    default y

config SOC_MPI_MEM_BLOCKS_NUM
    int
    default 4

config SOC_MPI_OPERATIONS_NUM
    bool
    default y

config SOC_RSA_MAX_BIT_LEN
    int
    default 4096

config SOC_AES_SUPPORT_AES_128
    bool
    default y

config SOC_AES_SUPPORT_AES_192
    bool
    default y

config SOC_AES_SUPPORT_AES_256
    bool
    default y

config SOC_SECURE_BOOT_V1
    bool
    default y

config SOC_EFUSE_SECURE_BOOT_KEY_DIGESTS
    bool
    default y

config SOC_FLASH_ENCRYPTED_XTS_AES_BLOCK_MAX
    int
    default 32

config SOC_PHY_DIG_REGS_MEM_SIZE
    int
    default 21

config SOC_PM_SUPPORT_EXT0_WAKEUP
    bool
    default y

config SOC_PM_SUPPORT_EXT1_WAKEUP
    bool
    default y

config SOC_PM_SUPPORT_EXT_WAKEUP
    bool
    default y

config SOC_PM_SUPPORT_TOUCH_SENSOR_WAKEUP
    bool
    default y

config SOC_PM_SUPPORT_RTC_PERIPH_PD
    bool
    default y

config SOC_PM_SUPPORT_RTC_FAST_MEM_PD
    bool
    default y

config SOC_PM_SUPPORT_RTC_SLOW_MEM_PD
    bool
    default y

config SOC_PM_SUPPORT_RC_FAST_PD
    bool
    default y

config SOC_PM_SUPPORT_VDDSDIO_PD
    bool
    default y

config SOC_PM_SUPPORT_MODEM_PD
    bool
    default y

config SOC_CONFIGURABLE_VDDSDIO_SUPPORTED
    bool
    default y

config SOC_PM_MODEM_PD_BY_SW
    bool
    default y

config SOC_CLK_APLL_SUPPORTED
    bool
    default y

config SOC_CLK_RC_FAST_D256_SUPPORTED
    bool
    default y

config SOC_RTC_SLOW_CLK_SUPPORT_RC_FAST_D256
    bool
    default y

config SOC_CLK_RC_FAST_SUPPORT_CALIBRATION
    bool
    default y

config SOC_CLK_XTAL32K_SUPPORTED
    bool
    default y

config SOC_SDMMC_USE_IOMUX
    bool
    default y

config SOC_SDMMC_NUM_SLOTS
    int
    default 2

config SOC_WIFI_WAPI_SUPPORT
    bool
    default y

config SOC_WIFI_CSI_SUPPORT
    bool
    default y

config SOC_WIFI_MESH_SUPPORT
    bool
    default y

config SOC_WIFI_SUPPORT_VARIABLE_BEACON_WINDOW
    bool
    default y

config SOC_WIFI_NAN_SUPPORT
    bool
    default y

config SOC_BLE_SUPPORTED
    bool
    default y

config SOC_BLE_MESH_SUPPORTED
    bool
    default y

config SOC_BT_CLASSIC_SUPPORTED
    bool
    default y

config SOC_BLE_DEVICE_PRIVACY_SUPPORTED
    bool
    default n

config SOC_BLUFI_SUPPORTED
    bool
    default y

config SOC_BT_H2C_ENC_KEY_CTRL_ENH_VSC_SUPPORTED
    bool
    default y

config SOC_ULP_HAS_ADC
    bool
    default y

config SOC_PHY_COMBO_MODULE
    bool
    default y

config SOC_EMAC_RMII_CLK_OUT_INTERNAL_LOOPBACK
    bool
    default y
