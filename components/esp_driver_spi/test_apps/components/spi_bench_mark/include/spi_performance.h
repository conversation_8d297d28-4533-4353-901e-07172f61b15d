/*
 * SPDX-FileCopyrightText: 2024 Espressif Systems (Shanghai) CO LTD
 *
 * SPDX-License-Identifier: Apache-2.0
 */

#pragma once

#if CONFIG_IDF_TARGET_ESP32
#define IDF_PERFORMANCE_MAX_SPI_CLK_FREQ                            16*1000*1000
#define IDF_PERFORMANCE_MAX_SPI_PER_TRANS_POLLING                   15
#define IDF_PERFORMANCE_MAX_SPI_PER_TRANS_POLLING_NO_DMA            15
#if !CONFIG_FREERTOS_SMP // IDF-5223
#define IDF_PERFORMANCE_MAX_SPI_PER_TRANS_NO_POLLING                34      // TODO: IDF-5180
#define IDF_PERFORMANCE_MAX_SPI_PER_TRANS_NO_POLLING_NO_DMA         30      // TODO: IDF-5180
#else
#define IDF_PERFORMANCE_MAX_SPI_PER_TRANS_NO_POLLING                50
#define IDF_PERFORMANCE_MAX_SPI_PER_TRANS_NO_POLLING_NO_DMA         50
#endif

#elif CONFIG_IDF_TARGET_ESP32S2
#define IDF_PERFORMANCE_MAX_SPI_CLK_FREQ                            40*1000*1000
#define IDF_PERFORMANCE_MAX_SPI_PER_TRANS_POLLING                   15
#define IDF_PERFORMANCE_MAX_SPI_PER_TRANS_POLLING_NO_DMA            15
#define IDF_PERFORMANCE_MAX_SPI_PER_TRANS_NO_POLLING                32
#define IDF_PERFORMANCE_MAX_SPI_PER_TRANS_NO_POLLING_NO_DMA         30

#elif CONFIG_IDF_TARGET_ESP32S3
#define IDF_PERFORMANCE_MAX_SPI_CLK_FREQ                            40*1000*1000
#define IDF_PERFORMANCE_MAX_SPI_PER_TRANS_POLLING                   15
#define IDF_PERFORMANCE_MAX_SPI_PER_TRANS_POLLING_NO_DMA            15
#define IDF_PERFORMANCE_MAX_SPI_PER_TRANS_NO_POLLING                32
#define IDF_PERFORMANCE_MAX_SPI_PER_TRANS_NO_POLLING_NO_DMA         30

#elif CONFIG_IDF_TARGET_ESP32C2
#define IDF_PERFORMANCE_MAX_SPI_CLK_FREQ                            40*1000*1000
#define IDF_PERFORMANCE_MAX_SPI_PER_TRANS_POLLING                   23
#define IDF_PERFORMANCE_MAX_SPI_PER_TRANS_POLLING_NO_DMA            18
#define IDF_PERFORMANCE_MAX_SPI_PER_TRANS_NO_POLLING                47
#define IDF_PERFORMANCE_MAX_SPI_PER_TRANS_NO_POLLING_NO_DMA         42

#elif CONFIG_IDF_TARGET_ESP32C3
#define IDF_PERFORMANCE_MAX_SPI_CLK_FREQ                            40*1000*1000
#if !CONFIG_FREERTOS_SMP // IDF-5223
#define IDF_PERFORMANCE_MAX_SPI_PER_TRANS_POLLING                   15
#define IDF_PERFORMANCE_MAX_SPI_PER_TRANS_POLLING_NO_DMA            15
#define IDF_PERFORMANCE_MAX_SPI_PER_TRANS_NO_POLLING                33
#define IDF_PERFORMANCE_MAX_SPI_PER_TRANS_NO_POLLING_NO_DMA         30
#else
#define IDF_PERFORMANCE_MAX_SPI_PER_TRANS_POLLING                   17
#define IDF_PERFORMANCE_MAX_SPI_PER_TRANS_POLLING_NO_DMA            17
#define IDF_PERFORMANCE_MAX_SPI_PER_TRANS_NO_POLLING                60
#define IDF_PERFORMANCE_MAX_SPI_PER_TRANS_NO_POLLING_NO_DMA         60
#endif

#elif CONFIG_IDF_TARGET_ESP32C6
#define IDF_PERFORMANCE_MAX_SPI_CLK_FREQ                            26*1000*1000
#define IDF_PERFORMANCE_MAX_SPI_PER_TRANS_NO_POLLING                35  //TODO: IDF-9551, check perform
#define IDF_PERFORMANCE_MAX_SPI_PER_TRANS_POLLING                   17
#define IDF_PERFORMANCE_MAX_SPI_PER_TRANS_NO_POLLING_NO_DMA         32
#define IDF_PERFORMANCE_MAX_SPI_PER_TRANS_POLLING_NO_DMA            15

#elif CONFIG_IDF_TARGET_ESP32H2
#define IDF_PERFORMANCE_MAX_SPI_CLK_FREQ                            26*1000*1000
#define IDF_PERFORMANCE_MAX_SPI_PER_TRANS_POLLING                   32
#define IDF_PERFORMANCE_MAX_SPI_PER_TRANS_POLLING_NO_DMA            25
#define IDF_PERFORMANCE_MAX_SPI_PER_TRANS_NO_POLLING                61
#define IDF_PERFORMANCE_MAX_SPI_PER_TRANS_NO_POLLING_NO_DMA         54

#elif CONFIG_IDF_TARGET_ESP32P4
#define IDF_PERFORMANCE_MAX_SPI_CLK_FREQ                            26*1000*1000
#define IDF_PERFORMANCE_MAX_SPI_PER_TRANS_NO_POLLING                44
#define IDF_PERFORMANCE_MAX_SPI_PER_TRANS_POLLING                   28
#define IDF_PERFORMANCE_MAX_SPI_PER_TRANS_NO_POLLING_NO_DMA         26
#define IDF_PERFORMANCE_MAX_SPI_PER_TRANS_POLLING_NO_DMA            12

#elif CONFIG_IDF_TARGET_ESP32C5
#define IDF_PERFORMANCE_MAX_SPI_CLK_FREQ                            40*1000*1000
#define IDF_PERFORMANCE_MAX_SPI_PER_TRANS_NO_POLLING                24
#define IDF_PERFORMANCE_MAX_SPI_PER_TRANS_POLLING                   15
#define IDF_PERFORMANCE_MAX_SPI_PER_TRANS_NO_POLLING_NO_DMA         22
#define IDF_PERFORMANCE_MAX_SPI_PER_TRANS_POLLING_NO_DMA            12

#elif CONFIG_IDF_TARGET_ESP32C61
#define IDF_PERFORMANCE_MAX_SPI_CLK_FREQ                            40*1000*1000
#define IDF_PERFORMANCE_MAX_SPI_PER_TRANS_NO_POLLING                32
#define IDF_PERFORMANCE_MAX_SPI_PER_TRANS_POLLING                   19
#define IDF_PERFORMANCE_MAX_SPI_PER_TRANS_NO_POLLING_NO_DMA         29
#define IDF_PERFORMANCE_MAX_SPI_PER_TRANS_POLLING_NO_DMA            14

#else
#pragma message "`spi_performance.h` is not updated with your target"
#endif
