---
tools_root: C:\Program Files\IAR Systems\Embedded Workbench 6.0\
colour: true
:unity:
  :plugins: []
:tools:
  :test_compiler:
    :name: compiler
    :executable:
    - C:\Program Files\IAR Systems\Embedded Workbench 6.0\
    - sh\bin\iccsh.exe
    :arguments:
    - "-e"
    - "--char_is_signed"
    - "-Ol"
    - "--no_cse"
    - "--no_unroll"
    - "--no_inline"
    - "--no_code_motion"
    - "--no_tbaa"
    - "--no_scheduling"
    - "--no_clustering"
    - "--debug"
    - "--dlib_config"
    - - C:\Program Files\IAR Systems\Embedded Workbench 6.0\
      - sh\inc\DLib_Product.h
    - "--double=32"
    - "--code_model=huge"
    - "--data_model=huge"
    - "--core=sh2afpu"
    - "--warnings_affect_exit_code"
    - "--warnings_are_errors"
    - "--mfc"
    - "--use_unix_directory_separators"
    - "--diag_suppress=Pe161"
    - '-I"$": COLLECTION_PATHS_TEST_TOOLCHAIN_INCLUDE'
    - '-I"$": COLLECTION_PATHS_TEST_SUPPORT_SOURCE_INCLUDE_VENDOR'
    - "-D$: COLLECTION_DEFINES_TEST_AND_VENDOR"
    - "${1}"
    - "-o ${2}"
  :test_linker:
    :name: linker
    :executable:
    - C:\Program Files\IAR Systems\Embedded Workbench 6.0\
    - sh\bin\ilinksh.exe
    :arguments:
    - "${1}"
    - "--redirect __Printf=__PrintfSmall"
    - "--redirect __Scanf=__ScanfSmall"
    - "--config"
    - - C:\Program Files\IAR Systems\Embedded Workbench 6.0\
      - sh\config\generic.icf
    - "--config_def _CSTACK_SIZE=0x800"
    - "--config_def _HEAP_SIZE=0x800"
    - "--config_def _INT_TABLE=0x10"
    - "--entry __iar_program_start"
    - "--debug_lib"
    - "-o ${2}"
  :test_fixture:
    :name: simulator
    :executable:
    - C:\Program Files\IAR Systems\Embedded Workbench 6.0\
    - common\bin\CSpyBat.exe
    :arguments:
    - "--silent"
    - - C:\Program Files\IAR Systems\Embedded Workbench 6.0\
      - sh\bin\shproc.dll
    - - C:\Program Files\IAR Systems\Embedded Workbench 6.0\
      - sh\bin\shsim.dll
    - "${1}"
    - "--plugin"
    - - C:\Program Files\IAR Systems\Embedded Workbench 6.0\
      - sh\bin\shbat.dll
    - "--backend"
    - "-B"
    - "--core sh2afpu"
    - "-p"
    - - C:\Program Files\IAR Systems\Embedded Workbench 6.0\
      - sh\config\debugger\io7264.ddf
    - "-d"
    - sim
:extension:
  :object: ".o"
  :executable: ".out"
:paths:
  :test:
    - - C:\Program Files\IAR Systems\Embedded Workbench 6.0\
      - sh\inc\
    - - C:\Program Files\IAR Systems\Embedded Workbench 6.0\
      - sh\inc\c
    - src\
    - "..\\src\\"
    - testdata/
    - tests\
    - vendor\unity\src\
:defines:
  :test:
  - UNITY_SUPPORT_64
  - UNITY_SUPPORT_TEST_CASES
