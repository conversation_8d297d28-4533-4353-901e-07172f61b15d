---
Checks: >
  -*,
  bugprone-argument-comment,
  bugprone-too-small-loop-variable,
  google-explicit-constructor,
  google-readability-casting,
  misc-unused-using-decls,
  modernize-loop-convert,
  modernize-use-bool-literals,
  modernize-use-equals-default,
  modernize-use-equals-delete,
  modernize-use-nullptr,
  readability-avoid-const-params-in-decls,
  readability-else-after-return,
  readability-inconsistent-declaration-parameter-name,
  readability-make-member-function-const,
  readability-redundant-control-flow,
  readability-redundant-member-init,
  readability-simplify-boolean-expr,
  readability-static-accessed-through-instance
WarningsAsErrors: '*'
HeaderFilterRegex: '(examples|include|src).*(?<!third_party.*repo)'
