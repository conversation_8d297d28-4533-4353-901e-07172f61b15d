# Tests of the generic message digest interface
MD list
mbedtls_md_list:

MD <-> PSA conversion
md_to_from_psa:

MD NULL/uninitialised arguments
md_null_args:

Information on MD5
depends_on:MBEDTLS_MD_CAN_MD5
md_info:MBEDTLS_MD_MD5:"MD5":16

Information on RIPEMD160
depends_on:MBEDTLS_MD_CAN_RIPEMD160
md_info:MBEDTLS_MD_RIPEMD160:"RIPEMD160":20

Information on SHA1
depends_on:MBEDTLS_MD_CAN_SHA1
md_info:MBEDTLS_MD_SHA1:"SHA1":20

Information on SHA224
depends_on:MBEDTLS_MD_CAN_SHA224
md_info:MBEDTLS_MD_SHA224:"SHA224":28

Information on SHA256
depends_on:MBEDTLS_MD_CAN_SHA256
md_info:MBEDTLS_MD_SHA256:"SHA256":32

Information on SHA384
depends_on:MBEDTLS_MD_CAN_SHA384
md_info:MBEDTLS_MD_SHA384:"SHA384":48

Information on SHA512
depends_on:MBEDTLS_MD_CAN_SHA512
md_info:MBEDTLS_MD_SHA512:"SHA512":64

Information on SHA3-224
depends_on:MBEDTLS_MD_CAN_SHA3_224
md_info:MBEDTLS_MD_SHA3_224:"SHA3-224":28

Information on SHA3-256
depends_on:MBEDTLS_MD_CAN_SHA3_256
md_info:MBEDTLS_MD_SHA3_256:"SHA3-256":32

Information on SHA3-384
depends_on:MBEDTLS_MD_CAN_SHA3_384
md_info:MBEDTLS_MD_SHA3_384:"SHA3-384":48

Information on SHA3-512
depends_on:MBEDTLS_MD_CAN_SHA3_512
md_info:MBEDTLS_MD_SHA3_512:"SHA3-512":64

generic mbedtls_md5 Test vector RFC1321 #1
depends_on:MBEDTLS_MD_CAN_MD5
md_text:MBEDTLS_MD_MD5:"":"d41d8cd98f00b204e9800998ecf8427e"

generic mbedtls_md5 Test vector RFC1321 #2
depends_on:MBEDTLS_MD_CAN_MD5
md_text:MBEDTLS_MD_MD5:"a":"0cc175b9c0f1b6a831c399e269772661"

generic mbedtls_md5 Test vector RFC1321 #3
depends_on:MBEDTLS_MD_CAN_MD5
md_text:MBEDTLS_MD_MD5:"abc":"900150983cd24fb0d6963f7d28e17f72"

generic mbedtls_md5 Test vector RFC1321 #4
depends_on:MBEDTLS_MD_CAN_MD5
md_text:MBEDTLS_MD_MD5:"message digest":"f96b697d7cb7938d525a2f31aaf161d0"

generic mbedtls_md5 Test vector RFC1321 #5
depends_on:MBEDTLS_MD_CAN_MD5
md_text:MBEDTLS_MD_MD5:"abcdefghijklmnopqrstuvwxyz":"c3fcd3d76192e4007dfb496cca67e13b"

generic mbedtls_md5 Test vector RFC1321 #6
depends_on:MBEDTLS_MD_CAN_MD5
md_text:MBEDTLS_MD_MD5:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789":"d174ab98d277d9f5a5611c2c9f419d9f"

generic mbedtls_md5 Test vector RFC1321 #7
depends_on:MBEDTLS_MD_CAN_MD5
md_text:MBEDTLS_MD_MD5:"12345678901234567890123456789012345678901234567890123456789012345678901234567890":"57edf4a22be3c955ac49da2e2107b67a"

generic mbedtls_ripemd160 Test vector from paper #1
depends_on:MBEDTLS_MD_CAN_RIPEMD160
md_text:MBEDTLS_MD_RIPEMD160:"":"9c1185a5c5e9fc54612808977ee8f548b2258d31"

generic mbedtls_ripemd160 Test vector from paper #2
depends_on:MBEDTLS_MD_CAN_RIPEMD160
md_text:MBEDTLS_MD_RIPEMD160:"a":"0bdc9d2d256b3ee9daae347be6f4dc835a467ffe"

generic mbedtls_ripemd160 Test vector from paper #3
depends_on:MBEDTLS_MD_CAN_RIPEMD160
md_text:MBEDTLS_MD_RIPEMD160:"abc":"8eb208f7e05d987a9b044a8e98c6b087f15a0bfc"

generic mbedtls_ripemd160 Test vector from paper #4
depends_on:MBEDTLS_MD_CAN_RIPEMD160
md_text:MBEDTLS_MD_RIPEMD160:"message digest":"5d0689ef49d2fae572b881b123a85ffa21595f36"

generic mbedtls_ripemd160 Test vector from paper #5
depends_on:MBEDTLS_MD_CAN_RIPEMD160
md_text:MBEDTLS_MD_RIPEMD160:"abcdefghijklmnopqrstuvwxyz":"f71c27109c692c1b56bbdceb5b9d2865b3708dbc"

generic mbedtls_ripemd160 Test vector from paper #6
depends_on:MBEDTLS_MD_CAN_RIPEMD160
md_text:MBEDTLS_MD_RIPEMD160:"abcdbcdecdefdefgefghfghighijhijkijkljklmklmnlmnomnopnopq":"12a053384a9c0c88e405a06c27dcf49ada62eb2b"

generic mbedtls_ripemd160 Test vector from paper #7
depends_on:MBEDTLS_MD_CAN_RIPEMD160
md_text:MBEDTLS_MD_RIPEMD160:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789":"b0e20b6e3116640286ed3a87a5713079b21f5189"

generic mbedtls_ripemd160 Test vector from paper #8
depends_on:MBEDTLS_MD_CAN_RIPEMD160
md_text:MBEDTLS_MD_RIPEMD160:"12345678901234567890123456789012345678901234567890123456789012345678901234567890":"9b752e45573d4b39f4dbd3323cab82bf63326bfb"

generic mbedtls_sha3 SHA3-224 Test vector from CAVS 19.0 with Len = 8
depends_on:MBEDTLS_MD_CAN_SHA3_224
md_hex:MBEDTLS_MD_SHA3_224:"01":"488286d9d32716e5881ea1ee51f36d3660d70f0db03b3f612ce9eda4"

generic mbedtls_sha3 SHA3-256 Test vector from CAVS 19.0 with Len = 8
depends_on:MBEDTLS_MD_CAN_SHA3_256
md_hex:MBEDTLS_MD_SHA3_256:"e9":"f0d04dd1e6cfc29a4460d521796852f25d9ef8d28b44ee91ff5b759d72c1e6d6"

generic mbedtls_sha3 SHA3-384 Test vector from CAVS 19.0 with Len = 8
depends_on:MBEDTLS_MD_CAN_SHA3_384
md_hex:MBEDTLS_MD_SHA3_384:"80":"7541384852e10ff10d5fb6a7213a4a6c15ccc86d8bc1068ac04f69277142944f4ee50d91fdc56553db06b2f5039c8ab7"

generic mbedtls_sha3 SHA3-512 Test vector from CAVS 19.0 with Len = 8
depends_on:MBEDTLS_MD_CAN_SHA3_512
md_hex:MBEDTLS_MD_SHA3_512:"e5":"150240baf95fb36f8ccb87a19a41767e7aed95125075a2b2dbba6e565e1ce8575f2b042b62e29a04e9440314a821c6224182964d8b557b16a492b3806f4c39c1"

generic HMAC-MD5 Hash File OpenSSL test #1
depends_on:MBEDTLS_MD_CAN_MD5
mbedtls_md_hmac:MBEDTLS_MD_MD5:16:"61616161616161616161616161616161":"b91ce5ac77d33c234e61002ed6":"42552882f00bd4633ea81135a184b284"

generic HMAC-MD5 Hash File OpenSSL test #2
depends_on:MBEDTLS_MD_CAN_MD5
mbedtls_md_hmac:MBEDTLS_MD_MD5:16:"61616161616161616161616161616161":"270fcf11f27c27448457d7049a7edb084a3e554e0b2acf5806982213f0ad516402e4c869c4ff2171e18e3489baa3125d2c3056ebb616296f9b6aa97ef68eeabcdc0b6dde47775004096a241efcf0a90d19b34e898cc7340cdc940f8bdd46e23e352f34bca131d4d67a7c2ddb8d0d68b67f06152a128168e1c341c37e0a66c5018999b7059bcc300beed2c19dd1152d2fe062853293b8f3c8b5":"a16a842891786d01fe50ba7731db7464"

generic HMAC-MD5 Hash File OpenSSL test #3
depends_on:MBEDTLS_MD_CAN_MD5
mbedtls_md_hmac:MBEDTLS_MD_MD5:16:"61616161616161616161616161616161616161616161616161616161616161616161616161616161616161616161616161616161616161616161616161616161616161616161":"b91ce5ac77d33c234e61002ed6":"e97f623936f98a7f741c4bd0612fecc2"

HMAC-MD5 Bouncy Castle test #1
depends_on:MBEDTLS_MD_CAN_MD5
mbedtls_md_hmac:MBEDTLS_MD_MD5:16:"0b0b0b0b0b0b0b0b0b0b0b0b0b0b0b0b0b0b0b0b":"4869205468657265":"5ccec34ea9656392457fa1ac27f08fbc"

generic HMAC-MD5 Test Vector RFC2202 #1
depends_on:MBEDTLS_MD_CAN_MD5
mbedtls_md_hmac:MBEDTLS_MD_MD5:16:"0b0b0b0b0b0b0b0b0b0b0b0b0b0b0b0b":"4869205468657265":"9294727a3638bb1c13f48ef8158bfc9d"

generic HMAC-MD5 Test Vector RFC2202 #2
depends_on:MBEDTLS_MD_CAN_MD5
mbedtls_md_hmac:MBEDTLS_MD_MD5:16:"4a656665":"7768617420646f2079612077616e7420666f72206e6f7468696e673f":"750c783e6ab0b503eaa86e310a5db738"

generic HMAC-MD5 Test Vector RFC2202 #3
depends_on:MBEDTLS_MD_CAN_MD5
mbedtls_md_hmac:MBEDTLS_MD_MD5:16:"aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa":"dddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddd":"56be34521d144c88dbb8c733f0e8b3f6"

generic HMAC-MD5 Test Vector RFC2202 #4
depends_on:MBEDTLS_MD_CAN_MD5
mbedtls_md_hmac:MBEDTLS_MD_MD5:16:"0102030405060708090a0b0c0d0e0f10111213141516171819":"cdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcd":"697eaf0aca3a3aea3a75164746ffaa79"

generic HMAC-MD5 Test Vector RFC2202 #5
depends_on:MBEDTLS_MD_CAN_MD5
mbedtls_md_hmac:MBEDTLS_MD_MD5:12:"0c0c0c0c0c0c0c0c0c0c0c0c0c0c0c0c":"546573742057697468205472756e636174696f6e":"56461ef2342edc00f9bab995"

generic HMAC-MD5 Test Vector RFC2202 #6
depends_on:MBEDTLS_MD_CAN_MD5
mbedtls_md_hmac:MBEDTLS_MD_MD5:16:"aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa":"54657374205573696e67204c6172676572205468616e20426c6f636b2d53697a65204b6579202d2048617368204b6579204669727374":"6b1ab7fe4bd7bf8f0b62e6ce61b9d0cd"

generic HMAC-MD5 Test Vector RFC2202 #7
depends_on:MBEDTLS_MD_CAN_MD5
mbedtls_md_hmac:MBEDTLS_MD_MD5:16:"aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa":"54657374205573696e67204c6172676572205468616e20426c6f636b2d53697a65204b657920616e64204c6172676572205468616e204f6e6520426c6f636b2d53697a652044617461":"6f630fad67cda0ee1fb1f562db3aa53e"

generic HMAC-RIPEMD160 Test vector RFC 2286 #1
depends_on:MBEDTLS_MD_CAN_RIPEMD160
mbedtls_md_hmac:MBEDTLS_MD_RIPEMD160:20:"0b0b0b0b0b0b0b0b0b0b0b0b0b0b0b0b0b0b0b0b":"4869205468657265":"24cb4bd67d20fc1a5d2ed7732dcc39377f0a5668"

generic HMAC-RIPEMD160 Test vector RFC 2286 #2
depends_on:MBEDTLS_MD_CAN_RIPEMD160
mbedtls_md_hmac:MBEDTLS_MD_RIPEMD160:20:"4a656665":"7768617420646f2079612077616e7420666f72206e6f7468696e673f":"dda6c0213a485a9e24f4742064a7f033b43c4069"

generic HMAC-RIPEMD160 Test vector RFC 2286 #3
depends_on:MBEDTLS_MD_CAN_RIPEMD160
mbedtls_md_hmac:MBEDTLS_MD_RIPEMD160:20:"aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa":"dddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddd":"b0b105360de759960ab4f35298e116e295d8e7c1"

generic HMAC-RIPEMD160 Test vector RFC 2286 #4
depends_on:MBEDTLS_MD_CAN_RIPEMD160
mbedtls_md_hmac:MBEDTLS_MD_RIPEMD160:20:"0102030405060708090a0b0c0d0e0f10111213141516171819":"cdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcd":"d5ca862f4d21d5e610e18b4cf1beb97a4365ecf4"

generic HMAC-RIPEMD160 Test vector RFC 2286 #5
depends_on:MBEDTLS_MD_CAN_RIPEMD160
mbedtls_md_hmac:MBEDTLS_MD_RIPEMD160:20:"0c0c0c0c0c0c0c0c0c0c0c0c0c0c0c0c0c0c0c0c":"546573742057697468205472756e636174696f6e":"7619693978f91d90539ae786500ff3d8e0518e39"

generic HMAC-RIPEMD160 Test vector RFC 2286 #6
depends_on:MBEDTLS_MD_CAN_RIPEMD160
mbedtls_md_hmac:MBEDTLS_MD_RIPEMD160:20:"aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa":"54657374205573696e67204c6172676572205468616e20426c6f636b2d53697a65204b6579202d2048617368204b6579204669727374":"6466ca07ac5eac29e1bd523e5ada7605b791fd8b"

generic HMAC-RIPEMD160 Test vector RFC 2286 #7
depends_on:MBEDTLS_MD_CAN_RIPEMD160
mbedtls_md_hmac:MBEDTLS_MD_RIPEMD160:20:"aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa":"54657374205573696e67204c6172676572205468616e20426c6f636b2d53697a65204b657920616e64204c6172676572205468616e204f6e6520426c6f636b2d53697a652044617461":"69ea60798d71616cce5fd0871e23754cd75d5a0a"

generic multi step mbedtls_md5 Test vector RFC1321 #1
depends_on:MBEDTLS_MD_CAN_MD5
md_text_multi:MBEDTLS_MD_MD5:"":"d41d8cd98f00b204e9800998ecf8427e"

generic multi step mbedtls_md5 Test vector RFC1321 #2
depends_on:MBEDTLS_MD_CAN_MD5
md_text_multi:MBEDTLS_MD_MD5:"a":"0cc175b9c0f1b6a831c399e269772661"

generic multi step mbedtls_md5 Test vector RFC1321 #3
depends_on:MBEDTLS_MD_CAN_MD5
md_text_multi:MBEDTLS_MD_MD5:"abc":"900150983cd24fb0d6963f7d28e17f72"

generic multi step mbedtls_md5 Test vector RFC1321 #4
depends_on:MBEDTLS_MD_CAN_MD5
md_text_multi:MBEDTLS_MD_MD5:"message digest":"f96b697d7cb7938d525a2f31aaf161d0"

generic multi step mbedtls_md5 Test vector RFC1321 #5
depends_on:MBEDTLS_MD_CAN_MD5
md_text_multi:MBEDTLS_MD_MD5:"abcdefghijklmnopqrstuvwxyz":"c3fcd3d76192e4007dfb496cca67e13b"

generic multi step mbedtls_md5 Test vector RFC1321 #6
depends_on:MBEDTLS_MD_CAN_MD5
md_text_multi:MBEDTLS_MD_MD5:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789":"d174ab98d277d9f5a5611c2c9f419d9f"

generic multi step mbedtls_md5 Test vector RFC1321 #7
depends_on:MBEDTLS_MD_CAN_MD5
md_text_multi:MBEDTLS_MD_MD5:"12345678901234567890123456789012345678901234567890123456789012345678901234567890":"57edf4a22be3c955ac49da2e2107b67a"

generic multi step mbedtls_ripemd160 Test vector from paper #1
depends_on:MBEDTLS_MD_CAN_RIPEMD160
md_text_multi:MBEDTLS_MD_RIPEMD160:"":"9c1185a5c5e9fc54612808977ee8f548b2258d31"

generic multi step mbedtls_ripemd160 Test vector from paper #2
depends_on:MBEDTLS_MD_CAN_RIPEMD160
md_text_multi:MBEDTLS_MD_RIPEMD160:"a":"0bdc9d2d256b3ee9daae347be6f4dc835a467ffe"

generic multi step mbedtls_ripemd160 Test vector from paper #3
depends_on:MBEDTLS_MD_CAN_RIPEMD160
md_text_multi:MBEDTLS_MD_RIPEMD160:"abc":"8eb208f7e05d987a9b044a8e98c6b087f15a0bfc"

generic multi step mbedtls_ripemd160 Test vector from paper #4
depends_on:MBEDTLS_MD_CAN_RIPEMD160
md_text_multi:MBEDTLS_MD_RIPEMD160:"message digest":"5d0689ef49d2fae572b881b123a85ffa21595f36"

generic multi step mbedtls_ripemd160 Test vector from paper #5
depends_on:MBEDTLS_MD_CAN_RIPEMD160
md_text_multi:MBEDTLS_MD_RIPEMD160:"abcdefghijklmnopqrstuvwxyz":"f71c27109c692c1b56bbdceb5b9d2865b3708dbc"

generic multi step mbedtls_ripemd160 Test vector from paper #6
depends_on:MBEDTLS_MD_CAN_RIPEMD160
md_text_multi:MBEDTLS_MD_RIPEMD160:"abcdbcdecdefdefgefghfghighijhijkijkljklmklmnlmnomnopnopq":"12a053384a9c0c88e405a06c27dcf49ada62eb2b"

generic multi step mbedtls_ripemd160 Test vector from paper #7
depends_on:MBEDTLS_MD_CAN_RIPEMD160
md_text_multi:MBEDTLS_MD_RIPEMD160:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789":"b0e20b6e3116640286ed3a87a5713079b21f5189"

generic multi step mbedtls_ripemd160 Test vector from paper #8
depends_on:MBEDTLS_MD_CAN_RIPEMD160
md_text_multi:MBEDTLS_MD_RIPEMD160:"12345678901234567890123456789012345678901234567890123456789012345678901234567890":"9b752e45573d4b39f4dbd3323cab82bf63326bfb"

generic multi step mbedtls_sha3 SHA3-224 Test vector from CAVS 19.0 with Len = 48
depends_on:MBEDTLS_MD_CAN_SHA3_224
md_hex_multi:MBEDTLS_MD_SHA3_224:"e7183e4d89c9":"650618f3b945c07de85b8478d69609647d5e2a432c6b15fbb3db91e4"

generic multi step mbedtls_sha3 SHA3-256 Test vector from CAVS 19.0 with Len = 48
depends_on:MBEDTLS_MD_CAN_SHA3_256
md_hex_multi:MBEDTLS_MD_SHA3_256:"e6fd42037f80":"2294f8d3834f24aa9037c431f8c233a66a57b23fa3de10530bbb6911f6e1850f"

generic multi step mbedtls_sha3 SHA3-384 Test vector from CAVS 19.0 with Len = 48
depends_on:MBEDTLS_MD_CAN_SHA3_384
md_hex_multi:MBEDTLS_MD_SHA3_384:"5a6659e9f0e7":"21b1f3f63b907f968821185a7fe30b16d47e1d6ee5b9c80be68947854de7a8ef4a03a6b2e4ec96abdd4fa29ab9796f28"

generic multi step mbedtls_sha3 SHA3-512 Test vector from CAVS 19.0 with Len = 48
depends_on:MBEDTLS_MD_CAN_SHA3_512
md_hex_multi:MBEDTLS_MD_SHA3_512:"71a986d2f662":"def6aac2b08c98d56a0501a8cb93f5b47d6322daf99e03255457c303326395f765576930f8571d89c01e727cc79c2d4497f85c45691b554e20da810c2bc865ef"

generic multi step HMAC-MD5 Hash File OpenSSL test #1
depends_on:MBEDTLS_MD_CAN_MD5
md_hmac_multi:MBEDTLS_MD_MD5:16:"61616161616161616161616161616161":"b91ce5ac77d33c234e61002ed6":"42552882f00bd4633ea81135a184b284"

generic multi step HMAC-MD5 Hash File OpenSSL test #2
depends_on:MBEDTLS_MD_CAN_MD5
md_hmac_multi:MBEDTLS_MD_MD5:16:"61616161616161616161616161616161":"270fcf11f27c27448457d7049a7edb084a3e554e0b2acf5806982213f0ad516402e4c869c4ff2171e18e3489baa3125d2c3056ebb616296f9b6aa97ef68eeabcdc0b6dde47775004096a241efcf0a90d19b34e898cc7340cdc940f8bdd46e23e352f34bca131d4d67a7c2ddb8d0d68b67f06152a128168e1c341c37e0a66c5018999b7059bcc300beed2c19dd1152d2fe062853293b8f3c8b5":"a16a842891786d01fe50ba7731db7464"

generic multi step HMAC-MD5 Hash File OpenSSL test #3
depends_on:MBEDTLS_MD_CAN_MD5
md_hmac_multi:MBEDTLS_MD_MD5:16:"61616161616161616161616161616161616161616161616161616161616161616161616161616161616161616161616161616161616161616161616161616161616161616161":"b91ce5ac77d33c234e61002ed6":"e97f623936f98a7f741c4bd0612fecc2"

generic multi step HMAC-MD5 Test Vector RFC2202 #1
depends_on:MBEDTLS_MD_CAN_MD5
md_hmac_multi:MBEDTLS_MD_MD5:16:"0b0b0b0b0b0b0b0b0b0b0b0b0b0b0b0b":"4869205468657265":"9294727a3638bb1c13f48ef8158bfc9d"

generic multi step HMAC-MD5 Test Vector RFC2202 #2
depends_on:MBEDTLS_MD_CAN_MD5
md_hmac_multi:MBEDTLS_MD_MD5:16:"4a656665":"7768617420646f2079612077616e7420666f72206e6f7468696e673f":"750c783e6ab0b503eaa86e310a5db738"

generic multi step HMAC-MD5 Test Vector RFC2202 #3
depends_on:MBEDTLS_MD_CAN_MD5
md_hmac_multi:MBEDTLS_MD_MD5:16:"aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa":"dddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddd":"56be34521d144c88dbb8c733f0e8b3f6"

generic multi step HMAC-MD5 Test Vector RFC2202 #4
depends_on:MBEDTLS_MD_CAN_MD5
md_hmac_multi:MBEDTLS_MD_MD5:16:"0102030405060708090a0b0c0d0e0f10111213141516171819":"cdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcd":"697eaf0aca3a3aea3a75164746ffaa79"

generic multi step HMAC-MD5 Test Vector RFC2202 #5
depends_on:MBEDTLS_MD_CAN_MD5
md_hmac_multi:MBEDTLS_MD_MD5:12:"0c0c0c0c0c0c0c0c0c0c0c0c0c0c0c0c":"546573742057697468205472756e636174696f6e":"56461ef2342edc00f9bab995"

generic multi step HMAC-MD5 Test Vector RFC2202 #6
depends_on:MBEDTLS_MD_CAN_MD5
md_hmac_multi:MBEDTLS_MD_MD5:16:"aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa":"54657374205573696e67204c6172676572205468616e20426c6f636b2d53697a65204b6579202d2048617368204b6579204669727374":"6b1ab7fe4bd7bf8f0b62e6ce61b9d0cd"

generic multi step HMAC-MD5 Test Vector RFC2202 #7
depends_on:MBEDTLS_MD_CAN_MD5
md_hmac_multi:MBEDTLS_MD_MD5:16:"aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa":"54657374205573696e67204c6172676572205468616e20426c6f636b2d53697a65204b657920616e64204c6172676572205468616e204f6e6520426c6f636b2d53697a652044617461":"6f630fad67cda0ee1fb1f562db3aa53e"

generic multi step HMAC-RIPEMD160 Test vector RFC 2286 #1
depends_on:MBEDTLS_MD_CAN_RIPEMD160
md_hmac_multi:MBEDTLS_MD_RIPEMD160:20:"0b0b0b0b0b0b0b0b0b0b0b0b0b0b0b0b0b0b0b0b":"4869205468657265":"24cb4bd67d20fc1a5d2ed7732dcc39377f0a5668"

generic multi step HMAC-RIPEMD160 Test vector RFC 2286 #2
depends_on:MBEDTLS_MD_CAN_RIPEMD160
md_hmac_multi:MBEDTLS_MD_RIPEMD160:20:"4a656665":"7768617420646f2079612077616e7420666f72206e6f7468696e673f":"dda6c0213a485a9e24f4742064a7f033b43c4069"

generic multi step HMAC-RIPEMD160 Test vector RFC 2286 #3
depends_on:MBEDTLS_MD_CAN_RIPEMD160
md_hmac_multi:MBEDTLS_MD_RIPEMD160:20:"aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa":"dddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddd":"b0b105360de759960ab4f35298e116e295d8e7c1"

generic multi step HMAC-RIPEMD160 Test vector RFC 2286 #4
depends_on:MBEDTLS_MD_CAN_RIPEMD160
md_hmac_multi:MBEDTLS_MD_RIPEMD160:20:"0102030405060708090a0b0c0d0e0f10111213141516171819":"cdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcd":"d5ca862f4d21d5e610e18b4cf1beb97a4365ecf4"

generic multi step HMAC-RIPEMD160 Test vector RFC 2286 #5
depends_on:MBEDTLS_MD_CAN_RIPEMD160
md_hmac_multi:MBEDTLS_MD_RIPEMD160:20:"0c0c0c0c0c0c0c0c0c0c0c0c0c0c0c0c0c0c0c0c":"546573742057697468205472756e636174696f6e":"7619693978f91d90539ae786500ff3d8e0518e39"

generic multi step HMAC-RIPEMD160 Test vector RFC 2286 #6
depends_on:MBEDTLS_MD_CAN_RIPEMD160
md_hmac_multi:MBEDTLS_MD_RIPEMD160:20:"aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa":"54657374205573696e67204c6172676572205468616e20426c6f636b2d53697a65204b6579202d2048617368204b6579204669727374":"6466ca07ac5eac29e1bd523e5ada7605b791fd8b"

generic multi step HMAC-RIPEMD160 Test vector RFC 2286 #7
depends_on:MBEDTLS_MD_CAN_RIPEMD160
md_hmac_multi:MBEDTLS_MD_RIPEMD160:20:"aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa":"54657374205573696e67204c6172676572205468616e20426c6f636b2d53697a65204b657920616e64204c6172676572205468616e204f6e6520426c6f636b2d53697a652044617461":"69ea60798d71616cce5fd0871e23754cd75d5a0a"

generic MD5 Hash file #1
depends_on:MBEDTLS_MD_CAN_MD5
mbedtls_md_file:MBEDTLS_MD_MD5:"../framework/data_files/hash_file_1":"52bcdc983c9ed64fc148a759b3c7a415"

generic MD5 Hash file #2
depends_on:MBEDTLS_MD_CAN_MD5
mbedtls_md_file:MBEDTLS_MD_MD5:"../framework/data_files/hash_file_2":"d17d466f15891df10542207ae78277f0"

generic MD5 Hash file #3
depends_on:MBEDTLS_MD_CAN_MD5
mbedtls_md_file:MBEDTLS_MD_MD5:"../framework/data_files/hash_file_3":"d945bcc6200ea95d061a2a818167d920"

generic MD5 Hash file #4
depends_on:MBEDTLS_MD_CAN_MD5
mbedtls_md_file:MBEDTLS_MD_MD5:"../framework/data_files/hash_file_4":"d41d8cd98f00b204e9800998ecf8427e"

generic RIPEMD160 Hash file #0 (from paper)
depends_on:MBEDTLS_MD_CAN_RIPEMD160
mbedtls_md_file:MBEDTLS_MD_RIPEMD160:"../framework/data_files/hash_file_5":"52783243c1697bdbe16d37f97f68f08325dc1528"

generic RIPEMD160 Hash file #1
depends_on:MBEDTLS_MD_CAN_RIPEMD160
mbedtls_md_file:MBEDTLS_MD_RIPEMD160:"../framework/data_files/hash_file_1":"82f1d072f0ec0c2b353703a7b575a04c113af1a6"

generic RIPEMD160 Hash file #2
depends_on:MBEDTLS_MD_CAN_RIPEMD160
mbedtls_md_file:MBEDTLS_MD_RIPEMD160:"../framework/data_files/hash_file_2":"996fbc8b79206ba7393ebcd246584069b1c08f0f"

generic RIPEMD160 Hash file #3
depends_on:MBEDTLS_MD_CAN_RIPEMD160
mbedtls_md_file:MBEDTLS_MD_RIPEMD160:"../framework/data_files/hash_file_3":"8653b46d65998fa8c8846efa17937e742533ae48"

generic RIPEMD160 Hash file #4
depends_on:MBEDTLS_MD_CAN_RIPEMD160
mbedtls_md_file:MBEDTLS_MD_RIPEMD160:"../framework/data_files/hash_file_4":"9c1185a5c5e9fc54612808977ee8f548b2258d31"

generic HMAC-SHA-1 Test Vector FIPS-198a #1
depends_on:MBEDTLS_MD_CAN_SHA1
mbedtls_md_hmac:MBEDTLS_MD_SHA1:20:"000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f":"53616d706c65202331":"4f4ca3d5d68ba7cc0a1208c9c61e9c5da0403c0a"

generic HMAC-SHA-1 Test Vector FIPS-198a #2
depends_on:MBEDTLS_MD_CAN_SHA1
mbedtls_md_hmac:MBEDTLS_MD_SHA1:20:"303132333435363738393a3b3c3d3e3f40414243":"53616d706c65202332":"0922d3405faa3d194f82a45830737d5cc6c75d24"

generic HMAC-SHA-1 Test Vector FIPS-198a #3
depends_on:MBEDTLS_MD_CAN_SHA1
mbedtls_md_hmac:MBEDTLS_MD_SHA1:20:"505152535455565758595a5b5c5d5e5f606162636465666768696a6b6c6d6e6f707172737475767778797a7b7c7d7e7f808182838485868788898a8b8c8d8e8f909192939495969798999a9b9c9d9e9fa0a1a2a3a4a5a6a7a8a9aaabacadaeafb0b1b2b3":"53616d706c65202333":"bcf41eab8bb2d802f3d05caf7cb092ecf8d1a3aa"

generic HMAC-SHA-1 Test Vector FIPS-198a #4
depends_on:MBEDTLS_MD_CAN_SHA1
mbedtls_md_hmac:MBEDTLS_MD_SHA1:12:"707172737475767778797a7b7c7d7e7f808182838485868788898a8b8c8d8e8f909192939495969798999a9b9c9d9e9fa0":"53616d706c65202334":"9ea886efe268dbecce420c75"

generic HMAC-SHA-1 Test Vector NIST CAVS #1
depends_on:MBEDTLS_MD_CAN_SHA1
mbedtls_md_hmac:MBEDTLS_MD_SHA1:10:"7b10f4124b15c82e":"27dcb5b1daf60cfd3e2f73d4d64ca9c684f8bf71fc682a46793b1790afa4feb100ca7aaff26f58f0e1d0ed42f1cdad1f474afa2e79d53a0c42892c4d7b327cbe46b295ed8da3b6ecab3d4851687a6f812b79df2f6b20f11f6706f5301790ca99625aad7391d84f78043d2a0a239b1477984c157bbc9276064e7a1a406b0612ca":"4ead12c2fe3d6ea43acb"

generic HMAC-SHA-1 Test Vector NIST CAVS #2
depends_on:MBEDTLS_MD_CAN_SHA1
mbedtls_md_hmac:MBEDTLS_MD_SHA1:10:"4fe9fb902172a21b":"4ceb3a7c13659c22fe51134f03dce4c239d181b63c6b0b59d367157fd05cab98384f92dfa482d2d5e78e72eef1b1838af4696026c54233d484ecbbe87f904df5546419f8567eafd232e6c2fcd3ee2b7682c63000524b078dbb2096f585007deae752562df1fe3b01278089e16f3be46e2d0f7cabac2d8e6cc02a2d0ca953425f":"564428a67be1924b5793"

generic HMAC-SHA-1 Test Vector NIST CAVS #3
depends_on:MBEDTLS_MD_CAN_SHA1
mbedtls_md_hmac:MBEDTLS_MD_SHA1:10:"d1f01455f78c4fb4":"00d40f67b57914bec456a3e3201ef1464be319a8d188c02e157af4b54f9b5a66d67f898a9bdbb19ff63a80aba6f246d013575721d52eb1b47a65def884011c49b257bcc2817fc853f106e8138ce386d7a5ac3103de0a3fa0ed6bb7af9ff66ebd1cc46fb86e4da0013d20a3c2dcd8fb828a4b70f7f104b41bf3f44682a66497ea":"56a665a7cdfe610f9fc5"

generic HMAC-SHA-1 Test Vector NIST CAVS #4
depends_on:MBEDTLS_MD_CAN_SHA1
mbedtls_md_hmac:MBEDTLS_MD_SHA1:10:"4e5ef77fdf033a5b":"e59326464e3201d195e29f2a3446ec1b1c9ff31154e2a4d0e40ed466f1bc855d29f76835624fa0127d29c9b1915939a046f385af7e5d47a23ba91f28bd22f811ea258dbbf3332bcd3543b8285d5df41bd064ffd64a341c22c4edb44f9c8d9e6df0c59dbf4a052a6c83da7478e179a6f3839c6870ff8ca8b9497f9ac1d725fdda":"981c0a7a8423b63a8fa6"

generic HMAC-SHA-1 Test Vector NIST CAVS #5
depends_on:MBEDTLS_MD_CAN_SHA1
mbedtls_md_hmac:MBEDTLS_MD_SHA1:10:"bcd9ff8aa60be2be":"51be4d0eb37bab714f92e19e9d70390655b363e8cd346a748245e731f437759cb8206412c8dab2ef1d4f36f880f41ff69d949da4594fdecb65e23cac1329b59e69e29bf875b38c31df6fa546c595f35cc2192aa750679a8a51a65e00e839d73a8d8c598a610d237fbe78955213589d80efcb73b95b8586f96d17b6f51a71c3b8":"84633f9f5040c8971478"

generic HMAC-SHA-1 Test Vector NIST CAVS #6
depends_on:MBEDTLS_MD_CAN_SHA1
mbedtls_md_hmac:MBEDTLS_MD_SHA1:10:"4a661bce6ed86d21":"5ff6c744f1aab1bc29697d71f67541b8b3cec3c7079183b10a83fb98a9ee251d4bac3e1cb581ca972aaed8efd7c2875a6fb4c991132f67c9742d45e53bc7e8eaa94b35b37a907be61086b426cd11088ac118934e85d968c9667fd69fc6f6ea38c0fe34710b7ece91211b9b7ea00acd31f022aa6726368f9928a1352f122233f1":"739df59353ac6694e55e"

generic HMAC-SHA-1 Test Vector NIST CAVS #7
depends_on:MBEDTLS_MD_CAN_SHA1
mbedtls_md_hmac:MBEDTLS_MD_SHA1:10:"1287e1565a57b547":"390ffdccc6171c11568d85b8f913e019bf4cd982ca9cd21ea730d41bdf3fcc0bc88ff48ba13a8f23deb2d96ec1033e7b2a58ca72b0c1e17bf03330db25d1e360fa6918009c4294bd1215b5ccd159a8f58bc3dc3d490eb7c3b9f887e8c98dbbb274a75373dcb695a59abd0219529d88518a96f92abc0bbcbda985c388f1fbbcc9":"d78ddf08077c7d9e2ba6"

generic HMAC-SHA-224 Test Vector NIST CAVS #1
depends_on:MBEDTLS_MD_CAN_SHA224
mbedtls_md_hmac:MBEDTLS_MD_SHA224:14:"e055eb756697ee573fd3214811a9f7fa":"3875847012ee42fe54a0027bdf38cca7021b83a2ed0503af69ef6c37c637bc1114fba40096c5947d736e19b7af3c68d95a4e3b8b073adbbb80f47e9db8f2d4f0018ddd847fabfdf9dd9b52c93e40458977725f6b7ba15f0816bb895cdf50401268f5d702b7e6a5f9faef57b8768c8a3fc14f9a4b3182b41d940e337d219b29ff":"40a453133361cc48da11baf616ee"

generic HMAC-SHA-224 Test Vector NIST CAVS #2
depends_on:MBEDTLS_MD_CAN_SHA224
mbedtls_md_hmac:MBEDTLS_MD_SHA224:14:"88e5258b55b1623385eb9632fa7c57d6":"ada76bb604be14326551701cf30e48a65eee80b44f0b9d4a07b1844543b7844a621097fdc99de57387458ae9354899b620d0617eabcaefa9eef3d413a33628054335ce656c26fa2986e0f111a6351096b283101ec7868871d770b370973c7405983f9756b3005a3eab492cfd0e7eb42e5c2e15fa6be8718c0a50acc4e5717230":"81c783af538015cef3c60095df53"

generic HMAC-SHA-224 Test Vector NIST CAVS #3
depends_on:MBEDTLS_MD_CAN_SHA224
mbedtls_md_hmac:MBEDTLS_MD_SHA224:14:"85d402d822114d31abf75526e2538705":"8020d8d98cc2e2298b32879c51c751e1dd5558fe2eabb8f158604297d6d072ce2261a1d6830b7cfe2617b57c7126f99c9476211d6161acd75d266da217ec8174b80484c9dc6f0448a0a036a3fc82e8bf54bdb71549368258d5d41f57978a4c266b92e8783ef66350215573d99be4089144b383ad8f3222bae8f3bf80ffb1bb2b":"2aa0340ac9deafe3be38129daca0"

generic HMAC-SHA-224 Test Vector NIST CAVS #4
depends_on:MBEDTLS_MD_CAN_SHA224
mbedtls_md_hmac:MBEDTLS_MD_SHA224:14:"545c6eecc5ee46fa17c59f91a94f81ae":"8fb7f3565593170152ddb2021874784e951977cfdd22f8b72a72a61320a8f2a35697b5e913f717805559b1af1861ee3ed42fb788481e4fd276b17bdbefcae7b4501dc5d20de5b7626dd5efdcd65294db4bdf682c33d9a9255c6435383fa5f1c886326a3acbc6bd50a33ab5b2dbb034ce0112d4e226bbcd57e3731a519aa1d784":"3eb566eac54c4a3a9ef092469f24"

generic HMAC-SHA-224 Test Vector NIST CAVS #5
depends_on:MBEDTLS_MD_CAN_SHA224
mbedtls_md_hmac:MBEDTLS_MD_SHA224:14:"4466ab4dc438841a9750c7f173dff02e":"2534c11c78c99cffaec8f722f04adc7045c7324d58ce98e37cfa94b6ed21ed7f58ce55379ef24b72d6d640ee9154f96c614734be9c408e225d7ba4cecc1179cc9f6e1808e1067aa8f244a99bd0c3267594c1887a40d167f8b7cf78db0d19f97b01fc50b8c86def490dfa7a5135002c33e71d77a8cce8ea0f93e0580439a33733":"59f44a9bbed4875b892d22d6b5ab"

generic HMAC-SHA-224 Test Vector NIST CAVS #6
depends_on:MBEDTLS_MD_CAN_SHA224
mbedtls_md_hmac:MBEDTLS_MD_SHA224:28:"0e3dd9bb5e4cf0f09a4c11600af56d8d":"f4589fa76c328ea25cf8bae582026ba40a59d45a546ff31cf80eb826088f69bb954c452c74586836416dee90a5255bc5d56d3b405b3705a5197045688b32fa984c3a3dfbdc9c2460a0b5e6312a624048bb6f170306535e9b371a3ab134a2642a230ad03d2c688cca80baeaee9a20e1d4c548b1cede29c6a45bf4df2c8c476f1a":"12175b93e3da4c58217145e4dc0a1cf142fab9319bb501e037b350ba"

generic HMAC-SHA-224 Test Vector NIST CAVS #7
depends_on:MBEDTLS_MD_CAN_SHA224
mbedtls_md_hmac:MBEDTLS_MD_SHA224:28:"cda5187b0c5dcb0f8e5a8beed2306584":"9011ae29b44c49b347487ce972965f16ade3c15be0856ce9c853a9739dba07e4f20d594ddc1dfe21560a65a4e458cfa17745575b915a30c7a9412ff8d1d689db9680dd2428c27588bb0dc92d2cd9445fe8f44b840a197c52c3c4333fff45533945134398df6436513cfab06c924046b8c795a5bd92e8d5f2de85bf306f2eed67":"4aaba92b40e2a600feab176eb9b292d814864195c03342aad6f67f08"

generic HMAC-SHA-256 Test Vector NIST CAVS #1
depends_on:MBEDTLS_MD_CAN_SHA256
mbedtls_md_hmac:MBEDTLS_MD_SHA256:16:"cdffd34e6b16fdc0":"d83e78b99ab61709608972b36e76a575603db742269cc5dd4e7d5ca7816e26b65151c92632550cb4c5253c885d5fce53bc47459a1dbd5652786c4aac0145a532f12c05138af04cbb558101a7af5df478834c2146594dd73690d01a4fe72545894335f427ac70204798068cb86c5a600b40b414ede23590b41e1192373df84fe3":"c6f0dde266cb4a26d41e8259d33499cc"

generic HMAC-SHA-256 Test Vector NIST CAVS #2
depends_on:MBEDTLS_MD_CAN_SHA256
mbedtls_md_hmac:MBEDTLS_MD_SHA256:16:"6d97bb5892245be2":"13c2b391d59c0252ca5d2302beaaf88c4bcd779bb505ad9a122003dfae4cc123ad2bd036f225c4f040021a6b9fb8bd6f0281cf2e2631a732bdc71693cc42ef6d52b6c6912a9ef77b3274eb85ad7f965ae6ed44ac1721962a884ec7acfb4534b1488b1c0c45afa4dae8da1eb7b0a88a3240365d7e4e7d826abbde9f9203fd99d7":"31588e241b015319a5ab8c4527296498"

generic HMAC-SHA-256 Test Vector NIST CAVS #3
depends_on:MBEDTLS_MD_CAN_SHA256
mbedtls_md_hmac:MBEDTLS_MD_SHA256:16:"3c7fc8a70b49007a":"60024e428a39c8b8bb2e9591bad9dc2115dfbfd716b6eb7af30a6eb34560caccbbfa47b710fa8d523aca71e9e5ba10fc1feb1a43556d71f07ea4f33496f093044e8caf1d02b79e46eb1288d5964a7a7494f6b92574c35784eece054c6151281d80822f7d47b8231c35d07f5cb5cf4310ddc844845a01c6bfab514c048eccaf9f":"1c98c94a32bec9f253c21070f82f8438"

generic HMAC-SHA-256 Test Vector NIST CAVS #4
depends_on:MBEDTLS_MD_CAN_SHA256
mbedtls_md_hmac:MBEDTLS_MD_SHA256:24:"369f33f85b927a07":"ae8e2a94ca386d448cbacdb0e9040ae3cb297c296363052cc157455da29a0c95897315fc11e3f12b81e2418da1ec280bccbc00e847584ce9d14deeba7b3c9b8dba958b04bba37551f6c9ba9c060be1a4b8cf43aa62e5078b76c6512c5619b71a6a7cf5727180e1ff14f5a1a3c1691bf8b6ebad365c151e58d749d57adb3a4986":"60b90383286533d309de46593e6ce39fc51fb00a8d88278c"

generic HMAC-SHA-256 Test Vector NIST CAVS #5
depends_on:MBEDTLS_MD_CAN_SHA256
mbedtls_md_hmac:MBEDTLS_MD_SHA256:24:"e5179687582b4dc4":"ce103bdacdf32f614f6727bcb31ca1c2824a850d00f5585b016fb234fe1ef2cd687f302d3c6b738ed89a24060d65c36675d0d96307c72ef3e8a83bfa8402e226de9d5d1724ba75c4879bf41a4a465ce61887d9f49a34757849b48bae81c27ebed76faae2ad669bca04747d409148d40812776e0ae2c395b3cb9c89981ce72d5c":"509581f6816df4b8cc9f2cf42b7cc6e6a5a1e375a16f2412"

generic HMAC-SHA-256 Test Vector NIST CAVS #6
depends_on:MBEDTLS_MD_CAN_SHA256
mbedtls_md_hmac:MBEDTLS_MD_SHA256:24:"63cec6246aeb1b61":"c178db908a405fa88aa255b8cad22b4057016585f139ee930388b083d86062fa0b3ea1f23f8a43bd11bee8464bcbd19b5ab9f6a8038d5245516f8274d20c8ee3033a07b908da528fa00343bb595deed500cab9745c4cb6391c23300f0d3584b090b3326c4cfa342620b78f9f5b4f27f7307ed770643ec1764aeae3dcf1a3ec69":"64f3dd861b7c7d29fce9ae0ce9ed954b5d7141806ee9eec7"

generic HMAC-SHA-384 Test Vector NIST CAVS #1
depends_on:MBEDTLS_MD_CAN_SHA384
mbedtls_md_hmac:MBEDTLS_MD_SHA384:32:"91a7401817386948ca952f9a20ee55dc":"2fea5b91035d6d501f3a834fa178bff4e64b99a8450432dafd32e4466b0e1e7781166f8a73f7e036b3b0870920f559f47bd1400a1a906e85e0dcf00a6c26862e9148b23806680f285f1fe4f93cdaf924c181a965465739c14f2268c8be8b471847c74b222577a1310bcdc1a85ef1468aa1a3fd4031213c97324b7509c9050a3d":"6d7be9490058cf413cc09fd043c224c2ec4fa7859b13783000a9a593c9f75838"

generic HMAC-SHA-384 Test Vector NIST CAVS #2
depends_on:MBEDTLS_MD_CAN_SHA384
mbedtls_md_hmac:MBEDTLS_MD_SHA384:32:"d6cac19657061aa90a6da11cd2e9ea47":"9f482e4655173135dfaa22a11bbbe6af263db48716406c5aec162ba3c4b41cad4f5a91558377521191c7343118beee65982929802913d67b6de5c4bdc3d27299bd722219d5ad2efa5bdb9ff7b229fc4bbc3f60719320cf2e7a51cad1133d21bad2d80919b1836ef825308b7c51c6b7677ac782e2bc30007afba065681cbdd215":"f3d5f3c008175321aa7b2ea379eaa4f8b9dcc60f895ec8940b8162f80a7dfe9f"

generic HMAC-SHA-384 Test Vector NIST CAVS #3
depends_on:MBEDTLS_MD_CAN_SHA384
mbedtls_md_hmac:MBEDTLS_MD_SHA384:32:"e06366ad149b8442cd4c1abdddd0afde":"2d140a194c02a5598f69174834679b8371234a0d505491f1bd03e128dd91a8bca2fb812e9d5da71613b5b00952ea78bf450d5b7547dea79135925085c7d3e6f52009c51ca3d88c6c09e9d074b0ee110736e0ec9b478b93efb34d7bf1c41b54decec43eab077a3aa4998ede53f67b4ea36c266745f9643d5360bdc8337c70dabf":"c19c67eda6fe29f3667bee1c897c333ce7683094ae77e84b4c16378d290895a1"

generic HMAC-SHA-384 Test Vector NIST CAVS #4
depends_on:MBEDTLS_MD_CAN_SHA384
mbedtls_md_hmac:MBEDTLS_MD_SHA384:48:"01ac59f42f8bb91d1bd10fe6990d7a87":"3caf18c476edd5615f343ac7b7d3a9da9efade755672d5ba4b8ae8a7505539ea2c124ff755ec0457fbe49e43480b3c71e7f4742ec3693aad115d039f90222b030fdc9440313691716d5302005808c07627483b916fdf61983063c2eb1268f2deeef42fc790334456bc6bad256e31fc9066de7cc7e43d1321b1866db45e905622":"1985fa2163a5943fc5d92f1fe8831215e7e91f0bff5332bc713a072bdb3a8f9e5c5157463a3bfeb36231416e65973e64"

generic HMAC-SHA-384 Test Vector NIST CAVS #5 [#1]
depends_on:MBEDTLS_MD_CAN_SHA384
mbedtls_md_hmac:MBEDTLS_MD_SHA384:48:"fd74b9d9e102a3a80df1baf0cb35bace":"1a068917584813d1689ccbd0370c2114d537cdc8cc52bf6db16d5535f8f7d1ad0c850a9fa0cf62373ffbf7642b1f1e8164010d350721d798d9f99e9724830399c2fce26377e83d38845675457865c03d4a07d741a505ef028343eb29fd46d0f761f3792886998c1e5c32ac3bc7e6f08faed194b34f06eff4d5d4a5b42c481e0e":"a981eaf5de3d78b20ebd4414a4edd0657e3667cd808a0dbc430cf7252f73a5b24efa136039207bd59806897457d74e0c"

generic HMAC-SHA-384 Test Vector NIST CAVS #5 [#2]
depends_on:MBEDTLS_MD_CAN_SHA384
mbedtls_md_hmac:MBEDTLS_MD_SHA384:48:"9fe794f0e26b669fa5f6883149377c6c":"6010c9745e8f1d44cfdc99e7e0fd79bc4271944c2d1d84dba589073dfc4ca5eb98c59356f60cd87bef28aeb83a832bde339b2087daf942aa1f67876c5d5ed33924bed4143bc12a2be532ccaf64daa7e2bc3c8872b9823b0533b6f5159135effe8c61545536975d7c3a61ba7365ec35f165bc92b4d19eb9156ade17dfa1bb4161":"915ae61f8754698c2b6ef9629e93441f8541bd4258a5e05372d19136cfaefc0473b48d96119291b38eb1a3cb1982a986"

generic HMAC-SHA-512 Test Vector NIST CAVS #1
depends_on:MBEDTLS_MD_CAN_SHA512
mbedtls_md_hmac:MBEDTLS_MD_SHA512:32:"c95a17c09940a691ed2d621571b0eb844ede55a9":"99cd28262e81f34878cdcebf4128e05e2098a7009278a66f4c785784d0e5678f3f2b22f86e982d273b6273a222ec61750b4556d766f1550a7aedfe83faedbc4bdae83fa560d62df17eb914d05fdaa48940551bac81d700f5fca7147295e386e8120d66742ec65c6ee8d89a92217a0f6266d0ddc60bb20ef679ae8299c8502c2f":"6bc1379d156559ddee2ed420ea5d5c5ff3e454a1059b7ba72c350e77b6e9333c"

generic HMAC-SHA-512 Test Vector NIST CAVS #2
depends_on:MBEDTLS_MD_CAN_SHA512
mbedtls_md_hmac:MBEDTLS_MD_SHA512:32:"3b10b8fa718840d1dea8e9fc317476bcf55875fd":"f04f5b7073d7d0274e8354433b390306c5607632f5f589c12edb62d55673aff2366d2e6b24de731adf92e654baa30b1cfd4a069788f65ec1b99b015d904d8832110dbd74eae35a81562d14ce4136d820ad0a55ff5489ba678fbbc1c27663ec1349d70e740f0e0ec27cfbe8971819f4789e486b50a2d7271d77e2aaea50de62fd":"fc3c38c7a17e3ce06db033f1c172866f01a00045db55f2e234f71c82264f2ba2"

generic HMAC-SHA-512 Test Vector NIST CAVS #3
depends_on:MBEDTLS_MD_CAN_SHA512
mbedtls_md_hmac:MBEDTLS_MD_SHA512:32:"4803d311394600dc1e0d8fc8cedeb8bde3fe7c42":"a10c125dd702a97153ad923ba5e9889cfac1ba169de370debe51f233735aa6effcc9785c4b5c7e48c477dc5c411ae6a959118584e26adc94b42c2b29b046f3cf01c65b24a24bd2e620bdf650a23bb4a72655b1100d7ce9a4dab697c6379754b4396c825de4b9eb73f2e6a6c0d0353bbdeaf706612800e137b858fdb30f3311c6":"7cd8236c55102e6385f52279506df6fcc388ab75092da21395ce14a82b202ffa"

generic HMAC-SHA-512 Test Vector NIST CAVS #4
depends_on:MBEDTLS_MD_CAN_SHA512
mbedtls_md_hmac:MBEDTLS_MD_SHA512:48:"aeb2f3b977fa6c8e71e07c5a5c74ff58166de092":"22457355dc76095abd46846b41cfe49a06ce42ac8857b4702fc771508dfb3626e0bfe851df897a07b36811ec433766e4b4166c26301b3493e7440d4554b0ef6ac20f1a530e58fac8aeba4e9ff2d4898d8a28783b49cd269c2965fd7f8e4f2d60cf1e5284f2495145b72382aad90e153a90ecae125ad75336fb128825c23fb8b0":"fa39bd8fcc3bfa218f9dea5d3b2ce10a7619e31678a56d8a9d927b1fe703b125af445debe9a89a07db6194d27b44d85a"

generic HMAC-SHA-512 Test Vector NIST CAVS #5
depends_on:MBEDTLS_MD_CAN_SHA512
mbedtls_md_hmac:MBEDTLS_MD_SHA512:48:"4285d3d7744da52775bb44ca436a3154f7980309":"208f0b6f2de2e5aa5df11927ddc6df485edc1193181c484d0f0a434a95418803101d4de9fdb798f93516a6916fa38a8207de1666fe50fe3441c03b112eaaae6954ed063f7ac4e3c1e3f73b20d153fe9e4857f5e91430f0a70ee820529adac2467469fd18adf10e2af0fea27c0abc83c5a9af77c364a466cffce8bab4e2b70bc1":"fe7603f205b2774fe0f14ecfa3e338e90608a806d11ca459dff5ce36b1b264ecd3af5f0492a7521d8da3102ba20927a5"

generic HMAC-SHA-512 Test Vector NIST CAVS #6
depends_on:MBEDTLS_MD_CAN_SHA512
mbedtls_md_hmac:MBEDTLS_MD_SHA512:48:"8ab783d5acf32efa0d9c0a21abce955e96630d89":"17371e013dce839963d54418e97be4bd9fa3cb2a368a5220f5aa1b8aaddfa3bdefc91afe7c717244fd2fb640f5cb9d9bf3e25f7f0c8bc758883b89dcdce6d749d9672fed222277ece3e84b3ec01b96f70c125fcb3cbee6d19b8ef0873f915f173bdb05d81629ba187cc8ac1934b2f75952fb7616ae6bd812946df694bd2763af":"9ac7ca8d1aefc166b046e4cf7602ebe181a0e5055474bff5b342106731da0d7e48e4d87bc0a6f05871574289a1b099f8"

HMAC-SHA3-224: NIST example #1: keylen<blocklen
depends_on:MBEDTLS_MD_CAN_SHA3_224
mbedtls_md_hmac:MBEDTLS_MD_SHA3_224:28:"000102030405060708090a0b0c0d0e0f101112131415161718191a1b":"53616d706c65206d65737361676520666f72206b65796c656e3c626c6f636b6c656e":"332cfd59347fdb8e576e77260be4aba2d6dc53117b3bfb52c6d18c04"

HMAC-SHA3-224: NIST example #2: keylen=blocklen
depends_on:MBEDTLS_MD_CAN_SHA3_224
mbedtls_md_hmac:MBEDTLS_MD_SHA3_224:28:"000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f404142434445464748494a4b4c4d4e4f505152535455565758595a5b5c5d5e5f606162636465666768696a6b6c6d6e6f707172737475767778797a7b7c7d7e7f808182838485868788898a8b8c8d8e8f":"53616d706c65206d65737361676520666f72206b65796c656e3d626c6f636b6c656e":"d8b733bcf66c644a12323d564e24dcf3fc75f231f3b67968359100c7"

HMAC-SHA3-224: NIST example #3: keylen>blocklen
depends_on:MBEDTLS_MD_CAN_SHA3_224
mbedtls_md_hmac:MBEDTLS_MD_SHA3_224:28:"000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f404142434445464748494a4b4c4d4e4f505152535455565758595a5b5c5d5e5f606162636465666768696a6b6c6d6e6f707172737475767778797a7b7c7d7e7f808182838485868788898a8b8c8d8e8f909192939495969798999a9b9c9d9e9fa0a1a2a3a4a5a6a7a8a9aaab":"53616d706c65206d65737361676520666f72206b65796c656e3e626c6f636b6c656e":"078695eecc227c636ad31d063a15dd05a7e819a66ec6d8de1e193e59"

HMAC-SHA3-224: NIST example #4: keylen<blocklen, with truncated tag
depends_on:MBEDTLS_MD_CAN_SHA3_224
mbedtls_md_hmac:MBEDTLS_MD_SHA3_224:14:"000102030405060708090a0b0c0d0e0f101112131415161718191a1b":"53616d706c65206d65737361676520666f72206b65796c656e3c626c6f636b6c656e2c2077697468207472756e636174656420746167":"8569c54cbb00a9b78ff1b391b0e5"

HMAC-SHA3-256: NIST example #1: keylen<blocklen
depends_on:MBEDTLS_MD_CAN_SHA3_256
mbedtls_md_hmac:MBEDTLS_MD_SHA3_256:32:"000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f":"53616d706c65206d65737361676520666f72206b65796c656e3c626c6f636b6c656e":"4fe8e202c4f058e8dddc23d8c34e467343e23555e24fc2f025d598f558f67205"

HMAC-SHA3-256: NIST example #2: keylen=blocklen
depends_on:MBEDTLS_MD_CAN_SHA3_256
mbedtls_md_hmac:MBEDTLS_MD_SHA3_256:32:"000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f404142434445464748494a4b4c4d4e4f505152535455565758595a5b5c5d5e5f606162636465666768696a6b6c6d6e6f707172737475767778797a7b7c7d7e7f8081828384858687":"53616d706c65206d65737361676520666f72206b65796c656e3d626c6f636b6c656e":"68b94e2e538a9be4103bebb5aa016d47961d4d1aa906061313b557f8af2c3faa"

HMAC-SHA3-256: NIST example #3: keylen>blocklen
depends_on:MBEDTLS_MD_CAN_SHA3_256
mbedtls_md_hmac:MBEDTLS_MD_SHA3_256:32:"000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f404142434445464748494a4b4c4d4e4f505152535455565758595a5b5c5d5e5f606162636465666768696a6b6c6d6e6f707172737475767778797a7b7c7d7e7f808182838485868788898a8b8c8d8e8f909192939495969798999a9b9c9d9e9fa0a1a2a3a4a5a6a7":"53616d706c65206d65737361676520666f72206b65796c656e3e626c6f636b6c656e":"9bcf2c238e235c3ce88404e813bd2f3a97185ac6f238c63d6229a00b07974258"

HMAC-SHA3-256: NIST example #4: keylen<blocklen, with truncated tag
depends_on:MBEDTLS_MD_CAN_SHA3_256
mbedtls_md_hmac:MBEDTLS_MD_SHA3_256:16:"000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f":"53616d706c65206d65737361676520666f72206b65796c656e3c626c6f636b6c656e2c2077697468207472756e636174656420746167":"c8dc7148d8c1423aa549105dafdf9cad"

HMAC-SHA3-384: NIST example #1: keylen<blocklen
depends_on:MBEDTLS_MD_CAN_SHA3_384
mbedtls_md_hmac:MBEDTLS_MD_SHA3_384:48:"000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f":"53616d706c65206d65737361676520666f72206b65796c656e3c626c6f636b6c656e":"d588a3c51f3f2d906e8298c1199aa8ff6296218127f6b38a90b6afe2c5617725bc99987f79b22a557b6520db710b7f42"

HMAC-SHA3-384: NIST example #2: keylen=blocklen
depends_on:MBEDTLS_MD_CAN_SHA3_384
mbedtls_md_hmac:MBEDTLS_MD_SHA3_384:48:"000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f404142434445464748494a4b4c4d4e4f505152535455565758595a5b5c5d5e5f6061626364656667":"53616d706c65206d65737361676520666f72206b65796c656e3d626c6f636b6c656e":"a27d24b592e8c8cbf6d4ce6fc5bf62d8fc98bf2d486640d9eb8099e24047837f5f3bffbe92dcce90b4ed5b1e7e44fa90"

HMAC-SHA3-384: NIST example #3: keylen>blocklen
depends_on:MBEDTLS_MD_CAN_SHA3_384
mbedtls_md_hmac:MBEDTLS_MD_SHA3_384:48:"000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f404142434445464748494a4b4c4d4e4f505152535455565758595a5b5c5d5e5f606162636465666768696a6b6c6d6e6f707172737475767778797a7b7c7d7e7f808182838485868788898a8b8c8d8e8f9091929394959697":"53616d706c65206d65737361676520666f72206b65796c656e3e626c6f636b6c656e":"e5ae4c739f455279368ebf36d4f5354c95aa184c899d3870e460ebc288ef1f9470053f73f7c6da2a71bcaec38ce7d6ac"

HMAC-SHA3-384: NIST example #4: keylen<blocklen, with truncated tag
depends_on:MBEDTLS_MD_CAN_SHA3_384
mbedtls_md_hmac:MBEDTLS_MD_SHA3_384:24:"000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f":"53616d706c65206d65737361676520666f72206b65796c656e3c626c6f636b6c656e2c2077697468207472756e636174656420746167":"25f4bf53606e91af79d24a4bb1fd6aecd44414a30c8ebb0a"

HMAC-SHA3-512: NIST example #1: keylen<blocklen
depends_on:MBEDTLS_MD_CAN_SHA3_512
mbedtls_md_hmac:MBEDTLS_MD_SHA3_512:64:"000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f":"53616d706c65206d65737361676520666f72206b65796c656e3c626c6f636b6c656e":"4efd629d6c71bf86162658f29943b1c308ce27cdfa6db0d9c3ce81763f9cbce5f7ebe9868031db1a8f8eb7b6b95e5c5e3f657a8996c86a2f6527e307f0213196"

HMAC-SHA3-512: NIST example #2: keylen=blocklen
depends_on:MBEDTLS_MD_CAN_SHA3_512
mbedtls_md_hmac:MBEDTLS_MD_SHA3_512:64:"000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f4041424344454647":"53616d706c65206d65737361676520666f72206b65796c656e3d626c6f636b6c656e":"544e257ea2a3e5ea19a590e6a24b724ce6327757723fe2751b75bf007d80f6b360744bf1b7a88ea585f9765b47911976d3191cf83c039f5ffab0d29cc9d9b6da"

HMAC-SHA3-512: NIST example #3: keylen>blocklen
depends_on:MBEDTLS_MD_CAN_SHA3_512
mbedtls_md_hmac:MBEDTLS_MD_SHA3_512:64:"000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f404142434445464748494a4b4c4d4e4f505152535455565758595a5b5c5d5e5f606162636465666768696a6b6c6d6e6f707172737475767778797a7b7c7d7e7f8081828384858687":"53616d706c65206d65737361676520666f72206b65796c656e3e626c6f636b6c656e":"5f464f5e5b7848e3885e49b2c385f0694985d0e38966242dc4a5fe3fea4b37d46b65ceced5dcf59438dd840bab22269f0ba7febdb9fcf74602a35666b2a32915"

HMAC-SHA3-512: NIST example #4: keylen<blocklen, with truncated tag
depends_on:MBEDTLS_MD_CAN_SHA3_512
mbedtls_md_hmac:MBEDTLS_MD_SHA3_512:32:"000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f":"53616d706c65206d65737361676520666f72206b65796c656e3c626c6f636b6c656e2c2077697468207472756e636174656420746167":"7bb06d859257b25ce73ca700df34c5cbef5c898bac91029e0b27975d4e526a08"

generic multi step HMAC-SHA-1 Test Vector FIPS-198a #1
depends_on:MBEDTLS_MD_CAN_SHA1
md_hmac_multi:MBEDTLS_MD_SHA1:20:"000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f":"53616d706c65202331":"4f4ca3d5d68ba7cc0a1208c9c61e9c5da0403c0a"

generic multi step HMAC-SHA-1 Test Vector FIPS-198a #2
depends_on:MBEDTLS_MD_CAN_SHA1
md_hmac_multi:MBEDTLS_MD_SHA1:20:"303132333435363738393a3b3c3d3e3f40414243":"53616d706c65202332":"0922d3405faa3d194f82a45830737d5cc6c75d24"

generic multi step HMAC-SHA-1 Test Vector FIPS-198a #3
depends_on:MBEDTLS_MD_CAN_SHA1
md_hmac_multi:MBEDTLS_MD_SHA1:20:"505152535455565758595a5b5c5d5e5f606162636465666768696a6b6c6d6e6f707172737475767778797a7b7c7d7e7f808182838485868788898a8b8c8d8e8f909192939495969798999a9b9c9d9e9fa0a1a2a3a4a5a6a7a8a9aaabacadaeafb0b1b2b3":"53616d706c65202333":"bcf41eab8bb2d802f3d05caf7cb092ecf8d1a3aa"

generic multi step HMAC-SHA-1 Test Vector FIPS-198a #4
depends_on:MBEDTLS_MD_CAN_SHA1
md_hmac_multi:MBEDTLS_MD_SHA1:12:"707172737475767778797a7b7c7d7e7f808182838485868788898a8b8c8d8e8f909192939495969798999a9b9c9d9e9fa0":"53616d706c65202334":"9ea886efe268dbecce420c75"

generic multi step HMAC-SHA-1 Test Vector NIST CAVS #1
depends_on:MBEDTLS_MD_CAN_SHA1
md_hmac_multi:MBEDTLS_MD_SHA1:10:"7b10f4124b15c82e":"27dcb5b1daf60cfd3e2f73d4d64ca9c684f8bf71fc682a46793b1790afa4feb100ca7aaff26f58f0e1d0ed42f1cdad1f474afa2e79d53a0c42892c4d7b327cbe46b295ed8da3b6ecab3d4851687a6f812b79df2f6b20f11f6706f5301790ca99625aad7391d84f78043d2a0a239b1477984c157bbc9276064e7a1a406b0612ca":"4ead12c2fe3d6ea43acb"

generic multi step HMAC-SHA-1 Test Vector NIST CAVS #2
depends_on:MBEDTLS_MD_CAN_SHA1
md_hmac_multi:MBEDTLS_MD_SHA1:10:"4fe9fb902172a21b":"4ceb3a7c13659c22fe51134f03dce4c239d181b63c6b0b59d367157fd05cab98384f92dfa482d2d5e78e72eef1b1838af4696026c54233d484ecbbe87f904df5546419f8567eafd232e6c2fcd3ee2b7682c63000524b078dbb2096f585007deae752562df1fe3b01278089e16f3be46e2d0f7cabac2d8e6cc02a2d0ca953425f":"564428a67be1924b5793"

generic multi step HMAC-SHA-1 Test Vector NIST CAVS #3
depends_on:MBEDTLS_MD_CAN_SHA1
md_hmac_multi:MBEDTLS_MD_SHA1:10:"d1f01455f78c4fb4":"00d40f67b57914bec456a3e3201ef1464be319a8d188c02e157af4b54f9b5a66d67f898a9bdbb19ff63a80aba6f246d013575721d52eb1b47a65def884011c49b257bcc2817fc853f106e8138ce386d7a5ac3103de0a3fa0ed6bb7af9ff66ebd1cc46fb86e4da0013d20a3c2dcd8fb828a4b70f7f104b41bf3f44682a66497ea":"56a665a7cdfe610f9fc5"

generic multi step HMAC-SHA-1 Test Vector NIST CAVS #4
depends_on:MBEDTLS_MD_CAN_SHA1
md_hmac_multi:MBEDTLS_MD_SHA1:10:"4e5ef77fdf033a5b":"e59326464e3201d195e29f2a3446ec1b1c9ff31154e2a4d0e40ed466f1bc855d29f76835624fa0127d29c9b1915939a046f385af7e5d47a23ba91f28bd22f811ea258dbbf3332bcd3543b8285d5df41bd064ffd64a341c22c4edb44f9c8d9e6df0c59dbf4a052a6c83da7478e179a6f3839c6870ff8ca8b9497f9ac1d725fdda":"981c0a7a8423b63a8fa6"

generic multi step HMAC-SHA-1 Test Vector NIST CAVS #5
depends_on:MBEDTLS_MD_CAN_SHA1
md_hmac_multi:MBEDTLS_MD_SHA1:10:"bcd9ff8aa60be2be":"51be4d0eb37bab714f92e19e9d70390655b363e8cd346a748245e731f437759cb8206412c8dab2ef1d4f36f880f41ff69d949da4594fdecb65e23cac1329b59e69e29bf875b38c31df6fa546c595f35cc2192aa750679a8a51a65e00e839d73a8d8c598a610d237fbe78955213589d80efcb73b95b8586f96d17b6f51a71c3b8":"84633f9f5040c8971478"

generic multi step HMAC-SHA-1 Test Vector NIST CAVS #6
depends_on:MBEDTLS_MD_CAN_SHA1
md_hmac_multi:MBEDTLS_MD_SHA1:10:"4a661bce6ed86d21":"5ff6c744f1aab1bc29697d71f67541b8b3cec3c7079183b10a83fb98a9ee251d4bac3e1cb581ca972aaed8efd7c2875a6fb4c991132f67c9742d45e53bc7e8eaa94b35b37a907be61086b426cd11088ac118934e85d968c9667fd69fc6f6ea38c0fe34710b7ece91211b9b7ea00acd31f022aa6726368f9928a1352f122233f1":"739df59353ac6694e55e"

generic multi step HMAC-SHA-1 Test Vector NIST CAVS #7
depends_on:MBEDTLS_MD_CAN_SHA1
md_hmac_multi:MBEDTLS_MD_SHA1:10:"1287e1565a57b547":"390ffdccc6171c11568d85b8f913e019bf4cd982ca9cd21ea730d41bdf3fcc0bc88ff48ba13a8f23deb2d96ec1033e7b2a58ca72b0c1e17bf03330db25d1e360fa6918009c4294bd1215b5ccd159a8f58bc3dc3d490eb7c3b9f887e8c98dbbb274a75373dcb695a59abd0219529d88518a96f92abc0bbcbda985c388f1fbbcc9":"d78ddf08077c7d9e2ba6"

generic multi step HMAC-SHA-224 Test Vector NIST CAVS #1
depends_on:MBEDTLS_MD_CAN_SHA224
md_hmac_multi:MBEDTLS_MD_SHA224:14:"e055eb756697ee573fd3214811a9f7fa":"3875847012ee42fe54a0027bdf38cca7021b83a2ed0503af69ef6c37c637bc1114fba40096c5947d736e19b7af3c68d95a4e3b8b073adbbb80f47e9db8f2d4f0018ddd847fabfdf9dd9b52c93e40458977725f6b7ba15f0816bb895cdf50401268f5d702b7e6a5f9faef57b8768c8a3fc14f9a4b3182b41d940e337d219b29ff":"40a453133361cc48da11baf616ee"

generic multi step HMAC-SHA-224 Test Vector NIST CAVS #2
depends_on:MBEDTLS_MD_CAN_SHA224
md_hmac_multi:MBEDTLS_MD_SHA224:14:"88e5258b55b1623385eb9632fa7c57d6":"ada76bb604be14326551701cf30e48a65eee80b44f0b9d4a07b1844543b7844a621097fdc99de57387458ae9354899b620d0617eabcaefa9eef3d413a33628054335ce656c26fa2986e0f111a6351096b283101ec7868871d770b370973c7405983f9756b3005a3eab492cfd0e7eb42e5c2e15fa6be8718c0a50acc4e5717230":"81c783af538015cef3c60095df53"

generic multi step HMAC-SHA-224 Test Vector NIST CAVS #3
depends_on:MBEDTLS_MD_CAN_SHA224
md_hmac_multi:MBEDTLS_MD_SHA224:14:"85d402d822114d31abf75526e2538705":"8020d8d98cc2e2298b32879c51c751e1dd5558fe2eabb8f158604297d6d072ce2261a1d6830b7cfe2617b57c7126f99c9476211d6161acd75d266da217ec8174b80484c9dc6f0448a0a036a3fc82e8bf54bdb71549368258d5d41f57978a4c266b92e8783ef66350215573d99be4089144b383ad8f3222bae8f3bf80ffb1bb2b":"2aa0340ac9deafe3be38129daca0"

generic multi step HMAC-SHA-224 Test Vector NIST CAVS #4
depends_on:MBEDTLS_MD_CAN_SHA224
md_hmac_multi:MBEDTLS_MD_SHA224:14:"545c6eecc5ee46fa17c59f91a94f81ae":"8fb7f3565593170152ddb2021874784e951977cfdd22f8b72a72a61320a8f2a35697b5e913f717805559b1af1861ee3ed42fb788481e4fd276b17bdbefcae7b4501dc5d20de5b7626dd5efdcd65294db4bdf682c33d9a9255c6435383fa5f1c886326a3acbc6bd50a33ab5b2dbb034ce0112d4e226bbcd57e3731a519aa1d784":"3eb566eac54c4a3a9ef092469f24"

generic multi step HMAC-SHA-224 Test Vector NIST CAVS #5
depends_on:MBEDTLS_MD_CAN_SHA224
md_hmac_multi:MBEDTLS_MD_SHA224:14:"4466ab4dc438841a9750c7f173dff02e":"2534c11c78c99cffaec8f722f04adc7045c7324d58ce98e37cfa94b6ed21ed7f58ce55379ef24b72d6d640ee9154f96c614734be9c408e225d7ba4cecc1179cc9f6e1808e1067aa8f244a99bd0c3267594c1887a40d167f8b7cf78db0d19f97b01fc50b8c86def490dfa7a5135002c33e71d77a8cce8ea0f93e0580439a33733":"59f44a9bbed4875b892d22d6b5ab"

generic multi step HMAC-SHA-224 Test Vector NIST CAVS #6
depends_on:MBEDTLS_MD_CAN_SHA224
md_hmac_multi:MBEDTLS_MD_SHA224:28:"0e3dd9bb5e4cf0f09a4c11600af56d8d":"f4589fa76c328ea25cf8bae582026ba40a59d45a546ff31cf80eb826088f69bb954c452c74586836416dee90a5255bc5d56d3b405b3705a5197045688b32fa984c3a3dfbdc9c2460a0b5e6312a624048bb6f170306535e9b371a3ab134a2642a230ad03d2c688cca80baeaee9a20e1d4c548b1cede29c6a45bf4df2c8c476f1a":"12175b93e3da4c58217145e4dc0a1cf142fab9319bb501e037b350ba"

generic multi step HMAC-SHA-224 Test Vector NIST CAVS #7
depends_on:MBEDTLS_MD_CAN_SHA224
md_hmac_multi:MBEDTLS_MD_SHA224:28:"cda5187b0c5dcb0f8e5a8beed2306584":"9011ae29b44c49b347487ce972965f16ade3c15be0856ce9c853a9739dba07e4f20d594ddc1dfe21560a65a4e458cfa17745575b915a30c7a9412ff8d1d689db9680dd2428c27588bb0dc92d2cd9445fe8f44b840a197c52c3c4333fff45533945134398df6436513cfab06c924046b8c795a5bd92e8d5f2de85bf306f2eed67":"4aaba92b40e2a600feab176eb9b292d814864195c03342aad6f67f08"

generic multi step HMAC-SHA-256 Test Vector NIST CAVS #1
depends_on:MBEDTLS_MD_CAN_SHA256
md_hmac_multi:MBEDTLS_MD_SHA256:16:"cdffd34e6b16fdc0":"d83e78b99ab61709608972b36e76a575603db742269cc5dd4e7d5ca7816e26b65151c92632550cb4c5253c885d5fce53bc47459a1dbd5652786c4aac0145a532f12c05138af04cbb558101a7af5df478834c2146594dd73690d01a4fe72545894335f427ac70204798068cb86c5a600b40b414ede23590b41e1192373df84fe3":"c6f0dde266cb4a26d41e8259d33499cc"

generic multi step HMAC-SHA-256 Test Vector NIST CAVS #2
depends_on:MBEDTLS_MD_CAN_SHA256
md_hmac_multi:MBEDTLS_MD_SHA256:16:"6d97bb5892245be2":"13c2b391d59c0252ca5d2302beaaf88c4bcd779bb505ad9a122003dfae4cc123ad2bd036f225c4f040021a6b9fb8bd6f0281cf2e2631a732bdc71693cc42ef6d52b6c6912a9ef77b3274eb85ad7f965ae6ed44ac1721962a884ec7acfb4534b1488b1c0c45afa4dae8da1eb7b0a88a3240365d7e4e7d826abbde9f9203fd99d7":"31588e241b015319a5ab8c4527296498"

generic multi step HMAC-SHA-256 Test Vector NIST CAVS #3
depends_on:MBEDTLS_MD_CAN_SHA256
md_hmac_multi:MBEDTLS_MD_SHA256:16:"3c7fc8a70b49007a":"60024e428a39c8b8bb2e9591bad9dc2115dfbfd716b6eb7af30a6eb34560caccbbfa47b710fa8d523aca71e9e5ba10fc1feb1a43556d71f07ea4f33496f093044e8caf1d02b79e46eb1288d5964a7a7494f6b92574c35784eece054c6151281d80822f7d47b8231c35d07f5cb5cf4310ddc844845a01c6bfab514c048eccaf9f":"1c98c94a32bec9f253c21070f82f8438"

generic multi step HMAC-SHA-256 Test Vector NIST CAVS #4
depends_on:MBEDTLS_MD_CAN_SHA256
md_hmac_multi:MBEDTLS_MD_SHA256:24:"369f33f85b927a07":"ae8e2a94ca386d448cbacdb0e9040ae3cb297c296363052cc157455da29a0c95897315fc11e3f12b81e2418da1ec280bccbc00e847584ce9d14deeba7b3c9b8dba958b04bba37551f6c9ba9c060be1a4b8cf43aa62e5078b76c6512c5619b71a6a7cf5727180e1ff14f5a1a3c1691bf8b6ebad365c151e58d749d57adb3a4986":"60b90383286533d309de46593e6ce39fc51fb00a8d88278c"

generic multi step HMAC-SHA-256 Test Vector NIST CAVS #5
depends_on:MBEDTLS_MD_CAN_SHA256
md_hmac_multi:MBEDTLS_MD_SHA256:24:"e5179687582b4dc4":"ce103bdacdf32f614f6727bcb31ca1c2824a850d00f5585b016fb234fe1ef2cd687f302d3c6b738ed89a24060d65c36675d0d96307c72ef3e8a83bfa8402e226de9d5d1724ba75c4879bf41a4a465ce61887d9f49a34757849b48bae81c27ebed76faae2ad669bca04747d409148d40812776e0ae2c395b3cb9c89981ce72d5c":"509581f6816df4b8cc9f2cf42b7cc6e6a5a1e375a16f2412"

generic multi step HMAC-SHA-256 Test Vector NIST CAVS #6
depends_on:MBEDTLS_MD_CAN_SHA256
md_hmac_multi:MBEDTLS_MD_SHA256:24:"63cec6246aeb1b61":"c178db908a405fa88aa255b8cad22b4057016585f139ee930388b083d86062fa0b3ea1f23f8a43bd11bee8464bcbd19b5ab9f6a8038d5245516f8274d20c8ee3033a07b908da528fa00343bb595deed500cab9745c4cb6391c23300f0d3584b090b3326c4cfa342620b78f9f5b4f27f7307ed770643ec1764aeae3dcf1a3ec69":"64f3dd861b7c7d29fce9ae0ce9ed954b5d7141806ee9eec7"

generic multi step HMAC-SHA-384 Test Vector NIST CAVS #1
depends_on:MBEDTLS_MD_CAN_SHA384
md_hmac_multi:MBEDTLS_MD_SHA384:32:"91a7401817386948ca952f9a20ee55dc":"2fea5b91035d6d501f3a834fa178bff4e64b99a8450432dafd32e4466b0e1e7781166f8a73f7e036b3b0870920f559f47bd1400a1a906e85e0dcf00a6c26862e9148b23806680f285f1fe4f93cdaf924c181a965465739c14f2268c8be8b471847c74b222577a1310bcdc1a85ef1468aa1a3fd4031213c97324b7509c9050a3d":"6d7be9490058cf413cc09fd043c224c2ec4fa7859b13783000a9a593c9f75838"

generic multi step HMAC-SHA-384 Test Vector NIST CAVS #2
depends_on:MBEDTLS_MD_CAN_SHA384
md_hmac_multi:MBEDTLS_MD_SHA384:32:"d6cac19657061aa90a6da11cd2e9ea47":"9f482e4655173135dfaa22a11bbbe6af263db48716406c5aec162ba3c4b41cad4f5a91558377521191c7343118beee65982929802913d67b6de5c4bdc3d27299bd722219d5ad2efa5bdb9ff7b229fc4bbc3f60719320cf2e7a51cad1133d21bad2d80919b1836ef825308b7c51c6b7677ac782e2bc30007afba065681cbdd215":"f3d5f3c008175321aa7b2ea379eaa4f8b9dcc60f895ec8940b8162f80a7dfe9f"

generic multi step HMAC-SHA-384 Test Vector NIST CAVS #3
depends_on:MBEDTLS_MD_CAN_SHA384
md_hmac_multi:MBEDTLS_MD_SHA384:32:"e06366ad149b8442cd4c1abdddd0afde":"2d140a194c02a5598f69174834679b8371234a0d505491f1bd03e128dd91a8bca2fb812e9d5da71613b5b00952ea78bf450d5b7547dea79135925085c7d3e6f52009c51ca3d88c6c09e9d074b0ee110736e0ec9b478b93efb34d7bf1c41b54decec43eab077a3aa4998ede53f67b4ea36c266745f9643d5360bdc8337c70dabf":"c19c67eda6fe29f3667bee1c897c333ce7683094ae77e84b4c16378d290895a1"

generic multi step HMAC-SHA-384 Test Vector NIST CAVS #4
depends_on:MBEDTLS_MD_CAN_SHA384
md_hmac_multi:MBEDTLS_MD_SHA384:48:"01ac59f42f8bb91d1bd10fe6990d7a87":"3caf18c476edd5615f343ac7b7d3a9da9efade755672d5ba4b8ae8a7505539ea2c124ff755ec0457fbe49e43480b3c71e7f4742ec3693aad115d039f90222b030fdc9440313691716d5302005808c07627483b916fdf61983063c2eb1268f2deeef42fc790334456bc6bad256e31fc9066de7cc7e43d1321b1866db45e905622":"1985fa2163a5943fc5d92f1fe8831215e7e91f0bff5332bc713a072bdb3a8f9e5c5157463a3bfeb36231416e65973e64"

generic multi step HMAC-SHA-384 Test Vector NIST CAVS #5 [#1]
depends_on:MBEDTLS_MD_CAN_SHA384
md_hmac_multi:MBEDTLS_MD_SHA384:48:"fd74b9d9e102a3a80df1baf0cb35bace":"1a068917584813d1689ccbd0370c2114d537cdc8cc52bf6db16d5535f8f7d1ad0c850a9fa0cf62373ffbf7642b1f1e8164010d350721d798d9f99e9724830399c2fce26377e83d38845675457865c03d4a07d741a505ef028343eb29fd46d0f761f3792886998c1e5c32ac3bc7e6f08faed194b34f06eff4d5d4a5b42c481e0e":"a981eaf5de3d78b20ebd4414a4edd0657e3667cd808a0dbc430cf7252f73a5b24efa136039207bd59806897457d74e0c"

generic multi step HMAC-SHA-384 Test Vector NIST CAVS #5 [#2]
depends_on:MBEDTLS_MD_CAN_SHA384
md_hmac_multi:MBEDTLS_MD_SHA384:48:"9fe794f0e26b669fa5f6883149377c6c":"6010c9745e8f1d44cfdc99e7e0fd79bc4271944c2d1d84dba589073dfc4ca5eb98c59356f60cd87bef28aeb83a832bde339b2087daf942aa1f67876c5d5ed33924bed4143bc12a2be532ccaf64daa7e2bc3c8872b9823b0533b6f5159135effe8c61545536975d7c3a61ba7365ec35f165bc92b4d19eb9156ade17dfa1bb4161":"915ae61f8754698c2b6ef9629e93441f8541bd4258a5e05372d19136cfaefc0473b48d96119291b38eb1a3cb1982a986"

generic multi step HMAC-SHA-512 Test Vector NIST CAVS #1
depends_on:MBEDTLS_MD_CAN_SHA512
md_hmac_multi:MBEDTLS_MD_SHA512:32:"c95a17c09940a691ed2d621571b0eb844ede55a9":"99cd28262e81f34878cdcebf4128e05e2098a7009278a66f4c785784d0e5678f3f2b22f86e982d273b6273a222ec61750b4556d766f1550a7aedfe83faedbc4bdae83fa560d62df17eb914d05fdaa48940551bac81d700f5fca7147295e386e8120d66742ec65c6ee8d89a92217a0f6266d0ddc60bb20ef679ae8299c8502c2f":"6bc1379d156559ddee2ed420ea5d5c5ff3e454a1059b7ba72c350e77b6e9333c"

generic multi step HMAC-SHA-512 Test Vector NIST CAVS #2
depends_on:MBEDTLS_MD_CAN_SHA512
md_hmac_multi:MBEDTLS_MD_SHA512:32:"3b10b8fa718840d1dea8e9fc317476bcf55875fd":"f04f5b7073d7d0274e8354433b390306c5607632f5f589c12edb62d55673aff2366d2e6b24de731adf92e654baa30b1cfd4a069788f65ec1b99b015d904d8832110dbd74eae35a81562d14ce4136d820ad0a55ff5489ba678fbbc1c27663ec1349d70e740f0e0ec27cfbe8971819f4789e486b50a2d7271d77e2aaea50de62fd":"fc3c38c7a17e3ce06db033f1c172866f01a00045db55f2e234f71c82264f2ba2"

generic multi step HMAC-SHA-512 Test Vector NIST CAVS #3
depends_on:MBEDTLS_MD_CAN_SHA512
md_hmac_multi:MBEDTLS_MD_SHA512:32:"4803d311394600dc1e0d8fc8cedeb8bde3fe7c42":"a10c125dd702a97153ad923ba5e9889cfac1ba169de370debe51f233735aa6effcc9785c4b5c7e48c477dc5c411ae6a959118584e26adc94b42c2b29b046f3cf01c65b24a24bd2e620bdf650a23bb4a72655b1100d7ce9a4dab697c6379754b4396c825de4b9eb73f2e6a6c0d0353bbdeaf706612800e137b858fdb30f3311c6":"7cd8236c55102e6385f52279506df6fcc388ab75092da21395ce14a82b202ffa"

generic multi step HMAC-SHA-512 Test Vector NIST CAVS #4
depends_on:MBEDTLS_MD_CAN_SHA512
md_hmac_multi:MBEDTLS_MD_SHA512:48:"aeb2f3b977fa6c8e71e07c5a5c74ff58166de092":"22457355dc76095abd46846b41cfe49a06ce42ac8857b4702fc771508dfb3626e0bfe851df897a07b36811ec433766e4b4166c26301b3493e7440d4554b0ef6ac20f1a530e58fac8aeba4e9ff2d4898d8a28783b49cd269c2965fd7f8e4f2d60cf1e5284f2495145b72382aad90e153a90ecae125ad75336fb128825c23fb8b0":"fa39bd8fcc3bfa218f9dea5d3b2ce10a7619e31678a56d8a9d927b1fe703b125af445debe9a89a07db6194d27b44d85a"

generic multi step HMAC-SHA-512 Test Vector NIST CAVS #5
depends_on:MBEDTLS_MD_CAN_SHA512
md_hmac_multi:MBEDTLS_MD_SHA512:48:"4285d3d7744da52775bb44ca436a3154f7980309":"208f0b6f2de2e5aa5df11927ddc6df485edc1193181c484d0f0a434a95418803101d4de9fdb798f93516a6916fa38a8207de1666fe50fe3441c03b112eaaae6954ed063f7ac4e3c1e3f73b20d153fe9e4857f5e91430f0a70ee820529adac2467469fd18adf10e2af0fea27c0abc83c5a9af77c364a466cffce8bab4e2b70bc1":"fe7603f205b2774fe0f14ecfa3e338e90608a806d11ca459dff5ce36b1b264ecd3af5f0492a7521d8da3102ba20927a5"

generic multi step HMAC-SHA-512 Test Vector NIST CAVS #6
depends_on:MBEDTLS_MD_CAN_SHA512
md_hmac_multi:MBEDTLS_MD_SHA512:48:"8ab783d5acf32efa0d9c0a21abce955e96630d89":"17371e013dce839963d54418e97be4bd9fa3cb2a368a5220f5aa1b8aaddfa3bdefc91afe7c717244fd2fb640f5cb9d9bf3e25f7f0c8bc758883b89dcdce6d749d9672fed222277ece3e84b3ec01b96f70c125fcb3cbee6d19b8ef0873f915f173bdb05d81629ba187cc8ac1934b2f75952fb7616ae6bd812946df694bd2763af":"9ac7ca8d1aefc166b046e4cf7602ebe181a0e5055474bff5b342106731da0d7e48e4d87bc0a6f05871574289a1b099f8"

HMAC-SHA3-224 multi-step: NIST example #1: keylen<blocklen
depends_on:MBEDTLS_MD_CAN_SHA3_224
md_hmac_multi:MBEDTLS_MD_SHA3_224:28:"000102030405060708090a0b0c0d0e0f101112131415161718191a1b":"53616d706c65206d65737361676520666f72206b65796c656e3c626c6f636b6c656e":"332cfd59347fdb8e576e77260be4aba2d6dc53117b3bfb52c6d18c04"

HMAC-SHA3-224 multi-step: NIST example #2: keylen=blocklen
depends_on:MBEDTLS_MD_CAN_SHA3_224
md_hmac_multi:MBEDTLS_MD_SHA3_224:28:"000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f404142434445464748494a4b4c4d4e4f505152535455565758595a5b5c5d5e5f606162636465666768696a6b6c6d6e6f707172737475767778797a7b7c7d7e7f808182838485868788898a8b8c8d8e8f":"53616d706c65206d65737361676520666f72206b65796c656e3d626c6f636b6c656e":"d8b733bcf66c644a12323d564e24dcf3fc75f231f3b67968359100c7"

HMAC-SHA3-224 multi-step: NIST example #3: keylen>blocklen
depends_on:MBEDTLS_MD_CAN_SHA3_224
md_hmac_multi:MBEDTLS_MD_SHA3_224:28:"000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f404142434445464748494a4b4c4d4e4f505152535455565758595a5b5c5d5e5f606162636465666768696a6b6c6d6e6f707172737475767778797a7b7c7d7e7f808182838485868788898a8b8c8d8e8f909192939495969798999a9b9c9d9e9fa0a1a2a3a4a5a6a7a8a9aaab":"53616d706c65206d65737361676520666f72206b65796c656e3e626c6f636b6c656e":"078695eecc227c636ad31d063a15dd05a7e819a66ec6d8de1e193e59"

HMAC-SHA3-224 multi-step: NIST example #4: keylen<blocklen, with truncated tag
depends_on:MBEDTLS_MD_CAN_SHA3_224
md_hmac_multi:MBEDTLS_MD_SHA3_224:14:"000102030405060708090a0b0c0d0e0f101112131415161718191a1b":"53616d706c65206d65737361676520666f72206b65796c656e3c626c6f636b6c656e2c2077697468207472756e636174656420746167":"8569c54cbb00a9b78ff1b391b0e5"

HMAC-SHA3-256 multi-step: NIST example #1: keylen<blocklen
depends_on:MBEDTLS_MD_CAN_SHA3_256
md_hmac_multi:MBEDTLS_MD_SHA3_256:32:"000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f":"53616d706c65206d65737361676520666f72206b65796c656e3c626c6f636b6c656e":"4fe8e202c4f058e8dddc23d8c34e467343e23555e24fc2f025d598f558f67205"

HMAC-SHA3-256 multi-step: NIST example #2: keylen=blocklen
depends_on:MBEDTLS_MD_CAN_SHA3_256
md_hmac_multi:MBEDTLS_MD_SHA3_256:32:"000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f404142434445464748494a4b4c4d4e4f505152535455565758595a5b5c5d5e5f606162636465666768696a6b6c6d6e6f707172737475767778797a7b7c7d7e7f8081828384858687":"53616d706c65206d65737361676520666f72206b65796c656e3d626c6f636b6c656e":"68b94e2e538a9be4103bebb5aa016d47961d4d1aa906061313b557f8af2c3faa"

HMAC-SHA3-256 multi-step: NIST example #3: keylen>blocklen
depends_on:MBEDTLS_MD_CAN_SHA3_256
md_hmac_multi:MBEDTLS_MD_SHA3_256:32:"000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f404142434445464748494a4b4c4d4e4f505152535455565758595a5b5c5d5e5f606162636465666768696a6b6c6d6e6f707172737475767778797a7b7c7d7e7f808182838485868788898a8b8c8d8e8f909192939495969798999a9b9c9d9e9fa0a1a2a3a4a5a6a7":"53616d706c65206d65737361676520666f72206b65796c656e3e626c6f636b6c656e":"9bcf2c238e235c3ce88404e813bd2f3a97185ac6f238c63d6229a00b07974258"

HMAC-SHA3-256 multi-step: NIST example #4: keylen<blocklen, with truncated tag
depends_on:MBEDTLS_MD_CAN_SHA3_256
md_hmac_multi:MBEDTLS_MD_SHA3_256:16:"000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f":"53616d706c65206d65737361676520666f72206b65796c656e3c626c6f636b6c656e2c2077697468207472756e636174656420746167":"c8dc7148d8c1423aa549105dafdf9cad"

HMAC-SHA3-384 multi-step: NIST example #1: keylen<blocklen
depends_on:MBEDTLS_MD_CAN_SHA3_384
md_hmac_multi:MBEDTLS_MD_SHA3_384:48:"000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f":"53616d706c65206d65737361676520666f72206b65796c656e3c626c6f636b6c656e":"d588a3c51f3f2d906e8298c1199aa8ff6296218127f6b38a90b6afe2c5617725bc99987f79b22a557b6520db710b7f42"

HMAC-SHA3-384 multi-step: NIST example #2: keylen=blocklen
depends_on:MBEDTLS_MD_CAN_SHA3_384
md_hmac_multi:MBEDTLS_MD_SHA3_384:48:"000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f404142434445464748494a4b4c4d4e4f505152535455565758595a5b5c5d5e5f6061626364656667":"53616d706c65206d65737361676520666f72206b65796c656e3d626c6f636b6c656e":"a27d24b592e8c8cbf6d4ce6fc5bf62d8fc98bf2d486640d9eb8099e24047837f5f3bffbe92dcce90b4ed5b1e7e44fa90"

HMAC-SHA3-384 multi-step: NIST example #3: keylen>blocklen
depends_on:MBEDTLS_MD_CAN_SHA3_384
md_hmac_multi:MBEDTLS_MD_SHA3_384:48:"000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f404142434445464748494a4b4c4d4e4f505152535455565758595a5b5c5d5e5f606162636465666768696a6b6c6d6e6f707172737475767778797a7b7c7d7e7f808182838485868788898a8b8c8d8e8f9091929394959697":"53616d706c65206d65737361676520666f72206b65796c656e3e626c6f636b6c656e":"e5ae4c739f455279368ebf36d4f5354c95aa184c899d3870e460ebc288ef1f9470053f73f7c6da2a71bcaec38ce7d6ac"

HMAC-SHA3-384 multi-step: NIST example #4: keylen<blocklen, with truncated tag
depends_on:MBEDTLS_MD_CAN_SHA3_384
md_hmac_multi:MBEDTLS_MD_SHA3_384:24:"000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f":"53616d706c65206d65737361676520666f72206b65796c656e3c626c6f636b6c656e2c2077697468207472756e636174656420746167":"25f4bf53606e91af79d24a4bb1fd6aecd44414a30c8ebb0a"

HMAC-SHA3-512 multi-step: NIST example #1: keylen<blocklen
depends_on:MBEDTLS_MD_CAN_SHA3_512
md_hmac_multi:MBEDTLS_MD_SHA3_512:64:"000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f":"53616d706c65206d65737361676520666f72206b65796c656e3c626c6f636b6c656e":"4efd629d6c71bf86162658f29943b1c308ce27cdfa6db0d9c3ce81763f9cbce5f7ebe9868031db1a8f8eb7b6b95e5c5e3f657a8996c86a2f6527e307f0213196"

HMAC-SHA3-512 multi-step: NIST example #2: keylen=blocklen
depends_on:MBEDTLS_MD_CAN_SHA3_512
md_hmac_multi:MBEDTLS_MD_SHA3_512:64:"000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f4041424344454647":"53616d706c65206d65737361676520666f72206b65796c656e3d626c6f636b6c656e":"544e257ea2a3e5ea19a590e6a24b724ce6327757723fe2751b75bf007d80f6b360744bf1b7a88ea585f9765b47911976d3191cf83c039f5ffab0d29cc9d9b6da"

HMAC-SHA3-512 multi-step: NIST example #3: keylen>blocklen
depends_on:MBEDTLS_MD_CAN_SHA3_512
md_hmac_multi:MBEDTLS_MD_SHA3_512:64:"000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f404142434445464748494a4b4c4d4e4f505152535455565758595a5b5c5d5e5f606162636465666768696a6b6c6d6e6f707172737475767778797a7b7c7d7e7f8081828384858687":"53616d706c65206d65737361676520666f72206b65796c656e3e626c6f636b6c656e":"5f464f5e5b7848e3885e49b2c385f0694985d0e38966242dc4a5fe3fea4b37d46b65ceced5dcf59438dd840bab22269f0ba7febdb9fcf74602a35666b2a32915"

HMAC-SHA3-512 multi-step: NIST example #4: keylen<blocklen, with truncated tag
depends_on:MBEDTLS_MD_CAN_SHA3_512
md_hmac_multi:MBEDTLS_MD_SHA3_512:32:"000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f":"53616d706c65206d65737361676520666f72206b65796c656e3c626c6f636b6c656e2c2077697468207472756e636174656420746167":"7bb06d859257b25ce73ca700df34c5cbef5c898bac91029e0b27975d4e526a08"

generic SHA-1 Test Vector NIST CAVS #1
depends_on:MBEDTLS_MD_CAN_SHA1
md_hex:MBEDTLS_MD_SHA1:"":"da39a3ee5e6b4b0d3255bfef95601890afd80709"

generic SHA-1 Test Vector NIST CAVS #2
depends_on:MBEDTLS_MD_CAN_SHA1
md_hex:MBEDTLS_MD_SHA1:"a8":"99f2aa95e36f95c2acb0eaf23998f030638f3f15"

generic SHA-1 Test Vector NIST CAVS #3
depends_on:MBEDTLS_MD_CAN_SHA1
md_hex:MBEDTLS_MD_SHA1:"3000":"f944dcd635f9801f7ac90a407fbc479964dec024"

generic SHA-1 Test Vector NIST CAVS #4
depends_on:MBEDTLS_MD_CAN_SHA1
md_hex:MBEDTLS_MD_SHA1:"42749e":"a444319e9b6cc1e8464c511ec0969c37d6bb2619"

generic SHA-1 Test Vector NIST CAVS #5
depends_on:MBEDTLS_MD_CAN_SHA1
md_hex:MBEDTLS_MD_SHA1:"9fc3fe08":"16a0ff84fcc156fd5d3ca3a744f20a232d172253"

generic SHA-1 Test Vector NIST CAVS #6
depends_on:MBEDTLS_MD_CAN_SHA1
md_hex:MBEDTLS_MD_SHA1:"b5c1c6f1af":"fec9deebfcdedaf66dda525e1be43597a73a1f93"

generic SHA-1 Test Vector NIST CAVS #7
depends_on:MBEDTLS_MD_CAN_SHA1
md_hex:MBEDTLS_MD_SHA1:"ec29561244ede706b6eb30a1c371d74450a105c3f9735f7fa9fe38cf67f304a5736a106e92e17139a6813b1c81a4f3d3fb9546ab4296fa9f722826c066869edacd73b2548035185813e22634a9da44000d95a281ff9f264ecce0a931222162d021cca28db5f3c2aa24945ab1e31cb413ae29810fd794cad5dfaf29ec43cb38d198fe4ae1da2359780221405bd6712a5305da4b1b737fce7cd21c0eb7728d08235a9011":"970111c4e77bcc88cc20459c02b69b4aa8f58217"

generic SHA-1 Test Vector NIST CAVS #8
depends_on:MBEDTLS_MD_CAN_SHA1
md_hex:MBEDTLS_MD_SHA1:"5fc2c3f6a7e79dc94be526e5166a238899d54927ce470018fbfd668fd9dd97cbf64e2c91584d01da63be3cc9fdff8adfefc3ac728e1e335b9cdc87f069172e323d094b47fa1e652afe4d6aa147a9f46fda33cacb65f3aa12234746b9007a8c85fe982afed7815221e43dba553d8fe8a022cdac1b99eeeea359e5a9d2e72e382dffa6d19f359f4f27dc3434cd27daeeda8e38594873398678065fbb23665aba9309d946135da0e4a4afdadff14db18e85e71dd93c3bf9faf7f25c8194c4269b1ee3d9934097ab990025d9c3aaf63d5109f52335dd3959d38ae485050e4bbb6235574fc0102be8f7a306d6e8de6ba6becf80f37415b57f9898a5824e77414197422be3d36a6080":"0423dc76a8791107d14e13f5265b343f24cc0f19"

generic SHA-1 Test Vector NIST CAVS #9
depends_on:MBEDTLS_MD_CAN_SHA1
md_hex:MBEDTLS_MD_SHA1:"0f865f46a8f3aed2da18482aa09a8f390dc9da07d51d1bd10fe0bf5f3928d5927d08733d32075535a6d1c8ac1b2dc6ba0f2f633dc1af68e3f0fa3d85e6c60cb7b56c239dc1519a007ea536a07b518ecca02a6c31b46b76f021620ef3fc6976804018380e5ab9c558ebfc5cb1c9ed2d974722bf8ab6398f1f2b82fa5083f85c16a5767a3a07271d67743f00850ce8ec428c7f22f1cf01f99895c0c844845b06a06cecb0c6cf83eb55a1d4ebc44c2c13f6f7aa5e0e08abfd84e7864279057abc471ee4a45dbbb5774afa24e51791a0eada11093b88681fe30baa3b2e94113dc63342c51ca5d1a6096d0897b626e42cb91761058008f746f35465465540ad8c6b8b60f7e1461b3ce9e6529625984cb8c7d46f07f735be067588a0117f23e34ff57800e2bbe9a1605fde6087fb15d22c5d3ac47566b8c448b0cee40373e5ba6eaa21abee71366afbb27dbbd300477d70c371e7b8963812f5ed4fb784fb2f3bd1d3afe883cdd47ef32beaea":"6692a71d73e00f27df976bc56df4970650d90e45"

generic SHA-1 Test Vector NIST CAVS #10
depends_on:MBEDTLS_MD_CAN_SHA1
md_hex:MBEDTLS_MD_SHA1:"8236153781bd2f1b81ffe0def1beb46f5a70191142926651503f1b3bb1016acdb9e7f7acced8dd168226f118ff664a01a8800116fd023587bfba52a2558393476f5fc69ce9c65001f23e70476d2cc81c97ea19caeb194e224339bcb23f77a83feac5096f9b3090c51a6ee6d204b735aa71d7e996d380b80822e4dfd43683af9c7442498cacbea64842dfda238cb099927c6efae07fdf7b23a4e4456e0152b24853fe0d5de4179974b2b9d4a1cdbefcbc01d8d311b5dda059136176ea698ab82acf20dd490be47130b1235cb48f8a6710473cfc923e222d94b582f9ae36d4ca2a32d141b8e8cc36638845fbc499bce17698c3fecae2572dbbd470552430d7ef30c238c2124478f1f780483839b4fb73d63a9460206824a5b6b65315b21e3c2f24c97ee7c0e78faad3df549c7ca8ef241876d9aafe9a309f6da352bec2caaa92ee8dca392899ba67dfed90aef33d41fc2494b765cb3e2422c8e595dabbfaca217757453fb322a13203f425f6073a9903e2dc5818ee1da737afc345f0057744e3a56e1681c949eb12273a3bfc20699e423b96e44bd1ff62e50a848a890809bfe1611c6787d3d741103308f849a790f9c015098286dbacfc34c1718b2c2b77e32194a75dda37954a320fa68764027852855a7e5b5274eb1e2cbcd27161d98b59ad245822015f48af82a45c0ed59be94f9af03d9736048570d6e3ef63b1770bc98dfb77de84b1bb1708d872b625d9ab9b06c18e5dbbf34399391f0f8aa26ec0dac7ff4cb8ec97b52bcb942fa6db2385dcd1b3b9d567aaeb425d567b0ebe267235651a1ed9bf78fd93d3c1dd077fe340bb04b00529c58f45124b717c168d07e9826e33376988bc5cf62845c2009980a4dfa69fbc7e5a0b1bb20a5958ca967aec68eb31dd8fccca9afcd30a26bab26279f1bf6724ff":"11863b483809ef88413ca9b0084ac4a5390640af"

generic SHA-224 Test Vector NIST CAVS #1
depends_on:MBEDTLS_MD_CAN_SHA224
md_hex:MBEDTLS_MD_SHA224:"":"d14a028c2a3a2bc9476102bb288234c415a2b01f828ea62ac5b3e42f"

generic SHA-224 Test Vector NIST CAVS #2
depends_on:MBEDTLS_MD_CAN_SHA224
md_hex:MBEDTLS_MD_SHA224:"ff":"e33f9d75e6ae1369dbabf81b96b4591ae46bba30b591a6b6c62542b5"

generic SHA-224 Test Vector NIST CAVS #3
depends_on:MBEDTLS_MD_CAN_SHA224
md_hex:MBEDTLS_MD_SHA224:"984c":"2fa9df9157d9e027cfbc4c6a9df32e1adc0cbe2328ec2a63c5ae934e"

generic SHA-224 Test Vector NIST CAVS #4
depends_on:MBEDTLS_MD_CAN_SHA224
md_hex:MBEDTLS_MD_SHA224:"50efd0":"b5a9820413c2bf8211fbbf5df1337043b32fa4eafaf61a0c8e9ccede"

generic SHA-224 Test Vector NIST CAVS #5
depends_on:MBEDTLS_MD_CAN_SHA224
md_hex:MBEDTLS_MD_SHA224:"e5e09924":"fd19e74690d291467ce59f077df311638f1c3a46e510d0e49a67062d"

generic SHA-224 Test Vector NIST CAVS #6
depends_on:MBEDTLS_MD_CAN_SHA224
md_hex:MBEDTLS_MD_SHA224:"21ebecb914":"78f4a71c21c694499ce1c7866611b14ace70d905012c356323c7c713"

generic SHA-224 Test Vector NIST CAVS #7
depends_on:MBEDTLS_MD_CAN_SHA224
md_hex:MBEDTLS_MD_SHA224:"fc488947c1a7a589726b15436b4f3d9556262f98fc6422fc5cdf20f0fad7fe427a3491c86d101ffe6b7514f06268f65b2d269b0f69ad9a97847eff1c16a2438775eb7be6847ccf11cb8b2e8dcd6640b095b49c0693fe3cf4a66e2d9b7ad68bff14f3ad69abf49d0aba36cbe0535202deb6599a47225ef05beb351335cd7bc0f480d691198c7e71305ffd53b39d33242bb79cfd98bfd69e137b5d18b2b89ac9ace01c8dbdcf2533cce3682ecc52118de0c1062ec2126c2e657d6ea3d9e2398e705d4b0b1f1ceecb266dffc4f31bf42744fb1e938dc22a889919ee1e73f463f7871fed720519e32186264b7ef2a0e5d9a18e6c95c0781894f77967f048951dec3b4d892a38710b1e3436d3c29088eb8b3da1789c25db3d3bc6c26081206e7155d210a89b80ca6ea877c41ff9947c0f25625dcb118294a163501f6239c326661a958fd12da4cd15a899f8b88cc723589056eaec5aa04a4cf5dbb6f480f9660423ccf38c486e210707e0fb25e1f126ceb2616f63e147a647dab0af9ebe89d65458bf636154a46e4cab95f5ee62da2c7974cd14b90d3e4f99f81733e85b3c1d5da2b508d9b90f5eed7eff0d9c7649de62bee00375454fee4a39576a5bbfdae428e7f8097bdf7797f167686cb68407e49079e4611ff3402b6384ba7b7e522bd2bb11ce8fd02ea4c1604d163ac4f6dde50b8b1f593f7edaadeac0868ed97df690200680c25f0f5d85431a529e4f339089dcdeda105e4ee51dead704cdf5a605c55fb055c9b0e86b8ba1b564c0dea3eb790a595cb103cb292268b07c5e59371e1a7ef597cd4b22977a820694c9f9aeb55d9de3ef62b75d6e656e3336698d960a3787bf8cf5b926a7faeef52ae128bcb5dc9e66d94b016c7b8e034879171a2d91c381f57e6a815b63b5ee6a6d2ff435b49f14c963966960194430d78f8f87627a67757fb3532b289550894da6dce4817a4e07f4d56877a1102ffcc8befa5c9f8fca6a4574d93ff70376c8861e0f8108cf907fce77ecb49728f86f034f80224b9695682e0824462f76cdb1fd1af151337b0d85419047a7aa284791718a4860cd586f7824b95bc837b6fd4f9be5aade68456e20356aa4d943dac36bf8b67b9e8f9d01a00fcda74b798bafa746c661b010f75b59904b29d0c8041504811c4065f82cf2ead58d2f595cbd8bc3e7043f4d94577b373b7cfe16a36fe564f505c03b70cfeb5e5f411c79481338aa67e86b3f5a2e77c21e454c333ae3da943ab723ab5f4c940395319534a5575f64acba0d0ecc43f60221ed3badf7289c9b3a7b903a2d6c94e15fa4c310dc4fa7faa0c24f405160a1002dbef20e4105d481db982f7243f79400a6e4cd9753c4b9732a47575f504b20c328fe9add7f432a4f075829da07b53b695037dc51737d3cd731934df333cd1a53fcf65aa31baa450ca501a6fae26e322347e618c5a444d92e9fec5a8261ae38b98fee5be77c02cec09ddccd5b3de92036":"1302149d1e197c41813b054c942329d420e366530f5517b470e964fe"

generic SHA-256 Test Vector NIST CAVS #1
depends_on:MBEDTLS_MD_CAN_SHA256
md_hex:MBEDTLS_MD_SHA256:"":"e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855"

generic SHA-256 Test Vector NIST CAVS #2
depends_on:MBEDTLS_MD_CAN_SHA256
md_hex:MBEDTLS_MD_SHA256:"bd":"68325720aabd7c82f30f554b313d0570c95accbb7dc4b5aae11204c08ffe732b"

generic SHA-256 Test Vector NIST CAVS #3
depends_on:MBEDTLS_MD_CAN_SHA256
md_hex:MBEDTLS_MD_SHA256:"5fd4":"7c4fbf484498d21b487b9d61de8914b2eadaf2698712936d47c3ada2558f6788"

generic SHA-256 Test Vector NIST CAVS #4
depends_on:MBEDTLS_MD_CAN_SHA256
md_hex:MBEDTLS_MD_SHA256:"b0bd69":"4096804221093ddccfbf46831490ea63e9e99414858f8d75ff7f642c7ca61803"

generic SHA-256 Test Vector NIST CAVS #5
depends_on:MBEDTLS_MD_CAN_SHA256
md_hex:MBEDTLS_MD_SHA256:"c98c8e55":"7abc22c0ae5af26ce93dbb94433a0e0b2e119d014f8e7f65bd56c61ccccd9504"

generic SHA-256 Test Vector NIST CAVS #6
depends_on:MBEDTLS_MD_CAN_SHA256
md_hex:MBEDTLS_MD_SHA256:"81a723d966":"7516fb8bb11350df2bf386bc3c33bd0f52cb4c67c6e4745e0488e62c2aea2605"

generic SHA-256 Test Vector NIST CAVS #7
depends_on:MBEDTLS_MD_CAN_SHA256
md_hex:MBEDTLS_MD_SHA256:"8390cf0be07661cc7669aac54ce09a37733a629d45f5d983ef201f9b2d13800e555d9b1097fec3b783d7a50dcb5e2b644b96a1e9463f177cf34906bf388f366db5c2deee04a30e283f764a97c3b377a034fefc22c259214faa99babaff160ab0aaa7e2ccb0ce09c6b32fe08cbc474694375aba703fadbfa31cf685b30a11c57f3cf4edd321e57d3ae6ebb1133c8260e75b9224fa47a2bb205249add2e2e62f817491482ae152322be0900355cdcc8d42a98f82e961a0dc6f537b7b410eff105f59673bfb787bf042aa071f7af68d944d27371c64160fe9382772372516c230c1f45c0d6b6cca7f274b394da9402d3eafdf733994ec58ab22d71829a98399574d4b5908a447a5a681cb0dd50a31145311d92c22a16de1ead66a5499f2dceb4cae694772ce90762ef8336afec653aa9b1a1c4820b221136dfce80dce2ba920d88a530c9410d0a4e0358a3a11052e58dd73b0b179ef8f56fe3b5a2d117a73a0c38a1392b6938e9782e0d86456ee4884e3c39d4d75813f13633bc79baa07c0d2d555afbf207f52b7dca126d015aa2b9873b3eb065e90b9b065a5373fe1fb1b20d594327d19fba56cb81e7b6696605ffa56eba3c27a438697cc21b201fd7e09f18deea1b3ea2f0d1edc02df0e20396a145412cd6b13c32d2e605641c948b714aec30c0649dc44143511f35ab0fd5dd64c34d06fe86f3836dfe9edeb7f08cfc3bd40956826356242191f99f53473f32b0cc0cf9321d6c92a112e8db90b86ee9e87cc32d0343db01e32ce9eb782cb24efbbbeb440fe929e8f2bf8dfb1550a3a2e742e8b455a3e5730e9e6a7a9824d17acc0f72a7f67eae0f0970f8bde46dcdefaed3047cf807e7f00a42e5fd11d40f5e98533d7574425b7d2bc3b3845c443008b58980e768e464e17cc6f6b3939eee52f713963d07d8c4abf02448ef0b889c9671e2f8a436ddeeffcca7176e9bf9d1005ecd377f2fa67c23ed1f137e60bf46018a8bd613d038e883704fc26e798969df35ec7bbc6a4fe46d8910bd82fa3cded265d0a3b6d399e4251e4d8233daa21b5812fded6536198ff13aa5a1cd46a5b9a17a4ddc1d9f85544d1d1cc16f3df858038c8e071a11a7e157a85a6a8dc47e88d75e7009a8b26fdb73f33a2a70f1e0c259f8f9533b9b8f9af9288b7274f21baeec78d396f8bacdcc22471207d9b4efccd3fedc5c5a2214ff5e51c553f35e21ae696fe51e8df733a8e06f50f419e599e9f9e4b37ce643fc810faaa47989771509d69a110ac916261427026369a21263ac4460fb4f708f8ae28599856db7cb6a43ac8e03d64a9609807e76c5f312b9d1863bfa304e8953647648b4f4ab0ed995e":"4109cdbec3240ad74cc6c37f39300f70fede16e21efc77f7865998714aad0b5e"

generic SHA-384 Test Vector NIST CAVS #1
depends_on:MBEDTLS_MD_CAN_SHA384
md_hex:MBEDTLS_MD_SHA384:"":"38b060a751ac96384cd9327eb1b1e36a21fdb71114be07434c0cc7bf63f6e1da274edebfe76f65fbd51ad2f14898b95b"

generic SHA-384 Test Vector NIST CAVS #2
depends_on:MBEDTLS_MD_CAN_SHA384
md_hex:MBEDTLS_MD_SHA384:"ab":"fb94d5be118865f6fcbc978b825da82cff188faec2f66cb84b2537d74b4938469854b0ca89e66fa2e182834736629f3d"

generic SHA-384 Test Vector NIST CAVS #3
depends_on:MBEDTLS_MD_CAN_SHA384
md_hex:MBEDTLS_MD_SHA384:"7c27":"3d80be467df86d63abb9ea1d3f9cb39cd19890e7f2c53a6200bedc5006842b35e820dc4e0ca90ca9b97ab23ef07080fc"

generic SHA-384 Test Vector NIST CAVS #4
depends_on:MBEDTLS_MD_CAN_SHA384
md_hex:MBEDTLS_MD_SHA384:"31f5ca":"78d54b943421fdf7ba90a7fb9637c2073aa480454bd841d39ff72f4511fc21fb67797b652c0c823229342873d3bef955"

generic SHA-384 Test Vector NIST CAVS #5
depends_on:MBEDTLS_MD_CAN_SHA384
md_hex:MBEDTLS_MD_SHA384:"7bdee3f8":"8bdafba0777ee446c3431c2d7b1fbb631089f71d2ca417abc1d230e1aba64ec2f1c187474a6f4077d372c14ad407f99a"

generic SHA-384 Test Vector NIST CAVS #6
depends_on:MBEDTLS_MD_CAN_SHA384
md_hex:MBEDTLS_MD_SHA384:"8f05604915":"504e414bf1db1060f14c8c799e25b1e0c4dcf1504ebbd129998f0ae283e6de86e0d3c7e879c73ec3b1836c3ee89c2649"

generic SHA-384 Test Vector NIST CAVS #7
depends_on:MBEDTLS_MD_CAN_SHA384
md_hex:MBEDTLS_MD_SHA384:"665da6eda214":"4c022f112010908848312f8b8f1072625fd5c105399d562ea1d56130619a7eac8dfc3748fd05ee37e4b690be9daa9980"

generic SHA-384 Test Vector NIST CAVS #8
depends_on:MBEDTLS_MD_CAN_SHA384
md_hex:MBEDTLS_MD_SHA384:"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":"cba9e3eb12a6f83db11e8a6ff40d1049854ee094416bc527fea931d8585428a8ed6242ce81f6769b36e2123a5c23483e"

generic SHA-512 Test Vector NIST CAVS #1
depends_on:MBEDTLS_MD_CAN_SHA512
md_hex:MBEDTLS_MD_SHA512:"":"cf83e1357eefb8bdf1542850d66d8007d620e4050b5715dc83f4a921d36ce9ce47d0d13c5d85f2b0ff8318d2877eec2f63b931bd47417a81a538327af927da3e"

generic SHA3-224 Hash file #1
depends_on:MBEDTLS_MD_CAN_SHA3_224
mbedtls_md_file:MBEDTLS_MD_SHA3_224:"../framework/data_files/hash_file_1":"320f1a9257d442178d90fda8987743a5e7bb5ed0b18bc7d66ee3633e"

generic SHA3-224 Hash file #2
depends_on:MBEDTLS_MD_CAN_SHA3_224
mbedtls_md_file:MBEDTLS_MD_SHA3_224:"../framework/data_files/hash_file_2":"db06a96306b43677f0e3592a0fe1d276141fa7458b7be93197550442"

generic SHA3-224 Hash file #3
depends_on:MBEDTLS_MD_CAN_SHA3_224
mbedtls_md_file:MBEDTLS_MD_SHA3_224:"../framework/data_files/hash_file_3":"0d125fdd48b0e322ca845402fbecb827053c9f324c58933be2e474a0"

generic SHA3-224 Hash file #4
depends_on:MBEDTLS_MD_CAN_SHA3_224
mbedtls_md_file:MBEDTLS_MD_SHA3_224:"../framework/data_files/hash_file_4":"6b4e03423667dbb73b6e15454f0eb1abd4597f9a1b078e3f5b5a6bc7"

generic SHA3-256 Hash file #1
depends_on:MBEDTLS_MD_CAN_SHA3_256
mbedtls_md_file:MBEDTLS_MD_SHA3_256:"../framework/data_files/hash_file_1":"f429826659dd9f313e6226ced5c841fe1b0e9dd16554392b694fa3000f1ae1e2"

generic SHA3-256 Hash file #2
depends_on:MBEDTLS_MD_CAN_SHA3_256
mbedtls_md_file:MBEDTLS_MD_SHA3_256:"../framework/data_files/hash_file_2":"3aed2fda8604dbec5a67710b4d4c89a90745e10ee633649e53e75c7e25d30152"

generic SHA3-256 Hash file #3
depends_on:MBEDTLS_MD_CAN_SHA3_256
mbedtls_md_file:MBEDTLS_MD_SHA3_256:"../framework/data_files/hash_file_3":"c4b6492fd1c475c5e560545a2573b0efcd02d54ef4f63c9d8158dd87bed99d85"

generic SHA3-256 Hash file #4
depends_on:MBEDTLS_MD_CAN_SHA3_256
mbedtls_md_file:MBEDTLS_MD_SHA3_256:"../framework/data_files/hash_file_4":"a7ffc6f8bf1ed76651c14756a061d662f580ff4de43b49fa82d80a4b80f8434a"

generic SHA3-384 Hash file #1
depends_on:MBEDTLS_MD_CAN_SHA3_384
mbedtls_md_file:MBEDTLS_MD_SHA3_384:"../framework/data_files/hash_file_1":"06ab3677496658d3faad937f3f7887b3e925b480190544c612e76b88c5d21b4ca12691f27b8ef569d601925915cdf2a6"

generic SHA3-384 Hash file #2
depends_on:MBEDTLS_MD_CAN_SHA3_384
mbedtls_md_file:MBEDTLS_MD_SHA3_384:"../framework/data_files/hash_file_2":"b5efc40db7af544bf3fb8c782f2db478dbb81aa83d2ef0e8bbdcf06371de7cc984aac5539c4c9244c1e6ebbb85e23983"

generic SHA3-384 Hash file #3
depends_on:MBEDTLS_MD_CAN_SHA3_384
mbedtls_md_file:MBEDTLS_MD_SHA3_384:"../framework/data_files/hash_file_3":"0f08dc09cb39240e09b01e7f3ee3ce6b893bf393f52d2ac87083cef7d3a469fa99763e58b25306b0a2381d9bbdaa802f"

generic SHA3-384 Hash file #4
depends_on:MBEDTLS_MD_CAN_SHA3_384
mbedtls_md_file:MBEDTLS_MD_SHA3_384:"../framework/data_files/hash_file_4":"0c63a75b845e4f7d01107d852e4c2485c51a50aaaa94fc61995e71bbee983a2ac3713831264adb47fb6bd1e058d5f004"

generic SHA3-512 Hash file #1
depends_on:MBEDTLS_MD_CAN_SHA3_512
mbedtls_md_file:MBEDTLS_MD_SHA3_512:"../framework/data_files/hash_file_1":"7d43cbb75218110d7fcc227b6977e6f3b855184c646b679055897cba0cd445ec968430231866801c4f0993f8735cf46bc4858868423d31ca283a6f1ecf25c580"

generic SHA3-512 Hash file #2
depends_on:MBEDTLS_MD_CAN_SHA3_512
mbedtls_md_file:MBEDTLS_MD_SHA3_512:"../framework/data_files/hash_file_2":"212bd00cfc7f3a5b73b5b4772dd83562826207eba30ab00be2c886aef3841ef66eb25097091bfacb6d45dd4557489f91836c04c4f0d96e32ae96fb006d4b2ad6"

generic SHA3-512 Hash file #3
depends_on:MBEDTLS_MD_CAN_SHA3_512
mbedtls_md_file:MBEDTLS_MD_SHA3_512:"../framework/data_files/hash_file_3":"a78a0266820e36f6fb26a0c8deb0b24108e209cc217852ed073904bc44ec586c5704c0a56de57f9906b8ced380fee6ac2bd432a93de7f39b23ed0aabdd7ae813"

generic SHA3-512 Hash file #4
depends_on:MBEDTLS_MD_CAN_SHA3_512
mbedtls_md_file:MBEDTLS_MD_SHA3_512:"../framework/data_files/hash_file_4":"a69f73cca23a9ac5c8b567dc185a756e97c982164fe25859e0d1dcc1475c80a615b2123af1f5f94c11e3e9402c3ac558f500199d95b6d3e301758586281dcd26"

generic SHA-512 Test Vector NIST CAVS #2
depends_on:MBEDTLS_MD_CAN_SHA512
md_hex:MBEDTLS_MD_SHA512:"8f":"e4cd2d19931b5aad9c920f45f56f6ce34e3d38c6d319a6e11d0588ab8b838576d6ce6d68eea7c830de66e2bd96458bfa7aafbcbec981d4ed040498c3dd95f22a"

generic SHA-512 Test Vector NIST CAVS #3
depends_on:MBEDTLS_MD_CAN_SHA512
md_hex:MBEDTLS_MD_SHA512:"e724":"7dbb520221a70287b23dbcf62bfc1b73136d858e86266732a7fffa875ecaa2c1b8f673b5c065d360c563a7b9539349f5f59bef8c0c593f9587e3cd50bb26a231"

generic SHA-512 Test Vector NIST CAVS #4
depends_on:MBEDTLS_MD_CAN_SHA512
md_hex:MBEDTLS_MD_SHA512:"de4c90":"33ce98281045a5c4c9df0363d8196f1d7dfcd5ee46ac89776fd8a4344c12f123a66788af5bd41ceff1941aa5637654b4064c88c14e00465ab79a2fc6c97e1014"

generic SHA-512 Test Vector NIST CAVS #5
depends_on:MBEDTLS_MD_CAN_SHA512
md_hex:MBEDTLS_MD_SHA512:"a801e94b":"dadb1b5a27f9fece8d86adb2a51879beb1787ff28f4e8ce162cad7fee0f942efcabbf738bc6f797fc7cc79a3a75048cd4c82ca0757a324695bfb19a557e56e2f"

generic SHA-512 Test Vector NIST CAVS #6
depends_on:MBEDTLS_MD_CAN_SHA512
md_hex:MBEDTLS_MD_SHA512:"94390d3502":"b6175c4c4cccf69e0ce5f0312010886ea6b34d43673f942ae42483f9cbb7da817de4e11b5d58e25a3d9bd721a22cdffe1c40411cc45df1911fa5506129b69297"

generic SHA-512 Test Vector NIST CAVS #7
depends_on:MBEDTLS_MD_CAN_SHA512
md_hex:MBEDTLS_MD_SHA512:"49297dd63e5f":"1fcc1e6f6870859d11649f5e5336a9cd16329c029baf04d5a6edf257889a2e9522b497dd656bb402da461307c4ee382e2e89380c8e6e6e7697f1e439f650fa94"

generic SHA-512 Test Vector NIST CAVS #8
depends_on:MBEDTLS_MD_CAN_SHA512
md_hex:MBEDTLS_MD_SHA512:"990d1ae71a62d7bda9bfdaa1762a68d296eee72a4cd946f287a898fbabc002ea941fd8d4d991030b4d27a637cce501a834bb95eab1b7889a3e784c7968e67cbf552006b206b68f76d9191327524fcc251aeb56af483d10b4e0c6c5e599ee8c0fe4faeca8293844a8547c6a9a90d093f2526873a19ad4a5e776794c68c742fb834793d2dfcb7fea46c63af4b70fd11cb6e41834e72ee40edb067b292a794990c288d5007e73f349fb383af6a756b8301ad6e5e0aa8cd614399bb3a452376b1575afa6bdaeaafc286cb064bb91edef97c632b6c1113d107fa93a0905098a105043c2f05397f702514439a08a9e5ddc196100721d45c8fc17d2ed659376f8a00bd5cb9a0860e26d8a29d8d6aaf52de97e9346033d6db501a35dbbaf97c20b830cd2d18c2532f3a59cc497ee64c0e57d8d060e5069b28d86edf1adcf59144b221ce3ddaef134b3124fbc7dd000240eff0f5f5f41e83cd7f5bb37c9ae21953fe302b0f6e8b68fa91c6ab99265c64b2fd9cd4942be04321bb5d6d71932376c6f2f88e02422ba6a5e2cb765df93fd5dd0728c6abdaf03bce22e0678a544e2c3636f741b6f4447ee58a8fc656b43ef817932176adbfc2e04b2c812c273cd6cbfa4098f0be036a34221fa02643f5ee2e0b38135f2a18ecd2f16ebc45f8eb31b8ab967a1567ee016904188910861ca1fa205c7adaa194b286893ffe2f4fbe0384c2aef72a4522aeafd3ebc71f9db71eeeef86c48394a1c86d5b36c352cc33a0a2c800bc99e62fd65b3a2fd69e0b53996ec13d8ce483ce9319efd9a85acefabdb5342226febb83fd1daf4b24265f50c61c6de74077ef89b6fecf9f29a1f871af1e9f89b2d345cda7499bd45c42fa5d195a1e1a6ba84851889e730da3b2b916e96152ae0c92154b49719841db7e7cc707ba8a5d7b101eb4ac7b629bb327817910fff61580b59aab78182d1a2e33473d05b00b170b29e331870826cfe45af206aa7d0246bbd8566ca7cfb2d3c10bfa1db7dd48dd786036469ce7282093d78b5e1a5b0fc81a54c8ed4ceac1e5305305e78284ac276f5d7862727aff246e17addde50c670028d572cbfc0be2e4f8b2eb28fa68ad7b4c6c2a239c460441bfb5ea049f23b08563b4e47729a59e5986a61a6093dbd54f8c36ebe87edae01f251cb060ad1364ce677d7e8d5a4a4ca966a7241cc360bc2acb280e5f9e9c1b032ad6a180a35e0c5180b9d16d026c865b252098cc1d99ba7375ca31c7702c0d943d5e3dd2f6861fa55bd46d94b67ed3e52eccd8dd06d968e01897d6de97ed3058d91dd":"8e4bc6f8b8c60fe4d68c61d9b159c8693c3151c46749af58da228442d927f23359bd6ccd6c2ec8fa3f00a86cecbfa728e1ad60b821ed22fcd309ba91a4138bc9"

generic multi step SHA-1 Test Vector NIST CAVS #1
depends_on:MBEDTLS_MD_CAN_SHA1
md_hex_multi:MBEDTLS_MD_SHA1:"":"da39a3ee5e6b4b0d3255bfef95601890afd80709"

generic multi step SHA-1 Test Vector NIST CAVS #2
depends_on:MBEDTLS_MD_CAN_SHA1
md_hex_multi:MBEDTLS_MD_SHA1:"a8":"99f2aa95e36f95c2acb0eaf23998f030638f3f15"

generic multi step SHA-1 Test Vector NIST CAVS #3
depends_on:MBEDTLS_MD_CAN_SHA1
md_hex_multi:MBEDTLS_MD_SHA1:"3000":"f944dcd635f9801f7ac90a407fbc479964dec024"

generic multi step SHA-1 Test Vector NIST CAVS #4
depends_on:MBEDTLS_MD_CAN_SHA1
md_hex_multi:MBEDTLS_MD_SHA1:"42749e":"a444319e9b6cc1e8464c511ec0969c37d6bb2619"

generic multi step SHA-1 Test Vector NIST CAVS #5
depends_on:MBEDTLS_MD_CAN_SHA1
md_hex_multi:MBEDTLS_MD_SHA1:"9fc3fe08":"16a0ff84fcc156fd5d3ca3a744f20a232d172253"

generic multi step SHA-1 Test Vector NIST CAVS #6
depends_on:MBEDTLS_MD_CAN_SHA1
md_hex_multi:MBEDTLS_MD_SHA1:"b5c1c6f1af":"fec9deebfcdedaf66dda525e1be43597a73a1f93"

generic multi step SHA-1 Test Vector NIST CAVS #7
depends_on:MBEDTLS_MD_CAN_SHA1
md_hex_multi:MBEDTLS_MD_SHA1:"ec29561244ede706b6eb30a1c371d74450a105c3f9735f7fa9fe38cf67f304a5736a106e92e17139a6813b1c81a4f3d3fb9546ab4296fa9f722826c066869edacd73b2548035185813e22634a9da44000d95a281ff9f264ecce0a931222162d021cca28db5f3c2aa24945ab1e31cb413ae29810fd794cad5dfaf29ec43cb38d198fe4ae1da2359780221405bd6712a5305da4b1b737fce7cd21c0eb7728d08235a9011":"970111c4e77bcc88cc20459c02b69b4aa8f58217"

generic multi step SHA-1 Test Vector NIST CAVS #8
depends_on:MBEDTLS_MD_CAN_SHA1
md_hex_multi:MBEDTLS_MD_SHA1:"5fc2c3f6a7e79dc94be526e5166a238899d54927ce470018fbfd668fd9dd97cbf64e2c91584d01da63be3cc9fdff8adfefc3ac728e1e335b9cdc87f069172e323d094b47fa1e652afe4d6aa147a9f46fda33cacb65f3aa12234746b9007a8c85fe982afed7815221e43dba553d8fe8a022cdac1b99eeeea359e5a9d2e72e382dffa6d19f359f4f27dc3434cd27daeeda8e38594873398678065fbb23665aba9309d946135da0e4a4afdadff14db18e85e71dd93c3bf9faf7f25c8194c4269b1ee3d9934097ab990025d9c3aaf63d5109f52335dd3959d38ae485050e4bbb6235574fc0102be8f7a306d6e8de6ba6becf80f37415b57f9898a5824e77414197422be3d36a6080":"0423dc76a8791107d14e13f5265b343f24cc0f19"

generic multi step SHA-1 Test Vector NIST CAVS #9
depends_on:MBEDTLS_MD_CAN_SHA1
md_hex_multi:MBEDTLS_MD_SHA1:"0f865f46a8f3aed2da18482aa09a8f390dc9da07d51d1bd10fe0bf5f3928d5927d08733d32075535a6d1c8ac1b2dc6ba0f2f633dc1af68e3f0fa3d85e6c60cb7b56c239dc1519a007ea536a07b518ecca02a6c31b46b76f021620ef3fc6976804018380e5ab9c558ebfc5cb1c9ed2d974722bf8ab6398f1f2b82fa5083f85c16a5767a3a07271d67743f00850ce8ec428c7f22f1cf01f99895c0c844845b06a06cecb0c6cf83eb55a1d4ebc44c2c13f6f7aa5e0e08abfd84e7864279057abc471ee4a45dbbb5774afa24e51791a0eada11093b88681fe30baa3b2e94113dc63342c51ca5d1a6096d0897b626e42cb91761058008f746f35465465540ad8c6b8b60f7e1461b3ce9e6529625984cb8c7d46f07f735be067588a0117f23e34ff57800e2bbe9a1605fde6087fb15d22c5d3ac47566b8c448b0cee40373e5ba6eaa21abee71366afbb27dbbd300477d70c371e7b8963812f5ed4fb784fb2f3bd1d3afe883cdd47ef32beaea":"6692a71d73e00f27df976bc56df4970650d90e45"

generic multi step SHA-1 Test Vector NIST CAVS #10
depends_on:MBEDTLS_MD_CAN_SHA1
md_hex_multi:MBEDTLS_MD_SHA1:"8236153781bd2f1b81ffe0def1beb46f5a70191142926651503f1b3bb1016acdb9e7f7acced8dd168226f118ff664a01a8800116fd023587bfba52a2558393476f5fc69ce9c65001f23e70476d2cc81c97ea19caeb194e224339bcb23f77a83feac5096f9b3090c51a6ee6d204b735aa71d7e996d380b80822e4dfd43683af9c7442498cacbea64842dfda238cb099927c6efae07fdf7b23a4e4456e0152b24853fe0d5de4179974b2b9d4a1cdbefcbc01d8d311b5dda059136176ea698ab82acf20dd490be47130b1235cb48f8a6710473cfc923e222d94b582f9ae36d4ca2a32d141b8e8cc36638845fbc499bce17698c3fecae2572dbbd470552430d7ef30c238c2124478f1f780483839b4fb73d63a9460206824a5b6b65315b21e3c2f24c97ee7c0e78faad3df549c7ca8ef241876d9aafe9a309f6da352bec2caaa92ee8dca392899ba67dfed90aef33d41fc2494b765cb3e2422c8e595dabbfaca217757453fb322a13203f425f6073a9903e2dc5818ee1da737afc345f0057744e3a56e1681c949eb12273a3bfc20699e423b96e44bd1ff62e50a848a890809bfe1611c6787d3d741103308f849a790f9c015098286dbacfc34c1718b2c2b77e32194a75dda37954a320fa68764027852855a7e5b5274eb1e2cbcd27161d98b59ad245822015f48af82a45c0ed59be94f9af03d9736048570d6e3ef63b1770bc98dfb77de84b1bb1708d872b625d9ab9b06c18e5dbbf34399391f0f8aa26ec0dac7ff4cb8ec97b52bcb942fa6db2385dcd1b3b9d567aaeb425d567b0ebe267235651a1ed9bf78fd93d3c1dd077fe340bb04b00529c58f45124b717c168d07e9826e33376988bc5cf62845c2009980a4dfa69fbc7e5a0b1bb20a5958ca967aec68eb31dd8fccca9afcd30a26bab26279f1bf6724ff":"11863b483809ef88413ca9b0084ac4a5390640af"

generic multi step SHA-224 Test Vector NIST CAVS #1
depends_on:MBEDTLS_MD_CAN_SHA224
md_hex_multi:MBEDTLS_MD_SHA224:"":"d14a028c2a3a2bc9476102bb288234c415a2b01f828ea62ac5b3e42f"

generic multi step SHA-224 Test Vector NIST CAVS #2
depends_on:MBEDTLS_MD_CAN_SHA224
md_hex_multi:MBEDTLS_MD_SHA224:"ff":"e33f9d75e6ae1369dbabf81b96b4591ae46bba30b591a6b6c62542b5"

generic multi step SHA-224 Test Vector NIST CAVS #3
depends_on:MBEDTLS_MD_CAN_SHA224
md_hex_multi:MBEDTLS_MD_SHA224:"984c":"2fa9df9157d9e027cfbc4c6a9df32e1adc0cbe2328ec2a63c5ae934e"

generic multi step SHA-224 Test Vector NIST CAVS #4
depends_on:MBEDTLS_MD_CAN_SHA224
md_hex_multi:MBEDTLS_MD_SHA224:"50efd0":"b5a9820413c2bf8211fbbf5df1337043b32fa4eafaf61a0c8e9ccede"

generic multi step SHA-224 Test Vector NIST CAVS #5
depends_on:MBEDTLS_MD_CAN_SHA224
md_hex_multi:MBEDTLS_MD_SHA224:"e5e09924":"fd19e74690d291467ce59f077df311638f1c3a46e510d0e49a67062d"

generic multi step SHA-224 Test Vector NIST CAVS #6
depends_on:MBEDTLS_MD_CAN_SHA224
md_hex_multi:MBEDTLS_MD_SHA224:"21ebecb914":"78f4a71c21c694499ce1c7866611b14ace70d905012c356323c7c713"

generic multi step SHA-224 Test Vector NIST CAVS #7
depends_on:MBEDTLS_MD_CAN_SHA224
md_hex_multi:MBEDTLS_MD_SHA224:"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":"1302149d1e197c41813b054c942329d420e366530f5517b470e964fe"

generic multi step SHA-256 Test Vector NIST CAVS #1
depends_on:MBEDTLS_MD_CAN_SHA256
md_hex_multi:MBEDTLS_MD_SHA256:"":"e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855"

generic multi step SHA-256 Test Vector NIST CAVS #2
depends_on:MBEDTLS_MD_CAN_SHA256
md_hex_multi:MBEDTLS_MD_SHA256:"bd":"68325720aabd7c82f30f554b313d0570c95accbb7dc4b5aae11204c08ffe732b"

generic multi step SHA-256 Test Vector NIST CAVS #3
depends_on:MBEDTLS_MD_CAN_SHA256
md_hex_multi:MBEDTLS_MD_SHA256:"5fd4":"7c4fbf484498d21b487b9d61de8914b2eadaf2698712936d47c3ada2558f6788"

generic multi step SHA-256 Test Vector NIST CAVS #4
depends_on:MBEDTLS_MD_CAN_SHA256
md_hex_multi:MBEDTLS_MD_SHA256:"b0bd69":"4096804221093ddccfbf46831490ea63e9e99414858f8d75ff7f642c7ca61803"

generic multi step SHA-256 Test Vector NIST CAVS #5
depends_on:MBEDTLS_MD_CAN_SHA256
md_hex_multi:MBEDTLS_MD_SHA256:"c98c8e55":"7abc22c0ae5af26ce93dbb94433a0e0b2e119d014f8e7f65bd56c61ccccd9504"

generic multi step SHA-256 Test Vector NIST CAVS #6
depends_on:MBEDTLS_MD_CAN_SHA256
md_hex_multi:MBEDTLS_MD_SHA256:"81a723d966":"7516fb8bb11350df2bf386bc3c33bd0f52cb4c67c6e4745e0488e62c2aea2605"

generic multi step SHA-256 Test Vector NIST CAVS #7
depends_on:MBEDTLS_MD_CAN_SHA256
md_hex_multi:MBEDTLS_MD_SHA256:"8390cf0be07661cc7669aac54ce09a37733a629d45f5d983ef201f9b2d13800e555d9b1097fec3b783d7a50dcb5e2b644b96a1e9463f177cf34906bf388f366db5c2deee04a30e283f764a97c3b377a034fefc22c259214faa99babaff160ab0aaa7e2ccb0ce09c6b32fe08cbc474694375aba703fadbfa31cf685b30a11c57f3cf4edd321e57d3ae6ebb1133c8260e75b9224fa47a2bb205249add2e2e62f817491482ae152322be0900355cdcc8d42a98f82e961a0dc6f537b7b410eff105f59673bfb787bf042aa071f7af68d944d27371c64160fe9382772372516c230c1f45c0d6b6cca7f274b394da9402d3eafdf733994ec58ab22d71829a98399574d4b5908a447a5a681cb0dd50a31145311d92c22a16de1ead66a5499f2dceb4cae694772ce90762ef8336afec653aa9b1a1c4820b221136dfce80dce2ba920d88a530c9410d0a4e0358a3a11052e58dd73b0b179ef8f56fe3b5a2d117a73a0c38a1392b6938e9782e0d86456ee4884e3c39d4d75813f13633bc79baa07c0d2d555afbf207f52b7dca126d015aa2b9873b3eb065e90b9b065a5373fe1fb1b20d594327d19fba56cb81e7b6696605ffa56eba3c27a438697cc21b201fd7e09f18deea1b3ea2f0d1edc02df0e20396a145412cd6b13c32d2e605641c948b714aec30c0649dc44143511f35ab0fd5dd64c34d06fe86f3836dfe9edeb7f08cfc3bd40956826356242191f99f53473f32b0cc0cf9321d6c92a112e8db90b86ee9e87cc32d0343db01e32ce9eb782cb24efbbbeb440fe929e8f2bf8dfb1550a3a2e742e8b455a3e5730e9e6a7a9824d17acc0f72a7f67eae0f0970f8bde46dcdefaed3047cf807e7f00a42e5fd11d40f5e98533d7574425b7d2bc3b3845c443008b58980e768e464e17cc6f6b3939eee52f713963d07d8c4abf02448ef0b889c9671e2f8a436ddeeffcca7176e9bf9d1005ecd377f2fa67c23ed1f137e60bf46018a8bd613d038e883704fc26e798969df35ec7bbc6a4fe46d8910bd82fa3cded265d0a3b6d399e4251e4d8233daa21b5812fded6536198ff13aa5a1cd46a5b9a17a4ddc1d9f85544d1d1cc16f3df858038c8e071a11a7e157a85a6a8dc47e88d75e7009a8b26fdb73f33a2a70f1e0c259f8f9533b9b8f9af9288b7274f21baeec78d396f8bacdcc22471207d9b4efccd3fedc5c5a2214ff5e51c553f35e21ae696fe51e8df733a8e06f50f419e599e9f9e4b37ce643fc810faaa47989771509d69a110ac916261427026369a21263ac4460fb4f708f8ae28599856db7cb6a43ac8e03d64a9609807e76c5f312b9d1863bfa304e8953647648b4f4ab0ed995e":"4109cdbec3240ad74cc6c37f39300f70fede16e21efc77f7865998714aad0b5e"

generic multi step SHA-384 Test Vector NIST CAVS #1
depends_on:MBEDTLS_MD_CAN_SHA384
md_hex_multi:MBEDTLS_MD_SHA384:"":"38b060a751ac96384cd9327eb1b1e36a21fdb71114be07434c0cc7bf63f6e1da274edebfe76f65fbd51ad2f14898b95b"

generic multi step SHA-384 Test Vector NIST CAVS #2
depends_on:MBEDTLS_MD_CAN_SHA384
md_hex_multi:MBEDTLS_MD_SHA384:"ab":"fb94d5be118865f6fcbc978b825da82cff188faec2f66cb84b2537d74b4938469854b0ca89e66fa2e182834736629f3d"

generic multi step SHA-384 Test Vector NIST CAVS #3
depends_on:MBEDTLS_MD_CAN_SHA384
md_hex_multi:MBEDTLS_MD_SHA384:"7c27":"3d80be467df86d63abb9ea1d3f9cb39cd19890e7f2c53a6200bedc5006842b35e820dc4e0ca90ca9b97ab23ef07080fc"

generic multi step SHA-384 Test Vector NIST CAVS #4
depends_on:MBEDTLS_MD_CAN_SHA384
md_hex_multi:MBEDTLS_MD_SHA384:"31f5ca":"78d54b943421fdf7ba90a7fb9637c2073aa480454bd841d39ff72f4511fc21fb67797b652c0c823229342873d3bef955"

generic multi step SHA-384 Test Vector NIST CAVS #5
depends_on:MBEDTLS_MD_CAN_SHA384
md_hex_multi:MBEDTLS_MD_SHA384:"7bdee3f8":"8bdafba0777ee446c3431c2d7b1fbb631089f71d2ca417abc1d230e1aba64ec2f1c187474a6f4077d372c14ad407f99a"

generic multi step SHA-384 Test Vector NIST CAVS #6
depends_on:MBEDTLS_MD_CAN_SHA384
md_hex_multi:MBEDTLS_MD_SHA384:"8f05604915":"504e414bf1db1060f14c8c799e25b1e0c4dcf1504ebbd129998f0ae283e6de86e0d3c7e879c73ec3b1836c3ee89c2649"

generic multi step SHA-384 Test Vector NIST CAVS #7
depends_on:MBEDTLS_MD_CAN_SHA384
md_hex_multi:MBEDTLS_MD_SHA384:"665da6eda214":"4c022f112010908848312f8b8f1072625fd5c105399d562ea1d56130619a7eac8dfc3748fd05ee37e4b690be9daa9980"

generic multi step SHA-384 Test Vector NIST CAVS #8
depends_on:MBEDTLS_MD_CAN_SHA384
md_hex_multi:MBEDTLS_MD_SHA384:"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":"cba9e3eb12a6f83db11e8a6ff40d1049854ee094416bc527fea931d8585428a8ed6242ce81f6769b36e2123a5c23483e"

generic multi step SHA-512 Test Vector NIST CAVS #1
depends_on:MBEDTLS_MD_CAN_SHA512
md_hex_multi:MBEDTLS_MD_SHA512:"":"cf83e1357eefb8bdf1542850d66d8007d620e4050b5715dc83f4a921d36ce9ce47d0d13c5d85f2b0ff8318d2877eec2f63b931bd47417a81a538327af927da3e"

generic multi step SHA-512 Test Vector NIST CAVS #2
depends_on:MBEDTLS_MD_CAN_SHA512
md_hex_multi:MBEDTLS_MD_SHA512:"8f":"e4cd2d19931b5aad9c920f45f56f6ce34e3d38c6d319a6e11d0588ab8b838576d6ce6d68eea7c830de66e2bd96458bfa7aafbcbec981d4ed040498c3dd95f22a"

generic multi step SHA-512 Test Vector NIST CAVS #3
depends_on:MBEDTLS_MD_CAN_SHA512
md_hex_multi:MBEDTLS_MD_SHA512:"e724":"7dbb520221a70287b23dbcf62bfc1b73136d858e86266732a7fffa875ecaa2c1b8f673b5c065d360c563a7b9539349f5f59bef8c0c593f9587e3cd50bb26a231"

generic multi step SHA-512 Test Vector NIST CAVS #4
depends_on:MBEDTLS_MD_CAN_SHA512
md_hex_multi:MBEDTLS_MD_SHA512:"de4c90":"33ce98281045a5c4c9df0363d8196f1d7dfcd5ee46ac89776fd8a4344c12f123a66788af5bd41ceff1941aa5637654b4064c88c14e00465ab79a2fc6c97e1014"

generic multi step SHA-512 Test Vector NIST CAVS #5
depends_on:MBEDTLS_MD_CAN_SHA512
md_hex_multi:MBEDTLS_MD_SHA512:"a801e94b":"dadb1b5a27f9fece8d86adb2a51879beb1787ff28f4e8ce162cad7fee0f942efcabbf738bc6f797fc7cc79a3a75048cd4c82ca0757a324695bfb19a557e56e2f"

generic multi step SHA-512 Test Vector NIST CAVS #6
depends_on:MBEDTLS_MD_CAN_SHA512
md_hex_multi:MBEDTLS_MD_SHA512:"94390d3502":"b6175c4c4cccf69e0ce5f0312010886ea6b34d43673f942ae42483f9cbb7da817de4e11b5d58e25a3d9bd721a22cdffe1c40411cc45df1911fa5506129b69297"

generic multi step SHA-512 Test Vector NIST CAVS #7
depends_on:MBEDTLS_MD_CAN_SHA512
md_hex_multi:MBEDTLS_MD_SHA512:"49297dd63e5f":"1fcc1e6f6870859d11649f5e5336a9cd16329c029baf04d5a6edf257889a2e9522b497dd656bb402da461307c4ee382e2e89380c8e6e6e7697f1e439f650fa94"

generic multi step SHA-512 Test Vector NIST CAVS #8
depends_on:MBEDTLS_MD_CAN_SHA512
md_hex_multi:MBEDTLS_MD_SHA512:"990d1ae71a62d7bda9bfdaa1762a68d296eee72a4cd946f287a898fbabc002ea941fd8d4d991030b4d27a637cce501a834bb95eab1b7889a3e784c7968e67cbf552006b206b68f76d9191327524fcc251aeb56af483d10b4e0c6c5e599ee8c0fe4faeca8293844a8547c6a9a90d093f2526873a19ad4a5e776794c68c742fb834793d2dfcb7fea46c63af4b70fd11cb6e41834e72ee40edb067b292a794990c288d5007e73f349fb383af6a756b8301ad6e5e0aa8cd614399bb3a452376b1575afa6bdaeaafc286cb064bb91edef97c632b6c1113d107fa93a0905098a105043c2f05397f702514439a08a9e5ddc196100721d45c8fc17d2ed659376f8a00bd5cb9a0860e26d8a29d8d6aaf52de97e9346033d6db501a35dbbaf97c20b830cd2d18c2532f3a59cc497ee64c0e57d8d060e5069b28d86edf1adcf59144b221ce3ddaef134b3124fbc7dd000240eff0f5f5f41e83cd7f5bb37c9ae21953fe302b0f6e8b68fa91c6ab99265c64b2fd9cd4942be04321bb5d6d71932376c6f2f88e02422ba6a5e2cb765df93fd5dd0728c6abdaf03bce22e0678a544e2c3636f741b6f4447ee58a8fc656b43ef817932176adbfc2e04b2c812c273cd6cbfa4098f0be036a34221fa02643f5ee2e0b38135f2a18ecd2f16ebc45f8eb31b8ab967a1567ee016904188910861ca1fa205c7adaa194b286893ffe2f4fbe0384c2aef72a4522aeafd3ebc71f9db71eeeef86c48394a1c86d5b36c352cc33a0a2c800bc99e62fd65b3a2fd69e0b53996ec13d8ce483ce9319efd9a85acefabdb5342226febb83fd1daf4b24265f50c61c6de74077ef89b6fecf9f29a1f871af1e9f89b2d345cda7499bd45c42fa5d195a1e1a6ba84851889e730da3b2b916e96152ae0c92154b49719841db7e7cc707ba8a5d7b101eb4ac7b629bb327817910fff61580b59aab78182d1a2e33473d05b00b170b29e331870826cfe45af206aa7d0246bbd8566ca7cfb2d3c10bfa1db7dd48dd786036469ce7282093d78b5e1a5b0fc81a54c8ed4ceac1e5305305e78284ac276f5d7862727aff246e17addde50c670028d572cbfc0be2e4f8b2eb28fa68ad7b4c6c2a239c460441bfb5ea049f23b08563b4e47729a59e5986a61a6093dbd54f8c36ebe87edae01f251cb060ad1364ce677d7e8d5a4a4ca966a7241cc360bc2acb280e5f9e9c1b032ad6a180a35e0c5180b9d16d026c865b252098cc1d99ba7375ca31c7702c0d943d5e3dd2f6861fa55bd46d94b67ed3e52eccd8dd06d968e01897d6de97ed3058d91dd":"8e4bc6f8b8c60fe4d68c61d9b159c8693c3151c46749af58da228442d927f23359bd6ccd6c2ec8fa3f00a86cecbfa728e1ad60b821ed22fcd309ba91a4138bc9"

generic SHA1 Hash file #1
depends_on:MBEDTLS_MD_CAN_SHA1
mbedtls_md_file:MBEDTLS_MD_SHA1:"../framework/data_files/hash_file_1":"d21c965b1e768bd7a6aa6869f5f821901d255f9f"

generic SHA1 Hash file #2
depends_on:MBEDTLS_MD_CAN_SHA1
mbedtls_md_file:MBEDTLS_MD_SHA1:"../framework/data_files/hash_file_2":"353f34271f2aef49d23a8913d4a6bd82b2cecdc6"

generic SHA1 Hash file #3
depends_on:MBEDTLS_MD_CAN_SHA1
mbedtls_md_file:MBEDTLS_MD_SHA1:"../framework/data_files/hash_file_3":"93640ed592076328096270c756db2fba9c486b35"

generic SHA1 Hash file #4
depends_on:MBEDTLS_MD_CAN_SHA1
mbedtls_md_file:MBEDTLS_MD_SHA1:"../framework/data_files/hash_file_4":"da39a3ee5e6b4b0d3255bfef95601890afd80709"

generic SHA-224 Hash file #1
depends_on:MBEDTLS_MD_CAN_SHA224
mbedtls_md_file:MBEDTLS_MD_SHA224:"../framework/data_files/hash_file_1":"8606da018870f0c16834a21bc3385704cb1683b9dbab04c5ddb90a48"

generic SHA-224 Hash file #2
depends_on:MBEDTLS_MD_CAN_SHA224
mbedtls_md_file:MBEDTLS_MD_SHA224:"../framework/data_files/hash_file_2":"733b2ab97b6f63f2e29b9a2089756d81e14c93fe4cc9615c0d5e8a03"

generic SHA-224 Hash file #3
depends_on:MBEDTLS_MD_CAN_SHA224
mbedtls_md_file:MBEDTLS_MD_SHA224:"../framework/data_files/hash_file_3":"e1df95867580e2cc2100e9565bf9c2e42c24fe5250c19efe33d1c4fe"

generic SHA-224 Hash file #4
depends_on:MBEDTLS_MD_CAN_SHA224
mbedtls_md_file:MBEDTLS_MD_SHA224:"../framework/data_files/hash_file_4":"d14a028c2a3a2bc9476102bb288234c415a2b01f828ea62ac5b3e42f"

generic SHA-256 Hash file #1
depends_on:MBEDTLS_MD_CAN_SHA256
mbedtls_md_file:MBEDTLS_MD_SHA256:"../framework/data_files/hash_file_1":"975d0c620d3936886f8a3665e585a3e84aa0501f4225bf53029710242823e391"

generic SHA-256 Hash file #2
depends_on:MBEDTLS_MD_CAN_SHA256
mbedtls_md_file:MBEDTLS_MD_SHA256:"../framework/data_files/hash_file_2":"11fcbf1baa36ca45745f10cc5467aee86f066f80ba2c46806d876bf783022ad2"

generic SHA-256 Hash file #3
depends_on:MBEDTLS_MD_CAN_SHA256
mbedtls_md_file:MBEDTLS_MD_SHA256:"../framework/data_files/hash_file_3":"9ae4b369f9f4f03b86505b46a5469542e00aaff7cf7417a71af6d6d0aba3b70c"

generic SHA-256 Hash file #4
depends_on:MBEDTLS_MD_CAN_SHA256
mbedtls_md_file:MBEDTLS_MD_SHA256:"../framework/data_files/hash_file_4":"e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855"

generic SHA-384 Hash file #1
depends_on:MBEDTLS_MD_CAN_SHA384
mbedtls_md_file:MBEDTLS_MD_SHA384:"../framework/data_files/hash_file_1":"e0a3e6259d6378001b54ef82f5dd087009c5fad86d8db226a9fe1d14ecbe33a6fc916e3a4b16f5f286424de15d5a8e0e"

generic SHA-384 Hash file #2
depends_on:MBEDTLS_MD_CAN_SHA384
mbedtls_md_file:MBEDTLS_MD_SHA384:"../framework/data_files/hash_file_2":"eff727afc8495c92e2f370f97a317f93c3350324b0646b0f0e264708b3c97d3d332d3c5390e1e47130f5c92f1ef4b9cf"

generic SHA-384 Hash file #3
depends_on:MBEDTLS_MD_CAN_SHA384
mbedtls_md_file:MBEDTLS_MD_SHA384:"../framework/data_files/hash_file_3":"6fc10ebda96a1ccf61777cac72f6034f92533d42052a4bf9f9d929c672973c71e5aeb1213268043c21527ac0f7f349c4"

generic SHA-384 Hash file #4
depends_on:MBEDTLS_MD_CAN_SHA384
mbedtls_md_file:MBEDTLS_MD_SHA384:"../framework/data_files/hash_file_4":"38b060a751ac96384cd9327eb1b1e36a21fdb71114be07434c0cc7bf63f6e1da274edebfe76f65fbd51ad2f14898b95b"

generic SHA-512 Hash file #1
depends_on:MBEDTLS_MD_CAN_SHA512
mbedtls_md_file:MBEDTLS_MD_SHA512:"../framework/data_files/hash_file_1":"d8207a2e1ff2b424f2c4163fe1b723c9bd42e464061eb411e8df730bcd24a7ab3956a6f3ff044a52eb2d262f9e4ca6b524092b544ab78f14d6f9c4cc8ddf335a"

generic SHA-512 Hash file #2
depends_on:MBEDTLS_MD_CAN_SHA512
mbedtls_md_file:MBEDTLS_MD_SHA512:"../framework/data_files/hash_file_2":"ecbb7f0ed8a702b49f16ad3088bcc06ea93451912a7187db15f64d93517b09630b039293aed418d4a00695777b758b1f381548c2fd7b92ce5ed996b32c8734e7"

generic SHA-512 Hash file #3
depends_on:MBEDTLS_MD_CAN_SHA512
mbedtls_md_file:MBEDTLS_MD_SHA512:"../framework/data_files/hash_file_3":"7ccc9b2da71ffde9966c3ce44d7f20945fccf33b1fade4da152b021f1afcc7293382944aa6c09eac67af25f22026758e2bf6bed86ae2a43592677ee50f8eea41"

generic SHA-512 Hash file #4
depends_on:MBEDTLS_MD_CAN_SHA512
mbedtls_md_file:MBEDTLS_MD_SHA512:"../framework/data_files/hash_file_4":"cf83e1357eefb8bdf1542850d66d8007d620e4050b5715dc83f4a921d36ce9ce47d0d13c5d85f2b0ff8318d2877eec2f63b931bd47417a81a538327af927da3e"
