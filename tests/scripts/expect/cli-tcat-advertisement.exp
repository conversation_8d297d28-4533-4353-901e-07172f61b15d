#!/usr/bin/expect -f
#
#  Copyright (c) 2022, The OpenThread Authors.
#  All rights reserved.
#
#  Redistribution and use in source and binary forms, with or without
#  modification, are permitted provided that the following conditions are met:
#  1. Redistributions of source code must retain the above copyright
#     notice, this list of conditions and the following disclaimer.
#  2. Redistributions in binary form must reproduce the above copyright
#     notice, this list of conditions and the following disclaimer in the
#     documentation and/or other materials provided with the distribution.
#  3. Neither the name of the copyright holder nor the
#     names of its contributors may be used to endorse or promote products
#     derived from this software without specific prior written permission.
#
#  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
#  AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
#  IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
#  ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE
#  LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
#  CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
#  SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
#  INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
#  CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
#  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
#  POSSIBILITY OF SUCH DAMAGE.
#

source "tests/scripts/expect/_common.exp"

spawn_node 1 "cli"

send "tcat advid ianapen f378\n"
expect_line "Done"

send "tcat advid\n"
expect_line "type ianapen, value: f378"
expect_line "Done"

send "tcat advid ianapen f378aabb\n"
expect_line "Done"

send "tcat advid\n"
expect_line "type ianapen, value: f378aabb"
expect_line "Done"

send "tcat advid oui24 f378aa\n"
expect_line "Done"

send "tcat advid\n"
expect_line "type oui24, value: f378aa"
expect_line "Done"

send "tcat advid oui36 f378aabbcc\n"
expect_line "Done"

send "tcat advid\n"
expect_line "type oui36, value: f378aabbcc"
expect_line "Done"

send "tcat advid discriminator f378aabbdd\n"
expect_line "Done"

send "tcat advid\n"
expect_line "type discriminator, value: f378aabbdd"
expect_line "Done"

send "tcat advid clear\n"
expect_line "Done"

send "tcat advid\n"
expect_line "Done"

send "tcat devid\n"
expect_line "Done"

send "tcat devid aaaa\n"
expect_line "aaaa"
expect_line "Done"

send "tcat devid\n"
expect_line "aaaa"
expect_line "Done"

send "tcat devid clear\n"
expect_line "Done"

send "tcat devid\n"
expect_line "Done"

dispose_all
