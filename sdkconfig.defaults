# ESP32-S3 Configuration
CONFIG_IDF_TARGET="esp32s3"

# PSRAM Configuration
CONFIG_SPIRAM=y
CONFIG_SPIRAM_MODE_OCT=y
CONFIG_SPIRAM_SPEED_80M=y
CONFIG_SPIRAM_USE_MALLOC=y
CONFIG_SPIRAM_MALLOC_ALWAYSINTERNAL=16384
CONFIG_SPIRAM_MALLOC_RESERVE_INTERNAL=32768

# Camera Configuration
CONFIG_CAMERA_CORE0=y
CONFIG_CAMERA_DMA_BUFFER_SIZE_MAX=32768

# I2C Configuration
CONFIG_I2C_ENABLE_DEBUG_LOG=y

# Log Configuration
CONFIG_LOG_DEFAULT_LEVEL_INFO=y
CONFIG_LOG_MAXIMUM_LEVEL_VERBOSE=y

# FreeRTOS Configuration
CONFIG_FREERTOS_HZ=1000

# Component Configuration
CONFIG_ESP32_CAMERA_ENABLE=y
