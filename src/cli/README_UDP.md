# OpenThread CLI - UDP Example

The OpenThread UDP APIs may be invoked via the OpenThread CLI.

## Quick Start

### Form Network

Form a network with at least two devices.

### Node 1

On node 1, open and bind the example UDP socket.

```bash
> udp open
> udp bind :: 1234
```

The `::` specifies the IPv6 Unspecified Address.

### Node 2

On node 2, open the example UDP socket and send a simple message.

```bash
> udp open
> udp send fdde:ad00:beef:0:bb1:ebd6:ad10:f33 1234 hello
```

### Result

On node 1, you should see a print out similar to below:

```bash
5 bytes from fdde:ad00:beef:0:dac3:6792:e2e:90d8 49153 hello
```

## Command List

- [help](#help)
- [bind](#bind-netif-ip-port)
- [close](#close)
- [connect](#connect-ip-port)
- [linksecurity](#linksecurity)
- [open](#open)
- [send](#send-ip-port-message)

## Command Details

### help

List the UDP CLI commands.

```bash
> udp help
help
bind
close
connect
open
send
Done
```

### bind [netif] \<ip\> \<port\>

Assigns a name (i.e. IPv6 address and port) to the example socket.

- netif: the network interface to bind to.
  - not specified: Thread network interface.
  - `-u`: unspecified network interface.
  - `-b`: Backbone network interface.
- ip: the unicast IPv6 address or the unspecified IPv6 address (`::`).
- port: the UDP port

```bash
> udp bind :: 1234
Done
> udp bind -u :: 1234
Done
> udp bind -b :: 1234
Done
```

> Note: to receive datagrams sent to a multicast IPv6 address, the unspecified IPv6 address must be used. Using a multicast address for the \<ip\> argument is not supported. Also, the node must subscribe to the multicast group using `ipmaddr add` before it can receive UDP multicast.

### close

Closes the example socket.

```bash
> udp close
Done
```

### connect \<ip\> \<port\>

Specifies the peer with which the socket is to be associated.

- ip: the peer's IP address.
- port: the peer's UDP port.

```bash
> udp connect fdde:ad00:beef:0:bb1:ebd6:ad10:f33 1234
Done
```

The address can be an IPv4 address, which will be synthesized to an IPv6 address using the preferred NAT64 prefix from the network data.

> Note: The command will return `InvalidState` when the preferred NAT64 prefix is unavailable.

```bash
> udp connect ********** 1234
Connecting to synthesized IPv6 address: fdde:ad00:beef:2:0:0:ac11:1
Done
```

### linksecurity

Indicates whether the link security is enabled or disabled.

```bash
> udp linksecurity
Enabled
Done
```

### linksecurity enable

Enable link security.

```bash
> udp linksecurity enable
Done
```

### linksecurity disable

Disable link security.

```bash
> udp linksecurity disable
Done
```

### open

Opens the example socket.

```bash
> udp open
Done
```

### send \<ip\> \<port\> \<message\>

Send a UDP message.

- ip: the destination address.
- port: the UDP destination port.
- message: the message to send.

```bash
> udp send fdde:ad00:beef:0:bb1:ebd6:ad10:f33 1234 hello
Done
```

The address can be an IPv4 address, which will be synthesized to an IPv6 address using the preferred NAT64 prefix from the network data.

> Note: The command will return `InvalidState` when the preferred NAT64 prefix is unavailable.

```bash
> udp send ********** 1234 hello
Sending to synthesized IPv6 address: fdde:ad00:beef:2:0:0:ac11:1
Done
```

### send \<ip\> \<port\> \<type\> \<value\>

Send a few bytes over UDP.

- ip: the IPv6 destination address.
- port: the UDP destination port.
- type: the type of the message:
  - `-t`: text payload in the `value`, same as without specifying the type.
  - `-s`: autogenerated payload with specified length indicated in the `value`.
  - `-x`: binary data in hexadecimal representation in the `value`.

```bash
> udp send fdde:ad00:beef:0:bb1:ebd6:ad10:f33 1234 -t hello
Done

> udp send fdde:ad00:beef:0:bb1:ebd6:ad10:f33 1234 -x 68656c6c6f
Done

> udp send fdde:ad00:beef:0:bb1:ebd6:ad10:f33 1234 -s 800
Done

```

### send \<message\>

Send a UDP message on a connected socket.

- message: the message to send.

```bash
> udp send hello
Done
```

### send \<type\> \<value\>

Send a few bytes over UDP.

- type: the type of the message:
  - `-t`: text payload in the `value`, same as without specifying the type.
  - `-s`: autogenerated payload with specified length indicated in the `value`.
  - `-x`: binary data in hexadecimal representation in the `value`.

```bash
> udp send -t hello
Done

> udp send -x 68656c6c6f
Done

> udp send -s 800
Done
```
