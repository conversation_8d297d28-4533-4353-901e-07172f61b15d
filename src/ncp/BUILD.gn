#  Copyright (c) 2020, The OpenThread Authors.
#  All rights reserved.
#
#  Redistribution and use in source and binary forms, with or without
#  modification, are permitted provided that the following conditions are met:
#  1. Redistributions of source code must retain the above copyright
#     notice, this list of conditions and the following disclaimer.
#  2. Redistributions in binary form must reproduce the above copyright
#     notice, this list of conditions and the following disclaimer in the
#     documentation and/or other materials provided with the distribution.
#  3. Neither the name of the copyright holder nor the
#     names of its contributors may be used to endorse or promote products
#     derived from this software without specific prior written permission.
#
#  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
#  AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
#  IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
#  ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE
#  LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, <PERSON>XEMPLARY, OR
#  CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
#  SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
#  INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
#  CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
#  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
#  POSSIBILITY OF SUCH DAMAGE.
#

import("../../etc/gn/openthread.gni")

openthread_ncp_sources = [
  "changed_props_set.cpp",
  "changed_props_set.hpp",
  "example_vendor_hook.cpp",
  "multipan_platform.cpp",
  "ncp_base.cpp",
  "ncp_base.hpp",
  "ncp_base_dispatcher.cpp",
  "ncp_base_ftd.cpp",
  "ncp_base_mtd.cpp",
  "ncp_base_radio.cpp",
  "ncp_config.h",
  "ncp_hdlc.cpp",
  "ncp_hdlc.hpp",
  "ncp_spi.cpp",
  "ncp_spi.hpp",
]

config("ncp_config") {
  include_dirs = [ ".." ]
}

static_library("libopenthread-ncp-ftd") {
  sources = openthread_ncp_sources
  public_deps = [
    "../core:libopenthread-ftd",
    "../lib/spinel:libopenthread-spinel-ncp",
  ]
  public_configs = [ ":ncp_config" ]
  visibility = [ "../../*" ]
}

static_library("libopenthread-ncp-mtd") {
  sources = openthread_ncp_sources
  public_deps = [
    "../core:libopenthread-mtd",
    "../lib/spinel:libopenthread-spinel-ncp",
  ]
  public_configs = [ ":ncp_config" ]
  visibility = [ "../../*" ]
}

static_library("libopenthread-rcp") {
  sources = openthread_ncp_sources
  public_deps = [
    "../core:libopenthread-radio",
    "../lib/spinel:libopenthread-spinel-rcp",
  ]
  public_configs = [ ":ncp_config" ]
  visibility = [ "../../*" ]
}
