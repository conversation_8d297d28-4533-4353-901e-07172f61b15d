# Security Policy

## Supported Versions

Security is of the highest importance and all security vulnerabilities or suspected security vulnerabilities should be reported to cjson team privately, to minimize attacks against current users of cjson before they are fixed. Vulnerabilities will be investigated and patched on the next patch (or minor) release as soon as possible. This information could be kept entirely internal to the project.

## Reporting a Vulnerability

If you know of a publicly disclosed security vulnerability for cjson, please <NAME_EMAIL> and <EMAIL> to inform the cjson Team.

IMPORTANT: Do not file public issues on GitHub for security vulnerabilities.
