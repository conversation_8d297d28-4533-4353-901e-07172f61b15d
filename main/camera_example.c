#include <stdio.h>
#include <string.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "esp_log.h"
#include "esp_system.h"
#include "nvs_flash.h"

#include "gc0308_camera.h"
#include "display_driver.h"

static const char *TAG = "CAMERA_EXAMPLE";

/**
 * @brief 摄像头测试任务
 */
void camera_test_task(void *pvParameters)
{
    ESP_LOGI(TAG, "Starting camera test...");
    
    // 初始化摄像头
    esp_err_t ret = gc0308_camera_init(NULL);  // 使用默认配置
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Camera initialization failed: %s", esp_err_to_name(ret));
        vTaskDelete(NULL);
        return;
    }
    
    // 启动摄像头
    ret = gc0308_camera_start();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Camera start failed: %s", esp_err_to_name(ret));
        gc0308_camera_deinit();
        vTaskDelete(NULL);
        return;
    }
    
    ESP_LOGI(TAG, "Camera initialized and started successfully");
    
    // 连续捕获图像
    int capture_count = 0;
    while (1) {
        // 捕获一帧图像
        camera_fb_t *fb = gc0308_camera_capture();
        if (fb != NULL) {
            capture_count++;
            ESP_LOGI(TAG, "Captured frame %d: %dx%d, size: %zu bytes, format: %d", 
                     capture_count, fb->width, fb->height, fb->len, fb->format);
            
            // 这里可以处理图像数据
            // 例如：保存到文件、通过网络传输等
            
            // 释放帧缓冲区
            gc0308_camera_fb_return(fb);
        } else {
            ESP_LOGE(TAG, "Failed to capture frame");
        }
        
        // 等待1秒后捕获下一帧
        vTaskDelay(pdMS_TO_TICKS(1000));
        
        // 测试10帧后停止
        if (capture_count >= 10) {
            break;
        }
    }
    
    ESP_LOGI(TAG, "Camera test completed, captured %d frames", capture_count);
    
    // 清理资源
    gc0308_camera_deinit();
    vTaskDelete(NULL);
}

/**
 * @brief 摄像头电源控制测试函数
 */
void test_camera_power_control(void)
{
    ESP_LOGI(TAG, "Testing camera power control...");

    // 初始化PCA9557 (通过display_driver)
    pca9557_init();

    // 测试摄像头电源控制
    for (int i = 0; i < 3; i++) {
        ESP_LOGI(TAG, "Turning camera power OFF (PWDN=1)");
        dvp_pwdn(1);  // 关闭摄像头电源
        vTaskDelay(pdMS_TO_TICKS(1000));

        ESP_LOGI(TAG, "Turning camera power ON (PWDN=0)");
        dvp_pwdn(0);  // 开启摄像头电源
        vTaskDelay(pdMS_TO_TICKS(1000));
    }

    ESP_LOGI(TAG, "Camera power control test completed");
}

/**
 * @brief 应用程序入口点
 */
void app_main(void)
{
    ESP_LOGI(TAG, "ESP32-S3 GC0308 Camera Example");
    ESP_LOGI(TAG, "ESP-IDF Version: %s", esp_get_idf_version());
    
    // 初始化NVS
    esp_err_t ret = nvs_flash_init();
    if (ret == ESP_ERR_NVS_NO_FREE_PAGES || ret == ESP_ERR_NVS_NEW_VERSION_FOUND) {
        ESP_ERROR_CHECK(nvs_flash_erase());
        ret = nvs_flash_init();
    }
    ESP_ERROR_CHECK(ret);
    
    // 测试摄像头电源控制
    test_camera_power_control();
    
    // 等待一段时间
    vTaskDelay(pdMS_TO_TICKS(2000));
    
    // 创建摄像头测试任务
    xTaskCreate(camera_test_task, "camera_test", 8192, NULL, 5, NULL);
    
    ESP_LOGI(TAG, "Application started");
}
