#include <stdio.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "esp_log.h"
#include "esp_system.h"
#include "nvs_flash.h"

#include "gc0308_camera.h"
#include "display_driver.h"

static const char *TAG = "SIMPLE_CAMERA_TEST";

/**
 * @brief 简单的摄像头测试
 */
void simple_camera_test(void)
{
    ESP_LOGI(TAG, "Starting simple camera test...");
    
    // 1. 初始化摄像头
    esp_err_t ret = gc0308_camera_init(NULL);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Camera initialization failed: %s", esp_err_to_name(ret));
        return;
    }
    
    ESP_LOGI(TAG, "Camera initialized successfully");
    
    // 2. 启动摄像头
    ret = gc0308_camera_start();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Camera start failed: %s", esp_err_to_name(ret));
        gc0308_camera_deinit();
        return;
    }
    
    ESP_LOGI(TAG, "Camera started, beginning capture test...");
    
    // 3. 捕获几帧图像进行测试
    for (int i = 0; i < 5; i++) {
        ESP_LOGI(TAG, "Capturing frame %d...", i + 1);
        
        camera_fb_t *fb = gc0308_camera_capture();
        if (fb != NULL) {
            ESP_LOGI(TAG, "Frame %d captured successfully:", i + 1);
            ESP_LOGI(TAG, "  Resolution: %dx%d", fb->width, fb->height);
            ESP_LOGI(TAG, "  Size: %zu bytes", fb->len);
            ESP_LOGI(TAG, "  Format: %d", fb->format);
            ESP_LOGI(TAG, "  Timestamp: %lld", fb->timestamp.tv_sec);
            
            // 释放帧缓冲区
            gc0308_camera_fb_return(fb);
        } else {
            ESP_LOGE(TAG, "Failed to capture frame %d", i + 1);
        }
        
        // 等待1秒
        vTaskDelay(pdMS_TO_TICKS(1000));
    }
    
    ESP_LOGI(TAG, "Capture test completed");
    
    // 4. 测试摄像头停止和重启
    ESP_LOGI(TAG, "Testing camera stop/start...");
    
    gc0308_camera_stop();
    vTaskDelay(pdMS_TO_TICKS(2000));
    
    gc0308_camera_start();
    vTaskDelay(pdMS_TO_TICKS(1000));
    
    // 再捕获一帧验证重启成功
    camera_fb_t *fb = gc0308_camera_capture();
    if (fb != NULL) {
        ESP_LOGI(TAG, "Camera restart successful, captured %dx%d frame", 
                 fb->width, fb->height);
        gc0308_camera_fb_return(fb);
    } else {
        ESP_LOGW(TAG, "Camera restart test failed");
    }
    
    // 5. 清理资源
    gc0308_camera_deinit();
    ESP_LOGI(TAG, "Simple camera test completed");
}

/**
 * @brief 测试摄像头电源控制
 */
void test_camera_power_cycle(void)
{
    ESP_LOGI(TAG, "Testing camera power cycle...");
    
    // 初始化PCA9557
    pca9557_init();
    
    for (int i = 0; i < 3; i++) {
        ESP_LOGI(TAG, "Power cycle %d: OFF", i + 1);
        dvp_pwdn(1);  // 关闭电源
        vTaskDelay(pdMS_TO_TICKS(500));
        
        ESP_LOGI(TAG, "Power cycle %d: ON", i + 1);
        dvp_pwdn(0);  // 开启电源
        vTaskDelay(pdMS_TO_TICKS(500));
    }
    
    ESP_LOGI(TAG, "Power cycle test completed");
}

/**
 * @brief 应用程序入口点
 */
void app_main_simple_test(void)
{
    ESP_LOGI(TAG, "ESP32-S3 Simple Camera Test");
    ESP_LOGI(TAG, "ESP-IDF Version: %s", esp_get_idf_version());
    
    // 初始化NVS
    esp_err_t ret = nvs_flash_init();
    if (ret == ESP_ERR_NVS_NO_FREE_PAGES || ret == ESP_ERR_NVS_NEW_VERSION_FOUND) {
        ESP_ERROR_CHECK(nvs_flash_erase());
        ret = nvs_flash_init();
    }
    ESP_ERROR_CHECK(ret);
    
    // 测试电源控制
    test_camera_power_cycle();
    
    // 等待一段时间
    vTaskDelay(pdMS_TO_TICKS(1000));
    
    // 运行摄像头测试
    simple_camera_test();
    
    ESP_LOGI(TAG, "All tests completed");
    
    // 保持运行
    while (1) {
        vTaskDelay(pdMS_TO_TICKS(10000));
        ESP_LOGI(TAG, "System running...");
    }
}
