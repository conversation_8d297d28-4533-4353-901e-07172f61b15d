#include <stdio.h>
#include <string.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/queue.h"
#include "esp_log.h"
#include "esp_system.h"
#include "esp_timer.h"

#include "gc0308_camera.h"
#include "display_driver.h"

static const char *TAG = "ADVANCED_CAMERA";

// 图像处理队列
static QueueHandle_t image_queue = NULL;

typedef struct {
    camera_fb_t *fb;
    int64_t timestamp;
} image_data_t;

/**
 * @brief 图像捕获任务
 */
void camera_capture_task(void *pvParameters)
{
    ESP_LOGI(TAG, "Camera capture task started");
    
    while (1) {
        // 捕获图像
        camera_fb_t *fb = gc0308_camera_capture();
        if (fb != NULL) {
            // 创建图像数据结构
            image_data_t *img_data = malloc(sizeof(image_data_t));
            if (img_data != NULL) {
                img_data->fb = fb;
                img_data->timestamp = esp_timer_get_time();
                
                // 发送到处理队列
                if (xQueueSend(image_queue, &img_data, pdMS_TO_TICKS(100)) != pdTRUE) {
                    ESP_LOGW(TAG, "Image queue full, dropping frame");
                    gc0308_camera_fb_return(fb);
                    free(img_data);
                }
            } else {
                ESP_LOGE(TAG, "Failed to allocate memory for image data");
                gc0308_camera_fb_return(fb);
            }
        }
        
        // 控制帧率 (约30fps)
        vTaskDelay(pdMS_TO_TICKS(33));
    }
}

/**
 * @brief 图像处理任务
 */
void image_process_task(void *pvParameters)
{
    ESP_LOGI(TAG, "Image processing task started");
    
    image_data_t *img_data;
    int processed_count = 0;
    
    while (1) {
        // 从队列获取图像数据
        if (xQueueReceive(image_queue, &img_data, portMAX_DELAY) == pdTRUE) {
            processed_count++;
            
            // 计算处理延迟
            int64_t processing_delay = esp_timer_get_time() - img_data->timestamp;
            
            ESP_LOGI(TAG, "Processing frame %d: %dx%d, size: %zu bytes, delay: %lld us",
                     processed_count,
                     img_data->fb->width,
                     img_data->fb->height,
                     img_data->fb->len,
                     processing_delay);
            
            // 这里可以添加图像处理算法
            // 例如：边缘检测、颜色识别、运动检测等
            process_image_data(img_data->fb);
            
            // 释放资源
            gc0308_camera_fb_return(img_data->fb);
            free(img_data);
        }
    }
}

/**
 * @brief 简单的图像处理示例
 */
void process_image_data(camera_fb_t *fb)
{
    if (fb->format == PIXFORMAT_RGB565) {
        // RGB565格式处理示例
        uint16_t *pixel_data = (uint16_t *)fb->buf;
        uint32_t pixel_count = fb->len / 2;
        
        // 计算平均亮度
        uint32_t total_brightness = 0;
        for (uint32_t i = 0; i < pixel_count; i++) {
            uint16_t pixel = pixel_data[i];
            // 提取RGB分量
            uint8_t r = (pixel >> 11) & 0x1F;
            uint8_t g = (pixel >> 5) & 0x3F;
            uint8_t b = pixel & 0x1F;
            
            // 计算亮度 (简化公式)
            uint8_t brightness = (r * 299 + g * 587 + b * 114) / 1000;
            total_brightness += brightness;
        }
        
        uint8_t avg_brightness = total_brightness / pixel_count;
        ESP_LOGD(TAG, "Average brightness: %d", avg_brightness);
        
        // 根据亮度调整摄像头参数
        adjust_camera_settings(avg_brightness);
    }
}

/**
 * @brief 根据图像亮度自动调整摄像头参数
 */
void adjust_camera_settings(uint8_t brightness)
{
    sensor_t *sensor = esp_camera_sensor_get();
    if (sensor == NULL) {
        return;
    }
    
    static uint8_t last_brightness = 128;
    static int adjustment_count = 0;
    
    // 避免频繁调整
    if (abs(brightness - last_brightness) < 10) {
        return;
    }
    
    adjustment_count++;
    ESP_LOGI(TAG, "Adjusting camera settings (count: %d), brightness: %d -> %d",
             adjustment_count, last_brightness, brightness);
    
    if (brightness < 80) {
        // 图像太暗，增加增益或延长曝光时间
        sensor->set_gainceiling(sensor, GAINCEILING_4X);
        ESP_LOGD(TAG, "Increased gain for low light");
    } else if (brightness > 180) {
        // 图像太亮，减少增益
        sensor->set_gainceiling(sensor, GAINCEILING_2X);
        ESP_LOGD(TAG, "Decreased gain for bright light");
    } else {
        // 正常亮度
        sensor->set_gainceiling(sensor, GAINCEILING_2X);
    }
    
    last_brightness = brightness;
}

/**
 * @brief 摄像头性能监控任务
 */
void camera_monitor_task(void *pvParameters)
{
    ESP_LOGI(TAG, "Camera monitor task started");
    
    TickType_t last_wake_time = xTaskGetTickCount();
    uint32_t frame_count = 0;
    uint32_t last_frame_count = 0;
    
    while (1) {
        // 每5秒统计一次性能
        vTaskDelayUntil(&last_wake_time, pdMS_TO_TICKS(5000));
        
        // 计算帧率
        uint32_t current_frames = frame_count;
        uint32_t fps = (current_frames - last_frame_count) / 5;
        last_frame_count = current_frames;
        
        // 获取队列状态
        UBaseType_t queue_waiting = uxQueueMessagesWaiting(image_queue);
        UBaseType_t queue_spaces = uxQueueSpacesAvailable(image_queue);
        
        // 获取内存使用情况
        uint32_t free_heap = esp_get_free_heap_size();
        uint32_t min_free_heap = esp_get_minimum_free_heap_size();
        
        ESP_LOGI(TAG, "Performance Stats:");
        ESP_LOGI(TAG, "  FPS: %lu", fps);
        ESP_LOGI(TAG, "  Queue: %lu/%lu", queue_waiting, queue_waiting + queue_spaces);
        ESP_LOGI(TAG, "  Heap: %lu bytes (min: %lu)", free_heap, min_free_heap);
        
        // 检查内存泄漏
        if (free_heap < min_free_heap + 10000) {
            ESP_LOGW(TAG, "Low memory warning!");
        }
    }
}

/**
 * @brief 高级摄像头应用初始化
 */
esp_err_t advanced_camera_app_init(void)
{
    esp_err_t ret;
    
    // 创建图像处理队列
    image_queue = xQueueCreate(5, sizeof(image_data_t*));
    if (image_queue == NULL) {
        ESP_LOGE(TAG, "Failed to create image queue");
        return ESP_FAIL;
    }
    
    // 初始化摄像头
    camera_config_t config = gc0308_get_default_config();
    config.frame_size = FRAMESIZE_QVGA;  // 320x240 for better performance
    config.pixel_format = PIXFORMAT_RGB565;
    config.fb_count = 3;  // 增加缓冲区数量
    
    ret = gc0308_camera_init(&config);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Camera initialization failed");
        vQueueDelete(image_queue);
        return ret;
    }
    
    // 启动摄像头
    ret = gc0308_camera_start();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Camera start failed");
        gc0308_camera_deinit();
        vQueueDelete(image_queue);
        return ret;
    }
    
    // 创建任务
    xTaskCreate(camera_capture_task, "cam_capture", 4096, NULL, 6, NULL);
    xTaskCreate(image_process_task, "img_process", 8192, NULL, 5, NULL);
    xTaskCreate(camera_monitor_task, "cam_monitor", 2048, NULL, 3, NULL);
    
    ESP_LOGI(TAG, "Advanced camera application initialized");
    return ESP_OK;
}

/**
 * @brief 应用程序入口点（高级示例）
 */
void app_main_advanced(void)
{
    ESP_LOGI(TAG, "ESP32-S3 Advanced Camera Example");
    
    // 初始化高级摄像头应用
    esp_err_t ret = advanced_camera_app_init();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Advanced camera app initialization failed");
        return;
    }
    
    ESP_LOGI(TAG, "Advanced camera application started");
    
    // 主任务可以处理其他事务
    while (1) {
        vTaskDelay(pdMS_TO_TICKS(10000));
        ESP_LOGI(TAG, "Main task heartbeat");
    }
}
