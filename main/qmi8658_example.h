/**
 * @file qmi8658_example.h
 * @brief QMI8658传感器使用示例头文件
 * 
 * <AUTHOR> 开发团队
 * @date 2025-07-27
 */

#ifndef QMI8658_EXAMPLE_H
#define QMI8658_EXAMPLE_H

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief QMI8658传感器示例主函数
 * 
 * 完整的传感器初始化和连续读取示例
 */
void qmi8658_example_main(void);

/**
 * @brief 简单的传感器数据读取示例
 * 
 * 演示基本的传感器初始化和数据读取
 */
void qmi8658_simple_read_example(void);

#ifdef __cplusplus
}
#endif

#endif /* QMI8658_EXAMPLE_H */
