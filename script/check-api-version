#!/bin/bash
#
#  Copyright (c) 2020, The OpenThread Authors.
#  All rights reserved.
#
#  Redistribution and use in source and binary forms, with or without
#  modification, are permitted provided that the following conditions are met:
#  1. Redistributions of source code must retain the above copyright
#     notice, this list of conditions and the following disclaimer.
#  2. Redistributions in binary form must reproduce the above copyright
#     notice, this list of conditions and the following disclaimer in the
#     documentation and/or other materials provided with the distribution.
#  3. Neither the name of the copyright holder nor the
#     names of its contributors may be used to endorse or promote products
#     derived from this software without specific prior written permission.
#
#  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
#  AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
#  IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
#  ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE
#  LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, <PERSON>ECIAL, EXEMPLARY, OR
#  CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
#  SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
#  INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
#  CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
#  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
#  POSSIBILITY OF SUCH DAMAGE.
#

set -euo pipefail

OT_SHA_OLD="$(git cat-file -p HEAD | grep 'parent ' | head -n1 | cut -d' ' -f2)"
readonly OT_SHA_OLD

OT_VERSIONS_FILE=tmp/api_versions
readonly OT_VERSIONS_FILE

die()
{
    echo >&2 "ERROR: $*"
    exit 1
}

main()
{
    mkdir -p tmp

    echo "The main branch must be ${OT_SHA_OLD}"

    git fetch --depth 1 origin "${OT_SHA_OLD}"

    if git diff --name-only --exit-code "${OT_SHA_OLD}" -- include/openthread; then
        echo 'No OpenThread public APIs updates.'
        exit 0
    fi

    git diff "${OT_SHA_OLD}" -- include/openthread | tee >(cat >&2) | grep -aP '[-+]#define OPENTHREAD_API_VERSION (.+)' >"${OT_VERSIONS_FILE}" || die 'Version number is not updated!'

    [[ $(wc -l <"${OT_VERSIONS_FILE}") == 2 ]] || die 'Multiple OPENTHREAD_API_VERSION definitions found!'

    old_version=$(grep -aoP '(?<=-#define OPENTHREAD_API_VERSION ).+' "${OT_VERSIONS_FILE}") || die 'Failed to find old version!'
    new_version=$(grep -aoP '(?<=\+#define OPENTHREAD_API_VERSION ).+' "${OT_VERSIONS_FILE}") || die 'Failed to find new version!'

    echo -e '\n--------------------------\n'

    echo "Old version: ${old_version}"
    echo "New version: ${new_version}"

    [[ $((new_version - old_version)) -gt 0 ]] || die 'Version is not increased!'

    echo -e '\n--------------------------\n'

    echo 'PASS: version check.'
}

main "$@"
