#!/bin/bash
#
#  Copyright (c) 2020, The OpenThread Authors.
#  All rights reserved.
#
#  Redistribution and use in source and binary forms, with or without
#  modification, are permitted provided that the following conditions are met:
#  1. Redistributions of source code must retain the above copyright
#     notice, this list of conditions and the following disclaimer.
#  2. Redistributions in binary form must reproduce the above copyright
#     notice, this list of conditions and the following disclaimer in the
#     documentation and/or other materials provided with the distribution.
#  3. Neither the name of the copyright holder nor the
#     names of its contributors may be used to endorse or promote products
#     derived from this software without specific prior written permission.
#
#  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
#  AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
#  IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
#  ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE
#  LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, <PERSON>ECIAL, EXEMPLARY, OR
#  CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
#  SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
#  INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
#  CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
#  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
#  POSSIBILITY OF SUCH DAMAGE.
#

set -euxo pipefail

: "${CFLAGS:=}"
: "${CXXFLAGS:=}"

OT_BUILDDIR="$(pwd)/build"
readonly OT_BUILDDIR

reset_source()
{
    rm -rf "$OT_BUILDDIR"
}

build_all_features()
{
    local cppflags=(
        "-DOPENTHREAD_CONFIG_ANNOUNCE_SENDER_ENABLE=1"
        "-DOPENTHREAD_CONFIG_ANYCAST_LOCATOR=1"
        "-DOPENTHREAD_CONFIG_BORDER_AGENT_ENABLE=1"
        "-DOPENTHREAD_CONFIG_BORDER_ROUTER_ENABLE=1"
        "-DOPENTHREAD_CONFIG_BORDER_ROUTING_ENABLE=1"
        "-DOPENTHREAD_CONFIG_CHANNEL_MANAGER_ENABLE=1"
        "-DOPENTHREAD_CONFIG_CHANNEL_MONITOR_ENABLE=1"
        "-DOPENTHREAD_CONFIG_COAP_API_ENABLE=1"
        "-DOPENTHREAD_CONFIG_COAP_SECURE_API_ENABLE=1"
        "-DOPENTHREAD_CONFIG_COMMISSIONER_ENABLE=1"
        "-DOPENTHREAD_CONFIG_DATASET_UPDATER_ENABLE=1"
        "-DOPENTHREAD_CONFIG_DHCP6_CLIENT_ENABLE=1"
        "-DOPENTHREAD_CONFIG_DHCP6_SERVER_ENABLE=1"
        "-DOPENTHREAD_CONFIG_DIAG_ENABLE=1"
        "-DOPENTHREAD_CONFIG_DNS_CLIENT_ENABLE=1"
        "-DOPENTHREAD_CONFIG_DNS_DSO_ENABLE=1"
        "-DOPENTHREAD_CONFIG_ECDSA_ENABLE=1"
        "-DOPENTHREAD_CONFIG_HEAP_EXTERNAL_ENABLE=1"
        "-DOPENTHREAD_CONFIG_HISTORY_TRACKER_ENABLE=1"
        "-DOPENTHREAD_CONFIG_IP6_FRAGMENTATION_ENABLE=1"
        "-DOPENTHREAD_CONFIG_IP6_SLAAC_ENABLE=1"
        "-DOPENTHREAD_CONFIG_JAM_DETECTION_ENABLE=1"
        "-DOPENTHREAD_CONFIG_JOINER_ENABLE=1"
        "-DOPENTHREAD_CONFIG_LINK_RAW_ENABLE=1"
        "-DOPENTHREAD_CONFIG_LOG_LEVEL_DYNAMIC_ENABLE=1"
        "-DOPENTHREAD_CONFIG_MAC_BEACON_RSP_WHEN_JOINABLE_ENABLE=1"
        "-DOPENTHREAD_CONFIG_MAC_FILTER_ENABLE=1"
        "-DOPENTHREAD_CONFIG_MAC_RETRY_SUCCESS_HISTOGRAM_ENABLE=1"
        "-DOPENTHREAD_CONFIG_MAC_SOFTWARE_ACK_TIMEOUT_ENABLE=1"
        "-DOPENTHREAD_CONFIG_MAC_SOFTWARE_CSMA_BACKOFF_ENABLE=1"
        "-DOPENTHREAD_CONFIG_MAC_SOFTWARE_ENERGY_SCAN_ENABLE=1"
        "-DOPENTHREAD_CONFIG_MAC_SOFTWARE_RETRANSMIT_ENABLE=1"
        "-DOPENTHREAD_CONFIG_MAC_SOFTWARE_TX_SECURITY_ENABLE=1"
        "-DOPENTHREAD_CONFIG_MLE_ATTACH_BACKOFF_ENABLE=1"
        "-DOPENTHREAD_CONFIG_MLE_STEERING_DATA_SET_OOB_ENABLE=1"
        "-DOPENTHREAD_CONFIG_MPL_DYNAMIC_INTERVAL_ENABLE"
        "-DOPENTHREAD_CONFIG_NCP_HDLC_ENABLE=1"
        "-DOPENTHREAD_CONFIG_NCP_SPI_ENABLE=1"
        "-DOPENTHREAD_CONFIG_PING_SENDER_ENABLE=1"
        "-DOPENTHREAD_CONFIG_PLATFORM_FLASH_API_ENABLE=1"
        "-DOPENTHREAD_CONFIG_PLATFORM_RADIO_COEX_ENABLE=1"
        "-DOPENTHREAD_CONFIG_PLATFORM_USEC_TIMER_ENABLE=1"
        "-DOPENTHREAD_CONFIG_REFERENCE_DEVICE_ENABLE=1"
        "-DOPENTHREAD_CONFIG_SNTP_CLIENT_ENABLE=1"
        "-DOPENTHREAD_CONFIG_SRP_CLIENT_ENABLE=1"
        "-DOPENTHREAD_CONFIG_TMF_NETDATA_SERVICE_ENABLE=1"
        "-DOPENTHREAD_CONFIG_TMF_NETDIAG_CLIENT_ENABLE=1"
        "-DOPENTHREAD_CONFIG_UDP_FORWARD_ENABLE=1"
        "-DOPENTHREAD_CONFIG_MAC_BEACON_PAYLOAD_PARSING_ENABLE=1"
        "-DOPENTHREAD_CONFIG_MAC_OUTGOING_BEACON_PAYLOAD_ENABLE=1"
    )

    # Build Thread 1.1 with full features
    reset_source
    CFLAGS="${cppflags[*]} ${CFLAGS}" CXXFLAGS="${cppflags[*]} ${CXXFLAGS}" \
        "$(dirname "$0")"/cmake-build simulation \
        -DOT_THREAD_VERSION=1.1

    # Build Thread 1.1 with full features and no log
    reset_source
    CFLAGS="${cppflags[*]} ${CFLAGS}" CXXFLAGS="${cppflags[*]} ${CXXFLAGS}" \
        "$(dirname "$0")"/cmake-build simulation \
        -DOT_THREAD_VERSION=1.1 \
        -DOT_LOG_OUTPUT=NONE

    # Build Thread 1.1 with full features and full logs
    reset_source
    CFLAGS="${cppflags[*]} ${CFLAGS}" CXXFLAGS="${cppflags[*]} ${CXXFLAGS}" \
        "$(dirname "$0")"/cmake-build simulation \
        -DOT_THREAD_VERSION=1.1 \
        -DOT_FULL_LOGS=ON

    # Build Thread 1.1 cli-radio
    reset_source
    "$(dirname "$0")"/cmake-build simulation \
        -DOT_THREAD_VERSION=1.1 \
        -DOT_DIAGNOSTIC=ON \
        -DOT_APP_CLI=ON \
        -DOT_APP_NCP=OFF \
        -DOT_APP_RCP=OFF \
        -DOT_FTD=OFF \
        -DOT_MTD=OFF

    # Build Thread 1.1 with ASSERT disabled
    reset_source
    CFLAGS="${cppflags[*]} ${CFLAGS}" CXXFLAGS="${cppflags[*]} ${CXXFLAGS}" \
        "$(dirname "$0")"/cmake-build simulation \
        -DOT_THREAD_VERSION=1.1 \
        -DOT_SIMULATION_VIRTUAL_TIME=ON \
        -DOT_ASSERT=OFF

    # Build Thread 1.1 OTNS
    reset_source
    CFLAGS="${cppflags[*]} ${CFLAGS}" CXXFLAGS="${cppflags[*]} ${CXXFLAGS}" \
        "$(dirname "$0")"/cmake-build simulation \
        -DOT_THREAD_VERSION=1.1 \
        -DOT_SIMULATION_VIRTUAL_TIME=ON \
        -DOT_OTNS=ON

    # Thread 1.4 options
    local options=(
        "-DOT_BACKBONE_ROUTER=ON"
        "-DOT_BORDER_ROUTING=ON"
        "-DOT_NAT64_BORDER_ROUTING=ON"
        "-DOT_NAT64_TRANSLATOR=ON"
        "-DOT_CSL_RECEIVER=ON"
        "-DOT_MLR=ON"
        "-DOT_OTNS=ON"
        "-DOT_SIMULATION_VIRTUAL_TIME=ON"
        "-DOT_THREAD_VERSION=1.4"
    )

    # Build Thread 1.4 with full features
    reset_source
    CFLAGS="${cppflags[*]} ${CFLAGS}" CXXFLAGS="${cppflags[*]} ${CXXFLAGS}" \
        "$(dirname "$0")"/cmake-build simulation "${options[@]}" -DOT_DUA=ON

    # Build Thread 1.4 with external heap and msg pool using heap
    reset_source
    CFLAGS="${cppflags[*]} ${CFLAGS} -DOPENTHREAD_CONFIG_MESSAGE_USE_HEAP_ENABLE=1" \
        CXXFLAGS="${cppflags[*]} ${CXXFLAGS} -DOPENTHREAD_CONFIG_MESSAGE_USE_HEAP_ENABLE=1" \
        "$(dirname "$0")"/cmake-build simulation "${options[@]}" -DOT_DUA=ON

    # Build Thread 1.4 with full features and no log
    reset_source
    CFLAGS="${cppflags[*]} ${CFLAGS}" CXXFLAGS="${cppflags[*]} ${CXXFLAGS}" \
        "$(dirname "$0")"/cmake-build simulation "${options[@]}" -DOT_DUA=ON -DOT_LOG_OUTPUT=NONE

    # Build Thread 1.4 with full features and full logs
    reset_source
    CFLAGS="${cppflags[*]} ${CFLAGS}" CXXFLAGS="${cppflags[*]} ${CXXFLAGS}" \
        "$(dirname "$0")"/cmake-build simulation "${options[@]}" -DOT_DUA=ON -DOT_FULL_LOGS=ON

    # Build Thread 1.4 Backbone Router without DUA ND Proxying
    reset_source
    "$(dirname "$0")"/cmake-build simulation "${options[@]}" -DOT_BACKBONE_ROUTER_DUA_NDPROXYING=OFF

    # Build Thread 1.4 Backbone Router without Multicast Routing
    reset_source
    "$(dirname "$0")"/cmake-build simulation "${options[@]}" -DOT_BACKBONE_ROUTER_MULTICAST_ROUTING=OFF

    # Build with Vendor Extension
    reset_source
    "$(dirname "$0")"/cmake-build simulation \
        -DOT_THREAD_VERSION=1.1 \
        -DOT_VENDOR_EXTENSION=../../src/core/instance/extension_example.cpp

    # Build Thread 1.4 with no additional features
    reset_source
    "$(dirname "$0")"/cmake-build simulation -DOT_THREAD_VERSION=1.4

    # Build Thread 1.4 with full features and OT_ASSERT=OFF
    reset_source
    "$(dirname "$0")"/cmake-build simulation "${options[@]}" -DOT_DUA=ON -DOT_ASSERT=OFF

    # Build with RAM settings
    reset_source
    "$(dirname "$0")"/cmake-build simulation -DOT_SETTINGS_RAM=ON

    reset_source
    "$(dirname "$0")"/cmake-build simulation -DOT_BLE_TCAT=ON
}

build_nest_common()
{
    local cppflags=(
        "-DOPENTHREAD_CONFIG_BORDER_ROUTER_ENABLE=1"
        "-DOPENTHREAD_CONFIG_CHANNEL_MANAGER_ENABLE=1"
        "-DOPENTHREAD_CONFIG_CHANNEL_MONITOR_ENABLE=1"
        "-DOPENTHREAD_CONFIG_DATASET_UPDATER_ENABLE=1"
        "-DOPENTHREAD_CONFIG_DIAG_ENABLE=1"
        "-DOPENTHREAD_CONFIG_JAM_DETECTION_ENABLE=1"
        "-DOPENTHREAD_CONFIG_MAC_FILTER_ENABLE=1"
        "-DOPENTHREAD_CONFIG_PING_SENDER_ENABLE=1"
        "-DOPENTHREAD_CONFIG_NCP_SPI_ENABLE=1"
        "-DOPENTHREAD_CONFIG_PLATFORM_FLASH_API_ENABLE=1"
        "-DOPENTHREAD_CONFIG_TMF_NETDATA_SERVICE_ENABLE=1"
    )

    reset_source
    mkdir build && cd build
    CFLAGS="${cppflags[*]} ${CFLAGS}" CXXFLAGS="${cppflags[*]} ${CXXFLAGS}" \
        cmake -GNinja -DOT_PLATFORM=simulation ..
    ninja
    cd ..

    cppflags=(
        "-DOPENTHREAD_CONFIG_ANOUNCE_SENDER_ENABLE=1"
        "-DOPENTHREAD_CONFIG_PLATFORM_FLASH_API_ENABLE=1"
        "-DOPENTHREAD_CONFIG_TIME_SYNC_ENABLE=1"
        "-DOPENTHREAD_CONFIG_NCP_HDLC_ENABLE=1"
    )

    reset_source
    mkdir build && cd build
    CFLAGS="${cppflags[*]} ${CFLAGS}" CXXFLAGS="${cppflags[*]} ${CXXFLAGS}" \
        cmake -GNinja -DOT_PLATFORM=simulation ..
    ninja
    cd ..

    cppflags=(
        "-DOPENTHREAD_CONFIG_NCP_HDLC_ENABLE=1"
        "-DOPENTHREAD_CONFIG_PLATFORM_FLASH_API_ENABLE=1"
    )

    reset_source
    mkdir build && cd build
    CFLAGS="${cppflags[*]} ${CFLAGS}" CXXFLAGS="${cppflags[*]} ${CXXFLAGS}" \
        cmake -GNinja -DOT_PLATFORM=simulation \
        -DOT_VENDOR_EXTENSION=instance/extension_example.cpp \
        -DOT_NCP_VENDOR_HOOK_SOURCE=example_vendor_hook.cpp \
        ..
    ninja
    cd ..
}

build_multi_radio_links()
{
    # TREL radio link only.
    reset_source
    ./script/cmake-build simulation -DOT_LOG_LEVEL=DEBG \
        -DOT_TREL=OFF -DOT_15_4=ON

    # Multi radio link - 15.4 and TREL.
    reset_source
    ./script/cmake-build simulation -DOT_LOG_LEVEL=DEBG \
        -DOT_TREL=ON -DOT_15_4=ON
}
main()
{
    build_all_features
    build_nest_common
    build_multi_radio_links
}

main "$@"
