#!/bin/bash
#
#  Copyright (c) 2021, The OpenThread Authors.
#  All rights reserved.
#
#  Redistribution and use in source and binary forms, with or without
#  modification, are permitted provided that the following conditions are met:
#  1. Redistributions of source code must retain the above copyright
#     notice, this list of conditions and the following disclaimer.
#  2. Redistributions in binary form must reproduce the above copyright
#     notice, this list of conditions and the following disclaimer in the
#     documentation and/or other materials provided with the distribution.
#  3. Neither the name of the copyright holder nor the
#     names of its contributors may be used to endorse or promote products
#     derived from this software without specific prior written permission.
#
#  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
#  AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
#  IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
#  ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE
#  LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, <PERSON>ECIAL, EXEMPLARY, OR
#  CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
#  SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
#  INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
#  CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
#  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
#  POSSIBILITY OF SUCH DAMAGE.
#

set -euxo pipefail

OT_SRCDIR="$(pwd)"
readonly OT_SRCDIR

OT_BUILD_OPTIONS=(
    "-DBUILD_TESTING=OFF"
    "-DOT_ANYCAST_LOCATOR=ON"
    "-DOT_BUILD_EXECUTABLES=OFF"
    "-DOT_BORDER_AGENT=ON"
    "-DOT_BORDER_ROUTER=ON"
    "-DOT_BORDER_ROUTING=ON"
    "-DOT_COAP=ON"
    "-DOT_COAP_BLOCK=ON"
    "-DOT_COAP_OBSERVE=ON"
    "-DOT_COAPS=ON"
    "-DOT_COMMISSIONER=ON"
    "-DOT_COMPILE_WARNING_AS_ERROR=ON"
    "-DOT_COVERAGE=ON"
    "-DOT_CHANNEL_MANAGER=ON"
    "-DOT_CHANNEL_MONITOR=ON"
    "-DOT_DATASET_UPDATER=ON"
    "-DOT_DHCP6_CLIENT=ON"
    "-DOT_DHCP6_SERVER=ON"
    "-DOT_DIAGNOSTIC=ON"
    "-DOT_DNSSD_DISCOVERY_PROXY=ON"
    "-DOT_DNSSD_SERVER=ON"
    "-DOT_DNS_CLIENT=ON"
    "-DOT_DNS_DSO=ON"
    "-DOT_ECDSA=ON"
    "-DOT_EXTERNAL_MBEDTLS=external"
    "-DOT_IP6_FRAGM=ON"
    "-DOT_JAM_DETECTION=ON"
    "-DOT_JOINER=ON"
    "-DOT_LOG_LEVEL_DYNAMIC=ON"
    "-DOT_MAC_FILTER=ON"
    "-DOT_MDNS=ON"
    "-DOT_MESH_DIAG=ON"
    "-DOT_NAT64_BORDER_ROUTING=ON"
    "-DOT_NAT64_TRANSLATOR=ON"
    "-DOT_NEIGHBOR_DISCOVERY_AGENT=ON"
    "-DOT_NETDIAG_CLIENT=ON"
    "-DOT_PING_SENDER=ON"
    "-DOT_PLATFORM=external"
    "-DOT_RCP_RESTORATION_MAX_COUNT=2"
    "-DOT_RCP_TX_WAIT_TIME_SECS=5"
    "-DOT_REFERENCE_DEVICE=ON"
    "-DOT_SERVICE=ON"
    "-DOT_SLAAC=ON"
    "-DOT_SNTP_CLIENT=ON"
    "-DOT_SRP_ADV_PROXY=ON"
    "-DOT_SRP_CLIENT=ON"
    "-DOT_SRP_SERVER=ON"
    "-DOT_UPTIME=ON"
    "-DOT_VENDOR_NAME=OpenThread"
    "-DOT_VENDOR_MODEL=Scan-build"
    "-DOT_VENDOR_SW_VERSION=OT"
)
readonly OT_BUILD_OPTIONS

OT_CFLAGS=(
    "-DMBEDTLS_DEBUG_C"
    "-isystem $(pwd)/third_party/mbedtls"
    "-isystem $(pwd)/third_party/mbedtls/repo/include"
    '-DMBEDTLS_CONFIG_FILE=\"mbedtls-config.h\"'
)
readonly OT_CFLAGS

main()
{
    mkdir -p build
    cd build

    scan-build-14 cmake -GNinja -DCMAKE_EXPORT_COMPILE_COMMANDS=ON -DOT_COMPILE_WARNING_AS_ERROR=ON -DCMAKE_C_FLAGS="${OT_CFLAGS[*]}" -DCMAKE_CXX_FLAGS="${OT_CFLAGS[*]}" "${OT_BUILD_OPTIONS[@]}" "${OT_SRCDIR}"
    scan-build-14 --status-bugs -analyze-headers -v ninja

    cd "${OT_SRCDIR}"
}

main "$@"
