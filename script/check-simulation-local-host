#!/bin/bash
#
#  Copyright (c) 2024, The OpenThread Authors.
#  All rights reserved.
#
#  Redistribution and use in source and binary forms, with or without
#  modification, are permitted provided that the following conditions are met:
#  1. Redistributions of source code must retain the above copyright
#     notice, this list of conditions and the following disclaimer.
#  2. Redistributions in binary form must reproduce the above copyright
#     notice, this list of conditions and the following disclaimer in the
#     documentation and/or other materials provided with the distribution.
#  3. Neither the name of the copyright holder nor the
#     names of its contributors may be used to endorse or promote products
#     derived from this software without specific prior written permission.
#
#  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
#  AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
#  IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
#  ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE
#  LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, <PERSON>ECIAL, EXEMPLARY, OR
#  CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
#  SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
#  INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
#  CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
#  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
#  POSSIBILITY OF SUCH DAMAGE.
#

set -euxo pipefail

IFACE_NAME="dummy116"
EXPECT_TEST=./tests/scripts/expect/cli-ping.exp

cleanup()
{
    echo "Cleaning up..."
    sudo ip link set dev "$IFACE_NAME" down
    sudo ip link del dev "$IFACE_NAME"
}

setup_dummy116()
{
    ip -V >/dev/null 2>&1 || {
        echo "Error: iproute2 is required but not installed. Please install it (e.g., 'sudo apt install iproute2' or similar) and try again."
        exit 1
    }

    ip link show "$IFACE_NAME" >/dev/null 2>&1 || sudo ip link add "$IFACE_NAME" type dummy

    sudo ip link set dev "$IFACE_NAME" up

    IP6ADDR="$(ip addr show $IFACE_NAME | grep fe80:: | awk '{print $2}' | cut -d/ -f1)"
    echo "Simulated interface $IFACE_NAME created with IPv6 address $IP6ADDR"

    trap cleanup EXIT
}

test_ipv6()
{
    setup_dummy116
    OT_SIMULATION_LOCAL_HOST=$IFACE_NAME $EXPECT_TEST
    OT_SIMULATION_LOCAL_HOST=$IP6ADDR $EXPECT_TEST

    OT_NODE_TYPE=rcp OT_SIMULATION_LOCAL_HOST=$IFACE_NAME $EXPECT_TEST
    OT_NODE_TYPE=rcp OT_SIMULATION_LOCAL_HOST=$IP6ADDR $EXPECT_TEST
}

test_ipv4()
{
    OT_SIMULATION_LOCAL_HOST=lo $EXPECT_TEST
    OT_SIMULATION_LOCAL_HOST=127.0.0.1 $EXPECT_TEST

    OT_NODE_TYPE=rcp OT_SIMULATION_LOCAL_HOST=lo $EXPECT_TEST
    OT_NODE_TYPE=rcp OT_SIMULATION_LOCAL_HOST=127.0.0.1 $EXPECT_TEST
}

main()
{
    ./script/cmake-build posix
    ./script/cmake-build simulation

    test_ipv4
    test_ipv6
}

main "$@"
