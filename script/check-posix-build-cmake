#!/bin/bash
#
#  Copyright (c) 2020, The OpenThread Authors.
#  All rights reserved.
#
#  Redistribution and use in source and binary forms, with or without
#  modification, are permitted provided that the following conditions are met:
#  1. Redistributions of source code must retain the above copyright
#     notice, this list of conditions and the following disclaimer.
#  2. Redistributions in binary form must reproduce the above copyright
#     notice, this list of conditions and the following disclaimer in the
#     documentation and/or other materials provided with the distribution.
#  3. Neither the name of the copyright holder nor the
#     names of its contributors may be used to endorse or promote products
#     derived from this software without specific prior written permission.
#
#  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
#  AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
#  IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
#  ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE
#  LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, <PERSON>ECIAL, EXEMPLARY, OR
#  CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
#  SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
#  INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
#  CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
#  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
#  POSSIBILITY OF SUCH DAMAGE.
#

set -euxo pipefail

OT_BUILDDIR="$(pwd)/build"
readonly OT_BUILDDIR

reset_source()
{
    rm -rf "$OT_BUILDDIR"
}

build()
{
    local options=("$@")

    options+=(
        "-DOT_FULL_LOGS=ON"
    )

    "$(dirname "$0")"/cmake-build posix "${options[@]}"

    options+=(
        "-DOT_TREL=ON"
    )

    if [[ $OSTYPE != "darwin"* ]]; then
        options+=(
            "-DOT_BORDER_ROUTING=ON"
            "-DOT_SRP_SERVER=ON"
        )
    fi

    reset_source
    "$(dirname "$0")"/cmake-build posix "${options[@]}"
}

main()
{
    reset_source
    build "$@"

    reset_source
    build -DOT_RCP_RESTORATION_MAX_COUNT=2

    if [[ $OSTYPE != "darwin"* ]]; then
        reset_source
        build -DOT_RCP_RESTORATION_MAX_COUNT=2 -DOT_POSIX_RCP_SPI_BUS=ON "$@"
    fi

    reset_source
    build -DOT_POSIX_RCP_VENDOR_BUS=ON "$@"
}

main "$@"
