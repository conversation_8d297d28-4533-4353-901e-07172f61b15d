#!/bin/bash
#
#  Copyright (c) 2020, The OpenThread Authors.
#  All rights reserved.
#
#  Redistribution and use in source and binary forms, with or without
#  modification, are permitted provided that the following conditions are met:
#  1. Redistributions of source code must retain the above copyright
#     notice, this list of conditions and the following disclaimer.
#  2. Redistributions in binary form must reproduce the above copyright
#     notice, this list of conditions and the following disclaimer in the
#     documentation and/or other materials provided with the distribution.
#  3. Neither the name of the copyright holder nor the
#     names of its contributors may be used to endorse or promote products
#     derived from this software without specific prior written permission.
#
#  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
#  AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
#  IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
#  ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE
#  LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, <PERSON>ECIAL, EXEMPLARY, OR
#  CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
#  SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
#  INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
#  CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
#  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
#  POSSIBILITY OF SUCH DAMAGE.
#

CLANG_TIDY_VERSION="LLVM version 14.0"
CLANG_APPLY_REPLACEMENTS_VERSION="clang-apply-replacements version 14.0"

die()
{
    echo " *** ERROR: $*"
    exit 1
}

# Search for clang-tidy-14
if command -v clang-tidy-14 >/dev/null; then
    clang_tidy=$(command -v clang-tidy-14)
elif command -v clang-tidy >/dev/null; then
    clang_tidy=$(command -v clang-tidy)
    case "$($clang_tidy --version)" in
        *"$CLANG_TIDY_VERSION"*) ;;

        *)
            die "$($clang_tidy --version); $CLANG_TIDY_VERSION required"
            ;;
    esac
else
    die "clang-tidy 14.0 required"
fi

# Search for clang-apply-replacements-14
if command -v clang-apply-replacements-14 >/dev/null; then
    clang_apply_replacements=$(command -v clang-apply-replacements-14)
elif command -v clang-apply-replacements >/dev/null; then
    clang_apply_replacements=$(command -v clang-apply-replacements)
    case "$($clang_apply_replacements --version)" in
        "$CLANG_APPLY_REPLACEMENTS_VERSION"*) ;;

        *)
            die "$($clang_apply_replacements --version); $CLANG_APPLY_REPLACEMENTS_VERSION required"
            ;;
    esac
else
    die "clang-apply-replacements 14.0 required"
fi

# Search for run-clang-tidy-14.py
if command -v run-clang-tidy-14.py >/dev/null; then
    run_clang_tidy=$(command -v run-clang-tidy-14.py)
elif command -v run-clang-tidy-14 >/dev/null; then
    run_clang_tidy=$(command -v run-clang-tidy-14)
elif command -v run-clang-tidy.py >/dev/null; then
    run_clang_tidy=$(command -v run-clang-tidy.py)
elif command -v run-clang-tidy >/dev/null; then
    run_clang_tidy=$(command -v run-clang-tidy)
else
    die "run-clang-tidy.py 14.0 required"
fi

$run_clang_tidy -clang-tidy-binary "$clang_tidy" -clang-apply-replacements-binary "$clang_apply_replacements" "$@" || die

exit 0
