#
#  Copyright (c) 2020, The OpenThread Authors.
#  All rights reserved.
#
#  Redistribution and use in source and binary forms, with or without
#  modification, are permitted provided that the following conditions are met:
#  1. Redistributions of source code must retain the above copyright
#     notice, this list of conditions and the following disclaimer.
#  2. Redistributions in binary form must reproduce the above copyright
#     notice, this list of conditions and the following disclaimer in the
#     documentation and/or other materials provided with the distribution.
#  3. Neither the name of the copyright holder nor the
#     names of its contributors may be used to endorse or promote products
#     derived from this software without specific prior written permission.
#
#  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
#  AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
#  IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
#  ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE
#  LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, <PERSON>ECIAL, E<PERSON>EMPLARY, OR
#  CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
#  SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
#  INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
#  CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
#  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
#  POSSIBILITY OF SUCH DAMAGE.
#

name: Simulation 1.1

on:
  push:
    branches-ignore:
      - 'dependabot/**'
  pull_request:
    branches:
      - 'main'

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || (github.repository == 'openthread/openthread' && github.run_id) || github.ref }}
  cancel-in-progress: true

permissions:
  contents: read

jobs:

  packet-verification:
    runs-on: ubuntu-20.04
    env:
      PACKET_VERIFICATION: 1
      REFERENCE_DEVICE: 1
      THREAD_VERSION: 1.1
      VIRTUAL_TIME: 1
      MULTIPLY: 3
    steps:
    - name: Harden Runner
      uses: step-security/harden-runner@91182cccc01eb5e619899d80e4e971d6181294a7 # v2.10.1
      with:
        egress-policy: audit # TODO: change to 'egress-policy: block' after couple of runs

    - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      with:
        submodules: true
    - uses: actions/setup-python@f677139bbe7f9c59b41e40162b753c062f5d49a3 # v5.2.0
      with:
        python-version: '3.12'
        cache: pip
    - name: Bootstrap
      run: |
        sudo rm /etc/apt/sources.list.d/* && sudo apt-get update
        sudo apt-get --no-install-recommends install -y ninja-build lcov
        python3 -m pip install -r tests/scripts/thread-cert/requirements.txt
    - name: Build
      run: |
        ./script/test build
    - name: Get Thread-Wireshark
      run: |
        ./script/test get_thread_wireshark
    - name: Run
      run: |
        ./script/test cert_suite ./tests/scripts/thread-cert/Cert_*.py ./tests/scripts/thread-cert/test_*.py
    - uses: actions/upload-artifact@50769540e7f4bd5e21e526ee35c689e35e0d6874 # v4.4.0
      if: ${{ failure() }}
      with:
        name: packet-verification-pcaps
        path: |
          *.pcap
          *.json
    - name: Generate Coverage
      run: |
        ./script/test generate_coverage gcc
    - uses: actions/upload-artifact@50769540e7f4bd5e21e526ee35c689e35e0d6874 # v4.4.0
      with:
        name: cov-packet-verification
        path: tmp/coverage.info
        retention-days: 1

  cli-ftd:
    runs-on: ubuntu-20.04
    env:
      CFLAGS: -m32
      CXXFLAGS: -m32
      LDFLAGS: -m32
      COVERAGE: 1
      REFERENCE_DEVICE: 1
      THREAD_VERSION: 1.1
      VIRTUAL_TIME: 1
    steps:
    - name: Harden Runner
      uses: step-security/harden-runner@91182cccc01eb5e619899d80e4e971d6181294a7 # v2.10.1
      with:
        egress-policy: audit # TODO: change to 'egress-policy: block' after couple of runs

    - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      with:
        submodules: true
    - uses: actions/setup-python@f677139bbe7f9c59b41e40162b753c062f5d49a3 # v5.2.0
      with:
        python-version: '3.12'
        cache: pip
    - name: Bootstrap
      run: |
        sudo rm /etc/apt/sources.list.d/* && sudo apt-get update
        sudo apt-get --no-install-recommends install -y lcov ninja-build g++-multilib
        python3 -m pip install -r tests/scripts/thread-cert/requirements.txt
    - name: Build
      run: |
        ./script/test build
    - name: Run
      run: |
        ./script/test cert_suite ./tests/scripts/thread-cert/Cert_*.py ./tests/scripts/thread-cert/test_*.py
    - uses: actions/upload-artifact@50769540e7f4bd5e21e526ee35c689e35e0d6874 # v4.4.0
      if: ${{ failure() }}
      with:
        name: cli-ftd-thread-cert
        path: ot_testing
    - name: Generate Coverage
      run: |
        ./script/test generate_coverage gcc
    - uses: actions/upload-artifact@50769540e7f4bd5e21e526ee35c689e35e0d6874 # v4.4.0
      with:
        name: cov-cli-ftd
        path: tmp/coverage.info
        retention-days: 1

  cli-mtd:
    name: cli-mtd MESSAGE_USE_HEAP=${{ matrix.message_use_heap }}
    runs-on: ubuntu-20.04
    strategy:
      fail-fast: false
      matrix:
        message_use_heap: [0, 1]
    env:
      CFLAGS: -m32
      CXXFLAGS: -m32
      LDFLAGS: -m32
      COVERAGE: 1
      REFERENCE_DEVICE: 1
      THREAD_VERSION: 1.1
      USE_MTD: 1
      VIRTUAL_TIME: 1
      MESSAGE_USE_HEAP: ${{ matrix.message_use_heap }}
    steps:
    - name: Harden Runner
      uses: step-security/harden-runner@91182cccc01eb5e619899d80e4e971d6181294a7 # v2.10.1
      with:
        egress-policy: audit # TODO: change to 'egress-policy: block' after couple of runs

    - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      with:
        submodules: true
    - uses: actions/setup-python@f677139bbe7f9c59b41e40162b753c062f5d49a3 # v5.2.0
      with:
        python-version: '3.12'
        cache: pip
    - name: Bootstrap
      run: |
        sudo rm /etc/apt/sources.list.d/* && sudo apt-get update
        sudo apt-get --no-install-recommends install -y lcov ninja-build g++-multilib
        python3 -m pip install -r tests/scripts/thread-cert/requirements.txt
    - name: Build
      run: |
        ./script/test build
    - name: Run
      run: |
        ./script/test cert_suite ./tests/scripts/thread-cert/Cert_*.py ./tests/scripts/thread-cert/test_*.py
    - uses: actions/upload-artifact@50769540e7f4bd5e21e526ee35c689e35e0d6874 # v4.4.0
      if: ${{ failure() }}
      with:
        name: cli-mtd-thread-cert
        path: ot_testing
    - name: Generate Coverage
      run: |
        ./script/test generate_coverage gcc
    - uses: actions/upload-artifact@50769540e7f4bd5e21e526ee35c689e35e0d6874 # v4.4.0
      with:
        name: cov-cli-mtd-${{ matrix.message_use_heap }}
        path: tmp/coverage.info
        retention-days: 1

  cli-time-sync:
    runs-on: ubuntu-20.04
    env:
      CFLAGS: -m32
      CXXFLAGS: -m32
      LDFLAGS: -m32
      COVERAGE: 1
      REFERENCE_DEVICE: 1
      THREAD_VERSION: 1.1
      VIRTUAL_TIME: 1
    steps:
    - name: Harden Runner
      uses: step-security/harden-runner@91182cccc01eb5e619899d80e4e971d6181294a7 # v2.10.1
      with:
        egress-policy: audit # TODO: change to 'egress-policy: block' after couple of runs

    - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      with:
        submodules: true
    - uses: actions/setup-python@f677139bbe7f9c59b41e40162b753c062f5d49a3 # v5.2.0
      with:
        python-version: '3.12'
        cache: pip
    - name: Bootstrap
      run: |
        sudo rm /etc/apt/sources.list.d/* && sudo apt-get update
        sudo apt-get --no-install-recommends install -y g++-multilib lcov ninja-build
        python3 -m pip install -r tests/scripts/thread-cert/requirements.txt
    - name: Build
      run: |
        OT_OPTIONS="-DOT_TIME_SYNC=ON" ./script/test build
    - name: Run
      run: |
        ./script/test cert_suite ./tests/scripts/thread-cert/Cert_*.py ./tests/scripts/thread-cert/test_*.py
    - uses: actions/upload-artifact@50769540e7f4bd5e21e526ee35c689e35e0d6874 # v4.4.0
      if: ${{ failure() }}
      with:
        name: cli-time-sync-thread-cert
        path: ot_testing
    - name: Generate Coverage
      run: |
        ./script/test generate_coverage gcc
    - uses: actions/upload-artifact@50769540e7f4bd5e21e526ee35c689e35e0d6874 # v4.4.0
      with:
        name: cov-cli-time-sync
        path: tmp/coverage.info
        retention-days: 1

  expects:
    runs-on: ubuntu-20.04
    env:
      CFLAGS: -DCLI_COAP_SECURE_USE_COAP_DEFAULT_HANDLER=1 -DOPENTHREAD_CONFIG_MLE_MAX_CHILDREN=15
      CXXFLAGS: -DCLI_COAP_SECURE_USE_COAP_DEFAULT_HANDLER=1 -DOPENTHREAD_CONFIG_MLE_MAX_CHILDREN=15
      THREAD_VERSION: 1.1
    steps:
    - name: Harden Runner
      uses: step-security/harden-runner@91182cccc01eb5e619899d80e4e971d6181294a7 # v2.10.1
      with:
        egress-policy: audit # TODO: change to 'egress-policy: block' after couple of runs

    - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
    - uses: actions/setup-python@f677139bbe7f9c59b41e40162b753c062f5d49a3 # v5.2.0
      with:
        python-version: '3.12'
        cache: pip
    - name: Bootstrap
      run: |
        sudo apt-get --no-install-recommends install -y expect ninja-build lcov socat
        pip install bleak 'cryptography==43.0.0'
    - name: Run
      run: |
        ulimit -c unlimited
        ./script/test prepare_coredump_upload
        OT_OPTIONS='-DOT_TIME_SYNC=ON -DOT_FULL_LOGS=ON -DOT_LOG_OUTPUT=PLATFORM_DEFINED' VIRTUAL_TIME=0 ./script/test build expect
    - name: Check Crash
      if: ${{ failure() }}
      run: |
          CRASHED=$(./script/test check_crash | tail -1)
          [[ $CRASHED -eq "1" ]] && echo "Crashed!" || echo "Not crashed."
          echo "CRASHED_CLI=$CRASHED" >> $GITHUB_ENV
    - uses: actions/upload-artifact@50769540e7f4bd5e21e526ee35c689e35e0d6874 # v4.4.0
      if: ${{ failure() && env.CRASHED_CLI == '1' }}
      with:
        name: core-expect-cli
        path: |
          ./ot-core-dump/*
    - name: Generate Coverage
      run: |
        ./script/test generate_coverage gcc
    - uses: actions/upload-artifact@50769540e7f4bd5e21e526ee35c689e35e0d6874 # v4.4.0
      with:
        name: cov-expects
        path: tmp/coverage.info
        retention-days: 1

  ot-commissioner:
    runs-on: ubuntu-22.04
    env:
      THREAD_VERSION: 1.1
    steps:
    - name: Harden Runner
      uses: step-security/harden-runner@91182cccc01eb5e619899d80e4e971d6181294a7 # v2.10.1
      with:
        egress-policy: audit # TODO: change to 'egress-policy: block' after couple of runs

    - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      with:
        submodules: true
    - name: Bootstrap
      env:
        GITHUB_TOKEN: "${{ secrets.GITHUB_TOKEN }}"
      run: |
        sudo rm /etc/apt/sources.list.d/*
        sudo apt-get update
        sudo apt-get install -y avahi-daemon avahi-utils lcov
        script/git-tool clone https://github.com/openthread/ot-commissioner.git /tmp/ot-commissioner --depth 1 --branch main
    - name: Build
      run: |
        cd /tmp/ot-commissioner
        script/bootstrap.sh
        cmake -GNinja                           \
              -DCMAKE_CXX_STANDARD=11           \
              -DCMAKE_CXX_STANDARD_REQUIRED=ON  \
              -DCMAKE_BUILD_TYPE=Release        \
              -DCMAKE_INSTALL_PREFIX=/usr/local \
              -DOT_COMM_COVERAGE=ON             \
              -DOT_COMM_CCM=OFF                 \
              -S . -B build
        cmake --build build
        sudo cmake --install build
    - name: Run
      run: |
        export OT_COMM_OPENTHREAD="$(pwd)"
        cd /tmp/ot-commissioner/tests/integration
        ./bootstrap.sh
        ./run_tests.sh
    - name: Generate Coverage
      run: |
        ./script/test generate_coverage gcc
    - uses: actions/upload-artifact@50769540e7f4bd5e21e526ee35c689e35e0d6874 # v4.4.0
      with:
        name: cov-ot-commissioner
        path: tmp/coverage.info
        retention-days: 1

  multiple-instance:
    runs-on: ubuntu-20.04
    env:
      COVERAGE: 1
      THREAD_VERSION: 1.1
      VIRTUAL_TIME: 1
      CXXFLAGS: "-DOPENTHREAD_CONFIG_LOG_PREPEND_UPTIME=0"
    steps:
    - name: Harden Runner
      uses: step-security/harden-runner@91182cccc01eb5e619899d80e4e971d6181294a7 # v2.10.1
      with:
        egress-policy: audit # TODO: change to 'egress-policy: block' after couple of runs

    - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      with:
        submodules: true
    - uses: actions/setup-python@f677139bbe7f9c59b41e40162b753c062f5d49a3 # v5.2.0
      with:
        python-version: '3.12'
        cache: pip
    - name: Bootstrap
      run: |
        sudo apt-get --no-install-recommends install -y lcov ninja-build
        python3 -m pip install -r tests/scripts/thread-cert/requirements.txt
    - name: Build
      run: |
        OT_OPTIONS="-DOT_MULTIPLE_INSTANCE=ON" ./script/test build
    - name: Run
      run: |
        ./script/test cert_suite ./tests/scripts/thread-cert/Cert_*.py ./tests/scripts/thread-cert/test_*.py
    - uses: actions/upload-artifact@50769540e7f4bd5e21e526ee35c689e35e0d6874 # v4.4.0
      if: ${{ failure() }}
      with:
        name: ot_testing
        path: build/simulation/tests/scripts/thread-cert
    - name: Generate Coverage
      run: |
        ./script/test generate_coverage gcc
    - uses: actions/upload-artifact@50769540e7f4bd5e21e526ee35c689e35e0d6874 # v4.4.0
      with:
        name: cov-multiple-instance
        path: tmp/coverage.info
        retention-days: 1

  simulation-local-host:
    runs-on: ubuntu-20.04
    env:
      COVERAGE: 1
    steps:
    - name: Harden Runner
      uses: step-security/harden-runner@91182cccc01eb5e619899d80e4e971d6181294a7 # v2.10.1
      with:
        egress-policy: audit # TODO: change to 'egress-policy: block' after couple of runs

    - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      with:
        submodules: true
    - name: Bootstrap
      run: |
        sudo apt-get --no-install-recommends install -y expect ninja-build lcov
    - name: Run
      run: |
        ./script/check-simulation-local-host
    - name: Generate Coverage
      run: |
        ./script/test generate_coverage gcc
    - uses: actions/upload-artifact@50769540e7f4bd5e21e526ee35c689e35e0d6874 # v4.4.0
      with:
        name: cov-simulation-local-host
        path: tmp/coverage.info
        retention-days: 1

  upload-coverage:
    needs:
    - packet-verification
    - cli-ftd
    - cli-mtd
    - cli-time-sync
    - expects
    - ot-commissioner
    - multiple-instance
    runs-on: ubuntu-20.04
    steps:
    - name: Harden Runner
      uses: step-security/harden-runner@91182cccc01eb5e619899d80e4e971d6181294a7 # v2.10.1
      with:
        egress-policy: audit # TODO: change to 'egress-policy: block' after couple of runs

    - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      with:
        submodules: true
    - name: Bootstrap
      run: |
        sudo apt-get --no-install-recommends install -y lcov
    - uses: actions/download-artifact@65a9edc5881444af0b9093a5e628f2fe47ea3b2e # v4.1.7
      with:
        path: coverage/
        pattern: cov-*
        merge-multiple: true
    - name: Combine Coverage
      run: |
        script/test combine_coverage
    - name: Upload Coverage
      uses: codecov/codecov-action@e28ff129e5465c2c0dcc6f003fc735cb6ae0c673 # v4.5.0
      env:
        CODECOV_TOKEN: ${{ secrets.CODECOV_TOKEN }}
      with:
        files: final.info
        fail_ci_if_error: true
