#
#  Copyright (c) 2020, The OpenThread Authors.
#  All rights reserved.
#
#  Redistribution and use in source and binary forms, with or without
#  modification, are permitted provided that the following conditions are met:
#  1. Redistributions of source code must retain the above copyright
#     notice, this list of conditions and the following disclaimer.
#  2. Redistributions in binary form must reproduce the above copyright
#     notice, this list of conditions and the following disclaimer in the
#     documentation and/or other materials provided with the distribution.
#  3. Neither the name of the copyright holder nor the
#     names of its contributors may be used to endorse or promote products
#     derived from this software without specific prior written permission.
#
#  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
#  AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
#  IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
#  ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE
#  LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, <PERSON>ECIAL, E<PERSON>EMPLARY, OR
#  CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
#  SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
#  INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
#  CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
#  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
#  POSSIBILITY OF SUCH DAMAGE.
#

name: Build

on:
  push:
    branches-ignore:
      - 'dependabot/**'
  pull_request:
    branches:
      - 'main'

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || (github.repository == 'openthread/openthread' && github.run_id) || github.ref }}
  cancel-in-progress: true

permissions:
  contents: read

jobs:

  pretty:
    runs-on: ubuntu-22.04
    steps:
    - name: Harden Runner
      uses: step-security/harden-runner@91182cccc01eb5e619899d80e4e971d6181294a7 # v2.10.1
      with:
        egress-policy: audit # TODO: change to 'egress-policy: block' after couple of runs

    - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      with:
        submodules: true
    - uses: actions/setup-python@f677139bbe7f9c59b41e40162b753c062f5d49a3 # v5.2.0
      with:
        python-version: '3.12'
        cache: pip
    - name: Bootstrap
      run: |
        sudo rm /etc/apt/sources.list.d/* && sudo apt-get update
        sudo apt-get --no-install-recommends install -y clang-format-14 clang-tidy-14 shellcheck
        python3 -m pip install yapf==0.31.0
        sudo snap install shfmt
        npm install prettier@2.0.4
    - name: Check
      run: |
        script/make-pretty check

  markdown-lint-check:
    runs-on: ubuntu-latest
    steps:
    - name: Harden Runner
      uses: step-security/harden-runner@91182cccc01eb5e619899d80e4e971d6181294a7 # v2.10.1
      with:
        egress-policy: audit # TODO: change to 'egress-policy: block' after couple of runs

    - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
    - name: Run linkspector
      uses: umbrelladocs/action-linkspector@v1
      with:
        github_token: ${{ secrets.GITHUB_TOKEN }}
        reporter: github-pr-review
        fail_on_error: true

  spell-check:
    runs-on: ubuntu-22.04
    steps:
    - name: Harden Runner
      uses: step-security/harden-runner@91182cccc01eb5e619899d80e4e971d6181294a7 # v2.10.1
      with:
        egress-policy: audit # TODO: change to 'egress-policy: block' after couple of runs

    - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      with:
        submodules: true
    - uses: actions/setup-python@f677139bbe7f9c59b41e40162b753c062f5d49a3 # v5.2.0
      with:
        python-version: '3.12'
        cache: pip
    - name: Bootstrap
      run: |
          python -m pip install --upgrade pip
          pip install --force-reinstall codespell==2.2.4
    - name: Check
      run: |
        script/code-spell check

  cmake-version:
    runs-on: ubuntu-20.04
    steps:
    - name: Harden Runner
      uses: step-security/harden-runner@91182cccc01eb5e619899d80e4e971d6181294a7 # v2.10.1
      with:
        egress-policy: audit # TODO: change to 'egress-policy: block' after couple of runs

    - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      with:
        submodules: true
    - name: Bootstrap
      run: |
        sudo apt-get --no-install-recommends install -y build-essential ninja-build libreadline-dev libncurses-dev
        sudo apt-get remove cmake
        sudo apt-get purge --auto-remove cmake
        wget http://www.cmake.org/files/v3.10/cmake-3.10.3.tar.gz
        tar xf cmake-3.10.3.tar.gz
        cd cmake-3.10.3
        ./configure
        sudo make install
        cmake --version | grep 3.10.3
    - name: Build
      run: |
        OT_NODE_TYPE=rcp ./script/test build

  package:
    name: package-${{ matrix.compiler }}
    runs-on: ubuntu-20.04
    strategy:
      fail-fast: false
      matrix:
        include:
          - compiler: gcc
            compiler_c: gcc
            compiler_cpp: g++
          - compiler: clang
            compiler_c: clang
            compiler_cpp: clang++
    env:
      CC: ${{ matrix.compiler_c }}
      CXX: ${{ matrix.compiler_cpp }}
    steps:
    - name: Harden Runner
      uses: step-security/harden-runner@91182cccc01eb5e619899d80e4e971d6181294a7 # v2.10.1
      with:
        egress-policy: audit # TODO: change to 'egress-policy: block' after couple of runs

    - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      with:
        submodules: true
    - name: Bootstrap
      run: |
        sudo rm /etc/apt/sources.list.d/* && sudo apt-get update
        sudo apt-get --no-install-recommends install -y ninja-build libreadline-dev libncurses-dev
    - name: Package
      run: |
        script/test package

  scan-build:
    runs-on: ubuntu-22.04
    steps:
    - name: Harden Runner
      uses: step-security/harden-runner@91182cccc01eb5e619899d80e4e971d6181294a7 # v2.10.1
      with:
        egress-policy: audit # TODO: change to 'egress-policy: block' after couple of runs

    - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      with:
        submodules: true
    - name: Bootstrap
      run: |
        sudo rm /etc/apt/sources.list.d/* && sudo apt-get update
        sudo apt-get --no-install-recommends install -y clang-tools-14 ninja-build
    - name: Run
      run: |
        script/check-scan-build

  mbedtls2-build:
    runs-on: ubuntu-20.04
    steps:
    - name: Harden Runner
      uses: step-security/harden-runner@91182cccc01eb5e619899d80e4e971d6181294a7 # v2.10.1
      with:
        egress-policy: audit # TODO: change to 'egress-policy: block' after couple of runs

    - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      with:
        submodules: true
    - name: Bootstrap
      run: |
        sudo apt-get --no-install-recommends install -y ninja-build libreadline-dev libncurses-dev
        rm -rf third_party/mbedtls/repo
    - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      with:
        repository: ARMmbed/mbedtls
        ref: v2.28.8
        path: third_party/mbedtls/repo
    - name: Build
      run: |
        ./script/test build

  arm-gcc:
    name: arm-gcc-${{ matrix.gcc_ver }}
    runs-on: ubuntu-20.04
    strategy:
      fail-fast: false
      matrix:
        include:
          - gcc_ver: 4
            gcc_download_url: https://launchpad.net/gcc-arm-embedded/4.9/4.9-2015-q3-update/+download/gcc-arm-none-eabi-4_9-2015q3-20150921-linux.tar.bz2
            gcc_extract_dir: gcc-arm-none-eabi-4_9-2015q3
          - gcc_ver: 5
            gcc_download_url: https://developer.arm.com/-/media/Files/downloads/gnu-rm/5_4-2016q3/gcc-arm-none-eabi-5_4-2016q3-20160926-linux.tar.bz2
            gcc_extract_dir: gcc-arm-none-eabi-5_4-2016q3
          - gcc_ver: 6
            gcc_download_url: https://developer.arm.com/-/media/Files/downloads/gnu-rm/6-2017q2/gcc-arm-none-eabi-6-2017-q2-update-linux.tar.bz2
            gcc_extract_dir: gcc-arm-none-eabi-6-2017-q2-update
          - gcc_ver: 7
            gcc_download_url: https://developer.arm.com/-/media/Files/downloads/gnu-rm/7-2018q2/gcc-arm-none-eabi-7-2018-q2-update-linux.tar.bz2
            gcc_extract_dir: gcc-arm-none-eabi-7-2018-q2-update
          - gcc_ver: 9
            gcc_download_url: https://developer.arm.com/-/media/Files/downloads/gnu-rm/9-2019q4/RC2.1/gcc-arm-none-eabi-9-2019-q4-major-x86_64-linux.tar.bz2
            gcc_extract_dir: gcc-arm-none-eabi-9-2019-q4-major
          - gcc_ver: 10
            gcc_download_url: https://developer.arm.com/-/media/Files/downloads/gnu-rm/10.3-2021.10/gcc-arm-none-eabi-10.3-2021.10-x86_64-linux.tar.bz2
            gcc_extract_dir: gcc-arm-none-eabi-10.3-2021.10
          - gcc_ver: 11
            gcc_download_url: https://developer.arm.com/-/media/Files/downloads/gnu/11.3.rel1/binrel/arm-gnu-toolchain-11.3.rel1-x86_64-arm-none-eabi.tar.xz
            gcc_extract_dir: arm-gnu-toolchain-11.3.rel1-x86_64-arm-none-eabi
          - gcc_ver: 12
            gcc_download_url: https://developer.arm.com/-/media/Files/downloads/gnu/12.2.rel1/binrel/arm-gnu-toolchain-12.2.rel1-x86_64-arm-none-eabi.tar.xz
            gcc_extract_dir: arm-gnu-toolchain-12.2.rel1-x86_64-arm-none-eabi
          - gcc_ver: 13
            gcc_download_url: https://developer.arm.com/-/media/Files/downloads/gnu/13.2.rel1/binrel/arm-gnu-toolchain-13.2.rel1-x86_64-arm-none-eabi.tar.xz
            gcc_extract_dir: arm-gnu-toolchain-13.2.Rel1-x86_64-arm-none-eabi
    steps:
    - name: Harden Runner
      uses: step-security/harden-runner@91182cccc01eb5e619899d80e4e971d6181294a7 # v2.10.1
      with:
        egress-policy: audit # TODO: change to 'egress-policy: block' after couple of runs

    - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      with:
        submodules: true
    - name: Bootstrap
      run: |
        cd /tmp
        sudo rm /etc/apt/sources.list.d/* && sudo apt-get update
        sudo apt-get --no-install-recommends install -y build-essential lib32z1 ninja-build gcc-arm-linux-gnueabihf g++-arm-linux-gnueabihf
        wget --tries 4 --no-check-certificate --quiet ${{ matrix.gcc_download_url }} -O gcc-arm
        tar xf gcc-arm
        sudo apt-get remove cmake
        sudo apt-get purge --auto-remove cmake
        wget http://www.cmake.org/files/v3.10/cmake-3.10.3.tar.gz
        tar xf cmake-3.10.3.tar.gz
        cd cmake-3.10.3
        ./configure
        sudo make install
        cmake --version | grep 3.10.3
    - name: Build
      env:
        GITHUB_TOKEN: "${{ secrets.GITHUB_TOKEN }}"
      run: |
        export PATH=/tmp/${{ matrix.gcc_extract_dir }}/bin:$PATH
        script/check-arm-build

  gcc:
    name: gcc-${{ matrix.gcc_ver }}
    runs-on: ubuntu-22.04
    strategy:
      fail-fast: false
      matrix:
        gcc_ver: [9, 10, 11, 12]
    env:
      CC: gcc-${{ matrix.gcc_ver }}
      CXX: g++-${{ matrix.gcc_ver }}
    steps:
    - name: Harden Runner
      uses: step-security/harden-runner@91182cccc01eb5e619899d80e4e971d6181294a7 # v2.10.1
      with:
        egress-policy: audit # TODO: change to 'egress-policy: block' after couple of runs

    - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      with:
        submodules: true
    - name: Bootstrap
      run: |
        sudo rm /etc/apt/sources.list.d/* && sudo apt-get update
        case ${{ matrix.gcc_ver }} in
          11)
            sudo add-apt-repository -y ppa:ubuntu-toolchain-r/test
            ;;
        esac
        sudo apt-get --no-install-recommends install -y gcc-${{ matrix.gcc_ver }} g++-${{ matrix.gcc_ver }} ninja-build libreadline-dev libncurses-dev
    - name: Build
      run: |
        script/check-simulation-build
        script/check-posix-build

  clang:
    name: clang-${{ matrix.clang_ver }}
    runs-on: ubuntu-20.04
    strategy:
      fail-fast: false
      matrix:
        clang_ver: ["17", "18", "19"]
    env:
      CC: clang-${{ matrix.clang_ver }}
      CXX: clang++-${{ matrix.clang_ver }}
    steps:
      - name: Harden Runner
        uses: step-security/harden-runner@91182cccc01eb5e619899d80e4e971d6181294a7 # v2.10.1
        with:
          egress-policy: audit # TODO: change to 'egress-policy: block' after couple of runs

      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          submodules: true
      - name: Bootstrap
        run: |
          wget https://apt.llvm.org/llvm.sh
          chmod +x llvm.sh
          sudo ./llvm.sh ${{ matrix.clang_ver }}
          sudo apt-get update
          sudo apt-get --no-install-recommends install -y ninja-build libreadline-dev libncurses-dev
      - name: Build
        run: |
          script/check-simulation-build
          script/check-posix-build

  gn:
    runs-on: ubuntu-20.04
    steps:
    - name: Harden Runner
      uses: step-security/harden-runner@91182cccc01eb5e619899d80e4e971d6181294a7 # v2.10.1
      with:
        egress-policy: audit # TODO: change to 'egress-policy: block' after couple of runs

    - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      with:
        submodules: true
    - name: Bootstrap
      run: |
        sudo rm /etc/apt/sources.list.d/* && sudo apt-get update
        sudo apt-get --no-install-recommends install -y ninja-build
        cd /tmp
        wget -O gn.zip https://chrome-infra-packages.appspot.com/dl/gn/gn/linux-amd64/+/latest
        unzip -o gn.zip
        chmod a+x gn && mkdir -p bin && mv -f gn bin/
    - name: Build
      run: |
        export PATH=/tmp/bin:$PATH
        script/check-gn-build

  macos:
    name: macos-${{ matrix.CC }}
    strategy:
      fail-fast: false
      matrix:
        include:
          - CC: clang
            CXX: clang++
          - CC: gcc
            CXX: g++
    runs-on: macos-14
    env:
      CC: ${{ matrix.CC }}
      CXX: ${{ matrix.CXX }}
    steps:
    - name: Harden Runner
      uses: step-security/harden-runner@91182cccc01eb5e619899d80e4e971d6181294a7 # v2.10.1
      with:
        egress-policy: audit # TODO: change to 'egress-policy: block' after couple of runs

    - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      with:
        submodules: true
    - name: Bootstrap
      run: |
        brew update
        wget --tries 4 https://github.com/ninja-build/ninja/releases/download/v1.11.1/ninja-mac.zip
        unzip ninja-mac.zip && mv ninja /usr/local/bin/.
    - name: Build
      run: |
        script/check-posix-build
        script/check-simulation-build

  android-ndk:
    name: android-ndk
    runs-on: ubuntu-22.04
    container:
      image: openthread/environment
    steps:
    - name: Harden Runner
      uses: step-security/harden-runner@91182cccc01eb5e619899d80e4e971d6181294a7 # v2.10.1
      with:
        egress-policy: audit # TODO: change to 'egress-policy: block' after couple of runs

    - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      with:
        submodules: true
    - name: Install unzip
      run: apt update && apt install -y unzip
    - name: Setup NDK
      id: setup-ndk
      uses: nttld/setup-ndk@v1
      with:
        ndk-version: r25c
        local-cache: true

    - name: Build
      env:
        NDK: ${{ steps.setup-ndk.outputs.ndk-path }}
      run: |
        rm -rf build/ && OT_CMAKE_NINJA_TARGET="ot-daemon ot-ctl" script/cmake-build android-ndk
        rm -rf build/ && OT_CMAKE_NINJA_TARGET="ot-cli" script/cmake-build android-ndk
