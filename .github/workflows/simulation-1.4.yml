#
#  Copyright (c) 2020, The OpenThread Authors.
#  All rights reserved.
#
#  Redistribution and use in source and binary forms, with or without
#  modification, are permitted provided that the following conditions are met:
#  1. Redistributions of source code must retain the above copyright
#     notice, this list of conditions and the following disclaimer.
#  2. Redistributions in binary form must reproduce the above copyright
#     notice, this list of conditions and the following disclaimer in the
#     documentation and/or other materials provided with the distribution.
#  3. Neither the name of the copyright holder nor the
#     names of its contributors may be used to endorse or promote products
#     derived from this software without specific prior written permission.
#
#  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
#  AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
#  IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
#  ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE
#  LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, <PERSON>ECIAL, E<PERSON>EMPLARY, OR
#  CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
#  SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
#  INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
#  CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
#  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
#  POSSIBILITY OF SUCH DAMAGE.
#

name: Simulation 1.4

on:
  push:
    branches-ignore:
      - 'dependabot/**'
  pull_request:
    branches:
      - 'main'

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || (github.repository == 'openthread/openthread' && github.run_id) || github.ref }}
  cancel-in-progress: true

permissions:  # added using https://github.com/step-security/secure-workflows
  contents: read

jobs:

  thread-1-4:
    name: thread-1-4-${{ matrix.compiler.c }}-${{ matrix.arch }}
    runs-on: ubuntu-20.04
    env:
      CFLAGS: -${{ matrix.arch }}
      CXXFLAGS: -${{ matrix.arch }}
      LDFLAGS: -${{ matrix.arch }}
      COVERAGE: 1
      THREAD_VERSION: 1.4
      VIRTUAL_TIME: 1
      INTER_OP: 1
      INTER_OP_BBR: 1
      CC: ${{ matrix.compiler.c }}
      CXX: ${{ matrix.compiler.cxx }}
    strategy:
      fail-fast: false
      matrix:
        compiler: [{c: "gcc", cxx: "g++", gcov: "gcc"}, { c: "clang-10", cxx: "clang++-10", gcov: "llvm"}]
        arch: ["m32", "m64"]
    steps:
    - name: Harden Runner
      uses: step-security/harden-runner@91182cccc01eb5e619899d80e4e971d6181294a7 # v2.10.1
      with:
        egress-policy: audit # TODO: change to 'egress-policy: block' after couple of runs

    - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      with:
        submodules: true
    - uses: actions/setup-python@f677139bbe7f9c59b41e40162b753c062f5d49a3 # v5.2.0
      with:
        python-version: '3.12'
        cache: pip
    - name: Bootstrap
      run: |
        sudo dpkg --add-architecture i386
        sudo apt-get update
        sudo apt-get --no-install-recommends install -y clang-10 clang++-10 ninja-build llvm lcov
        sudo apt-get --no-install-recommends install -y g++-multilib libreadline-dev:i386 libncurses-dev:i386
        python3 -m pip install -r tests/scripts/thread-cert/requirements.txt
    - name: Build
      run: |
        ./script/test build
    - name: Run
      run: |
        ulimit -c unlimited
        ./script/test prepare_coredump_upload
        ./script/test unit
        ./script/test cert_suite tests/scripts/thread-cert/v1_2_*
    - name: Check Crash
      if: ${{ failure() }}
      run: |
          CRASHED=$(./script/test check_crash | tail -1)
          [[ $CRASHED -eq "1" ]] && echo "Crashed!" || echo "Not crashed."
          echo "CRASHED=$CRASHED" >> $GITHUB_ENV
    - uses: actions/upload-artifact@50769540e7f4bd5e21e526ee35c689e35e0d6874 # v4.4.0
      if: ${{ failure() }}
      with:
        name: thread-1-4-${{ matrix.compiler.c }}-${{ matrix.arch }}-pcaps
        path: "*.pcap"
    - uses: actions/upload-artifact@50769540e7f4bd5e21e526ee35c689e35e0d6874 # v4.4.0
      if: ${{ failure() && env.CRASHED == '1' }}
      with:
        name: core-packet-verification-thread-1-4
        path: |
          ./ot-core-dump/*
    - name: Generate Coverage
      run: |
        ./script/test generate_coverage "${{ matrix.compiler.gcov }}"
    - uses: actions/upload-artifact@50769540e7f4bd5e21e526ee35c689e35e0d6874 # v4.4.0
      with:
        name: cov-thread-1-4-${{ matrix.compiler.c }}-${{ matrix.arch }}
        path: tmp/coverage.info
        retention-days: 1

  packet-verification-low-power:
    runs-on: ubuntu-20.04
    env:
      REFERENCE_DEVICE: 1
      VIRTUAL_TIME: 1
      COVERAGE: 1
      PACKET_VERIFICATION: 1
      THREAD_VERSION: 1.4
      MAC_FILTER: 1
      INTER_OP: 1
      INTER_OP_BBR: 0
    steps:
    - name: Harden Runner
      uses: step-security/harden-runner@91182cccc01eb5e619899d80e4e971d6181294a7 # v2.10.1
      with:
        egress-policy: audit # TODO: change to 'egress-policy: block' after couple of runs

    - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      with:
        submodules: true
    - uses: actions/setup-python@f677139bbe7f9c59b41e40162b753c062f5d49a3 # v5.2.0
      with:
        python-version: '3.12'
        cache: pip
    - name: Bootstrap
      run: |
        sudo rm /etc/apt/sources.list.d/* && sudo apt-get update
        sudo apt-get --no-install-recommends install -y ninja-build lcov
        python3 -m pip install -r tests/scripts/thread-cert/requirements.txt
    - name: Build
      run: |
        ./script/test build
    - name: Build with OT_CSL_RECEIVER_LOCAL_TIME_SYNC
      run: |
        OT_BUILDDIR="${PWD}/build_csl_receiver_local_time_sync" OT_OPTIONS="-DOT_CSL_RECEIVER_LOCAL_TIME_SYNC=ON" ./script/test build
    - name: Get Thread-Wireshark
      run: |
        ./script/test get_thread_wireshark
    - name: Run
      run: |
        ulimit -c unlimited
        ./script/test prepare_coredump_upload
        for i in {1..10}
        do
          ./script/test cert_suite ./tests/scripts/thread-cert/v1_2_LowPower*.py
        done
    - name: Run with OT_CSL_RECEIVER_LOCAL_TIME_SYNC
      run: |
        OT_BUILDDIR="${PWD}/build_csl_receiver_local_time_sync" ./script/test cert_suite ./tests/scripts/thread-cert/v1_2_LowPower*.py
    - name: Check Crash
      if: ${{ failure() }}
      run: |
          CRASHED=$(./script/test check_crash | tail -1)
          [[ $CRASHED -eq "1" ]] && echo "Crashed!" || echo "Not crashed."
          echo "CRASHED=$CRASHED" >> $GITHUB_ENV
    - uses: actions/upload-artifact@50769540e7f4bd5e21e526ee35c689e35e0d6874 # v4.4.0
      if: ${{ failure() }}
      with:
        name: packet-verification-low-power-pcaps
        path: |
          *.pcap
          *.json
    - uses: actions/upload-artifact@50769540e7f4bd5e21e526ee35c689e35e0d6874 # v4.4.0
      if: ${{ failure() && env.CRASHED == '1' }}
      with:
        name: core-packet-verification-low-power
        path: |
          ./ot-core-dump/*
    - name: Generate Coverage
      run: |
        ./script/test generate_coverage gcc
    - uses: actions/upload-artifact@50769540e7f4bd5e21e526ee35c689e35e0d6874 # v4.4.0
      with:
        name: cov-packet-verification-low-power
        path: tmp/coverage.info
        retention-days: 1

  packet-verification-1-1-on-1-4:
    runs-on: ubuntu-20.04
    env:
      REFERENCE_DEVICE: 1
      VIRTUAL_TIME: 1
      PACKET_VERIFICATION: 1
      THREAD_VERSION: 1.4
      INTER_OP_BBR: 1
      MULTIPLY: 3
    steps:
    - name: Harden Runner
      uses: step-security/harden-runner@91182cccc01eb5e619899d80e4e971d6181294a7 # v2.10.1
      with:
        egress-policy: audit # TODO: change to 'egress-policy: block' after couple of runs

    - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      with:
        submodules: true
    - uses: actions/setup-python@f677139bbe7f9c59b41e40162b753c062f5d49a3 # v5.2.0
      with:
        python-version: '3.12'
        cache: pip
    - name: Bootstrap
      run: |
        sudo rm /etc/apt/sources.list.d/* && sudo apt-get update
        sudo apt-get --no-install-recommends install -y ninja-build lcov
        python3 -m pip install -r tests/scripts/thread-cert/requirements.txt
    - name: Build
      run: |
        ./script/test build
    - name: Get Thread-Wireshark
      run: |
        ./script/test get_thread_wireshark
    - name: Run
      run: |
        ./script/test cert_suite ./tests/scripts/thread-cert/Cert_*.py ./tests/scripts/thread-cert/test_*.py
    - uses: actions/upload-artifact@50769540e7f4bd5e21e526ee35c689e35e0d6874 # v4.4.0
      if: ${{ failure() }}
      with:
        name: packet-verification-1.1-on-1.4-pcaps
        path: |
          *.pcap
          *.json
    - name: Generate Coverage
      run: |
        ./script/test generate_coverage gcc
    - uses: actions/upload-artifact@50769540e7f4bd5e21e526ee35c689e35e0d6874 # v4.4.0
      with:
        name: cov-packet-verification-1-1-on-1-4
        path: tmp/coverage.info
        retention-days: 1

  channel-manager-csl:
    runs-on: ubuntu-20.04
    env:
      CFLAGS: -m32
      CXXFLAGS: -m32
      LDFLAGS: -m32
      COVERAGE: 1
      THREAD_VERSION: 1.4
      VIRTUAL_TIME: 1
    steps:
    - name: Harden Runner
      uses: step-security/harden-runner@91182cccc01eb5e619899d80e4e971d6181294a7 # v2.10.1
      with:
        egress-policy: audit # TODO: change to 'egress-policy: block' after couple of runs

    - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      with:
        submodules: true
    - uses: actions/setup-python@f677139bbe7f9c59b41e40162b753c062f5d49a3 # v5.2.0
      with:
        python-version: '3.12'
        cache: pip
    - name: Bootstrap
      run: |
        sudo rm /etc/apt/sources.list.d/* && sudo apt-get update
        sudo apt-get --no-install-recommends install -y g++-multilib lcov ninja-build
        python3 -m pip install -r tests/scripts/thread-cert/requirements.txt
    - name: Build
      run: |
        OT_OPTIONS="-DOT_CHANNEL_MANAGER_CSL=ON" ./script/test build
    - name: Run
      run: |
        ulimit -c unlimited
        ./script/test cert_suite ./tests/scripts/thread-cert/addon_test_channel_manager_autocsl*.py
    - uses: actions/upload-artifact@50769540e7f4bd5e21e526ee35c689e35e0d6874 # v4.4.0
      if: ${{ failure() }}
      with:
        name: channel-manager-csl
        path: ot_testing
    - name: Generate Coverage
      run: |
        ./script/test generate_coverage gcc
    - uses: actions/upload-artifact@50769540e7f4bd5e21e526ee35c689e35e0d6874 # v4.4.0
      with:
        name: cov-channel-manager-csl
        path: tmp/coverage.info
        retention-days: 1

  expects:
    runs-on: ubuntu-20.04
    env:
      COVERAGE: 1
      THREAD_VERSION: 1.4
      VIRTUAL_TIME: 0
    steps:
    - name: Harden Runner
      uses: step-security/harden-runner@91182cccc01eb5e619899d80e4e971d6181294a7 # v2.10.1
      with:
        egress-policy: audit # TODO: change to 'egress-policy: block' after couple of runs

    - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      with:
        submodules: true
    - uses: actions/setup-python@f677139bbe7f9c59b41e40162b753c062f5d49a3 # v5.2.0
      with:
        python-version: '3.12'
        cache: pip
    - name: Bootstrap
      run: |
        sudo apt-get --no-install-recommends install -y expect ninja-build lcov socat
        pip install bleak 'cryptography==43.0.0'
    - name: Run RCP Mode
      run: |
        ulimit -c unlimited
        ./script/test prepare_coredump_upload
        OT_OPTIONS=-DOT_READLINE=OFF OT_NODE_TYPE=rcp ./script/test build expect
    - name: Check Crash
      if: ${{ failure() }}
      run: |
          CRASHED=$(./script/test check_crash | tail -1)
          [[ $CRASHED -eq "1" ]] && echo "Crashed!" || echo "Not crashed."
          echo "CRASHED=$CRASHED" >> $GITHUB_ENV
    - uses: actions/upload-artifact@50769540e7f4bd5e21e526ee35c689e35e0d6874 # v4.4.0
      if: ${{ failure() && env.CRASHED == '1' }}
      with:
        name: core-expect-1-4
        path: |
          ./ot-core-dump/*
    - name: Generate Coverage
      run: |
        ./script/test generate_coverage gcc
    - uses: actions/upload-artifact@50769540e7f4bd5e21e526ee35c689e35e0d6874 # v4.4.0
      with:
        name: cov-expects
        path: tmp/coverage.info
        retention-days: 1

  thread-1-4-posix:
    runs-on: ubuntu-20.04
    env:
      COVERAGE: 1
      PYTHONUNBUFFERED: 1
      READLINE: readline
      THREAD_VERSION: 1.4
      OT_NODE_TYPE: rcp
      USE_MTD: 1
      VIRTUAL_TIME: 1
      INTER_OP: 1
    steps:
    - name: Harden Runner
      uses: step-security/harden-runner@91182cccc01eb5e619899d80e4e971d6181294a7 # v2.10.1
      with:
        egress-policy: audit # TODO: change to 'egress-policy: block' after couple of runs

    - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      with:
        submodules: true
    - uses: actions/setup-python@f677139bbe7f9c59b41e40162b753c062f5d49a3 # v5.2.0
      with:
        python-version: '3.12'
        cache: pip
    - name: Bootstrap
      run: |
        sudo rm /etc/apt/sources.list.d/* && sudo apt-get update
        sudo apt-get --no-install-recommends install -y libreadline6-dev ninja-build llvm lcov
        python3 -m pip install -r tests/scripts/thread-cert/requirements.txt
    - name: Build
      run: |
        ./script/test build
    - name: Run
      run: |
        ulimit -c unlimited
        ./script/test prepare_coredump_upload
        ./script/test cert tests/scripts/thread-cert/v1_2_LowPower_5_3_01_SSEDAttachment.py
        ./script/test cert tests/scripts/thread-cert/v1_2_LowPower_6_1_07_PreferringARouterOverAReed.py
        ./script/test cert tests/scripts/thread-cert/v1_2_router_5_1_1.py
        ./script/test cert tests/scripts/thread-cert/v1_2_test_csl_transmission.py
        ./script/test cert tests/scripts/thread-cert/v1_2_test_enhanced_frame_pending.py
        ./script/test cert tests/scripts/thread-cert/v1_2_test_parent_selection.py
    - name: Check Crash
      if: ${{ failure() }}
      run: |
          CRASHED=$(./script/test check_crash | tail -1)
          [[ $CRASHED -eq "1" ]] && echo "Crashed!" || echo "Not crashed."
          echo "CRASHED=$CRASHED" >> $GITHUB_ENV
    - uses: actions/upload-artifact@50769540e7f4bd5e21e526ee35c689e35e0d6874 # v4.4.0
      if: ${{ failure() }}
      with:
        name: thread-1-4-posix-pcaps
        path: "*.pcap"
    - uses: actions/upload-artifact@50769540e7f4bd5e21e526ee35c689e35e0d6874 # v4.4.0
      if: ${{ failure() && env.CRASHED == '1' }}
      with:
        name: core-thread-1-4-posix
        path: |
            ./ot-core-dump/*
    - name: Generate Coverage
      run: |
        ./script/test generate_coverage gcc
    - uses: actions/upload-artifact@50769540e7f4bd5e21e526ee35c689e35e0d6874 # v4.4.0
      with:
        name: cov-thread-1-4-posix
        path: tmp/coverage.info
        retention-days: 1

  upload-coverage:
    needs:
    - thread-1-4
    - packet-verification-low-power
    - packet-verification-1-1-on-1-4
    - expects
    - thread-1-4-posix
    runs-on: ubuntu-20.04
    steps:
    - name: Harden Runner
      uses: step-security/harden-runner@91182cccc01eb5e619899d80e4e971d6181294a7 # v2.10.1
      with:
        egress-policy: audit # TODO: change to 'egress-policy: block' after couple of runs

    - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      with:
        submodules: true
    - name: Bootstrap
      run: |
        sudo apt-get --no-install-recommends install -y lcov
    - uses: actions/download-artifact@65a9edc5881444af0b9093a5e628f2fe47ea3b2e # v4.1.7
      with:
        path: coverage/
        pattern: cov-*
        merge-multiple: true
    - name: Combine Coverage
      run: |
        script/test combine_coverage
    - name: Upload Coverage
      uses: codecov/codecov-action@e28ff129e5465c2c0dcc6f003fc735cb6ae0c673 # v4.5.0
      env:
        CODECOV_TOKEN: ${{ secrets.CODECOV_TOKEN }}
      with:
        files: final.info
        fail_ci_if_error: true
