#
#  Copyright (c) 2020, The OpenThread Authors.
#  All rights reserved.
#
#  Redistribution and use in source and binary forms, with or without
#  modification, are permitted provided that the following conditions are met:
#  1. Redistributions of source code must retain the above copyright
#     notice, this list of conditions and the following disclaimer.
#  2. Redistributions in binary form must reproduce the above copyright
#     notice, this list of conditions and the following disclaimer in the
#     documentation and/or other materials provided with the distribution.
#  3. Neither the name of the copyright holder nor the
#     names of its contributors may be used to endorse or promote products
#     derived from this software without specific prior written permission.
#
#  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
#  AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
#  IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
#  ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE
#  LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, <PERSON>ECIAL, E<PERSON>EMPLARY, OR
#  CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
#  SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
#  INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
#  CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
#  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
#  POSSIBILITY OF SUCH DAMAGE.
#

name: Docker

on:
  push:
    branches-ignore:
      - 'dependabot/**'
  pull_request:
    branches:
      - 'main'

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || (github.repository == 'openthread/openthread' && github.run_id) || github.ref }}
  cancel-in-progress: true

permissions:  # added using https://github.com/step-security/secure-workflows
  contents: read

jobs:

  buildx:
    name: buildx-${{ matrix.docker_name }}
    runs-on: ubuntu-20.04
    strategy:
      fail-fast: false
      matrix:
        include:
          - docker_name: environment
    steps:
    - name: Harden Runner
      uses: step-security/harden-runner@91182cccc01eb5e619899d80e4e971d6181294a7 # v2.10.1
      with:
        egress-policy: audit # TODO: change to 'egress-policy: block' after couple of runs

    - name: Free Disk Space (Ubuntu)
      uses: jlumbroso/free-disk-space@main

    - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      with:
        submodules: true

    - name: Prepare
      id: prepare
      run: |
        DOCKER_IMAGE=openthread/${{ matrix.docker_name }}
        DOCKER_FILE=etc/docker/${{ matrix.docker_name }}/Dockerfile
        DOCKER_PLATFORMS=linux/amd64,linux/arm64
        VERSION=latest

        TAGS="--tag ${DOCKER_IMAGE}:${VERSION}"

        echo "docker_image=${DOCKER_IMAGE}" >> $GITHUB_OUTPUT
        echo "version=${VERSION}" >> $GITHUB_OUTPUT
        echo "buildx_args=--platform ${DOCKER_PLATFORMS} \
          --build-arg OT_GIT_REF=${{ github.sha }} \
          --build-arg VERSION=${VERSION} \
          --build-arg BUILD_DATE=$(date -u +'%Y-%m-%dT%H:%M:%SZ') \
          --build-arg VCS_REF=${GITHUB_SHA::8} \
          ${TAGS} --file ${DOCKER_FILE} ." >> $GITHUB_OUTPUT

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@4fd812986e6c8c2a69e18311145f9371337f27d4 # v3.4.0

    - name: Docker Buildx (build)
      run: |
        docker buildx build --output "type=image,push=false" ${{ steps.prepare.outputs.buildx_args }}

    - name: Login to DockerHub
      if: success() && github.repository == 'openthread/openthread' && github.event_name != 'pull_request'
      uses: docker/login-action@9780b0c442fbb1117ed29e0efdff1e18412f7567 # v3.3.0
      with:
        username: ${{ secrets.DOCKER_USERNAME }}
        password: ${{ secrets.DOCKER_PASSWORD }}

    - name: Docker Buildx (push)
      if: success() && github.repository == 'openthread/openthread' && github.event_name != 'pull_request'
      run: |
        docker buildx build --output "type=image,push=true" ${{ steps.prepare.outputs.buildx_args }}

    - name: Inspect Image
      if: always() && github.repository == 'openthread/openthread' && github.event_name != 'pull_request'
      run: |
        docker buildx imagetools inspect ${{ steps.prepare.outputs.docker_image }}:${{ steps.prepare.outputs.version }}
