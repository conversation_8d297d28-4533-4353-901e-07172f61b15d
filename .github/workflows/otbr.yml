#
#  Copyright (c) 2021, The OpenThread Authors.
#  All rights reserved.
#
#  Redistribution and use in source and binary forms, with or without
#  modification, are permitted provided that the following conditions are met:
#  1. Redistributions of source code must retain the above copyright
#     notice, this list of conditions and the following disclaimer.
#  2. Redistributions in binary form must reproduce the above copyright
#     notice, this list of conditions and the following disclaimer in the
#     documentation and/or other materials provided with the distribution.
#  3. Neither the name of the copyright holder nor the
#     names of its contributors may be used to endorse or promote products
#     derived from this software without specific prior written permission.
#
#  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
#  AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
#  IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
#  ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE
#  LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, <PERSON>ECIAL, <PERSON><PERSON>EMPLARY, OR
#  <PERSON><PERSON><PERSON>QUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
#  SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
#  INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
#  CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
#  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
#  POSSIBILITY OF SUCH DAMAGE.
#

name: Border Router

on:
  push:
    branches-ignore:
      - 'dependabot/**'
  pull_request:
    branches:
      - 'main'

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || (github.repository == 'openthread/openthread' && github.run_id) || github.ref }}
  cancel-in-progress: true

permissions:
  contents: read

jobs:

  backbone-router:
    runs-on: ubuntu-20.04
    env:
      REFERENCE_DEVICE: 1
      VIRTUAL_TIME: 0
      PACKET_VERIFICATION: 1
      THREAD_VERSION: 1.4
      INTER_OP: 1
      COVERAGE: 1
      MULTIPLY: 1
      PYTHONUNBUFFERED: 1
      VERBOSE: 1
      # The Border Routing and DUA feature can coexist, but current wireshark
      # packet verification can't handle it because of the order of context ID
      # of OMR prefix and Domain prefix is not deterministic.
      BORDER_ROUTING: 0
    steps:
    - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      with:
        submodules: true
    - name: Build OTBR Docker
      env:
        GITHUB_TOKEN: "${{ secrets.GITHUB_TOKEN }}"
      run: |
        ./script/test build_otbr_docker
    - name: Bootstrap
      run: |
        sudo rm /etc/apt/sources.list.d/* && sudo apt-get update
        sudo apt-get --no-install-recommends install -y python3-setuptools python3-wheel ninja-build socat lcov
        python3 -m pip install -r tests/scripts/thread-cert/requirements.txt
    - name: Build
      run: |
        ./script/test build
    - name: Get Thread-Wireshark
      run: |
        ./script/test get_thread_wireshark
    - name: Run
      run: |
        export CI_ENV="$(bash <(curl -s https://codecov.io/env)) -e GITHUB_ACTIONS -e COVERAGE"
        echo "CI_ENV=${CI_ENV}"
        sudo -E ./script/test cert_suite ./tests/scripts/thread-cert/backbone/*.py || (sudo chmod a+r ot_testing/* && false)
    - uses: actions/upload-artifact@50769540e7f4bd5e21e526ee35c689e35e0d6874 # v4.4.0
      with:
        name: cov-thread-1-3-backbone-docker
        path: /tmp/coverage/
        retention-days: 1
    - uses: actions/upload-artifact@50769540e7f4bd5e21e526ee35c689e35e0d6874 # v4.4.0
      if: ${{ failure() }}
      with:
        name: thread-1-3-backbone-results
        path: |
          ot_testing/*.pcap
          ot_testing/*.json
          ot_testing/*.log
          ot_testing/coredump_*
          ot_testing/otbr-agent_*
    - name: Generate Coverage
      run: |
        ./script/test generate_coverage gcc
    - uses: actions/upload-artifact@50769540e7f4bd5e21e526ee35c689e35e0d6874 # v4.4.0
      with:
        name: cov-thread-1-3-backbone
        path: tmp/coverage.info
        retention-days: 1

  thread-border-router:
    runs-on: ubuntu-20.04
    strategy:
      fail-fast: false
      matrix:
        include:
          - otbr_mdns: "mDNSResponder"
            otbr_trel: 0
            cert_scripts: ./tests/scripts/thread-cert/border_router/*.py
            packet_verification: 1
            nat64: 0
            description: ""
          - otbr_mdns: "mDNSResponder"
            otbr_trel: 1
            cert_scripts: ./tests/scripts/thread-cert/border_router/*.py
            packet_verification: 2
            nat64: 0
            description: ""
          - otbr_mdns: "mDNSResponder"
            otbr_trel: 0
            cert_scripts: ./tests/scripts/thread-cert/border_router/MATN/*.py
            packet_verification: 1
            nat64: 0
            description: "MATN"
          - otbr_mdns: "mDNSResponder"
            otbr_trel: 0
            cert_scripts: ./tests/scripts/thread-cert/border_router/LowPower/*.py
            packet_verification: 1
            nat64: 0
            description: "LowPower"
          - otbr_mdns: "mDNSResponder"
            otbr_trel: 0
            cert_scripts: ./tests/scripts/thread-cert/border_router/internet/*.py
            packet_verification: 1
            nat64: 1
            description: "internet access"
          - otbr_mdns: "avahi"
            otbr_trel: 0
            cert_scripts: ./tests/scripts/thread-cert/border_router/*.py
            packet_verification: 1
            nat64: 0
            description: ""
          - otbr_mdns: "avahi"
            otbr_trel: 1
            cert_scripts: ./tests/scripts/thread-cert/border_router/*.py
            packet_verification: 2
            nat64: 0
            description: ""
          - otbr_mdns: "avahi"
            otbr_trel: 0
            cert_scripts: ./tests/scripts/thread-cert/border_router/*.py
            packet_verification: 1
            nat64: 0
            use_core_firewall: 1
            description: "core-firewall"
    name: BR ${{ matrix.description }} (${{ matrix.otbr_mdns }}, TREL=${{matrix.otbr_trel}})
    env:
      REFERENCE_DEVICE: 1
      VIRTUAL_TIME: 0
      PACKET_VERIFICATION: ${{ matrix.packet_verification }}
      THREAD_VERSION: 1.4
      INTER_OP: 1
      COVERAGE: 1
      MULTIPLY: 1
      OTBR_MDNS: ${{ matrix.otbr_mdns }}
      PYTHONUNBUFFERED: 1
      VERBOSE: 1
      BORDER_ROUTING: 1
      NAT64: ${{ matrix.nat64 }}
      MAX_JOBS: 3
    steps:
    - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
    - name: Set firewall environment variables
      if: ${{ matrix.use_core_firewall }}
      run: |
        echo "FIREWALL=0" >> $GITHUB_ENV
    - name: Build OTBR Docker
      env:
        GITHUB_TOKEN: "${{ secrets.GITHUB_TOKEN }}"
        TREL: ${{ matrix.otbr_trel }}
      run: |
        ./script/test build_otbr_docker
    - name: Bootstrap
      run: |
        sudo rm /etc/apt/sources.list.d/* && sudo apt-get update
        sudo apt-get --no-install-recommends install -y python3-setuptools python3-wheel ninja-build socat lcov
        python3 -m pip install -r tests/scripts/thread-cert/requirements.txt
    - name: Build
      run: |
        ./script/test build
    - name: Get Thread-Wireshark
      run: |
        ./script/test get_thread_wireshark
    - name: Run
      run: |
        export CI_ENV="$(bash <(curl -s https://codecov.io/env)) -e GITHUB_ACTIONS -e COVERAGE"
        echo "CI_ENV=${CI_ENV}"
        sudo -E ./script/test cert_suite ${{ matrix.cert_scripts }} || (sudo chmod a+r ot_testing/* && false)
    - uses: actions/upload-artifact@50769540e7f4bd5e21e526ee35c689e35e0d6874 # v4.4.0
      with:
        name: cov-br-docker-${{ matrix.description }}-${{ matrix.otbr_mdns }}-${{matrix.otbr_trel}}
        path: /tmp/coverage/
        retention-days: 1
    - uses: actions/upload-artifact@50769540e7f4bd5e21e526ee35c689e35e0d6874 # v4.4.0
      if: ${{ failure() }}
      with:
        name: br-results-${{ matrix.description }}-${{ matrix.otbr_mdns }}-${{matrix.otbr_trel}}
        path: |
          ot_testing/*.pcap
          ot_testing/*.json
          ot_testing/*.log
          ot_testing/coredump_*
          ot_testing/otbr-agent_*
    - name: Generate Coverage
      run: |
        ./script/test generate_coverage gcc
    - uses: actions/upload-artifact@50769540e7f4bd5e21e526ee35c689e35e0d6874 # v4.4.0
      with:
        name: cov-br-${{ matrix.description }}-${{ matrix.otbr_mdns }}-${{matrix.otbr_trel}}
        path: tmp/coverage.info
        retention-days: 1

  upload-coverage:
    needs:
    - backbone-router
    - thread-border-router
    runs-on: ubuntu-20.04
    steps:
    - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      with:
        submodules: true
    - name: Bootstrap
      run: |
        sudo apt-get --no-install-recommends install -y lcov
    - uses: actions/download-artifact@65a9edc5881444af0b9093a5e628f2fe47ea3b2e # v4.1.7
      with:
        path: coverage/
        pattern: cov-*
        merge-multiple: true
    - name: Combine Coverage
      continue-on-error: true
      run: |
        script/test combine_coverage
    - name: Upload Coverage
      continue-on-error: true
      uses: codecov/codecov-action@e28ff129e5465c2c0dcc6f003fc735cb6ae0c673 # v4.5.0
      env:
        CODECOV_TOKEN: ${{ secrets.CODECOV_TOKEN }}
      with:
        files: final.info
        fail_ci_if_error: true
