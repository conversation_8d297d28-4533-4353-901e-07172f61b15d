#
#  Copyright (c) 2020, The OpenThread Authors.
#  All rights reserved.
#
#  Redistribution and use in source and binary forms, with or without
#  modification, are permitted provided that the following conditions are met:
#  1. Redistributions of source code must retain the above copyright
#     notice, this list of conditions and the following disclaimer.
#  2. Redistributions in binary form must reproduce the above copyright
#     notice, this list of conditions and the following disclaimer in the
#     documentation and/or other materials provided with the distribution.
#  3. Neither the name of the copyright holder nor the
#     names of its contributors may be used to endorse or promote products
#     derived from this software without specific prior written permission.
#
#  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
#  AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
#  IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
#  ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE
#  LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, <PERSON>ECIAL, E<PERSON>EMPLARY, OR
#  CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
#  SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
#  INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
#  CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
#  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
#  POSSIBILITY OF SUCH DAMAGE.
#

name: POSIX

on:
  push:
    branches-ignore:
      - 'dependabot/**'
  pull_request:
    branches:
      - 'main'

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || (github.repository == 'openthread/openthread' && github.run_id) || github.ref }}
  cancel-in-progress: true

permissions:
  contents: read

jobs:

  expects-linux:
    runs-on: ubuntu-20.04
    env:
      CFLAGS: -DCLI_COAP_SECURE_USE_COAP_DEFAULT_HANDLER=1 -DOPENTHREAD_CONFIG_MLE_MAX_CHILDREN=15
      CXXFLAGS: -DCLI_COAP_SECURE_USE_COAP_DEFAULT_HANDLER=1 -DOPENTHREAD_CONFIG_MLE_MAX_CHILDREN=15
    steps:
    - name: Harden Runner
      uses: step-security/harden-runner@91182cccc01eb5e619899d80e4e971d6181294a7 # v2.10.1
      with:
        egress-policy: audit # TODO: change to 'egress-policy: block' after couple of runs

    - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
    - name: Bootstrap
      run: |
        sudo apt-get --no-install-recommends install -y expect ninja-build lcov socat
        pip install bleak 'cryptography==43.0.0'
    - name: Run RCP Mode
      run: |
        ulimit -c unlimited
        ./script/test prepare_coredump_upload
        OT_OPTIONS='-DOT_READLINE=OFF -DOT_FULL_LOGS=ON -DOT_LOG_OUTPUT=PLATFORM_DEFINED' VIRTUAL_TIME=0 OT_NODE_TYPE=rcp ./script/test build expect
    - name: Run ot-fct
      run: |
        OT_CMAKE_NINJA_TARGET="ot-fct" script/cmake-build posix
        tests/scripts/expect/ot-fct.exp
    - name: Check Crash
      if: ${{ failure() }}
      run: |
          CRASHED=$(./script/test check_crash | tail -1)
          [[ $CRASHED -eq "1" ]] && echo "Crashed!" || echo "Not crashed."
          echo "CRASHED_RCP=$CRASHED" >> $GITHUB_ENV
    - uses: actions/upload-artifact@50769540e7f4bd5e21e526ee35c689e35e0d6874 # v4.4.0
      if: ${{ failure() && env.CRASHED_RCP == '1' }}
      with:
        name: core-expect-rcp
        path: |
          ./ot-core-dump/*
    - name: Generate Coverage
      run: |
        ./script/test generate_coverage gcc
    - uses: actions/upload-artifact@50769540e7f4bd5e21e526ee35c689e35e0d6874 # v4.4.0
      with:
        name: cov-expects-linux-1
        path: tmp/coverage.info
        retention-days: 1
    - name: Run TUN Mode
      run: |
        sudo rm /etc/apt/sources.list.d/* && sudo apt-get update
        echo 0 | sudo tee /proc/sys/net/ipv6/conf/all/disable_ipv6
        sudo apt-get install --no-install-recommends -y bind9-host ntp socat
        sudo systemctl restart ntp
        sudo socat 'UDP6-LISTEN:53,fork,reuseaddr,bind=[::1]' UDP:**********:53 &
        socat 'TCP6-LISTEN:2000,fork,reuseaddr' TCP:**********:53 &
        host ipv6.google.com **********
        host ipv6.google.com ::1
        ulimit -c unlimited
        ./script/test prepare_coredump_upload
        OT_OPTIONS='-DOT_READLINE=OFF -DOT_FULL_LOGS=ON -DOT_LOG_OUTPUT=PLATFORM_DEFINED' OT_NATIVE_IP=1 VIRTUAL_TIME=0 OT_NODE_TYPE=rcp ./script/test clean build expect
    - name: Check Crash
      if: ${{ failure() }}
      run: |
          CRASHED=$(./script/test check_crash | tail -1)
          [[ $CRASHED -eq "1" ]] && echo "Crashed!" || echo "Not crashed."
          echo "CRASHED_TUN=$CRASHED" >> $GITHUB_ENV
    - uses: actions/upload-artifact@50769540e7f4bd5e21e526ee35c689e35e0d6874 # v4.4.0
      if: ${{ failure() && env.CRASHED_TUN == '1' }}
      with:
        name: core-expect-linux
        path: |
          ./ot-core-dump/*
    - uses: actions/upload-artifact@50769540e7f4bd5e21e526ee35c689e35e0d6874 # v4.4.0
      if: ${{ failure() }}
      with:
        name: syslog-expect-linux
        path: /var/log/syslog
    - name: Generate Coverage
      run: |
        ./script/test generate_coverage gcc
    - uses: actions/upload-artifact@50769540e7f4bd5e21e526ee35c689e35e0d6874 # v4.4.0
      with:
        name: cov-expects-linux-2
        path: tmp/coverage.info
        retention-days: 1

  thread-cert:
    runs-on: ubuntu-20.04
    env:
      COVERAGE: 1
      PYTHONUNBUFFERED: 1
      THREAD_VERSION: 1.1
      VIRTUAL_TIME: 1
    steps:
    - name: Harden Runner
      uses: step-security/harden-runner@91182cccc01eb5e619899d80e4e971d6181294a7 # v2.10.1
      with:
        egress-policy: audit # TODO: change to 'egress-policy: block' after couple of runs

    - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      with:
        submodules: true
    - uses: actions/setup-python@f677139bbe7f9c59b41e40162b753c062f5d49a3 # v5.2.0
      with:
        python-version: '3.12'
        cache: pip
    - name: Bootstrap
      run: |
        sudo rm /etc/apt/sources.list.d/* && sudo apt-get update
        sudo apt-get --no-install-recommends install -y lcov ninja-build
        python3 -m pip install -r tests/scripts/thread-cert/requirements.txt
    - name: Build
      run: |
        OT_NODE_TYPE=rcp ./script/test build
    - name: Run
      run: |
        MAX_JOBS=$(getconf _NPROCESSORS_ONLN) ./script/test cert_suite ./tests/scripts/thread-cert/Cert_*.py ./tests/scripts/thread-cert/test_*.py
    - uses: actions/upload-artifact@50769540e7f4bd5e21e526ee35c689e35e0d6874 # v4.4.0
      if: ${{ failure() }}
      with:
        name: thread-cert
        path: ot_testing
    - name: Generate Coverage
      run: |
        ./script/test generate_coverage gcc
    - uses: actions/upload-artifact@50769540e7f4bd5e21e526ee35c689e35e0d6874 # v4.4.0
      with:
        name: cov-thread-cert
        path: tmp/coverage.info

  pty-linux:
    name: pty-linux OT_DAEMON=${{ matrix.OT_DAEMON }}
    runs-on: ubuntu-20.04
    strategy:
      fail-fast: false
      matrix:
        OT_DAEMON: ['off', 'on']
    env:
      COVERAGE: 1
      OT_DAEMON: ${{ matrix.OT_DAEMON }}
      OT_READLINE: 'readline'
    steps:
    - name: Harden Runner
      uses: step-security/harden-runner@91182cccc01eb5e619899d80e4e971d6181294a7 # v2.10.1
      with:
        egress-policy: audit # TODO: change to 'egress-policy: block' after couple of runs

    - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
    - name: Bootstrap
      run: |
        sudo rm /etc/apt/sources.list.d/* && sudo apt-get update
        sudo apt-get --no-install-recommends install -y socat expect lcov net-tools ninja-build
        cd /tmp
        wget https://github.com/obgm/libcoap/archive/bsd-licensed.tar.gz
        tar xvf bsd-licensed.tar.gz
        cd libcoap-bsd-licensed
        ./autogen.sh
        ./configure --prefix= --exec-prefix=/usr --with-boost=internal --disable-tests --disable-documentation
        make -j2
        sudo make install
    - name: Build
      run: |
        script/check-posix-pty build
    - name: Run
      run: |
        script/check-posix-pty check
    - name: Run (OT_DAEMON_ALLOW_ALL)
      if: matrix.OT_DAEMON == 'on'
      env:
        OT_DAEMON_ALLOW_ALL: 1
      run: |
        script/check-posix-pty check
    - name: Generate Coverage
      run: |
        ./script/test generate_coverage gcc
    - uses: actions/upload-artifact@50769540e7f4bd5e21e526ee35c689e35e0d6874 # v4.4.0
      with:
        name: cov-pty-linux-${{ matrix.OT_DAEMON }}
        path: tmp/coverage.info
        retention-days: 1

  pty-macos:
    name: pty-macos OT_DAEMON=${{ matrix.OT_DAEMON }}
    runs-on: macos-14
    strategy:
      fail-fast: false
      matrix:
        OT_DAEMON: ['off', 'on']
    env:
      OT_DAEMON: ${{ matrix.OT_DAEMON }}
      OT_READLINE: 'off'
    steps:
    - name: Harden Runner
      uses: step-security/harden-runner@91182cccc01eb5e619899d80e4e971d6181294a7 # v2.10.1
      with:
        egress-policy: audit # TODO: change to 'egress-policy: block' after couple of runs

    - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
    - name: Bootstrap
      run: |
        rm -f /usr/local/bin/2to3
        rm -f /usr/local/bin/2to3-3.11
        rm -f /usr/local/bin/idle3
        rm -f /usr/local/bin/idle3.11
        rm -f /usr/local/bin/pydoc3
        rm -f /usr/local/bin/pydoc3.11
        rm -f /usr/local/bin/python3
        rm -f /usr/local/bin/python3.11
        rm -f /usr/local/bin/python3-config
        rm -f /usr/local/bin/python3.11-config
        brew update
        brew install ninja socat
    - name: Build
      run: |
        script/check-posix-pty build
    - name: Run
      run: |
        script/check-posix-pty check

  rcp-stack-reset:
    runs-on: ubuntu-20.04
    steps:
    - name: Harden Runner
      uses: step-security/harden-runner@91182cccc01eb5e619899d80e4e971d6181294a7 # v2.10.1
      with:
        egress-policy: audit # TODO: change to 'egress-policy: block' after couple of runs

    - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
    - name: Bootstrap
      env:
        GITHUB_TOKEN: "${{ secrets.GITHUB_TOKEN }}"
      run: |
        sudo apt-get --no-install-recommends install -y expect ninja-build lcov socat
        sudo python3 -m pip install git+https://github.com/openthread/pyspinel
    - name: Build
      run: |
        script/cmake-build simulation -DOT_CSL_RECEIVER=ON -DOT_FULL_LOGS=ON -DOT_LOG_OUTPUT=PLATFORM_DEFINED
    - name: Run
      run: |
        python3 tests/scripts/misc/test_rcp_reset.py build/simulation/examples/apps/ncp/ot-rcp
    - name: Generate Coverage
      run: |
        ./script/test generate_coverage gcc
    - uses: actions/upload-artifact@50769540e7f4bd5e21e526ee35c689e35e0d6874 # v4.4.0
      with:
        name: cov-rcp-stack-reset
        path: tmp/coverage.info
        retention-days: 1

  upload-coverage:
    needs:
    - expects-linux
    - pty-linux
    - thread-cert
    runs-on: ubuntu-20.04
    steps:
    - name: Harden Runner
      uses: step-security/harden-runner@91182cccc01eb5e619899d80e4e971d6181294a7 # v2.10.1
      with:
        egress-policy: audit # TODO: change to 'egress-policy: block' after couple of runs

    - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      with:
        submodules: true
    - name: Bootstrap
      run: |
        sudo apt-get --no-install-recommends install -y lcov
    - uses: actions/download-artifact@65a9edc5881444af0b9093a5e628f2fe47ea3b2e # v4.1.7
      with:
        path: coverage/
        pattern: cov-*
        merge-multiple: true
    - name: Combine Coverage
      run: |
        script/test combine_coverage
    - name: Upload Coverage
      uses: codecov/codecov-action@e28ff129e5465c2c0dcc6f003fc735cb6ae0c673 # v4.5.0
      env:
        CODECOV_TOKEN: ${{ secrets.CODECOV_TOKEN }}
      with:
        files: final.info
        fail_ci_if_error: true
