#
#  Copyright (c) 2020, The OpenThread Authors.
#  All rights reserved.
#
#  Redistribution and use in source and binary forms, with or without
#  modification, are permitted provided that the following conditions are met:
#  1. Redistributions of source code must retain the above copyright
#     notice, this list of conditions and the following disclaimer.
#  2. Redistributions in binary form must reproduce the above copyright
#     notice, this list of conditions and the following disclaimer in the
#     documentation and/or other materials provided with the distribution.
#  3. Neither the name of the copyright holder nor the
#     names of its contributors may be used to endorse or promote products
#     derived from this software without specific prior written permission.
#
#  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
#  AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
#  IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
#  ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE
#  LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, <PERSON>ECIAL, E<PERSON>EMPLARY, OR
#  CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
#  SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
#  INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
#  CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
#  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
#  POSSIBILITY OF SUCH DAMAGE.
#

name: Toranj

on:
  push:
    branches-ignore:
      - 'dependabot/**'
  pull_request:
    branches:
      - 'main'

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || (github.repository == 'openthread/openthread' && github.run_id) || github.ref }}
  cancel-in-progress: true

permissions:
  contents: read

jobs:

  toranj-ncp:
    name: toranj-ncp-${{ matrix.TORANJ_RADIO }}
    runs-on: ubuntu-20.04
    strategy:
      fail-fast: false
      matrix:
        TORANJ_RADIO: ['15.4']
    env:
      COVERAGE: 1
      TORANJ_RADIO : ${{ matrix.TORANJ_RADIO }}
      TORANJ_NCP : 1
      TORANJ_EVENT_NAME: ${{ github.event_name }}
    steps:
    - name: Harden Runner
      uses: step-security/harden-runner@91182cccc01eb5e619899d80e4e971d6181294a7 # v2.10.1
      with:
        egress-policy: audit # TODO: change to 'egress-policy: block' after couple of runs

    - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      with:
        submodules: true
    - uses: actions/setup-python@f677139bbe7f9c59b41e40162b753c062f5d49a3 # v5.2.0
      with:
        python-version: '3.12'
        cache: pip
    - name: Bootstrap
      env:
        GITHUB_TOKEN: "${{ secrets.GITHUB_TOKEN }}"
      run: |
        sudo rm /etc/apt/sources.list.d/* && sudo apt-get update
        sudo apt-get --no-install-recommends install -y ninja-build lcov
        python3 -m pip install -r tests/scripts/thread-cert/requirements.txt
    - name: Build & Run
      run: |
        top_builddir=$(pwd)/build/toranj ./tests/toranj/start.sh


  toranj-cli:
    name: toranj-cli-${{ matrix.TORANJ_RADIO }}
    runs-on: ubuntu-20.04
    strategy:
      matrix:
        TORANJ_RADIO: ['15.4', 'trel', 'multi']
    env:
      COVERAGE: 1
      TORANJ_RADIO : ${{ matrix.TORANJ_RADIO }}
      TORANJ_CLI: 1
    steps:
    - name: Harden Runner
      uses: step-security/harden-runner@91182cccc01eb5e619899d80e4e971d6181294a7 # v2.10.1
      with:
        egress-policy: audit # TODO: change to 'egress-policy: block' after couple of runs

    - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      with:
        submodules: true
    - uses: actions/setup-python@f677139bbe7f9c59b41e40162b753c062f5d49a3 # v5.2.0
      with:
        python-version: '3.12'
        cache: pip
    - name: Bootstrap
      env:
        GITHUB_TOKEN: "${{ secrets.GITHUB_TOKEN }}"
      run: |
        sudo rm /etc/apt/sources.list.d/* && sudo apt-get update
        sudo apt-get --no-install-recommends install -y ninja-build lcov
        python3 -m pip install -r tests/scripts/thread-cert/requirements.txt
    - name: Build & Run
      run: |
        top_builddir=$(pwd)/build/toranj ./tests/toranj/start.sh
    - name: Generate Coverage
      if: "matrix.TORANJ_RADIO != 'multi'"
      run: |
        ./script/test generate_coverage gcc
    - uses: actions/upload-artifact@50769540e7f4bd5e21e526ee35c689e35e0d6874 # v4.4.0
      if: "matrix.TORANJ_RADIO != 'multi'"
      with:
        name: cov-toranj-cli-${{ matrix.TORANJ_RADIO }}
        path: tmp/coverage.info
        retention-days: 1

  toranj-unittest:
    name: toranj-unittest
    runs-on: ubuntu-20.04
    steps:
    - name: Harden Runner
      uses: step-security/harden-runner@91182cccc01eb5e619899d80e4e971d6181294a7 # v2.10.1
      with:
        egress-policy: audit # TODO: change to 'egress-policy: block' after couple of runs

    - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      with:
        submodules: true
    - uses: actions/setup-python@f677139bbe7f9c59b41e40162b753c062f5d49a3 # v5.2.0
      with:
        python-version: '3.12'
        cache: pip
    - name: Bootstrap
      env:
        GITHUB_TOKEN: "${{ secrets.GITHUB_TOKEN }}"
      run: |
        sudo apt-get update
        sudo apt-get --no-install-recommends install -y ninja-build
    - name: Build & Run
      run: |
        ./tests/toranj/build.sh all
        ninja test
        # Validate posix builds with different radio configs
        git clean -dfx
        ./tests/toranj/build.sh ncp-15.4
        git clean -dfx
        ./tests/toranj/build.sh ncp-trel
        git clean -dfx
        ./tests/toranj/build.sh ncp-15.4+trel
        git clean -dfx
        ./tests/toranj/build.sh posix-15.4
        git clean -dfx
        ./tests/toranj/build.sh posix-15.4+trel
        git clean -dfx
        ./tests/toranj/build.sh posix-trel
        git clean -dfx
        ./tests/toranj/build.sh --enable-plat-key-ref all

  toranj-macos:
    name: toranj-macos
    runs-on: macos-14
    steps:
    - name: Harden Runner
      uses: step-security/harden-runner@91182cccc01eb5e619899d80e4e971d6181294a7 # v2.10.1
      with:
        egress-policy: audit # TODO: change to 'egress-policy: block' after couple of runs

    - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      with:
        submodules: true
    - name: Bootstrap
      env:
        GITHUB_TOKEN: "${{ secrets.GITHUB_TOKEN }}"
      run: |
        brew update
        brew install ninja
    - name: Build & Run
      run: |
        ./tests/toranj/build.sh posix-15.4

  nexus:
    name: nexus
    runs-on: ubuntu-20.04
    steps:
    - name: Harden Runner
      uses: step-security/harden-runner@91182cccc01eb5e619899d80e4e971d6181294a7 # v2.10.1
      with:
        egress-policy: audit # TODO: change to 'egress-policy: block' after couple of runs

    - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      with:
        submodules: true
    - name: Bootstrap
      env:
        GITHUB_TOKEN: "${{ secrets.GITHUB_TOKEN }}"
      run: |
        sudo apt-get update
        sudo apt-get --no-install-recommends install -y clang-10 clang++-10 ninja-build llvm lcov
        sudo apt-get --no-install-recommends install -y g++-multilib
    - name: Build & Run
      run: |
        ./tests/nexus/build.sh
        ninja test

  upload-coverage:
    needs:
    - toranj-cli
    runs-on: ubuntu-20.04
    steps:
    - name: Harden Runner
      uses: step-security/harden-runner@91182cccc01eb5e619899d80e4e971d6181294a7 # v2.10.1
      with:
        egress-policy: audit # TODO: change to 'egress-policy: block' after couple of runs

    - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      with:
        submodules: true
    - name: Bootstrap
      run: |
        sudo apt-get --no-install-recommends install -y lcov
    - uses: actions/download-artifact@65a9edc5881444af0b9093a5e628f2fe47ea3b2e # v4.1.7
      with:
        path: coverage/
        pattern: cov-*
        merge-multiple: true
    - name: Combine Coverage
      run: |
        script/test combine_coverage
    - name: Upload Coverage
      uses: codecov/codecov-action@e28ff129e5465c2c0dcc6f003fc735cb6ae0c673 # v4.5.0
      env:
        CODECOV_TOKEN: ${{ secrets.CODECOV_TOKEN }}
      with:
        files: final.info
        fail_ci_if_error: true
