#
#  Copyright (c) 2020, The OpenThread Authors.
#  All rights reserved.
#
#  Redistribution and use in source and binary forms, with or without
#  modification, are permitted provided that the following conditions are met:
#  1. Redistributions of source code must retain the above copyright
#     notice, this list of conditions and the following disclaimer.
#  2. Redistributions in binary form must reproduce the above copyright
#     notice, this list of conditions and the following disclaimer in the
#     documentation and/or other materials provided with the distribution.
#  3. Neither the name of the copyright holder nor the
#     names of its contributors may be used to endorse or promote products
#     derived from this software without specific prior written permission.
#
#  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
#  AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
#  IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
#  ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE
#  LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, <PERSON>ECIAL, E<PERSON>EMPLARY, OR
#  CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
#  SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
#  INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
#  CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
#  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
#  POSSIBILITY OF SUCH DAMAGE.
#

name: OTNS

on:
  push:
    branches-ignore:
      - 'dependabot/**'
  pull_request:
    branches:
      - 'main'

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || (github.repository == 'openthread/openthread' && github.run_id) || github.ref }}
  cancel-in-progress: true

env:
  COVERAGE: 1
  REFERENCE_DEVICE: 1
  VIRTUAL_TIME: 1
  VIRTUAL_TIME_UART: 1
  MAX_NETWORK_SIZE: 999
  GITHUB_TOKEN: "${{ secrets.GITHUB_TOKEN }}"

permissions:  # added using https://github.com/step-security/secure-workflows
  contents: read

jobs:

  unittests:
    name: Unittests
    runs-on: ubuntu-22.04
    steps:
    - name: Harden Runner
      uses: step-security/harden-runner@91182cccc01eb5e619899d80e4e971d6181294a7 # v2.10.1
      with:
        egress-policy: audit # TODO: change to 'egress-policy: block' after couple of runs

    - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
    - uses: actions/setup-go@0a12ed9d6a96ab950c8f026ed9f722fe0da7ef32 # v5.0.2
      with:
        go-version: "1.20"
    - name: Set up Python
      uses: actions/setup-python@f677139bbe7f9c59b41e40162b753c062f5d49a3 # v5.2.0
      with:
        python-version: "3.9"
    - name: Bootstrap
      run: |
        sudo rm /etc/apt/sources.list.d/* && sudo apt-get update
        sudo apt-get --no-install-recommends install -y lcov ninja-build
    - name: Run
      run: |
        export OT_DIR=$PWD
        ./script/git-tool clone --depth 1 https://github.com/openthread/ot-ns.git /tmp/otns
        (
          cd /tmp/otns
          ./script/test py-unittests
        )
    - uses: actions/upload-artifact@50769540e7f4bd5e21e526ee35c689e35e0d6874 # v4.4.0
      if: ${{ failure() }}
      with:
        name: unittests-pcaps
        path: |
          ./output/*/bin/*.pcap
          ./output/*/bin/*.replay
    - name: Generate Coverage
      run: |
        ./script/test generate_coverage gcc
    - uses: actions/upload-artifact@50769540e7f4bd5e21e526ee35c689e35e0d6874 # v4.4.0
      with:
        name: cov-otns-unittests
        path: tmp/coverage.info
        retention-days: 1

  examples:
    name: Examples
    runs-on: ubuntu-22.04
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - uses: actions/setup-go@0a12ed9d6a96ab950c8f026ed9f722fe0da7ef32 # v5.0.2
        with:
          go-version: "1.20"
      - name: Set up Python
        uses: actions/setup-python@f677139bbe7f9c59b41e40162b753c062f5d49a3 # v5.2.0
        with:
          python-version: "3.9"
      - name: Bootstrap
        run: |
          sudo rm /etc/apt/sources.list.d/* && sudo apt-get update
          sudo apt-get --no-install-recommends install -y lcov ninja-build
      - name: Run
        run: |
          export OT_DIR=$PWD
          ./script/git-tool clone --depth 1 https://github.com/openthread/ot-ns.git /tmp/otns
          (
            cd /tmp/otns
            ./script/test py-examples
          )
      - uses: actions/upload-artifact@50769540e7f4bd5e21e526ee35c689e35e0d6874 # v4.4.0
        if: ${{ failure() }}
        with:
          name: examples-pcaps
          path: |
            ./output/*/bin/*.pcap
            ./output/*/bin/*.replay
      - name: Generate Coverage
        run: |
          ./script/test generate_coverage gcc
      - uses: actions/upload-artifact@50769540e7f4bd5e21e526ee35c689e35e0d6874 # v4.4.0
        with:
          name: cov-otns-examples
          path: tmp/coverage.info

  stress-tests:
    name: Stress ${{ matrix.suite }}
    runs-on: ubuntu-22.04
    strategy:
      fail-fast: false
      matrix:
        include:
          - suite: "network-forming"
            stress_level: 8
          - suite: "commissioning"
            stress_level: 14
          - suite: "connectivity"
            stress_level: 6
          - suite: "network-latency"
            stress_level: 10
          - suite: "multicast-performance"
            stress_level: 10
          - suite: "otns-performance"
            stress_level: 10
    env:
      STRESS_LEVEL: ${{ matrix.stress_level }}
    steps:
      - name: Harden Runner
        uses: step-security/harden-runner@91182cccc01eb5e619899d80e4e971d6181294a7 # v2.10.1
        with:
          egress-policy: audit # TODO: change to 'egress-policy: block' after couple of runs

      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - uses: actions/setup-go@0a12ed9d6a96ab950c8f026ed9f722fe0da7ef32 # v5.0.2
        with:
          go-version: "1.20"
      - name: Set up Python
        uses: actions/setup-python@f677139bbe7f9c59b41e40162b753c062f5d49a3 # v5.2.0
        with:
          python-version: "3.9"
      - name: Bootstrap
        run: |
          sudo rm /etc/apt/sources.list.d/* && sudo apt-get update
          sudo apt-get --no-install-recommends install -y lcov ninja-build
      - name: Run
        run: |
          export OT_DIR=$PWD
          ./script/git-tool clone --depth 1 https://github.com/openthread/ot-ns.git /tmp/otns
          (
            cd /tmp/otns
            ./script/test stress-tests ${{ matrix.suite }}
          )
      - uses: actions/upload-artifact@50769540e7f4bd5e21e526ee35c689e35e0d6874 # v4.4.0
        if: ${{ failure() }}
        with:
          name: stress-tests-${{ matrix.suite }}-pcaps
          path: |
            ./output/*/bin/*.pcap
            ./output/*/bin/*.replay
      - name: Generate Coverage
        run: |
          ./script/test generate_coverage gcc
      - uses: actions/upload-artifact@50769540e7f4bd5e21e526ee35c689e35e0d6874 # v4.4.0
        with:
          name: cov-otns-stress-tests-${{ matrix.suite }}
          path: tmp/coverage.info
          retention-days: 1

  upload-coverage:
    needs:
      - unittests
      - examples
      - stress-tests
    runs-on: ubuntu-22.04
    steps:
      - name: Harden Runner
        uses: step-security/harden-runner@91182cccc01eb5e619899d80e4e971d6181294a7 # v2.10.1
        with:
          egress-policy: audit # TODO: change to 'egress-policy: block' after couple of runs

      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: Bootstrap
        run: |
          sudo apt-get --no-install-recommends install -y lcov
      - uses: actions/download-artifact@65a9edc5881444af0b9093a5e628f2fe47ea3b2e # v4.1.7
        with:
          path: coverage/
          pattern: cov-*
          merge-multiple: true
      - name: Upload Coverage
        run: |
          script/test upload_codecov
