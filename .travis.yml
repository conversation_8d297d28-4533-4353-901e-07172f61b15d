sudo: required
language: ruby c

matrix:
 include:
    #- os: osx
    #  compiler: clang
    #  osx_image: xcode7.3
    - os: linux
      dist: trusty
      rvm: "2.4"
      compiler: gcc
    - os: linux
      dist: xenial
      rvm: "2.7"
      compiler: clang

before_install:
  - sudo apt-get install --assume-yes --quiet gcc-multilib

install:
  - gem install bundler
  - bundle install
  - gem install rspec
  - gem install rubocop -v 0.57.2

script:
  - cd test && rake ci
