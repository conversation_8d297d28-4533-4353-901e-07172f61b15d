Performance Monitor
===================

:link_to_translation:`zh_CN:[中文]`

The Performance Monitor component provides APIs to use {IDF_TARGET_NAME} internal performance counters to profile functions and applications.

Application Examples
--------------------

- :example:`system/perfmon` demonstrates how to use the `perfmon` APIs to monitor and profile functions.

High-Level API Reference
------------------------

Header Files
^^^^^^^^^^^^

* :component_file:`perfmon/include/perfmon.h`

API Reference
-------------

.. include-build-file:: inc/xtensa_perfmon_access.inc
.. include-build-file:: inc/xtensa_perfmon_apis.inc
