#
# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#  http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
#

pkg.name: babblesim/sdk
pkg.type: sdk
pkg.description: External files required to build BabbleSim
pkg.author: "Apache Mynewt <<EMAIL>>"
pkg.homepage: "https://mynewt.apache.org/"

pkg.cflags: -std=gnu99

pkg.include_dirs:
  - components/ext_NRF52_hw_models/src/nrfx_config
  - components/ext_NRF52_hw_models/src/nrfx/nrfx_replacements
  - components/ext_NRF52_hw_models/src/nrfx/mdk_replacements
  - components/ext_NRF52_hw_models/src/HW_models
  - components/libUtilv1/src
  - components/libPhyComv1/src
  - components/libRandv2/src
  - components/ext_libCryptov1/src
  - nrfx
  - nrfx/drivers
  - nrfx/hal
  - nrfx/mdk

pkg.pre_build_cmds:
  scripts/link_babblesim.sh: 1
  scripts/link_nrfx.sh: 2

pkg.lflags:
  - -ldl
