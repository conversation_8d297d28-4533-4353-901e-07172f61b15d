#
# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#  http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
#

syscfg.vals:
  # EDTT requires 0x123456789ABC address for the first device
  # and 0x456789ABCDEF for the second one
  BLE_LL_PUBLIC_DEV_ADDR: 0x123456789ABC  # d=1
  # BLE_LL_PUBLIC_DEV_ADDR: 0x456789ABCDEF # d=2

  EDTT_HCI_LOG_FILE: ("hci_logs")
  EDTT_HCI_LOGS: 1

  BLE_LL_HCI_VS_EVENT_ON_ASSERT: 0
  BLE_LL_CFG_FEAT_LE_ENCRYPTION: 1
  BLE_LL_CFG_FEAT_CONN_PARAM_REQ: 1
  BLE_LL_CFG_FEAT_SLAVE_INIT_FEAT_XCHG: 1
  BLE_LL_CFG_FEAT_LE_PING: 1
  BLE_LL_CFG_FEAT_DATA_LEN_EXT: 1
  BLE_LL_CFG_FEAT_LL_PRIVACY: 1
  BLE_LL_CFG_FEAT_LE_CSA2: 1
  BLE_LL_CFG_FEAT_LE_2M_PHY: 1
#  BLE_LL_CFG_FEAT_LE_CODED_PHY: 1  # not modeled in bsim
  BLE_LL_CFG_FEAT_LL_EXT_ADV: 1
  BLE_LL_CFG_FEAT_LL_PERIODIC_ADV: 1
  BLE_LL_CFG_FEAT_LL_PERIODIC_ADV_SYNC_TRANSFER: 1
  BLE_LL_CFG_FEAT_CTRL_TO_HOST_FLOW_CONTROL: 1
  BLE_LL_CFG_FEAT_LL_SCA_UPDATE: 1

  BLE_ROLE_CENTRAL: 1
  BLE_ROLE_PERIPHERAL: 1
  BLE_ROLE_BROADCASTER: 1
  BLE_ROLE_OBSERVER: 1

  BLE_VERSION: 52
  BLE_LL_ROLE_CENTRAL: 1
  BLE_LL_ROLE_PERIPHERAL: 1
  BLE_LL_ROLE_BROADCASTER: 1
  BLE_LL_ROLE_OBSERVER: 1
  BLE_HW_WHITELIST_ENABLE: 1
