#
# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#  *  http://www.apache.org/licenses/LICENSE-2.0
#  * Unless required by applicable law or agreed to in writing,
#  software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
#

# Configure NimBLE variables
NIMBLE_CFG_TINYCRYPT := 1

# Skip files that don't build for this port
NIMBLE_IGNORE := $(NIMBLE_ROOT)/porting/nimble/src/hal_timer.c \
	$(NIMBLE_ROOT)/porting/nimble/src/os_cputime.c \
	$(NIMBLE_ROOT)/porting/nimble/src/os_cputime_pwr2.c

include $(NIMBLE_ROOT)/porting/nimble/Makefile.defs

CSRCS := $(NIMBLE_SRC)

# Source files for NPL OSAL
CSRCS += \
	$(wildcard $(NIMBLE_ROOT)/porting/npl/nuttx/src/*.c) \
	$(wildcard $(NIMBLE_ROOT)/nimble/transport/socket/src/*.c) \
	$(TINYCRYPT_SRC)

# Source files for demo app
CSRCS += $(NIMBLE_ROOT)/porting/examples/nuttx/ble.c

MAINSRC = $(NIMBLE_ROOT)/porting/examples/nuttx/main.c

# Add NPL and all NimBLE directories to include paths
INC = \
  $(wildcard $(NIMBLE_ROOT)/porting/examples/nuttx/include) \
	$(NIMBLE_ROOT)/porting/npl/nuttx/include \
	$(NIMBLE_ROOT)/nimble/transport/socket/include \
	$(NIMBLE_INCLUDE) \
	$(TINYCRYPT_INCLUDE)

INCLUDES := $(addprefix -I, $(INC))

CFLAGS +=                   \
    $(INCLUDES)             \
    $(TINYCRYPT_CFLAGS)     \
    -DNIMBLE_CFG_CONTROLLER=0 -DOS_CFG_ALIGN_4=4 -DOS_CFG_ALIGNMENT=4 \
    -Ddefault_RNG_defined=0

PROGNAME=nimble
