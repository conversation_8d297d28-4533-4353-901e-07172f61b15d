############
# Patterns #
############
.patterns-c-files: &patterns-c-files
  - ".gitlab/ci/static-code-analysis.yml"

  - "tools/ci/static-analysis-rules.yml"
  - "tools/ci/clang_tidy_dirs.txt"

  - "**/*.{c,C,cpp}"
  - "**/*.{h,H,hpp}"
  - "components/**/Kconfig"
  - "components/**/CMakeList.txt"

.patterns-python-cache: &patterns-python-cache
  - "tools/requirements.json"
  - "tools/requirements/requirements.*.txt"

.patterns-python-files: &patterns-python-files
  - ".gitlab/ci/static-code-analysis.yml"
  - "**/*.py"

.patterns-sonarqube-files: &patterns-sonarqube-files
  - "tools/ci/sonar_exclude_list.txt"

.patterns-build_components: &patterns-build_components
  # components files except "test*/" "host*/" folders
  # ?? to include folders less than 4 characters
  - "components/*/???[!t]*/**/*"
  - "components/*/??[!s]?*/**/*"
  - "components/*/???/**/*"
  - "components/*/??/**/*"
  - "components/*/*"
  - "components/*"
  # Add folders excluded by "???[!t]" and "??[!s]?"
  # pre-commit: tools/ci/check_rules_components_patterns.py
  - "components/bt/host/**/*"

.patterns-downloadable-tools: &patterns-downloadable-tools
  - "tools/idf_tools.py"
  - "tools/tools.json"

.patterns-build_template-app: &patterns-build_template-app
  - "tools/ci/build_template_app.sh"

.patterns-build_system: &patterns-build_system
  - "tools/cmake/**/*"
  - "tools/kconfig_new/**/*"
  - "tools/idf.py"
  - "tools/idf_py_actions/**/*"
  - "tools/requirements.json"
  - "tools/ci/ignore_build_warnings.txt"
  - "tools/ci/test_build_system*.sh"
  - "tools/ci/test_build_system*.py"
  - "tools/ci/ci_build_apps.py"
  - "tools/test_build_system/**/*"

.patterns-build_macos: &patterns-build_macos
  - "tools/ci/test_configure_ci_environment.sh"

.patterns-build_check: &patterns-build_check
  - "tools/test_build_system/**/*"
  - "tools/ci/test_configure_ci_environment.sh"
  - "tools/gen_esp_err_to_name.py"
  - "tools/ci/check_soc_headers_leak.py"
  - "tools/ci/check_esp_memory_utils_headers.sh"
  - "tools/ci/check_blobs.sh"
  - "tools/ci/check_public_headers.py"
  - "tools/ci/check_register_rw_half_word.cmake"
  - "tools/ci/check_register_rw_half_word.py"

.patterns-host_test: &patterns-host_test
  - ".gitlab/ci/host-test.yml"

  - "components/**/*"

  - "tools/ci/test_autocomplete/*"

  - "tools/mass_mfg/**/*"

  - "tools/esp_app_trace/**/*"
  - "tools/ldgen/**/*"

  - "tools/idf_monitor.py"

  - "tools/activate.py"

  - "tools/idf.py"
  - "tools/idf_py_actions/**/*"
  - "tools/test_idf_py/**/*"

  - "tools/idf_size.py"
  - "tools/test_idf_size/**/*"

  - "tools/tools.json"
  - "tools/tools_schema.json"
  - "tools/idf_tools.py"
  - "tools/test_idf_tools/**/*"
  - "tools/install_util.py"

  - "tools/export_utils/utils.py"
  - "tools/export_utils/shell_types.py"
  - "tools/export_utils/console_output.py"
  - "tools/export_utils/activate_venv.py"

  - "tools/requirements/*"
  - "tools/requirements.json"
  - "tools/requirements_schema.json"

  - "tools/mkdfu.py"
  - "tools/test_mkdfu/**/*"

  - "tools/kconfig_new/**/*"

  - "tools/detect_python.sh"
  - "tools/detect_python.fish"

  - "tools/gen_soc_caps_kconfig/*"
  - "tools/gen_soc_caps_kconfig/test/test_gen_soc_caps_kconfig.py"

  - "tools/mkuf2.py"

  - "tools/split_paths_by_spaces.py"

  - "tools/check_python_dependencies.py"

.patterns-docker: &patterns-docker
  - "tools/docker/**/*"

.patterns-submodule: &patterns-submodule
  - "components/bootloader/subproject/components/micro-ecc/micro-ecc"
  - "components/bt/controller/lib_esp32"
  - "components/bt/controller/lib_esp32c2/esp32c2-bt-lib"
  - "components/bt/controller/lib_esp32c3_family"
  - "components/bt/controller/lib_esp32h2/esp32h2-bt-lib"
  - "components/bt/host/nimble/nimble"
  - "components/cmock/CMock"
  - "components/esp_phy/lib"
  - "components/esp_wifi/lib"
  - "components/esp_coex/lib"
  - "components/json/cJSON"
  - "components/lwip/lwip"
  - "components/mbedtls/mbedtls"
  - "components/mqtt/esp-mqtt"
  - "components/openthread/lib"
  - "components/openthread/openthread"
  - "components/protobuf-c/protobuf-c"
  - "components/spiffs/spiffs"
  - "components/unity/unity"
  - "components/heap/tlsf"
  - "components/bt/controller/lib_esp32c6/esp32c6-bt-lib"
  - "components/bt/controller/lib_esp32c5/esp32c5-bt-lib"
  - "components/bt/esp_ble_mesh/lib/lib"
  - ".gitmodules"

.patterns-idf-pytest-plugin: &patterns-idf-pytest-plugin
  - "tools/ci/idf_pytest/**/*"
  - "tools/ci/dynamic_pipelines/tests/**/*"

##############
# if anchors #
##############
.if-ref-master: &if-ref-master
  if: '$CI_COMMIT_REF_NAME == "master"'

.if-tag-release: &if-tag-release
  if: '$CI_COMMIT_TAG =~ /^v\d+\.\d+(\.\d+)?($|-)/'

.if-protected: &if-protected
  if: '($CI_COMMIT_REF_NAME == "master" || $CI_COMMIT_BRANCH =~ /^release\/v/ || $CI_COMMIT_TAG =~ /^v\d+\.\d+(\.\d+)?($|-)/) || $CI_COMMIT_TAG =~ /^qa-test/'

.if-protected-no_label: &if-protected-no_label
  if: '($CI_COMMIT_REF_NAME == "master" || $CI_COMMIT_BRANCH =~ /^release\/v/ || $CI_COMMIT_TAG =~ /^v\d+\.\d+(\.\d+)?($|-)/) && $BOT_TRIGGER_WITH_LABEL == null'

.if-protected-ref-push: &if-protected-ref-push
  # rules:changes always evaluates to true for new branch pipelines or when there is no Git push event
  if: '($CI_COMMIT_REF_NAME == "master" || $CI_COMMIT_BRANCH =~ /^release\/v/) && $CI_PIPELINE_SOURCE == "push"'

.if-qa-test-tag: &if-qa-test-tag
  if: '$CI_COMMIT_TAG =~ /^qa-test/'

.if-dev-push: &if-dev-push
  if: '$CI_COMMIT_REF_NAME != "master" && $CI_COMMIT_BRANCH !~ /^release\/v/ && $CI_COMMIT_TAG !~ /^v\d+\.\d+(\.\d+)?($|-)/ && $CI_COMMIT_TAG !~ /^qa-test/ && ($CI_PIPELINE_SOURCE == "push" || $CI_PIPELINE_SOURCE == "merge_request_event")'

.if-schedule: &if-schedule
  if: '$CI_PIPELINE_SOURCE == "schedule"'

.if-schedule-nightly: &if-schedule-nightly
  if: '$CI_PIPELINE_SOURCE == "schedule" && $INCLUDE_NIGHTLY_RUN == "1"'

.if-schedule-test-build-system-windows: &if-schedule-test-build-system-windows
  if: '$CI_PIPELINE_SOURCE == "schedule" && $SCHEDULED_BUILD_SYSTEM_TEST_WIN == "true"'

.if-label-build-only: &if-label-build-only
  if: '$CI_JOB_STAGE == "target_test" && $CI_MERGE_REQUEST_LABELS =~ /^(?:[^,\n\r]+,)*For Maintainers: Only Build Tests(?:,[^,\n\r]+)*$/i'

.if-label-upload_cache: &if-label-upload_cache
  if: '$CI_MERGE_REQUEST_LABELS =~ /^(?:[^,\n\r]+,)*upload_cache(?:,[^,\n\r]+)*$/i'

.if-revert-branch: &if-revert-branch
  if: '$CI_MERGE_REQUEST_SOURCE_BRANCH_NAME =~ /^revert-/'

#########
# Rules #
#########
### Branches ###
.rules:protected:
  rules:
    - <<: *if-protected

.rules:push_to_github:
  rules:
    - <<: *if-qa-test-tag
      when: never
    - <<: *if-protected-no_label

.rules:protected-no_label-always:
  rules:
    - <<: *if-qa-test-tag
      when: never
    - <<: *if-protected-no_label
      when: always

.rules:tag:release:
  rules:
    - <<: *if-tag-release

.rules:dev-push:
  rules:
    - <<: *if-dev-push

# Do not upload caches on dev branches by default
.rules:upload-python-cache:
  rules:
    - <<: *if-tag-release
    - <<: *if-schedule-nightly
    - <<: *if-protected-ref-push
      changes: *patterns-python-cache
    - <<: *if-label-upload_cache
      when: manual

.rules:upload-submodule-cache:
  rules:
    # Needn't upload submodule cache in schedule pipeline
    - <<: *if-tag-release
    - <<: *if-protected-ref-push
      changes: *patterns-submodule
    - <<: *if-label-upload_cache
      when: manual

### Patterns ###
.rules:patterns:clang_tidy:
  rules:
    - <<: *if-protected
    - <<: *if-dev-push
      changes: *patterns-c-files

.rules:patterns:python-files:
  rules:
    - <<: *if-protected
    - <<: *if-dev-push
      changes: *patterns-python-files

#.rules:patterns:static-code-analysis-preview:
#  rules:
#    - <<: *if-dev-push
#      changes: *patterns-c-files
#    - <<: *if-dev-push
#      changes: *patterns-python-files
#    - <<: *if-dev-push
#      changes: *patterns-sonarqube-files

.rules:patterns:idf-pytest-plugin:
  rules:
    - <<: *if-protected
    - <<: *if-dev-push
      changes: *patterns-idf-pytest-plugin

# !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
# DO NOT place comments or maintain any code from this line
#
# Use dependencies.yml and generate_rules.py under .gitlab/ci/dependencies dir
# to generate labels and rules
# Could also use pre-commit hook to finish this if detected changes on
# these two files
# !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!

##################
# Auto Generated #
##################
.if-label-build: &if-label-build
  if: '$BOT_LABEL_BUILD || $CI_MERGE_REQUEST_LABELS =~ /^(?:[^,\n\r]+,)*build(?:,[^,\n\r]+)*$/i'

.if-label-docker: &if-label-docker
  if: '$BOT_LABEL_DOCKER || $CI_MERGE_REQUEST_LABELS =~ /^(?:[^,\n\r]+,)*docker(?:,[^,\n\r]+)*$/i'

.if-label-host_test: &if-label-host_test
  if: '$BOT_LABEL_HOST_TEST || $CI_MERGE_REQUEST_LABELS =~ /^(?:[^,\n\r]+,)*host_test(?:,[^,\n\r]+)*$/i'

.if-label-macos: &if-label-macos
  if: '$BOT_LABEL_MACOS || $CI_MERGE_REQUEST_LABELS =~ /^(?:[^,\n\r]+,)*macos(?:,[^,\n\r]+)*$/i'

.if-label-macos_test: &if-label-macos_test
  if: '$BOT_LABEL_MACOS_TEST || $CI_MERGE_REQUEST_LABELS =~ /^(?:[^,\n\r]+,)*macos_test(?:,[^,\n\r]+)*$/i'

.if-label-nvs_coverage: &if-label-nvs_coverage
  if: '$BOT_LABEL_NVS_COVERAGE || $CI_MERGE_REQUEST_LABELS =~ /^(?:[^,\n\r]+,)*nvs_coverage(?:,[^,\n\r]+)*$/i'

.if-label-submodule: &if-label-submodule
  if: '$BOT_LABEL_SUBMODULE || $CI_MERGE_REQUEST_LABELS =~ /^(?:[^,\n\r]+,)*submodule(?:,[^,\n\r]+)*$/i'

.if-label-windows: &if-label-windows
  if: '$BOT_LABEL_WINDOWS || $CI_MERGE_REQUEST_LABELS =~ /^(?:[^,\n\r]+,)*windows(?:,[^,\n\r]+)*$/i'

.rules:build:
  rules:
    - <<: *if-revert-branch
      when: never
    - <<: *if-protected
    - <<: *if-label-build
    - <<: *if-dev-push
      changes: *patterns-build_components
    - <<: *if-dev-push
      changes: *patterns-build_system
    - <<: *if-dev-push
      changes: *patterns-downloadable-tools

.rules:build:check:
  rules:
    - <<: *if-revert-branch
      when: never
    - <<: *if-protected
    - <<: *if-label-build
    - <<: *if-dev-push
      changes: *patterns-build_check
    - <<: *if-dev-push
      changes: *patterns-build_components
    - <<: *if-dev-push
      changes: *patterns-build_system
    - <<: *if-dev-push
      changes: *patterns-downloadable-tools

.rules:build:docker:
  rules:
    - <<: *if-revert-branch
      when: never
    - <<: *if-protected
    - <<: *if-label-build
    - <<: *if-label-docker
    - <<: *if-dev-push
      changes: *patterns-build_system
    - <<: *if-dev-push
      changes: *patterns-docker
    - <<: *if-dev-push
      changes: *patterns-downloadable-tools
    - <<: *if-dev-push
      changes: *patterns-submodule

.rules:build:macos:
  rules:
    - <<: *if-revert-branch
      when: never
    - <<: *if-protected
    - <<: *if-label-build
    - <<: *if-label-macos
    - <<: *if-label-macos_test
    - <<: *if-dev-push
      changes: *patterns-build_macos
    - <<: *if-dev-push
      changes: *patterns-build_system
    - <<: *if-dev-push
      changes: *patterns-downloadable-tools

.rules:build:target_test:
  rules:
    - <<: *if-revert-branch
      when: never
    - <<: *if-protected
    - <<: *if-label-build
    - <<: *if-dev-push
      changes: *patterns-build_components
    - <<: *if-dev-push
      changes: *patterns-build_system
    - <<: *if-dev-push
      changes: *patterns-build_template-app
    - <<: *if-dev-push
      changes: *patterns-downloadable-tools

.rules:labels:nvs_coverage:
  rules:
    - <<: *if-revert-branch
      when: never
    - <<: *if-label-nvs_coverage

.rules:labels:windows_pytest_build_system:
  rules:
    - <<: *if-revert-branch
      when: never
    - <<: *if-schedule-test-build-system-windows
    - <<: *if-label-windows

.rules:test:host_test:
  rules:
    - <<: *if-revert-branch
      when: never
    - <<: *if-protected
    - <<: *if-label-build-only
      when: never
    - <<: *if-label-host_test
    - <<: *if-dev-push
      changes: *patterns-host_test

.rules:test:submodule:
  rules:
    - <<: *if-revert-branch
      when: never
    - <<: *if-protected
    - <<: *if-label-build-only
      when: never
    - <<: *if-label-submodule
    - <<: *if-dev-push
      changes: *patterns-submodule
