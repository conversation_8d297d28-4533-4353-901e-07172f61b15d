# ESP32-S3 GC0308摄像头驱动

本项目为ESP32-S3开发板提供了完整的GC0308摄像头驱动解决方案，支持通过PCA9557 I/O扩展芯片控制摄像头电源。

## 硬件配置

### 主控制器
- ESP32-S3

### 摄像头模块
- GC0308 (DVP接口)
- I2C地址: 0x21

### I2C总线配置
- SCL: GPIO2
- SDA: GPIO1
- 频率: 100kHz
- 共享总线，支持多设备

### DVP接口引脚
| 信号 | GPIO |
|------|------|
| XCLK | 5    |
| PCLK | 7    |
| VSYNC| 3    |
| HREF | 46   |
| D0   | 16   |
| D1   | 18   |
| D2   | 8    |
| D3   | 17   |
| D4   | 15   |
| D5   | 6    |
| D6   | 4    |
| D7   | 9    |

### 电源控制
- PWDN: 通过PCA9557 (地址0x19) 的IO2引脚控制
- RESET: 连接到全局RESET信号

## 软件架构

### 组件结构
```
components/camera_driver/
├── CMakeLists.txt
├── include/
│   └── gc0308_camera.h     # GC0308摄像头头文件
└── src/
    └── gc0308_camera.c     # GC0308摄像头实现

components/display_driver/  # PCA9557驱动在此组件中
├── include/
│   └── display_driver.h    # 包含PCA9557相关函数
└── src/
    └── display_driver.c    # PCA9557驱动实现
```

### 主要功能
1. **PCA9557 I/O扩展芯片驱动** (位于display_driver组件)
   - I2C通信
   - 引脚方向配置
   - 电平控制
   - 摄像头电源管理 (`dvp_pwdn()` 函数)

2. **GC0308摄像头驱动** (位于camera_driver组件)
   - 摄像头初始化
   - 图像捕获
   - 参数配置
   - 依赖display_driver进行电源管理

## API使用说明

### 摄像头电源控制API (来自display_driver组件)

```c
// 初始化PCA9557芯片
void pca9557_init(void);

// 控制摄像头电源 (PWDN引脚)
// level=0: 开启摄像头电源 (PWDN低电平)
// level=1: 关闭摄像头电源 (PWDN高电平)
void dvp_pwdn(uint8_t level);

// 其他PCA9557功能
void lcd_cs(uint8_t level);    // LCD片选控制
void pa_en(uint8_t level);     // 功放使能控制
```

### GC0308摄像头API

```c
// 获取默认配置
camera_config_t gc0308_get_default_config(void);

// 初始化摄像头
esp_err_t gc0308_camera_init(camera_config_t *config);

// 启动摄像头
esp_err_t gc0308_camera_start(void);

// 捕获图像
camera_fb_t* gc0308_camera_capture(void);

// 释放帧缓冲区
void gc0308_camera_fb_return(camera_fb_t *fb);

// 反初始化摄像头
esp_err_t gc0308_camera_deinit(void);
```

## 使用示例

### 基本使用
```c
#include "gc0308_camera.h"
#include "display_driver.h"  // 用于摄像头电源控制

void app_main(void)
{
    // 初始化摄像头（使用默认配置）
    // 注意：gc0308_camera_init()会自动调用pca9557_init()和dvp_pwdn()
    esp_err_t ret = gc0308_camera_init(NULL);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Camera init failed");
        return;
    }

    // 启动摄像头
    gc0308_camera_start();

    // 捕获图像
    camera_fb_t *fb = gc0308_camera_capture();
    if (fb != NULL) {
        // 处理图像数据
        printf("Captured: %dx%d, size: %zu\n",
               fb->width, fb->height, fb->len);

        // 释放缓冲区
        gc0308_camera_fb_return(fb);
    }

    // 清理资源
    gc0308_camera_deinit();
}
```

### 自定义配置
```c
#include "gc0308_camera.h"
#include "display_driver.h"

camera_config_t config = gc0308_get_default_config();
config.frame_size = FRAMESIZE_QVGA;  // 320x240
config.pixel_format = PIXFORMAT_JPEG;
config.jpeg_quality = 10;

esp_err_t ret = gc0308_camera_init(&config);
```

### 手动电源控制
```c
#include "display_driver.h"

// 初始化PCA9557
pca9557_init();

// 关闭摄像头电源
dvp_pwdn(1);
vTaskDelay(pdMS_TO_TICKS(100));

// 开启摄像头电源
dvp_pwdn(0);
vTaskDelay(pdMS_TO_TICKS(100));
```

## 编译和运行

### 环境要求
- ESP-IDF v5.4
- ESP32-S3开发板
- 支持PSRAM

### 编译步骤
```bash
# 设置目标芯片
idf.py set-target esp32s3

# 配置项目
idf.py menuconfig

# 编译项目
idf.py build

# 烧录固件
idf.py flash monitor
```

### 配置要点
1. 启用PSRAM支持
2. 配置摄像头组件
3. 设置I2C调试日志（可选）

## 故障排除

### 常见问题

1. **PCA9557通信失败**
   - 检查I2C接线
   - 确认I2C地址(0x19)
   - 检查上拉电阻

2. **摄像头初始化失败**
   - 确认DVP接线正确
   - 检查摄像头电源
   - 验证XCLK信号

3. **图像捕获失败**
   - 检查PSRAM配置
   - 确认帧缓冲区设置
   - 验证时钟频率

### 调试方法
1. 启用详细日志输出
2. 使用示例程序测试
3. 检查硬件连接
4. 验证I2C通信

## 技术支持

如有问题，请检查：
1. 硬件连接是否正确
2. 软件配置是否匹配
3. ESP-IDF版本兼容性
4. 组件依赖关系

## 版本历史

- v1.0.0: 初始版本，支持基本的摄像头功能
- 支持GC0308摄像头
- 支持PCA9557电源控制
- 兼容ESP-IDF v5.4
