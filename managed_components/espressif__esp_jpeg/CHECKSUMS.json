{"version": "1.0", "algorithm": "sha256", "created_at": "2025-07-12T20:18:17.313991+00:00", "files": [{"path": ".build-test-rules.yml", "size": 0, "hash": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855"}, {"path": "CHANGELOG.md", "size": 635, "hash": "9f11d5de6ef3d6ab2894be7e52c1085b0bc735c9c5a31848289abd952bdebf2a"}, {"path": "CMakeLists.txt", "size": 365, "hash": "374e1ed3c78d0623f434487bbcd6e05f3e9c01d5e543fa0eb00f6df6b75a7c77"}, {"path": "Kconfig", "size": 2678, "hash": "af77dfa3a532aa161a99cebb200b9aab7b511313d0328bfbde97ee6ba51a2641"}, {"path": "README.md", "size": 4951, "hash": "13d64e0beb982db707c395f7fa4e24c91a84caac4f4d1237c06e9a985e095a94"}, {"path": "idf_component.yml", "size": 311, "hash": "e0aee999ac891551353178542fe48607cd370f3fb52b88ffe90eb08ad15b9be0"}, {"path": "jpeg_decoder.c", "size": 9863, "hash": "ea97dd2bedb8cebbc5afed2bbcb9f2d9239340b51eab4fabf3a9bccd5a23d949"}, {"path": "jpeg_default_huffman_table.c", "size": 3727, "hash": "121cd6bf0ad81ca2d56f7fddb1c5b54f187f56678054066f19691d25f89ac5c1"}, {"path": "license.txt", "size": 11358, "hash": "cfc7749b96f63bd31c3c42b5c471bf756814053e847c10f3eb003417bc523d30"}, {"path": "include/jpeg_decoder.h", "size": 3193, "hash": "e9e48c8644defe9037123797a72f4e3a029bdedd1eb487bb8542d3f04cab78eb"}, {"path": "test_apps/CMakeLists.txt", "size": 132, "hash": "e7ececa71771ec59e607d6bb3d26d8650238b3237f62fc3b94054b87ef60cf5a"}, {"path": "test_apps/pytest_esp_jpeg.py", "size": 106, "hash": "eb7d24d9fe6bc94eb647c57580f014a3f86feaff73824d30548e028734a20ede"}, {"path": "test_apps/sdkconfig.ci", "size": 243, "hash": "17f9af1bfc5f5a269c1f8d284881ae532de790439f8d89bff8a49758cd28959d"}, {"path": "test_apps/sdkconfig.defaults", "size": 195, "hash": "1a504ae17c4ad9637e84f41d0e7f7c37b1a49aaaabe45c138baa2c7185cf6617"}, {"path": "tjpgd/tjpgd.c", "size": 58316, "hash": "1286f5e32e7a4750ee36fd57d2c442c99a74ffed0e81cb5d2fbf92fccf64a84d"}, {"path": "tjpgd/tjpgd.h", "size": 3910, "hash": "53d1156fd9fcc97d346e5ee8c16ae39c34d71c691dcad3c524424bb91519cd24"}, {"path": "tjpgd/tjpgdcnf.h", "size": 1245, "hash": "6a2134a4aae53bc361a7c4f1bfc8ef0107452191d6c4ae26bd1030ca441459ae"}, {"path": "test_apps/main/CMakeLists.txt", "size": 268, "hash": "560f601aded66742136e81ba46e18c2462754ea8a895512f1055972141013d57"}, {"path": "test_apps/main/idf_component.yml", "size": 81, "hash": "88a6234707c7ee9c886852565cd83d2996d4b0433fa6a74f79ccab05fc7506c3"}, {"path": "test_apps/main/jpg_to_rgb888_hex.py", "size": 1960, "hash": "2daa9ef0572cbf95b4ba551c8989774c309175c44b5eb49f310adb58d4770d97"}, {"path": "test_apps/main/logo.jpg", "size": 7561, "hash": "528977b08f4c70a21aa85c5818ab88f64a36c7032fd22ef4a21c082ad75998cd"}, {"path": "test_apps/main/test_logo_jpg.h", "size": 353, "hash": "67de0f8e2072eb54059c06b06bebaaed39af6439df54633291ab295d7e6dbb55"}, {"path": "test_apps/main/test_logo_rgb888.h", "size": 38125, "hash": "0c658db3518304c8f785fefa7ad1c5c840346976242c81a9687ace7937313cdf"}, {"path": "test_apps/main/test_tjpgd_main.c", "size": 555, "hash": "66d00e2eaaf03a11071a7f5e62df890478cc397f9161dea8b403097c804c790c"}, {"path": "test_apps/main/test_usb_camera_2_jpg.h", "size": 572, "hash": "e8dac72fb7625d6c851e3b3350867dc1deb33073f45f49d0dd232dc281e5a4b2"}, {"path": "test_apps/main/test_usb_camera_2_rgb888.h", "size": 268846, "hash": "75dddfd81a5ae7f94f11d90b3a86f53de2a84b0e0d7a8d4588fb61661d7c2083"}, {"path": "test_apps/main/test_usb_camera_jpg.h", "size": 592, "hash": "b63c08128fa27ad40d98789e32645cef219280d3cc86c0e9173a15b2dbdbb40c"}, {"path": "test_apps/main/test_usb_camera_rgb888.h", "size": 268849, "hash": "3892ea716903af6776d7aca70e213d81ab637a3b36b3c9a437401c4748cd5bae"}, {"path": "test_apps/main/tjpgd_test.c", "size": 10046, "hash": "9d3f52d5c59b5b4f6a7d1f82a1a24a732a9481a4afa4cb281fbebbc77287f124"}, {"path": "test_apps/main/usb_camera.jpg", "size": 2632, "hash": "f038468fd1e4fd141992516f65449df0cece19cca5fea3bdd02e3f7adc84eeaa"}, {"path": "test_apps/main/usb_camera_2.jpg", "size": 1384, "hash": "b16b790ffbff04b2736c80048bd9b54c9c7226906b95d51245426f15cdea9e20"}, {"path": "examples/get_started/CMakeLists.txt", "size": 255, "hash": "2bbfea2779f443c3f3c44384e14b028192949d4252783f81b018b7b8a571ffcb"}, {"path": "examples/get_started/README.md", "size": 2551, "hash": "0e36bcc5eaf0b57a851352ac789dc75d437dd655e6c49c73b368a0c0cb1114e3"}, {"path": "examples/get_started/sdkconfig.defaults", "size": 207, "hash": "9ded94a95a6008f8260f09136d214b6b6039386ecfcb5d9e9ac317ae93965ac1"}, {"path": "examples/get_started/main/CMakeLists.txt", "size": 244, "hash": "51a928fad21a526a67a04b01dcb20958684374c8665243bf1562f0fc7aca34f0"}, {"path": "examples/get_started/main/Kconfig.projbuild", "size": 272, "hash": "0679e987a2e2538a25062dd496ba8f3be2c7eb6cb6d02784efc6c7de0aed43da"}, {"path": "examples/get_started/main/decode_image.c", "size": 2350, "hash": "8fed37fe39517ce4e2f674606e2b76ec75a26fe49fe6742b0c9f833aa7b09851"}, {"path": "examples/get_started/main/decode_image.h", "size": 813, "hash": "73096c1ab196d00e387c735d8dec6bd7cc29cdeb1c30103f5aa56ab15ffc4fb2"}, {"path": "examples/get_started/main/idf_component.yml", "size": 285, "hash": "a25cb5aa9a9e08ae65eb7a25d4763df608d8e183da4077dcb796054d178a85b9"}, {"path": "examples/get_started/main/image.jpg", "size": 43700, "hash": "c62aff0127108296cb05372369b1ed11b92e7af53f9f7d8c6b11c0f6762e74e4"}, {"path": "examples/get_started/main/lcd_tjpgd_example_main.c", "size": 3314, "hash": "9f82d7437fc0faa259d26682d0d2156cd0fdab339b6843b91d8093749cdd0414"}, {"path": "examples/get_started/main/pretty_effect.c", "size": 2084, "hash": "653e3c794c39998c885f198f357a4dbce3ce307054b2c1beaca03615cf1e0bbc"}, {"path": "examples/get_started/main/pretty_effect.h", "size": 775, "hash": "1c2b47d3b6c57541cc8072b8f97ac84c0cc0ad8d657663fafe64054c860f54fd"}]}