## 1.3.1

- Fixed the format of Kconfig file

## 1.3.0

- Added option to get image size without decoding it

## 1.2.1

- Fixed decoding of non-conforming 0xFFFF marker

## 1.2.0

- Added option to for passing user defined working buffer

## 1.1.0

- Added support for decoding images without <PERSON><PERSON><PERSON> tables
- Fixed undefined configuration options from Kconfig

## 1.0.5~3

- Added option to swap output color bytes regardless of JD_FORMAT

## 1.0.4

- Added ROM implementation support for ESP32-C6

## 1.0.2

- Fixed compiler warnings

## 1.0.1

- Fixed: exclude ESP32-C2 from list of ROM implementations

## 1.0.0

- Initial version
