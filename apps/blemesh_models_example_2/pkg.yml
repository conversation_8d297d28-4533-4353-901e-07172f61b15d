# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#  http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
#
pkg.name: apps/blemesh_models_example_2
pkg.type: app
pkg.description: Sample application for BLE Mesh node with on/off, level, light and vendor models on nRF52840pdk
pkg.author: "<PERSON><PERSON><PERSON> <<EMAIL>>"
pkg.homepage: "http://mynewt.apache.org/"
pkg.keywords:

pkg.deps:
    - "@apache-mynewt-core/kernel/os"
    - "@apache-mynewt-core/sys/console"
    - "@apache-mynewt-core/sys/log"
    - "@apache-mynewt-core/sys/stats"
    - "@apache-mynewt-core/encoding/base64"
    - "@apache-mynewt-core/sys/config"
    - nimble/host
    - nimble/host/services/gap
    - nimble/host/services/gatt

pkg.lflags:
    - -DFLOAT_SUPPORT
    - -lm
