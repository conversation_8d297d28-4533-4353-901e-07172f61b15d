# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#  http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.


syscfg.vals:
    CONSOLE_IMPLEMENTATION: full
    LOG_IMPLEMENTATION: full
    STATS_IMPLEMENTATION: full

    # Disable central and observer roles.
    BLE_ROLE_BROADCASTER: 1
    BLE_ROLE_CENTRAL: 0
    BLE_ROLE_OBSERVER: 0
    BLE_ROLE_PERIPHERAL: 1

    # Disable unused eddystone feature.
    BLE_EDDYSTONE: 0

    # Log reboot messages to a flash circular buffer.
    REBOOT_LOG_FCB: 1
    LOG_FCB: 1
    CONFIG_FCB: 1

    # Set public device address.
    BLE_LL_PUBLIC_DEV_ADDR: 0x1122aabb33cc

    # Set device appearance to Cycling Speed and Cadence Sensor
    BLE_SVC_GAP_APPEARANCE: BLE_SVC_GAP_APPEARANCE_CYC_SPEED_AND_CADENCE_SENSOR

    # Whether to save data to sys/config, or just keep it in RAM.
    BLE_STORE_CONFIG_PERSIST: 0
