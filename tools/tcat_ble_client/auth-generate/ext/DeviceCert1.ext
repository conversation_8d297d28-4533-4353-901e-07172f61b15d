# This file contains X509v3 extension definitions for OpenSSL
# certificate generation. 

[ DeviceCert1 ]
subjectKeyIdentifier = none
authorityKeyIdentifier = keyid

# Include Thread version field
1.3.6.1.4.1.44970.2 = ASN1:INTEGER:5

# Include TCAT specified fields
# Because ASN1:OCTETSTRING doesn't work with hex input, we use raw DER here. Tag 0x04 is OCTETSTRING.
# See https://datatracker.ietf.org/doc/html/rfc5280#section-4.1
#
1.3.6.1.4.1.44970.3 = DER:04:05:20:01:01:01:01
