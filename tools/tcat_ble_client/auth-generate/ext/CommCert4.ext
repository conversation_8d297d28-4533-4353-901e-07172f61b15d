# This file contains X509v3 extension definitions for OpenSSL
# certificate generation. 

[ CommCert4 ]
subjectKeyIdentifier = none
authorityKeyIdentifier = keyid

# Include TCAT specified fields
# Because ASN1:OCTETSTRING doesn't work with hex input, we use raw DER here. Tag 0x04 is OCTETSTRING.
# See https://datatracker.ietf.org/doc/html/rfc5280#section-4.1
#
1.3.6.1.4.1.44970.1 = ASN1:IA5STRING:OtherDomain
1.3.6.1.4.1.44970.3 = DER:04:05:21:21:05:09:11
1.3.6.1.4.1.44970.4 = ASN1:UTF8STRING:OtherThread-c64e
1.3.6.1.4.1.44970.5 = DER:04:08:ef:13:98:c2:fd:50:4b:68
