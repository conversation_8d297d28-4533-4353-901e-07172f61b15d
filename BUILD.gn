#  Copyright (c) 2019, The OpenThread Authors.
#  All rights reserved.
#
#  Redistribution and use in source and binary forms, with or without
#  modification, are permitted provided that the following conditions are met:
#  1. Redistributions of source code must retain the above copyright
#     notice, this list of conditions and the following disclaimer.
#  2. Redistributions in binary form must reproduce the above copyright
#     notice, this list of conditions and the following disclaimer in the
#     documentation and/or other materials provided with the distribution.
#  3. Neither the name of the copyright holder nor the
#     names of its contributors may be used to endorse or promote products
#     derived from this software without specific prior written permission.
#
#  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
#  AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
#  IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
#  ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE
#  LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, <PERSON>XEMPLARY, OR
#  CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
#  SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
#  INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
#  CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
#  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
#  POSSIBILITY OF SUCH DAMAGE.
#

import("etc/gn/openthread.gni")

config("openthread_config") {
  defines = []
  if (openthread_config_file != "") {
    defines += [ "OPENTHREAD_CONFIG_FILE=${openthread_config_file}" ]
  }

  include_dirs = openthread_project_include_dirs

  include_dirs += [
    "${root_gen_dir}/include",
    "include",
  ]
}

config("openthread_ftd_config") {
  defines = [
    "OPENTHREAD_FTD=1",
    "OPENTHREAD_MTD=0",
    "OPENTHREAD_RADIO=0",
  ]
}

config("openthread_mtd_config") {
  defines = [
    "OPENTHREAD_MTD=1",
    "OPENTHREAD_FTD=0",
    "OPENTHREAD_RADIO=0",
  ]
}

config("openthread_radio_config") {
  defines = [
    "OPENTHREAD_RADIO=1",
    "OPENTHREAD_FTD=0",
    "OPENTHREAD_MTD=0",
  ]
}

group("libopenthread-ftd") {
  public_deps = [ "include/openthread" ]
  deps = [ "src/core:libopenthread-ftd" ]
}

group("libopenthread-mtd") {
  public_deps = [ "include/openthread" ]
  deps = [ "src/core:libopenthread-mtd" ]
}

group("libopenthread-radio") {
  public_deps = [ "include/openthread" ]
  deps = [ "src/core:libopenthread-radio" ]
}

group("libopenthread-cli-ftd") {
  public_deps = [ "include/openthread" ]
  deps = [ "src/cli:libopenthread-cli-ftd" ]
}

group("libopenthread-cli-mtd") {
  public_deps = [ "include/openthread" ]
  deps = [ "src/cli:libopenthread-cli-mtd" ]
}

group("libopenthread-spinel-ncp") {
  public_deps = [ "src/lib/spinel:spinel-api" ]
  deps = [ "src/lib/spinel:libopenthread-spinel-ncp" ]
}

group("libopenthread-spinel-rcp") {
  public_deps = [ "src/lib/spinel:spinel-api" ]
  deps = [ "src/lib/spinel:libopenthread-spinel-rcp" ]
}

if (current_os == "fuchsia") {
  group("lib-ot-core") {
    public_deps = [
      ":libopenthread-ftd",
      "src/core:libopenthread-ftd",
      "src/ncp:libopenthread-ncp-ftd",
    ]
  }
}
