# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#  http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
#

syscfg.defs:
    # Supported GAP roles.  By default, all four roles are enabled.
    BLE_ROLE_CENTRAL:
        description: 'Enables the Central bluetooth role. (0/1)'
        value: 1
    BLE_ROLE_PERIPHERAL:
        description: 'Enables the Peripheral bluetooth role. (0/1)'
        value: 1
    BLE_ROLE_BROADCASTER:
        description: 'Enables the Broadcaster bluetooth role. (0/1)'
        value: 1
    BLE_ROLE_OBSERVER:
        description: 'Enables the Observer bluetooth role. (0/1)'
        value: 1

    BLE_MAX_CONNECTIONS:
        description: 'The maximum number of concurrent connections.'
        value: 1
    BLE_MAX_PERIODIC_SYNCS:
        description: >
            The maximum number of concurrent periodic syncs that can 
            be created
        value: 1
    BLE_WHITELIST:
        description: >
            Enables the BLE whitelist for controlling who to connect to or
            accept a connection from. (0/1)
        value: 1
    BLE_MULTI_ADV_INSTANCES:
        description: >
            This is the number of multi-advertising instances. This is NOT the
            total number of advertising instances. The total number of
            advertising instances is this number plus 1 (assuming the device
            supports advertising).
        value: 0
    BLE_EXT_ADV:
        description: >
            This enables extended advertising feature.
        value: 0
    BLE_PERIODIC_ADV:
        description: >
            This enables periodic advertising feature.
        value: 0
        restrictions:
            - 'BLE_EXT_ADV if 1'
    BLE_PERIODIC_ADV_SYNC_TRANSFER:
        description: >
            This enables Periodic Advertising Sync Transfer Feature.
        value: 0
        restrictions:
            - 'BLE_PERIODIC_ADV if 1'
            - '(BLE_ROLE_CENTRAL || BLE_ROLE_PERIPHERAL) if 1'
    BLE_PERIODIC_ADV_SYNC_BIGINFO_REPORTS:
        description: >
            This enables BIGInfo reports.
        value: 0
        restrictions:
            - 'BLE_PERIODIC_ADV if 1'
            - '(BLE_VERSION >= 52) if 1'
        experimental: 1
    BLE_EXT_ADV_MAX_SIZE:
        description: >
            This allows to configure maximum size of advertising data and
            scan response data used in LE Advertising Extensions.
            Valid range 31-1650.
        value: 31
    BLE_VERSION:
        description: >
            This allows to configure supported Bluetooth Core version. Some
            features may not be available if version is too low. Version is
            integer for easy comparison.
        range: 50, 51, 52, 53, 54
        value: 50
    BLE_ISO:
        description: >
            This enables LE Isochronous Channels as per Bluetooth v5.2
        value: 0
        restrictions:
            - '(BLE_VERSION >= 52) if 1'

    BLE_ISO_TEST:
        description: >
            Enables BLE ISO Testing commands
        value: 0
        restrictions:
            - 'BLE_ISO if 1'

    BLE_HCI_VS:
        description: >
            Enables support for NimBLE specific vendor HCI commands
        value: 0

    BLE_HCI_VS_OCF_OFFSET:
        description: >
            This defines starting offset for NimBLE specific vendor HCI commands.
            Purpose of this is to improve compatibility with other custom
            implementations.
        value: 0

    BLE_POWER_CONTROL:
        description: >
            This enabled LE Power Control feature
        value: 0

    BLE_CONN_SUBRATING:
        description: >
            This enables LE Connection Subrating feature
        value: 0
    BLE_GATT_BLOB_TRANSFER:
        description: >
            This enables write blob feature
        value: 1

# Allow periodic sync transfer only if 5.1 or higher
syscfg.restrictions:
    - "'BLE_PERIODIC_ADV_SYNC_TRANSFER == 0' || 'BLE_VERSION >= 51'"

# Enable VS HCI by default for combined or standalone controller build
syscfg.vals.BLE_CONTROLLER:
    BLE_HCI_VS: 1
