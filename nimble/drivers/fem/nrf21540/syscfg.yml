# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#  http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
#

syscfg.defs:
    NRF21540_PIN_PDN:
        description: >
            GPIO pin number to control PDN signal.
        value:
        restrictions: $notnull
    NRF21540_PIN_ANT_SEL:
        description: >
            GPIO pin number to control ANT_SEL signal.
        value:
    NRF21540_PIN_MODE:
        description: >
            GPIO pin number to control MODE signal.
        value:
    NRF21540_ANTENNA_PORT:
        description: >
            Selects antenna port, valid only if ANT_SEL pin is configured.
        range: 1, 2
        value: 1
    NRF21540_TX_GAIN_PRESET:
        description: >
            Selects TX gain preset, valid only if MODE pin is configured.
        choices:
            - none
            - POUTA
            - POUTB
        value: none
