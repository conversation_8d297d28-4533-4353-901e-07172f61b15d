/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

#include <stddef.h>
#include <string.h>
#include <errno.h>
#include "testutil/testutil.h"
#include "nimble/hci_common.h"
#include "nimble/nimble_opt.h"
#include "host/ble_sm.h"
#include "ble_hs_test.h"
#include "ble_hs_test_util.h"
#include "ble_sm_test_util.h"

#if NIMBLE_BLE_SM

/**
 * Secure connections pairing
 * Master: peer
 * Pair algorithm: just works
 * Initiator IO capabilities: 3
 * Responder IO capabilities: 3
 * Bonding: true
 * Initiator address type: 0
 * Responder address type: 0
 * Initiator key distribution: 5
 * Responder key distribution: 7
 */
TEST_CASE_SELF(ble_sm_sc_peer_jw_iio3_rio3_b1_iat0_rat0_ik5_rk7)
{
    struct ble_sm_test_params params;

    params = (struct ble_sm_test_params) {
        .init_id_addr = {
            0xca, 0x61, 0xa0, 0x67, 0x94, 0xe0,
        },
        .resp_id_addr = {
            0x33, 0x22, 0x11, 0x00, 0x45, 0x0a,
        },
        .pair_req = {
            .io_cap = 0x03,
            .oob_data_flag = 0x00,
            .authreq = 0x09,
            .max_enc_key_size = 0x10,
            .init_key_dist = 0x0d,
            .resp_key_dist = 0x0f,
        },
        .pair_rsp = {
            .io_cap = 0x03,
            .oob_data_flag = 0x00,
            .authreq = 0x09,
            .max_enc_key_size = 0x10,
            .init_key_dist = 0x05,
            .resp_key_dist = 0x07,
        },
        .our_priv_key = {
            0x54, 0x8d, 0x20, 0xb8, 0x97, 0x0b, 0xbc, 0x43,
            0x9a, 0xad, 0x10, 0x6f, 0x60, 0x74, 0xd4, 0x6a,
            0x55, 0xc1, 0x7a, 0x17, 0x8b, 0x60, 0xe0, 0xb4,
            0x5a, 0xe6, 0x58, 0xf1, 0xea, 0x12, 0xd9, 0xfb,
        },
        .public_key_req = {
            .x = {
                0xbc, 0xf2, 0xd8, 0xa5, 0xdb, 0xa3, 0x95, 0x6c,
                0x99, 0xf9, 0x11, 0x0d, 0x4d, 0x2e, 0xf0, 0xbd,
                0xee, 0x9b, 0x69, 0xb6, 0xcd, 0x88, 0x74, 0xbe,
                0x40, 0xe8, 0xe5, 0xcc, 0xdc, 0x88, 0x44, 0x53,
            },
            .y = {
                0xbf, 0xa9, 0x82, 0x0e, 0x18, 0x7a, 0x14, 0xf8,
                0x77, 0xfd, 0x8e, 0x92, 0x2a, 0xf8, 0x5d, 0x39,
                0xd1, 0x6d, 0x92, 0x1f, 0x38, 0x74, 0x99, 0xdc,
                0x6c, 0x2c, 0x94, 0x23, 0xf9, 0x72, 0x56, 0xab,
            },
        },
        .public_key_rsp = {
            .x = {
                0x72, 0x8c, 0xd1, 0x88, 0xd7, 0xbe, 0x49, 0xb2,
                0xc5, 0x5c, 0x95, 0xb3, 0x64, 0xe0, 0x12, 0x32,
                0xb6, 0xc9, 0x47, 0x63, 0x37, 0x38, 0x5b, 0x9c,
                0x1e, 0x1b, 0x1a, 0x06, 0x09, 0xe2, 0x31, 0x85,
            },
            .y = {
                0x19, 0x3a, 0x29, 0x69, 0x62, 0xd6, 0x30, 0xe7,
                0xe8, 0x48, 0x63, 0xdc, 0x00, 0x73, 0x0a, 0x70,
                0x7d, 0x2e, 0x29, 0xcc, 0x91, 0x77, 0x71, 0xb1,
                0x75, 0xb8, 0xf7, 0xdc, 0xb0, 0xe2, 0x91, 0x10,
            },
        },
        .confirm_rsp[0] = {
            .value = {
                0x82, 0xed, 0xd0, 0x62, 0x91, 0x3d, 0x96, 0x7f,
                0x13, 0xc5, 0x0d, 0x02, 0x2b, 0x5e, 0x43, 0x16,
            },
        },
        .random_req[0] = {
            .value = {
                0xa4, 0x34, 0x5f, 0xb3, 0xaf, 0x73, 0x43, 0x64,
                0xcd, 0x19, 0x1b, 0x5b, 0x87, 0x58, 0x31, 0x66,
            },
        },
        .random_rsp[0] = {
            .value = {
                0xc0, 0x91, 0xfb, 0xb3, 0x77, 0xa2, 0x02, 0x0b,
                0xc6, 0xcd, 0x6c, 0x04, 0x51, 0x45, 0x45, 0x39,
            },
        },
        .dhkey_check_req = {
            .value = {
                0x82, 0x65, 0x1d, 0x02, 0xed, 0x89, 0x13, 0x44,
                0x04, 0x1a, 0x14, 0x7c, 0x32, 0x9a, 0x1e, 0x7d,
            },
        },
        .dhkey_check_rsp = {
            .value = {
                0x06, 0x3c, 0x28, 0x4a, 0xe5, 0x48, 0x4b, 0x51,
                0x65, 0x4e, 0x14, 0x5e, 0x2f, 0xdd, 0xfa, 0x22,
            },
        },
        .id_info_req = {
            .irk = {
                0xef, 0x8d, 0xe2, 0x16, 0x4f, 0xec, 0x43, 0x0d,
                0xbf, 0x5b, 0xdd, 0x34, 0xc0, 0x53, 0x1e, 0xb8,
            },
        },
        .id_addr_info_req = {
            .addr_type = 0,
            .bd_addr = {
                0x33, 0x22, 0x11, 0x00, 0x45, 0x0a,
            },
        },
        .sign_info_req = {
            .sig_key = {
                0x40, 0x53, 0xeb, 0x7a, 0x4d, 0x8e, 0xa2, 0xb5,
                0xca, 0xa1, 0xb6, 0xae, 0x7e, 0x6a, 0x4d, 0xd9,
            },
        },
        .sign_info_rsp = {
            .sig_key = {
                0xbc, 0x13, 0x4b, 0x45, 0xda, 0x76, 0x5b, 0xcd,
                0xc2, 0x43, 0x81, 0xb8, 0xc3, 0x68, 0x12, 0xbb,
            },
        },
        .ltk = {
            0x63, 0x59, 0x8a, 0x14, 0x09, 0x4b, 0x94, 0x6e,
            0xff, 0xae, 0x5e, 0x53, 0x86, 0x02, 0xa3, 0x6c,
        },
        .pair_alg = BLE_SM_PAIR_ALG_JW,
        .authenticated = 0,
        .passkey_info = {
            .passkey = {
                .action = BLE_SM_IOACT_NONE,
            },
        },
    };
    ble_sm_test_util_peer_sc_good(&params);

    ble_hs_test_util_assert_mbufs_freed(NULL);
}

/**
 * Secure connections pairing
 * Master: peer
 * Pair algorithm: passkey entry
 * Initiator IO capabilities: 0
 * Responder IO capabilities: 2
 * Bonding: true
 * Initiator address type: 0
 * Responder address type: 0
 * Initiator key distribution: 5
 * Responder key distribution: 7
 */
TEST_CASE_SELF(ble_sm_sc_peer_pk_iio0_rio2_b1_iat0_rat0_ik5_rk7)
{
    struct ble_sm_test_params params;

    params = (struct ble_sm_test_params) {
        .init_id_addr = {
            0xca, 0x61, 0xa0, 0x67, 0x94, 0xe0,
        },
        .resp_id_addr = {
            0x33, 0x22, 0x11, 0x00, 0x45, 0x0a,
        },
        .pair_req = {
            .io_cap = 0x00,
            .oob_data_flag = 0x00,
            .authreq = 0x0d,
            .max_enc_key_size = 0x10,
            .init_key_dist = 0x0d,
            .resp_key_dist = 0x0f,
        },
        .pair_rsp = {
            .io_cap = 0x02,
            .oob_data_flag = 0x00,
            .authreq = 0x0d,
            .max_enc_key_size = 0x10,
            .init_key_dist = 0x05,
            .resp_key_dist = 0x07,
        },
        .our_priv_key = {
            0x54, 0x8d, 0x20, 0xb8, 0x97, 0x0b, 0xbc, 0x43,
            0x9a, 0xad, 0x10, 0x6f, 0x60, 0x74, 0xd4, 0x6a,
            0x55, 0xc1, 0x7a, 0x17, 0x8b, 0x60, 0xe0, 0xb4,
            0x5a, 0xe6, 0x58, 0xf1, 0xea, 0x12, 0xd9, 0xfb,
        },
        .public_key_req = {
            .x = {
                0x22, 0x26, 0xcc, 0x64, 0x4d, 0xc1, 0x01, 0xd1,
                0xb9, 0x8d, 0xe2, 0xd4, 0xbc, 0x55, 0x37, 0x4c,
                0x12, 0x81, 0x14, 0x83, 0x81, 0xe8, 0x36, 0x1b,
                0x78, 0xff, 0x49, 0xfc, 0xe9, 0x2e, 0x56, 0xc0,
            },
            .y = {
                0xd9, 0x31, 0xa5, 0x8d, 0x02, 0xf1, 0x94, 0xb6,
                0x83, 0x97, 0xd1, 0xfb, 0x01, 0x97, 0x4d, 0x06,
                0xec, 0x18, 0x8d, 0x4a, 0xd2, 0x14, 0x12, 0x95,
                0x2d, 0x4d, 0x18, 0xde, 0x4d, 0xaa, 0x91, 0x25,
            },
        },
        .public_key_rsp = {
            .x = {
                0x72, 0x8c, 0xd1, 0x88, 0xd7, 0xbe, 0x49, 0xb2,
                0xc5, 0x5c, 0x95, 0xb3, 0x64, 0xe0, 0x12, 0x32,
                0xb6, 0xc9, 0x47, 0x63, 0x37, 0x38, 0x5b, 0x9c,
                0x1e, 0x1b, 0x1a, 0x06, 0x09, 0xe2, 0x31, 0x85,
            },
            .y = {
                0x19, 0x3a, 0x29, 0x69, 0x62, 0xd6, 0x30, 0xe7,
                0xe8, 0x48, 0x63, 0xdc, 0x00, 0x73, 0x0a, 0x70,
                0x7d, 0x2e, 0x29, 0xcc, 0x91, 0x77, 0x71, 0xb1,
                0x75, 0xb8, 0xf7, 0xdc, 0xb0, 0xe2, 0x91, 0x10,
            },
        },
        .confirm_req[0] = {
            .value = {
                0x2c, 0x16, 0x15, 0x0d, 0xe8, 0x18, 0x50, 0xd8,
                0xae, 0x04, 0x6c, 0xa8, 0x50, 0xb8, 0xe5, 0x85,
            },
        },
        .confirm_rsp[0] = {
            .value = {
                0x62, 0x53, 0xfb, 0x69, 0x94, 0x33, 0x11, 0xd3,
                0x8e, 0x03, 0xd5, 0x05, 0xd7, 0x68, 0x33, 0x16,
            },
        },
        .random_req[0] = {
            .value = {
                0xd5, 0x0e, 0x27, 0xcf, 0xa4, 0xc1, 0x52, 0x1b,
                0xf1, 0x9d, 0x5f, 0xbe, 0xe2, 0xc0, 0x48, 0x38,
            },
        },
        .random_rsp[0] = {
            .value = {
                0x94, 0x31, 0x95, 0x44, 0x6c, 0xc5, 0x73, 0xc8,
                0x8d, 0x72, 0x06, 0xe7, 0xfd, 0x16, 0x70, 0x5d,
            },
        },
        .confirm_req[1] = {
            .value = {
                0x80, 0xae, 0x74, 0xaa, 0x9a, 0xfc, 0x09, 0x97,
                0x10, 0x01, 0x4e, 0xbb, 0x16, 0x36, 0x6b, 0xc7,
            },
        },
        .confirm_rsp[1] = {
            .value = {
                0x5a, 0xb1, 0xe5, 0x81, 0x5a, 0x1b, 0xef, 0xf4,
                0xa8, 0x3d, 0xaa, 0x3f, 0x02, 0x1f, 0x78, 0x55,
            },
        },
        .random_req[1] = {
            .value = {
                0x04, 0x4a, 0xf4, 0xd5, 0x4b, 0x4f, 0x77, 0x37,
                0x2a, 0x3c, 0xfe, 0x83, 0x34, 0x6b, 0x38, 0x1a,
            },
        },
        .random_rsp[1] = {
            .value = {
                0x24, 0xb3, 0x47, 0xc8, 0xb0, 0xa2, 0xa3, 0xd8,
                0x78, 0x3d, 0x09, 0x8d, 0xea, 0x49, 0xf6, 0x22,
            },
        },
        .confirm_req[2] = {
            .value = {
                0x56, 0x5f, 0x07, 0x30, 0x3a, 0xc1, 0x44, 0xf9,
                0x00, 0x03, 0xb3, 0x93, 0x58, 0xb4, 0x2c, 0x85,
            },
        },
        .confirm_rsp[2] = {
            .value = {
                0x50, 0x8a, 0xb3, 0x0b, 0xe4, 0x2e, 0xd3, 0x49,
                0x59, 0x40, 0xb2, 0x71, 0xc9, 0x49, 0x29, 0x19,
            },
        },
        .random_req[2] = {
            .value = {
                0x32, 0x37, 0x8e, 0x63, 0x6d, 0xbd, 0xd6, 0x18,
                0xee, 0xa7, 0x0e, 0xe5, 0x7e, 0x5f, 0xe1, 0x80,
            },
        },
        .random_rsp[2] = {
            .value = {
                0xa2, 0x1a, 0x92, 0xcd, 0xc0, 0x8f, 0x92, 0xb0,
                0xe6, 0xbe, 0x43, 0x55, 0xc8, 0x47, 0x56, 0x4b,
            },
        },
        .confirm_req[3] = {
            .value = {
                0x1b, 0xa0, 0x82, 0xda, 0xfc, 0xaf, 0x3f, 0x9c,
                0xdf, 0xff, 0xa2, 0x18, 0xba, 0xbd, 0x9b, 0x48,
            },
        },
        .confirm_rsp[3] = {
            .value = {
                0x6a, 0x90, 0xb7, 0x1c, 0x93, 0x4e, 0x4a, 0x8b,
                0xda, 0xe8, 0x13, 0x6e, 0x01, 0x91, 0x74, 0xb1,
            },
        },
        .random_req[3] = {
            .value = {
                0x41, 0xbf, 0x60, 0x64, 0x1d, 0xfc, 0xe2, 0xee,
                0x00, 0xa3, 0x2a, 0xb1, 0xf8, 0x34, 0x6b, 0xeb,
            },
        },
        .random_rsp[3] = {
            .value = {
                0xeb, 0x9c, 0xaf, 0x20, 0x14, 0x0f, 0xf2, 0x3e,
                0xee, 0x45, 0xca, 0xe8, 0xdc, 0x17, 0xab, 0x22,
            },
        },
        .confirm_req[4] = {
            .value = {
                0x75, 0x8f, 0x97, 0xbb, 0x87, 0xa8, 0x70, 0xda,
                0x94, 0x5a, 0xd6, 0x09, 0x78, 0xe3, 0xdd, 0x43,
            },
        },
        .confirm_rsp[4] = {
            .value = {
                0x8c, 0x2d, 0xa7, 0x44, 0xd9, 0x15, 0xa8, 0x9e,
                0xdf, 0x3a, 0x59, 0xa5, 0xee, 0x92, 0x24, 0x3c,
            },
        },
        .random_req[4] = {
            .value = {
                0xb9, 0xe0, 0xf3, 0xf6, 0x6f, 0xbd, 0xa0, 0x7a,
                0x82, 0x20, 0x61, 0xbe, 0xf3, 0xe6, 0x4e, 0xac,
            },
        },
        .random_rsp[4] = {
            .value = {
                0xdd, 0x9b, 0xd3, 0x10, 0xed, 0x12, 0xe8, 0xb5,
                0xa2, 0x59, 0xe1, 0xdc, 0x5c, 0xd8, 0x6e, 0x96,
            },
        },
        .confirm_req[5] = {
            .value = {
                0x9d, 0xc7, 0x97, 0x67, 0x8d, 0xd0, 0xd6, 0x1a,
                0x4d, 0x52, 0xc0, 0x8d, 0x87, 0xa9, 0x75, 0xf5,
            },
        },
        .confirm_rsp[5] = {
            .value = {
                0xd4, 0x5d, 0x61, 0x76, 0x38, 0xe3, 0x81, 0x85,
                0x18, 0x5f, 0xac, 0xde, 0x49, 0x57, 0xf6, 0x9b,
            },
        },
        .random_req[5] = {
            .value = {
                0xfe, 0x83, 0xe9, 0xc6, 0xe9, 0xa4, 0x83, 0x0d,
                0xaf, 0x27, 0x6f, 0x79, 0x7a, 0x2b, 0x2d, 0x1f,
            },
        },
        .random_rsp[5] = {
            .value = {
                0xf2, 0x0c, 0x9e, 0x75, 0x5b, 0xb1, 0x8c, 0xf1,
                0x46, 0x4f, 0x68, 0xe8, 0x0a, 0x65, 0xd5, 0x81,
            },
        },
        .confirm_req[6] = {
            .value = {
                0x15, 0x2b, 0x2e, 0x14, 0xf7, 0x31, 0xa2, 0xff,
                0x93, 0xa7, 0x28, 0x65, 0xb1, 0x68, 0x96, 0xc6,
            },
        },
        .confirm_rsp[6] = {
            .value = {
                0x6f, 0x01, 0x22, 0x14, 0x78, 0xfb, 0x93, 0xf4,
                0xfa, 0xf1, 0x6d, 0x33, 0x49, 0x0e, 0x7d, 0x56,
            },
        },
        .random_req[6] = {
            .value = {
                0x05, 0xe5, 0xed, 0x99, 0x63, 0x05, 0x29, 0xb1,
                0xbd, 0xf7, 0x2b, 0xa6, 0x94, 0xfe, 0x45, 0xb2,
            },
        },
        .random_rsp[6] = {
            .value = {
                0x51, 0xf1, 0x2a, 0xa6, 0x7b, 0xe0, 0xb3, 0x20,
                0x7d, 0x7e, 0xd3, 0x47, 0xfb, 0x83, 0xe1, 0xc6,
            },
        },
        .confirm_req[7] = {
            .value = {
                0x9e, 0x7a, 0x3d, 0x12, 0x3b, 0x30, 0x81, 0x23,
                0x1c, 0x94, 0x42, 0x73, 0x41, 0x68, 0xc6, 0x17,
            },
        },
        .confirm_rsp[7] = {
            .value = {
                0x55, 0x31, 0x41, 0xe8, 0x1f, 0x11, 0xa6, 0x06,
                0x7a, 0x7c, 0x84, 0x10, 0xad, 0xd3, 0x73, 0xcf,
            },
        },
        .random_req[7] = {
            .value = {
                0xcb, 0x92, 0x18, 0xf6, 0x59, 0x6a, 0x1b, 0x18,
                0x63, 0x72, 0x54, 0xc2, 0x1a, 0x3d, 0x09, 0x67,
            },
        },
        .random_rsp[7] = {
            .value = {
                0xae, 0xf2, 0x96, 0xfd, 0xff, 0xd7, 0x18, 0xac,
                0x5d, 0xb2, 0x9d, 0x89, 0x56, 0x2a, 0x19, 0xae,
            },
        },
        .confirm_req[8] = {
            .value = {
                0x06, 0x8d, 0x5d, 0x19, 0xb3, 0x27, 0xc9, 0x6a,
                0xe8, 0x58, 0xe7, 0x17, 0x10, 0x6a, 0xf9, 0xf7,
            },
        },
        .confirm_rsp[8] = {
            .value = {
                0xf0, 0xbc, 0x2a, 0x03, 0x1f, 0x9b, 0x7b, 0x58,
                0x43, 0x0f, 0xf5, 0x17, 0xc4, 0xbd, 0xec, 0x23,
            },
        },
        .random_req[8] = {
            .value = {
                0xbe, 0x78, 0xcd, 0x84, 0x91, 0x4a, 0x1b, 0xdd,
                0x6a, 0x0d, 0x88, 0x72, 0x9e, 0xc2, 0x4f, 0x5a,
            },
        },
        .random_rsp[8] = {
            .value = {
                0xff, 0xac, 0xfe, 0x71, 0x2f, 0x6a, 0x13, 0xdc,
                0xd3, 0x02, 0x81, 0x88, 0xbf, 0xc9, 0x9c, 0xd6,
            },
        },
        .confirm_req[9] = {
            .value = {
                0xb0, 0x8d, 0x47, 0x23, 0x7e, 0xdb, 0xf5, 0x64,
                0x5e, 0x83, 0x52, 0x9f, 0x06, 0x65, 0x84, 0x10,
            },
        },
        .confirm_rsp[9] = {
            .value = {
                0x4d, 0x3f, 0xd4, 0x5a, 0x45, 0x57, 0xe9, 0xd7,
                0x1e, 0x65, 0x7a, 0xa0, 0xd8, 0x5a, 0xa8, 0x29,
            },
        },
        .random_req[9] = {
            .value = {
                0xb0, 0xcd, 0xfa, 0x39, 0x0d, 0x2e, 0x07, 0xfe,
                0x36, 0x47, 0x8d, 0x8e, 0x1a, 0x47, 0x67, 0xf2,
            },
        },
        .random_rsp[9] = {
            .value = {
                0xb4, 0xf5, 0x12, 0x64, 0xf4, 0xf6, 0xd7, 0x6e,
                0xeb, 0x1e, 0x9a, 0x3f, 0x18, 0xba, 0xfb, 0x99,
            },
        },
        .confirm_req[10] = {
            .value = {
                0xc9, 0x76, 0xb3, 0x3f, 0x80, 0xd9, 0x0c, 0xfb,
                0xe3, 0x90, 0x1b, 0x7a, 0xbc, 0xe1, 0x7c, 0xde,
            },
        },
        .confirm_rsp[10] = {
            .value = {
                0x21, 0x6a, 0x45, 0x6e, 0x6a, 0xac, 0xba, 0x9e,
                0x66, 0x39, 0x5b, 0xb6, 0x74, 0xfe, 0x2b, 0x28,
            },
        },
        .random_req[10] = {
            .value = {
                0xc0, 0xd4, 0xdf, 0x7b, 0x0f, 0x2f, 0xaa, 0x68,
                0x4e, 0x3d, 0xa4, 0x59, 0x6f, 0x24, 0xe6, 0x7e,
            },
        },
        .random_rsp[10] = {
            .value = {
                0xdf, 0x89, 0x49, 0xe7, 0x9f, 0x60, 0xdd, 0xf6,
                0x44, 0x97, 0xe3, 0x15, 0x52, 0x65, 0x67, 0x3e,
            },
        },
        .confirm_req[11] = {
            .value = {
                0xb0, 0x3f, 0x34, 0xce, 0x7d, 0x2e, 0xf1, 0xab,
                0x23, 0xd5, 0x89, 0xf5, 0xaa, 0xa8, 0x59, 0x9f,
            },
        },
        .confirm_rsp[11] = {
            .value = {
                0xb1, 0x33, 0x6a, 0x64, 0xd8, 0xeb, 0x8b, 0xa0,
                0xf4, 0x1a, 0x15, 0x28, 0xb9, 0xe4, 0xa1, 0x31,
            },
        },
        .random_req[11] = {
            .value = {
                0xd2, 0x88, 0x24, 0xfe, 0x95, 0x11, 0xc5, 0x0a,
                0x21, 0xfb, 0x96, 0xea, 0x61, 0xb9, 0x8b, 0x26,
            },
        },
        .random_rsp[11] = {
            .value = {
                0x8f, 0x22, 0x66, 0x8e, 0x7e, 0x62, 0x34, 0x37,
                0xfc, 0x4a, 0x48, 0x1f, 0xf7, 0x38, 0x3b, 0x4e,
            },
        },
        .confirm_req[12] = {
            .value = {
                0xc4, 0x50, 0xc8, 0x53, 0x58, 0xfb, 0xea, 0x9a,
                0xdc, 0x35, 0xc7, 0xf3, 0x5b, 0x7c, 0xfb, 0xe4,
            },
        },
        .confirm_rsp[12] = {
            .value = {
                0x27, 0xd9, 0x32, 0xd6, 0x43, 0xbf, 0x57, 0x3f,
                0x35, 0x73, 0x3c, 0x3e, 0xbe, 0x53, 0x19, 0xff,
            },
        },
        .random_req[12] = {
            .value = {
                0x99, 0xa1, 0x7a, 0x5f, 0xe0, 0x48, 0x1c, 0x6c,
                0x84, 0xac, 0xab, 0xed, 0x69, 0x55, 0x1e, 0x66,
            },
        },
        .random_rsp[12] = {
            .value = {
                0x37, 0x50, 0x90, 0x35, 0xef, 0x84, 0x06, 0x18,
                0xfd, 0x3b, 0xc1, 0x8a, 0x46, 0x91, 0xb8, 0x21,
            },
        },
        .confirm_req[13] = {
            .value = {
                0x2f, 0xcb, 0x3e, 0xc3, 0xce, 0x82, 0x0b, 0x5c,
                0xdc, 0x9c, 0xbd, 0x44, 0xf9, 0x04, 0x22, 0x8c,
            },
        },
        .confirm_rsp[13] = {
            .value = {
                0xab, 0xf2, 0x2e, 0x40, 0xd0, 0x74, 0x4f, 0xd4,
                0x26, 0x9c, 0x89, 0x9e, 0x38, 0x77, 0xac, 0x9d,
            },
        },
        .random_req[13] = {
            .value = {
                0xbc, 0xda, 0x58, 0xa2, 0x98, 0x88, 0xfe, 0x9f,
                0x95, 0x0e, 0x3a, 0x91, 0xba, 0xe9, 0xbf, 0x02,
            },
        },
        .random_rsp[13] = {
            .value = {
                0x04, 0xb9, 0x4c, 0x26, 0xce, 0x87, 0x8f, 0x17,
                0xdc, 0xbc, 0x36, 0x94, 0x47, 0x67, 0x9f, 0xde,
            },
        },
        .confirm_req[14] = {
            .value = {
                0xbd, 0xb6, 0x54, 0xc8, 0x1f, 0x51, 0x23, 0x98,
                0x48, 0x3d, 0x47, 0x9d, 0xa3, 0xb8, 0xe7, 0x55,
            },
        },
        .confirm_rsp[14] = {
            .value = {
                0x06, 0xc2, 0x7b, 0x80, 0x76, 0x9c, 0x37, 0x78,
                0x46, 0xc5, 0x45, 0x43, 0x5d, 0x8d, 0x5b, 0x3e,
            },
        },
        .random_req[14] = {
            .value = {
                0xef, 0x9e, 0x8a, 0x3a, 0xb7, 0xde, 0xa8, 0x07,
                0x58, 0x73, 0xe0, 0x07, 0xfc, 0x62, 0xdb, 0x62,
            },
        },
        .random_rsp[14] = {
            .value = {
                0xfa, 0xd5, 0xb2, 0x4e, 0x20, 0x01, 0x93, 0xc0,
                0xb3, 0x76, 0xa5, 0x7a, 0x92, 0x8f, 0xb9, 0x6d,
            },
        },
        .confirm_req[15] = {
            .value = {
                0x76, 0x2e, 0xc6, 0x64, 0x6c, 0x13, 0x01, 0x7e,
                0x34, 0x78, 0x12, 0xb8, 0x1a, 0xb7, 0xf7, 0x39,
            },
        },
        .confirm_rsp[15] = {
            .value = {
                0xbd, 0xae, 0x10, 0x32, 0xdb, 0x63, 0x30, 0x6f,
                0x68, 0x19, 0x49, 0x5e, 0x34, 0x4f, 0x13, 0xc6,
            },
        },
        .random_req[15] = {
            .value = {
                0x95, 0x2e, 0xe4, 0xe3, 0xb2, 0xdc, 0x79, 0xad,
                0x5f, 0x0c, 0x19, 0x9c, 0x47, 0x9c, 0x79, 0x17,
            },
        },
        .random_rsp[15] = {
            .value = {
                0x9e, 0x3d, 0x7f, 0xcd, 0x18, 0x40, 0xd7, 0xac,
                0xa1, 0x45, 0x5f, 0xcb, 0x29, 0x57, 0x2b, 0x63,
            },
        },
        .confirm_req[16] = {
            .value = {
                0x10, 0x18, 0x9d, 0xf2, 0xed, 0x76, 0x5c, 0x5f,
                0x32, 0xa6, 0x29, 0x61, 0x12, 0xb2, 0xb8, 0xa2,
            },
        },
        .confirm_rsp[16] = {
            .value = {
                0x3c, 0xd4, 0xbd, 0xe9, 0xd3, 0x29, 0xac, 0xf7,
                0xfc, 0x04, 0xd3, 0xe4, 0x46, 0x14, 0x28, 0x2c,
            },
        },
        .random_req[16] = {
            .value = {
                0x6d, 0xe8, 0x77, 0xc3, 0xab, 0x49, 0x6b, 0x79,
                0x4f, 0x0f, 0x4c, 0x65, 0xc5, 0x77, 0x68, 0xd9,
            },
        },
        .random_rsp[16] = {
            .value = {
                0xd0, 0x59, 0xf3, 0x53, 0xb1, 0x14, 0x81, 0x88,
                0x26, 0x88, 0xef, 0x4b, 0xa4, 0x7d, 0x0a, 0x84,
            },
        },
        .confirm_req[17] = {
            .value = {
                0xa3, 0x96, 0x9f, 0x96, 0x53, 0x0e, 0x38, 0x78,
                0x9e, 0xbd, 0xf7, 0x65, 0x23, 0x73, 0x99, 0xa7,
            },
        },
        .confirm_rsp[17] = {
            .value = {
                0x6b, 0x25, 0x8d, 0x51, 0xd8, 0xc4, 0xd9, 0xbf,
                0xa6, 0x4f, 0xa3, 0x25, 0x28, 0xb5, 0x7c, 0x05,
            },
        },
        .random_req[17] = {
            .value = {
                0xa5, 0xac, 0xd9, 0xb6, 0x9e, 0x98, 0x75, 0xae,
                0x9b, 0x16, 0xe1, 0x60, 0xc6, 0xa5, 0x07, 0xf2,
            },
        },
        .random_rsp[17] = {
            .value = {
                0x65, 0x53, 0x56, 0xe6, 0x2c, 0x22, 0x68, 0xc9,
                0xb8, 0xbe, 0xb1, 0x40, 0x08, 0xe2, 0xb6, 0xb9,
            },
        },
        .confirm_req[18] = {
            .value = {
                0x67, 0xcd, 0x0e, 0x4f, 0xfc, 0x38, 0x7f, 0x8a,
                0x3b, 0xea, 0xff, 0x86, 0xf3, 0x8a, 0x92, 0xcb,
            },
        },
        .confirm_rsp[18] = {
            .value = {
                0x22, 0x95, 0x1f, 0x20, 0xc9, 0x5c, 0x73, 0x39,
                0xa4, 0xd9, 0xc1, 0x37, 0x9d, 0x94, 0xb2, 0xfd,
            },
        },
        .random_req[18] = {
            .value = {
                0xe1, 0x80, 0x82, 0xdd, 0x21, 0x6c, 0xe4, 0x93,
                0xa3, 0x41, 0x0f, 0xfc, 0x96, 0x42, 0x8b, 0xde,
            },
        },
        .random_rsp[18] = {
            .value = {
                0x11, 0x1c, 0xd7, 0x7a, 0xe7, 0x1a, 0x88, 0xdd,
                0x2a, 0xdf, 0xe5, 0x30, 0xca, 0x0b, 0x9f, 0xb6,
            },
        },
        .confirm_req[19] = {
            .value = {
                0x45, 0x9b, 0x36, 0x3d, 0xf8, 0xc0, 0x43, 0x6d,
                0x94, 0xcf, 0xbd, 0x5f, 0xfe, 0xec, 0xd7, 0x4b,
            },
        },
        .confirm_rsp[19] = {
            .value = {
                0xf0, 0xaa, 0xfd, 0xae, 0xb7, 0x73, 0x3c, 0x9d,
                0x93, 0xd4, 0x00, 0xea, 0x81, 0x31, 0xde, 0x41,
            },
        },
        .random_req[19] = {
            .value = {
                0x1a, 0xaa, 0xff, 0x2a, 0xdc, 0xcc, 0x89, 0xbc,
                0xcf, 0x48, 0x5c, 0x1e, 0x4d, 0x69, 0x85, 0x39,
            },
        },
        .random_rsp[19] = {
            .value = {
                0xe7, 0xd0, 0xcb, 0x9a, 0xb5, 0x76, 0xec, 0xfc,
                0x48, 0xa3, 0x41, 0x48, 0x4c, 0xa7, 0xec, 0xb7,
            },
        },
        .dhkey_check_req = {
            .value = {
                0xe3, 0x4e, 0x42, 0xb5, 0xe3, 0x63, 0x4b, 0x7c,
                0xf0, 0x9f, 0xef, 0x6e, 0x97, 0xe2, 0x86, 0xc0,
            },
        },
        .dhkey_check_rsp = {
            .value = {
                0xea, 0x8a, 0xab, 0x7f, 0x15, 0x21, 0x5a, 0x36,
                0x9b, 0x56, 0xee, 0x51, 0x61, 0x97, 0xe2, 0x0a,
            },
        },
        .id_info_req = {
            .irk = {
                0xef, 0x8d, 0xe2, 0x16, 0x4f, 0xec, 0x43, 0x0d,
                0xbf, 0x5b, 0xdd, 0x34, 0xc0, 0x53, 0x1e, 0xb8,
            },
        },
        .id_addr_info_req = {
            .addr_type = 0,
            .bd_addr = {
                0x33, 0x22, 0x11, 0x00, 0x45, 0x0a,
            },
        },
        .sign_info_req = {
            .sig_key = {
                0x85, 0x54, 0x52, 0xe3, 0xb4, 0xe8, 0x26, 0xa4,
                0x38, 0xb0, 0x4c, 0xa0, 0x41, 0xf5, 0x30, 0x6e,
            },
        },
        .sign_info_rsp = {
            .sig_key = {
                0x6f, 0x93, 0xb8, 0x9c, 0x26, 0x88, 0xb4, 0x20,
                0x87, 0x95, 0xf2, 0xf4, 0x3a, 0xbe, 0x92, 0xb7,
            },
        },
        .ltk = {
            0x30, 0xf6, 0xd3, 0x2e, 0x1c, 0x81, 0x2c, 0x96,
            0x56, 0x30, 0x55, 0xec, 0x9b, 0x72, 0xf4, 0x83,
        },
        .pair_alg = BLE_SM_PAIR_ALG_PASSKEY,
        .authenticated = 1,
        .passkey_info = {
            .passkey = {
                .action = BLE_SM_IOACT_INPUT,
                .passkey = 879894,
            },
        },
    };
    ble_sm_test_util_peer_sc_good(&params);

    ble_hs_test_util_assert_mbufs_freed(NULL);
}

/**
 * Secure connections pairing
 * Master: peer
 * Pair algorithm: passkey entry
 * Initiator IO capabilities: 2
 * Responder IO capabilities: 0
 * Bonding: true
 * Initiator address type: 0
 * Responder address type: 0
 * Initiator key distribution: 5
 * Responder key distribution: 7
 */
TEST_CASE_SELF(ble_sm_sc_peer_pk_iio2_rio0_b1_iat0_rat0_ik5_rk7)
{
    struct ble_sm_test_params params;

    params = (struct ble_sm_test_params) {
        .init_id_addr = {
            0xca, 0x61, 0xa0, 0x67, 0x94, 0xe0,
        },
        .resp_id_addr = {
            0x33, 0x22, 0x11, 0x00, 0x45, 0x0a,
        },
        .pair_req = {
            .io_cap = 0x02,
            .oob_data_flag = 0x00,
            .authreq = 0x0d,
            .max_enc_key_size = 0x10,
            .init_key_dist = 0x0d,
            .resp_key_dist = 0x0f,
        },
        .pair_rsp = {
            .io_cap = 0x00,
            .oob_data_flag = 0x00,
            .authreq = 0x0d,
            .max_enc_key_size = 0x10,
            .init_key_dist = 0x05,
            .resp_key_dist = 0x07,
        },
        .our_priv_key = {
            0xd6, 0x2f, 0x4f, 0x6b, 0xeb, 0xfc, 0xbd, 0xee,
            0x9b, 0x94, 0xd7, 0x15, 0x98, 0xc6, 0x0c, 0x83,
            0x9b, 0xc7, 0xa2, 0x45, 0xfd, 0x00, 0xe8, 0xa4,
            0x52, 0xe9, 0x70, 0x2f, 0xd7, 0x62, 0xf1, 0xa4,
        },
        .public_key_req = {
            .x = {
                0xd6, 0xa7, 0xaf, 0xc1, 0x18, 0x8b, 0x92, 0x2f,
                0xbc, 0xbc, 0x4d, 0xb8, 0x5c, 0xfb, 0x39, 0x7c,
                0x1e, 0x90, 0x7e, 0xfa, 0xa2, 0x0d, 0xee, 0x9e,
                0xb4, 0x9e, 0xbe, 0x50, 0xf0, 0xbc, 0x2c, 0x10,
            },
            .y = {
                0xa4, 0x25, 0xad, 0x75, 0xbe, 0xab, 0x1e, 0xcf,
                0x4e, 0xc8, 0x19, 0xab, 0x6c, 0x68, 0x38, 0xa4,
                0xe7, 0x43, 0x7b, 0x19, 0xef, 0x28, 0xd5, 0x93,
                0x52, 0xe9, 0xb9, 0x31, 0x68, 0x60, 0x19, 0x71,
            },
        },
        .public_key_rsp = {
            .x = {
                0xbc, 0x6a, 0xcf, 0xc6, 0x8a, 0x3a, 0xdc, 0x89,
                0xdd, 0xa9, 0xaf, 0x29, 0xc7, 0xaf, 0xe2, 0x8b,
                0x25, 0xee, 0xce, 0xa6, 0x10, 0x1d, 0x33, 0x2f,
                0xd5, 0xfc, 0x30, 0xb8, 0xb1, 0x7b, 0xb1, 0x6e,
            },
            .y = {
                0x1a, 0xc6, 0x42, 0x36, 0x98, 0x40, 0x4f, 0x90,
                0x82, 0xa0, 0x10, 0x3a, 0xa5, 0x0f, 0xcf, 0x57,
                0xd2, 0x2e, 0x80, 0x9d, 0x61, 0xc7, 0x21, 0xac,
                0x47, 0x5b, 0x93, 0x75, 0x02, 0x30, 0x40, 0x14,
            },
        },
        .confirm_req[0] = {
            .value = {
                0xd1, 0x64, 0x49, 0xa0, 0xc4, 0x28, 0x81, 0x57,
                0x0c, 0x25, 0x62, 0xfb, 0x2c, 0xa2, 0xb0, 0xc7,
            },
        },
        .confirm_rsp[0] = {
            .value = {
                0xea, 0xae, 0x4e, 0x03, 0x00, 0xf9, 0xd1, 0x65,
                0xc7, 0x6a, 0x0d, 0x74, 0x4f, 0x02, 0x0b, 0x94,
            },
        },
        .random_req[0] = {
            .value = {
                0x05, 0xb2, 0x09, 0x9b, 0x36, 0x23, 0x4f, 0x74,
                0x4e, 0xc9, 0x7a, 0x2c, 0x65, 0x3a, 0xd1, 0xf6,
            },
        },
        .random_rsp[0] = {
            .value = {
                0x50, 0xd8, 0x88, 0xd4, 0x7e, 0xc1, 0x36, 0x92,
                0x0f, 0xa7, 0x17, 0x3c, 0xb4, 0xeb, 0xee, 0xa6,
            },
        },
        .confirm_req[1] = {
            .value = {
                0xab, 0xa2, 0xd0, 0xec, 0xdd, 0xf3, 0xd2, 0xa9,
                0x2d, 0xde, 0x4b, 0x02, 0x66, 0x45, 0x2f, 0xc0,
            },
        },
        .confirm_rsp[1] = {
            .value = {
                0xa9, 0xc1, 0x9d, 0x75, 0xd0, 0xb6, 0xec, 0x06,
                0x31, 0x87, 0xb6, 0x9d, 0x31, 0xdc, 0x92, 0x7c,
            },
        },
        .random_req[1] = {
            .value = {
                0xb9, 0x5b, 0xe0, 0x0f, 0x83, 0xe7, 0x2d, 0x77,
                0x2f, 0x55, 0x0a, 0x2c, 0xd9, 0xc1, 0x46, 0xcd,
            },
        },
        .random_rsp[1] = {
            .value = {
                0xa2, 0x9a, 0x5b, 0x99, 0xb1, 0xc0, 0xc5, 0xd6,
                0xf1, 0x87, 0x0b, 0x49, 0x9c, 0xfd, 0xfe, 0xd5,
            },
        },
        .confirm_req[2] = {
            .value = {
                0x3a, 0x9d, 0x58, 0xe5, 0xb0, 0x31, 0xd9, 0xde,
                0xac, 0xd2, 0x44, 0xb7, 0xe1, 0xe5, 0x89, 0x50,
            },
        },
        .confirm_rsp[2] = {
            .value = {
                0xae, 0x4e, 0x4f, 0x84, 0x5f, 0x4c, 0xd1, 0x9b,
                0x81, 0x22, 0x9c, 0x68, 0x52, 0xe0, 0x9a, 0xfc,
            },
        },
        .random_req[2] = {
            .value = {
                0xa5, 0xbb, 0x5f, 0x9a, 0xa2, 0x97, 0xdb, 0xcd,
                0x3d, 0xfe, 0xd9, 0x58, 0x21, 0x52, 0x99, 0xb7,
            },
        },
        .random_rsp[2] = {
            .value = {
                0xea, 0x44, 0xdd, 0x0c, 0xbf, 0xb5, 0x6b, 0xc7,
                0xe1, 0x19, 0xe8, 0x0b, 0xc2, 0x15, 0x04, 0x37,
            },
        },
        .confirm_req[3] = {
            .value = {
                0xa8, 0xa3, 0xdb, 0x08, 0xca, 0x31, 0xd5, 0xef,
                0x17, 0x37, 0x77, 0xd0, 0x64, 0x2e, 0x2f, 0x2f,
            },
        },
        .confirm_rsp[3] = {
            .value = {
                0xe4, 0xf6, 0xa5, 0x94, 0x1a, 0x09, 0x4b, 0x75,
                0x79, 0xb8, 0x0c, 0xe6, 0xe2, 0x28, 0x5a, 0x2c,
            },
        },
        .random_req[3] = {
            .value = {
                0x1a, 0x3f, 0x80, 0x6f, 0xd3, 0xe8, 0xc5, 0xfb,
                0x9b, 0xda, 0xa1, 0x07, 0x68, 0x1a, 0x54, 0xbc,
            },
        },
        .random_rsp[3] = {
            .value = {
                0x1b, 0x48, 0x22, 0x87, 0x04, 0x24, 0x87, 0xba,
                0x14, 0xb9, 0x85, 0xb2, 0xa6, 0xf5, 0xea, 0x89,
            },
        },
        .confirm_req[4] = {
            .value = {
                0x31, 0xcb, 0xc4, 0x0c, 0x36, 0xb5, 0xe2, 0x32,
                0xd8, 0x0e, 0xd3, 0x86, 0x96, 0xe3, 0x8c, 0x84,
            },
        },
        .confirm_rsp[4] = {
            .value = {
                0x90, 0x11, 0x30, 0x35, 0x5f, 0xe5, 0x45, 0xff,
                0xab, 0xd3, 0xe0, 0xbe, 0x1c, 0x20, 0x23, 0xb8,
            },
        },
        .random_req[4] = {
            .value = {
                0xa0, 0xc7, 0x79, 0x28, 0x87, 0x19, 0xa3, 0x78,
                0x33, 0xe5, 0x1a, 0x81, 0xba, 0x9b, 0xe3, 0x5c,
            },
        },
        .random_rsp[4] = {
            .value = {
                0x43, 0xcf, 0x20, 0x1b, 0x39, 0x3f, 0xdf, 0x73,
                0x58, 0xd2, 0x0d, 0xc7, 0x41, 0xd7, 0x58, 0xea,
            },
        },
        .confirm_req[5] = {
            .value = {
                0x59, 0xda, 0x78, 0xeb, 0xd5, 0xcd, 0x8e, 0x23,
                0xe5, 0x5e, 0xa7, 0xa5, 0xba, 0x13, 0x00, 0xff,
            },
        },
        .confirm_rsp[5] = {
            .value = {
                0x31, 0x7a, 0xf0, 0x56, 0x82, 0x69, 0xdb, 0xcd,
                0x27, 0x5a, 0x11, 0xd3, 0x65, 0x82, 0x0d, 0xda,
            },
        },
        .random_req[5] = {
            .value = {
                0x2e, 0xe8, 0x76, 0x40, 0x9c, 0x49, 0x07, 0x42,
                0x1e, 0x45, 0x7b, 0x1e, 0x73, 0xa3, 0x71, 0x05,
            },
        },
        .random_rsp[5] = {
            .value = {
                0x64, 0x99, 0x42, 0x5d, 0x05, 0xd6, 0x12, 0x41,
                0x2a, 0x44, 0x55, 0x26, 0xe7, 0x08, 0x5e, 0xfb,
            },
        },
        .confirm_req[6] = {
            .value = {
                0x1c, 0x55, 0xe1, 0x75, 0x4f, 0x6e, 0xdd, 0x7e,
                0xc8, 0xff, 0x76, 0x25, 0xdb, 0x2a, 0x6d, 0xe3,
            },
        },
        .confirm_rsp[6] = {
            .value = {
                0xf6, 0x36, 0x78, 0x88, 0x62, 0xa8, 0x78, 0xe6,
                0xf9, 0xa1, 0x17, 0x63, 0x86, 0xd3, 0xae, 0x60,
            },
        },
        .random_req[6] = {
            .value = {
                0x96, 0x9a, 0x1c, 0xbe, 0x82, 0x82, 0xc2, 0xa7,
                0x18, 0xc3, 0x7b, 0x40, 0x5d, 0x6c, 0x4e, 0xe3,
            },
        },
        .random_rsp[6] = {
            .value = {
                0x2b, 0x7d, 0x36, 0xc3, 0xf7, 0x59, 0x63, 0x40,
                0x6f, 0xc0, 0x2a, 0x2b, 0x1b, 0xd7, 0x41, 0x38,
            },
        },
        .confirm_req[7] = {
            .value = {
                0x88, 0x99, 0x53, 0xae, 0x2a, 0xaf, 0x97, 0x5a,
                0xcc, 0x9f, 0xfd, 0xe2, 0x1d, 0xd3, 0x27, 0x66,
            },
        },
        .confirm_rsp[7] = {
            .value = {
                0xdb, 0xae, 0xfb, 0xf7, 0x33, 0xd4, 0xd1, 0xcb,
                0xfe, 0x75, 0x8e, 0x81, 0x16, 0xd1, 0x49, 0xeb,
            },
        },
        .random_req[7] = {
            .value = {
                0x13, 0x5c, 0x00, 0x34, 0xe5, 0x96, 0xd0, 0x97,
                0xb1, 0x84, 0x3d, 0x00, 0xb4, 0x2a, 0x4a, 0x12,
            },
        },
        .random_rsp[7] = {
            .value = {
                0xed, 0x94, 0x1f, 0x41, 0x12, 0xe5, 0x35, 0x5b,
                0xa6, 0x6a, 0x72, 0x1e, 0xa2, 0x7c, 0xe1, 0x6c,
            },
        },
        .confirm_req[8] = {
            .value = {
                0xa3, 0xc7, 0x17, 0xad, 0xb6, 0xe6, 0xaa, 0x16,
                0x8d, 0x4b, 0x70, 0x5f, 0x49, 0x73, 0xa7, 0x19,
            },
        },
        .confirm_rsp[8] = {
            .value = {
                0x10, 0xb0, 0x31, 0xa7, 0x16, 0x61, 0xf7, 0xd6,
                0xe6, 0x16, 0x9e, 0xb1, 0x9e, 0xb5, 0x5e, 0x94,
            },
        },
        .random_req[8] = {
            .value = {
                0x6f, 0xe7, 0x62, 0x73, 0xfb, 0xbf, 0xf1, 0x4a,
                0x14, 0xa1, 0x09, 0x45, 0xd4, 0xde, 0x26, 0xad,
            },
        },
        .random_rsp[8] = {
            .value = {
                0x3f, 0x48, 0xa7, 0xdf, 0x4a, 0xd5, 0x55, 0x26,
                0xd3, 0x32, 0xbf, 0x98, 0x4a, 0x20, 0xad, 0xb0,
            },
        },
        .confirm_req[9] = {
            .value = {
                0x88, 0x1c, 0xef, 0xfe, 0x4e, 0x68, 0x41, 0x7c,
                0xe8, 0xe8, 0x81, 0x1a, 0xb9, 0x9e, 0xaf, 0xc6,
            },
        },
        .confirm_rsp[9] = {
            .value = {
                0xa3, 0x53, 0x2a, 0xe1, 0xbd, 0x9d, 0xbe, 0x89,
                0xf8, 0xc7, 0x70, 0x6e, 0xa9, 0x12, 0x07, 0x0d,
            },
        },
        .random_req[9] = {
            .value = {
                0x52, 0x06, 0x56, 0x09, 0xf4, 0xb2, 0xb9, 0x63,
                0x3f, 0x2e, 0x59, 0x6c, 0x6b, 0x43, 0xb6, 0xc0,
            },
        },
        .random_rsp[9] = {
            .value = {
                0x36, 0xb0, 0x33, 0x84, 0x52, 0xd1, 0x60, 0xac,
                0x37, 0x81, 0x6b, 0x18, 0x5f, 0xfc, 0x61, 0xb1,
            },
        },
        .confirm_req[10] = {
            .value = {
                0xc8, 0x55, 0xb7, 0x9e, 0x3e, 0xf0, 0x26, 0xa4,
                0x55, 0xb3, 0x1d, 0x4d, 0xa1, 0x5d, 0xa9, 0xaf,
            },
        },
        .confirm_rsp[10] = {
            .value = {
                0xb7, 0xb9, 0x6b, 0x8e, 0xef, 0xd3, 0xbc, 0x58,
                0x10, 0xbe, 0x5a, 0x9a, 0x4d, 0xbc, 0xec, 0xe3,
            },
        },
        .random_req[10] = {
            .value = {
                0x55, 0xa1, 0xf4, 0xd7, 0xfa, 0xe1, 0x84, 0x03,
                0xed, 0xb6, 0x95, 0x63, 0x4b, 0x93, 0x93, 0xc2,
            },
        },
        .random_rsp[10] = {
            .value = {
                0x72, 0xa9, 0xe5, 0xf7, 0x48, 0x1f, 0x64, 0x71,
                0xd9, 0x81, 0xf0, 0xc5, 0x4d, 0x38, 0xac, 0x9a,
            },
        },
        .confirm_req[11] = {
            .value = {
                0x12, 0x37, 0x56, 0xa6, 0x66, 0xa1, 0x23, 0xee,
                0xe3, 0x1e, 0x20, 0x66, 0x66, 0x85, 0x7c, 0xa8,
            },
        },
        .confirm_rsp[11] = {
            .value = {
                0x74, 0xaa, 0xbb, 0x5a, 0xdf, 0xd9, 0xc4, 0xaf,
                0xe4, 0xa7, 0xe6, 0x4b, 0x45, 0x97, 0xf8, 0x7d,
            },
        },
        .random_req[11] = {
            .value = {
                0x29, 0xce, 0xcc, 0xb7, 0xb2, 0x1e, 0x0e, 0xa8,
                0x48, 0x90, 0x43, 0x6d, 0x34, 0xa4, 0xa3, 0x12,
            },
        },
        .random_rsp[11] = {
            .value = {
                0x2e, 0xaf, 0x4c, 0x63, 0x84, 0x2c, 0x62, 0x67,
                0x68, 0x8f, 0x0b, 0xfd, 0xff, 0xef, 0x15, 0x26,
            },
        },
        .confirm_req[12] = {
            .value = {
                0xec, 0xcf, 0x6a, 0x60, 0x77, 0x04, 0x2c, 0x62,
                0x42, 0xf0, 0x21, 0xfd, 0x53, 0xd6, 0x8a, 0xe8,
            },
        },
        .confirm_rsp[12] = {
            .value = {
                0x2c, 0x13, 0x65, 0x69, 0xd7, 0x66, 0x04, 0x13,
                0x3c, 0xa8, 0xfb, 0xe5, 0x76, 0xbb, 0x4f, 0x48,
            },
        },
        .random_req[12] = {
            .value = {
                0x0d, 0x93, 0x30, 0xe2, 0x76, 0xf1, 0xbc, 0x24,
                0x61, 0x0d, 0xcd, 0xef, 0x33, 0x98, 0xe2, 0x3b,
            },
        },
        .random_rsp[12] = {
            .value = {
                0xb6, 0x32, 0x69, 0x81, 0xc0, 0x81, 0x46, 0xae,
                0x8d, 0x5a, 0x17, 0xb5, 0xc0, 0x0f, 0x9f, 0x4e,
            },
        },
        .confirm_req[13] = {
            .value = {
                0x89, 0x96, 0x22, 0x0c, 0x76, 0xdf, 0x27, 0x13,
                0x96, 0x5a, 0x0c, 0x88, 0x65, 0x18, 0x74, 0x52,
            },
        },
        .confirm_rsp[13] = {
            .value = {
                0x1c, 0x77, 0x25, 0x22, 0xc0, 0x28, 0x88, 0x45,
                0x29, 0x62, 0x7a, 0x8e, 0xc0, 0x2a, 0x5c, 0xd8,
            },
        },
        .random_req[13] = {
            .value = {
                0xcc, 0x84, 0xb6, 0x98, 0x3e, 0xf9, 0x09, 0xd2,
                0x71, 0x47, 0x56, 0xb1, 0x09, 0xf5, 0xd2, 0x0b,
            },
        },
        .random_rsp[13] = {
            .value = {
                0xf0, 0xcf, 0x1c, 0xa6, 0x24, 0xcd, 0xfa, 0x42,
                0xa4, 0x93, 0x8b, 0xa0, 0xe3, 0x42, 0x72, 0x51,
            },
        },
        .confirm_req[14] = {
            .value = {
                0xab, 0xb0, 0xa3, 0x80, 0x0d, 0xcb, 0x8e, 0xf6,
                0x6c, 0x07, 0x50, 0xe9, 0x8a, 0x85, 0x02, 0xae,
            },
        },
        .confirm_rsp[14] = {
            .value = {
                0xf6, 0x52, 0xd8, 0x34, 0x15, 0x62, 0x9f, 0x6e,
                0x2b, 0x52, 0xdc, 0x1c, 0x70, 0x17, 0x0a, 0x31,
            },
        },
        .random_req[14] = {
            .value = {
                0x8d, 0xc9, 0x0a, 0x45, 0xe9, 0x81, 0x0d, 0x5e,
                0xbb, 0xd8, 0x94, 0x29, 0x68, 0x42, 0x44, 0xe2,
            },
        },
        .random_rsp[14] = {
            .value = {
                0x96, 0x2a, 0x35, 0x39, 0x09, 0xf7, 0x66, 0x5a,
                0xb6, 0x33, 0x77, 0x6d, 0xba, 0xd3, 0x8a, 0xfb,
            },
        },
        .confirm_req[15] = {
            .value = {
                0x53, 0x08, 0x9b, 0x37, 0xc3, 0x79, 0xe6, 0x8c,
                0x42, 0x30, 0x94, 0x73, 0x6f, 0x39, 0x64, 0x20,
            },
        },
        .confirm_rsp[15] = {
            .value = {
                0x4d, 0xb7, 0xe9, 0x50, 0x8e, 0x0f, 0xe0, 0xd5,
                0x3e, 0xf6, 0x32, 0xdd, 0xb8, 0x18, 0x77, 0xd3,
            },
        },
        .random_req[15] = {
            .value = {
                0x8d, 0x49, 0x14, 0xdd, 0x95, 0x57, 0x55, 0x14,
                0x48, 0x97, 0xd3, 0x73, 0x29, 0xa0, 0xb9, 0x2b,
            },
        },
        .random_rsp[15] = {
            .value = {
                0xcf, 0x38, 0x8b, 0xab, 0xe4, 0x2b, 0x3f, 0x13,
                0xc3, 0xfb, 0x07, 0xee, 0x0e, 0x33, 0x2f, 0x04,
            },
        },
        .confirm_req[16] = {
            .value = {
                0xc6, 0x58, 0x13, 0x19, 0x56, 0x06, 0x52, 0x4b,
                0x3d, 0x5e, 0x9d, 0xa8, 0x48, 0xf2, 0x40, 0xf3,
            },
        },
        .confirm_rsp[16] = {
            .value = {
                0xbb, 0x93, 0xd2, 0xed, 0x89, 0x66, 0xa5, 0x1c,
                0xc9, 0x2a, 0x42, 0x2c, 0xff, 0x4a, 0x80, 0x84,
            },
        },
        .random_req[16] = {
            .value = {
                0x3d, 0x9c, 0x11, 0x2a, 0xd3, 0xce, 0x4b, 0x20,
                0xf2, 0xfb, 0xdd, 0x18, 0x4d, 0x7c, 0x58, 0xb6,
            },
        },
        .random_rsp[16] = {
            .value = {
                0xda, 0x80, 0x63, 0x9d, 0xa2, 0x73, 0x61, 0xdd,
                0x9a, 0x45, 0x91, 0x4d, 0x78, 0x39, 0x54, 0x75,
            },
        },
        .confirm_req[17] = {
            .value = {
                0x2e, 0xe4, 0x44, 0xe8, 0xdb, 0xc2, 0xbd, 0x62,
                0xd1, 0xc4, 0x23, 0x4e, 0x5f, 0x65, 0xb6, 0x3b,
            },
        },
        .confirm_rsp[17] = {
            .value = {
                0x19, 0x91, 0xa3, 0xc7, 0x3b, 0x68, 0x12, 0x24,
                0xcd, 0xd6, 0x02, 0xf5, 0xcd, 0x19, 0x6c, 0x88,
            },
        },
        .random_req[17] = {
            .value = {
                0xf0, 0x28, 0x18, 0xe8, 0xa7, 0x3e, 0xd8, 0x21,
                0x42, 0x58, 0xb3, 0x72, 0xa0, 0x34, 0x89, 0x04,
            },
        },
        .random_rsp[17] = {
            .value = {
                0xe9, 0xff, 0x0b, 0x9a, 0xfd, 0x29, 0x95, 0x37,
                0x89, 0x2c, 0x84, 0xfa, 0x02, 0xa0, 0xb6, 0xeb,
            },
        },
        .confirm_req[18] = {
            .value = {
                0x4f, 0x90, 0x70, 0xbe, 0xc4, 0x81, 0x9f, 0xc1,
                0x74, 0xa3, 0x01, 0x2e, 0x78, 0x7a, 0xe2, 0x61,
            },
        },
        .confirm_rsp[18] = {
            .value = {
                0xbb, 0xd5, 0x91, 0xec, 0x81, 0xe0, 0x9b, 0x5e,
                0xe9, 0xd2, 0x93, 0x57, 0xa8, 0x27, 0xdd, 0x9b,
            },
        },
        .random_req[18] = {
            .value = {
                0x78, 0xa4, 0x35, 0x1a, 0xbc, 0xa7, 0x19, 0x8c,
                0x96, 0x8f, 0x63, 0x9d, 0x11, 0xee, 0x27, 0x44,
            },
        },
        .random_rsp[18] = {
            .value = {
                0x39, 0x5b, 0x71, 0xfd, 0x7e, 0x39, 0x6b, 0xbe,
                0xaf, 0xe1, 0x55, 0x90, 0xa6, 0x58, 0xec, 0xc5,
            },
        },
        .confirm_req[19] = {
            .value = {
                0x91, 0x43, 0xe5, 0xc8, 0x26, 0x0c, 0x8c, 0x6c,
                0xf3, 0xd1, 0x30, 0xb3, 0x22, 0x94, 0x4c, 0x67,
            },
        },
        .confirm_rsp[19] = {
            .value = {
                0x51, 0xc4, 0x3e, 0x09, 0xca, 0x03, 0xbe, 0x2c,
                0xe8, 0x1a, 0x5d, 0x07, 0x12, 0x14, 0x2d, 0x43,
            },
        },
        .random_req[19] = {
            .value = {
                0x2f, 0xa1, 0x20, 0xde, 0xf5, 0xb4, 0xa6, 0x92,
                0x31, 0xe9, 0x86, 0x63, 0xef, 0xc1, 0x85, 0x3b,
            },
        },
        .random_rsp[19] = {
            .value = {
                0x41, 0xd0, 0xd0, 0x96, 0x93, 0xd1, 0xcb, 0xed,
                0xab, 0x27, 0xd5, 0x88, 0x5e, 0xe6, 0x5e, 0x5c,
            },
        },
        .dhkey_check_req = {
            .value = {
                0xec, 0xc5, 0x5f, 0xf3, 0xae, 0xfe, 0x79, 0x65,
                0x17, 0x5a, 0x60, 0xf7, 0x36, 0x4f, 0x90, 0x45,
            },
        },
        .dhkey_check_rsp = {
            .value = {
                0xa7, 0x45, 0x7a, 0x54, 0x1b, 0x64, 0x08, 0x60,
                0x51, 0x7d, 0x74, 0x27, 0x48, 0xa2, 0xf1, 0x0f,
            },
        },
        .id_info_req = {
            .irk = {
                0xef, 0x8d, 0xe2, 0x16, 0x4f, 0xec, 0x43, 0x0d,
                0xbf, 0x5b, 0xdd, 0x34, 0xc0, 0x53, 0x1e, 0xb8,
            },
        },
        .id_addr_info_req = {
            .addr_type = 0,
            .bd_addr = {
                0x33, 0x22, 0x11, 0x00, 0x45, 0x0a,
            },
        },
        .sign_info_req = {
            .sig_key = {
                0x36, 0x9a, 0xd9, 0x25, 0x5c, 0xdb, 0x78, 0xdc,
                0x1d, 0x2c, 0x83, 0xf7, 0xde, 0x99, 0xa0, 0x66,
            },
        },
        .sign_info_rsp = {
            .sig_key = {
                0x61, 0x6e, 0x9a, 0x26, 0xc5, 0xd0, 0x85, 0xdc,
                0xea, 0x9d, 0xca, 0x3b, 0x17, 0xd7, 0x43, 0x80,
            },
        },
        .ltk = {
            0xd6, 0x02, 0xba, 0x3d, 0xa2, 0xce, 0x93, 0x1a,
            0xfd, 0xd6, 0xb5, 0x54, 0x90, 0xc4, 0x2a, 0x8f,
        },
        .pair_alg = BLE_SM_PAIR_ALG_PASSKEY,
        .authenticated = 1,
        .passkey_info = {
            .passkey = {
                .action = BLE_SM_IOACT_DISP,
                .passkey = 222333,
            },
        },
    };
    ble_sm_test_util_peer_sc_good(&params);

    ble_hs_test_util_assert_mbufs_freed(NULL);
}

/**
 * Secure connections pairing
 * Master: peer
 * Pair algorithm: numeric comparison
 * Initiator IO capabilities: 1
 * Responder IO capabilities: 1
 * Bonding: true
 * Initiator address type: 0
 * Responder address type: 0
 * Initiator key distribution: 5
 * Responder key distribution: 7
 */
TEST_CASE_SELF(ble_sm_sc_peer_nc_iio1_rio1_b1_iat0_rat0_ik5_rk7)
{
    struct ble_sm_test_params params;

    params = (struct ble_sm_test_params) {
        .init_id_addr = {
            0xca, 0x61, 0xa0, 0x67, 0x94, 0xe0,
        },
        .resp_id_addr = {
            0x33, 0x22, 0x11, 0x00, 0x45, 0x0a,
        },
        .pair_req = {
            .io_cap = 0x01,
            .oob_data_flag = 0x00,
            .authreq = 0x0d,
            .max_enc_key_size = 0x10,
            .init_key_dist = 0x0d,
            .resp_key_dist = 0x0f,
        },
        .pair_rsp = {
            .io_cap = 0x01,
            .oob_data_flag = 0x00,
            .authreq = 0x0d,
            .max_enc_key_size = 0x10,
            .init_key_dist = 0x05,
            .resp_key_dist = 0x07,
        },
        .our_priv_key = {
            0xd6, 0x2f, 0x4f, 0x6b, 0xeb, 0xfc, 0xbd, 0xee,
            0x9b, 0x94, 0xd7, 0x15, 0x98, 0xc6, 0x0c, 0x83,
            0x9b, 0xc7, 0xa2, 0x45, 0xfd, 0x00, 0xe8, 0xa4,
            0x52, 0xe9, 0x70, 0x2f, 0xd7, 0x62, 0xf1, 0xa4,
        },
        .public_key_req = {
            .x = {
                0x41, 0x0d, 0x95, 0x8a, 0x68, 0xb8, 0xcf, 0x07,
                0x58, 0x25, 0x5f, 0x97, 0xd2, 0x99, 0x71, 0x44,
                0x06, 0xfc, 0x9c, 0x4d, 0xd1, 0x74, 0x80, 0xed,
                0x49, 0xd1, 0x36, 0x6b, 0x55, 0x8b, 0x54, 0x3b,
            },
            .y = {
                0x0f, 0x1a, 0x61, 0x45, 0xe5, 0x4b, 0x11, 0x13,
                0xb3, 0x15, 0x87, 0x09, 0xec, 0x16, 0xf8, 0x41,
                0x2e, 0xe2, 0x15, 0x93, 0x14, 0x56, 0x9f, 0xcd,
                0x60, 0x7d, 0x92, 0xec, 0xd3, 0xb5, 0x85, 0xc5,
            },
        },
        .public_key_rsp = {
            .x = {
                0xbc, 0x6a, 0xcf, 0xc6, 0x8a, 0x3a, 0xdc, 0x89,
                0xdd, 0xa9, 0xaf, 0x29, 0xc7, 0xaf, 0xe2, 0x8b,
                0x25, 0xee, 0xce, 0xa6, 0x10, 0x1d, 0x33, 0x2f,
                0xd5, 0xfc, 0x30, 0xb8, 0xb1, 0x7b, 0xb1, 0x6e,
            },
            .y = {
                0x1a, 0xc6, 0x42, 0x36, 0x98, 0x40, 0x4f, 0x90,
                0x82, 0xa0, 0x10, 0x3a, 0xa5, 0x0f, 0xcf, 0x57,
                0xd2, 0x2e, 0x80, 0x9d, 0x61, 0xc7, 0x21, 0xac,
                0x47, 0x5b, 0x93, 0x75, 0x02, 0x30, 0x40, 0x14,
            },
        },
        .confirm_rsp[0] = {
            .value = {
                0x73, 0xc8, 0x56, 0x5e, 0x33, 0x37, 0x26, 0xb6,
                0x00, 0x65, 0x9c, 0xa1, 0xee, 0xbf, 0x61, 0xf6,
            },
        },
        .random_req[0] = {
            .value = {
                0x7c, 0x23, 0x03, 0x70, 0x54, 0xa2, 0x70, 0xe4,
                0x2d, 0xe9, 0x88, 0x6f, 0x40, 0xd6, 0x2f, 0xb2,
            },
        },
        .random_rsp[0] = {
            .value = {
                0x2d, 0x9f, 0xe8, 0x1d, 0xf2, 0x4e, 0x2e, 0x58,
                0x16, 0x8c, 0x83, 0x89, 0x92, 0x70, 0xa2, 0xba,
            },
        },
        .dhkey_check_req = {
            .value = {
                0xc0, 0x8a, 0x1c, 0xff, 0x7f, 0xd6, 0xbc, 0xee,
                0x19, 0xa5, 0xc6, 0x3a, 0xbd, 0x48, 0x4b, 0xc3,
            },
        },
        .dhkey_check_rsp = {
            .value = {
                0x38, 0x36, 0x83, 0xd5, 0x1a, 0xfb, 0xe6, 0x3c,
                0x80, 0x0c, 0x81, 0x81, 0x78, 0x12, 0x41, 0x38,
            },
        },
        .id_info_req = {
            .irk = {
                0xef, 0x8d, 0xe2, 0x16, 0x4f, 0xec, 0x43, 0x0d,
                0xbf, 0x5b, 0xdd, 0x34, 0xc0, 0x53, 0x1e, 0xb8,
            },
        },
        .id_addr_info_req = {
            .addr_type = 0,
            .bd_addr = {
                0x33, 0x22, 0x11, 0x00, 0x45, 0x0a,
            },
        },
        .sign_info_req = {
            .sig_key = {
                0x52, 0xf4, 0xcc, 0x2f, 0xc6, 0xc1, 0xdb, 0x07,
                0xa5, 0x38, 0xc1, 0x09, 0x82, 0x2e, 0xa3, 0x53,
            },
        },
        .sign_info_rsp = {
            .sig_key = {
                0xc1, 0xa3, 0x62, 0x6a, 0x9e, 0xaa, 0x37, 0xd9,
                0x65, 0x9f, 0x7f, 0x5d, 0x62, 0x0c, 0x1c, 0x6c,
            },
        },
        .ltk = {
            0xd8, 0x7f, 0x0a, 0x94, 0x41, 0xa5, 0xfd, 0x84,
            0x15, 0x01, 0xb7, 0x2a, 0x7a, 0xe4, 0xfd, 0xfb,
        },
        .pair_alg = BLE_SM_PAIR_ALG_NUMCMP,
        .authenticated = 1,
        .passkey_info = {
            .passkey = {
                .action = BLE_SM_IOACT_NUMCMP,
                .numcmp_accept = 1,
            },
            .exp_numcmp = 516214,
        },
    };
    ble_sm_test_util_peer_sc_good(&params);

    ble_hs_test_util_assert_mbufs_freed(NULL);
}

/**
 * Secure connections pairing
 * Master: us
 * Pair algorithm: just works
 * Initiator IO capabilities: 3
 * Responder IO capabilities: 4
 * Bonding: true
 * Initiator address type: 0
 * Responder address type: 0
 * Initiator key distribution: 7
 * Responder key distribution: 5
 */
TEST_CASE_SELF(ble_sm_sc_us_jw_iio3_rio4_b1_iat0_rat0_ik7_rk5)
{
    struct ble_sm_test_params params;

    params = (struct ble_sm_test_params) {
        .init_id_addr = {
            0x01, 0x01, 0x01, 0x07, 0x08, 0x01,
        },
        .resp_id_addr = {
            0xca, 0x61, 0xa0, 0x67, 0x94, 0xe0,
        },
        .pair_req = {
            .io_cap = 0x03,
            .oob_data_flag = 0x00,
            .authreq = 0x09,
            .max_enc_key_size = 0x10,
            .init_key_dist = 0x07,
            .resp_key_dist = 0x07,
        },
        .pair_rsp = {
            .io_cap = 0x04,
            .oob_data_flag = 0x00,
            .authreq = 0x09,
            .max_enc_key_size = 0x10,
            .init_key_dist = 0x07,
            .resp_key_dist = 0x05,
        },
        .our_priv_key = {
            0xaf, 0xce, 0x12, 0x45, 0xa8, 0xe0, 0xa9, 0x45,
            0x8a, 0x56, 0xc5, 0xbf, 0x3b, 0xf9, 0x04, 0x69,
            0xf2, 0xf9, 0xe4, 0xd4, 0x7e, 0xb7, 0xc9, 0x65,
            0xb1, 0x68, 0x3e, 0xab, 0xcd, 0x8e, 0x6f, 0x1f,
        },
        .public_key_req = {
            .x = {
                0x45, 0xca, 0xda, 0xe3, 0x65, 0x7c, 0xf5, 0x37,
                0x36, 0x66, 0x8b, 0x3b, 0x54, 0xb9, 0x2b, 0xb2,
                0x09, 0xd5, 0x6e, 0xe0, 0x04, 0x1d, 0xd6, 0x49,
                0xff, 0x55, 0x41, 0x35, 0xa0, 0x2f, 0x12, 0xee,
            },
            .y = {
                0x65, 0x41, 0xd3, 0x7b, 0x59, 0xf2, 0xaf, 0x94,
                0x78, 0xd8, 0x63, 0xc4, 0x9b, 0x9a, 0x9a, 0x92,
                0x33, 0x0f, 0x14, 0x67, 0x98, 0x51, 0x9d, 0xff,
                0xef, 0x59, 0xb7, 0x17, 0xc2, 0x16, 0x72, 0x18,
            },
        },
        .public_key_rsp = {
            .x = {
                0x9e, 0x44, 0x09, 0x57, 0xb7, 0x01, 0x78, 0x5b,
                0x4e, 0x50, 0x0d, 0x99, 0x0d, 0x52, 0x88, 0x24,
                0x19, 0xf5, 0x40, 0x53, 0x06, 0x1e, 0x68, 0xd0,
                0xfd, 0xd2, 0x84, 0x8b, 0xae, 0x9d, 0xf7, 0xd9,
            },
            .y = {
                0xc2, 0xe7, 0xe0, 0x01, 0xb3, 0x2a, 0x1b, 0x01,
                0x19, 0xd1, 0x14, 0xb5, 0xc8, 0x98, 0x02, 0x2a,
                0xbe, 0x6b, 0x33, 0x1a, 0x99, 0x18, 0x77, 0x23,
                0xd4, 0x8b, 0x8c, 0x09, 0xf5, 0x77, 0x20, 0xa0,
            },
        },
        .confirm_rsp[0] = {
            .value = {
                0xbd, 0x85, 0xbe, 0x80, 0xd9, 0x77, 0x16, 0xa3,
                0x65, 0x1a, 0xdf, 0xff, 0x5a, 0x6f, 0x8b, 0x37,
            },
        },
        .random_req[0] = {
            .value = {
                0xb5, 0x59, 0x7c, 0x8e, 0x7b, 0x01, 0xac, 0x09,
                0x8f, 0xe8, 0x97, 0x98, 0x8d, 0x3f, 0xb7, 0x63,
            },
        },
        .random_rsp[0] = {
            .value = {
                0x86, 0x1f, 0x76, 0x11, 0x2e, 0x83, 0xed, 0x99,
                0x9b, 0xc0, 0x9a, 0xab, 0x7f, 0x94, 0x20, 0xcb,
            },
        },
        .dhkey_check_req = {
            .value = {
                0xe0, 0x9f, 0x87, 0x87, 0x9f, 0x82, 0xc5, 0x06,
                0x5f, 0x11, 0xfa, 0xa0, 0xe3, 0xbf, 0x72, 0xf2,
            },
        },
        .dhkey_check_rsp = {
            .value = {
                0x26, 0xc2, 0xf1, 0xb9, 0xf1, 0xc2, 0xbd, 0xcb,
                0xdb, 0x94, 0x96, 0x8e, 0x08, 0xcc, 0x53, 0xd4,
            },
        },
        .sign_info_req = {
            .sig_key = {
                0x74, 0x14, 0xcd, 0x5a, 0x49, 0x2e, 0xb6, 0x0d,
                0xc6, 0x82, 0xb0, 0x0f, 0x9c, 0xe6, 0xe5, 0x41,
            },
        },
        .id_info_rsp = {
            .irk = {
                0xef, 0x8d, 0xe2, 0x16, 0x4f, 0xec, 0x43, 0x0d,
                0xbf, 0x5b, 0xdd, 0x34, 0xc0, 0x53, 0x1e, 0xb8,
            },
        },
        .id_addr_info_rsp = {
            .addr_type = 0,
            .bd_addr = {
                0x01, 0x01, 0x01, 0x07, 0x08, 0x01,
            },
        },
        .sign_info_rsp = {
            .sig_key = {
                0xfb, 0x93, 0xa2, 0xb7, 0x4d, 0x0e, 0xcc, 0x92,
                0xe4, 0xbf, 0x5b, 0x3c, 0x6d, 0x87, 0x5b, 0x2d,
            },
        },
        .ltk = {
            0x2e, 0x6c, 0x8b, 0xdb, 0x9e, 0x19, 0x3e, 0x3d,
            0x4d, 0x6d, 0x29, 0xbc, 0x89, 0xca, 0x57, 0xed,
        },
        .pair_alg = BLE_SM_PAIR_ALG_JW,
        .authenticated = 0,
        .passkey_info = {
            .passkey = {
                .action = BLE_SM_IOACT_NONE,
            },
        },
    };
    ble_sm_test_util_us_sc_good(&params);

    ble_hs_test_util_assert_mbufs_freed(NULL);
}

/**
 * Secure connections pairing
 * Master: us
 * Pair algorithm: passkey entry
 * Initiator IO capabilities: 2
 * Responder IO capabilities: 4
 * Bonding: true
 * Initiator address type: 0
 * Responder address type: 0
 * Initiator key distribution: 7
 * Responder key distribution: 5
 */
TEST_CASE_SELF(ble_sm_sc_us_pk_iio2_rio4_b1_iat0_rat0_ik7_rk5)
{
    struct ble_sm_test_params params;

    params = (struct ble_sm_test_params) {
        .init_id_addr = {
            0x01, 0x01, 0x01, 0x07, 0x08, 0x01,
        },
        .resp_id_addr = {
            0xca, 0x61, 0xa0, 0x67, 0x94, 0xe0,
        },
        .pair_req = {
            .io_cap = 0x02,
            .oob_data_flag = 0x00,
            .authreq = 0x0d,
            .max_enc_key_size = 0x10,
            .init_key_dist = 0x07,
            .resp_key_dist = 0x07,
        },
        .pair_rsp = {
            .io_cap = 0x04,
            .oob_data_flag = 0x00,
            .authreq = 0x0d,
            .max_enc_key_size = 0x10,
            .init_key_dist = 0x07,
            .resp_key_dist = 0x05,
        },
        .our_priv_key = {
            0xb1, 0x6b, 0x4f, 0x81, 0xbc, 0xe3, 0x60, 0x9e,
            0x00, 0x20, 0xf1, 0x73, 0x3e, 0xfb, 0xcc, 0x6e,
            0x8c, 0xb6, 0xd2, 0x51, 0xd9, 0x36, 0x8a, 0x6d,
            0xca, 0x8c, 0xd7, 0xbe, 0x96, 0x03, 0xdf, 0xd6,
        },
        .public_key_req = {
            .x = {
                0xe5, 0x0f, 0x02, 0x0a, 0x37, 0x90, 0x94, 0x5a,
                0x06, 0x21, 0xf7, 0xbc, 0xd5, 0xbe, 0xb9, 0x24,
                0x8a, 0x35, 0xfd, 0xf8, 0x5e, 0xe2, 0x70, 0xd5,
                0x5a, 0xe8, 0xe7, 0xdd, 0x13, 0x90, 0xeb, 0xd4,
            },
            .y = {
                0x41, 0xc8, 0x51, 0x1a, 0x25, 0x44, 0x01, 0x53,
                0x42, 0x74, 0x07, 0x9c, 0x18, 0xe6, 0x3b, 0x8a,
                0xce, 0x7a, 0x37, 0x1f, 0x18, 0x5c, 0x02, 0x7c,
                0x67, 0x16, 0xf5, 0x30, 0x2b, 0x31, 0xa9, 0xc7,
            },
        },
        .public_key_rsp = {
            .x = {
                0x1b, 0xd2, 0x03, 0x79, 0xb4, 0x9b, 0x0a, 0xd7,
                0x1b, 0x28, 0x73, 0x2a, 0xd7, 0xe6, 0xa0, 0xd4,
                0x2d, 0x95, 0x8d, 0x29, 0xaf, 0x6a, 0xab, 0xee,
                0xa0, 0x0d, 0x13, 0x4d, 0xe7, 0x16, 0x76, 0x91,
            },
            .y = {
                0x2a, 0x26, 0x2c, 0x50, 0x55, 0xd1, 0x2b, 0x83,
                0xf6, 0x5f, 0xdb, 0x99, 0x5f, 0x85, 0xf6, 0x78,
                0x1c, 0x14, 0xed, 0xd3, 0x70, 0x5e, 0xe5, 0x2c,
                0x05, 0x1e, 0x5c, 0xec, 0xf8, 0x65, 0x43, 0x49,
            },
        },
        .confirm_req[0] = {
            .value = {
                0x55, 0x2c, 0xaa, 0x41, 0x59, 0x42, 0x4d, 0xfe,
                0x47, 0x74, 0xcd, 0x2b, 0x11, 0xab, 0x21, 0xe6,
            },
        },
        .confirm_rsp[0] = {
            .value = {
                0x6a, 0x3c, 0x45, 0xf5, 0xb2, 0xe2, 0x04, 0x30,
                0xde, 0xd6, 0x3c, 0x6d, 0x85, 0x00, 0x00, 0x2c,
            },
        },
        .random_req[0] = {
            .value = {
                0x78, 0x06, 0x04, 0x60, 0x76, 0xe9, 0xc4, 0x5a,
                0xfb, 0x34, 0x44, 0xae, 0x45, 0xa0, 0x84, 0xde,
            },
        },
        .random_rsp[0] = {
            .value = {
                0x91, 0xc8, 0xfd, 0x1b, 0xb2, 0x85, 0x08, 0x76,
                0xd3, 0xf1, 0xc4, 0xa0, 0xfa, 0x92, 0x8c, 0x94,
            },
        },
        .confirm_req[1] = {
            .value = {
                0xb1, 0x2f, 0x68, 0x35, 0xa1, 0xa5, 0x84, 0xb1,
                0x4f, 0x1a, 0xb1, 0xb5, 0xf0, 0xb2, 0xbe, 0x61,
            },
        },
        .confirm_rsp[1] = {
            .value = {
                0x07, 0xd8, 0x43, 0x74, 0xe8, 0x42, 0xf3, 0xf1,
                0x87, 0x3d, 0x9e, 0x92, 0xea, 0x33, 0xe8, 0x54,
            },
        },
        .random_req[1] = {
            .value = {
                0x4c, 0xb7, 0xcc, 0x6d, 0x90, 0x9f, 0x1e, 0x2d,
                0x9d, 0x1e, 0x52, 0xa7, 0xe0, 0x0c, 0x7b, 0xf7,
            },
        },
        .random_rsp[1] = {
            .value = {
                0x5c, 0x32, 0x82, 0xc8, 0x76, 0x17, 0x3b, 0x18,
                0x66, 0xda, 0xbf, 0xc3, 0x13, 0x49, 0x05, 0xfb,
            },
        },
        .confirm_req[2] = {
            .value = {
                0x27, 0x61, 0x4d, 0x04, 0x64, 0xa9, 0x58, 0xf1,
                0xe0, 0xf9, 0xe5, 0x78, 0x0b, 0x54, 0x89, 0x0a,
            },
        },
        .confirm_rsp[2] = {
            .value = {
                0xe4, 0x8f, 0xdb, 0xc8, 0x35, 0xed, 0x4e, 0x7d,
                0xbc, 0x92, 0x7f, 0x58, 0x02, 0xaa, 0xbf, 0x6b,
            },
        },
        .random_req[2] = {
            .value = {
                0xfe, 0x85, 0x08, 0xe0, 0x35, 0x90, 0x13, 0xa9,
                0xd3, 0xcf, 0xb6, 0x6d, 0x36, 0xaf, 0xbd, 0x59,
            },
        },
        .random_rsp[2] = {
            .value = {
                0x47, 0x40, 0x8e, 0x97, 0xe3, 0xfe, 0x8f, 0x52,
                0x29, 0x5e, 0x6b, 0x44, 0xdf, 0x0d, 0x60, 0xf4,
            },
        },
        .confirm_req[3] = {
            .value = {
                0xac, 0xab, 0x13, 0x7c, 0x1a, 0x6e, 0x7a, 0xdb,
                0xf6, 0xe8, 0x72, 0x9f, 0xc5, 0xc3, 0x99, 0x1b,
            },
        },
        .confirm_rsp[3] = {
            .value = {
                0x79, 0xf2, 0xd1, 0x89, 0x5e, 0xa5, 0xa2, 0x90,
                0xee, 0x25, 0x36, 0x81, 0x5a, 0x87, 0x20, 0x82,
            },
        },
        .random_req[3] = {
            .value = {
                0xd4, 0x46, 0xa0, 0xc4, 0x3d, 0xae, 0x22, 0x06,
                0xaf, 0x5d, 0x93, 0x96, 0xb7, 0x06, 0xc3, 0x61,
            },
        },
        .random_rsp[3] = {
            .value = {
                0x5f, 0x81, 0x97, 0x8b, 0x52, 0x87, 0x1c, 0x67,
                0xe0, 0x04, 0xcc, 0x50, 0xd9, 0x2b, 0x16, 0xb5,
            },
        },
        .confirm_req[4] = {
            .value = {
                0x6c, 0x51, 0xc3, 0x61, 0x77, 0x7f, 0xf1, 0x05,
                0x9e, 0x0f, 0xba, 0xfd, 0x32, 0x02, 0x09, 0x45,
            },
        },
        .confirm_rsp[4] = {
            .value = {
                0x54, 0xe5, 0x24, 0x81, 0x62, 0x68, 0xe2, 0x45,
                0x86, 0x2c, 0x11, 0x28, 0x15, 0xa8, 0x8e, 0x5b,
            },
        },
        .random_req[4] = {
            .value = {
                0xbb, 0x29, 0x3a, 0xba, 0xe6, 0x4f, 0x06, 0xcf,
                0xa3, 0x13, 0x27, 0xf2, 0xcb, 0xe4, 0xd2, 0xe6,
            },
        },
        .random_rsp[4] = {
            .value = {
                0x50, 0xba, 0xd0, 0x0e, 0x26, 0xab, 0x04, 0xf8,
                0xa2, 0x03, 0x1e, 0x63, 0x9a, 0xf7, 0x15, 0xdc,
            },
        },
        .confirm_req[5] = {
            .value = {
                0x12, 0x3e, 0xfe, 0x5a, 0xb1, 0x09, 0x6f, 0x17,
                0xb7, 0x77, 0x7e, 0x65, 0x88, 0xd4, 0x95, 0x56,
            },
        },
        .confirm_rsp[5] = {
            .value = {
                0xc6, 0x9b, 0xac, 0xde, 0x7e, 0x03, 0x7a, 0xd3,
                0xf1, 0xff, 0x3c, 0x4f, 0x4a, 0x85, 0xba, 0x73,
            },
        },
        .random_req[5] = {
            .value = {
                0x17, 0xd5, 0x5e, 0x69, 0x30, 0x2c, 0x1f, 0x01,
                0x87, 0x9c, 0xd6, 0xd2, 0xe4, 0x48, 0x8c, 0x84,
            },
        },
        .random_rsp[5] = {
            .value = {
                0x9d, 0x54, 0x83, 0x4a, 0xcd, 0x93, 0x7c, 0x1e,
                0x5b, 0xaf, 0xd2, 0x66, 0x8c, 0x2d, 0xaa, 0xc3,
            },
        },
        .confirm_req[6] = {
            .value = {
                0xdc, 0x24, 0x69, 0xa8, 0xd3, 0xa9, 0x17, 0x11,
                0x08, 0x37, 0x1a, 0x1e, 0x92, 0x03, 0xee, 0x36,
            },
        },
        .confirm_rsp[6] = {
            .value = {
                0x98, 0xf8, 0x72, 0x71, 0x99, 0xa0, 0xbd, 0xcd,
                0xb1, 0x97, 0x4c, 0x8a, 0xb8, 0xa8, 0x1a, 0x52,
            },
        },
        .random_req[6] = {
            .value = {
                0xbf, 0xb1, 0x8e, 0xa5, 0x14, 0xe3, 0xeb, 0x9e,
                0x29, 0x27, 0xe0, 0x19, 0xb1, 0xb2, 0x5c, 0xfe,
            },
        },
        .random_rsp[6] = {
            .value = {
                0xae, 0x8a, 0x92, 0x78, 0x53, 0x7b, 0xdb, 0x8c,
                0xec, 0x3a, 0x99, 0x2b, 0x94, 0xf1, 0x17, 0xfe,
            },
        },
        .confirm_req[7] = {
            .value = {
                0xcf, 0xaf, 0x70, 0x73, 0x53, 0x65, 0x89, 0x57,
                0x36, 0x98, 0xd2, 0x28, 0x86, 0x79, 0xfe, 0x85,
            },
        },
        .confirm_rsp[7] = {
            .value = {
                0x0d, 0x2d, 0x77, 0x8a, 0x21, 0x11, 0xd9, 0x61,
                0x9f, 0x80, 0x32, 0x8a, 0x32, 0x09, 0x42, 0x42,
            },
        },
        .random_req[7] = {
            .value = {
                0x8b, 0xd2, 0x53, 0xcd, 0x96, 0xd1, 0x14, 0xb5,
                0xea, 0x17, 0xb1, 0xa3, 0xa8, 0xfc, 0x3c, 0x2b,
            },
        },
        .random_rsp[7] = {
            .value = {
                0xc2, 0x4f, 0x84, 0x60, 0x54, 0x79, 0x16, 0xed,
                0x1a, 0x6e, 0x78, 0xa0, 0x99, 0x58, 0xf2, 0x94,
            },
        },
        .confirm_req[8] = {
            .value = {
                0x9a, 0x4c, 0xbc, 0x9c, 0x55, 0x15, 0xa2, 0x4f,
                0xa2, 0x5d, 0x3b, 0xa7, 0x43, 0xb3, 0x9c, 0x63,
            },
        },
        .confirm_rsp[8] = {
            .value = {
                0xa3, 0xb1, 0x88, 0xa5, 0x70, 0xca, 0xa3, 0xa9,
                0x67, 0x2a, 0xac, 0x99, 0x5e, 0x61, 0x68, 0xa0,
            },
        },
        .random_req[8] = {
            .value = {
                0xcf, 0xcf, 0x5b, 0x94, 0xe0, 0xb2, 0x9d, 0x5a,
                0x86, 0x71, 0x45, 0xce, 0xd9, 0xce, 0x13, 0xba,
            },
        },
        .random_rsp[8] = {
            .value = {
                0x10, 0x96, 0x8a, 0x50, 0xa4, 0xd0, 0xaa, 0x5f,
                0xd6, 0x32, 0xdb, 0x09, 0x7e, 0x22, 0x96, 0x42,
            },
        },
        .confirm_req[9] = {
            .value = {
                0xf0, 0x90, 0x61, 0x25, 0x04, 0x29, 0x4f, 0xb6,
                0x8b, 0xd5, 0x73, 0x49, 0xbd, 0xf7, 0x9b, 0xe7,
            },
        },
        .confirm_rsp[9] = {
            .value = {
                0x5b, 0xe6, 0xb4, 0x3f, 0x1b, 0x77, 0x12, 0x75,
                0x84, 0x94, 0xc6, 0x07, 0xfa, 0xa1, 0x41, 0x94,
            },
        },
        .random_req[9] = {
            .value = {
                0x3d, 0x1a, 0xa3, 0x95, 0xec, 0x72, 0x84, 0xf4,
                0xc5, 0xcd, 0xaa, 0x48, 0xe9, 0x0c, 0x0f, 0xe3,
            },
        },
        .random_rsp[9] = {
            .value = {
                0x8a, 0x5a, 0x53, 0xfc, 0x07, 0x52, 0x01, 0xb9,
                0xe9, 0x2d, 0xe7, 0x9d, 0x8c, 0x7c, 0xc7, 0xb3,
            },
        },
        .confirm_req[10] = {
            .value = {
                0xe7, 0x8e, 0xc5, 0x08, 0x7f, 0x7e, 0xb8, 0xdc,
                0x05, 0x88, 0x3a, 0x92, 0x5a, 0xf5, 0x9b, 0xa9,
            },
        },
        .confirm_rsp[10] = {
            .value = {
                0xf7, 0xa2, 0xb6, 0xec, 0xcd, 0xef, 0xcb, 0xb7,
                0x6f, 0xc3, 0xac, 0x17, 0xe2, 0xfd, 0xfa, 0x42,
            },
        },
        .random_req[10] = {
            .value = {
                0x0d, 0xd1, 0xa2, 0x1d, 0xff, 0x74, 0xc5, 0x99,
                0xe0, 0x67, 0x07, 0x99, 0x95, 0x75, 0x39, 0x76,
            },
        },
        .random_rsp[10] = {
            .value = {
                0x2f, 0x13, 0xd1, 0x59, 0xfe, 0x20, 0x60, 0xf0,
                0x02, 0x0c, 0xea, 0x79, 0xd7, 0x40, 0x86, 0x85,
            },
        },
        .confirm_req[11] = {
            .value = {
                0x8b, 0x57, 0x87, 0xdd, 0xb1, 0xcc, 0x2d, 0x65,
                0xc1, 0xba, 0xac, 0x88, 0x48, 0x23, 0xda, 0xe7,
            },
        },
        .confirm_rsp[11] = {
            .value = {
                0xb3, 0xc4, 0x2e, 0xea, 0x33, 0xaf, 0x12, 0x9c,
                0xb5, 0xab, 0xa1, 0x95, 0x30, 0xca, 0x46, 0x48,
            },
        },
        .random_req[11] = {
            .value = {
                0x35, 0x57, 0xcd, 0xd5, 0xd2, 0xf8, 0xd7, 0xf2,
                0x7b, 0xe3, 0xd7, 0xba, 0x31, 0xa5, 0xca, 0xfd,
            },
        },
        .random_rsp[11] = {
            .value = {
                0xe2, 0x3b, 0x20, 0xbe, 0xec, 0xa5, 0x34, 0x3b,
                0x76, 0x23, 0x53, 0x28, 0x36, 0xc4, 0x60, 0x13,
            },
        },
        .confirm_req[12] = {
            .value = {
                0xc9, 0xfe, 0x03, 0x49, 0xe4, 0xff, 0x7e, 0xf7,
                0x00, 0xd1, 0x2b, 0x13, 0xb1, 0x15, 0x6e, 0x92,
            },
        },
        .confirm_rsp[12] = {
            .value = {
                0xbc, 0xa2, 0xf2, 0x03, 0x5c, 0xfd, 0x20, 0x7b,
                0xd0, 0x1f, 0xd6, 0x50, 0xec, 0xc6, 0x7b, 0x31,
            },
        },
        .random_req[12] = {
            .value = {
                0x04, 0x50, 0xea, 0xb8, 0xca, 0x36, 0x1a, 0x61,
                0x92, 0xed, 0xa0, 0x67, 0x78, 0x15, 0x10, 0xb5,
            },
        },
        .random_rsp[12] = {
            .value = {
                0x0c, 0x8e, 0x9d, 0x7b, 0x9d, 0x7e, 0xda, 0x23,
                0xbb, 0x61, 0xd9, 0xff, 0x46, 0x77, 0x33, 0x1b,
            },
        },
        .confirm_req[13] = {
            .value = {
                0x9a, 0xff, 0xd6, 0xe5, 0x1a, 0xc3, 0xd3, 0x37,
                0x34, 0xeb, 0x3e, 0x3a, 0x8e, 0x0b, 0x86, 0xb4,
            },
        },
        .confirm_rsp[13] = {
            .value = {
                0xf6, 0x32, 0x19, 0xb4, 0x08, 0x6b, 0x8a, 0x0f,
                0xc9, 0x9c, 0x1b, 0x68, 0xb8, 0xa0, 0xd0, 0xc9,
            },
        },
        .random_req[13] = {
            .value = {
                0x86, 0xeb, 0x5c, 0xf9, 0x33, 0x54, 0x7d, 0xe4,
                0xa4, 0xe2, 0xe1, 0xf6, 0x6b, 0xea, 0x34, 0xed,
            },
        },
        .random_rsp[13] = {
            .value = {
                0xad, 0x53, 0xa0, 0x6e, 0xde, 0x1d, 0xda, 0x99,
                0x31, 0x45, 0xe5, 0x3a, 0x73, 0xa1, 0x5e, 0xe1,
            },
        },
        .confirm_req[14] = {
            .value = {
                0x93, 0xd4, 0xe0, 0xaa, 0x0c, 0x91, 0xba, 0xde,
                0xc9, 0x5c, 0x68, 0xb0, 0xce, 0xb6, 0x84, 0xcd,
            },
        },
        .confirm_rsp[14] = {
            .value = {
                0x85, 0xc7, 0x05, 0x02, 0x21, 0x9d, 0x4c, 0x4c,
                0x16, 0xf7, 0x8f, 0x7b, 0xaa, 0xb4, 0x8f, 0x37,
            },
        },
        .random_req[14] = {
            .value = {
                0x84, 0xfd, 0xf1, 0x39, 0x1a, 0x9a, 0xa5, 0xb8,
                0x49, 0xc0, 0x66, 0xdc, 0x33, 0x71, 0x32, 0x87,
            },
        },
        .random_rsp[14] = {
            .value = {
                0x5d, 0xaf, 0x38, 0xcd, 0xb5, 0x83, 0xaa, 0xa0,
                0xab, 0x30, 0x82, 0xed, 0x6f, 0xd2, 0x75, 0xe7,
            },
        },
        .confirm_req[15] = {
            .value = {
                0x88, 0x12, 0xe8, 0x89, 0xd4, 0x52, 0x6d, 0xac,
                0x61, 0x2a, 0x85, 0x85, 0x1e, 0x9c, 0x82, 0x21,
            },
        },
        .confirm_rsp[15] = {
            .value = {
                0xc1, 0xe9, 0xcd, 0x21, 0x29, 0x6a, 0x78, 0xe4,
                0x7b, 0x7d, 0x73, 0x25, 0x9e, 0x9b, 0x95, 0x8b,
            },
        },
        .random_req[15] = {
            .value = {
                0x95, 0x87, 0x9d, 0x5a, 0x10, 0x14, 0xa0, 0xdf,
                0x5e, 0x02, 0x22, 0x39, 0x23, 0xc9, 0xbc, 0xba,
            },
        },
        .random_rsp[15] = {
            .value = {
                0x1b, 0x91, 0xe2, 0xdf, 0xca, 0xfe, 0x2b, 0x61,
                0x33, 0x8c, 0x83, 0xbf, 0xcf, 0xc3, 0x72, 0xcc,
            },
        },
        .confirm_req[16] = {
            .value = {
                0xce, 0xc9, 0x68, 0xf7, 0xea, 0x41, 0x18, 0x5c,
                0x16, 0x6a, 0x98, 0x13, 0x0c, 0x10, 0xc2, 0xa3,
            },
        },
        .confirm_rsp[16] = {
            .value = {
                0x97, 0x73, 0xc9, 0x72, 0x68, 0x99, 0x63, 0xed,
                0x81, 0x3b, 0x5c, 0xee, 0x37, 0xfc, 0xca, 0xae,
            },
        },
        .random_req[16] = {
            .value = {
                0x5b, 0x85, 0xb0, 0x1b, 0xc3, 0xde, 0x18, 0xba,
                0xc1, 0xc7, 0x89, 0x99, 0xfe, 0xcd, 0xdb, 0x6a,
            },
        },
        .random_rsp[16] = {
            .value = {
                0x5e, 0x1a, 0xcb, 0xbc, 0xda, 0x41, 0x06, 0x5a,
                0x14, 0x34, 0x3a, 0xb1, 0xa1, 0x6f, 0xb2, 0xd8,
            },
        },
        .confirm_req[17] = {
            .value = {
                0x1d, 0x59, 0x8a, 0xb0, 0x19, 0xe5, 0xff, 0x45,
                0xb6, 0xc3, 0x33, 0x64, 0xd1, 0x6e, 0xee, 0xdd,
            },
        },
        .confirm_rsp[17] = {
            .value = {
                0x4c, 0x9b, 0xe8, 0x68, 0x52, 0x34, 0xef, 0xe1,
                0x84, 0xbd, 0x37, 0x85, 0x53, 0x0d, 0xd5, 0xc1,
            },
        },
        .random_req[17] = {
            .value = {
                0xa6, 0xf7, 0x97, 0x18, 0x9a, 0x3e, 0x9d, 0xcf,
                0x91, 0xa3, 0xa3, 0x8e, 0xda, 0x8f, 0x8f, 0x90,
            },
        },
        .random_rsp[17] = {
            .value = {
                0x94, 0x10, 0x19, 0x17, 0x8d, 0x0a, 0x72, 0xfd,
                0x24, 0x9d, 0xfd, 0x37, 0x4e, 0xdf, 0x4c, 0x30,
            },
        },
        .confirm_req[18] = {
            .value = {
                0xfc, 0x64, 0x8a, 0x8b, 0x37, 0x17, 0x90, 0x6d,
                0x25, 0x0e, 0xc6, 0x18, 0xc9, 0xc9, 0xc2, 0x2a,
            },
        },
        .confirm_rsp[18] = {
            .value = {
                0x50, 0x98, 0x86, 0xf5, 0xc0, 0xda, 0x45, 0x2d,
                0xea, 0xc8, 0x9d, 0x28, 0x04, 0xd8, 0x73, 0x6f,
            },
        },
        .random_req[18] = {
            .value = {
                0x13, 0x10, 0x38, 0xe8, 0x17, 0x6d, 0x72, 0xd5,
                0x94, 0xaf, 0xed, 0x4f, 0x23, 0xa0, 0x41, 0xfc,
            },
        },
        .random_rsp[18] = {
            .value = {
                0xdf, 0xed, 0xf7, 0x08, 0xce, 0x64, 0xbc, 0x11,
                0x41, 0x7a, 0xd9, 0xf7, 0x4a, 0xd9, 0x4a, 0x15,
            },
        },
        .confirm_req[19] = {
            .value = {
                0xae, 0x24, 0x8f, 0xdf, 0xb0, 0x57, 0xc4, 0x9c,
                0xe6, 0xae, 0x9b, 0xc2, 0x4d, 0x3d, 0x1c, 0xcb,
            },
        },
        .confirm_rsp[19] = {
            .value = {
                0xcc, 0x5c, 0xa3, 0xbe, 0xd7, 0x83, 0xee, 0x60,
                0x80, 0xff, 0x5f, 0x1a, 0x07, 0xbf, 0x4c, 0x33,
            },
        },
        .random_req[19] = {
            .value = {
                0x93, 0xc3, 0x62, 0x06, 0xcb, 0xe5, 0xb0, 0x01,
                0x02, 0x18, 0xa2, 0x50, 0x4c, 0x73, 0xa2, 0x27,
            },
        },
        .random_rsp[19] = {
            .value = {
                0x11, 0x2a, 0xd3, 0x06, 0x28, 0x9c, 0xdf, 0x73,
                0xa5, 0xa4, 0xe5, 0x1e, 0x07, 0xcf, 0xee, 0x71,
            },
        },
        .dhkey_check_req = {
            .value = {
                0x73, 0xa0, 0x40, 0x58, 0x78, 0x20, 0x5f, 0x2c,
                0xf4, 0x19, 0x23, 0xa8, 0x74, 0xbd, 0xc2, 0x3e,
            },
        },
        .dhkey_check_rsp = {
            .value = {
                0x5a, 0x30, 0xbc, 0xce, 0xec, 0xdf, 0xf0, 0x32,
                0x3c, 0x18, 0xa3, 0xd3, 0x3f, 0x20, 0x87, 0x10,
            },
        },
        .sign_info_req = {
            .sig_key = {
                0x2e, 0x81, 0x09, 0xde, 0x32, 0xc5, 0x28, 0x34,
                0xe1, 0x45, 0x4a, 0x35, 0x49, 0xef, 0xa2, 0xed,
            },
        },
        .id_info_rsp = {
            .irk = {
                0xef, 0x8d, 0xe2, 0x16, 0x4f, 0xec, 0x43, 0x0d,
                0xbf, 0x5b, 0xdd, 0x34, 0xc0, 0x53, 0x1e, 0xb8,
            },
        },
        .id_addr_info_rsp = {
            .addr_type = 0,
            .bd_addr = {
                0x01, 0x01, 0x01, 0x07, 0x08, 0x01,
            },
        },
        .sign_info_rsp = {
            .sig_key = {
                0x90, 0x3d, 0x26, 0x65, 0xc1, 0xd1, 0x5a, 0x9d,
                0xda, 0xab, 0x0d, 0x00, 0x05, 0x0e, 0x6c, 0x5d,
            },
        },
        .ltk = {
            0xf1, 0x41, 0x1a, 0x5b, 0x60, 0xc1, 0x43, 0xc6,
            0x80, 0x34, 0x5e, 0x7f, 0xd8, 0x0c, 0x75, 0xdc,
        },
        .pair_alg = BLE_SM_PAIR_ALG_PASSKEY,
        .authenticated = 1,
        .passkey_info = {
            .passkey = {
                .action = BLE_SM_IOACT_INPUT,
                .passkey = 516645,
            },
        },
    };
    ble_sm_test_util_us_sc_good(&params);

    ble_hs_test_util_assert_mbufs_freed(NULL);
}

/**
 * Secure connections pairing
 * Master: us
 * Pair algorithm: passkey entry
 * Initiator IO capabilities: 2
 * Responder IO capabilities: 4
 * Bonding: true
 * Initiator address type: 0
 * Responder address type: 0
 * Initiator key distribution: 7
 * Responder key distribution: 5
 * Peer responds with same public key
 */
TEST_CASE_SELF(ble_sm_sc_us_pk_iio2_rio4_b1_iat0_rat0_ik7_rk5_peer_same_pk) {
    struct ble_sm_test_params params;

    params = (struct ble_sm_test_params) {
        .init_id_addr = {
            0x01, 0x01, 0x01, 0x07, 0x08, 0x01,
        },
        .resp_id_addr = {
            0xca, 0x61, 0xa0, 0x67, 0x94, 0xe0,
        },
        .pair_req = {
            .io_cap = 0x02,
            .oob_data_flag = 0x00,
            .authreq = 0x0d,
            .max_enc_key_size = 0x10,
            .init_key_dist = 0x07,
            .resp_key_dist = 0x07,
        },
        .pair_rsp = {
            .io_cap = 0x04,
            .oob_data_flag = 0x00,
            .authreq = 0x0d,
            .max_enc_key_size = 0x10,
            .init_key_dist = 0x07,
            .resp_key_dist = 0x05,
        },
        .our_priv_key = {
            0xb1, 0x6b, 0x4f, 0x81, 0xbc, 0xe3, 0x60, 0x9e,
            0x00, 0x20, 0xf1, 0x73, 0x3e, 0xfb, 0xcc, 0x6e,
            0x8c, 0xb6, 0xd2, 0x51, 0xd9, 0x36, 0x8a, 0x6d,
            0xca, 0x8c, 0xd7, 0xbe, 0x96, 0x03, 0xdf, 0xd6,
        },
        .public_key_req = {
            .x = {
                0xe5, 0x0f, 0x02, 0x0a, 0x37, 0x90, 0x94, 0x5a,
                0x06, 0x21, 0xf7, 0xbc, 0xd5, 0xbe, 0xb9, 0x24,
                0x8a, 0x35, 0xfd, 0xf8, 0x5e, 0xe2, 0x70, 0xd5,
                0x5a, 0xe8, 0xe7, 0xdd, 0x13, 0x90, 0xeb, 0xd4,
            },
            .y = {
                0x41, 0xc8, 0x51, 0x1a, 0x25, 0x44, 0x01, 0x53,
                0x42, 0x74, 0x07, 0x9c, 0x18, 0xe6, 0x3b, 0x8a,
                0xce, 0x7a, 0x37, 0x1f, 0x18, 0x5c, 0x02, 0x7c,
                0x67, 0x16, 0xf5, 0x30, 0x2b, 0x31, 0xa9, 0xc7,
            },
        },
        .public_key_rsp = {
            .x = {
                0xe5, 0x0f, 0x02, 0x0a, 0x37, 0x90, 0x94, 0x5a,
                0x06, 0x21, 0xf7, 0xbc, 0xd5, 0xbe, 0xb9, 0x24,
                0x8a, 0x35, 0xfd, 0xf8, 0x5e, 0xe2, 0x70, 0xd5,
                0x5a, 0xe8, 0xe7, 0xdd, 0x13, 0x90, 0xeb, 0xd4,
            },
            .y = {
                0x41, 0xc8, 0x51, 0x1a, 0x25, 0x44, 0x01, 0x53,
                0x42, 0x74, 0x07, 0x9c, 0x18, 0xe6, 0x3b, 0x8a,
                0xce, 0x7a, 0x37, 0x1f, 0x18, 0x5c, 0x02, 0x7c,
                0x67, 0x16, 0xf5, 0x30, 0x2b, 0x31, 0xa9, 0xc7,
            },
        },
        .confirm_req[0] = {
            .value = {
                0x55, 0x2c, 0xaa, 0x41, 0x59, 0x42, 0x4d, 0xfe,
                0x47, 0x74, 0xcd, 0x2b, 0x11, 0xab, 0x21, 0xe6,
            },
        },
        .confirm_rsp[0] = {
            .value = {
                0x6a, 0x3c, 0x45, 0xf5, 0xb2, 0xe2, 0x04, 0x30,
                0xde, 0xd6, 0x3c, 0x6d, 0x85, 0x00, 0x00, 0x2c,
            },
        },
        .random_req[0] = {
            .value = {
                0x78, 0x06, 0x04, 0x60, 0x76, 0xe9, 0xc4, 0x5a,
                0xfb, 0x34, 0x44, 0xae, 0x45, 0xa0, 0x84, 0xde,
            },
        },
        .random_rsp[0] = {
            .value = {
                0x91, 0xc8, 0xfd, 0x1b, 0xb2, 0x85, 0x08, 0x76,
                0xd3, 0xf1, 0xc4, 0xa0, 0xfa, 0x92, 0x8c, 0x94,
            },
        },
        .confirm_req[1] = {
            .value = {
                0xb1, 0x2f, 0x68, 0x35, 0xa1, 0xa5, 0x84, 0xb1,
                0x4f, 0x1a, 0xb1, 0xb5, 0xf0, 0xb2, 0xbe, 0x61,
            },
        },
        .confirm_rsp[1] = {
            .value = {
                0x07, 0xd8, 0x43, 0x74, 0xe8, 0x42, 0xf3, 0xf1,
                0x87, 0x3d, 0x9e, 0x92, 0xea, 0x33, 0xe8, 0x54,
            },
        },
        .random_req[1] = {
            .value = {
                0x4c, 0xb7, 0xcc, 0x6d, 0x90, 0x9f, 0x1e, 0x2d,
                0x9d, 0x1e, 0x52, 0xa7, 0xe0, 0x0c, 0x7b, 0xf7,
            },
        },
        .random_rsp[1] = {
            .value = {
                0x5c, 0x32, 0x82, 0xc8, 0x76, 0x17, 0x3b, 0x18,
                0x66, 0xda, 0xbf, 0xc3, 0x13, 0x49, 0x05, 0xfb,
            },
        },
        .confirm_req[2] = {
            .value = {
                0x27, 0x61, 0x4d, 0x04, 0x64, 0xa9, 0x58, 0xf1,
                0xe0, 0xf9, 0xe5, 0x78, 0x0b, 0x54, 0x89, 0x0a,
            },
        },
        .confirm_rsp[2] = {
            .value = {
                0xe4, 0x8f, 0xdb, 0xc8, 0x35, 0xed, 0x4e, 0x7d,
                0xbc, 0x92, 0x7f, 0x58, 0x02, 0xaa, 0xbf, 0x6b,
            },
        },
        .random_req[2] = {
            .value = {
                0xfe, 0x85, 0x08, 0xe0, 0x35, 0x90, 0x13, 0xa9,
                0xd3, 0xcf, 0xb6, 0x6d, 0x36, 0xaf, 0xbd, 0x59,
            },
        },
        .random_rsp[2] = {
            .value = {
                0x47, 0x40, 0x8e, 0x97, 0xe3, 0xfe, 0x8f, 0x52,
                0x29, 0x5e, 0x6b, 0x44, 0xdf, 0x0d, 0x60, 0xf4,
            },
        },
        .confirm_req[3] = {
            .value = {
                0xac, 0xab, 0x13, 0x7c, 0x1a, 0x6e, 0x7a, 0xdb,
                0xf6, 0xe8, 0x72, 0x9f, 0xc5, 0xc3, 0x99, 0x1b,
            },
        },
        .confirm_rsp[3] = {
            .value = {
                0x79, 0xf2, 0xd1, 0x89, 0x5e, 0xa5, 0xa2, 0x90,
                0xee, 0x25, 0x36, 0x81, 0x5a, 0x87, 0x20, 0x82,
            },
        },
        .random_req[3] = {
            .value = {
                0xd4, 0x46, 0xa0, 0xc4, 0x3d, 0xae, 0x22, 0x06,
                0xaf, 0x5d, 0x93, 0x96, 0xb7, 0x06, 0xc3, 0x61,
            },
        },
        .random_rsp[3] = {
            .value = {
                0x5f, 0x81, 0x97, 0x8b, 0x52, 0x87, 0x1c, 0x67,
                0xe0, 0x04, 0xcc, 0x50, 0xd9, 0x2b, 0x16, 0xb5,
            },
        },
        .confirm_req[4] = {
            .value = {
                0x6c, 0x51, 0xc3, 0x61, 0x77, 0x7f, 0xf1, 0x05,
                0x9e, 0x0f, 0xba, 0xfd, 0x32, 0x02, 0x09, 0x45,
            },
        },
        .confirm_rsp[4] = {
            .value = {
                0x54, 0xe5, 0x24, 0x81, 0x62, 0x68, 0xe2, 0x45,
                0x86, 0x2c, 0x11, 0x28, 0x15, 0xa8, 0x8e, 0x5b,
            },
        },
        .random_req[4] = {
            .value = {
                0xbb, 0x29, 0x3a, 0xba, 0xe6, 0x4f, 0x06, 0xcf,
                0xa3, 0x13, 0x27, 0xf2, 0xcb, 0xe4, 0xd2, 0xe6,
            },
        },
        .random_rsp[4] = {
            .value = {
                0x50, 0xba, 0xd0, 0x0e, 0x26, 0xab, 0x04, 0xf8,
                0xa2, 0x03, 0x1e, 0x63, 0x9a, 0xf7, 0x15, 0xdc,
            },
        },
        .confirm_req[5] = {
            .value = {
                0x12, 0x3e, 0xfe, 0x5a, 0xb1, 0x09, 0x6f, 0x17,
                0xb7, 0x77, 0x7e, 0x65, 0x88, 0xd4, 0x95, 0x56,
            },
        },
        .confirm_rsp[5] = {
            .value = {
                0xc6, 0x9b, 0xac, 0xde, 0x7e, 0x03, 0x7a, 0xd3,
                0xf1, 0xff, 0x3c, 0x4f, 0x4a, 0x85, 0xba, 0x73,
            },
        },
        .random_req[5] = {
            .value = {
                0x17, 0xd5, 0x5e, 0x69, 0x30, 0x2c, 0x1f, 0x01,
                0x87, 0x9c, 0xd6, 0xd2, 0xe4, 0x48, 0x8c, 0x84,
            },
        },
        .random_rsp[5] = {
            .value = {
                0x9d, 0x54, 0x83, 0x4a, 0xcd, 0x93, 0x7c, 0x1e,
                0x5b, 0xaf, 0xd2, 0x66, 0x8c, 0x2d, 0xaa, 0xc3,
            },
        },
        .confirm_req[6] = {
            .value = {
                0xdc, 0x24, 0x69, 0xa8, 0xd3, 0xa9, 0x17, 0x11,
                0x08, 0x37, 0x1a, 0x1e, 0x92, 0x03, 0xee, 0x36,
            },
        },
        .confirm_rsp[6] = {
            .value = {
                0x98, 0xf8, 0x72, 0x71, 0x99, 0xa0, 0xbd, 0xcd,
                0xb1, 0x97, 0x4c, 0x8a, 0xb8, 0xa8, 0x1a, 0x52,
            },
        },
        .random_req[6] = {
            .value = {
                0xbf, 0xb1, 0x8e, 0xa5, 0x14, 0xe3, 0xeb, 0x9e,
                0x29, 0x27, 0xe0, 0x19, 0xb1, 0xb2, 0x5c, 0xfe,
            },
        },
        .random_rsp[6] = {
            .value = {
                0xae, 0x8a, 0x92, 0x78, 0x53, 0x7b, 0xdb, 0x8c,
                0xec, 0x3a, 0x99, 0x2b, 0x94, 0xf1, 0x17, 0xfe,
            },
        },
        .confirm_req[7] = {
            .value = {
                0xcf, 0xaf, 0x70, 0x73, 0x53, 0x65, 0x89, 0x57,
                0x36, 0x98, 0xd2, 0x28, 0x86, 0x79, 0xfe, 0x85,
            },
        },
        .confirm_rsp[7] = {
            .value = {
                0x0d, 0x2d, 0x77, 0x8a, 0x21, 0x11, 0xd9, 0x61,
                0x9f, 0x80, 0x32, 0x8a, 0x32, 0x09, 0x42, 0x42,
            },
        },
        .random_req[7] = {
            .value = {
                0x8b, 0xd2, 0x53, 0xcd, 0x96, 0xd1, 0x14, 0xb5,
                0xea, 0x17, 0xb1, 0xa3, 0xa8, 0xfc, 0x3c, 0x2b,
            },
        },
        .random_rsp[7] = {
            .value = {
                0xc2, 0x4f, 0x84, 0x60, 0x54, 0x79, 0x16, 0xed,
                0x1a, 0x6e, 0x78, 0xa0, 0x99, 0x58, 0xf2, 0x94,
            },
        },
        .confirm_req[8] = {
            .value = {
                0x9a, 0x4c, 0xbc, 0x9c, 0x55, 0x15, 0xa2, 0x4f,
                0xa2, 0x5d, 0x3b, 0xa7, 0x43, 0xb3, 0x9c, 0x63,
            },
        },
        .confirm_rsp[8] = {
            .value = {
                0xa3, 0xb1, 0x88, 0xa5, 0x70, 0xca, 0xa3, 0xa9,
                0x67, 0x2a, 0xac, 0x99, 0x5e, 0x61, 0x68, 0xa0,
            },
        },
        .random_req[8] = {
            .value = {
                0xcf, 0xcf, 0x5b, 0x94, 0xe0, 0xb2, 0x9d, 0x5a,
                0x86, 0x71, 0x45, 0xce, 0xd9, 0xce, 0x13, 0xba,
            },
        },
        .random_rsp[8] = {
            .value = {
                0x10, 0x96, 0x8a, 0x50, 0xa4, 0xd0, 0xaa, 0x5f,
                0xd6, 0x32, 0xdb, 0x09, 0x7e, 0x22, 0x96, 0x42,
            },
        },
        .confirm_req[9] = {
            .value = {
                0xf0, 0x90, 0x61, 0x25, 0x04, 0x29, 0x4f, 0xb6,
                0x8b, 0xd5, 0x73, 0x49, 0xbd, 0xf7, 0x9b, 0xe7,
            },
        },
        .confirm_rsp[9] = {
            .value = {
                0x5b, 0xe6, 0xb4, 0x3f, 0x1b, 0x77, 0x12, 0x75,
                0x84, 0x94, 0xc6, 0x07, 0xfa, 0xa1, 0x41, 0x94,
            },
        },
        .random_req[9] = {
            .value = {
                0x3d, 0x1a, 0xa3, 0x95, 0xec, 0x72, 0x84, 0xf4,
                0xc5, 0xcd, 0xaa, 0x48, 0xe9, 0x0c, 0x0f, 0xe3,
            },
        },
        .random_rsp[9] = {
            .value = {
                0x8a, 0x5a, 0x53, 0xfc, 0x07, 0x52, 0x01, 0xb9,
                0xe9, 0x2d, 0xe7, 0x9d, 0x8c, 0x7c, 0xc7, 0xb3,
            },
        },
        .confirm_req[10] = {
            .value = {
                0xe7, 0x8e, 0xc5, 0x08, 0x7f, 0x7e, 0xb8, 0xdc,
                0x05, 0x88, 0x3a, 0x92, 0x5a, 0xf5, 0x9b, 0xa9,
            },
        },
        .confirm_rsp[10] = {
            .value = {
                0xf7, 0xa2, 0xb6, 0xec, 0xcd, 0xef, 0xcb, 0xb7,
                0x6f, 0xc3, 0xac, 0x17, 0xe2, 0xfd, 0xfa, 0x42,
            },
        },
        .random_req[10] = {
            .value = {
                0x0d, 0xd1, 0xa2, 0x1d, 0xff, 0x74, 0xc5, 0x99,
                0xe0, 0x67, 0x07, 0x99, 0x95, 0x75, 0x39, 0x76,
            },
        },
        .random_rsp[10] = {
            .value = {
                0x2f, 0x13, 0xd1, 0x59, 0xfe, 0x20, 0x60, 0xf0,
                0x02, 0x0c, 0xea, 0x79, 0xd7, 0x40, 0x86, 0x85,
            },
        },
        .confirm_req[11] = {
            .value = {
                0x8b, 0x57, 0x87, 0xdd, 0xb1, 0xcc, 0x2d, 0x65,
                0xc1, 0xba, 0xac, 0x88, 0x48, 0x23, 0xda, 0xe7,
            },
        },
        .confirm_rsp[11] = {
            .value = {
                0xb3, 0xc4, 0x2e, 0xea, 0x33, 0xaf, 0x12, 0x9c,
                0xb5, 0xab, 0xa1, 0x95, 0x30, 0xca, 0x46, 0x48,
            },
        },
        .random_req[11] = {
            .value = {
                0x35, 0x57, 0xcd, 0xd5, 0xd2, 0xf8, 0xd7, 0xf2,
                0x7b, 0xe3, 0xd7, 0xba, 0x31, 0xa5, 0xca, 0xfd,
            },
        },
        .random_rsp[11] = {
            .value = {
                0xe2, 0x3b, 0x20, 0xbe, 0xec, 0xa5, 0x34, 0x3b,
                0x76, 0x23, 0x53, 0x28, 0x36, 0xc4, 0x60, 0x13,
            },
        },
        .confirm_req[12] = {
            .value = {
                0xc9, 0xfe, 0x03, 0x49, 0xe4, 0xff, 0x7e, 0xf7,
                0x00, 0xd1, 0x2b, 0x13, 0xb1, 0x15, 0x6e, 0x92,
            },
        },
        .confirm_rsp[12] = {
            .value = {
                0xbc, 0xa2, 0xf2, 0x03, 0x5c, 0xfd, 0x20, 0x7b,
                0xd0, 0x1f, 0xd6, 0x50, 0xec, 0xc6, 0x7b, 0x31,
            },
        },
        .random_req[12] = {
            .value = {
                0x04, 0x50, 0xea, 0xb8, 0xca, 0x36, 0x1a, 0x61,
                0x92, 0xed, 0xa0, 0x67, 0x78, 0x15, 0x10, 0xb5,
            },
        },
        .random_rsp[12] = {
            .value = {
                0x0c, 0x8e, 0x9d, 0x7b, 0x9d, 0x7e, 0xda, 0x23,
                0xbb, 0x61, 0xd9, 0xff, 0x46, 0x77, 0x33, 0x1b,
            },
        },
        .confirm_req[13] = {
            .value = {
                0x9a, 0xff, 0xd6, 0xe5, 0x1a, 0xc3, 0xd3, 0x37,
                0x34, 0xeb, 0x3e, 0x3a, 0x8e, 0x0b, 0x86, 0xb4,
            },
        },
        .confirm_rsp[13] = {
            .value = {
                0xf6, 0x32, 0x19, 0xb4, 0x08, 0x6b, 0x8a, 0x0f,
                0xc9, 0x9c, 0x1b, 0x68, 0xb8, 0xa0, 0xd0, 0xc9,
            },
        },
        .random_req[13] = {
            .value = {
                0x86, 0xeb, 0x5c, 0xf9, 0x33, 0x54, 0x7d, 0xe4,
                0xa4, 0xe2, 0xe1, 0xf6, 0x6b, 0xea, 0x34, 0xed,
            },
        },
        .random_rsp[13] = {
            .value = {
                0xad, 0x53, 0xa0, 0x6e, 0xde, 0x1d, 0xda, 0x99,
                0x31, 0x45, 0xe5, 0x3a, 0x73, 0xa1, 0x5e, 0xe1,
            },
        },
        .confirm_req[14] = {
            .value = {
                0x93, 0xd4, 0xe0, 0xaa, 0x0c, 0x91, 0xba, 0xde,
                0xc9, 0x5c, 0x68, 0xb0, 0xce, 0xb6, 0x84, 0xcd,
            },
        },
        .confirm_rsp[14] = {
            .value = {
                0x85, 0xc7, 0x05, 0x02, 0x21, 0x9d, 0x4c, 0x4c,
                0x16, 0xf7, 0x8f, 0x7b, 0xaa, 0xb4, 0x8f, 0x37,
            },
        },
        .random_req[14] = {
            .value = {
                0x84, 0xfd, 0xf1, 0x39, 0x1a, 0x9a, 0xa5, 0xb8,
                0x49, 0xc0, 0x66, 0xdc, 0x33, 0x71, 0x32, 0x87,
            },
        },
        .random_rsp[14] = {
            .value = {
                0x5d, 0xaf, 0x38, 0xcd, 0xb5, 0x83, 0xaa, 0xa0,
                0xab, 0x30, 0x82, 0xed, 0x6f, 0xd2, 0x75, 0xe7,
            },
        },
        .confirm_req[15] = {
            .value = {
                0x88, 0x12, 0xe8, 0x89, 0xd4, 0x52, 0x6d, 0xac,
                0x61, 0x2a, 0x85, 0x85, 0x1e, 0x9c, 0x82, 0x21,
            },
        },
        .confirm_rsp[15] = {
            .value = {
                0xc1, 0xe9, 0xcd, 0x21, 0x29, 0x6a, 0x78, 0xe4,
                0x7b, 0x7d, 0x73, 0x25, 0x9e, 0x9b, 0x95, 0x8b,
            },
        },
        .random_req[15] = {
            .value = {
                0x95, 0x87, 0x9d, 0x5a, 0x10, 0x14, 0xa0, 0xdf,
                0x5e, 0x02, 0x22, 0x39, 0x23, 0xc9, 0xbc, 0xba,
            },
        },
        .random_rsp[15] = {
            .value = {
                0x1b, 0x91, 0xe2, 0xdf, 0xca, 0xfe, 0x2b, 0x61,
                0x33, 0x8c, 0x83, 0xbf, 0xcf, 0xc3, 0x72, 0xcc,
            },
        },
        .confirm_req[16] = {
            .value = {
                0xce, 0xc9, 0x68, 0xf7, 0xea, 0x41, 0x18, 0x5c,
                0x16, 0x6a, 0x98, 0x13, 0x0c, 0x10, 0xc2, 0xa3,
            },
        },
        .confirm_rsp[16] = {
            .value = {
                0x97, 0x73, 0xc9, 0x72, 0x68, 0x99, 0x63, 0xed,
                0x81, 0x3b, 0x5c, 0xee, 0x37, 0xfc, 0xca, 0xae,
            },
        },
        .random_req[16] = {
            .value = {
                0x5b, 0x85, 0xb0, 0x1b, 0xc3, 0xde, 0x18, 0xba,
                0xc1, 0xc7, 0x89, 0x99, 0xfe, 0xcd, 0xdb, 0x6a,
            },
        },
        .random_rsp[16] = {
            .value = {
                0x5e, 0x1a, 0xcb, 0xbc, 0xda, 0x41, 0x06, 0x5a,
                0x14, 0x34, 0x3a, 0xb1, 0xa1, 0x6f, 0xb2, 0xd8,
            },
        },
        .confirm_req[17] = {
            .value = {
                0x1d, 0x59, 0x8a, 0xb0, 0x19, 0xe5, 0xff, 0x45,
                0xb6, 0xc3, 0x33, 0x64, 0xd1, 0x6e, 0xee, 0xdd,
            },
        },
        .confirm_rsp[17] = {
            .value = {
                0x4c, 0x9b, 0xe8, 0x68, 0x52, 0x34, 0xef, 0xe1,
                0x84, 0xbd, 0x37, 0x85, 0x53, 0x0d, 0xd5, 0xc1,
            },
        },
        .random_req[17] = {
            .value = {
                0xa6, 0xf7, 0x97, 0x18, 0x9a, 0x3e, 0x9d, 0xcf,
                0x91, 0xa3, 0xa3, 0x8e, 0xda, 0x8f, 0x8f, 0x90,
            },
        },
        .random_rsp[17] = {
            .value = {
                0x94, 0x10, 0x19, 0x17, 0x8d, 0x0a, 0x72, 0xfd,
                0x24, 0x9d, 0xfd, 0x37, 0x4e, 0xdf, 0x4c, 0x30,
            },
        },
        .confirm_req[18] = {
            .value = {
                0xfc, 0x64, 0x8a, 0x8b, 0x37, 0x17, 0x90, 0x6d,
                0x25, 0x0e, 0xc6, 0x18, 0xc9, 0xc9, 0xc2, 0x2a,
            },
        },
        .confirm_rsp[18] = {
            .value = {
                0x50, 0x98, 0x86, 0xf5, 0xc0, 0xda, 0x45, 0x2d,
                0xea, 0xc8, 0x9d, 0x28, 0x04, 0xd8, 0x73, 0x6f,
            },
        },
        .random_req[18] = {
            .value = {
                0x13, 0x10, 0x38, 0xe8, 0x17, 0x6d, 0x72, 0xd5,
                0x94, 0xaf, 0xed, 0x4f, 0x23, 0xa0, 0x41, 0xfc,
            },
        },
        .random_rsp[18] = {
            .value = {
                0xdf, 0xed, 0xf7, 0x08, 0xce, 0x64, 0xbc, 0x11,
                0x41, 0x7a, 0xd9, 0xf7, 0x4a, 0xd9, 0x4a, 0x15,
            },
        },
        .confirm_req[19] = {
            .value = {
                0xae, 0x24, 0x8f, 0xdf, 0xb0, 0x57, 0xc4, 0x9c,
                0xe6, 0xae, 0x9b, 0xc2, 0x4d, 0x3d, 0x1c, 0xcb,
            },
        },
        .confirm_rsp[19] = {
            .value = {
                0xcc, 0x5c, 0xa3, 0xbe, 0xd7, 0x83, 0xee, 0x60,
                0x80, 0xff, 0x5f, 0x1a, 0x07, 0xbf, 0x4c, 0x33,
            },
        },
        .random_req[19] = {
            .value = {
                0x93, 0xc3, 0x62, 0x06, 0xcb, 0xe5, 0xb0, 0x01,
                0x02, 0x18, 0xa2, 0x50, 0x4c, 0x73, 0xa2, 0x27,
            },
        },
        .random_rsp[19] = {
            .value = {
                0x11, 0x2a, 0xd3, 0x06, 0x28, 0x9c, 0xdf, 0x73,
                0xa5, 0xa4, 0xe5, 0x1e, 0x07, 0xcf, 0xee, 0x71,
            },
        },
        .dhkey_check_req = {
            .value = {
                0x73, 0xa0, 0x40, 0x58, 0x78, 0x20, 0x5f, 0x2c,
                0xf4, 0x19, 0x23, 0xa8, 0x74, 0xbd, 0xc2, 0x3e,
            },
        },
        .dhkey_check_rsp = {
            .value = {
                0x5a, 0x30, 0xbc, 0xce, 0xec, 0xdf, 0xf0, 0x32,
                0x3c, 0x18, 0xa3, 0xd3, 0x3f, 0x20, 0x87, 0x10,
            },
        },
        .sign_info_req = {
            .sig_key = {
                0x2e, 0x81, 0x09, 0xde, 0x32, 0xc5, 0x28, 0x34,
                0xe1, 0x45, 0x4a, 0x35, 0x49, 0xef, 0xa2, 0xed,
            },
        },
        .id_info_rsp = {
            .irk = {
                0xef, 0x8d, 0xe2, 0x16, 0x4f, 0xec, 0x43, 0x0d,
                0xbf, 0x5b, 0xdd, 0x34, 0xc0, 0x53, 0x1e, 0xb8,
            },
        },
        .id_addr_info_rsp = {
            .addr_type = 0,
            .bd_addr = {
                0x01, 0x01, 0x01, 0x07, 0x08, 0x01,
            },
        },
        .sign_info_rsp = {
            .sig_key = {
                0x90, 0x3d, 0x26, 0x65, 0xc1, 0xd1, 0x5a, 0x9d,
                0xda, 0xab, 0x0d, 0x00, 0x05, 0x0e, 0x6c, 0x5d,
            },
        },
        .ltk = {
            0xf1, 0x41, 0x1a, 0x5b, 0x60, 0xc1, 0x43, 0xc6,
            0x80, 0x34, 0x5e, 0x7f, 0xd8, 0x0c, 0x75, 0xdc,
        },
        .pair_alg = BLE_SM_PAIR_ALG_PASSKEY,
        .authenticated = 1,
        .passkey_info = {
            .passkey = {
                .action = BLE_SM_IOACT_INPUT,
                .passkey = 516645,
            },
        },
    };
    ble_sm_test_util_us_sc_bad(&params);

    ble_hs_test_util_assert_mbufs_freed(NULL);
}
/**
 * Secure connections pairing
 * Master: us
 * Pair algorithm: passkey entry
 * Initiator IO capabilities: 0
 * Responder IO capabilities: 4
 * Bonding: true
 * Initiator address type: 0
 * Responder address type: 0
 * Initiator key distribution: 7
 * Responder key distribution: 5
 */
TEST_CASE_SELF(ble_sm_sc_us_pk_iio0_rio4_b1_iat0_rat0_ik7_rk5)
{
    struct ble_sm_test_params params;

    params = (struct ble_sm_test_params) {
        .init_id_addr = {
            0x01, 0x01, 0x01, 0x07, 0x08, 0x01,
        },
        .resp_id_addr = {
            0xca, 0x61, 0xa0, 0x67, 0x94, 0xe0,
        },
        .pair_req = {
            .io_cap = 0x00,
            .oob_data_flag = 0x00,
            .authreq = 0x0d,
            .max_enc_key_size = 0x10,
            .init_key_dist = 0x07,
            .resp_key_dist = 0x07,
        },
        .pair_rsp = {
            .io_cap = 0x04,
            .oob_data_flag = 0x00,
            .authreq = 0x0d,
            .max_enc_key_size = 0x10,
            .init_key_dist = 0x07,
            .resp_key_dist = 0x05,
        },
        .our_priv_key = {
            0xb1, 0x6b, 0x4f, 0x81, 0xbc, 0xe3, 0x60, 0x9e,
            0x00, 0x20, 0xf1, 0x73, 0x3e, 0xfb, 0xcc, 0x6e,
            0x8c, 0xb6, 0xd2, 0x51, 0xd9, 0x36, 0x8a, 0x6d,
            0xca, 0x8c, 0xd7, 0xbe, 0x96, 0x03, 0xdf, 0xd6,
        },
        .public_key_req = {
            .x = {
                0xe5, 0x0f, 0x02, 0x0a, 0x37, 0x90, 0x94, 0x5a,
                0x06, 0x21, 0xf7, 0xbc, 0xd5, 0xbe, 0xb9, 0x24,
                0x8a, 0x35, 0xfd, 0xf8, 0x5e, 0xe2, 0x70, 0xd5,
                0x5a, 0xe8, 0xe7, 0xdd, 0x13, 0x90, 0xeb, 0xd4,
            },
            .y = {
                0x41, 0xc8, 0x51, 0x1a, 0x25, 0x44, 0x01, 0x53,
                0x42, 0x74, 0x07, 0x9c, 0x18, 0xe6, 0x3b, 0x8a,
                0xce, 0x7a, 0x37, 0x1f, 0x18, 0x5c, 0x02, 0x7c,
                0x67, 0x16, 0xf5, 0x30, 0x2b, 0x31, 0xa9, 0xc7,
            },
        },
        .public_key_rsp = {
            .x = {
                0x03, 0x0d, 0x13, 0x55, 0xd9, 0xee, 0x3f, 0xac,
                0x8e, 0x8a, 0xa6, 0x2a, 0xcb, 0x60, 0x35, 0xb9,
                0xb2, 0x4d, 0x63, 0x91, 0x5e, 0xa1, 0xdd, 0xdf,
                0x60, 0xdc, 0x6e, 0x09, 0xb9, 0x9e, 0xf1, 0x4d,
            },
            .y = {
                0xa8, 0x09, 0x31, 0x1e, 0x39, 0x96, 0x74, 0x41,
                0xea, 0x19, 0x4f, 0x24, 0x36, 0x57, 0x7c, 0x9f,
                0x21, 0xa3, 0xad, 0xa1, 0x3d, 0xe2, 0x1c, 0x6a,
                0xd6, 0xc9, 0xdb, 0xff, 0xce, 0x0a, 0x94, 0x12,
            },
        },
        .confirm_req[0] = {
            .value = {
                0x3b, 0x3d, 0xb2, 0x2f, 0x72, 0x0f, 0x93, 0x19,
                0x95, 0xdb, 0x88, 0xdf, 0x5d, 0x58, 0x95, 0x37,
            },
        },
        .confirm_rsp[0] = {
            .value = {
                0x98, 0xab, 0x20, 0x8d, 0x51, 0x3b, 0x6c, 0x29,
                0x2d, 0x73, 0x15, 0xf6, 0x6d, 0x6d, 0xb9, 0xb3,
            },
        },
        .random_req[0] = {
            .value = {
                0xc1, 0xdf, 0x20, 0x3d, 0x7b, 0xcb, 0x5f, 0xe2,
                0x9a, 0x23, 0x9c, 0xba, 0x2f, 0x42, 0x3c, 0xb8,
            },
        },
        .random_rsp[0] = {
            .value = {
                0x7d, 0x82, 0xb4, 0xae, 0x41, 0xdb, 0x67, 0x8f,
                0x54, 0x01, 0x21, 0x64, 0x31, 0xd4, 0xfc, 0xb5,
            },
        },
        .confirm_req[1] = {
            .value = {
                0xc3, 0xa5, 0xd0, 0xdd, 0xd5, 0xec, 0x1d, 0xc3,
                0x14, 0x95, 0x79, 0xb2, 0x61, 0x4d, 0x4f, 0x36,
            },
        },
        .confirm_rsp[1] = {
            .value = {
                0xe2, 0x20, 0xf4, 0x4d, 0xa1, 0x9c, 0x83, 0x51,
                0x18, 0xf9, 0x35, 0x2a, 0x51, 0x50, 0xdf, 0xe7,
            },
        },
        .random_req[1] = {
            .value = {
                0x71, 0xcb, 0x01, 0xb4, 0x83, 0xdc, 0xd8, 0x54,
                0x0f, 0xe5, 0xd5, 0x6b, 0x6a, 0x0d, 0x98, 0xb6,
            },
        },
        .random_rsp[1] = {
            .value = {
                0x30, 0xbf, 0xd3, 0xfd, 0xf4, 0xc2, 0xa1, 0xd0,
                0xba, 0x4b, 0x27, 0x7c, 0x29, 0x98, 0x54, 0xa2,
            },
        },
        .confirm_req[2] = {
            .value = {
                0xf0, 0x92, 0xc4, 0xda, 0x8a, 0x17, 0x7c, 0xc6,
                0x14, 0x05, 0x7d, 0xbb, 0xfc, 0x7c, 0xcd, 0x0a,
            },
        },
        .confirm_rsp[2] = {
            .value = {
                0xf3, 0x89, 0xca, 0xe0, 0xfb, 0xbe, 0x8c, 0xc3,
                0x4c, 0x6c, 0x6e, 0x11, 0x36, 0x4e, 0xaa, 0x25,
            },
        },
        .random_req[2] = {
            .value = {
                0x78, 0x5a, 0xf0, 0x1e, 0x2a, 0x0d, 0x16, 0xb3,
                0x03, 0x4b, 0x4b, 0x68, 0x17, 0xe0, 0xf0, 0x82,
            },
        },
        .random_rsp[2] = {
            .value = {
                0xbf, 0x96, 0xdd, 0xf5, 0x30, 0x2a, 0xe9, 0x8c,
                0xb9, 0x13, 0xc5, 0xb7, 0x15, 0x1f, 0xa3, 0x9b,
            },
        },
        .confirm_req[3] = {
            .value = {
                0x36, 0xe4, 0x4c, 0x00, 0xe7, 0x0d, 0xee, 0xe4,
                0x95, 0xb8, 0x6a, 0xf9, 0xf7, 0x24, 0xef, 0xea,
            },
        },
        .confirm_rsp[3] = {
            .value = {
                0x2e, 0x90, 0x87, 0x85, 0xb8, 0x29, 0x93, 0x9e,
                0x38, 0xa6, 0xdb, 0x17, 0xb2, 0xa8, 0x32, 0x65,
            },
        },
        .random_req[3] = {
            .value = {
                0x0a, 0xee, 0x93, 0xf6, 0x56, 0x35, 0x8e, 0xed,
                0x3f, 0x45, 0xa5, 0x01, 0x59, 0xeb, 0xea, 0xa8,
            },
        },
        .random_rsp[3] = {
            .value = {
                0x38, 0xd0, 0xf8, 0x11, 0x5e, 0x47, 0x72, 0x66,
                0xce, 0x56, 0x9c, 0x81, 0x5f, 0x52, 0xd4, 0x9a,
            },
        },
        .confirm_req[4] = {
            .value = {
                0x2c, 0x98, 0x9b, 0x71, 0xe4, 0xde, 0x6d, 0x20,
                0x84, 0x30, 0xab, 0x7a, 0xfc, 0x43, 0x82, 0xc6,
            },
        },
        .confirm_rsp[4] = {
            .value = {
                0x76, 0xfe, 0x1f, 0x78, 0xaa, 0x42, 0xd5, 0xc6,
                0x9f, 0xe4, 0xa7, 0xc7, 0xb8, 0xd2, 0x1e, 0x59,
            },
        },
        .random_req[4] = {
            .value = {
                0x61, 0x5e, 0x47, 0xb1, 0x77, 0x6f, 0x04, 0xee,
                0x94, 0xc4, 0x6c, 0xa9, 0xf5, 0xf8, 0x11, 0x6e,
            },
        },
        .random_rsp[4] = {
            .value = {
                0xa5, 0xad, 0x98, 0x65, 0x28, 0xfc, 0x6b, 0x02,
                0x6d, 0x9a, 0x29, 0x61, 0x1c, 0x02, 0x0a, 0x6b,
            },
        },
        .confirm_req[5] = {
            .value = {
                0x4d, 0x55, 0x3e, 0x1f, 0x87, 0x12, 0xc7, 0x6c,
                0xd7, 0x9a, 0xa6, 0xf1, 0x6e, 0x48, 0xd3, 0x7d,
            },
        },
        .confirm_rsp[5] = {
            .value = {
                0xff, 0x77, 0x6e, 0xba, 0x1f, 0xb7, 0xbd, 0x0b,
                0x3f, 0xce, 0xd5, 0x39, 0x81, 0x17, 0x51, 0xfc,
            },
        },
        .random_req[5] = {
            .value = {
                0xf9, 0x2c, 0x77, 0x41, 0xcd, 0x9a, 0x10, 0x99,
                0xc6, 0x70, 0x5a, 0xc8, 0x24, 0x26, 0xb2, 0xc8,
            },
        },
        .random_rsp[5] = {
            .value = {
                0xa0, 0x44, 0x0a, 0x8b, 0xda, 0x6a, 0x74, 0x90,
                0x5f, 0x89, 0x44, 0xa5, 0x9a, 0x58, 0xd5, 0x08,
            },
        },
        .confirm_req[6] = {
            .value = {
                0x7e, 0x6e, 0x89, 0xc8, 0xbe, 0xde, 0x1c, 0xc3,
                0x45, 0xb6, 0x4c, 0x83, 0x71, 0xe2, 0xd6, 0xda,
            },
        },
        .confirm_rsp[6] = {
            .value = {
                0x4a, 0x29, 0x7b, 0x88, 0x97, 0xc1, 0x60, 0x85,
                0x32, 0x7d, 0xf1, 0xaa, 0x04, 0x13, 0x89, 0x11,
            },
        },
        .random_req[6] = {
            .value = {
                0x2a, 0xaf, 0x7d, 0x21, 0x4e, 0x14, 0xf5, 0x7e,
                0xcc, 0x39, 0xf7, 0x56, 0x45, 0x87, 0x23, 0x64,
            },
        },
        .random_rsp[6] = {
            .value = {
                0x74, 0xd2, 0xff, 0xf0, 0x19, 0xf7, 0x87, 0xe7,
                0x0d, 0x65, 0x27, 0x61, 0xea, 0x9e, 0x05, 0x3d,
            },
        },
        .confirm_req[7] = {
            .value = {
                0x4f, 0x77, 0x22, 0x08, 0x58, 0xed, 0x8c, 0x60,
                0xbf, 0xbc, 0x78, 0x0c, 0x80, 0xc9, 0xb7, 0x60,
            },
        },
        .confirm_rsp[7] = {
            .value = {
                0xd2, 0x47, 0xfd, 0xea, 0xa3, 0x32, 0x53, 0xc1,
                0x06, 0xcd, 0x64, 0xeb, 0x88, 0x64, 0x0e, 0xe5,
            },
        },
        .random_req[7] = {
            .value = {
                0xc8, 0xd0, 0x45, 0xa8, 0x29, 0xdb, 0x5a, 0x42,
                0xfe, 0x68, 0xa8, 0x7a, 0x0a, 0x13, 0x22, 0xa4,
            },
        },
        .random_rsp[7] = {
            .value = {
                0x78, 0x14, 0x46, 0xe2, 0x47, 0x0e, 0xd4, 0xb4,
                0xde, 0x35, 0x4a, 0x82, 0x4b, 0x32, 0x9b, 0x46,
            },
        },
        .confirm_req[8] = {
            .value = {
                0x24, 0x96, 0xe5, 0x50, 0xfa, 0xff, 0xba, 0xdf,
                0x6b, 0x76, 0x40, 0x60, 0x56, 0x5e, 0x5a, 0x66,
            },
        },
        .confirm_rsp[8] = {
            .value = {
                0x09, 0xfe, 0x15, 0x3e, 0x55, 0xe5, 0xbe, 0xb7,
                0x8d, 0xaa, 0x04, 0x59, 0xe6, 0x8b, 0x2c, 0x4e,
            },
        },
        .random_req[8] = {
            .value = {
                0x02, 0x25, 0xbe, 0x88, 0x37, 0xb4, 0x6e, 0xcb,
                0xbc, 0xa9, 0xef, 0x5a, 0xfd, 0x1a, 0x5f, 0x5f,
            },
        },
        .random_rsp[8] = {
            .value = {
                0x0b, 0x35, 0xdc, 0x9b, 0x3d, 0xf7, 0xa6, 0x99,
                0xf3, 0xb9, 0x3c, 0x73, 0x67, 0x0e, 0xcc, 0x12,
            },
        },
        .confirm_req[9] = {
            .value = {
                0x38, 0x1c, 0xf7, 0xf0, 0x31, 0xb1, 0x20, 0xa0,
                0x51, 0x1c, 0xf1, 0xbd, 0x67, 0xfa, 0x84, 0xb4,
            },
        },
        .confirm_rsp[9] = {
            .value = {
                0x8e, 0xa1, 0xc1, 0xf5, 0x39, 0xf0, 0x00, 0x49,
                0xfb, 0xfc, 0xdc, 0xdf, 0x87, 0x0e, 0x96, 0x7e,
            },
        },
        .random_req[9] = {
            .value = {
                0xd0, 0xed, 0x6c, 0x52, 0x20, 0x4b, 0x7b, 0x24,
                0xdd, 0x28, 0x53, 0x2d, 0x71, 0x76, 0xfb, 0x8f,
            },
        },
        .random_rsp[9] = {
            .value = {
                0xac, 0xd7, 0x34, 0x6b, 0x7b, 0x59, 0x9e, 0x9b,
                0x5b, 0x37, 0xc6, 0x5c, 0x3e, 0x9d, 0xe2, 0x13,
            },
        },
        .confirm_req[10] = {
            .value = {
                0xa6, 0xd7, 0xb6, 0xd6, 0xb5, 0x01, 0x4a, 0x02,
                0x0d, 0xf0, 0x22, 0xcb, 0x68, 0xad, 0x7d, 0x73,
            },
        },
        .confirm_rsp[10] = {
            .value = {
                0x01, 0xcc, 0x5f, 0xbc, 0xd0, 0x22, 0xa1, 0xb2,
                0x71, 0x9d, 0x5c, 0x97, 0xfa, 0xd3, 0x6a, 0xc7,
            },
        },
        .random_req[10] = {
            .value = {
                0x9f, 0x3a, 0x25, 0xc7, 0x9b, 0xb7, 0xb3, 0x51,
                0xff, 0xde, 0x3b, 0x1c, 0xdd, 0xf5, 0x08, 0x21,
            },
        },
        .random_rsp[10] = {
            .value = {
                0x75, 0x1e, 0x8d, 0xa4, 0x5b, 0x35, 0xec, 0xae,
                0x17, 0xda, 0xa5, 0x43, 0x76, 0x3c, 0x6a, 0x67,
            },
        },
        .confirm_req[11] = {
            .value = {
                0xfc, 0x0c, 0x3f, 0x86, 0xbf, 0xbe, 0x96, 0x0f,
                0x99, 0x11, 0xa5, 0x36, 0x03, 0xcd, 0xbd, 0x7f,
            },
        },
        .confirm_rsp[11] = {
            .value = {
                0x48, 0xcd, 0xc8, 0x89, 0xd6, 0x1c, 0x0d, 0xb1,
                0x90, 0x01, 0x0e, 0xea, 0x80, 0xbc, 0xff, 0xb3,
            },
        },
        .random_req[11] = {
            .value = {
                0xec, 0xfc, 0xe3, 0x0a, 0x97, 0xed, 0xe8, 0x51,
                0x5d, 0x64, 0x3c, 0x73, 0x59, 0x2e, 0x62, 0xac,
            },
        },
        .random_rsp[11] = {
            .value = {
                0x81, 0x74, 0x44, 0xca, 0xec, 0x38, 0x20, 0x6d,
                0x52, 0x27, 0x49, 0x55, 0x61, 0x97, 0x01, 0x34,
            },
        },
        .confirm_req[12] = {
            .value = {
                0x27, 0x8c, 0x88, 0x09, 0xcb, 0xd6, 0x45, 0xb7,
                0x30, 0x4b, 0x1b, 0xcd, 0xc3, 0xac, 0x83, 0xd6,
            },
        },
        .confirm_rsp[12] = {
            .value = {
                0xea, 0xbc, 0xe2, 0x43, 0xc8, 0xe0, 0x06, 0xd8,
                0x7b, 0x3e, 0xa4, 0x55, 0x95, 0xa2, 0x23, 0x9b,
            },
        },
        .random_req[12] = {
            .value = {
                0x68, 0x8f, 0xb6, 0x7b, 0x91, 0x0d, 0xc9, 0x30,
                0xe7, 0xb7, 0xb7, 0x7a, 0x79, 0x29, 0x59, 0x7d,
            },
        },
        .random_rsp[12] = {
            .value = {
                0xfd, 0xa1, 0x3d, 0xaf, 0x8d, 0xd2, 0xa0, 0x02,
                0x82, 0x92, 0xeb, 0x2e, 0x4d, 0x6c, 0x8d, 0x69,
            },
        },
        .confirm_req[13] = {
            .value = {
                0x6f, 0xa8, 0x20, 0x81, 0x1c, 0x4b, 0xe8, 0xe3,
                0xdc, 0xea, 0x39, 0xbd, 0xfb, 0xbf, 0x79, 0xc4,
            },
        },
        .confirm_rsp[13] = {
            .value = {
                0x2a, 0x09, 0xec, 0x32, 0x63, 0x3d, 0x38, 0x5d,
                0x28, 0xb2, 0xb1, 0x62, 0xee, 0x6c, 0x0a, 0x6c,
            },
        },
        .random_req[13] = {
            .value = {
                0x35, 0xb5, 0xc5, 0xc0, 0x74, 0x1f, 0x40, 0xac,
                0x23, 0x52, 0x02, 0x68, 0xdf, 0x62, 0x73, 0xca,
            },
        },
        .random_rsp[13] = {
            .value = {
                0xb8, 0xe2, 0x65, 0xdc, 0x22, 0xcb, 0xc2, 0xdb,
                0x00, 0x60, 0x37, 0xe2, 0xcc, 0xc0, 0x41, 0x72,
            },
        },
        .confirm_req[14] = {
            .value = {
                0x05, 0x0b, 0x5c, 0xa7, 0x58, 0x9c, 0x08, 0x81,
                0x4a, 0x6b, 0x12, 0xae, 0xaa, 0xe5, 0x81, 0xf3,
            },
        },
        .confirm_rsp[14] = {
            .value = {
                0xdd, 0x2b, 0xd1, 0xdd, 0x49, 0x92, 0xf3, 0xe1,
                0xae, 0xf3, 0x6d, 0x89, 0xfd, 0x77, 0xf9, 0xaa,
            },
        },
        .random_req[14] = {
            .value = {
                0xbc, 0x27, 0x29, 0x1b, 0xc4, 0xbc, 0x0e, 0x88,
                0x95, 0x50, 0xf7, 0x92, 0xe6, 0xf7, 0x29, 0xe8,
            },
        },
        .random_rsp[14] = {
            .value = {
                0xe7, 0x15, 0xfe, 0x53, 0x77, 0xd9, 0x98, 0x1d,
                0x5b, 0x4e, 0x37, 0xa3, 0x1f, 0xc9, 0x47, 0x5d,
            },
        },
        .confirm_req[15] = {
            .value = {
                0x75, 0x70, 0x9f, 0x84, 0x3e, 0x6b, 0x88, 0xcb,
                0x66, 0xda, 0x8f, 0x79, 0xbc, 0xf8, 0x44, 0x99,
            },
        },
        .confirm_rsp[15] = {
            .value = {
                0x13, 0xe4, 0x43, 0xb2, 0x61, 0x72, 0xfd, 0x33,
                0xba, 0x87, 0x44, 0x27, 0x6f, 0x9a, 0xea, 0x19,
            },
        },
        .random_req[15] = {
            .value = {
                0xda, 0x90, 0x59, 0x72, 0xed, 0x67, 0xde, 0x65,
                0x21, 0xab, 0x7d, 0x9d, 0x72, 0x8c, 0x88, 0x8e,
            },
        },
        .random_rsp[15] = {
            .value = {
                0x94, 0x92, 0x0f, 0x6c, 0x08, 0xde, 0xae, 0xa7,
                0xfd, 0x36, 0xe0, 0x02, 0xc8, 0xfd, 0xdd, 0x69,
            },
        },
        .confirm_req[16] = {
            .value = {
                0x35, 0x68, 0x1e, 0x80, 0x37, 0xc4, 0x91, 0xe8,
                0xbf, 0x5e, 0x27, 0x0c, 0xaa, 0x8e, 0x85, 0x7b,
            },
        },
        .confirm_rsp[16] = {
            .value = {
                0x1e, 0x42, 0x47, 0x29, 0x06, 0xdc, 0x2b, 0x45,
                0xec, 0x95, 0x23, 0x31, 0x29, 0x24, 0x95, 0xf0,
            },
        },
        .random_req[16] = {
            .value = {
                0x4e, 0x9f, 0x5d, 0x5a, 0x8f, 0xf7, 0x28, 0xc9,
                0x29, 0x62, 0x0a, 0x67, 0x19, 0x17, 0x5e, 0xa7,
            },
        },
        .random_rsp[16] = {
            .value = {
                0x7c, 0xd4, 0x13, 0xba, 0x27, 0x16, 0x39, 0xe7,
                0xf0, 0xbf, 0xec, 0x1e, 0xe5, 0xcc, 0x20, 0x0b,
            },
        },
        .confirm_req[17] = {
            .value = {
                0xb9, 0xcd, 0xf5, 0xf9, 0x2b, 0x4f, 0x6d, 0x08,
                0x51, 0xe0, 0x92, 0x99, 0x15, 0xca, 0x15, 0x2a,
            },
        },
        .confirm_rsp[17] = {
            .value = {
                0xb8, 0xf2, 0xf9, 0x61, 0x4f, 0x0e, 0xfd, 0x19,
                0xcb, 0x5d, 0x7e, 0x93, 0x87, 0x7a, 0x0a, 0x6e,
            },
        },
        .random_req[17] = {
            .value = {
                0x8b, 0xf8, 0xc8, 0xeb, 0xe5, 0xdb, 0xcf, 0xfe,
                0x68, 0x70, 0x1f, 0xbe, 0x1e, 0x3c, 0x94, 0x7d,
            },
        },
        .random_rsp[17] = {
            .value = {
                0x0d, 0xfc, 0x68, 0x2e, 0x50, 0x31, 0x9f, 0x60,
                0xe6, 0x12, 0x72, 0x24, 0x7c, 0xad, 0xf7, 0x48,
            },
        },
        .confirm_req[18] = {
            .value = {
                0x27, 0x68, 0x07, 0xaa, 0xa6, 0x33, 0x13, 0x49,
                0x65, 0x4c, 0x80, 0x54, 0xfb, 0x69, 0xcb, 0x0e,
            },
        },
        .confirm_rsp[18] = {
            .value = {
                0xc5, 0x8d, 0x45, 0x81, 0xb0, 0x5a, 0x69, 0x0f,
                0x6c, 0x89, 0x0b, 0x60, 0x1e, 0x27, 0x9b, 0x9e,
            },
        },
        .random_req[18] = {
            .value = {
                0x97, 0x46, 0x95, 0xb5, 0x86, 0xa1, 0xc1, 0x86,
                0x3a, 0x8a, 0x1f, 0x29, 0x38, 0xe0, 0x69, 0x7f,
            },
        },
        .random_rsp[18] = {
            .value = {
                0x8f, 0x0e, 0x56, 0x17, 0x1c, 0x4b, 0x78, 0x1f,
                0xd1, 0x8a, 0x69, 0xbd, 0x65, 0xe3, 0xde, 0x3c,
            },
        },
        .confirm_req[19] = {
            .value = {
                0xd2, 0xa5, 0x4e, 0x31, 0x34, 0xde, 0x68, 0xf0,
                0x69, 0x88, 0x6f, 0x28, 0xa2, 0xdd, 0xba, 0xe1,
            },
        },
        .confirm_rsp[19] = {
            .value = {
                0xf8, 0x5e, 0x4f, 0x4d, 0x56, 0xf6, 0x22, 0xc0,
                0x57, 0x04, 0x04, 0x45, 0x24, 0x83, 0x09, 0x80,
            },
        },
        .random_req[19] = {
            .value = {
                0x64, 0xe1, 0x5a, 0x76, 0x71, 0x94, 0xc0, 0x64,
                0x2b, 0xea, 0x9d, 0xaf, 0xbd, 0x10, 0x25, 0x9b,
            },
        },
        .random_rsp[19] = {
            .value = {
                0x1e, 0x38, 0x6e, 0x66, 0x55, 0xf1, 0x7f, 0x55,
                0x7c, 0x00, 0xff, 0xad, 0x07, 0x13, 0x25, 0x97,
            },
        },
        .dhkey_check_req = {
            .value = {
                0x98, 0xf1, 0x5a, 0x24, 0x81, 0x5d, 0xb5, 0xac,
                0x04, 0x4e, 0x3a, 0x31, 0x8b, 0x7d, 0xf6, 0x09,
            },
        },
        .dhkey_check_rsp = {
            .value = {
                0x1a, 0xb4, 0xf4, 0xf3, 0xc0, 0x5a, 0xf3, 0x13,
                0x8d, 0x6e, 0x01, 0x16, 0x1e, 0x54, 0xf3, 0xe1,
            },
        },
        .sign_info_req = {
            .sig_key = {
                0x4b, 0x01, 0x33, 0x5f, 0x4b, 0xfe, 0x12, 0x8b,
                0x9f, 0x81, 0x44, 0x78, 0x90, 0x03, 0x9e, 0x53,
            },
        },
        .id_info_rsp = {
            .irk = {
                0xef, 0x8d, 0xe2, 0x16, 0x4f, 0xec, 0x43, 0x0d,
                0xbf, 0x5b, 0xdd, 0x34, 0xc0, 0x53, 0x1e, 0xb8,
            },
        },
        .id_addr_info_rsp = {
            .addr_type = 0,
            .bd_addr = {
                0x01, 0x01, 0x01, 0x07, 0x08, 0x01,
            },
        },
        .sign_info_rsp = {
            .sig_key = {
                0xd2, 0xa1, 0x2c, 0xf0, 0xa6, 0xeb, 0x97, 0x5e,
                0xac, 0x53, 0xa1, 0x3d, 0x41, 0x40, 0x36, 0x2f,
            },
        },
        .ltk = {
            0xce, 0x28, 0x91, 0xa9, 0x36, 0xb7, 0xe1, 0xda,
            0x3c, 0x66, 0xd9, 0x33, 0x3d, 0x03, 0x8e, 0x31,
        },
        .pair_alg = BLE_SM_PAIR_ALG_PASSKEY,
        .authenticated = 1,
        .passkey_info = {
            .passkey = {
                .action = BLE_SM_IOACT_DISP,
                .passkey = 866744,
            },
        },
    };
    ble_sm_test_util_us_sc_good(&params);

    ble_hs_test_util_assert_mbufs_freed(NULL);
}

/**
 * Secure connections pairing
 * Master: us
 * Pair algorithm: numeric comparison
 * Initiator IO capabilities: 1
 * Responder IO capabilities: 4
 * Bonding: true
 * Initiator address type: 0
 * Responder address type: 0
 * Initiator key distribution: 7
 * Responder key distribution: 5
 */
TEST_CASE_SELF(ble_sm_sc_us_nc_iio1_rio4_b1_iat0_rat0_ik7_rk5)
{
    struct ble_sm_test_params params;

    params = (struct ble_sm_test_params) {
        .init_id_addr = {
            0x01, 0x01, 0x01, 0x07, 0x08, 0x01,
        },
        .resp_id_addr = {
            0xca, 0x61, 0xa0, 0x67, 0x94, 0xe0,
        },
        .pair_req = {
            .io_cap = 0x01,
            .oob_data_flag = 0x00,
            .authreq = 0x0d,
            .max_enc_key_size = 0x10,
            .init_key_dist = 0x07,
            .resp_key_dist = 0x07,
        },
        .pair_rsp = {
            .io_cap = 0x04,
            .oob_data_flag = 0x00,
            .authreq = 0x0d,
            .max_enc_key_size = 0x10,
            .init_key_dist = 0x07,
            .resp_key_dist = 0x05,
        },
        .our_priv_key = {
            0xb1, 0x6b, 0x4f, 0x81, 0xbc, 0xe3, 0x60, 0x9e,
            0x00, 0x20, 0xf1, 0x73, 0x3e, 0xfb, 0xcc, 0x6e,
            0x8c, 0xb6, 0xd2, 0x51, 0xd9, 0x36, 0x8a, 0x6d,
            0xca, 0x8c, 0xd7, 0xbe, 0x96, 0x03, 0xdf, 0xd6,
        },
        .public_key_req = {
            .x = {
                0xe5, 0x0f, 0x02, 0x0a, 0x37, 0x90, 0x94, 0x5a,
                0x06, 0x21, 0xf7, 0xbc, 0xd5, 0xbe, 0xb9, 0x24,
                0x8a, 0x35, 0xfd, 0xf8, 0x5e, 0xe2, 0x70, 0xd5,
                0x5a, 0xe8, 0xe7, 0xdd, 0x13, 0x90, 0xeb, 0xd4,
            },
            .y = {
                0x41, 0xc8, 0x51, 0x1a, 0x25, 0x44, 0x01, 0x53,
                0x42, 0x74, 0x07, 0x9c, 0x18, 0xe6, 0x3b, 0x8a,
                0xce, 0x7a, 0x37, 0x1f, 0x18, 0x5c, 0x02, 0x7c,
                0x67, 0x16, 0xf5, 0x30, 0x2b, 0x31, 0xa9, 0xc7,
            },
        },
        .public_key_rsp = {
            .x = {
                0x7c, 0x27, 0x39, 0xdc, 0x10, 0xfa, 0x57, 0x97,
                0x4a, 0x18, 0xdc, 0x0e, 0xfc, 0x4b, 0xd0, 0xac,
                0x3a, 0xa4, 0x4c, 0x65, 0xb5, 0xbe, 0x7b, 0xd8,
                0xd1, 0xfd, 0x9d, 0xf8, 0xe3, 0x00, 0x4e, 0xf3,
            },
            .y = {
                0xae, 0xfd, 0x8e, 0x93, 0xb4, 0xa9, 0x4d, 0xd3,
                0xb6, 0xbd, 0x4c, 0x1d, 0xc1, 0x7e, 0x67, 0x57,
                0x07, 0x10, 0x4e, 0xd0, 0x0f, 0x23, 0x23, 0xab,
                0x09, 0x86, 0xc3, 0xb9, 0x63, 0x14, 0xe4, 0xe5,
            },
        },
        .confirm_rsp[0] = {
            .value = {
                0x40, 0x35, 0x6d, 0xa1, 0x19, 0xa6, 0x8b, 0xff,
                0x4f, 0x0c, 0x86, 0x7e, 0x95, 0x7c, 0xb8, 0xc1,
            },
        },
        .random_req[0] = {
            .value = {
                0x95, 0x84, 0x0d, 0x2d, 0x7a, 0xfd, 0x5a, 0xa6,
                0xea, 0xfd, 0x7b, 0xf0, 0x68, 0x95, 0xeb, 0x77,
            },
        },
        .random_rsp[0] = {
            .value = {
                0x25, 0x41, 0xda, 0xdf, 0xdd, 0xca, 0xcd, 0x2e,
                0x49, 0x79, 0xb0, 0xaa, 0x7a, 0x23, 0x28, 0x7f,
            },
        },
        .dhkey_check_req = {
            .value = {
                0x49, 0xbb, 0x89, 0x9e, 0xa1, 0x10, 0x26, 0x7a,
                0xe7, 0x42, 0x51, 0xcd, 0x1f, 0x3b, 0x22, 0x1d,
            },
        },
        .dhkey_check_rsp = {
            .value = {
                0x59, 0x51, 0xc8, 0x7b, 0x4f, 0xae, 0xfe, 0xb8,
                0x0c, 0x41, 0xe8, 0xe0, 0xf9, 0x4c, 0x2b, 0xc7,
            },
        },
        .sign_info_req = {
            .sig_key = {
                0x37, 0x26, 0xc0, 0x79, 0x59, 0xcd, 0xb7, 0x0f,
                0xa6, 0xd8, 0xe4, 0x02, 0xc9, 0xe6, 0x02, 0x71,
            },
        },
        .id_info_rsp = {
            .irk = {
                0xef, 0x8d, 0xe2, 0x16, 0x4f, 0xec, 0x43, 0x0d,
                0xbf, 0x5b, 0xdd, 0x34, 0xc0, 0x53, 0x1e, 0xb8,
            },
        },
        .id_addr_info_rsp = {
            .addr_type = 0,
            .bd_addr = {
                0x01, 0x01, 0x01, 0x07, 0x08, 0x01,
            },
        },
        .sign_info_rsp = {
            .sig_key = {
                0x45, 0x69, 0x05, 0xe3, 0x0c, 0x9e, 0x01, 0xb3,
                0xe8, 0xea, 0xa0, 0x5b, 0x70, 0xd9, 0x62, 0x0e,
            },
        },
        .ltk = {
            0xf5, 0x60, 0x02, 0x97, 0x2f, 0xbb, 0x3c, 0xe9,
            0x97, 0xd7, 0xd5, 0x58, 0x04, 0x96, 0xa6, 0xe7,
        },
        .pair_alg = BLE_SM_PAIR_ALG_NUMCMP,
        .authenticated = 1,
        .passkey_info = {
            .passkey = {
                .action = BLE_SM_IOACT_NUMCMP,
                .numcmp_accept = 1,
            },
            .exp_numcmp = 344302,
        },
    };
    ble_sm_test_util_us_sc_good(&params);

    ble_hs_test_util_assert_mbufs_freed(NULL);
}

/**
 * Secure connections pairing
 * Master: peer
 * Pair algorithm: just works
 * Initiator IO capabilities: 3
 * Responder IO capabilities: 3
 * Bonding: true
 * Initiator address type: BLE_OWN_ADDR_RPA_PUBLIC_DEFAULT
 * Responder address type: BLE_ADDR_PUBLIC_ID
 * Initiator key distribution: 7
 * Responder key distribution: 7
 */
TEST_CASE_SELF(ble_sm_sc_peer_jw_iio3_rio3_b1_iat2_rat2_ik7_rk7)
{
    struct ble_sm_test_params params;

    params = (struct ble_sm_test_params) {
        .init_addr_type = BLE_OWN_ADDR_RPA_PUBLIC_DEFAULT,
        .init_id_addr = {
            0x06, 0x05, 0x04, 0x03, 0x02, 0x01,
        },
        .init_rpa = {
            0xd0, 0x8e, 0xf7, 0x42, 0x8c, 0x69,
        },
        .resp_addr_type = BLE_ADDR_PUBLIC_ID,
        .resp_id_addr = {
            0x0c, 0x0b, 0x0a, 0x09, 0x08, 0x07,
        },
        .resp_rpa = {
            0x1a, 0x6e, 0x83, 0x55, 0x5b, 0x5a,
        },
        .pair_req = {
            .io_cap = 0x03,
            .oob_data_flag = 0x00,
            .authreq = 0x0d,
            .max_enc_key_size = 0x10,
            .init_key_dist = 0x07,
            .resp_key_dist = 0x07,
        },
        .pair_rsp = {
            .io_cap = 0x03,
            .oob_data_flag = 0x00,
            .authreq = 0x0d,
            .max_enc_key_size = 0x10,
            .init_key_dist = 0x07,
            .resp_key_dist = 0x07,
        },
        .our_priv_key = {
            0xc5, 0x04, 0xc5, 0xf9, 0x28, 0x95, 0x78, 0x17,
            0xd5, 0x97, 0x1d, 0x01, 0xbb, 0x2c, 0xcf, 0x77,
            0x5c, 0x70, 0x52, 0xc6, 0x5e, 0x33, 0x2e, 0xe7,
            0x79, 0x58, 0xc8, 0xf1, 0xc2, 0x2d, 0xb0, 0x61,
        },
        .public_key_req = {
            .x = {
                0x54, 0xd9, 0x8f, 0xeb, 0xc1, 0xbb, 0xe6, 0x74,
                0x8a, 0x55, 0x3a, 0x80, 0x0e, 0xef, 0x90, 0xc9,
                0xab, 0x79, 0x12, 0x88, 0x97, 0xd9, 0x1c, 0x62,
                0x0d, 0x26, 0x43, 0x7d, 0x25, 0x86, 0x79, 0xc7,
            },
            .y = {
                0x07, 0x33, 0x91, 0x40, 0xde, 0x25, 0xb4, 0x3d,
                0x81, 0x2f, 0xd2, 0x41, 0x98, 0xe7, 0xaf, 0x0f,
                0x5f, 0x17, 0x85, 0x1f, 0x75, 0x6e, 0xf4, 0x0e,
                0x05, 0x19, 0x7f, 0x03, 0x9b, 0xf4, 0x41, 0x23,
            },
        },
        .public_key_rsp = {
            .x = {
                0x1d, 0x44, 0x66, 0x0d, 0x3a, 0x03, 0x71, 0x17,
                0xb3, 0x10, 0x2e, 0xf0, 0xd3, 0xf8, 0xa2, 0x6c,
                0x1f, 0xfc, 0xbf, 0x02, 0x62, 0x6b, 0x11, 0x5a,
                0x76, 0x5b, 0x30, 0x20, 0xb1, 0xef, 0xb3, 0x76,
            },
            .y = {
                0xf1, 0x23, 0x63, 0x75, 0xfc, 0xb6, 0xc9, 0x32,
                0xa4, 0x36, 0xbe, 0x18, 0xa0, 0x7d, 0x0b, 0x16,
                0x65, 0x24, 0xd0, 0xe3, 0x74, 0x1b, 0x34, 0x1a,
                0xf9, 0xe2, 0xcb, 0x30, 0x93, 0x88, 0xd7, 0xfa,
            },
        },
        .confirm_rsp[0] = {
            .value = {
                0x1e, 0x07, 0x87, 0xb2, 0x54, 0x3a, 0x44, 0x6b,
                0x97, 0x45, 0xa7, 0xa2, 0x36, 0xf4, 0x10, 0x42,
            },
        },
        .random_req[0] = {
            .value = {
                0x99, 0xc4, 0xdf, 0x4a, 0x2f, 0x14, 0xd8, 0x11,
                0xd3, 0x93, 0x53, 0xac, 0x64, 0xc8, 0x67, 0xe6,
            },
        },
        .random_rsp[0] = {
            .value = {
                0xc5, 0xb0, 0xf5, 0x2a, 0x65, 0x77, 0x05, 0xb8,
                0xf7, 0x5b, 0xad, 0x4e, 0xa9, 0x9e, 0x79, 0x98,
            },
        },
        .dhkey_check_req = {
            .value = {
                0xbb, 0x44, 0x9b, 0x1b, 0xcd, 0xfc, 0xdf, 0xff,
                0xbb, 0x34, 0xb7, 0x3b, 0x3e, 0x30, 0xa1, 0x6e,
            },
        },
        .dhkey_check_rsp = {
            .value = {
                0x58, 0x8f, 0xbe, 0xa2, 0x5f, 0xe3, 0x0a, 0xbc,
                0x17, 0x0f, 0x3b, 0x23, 0x27, 0xa5, 0xfb, 0x25,
            },
        },
        .id_info_req = {
            .irk = {
                0x00, 0x11, 0x22, 0x33, 0x44, 0x55, 0x66, 0x77,
                0x88, 0x99, 0xaa, 0xbb, 0xcc, 0xdd, 0xee, 0xff,
            },
        },
        .id_addr_info_req = {
            .addr_type = 0,
            .bd_addr = {
                0x0c, 0x0b, 0x0a, 0x09, 0x08, 0x07,
            },
        },
        .sign_info_req = {
            .sig_key = {
                0xd3, 0x46, 0x86, 0xf7, 0xeb, 0x19, 0x0a, 0x18,
                0x5a, 0xb2, 0xd0, 0x5b, 0x0f, 0x03, 0x64, 0x01,
            },
        },
        .id_info_rsp = {
            .irk = {
                0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
                0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f,
            },
        },
        .id_addr_info_rsp = {
            .addr_type = 0,
            .bd_addr = {
                0x06, 0x05, 0x04, 0x03, 0x02, 0x01,
            },
        },
        .sign_info_rsp = {
            .sig_key = {
                0x84, 0x91, 0x5d, 0x89, 0xf6, 0xf0, 0x01, 0x65,
                0xed, 0xa9, 0xcc, 0x9b, 0xa4, 0xd4, 0x97, 0x86,
            },
        },
        .ltk = {
            0x4b, 0xb6, 0x1d, 0xd2, 0xba, 0xa4, 0x94, 0xe5,
            0x78, 0xde, 0xee, 0x47, 0x7a, 0x95, 0x91, 0x1c,
        },
        .pair_alg = BLE_SM_PAIR_ALG_JW,
        .authenticated = 0,
        .passkey_info = {
            .passkey = {
                .action = BLE_SM_IOACT_NONE,
            },
        },
    };
    ble_sm_test_util_peer_sc_good(&params);

    ble_hs_test_util_assert_mbufs_freed(NULL);
}

/**
 * Secure connections pairing
 * Master: peer
 * Pair algorithm: numeric comparison
 * Initiator IO capabilities: 1
 * Responder IO capabilities: 1
 * Bonding: true
 * Initiator address type: BLE_OWN_ADDR_RPA_PUBLIC_DEFAULT
 * Responder address type: BLE_ADDR_PUBLIC_ID
 * Initiator key distribution: 3
 * Responder key distribution: 3
 */
TEST_CASE_SELF(ble_sm_sc_peer_nc_iio1_rio1_b1_iat2_rat2_ik3_rk3)
{
    struct ble_sm_test_params params;

    params = (struct ble_sm_test_params) {
        .init_addr_type = BLE_OWN_ADDR_RPA_PUBLIC_DEFAULT,
        .init_id_addr = {
            0x06, 0x05, 0x04, 0x03, 0x02, 0x01,
        },
        .init_rpa = {
            0xc5, 0xf3, 0x5d, 0x83, 0xcd, 0x4a,
        },
        .resp_addr_type = BLE_ADDR_PUBLIC_ID,
        .resp_id_addr = {
            0x0c, 0x0b, 0x0a, 0x09, 0x08, 0x07,
        },
        .resp_rpa = {
            0x9f, 0x56, 0x57, 0x5e, 0x12, 0x65,
        },
        .pair_req = {
            .io_cap = 0x01,
            .oob_data_flag = 0x00,
            .authreq = 0x0d,
            .max_enc_key_size = 0x10,
            .init_key_dist = 0x03,
            .resp_key_dist = 0x03,
        },
        .pair_rsp = {
            .io_cap = 0x01,
            .oob_data_flag = 0x00,
            .authreq = 0x0d,
            .max_enc_key_size = 0x10,
            .init_key_dist = 0x03,
            .resp_key_dist = 0x03,
        },
        .our_priv_key = {
            0xc5, 0x04, 0xc5, 0xf9, 0x28, 0x95, 0x78, 0x17,
            0xd5, 0x97, 0x1d, 0x01, 0xbb, 0x2c, 0xcf, 0x77,
            0x5c, 0x70, 0x52, 0xc6, 0x5e, 0x33, 0x2e, 0xe7,
            0x79, 0x58, 0xc8, 0xf1, 0xc2, 0x2d, 0xb0, 0x61,
        },
        .public_key_req = {
            .x = {
                0x54, 0xd9, 0x8f, 0xeb, 0xc1, 0xbb, 0xe6, 0x74,
                0x8a, 0x55, 0x3a, 0x80, 0x0e, 0xef, 0x90, 0xc9,
                0xab, 0x79, 0x12, 0x88, 0x97, 0xd9, 0x1c, 0x62,
                0x0d, 0x26, 0x43, 0x7d, 0x25, 0x86, 0x79, 0xc7,
            },
            .y = {
                0x07, 0x33, 0x91, 0x40, 0xde, 0x25, 0xb4, 0x3d,
                0x81, 0x2f, 0xd2, 0x41, 0x98, 0xe7, 0xaf, 0x0f,
                0x5f, 0x17, 0x85, 0x1f, 0x75, 0x6e, 0xf4, 0x0e,
                0x05, 0x19, 0x7f, 0x03, 0x9b, 0xf4, 0x41, 0x23,
            },
        },
        .public_key_rsp = {
            .x = {
                0x1d, 0x44, 0x66, 0x0d, 0x3a, 0x03, 0x71, 0x17,
                0xb3, 0x10, 0x2e, 0xf0, 0xd3, 0xf8, 0xa2, 0x6c,
                0x1f, 0xfc, 0xbf, 0x02, 0x62, 0x6b, 0x11, 0x5a,
                0x76, 0x5b, 0x30, 0x20, 0xb1, 0xef, 0xb3, 0x76,
            },
            .y = {
                0xf1, 0x23, 0x63, 0x75, 0xfc, 0xb6, 0xc9, 0x32,
                0xa4, 0x36, 0xbe, 0x18, 0xa0, 0x7d, 0x0b, 0x16,
                0x65, 0x24, 0xd0, 0xe3, 0x74, 0x1b, 0x34, 0x1a,
                0xf9, 0xe2, 0xcb, 0x30, 0x93, 0x88, 0xd7, 0xfa,
            },
        },
        .confirm_rsp[0] = {
            .value = {
                0x39, 0xba, 0x86, 0x47, 0x06, 0x87, 0x14, 0xe4,
                0x5c, 0x82, 0xe9, 0x6a, 0x80, 0xca, 0x87, 0xcd,
            },
        },
        .random_req[0] = {
            .value = {
                0xce, 0xe2, 0xa3, 0x29, 0x8a, 0xc6, 0x76, 0x1d,
                0xa2, 0xfd, 0xe0, 0x7f, 0x8c, 0xbe, 0xf8, 0x1d,
            },
        },
        .random_rsp[0] = {
            .value = {
                0x3d, 0xac, 0xf0, 0xfe, 0x7c, 0x78, 0x73, 0x03,
                0xe2, 0xb6, 0x59, 0x7e, 0x80, 0xb4, 0x69, 0x07,
            },
        },
        .dhkey_check_req = {
            .value = {
                0xaa, 0x95, 0x9f, 0x33, 0x32, 0xa1, 0xbd, 0xf9,
                0xef, 0xb9, 0x3d, 0xfb, 0x08, 0xd1, 0x28, 0xa0,
            },
        },
        .dhkey_check_rsp = {
            .value = {
                0x3c, 0x10, 0x17, 0x76, 0x55, 0x65, 0x6f, 0x14,
                0xfa, 0x80, 0xd3, 0x52, 0x04, 0x82, 0xe2, 0xf7,
            },
        },
        .id_info_req = {
            .irk = {
                0xd4, 0x66, 0x94, 0xc9, 0x96, 0xd0, 0x28, 0x96,
                0x1c, 0xa1, 0x3b, 0xf7, 0x15, 0x95, 0x95, 0x43,
            },
        },
        .id_addr_info_req = {
            .addr_type = 0,
            .bd_addr = {
                0x0c, 0x0b, 0x0a, 0x09, 0x08, 0x07,
            },
        },
        .id_info_rsp = {
            .irk = {
                0xb7, 0x98, 0xac, 0x85, 0xc4, 0x0a, 0x69, 0x8d,
                0xa6, 0xaf, 0xf3, 0x1f, 0x63, 0x3c, 0xf2, 0x33,
            },
        },
        .id_addr_info_rsp = {
            .addr_type = 0,
            .bd_addr = {
                0x06, 0x05, 0x04, 0x03, 0x02, 0x01,
            },
        },
        .ltk = {
            0x95, 0x46, 0xe6, 0x8e, 0x52, 0xcc, 0x05, 0xca,
            0xf4, 0x59, 0x57, 0x54, 0x8c, 0x0d, 0x51, 0xfc,
        },
        .pair_alg = BLE_SM_PAIR_ALG_NUMCMP,
        .authenticated = 1,
        .passkey_info = {
            .passkey = {
                .action = BLE_SM_IOACT_NUMCMP,
                .numcmp_accept = 1,
            },
            .exp_numcmp = 70210,
        },
    };
    ble_sm_test_util_peer_sc_good(&params);

    ble_hs_test_util_assert_mbufs_freed(NULL);
}

/**
 * Secure connections pairing
 * Master: peer
 * Pair algorithm: passkey entry
 * Initiator IO capabilities: 2
 * Responder IO capabilities: 0
 * Bonding: true
 * Initiator address type: BLE_OWN_ADDR_RPA_PUBLIC_DEFAULT
 * Responder address type: BLE_ADDR_PUBLIC_ID
 * Initiator key distribution: 7
 * Responder key distribution: 3
 */
TEST_CASE_SELF(ble_sm_sc_peer_pk_iio2_rio0_b1_iat2_rat2_ik7_rk3)
{
    struct ble_sm_test_params params;

    params = (struct ble_sm_test_params) {
        .init_addr_type = BLE_OWN_ADDR_RPA_PUBLIC_DEFAULT,
        .init_id_addr = {
            0x06, 0x05, 0x04, 0x03, 0x02, 0x01,
        },
        .init_rpa = {
            0x6e, 0x56, 0x09, 0xef, 0x1e, 0x76,
        },
        .resp_addr_type = BLE_ADDR_PUBLIC_ID,
        .resp_id_addr = {
            0x0c, 0x0b, 0x0a, 0x09, 0x08, 0x07,
        },
        .resp_rpa = {
            0xb5, 0x29, 0xdf, 0xb4, 0x9b, 0x62,
        },
        .pair_req = {
            .io_cap = 0x02,
            .oob_data_flag = 0x00,
            .authreq = 0x0d,
            .max_enc_key_size = 0x10,
            .init_key_dist = 0x07,
            .resp_key_dist = 0x03,
        },
        .pair_rsp = {
            .io_cap = 0x00,
            .oob_data_flag = 0x00,
            .authreq = 0x0d,
            .max_enc_key_size = 0x10,
            .init_key_dist = 0x07,
            .resp_key_dist = 0x03,
        },
        .our_priv_key = {
            0xc5, 0x04, 0xc5, 0xf9, 0x28, 0x95, 0x78, 0x17,
            0xd5, 0x97, 0x1d, 0x01, 0xbb, 0x2c, 0xcf, 0x77,
            0x5c, 0x70, 0x52, 0xc6, 0x5e, 0x33, 0x2e, 0xe7,
            0x79, 0x58, 0xc8, 0xf1, 0xc2, 0x2d, 0xb0, 0x61,
        },
        .public_key_req = {
            .x = {
                0x54, 0xd9, 0x8f, 0xeb, 0xc1, 0xbb, 0xe6, 0x74,
                0x8a, 0x55, 0x3a, 0x80, 0x0e, 0xef, 0x90, 0xc9,
                0xab, 0x79, 0x12, 0x88, 0x97, 0xd9, 0x1c, 0x62,
                0x0d, 0x26, 0x43, 0x7d, 0x25, 0x86, 0x79, 0xc7,
            },
            .y = {
                0x07, 0x33, 0x91, 0x40, 0xde, 0x25, 0xb4, 0x3d,
                0x81, 0x2f, 0xd2, 0x41, 0x98, 0xe7, 0xaf, 0x0f,
                0x5f, 0x17, 0x85, 0x1f, 0x75, 0x6e, 0xf4, 0x0e,
                0x05, 0x19, 0x7f, 0x03, 0x9b, 0xf4, 0x41, 0x23,
            },
        },
        .public_key_rsp = {
            .x = {
                0x1d, 0x44, 0x66, 0x0d, 0x3a, 0x03, 0x71, 0x17,
                0xb3, 0x10, 0x2e, 0xf0, 0xd3, 0xf8, 0xa2, 0x6c,
                0x1f, 0xfc, 0xbf, 0x02, 0x62, 0x6b, 0x11, 0x5a,
                0x76, 0x5b, 0x30, 0x20, 0xb1, 0xef, 0xb3, 0x76,
            },
            .y = {
                0xf1, 0x23, 0x63, 0x75, 0xfc, 0xb6, 0xc9, 0x32,
                0xa4, 0x36, 0xbe, 0x18, 0xa0, 0x7d, 0x0b, 0x16,
                0x65, 0x24, 0xd0, 0xe3, 0x74, 0x1b, 0x34, 0x1a,
                0xf9, 0xe2, 0xcb, 0x30, 0x93, 0x88, 0xd7, 0xfa,
            },
        },
        .confirm_req[0] = {
            .value = {
                0x12, 0xe3, 0x01, 0xd0, 0x30, 0x59, 0xca, 0xd9,
                0x78, 0x0b, 0x45, 0x73, 0xb1, 0x7a, 0x4d, 0xca,
            },
        },
        .confirm_rsp[0] = {
            .value = {
                0x47, 0x68, 0x16, 0x24, 0xd4, 0x07, 0x60, 0x6c,
                0xa5, 0x47, 0x6f, 0x05, 0x78, 0x71, 0x3e, 0xa8,
            },
        },
        .random_req[0] = {
            .value = {
                0x2a, 0x29, 0xa8, 0xef, 0x0b, 0x70, 0x5f, 0x1b,
                0x81, 0x4d, 0x97, 0xff, 0xfb, 0x7f, 0x30, 0x90,
            },
        },
        .random_rsp[0] = {
            .value = {
                0x12, 0x9e, 0x1d, 0x12, 0x11, 0x44, 0x36, 0x74,
                0xa3, 0x0c, 0xea, 0x36, 0x4d, 0xdf, 0x2d, 0x5d,
            },
        },
        .confirm_req[1] = {
            .value = {
                0x4d, 0x6a, 0x32, 0xfe, 0xe2, 0xa0, 0xdd, 0x92,
                0x60, 0x5c, 0x82, 0x7f, 0xa6, 0xa6, 0x24, 0xd6,
            },
        },
        .confirm_rsp[1] = {
            .value = {
                0xd5, 0x3e, 0xa7, 0xa0, 0xbf, 0x39, 0x8e, 0xfe,
                0xfd, 0x73, 0x47, 0x4c, 0x92, 0x8b, 0x74, 0x06,
            },
        },
        .random_req[1] = {
            .value = {
                0xc1, 0x88, 0xdf, 0xb0, 0x99, 0xbb, 0xbf, 0xed,
                0xdc, 0x40, 0x66, 0x55, 0xbe, 0x91, 0x56, 0x9a,
            },
        },
        .random_rsp[1] = {
            .value = {
                0xed, 0xed, 0x9a, 0x61, 0xb8, 0x21, 0x03, 0x77,
                0xa6, 0xcf, 0x34, 0x65, 0x8c, 0x18, 0x82, 0x9f,
            },
        },
        .confirm_req[2] = {
            .value = {
                0xdb, 0xea, 0x94, 0x29, 0xe4, 0x44, 0x7d, 0x7b,
                0xd3, 0x16, 0x81, 0x8e, 0xaf, 0xe6, 0x9c, 0x85,
            },
        },
        .confirm_rsp[2] = {
            .value = {
                0x3f, 0xdd, 0x54, 0x76, 0xab, 0x45, 0x7f, 0x53,
                0x64, 0x6b, 0x37, 0xa6, 0xc7, 0xc6, 0x4a, 0x73,
            },
        },
        .random_req[2] = {
            .value = {
                0x5a, 0xf1, 0xfb, 0xde, 0xb3, 0xbe, 0x6e, 0xac,
                0x68, 0x51, 0x47, 0x8e, 0x0b, 0xcd, 0xc1, 0xa0,
            },
        },
        .random_rsp[2] = {
            .value = {
                0x29, 0x0f, 0x5e, 0x83, 0x87, 0xca, 0xd3, 0x21,
                0xa7, 0x7e, 0x3d, 0x78, 0x47, 0x54, 0xf8, 0xe4,
            },
        },
        .confirm_req[3] = {
            .value = {
                0xca, 0x3e, 0xd5, 0xe3, 0x59, 0xb0, 0x5d, 0x1e,
                0x0f, 0x4c, 0x95, 0x0f, 0x6a, 0x72, 0xcf, 0x25,
            },
        },
        .confirm_rsp[3] = {
            .value = {
                0x2f, 0x4d, 0x06, 0x40, 0x09, 0x68, 0x68, 0x45,
                0x87, 0x79, 0x78, 0x48, 0xda, 0xe4, 0xf5, 0xae,
            },
        },
        .random_req[3] = {
            .value = {
                0x63, 0x5a, 0xee, 0x91, 0xe4, 0xf8, 0xe8, 0x69,
                0xd1, 0x46, 0x18, 0x0d, 0xd2, 0x94, 0xd8, 0x20,
            },
        },
        .random_rsp[3] = {
            .value = {
                0x76, 0x36, 0xf5, 0xc2, 0x41, 0xb6, 0x3c, 0x1f,
                0x36, 0x19, 0x58, 0xce, 0x8f, 0x41, 0xeb, 0x8c,
            },
        },
        .confirm_req[4] = {
            .value = {
                0x76, 0xfd, 0x84, 0x0f, 0x0f, 0x58, 0x70, 0x45,
                0x41, 0x33, 0x5d, 0xce, 0xe5, 0xe2, 0x2f, 0x83,
            },
        },
        .confirm_rsp[4] = {
            .value = {
                0x87, 0xcf, 0xdf, 0xa5, 0x60, 0x82, 0x4f, 0x09,
                0x4c, 0x50, 0x24, 0xba, 0x91, 0x96, 0x0d, 0x65,
            },
        },
        .random_req[4] = {
            .value = {
                0x67, 0xdb, 0x73, 0x1e, 0x57, 0x5c, 0xb7, 0x86,
                0xf8, 0xaf, 0x58, 0xd8, 0x0f, 0x97, 0x47, 0xce,
            },
        },
        .random_rsp[4] = {
            .value = {
                0xaa, 0x99, 0x90, 0x05, 0x11, 0xfc, 0xc2, 0xd9,
                0xb8, 0xd6, 0x9d, 0xef, 0x86, 0x10, 0xcf, 0x26,
            },
        },
        .confirm_req[5] = {
            .value = {
                0xfc, 0x22, 0xd9, 0x1f, 0x5f, 0x86, 0x25, 0xe7,
                0x5e, 0x55, 0x48, 0x35, 0xec, 0x32, 0x37, 0x6d,
            },
        },
        .confirm_rsp[5] = {
            .value = {
                0x98, 0xbc, 0x07, 0x72, 0xa2, 0xe7, 0xa7, 0x66,
                0x64, 0xf7, 0x29, 0x3a, 0xaf, 0x52, 0x18, 0x04,
            },
        },
        .random_req[5] = {
            .value = {
                0xd3, 0x36, 0xb9, 0x69, 0x6a, 0x6d, 0x55, 0xbc,
                0x82, 0xdf, 0x1c, 0x04, 0xa7, 0xd5, 0x00, 0x68,
            },
        },
        .random_rsp[5] = {
            .value = {
                0xb9, 0x03, 0xbf, 0xd9, 0x86, 0x5a, 0x1a, 0xb4,
                0xdc, 0xe6, 0x8f, 0x9b, 0xa4, 0xa8, 0x2a, 0x12,
            },
        },
        .confirm_req[6] = {
            .value = {
                0xfe, 0x14, 0xab, 0x1c, 0xfd, 0x36, 0x64, 0x38,
                0xc1, 0xf8, 0xdd, 0xcd, 0xf4, 0x77, 0xa1, 0xb8,
            },
        },
        .confirm_rsp[6] = {
            .value = {
                0x2e, 0x70, 0x54, 0xdc, 0xa6, 0xae, 0xb2, 0xcd,
                0x4a, 0x26, 0x97, 0xf8, 0xbf, 0xb4, 0xb4, 0x52,
            },
        },
        .random_req[6] = {
            .value = {
                0x1e, 0x27, 0x73, 0x94, 0x44, 0xfc, 0xd4, 0x44,
                0xbf, 0x5b, 0x7d, 0x5d, 0x6d, 0x13, 0x68, 0xb1,
            },
        },
        .random_rsp[6] = {
            .value = {
                0xeb, 0xfd, 0x0b, 0xa1, 0x7b, 0xda, 0x61, 0xdc,
                0x6d, 0xe4, 0x3b, 0x51, 0xa7, 0x09, 0x29, 0x6d,
            },
        },
        .confirm_req[7] = {
            .value = {
                0x38, 0x2b, 0x23, 0xb9, 0x18, 0x2d, 0xb9, 0x0b,
                0xe7, 0x4d, 0x20, 0x83, 0xab, 0x17, 0xfd, 0x88,
            },
        },
        .confirm_rsp[7] = {
            .value = {
                0x65, 0x60, 0x85, 0xef, 0x0e, 0x9a, 0x23, 0x96,
                0xe7, 0xa9, 0xee, 0xba, 0x9e, 0x48, 0xb9, 0x1c,
            },
        },
        .random_req[7] = {
            .value = {
                0x8b, 0xa8, 0x7a, 0x33, 0x15, 0x1e, 0xa7, 0x78,
                0x27, 0x01, 0x3e, 0x90, 0x43, 0x47, 0x5a, 0x9d,
            },
        },
        .random_rsp[7] = {
            .value = {
                0x76, 0xf1, 0x21, 0x67, 0x94, 0x20, 0x6f, 0xc7,
                0x84, 0xc8, 0xdb, 0x07, 0xdb, 0x77, 0xdd, 0x50,
            },
        },
        .confirm_req[8] = {
            .value = {
                0x4e, 0x7f, 0x83, 0x8e, 0xa6, 0x28, 0xaa, 0x46,
                0xa2, 0x69, 0x95, 0x3b, 0xf0, 0x71, 0x14, 0x24,
            },
        },
        .confirm_rsp[8] = {
            .value = {
                0x93, 0x0b, 0x4d, 0xbe, 0x49, 0x36, 0xa0, 0x26,
                0xe9, 0x18, 0x4e, 0xc8, 0x19, 0x59, 0xc1, 0x7d,
            },
        },
        .random_req[8] = {
            .value = {
                0x11, 0xa9, 0xce, 0x26, 0x0e, 0x2f, 0x11, 0x0e,
                0xc1, 0xbd, 0x68, 0x80, 0xc8, 0xf8, 0x41, 0x65,
            },
        },
        .random_rsp[8] = {
            .value = {
                0xb6, 0x3d, 0x6b, 0x62, 0xb5, 0x37, 0x31, 0x28,
                0x79, 0xc4, 0xe2, 0x62, 0xbb, 0x63, 0xf9, 0x91,
            },
        },
        .confirm_req[9] = {
            .value = {
                0x5f, 0x55, 0xb5, 0xa4, 0x80, 0xa8, 0x54, 0x47,
                0xa7, 0x79, 0x87, 0x12, 0x2e, 0x44, 0x92, 0x42,
            },
        },
        .confirm_rsp[9] = {
            .value = {
                0x01, 0x69, 0xa2, 0xac, 0xd6, 0x62, 0x8a, 0x64,
                0xa2, 0x0b, 0xd0, 0xb4, 0x0e, 0x68, 0xe0, 0x88,
            },
        },
        .random_req[9] = {
            .value = {
                0x75, 0x1e, 0x56, 0xd0, 0xcb, 0x06, 0xfd, 0x51,
                0x55, 0xae, 0x77, 0xa4, 0xf2, 0xe7, 0x86, 0x3c,
            },
        },
        .random_rsp[9] = {
            .value = {
                0xff, 0xab, 0x8a, 0x7d, 0xb7, 0x40, 0xe5, 0x07,
                0xfe, 0x8f, 0x74, 0xdb, 0x2c, 0x35, 0x35, 0x12,
            },
        },
        .confirm_req[10] = {
            .value = {
                0x1f, 0x2a, 0xed, 0xcd, 0x6b, 0x87, 0xea, 0xa2,
                0xf8, 0xd8, 0xad, 0x04, 0x23, 0xc7, 0x5d, 0x47,
            },
        },
        .confirm_rsp[10] = {
            .value = {
                0x5b, 0x18, 0x2d, 0x96, 0x3b, 0xf6, 0xdc, 0x82,
                0x3b, 0xfa, 0xc9, 0x81, 0xc7, 0x33, 0xa0, 0x07,
            },
        },
        .random_req[10] = {
            .value = {
                0xd1, 0x3a, 0x82, 0xce, 0x31, 0x75, 0xa2, 0xbf,
                0x6f, 0x12, 0xf2, 0xac, 0xf6, 0xcc, 0xea, 0x34,
            },
        },
        .random_rsp[10] = {
            .value = {
                0xcf, 0x11, 0x3d, 0x44, 0x10, 0x0d, 0x26, 0x32,
                0xa5, 0x61, 0x13, 0xfd, 0xb8, 0xed, 0x31, 0x53,
            },
        },
        .confirm_req[11] = {
            .value = {
                0x67, 0x14, 0x8a, 0xf6, 0xc8, 0xb8, 0x73, 0x6b,
                0xb2, 0xec, 0xa9, 0x61, 0xaa, 0xc0, 0xc9, 0x28,
            },
        },
        .confirm_rsp[11] = {
            .value = {
                0xa5, 0xbf, 0x00, 0x07, 0x48, 0xff, 0x30, 0x36,
                0x20, 0x83, 0xd7, 0xd6, 0xd0, 0x90, 0x46, 0x03,
            },
        },
        .random_req[11] = {
            .value = {
                0x75, 0x99, 0x9a, 0xa3, 0xad, 0x9a, 0xe5, 0x9d,
                0x2f, 0x21, 0xdb, 0x72, 0x2f, 0xaf, 0xb8, 0x79,
            },
        },
        .random_rsp[11] = {
            .value = {
                0xa3, 0xb7, 0xb7, 0x46, 0x39, 0x99, 0xc2, 0x82,
                0xe9, 0x31, 0x8d, 0xc2, 0x28, 0x1b, 0x86, 0x91,
            },
        },
        .confirm_req[12] = {
            .value = {
                0x46, 0x2f, 0xc8, 0x0e, 0x2c, 0x70, 0x3a, 0xdb,
                0x25, 0x2f, 0xce, 0xe6, 0x15, 0x1f, 0x9a, 0x06,
            },
        },
        .confirm_rsp[12] = {
            .value = {
                0x9a, 0xa4, 0xe0, 0x03, 0x3a, 0xb5, 0x43, 0x75,
                0x8e, 0x93, 0x35, 0x25, 0xe6, 0x5e, 0x9d, 0x7f,
            },
        },
        .random_req[12] = {
            .value = {
                0x1f, 0x01, 0x32, 0x56, 0x64, 0x45, 0xc5, 0x20,
                0xd4, 0xad, 0x13, 0x8f, 0xbe, 0x82, 0xc8, 0x01,
            },
        },
        .random_rsp[12] = {
            .value = {
                0xd4, 0x3f, 0xa4, 0xc9, 0xe9, 0x2e, 0x62, 0x77,
                0x4e, 0x21, 0x55, 0xd8, 0xde, 0x31, 0xf5, 0xea,
            },
        },
        .confirm_req[13] = {
            .value = {
                0x4e, 0x48, 0x88, 0x4e, 0x4f, 0x74, 0x7e, 0xec,
                0x99, 0x5d, 0xb1, 0xcb, 0x84, 0x88, 0x80, 0xe9,
            },
        },
        .confirm_rsp[13] = {
            .value = {
                0x1a, 0x84, 0xfa, 0x2f, 0xd7, 0x3c, 0x5f, 0xee,
                0x3e, 0x81, 0xc0, 0x4b, 0x35, 0x4b, 0x7e, 0x98,
            },
        },
        .random_req[13] = {
            .value = {
                0xe3, 0x3a, 0xc5, 0x2f, 0x9f, 0x91, 0x93, 0xfb,
                0xcb, 0xd8, 0x53, 0x63, 0xab, 0xc4, 0xa5, 0x85,
            },
        },
        .random_rsp[13] = {
            .value = {
                0xa0, 0xcf, 0xad, 0x30, 0x2d, 0xec, 0xea, 0x81,
                0xfd, 0x7f, 0xcf, 0x7c, 0x70, 0xc9, 0x89, 0x7b,
            },
        },
        .confirm_req[14] = {
            .value = {
                0xe1, 0x64, 0x22, 0x19, 0x41, 0x44, 0x37, 0x2b,
                0x92, 0x60, 0xa4, 0x1f, 0xd6, 0x53, 0xe0, 0xa0,
            },
        },
        .confirm_rsp[14] = {
            .value = {
                0x08, 0xfa, 0xa4, 0xf8, 0x04, 0x08, 0xb8, 0x9f,
                0x61, 0xb5, 0x68, 0xaf, 0x31, 0x12, 0x8d, 0x3f,
            },
        },
        .random_req[14] = {
            .value = {
                0xad, 0x76, 0xc3, 0x1a, 0x4c, 0x64, 0x2c, 0x11,
                0x5e, 0x48, 0x6d, 0x41, 0xf5, 0x77, 0xc2, 0x40,
            },
        },
        .random_rsp[14] = {
            .value = {
                0x1b, 0xec, 0x78, 0x2b, 0xd9, 0xbe, 0x93, 0xbd,
                0x0b, 0x03, 0xf1, 0xd8, 0x31, 0xe8, 0x60, 0x67,
            },
        },
        .confirm_req[15] = {
            .value = {
                0x5e, 0x22, 0x44, 0x09, 0x97, 0xf9, 0xc5, 0xc7,
                0x23, 0xc7, 0x74, 0x51, 0xe5, 0x9d, 0x5c, 0xed,
            },
        },
        .confirm_rsp[15] = {
            .value = {
                0xfe, 0xb2, 0x90, 0xa7, 0x06, 0xaf, 0xdd, 0x6a,
                0x83, 0x26, 0x3c, 0x78, 0x66, 0xe0, 0x9d, 0xd9,
            },
        },
        .random_req[15] = {
            .value = {
                0xb2, 0xa0, 0x75, 0x6f, 0x77, 0xc1, 0x0b, 0x4e,
                0x99, 0xfa, 0x9a, 0x02, 0xf6, 0xe4, 0x66, 0x27,
            },
        },
        .random_rsp[15] = {
            .value = {
                0xf9, 0xdd, 0x69, 0xae, 0xc8, 0x66, 0xa9, 0xab,
                0xb8, 0x01, 0x38, 0xc3, 0x2a, 0x6b, 0x94, 0x66,
            },
        },
        .confirm_req[16] = {
            .value = {
                0x17, 0xc9, 0xf7, 0x2d, 0xe6, 0xb7, 0x99, 0x77,
                0x65, 0xf7, 0x62, 0xc8, 0x0d, 0x7d, 0xbd, 0x81,
            },
        },
        .confirm_rsp[16] = {
            .value = {
                0x39, 0xef, 0xbf, 0x39, 0xfa, 0x79, 0xc3, 0x7b,
                0x71, 0x40, 0x3c, 0x1f, 0x67, 0xe5, 0x60, 0xe5,
            },
        },
        .random_req[16] = {
            .value = {
                0x32, 0xab, 0x8b, 0xed, 0x90, 0x04, 0x5e, 0x17,
                0xd2, 0x5e, 0xa8, 0x91, 0xf7, 0x77, 0xe3, 0xd7,
            },
        },
        .random_rsp[16] = {
            .value = {
                0x6c, 0xc7, 0x14, 0x13, 0xdf, 0xfb, 0xc6, 0xed,
                0xa3, 0x9c, 0xa7, 0x90, 0xae, 0x4c, 0x61, 0x47,
            },
        },
        .confirm_req[17] = {
            .value = {
                0xc5, 0x17, 0x07, 0x35, 0x34, 0xbf, 0xc1, 0x4d,
                0xc4, 0x57, 0xc0, 0xd9, 0xfd, 0xe9, 0x10, 0x08,
            },
        },
        .confirm_rsp[17] = {
            .value = {
                0xbb, 0xcf, 0x41, 0xd2, 0x94, 0xea, 0xbe, 0x2f,
                0xde, 0xb2, 0xb4, 0x20, 0x72, 0x1c, 0xf8, 0x35,
            },
        },
        .random_req[17] = {
            .value = {
                0x59, 0x20, 0xb5, 0xdc, 0xaf, 0xc3, 0x8b, 0x32,
                0xe6, 0x40, 0x0f, 0x02, 0x67, 0x45, 0x49, 0x1f,
            },
        },
        .random_rsp[17] = {
            .value = {
                0xf5, 0x95, 0x60, 0x4c, 0x5f, 0x39, 0x54, 0xbf,
                0x62, 0x9e, 0x85, 0xca, 0x31, 0x9a, 0x95, 0xee,
            },
        },
        .confirm_req[18] = {
            .value = {
                0x36, 0x50, 0x78, 0x6b, 0x0f, 0x11, 0xe3, 0xa9,
                0x79, 0x3a, 0xa6, 0x9d, 0xd4, 0x8b, 0x13, 0x3f,
            },
        },
        .confirm_rsp[18] = {
            .value = {
                0xa5, 0x34, 0x5d, 0x5e, 0x43, 0x01, 0xf2, 0xe1,
                0x3f, 0xf2, 0x1c, 0x8b, 0x13, 0xf7, 0x17, 0x3e,
            },
        },
        .random_req[18] = {
            .value = {
                0x77, 0xa1, 0xbe, 0xbf, 0x49, 0xb8, 0x74, 0x73,
                0x47, 0x78, 0x2a, 0xf8, 0x66, 0x6b, 0xff, 0xd2,
            },
        },
        .random_rsp[18] = {
            .value = {
                0xa2, 0x05, 0x69, 0x65, 0x3f, 0xd4, 0xb4, 0xcd,
                0xed, 0x8c, 0x36, 0x6d, 0x51, 0x6a, 0xbb, 0xef,
            },
        },
        .confirm_req[19] = {
            .value = {
                0xda, 0xd8, 0x96, 0xfd, 0x1c, 0x0d, 0x1e, 0x56,
                0xe2, 0x62, 0xed, 0x18, 0x4b, 0xd3, 0x46, 0x48,
            },
        },
        .confirm_rsp[19] = {
            .value = {
                0xeb, 0x79, 0x5e, 0x52, 0x70, 0x25, 0xa7, 0x41,
                0x33, 0xfa, 0xac, 0xd3, 0x27, 0x35, 0xfc, 0x5f,
            },
        },
        .random_req[19] = {
            .value = {
                0xa8, 0x9c, 0xb9, 0xcd, 0x13, 0xb8, 0xdd, 0xd2,
                0x09, 0xd6, 0xc8, 0x12, 0xc3, 0x69, 0x9a, 0x64,
            },
        },
        .random_rsp[19] = {
            .value = {
                0x06, 0xe3, 0x8a, 0xef, 0xe4, 0x42, 0xae, 0x86,
                0xef, 0x58, 0x80, 0xe8, 0xe3, 0xa2, 0x09, 0x44,
            },
        },
        .dhkey_check_req = {
            .value = {
                0x6f, 0xa5, 0x37, 0x06, 0x4a, 0x89, 0x98, 0x39,
                0xf6, 0x69, 0x48, 0x56, 0x17, 0x6d, 0x44, 0x7c,
            },
        },
        .dhkey_check_rsp = {
            .value = {
                0x82, 0x48, 0xd4, 0x9e, 0xb8, 0x3c, 0xb4, 0xdc,
                0x44, 0xcb, 0x19, 0xdb, 0xcb, 0xa2, 0x00, 0x5d,
            },
        },
        .id_info_req = {
            .irk = {
                0x79, 0x12, 0x88, 0x97, 0xd9, 0x1c, 0x62, 0x0d,
                0x26, 0x43, 0x7d, 0x25, 0x86, 0x79, 0xc7, 0x07,
            },
        },
        .id_addr_info_req = {
            .addr_type = 0,
            .bd_addr = {
                0x0c, 0x0b, 0x0a, 0x09, 0x08, 0x07,
            },
        },
        .id_info_rsp = {
            .irk = {
                0xda, 0x6b, 0x27, 0xa0, 0xac, 0x71, 0xf0, 0xc3,
                0x75, 0x51, 0xf6, 0x21, 0x94, 0xec, 0x81, 0x92,
            },
        },
        .id_addr_info_rsp = {
            .addr_type = 0,
            .bd_addr = {
                0x06, 0x05, 0x04, 0x03, 0x02, 0x01,
            },
        },
        .sign_info_rsp = {
            .sig_key = {
                0x49, 0x5b, 0x11, 0xb3, 0x4c, 0x1a, 0x23, 0x5c,
                0x61, 0x4f, 0xe3, 0x08, 0xf9, 0x47, 0x8b, 0xdc,
            },
        },
        .ltk = {
            0x5a, 0x49, 0x28, 0xf0, 0x11, 0x3b, 0x6f, 0x6b,
            0x3a, 0x69, 0x6d, 0xdd, 0xb2, 0xe5, 0xa8, 0x97,
        },
        .pair_alg = BLE_SM_PAIR_ALG_PASSKEY,
        .authenticated = 1,
        .passkey_info = {
            .passkey = {
                .action = BLE_SM_IOACT_DISP,
                .passkey = 4915,
            },
        },
    };
    ble_sm_test_util_peer_sc_good(&params);

    ble_hs_test_util_assert_mbufs_freed(NULL);
}

/**
 * Secure connections pairing
 * Master: us
 * Pair algorithm: just works
 * Initiator IO capabilities: 3
 * Responder IO capabilities: 3
 * Bonding: true
 * Initiator address type: BLE_OWN_ADDR_RPA_PUBLIC_DEFAULT
 * Responder address type: BLE_ADDR_PUBLIC_ID
 * Initiator key distribution: 3
 * Responder key distribution: 3
 */
TEST_CASE_SELF(ble_sm_sc_us_jw_iio3_rio3_b1_iat2_rat2_ik3_rk3)
{
    struct ble_sm_test_params params;

    params = (struct ble_sm_test_params) {
        .init_addr_type = BLE_OWN_ADDR_RPA_PUBLIC_DEFAULT,
        .init_id_addr = {
            0x06, 0x05, 0x04, 0x03, 0x02, 0x01,
        },
        .init_rpa = {
            0x46, 0x85, 0x37, 0x90, 0x86, 0x58,
        },
        .resp_addr_type = BLE_ADDR_PUBLIC_ID,
        .resp_id_addr = {
            0x0c, 0x0b, 0x0a, 0x09, 0x08, 0x07,
        },
        .resp_rpa = {
            0x6d, 0x59, 0x7d, 0xa9, 0x87, 0x74,
        },
        .pair_req = {
            .io_cap = 0x03,
            .oob_data_flag = 0x00,
            .authreq = 0x09,
            .max_enc_key_size = 0x10,
            .init_key_dist = 0x03,
            .resp_key_dist = 0x03,
        },
        .pair_rsp = {
            .io_cap = 0x03,
            .oob_data_flag = 0x00,
            .authreq = 0x09,
            .max_enc_key_size = 0x10,
            .init_key_dist = 0x03,
            .resp_key_dist = 0x03,
        },
        .our_priv_key = {
            0xdb, 0x24, 0x2e, 0x91, 0xda, 0xaa, 0x33, 0x33,
            0x23, 0xa2, 0x1e, 0xbe, 0x06, 0x69, 0xdb, 0xad,
            0xa9, 0x2a, 0x91, 0xb1, 0x24, 0x0a, 0xc7, 0xaf,
            0x50, 0x0c, 0x65, 0x5b, 0x97, 0x1e, 0x12, 0x10,
        },
        .public_key_req = {
            .x = {
                0x54, 0xd9, 0x8f, 0xeb, 0xc1, 0xbb, 0xe6, 0x74,
                0x8a, 0x55, 0x3a, 0x80, 0x0e, 0xef, 0x90, 0xc9,
                0xab, 0x79, 0x12, 0x88, 0x97, 0xd9, 0x1c, 0x62,
                0x0d, 0x26, 0x43, 0x7d, 0x25, 0x86, 0x79, 0xc7,
            },
            .y = {
                0x07, 0x33, 0x91, 0x40, 0xde, 0x25, 0xb4, 0x3d,
                0x81, 0x2f, 0xd2, 0x41, 0x98, 0xe7, 0xaf, 0x0f,
                0x5f, 0x17, 0x85, 0x1f, 0x75, 0x6e, 0xf4, 0x0e,
                0x05, 0x19, 0x7f, 0x03, 0x9b, 0xf4, 0x41, 0x23,
            },
        },
        .public_key_rsp = {
            .x = {
                0x1d, 0x44, 0x66, 0x0d, 0x3a, 0x03, 0x71, 0x17,
                0xb3, 0x10, 0x2e, 0xf0, 0xd3, 0xf8, 0xa2, 0x6c,
                0x1f, 0xfc, 0xbf, 0x02, 0x62, 0x6b, 0x11, 0x5a,
                0x76, 0x5b, 0x30, 0x20, 0xb1, 0xef, 0xb3, 0x76,
            },
            .y = {
                0xf1, 0x23, 0x63, 0x75, 0xfc, 0xb6, 0xc9, 0x32,
                0xa4, 0x36, 0xbe, 0x18, 0xa0, 0x7d, 0x0b, 0x16,
                0x65, 0x24, 0xd0, 0xe3, 0x74, 0x1b, 0x34, 0x1a,
                0xf9, 0xe2, 0xcb, 0x30, 0x93, 0x88, 0xd7, 0xfa,
            },
        },
        .confirm_rsp[0] = {
            .value = {
                0x32, 0x5b, 0xee, 0x46, 0x42, 0x63, 0xca, 0x86,
                0x2d, 0xe7, 0xd2, 0x75, 0x23, 0x7b, 0x4d, 0x59,
            },
        },
        .random_req[0] = {
            .value = {
                0xd4, 0x66, 0x94, 0xc9, 0x96, 0xd0, 0x28, 0x96,
                0x1c, 0xa1, 0x3b, 0xf7, 0x15, 0x95, 0x95, 0x43,
            },
        },
        .random_rsp[0] = {
            .value = {
                0xb7, 0x98, 0xac, 0x85, 0xc4, 0x0a, 0x69, 0x8d,
                0xa6, 0xaf, 0xf3, 0x1f, 0x63, 0x3c, 0xf2, 0x33,
            },
        },
        .dhkey_check_req = {
            .value = {
                0x1a, 0xc7, 0x0b, 0xfe, 0xc0, 0x55, 0xc3, 0xdb,
                0x94, 0x00, 0x89, 0x4f, 0x0e, 0x64, 0x05, 0xcd,
            },
        },
        .dhkey_check_rsp = {
            .value = {
                0xf2, 0x45, 0x41, 0xc0, 0xba, 0x8d, 0x58, 0xec,
                0x61, 0xfb, 0x48, 0x71, 0xb4, 0x0e, 0x7b, 0x19,
            },
        },
        .id_info_req = {
            .irk = {
                0x00, 0x11, 0x22, 0x33, 0x44, 0x55, 0x66, 0x77,
                0x88, 0x99, 0xaa, 0xbb, 0xcc, 0xdd, 0xee, 0xff,
            },
        },
        .id_addr_info_req = {
            .addr_type = 0,
            .bd_addr = {
                0x0c, 0x0b, 0x0a, 0x09, 0x08, 0x07,
            },
        },
        .id_info_rsp = {
            .irk = {
                0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
                0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f,
            },
        },
        .id_addr_info_rsp = {
            .addr_type = 0,
            .bd_addr = {
                0x06, 0x05, 0x04, 0x03, 0x02, 0x01,
            },
        },
        .ltk = {
            0x8b, 0xb6, 0xf6, 0x5a, 0x52, 0x7b, 0xb8, 0xf4,
            0xb8, 0x4c, 0xe7, 0x60, 0x4f, 0x0b, 0x88, 0xfe,
        },
        .pair_alg = BLE_SM_PAIR_ALG_JW,
        .authenticated = 0,
        .passkey_info = {
            .passkey = {
                .action = BLE_SM_IOACT_NONE,
            },
        },
    };
    ble_sm_test_util_us_sc_good(&params);

    ble_hs_test_util_assert_mbufs_freed(NULL);
}

/**
 * Secure connections pairing
 * Master: us
 * Pair algorithm: numeric comparison
 * Initiator IO capabilities: 1
 * Responder IO capabilities: 1
 * Bonding: true
 * Initiator address type: BLE_OWN_ADDR_RPA_PUBLIC_DEFAULT
 * Responder address type: BLE_ADDR_PUBLIC_ID
 * Initiator key distribution: 3
 * Responder key distribution: 3
 */
TEST_CASE_SELF(ble_sm_sc_us_nc_iio1_rio1_b1_iat2_rat2_ik3_rk3)
{
    struct ble_sm_test_params params;

    params = (struct ble_sm_test_params) {
        .init_addr_type = BLE_OWN_ADDR_RPA_PUBLIC_DEFAULT,
        .init_id_addr = {
            0x06, 0x05, 0x04, 0x03, 0x02, 0x01,
        },
        .init_rpa = {
            0xc5, 0xf3, 0x5d, 0x83, 0xcd, 0x4a,
        },
        .resp_addr_type = BLE_ADDR_PUBLIC_ID,
        .resp_id_addr = {
            0x0c, 0x0b, 0x0a, 0x09, 0x08, 0x07,
        },
        .resp_rpa = {
            0x9f, 0x56, 0x57, 0x5e, 0x12, 0x65,
        },
        .pair_req = {
            .io_cap = 0x01,
            .oob_data_flag = 0x00,
            .authreq = 0x0d,
            .max_enc_key_size = 0x10,
            .init_key_dist = 0x03,
            .resp_key_dist = 0x03,
        },
        .pair_rsp = {
            .io_cap = 0x01,
            .oob_data_flag = 0x00,
            .authreq = 0x0d,
            .max_enc_key_size = 0x10,
            .init_key_dist = 0x03,
            .resp_key_dist = 0x03,
        },
        .our_priv_key = {
            0xdb, 0x24, 0x2e, 0x91, 0xda, 0xaa, 0x33, 0x33,
            0x23, 0xa2, 0x1e, 0xbe, 0x06, 0x69, 0xdb, 0xad,
            0xa9, 0x2a, 0x91, 0xb1, 0x24, 0x0a, 0xc7, 0xaf,
            0x50, 0x0c, 0x65, 0x5b, 0x97, 0x1e, 0x12, 0x10,
        },
        .public_key_req = {
            .x = {
                0x54, 0xd9, 0x8f, 0xeb, 0xc1, 0xbb, 0xe6, 0x74,
                0x8a, 0x55, 0x3a, 0x80, 0x0e, 0xef, 0x90, 0xc9,
                0xab, 0x79, 0x12, 0x88, 0x97, 0xd9, 0x1c, 0x62,
                0x0d, 0x26, 0x43, 0x7d, 0x25, 0x86, 0x79, 0xc7,
            },
            .y = {
                0x07, 0x33, 0x91, 0x40, 0xde, 0x25, 0xb4, 0x3d,
                0x81, 0x2f, 0xd2, 0x41, 0x98, 0xe7, 0xaf, 0x0f,
                0x5f, 0x17, 0x85, 0x1f, 0x75, 0x6e, 0xf4, 0x0e,
                0x05, 0x19, 0x7f, 0x03, 0x9b, 0xf4, 0x41, 0x23,
            },
        },
        .public_key_rsp = {
            .x = {
                0x1d, 0x44, 0x66, 0x0d, 0x3a, 0x03, 0x71, 0x17,
                0xb3, 0x10, 0x2e, 0xf0, 0xd3, 0xf8, 0xa2, 0x6c,
                0x1f, 0xfc, 0xbf, 0x02, 0x62, 0x6b, 0x11, 0x5a,
                0x76, 0x5b, 0x30, 0x20, 0xb1, 0xef, 0xb3, 0x76,
            },
            .y = {
                0xf1, 0x23, 0x63, 0x75, 0xfc, 0xb6, 0xc9, 0x32,
                0xa4, 0x36, 0xbe, 0x18, 0xa0, 0x7d, 0x0b, 0x16,
                0x65, 0x24, 0xd0, 0xe3, 0x74, 0x1b, 0x34, 0x1a,
                0xf9, 0xe2, 0xcb, 0x30, 0x93, 0x88, 0xd7, 0xfa,
            },
        },
        .confirm_rsp[0] = {
            .value = {
                0x39, 0xba, 0x86, 0x47, 0x06, 0x87, 0x14, 0xe4,
                0x5c, 0x82, 0xe9, 0x6a, 0x80, 0xca, 0x87, 0xcd,
            },
        },
        .random_req[0] = {
            .value = {
                0xce, 0xe2, 0xa3, 0x29, 0x8a, 0xc6, 0x76, 0x1d,
                0xa2, 0xfd, 0xe0, 0x7f, 0x8c, 0xbe, 0xf8, 0x1d,
            },
        },
        .random_rsp[0] = {
            .value = {
                0x3d, 0xac, 0xf0, 0xfe, 0x7c, 0x78, 0x73, 0x03,
                0xe2, 0xb6, 0x59, 0x7e, 0x80, 0xb4, 0x69, 0x07,
            },
        },
        .dhkey_check_req = {
            .value = {
                0xaa, 0x95, 0x9f, 0x33, 0x32, 0xa1, 0xbd, 0xf9,
                0xef, 0xb9, 0x3d, 0xfb, 0x08, 0xd1, 0x28, 0xa0,
            },
        },
        .dhkey_check_rsp = {
            .value = {
                0x3c, 0x10, 0x17, 0x76, 0x55, 0x65, 0x6f, 0x14,
                0xfa, 0x80, 0xd3, 0x52, 0x04, 0x82, 0xe2, 0xf7,
            },
        },
        .id_info_req = {
            .irk = {
                0xd4, 0x66, 0x94, 0xc9, 0x96, 0xd0, 0x28, 0x96,
                0x1c, 0xa1, 0x3b, 0xf7, 0x15, 0x95, 0x95, 0x43,
            },
        },
        .id_addr_info_req = {
            .addr_type = 0,
            .bd_addr = {
                0x0c, 0x0b, 0x0a, 0x09, 0x08, 0x07,
            },
        },
        .id_info_rsp = {
            .irk = {
                0xb7, 0x98, 0xac, 0x85, 0xc4, 0x0a, 0x69, 0x8d,
                0xa6, 0xaf, 0xf3, 0x1f, 0x63, 0x3c, 0xf2, 0x33,
            },
        },
        .id_addr_info_rsp = {
            .addr_type = 0,
            .bd_addr = {
                0x06, 0x05, 0x04, 0x03, 0x02, 0x01,
            },
        },
        .ltk = {
            0x95, 0x46, 0xe6, 0x8e, 0x52, 0xcc, 0x05, 0xca,
            0xf4, 0x59, 0x57, 0x54, 0x8c, 0x0d, 0x51, 0xfc,
        },
        .pair_alg = BLE_SM_PAIR_ALG_NUMCMP,
        .authenticated = 1,
        .passkey_info = {
            .passkey = {
                .action = BLE_SM_IOACT_NUMCMP,
                .numcmp_accept = 1,
            },
            .exp_numcmp = 70210,
        },
    };
    ble_sm_test_util_us_sc_good(&params);

    ble_hs_test_util_assert_mbufs_freed(NULL);
}

/**
 * Secure connections pairing
 * Master: us
 * Pair algorithm: passkey entry
 * Initiator IO capabilities: 2
 * Responder IO capabilities: 0
 * Bonding: true
 * Initiator address type: BLE_OWN_ADDR_RPA_PUBLIC_DEFAULT
 * Responder address type: BLE_ADDR_PUBLIC_ID
 * Initiator key distribution: 7
 * Responder key distribution: 3
 */
TEST_CASE_SELF(ble_sm_sc_us_pk_iio2_rio0_b1_iat2_rat2_ik7_rk3)
{
    struct ble_sm_test_params params;

    params = (struct ble_sm_test_params) {
        .init_addr_type = BLE_OWN_ADDR_RPA_PUBLIC_DEFAULT,
        .init_id_addr = {
            0x06, 0x05, 0x04, 0x03, 0x02, 0x01,
        },
        .init_rpa = {
            0x6e, 0x56, 0x09, 0xef, 0x1e, 0x76,
        },
        .resp_addr_type = BLE_ADDR_PUBLIC_ID,
        .resp_id_addr = {
            0x0c, 0x0b, 0x0a, 0x09, 0x08, 0x07,
        },
        .resp_rpa = {
            0xb5, 0x29, 0xdf, 0xb4, 0x9b, 0x62,
        },
        .pair_req = {
            .io_cap = 0x02,
            .oob_data_flag = 0x00,
            .authreq = 0x0d,
            .max_enc_key_size = 0x10,
            .init_key_dist = 0x07,
            .resp_key_dist = 0x03,
        },
        .pair_rsp = {
            .io_cap = 0x00,
            .oob_data_flag = 0x00,
            .authreq = 0x0d,
            .max_enc_key_size = 0x10,
            .init_key_dist = 0x07,
            .resp_key_dist = 0x03,
        },
        .our_priv_key = {
            0xdb, 0x24, 0x2e, 0x91, 0xda, 0xaa, 0x33, 0x33,
            0x23, 0xa2, 0x1e, 0xbe, 0x06, 0x69, 0xdb, 0xad,
            0xa9, 0x2a, 0x91, 0xb1, 0x24, 0x0a, 0xc7, 0xaf,
            0x50, 0x0c, 0x65, 0x5b, 0x97, 0x1e, 0x12, 0x10,
        },
        .public_key_req = {
            .x = {
                0x54, 0xd9, 0x8f, 0xeb, 0xc1, 0xbb, 0xe6, 0x74,
                0x8a, 0x55, 0x3a, 0x80, 0x0e, 0xef, 0x90, 0xc9,
                0xab, 0x79, 0x12, 0x88, 0x97, 0xd9, 0x1c, 0x62,
                0x0d, 0x26, 0x43, 0x7d, 0x25, 0x86, 0x79, 0xc7,
            },
            .y = {
                0x07, 0x33, 0x91, 0x40, 0xde, 0x25, 0xb4, 0x3d,
                0x81, 0x2f, 0xd2, 0x41, 0x98, 0xe7, 0xaf, 0x0f,
                0x5f, 0x17, 0x85, 0x1f, 0x75, 0x6e, 0xf4, 0x0e,
                0x05, 0x19, 0x7f, 0x03, 0x9b, 0xf4, 0x41, 0x23,
            },
        },
        .public_key_rsp = {
            .x = {
                0x1d, 0x44, 0x66, 0x0d, 0x3a, 0x03, 0x71, 0x17,
                0xb3, 0x10, 0x2e, 0xf0, 0xd3, 0xf8, 0xa2, 0x6c,
                0x1f, 0xfc, 0xbf, 0x02, 0x62, 0x6b, 0x11, 0x5a,
                0x76, 0x5b, 0x30, 0x20, 0xb1, 0xef, 0xb3, 0x76,
            },
            .y = {
                0xf1, 0x23, 0x63, 0x75, 0xfc, 0xb6, 0xc9, 0x32,
                0xa4, 0x36, 0xbe, 0x18, 0xa0, 0x7d, 0x0b, 0x16,
                0x65, 0x24, 0xd0, 0xe3, 0x74, 0x1b, 0x34, 0x1a,
                0xf9, 0xe2, 0xcb, 0x30, 0x93, 0x88, 0xd7, 0xfa,
            },
        },
        .confirm_req[0] = {
            .value = {
                0x12, 0xe3, 0x01, 0xd0, 0x30, 0x59, 0xca, 0xd9,
                0x78, 0x0b, 0x45, 0x73, 0xb1, 0x7a, 0x4d, 0xca,
            },
        },
        .confirm_rsp[0] = {
            .value = {
                0x47, 0x68, 0x16, 0x24, 0xd4, 0x07, 0x60, 0x6c,
                0xa5, 0x47, 0x6f, 0x05, 0x78, 0x71, 0x3e, 0xa8,
            },
        },
        .random_req[0] = {
            .value = {
                0x2a, 0x29, 0xa8, 0xef, 0x0b, 0x70, 0x5f, 0x1b,
                0x81, 0x4d, 0x97, 0xff, 0xfb, 0x7f, 0x30, 0x90,
            },
        },
        .random_rsp[0] = {
            .value = {
                0x12, 0x9e, 0x1d, 0x12, 0x11, 0x44, 0x36, 0x74,
                0xa3, 0x0c, 0xea, 0x36, 0x4d, 0xdf, 0x2d, 0x5d,
            },
        },
        .confirm_req[1] = {
            .value = {
                0x4d, 0x6a, 0x32, 0xfe, 0xe2, 0xa0, 0xdd, 0x92,
                0x60, 0x5c, 0x82, 0x7f, 0xa6, 0xa6, 0x24, 0xd6,
            },
        },
        .confirm_rsp[1] = {
            .value = {
                0xd5, 0x3e, 0xa7, 0xa0, 0xbf, 0x39, 0x8e, 0xfe,
                0xfd, 0x73, 0x47, 0x4c, 0x92, 0x8b, 0x74, 0x06,
            },
        },
        .random_req[1] = {
            .value = {
                0xc1, 0x88, 0xdf, 0xb0, 0x99, 0xbb, 0xbf, 0xed,
                0xdc, 0x40, 0x66, 0x55, 0xbe, 0x91, 0x56, 0x9a,
            },
        },
        .random_rsp[1] = {
            .value = {
                0xed, 0xed, 0x9a, 0x61, 0xb8, 0x21, 0x03, 0x77,
                0xa6, 0xcf, 0x34, 0x65, 0x8c, 0x18, 0x82, 0x9f,
            },
        },
        .confirm_req[2] = {
            .value = {
                0xdb, 0xea, 0x94, 0x29, 0xe4, 0x44, 0x7d, 0x7b,
                0xd3, 0x16, 0x81, 0x8e, 0xaf, 0xe6, 0x9c, 0x85,
            },
        },
        .confirm_rsp[2] = {
            .value = {
                0x3f, 0xdd, 0x54, 0x76, 0xab, 0x45, 0x7f, 0x53,
                0x64, 0x6b, 0x37, 0xa6, 0xc7, 0xc6, 0x4a, 0x73,
            },
        },
        .random_req[2] = {
            .value = {
                0x5a, 0xf1, 0xfb, 0xde, 0xb3, 0xbe, 0x6e, 0xac,
                0x68, 0x51, 0x47, 0x8e, 0x0b, 0xcd, 0xc1, 0xa0,
            },
        },
        .random_rsp[2] = {
            .value = {
                0x29, 0x0f, 0x5e, 0x83, 0x87, 0xca, 0xd3, 0x21,
                0xa7, 0x7e, 0x3d, 0x78, 0x47, 0x54, 0xf8, 0xe4,
            },
        },
        .confirm_req[3] = {
            .value = {
                0xca, 0x3e, 0xd5, 0xe3, 0x59, 0xb0, 0x5d, 0x1e,
                0x0f, 0x4c, 0x95, 0x0f, 0x6a, 0x72, 0xcf, 0x25,
            },
        },
        .confirm_rsp[3] = {
            .value = {
                0x2f, 0x4d, 0x06, 0x40, 0x09, 0x68, 0x68, 0x45,
                0x87, 0x79, 0x78, 0x48, 0xda, 0xe4, 0xf5, 0xae,
            },
        },
        .random_req[3] = {
            .value = {
                0x63, 0x5a, 0xee, 0x91, 0xe4, 0xf8, 0xe8, 0x69,
                0xd1, 0x46, 0x18, 0x0d, 0xd2, 0x94, 0xd8, 0x20,
            },
        },
        .random_rsp[3] = {
            .value = {
                0x76, 0x36, 0xf5, 0xc2, 0x41, 0xb6, 0x3c, 0x1f,
                0x36, 0x19, 0x58, 0xce, 0x8f, 0x41, 0xeb, 0x8c,
            },
        },
        .confirm_req[4] = {
            .value = {
                0x76, 0xfd, 0x84, 0x0f, 0x0f, 0x58, 0x70, 0x45,
                0x41, 0x33, 0x5d, 0xce, 0xe5, 0xe2, 0x2f, 0x83,
            },
        },
        .confirm_rsp[4] = {
            .value = {
                0x87, 0xcf, 0xdf, 0xa5, 0x60, 0x82, 0x4f, 0x09,
                0x4c, 0x50, 0x24, 0xba, 0x91, 0x96, 0x0d, 0x65,
            },
        },
        .random_req[4] = {
            .value = {
                0x67, 0xdb, 0x73, 0x1e, 0x57, 0x5c, 0xb7, 0x86,
                0xf8, 0xaf, 0x58, 0xd8, 0x0f, 0x97, 0x47, 0xce,
            },
        },
        .random_rsp[4] = {
            .value = {
                0xaa, 0x99, 0x90, 0x05, 0x11, 0xfc, 0xc2, 0xd9,
                0xb8, 0xd6, 0x9d, 0xef, 0x86, 0x10, 0xcf, 0x26,
            },
        },
        .confirm_req[5] = {
            .value = {
                0xfc, 0x22, 0xd9, 0x1f, 0x5f, 0x86, 0x25, 0xe7,
                0x5e, 0x55, 0x48, 0x35, 0xec, 0x32, 0x37, 0x6d,
            },
        },
        .confirm_rsp[5] = {
            .value = {
                0x98, 0xbc, 0x07, 0x72, 0xa2, 0xe7, 0xa7, 0x66,
                0x64, 0xf7, 0x29, 0x3a, 0xaf, 0x52, 0x18, 0x04,
            },
        },
        .random_req[5] = {
            .value = {
                0xd3, 0x36, 0xb9, 0x69, 0x6a, 0x6d, 0x55, 0xbc,
                0x82, 0xdf, 0x1c, 0x04, 0xa7, 0xd5, 0x00, 0x68,
            },
        },
        .random_rsp[5] = {
            .value = {
                0xb9, 0x03, 0xbf, 0xd9, 0x86, 0x5a, 0x1a, 0xb4,
                0xdc, 0xe6, 0x8f, 0x9b, 0xa4, 0xa8, 0x2a, 0x12,
            },
        },
        .confirm_req[6] = {
            .value = {
                0xfe, 0x14, 0xab, 0x1c, 0xfd, 0x36, 0x64, 0x38,
                0xc1, 0xf8, 0xdd, 0xcd, 0xf4, 0x77, 0xa1, 0xb8,
            },
        },
        .confirm_rsp[6] = {
            .value = {
                0x2e, 0x70, 0x54, 0xdc, 0xa6, 0xae, 0xb2, 0xcd,
                0x4a, 0x26, 0x97, 0xf8, 0xbf, 0xb4, 0xb4, 0x52,
            },
        },
        .random_req[6] = {
            .value = {
                0x1e, 0x27, 0x73, 0x94, 0x44, 0xfc, 0xd4, 0x44,
                0xbf, 0x5b, 0x7d, 0x5d, 0x6d, 0x13, 0x68, 0xb1,
            },
        },
        .random_rsp[6] = {
            .value = {
                0xeb, 0xfd, 0x0b, 0xa1, 0x7b, 0xda, 0x61, 0xdc,
                0x6d, 0xe4, 0x3b, 0x51, 0xa7, 0x09, 0x29, 0x6d,
            },
        },
        .confirm_req[7] = {
            .value = {
                0x38, 0x2b, 0x23, 0xb9, 0x18, 0x2d, 0xb9, 0x0b,
                0xe7, 0x4d, 0x20, 0x83, 0xab, 0x17, 0xfd, 0x88,
            },
        },
        .confirm_rsp[7] = {
            .value = {
                0x65, 0x60, 0x85, 0xef, 0x0e, 0x9a, 0x23, 0x96,
                0xe7, 0xa9, 0xee, 0xba, 0x9e, 0x48, 0xb9, 0x1c,
            },
        },
        .random_req[7] = {
            .value = {
                0x8b, 0xa8, 0x7a, 0x33, 0x15, 0x1e, 0xa7, 0x78,
                0x27, 0x01, 0x3e, 0x90, 0x43, 0x47, 0x5a, 0x9d,
            },
        },
        .random_rsp[7] = {
            .value = {
                0x76, 0xf1, 0x21, 0x67, 0x94, 0x20, 0x6f, 0xc7,
                0x84, 0xc8, 0xdb, 0x07, 0xdb, 0x77, 0xdd, 0x50,
            },
        },
        .confirm_req[8] = {
            .value = {
                0x4e, 0x7f, 0x83, 0x8e, 0xa6, 0x28, 0xaa, 0x46,
                0xa2, 0x69, 0x95, 0x3b, 0xf0, 0x71, 0x14, 0x24,
            },
        },
        .confirm_rsp[8] = {
            .value = {
                0x93, 0x0b, 0x4d, 0xbe, 0x49, 0x36, 0xa0, 0x26,
                0xe9, 0x18, 0x4e, 0xc8, 0x19, 0x59, 0xc1, 0x7d,
            },
        },
        .random_req[8] = {
            .value = {
                0x11, 0xa9, 0xce, 0x26, 0x0e, 0x2f, 0x11, 0x0e,
                0xc1, 0xbd, 0x68, 0x80, 0xc8, 0xf8, 0x41, 0x65,
            },
        },
        .random_rsp[8] = {
            .value = {
                0xb6, 0x3d, 0x6b, 0x62, 0xb5, 0x37, 0x31, 0x28,
                0x79, 0xc4, 0xe2, 0x62, 0xbb, 0x63, 0xf9, 0x91,
            },
        },
        .confirm_req[9] = {
            .value = {
                0x5f, 0x55, 0xb5, 0xa4, 0x80, 0xa8, 0x54, 0x47,
                0xa7, 0x79, 0x87, 0x12, 0x2e, 0x44, 0x92, 0x42,
            },
        },
        .confirm_rsp[9] = {
            .value = {
                0x01, 0x69, 0xa2, 0xac, 0xd6, 0x62, 0x8a, 0x64,
                0xa2, 0x0b, 0xd0, 0xb4, 0x0e, 0x68, 0xe0, 0x88,
            },
        },
        .random_req[9] = {
            .value = {
                0x75, 0x1e, 0x56, 0xd0, 0xcb, 0x06, 0xfd, 0x51,
                0x55, 0xae, 0x77, 0xa4, 0xf2, 0xe7, 0x86, 0x3c,
            },
        },
        .random_rsp[9] = {
            .value = {
                0xff, 0xab, 0x8a, 0x7d, 0xb7, 0x40, 0xe5, 0x07,
                0xfe, 0x8f, 0x74, 0xdb, 0x2c, 0x35, 0x35, 0x12,
            },
        },
        .confirm_req[10] = {
            .value = {
                0x1f, 0x2a, 0xed, 0xcd, 0x6b, 0x87, 0xea, 0xa2,
                0xf8, 0xd8, 0xad, 0x04, 0x23, 0xc7, 0x5d, 0x47,
            },
        },
        .confirm_rsp[10] = {
            .value = {
                0x5b, 0x18, 0x2d, 0x96, 0x3b, 0xf6, 0xdc, 0x82,
                0x3b, 0xfa, 0xc9, 0x81, 0xc7, 0x33, 0xa0, 0x07,
            },
        },
        .random_req[10] = {
            .value = {
                0xd1, 0x3a, 0x82, 0xce, 0x31, 0x75, 0xa2, 0xbf,
                0x6f, 0x12, 0xf2, 0xac, 0xf6, 0xcc, 0xea, 0x34,
            },
        },
        .random_rsp[10] = {
            .value = {
                0xcf, 0x11, 0x3d, 0x44, 0x10, 0x0d, 0x26, 0x32,
                0xa5, 0x61, 0x13, 0xfd, 0xb8, 0xed, 0x31, 0x53,
            },
        },
        .confirm_req[11] = {
            .value = {
                0x67, 0x14, 0x8a, 0xf6, 0xc8, 0xb8, 0x73, 0x6b,
                0xb2, 0xec, 0xa9, 0x61, 0xaa, 0xc0, 0xc9, 0x28,
            },
        },
        .confirm_rsp[11] = {
            .value = {
                0xa5, 0xbf, 0x00, 0x07, 0x48, 0xff, 0x30, 0x36,
                0x20, 0x83, 0xd7, 0xd6, 0xd0, 0x90, 0x46, 0x03,
            },
        },
        .random_req[11] = {
            .value = {
                0x75, 0x99, 0x9a, 0xa3, 0xad, 0x9a, 0xe5, 0x9d,
                0x2f, 0x21, 0xdb, 0x72, 0x2f, 0xaf, 0xb8, 0x79,
            },
        },
        .random_rsp[11] = {
            .value = {
                0xa3, 0xb7, 0xb7, 0x46, 0x39, 0x99, 0xc2, 0x82,
                0xe9, 0x31, 0x8d, 0xc2, 0x28, 0x1b, 0x86, 0x91,
            },
        },
        .confirm_req[12] = {
            .value = {
                0x46, 0x2f, 0xc8, 0x0e, 0x2c, 0x70, 0x3a, 0xdb,
                0x25, 0x2f, 0xce, 0xe6, 0x15, 0x1f, 0x9a, 0x06,
            },
        },
        .confirm_rsp[12] = {
            .value = {
                0x9a, 0xa4, 0xe0, 0x03, 0x3a, 0xb5, 0x43, 0x75,
                0x8e, 0x93, 0x35, 0x25, 0xe6, 0x5e, 0x9d, 0x7f,
            },
        },
        .random_req[12] = {
            .value = {
                0x1f, 0x01, 0x32, 0x56, 0x64, 0x45, 0xc5, 0x20,
                0xd4, 0xad, 0x13, 0x8f, 0xbe, 0x82, 0xc8, 0x01,
            },
        },
        .random_rsp[12] = {
            .value = {
                0xd4, 0x3f, 0xa4, 0xc9, 0xe9, 0x2e, 0x62, 0x77,
                0x4e, 0x21, 0x55, 0xd8, 0xde, 0x31, 0xf5, 0xea,
            },
        },
        .confirm_req[13] = {
            .value = {
                0x4e, 0x48, 0x88, 0x4e, 0x4f, 0x74, 0x7e, 0xec,
                0x99, 0x5d, 0xb1, 0xcb, 0x84, 0x88, 0x80, 0xe9,
            },
        },
        .confirm_rsp[13] = {
            .value = {
                0x1a, 0x84, 0xfa, 0x2f, 0xd7, 0x3c, 0x5f, 0xee,
                0x3e, 0x81, 0xc0, 0x4b, 0x35, 0x4b, 0x7e, 0x98,
            },
        },
        .random_req[13] = {
            .value = {
                0xe3, 0x3a, 0xc5, 0x2f, 0x9f, 0x91, 0x93, 0xfb,
                0xcb, 0xd8, 0x53, 0x63, 0xab, 0xc4, 0xa5, 0x85,
            },
        },
        .random_rsp[13] = {
            .value = {
                0xa0, 0xcf, 0xad, 0x30, 0x2d, 0xec, 0xea, 0x81,
                0xfd, 0x7f, 0xcf, 0x7c, 0x70, 0xc9, 0x89, 0x7b,
            },
        },
        .confirm_req[14] = {
            .value = {
                0xe1, 0x64, 0x22, 0x19, 0x41, 0x44, 0x37, 0x2b,
                0x92, 0x60, 0xa4, 0x1f, 0xd6, 0x53, 0xe0, 0xa0,
            },
        },
        .confirm_rsp[14] = {
            .value = {
                0x08, 0xfa, 0xa4, 0xf8, 0x04, 0x08, 0xb8, 0x9f,
                0x61, 0xb5, 0x68, 0xaf, 0x31, 0x12, 0x8d, 0x3f,
            },
        },
        .random_req[14] = {
            .value = {
                0xad, 0x76, 0xc3, 0x1a, 0x4c, 0x64, 0x2c, 0x11,
                0x5e, 0x48, 0x6d, 0x41, 0xf5, 0x77, 0xc2, 0x40,
            },
        },
        .random_rsp[14] = {
            .value = {
                0x1b, 0xec, 0x78, 0x2b, 0xd9, 0xbe, 0x93, 0xbd,
                0x0b, 0x03, 0xf1, 0xd8, 0x31, 0xe8, 0x60, 0x67,
            },
        },
        .confirm_req[15] = {
            .value = {
                0x5e, 0x22, 0x44, 0x09, 0x97, 0xf9, 0xc5, 0xc7,
                0x23, 0xc7, 0x74, 0x51, 0xe5, 0x9d, 0x5c, 0xed,
            },
        },
        .confirm_rsp[15] = {
            .value = {
                0xfe, 0xb2, 0x90, 0xa7, 0x06, 0xaf, 0xdd, 0x6a,
                0x83, 0x26, 0x3c, 0x78, 0x66, 0xe0, 0x9d, 0xd9,
            },
        },
        .random_req[15] = {
            .value = {
                0xb2, 0xa0, 0x75, 0x6f, 0x77, 0xc1, 0x0b, 0x4e,
                0x99, 0xfa, 0x9a, 0x02, 0xf6, 0xe4, 0x66, 0x27,
            },
        },
        .random_rsp[15] = {
            .value = {
                0xf9, 0xdd, 0x69, 0xae, 0xc8, 0x66, 0xa9, 0xab,
                0xb8, 0x01, 0x38, 0xc3, 0x2a, 0x6b, 0x94, 0x66,
            },
        },
        .confirm_req[16] = {
            .value = {
                0x17, 0xc9, 0xf7, 0x2d, 0xe6, 0xb7, 0x99, 0x77,
                0x65, 0xf7, 0x62, 0xc8, 0x0d, 0x7d, 0xbd, 0x81,
            },
        },
        .confirm_rsp[16] = {
            .value = {
                0x39, 0xef, 0xbf, 0x39, 0xfa, 0x79, 0xc3, 0x7b,
                0x71, 0x40, 0x3c, 0x1f, 0x67, 0xe5, 0x60, 0xe5,
            },
        },
        .random_req[16] = {
            .value = {
                0x32, 0xab, 0x8b, 0xed, 0x90, 0x04, 0x5e, 0x17,
                0xd2, 0x5e, 0xa8, 0x91, 0xf7, 0x77, 0xe3, 0xd7,
            },
        },
        .random_rsp[16] = {
            .value = {
                0x6c, 0xc7, 0x14, 0x13, 0xdf, 0xfb, 0xc6, 0xed,
                0xa3, 0x9c, 0xa7, 0x90, 0xae, 0x4c, 0x61, 0x47,
            },
        },
        .confirm_req[17] = {
            .value = {
                0xc5, 0x17, 0x07, 0x35, 0x34, 0xbf, 0xc1, 0x4d,
                0xc4, 0x57, 0xc0, 0xd9, 0xfd, 0xe9, 0x10, 0x08,
            },
        },
        .confirm_rsp[17] = {
            .value = {
                0xbb, 0xcf, 0x41, 0xd2, 0x94, 0xea, 0xbe, 0x2f,
                0xde, 0xb2, 0xb4, 0x20, 0x72, 0x1c, 0xf8, 0x35,
            },
        },
        .random_req[17] = {
            .value = {
                0x59, 0x20, 0xb5, 0xdc, 0xaf, 0xc3, 0x8b, 0x32,
                0xe6, 0x40, 0x0f, 0x02, 0x67, 0x45, 0x49, 0x1f,
            },
        },
        .random_rsp[17] = {
            .value = {
                0xf5, 0x95, 0x60, 0x4c, 0x5f, 0x39, 0x54, 0xbf,
                0x62, 0x9e, 0x85, 0xca, 0x31, 0x9a, 0x95, 0xee,
            },
        },
        .confirm_req[18] = {
            .value = {
                0x36, 0x50, 0x78, 0x6b, 0x0f, 0x11, 0xe3, 0xa9,
                0x79, 0x3a, 0xa6, 0x9d, 0xd4, 0x8b, 0x13, 0x3f,
            },
        },
        .confirm_rsp[18] = {
            .value = {
                0xa5, 0x34, 0x5d, 0x5e, 0x43, 0x01, 0xf2, 0xe1,
                0x3f, 0xf2, 0x1c, 0x8b, 0x13, 0xf7, 0x17, 0x3e,
            },
        },
        .random_req[18] = {
            .value = {
                0x77, 0xa1, 0xbe, 0xbf, 0x49, 0xb8, 0x74, 0x73,
                0x47, 0x78, 0x2a, 0xf8, 0x66, 0x6b, 0xff, 0xd2,
            },
        },
        .random_rsp[18] = {
            .value = {
                0xa2, 0x05, 0x69, 0x65, 0x3f, 0xd4, 0xb4, 0xcd,
                0xed, 0x8c, 0x36, 0x6d, 0x51, 0x6a, 0xbb, 0xef,
            },
        },
        .confirm_req[19] = {
            .value = {
                0xda, 0xd8, 0x96, 0xfd, 0x1c, 0x0d, 0x1e, 0x56,
                0xe2, 0x62, 0xed, 0x18, 0x4b, 0xd3, 0x46, 0x48,
            },
        },
        .confirm_rsp[19] = {
            .value = {
                0xeb, 0x79, 0x5e, 0x52, 0x70, 0x25, 0xa7, 0x41,
                0x33, 0xfa, 0xac, 0xd3, 0x27, 0x35, 0xfc, 0x5f,
            },
        },
        .random_req[19] = {
            .value = {
                0xa8, 0x9c, 0xb9, 0xcd, 0x13, 0xb8, 0xdd, 0xd2,
                0x09, 0xd6, 0xc8, 0x12, 0xc3, 0x69, 0x9a, 0x64,
            },
        },
        .random_rsp[19] = {
            .value = {
                0x06, 0xe3, 0x8a, 0xef, 0xe4, 0x42, 0xae, 0x86,
                0xef, 0x58, 0x80, 0xe8, 0xe3, 0xa2, 0x09, 0x44,
            },
        },
        .dhkey_check_req = {
            .value = {
                0x6f, 0xa5, 0x37, 0x06, 0x4a, 0x89, 0x98, 0x39,
                0xf6, 0x69, 0x48, 0x56, 0x17, 0x6d, 0x44, 0x7c,
            },
        },
        .dhkey_check_rsp = {
            .value = {
                0x82, 0x48, 0xd4, 0x9e, 0xb8, 0x3c, 0xb4, 0xdc,
                0x44, 0xcb, 0x19, 0xdb, 0xcb, 0xa2, 0x00, 0x5d,
            },
        },
        .id_info_req = {
            .irk = {
                0x79, 0x12, 0x88, 0x97, 0xd9, 0x1c, 0x62, 0x0d,
                0x26, 0x43, 0x7d, 0x25, 0x86, 0x79, 0xc7, 0x07,
            },
        },
        .id_addr_info_req = {
            .addr_type = 0,
            .bd_addr = {
                0x0c, 0x0b, 0x0a, 0x09, 0x08, 0x07,
            },
        },
        .id_info_rsp = {
            .irk = {
                0xda, 0x6b, 0x27, 0xa0, 0xac, 0x71, 0xf0, 0xc3,
                0x75, 0x51, 0xf6, 0x21, 0x94, 0xec, 0x81, 0x92,
            },
        },
        .id_addr_info_rsp = {
            .addr_type = 0,
            .bd_addr = {
                0x06, 0x05, 0x04, 0x03, 0x02, 0x01,
            },
        },
        .sign_info_rsp = {
            .sig_key = {
                0x49, 0x5b, 0x11, 0xb3, 0x4c, 0x1a, 0x23, 0x5c,
                0x61, 0x4f, 0xe3, 0x08, 0xf9, 0x47, 0x8b, 0xdc,
            },
        },
        .ltk = {
            0x5a, 0x49, 0x28, 0xf0, 0x11, 0x3b, 0x6f, 0x6b,
            0x3a, 0x69, 0x6d, 0xdd, 0xb2, 0xe5, 0xa8, 0x97,
        },
        .pair_alg = BLE_SM_PAIR_ALG_PASSKEY,
        .authenticated = 1,
        .passkey_info = {
            .passkey = {
                .action = BLE_SM_IOACT_INPUT,
                .passkey = 4915,
            },
        },
    };
    ble_sm_test_util_us_sc_good(&params);

    ble_hs_test_util_assert_mbufs_freed(NULL);
}

TEST_SUITE(ble_sm_sc_test_suite)
{
    /*** No privacy. */

    /* Peer as initiator. */
    ble_sm_sc_peer_jw_iio3_rio3_b1_iat0_rat0_ik5_rk7();
    ble_sm_sc_peer_pk_iio0_rio2_b1_iat0_rat0_ik5_rk7();
    ble_sm_sc_peer_pk_iio2_rio0_b1_iat0_rat0_ik5_rk7();
    ble_sm_sc_peer_nc_iio1_rio1_b1_iat0_rat0_ik5_rk7();

    /* Us as initiator. */
    ble_sm_sc_us_jw_iio3_rio4_b1_iat0_rat0_ik7_rk5();
    ble_sm_sc_us_pk_iio2_rio4_b1_iat0_rat0_ik7_rk5();
    ble_sm_sc_us_pk_iio0_rio4_b1_iat0_rat0_ik7_rk5();
    ble_sm_sc_us_nc_iio1_rio4_b1_iat0_rat0_ik7_rk5();

    /*** Privacy (id = public). */
    // FIXME: needs to be fixed due to fix for address type used
#if 0
    /* Peer as initiator. */
    ble_sm_sc_peer_jw_iio3_rio3_b1_iat2_rat2_ik7_rk7();
    ble_sm_sc_peer_nc_iio1_rio1_b1_iat2_rat2_ik3_rk3();
    ble_sm_sc_peer_pk_iio2_rio0_b1_iat2_rat2_ik7_rk3();

    /* Us as initiator. */
    ble_sm_sc_us_jw_iio3_rio3_b1_iat2_rat2_ik3_rk3();
    ble_sm_sc_us_nc_iio1_rio1_b1_iat2_rat2_ik3_rk3();
    ble_sm_sc_us_pk_iio2_rio0_b1_iat2_rat2_ik7_rk3();
#endif
}

#endif /* NIMBLE_BLE_SM */
