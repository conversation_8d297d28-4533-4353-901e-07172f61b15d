# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#  http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
#

syscfg.defs:
    BLE_SVC_DIS_DEFAULT_READ_PERM:
        description: >
            Defines default permissions for reading characteristics. Can be
            zero to allow read without extra permissions or combination of:
                BLE_GATT_CHR_F_READ_ENC
                BLE_GATT_CHR_F_READ_AUTHEN
                BLE_GATT_CHR_F_READ_AUTHOR
            Set to '-1' to remove this characteristic.
        value: -1
    BLE_SVC_DIS_MODEL_NUMBER_READ_PERM:
        description: >
            Defines permissions for reading "Model Number" characteristics.
            Can be set to BLE_SVC_DIS_DEFAULT_READ_PERM or use any of the
            possible values defined for that setting.
        value: 0
    BLE_SVC_DIS_MODEL_NUMBER_DEFAULT:
        description: >
            Defines a default value for "Model number" if not set with
            'ble_svc_dis_model_number_set'.
        value: '"Apache Mynewt NimBLE"'
    BLE_SVC_DIS_SERIAL_NUMBER_READ_PERM:
        description: >
            Defines permissions for reading "Serial Number" characteristics.
            Can be set to BLE_SVC_DIS_DEFAULT_READ_PERM or use any of the
            possible values defined for that setting.
        value: MYNEWT_VAL(BLE_SVC_DIS_DEFAULT_READ_PERM)
    BLE_SVC_DIS_SERIAL_NUMBER_DEFAULT:
        description: >
            Defines a default value for "Serial number" if not set with
            'ble_svc_dis_serial_number_set'.
        value: NULL
    BLE_SVC_DIS_HARDWARE_REVISION_READ_PERM:
        description: >
            Defines permissions for reading "Hardware Revision" characteristics.
            Can be set to BLE_SVC_DIS_DEFAULT_READ_PERM or use any of the
            possible values defined for that setting.
        value: MYNEWT_VAL(BLE_SVC_DIS_DEFAULT_READ_PERM)
    BLE_SVC_DIS_HARDWARE_REVISION_DEFAULT:
        description: >
            Defines a default value for "Hardware revision" if not set with
            'ble_svc_dis_hardware_revision_set'.
        value: NULL
    BLE_SVC_DIS_FIRMWARE_REVISION_READ_PERM:
        description: >
            Defines permissions for reading "Firmware Revision" characteristics.
            Can be set to BLE_SVC_DIS_DEFAULT_READ_PERM or use any of the
            possible values defined for that setting.
        value: MYNEWT_VAL(BLE_SVC_DIS_DEFAULT_READ_PERM)
    BLE_SVC_DIS_FIRMWARE_REVISION_DEFAULT:
        description: >
            Defines a default value for "Software revision" if not set with
            'ble_svc_dis_firmware_revision_set'.
        value: NULL
    BLE_SVC_DIS_SOFTWARE_REVISION_READ_PERM:
        description: >
            Defines permissions for reading "Software Revision" characteristics.
            Can be set to BLE_SVC_DIS_DEFAULT_READ_PERM or use any of the
            possible values defined for that setting.
        value: MYNEWT_VAL(BLE_SVC_DIS_DEFAULT_READ_PERM)
    BLE_SVC_DIS_SOFTWARE_REVISION_DEFAULT:
        description: >
            Defines a default value for "Software revision" if not set with
            'ble_svc_dis_software_revision_set'.
        value: NULL
    BLE_SVC_DIS_MANUFACTURER_NAME_READ_PERM:
        description: >
            Defines permissions for reading "Manufacturer name" characteristics.
            Can be set to BLE_SVC_DIS_DEFAULT_READ_PERM or use any of the
            possible values defined for that setting.
        value: MYNEWT_VAL(BLE_SVC_DIS_DEFAULT_READ_PERM)
    BLE_SVC_DIS_MANUFACTURER_NAME_DEFAULT:
        description: >
            Defines a default value for "Manufacturer name" if not set with
            'ble_svc_dis_manufacturer_name_set'.
        value: NULL
    BLE_SVC_DIS_SYSTEM_ID_READ_PERM:
        description: >
            Defines permissions for reading "System ID" characteristics.
            Can be set to BLE_SVC_DIS_DEFAULT_READ_PERM or use any of the
            possible values defined for that setting.
        value: MYNEWT_VAL(BLE_SVC_DIS_DEFAULT_READ_PERM)
    BLE_SVC_DIS_SYSTEM_ID_DEFAULT:
        description: >
            Defines a default value for "System ID" if not set with
            'ble_svc_dis_manufacturer_name_set'.
        value: NULL
    BLE_SVC_DIS_SYSINIT_STAGE:
        description: >
            Sysinit stage for the device information BLE service.
        value: 303
