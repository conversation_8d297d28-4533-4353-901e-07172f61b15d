/**
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

/* Scan Parameters Service */

#ifndef H_BLE_SVC_SPS_
#define H_BLE_SVC_SPS_


#define BLE_SVC_SPS_UUID16                              0x1813
#define BLE_SVC_SPS_CHR_UUID16_SCAN_ITVL_WINDOW	        0x2A4F
#define BLE_SVC_SPS_CHR_UUID16_SCAN_REFRESH             0x2A31

void ble_svc_sps_scan_refresh(void);
void ble_svc_sps_init(uint16_t scan_itvl, uint16_t scan_window);
#endif
