# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#  http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
#

syscfg.defs:
    BLE_TRANSPORT_UART_PORT:
        description: UART index to use for HCI interface
        value: 0
    BLE_TRANSPORT_UART_BAUDRATE:
        description: Baudrate on UART
        value: 1000000
    BLE_TRANSPORT_UART_DATA_BITS:
        description: Number of data bits on UART
        value: 8
    BLE_TRANSPORT_UART_STOP_BITS:
        description: Number of stop bits on UART
        value: 1
    BLE_TRANSPORT_UART_PARITY:
        description: Parity on UART
        value: none
        choices: none,even,odd
    BLE_TRANSPORT_UART_FLOW_CONTROL:
        description: Flow control on UART
        choices: off,rtscts
        value: rtscts
