Mbed TLS ChangeLog (Sorted per branch, date)

= Mbed TLS 3.6.2 branch released 2024-10-14

Security
   * Fix a buffer underrun in mbedtls_pk_write_key_der() when
     called on an opaque key, MBEDTLS_USE_PSA_CRYPTO is enabled,
     and the output buffer is smaller than the actual output.
     Fix a related buffer underrun in mbedtls_pk_write_key_pem()
     when called on an opaque RSA key, MBEDTLS_USE_PSA_CRYPTO is enabled
     and MBEDTLS_MPI_MAX_SIZE is smaller than needed for a 4096-bit RSA key.
     CVE-2024-49195

= Mbed TLS 3.6.1 branch released 2024-08-30

API changes
   * The experimental functions psa_generate_key_ext() and
     psa_key_derivation_output_key_ext() are no longer declared when compiling
     in C++. This resolves a build failure under C++ compilers that do not
     support flexible array members (a C99 feature not adopted by C++).
     Fixes #9020.

Default behavior changes
   * In a PSA-client-only build (i.e. MBEDTLS_PSA_CRYPTO_CLIENT &&
     !MBEDTLS_PSA_CRYPTO_C), do not automatically enable local crypto when the
     corresponding PSA mechanism is enabled, since the server provides the
     crypto. Fixes #9126.
   * A TLS handshake may now call psa_crypto_init() if TLS 1.3 is enabled.
     This can happen even if TLS 1.3 is offered but eventually not selected
     in the protocol version negotiation.
   * By default, the handling of TLS 1.3 tickets by the Mbed TLS client is now
     disabled at runtime. Applications that were using TLS 1.3 tickets
     signalled by MBEDTLS_ERR_SSL_RECEIVED_NEW_SESSION_TICKET return values now
     need to enable the handling of TLS 1.3 tickets through the new
     mbedtls_ssl_conf_tls13_enable_signal_new_session_tickets() API.

New deprecations
   * The experimental functions psa_generate_key_ext() and
     psa_key_derivation_output_key_ext() are deprecated in favor of
     psa_generate_key_custom() and psa_key_derivation_output_key_custom().
     They have almost exactly the same interface, but the variable-length
     data is passed in a separate parameter instead of a flexible array
     member.
   * The following cryptographic mechanisms are planned to be removed
     in Mbed TLS 4.0:
     - DES (including 3DES).
     - PKCS#1v1.5 encryption/decryption (RSAES-PKCS1-v1_5).
       (OAEP, PSS, and PKCS#1v1.5 signature are staying.)
     - Finite-field Diffie-Hellman with custom groups.
       (RFC 7919 groups remain supported.)
     - Elliptic curves of size 225 bits or less.
   * The following cipher suites are planned to be removed from (D)TLS 1.2
     in Mbed TLS 4.0:
     - TLS_RSA_* (including TLS_RSA_PSK_*), i.e. cipher suites using
       RSA decryption.
       (RSA signatures, i.e. TLS_ECDHE_RSA_*, are staying.)
     - TLS_ECDH_*, i.e. cipher suites using static ECDH.
       (Ephemeral ECDH, i.e. TLS_ECDHE_*, is staying.)
     - TLS_DHE_*, i.e. cipher suites using finite-field Diffie-Hellman.
       (Ephemeral ECDH, i.e. TLS_ECDHE_*, is staying.)
     - TLS_*CBC*, i.e. all cipher suites using CBC.
   * The following low-level application interfaces are planned to be removed
     from the public API in Mbed TLS 4.0:
     - Hashes: hkdf.h, md5.h, ripemd160.h, sha1.h, sha3.h, sha256.h, sha512.h;
     - Random generation: ctr_drbg.h, hmac_drbg.h, entropy.h;
     - Ciphers and modes: aes.h, aria.h, camellia.h, chacha20.h, chachapoly.h,
       cipher.h, cmac.h, gcm.h, poly1305.h;
     - Private key encryption mechanisms: pkcs5.h, pkcs12.h.
     - Asymmetric cryptography: bignum.h, dhm.h, ecdh.h, ecdsa.h, ecjpake.h,
       ecp.h, rsa.h.
     The cryptographic mechanisms remain present, but they will only be
     accessible via the PSA API (psa_xxx functions introduced gradually
     starting with Mbed TLS 2.17) and, where relevant, `pk.h`.
     For guidance on migrating application code to the PSA API, please consult
     the PSA transition guide (docs/psa-transition.md).
   * The following integration interfaces are planned to be removed
     in Mbed TLS 4.0:
     - MBEDTLS_xxx_ALT replacement of cryptographic modules and functions.
       Use PSA transparent drivers instead.
     - MBEDTLS_PK_RSA_ALT and MBEDTLS_PSA_CRYPTO_SE_C.
       Use PSA opaque drivers instead.

Features
   * When the new compilation option MBEDTLS_PSA_KEY_STORE_DYNAMIC is enabled,
     the number of volatile PSA keys is virtually unlimited, at the expense
     of increased code size. This option is off by default, but enabled in
     the default mbedtls_config.h. Fixes #9216.

Security
   * Unlike previously documented, enabling MBEDTLS_PSA_HMAC_DRBG_MD_TYPE does
     not cause the PSA subsystem to use HMAC_DRBG: it uses HMAC_DRBG only when
     MBEDTLS_PSA_CRYPTO_EXTERNAL_RNG and MBEDTLS_CTR_DRBG_C are disabled.
     CVE-2024-45157
   * Fix a stack buffer overflow in mbedtls_ecdsa_der_to_raw() and
     mbedtls_ecdsa_raw_to_der() when the bits parameter is larger than the
     largest supported curve. In some configurations with PSA disabled,
     all values of bits are affected. This never happens in internal library
     calls, but can affect applications that call these functions directly.
     CVE-2024-45158
   * With TLS 1.3, when a server enables optional authentication of the
     client, if the client-provided certificate does not have appropriate values
     in keyUsage or extKeyUsage extensions, then the return value of
     mbedtls_ssl_get_verify_result() would incorrectly have the
     MBEDTLS_X509_BADCERT_KEY_USAGE and MBEDTLS_X509_BADCERT_EXT_KEY_USAGE bits
     clear. As a result, an attacker that had a certificate valid for uses other
     than TLS client authentication could be able to use it for TLS client
     authentication anyway. Only TLS 1.3 servers were affected, and only with
     optional authentication (required would abort the handshake with a fatal
     alert).
     CVE-2024-45159

Bugfix
   * Fix TLS 1.3 client build and runtime when support for session tickets is
     disabled (MBEDTLS_SSL_SESSION_TICKETS configuration option). Fixes #6395.
   * Fix compilation error when memcpy() is a function-like macros. Fixes #8994.
   * MBEDTLS_ASN1_PARSE_C and MBEDTLS_ASN1_WRITE_C are now automatically enabled
     as soon as MBEDTLS_RSA_C is enabled. Fixes #9041.
   * Fix undefined behaviour (incrementing a NULL pointer by zero length) when
     passing in zero length additional data to multipart AEAD.
   * Fix rare concurrent access bug where attempting to operate on a
     non-existent key while concurrently creating a new key could potentially
     corrupt the key store.
   * Fix error handling when creating a key in a dynamic secure element
     (feature enabled by MBEDTLS_PSA_CRYPTO_SE_C). In a low memory condition,
     the creation could return PSA_SUCCESS but using or destroying the key
     would not work. Fixes #8537.
   * Fix issue of redefinition warning messages for _GNU_SOURCE in
     entropy_poll.c and sha_256.c. There was a build warning during
     building for linux platform.
     Resolves #9026
   * Fix a compilation warning in pk.c when PSA is enabled and RSA is disabled.
   * Fix the build when MBEDTLS_PSA_CRYPTO_CONFIG is enabled and the built-in
     CMAC is enabled, but no built-in unauthenticated cipher is enabled.
     Fixes #9209.
   * Fix redefinition warnings when SECP192R1 and/or SECP192K1 are disabled.
     Fixes #9029.
   * Fix psa_cipher_decrypt() with CCM* rejecting messages less than 3 bytes
     long. Credit to Cryptofuzz. Fixes #9314.
   * Fix interference between PSA volatile keys and built-in keys
     when MBEDTLS_PSA_CRYPTO_BUILTIN_KEYS is enabled and
     MBEDTLS_PSA_KEY_SLOT_COUNT is more than 4096.
   * Document and enforce the limitation of mbedtls_psa_register_se_key()
     to persistent keys. Resolves #9253.
   * Fix Clang compilation error when MBEDTLS_USE_PSA_CRYPTO is enabled
     but MBEDTLS_DHM_C is disabled. Reported by Michael Schuster in #9188.
   * Fix server mode only build when MBEDTLS_SSL_SRV_C is enabled but
     MBEDTLS_SSL_CLI_C is disabled. Reported by M-Bab on GitHub in #9186.
   * When MBEDTLS_PSA_CRYPTO_C was disabled and MBEDTLS_ECDSA_C enabled,
     some code was defining 0-size arrays, resulting in compilation errors.
     Fixed by disabling the offending code in configurations without PSA
     Crypto, where it never worked. Fixes #9311.
   * Fix unintended performance regression when using short RSA public keys.
     Fixes #9232.
   * Fixes an issue where some TLS 1.2 clients could not connect to an
     Mbed TLS 3.6.0 server, due to incorrect handling of
     legacy_compression_methods in the ClientHello.
     Fixes #8995, #9243.
   * Fix TLS connections failing when the handshake selects TLS 1.3
     in an application that does not call psa_crypto_init().
     Fixes #9072.
   * Fix TLS connection failure in applications using an Mbed TLS client in
     the default configuration connecting to a TLS 1.3 server sending tickets.
     See the documentation of
     mbedtls_ssl_conf_tls13_enable_signal_new_session_tickets() for more
     information.
     Fixes #8749.
   * Fix a memory leak that could occur when failing to process an RSA
     key through some PSA functions due to low memory conditions.
   * Fixed a regression introduced in 3.6.0 where the CA callback set with
     mbedtls_ssl_conf_ca_cb() would stop working when connections were
     upgraded to TLS 1.3. Fixed by adding support for the CA callback with TLS
     1.3.
   * Fixed a regression introduced in 3.6.0 where clients that relied on
     optional/none authentication mode, by calling mbedtls_ssl_conf_authmode()
     with MBEDTLS_SSL_VERIFY_OPTIONAL or MBEDTLS_SSL_VERIFY_NONE, would stop
     working when connections were upgraded to TLS 1.3. Fixed by adding
     support for optional/none with TLS 1.3 as well. Note that the TLS 1.3
     standard makes server authentication mandatory; users are advised not to
     use authmode none, and to carefully check the results when using optional
     mode.
   * Fixed a regression introduced in 3.6.0 where context-specific certificate
     verify callbacks, set with mbedtls_ssl_set_verify() as opposed to
     mbedtls_ssl_conf_verify(), would stop working when connections were
     upgraded to TLS 1.3. Fixed by adding support for context-specific verify
     callback in TLS 1.3.

Changes
   * Warn if mbedtls/check_config.h is included manually, as this can
     lead to spurious errors. Error if a *adjust*.h header is included
     manually, as this can lead to silently inconsistent configurations,
     potentially resulting in buffer overflows.
     When migrating from Mbed TLS 2.x, if you had a custom config.h that
     included check_config.h, remove this inclusion from the Mbed TLS 3.x
     configuration file (renamed to mbedtls_config.h). This change was made
     in Mbed TLS 3.0, but was not announced in a changelog entry at the time.

= Mbed TLS 3.6.0 branch released 2024-03-28

API changes
   * Remove `tls13_` in mbedtls_ssl_tls13_conf_early_data() and
     mbedtls_ssl_tls13_conf_max_early_data_size() API names. Early data
     feature may not be TLS 1.3 specific in the future. Fixes #6909.

Default behavior changes
   * psa_import_key() now only accepts RSA keys in the PSA standard formats.
     The undocumented ability to import other formats (PKCS#8, SubjectPublicKey,
     PEM) accepted by the pkparse module has been removed. Applications that
     need these formats can call mbedtls_pk_parse_{public,}key() followed by
     mbedtls_pk_import_into_psa().

Requirement changes
   * Drop support for Visual Studio 2013 and 2015, and Arm Compiler 5.

New deprecations
   * Rename the MBEDTLS_SHA256_USE_A64_CRYPTO_xxx config options to
     MBEDTLS_SHA256_USE_ARMV8_A_CRYPTO_xxx. The old names may still
     be used, but are deprecated.
   * In the PSA API, domain parameters are no longer used for anything.
     They are deprecated and will be removed in a future version of the
     library.
   * mbedtls_ecp_write_key() is deprecated in favor of
     mbedtls_ecp_write_key_ext().

Removals
   * In the PSA API, the experimental way to encode the public exponent of
     an RSA key as a domain parameter is no longer supported. Use
     psa_generate_key_ext() instead.
   * Temporary function mbedtls_pk_wrap_as_opaque() is removed. To mimic the
     same behavior mbedtls_pk_get_psa_attributes() and
     mbedtls_pk_import_into_psa() can be used to import a PK key into PSA,
     while mbedtls_pk_setup_opaque() can be used to wrap a PSA key into a opaque
     PK context.

Features
   * Added an example program showing how to hash with the PSA API.
   * Support Armv8-A Crypto Extension acceleration for SHA-256
     when compiling for Thumb (T32) or 32-bit Arm (A32).
   * AES-NI is now supported in Windows builds with clang and clang-cl.
     Resolves #8372.
   * Add new mbedtls_x509_csr_parse_der_with_ext_cb() routine which allows
     parsing unsupported certificate extensions via user provided callback.
   * Enable the new option MBEDTLS_BLOCK_CIPHER_NO_DECRYPT to omit
     the decryption direction of block ciphers (AES, ARIA, Camellia).
     This affects both the low-level modules and the high-level APIs
     (the cipher and PSA interfaces). This option is incompatible with modes
     that use the decryption direction (ECB in PSA, CBC, XTS, KW) and with DES.
   * Support use of Armv8-A Cryptographic Extensions for hardware acclerated
     AES when compiling for Thumb (T32) or 32-bit Arm (A32).
   * If a cipher or AEAD mechanism has a PSA driver, you can now build the
     library without the corresponding built-in implementation. Generally
     speaking that requires both the key type and algorithm to be accelerated
     or they'll both be built in. However, for CCM and GCM the built-in
     implementation is able to take advantage of a driver that only
     accelerates the key type (that is, the block cipher primitive). See
     docs/driver-only-builds.md for full details and current limitations.
   * The CTR_DRBG module will now use AES from a PSA driver if MBEDTLS_AES_C is
     disabled. This requires PSA_WANT_ALG_ECB_NO_PADDING in addition to
     MBEDTLS_PSA_CRYPTO_C and PSA_WANT_KEY_TYPE_AES.
   * Fewer modules depend on MBEDTLS_CIPHER_C, making it possible to save code
     size by disabling it in more circumstances. In particular, the CCM and
     GCM modules no longer depend on MBEDTLS_CIPHER_C. Also,
     MBEDTLS_PSA_CRYPTO can now be enabled without MBEDTLS_CIPHER_C if all
     unauthenticated (non-AEAD) ciphers are disabled, or if they're all
     fully provided by drivers. See docs/driver-only-builds.md for full
     details and current limitations; in particular, NIST_KW and PKCS5/PKCS12
     decryption still unconditionally depend on MBEDTLS_CIPHER_C.
   * Add support for record size limit extension as defined by RFC 8449
     and configured with MBEDTLS_SSL_RECORD_SIZE_LIMIT.
     Application data sent and received will be fragmented according to
     Record size limits negotiated during handshake.
   * Improve performance of AES-GCM, AES-CTR and CTR-DRBG when
     hardware accelerated AES is not present (around 13-23% on 64-bit Arm).
   * Add functions mbedtls_ecc_group_to_psa() and mbedtls_ecc_group_from_psa()
     to convert between Mbed TLS and PSA curve identifiers.
   * Add utility functions to manipulate mbedtls_ecp_keypair objects, filling
     gaps made by making its fields private: mbedtls_ecp_set_public_key(),
     mbedtls_ecp_write_public_key(), mbedtls_ecp_keypair_calc_public(),
     mbedtls_ecp_keypair_get_group_id(). Fixes #5017, #5441, #8367, #8652.
   * Add functions mbedtls_md_psa_alg_from_type() and
     mbedtls_md_type_from_psa_alg() to convert between mbedtls_md_type_t and
     psa_algorithm_t.
   * Add partial platform support for z/OS.
   * Improve performance for gcc (versions older than 9.3.0) and IAR.
   * Add functions mbedtls_ecdsa_raw_to_der() and mbedtls_ecdsa_der_to_raw() to
     convert ECDSA signatures between raw and DER (ASN.1) formats.
   * Add support for using AES-CBC 128, 192, and 256 bit schemes
     with PKCS#5 PBES2. Keys encrypted this way can now be parsed by PK parse.
   * The new function mbedtls_rsa_get_bitlen() returns the length of the modulus
     in bits, i.e. the key size for an RSA key.
   * Add pc files for pkg-config, e.g.:
     pkg-config --cflags --libs (mbedtls|mbedcrypto|mbedx509)
   * Add getter (mbedtls_ssl_session_get_ticket_creation_time()) to access
     `mbedtls_ssl_session.ticket_creation_time`.
   * The new functions mbedtls_pk_get_psa_attributes() and
     mbedtls_pk_import_into_psa() provide a uniform way to create a PSA
     key from a PK key.
   * The benchmark program now reports times for both ephemeral and static
     ECDH in all ECDH configurations.
   * Add support for 8-bit GCM tables for Shoup's algorithm to speedup GCM
     operations when hardware accelerated AES is not present. Improves
     performance by around 30% on 64-bit Intel; 125% on Armv7-M.
   * The new function psa_generate_key_ext() allows generating an RSA
     key pair with a custom public exponent.
   * The new function mbedtls_ecp_write_key_ext() is similar to
     mbedtls_ecp_write_key(), but can be used without separately calculating
     the output length.
   * Add new accessor to expose the private group id member of
     `mbedtls_ecdh_context` structure.
   * Add new accessor to expose the `MBEDTLS_PRIVATE(ca_istrue)` member of
     `mbedtls_x509_crt` structure. This requires setting
     the MBEDTLS_X509_EXT_BASIC_CONSTRAINTS bit in the certificate's
     ext_types field.
   * mbedtls_psa_get_random() is always available as soon as
     MBEDTLS_PSA_CRYPTO_CLIENT is enabled at build time and psa_crypto_init() is
     called at runtime. This together with MBEDTLS_PSA_RANDOM_STATE can be
     used as random number generator function (f_rng) and context (p_rng) in
     legacy functions.
   * The new functions mbedtls_pk_copy_from_psa() and
     mbedtls_pk_copy_public_from_psa() provide ways to set up a PK context
     with the same content as a PSA key.
   * Add new accessors to expose the private session-id,
     session-id length, and ciphersuite-id members of
     `mbedtls_ssl_session` structure.
     Add new accessor to expose the ciphersuite-id of
     `mbedtls_ssl_ciphersuite_t` structure.Design ref: #8529
   * Mbed TLS now supports the writing and reading of TLS 1.3 early data (see
     docs/tls13-early-data.md). The support enablement is controlled at build
     time by the MBEDTLS_SSL_EARLY_DATA configuration option and at runtime by
     the mbedtls_ssl_conf_early_data() API (by default disabled in both cases).
   * Add protection for multithreaded access to the PSA keystore and protection
     for multithreaded access to the the PSA global state, including
     concurrently calling psa_crypto_init() when MBEDTLS_THREADING_C and
     MBEDTLS_THREADING_PTHREAD are defined. See
     docs/architecture/psa-thread-safety/psa-thread-safety.md for more details.
     Resolves issues #3263 and #7945.

Security
   * Fix a stack buffer overread (less than 256 bytes) when parsing a TLS 1.3
     ClientHello in a TLS 1.3 server supporting some PSK key exchange mode. A
     malicious client could cause information disclosure or a denial of service.
     Fixes CVE-2024-30166.
   * Passing buffers that are stored in untrusted memory as arguments
     to PSA functions is now secure by default.
     The PSA core now protects against modification of inputs or exposure
     of intermediate outputs during operations. This is currently implemented
     by copying buffers.
     This feature increases code size and memory usage. If buffers passed to
     PSA functions are owned exclusively by the PSA core for the duration of
     the function call (i.e. no buffer parameters are in shared memory),
     copying may be disabled by setting MBEDTLS_PSA_ASSUME_EXCLUSIVE_BUFFERS.
     Note that setting this option will cause input-output buffer overlap to
     be only partially supported (#3266).
     Fixes CVE-2024-28960.
   * Restore the maximum TLS version to be negotiated to the configured one
     when an SSL context is reset with the mbedtls_ssl_session_reset() API.
     An attacker was able to prevent an Mbed TLS server from establishing any
     TLS 1.3 connection potentially resulting in a Denial of Service or forced
     version downgrade from TLS 1.3 to TLS 1.2. Fixes #8654 reported by hey3e.
     Fixes CVE-2024-28755.
   * When negotiating TLS version on server side, do not fall back to the
     TLS 1.2 implementation of the protocol if it is disabled.
     - If the TLS 1.2 implementation was disabled at build time, a TLS 1.2
       client could put the TLS 1.3-only server in an infinite loop processing
       a TLS 1.2 ClientHello, resulting in a denial of service. Reported by
       Matthias Mucha and Thomas Blattmann, SICK AG.
     - If the TLS 1.2 implementation was disabled at runtime, a TLS 1.2 client
       was able to successfully establish a TLS 1.2 connection with the server.
       Reported by alluettiv on GitHub.
    Fixes CVE-2024-28836.

Bugfix
   * Fix the build with CMake when Everest or P256-m is enabled through
     a user configuration file or the compiler command line. Fixes #8165.
   * Fix compilation error in C++ programs when MBEDTLS_ASN1_PARSE_C is
     disabled.
   * Fix possible NULL dereference issue in X509 cert_req program if an entry
     in the san parameter is not separated by a colon.
   * Fix possible NULL dereference issue in X509 cert_write program if an entry
     in the san parameter is not separated by a colon.
   * Fix an inconsistency between implementations and usages of `__cpuid`,
     which mainly causes failures when building Windows target using
     mingw or clang. Fixes #8334 & #8332.
   * Fix build failure in conda-forge.  Fixes #8422.
   * Fix parsing of CSRs with critical extensions.
   * Switch to milliseconds as the unit for ticket creation and reception time
     instead of seconds. That avoids rounding errors when computing the age of
     tickets compared to peer using a millisecond clock (observed with GnuTLS).
     Fixes #6623.
   * Fix TLS server accepting TLS 1.2 handshake while TLS 1.2
     is disabled at runtime. Fixes #8593.
   * Remove accidental introduction of RSA signature algorithms
     in TLS Suite B Profile. Fixes #8221.
   * Fix unsupported PSA asymmetric encryption and decryption
     (psa_asymmetric_[en|de]crypt) with opaque keys.
     Resolves #8461.
   * On Linux on ARMv8, fix a build error with SHA-256 and SHA-512
     acceleration detection when the libc headers do not define the
     corresponding constant. Reported by valord577.
   * Correct initial capacities for key derivation algorithms:TLS12_PRF,
     TLS12_PSK_TO_MS, PBKDF2-HMAC, PBKDF2-CMAC
   * Fix mbedtls_pk_get_bitlen() for RSA keys whose size is not a
     multiple of 8. Fixes #868.
   * Avoid segmentation fault caused by releasing not initialized
     entropy resource in gen_key example. Fixes #8809.
   * mbedtls_pem_read_buffer() now performs a check on the padding data of
     decrypted keys and it rejects invalid ones.
   * Fix mbedtls_pk_sign(), mbedtls_pk_verify(), mbedtls_pk_decrypt() and
     mbedtls_pk_encrypt() on non-opaque RSA keys to honor the padding mode in
     the RSA context. Before, if MBEDTLS_USE_PSA_CRYPTO was enabled and the
     RSA context was configured for PKCS#1 v2.1 (PSS/OAEP), the sign/verify
     functions performed a PKCS#1 v1.5 signature instead and the
     encrypt/decrypt functions returned an error. Fixes #8824.
   * Fix missing bitflags in SSL session serialization headers. Their absence
     allowed SSL sessions saved in one configuration to be loaded in a
     different, incompatible configuration.
   * In TLS 1.3 clients, fix an interoperability problem due to the client
     generating a new random after a HelloRetryRequest. Fixes #8669.
   * Fix the restoration of the ALPN when loading serialized connection with
     the mbedtls_ssl_context_load() API.
   * Fix NULL pointer dereference in mbedtls_pk_verify_ext() when called using
     an opaque RSA context and specifying MBEDTLS_PK_RSASSA_PSS as key type.
   * Fix RSA opaque keys always using PKCS1 v1.5 algorithms instead of the
     primary algorithm of the wrapped PSA key.
   * Fully support arbitrary overlap between inputs and outputs of PSA
     functions. Note that overlap is still only partially supported when
     MBEDTLS_PSA_ASSUME_EXCLUSIVE_BUFFERS is set (#3266).

Changes
   * Use heap memory to allocate DER encoded public/private key.
     This reduces stack usage significantly for writing a public/private
     key to a PEM string.
   * PSA_WANT_ALG_CCM and PSA_WANT_ALG_CCM_STAR_NO_TAG are no more synonyms and
     they are now treated separately. This means that they should be
     individually enabled in order to enable respective support; also the
     corresponding MBEDTLS_PSA_ACCEL symbol should be defined in case
     acceleration is required.
   * Moved declaration of functions mbedtls_ecc_group_to_psa and
     mbedtls_ecc_group_of_psa from psa/crypto_extra.h to mbedtls/psa_util.h
   * mbedtls_pk_sign_ext() is now always available, not just when
     PSA (MBEDTLS_PSA_CRYPTO_C) is enabled.
   * Extended PSA Crypto configurations options for FFDH by making it possible
     to select only some of the parameters / groups, with the macros
     PSA_WANT_DH_RFC7919_XXXX. You now need to defined the corresponding macro
     for each size you want to support. Also, if you have an FFDH accelerator,
     you'll need to define the appropriate MBEDTLS_PSA_ACCEL macros to signal
     support for these domain parameters.
   * RSA support in PSA no longer auto-enables the pkparse and pkwrite modules,
     saving code size when those are not otherwise enabled.
   * mbedtls_mpi_exp_mod and code that uses it, notably RSA and DHM operations,
     have changed their speed/memory compromise as part of a proactive security
     improvement. The new default value of MBEDTLS_MPI_WINDOW_SIZE roughly
     preserves the current speed, at the expense of increasing memory
     consumption.
   * Rename directory containing Visual Studio files from visualc/VS2013 to
     visualc/VS2017.
   * The TLS 1.3 protocol is now enabled in the default configuration.

= Mbed TLS 3.5.2 branch released 2024-01-26

Security
   * Fix a timing side channel in private key RSA operations. This side channel
     could be sufficient for an attacker to recover the plaintext. A local
     attacker or a remote attacker who is close to the victim on the network
     might have precise enough timing measurements to exploit this. It requires
     the attacker to send a large number of messages for decryption. For
     details, see "Everlasting ROBOT: the Marvin Attack", Hubert Kario. Reported
     by Hubert Kario, Red Hat.
   * Fix a failure to validate input when writing x509 extensions lengths which
     could result in an integer overflow, causing a zero-length buffer to be
     allocated to hold the extension. The extension would then be copied into
     the buffer, causing a heap buffer overflow.

= Mbed TLS 3.5.1 branch released 2023-11-06

Changes
   *  Mbed TLS is now released under a dual Apache-2.0 OR GPL-2.0-or-later
      license. Users may choose which license they take the code under.

Bugfix
   * Fix accidental omission of MBEDTLS_TARGET_PREFIX in 3rdparty modules
     in CMake.

= Mbed TLS 3.5.0 branch released 2023-10-05

API changes
   * Mbed TLS 3.4 introduced support for omitting the built-in implementation
     of ECDSA and/or EC J-PAKE when those are provided by a driver. However,
     there was a flaw in the logic checking if the built-in implementation, in
     that it failed to check if all the relevant curves were supported by the
     accelerator. As a result, it was possible to declare no curves as
     accelerated and still have the built-in implementation compiled out.
     Starting with this release, it is necessary to declare which curves are
     accelerated (using MBEDTLS_PSA_ACCEL_ECC_xxx macros), or they will be
     considered not accelerated, and the built-in implementation of the curves
     and any algorithm possible using them will be included in the build.
   * Add new millisecond time type `mbedtls_ms_time_t` and `mbedtls_ms_time()`
     function, needed for TLS 1.3 ticket lifetimes. Alternative implementations
     can be created using an ALT interface.

Requirement changes
   * Officially require Python 3.8 now that earlier versions are out of support.
   * Minimum required Windows version is now Windows Vista, or
     Windows Server 2008.

New deprecations
   * PSA_WANT_KEY_TYPE_xxx_KEY_PAIR and
     MBEDTLS_PSA_ACCEL_KEY_TYPE_xxx_KEY_PAIR, where xxx is either ECC or RSA,
     are now being deprecated in favor of PSA_WANT_KEY_TYPE_xxx_KEY_PAIR_yyy and
     MBEDTLS_PSA_ACCEL_KEY_TYPE_xxx_KEY_PAIR_yyy. Here yyy can be: BASIC,
     IMPORT, EXPORT, GENERATE, DERIVE. The goal is to have a finer detail about
     the capabilities of the PSA side for either key.
   * MBEDTLS_CIPHER_BLKSIZE_MAX is deprecated in favor of
     MBEDTLS_MAX_BLOCK_LENGTH (if you intended what the name suggests:
     maximum size of any supported block cipher) or the new name
     MBEDTLS_CMAC_MAX_BLOCK_SIZE (if you intended the actual semantics:
     maximum size of a block cipher supported by the CMAC module).
   * mbedtls_pkcs5_pbes2() and mbedtls_pkcs12_pbe() functions are now
     deprecated in favor of mbedtls_pkcs5_pbes2_ext() and
     mbedtls_pkcs12_pbe_ext() as they offer more security by checking
     for overflow of the output buffer and reporting the actual length
     of the output.

Features
   * All modules that use hashes or HMAC can now take advantage of PSA Crypto
     drivers when MBEDTLS_PSA_CRYPTO_C is enabled and psa_crypto_init() has
     been called. Previously (in 3.3), this was restricted to a few modules,
     and only in builds where MBEDTLS_MD_C was disabled; in particular the
     entropy module was not covered which meant an external RNG had to be
     provided - these limitations are lifted in this version. A new set of
     feature macros, MBEDTLS_MD_CAN_xxx, has been introduced that can be used
     to check for availability of hash algorithms, regardless of whether
     they're provided by a built-in implementation, a driver or both. See
     docs/driver-only-builds.md.
   * When a PSA driver for ECDH is present, it is now possible to disable
     MBEDTLS_ECDH_C in the build in order to save code size. For TLS 1.2
     key exchanges based on ECDH(E) to work, this requires
     MBEDTLS_USE_PSA_CRYPTO. Restartable/interruptible ECDHE operations in
     TLS 1.2 (ECDHE-ECDSA key exchange) are not supported in those builds yet,
     as PSA does not have an API for restartable ECDH yet.
   * When all of ECDH, ECDSA and EC J-PAKE are either disabled or provided by
     a driver, it is possible to disable MBEDTLS_ECP_C (and MBEDTLS_BIGNUM_C
     if not required by another module) and still get support for ECC keys and
     algorithms in PSA, with some limitations. See docs/driver-only-builds.txt
     for details.
   * Add parsing of directoryName subtype for subjectAltName extension in
     x509 certificates.
   * Add support for server-side TLS version negotiation. If both TLS 1.2 and
     TLS 1.3 protocols are enabled, the TLS server now selects TLS 1.2 or
     TLS 1.3 depending on the capabilities and preferences of TLS clients.
     Fixes #6867.
   * X.509 hostname verification now supports IPAddress Subject Alternate Names.
   * Add support for reading and writing X25519 and X448
     public and private keys in RFC 8410 format using the existing PK APIs.
   * When parsing X.509 certificates, support the extensions
     SignatureKeyIdentifier and AuthorityKeyIdentifier.
   * Don't include the PSA dispatch functions for PAKEs (psa_pake_setup() etc)
     if no PAKE algorithms are requested
   * Add support for the FFDH algorithm and DH key types in PSA, with
     parameters from RFC 7919. This includes a built-in implementation based
     on MBEDTLS_BIGNUM_C, and a driver dispatch layer enabling alternative
     implementations of FFDH through the driver entry points.
   * It is now possible to generate certificates with SubjectAltNames.
     Currently supported subtypes: DnsName, UniformResourceIdentifier,
     IP address, OtherName, and DirectoryName, as defined in RFC 5280.
     See mbedtls_x509write_crt_set_subject_alternative_name for
     more information.
   * X.509 hostname verification now partially supports URI Subject Alternate
     Names. Only exact matching, without any normalization procedures
     described in 7.4 of RFC5280, will result in a positive URI verification.
   * Add function mbedtls_oid_from_numeric_string() to parse an OID from a
     string to a DER-encoded mbedtls_asn1_buf.
   * Add SHA-3 family hash functions.
   * Add support to restrict AES to 128-bit keys in order to save code size.
     A new configuration option, MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH, can be
     used to enable this feature.
   * AES performance improvements. Uplift varies by platform,
     toolchain, optimisation flags and mode.
     Aarch64, gcc -Os and CCM, GCM and XTS benefit the most.
     On Aarch64, uplift is typically around 20 - 110%.
     When compiling with gcc -Os on Aarch64, AES-XTS improves
     by 4.5x.
   * Add support for PBKDF2-HMAC through the PSA API.
   * New symbols PSA_WANT_KEY_TYPE_xxx_KEY_PAIR_yyy and
     MBEDTLS_PSA_ACCEL_KEY_TYPE_xxx_KEY_PAIR_yyy (where xxx is either ECC, RSA
     or DH) were introduced in order to have finer accuracy in defining the
     PSA capabilities for each key. These capabilities, named yyy above, can be
     any of: BASIC, IMPORT, EXPORT, GENERATE, DERIVE.
     - DERIVE is only available for ECC keys, not for RSA or DH ones.
     - implementations are free to enable more than what it was strictly
       requested. For example BASIC internally enables IMPORT and EXPORT
       (useful for testing purposes), but this might change in the future.
   * Add support for FFDH key exchange in TLS 1.3.
     This is automatically enabled as soon as PSA_WANT_ALG_FFDH
     and the ephemeral or psk-ephemeral key exchange mode are enabled.
     By default, all groups are offered; the list of groups can be
     configured using the existing API function mbedtls_ssl_conf_groups().
   * Improve mbedtls_x509_time performance and reduce memory use.
   * Reduce syscalls to time() during certificate verification.
   * Allow MBEDTLS_CONFIG_FILE and MBEDTLS_USER_CONFIG_FILE to be set by
     setting the CMake variable of the same name at configuration time.
   * Add getter (mbedtls_ssl_cache_get_timeout()) to access
     `mbedtls_ssl_cache_context.timeout`.
   * Add getter (mbedtls_ssl_get_hostname()) to access
     `mbedtls_ssl_context.hostname`.
   * Add getter (mbedtls_ssl_conf_get_endpoint()) to access
     `mbedtls_ssl_config.endpoint`.
   * Support for "opaque" (PSA-held) ECC keys in the PK module has been
     extended: it is now possible to use mbedtls_pk_write_key_der(),
     mbedtls_pk_write_key_pem(), mbedtls_pk_check_pair(), and
     mbedtls_pk_verify() with opaque ECC keys (provided the PSA attributes
     allow it).
   * The documentation of mbedtls_ecp_group now describes the optimized
     representation of A for some curves. Fixes #8045.
   * Add a possibility to generate CSR's with RCF822 and directoryName subtype
     of subjectAltName extension in x509 certificates.
   * Add support for PBKDF2-CMAC through the PSA API.
   * New configuration option MBEDTLS_AES_USE_HARDWARE_ONLY introduced. When
     using CPU-accelerated AES (e.g., Arm Crypto Extensions), this option
     disables the plain C implementation and the run-time detection for the
     CPU feature, which reduces code size and avoids the vulnerability of the
     plain C implementation.
   * Accept arbitrary AttributeType and AttributeValue in certificate
     Distinguished Names using RFC 4514 syntax.
   * Applications using ECC over secp256r1 through the PSA API can use a
     new implementation with a much smaller footprint, but some minor
     usage restrictions. See the documentation of the new configuration
     option MBEDTLS_PSA_P256M_DRIVER_ENABLED for details.

Security
   * Fix a case where potentially sensitive information held in memory would not
     be completely zeroized during TLS 1.2 handshake, in both server and client
     configurations.
   * In configurations with ARIA or Camellia but not AES, the value of
     MBEDTLS_CIPHER_BLKSIZE_MAX was 8, rather than 16 as the name might
     suggest. This did not affect any library code, because this macro was
     only used in relation with CMAC which does not support these ciphers.
     This may affect application code that uses this macro.
   * Developers using mbedtls_pkcs5_pbes2() or mbedtls_pkcs12_pbe() should
     review the size of the output buffer passed to this function, and note
     that the output after decryption may include CBC padding. Consider moving
     to the new functions mbedtls_pkcs5_pbes2_ext() or mbedtls_pkcs12_pbe_ext()
     which checks for overflow of the output buffer and reports the actual
     length of the output.
   * Improve padding calculations in CBC decryption, NIST key unwrapping and
     RSA OAEP decryption. With the previous implementation, some compilers
     (notably recent versions of Clang and IAR) could produce non-constant
     time code, which could allow a padding oracle attack if the attacker
     has access to precise timing measurements.
   * Updates to constant-time C code so that compilers are less likely to use
     conditional instructions, which can have an observable difference in
     timing. (Clang has been seen to do this.) Also introduce assembly
     implementations for 32- and 64-bit Arm and for x86 and x86-64, which are
     guaranteed not to use conditional instructions.
   * Fix definition of MBEDTLS_MD_MAX_BLOCK_SIZE, which was too
     small when MBEDTLS_SHA384_C was defined and MBEDTLS_SHA512_C was
     undefined. Mbed TLS itself was unaffected by this, but user code
     which used MBEDTLS_MD_MAX_BLOCK_SIZE could be affected. The only
     release containing this bug was Mbed TLS 3.4.0.
   * Fix a buffer overread when parsing short TLS application data records in
     null-cipher cipher suites. Credit to OSS-Fuzz.
   * Fix a remotely exploitable heap buffer overflow in TLS handshake parsing.
     In TLS 1.3, all configurations are affected except PSK-only ones, and
     both clients and servers are affected.
     In TLS 1.2, the affected configurations are those with
     MBEDTLS_USE_PSA_CRYPTO and ECDH enabled but DHM and RSA disabled,
     and only servers are affected, not clients.
     Credit to OSS-Fuzz.

Bugfix
   * Fix proper sizing for PSA_EXPORT_[KEY_PAIR/PUBLIC_KEY]_MAX_SIZE and
     PSA_SIGNATURE_MAX_SIZE buffers when at least one accelerated EC is bigger
     than all built-in ones and RSA is disabled.
     Resolves #6622.
   * Add missing md.h includes to some of the external programs from
     the programs directory. Without this, even though the configuration
     was sufficient for a particular program to work, it would only print
     a message that one of the required defines is missing.
   * Fix declaration of mbedtls_ecdsa_sign_det_restartable() function
     in the ecdsa.h header file. There was a build warning when the
     configuration macro MBEDTLS_ECDSA_SIGN_ALT was defined.
     Resolves #7407.
   * Fix an error when MBEDTLS_ECDSA_SIGN_ALT is defined but not
     MBEDTLS_ECDSA_VERIFY_ALT, causing ecdsa verify to fail. Fixes #7498.
   * Fix missing PSA initialization in sample programs when
     MBEDTLS_USE_PSA_CRYPTO is enabled.
   * Fix the J-PAKE driver interface for user and peer to accept any values
     (previously accepted values were limited to "client" or "server").
   * Fix clang and armclang compilation error when targeting certain Arm
     M-class CPUs (Cortex-M0, Cortex-M0+, Cortex-M1, Cortex-M23,
     SecurCore SC000). Fixes #1077.
   * Fix "unterminated '#pragma clang attribute push'" in sha256/sha512.c when
     built with MBEDTLS_SHAxxx_USE_A64_CRYPTO_IF_PRESENT but don't have a
     way to detect the crypto extensions required. A warning is still issued.
   * Fixed an issue that caused compile errors when using CMake and the IAR
     toolchain.
   * Fix very high stack usage in SSL debug code. Reported by Maximilian
     Gerhardt in #7804.
   * Fix a compilation failure in the constant_time module when
     building for arm64_32 (e.g., for watchos). Reported by Paulo
     Coutinho in #7787.
   * Fix crypt_and_hash decryption fail when used with a stream cipher
     mode of operation due to the input not being multiple of block size.
     Resolves #7417.
   * Fix a bug in which mbedtls_x509_string_to_names() would return success
     when given a invalid name string if it did not contain '=' or ','.
   * Fix compilation warnings in aes.c, which prevented the
     example TF-M configuration in configs/ from building cleanly:
     tfm_mbedcrypto_config_profile_medium.h with
     crypto_config_profile_medium.h.
   * In TLS 1.3, fix handshake failure when a client in its ClientHello
     proposes an handshake based on PSK only key exchange mode or at least
     one of the key exchange modes using ephemeral keys to a server that
     supports only the PSK key exchange mode.
   * Fix CCM* with no tag being not supported in a build with CCM as the only
     symmetric encryption algorithm and the PSA configuration enabled.
   * Fix the build with MBEDTLS_PSA_INJECT_ENTROPY. Fixes #7516.
   * Fix a compilation error on some platforms when including mbedtls/ssl.h
     with all TLS support disabled. Fixes #6628.
   * Fix x509 certificate generation to conform to RFC 5480 / RFC 5758 when
     using ECC key. The certificate was rejected by some crypto frameworks.
     Fixes #2924.
   * Fix a potential corruption of the passed-in IV when mbedtls_aes_crypt_cbc()
     is called with zero length and padlock is not enabled.
   * Fix compile failure due to empty enum in cipher_wrap.c, when building
     with a very minimal configuration. Fixes #7625.
   * Fix some cases where mbedtls_mpi_mod_exp, RSA key construction or ECDSA
     signature can silently return an incorrect result in low memory conditions.
   * Don't try to include MBEDTLS_PSA_CRYPTO_USER_CONFIG_FILE when
     MBEDTLS_PSA_CRYPTO_CONFIG is disabled.
   * Fix IAR compiler warnings.
   * Fix an issue when parsing an otherName subject alternative name into a
     mbedtls_x509_san_other_name struct. The type-id of the otherName was not
     copied to the struct. This meant that the struct had incomplete
     information about the otherName SAN and contained uninitialized memory.
   * Fix the detection of HardwareModuleName otherName SANs. These were being
     detected by comparing the wrong field and the check was erroneously
     inverted.
   * Fix a build error in some configurations with MBEDTLS_PSA_CRYPTO_CONFIG
     enabled, where some low-level modules required by requested PSA crypto
     features were not getting automatically enabled. Fixes #7420.
   * Fix undefined symbols in some builds using TLS 1.3 with a custom
     configuration file.
   * Fix log level for the got supported group message. Fixes #6765
   * Functions in the ssl_cache module now return a negative MBEDTLS_ERR_xxx
     error code on failure. Before, they returned 1 to indicate failure in
     some cases involving a missing entry or a full cache.
   * mbedtls_pk_parse_key() now rejects trailing garbage in encrypted keys.
   * Fix the build with CMake when Everest or P256-m is enabled through
     a user configuration file or the compiler command line. Fixes #8165.

Changes
   * Enable Arm / Thumb bignum assembly for most Arm platforms when
     compiling with gcc, clang or armclang and -O0.
   * Enforce minimum RSA key size when generating a key
     to avoid accidental misuse.
   * Use heap memory to allocate DER encoded RSA private key.
     This reduces stack usage significantly for RSA signature
     operations when MBEDTLS_PSA_CRYPTO_C is defined.
   * Update Windows code to use BCryptGenRandom and wcslen, and
     ensure that conversions between size_t, ULONG, and int are
     always done safely.  Original contribution by Kevin Kane #635, #730
     followed by Simon Butcher #1453.
   * Users integrating their own PSA drivers should be aware that
     the file library/psa_crypto_driver_wrappers.c has been renamed
     to psa_crypto_driver_wrappers_no_static.c.
   * When using CBC with the cipher module, the requirement to call
     mbedtls_cipher_set_padding_mode() is now enforced. Previously, omitting
     this call accidentally applied a default padding mode chosen at compile
     time.

= Mbed TLS 3.4.1 branch released 2023-08-04

Bugfix
   * Fix builds on Windows with clang

Changes
   * Update test data to avoid failures of unit tests after 2023-08-07.

= Mbed TLS 3.4.0 branch released 2023-03-28

Default behavior changes
   * The default priority order of TLS 1.3 cipher suites has been modified to
     follow the same rules as the TLS 1.2 cipher suites (see
     ssl_ciphersuites.c). The preferred cipher suite is now
     TLS_CHACHA20_POLY1305_SHA256.

New deprecations
   * mbedtls_x509write_crt_set_serial() is now being deprecated in favor of
     mbedtls_x509write_crt_set_serial_raw(). The goal here is to remove any
     direct dependency of X509 on BIGNUM_C.
   * PSA to mbedtls error translation is now unified in psa_util.h,
     deprecating mbedtls_md_error_from_psa. Each file that performs error
     translation should define its own version of PSA_TO_MBEDTLS_ERR,
     optionally providing file-specific error pairs. Please see psa_util.h for
     more details.

Features
   * Added partial support for parsing the PKCS #7 Cryptographic Message
     Syntax, as defined in RFC 2315. Currently, support is limited to the
     following:
     - Only the signed-data content type, version 1 is supported.
     - Only DER encoding is supported.
     - Only a single digest algorithm per message is supported.
     - Certificates must be in X.509 format. A message must have either 0
       or 1 certificates.
     - There is no support for certificate revocation lists.
     - The authenticated and unauthenticated attribute fields of SignerInfo
       must be empty.
     Many thanks to Daniel Axtens, Nayna Jain, and Nick Child from IBM for
     contributing this feature, and to Demi-Marie Obenour for contributing
     various improvements, tests and bug fixes.
   * General performance improvements by accessing multiple bytes at a time.
     Fixes #1666.
   * Improvements to use of unaligned and byte-swapped memory, reducing code
     size and improving performance (depending on compiler and target
     architecture).
   * Add support for reading points in compressed format
     (MBEDTLS_ECP_PF_COMPRESSED) with mbedtls_ecp_point_read_binary()
     (and callers) for Short Weierstrass curves with prime p where p = 3 mod 4
     (all mbedtls MBEDTLS_ECP_DP_SECP* and MBEDTLS_ECP_DP_BP* curves
      except MBEDTLS_ECP_DP_SECP224R1 and MBEDTLS_ECP_DP_SECP224K1)
   * SHA224_C/SHA384_C are now independent from SHA384_C/SHA512_C respectively.
     This helps in saving code size when some of the above hashes are not
     required.
   * Add parsing of V3 extensions (key usage, Netscape cert-type,
     Subject Alternative Names) in x509 Certificate Sign Requests.
   * Use HOSTCC (if it is set) when compiling C code during generation of the
     configuration-independent files. This allows them to be generated when
     CC is set for cross compilation.
   * Add parsing of uniformResourceIdentifier subtype for subjectAltName
     extension in x509 certificates.
   * Add an interruptible version of sign and verify hash to the PSA interface,
     backed by internal library support for ECDSA signing and verification.
   * Add parsing of rfc822Name subtype for subjectAltName
     extension in x509 certificates.
   * The configuration macros MBEDTLS_PSA_CRYPTO_PLATFORM_FILE and
     MBEDTLS_PSA_CRYPTO_STRUCT_FILE specify alternative locations for
     the headers "psa/crypto_platform.h" and "psa/crypto_struct.h".
   * When a PSA driver for ECDSA is present, it is now possible to disable
     MBEDTLS_ECDSA_C in the build in order to save code size. For PK, X.509
     and TLS to fully work, this requires MBEDTLS_USE_PSA_CRYPTO to be enabled.
     Restartable/interruptible ECDSA operations in PK, X.509 and TLS are not
     supported in those builds yet, as driver support for interruptible ECDSA
     operations is not present yet.
   * Add a driver dispatch layer for EC J-PAKE, enabling alternative
     implementations of EC J-PAKE through the driver entry points.
   * Add new API mbedtls_ssl_cache_remove for cache entry removal by
     its session id.
   * Add support to include the SubjectAltName extension to a CSR.
   * Add support for AES with the Armv8-A Cryptographic Extension on
     64-bit Arm. A new configuration option, MBEDTLS_AESCE_C, can
     be used to enable this feature. Run-time detection is supported
     under Linux only.
   * When a PSA driver for EC J-PAKE is present, it is now possible to disable
     MBEDTLS_ECJPAKE_C in the build in order to save code size. For the
     corresponding TLS 1.2 key exchange to work, MBEDTLS_USE_PSA_CRYPTO needs
     to be enabled.
   * Add functions mbedtls_rsa_get_padding_mode() and mbedtls_rsa_get_md_alg()
     to read non-public fields for padding mode and hash id from
     an mbedtls_rsa_context, as requested in #6917.
   * AES-NI is now supported with Visual Studio.
   * AES-NI is now supported in 32-bit builds, or when MBEDTLS_HAVE_ASM
     is disabled, when compiling with GCC or Clang or a compatible compiler
     for a target CPU that supports the requisite instructions (for example
     gcc -m32 -msse2 -maes -mpclmul). (Generic x86 builds with GCC-like
     compilers still require MBEDTLS_HAVE_ASM and a 64-bit target.)
   * It is now possible to use a PSA-held (opaque) password with the TLS 1.2
     ECJPAKE key exchange, using the new API function
     mbedtls_ssl_set_hs_ecjpake_password_opaque().

Security
   * Use platform-provided secure zeroization function where possible, such as
     explicit_bzero().
   * Zeroize SSL cache entries when they are freed.
   * Fix a potential heap buffer overread in TLS 1.3 client-side when
     MBEDTLS_DEBUG_C is enabled. This may result in an application crash.
   * Add support for AES with the Armv8-A Cryptographic Extension on 64-bit
     Arm, so that these systems are no longer vulnerable to timing side-channel
     attacks. This is configured by MBEDTLS_AESCE_C, which is on by default.
     Reported by Demi Marie Obenour.
   * MBEDTLS_AESNI_C, which is enabled by default, was silently ignored on
     builds that couldn't compile the GCC-style assembly implementation
     (most notably builds with Visual Studio), leaving them vulnerable to
     timing side-channel attacks. There is now an intrinsics-based AES-NI
     implementation as a fallback for when the assembly one cannot be used.

Bugfix
   * Fix possible integer overflow in mbedtls_timing_hardclock(), which
     could cause a crash in programs/test/benchmark.
   * Fix IAR compiler warnings. Fixes #6924.
   * Fix a bug in the build where directory names containing spaces were
     causing generate_errors.pl to error out resulting in a build failure.
     Fixes issue #6879.
   * In TLS 1.3, when using a ticket for session resumption, tweak its age
     calculation on the client side. It prevents a server with more accurate
     ticket timestamps (typically timestamps in milliseconds) compared to the
     Mbed TLS ticket timestamps (in seconds) to compute a ticket age smaller
     than the age computed and transmitted by the client and thus potentially
     reject the ticket. Fix #6623.
   * Fix compile error where MBEDTLS_RSA_C and MBEDTLS_X509_CRT_WRITE_C are
     defined, but MBEDTLS_PK_RSA_ALT_SUPPORT is not defined. Fixes #3174.
   * List PSA_WANT_ALG_CCM_STAR_NO_TAG in psa/crypto_config.h so that it can
     be toggled with config.py.
   * The key derivation algorithm PSA_ALG_TLS12_ECJPAKE_TO_PMS cannot be
     used on a shared secret from a key agreement since its input must be
     an ECC public key. Reject this properly.
   * mbedtls_x509write_crt_set_serial() now explicitly rejects serial numbers
     whose binary representation is longer than 20 bytes. This was already
     forbidden by the standard (RFC5280 - section 4.1.2.2) and now it's being
     enforced also at code level.
   * Fix potential undefined behavior in mbedtls_mpi_sub_abs().  Reported by
     Pascal Cuoq using TrustInSoft Analyzer in #6701; observed independently by
     Aaron Ucko under Valgrind.
   * Fix behavior of certain sample programs which could, when run with no
     arguments, access uninitialized memory in some cases. Fixes #6700 (which
     was found by TrustInSoft Analyzer during REDOCS'22) and #1120.
   * Fix parsing of X.509 SubjectAlternativeName extension. Previously,
     malformed alternative name components were not caught during initial
     certificate parsing, but only on subsequent calls to
     mbedtls_x509_parse_subject_alt_name(). Fixes #2838.
   * Make the fields of mbedtls_pk_rsassa_pss_options public. This makes it
     possible to verify RSA PSS signatures with the pk module, which was
     inadvertently broken since Mbed TLS 3.0.
   * Fix bug in conversion from OID to string in
     mbedtls_oid_get_numeric_string(). OIDs such as ********* are now printed
     correctly.
   * Reject OIDs with overlong-encoded subidentifiers when converting
     them to a string.
   * Reject OIDs with subidentifier values exceeding UINT_MAX.  Such
     subidentifiers can be valid, but Mbed TLS cannot currently handle them.
   * Reject OIDs that have unterminated subidentifiers, or (equivalently)
     have the most-significant bit set in their last byte.
   * Silence warnings from clang -Wdocumentation about empty \retval
     descriptions, which started appearing with Clang 15. Fixes #6960.
   * Fix the handling of renegotiation attempts in TLS 1.3. They are now
     systematically rejected.
   * Fix an unused-variable warning in TLS 1.3-only builds if
     MBEDTLS_SSL_RENEGOTIATION was enabled. Fixes #6200.
   * Fix undefined behavior in mbedtls_ssl_read() and mbedtls_ssl_write() if
     len argument is 0 and buffer is NULL.
   * Allow setting user and peer identifiers for EC J-PAKE operation
     instead of role in PAKE PSA Crypto API as described in the specification.
     This is a partial fix that allows only "client" and "server" identifiers.
   * Fix a compilation error when PSA Crypto is built with support for
     TLS12_PRF but not TLS12_PSK_TO_MS. Reported by joerchan in #7125.
   * In the TLS 1.3 server, select the preferred client cipher suite, not the
     least preferred. The selection error was introduced in Mbed TLS 3.3.0.
   * Fix TLS 1.3 session resumption when the established pre-shared key is
     384 bits long. That is the length of pre-shared keys created under a
     session where the cipher suite is TLS_AES_256_GCM_SHA384.
   * Fix an issue when compiling with MBEDTLS_SHA512_USE_A64_CRYPTO_IF_PRESENT
     enabled, which required specifying compiler flags enabling SHA3 Crypto
     Extensions, where some compilers would emit EOR3 instructions in other
     modules, which would then fail if run on a CPU without the SHA3
     extensions. Fixes #5758.

Changes
   * Install the .cmake files into CMAKE_INSTALL_LIBDIR/cmake/MbedTLS,
     typically /usr/lib/cmake/MbedTLS.
   * Mixed-endian systems are explicitly not supported any more.
   * When MBEDTLS_USE_PSA_CRYPTO and MBEDTLS_ECDSA_DETERMINISTIC are both
     defined, mbedtls_pk_sign() now use deterministic ECDSA for ECDSA
     signatures. This aligns the behaviour with MBEDTLS_USE_PSA_CRYPTO to
     the behaviour without it, where deterministic ECDSA was already used.
   * Visual Studio: Rename the directory containing Visual Studio files from
     visualc/VS2010 to visualc/VS2013 as we do not support building with versions
     older than 2013. Update the solution file to specify VS2013 as a minimum.
   * programs/x509/cert_write:
     - now it accepts the serial number in 2 different formats: decimal and
       hex. They cannot be used simultaneously
     - "serial" is used for the decimal format and it's limted in size to
       unsigned long long int
     - "serial_hex" is used for the hex format; max length here is
       MBEDTLS_X509_RFC5280_MAX_SERIAL_LEN*2
   * The C code follows a new coding style. This is transparent for users but
     affects contributors and maintainers of local patches. For more
     information, see
     https://mbed-tls.readthedocs.io/en/latest/kb/how-to/rewrite-branch-for-coding-style/
   * Changed the default MBEDTLS_ECP_WINDOW_SIZE from 6 to 2.
     As tested in issue 6790, the correlation between this define and
     RSA decryption performance has changed lately due to security fixes.
     To fix the performance degradation when using default values the
     window was reduced from 6 to 2, a value that gives the best or close
     to best results when tested on Cortex-M4 and Intel i7.
   * When enabling MBEDTLS_SHA256_USE_A64_CRYPTO_* or
     MBEDTLS_SHA512_USE_A64_CRYPTO_*, it is no longer necessary to specify
     compiler target flags on the command line; the library now sets target
     options within the appropriate modules.

= Mbed TLS 3.3.0 branch released 2022-12-14

Default behavior changes
   * Previously the macro MBEDTLS_SSL_DTLS_CONNECTION_ID implemented version 05
     of the IETF draft, and was marked experimental and disabled by default.
     It is now no longer experimental, and implements the final version from
     RFC 9146, which is not interoperable with the draft-05 version.
     If you need to communicate with peers that use earlier versions of
     Mbed TLS, then you need to define MBEDTLS_SSL_DTLS_CONNECTION_ID_COMPAT
     to 1, but then you won't be able to communicate with peers that use the
     standard (non-draft) version.
     If you need to interoperate with both classes of peers with the
     same build of Mbed TLS, please let us know about your situation on the
     mailing list or GitHub.

Requirement changes
   * When building with PSA drivers using generate_driver_wrappers.py, or
     when building the library from the development branch rather than
     from a release, the Python module jsonschema is now necessary, in
     addition to jinja2. The official list of required Python modules is
     maintained in scripts/basic.requirements.txt and may change again
     in the future.

New deprecations
   * Deprecate mbedtls_asn1_free_named_data().
     Use mbedtls_asn1_free_named_data_list()
     or mbedtls_asn1_free_named_data_list_shallow().

Features
   * Support rsa_pss_rsae_* signature algorithms in TLS 1.2.
   * make: enable building unversioned shared library, with e.g.:
     "SHARED=1 SOEXT_TLS=so SOEXT_X509=so SOEXT_CRYPTO=so make lib"
     resulting in library names like "libmbedtls.so" rather than
     "libmbedcrypto.so.11".
   * Expose the EC J-PAKE functionality through the Draft PSA PAKE Crypto API.
     Only the ECC primitive with secp256r1 curve and SHA-256 hash algorithm
     are supported in this implementation.
   * Some modules can now use PSA drivers for hashes, including with no
     built-in implementation present, but only in some configurations.
     - RSA OAEP and PSS (PKCS#1 v2.1), PKCS5, PKCS12 and EC J-PAKE now use
       hashes from PSA when (and only when) MBEDTLS_MD_C is disabled.
     - PEM parsing of encrypted files now uses MD-5 from PSA when (and only
       when) MBEDTLS_MD5_C is disabled.
     See the documentation of the corresponding macros in mbedtls_config.h for
     details.
     Note that some modules are not able to use hashes from PSA yet, including
     the entropy module. As a consequence, for now the only way to build with
     all hashes only provided by drivers (no built-in hash) is to use
     MBEDTLS_PSA_CRYPTO_EXTERNAL_RNG.
   * When MBEDTLS_USE_PSA_CRYPTO is enabled, X.509, TLS 1.2 and TLS 1.3 now
     properly negotiate/accept hashes based on their availability in PSA.
     As a consequence, they now work in configurations where the built-in
     implementations of (some) hashes are excluded and those hashes are only
     provided by PSA drivers. (See previous entry for limitation on RSA-PSS
     though: that module only use hashes from PSA when MBEDTLS_MD_C is off).
   * Add support for opaque keys as the private keys associated to certificates
     for authentication in TLS 1.3.
   * Add the LMS post-quantum-safe stateful-hash asymmetric signature scheme.
     Signature verification is production-ready, but generation is for testing
     purposes only. This currently only supports one parameter set
     (LMS_SHA256_M32_H10), meaning that each private key can be used to sign
     1024 messages. As such, it is not intended for use in TLS, but instead
     for verification of assets transmitted over an insecure channel,
     particularly firmware images.
   * Add the LM-OTS post-quantum-safe one-time signature scheme, which is
     required for LMS. This can be used independently, but each key can only
     be used to sign one message so is impractical for most circumstances.
   * Mbed TLS now supports TLS 1.3 key establishment via pre-shared keys.
     The pre-shared keys can be provisioned externally or via the ticket
     mechanism (session resumption).
     The ticket mechanism is supported when the configuration option
     MBEDTLS_SSL_SESSION_TICKETS is enabled.
     New options MBEDTLS_SSL_TLS1_3_KEY_EXCHANGE_MODE_xxx_ENABLED
     control the support for the three possible TLS 1.3 key exchange modes.
   * cert_write: support for setting extended key usage attributes. A
     corresponding new public API call has been added in the library,
     mbedtls_x509write_crt_set_ext_key_usage().
   * cert_write: support for writing certificate files in either PEM
     or DER format.
   * The PSA driver wrapper generator generate_driver_wrappers.py now
     supports a subset of the driver description language, including
     the following entry points: import_key, export_key, export_public_key,
     get_builtin_key, copy_key.
   * The new functions mbedtls_asn1_free_named_data_list() and
     mbedtls_asn1_free_named_data_list_shallow() simplify the management
     of memory in named data lists in X.509 structures.
   * The TLS 1.2 EC J-PAKE key exchange can now use the PSA Crypto API.
     Additional PSA key slots will be allocated in the process of such key
     exchange for builds that enable MBEDTLS_KEY_EXCHANGE_ECJPAKE_ENABLED and
     MBEDTLS_USE_PSA_CRYPTO.
   * Add support for DTLS Connection ID as defined by RFC 9146, controlled by
     MBEDTLS_SSL_DTLS_CONNECTION_ID (enabled by default) and configured with
     mbedtls_ssl_set_cid().
   * Add a driver dispatch layer for raw key agreement, enabling alternative
     implementations of raw key agreement through the key_agreement driver
     entry point. This entry point is specified in the proposed PSA driver
     interface, but had not yet been implemented.
   * Add an ad-hoc key derivation function handling EC J-PAKE to PMS
     calculation that can be used to derive the session secret in TLS 1.2,
     as described in draft-cragie-tls-ecjpake-01. This can be achieved by
     using PSA_ALG_TLS12_ECJPAKE_TO_PMS as the key derivation algorithm.

Security
   * Fix potential heap buffer overread and overwrite in DTLS if
     MBEDTLS_SSL_DTLS_CONNECTION_ID is enabled and
     MBEDTLS_SSL_CID_IN_LEN_MAX > 2 * MBEDTLS_SSL_CID_OUT_LEN_MAX.
   * Fix an issue where an adversary with access to precise enough information
     about memory accesses (typically, an untrusted operating system attacking
     a secure enclave) could recover an RSA private key after observing the
     victim performing a single private-key operation if the window size used
     for the exponentiation was 3 or smaller. Found and reported by Zili KOU,
     Wenjian HE, Sharad Sinha, and Wei ZHANG. See "Cache Side-channel Attacks
     and Defenses of the Sliding Window Algorithm in TEEs" - Design, Automation
     and Test in Europe 2023.

Bugfix
   * Refactor mbedtls_aes_context to support shallow-copying. Fixes #2147.
   * Fix an issue with in-tree CMake builds in releases with GEN_FILES
     turned off: if a shipped file was missing from the working directory,
     it could be turned into a symbolic link to itself.
   * Fix a long-standing build failure when building x86 PIC code with old
     gcc (4.x). The code will be slower, but will compile. We do however
     recommend upgrading to a more recent compiler instead. Fixes #1910.
   * Fix support for little-endian Microblaze when MBEDTLS_HAVE_ASM is defined.
     Contributed by Kazuyuki Kimura to fix #2020.
   * Use double quotes to include private header file psa_crypto_cipher.h.
     Fixes 'file not found with <angled> include' error
     when building with Xcode.
   * Fix handling of broken symlinks when loading certificates using
     mbedtls_x509_crt_parse_path(). Instead of returning an error as soon as a
     broken link is encountered, skip the broken link and continue parsing
     other certificate files. Contributed by Eduardo Silva in #2602.
   * Fix an interoperability failure between an Mbed TLS client with both
     TLS 1.2 and TLS 1.3 support, and a TLS 1.2 server that supports
     rsa_pss_rsae_* signature algorithms. This failed because Mbed TLS
     advertised support for PSS in both TLS 1.2 and 1.3, but only
     actually supported PSS in TLS 1.3.
   * Fix a compilation error when using CMake with an IAR toolchain.
     Fixes #5964.
   * Fix a build error due to a missing prototype warning when
     MBEDTLS_DEPRECATED_REMOVED is enabled.
   * Fix mbedtls_ctr_drbg_free() on an initialized but unseeded context. When
     MBEDTLS_AES_ALT is enabled, it could call mbedtls_aes_free() on an
     uninitialized context.
   * Fix a build issue on Windows using CMake where the source and build
     directories could not be on different drives. Fixes #5751.
   * Fix bugs and missing dependencies when building and testing
     configurations with only one encryption type enabled in TLS 1.2.
   * Provide the missing definition of mbedtls_setbuf() in some configurations
     with MBEDTLS_PLATFORM_C disabled. Fixes #6118, #6196.
   * Fix compilation errors when trying to build with
     PSA drivers for AEAD (GCM, CCM, Chacha20-Poly1305).
   * Fix memory leak in ssl_parse_certificate_request() caused by
     mbedtls_x509_get_name() not freeing allocated objects in case of error.
     Change mbedtls_x509_get_name() to clean up allocated objects on error.
   * Fix build failure with MBEDTLS_RSA_C and MBEDTLS_PSA_CRYPTO_C but not
     MBEDTLS_USE_PSA_CRYPTO or MBEDTLS_PK_WRITE_C. Fixes #6408.
   * Fix build failure with MBEDTLS_RSA_C and MBEDTLS_PSA_CRYPTO_C but not
     MBEDTLS_PK_PARSE_C. Fixes #6409.
   * Fix ECDSA verification, where it was not always validating the
     public key. This bug meant that it was possible to verify a
     signature with an invalid public key, in some cases. Reported by
     Guido Vranken using Cryptofuzz in #4420.
   * Fix a possible null pointer dereference if a memory allocation fails
     in TLS PRF code. Reported by Michael Madsen in #6516.
   * Fix TLS 1.3 session resumption. Fixes #6488.
   * Add a configuration check to exclude optional client authentication
     in TLS 1.3 (where it is forbidden).
   * Fix a bug in which mbedtls_x509_crt_info() would produce non-printable
     bytes when parsing certificates containing a binary RFC 4108
     HardwareModuleName as a Subject Alternative Name extension. Hardware
     serial numbers are now rendered in hex format. Fixes #6262.
   * Fix bug in error reporting in dh_genprime.c where upon failure,
     the error code returned by mbedtls_mpi_write_file() is overwritten
     and therefore not printed.
   * In the bignum module, operations of the form (-A) - (+A) or (-A) - (-A)
     with A > 0 created an unintended representation of the value 0 which was
     not processed correctly by some bignum operations. Fix this. This had no
     consequence on cryptography code, but might affect applications that call
     bignum directly and use negative numbers.
   * Fix a bug whereby the list of signature algorithms sent as part of
     the TLS 1.2 server certificate request would get corrupted, meaning the
     first algorithm would not get sent and an entry consisting of two random
     bytes would be sent instead. Found by Serban Bejan and Dudek Sebastian.
   * Fix undefined behavior (typically harmless in practice) of
     mbedtls_mpi_add_mpi(), mbedtls_mpi_add_abs() and mbedtls_mpi_add_int()
     when both operands are 0 and the left operand is represented with 0 limbs.
   * Fix undefined behavior (typically harmless in practice) when some bignum
     functions receive the most negative value of mbedtls_mpi_sint. Credit
     to OSS-Fuzz. Fixes #6597.
   * Fix undefined behavior (typically harmless in practice) in PSA ECB
     encryption and decryption.
   * Move some SSL-specific code out of libmbedcrypto where it had been placed
     accidentally.
   * Fix a build error when compiling the bignum module for some Arm platforms.
     Fixes #6089, #6124, #6217.

Changes
   * Add the ability to query PSA_WANT_xxx macros to query_compile_time_config.
   * Calling AEAD tag-specific functions for non-AEAD algorithms (which
     should not be done - they are documented for use only by AES-GCM and
     ChaCha20+Poly1305) now returns MBEDTLS_ERR_CIPHER_FEATURE_UNAVAILABLE
     instead of success (0).

= Mbed TLS 3.2.1 branch released 2022-07-12

Bugfix
   *  Re-add missing generated file library/psa_crypto_driver_wrappers.c

= Mbed TLS 3.2.0 branch released 2022-07-11

Default behavior changes
   * mbedtls_cipher_set_iv will now fail with ChaCha20 and ChaCha20+Poly1305
     for IV lengths other than 12. The library was silently overwriting this
     length with 12, but did not inform the caller about it. Fixes #4301.

Requirement changes
   * The library will no longer compile out of the box on a platform without
     setbuf(). If your platform does not have setbuf(), you can configure an
     alternative function by enabling MBEDTLS_PLATFORM_SETBUF_ALT or
     MBEDTLS_PLATFORM_SETBUF_MACRO.

New deprecations
   * Deprecate mbedtls_ssl_conf_max_version() and
     mbedtls_ssl_conf_min_version() in favor of
     mbedtls_ssl_conf_max_tls_version() and
     mbedtls_ssl_conf_min_tls_version().
   * Deprecate mbedtls_cipher_setup_psa(). Use psa_aead_xxx() or
     psa_cipher_xxx() directly instead.
   * Secure element drivers enabled by MBEDTLS_PSA_CRYPTO_SE_C are deprecated.
     This was intended as an experimental feature, but had not been explicitly
     documented as such. Use opaque drivers with the interface enabled by
     MBEDTLS_PSA_CRYPTO_DRIVERS instead.
   * Deprecate mbedtls_ssl_conf_sig_hashes() in favor of the more generic
     mbedtls_ssl_conf_sig_algs(). Signature algorithms for the TLS 1.2 and
     TLS 1.3 handshake should now be configured with
     mbedtls_ssl_conf_sig_algs().

Features
   * Add accessor to obtain ciphersuite id from ssl context.
   * Add accessors to get members from ciphersuite info.
   * Add mbedtls_ssl_ticket_rotate() for external ticket rotation.
   * Add accessor to get the raw buffer pointer from a PEM context.
   * The structures mbedtls_ssl_config and mbedtls_ssl_context now store
     a piece of user data which is reserved for the application. The user
     data can be either a pointer or an integer.
   * Add an accessor function to get the configuration associated with
     an SSL context.
   * Add a function to access the protocol version from an SSL context in a
     form that's easy to compare. Fixes #5407.
   * Add function mbedtls_md_info_from_ctx() to recall the message digest
     information that was used to set up a message digest context.
   * Add ALPN support in TLS 1.3 clients.
   * Add server certificate selection callback near end of Client Hello.
     Register callback with mbedtls_ssl_conf_cert_cb().
   * Provide mechanism to reset handshake cert list by calling
     mbedtls_ssl_set_hs_own_cert() with NULL value for own_cert param.
   * Add accessor mbedtls_ssl_get_hs_sni() to retrieve SNI from within
     cert callback (mbedtls_ssl_conf_cert_cb()) during handshake.
   * The X.509 module now uses PSA hash acceleration if present.
   * Add support for psa crypto key derivation for elliptic curve
     keys. Fixes #3260.
   * Add function mbedtls_timing_get_final_delay() to access the private
     final delay field in an mbedtls_timing_delay_context, as requested in
     #5183.
    * Add mbedtls_pk_sign_ext() which allows generating RSA-PSS signatures when
      PSA Crypto is enabled.
   * Add function mbedtls_ecp_export() to export ECP key pair parameters.
     Fixes #4838.
   * Add function mbedtls_ssl_is_handshake_over() to enable querying if the SSL
     Handshake has completed or not, and thus whether to continue calling
     mbedtls_ssl_handshake_step(), requested in #4383.
   * Add the function mbedtls_ssl_get_own_cid() to access our own connection id
     within mbedtls_ssl_context, as requested in #5184.
   * Introduce mbedtls_ssl_hs_cb_t typedef for use with
     mbedtls_ssl_conf_cert_cb() and perhaps future callbacks
     during TLS handshake.
   * Add functions mbedtls_ssl_conf_max_tls_version() and
     mbedtls_ssl_conf_min_tls_version() that use a single value to specify
     the protocol version.
    * Extend the existing PSA_ALG_TLS12_PSK_TO_MS() algorithm to support
      mixed-PSK. Add an optional input PSA_KEY_DERIVATION_INPUT_OTHER_SECRET
      holding the other secret.
   * When MBEDTLS_PSA_CRYPTO_CONFIG is enabled, you may list the PSA crypto
     feature requirements in the file named by the new macro
     MBEDTLS_PSA_CRYPTO_CONFIG_FILE instead of the default psa/crypto_config.h.
     Furthermore you may name an additional file to include after the main
     file with the macro MBEDTLS_PSA_CRYPTO_USER_CONFIG_FILE.
   * Add the function mbedtls_x509_crt_has_ext_type() to access the ext types
     field within mbedtls_x509_crt context, as requested in #5585.
   * Add HKDF-Expand and HKDF-Extract as separate algorithms in the PSA API.
   * Add support for the ARMv8 SHA-2 acceleration instructions when building
     for Aarch64.
   * Add support for authentication of TLS 1.3 clients by TLS 1.3 servers.
   * Add support for server HelloRetryRequest message. The TLS 1.3 client is
     now capable of negotiating another shared secret if the one sent in its
     first ClientHello was not suitable to the server.
   * Add support for client-side TLS version negotiation. If both TLS 1.2 and
     TLS 1.3 protocols are enabled in the build of Mbed TLS, the TLS client now
     negotiates TLS 1.3 or TLS 1.2 with TLS servers.
   * Enable building of Mbed TLS with TLS 1.3 protocol support but without TLS
     1.2 protocol support.
   * Mbed TLS provides an implementation of a TLS 1.3 server (ephemeral key
     establishment only). See docs/architecture/tls13-support.md for a
     description of the support. The MBEDTLS_SSL_PROTO_TLS1_3 and
     MBEDTLS_SSL_SRV_C configuration options control this.
   * Add accessors to configure DN hints for certificate request:
     mbedtls_ssl_conf_dn_hints() and mbedtls_ssl_set_hs_dn_hints()
   * The configuration option MBEDTLS_USE_PSA_CRYPTO, which previously
     affected only a limited subset of crypto operations in TLS, X.509 and PK,
     now causes most of them to be done using PSA Crypto; see
     docs/use-psa-crypto.md for the list of exceptions.
   * The function mbedtls_pk_setup_opaque() now supports RSA key pairs as well.
     Opaque keys can now be used everywhere a private key is expected in the
     TLS and X.509 modules.
   * Opaque pre-shared keys for TLS, provisioned with
     mbedtls_ssl_conf_psk_opaque() or mbedtls_ssl_set_hs_psk_opaque(), which
     previously only worked for "pure" PSK key exchange, now can also be used
     for the "mixed" PSK key exchanges as well: ECDHE-PSK, DHE-PSK, RSA-PSK.
   * cmake now detects if it is being built as a sub-project, and in that case
     disables the target export/installation and package configuration.
   * Make USE_PSA_CRYPTO compatible with KEY_ID_ENCODES_OWNER. Fixes #5259.
   * Add example programs cipher_aead_demo.c, md_hmac_demo.c, aead_demo.c
     and hmac_demo.c, which use PSA and the md/cipher interfaces side
     by side in order to illustrate how the operation is performed in PSA.
     Addresses #5208.

Security
   * Zeroize dynamically-allocated buffers used by the PSA Crypto key storage
     module before freeing them. These buffers contain secret key material, and
     could thus potentially leak the key through freed heap.
   * Fix potential memory leak inside mbedtls_ssl_cache_set() with
     an invalid session id length.
   * Add the platform function mbedtls_setbuf() to allow buffering to be
     disabled on stdio files, to stop secrets loaded from said files being
     potentially left in memory after file operations. Reported by
     Glenn Strauss.
   * Fix a potential heap buffer overread in TLS 1.2 server-side when
     MBEDTLS_USE_PSA_CRYPTO is enabled, an opaque key (created with
     mbedtls_pk_setup_opaque()) is provisioned, and a static ECDH ciphersuite
     is selected. This may result in an application crash or potentially an
     information leak.
   * Fix a buffer overread in DTLS ClientHello parsing in servers with
     MBEDTLS_SSL_DTLS_CLIENT_PORT_REUSE enabled. An unauthenticated client
     or a man-in-the-middle could cause a DTLS server to read up to 255 bytes
     after the end of the SSL input buffer. The buffer overread only happens
     when MBEDTLS_SSL_IN_CONTENT_LEN is less than a threshold that depends on
     the exact configuration: 258 bytes if using mbedtls_ssl_cookie_check(),
     and possibly up to 571 bytes with a custom cookie check function.
     Reported by the Cybeats PSI Team.
    * Fix a buffer overread in TLS 1.3 Certificate parsing. An unauthenticated
      client or server could cause an MbedTLS server or client to overread up
      to 64 kBytes of data and potentially overread the input buffer by that
      amount minus the size of the input buffer. As overread data undergoes
      various checks, the likelihood of reaching the boundary of the input
      buffer is rather small but increases as its size
      MBEDTLS_SSL_IN_CONTENT_LEN decreases.
   * Fix check of certificate key usage in TLS 1.3. The usage of the public key
     provided by a client or server certificate for authentication was not
     checked properly when validating the certificate. This could cause a
     client or server to be able to authenticate itself through a certificate
     to an Mbed TLS TLS 1.3 server or client while it does not own a proper
     certificate to do so.

Bugfix
   * Declare or use PSA_WANT_ALG_CCM_STAR_NO_TAG following the general
     pattern for PSA_WANT_xxx symbols. Previously you had to specify
     PSA_WANT_ALG_CCM for PSA_ALG_CCM_STAR_NO_TAG.
   * Fix a memory leak if mbedtls_ssl_config_defaults() is called twice.
   * Fixed swap of client and server random bytes when exporting them alongside
     TLS 1.3 handshake and application traffic secret.
   * Fix several bugs (warnings, compiler and linker errors, test failures)
     in reduced configurations when MBEDTLS_USE_PSA_CRYPTO is enabled.
   * Fix a bug in (D)TLS curve negotiation: when MBEDTLS_USE_PSA_CRYPTO was
     enabled and an ECDHE-ECDSA or ECDHE-RSA key exchange was used, the
     client would fail to check that the curve selected by the server for
     ECDHE was indeed one that was offered. As a result, the client would
     accept any curve that it supported, even if that curve was not allowed
     according to its configuration. Fixes #5291.
   * The TLS 1.3 implementation is now compatible with the
     MBEDTLS_USE_PSA_CRYPTO configuration option.
   * Fix unit tests that used 0 as the file UID. This failed on some
     implementations of PSA ITS. Fixes #3838.
   * Fix mbedtls_ssl_get_version() not reporting TLSv1.3. Fixes #5406.
   * Fix API violation in mbedtls_md_process() test by adding a call to
     mbedtls_md_starts(). Fixes #2227.
   * Fix compile errors when MBEDTLS_HAVE_TIME is not defined. Add tests
     to catch bad uses of time.h.
   * Fix a race condition in out-of-source builds with CMake when generated data
     files are already present. Fixes #5374.
   * Fix the library search path when building a shared library with CMake
     on Windows.
   * Fix bug in the alert sending function mbedtls_ssl_send_alert_message()
     potentially leading to corrupted alert messages being sent in case
     the function needs to be re-called after initially returning
     MBEDTLS_SSL_WANT_WRITE. Fixes #1916.
   * In configurations with MBEDTLS_SSL_DTLS_CONNECTION_ID enabled but not
     MBEDTLS_DEBUG_C, DTLS handshakes using CID would crash due to a null
     pointer dereference. Fix this. Fixes #3998.
     The fix was released, but not announced, in Mbed TLS 3.1.0.
   * Fix incorrect documentation of mbedtls_x509_crt_profile. The previous
     documentation stated that the `allowed_pks` field applies to signatures
     only, but in fact it does apply to the public key type of the end entity
     certificate, too. Fixes #1992.
   * Fix undefined behavior in mbedtls_asn1_find_named_data(), where val is
     not NULL and val_len is zero.
   * Fix compilation error with mingw32. Fixed by Cameron Cawley in #4211.
   * Fix compilation error when using C++ Builder on Windows. Reported by
     Miroslav Mastny in #4015.
   * psa_raw_key_agreement() now returns PSA_ERROR_BUFFER_TOO_SMALL when
     applicable. Fixes #5735.
   * Fix a bug in the x25519 example program where the removal of
     MBEDTLS_ECDH_LEGACY_CONTEXT caused the program not to run. Fixes #4901 and
     #3191.
   * Fix a TLS 1.3 handshake failure when the peer Finished message has not
     been received yet when we first try to fetch it.
   * Encode X.509 dates before 1/1/2000 as UTCTime rather than
     GeneralizedTime. Fixes #5465.
   * Add mbedtls_x509_dn_get_next function to return the next relative DN in
     an X509 name, to allow walking the name list. Fixes #5431.
    * Fix order value of curve x448.
   * Fix string representation of DNs when outputting values containing commas
     and other special characters, conforming to RFC 1779. Fixes #769.
   * Silence a warning from GCC 12 in the selftest program. Fixes #5974.
   * Fix check_config.h to check that we have MBEDTLS_SSL_KEEP_PEER_CERTIFICATE
     when MBEDTLS_SSL_PROTO_TLS1_3 is specified, and make this and other
     dependencies explicit in the documentation. Fixes #5610.
   * Fix mbedtls_asn1_write_mpi() writing an incorrect encoding of 0.
   * Fix a TLS 1.3 handshake failure when the first attempt to send the client
     Finished message on the network cannot be satisfied. Fixes #5499.
   * Fix resource leaks in mbedtls_pk_parse_public_key() in low
     memory conditions.
   * Fix server connection identifier setting for outgoing encrypted records
     on DTLS 1.2 session resumption. After DTLS 1.2 session resumption with
     connection identifier, the Mbed TLS client now properly sends the server
     connection identifier in encrypted record headers. Fix #5872.
   * Fix a null pointer dereference when performing some operations on zero
     represented with 0 limbs (specifically mbedtls_mpi_mod_int() dividing
     by 2, and mbedtls_mpi_write_string() in base 2).
   * Fix record sizes larger than 16384 being sometimes accepted despite being
     non-compliant. This could not lead to a buffer overflow. In particular,
     application data size was already checked correctly.
   * Fix MBEDTLS_SVC_KEY_ID_GET_KEY_ID() and MBEDTLS_SVC_KEY_ID_GET_OWNER_ID()
     which have been broken, resulting in compilation errors, since Mbed TLS
     3.0.
   * Ensure that TLS 1.2 ciphersuite/certificate and key selection takes into
     account not just the type of the key (RSA vs EC) but also what it can
     actually do. Resolves #5831.
   * Fix CMake windows host detection, especially when cross compiling.
   * Fix an error in make where the absence of a generated file caused
     make to break on a clean checkout. Fixes #5340.
   * Work around an MSVC ARM64 compiler bug causing incorrect behaviour
     in mbedtls_mpi_exp_mod(). Reported by Tautvydas Žilys in #5467.
   * Removed the prompt to exit from all windows build programs, which was causing
     issues in CI/CD environments.

Changes
   * The file library/psa_crypto_driver_wrappers.c is now generated
     from a template. In the future, the generation will support
     driver descriptions. For the time being, to customize this file,
     see docs/proposed/psa-driver-wrappers-codegen-migration-guide.md
   * Return PSA_ERROR_INVALID_ARGUMENT if the algorithm passed to one-shot
     AEAD functions is not an AEAD algorithm. This aligns them with the
     multipart functions, and the PSA Crypto API 1.1 specification.
   * In mbedtls_pk_parse_key(), if no password is provided, don't allocate a
     temporary variable on the heap. Suggested by Sergey Kanatov in #5304.
   * Assume source files are in UTF-8 when using MSVC with CMake.
   * Fix runtime library install location when building with CMake and MinGW.
     DLLs are now installed in the bin directory instead of lib.
   * cmake: Use GnuInstallDirs to customize install directories
     Replace custom LIB_INSTALL_DIR variable with standard CMAKE_INSTALL_LIBDIR
     variable. For backward compatibility, set CMAKE_INSTALL_LIBDIR if
     LIB_INSTALL_DIR is set.
   * Add a CMake option that enables static linking of the runtime library
     in Microsoft Visual C++ compiler. Contributed by Microplankton.
   * In CMake builds, add aliases for libraries so that the normal MbedTLS::*
     targets work when MbedTLS is built as a subdirectory. This allows the
     use of FetchContent, as requested in #5688.

= mbed TLS 3.1.0 branch released 2021-12-17

API changes
   * New error code for GCM: MBEDTLS_ERR_GCM_BUFFER_TOO_SMALL.
     Alternative GCM implementations are expected to verify
     the length of the provided output buffers and to return the
     MBEDTLS_ERR_GCM_BUFFER_TOO_SMALL in case the buffer length is too small.
   * You can configure groups for a TLS key exchange with the new function
     mbedtls_ssl_conf_groups(). It extends mbedtls_ssl_conf_curves().
   * Declare a number of structure fields as public: the fields of
     mbedtls_ecp_curve_info, the fields describing the result of ASN.1 and
     X.509 parsing, and finally the field fd of mbedtls_net_context on
     POSIX/Unix-like platforms.

Requirement changes
   * Sign-magnitude and one's complement representations for signed integers are
     not supported. Two's complement is the only supported representation.

New deprecations
   * Deprecate mbedtls_ssl_conf_curves() in favor of the more generic
     mbedtls_ssl_conf_groups().

Removals
   * Remove the partial support for running unit tests via Greentea on Mbed OS,
     which had been unmaintained since 2018.

Features
   * Enable support for Curve448 via the PSA API. Contributed by
     Archana Madhavan in #4626. Fixes #3399 and #4249.
   * The identifier of the CID TLS extension can be configured by defining
     MBEDTLS_TLS_EXT_CID at compile time.
   * Implement the PSA multipart AEAD interface, currently supporting
     ChaChaPoly and GCM.
   * Warn if errors from certain functions are ignored. This is currently
     supported on GCC-like compilers and on MSVC and can be configured through
     the macro MBEDTLS_CHECK_RETURN. The warnings are always enabled
     (where supported) for critical functions where ignoring the return
     value is almost always a bug. Enable the new configuration option
     MBEDTLS_CHECK_RETURN_WARNING to get warnings for other functions. This
     is currently implemented in the AES, DES and md modules, and will be
     extended to other modules in the future.
   * Add missing PSA macros declared by PSA Crypto API 1.0.0:
     PSA_ALG_IS_SIGN_HASH, PSA_ALG_NONE, PSA_HASH_BLOCK_LENGTH, PSA_KEY_ID_NULL.
   * Add support for CCM*-no-tag cipher to the PSA.
     Currently only 13-byte long IV's are supported.
     For decryption a minimum of 16-byte long input is expected.
     These restrictions may be subject to change.
   * Add new API mbedtls_ct_memcmp for constant time buffer comparison.
   * Add functions to get the IV and block size from cipher_info structs.
   * Add functions to check if a cipher supports variable IV or key size.
   * Add the internal implementation of and support for CCM to the PSA multipart
     AEAD interface.
   * Mbed TLS provides a minimum viable implementation of the TLS 1.3
     protocol. See docs/architecture/tls13-support.md for the definition of
     the TLS 1.3 Minimum Viable Product (MVP). The MBEDTLS_SSL_PROTO_TLS1_3
     configuration option controls the enablement of the support. The APIs
     mbedtls_ssl_conf_min_version() and mbedtls_ssl_conf_max_version() allow
     to select the 1.3 version of the protocol to establish a TLS connection.
   * Add PSA API definition for ARIA.

Security
   * Zeroize several intermediate variables used to calculate the expected
     value when verifying a MAC or AEAD tag. This hardens the library in
     case the value leaks through a memory disclosure vulnerability. For
     example, a memory disclosure vulnerability could have allowed a
     man-in-the-middle to inject fake ciphertext into a DTLS connection.
   * In psa_aead_generate_nonce(), do not read back from the output buffer.
     This fixes a potential policy bypass or decryption oracle vulnerability
     if the output buffer is in memory that is shared with an untrusted
     application.
   * In psa_cipher_generate_iv() and psa_cipher_encrypt(), do not read back
     from the output buffer. This fixes a potential policy bypass or decryption
     oracle vulnerability if the output buffer is in memory that is shared with
     an untrusted application.
   * Fix a double-free that happened after mbedtls_ssl_set_session() or
     mbedtls_ssl_get_session() failed with MBEDTLS_ERR_SSL_ALLOC_FAILED
     (out of memory). After that, calling mbedtls_ssl_session_free()
     and mbedtls_ssl_free() would cause an internal session buffer to
     be free()'d twice.

Bugfix
   * Stop using reserved identifiers as local variables. Fixes #4630.
   * The GNU makefiles invoke python3 in preference to python except on Windows.
     The check was accidentally not performed when cross-compiling for Windows
     on Linux. Fix this. Fixes #4774.
   * Prevent divide by zero if either of PSA_CIPHER_ENCRYPT_OUTPUT_SIZE() or
     PSA_CIPHER_UPDATE_OUTPUT_SIZE() were called using an asymmetric key type.
   * Fix a parameter set but unused in psa_crypto_cipher.c. Fixes #4935.
   * Don't use the obsolete header path sys/fcntl.h in unit tests.
     These header files cause compilation errors in musl.
     Fixes #4969.
   * Fix missing constraints on x86_64 and aarch64 assembly code
     for bignum multiplication that broke some bignum operations with
     (at least) Clang 12.
     Fixes #4116, #4786, #4917, #4962.
   * Fix mbedtls_cipher_crypt: AES-ECB when MBEDTLS_USE_PSA_CRYPTO is enabled.
   * Failures of alternative implementations of AES or DES single-block
     functions enabled with MBEDTLS_AES_ENCRYPT_ALT, MBEDTLS_AES_DECRYPT_ALT,
     MBEDTLS_DES_CRYPT_ECB_ALT or MBEDTLS_DES3_CRYPT_ECB_ALT were ignored.
     This does not concern the implementation provided with Mbed TLS,
     where this function cannot fail, or full-module replacements with
     MBEDTLS_AES_ALT or MBEDTLS_DES_ALT. Reported by Armelle Duboc in #1092.
   * Some failures of HMAC operations were ignored. These failures could only
     happen with an alternative implementation of the underlying hash module.
   * Fix the error returned by psa_generate_key() for a public key. Fixes #4551.
   * Fix compile-time or run-time errors in PSA
     AEAD functions when ChachaPoly is disabled. Fixes #5065.
   * Remove PSA'a AEAD finish/verify output buffer limitation for GCM.
     The requirement of minimum 15 bytes for output buffer in
     psa_aead_finish() and psa_aead_verify() does not apply to the built-in
     implementation of GCM.
   * Move GCM's update output buffer length verification from PSA AEAD to
     the built-in implementation of the GCM.
     The requirement for output buffer size to be equal or greater then
     input buffer size is valid only for the built-in implementation of GCM.
     Alternative GCM implementations can process whole blocks only.
   * Fix the build of sample programs when neither MBEDTLS_ERROR_C nor
     MBEDTLS_ERROR_STRERROR_DUMMY is enabled.
   * Fix PSA_ALG_RSA_PSS verification accepting an arbitrary salt length.
     This algorithm now accepts only the same salt length for verification
     that it produces when signing, as documented. Use the new algorithm
     PSA_ALG_RSA_PSS_ANY_SALT to accept any salt length. Fixes #4946.
   * The existing predicate macro name PSA_ALG_IS_HASH_AND_SIGN is now reserved
     for algorithm values that fully encode the hashing step, as per the PSA
     Crypto API specification. This excludes PSA_ALG_RSA_PKCS1V15_SIGN_RAW and
     PSA_ALG_ECDSA_ANY. The new predicate macro PSA_ALG_IS_SIGN_HASH covers
     all algorithms that can be used with psa_{sign,verify}_hash(), including
     these two.
   * Fix issue in Makefile on Linux with SHARED=1, that caused shared libraries
     not to list other shared libraries they need.
   * Fix a bug in mbedtls_gcm_starts() when the bit length of the iv
     exceeds 2^32. Fixes #4884.
   * Fix an uninitialized variable warning in test_suite_ssl.function with GCC
     version 11.
   * Fix the build when no SHA2 module is included. Fixes #4930.
   * Fix the build when only the bignum module is included. Fixes #4929.
   * Fix a potential invalid pointer dereference and infinite loop bugs in
     pkcs12 functions when the password is empty. Fix the documentation to
     better describe the inputs to these functions and their possible values.
     Fixes #5136.
   * The key usage flags PSA_KEY_USAGE_SIGN_MESSAGE now allows the MAC
     operations psa_mac_compute() and psa_mac_sign_setup().
   * The key usage flags PSA_KEY_USAGE_VERIFY_MESSAGE now allows the MAC
     operations psa_mac_verify() and psa_mac_verify_setup().

Changes
    * Explicitly mark the fields mbedtls_ssl_session.exported and
      mbedtls_ssl_config.respect_cli_pref as private. This was an
      oversight during the run-up to the release of Mbed TLS 3.0.
      The fields were never intended to be public.
   * Implement multi-part CCM API.
     The multi-part functions: mbedtls_ccm_starts(), mbedtls_ccm_set_lengths(),
     mbedtls_ccm_update_ad(), mbedtls_ccm_update(), mbedtls_ccm_finish()
     were introduced in mbedTLS 3.0 release, however their implementation was
     postponed until now.
     Implemented functions support chunked data input for both CCM and CCM*
     algorithms.
   * Remove MBEDTLS_SSL_EXPORT_KEYS, making it always on and increasing the
     code size by about 80B on an M0 build. This option only gated an ability
     to set a callback, but was deemed unnecessary as it was yet another define
     to remember when writing tests, or test configurations. Fixes #4653.
   * Improve the performance of base64 constant-flow code. The result is still
     slower than the original non-constant-flow implementation, but much faster
     than the previous constant-flow implementation. Fixes #4814.
   * Ignore plaintext/ciphertext lengths for CCM*-no-tag operations.
     For CCM* encryption/decryption without authentication, input
     length will be ignored.
   * Indicate in the error returned if the nonce length used with
     ChaCha20-Poly1305 is invalid, and not just unsupported.
   * The mbedcrypto library includes a new source code module constant_time.c,
     containing various functions meant to resist timing side channel attacks.
     This module does not have a separate configuration option, and functions
     from this module will be included in the build as required. Currently
     most of the interface of this module is private and may change at any
     time.
   * The generated configuration-independent files are now automatically
     generated by the CMake build system on Unix-like systems. This is not
     yet supported when cross-compiling.

= Mbed TLS 3.0.0 branch released 2021-07-07

API changes
   * Remove HAVEGE module.
     The design of HAVEGE makes it unsuitable for microcontrollers. Platforms
     with a more complex CPU usually have an operating system interface that
     provides better randomness. Instead of HAVEGE, declare OS or hardware RNG
     interfaces with mbedtls_entropy_add_source() and/or use an entropy seed
     file created securely during device provisioning. See
     https://mbed-tls.readthedocs.io/en/latest/kb/how-to/add-entropy-sources-to-entropy-pool/ for
     more information.
   * Add missing const attributes to API functions.
   * Remove helpers for the transition from Mbed TLS 1.3 to Mbed TLS 2.0: the
     header compat-1.3.h and the script rename.pl.
   * Remove certs module from the API.
     Transfer keys and certificates embedded in the library to the test
     component. This contributes to minimizing library API and discourages
     users from using unsafe keys in production.
   * Move alt helpers and definitions.
     Various helpers and definitions available for use in alt implementations
     have been moved out of the include/ directory and into the library/
     directory. The files concerned are ecp_internal.h and rsa_internal.h
     which have also been renamed to ecp_internal_alt.h and rsa_alt_helpers.h
     respectively.
   * Move internal headers.
     Header files that were only meant for the library's internal use and
     were not meant to be used in application code have been moved out of
     the include/ directory. The headers concerned are bn_mul.h, aesni.h,
     padlock.h, entropy_poll.h and *_internal.h.
   * Drop support for parsing SSLv2 ClientHello
     (MBEDTLS_SSL_SRV_SUPPORT_SSLV2_CLIENT_HELLO).
   * Drop support for SSLv3 (MBEDTLS_SSL_PROTO_SSL3).
   * Drop support for TLS record-level compression (MBEDTLS_ZLIB_SUPPORT).
   * Drop support for RC4 TLS ciphersuites.
   * Drop support for single-DES ciphersuites.
   * Drop support for MBEDTLS_SSL_HW_RECORD_ACCEL.
   * Update AEAD output size macros to bring them in line with the PSA Crypto
     API version 1.0 spec. This version of the spec parameterizes them on the
     key type used, as well as the key bit-size in the case of
     PSA_AEAD_TAG_LENGTH.
   * Add configuration option MBEDTLS_X509_REMOVE_INFO which
     removes the mbedtls_x509_*_info(), mbedtls_debug_print_crt()
     as well as other functions and constants only used by
     those functions. This reduces the code footprint by
     several kB.
   * Remove SSL error codes `MBEDTLS_ERR_SSL_CERTIFICATE_REQUIRED`
     and `MBEDTLS_ERR_SSL_INVALID_VERIFY_HASH` which are never
     returned from the public SSL API.
   * Remove `MBEDTLS_ERR_SSL_CERTIFICATE_TOO_LARGE` and return
     `MBEDTLS_ERR_SSL_BUFFER_TOO_SMALL` instead.
   * The output parameter of mbedtls_sha512_finish, mbedtls_sha512,
     mbedtls_sha256_finish and mbedtls_sha256 now has a pointer type
     rather than array type. This removes spurious warnings in some compilers
     when outputting a SHA-384 or SHA-224 hash into a buffer of exactly
     the hash size.
   * Remove the MBEDTLS_TEST_NULL_ENTROPY config option. Fixes #4388.
   * The interface of the GCM module has changed to remove restrictions on
     how the input to multipart operations is broken down. mbedtls_gcm_finish()
     now takes extra output parameters for the last partial output block.
     mbedtls_gcm_update() now takes extra parameters for the output length.
     The software implementation always produces the full output at each
     call to mbedtls_gcm_update(), but alternative implementations activated
     by MBEDTLS_GCM_ALT may delay partial blocks to the next call to
     mbedtls_gcm_update() or mbedtls_gcm_finish(). Furthermore, applications
     no longer pass the associated data to mbedtls_gcm_starts(), but to the
     new function mbedtls_gcm_update_ad().
     These changes are backward compatible for users of the cipher API.
   * Replace MBEDTLS_SHA512_NO_SHA384 config option with MBEDTLS_SHA384_C.
     This separates config option enabling the SHA384 algorithm from option
     enabling the SHA512 algorithm. Fixes #4034.
   * Introduce MBEDTLS_SHA224_C.
     This separates config option enabling the SHA224 algorithm from option
     enabling SHA256.
   * The getter and setter API of the SSL session cache (used for
     session-ID based session resumption) has changed to that of
     a key-value store with keys being session IDs and values
     being opaque instances of `mbedtls_ssl_session`.
   * Remove the mode parameter from RSA operation functions. Signature and
     decryption functions now always use the private key and verification and
     encryption use the public key. Verification functions also no longer have
     RNG parameters.
   * Modify semantics of `mbedtls_ssl_conf_[opaque_]psk()`:
     In Mbed TLS 2.X, the API prescribes that later calls overwrite
     the effect of earlier calls. In Mbed TLS 3.0, calling
     `mbedtls_ssl_conf_[opaque_]psk()` more than once will fail,
     leaving the PSK that was configured first intact.
     Support for more than one PSK may be added in 3.X.
   * The function mbedtls_x509write_csr_set_extension() has an extra parameter
     which allows to mark an extension as critical. Fixes #4055.
   * For multi-part AEAD operations with the cipher module, calling
     mbedtls_cipher_finish() is now mandatory. Previously the documentation
     was unclear on this point, and this function happened to never do
     anything with the currently implemented AEADs, so in practice it was
     possible to skip calling it, which is no longer supported.
   * The option MBEDTLS_ECP_FIXED_POINT_OPTIM use pre-computed comb tables
     instead of computing tables in runtime. Thus, this option now increase
     code size, and it does not increase RAM usage in runtime anymore.
   * Remove the SSL APIs mbedtls_ssl_get_input_max_frag_len() and
     mbedtls_ssl_get_output_max_frag_len(), and add a new API
     mbedtls_ssl_get_max_in_record_payload(), complementing the existing
     mbedtls_ssl_get_max_out_record_payload().
     Uses of mbedtls_ssl_get_input_max_frag_len() and
     mbedtls_ssl_get_input_max_frag_len() should be replaced by
     mbedtls_ssl_get_max_in_record_payload() and
     mbedtls_ssl_get_max_out_record_payload(), respectively.
   * mbedtls_rsa_init() now always selects the PKCS#1v1.5 encoding for an RSA
     key. To use an RSA key with PSS or OAEP, call mbedtls_rsa_set_padding()
     after initializing the context. mbedtls_rsa_set_padding() now returns an
     error if its parameters are invalid.
   * Replace MBEDTLS_SSL_SRV_RESPECT_CLIENT_PREFERENCE by a runtime
     configuration function mbedtls_ssl_conf_preference_order(). Fixes #4398.
   * Instead of accessing the len field of a DHM context, which is no longer
     supported, use the new function mbedtls_dhm_get_len() .
   * In modules that implement cryptographic hash functions, many functions
     mbedtls_xxx() now return int instead of void, and the corresponding
     function mbedtls_xxx_ret() which was identical except for returning int
     has been removed. This also concerns mbedtls_xxx_drbg_update(). See the
     migration guide for more information. Fixes #4212.
   * For all functions that take a random number generator (RNG) as a
     parameter, this parameter is now mandatory (that is, NULL is not an
     acceptable value). Functions which previously accepted NULL and now
     reject it are: the X.509 CRT and CSR writing functions; the PK and RSA
     sign and decrypt function; mbedtls_rsa_private(); the functions
     in DHM and ECDH that compute the shared secret; the scalar multiplication
     functions in ECP.
   * The following functions now require an RNG parameter:
     mbedtls_ecp_check_pub_priv(), mbedtls_pk_check_pair(),
     mbedtls_pk_parse_key(), mbedtls_pk_parse_keyfile().
   * mbedtls_ssl_conf_export_keys_ext_cb() and
     mbedtls_ssl_conf_export_keys_cb() have been removed and
     replaced by a new API mbedtls_ssl_set_export_keys_cb().
     Raw keys and IVs are no longer passed to the callback.
     Further, callbacks now receive an additional parameter
     indicating the type of secret that's being exported,
     paving the way for the larger number of secrets
     in TLS 1.3. Finally, the key export callback and
     context are now connection-specific.
   * Signature functions in the RSA and PK modules now require the hash
     length parameter to be the size of the hash input. For RSA signatures
     other than raw PKCS#1 v1.5, this must match the output size of the
     specified hash algorithm.
   * The functions mbedtls_pk_sign(), mbedtls_pk_sign_restartable(),
     mbedtls_ecdsa_write_signature() and
     mbedtls_ecdsa_write_signature_restartable() now take an extra parameter
     indicating the size of the output buffer for the signature.
   * Implement one-shot cipher functions, psa_cipher_encrypt and
     psa_cipher_decrypt, according to the PSA Crypto API 1.0.0
     specification.
   * Direct access to fields of structures declared in public headers is no
     longer supported except for fields that are documented public. Use accessor
     functions instead. For more information, see the migration guide entry
     "Most structure fields are now private".
   * mbedtls_ssl_get_session_pointer() has been removed, and
     mbedtls_ssl_{set,get}_session() may now only be called once for any given
     SSL context.

Default behavior changes
   * Enable by default the functionalities which have no reason to be disabled.
     They are: ARIA block cipher, CMAC mode, elliptic curve J-PAKE library and
     Key Wrapping mode as defined in NIST SP 800-38F. Fixes #4036.
   * Some default policies for X.509 certificate verification and TLS have
     changed: curves and hashes weaker than 255 bits are no longer accepted
     by default. The default order in TLS now favors faster curves over larger
     curves.

Requirement changes
   * The library now uses the %zu format specifier with the printf() family of
     functions, so requires a toolchain that supports it. This change does not
     affect the maintained LTS branches, so when contributing changes please
     bear this in mind and do not add them to backported code.
   * If you build the development version of Mbed TLS, rather than an official
     release, some configuration-independent files are now generated at build
     time rather than checked into source control. This includes some library
     source files as well as the Visual Studio solution. Perl, Python 3 and a
     C compiler for the host platform are required. See “Generated source files
     in the development branch” in README.md for more information.
   * Refresh the minimum supported versions of tools to build the
     library. CMake versions older than 3.10.2 and Python older
     than 3.6 are no longer supported.

Removals
   * Remove the MBEDTLS_TLS_DEFAULT_ALLOW_SHA1_IN_CERTIFICATES
     compile-time option, which was off by default. Users should not trust
     certificates signed with SHA-1 due to the known attacks against SHA-1.
     If needed, SHA-1 certificates can still be verified by using a custom
     verification profile.
   * Removed deprecated things in psa/crypto_compat.h. Fixes #4284
   * Removed deprecated functions from hashing modules. Fixes #4280.
   * Remove PKCS#11 library wrapper. PKCS#11 has limited functionality,
     lacks automated tests and has scarce documentation. Also, PSA Crypto
     provides a more flexible private key management.
     More details on PCKS#11 wrapper removal can be found in the mailing list
     https://lists.trustedfirmware.org/pipermail/mbed-tls/2020-April/000024.html
   * Remove deprecated error codes. Fix #4283
   * Remove MBEDTLS_ENABLE_WEAK_CIPHERSUITES configuration option. Fixes #4416.
   * Remove the MBEDTLS_TLS_DEFAULT_ALLOW_SHA1_IN_CERTIFICATES
     compile-time option. This option has been inactive for a long time.
     Please use the `lifetime` parameter of `mbedtls_ssl_ticket_setup()`
     instead.
   * Remove the following deprecated functions and constants of hex-encoded
     primes based on RFC 5114 and RFC 3526 from library code and tests:
     mbedtls_aes_encrypt(), mbedtls_aes_decrypt(), mbedtls_mpi_is_prime(),
     mbedtls_cipher_auth_encrypt(), mbedtls_cipher_auth_decrypt(),
     mbedtls_ctr_drbg_update(), mbedtls_hmac_drbg_update(),
     mbedtls_ecdsa_write_signature_det(), mbedtls_ecdsa_sign_det(),
     mbedtls_ssl_conf_dh_param(), mbedtls_ssl_get_max_frag_len(),
     MBEDTLS_DHM_RFC5114_MODP_2048_P, MBEDTLS_DHM_RFC5114_MODP_2048_G,
     MBEDTLS_DHM_RFC3526_MODP_2048_P, MBEDTLS_DHM_RFC3526_MODP_2048_G,
     MBEDTLS_DHM_RFC3526_MODP_3072_P, MBEDTLS_DHM_RFC3526_MODP_3072_G,
     MBEDTLS_DHM_RFC3526_MODP_4096_P, MBEDTLS_DHM_RFC3526_MODP_4096_G.
     Remove the deprecated file: include/mbedtls/net.h. Fixes #4282.
   * Remove MBEDTLS_SSL_MAX_CONTENT_LEN configuration option, since
     MBEDTLS_SSL_IN_CONTENT_LEN and MBEDTLS_SSL_OUT_CONTENT_LEN replace
     it. Fixes #4362.
   * Remove the MBEDTLS_SSL_RECORD_CHECKING option and enable by default its
     previous action. Fixes #4361.
   * Remove support for TLS 1.0, TLS 1.1 and DTLS 1.0, as well as support for
     CBC record splitting, fallback SCSV, and the ability to configure
     ciphersuites per version, which are no longer relevant. This removes the
     configuration options MBEDTLS_SSL_PROTO_TLS1,
     MBEDTLS_SSL_PROTO_TLS1_1, MBEDTLS_SSL_CBC_RECORD_SPLITTING and
     MBEDTLS_SSL_FALLBACK_SCSV as well as the functions
     mbedtls_ssl_conf_cbc_record_splitting(),
     mbedtls_ssl_get_key_exchange_md_ssl_tls(), mbedtls_ssl_conf_fallback(),
     and mbedtls_ssl_conf_ciphersuites_for_version(). Fixes #4286.
   * The RSA module no longer supports private-key operations with the public
     key and vice versa.
   * Remove the MBEDTLS_SSL_DTLS_BADMAC_LIMIT config.h option. Fixes #4403.
   * Remove all the 3DES ciphersuites:
     MBEDTLS_TLS_DHE_PSK_WITH_3DES_EDE_CBC_SHA,
     MBEDTLS_TLS_ECDH_ECDSA_WITH_3DES_EDE_CBC_SHA,
     MBEDTLS_TLS_ECDHE_ECDSA_WITH_3DES_EDE_CBC_SHA,
     MBEDTLS_TLS_ECDHE_PSK_WITH_3DES_EDE_CBC_SHA,
     MBEDTLS_TLS_ECDHE_RSA_WITH_3DES_EDE_CBC_SHA,
     MBEDTLS_TLS_ECDH_RSA_WITH_3DES_EDE_CBC_SHA,
     MBEDTLS_TLS_PSK_WITH_3DES_EDE_CBC_SHA,
     MBEDTLS_TLS_RSA_PSK_WITH_3DES_EDE_CBC_SHA,
     MBEDTLS_TLS_RSA_WITH_3DES_EDE_CBC_SHA. Remove the
     MBEDTLS_REMOVE_3DES_CIPHERSUITES option which is no longer relevant.
     Fixes #4367.
   * Remove the MBEDTLS_X509_ALLOW_EXTENSIONS_NON_V3 option and let the code
     behave as if it was always disabled. Fixes #4386.
   * Remove MBEDTLS_ECDH_LEGACY_CONTEXT config option since this was purely for
     backward compatibility which is no longer supported. Addresses #4404.
   * Remove the following macros: MBEDTLS_CHECK_PARAMS,
     MBEDTLS_CHECK_PARAMS_ASSERT, MBEDTLS_PARAM_FAILED,
     MBEDTLS_PARAM_FAILED_ALT. Fixes #4313.
   * Remove the  MBEDTLS_X509_ALLOW_UNSUPPORTED_CRITICAL_EXTENSION config.h
     option. The mbedtls_x509_crt_parse_der_with_ext_cb() is the way to go for
     migration path. Fixes #4378.
   * Remove the MBEDTLS_X509_CHECK_KEY_USAGE and
     MBEDTLS_X509_CHECK_EXTENDED_KEY_USAGE config.h options and let the code
     behave as if they were always enabled. Fixes #4405.
   * MBEDTLS_ECP_MAX_BITS is no longer a configuration option because it is
     now determined automatically based on supported curves.
   * Remove the following functions: mbedtls_timing_self_test(),
     mbedtls_hardclock_poll(), mbedtls_timing_hardclock() and
     mbedtls_set_alarm(). Fixes #4083.
   * The configuration option MBEDTLS_ECP_NO_INTERNAL_RNG has been removed as
     it no longer had any effect.
   * Remove all support for MD2, MD4, RC4, Blowfish and XTEA. This removes the
     corresponding modules and all their APIs and related configuration
     options. Fixes #4084.
   * Remove MBEDTLS_SSL_TRUNCATED_HMAC and also remove
     MBEDTLS_SSL_TRUNCATED_HMAC_COMPAT config option. Users are better served by
     using a CCM-8 ciphersuite than a CBC ciphersuite with truncated HMAC.
     See issue #4341 for more details.
   * Remove the compile-time option
     MBEDTLS_TLS_DEFAULT_ALLOW_SHA1_IN_KEY_EXCHANGE.

Features
   * Add mbedtls_rsa_rsassa_pss_sign_ext() function allowing to generate a
     signature with a specific salt length. This function allows to validate
     test cases provided in the NIST's CAVP test suite. Contributed by Cédric
     Meuter in PR #3183.
   * Added support for built-in driver keys through the PSA opaque crypto
     driver interface. Refer to the documentation of
     MBEDTLS_PSA_CRYPTO_BUILTIN_KEYS for more information.
   * Implement psa_sign_message() and psa_verify_message().
   * The multi-part GCM interface (mbedtls_gcm_update() or
     mbedtls_cipher_update()) no longer requires the size of partial inputs to
     be a multiple of 16.
   * The multi-part GCM interface now supports chunked associated data through
     multiple calls to mbedtls_gcm_update_ad().
   * The new function mbedtls_mpi_random() generates a random value in a
     given range uniformly.
   * Alternative implementations of the AES, DHM, ECJPAKE, ECP, RSA and timing
     modules had undocumented constraints on their context types. These
     constraints have been relaxed.
     See docs/architecture/alternative-implementations.md for the remaining
     constraints.
   * The new functions mbedtls_dhm_get_len() and mbedtls_dhm_get_bitlen()
     query the size of the modulus in a Diffie-Hellman context.
   * The new function mbedtls_dhm_get_value() copy a field out of a
     Diffie-Hellman context.
   * Use the new function mbedtls_ecjpake_set_point_format() to select the
     point format for ECJPAKE instead of accessing the point_format field
     directly, which is no longer supported.
   * Implement psa_mac_compute() and psa_mac_verify() as defined in the
     PSA Cryptograpy API 1.0.0 specification.

Security
   * Fix a bias in the generation of finite-field Diffie-Hellman-Merkle (DHM)
     private keys and of blinding values for DHM and elliptic curves (ECP)
     computations. Reported by FlorianF89 in #4245.
   * Fix a potential side channel vulnerability in ECDSA ephemeral key generation.
     An adversary who is capable of very precise timing measurements could
     learn partial information about the leading bits of the nonce used for the
     signature, allowing the recovery of the private key after observing a
     large number of signature operations. This completes a partial fix in
     Mbed TLS 2.20.0.
   * Fix an issue where an adversary with access to precise enough information
     about memory accesses (typically, an untrusted operating system attacking
     a secure enclave) could recover an RSA private key after observing the
     victim performing a single private-key operation. Found and reported by
     Zili KOU, Wenjian HE, Sharad Sinha, and Wei ZHANG.
   * Fix an issue where an adversary with access to precise enough timing
     information (typically, a co-located process) could recover a Curve25519
     or Curve448 static ECDH key after inputting a chosen public key and
     observing the victim performing the corresponding private-key operation.
     Found and reported by Leila Batina, Lukas Chmielewski, Björn Haase, Niels
     Samwel and Peter Schwabe.

Bugfix
   * Fix premature fopen() call in mbedtls_entropy_write_seed_file which may
     lead to the seed file corruption in case if the path to the seed file is
     equal to MBEDTLS_PLATFORM_STD_NV_SEED_FILE. Contributed by Victor
     Krasnoshchok in #3616.
   * PSA functions creating a key now return PSA_ERROR_INVALID_ARGUMENT rather
     than PSA_ERROR_INVALID_HANDLE when the identifier specified for the key
     to create is not valid, bringing them in line with version 1.0.0 of the
     specification. Fix #4271.
   * Add printf function attributes to mbedtls_debug_print_msg to ensure we
     get printf format specifier warnings.
   * PSA functions other than psa_open_key now return PSA_ERROR_INVALID_HANDLE
     rather than PSA_ERROR_DOES_NOT_EXIST for an invalid handle, bringing them
     in line with version 1.0.0 of the specification. Fix #4162.
   * Fix a bug in ECDSA that would cause it to fail when the hash is all-bits
     zero. Fixes #1792
   * Fix some cases in the bignum module where the library constructed an
     unintended representation of the value 0 which was not processed
     correctly by some bignum operations. This could happen when
     mbedtls_mpi_read_string() was called on "-0", or when
     mbedtls_mpi_mul_mpi() and mbedtls_mpi_mul_int() was called with one of
     the arguments being negative and the other being 0. Fixes #4643.
   * Fix a compilation error when MBEDTLS_ECP_RANDOMIZE_MXZ_ALT is
     defined. Fixes #4217.
   * Fix an incorrect error code when parsing a PKCS#8 private key.
   * In a TLS client, enforce the Diffie-Hellman minimum parameter size
     set with mbedtls_ssl_conf_dhm_min_bitlen() precisely. Before, the
     minimum size was rounded down to the nearest multiple of 8.
   * In library/net_sockets.c, _POSIX_C_SOURCE and _XOPEN_SOURCE are
     defined to specific values.  If the code is used in a context
     where these are already defined, this can result in a compilation
     error.  Instead, assume that if they are defined, the values will
     be adequate to build Mbed TLS.
   * With MBEDTLS_PSA_CRYPTO_C disabled, some functions were getting built
     nonetheless, resulting in undefined reference errors when building a
     shared library. Reported by Guillermo Garcia M. in #4411.
   * The cipher suite TLS-RSA-WITH-CAMELLIA-256-GCM-SHA384 was not available
     when SHA-1 was disabled and was offered when SHA-1 was enabled but SHA-384
     was disabled. Fix the dependency. Fixes #4472.
   * Do not offer SHA384 cipher suites when SHA-384 is disabled. Fixes #4499.
   * Fix test suite code on platforms where int32_t is not int, such as
     Arm Cortex-M. Fixes #4530.
   * Fix some issues affecting MBEDTLS_ARIA_ALT implementations: a misplaced
     directive in a header and a missing initialization in the self-test.
   * Fix a missing initialization in the Camellia self-test, affecting
     MBEDTLS_CAMELLIA_ALT implementations.
   * Restore the ability to configure PSA via Mbed TLS options to support RSA
     key pair operations but exclude RSA key generation. When MBEDTLS_GENPRIME
     is not defined PSA will no longer attempt to use mbedtls_rsa_gen_key().
     Fixes #4512.
   * Fix a regression introduced in 2.24.0 which broke (D)TLS CBC ciphersuites
     (when the encrypt-then-MAC extension is not in use) with some ALT
     implementations of the underlying hash (SHA-1, SHA-256, SHA-384), causing
     the affected side to wrongly reject valid messages. Fixes #4118.
   * Remove outdated check-config.h check that prevented implementing the
     timing module on Mbed OS. Fixes #4633.
   * Fix PSA_ALG_TLS12_PRF and PSA_ALG_TLS12_PSK_TO_MS being too permissive
     about missing inputs.
   * Fix mbedtls_net_poll() and mbedtls_net_recv_timeout() often failing with
     MBEDTLS_ERR_NET_POLL_FAILED on Windows. Fixes #4465.
   * Fix a resource leak in a test suite with an alternative AES
     implementation. Fixes #4176.
   * Fix a crash in mbedtls_mpi_debug_mpi on a bignum having 0 limbs. This
     could notably be triggered by setting the TLS debug level to 3 or above
     and using a Montgomery curve for the key exchange. Reported by lhuang04
     in #4578. Fixes #4608.
   * psa_verify_hash() was relying on implementation-specific behavior of
     mbedtls_rsa_rsassa_pss_verify() and was causing failures in some _ALT
     implementations. This reliance is now removed. Fixes #3990.
   * Disallow inputs of length different from the corresponding hash when
     signing or verifying with PSA_ALG_RSA_PSS (The PSA Crypto API mandates
     that PSA_ALG_RSA_PSS uses the same hash throughout the algorithm.)
   * Fix a null pointer dereference when mbedtls_mpi_exp_mod() was called with
     A=0 represented with 0 limbs. Up to and including Mbed TLS 2.26, this bug
     could not be triggered by code that constructed A with one of the
     mbedtls_mpi_read_xxx functions (including in particular TLS code) since
     those always built an mpi object with at least one limb.
     Credit to OSS-Fuzz. Fixes #4641.
   * Fix mbedtls_mpi_gcd(G,A,B) when the value of B is zero. This had no
     effect on Mbed TLS's internal use of mbedtls_mpi_gcd(), but may affect
     applications that call mbedtls_mpi_gcd() directly. Fixes #4642.
   * The PSA API no longer allows the creation or destruction of keys with a
     read-only lifetime. The persistence level PSA_KEY_PERSISTENCE_READ_ONLY
     can now only be used as intended, for keys that cannot be modified through
     normal use of the API.
   * When MBEDTLS_PSA_CRYPTO_SPM is enabled, crypto_spe.h was not included
     in all the right places. Include it from crypto_platform.h, which is
     the natural place. Fixes #4649.
   * Fix which alert is sent in some cases to conform to the
     applicable RFC: on an invalid Finished message value, an
     invalid max_fragment_length extension, or an
     unsupported extension used by the server.
   * Correct (change from 12 to 13 bytes) the value of the macro describing the
     maximum nonce length returned by psa_aead_generate_nonce().

Changes
   * Fix the setting of the read timeout in the DTLS sample programs.
   * Add extra printf compiler warning flags to builds.
   * Fix memsan build false positive in x509_crt.c with clang 11
   * Alternative implementations of CMAC may now opt to not support 3DES as a
     CMAC block cipher, and still pass the CMAC self test.
   * Remove the AES sample application programs/aes/aescrypt2 which shows
     bad cryptographic practice. Fix #1906.
   * Remove configs/config-psa-crypto.h, which no longer had any intended
     differences from the default configuration, but had accidentally diverged.
   * When building the test suites with GNU make, invoke python3 or python, not
     python2, which is no longer supported upstream.
   * fix build failure on MinGW toolchain when __USE_MING_ANSI_STDIO is on.
     When that flag is on, standard GNU C printf format specifiers
     should be used.
   * Replace MBEDTLS_SSL_CID_PADDING_GRANULARITY and
     MBEDTLS_SSL_TLS1_3_PADDING_GRANULARITY with a new single unified option
     MBEDTLS_SSL_CID_TLS1_3_PADDING_GRANULARITY. Fixes #4335.
   * Reduce the default value of MBEDTLS_ECP_WINDOW_SIZE. This reduces RAM usage
     during ECC operations at a negligible performance cost.
   * mbedtls_mpi_read_binary(), mbedtls_mpi_read_binary_le() and
     mbedtls_mpi_read_string() now construct an mbedtls_mpi object with 0 limbs
     when their input has length 0. Note that this is an implementation detail
     and can change at any time, so this change should be transparent, but it
     may result in mbedtls_mpi_write_binary() or mbedtls_mpi_write_string()
     now writing an empty string where it previously wrote one or more
     zero digits when operating from values constructed with an mpi_read
     function and some mpi operations.
   * Add CMake package config generation for CMake projects consuming Mbed TLS.
   * config.h has been split into build_info.h and mbedtls_config.h
     build_info.h is intended to be included from C code directly, while
     mbedtls_config.h is intended to be edited by end users wishing to
     change the build configuration, and should generally only be included from
     build_info.h.
   * The handling of MBEDTLS_CONFIG_FILE has been moved into build_info.h.
   * A config file version symbol, MBEDTLS_CONFIG_VERSION was introduced.
     Defining it to a particular value will ensure that Mbed TLS interprets
     the config file in a way that's compatible with the config file format
     used by the Mbed TLS release whose MBEDTLS_VERSION_NUMBER has the same
     value.
     The only value supported by Mbed TLS 3.0.0 is 0x03000000.
   * Various changes to which alert and/or error code may be returned
   * during the TLS handshake.
   * Implicitly add PSA_KEY_USAGE_SIGN_MESSAGE key usage policy flag when
     PSA_KEY_USAGE_SIGN_HASH flag is set and PSA_KEY_USAGE_VERIFY_MESSAGE flag
     when PSA_KEY_USAGE_VERIFY_HASH flag is set. This usage flag extension
     is also applied when loading a key from storage.

= mbed TLS 2.26.0 branch released 2021-03-08

API changes
   * Renamed the PSA Crypto API output buffer size macros to bring them in line
     with version 1.0.0 of the specification.
   * The API glue function mbedtls_ecc_group_of_psa() now takes the curve size
     in bits rather than bytes, with an additional flag to indicate if the
     size may have been rounded up to a whole number of bytes.
   * Renamed the PSA Crypto API AEAD tag length macros to bring them in line
     with version 1.0.0 of the specification.

Default behavior changes
   * In mbedtls_rsa_context objects, the ver field was formerly documented
     as always 0. It is now reserved for internal purposes and may take
     different values.

New deprecations
   * PSA_KEY_EXPORT_MAX_SIZE, PSA_HASH_SIZE, PSA_MAC_FINAL_SIZE,
     PSA_BLOCK_CIPHER_BLOCK_SIZE, PSA_MAX_BLOCK_CIPHER_BLOCK_SIZE and
     PSA_ALG_TLS12_PSK_TO_MS_MAX_PSK_LEN have been renamed, and the old names
     deprecated.
   * PSA_ALG_AEAD_WITH_DEFAULT_TAG_LENGTH and PSA_ALG_AEAD_WITH_TAG_LENGTH
     have been renamed, and the old names deprecated.

Features
   * The PSA crypto subsystem can now use HMAC_DRBG instead of CTR_DRBG.
     CTR_DRBG is used by default if it is available, but you can override
     this choice by setting MBEDTLS_PSA_HMAC_DRBG_MD_TYPE at compile time.
     Fix #3354.
   * Automatic fallback to a software implementation of ECP when
     MBEDTLS_ECP_xxx_ALT accelerator hooks are in use can now be turned off
     through setting the new configuration flag MBEDTLS_ECP_NO_FALLBACK.
   * The PSA crypto subsystem can now be configured to use less static RAM by
     tweaking the setting for the maximum amount of keys simultaneously in RAM.
     MBEDTLS_PSA_KEY_SLOT_COUNT sets the maximum number of volatile keys that
     can exist simultaneously. It has a sensible default if not overridden.
   * Partial implementation of the PSA crypto driver interface: Mbed TLS can
     now use an external random generator instead of the library's own
     entropy collection and DRBG code. Enable MBEDTLS_PSA_CRYPTO_EXTERNAL_RNG
     and see the documentation of mbedtls_psa_external_get_random() for details.
   * Applications using both mbedtls_xxx and psa_xxx functions (for example,
     applications using TLS and MBEDTLS_USE_PSA_CRYPTO) can now use the PSA
     random generator with mbedtls_xxx functions. See the documentation of
     mbedtls_psa_get_random() for details.
   * In the PSA API, the policy for a MAC or AEAD algorithm can specify a
     minimum MAC or tag length thanks to the new wildcards
     PSA_ALG_AT_LEAST_THIS_LENGTH_MAC and
     PSA_ALG_AEAD_WITH_AT_LEAST_THIS_LENGTH_TAG.

Security
   * Fix a security reduction in CTR_DRBG when the initial seeding obtained a
     nonce from entropy. Applications were affected if they called
     mbedtls_ctr_drbg_set_nonce_len(), if they called
     mbedtls_ctr_drbg_set_entropy_len() with a size that was 3/2 times the key
     length, or when the entropy module uses SHA-256 and CTR_DRBG uses AES-256.
     In such cases, a random nonce was necessary to achieve the advertised
     security strength, but the code incorrectly used a constant instead of
     entropy from the nonce.
     Found by John Stroebel in #3819 and fixed in #3973.
   * Fix a buffer overflow in mbedtls_mpi_sub_abs() when calculating
     |A| - |B| where |B| is larger than |A| and has more limbs (so the
     function should return MBEDTLS_ERR_MPI_NEGATIVE_VALUE). Only
     applications calling mbedtls_mpi_sub_abs() directly are affected:
     all calls inside the library were safe since this function is
     only called with |A| >= |B|. Reported by Guido Vranken in #4042.
   * Fix an errorneous estimation for an internal buffer in
     mbedtls_pk_write_key_pem(). If MBEDTLS_MPI_MAX_SIZE is set to an odd
     value the function might fail to write a private RSA keys of the largest
     supported size.
     Found by Daniel Otte, reported in #4093 and fixed in #4094.
   * Fix a stack buffer overflow with mbedtls_net_poll() and
     mbedtls_net_recv_timeout() when given a file descriptor that is
     beyond FD_SETSIZE. Reported by FigBug in #4169.
   * Guard against strong local side channel attack against base64 tables by
     making access aceess to them use constant flow code.

Bugfix
   * Fix use-after-scope error in programs/ssl/ssl_client2.c and ssl_server2.c
   * Fix memory leak that occured when calling psa_close_key() on a
     wrapped key with MBEDTLS_PSA_CRYPTO_SE_C defined.
   * Fix an incorrect error code if an RSA private operation glitched.
   * Fix a memory leak in an error case in psa_generate_derived_key_internal().
   * Fix a resource leak in CTR_DRBG and HMAC_DRBG when MBEDTLS_THREADING_C
     is enabled, on platforms where initializing a mutex allocates resources.
     This was a regression introduced in the previous release. Reported in
     #4017, #4045 and #4071.
   * Ensure that calling mbedtls_rsa_free() or mbedtls_entropy_free()
     twice is safe. This happens for RSA when some Mbed TLS library functions
     fail. Such a double-free was not safe when MBEDTLS_THREADING_C was
     enabled on platforms where freeing a mutex twice is not safe.
   * Fix a resource leak in a bad-arguments case of mbedtls_rsa_gen_key()
     when MBEDTLS_THREADING_C is enabled on platforms where initializing
     a mutex allocates resources.
   * Fixes a bug where, if the library was configured to include support for
     both the old SE interface and the new PSA driver interface, external keys were
     not loaded from storage. This was fixed by #3996.
   * This change makes 'mbedtls_x509write_crt_set_basic_constraints'
     consistent with RFC 5280 4.2.1.9 which says: "Conforming CAs MUST
     include this extension in all CA certificates that contain public keys
     used to validate digital signatures on certificates and MUST mark the
     extension as critical in such certificates." Previous to this change,
     the extension was always marked as non-critical. This was fixed by
     #3698.

Changes
   * A new library C file psa_crypto_client.c has been created to contain
     the PSA code needed by a PSA crypto client when the PSA crypto
     implementation is not included into the library.
   * On recent enough versions of FreeBSD and DragonFlyBSD, the entropy module
     now uses the getrandom syscall instead of reading from /dev/urandom.

= mbed TLS 2.25.0 branch released 2020-12-11

API changes
   * The numerical values of the PSA Crypto API macros have been updated to
     conform to version 1.0.0 of the specification.
   * PSA_ALG_STREAM_CIPHER replaces PSA_ALG_CHACHA20 and PSA_ALG_ARC4.
     The underlying stream cipher is determined by the key type
     (PSA_KEY_TYPE_CHACHA20 or PSA_KEY_TYPE_ARC4).
   * The functions mbedtls_cipher_auth_encrypt() and
     mbedtls_cipher_auth_decrypt() no longer accept NIST_KW contexts,
     as they have no way to check if the output buffer is large enough.
     Please use mbedtls_cipher_auth_encrypt_ext() and
     mbedtls_cipher_auth_decrypt_ext() instead. Credit to OSS-Fuzz and
     Cryptofuzz. Fixes #3665.

Requirement changes
   * Update the minimum required CMake version to 2.8.12.  This silences a
     warning on CMake 3.19.0. #3801

New deprecations
   * PSA_ALG_CHACHA20 and PSA_ALG_ARC4 have been deprecated.
     Use PSA_ALG_STREAM_CIPHER instead.
   * The functions mbedtls_cipher_auth_encrypt() and
     mbedtls_cipher_auth_decrypt() are deprecated in favour of the new
     functions mbedtls_cipher_auth_encrypt_ext() and
     mbedtls_cipher_auth_decrypt_ext(). Please note that with AEAD ciphers,
     these new functions always append the tag to the ciphertext, and include
     the tag in the ciphertext length.

Features
   * Partial implementation of the new PSA Crypto accelerator APIs. (Symmetric
     ciphers, asymmetric signing/verification and key generation, validate_key
     entry point, and export_public_key interface.)
   * Add support for ECB to the PSA cipher API.
   * In PSA, allow using a key declared with a base key agreement algorithm
     in combined key agreement and derivation operations, as long as the key
     agreement algorithm in use matches the algorithm the key was declared with.
     This is currently non-standard behaviour, but expected to make it into a
     future revision of the PSA Crypto standard.
   * Add MBEDTLS_TARGET_PREFIX CMake variable, which is prefixed to the mbedtls,
     mbedcrypto, mbedx509 and apidoc CMake target names. This can be used by
     external CMake projects that include this one to avoid CMake target name
     clashes.  The default value of this variable is "", so default target names
     are unchanged.
   * Add support for DTLS-SRTP as defined in RFC 5764. Contributed by Johan
     Pascal, improved by Ron Eldor.
   * In the PSA API, it is no longer necessary to open persistent keys:
     operations now accept the key identifier. The type psa_key_handle_t is now
     identical to psa_key_id_t instead of being platform-defined. This bridges
     the last major gap to compliance with the PSA Cryptography specification
     version 1.0.0. Opening persistent keys is still supported for backward
     compatibility, but will be deprecated and later removed in future
     releases.
   * PSA_AEAD_NONCE_LENGTH, PSA_AEAD_NONCE_MAX_SIZE, PSA_CIPHER_IV_LENGTH and
     PSA_CIPHER_IV_MAX_SIZE macros have been added as defined in version
     1.0.0 of the PSA Crypto API specification.

Security
   * The functions mbedtls_cipher_auth_encrypt() and
     mbedtls_cipher_auth_decrypt() would write past the minimum documented
     size of the output buffer when used with NIST_KW. As a result, code using
     those functions as documented with NIST_KW could have a buffer overwrite
     of up to 15 bytes, with consequences ranging up to arbitrary code
     execution depending on the location of the output buffer.
   * Limit the size of calculations performed by mbedtls_mpi_exp_mod to
     MBEDTLS_MPI_MAX_SIZE to prevent a potential denial of service when
     generating Diffie-Hellman key pairs. Credit to OSS-Fuzz.
   * A failure of the random generator was ignored in mbedtls_mpi_fill_random(),
     which is how most uses of randomization in asymmetric cryptography
     (including key generation, intermediate value randomization and blinding)
     are implemented. This could cause failures or the silent use of non-random
     values. A random generator can fail if it needs reseeding and cannot not
     obtain entropy, or due to an internal failure (which, for Mbed TLS's own
     CTR_DRBG or HMAC_DRBG, can only happen due to a misconfiguration).
   * Fix a compliance issue whereby we were not checking the tag on the
     algorithm parameters (only the size) when comparing the signature in the
     description part of the cert to the real signature. This meant that a
     NULL algorithm parameters entry would look identical to an array of REAL
     (size zero) to the library and thus the certificate would be considered
     valid. However, if the parameters do not match in *any* way then the
     certificate should be considered invalid, and indeed OpenSSL marks these
     certs as invalid when mbedtls did not.
     Many thanks to guidovranken who found this issue via differential fuzzing
     and reported it in #3629.
   * Zeroising of local buffers and variables which are used for calculations
     in mbedtls_pkcs5_pbkdf2_hmac(), mbedtls_internal_sha*_process(),
     mbedtls_internal_md*_process() and mbedtls_internal_ripemd160_process()
     functions to erase sensitive data from memory. Reported by
     Johan Malmgren and Johan Uppman Bruce from Sectra.

Bugfix
   * Fix an invalid (but nonzero) return code from mbedtls_pk_parse_subpubkey()
     when the input has trailing garbage. Fixes #2512.
   * Fix build failure in configurations where MBEDTLS_USE_PSA_CRYPTO is
     enabled but ECDSA is disabled. Contributed by jdurkop. Fixes #3294.
   * Include the psa_constant_names generated source code in the source tree
     instead of generating it at build time. Fixes #3524.
   * Fix rsa_prepare_blinding() to retry when the blinding value is not
     invertible (mod N), instead of returning MBEDTLS_ERR_RSA_RNG_FAILED. This
     addresses a regression but is rare in practice (approx. 1 in 2/sqrt(N)).
     Found by Synopsys Coverity, fix contributed by Peter Kolbus (Garmin).
     Fixes #3647.
   * Use socklen_t on Android and other POSIX-compliant system
   * Fix the build when the macro _GNU_SOURCE is defined to a non-empty value.
     Fix #3432.
   * Consistently return PSA_ERROR_INVALID_ARGUMENT on invalid cipher input
     sizes (instead of PSA_ERROR_BAD_STATE in some cases) to make the
     psa_cipher_* functions compliant with the PSA Crypto API specification.
   * mbedtls_ecp_curve_list() now lists Curve25519 and Curve448 under the names
     "x25519" and "x448". These curves support ECDH but not ECDSA. If you need
     only the curves that support ECDSA, filter the list with
     mbedtls_ecdsa_can_do().
   * Fix psa_generate_key() returning an error when asked to generate
     an ECC key pair on Curve25519 or secp244k1.
   * Fix psa_key_derivation_output_key() to allow the output of a combined key
     agreement and subsequent key derivation operation to be used as a key
     inside of the PSA Crypto core.
   * Fix handling of EOF against 0xff bytes and on platforms with unsigned
     chars. Fixes a build failure on platforms where char is unsigned. Fixes
     #3794.
   * Fix an off-by-one error in the additional data length check for
     CCM, which allowed encryption with a non-standard length field.
     Fixes #3719.
   * Correct the default IV size for mbedtls_cipher_info_t structures using
     MBEDTLS_MODE_ECB to 0, since ECB mode ciphers don't use IVs.
   * Make arc4random_buf available on NetBSD and OpenBSD when _POSIX_C_SOURCE is
     defined. Fix contributed in #3571.
   * Fix conditions for including string.h in error.c. Fixes #3866.
   * psa_set_key_id() now also sets the lifetime to persistent for keys located
     in a secure element.
   * Attempting to create a volatile key with a non-zero key identifier now
     fails. Previously the key identifier was just ignored when creating a
     volatile key.
   * Attempting to create or register a key with a key identifier in the vendor
     range now fails.
   * Fix build failures on GCC 11. Fixes #3782.
   * Add missing arguments of debug message in mbedtls_ssl_decrypt_buf.
   * Fix a memory leak in mbedtls_mpi_sub_abs() when the result was negative
     (an error condition) and the second operand was aliased to the result.
   * Fix a case in elliptic curve arithmetic where an out-of-memory condition
     could go undetected, resulting in an incorrect result.
   * In CTR_DRBG and HMAC_DRBG, don't reset the reseed interval in seed().
     Fixes #2927.
   * In PEM writing functions, fill the trailing part of the buffer with null
     bytes. This guarantees that the corresponding parsing function can read
     the buffer back, which was the case for mbedtls_x509write_{crt,csr}_pem
     until this property was inadvertently broken in Mbed TLS 2.19.0.
     Fixes #3682.
   * Fix a build failure that occurred with the MBEDTLS_AES_SETKEY_DEC_ALT
     option on. In this configuration key management methods that are required
     for MBEDTLS_CIPHER_MODE_XTS were excluded from the build and made it fail.
     Fixes #3818. Reported by John Stroebel.

Changes
   * Reduce stack usage significantly during sliding window exponentiation.
     Reported in #3591 and fix contributed in #3592 by Daniel Otte.
   * The PSA persistent storage format is updated to always store the key bits
     attribute. No automatic upgrade path is provided. Previously stored keys
     must be erased, or manually upgraded based on the key storage format
     specification (docs/architecture/mbed-crypto-storage-specification.md).
     Fixes #3740.
   * Remove the zeroization of a pointer variable in AES rounds. It was valid
     but spurious and misleading since it looked like a mistaken attempt to
     zeroize the pointed-to buffer. Reported by Antonio de la Piedra, CEA
     Leti, France.

= mbed TLS 2.24.0 branch released 2020-09-01

API changes
   * In the PSA API, rename the types of elliptic curve and Diffie-Hellman
     group families to psa_ecc_family_t and psa_dh_family_t, in line with the
     PSA Crypto API specification version 1.0.0.
     Rename associated macros as well:
     PSA_ECC_CURVE_xxx renamed to PSA_ECC_FAMILY_xxx
     PSA_DH_GROUP_xxx renamed to PSA_DH_FAMILY_xxx
     PSA_KEY_TYPE_GET_CURVE renamed to to PSA_KEY_TYPE_ECC_GET_FAMILY
     PSA_KEY_TYPE_GET_GROUP renamed to PSA_KEY_TYPE_DH_GET_FAMILY

Default behavior changes
   * Stop storing persistent information about externally stored keys created
     through PSA Crypto with a volatile lifetime. Reported in #3288 and
     contributed by Steven Cooreman in #3382.

Features
   * The new function mbedtls_ecp_write_key() exports private ECC keys back to
     a byte buffer. It is the inverse of the existing mbedtls_ecp_read_key().
   * Support building on e2k (Elbrus) architecture: correctly enable
     -Wformat-signedness, and fix the code that causes signed-one-bit-field
     and sign-compare warnings. Contributed by makise-homura (Igor Molchanov)
     <<EMAIL>>.

Security
   * Fix a vulnerability in the verification of X.509 certificates when
     matching the expected common name (the cn argument of
     mbedtls_x509_crt_verify()) with the actual certificate name: when the
     subjecAltName extension is present, the expected name was compared to any
     name in that extension regardless of its type. This means that an
     attacker could for example impersonate a 4-bytes or 16-byte domain by
     getting a certificate for the corresponding IPv4 or IPv6 (this would
     require the attacker to control that IP address, though). Similar attacks
     using other subjectAltName name types might be possible. Found and
     reported by kFYatek in #3498.
   * When checking X.509 CRLs, a certificate was only considered as revoked if
     its revocationDate was in the past according to the local clock if
     available. In particular, on builds without MBEDTLS_HAVE_TIME_DATE,
     certificates were never considered as revoked. On builds with
     MBEDTLS_HAVE_TIME_DATE, an attacker able to control the local clock (for
     example, an untrusted OS attacking a secure enclave) could prevent
     revocation of certificates via CRLs. Fixed by no longer checking the
     revocationDate field, in accordance with RFC 5280. Reported by
     yuemonangong in #3340. Reported independently and fixed by
     Raoul Strackx and Jethro Beekman in #3433.
   * In (D)TLS record decryption, when using a CBC ciphersuites without the
     Encrypt-then-Mac extension, use constant code flow memory access patterns
     to extract and check the MAC. This is an improvement to the existing
     countermeasure against Lucky 13 attacks. The previous countermeasure was
     effective against network-based attackers, but less so against local
     attackers. The new countermeasure defends against local attackers, even
     if they have access to fine-grained measurements. In particular, this
     fixes a local Lucky 13 cache attack found and reported by Tuba Yavuz,
     Farhaan Fowze, Ken (Yihan) Bai, Grant Hernandez, and Kevin Butler
     (University of Florida) and Dave Tian (Purdue University).
   * Fix side channel in RSA private key operations and static (finite-field)
     Diffie-Hellman. An adversary with precise enough timing and memory access
     information (typically an untrusted operating system attacking a secure
     enclave) could bypass an existing counter-measure (base blinding) and
     potentially fully recover the private key.
   * Fix a 1-byte buffer overread in mbedtls_x509_crl_parse_der().
     Credit to OSS-Fuzz for detecting the problem and to Philippe Antoine
     for pinpointing the problematic code.
   * Zeroising of plaintext buffers in mbedtls_ssl_read() to erase unused
     application data from memory. Reported in #689 by
     Johan Uppman Bruce of Sectra.

Bugfix
   * Library files installed after a CMake build no longer have execute
     permission.
   * Use local labels in mbedtls_padlock_has_support() to fix an invalid symbol
     redefinition if the function is inlined.
     Reported in #3451 and fix contributed in #3452 by okhowang.
   * Fix the endianness of Curve25519 keys imported/exported through the PSA
     APIs. psa_import_key and psa_export_key will now correctly expect/output
     Montgomery keys in little-endian as defined by RFC7748. Contributed by
     Steven Cooreman in #3425.
   * Fix build errors when the only enabled elliptic curves are Montgomery
     curves. Raised by signpainter in #941 and by Taiki-San in #1412. This
     also fixes missing declarations reported by Steven Cooreman in #1147.
   * Fix self-test failure when the only enabled short Weierstrass elliptic
     curve is secp192k1. Fixes #2017.
   * PSA key import will now correctly import a Curve25519/Curve448 public key
     instead of erroring out. Contributed by Steven Cooreman in #3492.
   * Use arc4random_buf on NetBSD instead of rand implementation with cyclical
     lower bits. Fix contributed in #3540.
   * Fix a memory leak in mbedtls_md_setup() when using HMAC under low memory
     conditions. Reported and fix suggested by Guido Vranken in #3486.
   * Fix bug in redirection of unit test outputs on platforms where stdout is
     defined as a macro. First reported in #2311 and fix contributed in #3528.

Changes
   * Only pass -Wformat-signedness to versions of GCC that support it. Reported
     in #3478 and fix contributed in #3479 by okhowang.
   * Reduce the stack consumption of mbedtls_x509write_csr_der() which
     previously could lead to stack overflow on constrained devices.
     Contributed by Doru Gucea and Simon Leet in #3464.
   * Undefine the ASSERT macro before defining it locally, in case it is defined
     in a platform header. Contributed by Abdelatif Guettouche in #3557.
   * Update copyright notices to use Linux Foundation guidance. As a result,
     the copyright of contributors other than Arm is now acknowledged, and the
     years of publishing are no longer tracked in the source files. This also
     eliminates the need for the lines declaring the files to be part of
     MbedTLS. Fixes #3457.
   * Add the command line parameter key_pwd to the ssl_client2 and ssl_server2
     example applications which allows to provide a password for the key file
     specified through the existing key_file argument. This allows the use of
     these applications with password-protected key files. Analogously but for
     ssl_server2 only, add the command line parameter key_pwd2 which allows to
     set a password for the key file provided through the existing key_file2
     argument.

= mbed TLS 2.23.0 branch released 2020-07-01

Default behavior changes
   * In the experimental PSA secure element interface, change the encoding of
     key lifetimes to encode a persistence level and the location. Although C
     prototypes do not effectively change, code calling
     psa_register_se_driver() must be modified to pass the driver's location
     instead of the keys' lifetime. If the library is upgraded on an existing
     device, keys created with the old lifetime value will not be readable or
     removable through Mbed TLS after the upgrade.

Features
   * New functions in the error module return constant strings for
     high- and low-level error codes, complementing mbedtls_strerror()
     which constructs a string for any error code, including compound
     ones, but requires a writable buffer. Contributed by Gaurav Aggarwal
     in #3176.
   * The new utility programs/ssl/ssl_context_info prints a human-readable
     dump of an SSL context saved with mbedtls_ssl_context_save().
   * Add support for midipix, a POSIX layer for Microsoft Windows.
   * Add new mbedtls_x509_crt_parse_der_with_ext_cb() routine which allows
     parsing unsupported certificate extensions via user provided callback.
     Contributed by Nicola Di Lieto <<EMAIL>> in #3243 as
     a solution to #3241.
   * Pass the "certificate policies" extension to the callback supplied to
     mbedtls_x509_crt_parse_der_with_ext_cb() if it contains unsupported
     policies (#3419).
   * Added support to entropy_poll for the kern.arandom syscall supported on
     some BSD systems. Contributed by Nia Alarie in #3423.
   * Add support for Windows 2000 in net_sockets. Contributed by opatomic. #3239

Security
   * Fix a side channel vulnerability in modular exponentiation that could
     reveal an RSA private key used in a secure enclave. Noticed by Sangho Lee,
     Ming-Wei Shih, Prasun Gera, Taesoo Kim and Hyesoon Kim (Georgia Institute
     of Technology); and Marcus Peinado (Microsoft Research). Reported by Raoul
     Strackx (Fortanix) in #3394.
   * Fix side channel in mbedtls_ecp_check_pub_priv() and
     mbedtls_pk_parse_key() / mbedtls_pk_parse_keyfile() (when loading a
     private key that didn't include the uncompressed public key), as well as
     mbedtls_ecp_mul() / mbedtls_ecp_mul_restartable() when called with a NULL
     f_rng argument. An attacker with access to precise enough timing and
     memory access information (typically an untrusted operating system
     attacking a secure enclave) could fully recover the ECC private key.
     Found and reported by Alejandro Cabrera Aldaya and Billy Brumley.
   * Fix issue in Lucky 13 counter-measure that could make it ineffective when
     hardware accelerators were used (using one of the MBEDTLS_SHAxxx_ALT
     macros). This would cause the original Lucky 13 attack to be possible in
     those configurations, allowing an active network attacker to recover
     plaintext after repeated timing measurements under some conditions.
     Reported and fix suggested by Luc Perneel in #3246.

Bugfix
   * Fix the Visual Studio Release x64 build configuration for mbedtls itself.
     Completes a previous fix in Mbed TLS 2.19 that only fixed the build for
     the example programs. Reported in #1430 and fix contributed by irwir.
   * Fix undefined behavior in X.509 certificate parsing if the
     pathLenConstraint basic constraint value is equal to INT_MAX.
     The actual effect with almost every compiler is the intended
     behavior, so this is unlikely to be exploitable anywhere. #3192
   * Fix issue with a detected HW accelerated record error not being exposed
     due to shadowed variable. Contributed by Sander Visser in #3310.
   * Avoid NULL pointer dereferencing if mbedtls_ssl_free() is called with a
     NULL pointer argument. Contributed by Sander Visser in #3312.
   * Fix potential linker errors on dual world platforms by inlining
     mbedtls_gcc_group_to_psa(). This allows the pk.c module to link separately
     from psa_crypto.c. Fixes #3300.
   * Remove dead code in X.509 certificate parsing. Contributed by irwir in
     #2855.
   * Include asn1.h in error.c. Fixes #3328 reported by David Hu.
   * Fix potential memory leaks in ecp_randomize_jac() and ecp_randomize_mxz()
     when PRNG function fails. Contributed by Jonas Lejeune in #3318.
   * Remove unused macros from MSVC projects. Reported in #3297 and fix
     submitted in #3333 by irwir.
   * Add additional bounds checks in ssl_write_client_hello() preventing
     output buffer overflow if the configuration declared a buffer that was
     too small.
   * Set _POSIX_C_SOURCE to at least 200112L in C99 code. Reported in #3420 and
     fix submitted in #3421 by Nia Alarie.
   * Fix building library/net_sockets.c and the ssl_mail_client program on
     NetBSD. Contributed by Nia Alarie in #3422.
   * Fix false positive uninitialised variable reported by cpp-check.
     Contributed by Sander Visser in #3311.
   * Update iv and len context pointers manually when reallocating buffers
     using the MBEDTLS_SSL_VARIABLE_BUFFER_LENGTH feature. This caused issues
     when receiving a connection with CID, when these fields were shifted
     in ssl_parse_record_header().

Changes
   * Fix warnings about signedness issues in format strings. The build is now
     clean of -Wformat-signedness warnings. Contributed by Kenneth Soerensen
     in #3153.
   * Fix minor performance issue in operations on Curve25519 caused by using a
     suboptimal modular reduction in one place. Found and fix contributed by
     Aurelien Jarno in #3209.
   * Combine identical cases in switch statements in md.c. Contributed
     by irwir in #3208.
   * Simplify a bounds check in ssl_write_certificate_request(). Contributed
     by irwir in #3150.
   * Unify the example programs termination to call mbedtls_exit() instead of
     using a return command. This has been done to enable customization of the
     behavior in bare metal environments.
   * Fix mbedtls_x509_dn_gets to escape non-ASCII characters as "?".
     Contributed by Koh M. Nakagawa in #3326.
   * Use FindPython3 when cmake version >= 3.15.0
   * Abort the ClientHello writing function as soon as some extension doesn't
     fit into the record buffer. Previously, such extensions were silently
     dropped. As a consequence, the TLS handshake now fails when the output
     buffer is not large enough to hold the ClientHello.
   * The unit tests now rely on header files in tests/include/test and source
     files in tests/src. When building with make or cmake, the files in
     tests/src are compiled and the resulting object linked into each test
     executable.
   * The ECP module, enabled by `MBEDTLS_ECP_C`, now depends on
     `MBEDTLS_CTR_DRBG_C` or `MBEDTLS_HMAC_DRBG_C` for some side-channel
     coutermeasures. If side channels are not a concern, this dependency can
     be avoided by enabling the new option `MBEDTLS_ECP_NO_INTERNAL_RNG`.
   * Align MSVC error flag with GCC and Clang. Contributed by Carlos Gomes
     Martinho. #3147
   * Remove superfluous assignment in mbedtls_ssl_parse_certificate(). Reported
     in #3182 and fix submitted by irwir. #3217
   * Fix typo in XTS tests. Reported and fix submitted by Kxuan. #3319

= mbed TLS 2.22.0 branch released 2020-04-14

New deprecations
   * Deprecate MBEDTLS_SSL_HW_RECORD_ACCEL that enables function hooks in the
     SSL module for hardware acceleration of individual records.
   * Deprecate mbedtls_ssl_get_max_frag_len() in favour of
     mbedtls_ssl_get_output_max_frag_len() and
     mbedtls_ssl_get_input_max_frag_len() to be more precise about which max
     fragment length is desired.

Security
   * Fix issue in DTLS handling of new associations with the same parameters
     (RFC 6347 section 4.2.8): an attacker able to send forged UDP packets to
     the server could cause it to drop established associations with
     legitimate clients, resulting in a Denial of Service. This could only
     happen when MBEDTLS_SSL_DTLS_CLIENT_PORT_REUSE was enabled in config.h
     (which it is by default).
   * Fix side channel in ECC code that allowed an adversary with access to
     precise enough timing and memory access information (typically an
     untrusted operating system attacking a secure enclave) to fully recover
     an ECDSA private key. Found and reported by Alejandro Cabrera Aldaya,
     Billy Brumley and Cesar Pereida Garcia. CVE-2020-10932
   * Fix a potentially remotely exploitable buffer overread in a
     DTLS client when parsing the Hello Verify Request message.

Features
   * The new build option MBEDTLS_SSL_VARIABLE_BUFFER_LENGTH automatically
     resizes the I/O buffers before and after handshakes, reducing the memory
     consumption during application data transfer.

Bugfix
   * Fix compilation failure when both MBEDTLS_SSL_PROTO_DTLS and
     MBEDTLS_SSL_HW_RECORD_ACCEL are enabled.
   * Remove a spurious check in ssl_parse_client_psk_identity that triggered
     a warning with some compilers. Fix contributed by irwir in #2856.
   * Fix a function name in a debug message. Contributed by Ercan Ozturk in
     #3013.

Changes
   * Mbed Crypto is no longer a Git submodule. The crypto part of the library
     is back directly in the present repository.
   * Split mbedtls_ssl_get_max_frag_len() into
     mbedtls_ssl_get_output_max_frag_len() and
     mbedtls_ssl_get_input_max_frag_len() to ensure that a sufficient input
     buffer is allocated by the server (if MBEDTLS_SSL_VARIABLE_BUFFER_LENGTH
     is defined), regardless of what MFL was configured for it.

= mbed TLS 2.21.0 branch released 2020-02-20

New deprecations
   * Deprecate MBEDTLS_SSL_SRV_SUPPORT_SSLV2_CLIENT_HELLO that enables parsing
     SSLv2 ClientHello messages.
   * Deprecate MBEDTLS_SSL_PROTO_SSL3 that enables support for SSLv3.
   * Deprecate for MBEDTLS_PKCS11_C, the wrapper around the pkcs11-helper
     library which allows TLS authentication to use keys stored in a
     PKCS#11 token such as a smartcard.

Security
   * Fix potential memory overread when performing an ECDSA signature
     operation. The overread only happens with cryptographically low
     probability (of the order of 2^-n where n is the bitsize of the curve)
     unless the RNG is broken, and could result in information disclosure or
     denial of service (application crash or extra resource consumption).
     Found by Auke Zeilstra and Peter Schwabe, using static analysis.
   * To avoid a side channel vulnerability when parsing an RSA private key,
     read all the CRT parameters from the DER structure rather than
     reconstructing them. Found by Alejandro Cabrera Aldaya and Billy Bob
     Brumley. Reported and fix contributed by Jack Lloyd.
     ARMmbed/mbed-crypto#352

Features
   * The new build option MBEDTLS_SHA512_NO_SHA384 allows building SHA-512
     support without SHA-384.

API changes
   * Change the encoding of key types and curves in the PSA API. The new
     values are aligned with the upcoming release of the PSA Crypto API
     specification version 1.0.0. The main change which may break some
     existing code is that elliptic curve key types no longer encode the
     exact curve: a psa_ecc_curve_t or psa_key_type_t value only encodes
     a curve family and the key size determines the exact curve (for example,
     PSA_ECC_CURVE_SECP_R1 with 256 bits is P256R1). ARMmbed/mbed-crypto#330

Bugfix
   * Fix an unchecked call to mbedtls_md() in the x509write module.
   * Fix build failure with MBEDTLS_ZLIB_SUPPORT enabled. Reported by
     Jack Lloyd in #2859. Fix submitted by jiblime in #2963.
   * Fix some false-positive uninitialized variable warnings in X.509. Fix
     contributed by apple-ihack-geek in #2663.
   * Fix a possible error code mangling in psa_mac_verify_finish() when
     a cryptographic accelerator fails. ARMmbed/mbed-crypto#345
   * Fix a bug in mbedtls_pk_parse_key() that would cause it to accept some
     RSA keys that would later be rejected by functions expecting private
     keys. Found by Catena cyber using oss-fuzz (issue 20467).
   * Fix a bug in mbedtls_pk_parse_key() that would cause it to
     accept some RSA keys with invalid values by silently fixing those values.

= mbed TLS 2.20.0 branch released 2020-01-15

Default behavior changes
   * The initial seeding of a CTR_DRBG instance makes a second call to the
     entropy function to obtain entropy for a nonce if the entropy size is less
     than 3/2 times the key size. In case you want to disable the extra call to
     grab entropy, you can call mbedtls_ctr_drbg_set_nonce_len() to force the
     nonce length to 0.

Security
   * Enforce that mbedtls_entropy_func() gathers a total of
     MBEDTLS_ENTROPY_BLOCK_SIZE bytes or more from strong sources. In the
     default configuration, on a platform with a single entropy source, the
     entropy module formerly only grabbed 32 bytes, which is good enough for
     security if the source is genuinely strong, but less than the expected 64
     bytes (size of the entropy accumulator).
   * Zeroize local variables in mbedtls_internal_aes_encrypt() and
     mbedtls_internal_aes_decrypt() before exiting the function. The value of
     these variables can be used to recover the last round key. To follow best
     practice and to limit the impact of buffer overread vulnerabilities (like
     Heartbleed) we need to zeroize them before exiting the function.
     Issue reported by Tuba Yavuz, Farhaan Fowze, Ken (Yihang) Bai,
     Grant Hernandez, and Kevin Butler (University of Florida) and
     Dave Tian (Purdue University).
   * Fix side channel vulnerability in ECDSA. Our bignum implementation is not
     constant time/constant trace, so side channel attacks can retrieve the
     blinded value, factor it (as it is smaller than RSA keys and not guaranteed
     to have only large prime factors), and then, by brute force, recover the
     key. Reported by Alejandro Cabrera Aldaya and Billy Brumley.
   * Fix side channel vulnerability in ECDSA key generation. Obtaining precise
     timings on the comparison in the key generation enabled the attacker to
     learn leading bits of the ephemeral key used during ECDSA signatures and to
     recover the private key. Reported by Jeremy Dubeuf.
   * Catch failure of AES functions in mbedtls_ctr_drbg_random(). Uncaught
     failures could happen with alternative implementations of AES. Bug
     reported and fix proposed by Johan Uppman Bruce and Christoffer Lauri,
     Sectra.

Features
   * Key derivation inputs in the PSA API can now either come from a key object
     or from a buffer regardless of the step type.
   * The CTR_DRBG module can grab a nonce from the entropy source during the
     initial seeding. The default nonce length is chosen based on the key size
     to achieve the security strength defined by NIST SP 800-90A. You can
     change it with mbedtls_ctr_drbg_set_nonce_len().
   * Add ENUMERATED tag support to the ASN.1 module. Contributed by
     msopiha-linaro in ARMmbed/mbed-crypto#307.

API changes
   * In the PSA API, forbid zero-length keys. To pass a zero-length input to a
     key derivation function, use a buffer instead (this is now always
     possible).
   * Rename psa_asymmetric_sign() to psa_sign_hash() and
     psa_asymmetric_verify() to psa_verify_hash().

Bugfix
   * Fix an incorrect size in a debugging message. Reported and fix
     submitted by irwir. Fixes #2717.
   * Fix an unused variable warning when compiling without DTLS.
     Reported and fix submitted by irwir. Fixes #2800.
   * Remove a useless assignment. Reported and fix submitted by irwir.
     Fixes #2801.
   * Fix a buffer overflow in the PSA HMAC code when using a long key with an
     unsupported algorithm. Fixes ARMmbed/mbed-crypto#254.
   * Fix mbedtls_asn1_get_int to support any number of leading zeros. Credit
     to OSS-Fuzz for finding a bug in an intermediate version of the fix.
   * Fix mbedtls_asn1_get_bitstring_null to correctly parse bitstrings of at
     most 2 bytes.
   * mbedtls_ctr_drbg_set_entropy_len() and
     mbedtls_hmac_drbg_set_entropy_len() now work if you call them before
     mbedtls_ctr_drbg_seed() or mbedtls_hmac_drbg_seed().

Changes
   * Remove the technical possibility to define custom mbedtls_md_info
     structures, which was exposed only in an internal header.
   * psa_close_key(0) and psa_destroy_key(0) now succeed (doing nothing, as
     before).
   * Variables containing error codes are now initialized to an error code
     rather than success, so that coding mistakes or memory corruption tends to
     cause functions to return this error code rather than a success. There are
     no known instances where this changes the behavior of the library: this is
     merely a robustness improvement. ARMmbed/mbed-crypto#323
   * Remove a useless call to mbedtls_ecp_group_free(). Contributed by
     Alexander Krizhanovsky in ARMmbed/mbed-crypto#210.
   * Speed up PBKDF2 by caching the digest calculation. Contributed by Jack
     Lloyd and Fortanix Inc in ARMmbed/mbed-crypto#277.
   * Small performance improvement of mbedtls_mpi_div_mpi(). Contributed by
     Alexander Krizhanovsky in ARMmbed/mbed-crypto#308.

= mbed TLS 2.19.1 branch released 2019-09-16

Features
   * Declare include headers as PUBLIC to propagate to CMake project consumers
     Contributed by Zachary J. Fields in PR #2949.
   * Add nss_keylog to ssl_client2 and ssl_server2, enabling easier analysis of
     TLS sessions with tools like Wireshark.

API Changes
   * Make client_random and server_random const in
     mbedtls_ssl_export_keys_ext_t, so that the key exporter is discouraged
     from modifying the client/server hello.

Bugfix
   * Fix some false-positive uninitialized variable warnings in crypto. Fix
     contributed by apple-ihack-geek in #2663.

= mbed TLS 2.19.0 branch released 2019-09-06

Security
   * Fix a missing error detection in ECJPAKE. This could have caused a
     predictable shared secret if a hardware accelerator failed and the other
     side of the key exchange had a similar bug.
   * When writing a private EC key, use a constant size for the private
     value, as specified in RFC 5915. Previously, the value was written
     as an ASN.1 INTEGER, which caused the size of the key to leak
     about 1 bit of information on average and could cause the value to be
     1 byte too large for the output buffer.
   * The deterministic ECDSA calculation reused the scheme's HMAC-DRBG to
     implement blinding. Because of this for the same key and message the same
     blinding value was generated. This reduced the effectiveness of the
     countermeasure and leaked information about the private key through side
     channels. Reported by Jack Lloyd.

Features
   * Add new API functions mbedtls_ssl_session_save() and
     mbedtls_ssl_session_load() to allow serializing a session, for example to
     store it in non-volatile storage, and later using it for TLS session
     resumption.
   * Add a new API function mbedtls_ssl_check_record() to allow checking that
     an incoming record is valid, authentic and has not been seen before. This
     feature can be used alongside Connection ID and SSL context serialisation.
     The feature is enabled at compile-time by MBEDTLS_SSL_RECORD_CHECKING
     option.
   * New implementation of X25519 (ECDH using Curve25519) from Project Everest
     (https://project-everest.github.io/). It can be enabled at compile time
     with MBEDTLS_ECDH_VARIANT_EVEREST_ENABLED. This implementation is formally
     verified and significantly faster, but is only supported on x86 platforms
     (32-bit and 64-bit) using GCC, Clang or Visual Studio. Contributed by
     Christoph Wintersteiger from Microsoft Research.
   * Add mbedtls_net_close(), enabling the building of forking servers where
     the parent process closes the client socket and continue accepting, and
     the child process closes the listening socket and handles the client
     socket. Contributed by Robert Larsen in #2803.

API Changes
   * Add DER-encoded test CRTs to library/certs.c, allowing
     the example programs ssl_server2 and ssl_client2 to be run
     if MBEDTLS_FS_IO and MBEDTLS_PEM_PARSE_C are unset. Fixes #2254.
   * The HAVEGE state type now uses uint32_t elements instead of int.
   * The functions mbedtls_ecp_curve_list() and mbedtls_ecp_grp_id_list() now
     list all curves for which at least one of ECDH or ECDSA is supported, not
     just curves for which both are supported. Call mbedtls_ecdsa_can_do() or
     mbedtls_ecdh_can_do() on each result to check whether each algorithm is
     supported.
   * The new function mbedtls_ecdsa_sign_det_ext() is similar to
     mbedtls_ecdsa_sign_det() but allows passing an external RNG for the
     purpose of blinding.

New deprecations
   * Deprecate mbedtls_ecdsa_sign_det() in favor of a functions that can take an
     RNG function as an input.
   * Calling mbedtls_ecdsa_write_signature() with NULL as the f_rng argument
     is now deprecated.

Bugfix
   * Fix missing bounds checks in X.509 parsing functions that could
     lead to successful parsing of ill-formed X.509 CRTs. Fixes #2437.
   * Fix multiple X.509 functions previously returning ASN.1 low-level error
     codes to always wrap these codes into X.509 high level error codes before
     returning. Fixes #2431.
   * Fix to allow building test suites with any warning that detects unused
     functions. Fixes #1628.
   * Fix typo in net_would_block(). Fixes #528 reported by github-monoculture.
   * Remove redundant include file in timing.c. Fixes #2640 reported by irwir.
   * Fix build failure when building with mingw on Windows by including
     stdarg.h where needed. Fixes #2656.
   * Fix Visual Studio Release x64 build configuration by inheriting
     PlatformToolset from the project configuration. Fixes #1430 reported by
     irwir.
   * Enable Suite B with subset of ECP curves. Make sure the code compiles even
     if some curves are not defined. Fixes #1591 reported by dbedev.
   * Fix misuse of signed arithmetic in the HAVEGE module. #2598
   * Avoid use of statically sized stack buffers for certificate writing.
     This previously limited the maximum size of DER encoded certificates
     in mbedtls_x509write_crt_der() to 2Kb. Reported by soccerGB in #2631.
   * Fix partial zeroing in x509_get_other_name. Found and fixed by ekse, #2716.
   * Update test certificates that were about to expire. Reported by
     Bernhard M. Wiedemann in #2357.
   * Fix the build on ARMv5TE in ARM mode to not use assembly instructions
     that are only available in Thumb mode. Fix contributed by Aurelien Jarno
     in #2169.
   * Fix propagation of restart contexts in restartable EC operations.
     This could previously lead to segmentation faults in builds using an
     address-sanitizer and enabling but not using MBEDTLS_ECP_RESTARTABLE.
   * Fix memory leak in in mpi_miller_rabin(). Contributed by
     Jens Wiklander <<EMAIL>> in #2363
   * Improve code clarity in x509_crt module, removing false-positive
     uninitialized variable warnings on some recent toolchains (GCC8, etc).
     Discovered and fixed by Andy Gross (Linaro), #2392.
   * Fix bug in endianness conversion in bignum module. This lead to
     functionally incorrect code on bigendian systems which don't have
     __BYTE_ORDER__ defined. Reported by Brendan Shanks. Fixes #2622.

Changes
   * Replace multiple uses of MD2 by SHA-256 in X.509 test suite. Fixes #821.
   * Make it easier to define MBEDTLS_PARAM_FAILED as assert (which config.h
     suggests). #2671
   * Make `make clean` clean all programs always. Fixes #1862.
   * Add a Dockerfile and helper scripts (all-in-docker.sh, basic-in-docker.sh,
     docker-env.sh) to simplify running test suites on a Linux host. Contributed
     by Peter Kolbus (Garmin).
   * Add `reproducible` option to `ssl_client2` and `ssl_server2` to enable
     test runs without variability. Contributed by Philippe Antoine (Catena
     cyber) in #2681.
   * Extended .gitignore to ignore Visual Studio artifacts. Fixed by ConfusedSushi.
   * Adds fuzz targets, especially for continuous fuzzing with OSS-Fuzz.
     Contributed by Philippe Antoine (Catena cyber).
   * Remove the crypto part of the library from Mbed TLS. The crypto
     code and tests are now only available via Mbed Crypto, which
     Mbed TLS references as a Git submodule.

= mbed TLS 2.18.1 branch released 2019-07-12

Bugfix
   * Fix build failure when building with mingw on Windows by including
     stdarg.h where needed. Fixes #2656.

Changes
   * Enable building of Mbed TLS as a CMake subproject. Suggested and fixed by
     Ashley Duncan in #2609.

= mbed TLS 2.18.0 branch released 2019-06-11

Features
   * Add the Any Policy certificate policy oid, as defined in
     rfc 5280 section *******.
   * It is now possible to use NIST key wrap mode via the mbedtls_cipher API.
     Contributed by Jack Lloyd and Fortanix Inc.
   * Add the Wi-SUN Field Area Network (FAN) device extended key usage.
   * Add the oid certificate policy x509 extension.
   * It is now possible to perform RSA PKCS v1.5 signatures with RIPEMD-160 digest.
     Contributed by Jack Lloyd and Fortanix Inc.
   * Extend the MBEDTLS_SSL_EXPORT_KEYS to export the handshake randbytes,
     and the used tls-prf.
   * Add public API for tls-prf function, according to requested enum.
   * Add support for parsing otherName entries in the Subject Alternative Name
     X.509 certificate extension, specifically type hardware module name,
     as defined in RFC 4108 section 5.
   * Add support for parsing certificate policies extension, as defined in
     RFC 5280 section *******. Currently, only the "Any Policy" policy is
     supported.
   * List all SAN types in the subject_alt_names field of the certificate.
     Resolves #459.
   * Add support for draft-05 of the Connection ID extension, as specified
     in https://tools.ietf.org/html/draft-ietf-tls-dtls-connection-id-05.
     The Connection ID extension allows to keep DTLS connections beyond the
     lifetime of the underlying transport by adding a connection identifier
     to the DTLS record header. This identifier can be used to associated an
     incoming record with the correct connection data even after the peer has
     changed its IP or port. The feature is enabled at compile-time by setting
     MBEDTLS_SSL_DTLS_CONNECTION_ID (disabled by default), and at run-time
     through the new APIs mbedtls_ssl_conf_cid() and mbedtls_ssl_set_cid().


API Changes
   * Extend the MBEDTLS_SSL_EXPORT_KEYS to export the handshake randbytes,
     and the used tls-prf.
   * Add public API for tls-prf function, according to requested enum.

Bugfix
   * Fix private key DER output in the key_app_writer example. File contents
     were shifted by one byte, creating an invalid ASN.1 tag. Fixed by
     Christian Walther in #2239.
   * Fix potential memory leak in X.509 self test. Found and fixed by
     Junhwan Park, #2106.
   * Reduce stack usage of hkdf tests. Fixes #2195.
   * Fix 1-byte buffer overflow in mbedtls_mpi_write_string() when
     used with negative inputs. Found by Guido Vranken in #2404. Credit to
     OSS-Fuzz.
   * Fix bugs in the AEAD test suite which would be exposed by ciphers which
     either used both encrypt and decrypt key schedules, or which perform padding.
     GCM and CCM were not affected. Fixed by Jack Lloyd.
   * Fix incorrect default port number in ssl_mail_client example's usage.
     Found and fixed by irwir. #2337
   * Add psa_util.h to test/cpp_dummy_build to fix build_default_make_gcc_and_cxx.
     Fixed by Peter Kolbus (Garmin). #2579
   * Add missing parentheses around parameters in the definition of the
     public macro MBEDTLS_X509_ID_FLAG. This could lead to invalid evaluation
     in case operators binding less strongly than subtraction were used
     for the parameter.
   * Add a check for MBEDTLS_X509_CRL_PARSE_C in ssl_server2, guarding the crl
     sni entry parameter. Reported by inestlerode in #560.
   * Set the next sequence of the subject_alt_name to NULL when deleting
     sequence on failure. Found and fix suggested by Philippe Antoine.
     Credit to OSS-Fuzz.

Changes
   * Server's RSA certificate in certs.c was SHA-1 signed. In the default
     mbedTLS configuration only SHA-2 signed certificates are accepted.
     This certificate is used in the demo server programs, which lead the
     client programs to fail at the peer's certificate verification
     due to an unacceptable hash signature. The certificate has been
     updated to one that is SHA-256 signed. Fix contributed by
     Illya Gerasymchuk.
   * Return from various debugging routines immediately if the
     provided SSL context is unset.
   * Remove dead code from bignum.c in the default configuration.
     Found by Coverity, reported and fixed by Peter Kolbus (Garmin). Fixes #2309.
   * Add test for minimal value of MBEDTLS_MPI_WINDOW_SIZE to all.sh.
     Contributed by Peter Kolbus (Garmin).
   * Change wording in the `mbedtls_ssl_conf_max_frag_len()`'s documentation to
     improve clarity. Fixes #2258.

= mbed TLS 2.17.0 branch released 2019-03-19

Features
   * Add a new X.509 API call `mbedtls_x509_parse_der_nocopy()`
     which allows copy-less parsing of DER encoded X.509 CRTs,
     at the cost of additional lifetime constraints on the input
     buffer, but at the benefit of reduced RAM consumption.
   * Add a new function mbedtls_asn1_write_named_bitstring() to write ASN.1
     named bitstring in DER as required by RFC 5280 Appendix B.
   * Add MBEDTLS_REMOVE_3DES_CIPHERSUITES to allow removing 3DES ciphersuites
     from the default list (enabled by default). See
     https://sweet32.info/SWEET32_CCS16.pdf.

API Changes
   * Add a new X.509 API call `mbedtls_x509_parse_der_nocopy()`.
     See the Features section for more information.
   * Allow to opt in to the removal the API mbedtls_ssl_get_peer_cert()
     for the benefit of saving RAM, by disabling the new compile-time
     option MBEDTLS_SSL_KEEP_PEER_CERTIFICATE (enabled by default for
     API stability). Disabling this option makes mbedtls_ssl_get_peer_cert()
     always return NULL, and removes the peer_cert field from the
     mbedtls_ssl_session structure which otherwise stores the peer's
     certificate.

Security
   * Make mbedtls_ecdh_get_params return an error if the second key
     belongs to a different group from the first. Before, if an application
     passed keys that belonged to different group, the first key's data was
     interpreted according to the second group, which could lead to either
     an error or a meaningless output from mbedtls_ecdh_get_params. In the
     latter case, this could expose at most 5 bits of the private key.

Bugfix
   * Fix a compilation issue with mbedtls_ecp_restart_ctx not being defined
     when MBEDTLS_ECP_ALT is defined. Reported by jwhui. Fixes #2242.
   * Run the AD too long test only if MBEDTLS_CCM_ALT is not defined.
     Raised as a comment in #1996.
   * Reduce the stack consumption of mbedtls_mpi_fill_random() which could
     previously lead to a stack overflow on constrained targets.
   * Add `MBEDTLS_SELF_TEST` for the mbedtls_self_test functions
     in the header files, which missed the precompilation check. #971
   * Fix returning the value 1 when mbedtls_ecdsa_genkey failed.
   * Remove a duplicate #include in a sample program. Fixed by Masashi Honma #2326.
   * Remove the mbedtls namespacing from the header file, to fix a "file not found"
     build error. Fixed by Haijun Gu #2319.
   * Fix signed-to-unsigned integer conversion warning
     in X.509 module. Fixes #2212.
   * Reduce stack usage of `mpi_write_hlp()` by eliminating recursion.
     Fixes #2190.
   * Fix false failure in all.sh when backup files exist in include/mbedtls
     (e.g. config.h.bak). Fixed by Peter Kolbus (Garmin) #2407.
   * Ensure that unused bits are zero when writing ASN.1 bitstrings when using
     mbedtls_asn1_write_bitstring().
   * Fix issue when writing the named bitstrings in KeyUsage and NsCertType
     extensions in CSRs and CRTs that caused these bitstrings to not be encoded
     correctly as trailing zeroes were not accounted for as unused bits in the
     leading content octet. Fixes #1610.

Changes
   * Reduce RAM consumption during session renegotiation by not storing
     the peer CRT chain and session ticket twice.
   * Include configuration file in all header files that use configuration,
     instead of relying on other header files that they include.
     Inserted as an enhancement for #1371
   * Add support for alternative CSR headers, as used by Microsoft and defined
     in RFC 7468. Found by Michael Ernst. Fixes #767.
   * Correct many misspellings. Fixed by MisterDA #2371.
   * Provide an abstraction of vsnprintf to allow alternative implementations
     for platforms that don't provide it. Based on contributions by Joris Aerts
     and Nathaniel Wesley Filardo.
   * Fix clobber list in MIPS assembly for large integer multiplication.
     Previously, this could lead to functionally incorrect assembly being
     produced by some optimizing compilers, showing up as failures in
     e.g. RSA or ECC signature operations. Reported in #1722, fix suggested
     by Aurelien Jarno and submitted by Jeffrey Martin.
   * Reduce the complexity of the timing tests. They were assuming more than the
     underlying OS actually guarantees.
   * Fix configuration queries in ssl-opt.h. #2030
   * Ensure that ssl-opt.h can be run in OS X. #2029
   * Re-enable certain interoperability tests in ssl-opt.sh which had previously
     been disabled for lack of a sufficiently recent version of GnuTLS on the CI.
   * Ciphersuites based on 3DES now have the lowest priority by default when
     they are enabled.

= mbed TLS 2.16.0 branch released 2018-12-21

Features
   * Add a new config.h option of MBEDTLS_CHECK_PARAMS that enables validation
     of parameters in the API. This allows detection of obvious misuses of the
     API, such as passing NULL pointers. The API of existing functions hasn't
     changed, but requirements on parameters have been made more explicit in
     the documentation. See the corresponding API documentation for each
     function to see for which parameter values it is defined. This feature is
     disabled by default. See its API documentation in config.h for additional
     steps you have to take when enabling it.

API Changes
   * The following functions in the random generator modules have been
     deprecated and replaced as shown below. The new functions change
     the return type from void to int to allow returning error codes when
     using MBEDTLS_<MODULE>_ALT for the underlying AES or message digest
     primitive. Fixes #1798.
     mbedtls_ctr_drbg_update() -> mbedtls_ctr_drbg_update_ret()
     mbedtls_hmac_drbg_update() -> mbedtls_hmac_drbg_update_ret()
   * Extend ECDH interface to enable alternative implementations.
   * Deprecate error codes of the form MBEDTLS_ERR_xxx_INVALID_KEY_LENGTH for
     ARIA, CAMELLIA and Blowfish. These error codes will be replaced by
     the more generic per-module error codes MBEDTLS_ERR_xxx_BAD_INPUT_DATA.
   * Additional parameter validation checks have been added for the following
     modules - AES, ARIA, Blowfish, CAMELLIA, CCM, GCM, DHM, ECP, ECDSA, ECDH,
     ECJPAKE, SHA, Chacha20 and Poly1305, cipher, pk, RSA, and MPI.
     Where modules have had parameter validation added, existing parameter
     checks may have changed. Some modules, such as Chacha20 had existing
     parameter validation whereas other modules had little. This has now been
     changed so that the same level of validation is present in all modules, and
     that it is now optional with the MBEDTLS_CHECK_PARAMS flag which by default
     is off. That means that checks which were previously present by default
     will no longer be.

New deprecations
   * Deprecate mbedtls_ctr_drbg_update and mbedtls_hmac_drbg_update
     in favor of functions that can return an error code.

Bugfix
   * Fix for Clang, which was reporting a warning for the bignum.c inline
     assembly for AMD64 targets creating string literals greater than those
     permitted by the ISO C99 standard. Found by Aaron Jones. Fixes #482.
   * Fix runtime error in `mbedtls_platform_entropy_poll()` when run
     through qemu user emulation. Reported and fix suggested by randombit
     in #1212. Fixes #1212.
   * Fix an unsafe bounds check when restoring an SSL session from a ticket.
     This could lead to a buffer overflow, but only in case ticket authentication
     was broken. Reported and fix suggested by Guido Vranken in #659.
   * Add explicit integer to enumeration type casts to example program
     programs/pkey/gen_key which previously led to compilation failure
     on some toolchains. Reported by phoenixmcallister. Fixes #2170.
   * Fix double initialization of ECC hardware that made some accelerators
     hang.
   * Clarify documentation of mbedtls_ssl_set_own_cert() regarding the absence
     of check for certificate/key matching. Reported by Attila Molnar, #507.

 = mbed TLS 2.15.1 branch released 2018-11-30

 Changes
    * Update the Mbed Crypto submodule to version 0.1.0b2.

 = mbed TLS 2.15.0 branch released 2018-11-23

 Features
    * Add an experimental build option, USE_CRYPTO_SUBMODULE, to enable use of
      Mbed Crypto as the source of the cryptography implementation.
    * Add an experimental configuration option, MBEDTLS_PSA_CRYPTO_C, to enable
      the PSA Crypto API from Mbed Crypto when additionally used with the
      USE_CRYPTO_SUBMODULE build option.

 Changes
    * Add unit tests for AES-GCM when called through mbedtls_cipher_auth_xxx()
      from the cipher abstraction layer. Fixes #2198.

= mbed TLS 2.14.1 branch released 2018-11-30

Security
   * Fix timing variations and memory access variations in RSA PKCS#1 v1.5
     decryption that could lead to a Bleichenbacher-style padding oracle
     attack. In TLS, this affects servers that accept ciphersuites based on
     RSA decryption (i.e. ciphersuites whose name contains RSA but not
     (EC)DH(E)). Discovered by Eyal Ronen (Weizmann Institute),  Robert Gillham
     (University of Adelaide), Daniel Genkin (University of Michigan),
     Adi Shamir (Weizmann Institute), David Wong (NCC Group), and Yuval Yarom
     (University of Adelaide, Data61). The attack is described in more detail
     in the paper available here: http://cat.eyalro.net/cat.pdf  CVE-2018-19608
   * In mbedtls_mpi_write_binary(), don't leak the exact size of the number
     via branching and memory access patterns. An attacker who could submit
     a plaintext for RSA PKCS#1 v1.5 decryption but only observe the timing
     of the decryption and not its result could nonetheless decrypt RSA
     plaintexts and forge RSA signatures. Other asymmetric algorithms may
     have been similarly vulnerable. Reported by Eyal Ronen, Robert Gillham,
     Daniel Genkin, Adi Shamir, David Wong and Yuval Yarom.
   * Wipe sensitive buffers on the stack in the CTR_DRBG and HMAC_DRBG
     modules.

API Changes
   * The new functions mbedtls_ctr_drbg_update_ret() and
     mbedtls_hmac_drbg_update_ret() are similar to mbedtls_ctr_drbg_update()
     and mbedtls_hmac_drbg_update() respectively, but the new functions
     report errors whereas the old functions return void. We recommend that
     applications use the new functions.

= mbed TLS 2.14.0 branch released 2018-11-19

Security
   * Fix overly strict DN comparison when looking for CRLs belonging to a
     particular CA. This previously led to ignoring CRLs when the CRL's issuer
     name and the CA's subject name differed in their string encoding (e.g.,
     one using PrintableString and the other UTF8String) or in the choice of
     upper and lower case. Reported by Henrik Andersson of Bosch GmbH in issue
     #1784.
   * Fix a flawed bounds check in server PSK hint parsing. In case the
     incoming message buffer was placed within the first 64KiB of address
     space and a PSK-(EC)DHE ciphersuite was used, this allowed an attacker
     to trigger a memory access up to 64KiB beyond the incoming message buffer,
     potentially leading to an application crash or information disclosure.
   * Fix mbedtls_mpi_is_prime() to use more rounds of probabilistic testing. The
     previous settings for the number of rounds made it practical for an
     adversary to construct non-primes that would be erroneously accepted as
     primes with high probability. This does not have an impact on the
     security of TLS, but can matter in other contexts with numbers chosen
     potentially by an adversary that should be prime and can be validated.
     For example, the number of rounds was enough to securely generate RSA key
     pairs or Diffie-Hellman parameters, but was insufficient to validate
     Diffie-Hellman parameters properly.
     See "Prime and Prejudice" by by Martin R. Albrecht and Jake Massimo and
     Kenneth G. Paterson and Juraj Somorovsky.

Features
   * Add support for temporarily suspending expensive ECC computations after
     some configurable amount of operations. This is intended to be used in
     constrained, single-threaded systems where ECC is time consuming and can
     block other operations until they complete. This is disabled by default,
     but can be enabled by MBEDTLS_ECP_RESTARTABLE at compile time and
     configured by mbedtls_ecp_set_max_ops() at runtime. It applies to the new
     xxx_restartable functions in ECP, ECDSA, PK and X.509 (CRL not supported
     yet), and to existing functions in ECDH and SSL (currently only
     implemented client-side, for ECDHE-ECDSA ciphersuites in TLS 1.2,
     including client authentication).
   * Add support for Arm CPU DSP extensions to accelerate asymmetric key
     operations. On CPUs where the extensions are available, they can accelerate
     MPI multiplications used in ECC and RSA cryptography. Contributed by
     Aurelien Jarno.
   * Extend RSASSA-PSS signature to allow a smaller salt size. Previously, PSS
     signature always used a salt with the same length as the hash, and returned
     an error if this was not possible. Now the salt size may be up to two bytes
     shorter. This allows the library to support all hash and signature sizes
     that comply with FIPS 186-4, including SHA-512 with a 1024-bit key.
   * Add support for 128-bit keys in CTR_DRBG. Note that using keys shorter
     than 256 bits limits the security of generated material to 128 bits.

API Changes
   * Add a common error code of `MBEDTLS_ERR_PLATFORM_FEATURE_UNSUPPORTED` for
     a feature that is not supported by underlying alternative
     implementations implementing cryptographic primitives. This is useful for
     hardware accelerators that don't implement all options or features.

New deprecations
   * All module specific errors following the form
     MBEDTLS_ERR_XXX_FEATURE_UNAVAILABLE that indicate a feature is not
     supported are deprecated and are now replaced by the new equivalent
     platform error.
   * All module specific generic hardware acceleration errors following the
     form MBEDTLS_ERR_XXX_HW_ACCEL_FAILED that are deprecated and are replaced
     by the equivalent plaform error.
   * Deprecate the function mbedtls_mpi_is_prime() in favor of
     mbedtls_mpi_is_prime_ext() which allows specifying the number of
     Miller-Rabin rounds.

Bugfix
   * Fix wrong order of freeing in programs/ssl/ssl_server2 example
     application leading to a memory leak in case both
     MBEDTLS_MEMORY_BUFFER_ALLOC_C and MBEDTLS_MEMORY_BACKTRACE are set.
     Fixes #2069.
   * Fix a bug in the update function for SSL ticket keys which previously
     invalidated keys of a lifetime of less than a 1s. Fixes #1968.
   * Fix failure in hmac_drbg in the benchmark sample application, when
     MBEDTLS_THREADING_C is defined. Found by TrinityTonic, #1095
   * Fix a bug in the record decryption routine ssl_decrypt_buf()
     which lead to accepting properly authenticated but improperly
     padded records in case of CBC ciphersuites using Encrypt-then-MAC.
   * Fix memory leak and freeing without initialization in the example
     program programs/x509/cert_write. Fixes #1422.
   * Ignore IV in mbedtls_cipher_set_iv() when the cipher mode is
     MBEDTLS_MODE_ECB. Found by ezdevelop. Fixes #1091.
   * Zeroize memory used for buffering or reassembling handshake messages
     after use.
   * Use `mbedtls_platform_zeroize()` instead of `memset()` for zeroization
     of sensitive data in the example programs aescrypt2 and crypt_and_hash.
   * Change the default string format used for various X.509 DN attributes to
     UTF8String. Previously, the use of the PrintableString format led to
     wildcards and non-ASCII characters being unusable in some DN attributes.
     Reported by raprepo in #1860 and by kevinpt in #468. Fix contributed by
     Thomas-Dee.
   * Fix compilation failure for configurations which use compile time
     replacements of standard calloc/free functions through the macros
     MBEDTLS_PLATFORM_CALLOC_MACRO and MBEDTLS_PLATFORM_FREE_MACRO.
     Reported by ole-de and ddhome2006. Fixes #882, #1642 and #1706.

Changes
   * Removed support for Yotta as a build tool.
   * Add tests for session resumption in DTLS.
   * Close a test gap in (D)TLS between the client side and the server side:
     test the handling of large packets and small packets on the client side
     in the same way as on the server side.
   * Change the dtls_client and dtls_server samples to work by default over
     IPv6 and optionally by a build option over IPv4.
   * Change the use of Windows threading to use Microsoft Visual C++ runtime
     calls, rather than Win32 API calls directly. This is necessary to avoid
     conflict with C runtime usage. Found and fixed by irwir.
   * Remember the string format of X.509 DN attributes when replicating
     X.509 DNs. Previously, DN attributes were always written in their default
     string format (mostly PrintableString), which could lead to CRTs being
     created which used PrintableStrings in the issuer field even though the
     signing CA used UTF8Strings in its subject field; while X.509 compliant,
     such CRTs were rejected in some applications, e.g. some versions of
     Firefox, curl and GnuTLS. Reported in #1033 by Moschn. Fix contributed by
     Thomas-Dee.
   * Improve documentation of mbedtls_ssl_get_verify_result().
     Fixes #517 reported by github-monoculture.
   * Add MBEDTLS_MPI_GEN_PRIME_FLAG_LOW_ERR flag to mbedtls_mpi_gen_prime() and
     use it to reduce error probability in RSA key generation to levels mandated
     by FIPS-186-4.

= mbed TLS 2.13.1 branch released 2018-09-06

API Changes
   * Extend the platform module with an abstraction mbedtls_platform_gmtime_r()
     whose implementation should behave as a thread-safe version of gmtime().
     This allows users to configure such an implementation at compile time when
     the target system cannot be deduced automatically, by setting the option
     MBEDTLS_PLATFORM_GMTIME_R_ALT. At this stage Mbed TLS is only able to
     automatically select implementations for Windows and POSIX C libraries.

Bugfix
   * Fix build failures on platforms where only gmtime() is available but
     neither gmtime_r() nor gmtime_s() are present. Fixes #1907.

= mbed TLS 2.13.0 branch released 2018-08-31

Security
   * Fix an issue in the X.509 module which could lead to a buffer overread
     during certificate extensions parsing. In case of receiving malformed
     input (extensions length field equal to 0), an illegal read of one byte
     beyond the input buffer is made. Found and analyzed by Nathan Crandall.

Features
   * Add support for fragmentation of outgoing DTLS handshake messages. This
     is controlled by the maximum fragment length as set locally or negotiated
     with the peer, as well as by a new per-connection MTU option, set using
     mbedtls_ssl_set_mtu().
   * Add support for auto-adjustment of MTU to a safe value during the
     handshake when flights do not get through (RFC 6347, section *******,
     last paragraph).
   * Add support for packing multiple records within a single datagram,
     enabled by default.
   * Add support for buffering out-of-order handshake messages in DTLS.
     The maximum amount of RAM used for this can be controlled by the
     compile-time constant MBEDTLS_SSL_DTLS_MAX_BUFFERING defined
     in mbedtls/config.h.

API Changes
   * Add function mbedtls_ssl_set_datagram_packing() to configure
     the use of datagram packing (enabled by default).

Bugfix
   * Fix a potential memory leak in mbedtls_ssl_setup() function. An allocation
     failure in the function could lead to other buffers being leaked.
   * Fixes an issue with MBEDTLS_CHACHAPOLY_C which would not compile if
     MBEDTLS_ARC4_C and MBEDTLS_CIPHER_NULL_CIPHER weren't also defined. #1890
   * Fix a memory leak in ecp_mul_comb() if ecp_precompute_comb() fails.
     Fix contributed by Espressif Systems.
   * Add ecc extensions only if an ecc based ciphersuite is used.
     This improves compliance to RFC 4492, and as a result, solves
     interoperability issues with BouncyCastle. Raised by milenamil in #1157.
   * Replace printf with mbedtls_printf in the ARIA module. Found by
     TrinityTonic in #1908.
   * Fix potential use-after-free in mbedtls_ssl_get_max_frag_len()
     and mbedtls_ssl_get_record_expansion() after a session reset. Fixes #1941.
   * Fix a bug that caused SSL/TLS clients to incorrectly abort the handshake
     with TLS versions 1.1 and earlier when the server requested authentication
     without providing a list of CAs. This was due to an overly strict bounds
     check in parsing the CertificateRequest message,
     introduced in Mbed TLS 2.12.0. Fixes #1954.
   * Fix a miscalculation of the maximum record expansion in
     mbedtls_ssl_get_record_expansion() in case of ChachaPoly ciphersuites,
     or CBC ciphersuites in (D)TLS versions 1.1 or higher. Fixes #1913, #1914.
   * Fix undefined shifts with negative values in certificates parsing
     (found by Catena cyber using oss-fuzz)
   * Fix memory leak and free without initialization in pk_encrypt
     and pk_decrypt example programs. Reported by Brace Stout. Fixes #1128.
   * Remove redundant else statement. Raised by irwir. Fixes #1776.

Changes
   * Copy headers preserving timestamps when doing a "make install".
     Contributed by xueruini.
   * Allow the forward declaration of public structs. Contributed by Dawid
     Drozd. Fixes #1215 raised by randombit.
   * Improve compatibility with some alternative CCM implementations by using
     CCM test vectors from RAM.
   * Add support for buffering of out-of-order handshake messages.
   * Add warnings to the documentation of the HKDF module to reduce the risk
     of misusing the mbedtls_hkdf_extract() and mbedtls_hkdf_expand()
     functions. Fixes #1775. Reported by Brian J. Murray.

= mbed TLS 2.12.0 branch released 2018-07-25

Security
   * Fix a vulnerability in TLS ciphersuites based on CBC and using SHA-384,
     in (D)TLS 1.0 to 1.2, that allowed an active network attacker to
     partially recover the plaintext of messages under some conditions by
     exploiting timing measurements. With DTLS, the attacker could perform
     this recovery by sending many messages in the same connection. With TLS
     or if mbedtls_ssl_conf_dtls_badmac_limit() was used, the attack only
     worked if the same secret (for example a HTTP Cookie) has been repeatedly
     sent over connections manipulated by the attacker. Connections using GCM
     or CCM instead of CBC, using hash sizes other than SHA-384, or using
     Encrypt-then-Mac (RFC 7366) were not affected. The vulnerability was
     caused by a miscalculation (for SHA-384) in a countermeasure to the
     original Lucky 13 attack. Found by Kenny Paterson, Eyal Ronen and Adi
     Shamir.
   * Fix a vulnerability in TLS ciphersuites based on CBC, in (D)TLS 1.0 to
     1.2, that allowed a local attacker, able to execute code on the local
     machine as well as manipulate network packets, to partially recover the
     plaintext of messages under some conditions by using a cache attack
     targeting an internal MD/SHA buffer. With TLS or if
     mbedtls_ssl_conf_dtls_badmac_limit() was used, the attack only worked if
     the same secret (for example a HTTP Cookie) has been repeatedly sent over
     connections manipulated by the attacker. Connections using GCM or CCM
     instead of CBC or using Encrypt-then-Mac (RFC 7366) were not affected.
     Found by Kenny Paterson, Eyal Ronen and Adi Shamir.
   * Add a counter-measure against a vulnerability in TLS ciphersuites based
     on CBC, in (D)TLS 1.0 to 1.2, that allowed a local attacker, able to
     execute code on the local machine as well as manipulate network packets,
     to partially recover the plaintext of messages under some conditions (see
     previous entry) by using a cache attack targeting the SSL input record
     buffer. Connections using GCM or CCM instead of CBC or using
     Encrypt-then-Mac (RFC 7366) were not affected. Found by Kenny Paterson,
     Eyal Ronen and Adi Shamir.

Features
   * Add new crypto primitives from RFC 7539: stream cipher Chacha20, one-time
     authenticator Poly1305 and AEAD construct Chacha20-Poly1305. Contributed
     by Daniel King.
   * Add support for CHACHA20-POLY1305 ciphersuites from RFC 7905.
   * Add platform support for the Haiku OS. (https://www.haiku-os.org).
     Contributed by Augustin Cavalier.
   * Make the receive and transmit buffers independent sizes, for situations
     where the outgoing buffer can be fixed at a smaller size than the incoming
     buffer, which can save some RAM. If buffer lengths are kept equal, there
     is no functional difference. Contributed by Angus Gratton, and also
     independently contributed again by Paul Sokolovsky.
   * Add support for key wrapping modes based on AES as defined by
     NIST SP 800-38F algorithms KW and KWP and by RFC 3394 and RFC 5649.

Bugfix
   * Fix the key_app_writer example which was writing a leading zero byte which
     was creating an invalid ASN.1 tag. Found by Aryeh R. Fixes #1257.
   * Fix compilation error on C++, because of a variable named new.
     Found and fixed by Hirotaka Niisato in #1783.
   * Fix "no symbols" warning issued by ranlib when building on Mac OS X. Fix
     contributed by tabascoeye.
   * Clarify documentation for mbedtls_ssl_write() to include 0 as a valid
     return value. Found by @davidwu2000. #839
   * Fix a memory leak in mbedtls_x509_csr_parse(), found by catenacyber,
     Philippe Antoine. Fixes #1623.
   * Remove unused headers included in x509.c. Found by Chris Hanson and fixed
     by Brendan Shanks. Part of a fix for #992.
   * Fix compilation error when MBEDTLS_ARC4_C is disabled and
     MBEDTLS_CIPHER_NULL_CIPHER is enabled. Found by TrinityTonic in #1719.
   * Added length checks to some TLS parsing functions. Found and fixed by
     Philippe Antoine from Catena cyber. #1663.
   * Fix the inline assembly for the MPI multiply helper function for i386 and
     i386 with SSE2. Found by László Langó. Fixes #1550
   * Fix namespacing in header files. Remove the `mbedtls` namespacing in
     the `#include` in the header files. Resolves #857
   * Fix compiler warning of 'use before initialisation' in
     mbedtls_pk_parse_key(). Found by Martin Boye Petersen and fixed by Dawid
     Drozd. #1098
   * Fix decryption for zero length messages (which contain all padding) when a
     CBC based ciphersuite is used together with Encrypt-then-MAC. Previously,
     such a message was wrongly reported as an invalid record and therefore lead
     to the connection being terminated. Seen most often with OpenSSL using
     TLS 1.0. Reported by @kFYatek and by Conor Murphy on the forum. Fix
     contributed by Espressif Systems. Fixes #1632
   * Fix ssl_client2 example to send application data with 0-length content
     when the request_size argument is set to 0 as stated in the documentation.
     Fixes #1833.
   * Correct the documentation for `mbedtls_ssl_get_session()`. This API has
     deep copy of the session, and the peer certificate is not lost. Fixes #926.
   * Fix build using -std=c99. Fixed by Nick Wilson.

Changes
   * Fail when receiving a TLS alert message with an invalid length, or invalid
     zero-length messages when using TLS 1.2. Contributed by Espressif Systems.
   * Change the default behaviour of mbedtls_hkdf_extract() to return an error
     when calling with a NULL salt and non-zero salt_len. Contributed by
     Brian J Murray
   * Change the shebang line in Perl scripts to look up perl in the PATH.
     Contributed by fbrosson.
   * Allow overriding the time on Windows via the platform-time abstraction.
     Fixed by Nick Wilson.
   * Use gmtime_r/gmtime_s for thread-safety. Fixed by Nick Wilson.

= mbed TLS 2.11.0 branch released 2018-06-18

Features
   * Add additional block mode, OFB (Output Feedback), to the AES module and
     cipher abstraction module.
   * Implement the HMAC-based extract-and-expand key derivation function
     (HKDF) per RFC 5869. Contributed by Thomas Fossati.
   * Add support for the CCM* block cipher mode as defined in IEEE Std. 802.15.4.
   * Add support for the XTS block cipher mode with AES (AES-XTS).
     Contributed by Aorimn in pull request #414.
   * In TLS servers, support offloading private key operations to an external
     cryptoprocessor. Private key operations can be asynchronous to allow
     non-blocking operation of the TLS server stack.

Bugfix
   * Fix the cert_write example to handle certificates signed with elliptic
     curves as well as RSA. Fixes #777 found by dbedev.
   * Fix for redefinition of _WIN32_WINNT to avoid overriding a definition
     used by user applications. Found and fixed by Fabio Alessandrelli.
   * Fix compilation warnings with IAR toolchain, on 32 bit platform.
     Reported by rahmanih in #683
   * Fix braces in mbedtls_memory_buffer_alloc_status(). Found by sbranden, #552.

Changes
   * Changed CMake defaults for IAR to treat all compiler warnings as errors.
   * Changed the Clang parameters used in the CMake build files to work for
     versions later than 3.6. Versions of Clang earlier than this may no longer
     work. Fixes #1072

= mbed TLS 2.10.0 branch released 2018-06-06

Features
   * Add support for ARIA cipher (RFC 5794) and associated TLS ciphersuites
     (RFC 6209). Disabled by default, see MBEDTLS_ARIA_C in config.h

API Changes
   * Extend the platform module with a util component that contains
     functionality shared by multiple Mbed TLS modules. At this stage
     platform_util.h (and its associated platform_util.c) only contain
     mbedtls_platform_zeroize(), which is a critical function from a security
     point of view. mbedtls_platform_zeroize() needs to be regularly tested
     against compilers to ensure that calls to it are not removed from the
     output binary as part of redundant code elimination optimizations.
     Therefore, mbedtls_platform_zeroize() is moved to the platform module to
     facilitate testing and maintenance.

Bugfix
   * Fix an issue with MicroBlaze support in bn_mul.h which was causing the
     build to fail. Found by zv-io. Fixes #1651.

Changes
   * Support TLS testing in out-of-source builds using cmake. Fixes #1193.
   * Fix redundant declaration of mbedtls_ssl_list_ciphersuites. Raised by
     TrinityTonic. #1359.

= mbed TLS 2.9.0 branch released 2018-04-30

Security
   * Fix an issue in the X.509 module which could lead to a buffer overread
     during certificate validation. Additionally, the issue could also lead to
     unnecessary callback checks being made or to some validation checks to be
     omitted. The overread could be triggered remotely, while the other issues
     would require a non DER-compliant certificate to be correctly signed by a
     trusted CA, or a trusted CA with a non DER-compliant certificate. Found by
     luocm. Fixes #825.
   * Fix the buffer length assertion in the ssl_parse_certificate_request()
     function which led to an arbitrary overread of the message buffer. The
     overreads could be caused by receiving a malformed message at the point
     where an optional signature algorithms list is expected when the signature
     algorithms section is too short. In builds with debug output, the overread
     data is output with the debug data.
   * Fix a client-side bug in the validation of the server's ciphersuite choice
     which could potentially lead to the client accepting a ciphersuite it didn't
     offer or a ciphersuite that cannot be used with the TLS or DTLS version
     chosen by the server. This could lead to corruption of internal data
     structures for some configurations.

Features
   * Add an option, MBEDTLS_AES_FEWER_TABLES, to dynamically compute smaller AES
     tables during runtime, thereby reducing the RAM/ROM footprint by ~6KiB.
     Suggested and contributed by jkivilin in pull request #394.
   * Add initial support for Curve448 (RFC 7748). Only mbedtls_ecp_mul() and
     ECDH primitive functions (mbedtls_ecdh_gen_public(),
     mbedtls_ecdh_compute_shared()) are supported for now. Contributed by
     Nicholas Wilson in pull request #348.

API Changes
   * Extend the public API with the function of mbedtls_net_poll() to allow user
     applications to wait for a network context to become ready before reading
     or writing.
   * Add function mbedtls_ssl_check_pending() to the public API to allow
     a check for whether more more data is pending to be processed in the
     internal message buffers.
     This function is necessary to determine when it is safe to idle on the
     underlying transport in case event-driven IO is used.

Bugfix
   * Fix a spurious uninitialized variable warning in cmac.c. Fix independently
     contributed by Brian J Murray and David Brown.
   * Add missing dependencies in test suites that led to build failures
     in configurations that omit certain hashes or public-key algorithms.
     Fixes #1040.
   * Fix C89 incompatibility in benchmark.c. Contributed by Brendan Shanks.
     #1353
   * Add missing dependencies for MBEDTLS_HAVE_TIME_DATE and
     MBEDTLS_VERSION_FEATURES in some test suites. Contributed by
     Deomid Ryabkov. Fixes #1299, #1475.
   * Fix the Makefile build process for building shared libraries on Mac OS X.
     Fixed by mnacamura.
   * Fix parsing of PKCS#8 encoded Elliptic Curve keys. Previously Mbed TLS was
     unable to parse keys which had only the optional parameters field of the
     ECPrivateKey structure. Found by Jethro Beekman, fixed in #1379.
   * Return the plaintext data more quickly on unpadded CBC decryption, as
     stated in the mbedtls_cipher_update() documentation. Contributed by
     Andy Leiserson.
   * Fix overriding and ignoring return values when parsing and writing to
     a file in pk_sign program. Found by kevlut in #1142.
   * Restrict usage of error code MBEDTLS_ERR_SSL_WANT_READ to situations
     where data needs to be fetched from the underlying transport in order
     to make progress. Previously, this error code was also occasionally
     returned when unexpected messages were being discarded, ignoring that
     further messages could potentially already be pending to be processed
     in the internal buffers; these cases led to deadlocks when event-driven
     I/O was used. Found and reported by Hubert Mis in #772.
   * Fix buffer length assertions in the ssl_parse_certificate_request()
     function which leads to a potential one byte overread of the message
     buffer.
   * Fix invalid buffer sizes passed to zlib during record compression and
     decompression.
   * Fix the soversion of libmbedcrypto to match the soversion of the
     maintained 2.7 branch. The soversion was increased in Mbed TLS
     version 2.7.1 to reflect breaking changes in that release, but the
     increment was missed in 2.8.0 and later releases outside of the 2.7 branch.

Changes
   * Remove some redundant code in bignum.c. Contributed by Alexey Skalozub.
   * Support cmake builds where Mbed TLS is a subproject. Fix contributed
     independently by Matthieu Volat and Arne Schwabe.
   * Improve testing in configurations that omit certain hashes or
     public-key algorithms. Includes contributions by Gert van Dijk.
   * Improve negative testing of X.509 parsing.
   * Do not define global mutexes around readdir() and gmtime() in
     configurations where the feature is disabled. Found and fixed by Gergely
     Budai.
   * Harden the function mbedtls_ssl_config_free() against misuse, so that it
     doesn't leak memory if the user doesn't use mbedtls_ssl_conf_psk() and
     instead incorrectly manipulates the configuration structure directly.
     Found and fix submitted by junyeonLEE in #1220.
   * Provide an empty implementation of mbedtls_pkcs5_pbes2() when
     MBEDTLS_ASN1_PARSE_C is not enabled. This allows the use of PBKDF2
     without PBES2. Fixed by Marcos Del Sol Vives.
   * Add the order of the base point as N in the mbedtls_ecp_group structure
     for Curve25519 (other curves had it already). Contributed by Nicholas
     Wilson #481
   * Improve the documentation of mbedtls_net_accept(). Contributed by Ivan
     Krylov.
   * Improve the documentation of mbedtls_ssl_write(). Suggested by
     Paul Sokolovsky in #1356.
   * Add an option in the Makefile to support ar utilities where the operation
     letter must not be prefixed by '-', such as LLVM. Found and fixed by
     Alex Hixon.
   * Allow configuring the shared library extension by setting the DLEXT
     environment variable when using the project makefiles.
   * Optimize unnecessary zeroing in mbedtls_mpi_copy. Based on a contribution
     by Alexey Skalozub in #405.
   * In the SSL module, when f_send, f_recv or f_recv_timeout report
     transmitting more than the required length, return an error. Raised by
     Sam O'Connor in #1245.
   * Improve robustness of mbedtls_ssl_derive_keys against the use of
     HMAC functions with non-HMAC ciphersuites. Independently contributed
     by Jiayuan Chen in #1377. Fixes #1437.
   * Improve security of RSA key generation by including criteria from
     FIPS 186-4. Contributed by Jethro Beekman. #1380
   * Declare functions in header files even when an alternative implementation
     of the corresponding module is activated by defining the corresponding
     MBEDTLS_XXX_ALT macro. This means that alternative implementations do
     not need to copy the declarations, and ensures that they will have the
     same API.
   * Add platform setup and teardown calls in test suites.

= mbed TLS 2.8.0 branch released 2018-03-16

Default behavior changes
   * The truncated HMAC extension now conforms to RFC 6066. This means
     that when both sides of a TLS connection negotiate the truncated
     HMAC extension, Mbed TLS can now interoperate with other
     compliant implementations, but this breaks interoperability with
     prior versions of Mbed TLS. To restore the old behavior, enable
     the (deprecated) option MBEDTLS_SSL_TRUNCATED_HMAC_COMPAT in
     config.h. Found by Andreas Walz (ivESK, Offenburg University of
     Applied Sciences).

Security
   * Fix implementation of the truncated HMAC extension. The previous
     implementation allowed an offline 2^80 brute force attack on the
     HMAC key of a single, uninterrupted connection (with no
     resumption of the session).
   * Verify results of RSA private key operations to defend
     against Bellcore glitch attack.
   * Fix a buffer overread in ssl_parse_server_key_exchange() that could cause
     a crash on invalid input.
   * Fix a buffer overread in ssl_parse_server_psk_hint() that could cause a
     crash on invalid input.
   * Fix CRL parsing to reject CRLs containing unsupported critical
     extensions. Found by Falko Strenzke and Evangelos Karatsiolis.

Features
   * Extend PKCS#8 interface by introducing support for the entire SHA
     algorithms family when encrypting private keys using PKCS#5 v2.0.
     This allows reading encrypted PEM files produced by software that
     uses PBKDF2-SHA2, such as OpenSSL 1.1. Submitted by Antonio Quartulli,
     OpenVPN Inc. Fixes #1339
   * Add support for public keys encoded in PKCS#1 format. #1122

New deprecations
   * Deprecate support for record compression (configuration option
     MBEDTLS_ZLIB_SUPPORT).

Bugfix
   * Fix the name of a DHE parameter that was accidentally changed in 2.7.0.
     Fixes #1358.
   * Fix test_suite_pk to work on 64-bit ILP32 systems. #849
   * Fix mbedtls_x509_crt_profile_suiteb, which used to reject all certificates
     with flag MBEDTLS_X509_BADCERT_BAD_PK even when the key type was correct.
     In the context of SSL, this resulted in handshake failure. Reported by
     daniel in the Mbed TLS forum. #1351
   * Fix Windows x64 builds with the included mbedTLS.sln file. #1347
   * Fix setting version TLSv1 as minimal version, even if TLS 1
     is not enabled. Set MBEDTLS_SSL_MIN_MAJOR_VERSION
     and MBEDTLS_SSL_MIN_MINOR_VERSION instead of
     MBEDTLS_SSL_MAJOR_VERSION_3 and MBEDTLS_SSL_MINOR_VERSION_1. #664
   * Fix compilation error on Mingw32 when _TRUNCATE is defined. Use _TRUNCATE
     only if __MINGW32__ not defined. Fix suggested by Thomas Glanzmann and
     Nick Wilson on issue #355
   * In test_suite_pk, pass valid parameters when testing for hash length
     overflow. #1179
   * Fix memory allocation corner cases in memory_buffer_alloc.c module. Found
     by Guido Vranken. #639
   * Log correct number of ciphersuites used in Client Hello message. #918
   * Fix X509 CRT parsing that would potentially accept an invalid tag when
     parsing the subject alternative names.
   * Fix a possible arithmetic overflow in ssl_parse_server_key_exchange()
     that could cause a key exchange to fail on valid data.
   * Fix a possible arithmetic overflow in ssl_parse_server_psk_hint() that
     could cause a key exchange to fail on valid data.
   * Don't define mbedtls_aes_decrypt and mbedtls_aes_encrypt under
     MBEDTLS_DEPRECATED_REMOVED. #1388
   * Fix a 1-byte heap buffer overflow (read-only) during private key parsing.
     Found through fuzz testing.

Changes
   * Fix tag lengths and value ranges in the documentation of CCM encryption.
     Contributed by Mathieu Briand.
   * Fix typo in a comment ctr_drbg.c. Contributed by Paul Sokolovsky.
   * Remove support for the library reference configuration for picocoin.
   * MD functions deprecated in 2.7.0 are no longer inline, to provide
     a migration path for those depending on the library's ABI.
   * Clarify the documentation of mbedtls_ssl_setup.
   * Use (void) when defining functions with no parameters. Contributed by
     Joris Aerts. #678

= mbed TLS 2.7.0 branch released 2018-02-03

Security
   * Fix a heap corruption issue in the implementation of the truncated HMAC
     extension. When the truncated HMAC extension is enabled and CBC is used,
     sending a malicious application packet could be used to selectively corrupt
     6 bytes on the peer's heap, which could potentially lead to crash or remote
     code execution. The issue could be triggered remotely from either side in
     both TLS and DTLS. CVE-2018-0488
   * Fix a buffer overflow in RSA-PSS verification when the hash was too large
     for the key size, which could potentially lead to crash or remote code
     execution. Found by Seth Terashima, Qualcomm Product Security Initiative,
     Qualcomm Technologies Inc. CVE-2018-0487
   * Fix buffer overflow in RSA-PSS verification when the unmasked data is all
     zeros.
   * Fix an unsafe bounds check in ssl_parse_client_psk_identity() when adding
     64 KiB to the address of the SSL buffer and causing a wrap around.
   * Fix a potential heap buffer overflow in mbedtls_ssl_write(). When the (by
     default enabled) maximum fragment length extension is disabled in the
     config and the application data buffer passed to mbedtls_ssl_write
     is larger than the internal message buffer (16384 bytes by default), the
     latter overflows. The exploitability of this issue depends on whether the
     application layer can be forced into sending such large packets. The issue
     was independently reported by Tim Nordell via e-mail and by Florin Petriuc
     and sjorsdewit on GitHub. Fix proposed by Florin Petriuc in #1022.
     Fixes #707.
   * Add a provision to prevent compiler optimizations breaking the time
     constancy of mbedtls_ssl_safer_memcmp().
   * Ensure that buffers are cleared after use if they contain sensitive data.
     Changes were introduced in multiple places in the library.
   * Set PEM buffer to zero before freeing it, to avoid decoded private keys
     being leaked to memory after release.
   * Fix dhm_check_range() failing to detect trivial subgroups and potentially
     leaking 1 bit of the private key. Reported by prashantkspatil.
   * Make mbedtls_mpi_read_binary() constant-time with respect to the input
     data. Previously, trailing zero bytes were detected and omitted for the
     sake of saving memory, but potentially leading to slight timing
     differences. Reported by Marco Macchetti, Kudelski Group.
   * Wipe stack buffer temporarily holding EC private exponent
     after keypair generation.
   * Fix a potential heap buffer over-read in ALPN extension parsing
     (server-side). Could result in application crash, but only if an ALPN
     name larger than 16 bytes had been configured on the server.
   * Change default choice of DHE parameters from untrustworthy RFC 5114
     to RFC 3526 containing parameters generated in a nothing-up-my-sleeve
     manner.

Features
   * Allow comments in test data files.
   * The selftest program can execute a subset of the tests based on command
     line arguments.
   * New unit tests for timing. Improve the self-test to be more robust
     when run on a heavily-loaded machine.
   * Add alternative implementation support for CCM and CMAC (MBEDTLS_CCM_ALT,
     MBEDTLS_CMAC_ALT). Submitted by Steven Cooreman, Silicon Labs.
   * Add support for alternative implementations of GCM, selected by the
     configuration flag MBEDTLS_GCM_ALT.
   * Add support for alternative implementations for ECDSA, controlled by new
     configuration flags MBEDTLS_ECDSA_SIGN_ALT, MBEDTLS_ECDSA_VERIFY_ALT and
     MBEDTLS_ECDSDA_GENKEY_AT in config.h.
     The following functions from the ECDSA module can be replaced
     with alternative implementation:
     mbedtls_ecdsa_sign(), mbedtls_ecdsa_verify() and mbedtls_ecdsa_genkey().
   * Add support for alternative implementation of ECDH, controlled by the
     new configuration flags MBEDTLS_ECDH_COMPUTE_SHARED_ALT and
     MBEDTLS_ECDH_GEN_PUBLIC_ALT in config.h.
     The following functions from the ECDH module can be replaced
     with an alternative implementation:
     mbedtls_ecdh_gen_public() and mbedtls_ecdh_compute_shared().
   * Add support for alternative implementation of ECJPAKE, controlled by
     the new configuration flag MBEDTLS_ECJPAKE_ALT.
   * Add mechanism to provide alternative implementation of the DHM module.

API Changes
   * Extend RSA interface by multiple functions allowing structure-
     independent setup and export of RSA contexts. Most notably,
     mbedtls_rsa_import() and mbedtls_rsa_complete() are introduced for setting
     up RSA contexts from partial key material and having them completed to the
     needs of the implementation automatically. This allows to setup private RSA
     contexts from keys consisting of N,D,E only, even if P,Q are needed for the
     purpose or CRT and/or blinding.
   * The configuration option MBEDTLS_RSA_ALT can be used to define alternative
     implementations of the RSA interface declared in rsa.h.
   * The following functions in the message digest modules (MD2, MD4, MD5,
     SHA1, SHA256, SHA512) have been deprecated and replaced as shown below.
     The new functions change the return type from void to int to allow
     returning error codes when using MBEDTLS_<MODULE>_ALT.
     mbedtls_<MODULE>_starts() -> mbedtls_<MODULE>_starts_ret()
     mbedtls_<MODULE>_update() -> mbedtls_<MODULE>_update_ret()
     mbedtls_<MODULE>_finish() -> mbedtls_<MODULE>_finish_ret()
     mbedtls_<MODULE>_process() -> mbedtls_internal_<MODULE>_process()

New deprecations
   * Deprecate usage of RSA primitives with non-matching key-type
     (e.g. signing with a public key).
   * Direct manipulation of structure fields of RSA contexts is deprecated.
     Users are advised to use the extended RSA API instead.
   * Deprecate usage of message digest functions that return void
     (mbedtls_<MODULE>_starts, mbedtls_<MODULE>_update,
     mbedtls_<MODULE>_finish and mbedtls_<MODULE>_process where <MODULE> is
     any of MD2, MD4, MD5, SHA1, SHA256, SHA512) in favor of functions
     that can return an error code.
   * Deprecate untrustworthy DHE parameters from RFC 5114. Superseded by
     parameters from RFC 3526 or the newly added parameters from RFC 7919.
   * Deprecate hex string DHE constants MBEDTLS_DHM_RFC3526_MODP_2048_P etc.
     Supserseded by binary encoded constants MBEDTLS_DHM_RFC3526_MODP_2048_P_BIN
     etc.
   * Deprecate mbedtls_ssl_conf_dh_param() for setting default DHE parameters
     from hex strings. Superseded by mbedtls_ssl_conf_dh_param_bin()
     accepting DHM parameters in binary form, matching the new constants.

Bugfix
   * Fix ssl_parse_record_header() to silently discard invalid DTLS records
     as recommended in RFC 6347 Section *******.
   * Fix memory leak in mbedtls_ssl_set_hostname() when called multiple times.
     Found by projectgus and Jethro Beekman, #836.
   * Fix usage help in ssl_server2 example. Found and fixed by Bei Lin.
   * Parse signature algorithm extension when renegotiating. Previously,
     renegotiated handshakes would only accept signatures using SHA-1
     regardless of the peer's preferences, or fail if SHA-1 was disabled.
   * Fix leap year calculation in x509_date_is_valid() to ensure that invalid
     dates on leap years with 100 and 400 intervals are handled correctly. Found
     by Nicholas Wilson. #694
   * Fix some invalid RSA-PSS signatures with keys of size 8N+1 that were
     accepted. Generating these signatures required the private key.
   * Fix out-of-memory problem when parsing 4096-bit PKCS8-encrypted RSA keys.
     Found independently by Florian in the mbed TLS forum and by Mishamax.
     #878, #1019.
   * Fix variable used before assignment compilation warnings with IAR
     toolchain. Found by gkerrien38.
   * Fix unchecked return codes from AES, DES and 3DES functions in
     pem_aes_decrypt(), pem_des_decrypt() and pem_des3_decrypt() respectively.
     If a call to one of the functions of the cryptographic primitive modules
     failed, the error may not be noticed by the function
     mbedtls_pem_read_buffer() causing it to return invalid values. Found by
     Guido Vranken. #756
   * Include configuration file in md.h, to fix compilation warnings.
     Reported by aaronmdjones in #1001
   * Correct extraction of signature-type from PK instance in X.509 CRT and CSR
     writing routines that prevented these functions to work with alternative
     RSA implementations. Raised by J.B. in the Mbed TLS forum. Fixes #1011.
   * Don't print X.509 version tag for v1 CRT's, and omit extensions for
     non-v3 CRT's.
   * Fix bugs in RSA test suite under MBEDTLS_NO_PLATFORM_ENTROPY. #1023 #1024
   * Fix net_would_block() to avoid modification by errno through fcntl() call.
     Found by nkolban. Fixes #845.
   * Fix handling of handshake messages in mbedtls_ssl_read() in case
     MBEDTLS_SSL_RENEGOTIATION is disabled. Found by erja-gp.
   * Add a check for invalid private parameters in mbedtls_ecdsa_sign().
     Reported by Yolan Romailler.
   * Fix word size check in in pk.c to not depend on MBEDTLS_HAVE_INT64.
   * Fix incorrect unit in benchmark output. #850
   * Add size-checks for record and handshake message content, securing
     fragile yet non-exploitable code-paths.
   * Fix crash when calling mbedtls_ssl_cache_free() twice. Found by
     MilenkoMitrovic, #1104
   * Fix mbedtls_timing_alarm(0) on Unix and MinGW.
   * Fix use of uninitialized memory in mbedtls_timing_get_timer() when reset=1.
   * Fix possible memory leaks in mbedtls_gcm_self_test().
   * Added missing return code checks in mbedtls_aes_self_test().
   * Fix issues in RSA key generation program programs/x509/rsa_genkey and the
     RSA test suite where the failure of CTR DRBG initialization lead to
     freeing an RSA context and several MPI's without proper initialization
     beforehand.
   * Fix error message in programs/pkey/gen_key.c. Found and fixed by Chris Xue.
   * Fix programs/pkey/dh_server.c so that it actually works with dh_client.c.
     Found and fixed by Martijn de Milliano.
   * Fix an issue in the cipher decryption with the mode
     MBEDTLS_PADDING_ONE_AND_ZEROS that sometimes accepted invalid padding.
     Note, this padding mode is not used by the TLS protocol. Found and fixed by
     Micha Kraus.
   * Fix the entropy.c module to not call mbedtls_sha256_starts() or
     mbedtls_sha512_starts() in the mbedtls_entropy_init() function.
   * Fix the entropy.c module to ensure that mbedtls_sha256_init() or
     mbedtls_sha512_init() is called before operating on the relevant context
     structure. Do not assume that zeroizing a context is a correct way to
     reset it. Found independently by ccli8 on Github.
   * In mbedtls_entropy_free(), properly free the message digest context.
   * Fix status handshake status message in programs/ssl/dtls_client.c. Found
     and fixed by muddog.

Changes
   * Extend cert_write example program by options to set the certificate version
     and the message digest. Further, allow enabling/disabling of authority
     identifier, subject identifier and basic constraints extensions.
   * Only check for necessary RSA structure fields in `mbedtls_rsa_private`. In
     particular, don't require P,Q if neither CRT nor blinding are
     used. Reported and fix proposed independently by satur9nine and sliai
     on GitHub.
   * Only run AES-192 self-test if AES-192 is available. Fixes #963.
   * Tighten the RSA PKCS#1 v1.5 signature verification code and remove the
     undeclared dependency of the RSA module on the ASN.1 module.
   * Update all internal usage of deprecated message digest functions to the
     new ones with return codes. In particular, this modifies the
     mbedtls_md_info_t structure. Propagate errors from these functions
     everywhere except some locations in the ssl_tls.c module.
   * Improve CTR_DRBG error handling by propagating underlying AES errors.
   * Add MBEDTLS_ERR_XXX_HW_ACCEL_FAILED error codes for all cryptography
     modules where the software implementation can be replaced by a hardware
     implementation.
   * Add explicit warnings for the use of MD2, MD4, MD5, SHA-1, DES and ARC4
     throughout the library.

= mbed TLS 2.6.0 branch released 2017-08-10

Security
   * Fix authentication bypass in SSL/TLS: when authmode is set to optional,
     mbedtls_ssl_get_verify_result() would incorrectly return 0 when the peer's
     X.509 certificate chain had more than MBEDTLS_X509_MAX_INTERMEDIATE_CA
     (default: 8) intermediates, even when it was not trusted. This could be
     triggered remotely from either side. (With authmode set to 'required'
     (the default), the handshake was correctly aborted).
   * Reliably wipe sensitive data after use in the AES example applications
     programs/aes/aescrypt2 and programs/aes/crypt_and_hash.
     Found by Laurent Simon.

Features
   * Add the functions mbedtls_platform_setup() and mbedtls_platform_teardown()
     and the context struct mbedtls_platform_context to perform
     platform-specific setup and teardown operations. The macro
     MBEDTLS_PLATFORM_SETUP_TEARDOWN_ALT allows the functions to be overridden
     by the user in a platform_alt.h file. These new functions are required in
     some embedded environments to provide a means of initialising underlying
     cryptographic acceleration hardware.

API Changes
   * Reverted API/ABI breaking changes introduced in mbed TLS 2.5.1, to make the
     API consistent with mbed TLS 2.5.0. Specifically removed the inline
     qualifier from the functions mbedtls_aes_decrypt, mbedtls_aes_encrypt,
     mbedtls_ssl_ciphersuite_uses_ec and mbedtls_ssl_ciphersuite_uses_psk. Found
     by James Cowgill. #978
   * Certificate verification functions now set flags to -1 in case the full
     chain was not verified due to an internal error (including in the verify
     callback) or chain length limitations.
   * With authmode set to optional, the TLS handshake is now aborted if the
     verification of the peer's certificate failed due to an overlong chain or
     a fatal error in the verify callback.

Bugfix
   * Add a check if iv_len is zero in GCM, and return an error if it is zero.
     Reported by roberto. #716
   * Replace preprocessor condition from #if defined(MBEDTLS_THREADING_PTHREAD)
     to #if defined(MBEDTLS_THREADING_C) as the library cannot assume they will
     always be implemented by pthread support. #696
   * Fix a resource leak on Windows platforms in mbedtls_x509_crt_parse_path(),
     in the case of an error. Found by redplait. #590
   * Add MBEDTLS_MPI_CHK to check for error value of mbedtls_mpi_fill_random.
     Reported and fix suggested by guidovranken. #740
   * Fix conditional preprocessor directives in bignum.h to enable 64-bit
     compilation when using ARM Compiler 6.
   * Fix a potential integer overflow in the version verification for DER
     encoded X.509 CRLs. The overflow could enable maliciously constructed CRLs
     to bypass the version verification check. Found by Peng Li/Yueh-Hsun Lin,
     KNOX Security, Samsung Research America
   * Fix potential integer overflow in the version verification for DER
     encoded X.509 CSRs. The overflow could enable maliciously constructed CSRs
     to bypass the version verification check. Found by Peng Li/Yueh-Hsun Lin,
     KNOX Security, Samsung Research America
   * Fix a potential integer overflow in the version verification for DER
     encoded X.509 certificates. The overflow could enable maliciously
     constructed certificates to bypass the certificate verification check.
   * Fix a call to the libc function time() to call the platform abstraction
     function mbedtls_time() instead. Found by wairua. #666
   * Avoid shadowing of time and index functions through mbed TLS function
     arguments. Found by inestlerode. #557.

Changes
   * Added config.h option MBEDTLS_NO_UDBL_DIVISION, to prevent the use of
     64-bit division. This is useful on embedded platforms where 64-bit division
     created a dependency on external libraries. #708
   * Removed mutexes from ECP hardware accelerator code. Now all hardware
     accelerator code in the library leaves concurrency handling to the
     platform. Reported by Steven Cooreman. #863
   * Define the macro MBEDTLS_AES_ROM_TABLES in the configuration file
     config-no-entropy.h to reduce the RAM footprint.
   * Added a test script that can be hooked into git that verifies commits
     before they are pushed.
   * Improve documentation of PKCS1 decryption functions.

= mbed TLS 2.5.1 released 2017-06-21

Security
   * Fixed unlimited overread of heap-based buffer in mbedtls_ssl_read().
     The issue could only happen client-side with renegotiation enabled.
     Could result in DoS (application crash) or information leak
     (if the application layer sent data read from mbedtls_ssl_read()
     back to the server or to a third party). Can be triggered remotely.
   * Removed SHA-1 and RIPEMD-160 from the default hash algorithms for
     certificate verification. SHA-1 can be turned back on with a compile-time
     option if needed.
   * Fixed offset in FALLBACK_SCSV parsing that caused TLS server to fail to
     detect it sometimes. Reported by Hugo Leisink. #810
   * Tighten parsing of RSA PKCS#1 v1.5 signatures, to avoid a
     potential Bleichenbacher/BERserk-style attack.

Bugfix
   * Remove size zero arrays from ECJPAKE test suite. Size zero arrays are not
     valid C and they prevented the test from compiling in Visual Studio 2015
     and with GCC using the -Wpedantic compilation option.
   * Fix insufficient support for signature-hash-algorithm extension,
     resulting in compatibility problems with Chrome. Found by hfloyrd. #823
   * Fix behaviour that hid the original cause of fatal alerts in some cases
     when sending the alert failed. The fix makes sure not to hide the error
     that triggered the alert.
   * Fix SSLv3 renegotiation behaviour and stop processing data received from
     peer after sending a fatal alert to refuse a renegotiation attempt.
     Previous behaviour was to keep processing data even after the alert has
     been sent.
   * Accept empty trusted CA chain in authentication mode
     MBEDTLS_SSL_VERIFY_OPTIONAL. Found by Jethro Beekman. #864
   * Fix implementation of mbedtls_ssl_parse_certificate() to not annihilate
     fatal errors in authentication mode MBEDTLS_SSL_VERIFY_OPTIONAL and to
     reflect bad EC curves within verification result.
   * Fix bug that caused the modular inversion function to accept the invalid
     modulus 1 and therefore to hang. Found by blaufish. #641.
   * Fix incorrect sign computation in modular exponentiation when the base is
     a negative MPI. Previously the result was always negative. Found by Guido
     Vranken.
   * Fix a numerical underflow leading to stack overflow in mpi_read_file()
     that was triggered uppon reading an empty line. Found by Guido Vranken.

Changes
   * Send fatal alerts in more cases. The previous behaviour was to skip
     sending the fatal alert and just drop the connection.
   * Clarify ECDSA documentation and improve the sample code to avoid
     misunderstanding and potentially dangerous use of the API. Pointed out
     by Jean-Philippe Aumasson.

= mbed TLS 2.5.0 branch released 2017-05-17

Security
   * Wipe stack buffers in RSA private key operations
     (rsa_rsaes_pkcs1_v15_decrypt(), rsa_rsaes_oaep_decrypt). Found by Laurent
     Simon.
   * Add exponent blinding to RSA private operations as a countermeasure
     against side-channel attacks like the cache attack described in
     https://arxiv.org/abs/1702.08719v2.
     Found and fix proposed by Michael Schwarz, Samuel Weiser, Daniel Gruss,
     Clémentine Maurice and Stefan Mangard.

Features
   * Add hardware acceleration support for the Elliptic Curve Point module.
     This involved exposing parts of the internal interface to enable
     replacing the core functions and adding and alternative, module level
     replacement support for enabling the extension of the interface.
   * Add a new configuration option to 'mbedtls_ssl_config' to enable
     suppressing the CA list in Certificate Request messages. The default
     behaviour has not changed, namely every configured CAs name is included.

API Changes
   * The following functions in the AES module have been deprecated and replaced
     by the functions shown below. The new functions change the return type from
     void to int to allow returning error codes when using MBEDTLS_AES_ALT,
     MBEDTLS_AES_DECRYPT_ALT or MBEDTLS_AES_ENCRYPT_ALT.
     mbedtls_aes_decrypt() -> mbedtls_internal_aes_decrypt()
     mbedtls_aes_encrypt() -> mbedtls_internal_aes_encrypt()

Bugfix
   * Remove macros from compat-1.3.h that correspond to deleted items from most
     recent versions of the library. Found by Kyle Keen.
   * Fixed issue in the Threading module that prevented mutexes from
     initialising. Found by sznaider. #667 #843
   * Add checks in the PK module for the RSA functions on 64-bit systems.
     The PK and RSA modules use different types for passing hash length and
     without these checks the type cast could lead to data loss. Found by Guido
     Vranken.

= mbed TLS 2.4.2 branch released 2017-03-08

Security
   * Add checks to prevent signature forgeries for very large messages while
     using RSA through the PK module in 64-bit systems. The issue was caused by
     some data loss when casting a size_t to an unsigned int value in the
     functions rsa_verify_wrap(), rsa_sign_wrap(), rsa_alt_sign_wrap() and
     mbedtls_pk_sign(). Found by Jean-Philippe Aumasson.
   * Fixed potential livelock during the parsing of a CRL in PEM format in
     mbedtls_x509_crl_parse(). A string containing a CRL followed by trailing
     characters after the footer could result in the execution of an infinite
     loop. The issue can be triggered remotely. Found by Greg Zaverucha,
     Microsoft.
   * Removed MD5 from the allowed hash algorithms for CertificateRequest and
     CertificateVerify messages, to prevent SLOTH attacks against TLS 1.2.
     Introduced by interoperability fix for #513.
   * Fixed a bug that caused freeing a buffer that was allocated on the stack,
     when verifying the validity of a key on secp224k1. This could be
     triggered remotely for example with a maliciously constructed certificate
     and potentially could lead to remote code execution on some platforms.
     Reported independently by rongsaws and Aleksandar Nikolic, Cisco Talos
     team. #569 CVE-2017-2784

Bugfix
   * Fix output certificate verification flags set by x509_crt_verify_top() when
     traversing a chain of trusted CA. The issue would cause both flags,
     MBEDTLS_X509_BADCERT_NOT_TRUSTED and MBEDTLS_X509_BADCERT_EXPIRED, to be
     set when the verification conditions are not met regardless of the cause.
     Found by Harm Verhagen and inestlerode. #665 #561
   * Fix the redefinition of macro ssl_set_bio to an undefined symbol
     mbedtls_ssl_set_bio_timeout in compat-1.3.h, by removing it.
     Found by omlib-lin. #673
   * Fix unused variable/function compilation warnings in pem.c, x509_crt.c and
     x509_csr.c that are reported when building mbed TLS with a config.h that
     does not define MBEDTLS_PEM_PARSE_C. Found by omnium21. #562
   * Fix incorrect renegotiation condition in ssl_check_ctr_renegotiate() that
     would compare 64 bits of the record counter instead of 48 bits as indicated
     in RFC 6347 Section 4.3.1. This could cause the execution of the
     renegotiation routines at unexpected times when the protocol is DTLS. Found
     by wariua. #687
   * Fixed multiple buffer overreads in mbedtls_pem_read_buffer() when parsing
     the input string in PEM format to extract the different components. Found
     by Eyal Itkin.
   * Fixed potential arithmetic overflow in mbedtls_ctr_drbg_reseed() that could
     cause buffer bound checks to be bypassed. Found by Eyal Itkin.
   * Fixed potential arithmetic overflows in mbedtls_cipher_update() that could
     cause buffer bound checks to be bypassed. Found by Eyal Itkin.
   * Fixed potential arithmetic overflow in mbedtls_md2_update() that could
     cause buffer bound checks to be bypassed. Found by Eyal Itkin.
   * Fixed potential arithmetic overflow in mbedtls_base64_decode() that could
     cause buffer bound checks to be bypassed. Found by Eyal Itkin.
   * Fixed heap overreads in mbedtls_x509_get_time(). Found by Peng
     Li/Yueh-Hsun Lin, KNOX Security, Samsung Research America.
   * Fix potential memory leak in mbedtls_x509_crl_parse(). The leak was caused
     by missing calls to mbedtls_pem_free() in cases when a
     MBEDTLS_ERR_PEM_NO_HEADER_FOOTER_PRESENT error was encountered. Found and
     fix proposed by Guido Vranken. #722
   * Fixed the templates used to generate project and solution files for Visual
     Studio 2015 as well as the files themselves, to remove a build warning
     generated in Visual Studio 2015. Reported by Steve Valliere. #742
   * Fix a resource leak in ssl_cookie, when using MBEDTLS_THREADING_C.
     Raised and fix suggested by Alan Gillingham in the mbed TLS forum. #771
   * Fix 1 byte buffer overflow in mbedtls_mpi_write_string() when the MPI
     number to write in hexadecimal is negative and requires an odd number of
     digits. Found and fixed by Guido Vranken.
   * Fix unlisted DES configuration dependency in some pkparse test cases. Found
     by inestlerode. #555

= mbed TLS 2.4.1 branch released 2016-12-13

Changes
   * Update to CMAC test data, taken from - NIST Special Publication 800-38B -
     Recommendation for Block Cipher Modes of Operation: The CMAC Mode for
     Authentication – October  2016

= mbed TLS 2.4.0 branch released 2016-10-17

Security
   * Removed the MBEDTLS_SSL_AEAD_RANDOM_IV option, because it was not compliant
     with RFC-5116 and could lead to session key recovery in very long TLS
     sessions. "Nonce-Disrespecting Adversaries Practical Forgery Attacks on GCM in
     TLS" - H. Bock, A. Zauner, S. Devlin, J. Somorovsky, P. Jovanovic.
     https://eprint.iacr.org/2016/475.pdf
   * Fixed potential stack corruption in mbedtls_x509write_crt_der() and
     mbedtls_x509write_csr_der() when the signature is copied to the buffer
     without checking whether there is enough space in the destination. The
     issue cannot be triggered remotely. Found by Jethro Beekman.

Features
   * Added support for CMAC for AES and 3DES and AES-CMAC-PRF-128, as defined by
     NIST SP 800-38B, RFC-4493 and RFC-4615.
   * Added hardware entropy selftest to verify that the hardware entropy source
     is functioning correctly.
   * Added a script to print build environment info for diagnostic use in test
     scripts, which is also now called by all.sh.
   * Added the macro MBEDTLS_X509_MAX_FILE_PATH_LEN that enables the user to
     configure the maximum length of a file path that can be buffered when
     calling mbedtls_x509_crt_parse_path().
   * Added a configuration file config-no-entropy.h that configures the subset of
     library features that do not require an entropy source.
   * Added the macro MBEDTLS_ENTROPY_MIN_HARDWARE in config.h. This allows users
     to configure the minimum number of bytes for entropy sources using the
     mbedtls_hardware_poll() function.

Bugfix
   * Fix for platform time abstraction to avoid dependency issues where a build
     may need time but not the standard C library abstraction, and added
     configuration consistency checks to check_config.h
   * Fix dependency issue in Makefile to allow parallel builds.
   * Fix incorrect handling of block lengths in crypt_and_hash.c sample program,
     when GCM is used. Found by udf2457. #441
   * Fix for key exchanges based on ECDH-RSA or ECDH-ECDSA which weren't
     enabled unless others were also present. Found by David Fernandez. #428
   * Fix for out-of-tree builds using CMake. Found by jwurzer, and fix based on
     a contribution from Tobias Tangemann. #541
   * Fixed cert_app.c sample program for debug output and for use when no root
     certificates are provided.
   * Fix conditional statement that would cause a 1 byte overread in
     mbedtls_asn1_get_int(). Found and fixed by Guido Vranken. #599
   * Fixed pthread implementation to avoid unintended double initialisations
     and double frees. Found by Niklas Amnebratt.
   * Fixed the sample applications gen_key.c, cert_req.c and cert_write.c for
     builds where the configuration MBEDTLS_PEM_WRITE_C is not defined. Found
     by inestlerode. #559.
   * Fix mbedtls_x509_get_sig() to update the ASN1 type in the mbedtls_x509_buf
     data structure until after error checks are successful. Found by
     subramanyam-c. #622
   * Fix documentation and implementation missmatch for function arguments of
     mbedtls_gcm_finish(). Found by cmiatpaar. #602
   * Guarantee that P>Q at RSA key generation. Found by inestlerode. #558
   * Fix potential byte overread when verifying malformed SERVER_HELLO in
     ssl_parse_hello_verify_request() for DTLS. Found by Guido Vranken.
   * Fix check for validity of date when parsing in mbedtls_x509_get_time().
     Found by subramanyam-c. #626
   * Fix compatibility issue with Internet Explorer client authentication,
     where the limited hash choices prevented the client from sending its
     certificate. Found by teumas. #513
   * Fix compilation without MBEDTLS_SELF_TEST enabled.

Changes
   * Extended test coverage of special cases, and added new timing test suite.
   * Removed self-tests from the basic-built-test.sh script, and added all
     missing self-tests to the test suites, to ensure self-tests are only
     executed once.
   * Added support for 3 and 4 byte lengths to mbedtls_asn1_write_len().
   * Added support for a Yotta specific configuration file -
     through the symbol YOTTA_CFG_MBEDTLS_TARGET_CONFIG_FILE.
   * Added optimization for code space for X.509/OID based on configured
     features. Contributed by Aviv Palivoda.
   * Renamed source file library/net.c to library/net_sockets.c to avoid
     naming collision in projects which also have files with the common name
     net.c. For consistency, the corresponding header file, net.h, is marked as
     deprecated, and its contents moved to net_sockets.h.
   * Changed the strategy for X.509 certificate parsing and validation, to no
     longer disregard certificates with unrecognised fields.

= mbed TLS 2.3.0 branch released 2016-06-28

Security
   * Fix missing padding length check in mbedtls_rsa_rsaes_pkcs1_v15_decrypt
     required by PKCS1 v2.2
   * Fix potential integer overflow to buffer overflow in
     mbedtls_rsa_rsaes_pkcs1_v15_encrypt and mbedtls_rsa_rsaes_oaep_encrypt
     (not triggerable remotely in (D)TLS).
   * Fix a potential integer underflow to buffer overread in
     mbedtls_rsa_rsaes_oaep_decrypt. It is not triggerable remotely in
     SSL/TLS.

Features
   * Support for platform abstraction of the standard C library time()
     function.

Bugfix
   * Fix bug in mbedtls_mpi_add_mpi() that caused wrong results when the three
     arguments where the same (in-place doubling). Found and fixed by Janos
     Follath. #309
   * Fix potential build failures related to the 'apidoc' target, introduced
     in the previous patch release. Found by Robert Scheck. #390 #391
   * Fix issue in Makefile that prevented building using armar. #386
   * Fix memory leak that occurred only when ECJPAKE was enabled and ECDHE and
     ECDSA was disabled in config.h . The leak didn't occur by default.
   * Fix an issue that caused valid certificates to be rejected whenever an
     expired or not yet valid certificate was parsed before a valid certificate
     in the trusted certificate list.
   * Fix bug in mbedtls_x509_crt_parse that caused trailing extra data in the
     buffer after DER certificates to be included in the raw representation.
   * Fix issue that caused a hang when generating RSA keys of odd bitlength
   * Fix bug in mbedtls_rsa_rsaes_pkcs1_v15_encrypt that made null pointer
     dereference possible.
   * Fix issue that caused a crash if invalid curves were passed to
     mbedtls_ssl_conf_curves. #373
   * Fix issue in ssl_fork_server which was preventing it from functioning. #429
   * Fix memory leaks in test framework
   * Fix test in ssl-opt.sh that does not run properly with valgrind
   * Fix unchecked calls to mmbedtls_md_setup(). Fix by Brian Murray. #502

Changes
   * On ARM platforms, when compiling with -O0 with GCC, Clang or armcc5,
     don't use the optimized assembly for bignum multiplication. This removes
     the need to pass -fomit-frame-pointer to avoid a build error with -O0.
   * Disabled SSLv3 in the default configuration.
   * Optimized mbedtls_mpi_zeroize() for MPI integer size. (Fix by Alexey
     Skalozub).
   * Fix non-compliance server extension handling. Extensions for SSLv3 are now
     ignored, as required by RFC6101.

= mbed TLS 2.2.1 released 2016-01-05

Security
   * Fix potential double free when mbedtls_asn1_store_named_data() fails to
     allocate memory. Only used for certificate generation, not triggerable
     remotely in SSL/TLS. Found by Rafał Przywara. #367
   * Disable MD5 handshake signatures in TLS 1.2 by default to prevent the
     SLOTH attack on TLS 1.2 server authentication (other attacks from the
     SLOTH paper do not apply to any version of mbed TLS or PolarSSL).
     https://www.mitls.org/pages/attacks/SLOTH

Bugfix
   * Fix over-restrictive length limit in GCM. Found by Andreas-N. #362
   * Fix bug in certificate validation that caused valid chains to be rejected
     when the first intermediate certificate has pathLenConstraint=0. Found by
     Nicholas Wilson. Introduced in mbed TLS 2.2.0. #280
   * Removed potential leak in mbedtls_rsa_rsassa_pkcs1_v15_sign(), found by
     JayaraghavendranK. #372
   * Fix suboptimal handling of unexpected records that caused interop issues
     with some peers over unreliable links. Avoid dropping an entire DTLS
     datagram if a single record in a datagram is unexpected, instead only
     drop the record and look at subsequent records (if any are present) in
     the same datagram. Found by jeannotlapin. #345

= mbed TLS 2.2.0 released 2015-11-04

Security
   * Fix potential double free if mbedtls_ssl_conf_psk() is called more than
     once and some allocation fails. Cannot be forced remotely. Found by Guido
     Vranken, Intelworks.
   * Fix potential heap corruption on Windows when
     mbedtls_x509_crt_parse_path() is passed a path longer than 2GB. Cannot be
     triggered remotely. Found by Guido Vranken, Intelworks.
   * Fix potential buffer overflow in some asn1_write_xxx() functions.
     Cannot be triggered remotely unless you create X.509 certificates based
     on untrusted input or write keys of untrusted origin. Found by Guido
     Vranken, Intelworks.
   * The X509 max_pathlen constraint was not enforced on intermediate
     certificates. Found by Nicholas Wilson, fix and tests provided by
     Janos Follath. #280 and #319

Features
   * Experimental support for EC J-PAKE as defined in Thread 1.0.0.
     Disabled by default as the specification might still change.
   * Added a key extraction callback to accees the master secret and key
     block. (Potential uses include EAP-TLS and Thread.)

Bugfix
   * Self-signed certificates were not excluded from pathlen counting,
     resulting in some valid X.509 being incorrectly rejected. Found and fix
     provided by Janos Follath. #319
   * Fix build error with configurations where ECDHE-PSK is the only key
     exchange. Found and fix provided by Chris Hammond. #270
   * Fix build error with configurations where RSA, RSA-PSK, ECDH-RSA or
     ECHD-ECDSA if the only key exchange. Multiple reports. #310
   * Fixed a bug causing some handshakes to fail due to some non-fatal alerts
     not being properly ignored. Found by mancha and Kasom Koht-arsa, #308
   * mbedtls_x509_crt_verify(_with_profile)() now also checks the key type and
     size/curve against the profile. Before that, there was no way to set a
     minimum key size for end-entity certificates with RSA keys. Found by
     Matthew Page of Scannex Electronics Ltd.
   * Fix failures in MPI on Sparc(64) due to use of bad assembly code.
     Found by Kurt Danielson. #292
   * Fix typo in name of the extKeyUsage OID. Found by inestlerode, #314
   * Fix bug in ASN.1 encoding of booleans that caused generated CA
     certificates to be rejected by some applications, including OS X
     Keychain. Found and fixed by Jonathan Leroy, Inikup.

Changes
   * Improved performance of mbedtls_ecp_muladd() when one of the scalars is 1
     or -1.

= mbed TLS 2.1.2 released 2015-10-06

Security
   * Added fix for CVE-2015-5291 to prevent heap corruption due to buffer
     overflow of the hostname or session ticket. Found by Guido Vranken,
     Intelworks.
   * Fix potential double-free if mbedtls_ssl_set_hs_psk() is called more than
     once in the same handhake and mbedtls_ssl_conf_psk() was used.
     Found and patch provided by Guido Vranken, Intelworks. Cannot be forced
     remotely.
   * Fix stack buffer overflow in pkcs12 decryption (used by
     mbedtls_pk_parse_key(file)() when the password is > 129 bytes.
     Found by Guido Vranken, Intelworks. Not triggerable remotely.
   * Fix potential buffer overflow in mbedtls_mpi_read_string().
     Found by Guido Vranken, Intelworks. Not exploitable remotely in the context
     of TLS, but might be in other uses. On 32 bit machines, requires reading a
     string of close to or larger than 1GB to exploit; on 64 bit machines, would
     require reading a string of close to or larger than 2^62 bytes.
   * Fix potential random memory allocation in mbedtls_pem_read_buffer()
     on crafted PEM input data. Found and fix provided by Guido Vranken,
     Intelworks. Not triggerable remotely in TLS. Triggerable remotely if you
     accept PEM data from an untrusted source.
   * Fix possible heap buffer overflow in base64_encoded() when the input
     buffer is 512MB or larger on 32-bit platforms. Found by Guido Vranken,
     Intelworks. Not trigerrable remotely in TLS.
   * Fix potential double-free if mbedtls_conf_psk() is called repeatedly on
     the same mbedtls_ssl_config object and memory allocation fails. Found by
     Guido Vranken, Intelworks. Cannot be forced remotely.
   * Fix potential heap buffer overflow in servers that perform client
     authentication against a crafted CA cert. Cannot be triggered remotely
     unless you allow third parties to pick trust CAs for client auth.
     Found by Guido Vranken, Intelworks.

Bugfix
   * Fix compile error in net.c with musl libc. Found and patch provided by
     zhasha (#278).
   * Fix macroization of 'inline' keyword when building as C++. (#279)

Changes
   * Added checking of hostname length in mbedtls_ssl_set_hostname() to ensure
     domain names are compliant with RFC 1035.
   * Fixed paths for check_config.h in example config files. (Found by bachp)
     (#291)

= mbed TLS 2.1.1 released 2015-09-17

Security
   * Add countermeasure against Lenstra's RSA-CRT attack for PKCS#1 v1.5
     signatures. (Found by Florian Weimer, Red Hat.)
     https://securityblog.redhat.com/2015/09/02/factoring-rsa-keys-with-tls-perfect-forward-secrecy/
   * Fix possible client-side NULL pointer dereference (read) when the client
     tries to continue the handshake after it failed (a misuse of the API).
     (Found and patch provided by Fabian Foerg, Gotham Digital Science using
     afl-fuzz.)

Bugfix
   * Fix warning when using a 64bit platform. (found by embedthis) (#275)
   * Fix off-by-one error in parsing Supported Point Format extension that
     caused some handshakes to fail.

Changes
   * Made X509 profile pointer const in mbedtls_ssl_conf_cert_profile() to allow
     use of mbedtls_x509_crt_profile_next. (found by NWilson)
   * When a client initiates a reconnect from the same port as a live
     connection, if cookie verification is available
     (MBEDTLS_SSL_DTLS_HELLO_VERIFY defined in config.h, and usable cookie
     callbacks set with mbedtls_ssl_conf_dtls_cookies()), this will be
     detected and mbedtls_ssl_read() will return
     MBEDTLS_ERR_SSL_CLIENT_RECONNECT - it is then possible to start a new
     handshake with the same context. (See RFC 6347 section 4.2.8.)

= mbed TLS 2.1.0 released 2015-09-04

Features
   * Added support for yotta as a build system.
   * Primary open source license changed to Apache 2.0 license.

Bugfix
   * Fix segfault in the benchmark program when benchmarking DHM.
   * Fix build error with CMake and pre-4.5 versions of GCC (found by Hugo
     Leisink).
   * Fix bug when parsing a ServerHello without extensions (found by David
     Sears).
   * Fix bug in CMake lists that caused libmbedcrypto.a not to be installed
     (found by Benoit Lecocq).
   * Fix bug in Makefile that caused libmbedcrypto and libmbedx509 not to be
     installed (found by Rawi666).
   * Fix compile error with armcc 5 with --gnu option.
   * Fix bug in Makefile that caused programs not to be installed correctly
     (found by robotanarchy) (#232).
   * Fix bug in Makefile that prevented from installing without building the
     tests (found by robotanarchy) (#232).
   * Fix missing -static-libgcc when building shared libraries for Windows
     with make.
   * Fix link error when building shared libraries for Windows with make.
   * Fix error when loading libmbedtls.so.
   * Fix bug in mbedtls_ssl_conf_default() that caused the default preset to
     be always used (found by dcb314) (#235)
   * Fix bug in mbedtls_rsa_public() and mbedtls_rsa_private() that could
     result trying to unlock an unlocked mutex on invalid input (found by
     Fredrik Axelsson) (#257)
   * Fix -Wshadow warnings (found by hnrkp) (#240)
   * Fix memory corruption on client with overlong PSK identity, around
     SSL_MAX_CONTENT_LEN or higher - not triggerrable remotely (found by
     Aleksandrs Saveljevs) (#238)
   * Fix unused function warning when using MBEDTLS_MDx_ALT or
     MBEDTLS_SHAxxx_ALT (found by Henrik) (#239)
   * Fix memory corruption in pkey programs (found by yankuncheng) (#210)

Changes
   * The PEM parser now accepts a trailing space at end of lines (#226).
   * It is now possible to #include a user-provided configuration file at the
     end of the default config.h by defining MBEDTLS_USER_CONFIG_FILE on the
     compiler's command line.
   * When verifying a certificate chain, if an intermediate certificate is
     trusted, no later cert is checked. (suggested by hannes-landeholm)
     (#220).
   * Prepend a "thread identifier" to debug messages (issue pointed out by
     Hugo Leisink) (#210).
   * Add mbedtls_ssl_get_max_frag_len() to query the current maximum fragment
     length.

= mbed TLS 2.0.0 released 2015-07-13

Features
   * Support for DTLS 1.0 and 1.2 (RFC 6347).
   * Ability to override core functions from MDx, SHAx, AES and DES modules
     with custom implementation (eg hardware accelerated), complementing the
     ability to override the whole module.
   * New server-side implementation of session tickets that rotate keys to
     preserve forward secrecy, and allows sharing across multiple contexts.
   * Added a concept of X.509 cerificate verification profile that controls
     which algorithms and key sizes (curves for ECDSA) are acceptable.
   * Expanded configurability of security parameters in the SSL module with
     mbedtls_ssl_conf_dhm_min_bitlen() and mbedtls_ssl_conf_sig_hashes().
   * Introduced a concept of presets for SSL security-relevant configuration
     parameters.

API Changes
   * The library has been split into libmbedcrypto, libmbedx509, libmbedtls.
     You now need to link to all of them if you use TLS for example.
   * All public identifiers moved to the mbedtls_* or MBEDTLS_* namespace.
     Some names have been further changed to make them more consistent.
     Migration helpers scripts/rename.pl and include/mbedtls/compat-1.3.h are
     provided. Full list of renamings in scripts/data_files/rename-1.3-2.0.txt
   * Renamings of fields inside structures, not covered by the previous list:
     mbedtls_cipher_info_t.key_length -> key_bitlen
     mbedtls_cipher_context_t.key_length -> key_bitlen
     mbedtls_ecp_curve_info.size -> bit_size
   * Headers are now found in the 'mbedtls' directory (previously 'polarssl').
   * The following _init() functions that could return errors have
     been split into an _init() that returns void and another function that
     should generally be the first function called on this context after init:
     mbedtls_ssl_init() -> mbedtls_ssl_setup()
     mbedtls_ccm_init() -> mbedtls_ccm_setkey()
     mbedtls_gcm_init() -> mbedtls_gcm_setkey()
     mbedtls_hmac_drbg_init() -> mbedtls_hmac_drbg_seed(_buf)()
     mbedtls_ctr_drbg_init()  -> mbedtls_ctr_drbg_seed()
     Note that for mbedtls_ssl_setup(), you need to be done setting up the
     ssl_config structure before calling it.
   * Most ssl_set_xxx() functions (all except ssl_set_bio(), ssl_set_hostname(),
     ssl_set_session() and ssl_set_client_transport_id(), plus
     ssl_legacy_renegotiation()) have been renamed to mbedtls_ssl_conf_xxx()
     (see rename.pl and compat-1.3.h above) and their first argument's type
     changed from ssl_context to ssl_config.
   * ssl_set_bio() changed signature (contexts merged, order switched, one
     additional callback for read-with-timeout).
   * The following functions have been introduced and must be used in callback
     implementations (SNI, PSK) instead of their *conf counterparts:
     mbedtls_ssl_set_hs_own_cert()
     mbedtls_ssl_set_hs_ca_chain()
     mbedtls_ssl_set_hs_psk()
   * mbedtls_ssl_conf_ca_chain() lost its last argument (peer_cn), now set
     using mbedtls_ssl_set_hostname().
   * mbedtls_ssl_conf_session_cache() changed prototype (only one context
     pointer, parameters reordered).
   * On server, mbedtls_ssl_conf_session_tickets_cb() must now be used in
     place of mbedtls_ssl_conf_session_tickets() to enable session tickets.
   * The SSL debug callback gained two new arguments (file name, line number).
   * Debug modes were removed.
   * mbedtls_ssl_conf_truncated_hmac() now returns void.
   * mbedtls_memory_buffer_alloc_init() now returns void.
   * X.509 verification flags are now an uint32_t. Affect the signature of:
     mbedtls_ssl_get_verify_result()
     mbedtls_x509_ctr_verify_info()
     mbedtls_x509_crt_verify() (flags, f_vrfy -> needs to be updated)
     mbedtls_ssl_conf_verify() (f_vrfy -> needs to be updated)
   * The following functions changed prototype to avoid an in-out length
     parameter:
     mbedtls_base64_encode()
     mbedtls_base64_decode()
     mbedtls_mpi_write_string()
     mbedtls_dhm_calc_secret()
   * In the NET module, all "int" and "int *" arguments for file descriptors
     changed type to "mbedtls_net_context *".
   * net_accept() gained new arguments for the size of the client_ip buffer.
   * In the threading layer, mbedtls_mutex_init() and mbedtls_mutex_free() now
     return void.
   * ecdsa_write_signature() gained an additional md_alg argument and
     ecdsa_write_signature_det() was deprecated.
   * pk_sign() no longer accepts md_alg == POLARSSL_MD_NONE with ECDSA.
   * Last argument of x509_crt_check_key_usage() and
     mbedtls_x509write_crt_set_key_usage() changed from int to unsigned.
   * test_ca_list (from certs.h) is renamed to test_cas_pem and is only
     available if POLARSSL_PEM_PARSE_C is defined (it never worked without).
   * Test certificates in certs.c are no longer guaranteed to be nul-terminated
     strings; use the new *_len variables instead of strlen().
   * Functions mbedtls_x509_xxx_parse(), mbedtls_pk_parse_key(),
     mbedtls_pk_parse_public_key() and mbedtls_dhm_parse_dhm() now expect the
     length parameter to include the terminating null byte for PEM input.
   * Signature of mpi_mul_mpi() changed to make the last argument unsigned
   * calloc() is now used instead of malloc() everywhere. API of platform
     layer and the memory_buffer_alloc module changed accordingly.
     (Thanks to Mansour Moufid for helping with the replacement.)
   * Change SSL_DISABLE_RENEGOTIATION config.h flag to SSL_RENEGOTIATION
     (support for renegotiation now needs explicit enabling in config.h).
   * Split MBEDTLS_HAVE_TIME into MBEDTLS_HAVE_TIME and MBEDTLS_HAVE_TIME_DATE
     in config.h
   * net_connect() and net_bind() have a new 'proto' argument to choose
     between TCP and UDP, using the macros NET_PROTO_TCP or NET_PROTO_UDP.
     Their 'port' argument type is changed to a string.
   * Some constness fixes

Removals
   * Removed mbedtls_ecp_group_read_string(). Only named groups are supported.
   * Removed mbedtls_ecp_sub() and mbedtls_ecp_add(), use
     mbedtls_ecp_muladd().
   * Removed individual mdX_hmac, shaX_hmac, mdX_file and shaX_file functions
     (use generic functions from md.h)
   * Removed mbedtls_timing_msleep(). Use mbedtls_net_usleep() or a custom
     waiting function.
   * Removed test DHM parameters from the test certs module.
   * Removed the PBKDF2 module (use PKCS5).
   * Removed POLARSSL_ERROR_STRERROR_BC (use mbedtls_strerror()).
   * Removed compat-1.2.h (helper for migrating from 1.2 to 1.3).
   * Removed openssl.h (very partial OpenSSL compatibility layer).
   * Configuration options POLARSSL_HAVE_LONGLONG was removed (now always on).
   * Configuration options POLARSSL_HAVE_INT8 and POLARSSL_HAVE_INT16 have
     been removed (compiler is required to support 32-bit operations).
   * Configuration option POLARSSL_HAVE_IPV6 was removed (always enabled).
   * Removed test program o_p_test, the script compat.sh does more.
   * Removed test program ssl_test, superseded by ssl-opt.sh.
   * Removed helper script active-config.pl

New deprecations
   * md_init_ctx() is deprecated in favour of md_setup(), that adds a third
     argument (allowing memory savings if HMAC is not used)

Semi-API changes (technically public, morally private)
   * Renamed a few headers to include _internal in the name. Those headers are
     not supposed to be included by users.
   * Changed md_info_t into an opaque structure (use md_get_xxx() accessors).
   * Changed pk_info_t into an opaque structure.
   * Changed cipher_base_t into an opaque structure.
   * Removed sig_oid2 and rename sig_oid1 to sig_oid in x509_crt and x509_crl.
   * x509_crt.key_usage changed from unsigned char to unsigned int.
   * Removed r and s from ecdsa_context
   * Removed mode from des_context and des3_context

Default behavior changes
   * The default minimum TLS version is now TLS 1.0.
   * RC4 is now blacklisted by default in the SSL/TLS layer, and excluded from the
     default ciphersuite list returned by ssl_list_ciphersuites()
   * Support for receiving SSLv2 ClientHello is now disabled by default at
     compile time.
   * The default authmode for SSL/TLS clients is now REQUIRED.
   * Support for RSA_ALT contexts in the PK layer is now optional. Since is is
     enabled in the default configuration, this is only noticeable if using a
     custom config.h
   * Default DHM parameters server-side upgraded from 1024 to 2048 bits.
   * A minimum RSA key size of 2048 bits is now enforced during ceritificate
     chain verification.
   * Negotiation of truncated HMAC is now disabled by default on server too.
   * The following functions are now case-sensitive:
     mbedtls_cipher_info_from_string()
     mbedtls_ecp_curve_info_from_name()
     mbedtls_md_info_from_string()
     mbedtls_ssl_ciphersuite_from_string()
     mbedtls_version_check_feature()

Requirement changes
   * The minimum MSVC version required is now 2010 (better C99 support).
   * The NET layer now unconditionnaly relies on getaddrinfo() and select().
   * Compiler is required to support C99 types such as long long and uint32_t.

API changes from the 1.4 preview branch
   * ssl_set_bio_timeout() was removed, split into mbedtls_ssl_set_bio() with
     new prototype, and mbedtls_ssl_set_read_timeout().
   * The following functions now return void:
     mbedtls_ssl_conf_transport()
     mbedtls_ssl_conf_max_version()
     mbedtls_ssl_conf_min_version()
   * DTLS no longer hard-depends on TIMING_C, but uses a callback interface
     instead, see mbedtls_ssl_set_timer_cb(), with the Timing module providing
     an example implementation, see mbedtls_timing_delay_context and
     mbedtls_timing_set/get_delay().
   * With UDP sockets, it is no longer necessary to call net_bind() again
     after a successful net_accept().

Changes
   * mbedtls_ctr_drbg_random() and mbedtls_hmac_drbg_random() are now
     thread-safe if MBEDTLS_THREADING_C is enabled.
   * Reduced ROM fooprint of SHA-256 and added an option to reduce it even
     more (at the expense of performance) MBEDTLS_SHA256_SMALLER.

= mbed TLS 1.3 branch

Security
   * With authmode set to SSL_VERIFY_OPTIONAL, verification of keyUsage and
     extendedKeyUsage on the leaf certificate was lost (results not accessible
     via ssl_get_verify_results()).
   * Add countermeasure against "Lucky 13 strikes back" cache-based attack,
     https://dl.acm.org/citation.cfm?id=2714625

Features
   * Improve ECC performance by using more efficient doubling formulas
     (contributed by Peter Dettman).
   * Add x509_crt_verify_info() to display certificate verification results.
   * Add support for reading DH parameters with privateValueLength included
     (contributed by Daniel Kahn Gillmor).
   * Add support for bit strings in X.509 names (request by Fredrik Axelsson).
   * Add support for id-at-uniqueIdentifier in X.509 names.
   * Add support for overriding snprintf() (except on Windows) and exit() in
     the platform layer.
   * Add an option to use macros instead of function pointers in the platform
     layer (helps get rid of unwanted references).
   * Improved Makefiles for Windows targets by fixing library targets and making
     cross-compilation easier (thanks to Alon Bar-Lev).
   * The benchmark program also prints heap usage for public-key primitives
     if POLARSSL_MEMORY_BUFFER_ALLOC_C and POLARSSL_MEMORY_DEBUG are defined.
   * New script ecc-heap.sh helps measuring the impact of ECC parameters on
     speed and RAM (heap only for now) usage.
   * New script memory.sh helps measuring the ROM and RAM requirements of two
     reduced configurations (PSK-CCM and NSA suite B).
   * Add config flag POLARSSL_DEPRECATED_WARNING (off by default) to produce
     warnings on use of deprecated functions (with GCC and Clang only).
   * Add config flag POLARSSL_DEPRECATED_REMOVED (off by default) to produce
     errors on use of deprecated functions.

Bugfix
   * Fix compile errors with PLATFORM_NO_STD_FUNCTIONS.
   * Fix compile error with PLATFORM_EXIT_ALT (thanks to Rafał Przywara).
   * Fix bug in entropy.c when THREADING_C is also enabled that caused
     entropy_free() to crash (thanks to Rafał Przywara).
   * Fix memory leak when gcm_setkey() and ccm_setkey() are used more than
     once on the same context.
   * Fix bug in ssl_mail_client when password is longer that username (found
     by Bruno Pape).
   * Fix undefined behaviour (memcmp( NULL, NULL, 0 );) in X.509 modules
     (detected by Clang's 3.6 UBSan).
   * mpi_size() and mpi_msb() would segfault when called on an mpi that is
     initialized but not set (found by pravic).
   * Fix detection of support for getrandom() on Linux (reported by syzzer) by
     doing it at runtime (using uname) rather that compile time.
   * Fix handling of symlinks by "make install" (found by Gaël PORTAY).
   * Fix potential NULL pointer dereference (not trigerrable remotely) when
     ssl_write() is called before the handshake is finished (introduced in
     1.3.10) (first reported by Martin Blumenstingl).
   * Fix bug in pk_parse_key() that caused some valid private EC keys to be
     rejected.
   * Fix bug in Via Padlock support (found by Nikos Mavrogiannopoulos).
   * Fix thread safety bug in RSA operations (found by Fredrik Axelsson).
   * Fix hardclock() (only used in the benchmarking program) with some
     versions of mingw64 (found by kxjhlele).
   * Fix warnings from mingw64 in timing.c (found by kxjklele).
   * Fix potential unintended sign extension in asn1_get_len() on 64-bit
     platforms.
   * Fix potential memory leak in ssl_set_psk() (found by Mansour Moufid).
   * Fix compile error when POLARSSL_SSL_DISABLE_RENEGOTATION and
     POLARSSL_SSL_SSESSION_TICKETS where both enabled in config.h (introduced
     in 1.3.10).
   * Add missing extern "C" guard in aesni.h (reported by amir zamani).
   * Add missing dependency on SHA-256 in some x509 programs (reported by
     Gergely Budai).
   * Fix bug related to ssl_set_curves(): the client didn't check that the
     curve picked by the server was actually allowed.

Changes
   * Remove bias in mpi_gen_prime (contributed by Pascal Junod).
   * Remove potential sources of timing variations (some contributed by Pascal
     Junod).
   * Options POLARSSL_HAVE_INT8 and POLARSSL_HAVE_INT16 are deprecated.
   * Enabling POLARSSL_NET_C without POLARSSL_HAVE_IPV6 is deprecated.
   * compat-1.2.h and openssl.h are deprecated.
   * Adjusting/overriding CFLAGS and LDFLAGS with the make build system is now
     more flexible (warning: OFLAGS is not used any more) (see the README)
     (contributed by Alon Bar-Lev).
   * ssl_set_own_cert() no longer calls pk_check_pair() since the
     performance impact was bad for some users (this was introduced in 1.3.10).
   * Move from SHA-1 to SHA-256 in example programs using signatures
     (suggested by Thorsten Mühlfelder).
   * Remove some unneeded inclusions of header files from the standard library
     "minimize" others (eg use stddef.h if only size_t is needed).
   * Change #include lines in test files to use double quotes instead of angle
     brackets for uniformity with the rest of the code.
   * Remove dependency on sscanf() in X.509 parsing modules.

= mbed TLS 1.3.10 released 2015-02-09
Security
   * NULL pointer dereference in the buffer-based allocator when the buffer is
     full and polarssl_free() is called (found by Mark Hasemeyer)
     (only possible if POLARSSL_MEMORY_BUFFER_ALLOC_C is enabled, which it is
     not by default).
   * Fix remotely-triggerable uninitialised pointer dereference caused by
     crafted X.509 certificate (TLS server is not affected if it doesn't ask for a
     client certificate) (found using Codenomicon Defensics).
   * Fix remotely-triggerable memory leak caused by crafted X.509 certificates
     (TLS server is not affected if it doesn't ask for a client certificate)
     (found using Codenomicon Defensics).
   * Fix potential stack overflow while parsing crafted X.509 certificates
     (TLS server is not affected if it doesn't ask for a client certificate)
     (found using Codenomicon Defensics).
   * Fix timing difference that could theoretically lead to a
     Bleichenbacher-style attack in the RSA and RSA-PSK key exchanges
     (reported by Sebastian Schinzel).

Features
   * Add support for FALLBACK_SCSV (draft-ietf-tls-downgrade-scsv).
   * Add support for Extended Master Secret (draft-ietf-tls-session-hash).
   * Add support for Encrypt-then-MAC (RFC 7366).
   * Add function pk_check_pair() to test if public and private keys match.
   * Add x509_crl_parse_der().
   * Add compile-time option POLARSSL_X509_MAX_INTERMEDIATE_CA to limit the
     length of an X.509 verification chain.
   * Support for renegotiation can now be disabled at compile-time
   * Support for 1/n-1 record splitting, a countermeasure against BEAST.
   * Certificate selection based on signature hash, preferring SHA-1 over SHA-2
     for pre-1.2 clients when multiple certificates are available.
   * Add support for getrandom() syscall on recent Linux kernels with Glibc or
     a compatible enough libc (eg uClibc).
   * Add ssl_set_arc4_support() to make it easier to disable RC4 at runtime
     while using the default ciphersuite list.
   * Added new error codes and debug messages about selection of
     ciphersuite/certificate.

Bugfix
   * Stack buffer overflow if ctr_drbg_update() is called with too large
     add_len (found by Jean-Philippe Aumasson) (not triggerable remotely).
   * Possible buffer overflow of length at most POLARSSL_MEMORY_ALIGN_MULTIPLE
     if memory_buffer_alloc_init() was called with buf not aligned and len not
     a multiple of POLARSSL_MEMORY_ALIGN_MULTIPLE (not triggerable remotely).
   * User set CFLAGS were ignored by Cmake with gcc (introduced in 1.3.9, found
     by Julian Ospald).
   * Fix potential undefined behaviour in Camellia.
   * Fix potential failure in ECDSA signatures when POLARSSL_ECP_MAX_BITS is a
     multiple of 8 (found by Gergely Budai).
   * Fix unchecked return code in x509_crt_parse_path() on Windows (found by
     Peter Vaskovic).
   * Fix assembly selection for MIPS64 (thanks to James Cowgill).
   * ssl_get_verify_result() now works even if the handshake was aborted due
     to a failed verification (found by Fredrik Axelsson).
   * Skip writing and parsing signature_algorithm extension if none of the
     key exchanges enabled needs certificates. This fixes a possible interop
     issue with some servers when a zero-length extension was sent. (Reported
     by Peter Dettman.)
   * On a 0-length input, base64_encode() did not correctly set output length
     (found by Hendrik van den Boogaard).

Changes
   * Use deterministic nonces for AEAD ciphers in TLS by default (possible to
     switch back to random with POLARSSL_SSL_AEAD_RANDOM_IV in config.h).
   * Blind RSA private operations even when POLARSSL_RSA_NO_CRT is defined.
   * ssl_set_own_cert() now returns an error on key-certificate mismatch.
   * Forbid repeated extensions in X.509 certificates.
   * debug_print_buf() now prints a text view in addition to hexadecimal.
   * A specific error is now returned when there are ciphersuites in common
     but none of them is usable due to external factors such as no certificate
     with a suitable (extended)KeyUsage or curve or no PSK set.
   * It is now possible to disable negotiation of truncated HMAC server-side
     at runtime with ssl_set_truncated_hmac().
   * Example programs for SSL client and server now disable SSLv3 by default.
   * Example programs for SSL client and server now disable RC4 by default.
   * Use platform.h in all test suites and programs.

= PolarSSL 1.3.9 released 2014-10-20
Security
   * Lowest common hash was selected from signature_algorithms extension in
     TLS 1.2 (found by Darren Bane) (introduced in 1.3.8).
   * Remotely-triggerable memory leak when parsing some X.509 certificates
     (server is not affected if it doesn't ask for a client certificate)
     (found using Codenomicon Defensics).
   * Remotely-triggerable memory leak when parsing crafted ClientHello
     (not affected if ECC support was compiled out) (found using Codenomicon
     Defensics).

Bugfix
   * Support escaping of commas in x509_string_to_names()
   * Fix compile error in ssl_pthread_server (found by Julian Ospald).
   * Fix net_accept() regarding non-blocking sockets (found by Luca Pesce).
   * Don't print uninitialised buffer in ssl_mail_client (found by Marc Abel).
   * Fix warnings from Clang's scan-build (contributed by Alfred Klomp).
   * Fix compile error in timing.c when POLARSSL_NET_C and POLARSSL_SELFTEST
     are defined but not POLARSSL_HAVE_TIME (found by Stephane Di Vito).
   * Remove non-existent file from VS projects (found by Peter Vaskovic).
   * ssl_read() could return non-application data records on server while
     renegotation was pending, and on client when a HelloRequest was received.
   * Server-initiated renegotiation would fail with non-blocking I/O if the
     write callback returned WANT_WRITE when requesting renegotiation.
   * ssl_close_notify() could send more than one message in some circumstances
     with non-blocking I/O.
   * Fix compiler warnings on iOS (found by Sander Niemeijer).
   * x509_crt_parse() did not increase total_failed on PEM error
   * Fix compile error with armcc in mpi_is_prime()
   * Fix potential bad read in parsing ServerHello (found by Adrien
     Vialletelle).

Changes
   * Ciphersuites using SHA-256 or SHA-384 now require TLS 1.x (there is no
     standard defining how to use SHA-2 with SSL 3.0).
   * Ciphersuites using RSA-PSK key exchange new require TLS 1.x (the spec is
     ambiguous on how to encode some packets with SSL 3.0).
   * Made buffer size in pk_write_(pub)key_pem() more dynamic, eg smaller if
     RSA is disabled, larger if POLARSSL_MPI_MAX_SIZE is larger.
   * ssl_read() now returns POLARSSL_ERR_NET_WANT_READ rather than
     POLARSSL_ERR_SSL_UNEXPECTED_MESSAGE on harmless alerts.
   * POLARSSL_MPI_MAX_SIZE now defaults to 1024 in order to allow 8192 bits
     RSA keys.
   * Accept spaces at end of line or end of buffer in base64_decode().
   * X.509 certificates with more than one AttributeTypeAndValue per
     RelativeDistinguishedName are not accepted any more.

= PolarSSL 1.3.8 released 2014-07-11
Security
   * Fix length checking for AEAD ciphersuites (found by Codenomicon).
     It was possible to crash the server (and client) using crafted messages
     when a GCM suite was chosen.

Features
   * Add CCM module and cipher mode to Cipher Layer
   * Support for CCM and CCM_8 ciphersuites
   * Support for parsing and verifying RSASSA-PSS signatures in the X.509
     modules (certificates, CRLs and CSRs).
   * Blowfish in the cipher layer now supports variable length keys.
   * Add example config.h for PSK with CCM, optimized for low RAM usage.
   * Optimize for RAM usage in example config.h for NSA Suite B profile.
   * Add POLARSSL_REMOVE_ARC4_CIPHERSUITES to allow removing RC4 ciphersuites
     from the default list (inactive by default).
   * Add server-side enforcement of sent renegotiation requests
     (ssl_set_renegotiation_enforced())
   * Add SSL_CIPHERSUITES config.h flag to allow specifying a list of
     ciphersuites to use and save some memory if the list is small.

Changes
   * Add LINK_WITH_PTHREAD option in CMake for explicit linking that is
     required on some platforms (e.g. OpenBSD)
   * Migrate zeroizing of data to polarssl_zeroize() instead of memset()
     against unwanted compiler optimizations
   * md_list() now returns hashes strongest first
   * Selection of hash for signing ServerKeyExchange in TLS 1.2 now picks
     strongest offered by client.
   * All public contexts have _init() and _free() functions now for simpler
     usage pattern

Bugfix
   * Fix in debug_print_msg()
   * Enforce alignment in the buffer allocator even if buffer is not aligned
   * Remove less-than-zero checks on unsigned numbers
   * Stricter check on SSL ClientHello internal sizes compared to actual packet
     size (found by TrustInSoft)
   * Fix WSAStartup() return value check (found by Peter Vaskovic)
   * Other minor issues (found by Peter Vaskovic)
   * Fix symlink command for cross compiling with CMake (found by Andre
     Heinecke)
   * Fix DER output of gen_key app (found by Gergely Budai)
   * Very small records were incorrectly rejected when truncated HMAC was in
     use with some ciphersuites and versions (RC4 in all versions, CBC with
     versions < TLS 1.1).
   * Very large records using more than 224 bytes of padding were incorrectly
     rejected with CBC-based ciphersuites and TLS >= 1.1
   * Very large records using less padding could cause a buffer overread of up
     to 32 bytes with CBC-based ciphersuites and TLS >= 1.1
   * Restore ability to use a v1 cert as a CA if trusted locally. (This had
     been removed in 1.3.6.)
   * Restore ability to locally trust a self-signed cert that is not a proper
     CA for use as an end entity certificate. (This had been removed in
     1.3.6.)
   * Fix preprocessor checks for bn_mul PPC asm (found by Barry K. Nathan).
   * Use \n\t rather than semicolons for bn_mul asm, since some assemblers
     interpret semicolons as comment delimiters (found by Barry K. Nathan).
   * Fix off-by-one error in parsing Supported Point Format extension that
     caused some handshakes to fail.
   * Fix possible miscomputation of the premaster secret with DHE-PSK key
     exchange that caused some handshakes to fail with other implementations.
     (Failure rate <= 1/255 with common DHM moduli.)
   * Disable broken Sparc64 bn_mul assembly (found by Florian Obser).
   * Fix base64_decode() to return and check length correctly (in case of
     tight buffers)
   * Fix mpi_write_string() to write "00" as hex output for empty MPI (found
     by Hui Dong)

= PolarSSL 1.3.7 released on 2014-05-02
Features
   * debug_set_log_mode() added to determine raw or full logging
   * debug_set_threshold() added to ignore messages over threshold level
   * version_check_feature() added to check for compile-time options at
     run-time

Changes
   * POLARSSL_CONFIG_OPTIONS has been removed. All values are individually
     checked and filled in the relevant module headers
   * Debug module only outputs full lines instead of parts
   * Better support for the different Attribute Types from IETF PKIX (RFC 5280)
   * AES-NI now compiles with "old" assemblers too
   * Ciphersuites based on RC4 now have the lowest priority by default

Bugfix
   * Only iterate over actual certificates in ssl_write_certificate_request()
     (found by Matthew Page)
   * Typos in platform.c and pkcs11.c (found by Daniel Phillips and Steffan
     Karger)
   * cert_write app should use subject of issuer certificate as issuer of cert
   * Fix false reject in padding check in ssl_decrypt_buf() for CBC
     ciphersuites, for full SSL frames of data.
   * Improve interoperability by not writing extension length in ClientHello /
     ServerHello when no extensions are present (found by Matthew Page)
   * rsa_check_pubkey() now allows an E up to N
   * On OpenBSD, use arc4random_buf() instead of rand() to prevent warnings
   * mpi_fill_random() was creating numbers larger than requested on
     big-endian platform when size was not an integer number of limbs
   * Fix dependencies issues in X.509 test suite.
   * Some parts of ssl_tls.c were compiled even when the module was disabled.
   * Fix detection of DragonflyBSD in net.c (found by Markus Pfeiffer)
   * Fix detection of Clang on some Apple platforms with CMake
     (found by Barry K. Nathan)

= PolarSSL 1.3.6 released on 2014-04-11

Features
   * Support for the ALPN SSL extension
   * Add option 'use_dev_random' to gen_key application
   * Enable verification of the keyUsage extension for CA and leaf
     certificates (POLARSSL_X509_CHECK_KEY_USAGE)
   * Enable verification of the extendedKeyUsage extension
     (POLARSSL_X509_CHECK_EXTENDED_KEY_USAGE)

Changes
   * x509_crt_info() now prints information about parsed extensions as well
   * pk_verify() now returns a specific error code when the signature is valid
     but shorter than the supplied length.
   * Use UTC time to check certificate validity.
   * Reject certificates with times not in UTC, per RFC 5280.

Security
   * Avoid potential timing leak in ecdsa_sign() by blinding modular division.
     (Found by Watson Ladd.)
   * The notAfter date of some certificates was no longer checked since 1.3.5.
     This affects certificates in the user-supplied chain except the top
     certificate. If the user-supplied chain contains only one certificates,
     it is not affected (ie, its notAfter date is properly checked).
   * Prevent potential NULL pointer dereference in ssl_read_record() (found by
     TrustInSoft)

Bugfix
   * The length of various ClientKeyExchange messages was not properly checked.
   * Some example server programs were not sending the close_notify alert.
   * Potential memory leak in mpi_exp_mod() when error occurs during
     calculation of RR.
   * Fixed malloc/free default #define in platform.c (found by Gergely Budai).
   * Fixed type which made POLARSSL_ENTROPY_FORCE_SHA256 uneffective (found by
     Gergely Budai).
   * Fix #include path in ecdsa.h which wasn't accepted by some compilers.
     (found by Gergely Budai)
   * Fix compile errors when POLARSSL_ERROR_STRERROR_BC is undefined (found by
     Shuo Chen).
   * oid_get_numeric_string() used to truncate the output without returning an
     error if the output buffer was just 1 byte too small.
   * dhm_parse_dhm() (hence dhm_parse_dhmfile()) did not set dhm->len.
   * Calling pk_debug() on an RSA-alt key would segfault.
   * pk_get_size() and pk_get_len() were off by a factor 8 for RSA-alt keys.
   * Potential buffer overwrite in pem_write_buffer() because of low length
     indication (found by Thijs Alkemade)
   * EC curves constants, which should be only in ROM since 1.3.3, were also
     stored in RAM due to missing 'const's (found by Gergely Budai).

= PolarSSL 1.3.5 released on 2014-03-26
Features
   * HMAC-DRBG as a separate module
   * Option to set the Curve preference order (disabled by default)
   * Single Platform compatilibity layer (for memory / printf / fprintf)
   * Ability to provide alternate timing implementation
   * Ability to force the entropy module to use SHA-256 as its basis
     (POLARSSL_ENTROPY_FORCE_SHA256)
   * Testing script ssl-opt.sh added for testing 'live' ssl option
     interoperability against OpenSSL and PolarSSL
   * Support for reading EC keys that use SpecifiedECDomain in some cases.
   * Entropy module now supports seed writing and reading

Changes
   * Deprecated the Memory layer
   * entropy_add_source(), entropy_update_manual() and entropy_gather()
     now thread-safe if POLARSSL_THREADING_C defined
   * Improvements to the CMake build system, contributed by Julian Ospald.
   * Work around a bug of the version of Clang shipped by Apple with Mavericks
     that prevented bignum.c from compiling. (Reported by Rafael Baptista.)
   * Revamped the compat.sh interoperatibility script to include support for
     testing against GnuTLS
   * Deprecated ssl_set_own_cert_rsa() and ssl_set_own_cert_rsa_alt()
   * Improvements to tests/Makefile, contributed by Oden Eriksson.

Security
   * Forbid change of server certificate during renegotiation to prevent
     "triple handshake" attack when authentication mode is 'optional' (the
     attack was already impossible when authentication is required).
   * Check notBefore timestamp of certificates and CRLs from the future.
   * Forbid sequence number wrapping
   * Fixed possible buffer overflow with overlong PSK
   * Possible remotely-triggered out-of-bounds memory access fixed (found by
     TrustInSoft)

Bugfix
   * ecp_gen_keypair() does more tries to prevent failure because of
     statistics
   * Fixed bug in RSA PKCS#1 v1.5 "reversed" operations
   * Fixed testing with out-of-source builds using cmake
   * Fixed version-major intolerance in server
   * Fixed CMake symlinking on out-of-source builds
   * Fixed dependency issues in test suite
   * Programs rsa_sign_pss and rsa_verify_pss were not using PSS since 1.3.0
   * Bignum's MIPS-32 assembly was used on MIPS-64, causing chaos. (Found by
     Alex Wilson.)
   * ssl_cache was creating entries when max_entries=0 if TIMING_C was enabled.
   * m_sleep() was sleeping twice too long on most Unix platforms.
   * Fixed bug with session tickets and non-blocking I/O in the unlikely case
     send() would return an EAGAIN error when sending the ticket.
   * ssl_cache was leaking memory when reusing a timed out entry containing a
     client certificate.
   * ssl_srv was leaking memory when client presented a timed out ticket
     containing a client certificate
   * ssl_init() was leaving a dirty pointer in ssl_context if malloc of
     out_ctr failed
   * ssl_handshake_init() was leaving dirty pointers in subcontexts if malloc
     of one of them failed
   * Fix typo in rsa_copy() that impacted PKCS#1 v2 contexts
   * x509_get_current_time() uses localtime_r() to prevent thread issues

= PolarSSL 1.3.4 released on 2014-01-27
Features
   * Support for the Koblitz curves: secp192k1, secp224k1, secp256k1
   * Support for RIPEMD-160
   * Support for AES CFB8 mode
   * Support for deterministic ECDSA (RFC 6979)

Bugfix
   * Potential memory leak in bignum_selftest()
   * Replaced expired test certificate
   * ssl_mail_client now terminates lines with CRLF, instead of LF
   * net module handles timeouts on blocking sockets better (found by Tilman
     Sauerbeck)
   * Assembly format fixes in bn_mul.h

Security
   * Missing MPI_CHK calls added around unguarded mpi calls (found by
     TrustInSoft)

= PolarSSL 1.3.3 released on 2013-12-31
Features
   * EC key generation support in gen_key app
   * Support for adhering to client ciphersuite order preference
     (POLARSSL_SSL_SRV_RESPECT_CLIENT_PREFERENCE)
   * Support for Curve25519
   * Support for ECDH-RSA and ECDH-ECDSA key exchanges and ciphersuites
   * Support for IPv6 in the NET module
   * AES-NI support for AES, AES-GCM and AES key scheduling
   * SSL Pthread-based server example added (ssl_pthread_server)

Changes
   * gen_prime() speedup
   * Speedup of ECP multiplication operation
   * Relaxed some SHA2 ciphersuite's version requirements
   * Dropped use of readdir_r() instead of readdir() with threading support
   * More constant-time checks in the RSA module
   * Split off curves from ecp.c into ecp_curves.c
   * Curves are now stored fully in ROM
   * Memory usage optimizations in ECP module
   * Removed POLARSSL_THREADING_DUMMY

Bugfix
   * Fixed bug in mpi_set_bit() on platforms where t_uint is wider than int
   * Fixed X.509 hostname comparison (with non-regular characters)
   * SSL now gracefully handles missing RNG
   * Missing defines / cases for RSA_PSK key exchange
   * crypt_and_hash app checks MAC before final decryption
   * Potential memory leak in ssl_ticket_keys_init()
   * Memory leak in benchmark application
   * Fixed x509_crt_parse_path() bug on Windows platforms
   * Added missing MPI_CHK() around some statements in mpi_div_mpi() (found by
     TrustInSoft)
   * Fixed potential overflow in certificate size verification in
     ssl_write_certificate() (found by TrustInSoft)

Security
   * Possible remotely-triggered out-of-bounds memory access fixed (found by
     TrustInSoft)

= PolarSSL 1.3.2 released on 2013-11-04
Features
   * PK tests added to test framework
   * Added optional optimization for NIST MODP curves (POLARSSL_ECP_NIST_OPTIM)
   * Support for Camellia-GCM mode and ciphersuites

Changes
   * Padding checks in cipher layer are now constant-time
   * Value comparisons in SSL layer are now constant-time
   * Support for serialNumber, postalAddress and postalCode in X509 names
   * SSL Renegotiation was refactored

Bugfix
   * More stringent checks in cipher layer
   * Server does not send out extensions not advertised by client
   * Prevent possible alignment warnings on casting from char * to 'aligned *'
   * Misc fixes and additions to dependency checks
   * Const correctness
   * cert_write with selfsign should use issuer_name as subject_name
   * Fix ECDSA corner case: missing reduction mod N (found by DualTachyon)
   * Defines to handle UEFI environment under MSVC
   * Server-side initiated renegotiations send HelloRequest

= PolarSSL 1.3.1 released on 2013-10-15
Features
   * Support for Brainpool curves and TLS ciphersuites (RFC 7027)
   * Support for ECDHE-PSK key-exchange and ciphersuites
   * Support for RSA-PSK key-exchange and ciphersuites

Changes
   * RSA blinding locks for a smaller amount of time
   * TLS compression only allocates working buffer once
   * Introduced POLARSSL_HAVE_READDIR_R for systems without it
   * config.h is more script-friendly

Bugfix
   * Missing MSVC defines added
   * Compile errors with POLARSSL_RSA_NO_CRT
   * Header files with 'polarssl/'
   * Const correctness
   * Possible naming collision in dhm_context
   * Better support for MSVC
   * threading_set_alt() name
   * Added missing x509write_crt_set_version()

= PolarSSL 1.3.0 released on 2013-10-01
Features
   * Elliptic Curve Cryptography module added
   * Elliptic Curve Diffie Hellman module added
   * Ephemeral Elliptic Curve Diffie Hellman support for SSL/TLS
    (ECDHE-based ciphersuites)
   * Ephemeral Elliptic Curve Digital Signature Algorithm support for SSL/TLS
    (ECDSA-based ciphersuites)
   * Ability to specify allowed ciphersuites based on the protocol version.
   * PSK and DHE-PSK based ciphersuites added
   * Memory allocation abstraction layer added
   * Buffer-based memory allocator added (no malloc() / free() / HEAP usage)
   * Threading abstraction layer added (dummy / pthread / alternate)
   * Public Key abstraction layer added
   * Parsing Elliptic Curve keys
   * Parsing Elliptic Curve certificates
   * Support for max_fragment_length extension (RFC 6066)
   * Support for truncated_hmac extension (RFC 6066)
   * Support for zeros-and-length (ANSI X.923) padding, one-and-zeros
     (ISO/IEC 7816-4) padding and zero padding in the cipher layer
   * Support for session tickets (RFC 5077)
   * Certificate Request (CSR) generation with extensions (key_usage,
     ns_cert_type)
   * X509 Certificate writing with extensions (basic_constraints,
     issuer_key_identifier, etc)
   * Optional blinding for RSA, DHM and EC
   * Support for multiple active certificate / key pairs in SSL servers for
     the same host (Not to be confused with SNI!)

Changes
   * Ability to enable / disable SSL v3 / TLS 1.0 / TLS 1.1 / TLS 1.2
     individually
   * Introduced separate SSL Ciphersuites module that is based on
     Cipher and MD information
   * Internals for SSL module adapted to have separate IV pointer that is
     dynamically set (Better support for hardware acceleration)
   * Moved all OID functionality to a separate module. RSA function
     prototypes for the RSA sign and verify functions changed as a result
   * Split up the GCM module into a starts/update/finish cycle
   * Client and server now filter sent and accepted ciphersuites on minimum
     and maximum protocol version
   * Ability to disable server_name extension (RFC 6066)
   * Renamed error_strerror() to the less conflicting polarssl_strerror()
     (Ability to keep old as well with POLARSSL_ERROR_STRERROR_BC)
   * SHA2 renamed to SHA256, SHA4 renamed to SHA512 and functions accordingly
   * All RSA operations require a random generator for blinding purposes
   * X509 core refactored
   * x509_crt_verify() now case insensitive for cn (RFC 6125 6.4)
   * Also compiles / runs without time-based functions (!POLARSSL_HAVE_TIME)
   * Support faulty X509 v1 certificates with extensions
     (POLARSSL_X509_ALLOW_EXTENSIONS_NON_V3)

Bugfix
   * Fixed parse error in ssl_parse_certificate_request()
   * zlib compression/decompression skipped on empty blocks
   * Support for AIX header locations in net.c module
   * Fixed file descriptor leaks

Security
   * RSA blinding on CRT operations to counter timing attacks
     (found by Cyril Arnaud and Pierre-Alain Fouque)


= Version 1.2.14 released 2015-05-??

Security
   * Fix potential invalid memory read in the server, that allows a client to
     crash it remotely (found by Caj Larsson).
   * Fix potential invalid memory read in certificate parsing, that allows a
     client to crash the server remotely if client authentication is enabled
     (found using Codenomicon Defensics).
   * Add countermeasure against "Lucky 13 strikes back" cache-based attack,
     https://dl.acm.org/citation.cfm?id=2714625

Bugfix
   * Fix bug in Via Padlock support (found by Nikos Mavrogiannopoulos).
   * Fix hardclock() (only used in the benchmarking program) with some
     versions of mingw64 (found by kxjhlele).
   * Fix warnings from mingw64 in timing.c (found by kxjklele).
   * Fix potential unintended sign extension in asn1_get_len() on 64-bit
     platforms (found with Coverity Scan).

= Version 1.2.13 released 2015-02-16
Note: Although PolarSSL has been renamed to mbed TLS, no changes reflecting
      this will be made in the 1.2 branch at this point.

Security
   * Fix remotely-triggerable uninitialised pointer dereference caused by
     crafted X.509 certificate (TLS server is not affected if it doesn't ask
     for a client certificate) (found using Codenomicon Defensics).
   * Fix remotely-triggerable memory leak caused by crafted X.509 certificates
     (TLS server is not affected if it doesn't ask for a client certificate)
     (found using Codenomicon Defensics).
   * Fix potential stack overflow while parsing crafted X.509 certificates
     (TLS server is not affected if it doesn't ask for a client certificate)
     found using Codenomicon Defensics).
   * Fix buffer overread of size 1 when parsing crafted X.509 certificates
     (TLS server is not affected if it doesn't ask for a client certificate).

Bugfix
   * Fix potential undefined behaviour in Camellia.
   * Fix memory leaks in PKCS#5 and PKCS#12.
   * Stack buffer overflow if ctr_drbg_update() is called with too large
     add_len (found by Jean-Philippe Aumasson) (not triggerable remotely).
   * Fix bug in MPI/bignum on s390/s390x (reported by Dan Horák) (introduced
     in 1.2.12).
   * Fix unchecked return code in x509_crt_parse_path() on Windows (found by
     Peter Vaskovic).
   * Fix assembly selection for MIPS64 (thanks to James Cowgill).
   * ssl_get_verify_result() now works even if the handshake was aborted due
     to a failed verification (found by Fredrik Axelsson).
   * Skip writing and parsing signature_algorithm extension if none of the
     key exchanges enabled needs certificates. This fixes a possible interop
     issue with some servers when a zero-length extension was sent. (Reported
     by Peter Dettman.)
   * On a 0-length input, base64_encode() did not correctly set output length
     (found by Hendrik van den Boogaard).

Changes
   * Blind RSA private operations even when POLARSSL_RSA_NO_CRT is defined.
   * Forbid repeated extensions in X.509 certificates.
   * Add compile-time option POLARSSL_X509_MAX_INTERMEDIATE_CA to limit the
     length of an X.509 verification chain (default = 8).
= Version 1.2.12 released 2014-10-24

Security
   * Remotely-triggerable memory leak when parsing some X.509 certificates
     (server is not affected if it doesn't ask for a client certificate).
     (Found using Codenomicon Defensics.)

Bugfix
   * Fix potential bad read in parsing ServerHello (found by Adrien
     Vialletelle).
   * ssl_close_notify() could send more than one message in some circumstances
     with non-blocking I/O.
   * x509_crt_parse() did not increase total_failed on PEM error
   * Fix compiler warnings on iOS (found by Sander Niemeijer).
   * Don't print uninitialised buffer in ssl_mail_client (found by Marc Abel).
   * Fix net_accept() regarding non-blocking sockets (found by Luca Pesce).
   * ssl_read() could return non-application data records on server while
     renegotation was pending, and on client when a HelloRequest was received.
   * Fix warnings from Clang's scan-build (contributed by Alfred Klomp).

Changes
   * X.509 certificates with more than one AttributeTypeAndValue per
     RelativeDistinguishedName are not accepted any more.
   * ssl_read() now returns POLARSSL_ERR_NET_WANT_READ rather than
     POLARSSL_ERR_SSL_UNEXPECTED_MESSAGE on harmless alerts.
   * Accept spaces at end of line or end of buffer in base64_decode().

= Version 1.2.11 released 2014-07-11
Features
   * Entropy module now supports seed writing and reading

Changes
   * Introduced POLARSSL_HAVE_READDIR_R for systems without it
   * Improvements to the CMake build system, contributed by Julian Ospald.
   * Work around a bug of the version of Clang shipped by Apple with Mavericks
     that prevented bignum.c from compiling. (Reported by Rafael Baptista.)
   * Improvements to tests/Makefile, contributed by Oden Eriksson.
   * Use UTC time to check certificate validity.
   * Reject certificates with times not in UTC, per RFC 5280.
   * Migrate zeroizing of data to polarssl_zeroize() instead of memset()
     against unwanted compiler optimizations

Security
   * Forbid change of server certificate during renegotiation to prevent
     "triple handshake" attack when authentication mode is optional (the
     attack was already impossible when authentication is required).
   * Check notBefore timestamp of certificates and CRLs from the future.
   * Forbid sequence number wrapping
   * Prevent potential NULL pointer dereference in ssl_read_record() (found by
     TrustInSoft)
   * Fix length checking for AEAD ciphersuites (found by Codenomicon).
     It was possible to crash the server (and client) using crafted messages
     when a GCM suite was chosen.

Bugfix
   * Fixed X.509 hostname comparison (with non-regular characters)
   * SSL now gracefully handles missing RNG
   * crypt_and_hash app checks MAC before final decryption
   * Fixed x509_crt_parse_path() bug on Windows platforms
   * Added missing MPI_CHK() around some statements in mpi_div_mpi() (found by
     TrustInSoft)
   * Fixed potential overflow in certificate size verification in
     ssl_write_certificate() (found by TrustInSoft)
   * Fix ASM format in bn_mul.h
   * Potential memory leak in bignum_selftest()
   * Replaced expired test certificate
   * ssl_mail_client now terminates lines with CRLF, instead of LF
   * Fix bug in RSA PKCS#1 v1.5 "reversed" operations
   * Fixed testing with out-of-source builds using cmake
   * Fixed version-major intolerance in server
   * Fixed CMake symlinking on out-of-source builds
   * Bignum's MIPS-32 assembly was used on MIPS-64, causing chaos. (Found by
     Alex Wilson.)
   * ssl_init() was leaving a dirty pointer in ssl_context if malloc of
     out_ctr failed
   * ssl_handshake_init() was leaving dirty pointers in subcontexts if malloc
     of one of them failed
   * x509_get_current_time() uses localtime_r() to prevent thread issues
   * Some example server programs were not sending the close_notify alert.
   * Potential memory leak in mpi_exp_mod() when error occurs during
     calculation of RR.
   * Improve interoperability by not writing extension length in ClientHello
     when no extensions are present (found by Matthew Page)
   * rsa_check_pubkey() now allows an E up to N
   * On OpenBSD, use arc4random_buf() instead of rand() to prevent warnings
   * mpi_fill_random() was creating numbers larger than requested on
     big-endian platform when size was not an integer number of limbs
   * Fix detection of DragonflyBSD in net.c (found by Markus Pfeiffer)
   * Stricter check on SSL ClientHello internal sizes compared to actual packet
     size (found by TrustInSoft)
   * Fix preprocessor checks for bn_mul PPC asm (found by Barry K. Nathan).
   * Use \n\t rather than semicolons for bn_mul asm, since some assemblers
     interpret semicolons as comment delimiters (found by Barry K. Nathan).
   * Disable broken Sparc64 bn_mul assembly (found by Florian Obser).
   * Fix base64_decode() to return and check length correctly (in case of
     tight buffers)

= Version 1.2.10 released 2013-10-07
Changes
   * Changed RSA blinding to a slower but thread-safe version

Bugfix
   * Fixed memory leak in RSA as a result of introduction of blinding
   * Fixed ssl_pkcs11_decrypt() prototype
   * Fixed MSVC project files

= Version 1.2.9 released 2013-10-01
Changes
   * x509_verify() now case insensitive for cn (RFC 6125 6.4)

Bugfix
   * Fixed potential memory leak when failing to resume a session
   * Fixed potential file descriptor leaks (found by Remi Gacogne)
   * Minor fixes

Security
   * Fixed potential heap buffer overflow on large hostname setting
   * Fixed potential negative value misinterpretation in load_file()
   * RSA blinding on CRT operations to counter timing attacks
     (found by Cyril Arnaud and Pierre-Alain Fouque)

= Version 1.2.8 released 2013-06-19
Features
   * Parsing of PKCS#8 encrypted private key files
   * PKCS#12 PBE and derivation functions
   * Centralized module option values in config.h to allow user-defined
     settings without editing header files by using POLARSSL_CONFIG_OPTIONS

Changes
   * HAVEGE random generator disabled by default
   * Internally split up x509parse_key() into a (PEM) handler function
     and specific DER parser functions for the PKCS#1 and unencrypted
     PKCS#8 private key formats
   * Added mechanism to provide alternative implementations for all
     symmetric cipher and hash algorithms (e.g. POLARSSL_AES_ALT in
     config.h)
   * PKCS#5 module added. Moved PBKDF2 functionality inside and deprecated
     old PBKDF2 module

Bugfix
   * Secure renegotiation extension should only be sent in case client
     supports secure renegotiation
   * Fixed offset for cert_type list in ssl_parse_certificate_request()
   * Fixed const correctness issues that have no impact on the ABI
   * x509parse_crt() now better handles PEM error situations
   * ssl_parse_certificate() now calls x509parse_crt_der() directly
     instead of the x509parse_crt() wrapper that can also parse PEM
     certificates
   * x509parse_crtpath() is now reentrant and uses more portable stat()
   * Fixed bignum.c and bn_mul.h to support Thumb2 and LLVM compiler
   * Fixed values for 2-key Triple DES in cipher layer
   * ssl_write_certificate_request() can handle empty ca_chain

Security
   * A possible DoS during the SSL Handshake, due to faulty parsing of
     PEM-encoded certificates has been fixed (found by Jack Lloyd)

= Version 1.2.7 released 2013-04-13
Features
   * Ability to specify allowed ciphersuites based on the protocol version.

Changes
   * Default Blowfish keysize is now 128-bits
   * Test suites made smaller to accommodate Raspberry Pi

Bugfix
   * Fix for MPI assembly for ARM
   * GCM adapted to support sizes > 2^29

= Version 1.2.6 released 2013-03-11
Bugfix
   * Fixed memory leak in ssl_free() and ssl_reset() for active session
   * Corrected GCM counter incrementation to use only 32-bits instead of
     128-bits (found by Yawning Angel)
   * Fixes for 64-bit compilation with MS Visual Studio
   * Fixed net_bind() for specified IP addresses on little endian systems
   * Fixed assembly code for ARM (Thumb and regular) for some compilers

Changes
   * Internally split up rsa_pkcs1_encrypt(), rsa_pkcs1_decrypt(),
     rsa_pkcs1_sign() and rsa_pkcs1_verify() to separate PKCS#1 v1.5 and
     PKCS#1 v2.1 functions
   * Added support for custom labels when using rsa_rsaes_oaep_encrypt()
     or rsa_rsaes_oaep_decrypt()
   * Re-added handling for SSLv2 Client Hello when the define
     POLARSSL_SSL_SRV_SUPPORT_SSLV2_CLIENT_HELLO is set
   * The SSL session cache module (ssl_cache) now also retains peer_cert
     information (not the entire chain)

Security
   * Removed further timing differences during SSL message decryption in
     ssl_decrypt_buf()
   * Removed timing differences due to bad padding from
     rsa_rsaes_pkcs1_v15_decrypt() and rsa_pkcs1_decrypt() for PKCS#1 v1.5
     operations

= Version 1.2.5 released 2013-02-02
Changes
   * Allow enabling of dummy error_strerror() to support some use-cases
   * Debug messages about padding errors during SSL message decryption are
     disabled by default and can be enabled with POLARSSL_SSL_DEBUG_ALL
   * Sending of security-relevant alert messages that do not break
     interoperability can be switched on/off with the flag
     POLARSSL_SSL_ALL_ALERT_MESSAGES

Security
   * Removed timing differences during SSL message decryption in
     ssl_decrypt_buf() due to badly formatted padding

= Version 1.2.4 released 2013-01-25
Changes
   * More advanced SSL ciphersuite representation and moved to more dynamic
     SSL core
   * Added ssl_handshake_step() to allow single stepping the handshake process

Bugfix
   * Memory leak when using RSA_PKCS_V21 operations fixed
   * Handle future version properly in ssl_write_certificate_request()
   * Correctly handle CertificateRequest message in client for <= TLS 1.1
     without DN list

= Version 1.2.3 released 2012-11-26
Bugfix
   * Server not always sending correct CertificateRequest message

= Version 1.2.2 released 2012-11-24
Changes
   * Added p_hw_data to ssl_context for context specific hardware acceleration
     data
   * During verify trust-CA is only checked for expiration and CRL presence

Bugfixes
   * Fixed client authentication compatibility
   * Fixed dependency on POLARSSL_SHA4_C in SSL modules

= Version 1.2.1 released 2012-11-20
Changes
   * Depth that the certificate verify callback receives is now numbered
     bottom-up (Peer cert depth is 0)

Bugfixes
   * Fixes for MSVC6
   * Moved mpi_inv_mod() outside POLARSSL_GENPRIME
   * Allow R and A to point to same mpi in mpi_div_mpi (found by Manuel
     Pégourié-Gonnard)
   * Fixed possible segfault in mpi_shift_r() (found by Manuel
     Pégourié-Gonnard)
   * Added max length check for rsa_pkcs1_sign with PKCS#1 v2.1

= Version 1.2.0 released 2012-10-31
Features
   * Added support for NULL cipher (POLARSSL_CIPHER_NULL_CIPHER) and weak
     ciphersuites (POLARSSL_ENABLE_WEAK_CIPHERSUITES). They are disabled by
     default!
   * Added support for wildcard certificates
   * Added support for multi-domain certificates through the X509 Subject
     Alternative Name extension
   * Added preliminary ASN.1 buffer writing support
   * Added preliminary X509 Certificate Request writing support
   * Added key_app_writer example application
   * Added cert_req example application
   * Added base Galois Counter Mode (GCM) for AES
   * Added TLS 1.2 support (RFC 5246)
   * Added GCM suites to TLS 1.2 (RFC 5288)
   * Added commandline error code convertor (util/strerror)
   * Added support for Hardware Acceleration hooking in SSL/TLS
   * Added OpenSSL / PolarSSL compatibility script (tests/compat.sh) and
     example application (programs/ssl/o_p_test) (requires OpenSSL)
   * Added X509 CA Path support
   * Added Thumb assembly optimizations
   * Added DEFLATE compression support as per RFC3749 (requires zlib)
   * Added blowfish algorithm (Generic and cipher layer)
   * Added PKCS#5 PBKDF2 key derivation function
   * Added Secure Renegotiation (RFC 5746)
   * Added predefined DHM groups from RFC 5114
   * Added simple SSL session cache implementation
   * Added ServerName extension parsing (SNI) at server side
   * Added option to add minimum accepted SSL/TLS protocol version

Changes
   * Removed redundant POLARSSL_DEBUG_MSG define
   * AES code only check for Padlock once
   * Fixed const-correctness mpi_get_bit()
   * Documentation for mpi_lsb() and mpi_msb()
   * Moved out_msg to out_hdr + 32 to support hardware acceleration
   * Changed certificate verify behaviour to comply with RFC 6125 section 6.3
     to not match CN if subjectAltName extension is present (Closes ticket #56)
   * Cipher layer cipher_mode_t POLARSSL_MODE_CFB128 is renamed to
     POLARSSL_MODE_CFB, to also handle different block size CFB modes.
   * Removed handling for SSLv2 Client Hello (as per RFC 5246 recommendation)
   * Revamped session resumption handling
   * Generalized external private key implementation handling (like PKCS#11)
     in SSL/TLS
   * Revamped x509_verify() and the SSL f_vrfy callback implementations
   * Moved from unsigned long to fixed width uint32_t types throughout code
   * Renamed ciphersuites naming scheme to IANA reserved names

Bugfix
   * Fixed handling error in mpi_cmp_mpi() on longer B values (found by
     Hui Dong)
   * Fixed potential heap corruption in x509_name allocation
   * Fixed single RSA test that failed on Big Endian systems (Closes ticket #54)
   * mpi_exp_mod() now correctly handles negative base numbers (Closes ticket
     #52)
   * Handle encryption with private key and decryption with public key as per
     RFC 2313
   * Handle empty certificate subject names
   * Prevent reading over buffer boundaries on X509 certificate parsing
   * mpi_add_abs() now correctly handles adding short numbers to long numbers
     with carry rollover (found by Ruslan Yushchenko)
   * Handle existence of OpenSSL Trust Extensions at end of X.509 DER blob
   * Fixed MPI assembly for SPARC64 platform

Security
   * Fixed potential memory zeroization on miscrafted RSA key (found by Eloi
     Vanderbeken)

= Version 1.1.8 released on 2013-10-01
Bugfix
   * Fixed potential memory leak when failing to resume a session
   * Fixed potential file descriptor leaks

Security
   * Potential buffer-overflow for ssl_read_record() (independently found by
     both TrustInSoft and Paul Brodeur of Leviathan Security Group)
   * Potential negative value misinterpretation in load_file()
   * Potential heap buffer overflow on large hostname setting

= Version 1.1.7 released on 2013-06-19
Changes
   * HAVEGE random generator disabled by default

Bugfix
   * x509parse_crt() now better handles PEM error situations
   * ssl_parse_certificate() now calls x509parse_crt_der() directly
     instead of the x509parse_crt() wrapper that can also parse PEM
     certificates
   * Fixed values for 2-key Triple DES in cipher layer
   * ssl_write_certificate_request() can handle empty ca_chain

Security
   * A possible DoS during the SSL Handshake, due to faulty parsing of
     PEM-encoded certificates has been fixed (found by Jack Lloyd)

= Version 1.1.6 released on 2013-03-11
Bugfix
   * Fixed net_bind() for specified IP addresses on little endian systems

Changes
   * Allow enabling of dummy error_strerror() to support some use-cases
   * Debug messages about padding errors during SSL message decryption are
     disabled by default and can be enabled with POLARSSL_SSL_DEBUG_ALL

Security
   * Removed timing differences during SSL message decryption in
     ssl_decrypt_buf()
   * Removed timing differences due to bad padding from
     rsa_rsaes_pkcs1_v15_decrypt() and rsa_pkcs1_decrypt() for PKCS#1 v1.5
     operations

= Version 1.1.5 released on 2013-01-16
Bugfix
   * Fixed MPI assembly for SPARC64 platform
   * Handle existence of OpenSSL Trust Extensions at end of X.509 DER blob
   * mpi_add_abs() now correctly handles adding short numbers to long numbers
     with carry rollover
   * Moved mpi_inv_mod() outside POLARSSL_GENPRIME
   * Prevent reading over buffer boundaries on X509 certificate parsing
   * mpi_exp_mod() now correctly handles negative base numbers (Closes ticket
     #52)
   * Fixed possible segfault in mpi_shift_r() (found by Manuel
     Pégourié-Gonnard)
   * Allow R and A to point to same mpi in mpi_div_mpi (found by Manuel
     Pégourié-Gonnard)
   * Added max length check for rsa_pkcs1_sign with PKCS#1 v2.1
   * Memory leak when using RSA_PKCS_V21 operations fixed
   * Handle encryption with private key and decryption with public key as per
     RFC 2313
   * Fixes for MSVC6

Security
   * Fixed potential memory zeroization on miscrafted RSA key (found by Eloi
     Vanderbeken)

= Version 1.1.4 released on 2012-05-31
Bugfix
   * Correctly handle empty SSL/TLS packets (Found by James Yonan)
   * Fixed potential heap corruption in x509_name allocation
   * Fixed single RSA test that failed on Big Endian systems (Closes ticket #54)

= Version 1.1.3 released on 2012-04-29
Bugfix
   * Fixed random MPI generation to not generate more size than requested.

= Version 1.1.2 released on 2012-04-26
Bugfix
   * Fixed handling error in mpi_cmp_mpi() on longer B values (found by
     Hui Dong)

Security
   * Fixed potential memory corruption on miscrafted client messages (found by
     Frama-C team at CEA LIST)
   * Fixed generation of DHM parameters to correct length (found by Ruslan
     Yushchenko)

= Version 1.1.1 released on 2012-01-23
Bugfix
   * Check for failed malloc() in ssl_set_hostname() and x509_get_entries()
     (Closes ticket #47, found by Hugo Leisink)
   * Fixed issues with Intel compiler on 64-bit systems (Closes ticket #50)
   * Fixed multiple compiler warnings for VS6 and armcc
   * Fixed bug in CTR_CRBG selftest

= Version 1.1.0 released on 2011-12-22
Features
   * Added ssl_session_reset() to allow better multi-connection pools of
     SSL contexts without needing to set all non-connection-specific
     data and pointers again. Adapted ssl_server to use this functionality.
   * Added ssl_set_max_version() to allow clients to offer a lower maximum
     supported version to a server to help buggy server implementations.
     (Closes ticket #36)
   * Added cipher_get_cipher_mode() and cipher_get_cipher_operation()
     introspection functions (Closes ticket #40)
   * Added CTR_DRBG based on AES-256-CTR (NIST SP 800-90) random generator
   * Added a generic entropy accumulator that provides support for adding
     custom entropy sources and added some generic and platform dependent
     entropy sources

Changes
   * Documentation for AES and Camellia in modes CTR and CFB128 clarified.
   * Fixed rsa_encrypt and rsa_decrypt examples to use public key for
     encryption and private key for decryption. (Closes ticket #34)
   * Inceased maximum size of ASN1 length reads to 32-bits.
   * Added an EXPLICIT tag number parameter to x509_get_ext()
   * Added a separate CRL entry extension parsing function
   * Separated the ASN.1 parsing code from the X.509 specific parsing code.
     So now there is a module that is controlled with POLARSSL_ASN1_PARSE_C.
   * Changed the defined key-length of DES ciphers in cipher.h to include the
     parity bits, to prevent mistakes in copying data. (Closes ticket #33)
   * Loads of minimal changes to better support WINCE as a build target
     (Credits go to Marco Lizza)
   * Added POLARSSL_MPI_WINDOW_SIZE definition to allow easier time to memory
     trade-off
   * Introduced POLARSSL_MPI_MAX_SIZE and POLARSSL_MPI_MAX_BITS for MPI size
     management (Closes ticket #44)
   * Changed the used random function pointer to more flexible format. Renamed
     havege_rand() to havege_random() to prevent mistakes. Lots of changes as
     a consequence in library code and programs
   * Moved all examples programs to use the new entropy and CTR_DRBG
   * Added permissive certificate parsing to x509parse_crt() and
     x509parse_crtfile(). With permissive parsing the parsing does not stop on
     encountering a parse-error. Beware that the meaning of return values has
     changed!
   * All error codes are now negative. Even on mermory failures and IO errors.

Bugfix
   * Fixed faulty HMAC-MD2 implementation. Found by dibac. (Closes
     ticket #37)
   * Fixed a bug where the CRL parser expected an EXPLICIT ASN.1 tag
     before version numbers
   * Allowed X509 key usage parsing to accept 4 byte values instead of the
     standard 1 byte version sometimes used by Microsoft. (Closes ticket #38)
   * Fixed incorrect behaviour in case of RSASSA-PSS with a salt length
     smaller than the hash length. (Closes ticket #41)
   * If certificate serial is longer than 32 octets, serial number is now
     appended with '....' after first 28 octets
   * Improved build support for s390x and sparc64 in bignum.h
   * Fixed MS Visual C++ name clash with int64 in sha4.h
   * Corrected removal of leading "00:" in printing serial numbers in
     certificates and CRLs

= Version 1.0.0 released on 2011-07-27
Features
   * Expanded cipher layer with support for CFB128 and CTR mode
   * Added rsa_encrypt and rsa_decrypt simple example programs.

Changes
   * The generic cipher and message digest layer now have normal error
     codes instead of integers

Bugfix
   * Undid faulty bug fix in ssl_write() when flushing old data (Ticket
     #18)

= Version 0.99-pre5 released on 2011-05-26
Features
   * Added additional Cipher Block Modes to symmetric ciphers
     (AES CTR, Camellia CTR, XTEA CBC) including the option to
     enable and disable individual modes when needed
   * Functions requiring File System functions can now be disabled
     by undefining POLARSSL_FS_IO
   * A error_strerror function() has been added to translate between
     error codes and their description.
   * Added mpi_get_bit() and mpi_set_bit() individual bit setter/getter
     functions.
   * Added ssl_mail_client and ssl_fork_server as example programs.

Changes
   * Major argument / variable rewrite. Introduced use of size_t
     instead of int for buffer lengths and loop variables for
     better unsigned / signed use. Renamed internal bigint types
     t_int and t_dbl to t_uint and t_udbl in the process
   * mpi_init() and mpi_free() now only accept a single MPI
     argument and do not accept variable argument lists anymore.
   * The error codes have been remapped and combining error codes
     is now done with a PLUS instead of an OR as error codes
     used are negative.
   * Changed behaviour of net_read(), ssl_fetch_input() and ssl_recv().
     net_recv() now returns 0 on EOF instead of
     POLARSSL_ERR_NET_CONN_RESET. ssl_fetch_input() returns
     POLARSSL_ERR_SSL_CONN_EOF on an EOF from its f_recv() function.
     ssl_read() returns 0 if a POLARSSL_ERR_SSL_CONN_EOF is received
     after the handshake.
   * Network functions now return POLARSSL_ERR_NET_WANT_READ or
     POLARSSL_ERR_NET_WANT_WRITE instead of the ambiguous
     POLARSSL_ERR_NET_TRY_AGAIN

= Version 0.99-pre4 released on 2011-04-01
Features
   * Added support for PKCS#1 v2.1 encoding and thus support
     for the RSAES-OAEP and RSASSA-PSS operations.
   * Reading of Public Key files incorporated into default x509
     functionality as well.
   * Added mpi_fill_random() for centralized filling of big numbers
     with random data (Fixed ticket #10)

Changes
   * Debug print of MPI now removes leading zero octets and
     displays actual bit size of the value.
   * x509parse_key() (and as a consequence x509parse_keyfile())
     does not zeroize memory in advance anymore. Use rsa_init()
     before parsing a key or keyfile!

Bugfix
   * Debug output of MPI's now the same independent of underlying
     platform (32-bit / 64-bit) (Fixes ticket #19, found by Mads
     Kiilerich and Mihai Militaru)
   * Fixed bug in ssl_write() when flushing old data (Fixed ticket
     #18, found by Nikolay Epifanov)
   * Fixed proper handling of RSASSA-PSS verification with variable
     length salt lengths

= Version 0.99-pre3 released on 2011-02-28
This release replaces version 0.99-pre2 which had possible copyright issues.
Features
   * Parsing PEM private keys encrypted with DES and AES
     are now supported as well (Fixes ticket #5)
   * Added crl_app program to allow easy reading and
     printing of X509 CRLs from file

Changes
   * Parsing of PEM files moved to separate module (Fixes
     ticket #13). Also possible to remove PEM support for
     systems only using DER encoding

Bugfixes
   * Corrected parsing of UTCTime dates before 1990 and
     after 1950
   * Support more exotic OID's when parsing certificates
     (found by Mads Kiilerich)
   * Support more exotic name representations when parsing
     certificates (found by Mads Kiilerich)
   * Replaced the expired test certificates
   * Do not bail out if no client certificate specified. Try
     to negotiate anonymous connection (Fixes ticket #12,
     found by Boris Krasnovskiy)

Security fixes
   * Fixed a possible Man-in-the-Middle attack on the
     Diffie Hellman key exchange (thanks to Larry Highsmith,
     Subreption LLC)

= Version 0.99-pre1 released on 2011-01-30
Features
Note: Most of these features have been donated by Fox-IT
   * Added Doxygen source code documentation parts
   * Added reading of DHM context from memory and file
   * Improved X509 certificate parsing to include extended
     certificate fields, including Key Usage
   * Improved certificate verification and verification
     against the available CRLs
   * Detection for DES weak keys and parity bits added
   * Improvements to support integration in other
     applications:
       + Added generic message digest and cipher wrapper
       + Improved information about current capabilities,
         status, objects and configuration
       + Added verification callback on certificate chain
         verification to allow external blacklisting
       + Additional example programs to show usage
   * Added support for PKCS#11 through the use of the
     libpkcs11-helper library

Changes
   * x509parse_time_expired() checks time in addition to
     the existing date check
   * The ciphers member of ssl_context and the cipher member
     of ssl_session have been renamed to ciphersuites and
     ciphersuite respectively. This clarifies the difference
     with the generic cipher layer and is better naming
     altogether

= Version 0.14.0 released on 2010-08-16
Features
   * Added support for SSL_EDH_RSA_AES_128_SHA and
     SSL_EDH_RSA_CAMELLIA_128_SHA ciphersuites
   * Added compile-time and run-time version information
   * Expanded ssl_client2 arguments for more flexibility
   * Added support for TLS v1.1

Changes
   * Made Makefile cleaner
   * Removed dependency on rand() in rsa_pkcs1_encrypt().
     Now using random fuction provided to function and
     changed the prototype of rsa_pkcs1_encrypt(),
     rsa_init() and rsa_gen_key().
   * Some SSL defines were renamed in order to avoid
     future confusion

Bug fixes
   * Fixed CMake out of source build for tests (found by
     kkert)
   * rsa_check_private() now supports PKCS1v2 keys as well
   * Fixed deadlock in rsa_pkcs1_encrypt() on failing random
     generator

= Version 0.13.1 released on 2010-03-24
Bug fixes
   * Fixed Makefile in library that was mistakenly merged
   * Added missing const string fixes

= Version 0.13.0 released on 2010-03-21
Features
   * Added option parsing for host and port selection to
     ssl_client2
   * Added support for GeneralizedTime in X509 parsing
   * Added cert_app program to allow easy reading and
     printing of X509 certificates from file or SSL
     connection.

Changes
   * Added const correctness for main code base
   * X509 signature algorithm determination is now
     in a function to allow easy future expansion
   * Changed symmetric cipher functions to
     identical interface (returning int result values)
   * Changed ARC4 to use separate input/output buffer
   * Added reset function for HMAC context as speed-up
     for specific use-cases

Bug fixes
   * Fixed bug resulting in failure to send the last
     certificate in the chain in ssl_write_certificate() and
     ssl_write_certificate_request() (found by fatbob)
   * Added small fixes for compiler warnings on a Mac
     (found by Frank de Brabander)
   * Fixed algorithmic bug in mpi_is_prime() (found by
     Smbat Tonoyan)

= Version 0.12.1 released on 2009-10-04
Changes
   * Coverage test definitions now support 'depends_on'
     tagging system.
   * Tests requiring specific hashing algorithms now honor
     the defines.

Bug fixes
   * Changed typo in #ifdef in x509parse.c (found
     by Eduardo)

= Version 0.12.0 released on 2009-07-28
Features
   * Added CMake makefiles as alternative to regular Makefiles.
   * Added preliminary Code Coverage tests for AES, ARC4,
     Base64, MPI, SHA-family, MD-family, HMAC-SHA-family,
     Camellia, DES, 3-DES, RSA PKCS#1, XTEA, Diffie-Hellman
     and X509parse.

Changes
   * Error codes are not (necessarily) negative. Keep
     this is mind when checking for errors.
   * RSA_RAW renamed to SIG_RSA_RAW for consistency.
   * Fixed typo in name of POLARSSL_ERR_RSA_OUTPUT_TOO_LARGE.
   * Changed interface for AES and Camellia setkey functions
     to indicate invalid key lengths.

Bug fixes
   * Fixed include location of endian.h on FreeBSD (found by
     Gabriel)
   * Fixed include location of endian.h and name clash on
     Apples (found by Martin van Hensbergen)
   * Fixed HMAC-MD2 by modifying md2_starts(), so that the
     required HMAC ipad and opad variables are not cleared.
     (found by code coverage tests)
   * Prevented use of long long in bignum if
     POLARSSL_HAVE_LONGLONG not defined (found by Giles
     Bathgate).
   * Fixed incorrect handling of negative strings in
     mpi_read_string() (found by code coverage tests).
   * Fixed segfault on handling empty rsa_context in
     rsa_check_pubkey() and rsa_check_privkey() (found by
     code coverage tests).
   * Fixed incorrect handling of one single negative input
     value in mpi_add_abs() (found by code coverage tests).
   * Fixed incorrect handling of negative first input
     value in mpi_sub_abs() (found by code coverage tests).
   * Fixed incorrect handling of negative first input
     value in mpi_mod_mpi() and mpi_mod_int(). Resulting
     change also affects mpi_write_string() (found by code
     coverage tests).
   * Corrected is_prime() results for 0, 1 and 2 (found by
     code coverage tests).
   * Fixed Camellia and XTEA for 64-bit Windows systems.

= Version 0.11.1 released on 2009-05-17
   * Fixed missing functionality for SHA-224, SHA-256, SHA384,
     SHA-512 in rsa_pkcs1_sign()

= Version 0.11.0 released on 2009-05-03
   * Fixed a bug in mpi_gcd() so that it also works when both
     input numbers are even and added testcases to check
     (found by Pierre Habouzit).
   * Added support for SHA-224, SHA-256, SHA-384 and SHA-512
     one way hash functions with the PKCS#1 v1.5 signing and
     verification.
   * Fixed minor bug regarding mpi_gcd located within the
     POLARSSL_GENPRIME block.
   * Fixed minor memory leak in x509parse_crt() and added better
     handling of 'full' certificate chains (found by Mathias
     Olsson).
   * Centralized file opening and reading for x509 files into
     load_file()
   * Made definition of net_htons() endian-clean for big endian
     systems (Found by Gernot).
   * Undefining POLARSSL_HAVE_ASM now also handles prevents asm in
     padlock and timing code.
   * Fixed an off-by-one buffer allocation in ssl_set_hostname()
     responsible for crashes and unwanted behaviour.
   * Added support for Certificate Revocation List (CRL) parsing.
   * Added support for CRL revocation to x509parse_verify() and
     SSL/TLS code.
   * Fixed compatibility of XTEA and Camellia on a 64-bit system
     (found by Felix von Leitner).

= Version 0.10.0 released on 2009-01-12
   * Migrated XySSL to PolarSSL
   * Added XTEA symmetric cipher
   * Added Camellia symmetric cipher
   * Added support for ciphersuites: SSL_RSA_CAMELLIA_128_SHA,
     SSL_RSA_CAMELLIA_256_SHA and SSL_EDH_RSA_CAMELLIA_256_SHA
   * Fixed dangerous bug that can cause a heap overflow in
     rsa_pkcs1_decrypt (found by Christophe Devine)

================================================================
XySSL ChangeLog

= Version 0.9 released on 2008-03-16

    * Added support for ciphersuite: SSL_RSA_AES_128_SHA
    * Enabled support for large files by default in aescrypt2.c
    * Preliminary openssl wrapper contributed by David Barrett
    * Fixed a bug in ssl_write() that caused the same payload to
      be sent twice in non-blocking mode when send returns EAGAIN
    * Fixed ssl_parse_client_hello(): session id and challenge must
      not be swapped in the SSLv2 ClientHello (found by Greg Robson)
    * Added user-defined callback debug function (Krystian Kolodziej)
    * Before freeing a certificate, properly zero out all cert. data
    * Fixed the "mode" parameter so that encryption/decryption are
      not swapped on PadLock; also fixed compilation on older versions
      of gcc (bug reported by David Barrett)
    * Correctly handle the case in padlock_xcryptcbc() when input or
      output data is non-aligned by falling back to the software
      implementation, as VIA Nehemiah cannot handle non-aligned buffers
    * Fixed a memory leak in x509parse_crt() which was reported by Greg
      Robson-Garth; some x509write.c fixes by Pascal Vizeli, thanks to
      Matthew Page who reported several bugs
    * Fixed x509_get_ext() to accept some rare certificates which have
      an INTEGER instead of a BOOLEAN for BasicConstraints::cA.
    * Added support on the client side for the TLS "hostname" extension
      (patch contributed by David Patino)
    * Make x509parse_verify() return BADCERT_CN_MISMATCH when an empty
      string is passed as the CN (bug reported by spoofy)
    * Added an option to enable/disable the BN assembly code
    * Updated rsa_check_privkey() to verify that (D*E) = 1 % (P-1)*(Q-1)
    * Disabled obsolete hash functions by default (MD2, MD4); updated
      selftest and benchmark to not test ciphers that have been disabled
    * Updated x509parse_cert_info() to correctly display byte 0 of the
      serial number, setup correct server port in the ssl client example
    * Fixed a critical denial-of-service with X.509 cert. verification:
      peer may cause xyssl to loop indefinitely by sending a certificate
      for which the RSA signature check fails (bug reported by Benoit)
    * Added test vectors for: AES-CBC, AES-CFB, DES-CBC and 3DES-CBC,
      HMAC-MD5, HMAC-SHA1, HMAC-SHA-256, HMAC-SHA-384, and HMAC-SHA-512
    * Fixed HMAC-SHA-384 and HMAC-SHA-512 (thanks to Josh Sinykin)
    * Modified ssl_parse_client_key_exchange() to protect against
      Daniel Bleichenbacher attack on PKCS#1 v1.5 padding, as well
      as the Klima-Pokorny-Rosa extension of Bleichenbacher's attack
    * Updated rsa_gen_key() so that ctx->N is always nbits in size
    * Fixed assembly PPC compilation errors on Mac OS X, thanks to
      David Barrett and Dusan Semen

= Version 0.8 released on 2007-10-20

    * Modified the HMAC functions to handle keys larger
      than 64 bytes, thanks to Stephane Desneux and gary ng
    * Fixed ssl_read_record() to properly update the handshake
      message digests, which fixes IE6/IE7 client authentication
    * Cleaned up the XYSSL* #defines, suggested by Azriel Fasten
    * Fixed net_recv(), thanks to Lorenz Schori and Egon Kocjan
    * Added user-defined callbacks for handling I/O and sessions
    * Added lots of debugging output in the SSL/TLS functions
    * Added preliminary X.509 cert. writing by Pascal Vizeli
    * Added preliminary support for the VIA PadLock routines
    * Added AES-CFB mode of operation, contributed by chmike
    * Added an SSL/TLS stress testing program (ssl_test.c)
    * Updated the RSA PKCS#1 code to allow choosing between
      RSA_PUBLIC and RSA_PRIVATE, as suggested by David Barrett
    * Updated ssl_read() to skip 0-length records from OpenSSL
    * Fixed the make install target to comply with *BSD make
    * Fixed a bug in mpi_read_binary() on 64-bit platforms
    * mpi_is_prime() speedups, thanks to Kevin McLaughlin
    * Fixed a long standing memory leak in mpi_is_prime()
    * Replaced realloc with malloc in mpi_grow(), and set
      the sign of zero as positive in mpi_init() (reported
      by Jonathan M. McCune)

= Version 0.7 released on 2007-07-07

    * Added support for the MicroBlaze soft-core processor
    * Fixed a bug in ssl_tls.c which sometimes prevented SSL
      connections from being established with non-blocking I/O
    * Fixed a couple bugs in the VS6 and UNIX Makefiles
    * Fixed the "PIC register ebx clobbered in asm" bug
    * Added HMAC starts/update/finish support functions
    * Added the SHA-224, SHA-384 and SHA-512 hash functions
    * Fixed the net_set_*block routines, thanks to Andreas
    * Added a few demonstration programs: md5sum, sha1sum,
      dh_client, dh_server, rsa_genkey, rsa_sign, rsa_verify
    * Added new bignum import and export helper functions
    * Rewrote README.txt in program/ssl/ca to better explain
      how to create a test PKI

= Version 0.6 released on 2007-04-01

    * Ciphers used in SSL/TLS can now be disabled at compile
      time, to reduce the memory footprint on embedded systems
    * Added multiply assembly code for the TriCore and modified
      havege_struct for this processor, thanks to David Patiño
    * Added multiply assembly code for 64-bit PowerPCs,
      thanks to Peking University and the OSU Open Source Lab
    * Added experimental support of Quantum Cryptography
    * Added support for autoconf, contributed by Arnaud Cornet
    * Fixed "long long" compilation issues on IA-64 and PPC64
    * Fixed a bug introduced in xyssl-0.5/timing.c: hardclock
      was not being correctly defined on ARM and MIPS

= Version 0.5 released on 2007-03-01

    * Added multiply assembly code for SPARC and Alpha
    * Added (beta) support for non-blocking I/O operations
    * Implemented session resuming and client authentication
    * Fixed some portability issues on WinCE, MINIX 3, Plan9
      (thanks to Benjamin Newman), HP-UX, FreeBSD and Solaris
    * Improved the performance of the EDH key exchange
    * Fixed a bug that caused valid packets with a payload
      size of 16384 bytes to be rejected

= Version 0.4 released on 2007-02-01

    * Added support for Ephemeral Diffie-Hellman key exchange
    * Added multiply asm code for SSE2, ARM, PPC, MIPS and M68K
    * Various improvement to the modular exponentiation code
    * Rewrote the headers to generate the API docs with doxygen
    * Fixed a bug in ssl_encrypt_buf (incorrect padding was
      generated) and in ssl_parse_client_hello (max. client
      version was not properly set), thanks to Didier Rebeix
    * Fixed another bug in ssl_parse_client_hello: clients with
      cipherlists larger than 96 bytes were incorrectly rejected
    * Fixed a couple memory leak in x509_read.c

= Version 0.3 released on 2007-01-01

    * Added server-side SSLv3 and TLSv1.0 support
    * Multiple fixes to enhance the compatibility with g++,
      thanks to Xosé Antón Otero Ferreira
    * Fixed a bug in the CBC code, thanks to dowst; also,
      the bignum code is no longer dependent on long long
    * Updated rsa_pkcs1_sign to handle arbitrary large inputs
    * Updated timing.c for improved compatibility with i386
      and 486 processors, thanks to Arnaud Cornet

= Version 0.2 released on 2006-12-01

    * Updated timing.c to support ARM and MIPS arch
    * Updated the MPI code to support 8086 on MSVC 1.5
    * Added the copyright notice at the top of havege.h
    * Fixed a bug in sha2_hmac, thanks to newsoft/Wenfang Zhang
    * Fixed a bug reported by Adrian Rüegsegger in x509_read_key
    * Fixed a bug reported by Torsten Lauter in ssl_read_record
    * Fixed a bug in rsa_check_privkey that would wrongly cause
      valid RSA keys to be dismissed (thanks to oldwolf)
    * Fixed a bug in mpi_is_prime that caused some primes to fail
      the Miller-Rabin primality test

    I'd also like to thank Younès Hafri for the CRUX linux port,
    Khalil Petit who added XySSL into pkgsrc and Arnaud Cornet
    who maintains the Debian package :-)

= Version 0.1 released on 2006-11-01
